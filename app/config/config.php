<?php

/*
 * Modified: prepend directory path of current file, because of this file own different ENV under between Apache and command line.
 * NOTE: please remove this comment.
 */

use Phalcon\Config;

defined('BASE_PATH') || define('BASE_PATH', getenv('BASE_PATH') ?: realpath(dirname(__FILE__) . '/../..'));
defined('APP_PATH') || define('APP_PATH', BASE_PATH . '/app');

// 兼容马来本地节点服务
$defaultImgPrefix = 'MY' == strtoupper(env('country_code')) ? 'http://fle-staging-asset-internal.oss-ap-southeast-3.aliyuncs.com/' : 'http://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/';

return new Config(
    [
        'application' => [
            'appDir' => APP_PATH . '/',
            'controllersDir' => APP_PATH . '/controllers/',
            'modelsDir' => APP_PATH . '/models/',
            'viewsDir' => APP_PATH . '/views/',
            'pluginsDir' => APP_PATH . '/plugins/',
            'libraryDir' => APP_PATH . '/library/',
            'modulesDir' => APP_PATH . '/modules/',
            'traitDir' => APP_PATH . '/traits/',
            'migrationsDir' => APP_PATH . '/migrations/',
            'runtimeDir' => APP_PATH . '/runtime/',
            'tasksDir' => APP_PATH . '/tasks/',
            'utilDir' => APP_PATH . '/util/',
            'cacheDir' => BASE_PATH . '/cache/',
            'repositoryDir' => APP_PATH . '/repository/',
            'baseUri' => env('base_url','/'),
            'logLevel' => env('log_level', \Phalcon\Logger::INFO),
            'img_prefix' => env('img_prefix',$defaultImgPrefix),
            'trace_sql_log' => env('trace_sql_log', 0),
            'add_hour' => env('add_hour', 7),
            'common_user_oa_redhot_switch' => env('common_user_oa_redhot_switch', 0),
        ],
        'mail' =>[
            'smtp_host'    => env('mail_smtp_host', 'smtpdm-ap-southeast-1.aliyun.com'),
            'smtp_port'    => env('mail_smtp_port', 465),
            'username'     => env('mail_username', '<EMAIL>'),
            'password'     => env('mail_password', '4Ry4MTT3c38P0sWs'),
        ],
        //lnt公司 发送邮件
        'lnt_mail' =>[
            'smtp_host' => env('lnt_mail_smtp_host', 'sg-smtp.qcloudmail.com'),
            'smtp_port' => env('lnt_mail_smtp_port', 465),
            'username'  => env('lnt_mail_username', '<EMAIL>'),
            'password'  => env('lnt_mail_password', 'C4NrJCwsGqA5'),
        ],
        'database_rbi' => [
            'adapter'     =>  env('database_rbi_adapter','Mysql'),
            'host'        => env('database_rbi_host','*************'),
            'port'        => env('database_rbi_port', '3306'),
            'username'    => env('database_rbi_username','bi'),
            'password'    => env('database_rbi_password','123456'),
            'dbname'      => env('database_rbi_dbname','bi'),
            'charset'     => env('database_rbi_charset','utf8mb4'),
        ],
        'database_oa' => [
            'adapter' => 'Mysql',
            'host' => env('database_oa_host'),
            'port' => env('database_oa_port'),
            'username' => env('database_oa_username'),
            'password' => env('database_oa_password'),
            'dbname' => env('database_oa_dbname'),
            'charset' => env('database_oa_charset','utf8mb4'),
        ],
        'database_arch_oa' => [
            'adapter' => 'Mysql',
            'host' => env('database_arch_oa_host'),
            'port' => env('database_arch_oa_port'),
            'username' => env('database_arch_oa_username'),
            'password' => env('database_arch_oa_password'),
            'dbname' => env('database_arch_oa_dbname'),
            'charset' => env('database_arch_oa_charset','utf8mb4'),
        ],
        'database_backyard' => [
            'adapter'     => env('database_backyard_adapter','Mysql'),
            'host'        => env('database_backyard_host','*************'),
            'port'        => env('database_backyard_port', '3306'),
            'username'    => env('database_backyard_username','backyard'),
            'password'    => env('database_backyard_password','usVXU3vbd8TIIaxR'),
            'dbname'      => env('database_backyard_dbname','backyard'),
            'charset'     => env('database_backyard_charset','utf8mb4'),
        ],
        'database_fle' => [
            'adapter' => 'Mysql',
            'host' => env('database_fle_host'),
            'port' => env('database_fle_port'),
            'username' => env('database_fle_username'),
            'password' => env('database_fle_password'),
            'dbname' => env('database_fle_dbname'),
            'charset' => env('database_fle_charset','utf8mb4'),
        ],
        'database_rbackyard' => [
            'adapter' => env('database_rbackyard_adapter','Mysql'),
            'host' => env('database_rbackyard_host','*************'),
            'port' => env('database_rbackyard_port','3306'),
            'username' => env('database_rbackyard_username','backyard'),
            'password' => env('database_rbackyard_password', '9ZerQbncME2Dw'),
            'dbname' => env('database_rbackyard_dbname','backyard'),
            'charset' => env('database_rbackyard_charset','utf8mb4'),
        ],
        'redis' => [
            'host' => env('redis_host','*************'),
            'port' => env('redis_port','6379'),
            'auth' => env('redis_auth', null),
            'lifetime' => env('redis_lifetime',24*60*60),
            'prefix' => env('redis_prefix','oa_'),
            'db' => env('redis_db',0)
        ],
        'rocket_mq' => [
            'rocket_mq_access' => env('rocket_mq_access_key', 'LTAI1YjpUu5SL6fD'),
            'rocket_mq_secret' => env('rocket_mq_secret_key', 'xrYCCGepKDZlYWr86D3GYxvQYUwjuz'),
            //'rocket_mq_tcp_endpoint' => env('rocket_mq_tcp_endpoint'),
            'rocket_mq_http_endpoint' => env('rocket_mq_http_endpoint', 'http://1956905707885195.mqrest.cn-qingdao-public.aliyuncs.com'),
            'rocket_mq_event_tracking_topic' => env('rocket_mq_event_tracking_topic', 'dev-by-create-approval'),
            //'rocket_mq_event_tracking_group' => env('rocket_mq_event_tracking_group', 'GID-dev-po-bi-tracking-excel'),
            'rocket_mq_instance_id' => env('rocket_mq_instance_id', 'MQ_INST_1956905707885195_BauvlVUQ'),
        ],
        'invoice' => [
            // OAuth 配置
            'oauth' => [
                // 客户端凭据
                'client_id' => env('e_invoice_oauth_client_id') ?: '',
                'client_secret' => env('e_invoice_oauth_client_secret') ?: '',

                // 开发/生产模式
                'pro_mode' => env('e_invoice_pro_mode') ?: false,

                // 作用域
                'scope' => env('e_invoice_oauth_scope') ?: 'InvoicingAPI',

                // 缓存键
                'cache_key' => 'e_invoice_oauth_token',
            ],

        ]
    ]
);
