<?php

return [
    'common' => [
        'className' => App\Modules\Common\Module::class,
        'path' => APP_PATH . '/modules/Common/Module.php',
        'metadata'  => [
            'controllersNamespace' => 'App\Modules\Common\Controllers',
        ],
    ],
    'user' => [
        'className' => App\Modules\User\Module::class,
        'path' => APP_PATH . '/modules/User/Module.php',
        'metadata'  => [
            'controllersNamespace' => 'App\Modules\User\Controllers',
        ],
    ],
    'workflow' => [
        'className' => App\Modules\Workflow\Module::class,
        'path' => APP_PATH . '/modules/Workflow/Module.php',
        'metadata'  => [
            'controllersNamespace' => 'App\Modules\Workflow\Controllers',
        ],
    ],
    'contract' => [
        'className' => App\Modules\Contract\Module::class,
        'path' => APP_PATH . '/modules/Contract/Module.php',
        'metadata'  => [
            'controllersNamespace' => 'App\Modules\Contract\Controllers',
        ],
    ],
    'vendor' => [
        'className' => App\Modules\Vendor\Module::class,
        'path' => APP_PATH . '/modules/Vendor/Module.php',
        'metadata'  => [
            'controllersNamespace' => 'App\Modules\Vendor\Controllers',
        ],
    ],
    'asset' => [
        'className' => App\Modules\Asset\Module::class,
        'path' => APP_PATH . '/modules/Asset/Module.php',
        'metadata'  => [
            'controllersNamespace' => 'App\Modules\Asset\Controllers',
        ],
    ],
    'wms' => [
        'className' => App\Modules\Wms\Module::class,
        'path' => APP_PATH . '/modules/Wms/Module.php',
        'metadata'  => [
            'controllersNamespace' => 'App\Modules\Wms\Controllers',
        ],
    ],
    'loan' => [
        'className' => App\Modules\Loan\Module::class,
        'path' => APP_PATH . '/modules/Loan/Module.php',
        'metadata'  => [
            'controllersNamespace' => 'App\Modules\Loan\Controllers',
        ],
    ],
    'share' => [
        'className' => App\Modules\Share\Module::class,
        'path' => APP_PATH . '/modules/Share/Module.php',
        'metadata'  => [
            'controllersNamespace' => 'App\Modules\Share\Controllers',
        ],
    ],
    'organization' => [
        'className' => App\Modules\Organization\Module::class,
        'path' => APP_PATH . '/modules/Organization/Module.php',
        'metadata'  => [
            'controllersNamespace' => 'App\Modules\Organization\Controllers',
        ],
    ],
    'ticket' => [
        'className' => App\Modules\Ticket\Module::class,
        'path' => APP_PATH . '/modules/Ticket/Module.php',
        'metadata'  => [
            'controllersNamespace' => 'App\Modules\Ticket\Controllers',
        ],
    ],
    'purchase' => [
        'className' => App\Modules\Purchase\Module::class,
        'path' => APP_PATH . '/modules/Purchase/Module.php',
        'metadata'  => [
            'controllersNamespace' => 'App\Modules\Purchase\Controllers',
        ],
    ],
    'reimbursement' => [
        'className' => App\Modules\Reimbursement\Module::class,
        'path' => APP_PATH . '/modules/Reimbursement/Module.php',
        'metadata'  => [
            'controllersNamespace' => 'App\Modules\Reimbursement\Controllers',
        ],
    ],
    'wages' => [
        'className' => App\Modules\Wages\Module::class,
        'path' => APP_PATH . '/modules/Wages/Module.php',
        'metadata'  => [
            'controllersNamespace' => 'App\Modules\Wages\Controllers',
        ],
    ],
    'hc' => [
        'className' => App\Modules\Hc\Module::class,
        'path' => APP_PATH . '/modules/Hc/Module.php',
        'metadata'  => [
            'controllersNamespace' => 'App\Modules\Hc\Controllers',
        ],
    ],
    'ledger' => [
        'className' => App\Modules\Ledger\Module::class,
        'path' => APP_PATH . '/modules/Ledger/Module.php',
        'metadata'  => [
            'controllersNamespace' => 'App\Modules\Ledger\Controllers',
        ],
    ],
    'payment' => [
        'className' => App\Modules\Payment\Module::class,
        'path' => APP_PATH . '/modules/Payment/Module.php',
        'metadata'  => [
            'controllersNamespace' => 'App\Modules\Payment\Controllers',
        ],
    ],

    'budget' => [
        'className' => App\Modules\Budget\Module::class,
        'path' => APP_PATH . '/modules/Budget/Module.php',
        'metadata'  => [
            'controllersNamespace' => 'App\Modules\Budget\Controllers',
        ],
    ],
    'ordinarypayment' => [
        'className' => App\Modules\OrdinaryPayment\Module::class,
        'path' => APP_PATH . '/modules/OrdinaryPayment/Module.php',
        'metadata'  => [
            'controllersNamespace' => 'App\Modules\OrdinaryPayment\Controllers',
        ],
    ],
    'deposit' => [
        'className' => App\Modules\Deposit\Module::class,
        'path' => APP_PATH . '/modules/Deposit/Module.php',
        'metadata'  => [
            'controllersNamespace' => 'App\Modules\Deposit\Controllers',
        ],
    ],
    'salary' => [
        'className' => App\Modules\Salary\Module::class,
        'path' => APP_PATH . '/modules/Salary/Module.php',
        'metadata'  => [
            'controllersNamespace' => 'App\Modules\Salary\Controllers',
        ],
    ],

    'AccessData' => [
        'className' => App\Modules\AccessData\Module::class,
        'path' => APP_PATH . '/modules/AccessData/Module.php',
        'metadata'  => [
            'controllersNamespace' => 'App\Modules\AccessData\Controllers',
        ],
    ],

    'transfer' => [
        'className' => App\Modules\Transfer\Module::class,
        'path' => APP_PATH . '/modules/Transfer/Module.php',
        'metadata'  => [
            'controllersNamespace' => 'App\Modules\Transfer\Controllers',
        ],
    ],
    'job_transfer' => [
        'className' => App\Modules\JobTransfer\Module::class,
        'path' => APP_PATH . '/modules/JobTransfer/Module.php',
        'metadata'  => [
            'controllersNamespace' => 'App\Modules\JobTransfer\Controllers',
        ],
    ],

    'training' => [
        'className' => App\Modules\Training\Module::class,
        'path' => APP_PATH . '/modules/Training/Module.php',
        'metadata'  => [
            'controllersNamespace' => 'App\Modules\Training\Controllers',
        ],
    ],

    'CrmQuotation' => [
        'className' => App\Modules\CrmQuotation\Module::class,
        'path' => APP_PATH . '/modules/CrmQuotation/Module.php',
        'metadata'  => [
            'controllersNamespace' => 'App\Modules\CrmQuotation\Controllers',
        ],

    ],


    'rpc' => [
        'className' => App\Modules\Rpc\Module::class,
        'path' => APP_PATH . '/modules/Rpc/Module.php',
        'metadata'  => [
            'controllersNamespace' => 'App\Modules\Rpc\Controllers',
        ],
    ],


    'reserve' => [
        'className' => App\Modules\ReserveFund\Module::class,
        'path' => APP_PATH . '/modules/ReserveFund/Module.php',
        'metadata'  => [
            'controllersNamespace' => 'App\Modules\ReserveFund\Controllers',
        ],
    ],


    'bank_flow' => [
        'className' => App\Modules\BankFlow\Module::class,
        'path' => APP_PATH . '/modules/BankFlow/Module.php',
        'metadata'  => [
            'controllersNamespace' => 'App\Modules\BankFlow\Controllers',
        ],
    ],


    'pay' => [
        'className' => App\Modules\Pay\Module::class,
        'path' => APP_PATH . '/modules/Pay/Module.php',
        'metadata'  => [
            'controllersNamespace' => 'App\Modules\Pay\Controllers',
        ],
    ],

    'shop' => [
        'className' => App\Modules\Shop\Module::class,
        'path' => APP_PATH . '/modules/Shop/Module.php',
        'metadata'  => [
            'controllersNamespace' => 'App\Modules\Shop\Controllers',
        ],
    ],

    'AccidentReport' => [
        'className' => App\Modules\AccidentReport\Module::class,
        'path' => APP_PATH . '/modules/AccidentReport/Module.php',
        'metadata'  => [
            'controllersNamespace' => 'App\Modules\AccidentReport\Controllers',
        ],
    ],

    'withholding_tax' => [
        'className' => App\Modules\WithholdingTax\Module::class,
        'path' => APP_PATH . '/modules/WithholdingTax/Module.php',
        'metadata'  => [
            'controllersNamespace' => 'App\Modules\WithholdingTax\Controllers',
        ],
    ],

    'material' => [
        'className' => App\Modules\Material\Module::class,
        'path' => APP_PATH . '/modules/Material/Module.php',
        'metadata'  => [
            'controllersNamespace' => 'App\Modules\Material\Controllers',
        ],
    ],

    'workflow_management' => [
        'className' => App\Modules\WorkflowManagement\Module::class,
        'path' => APP_PATH . '/modules/WorkflowManagement/Module.php',
        'metadata'  => [
            'controllersNamespace' => 'App\Modules\WorkflowManagement\Controllers',
        ],
    ],
    'school' => [
        'className' => App\Modules\School\Module::class,
        'path' => APP_PATH . '/modules/School/Module.php',
        'metadata'  => [
            'controllersNamespace' => 'App\Modules\School\Controllers',
        ],
    ],
    'Extinguisher' => [
        'className' => App\Modules\Extinguisher\Module::class,
        'path' => APP_PATH . '/modules/Extinguisher/Module.php',
        'metadata'  => [
            'controllersNamespace' => 'App\Modules\Extinguisher\Controllers',
        ],
    ],

    'sap_manage' => [
        'className' => App\Modules\SapManage\Module::class,
        'path' => APP_PATH . '/modules/SapManage/Module.php',
        'metadata'  => [
            'controllersNamespace' => 'App\Modules\SapManage\Controllers',
        ],
    ],

    'kpi' => [
        'className' => App\Modules\Kpi\Module::class,
        'path' => APP_PATH . '/modules/Kpi/Module.php',
        'metadata'  => [
            'controllersNamespace' => 'App\Modules\Kpi\Controllers',
        ],
    ],

    'setting' => [
        'className' => App\Modules\Setting\Module::class,
        'path' => APP_PATH . '/modules/Setting/Module.php',
        'metadata'  => [
            'controllersNamespace' => 'App\Modules\Setting\Controllers',
        ],
    ],

    'talent_review' => [
        'className' => App\Modules\TalentReview\Module::class,
        'path' => APP_PATH . '/modules/TalentReview/Module.php',
        'metadata'  => [
            'controllersNamespace' => 'App\Modules\TalentReview\Controllers',
        ],
    ],
    'cheque' => [
        'className' => App\Modules\Cheque\Module::class,
        'path' => APP_PATH . '/modules/Cheque/Module.php',
        'metadata'  => [
            'controllersNamespace' => 'App\Modules\Cheque\Controllers',
        ],
    ],

    'administration_ticket' => [
        'className' => App\Modules\AdministrationTicket\Module::class,
        'path' => APP_PATH . '/modules/AdministrationTicket/Module.php',
        'metadata'  => [
            'controllersNamespace' => 'App\Modules\AdministrationTicket\Controllers',
        ],
    ],

    'third' => [
        'className' => App\Modules\Third\Module::class,
        'path' => APP_PATH . '/modules/Third/Module.php',
        'metadata'  => [
            'controllersNamespace' => 'App\Modules\Third\Controllers',
        ],
    ],

    'asset_work_order' => [
        'className' => App\Modules\AssetWorkOrder\Module::class,
        'path' => APP_PATH . '/modules/AssetWorkOrder/Module.php',
        'metadata'  => [
            'controllersNamespace' => 'App\Modules\AssetWorkOrder\Controllers',
        ],
    ],

    'salary_deduct' => [
        'className' => App\Modules\SalaryDeduct\Module::class,
        'path' => APP_PATH . '/modules/SalaryDeduct/Module.php',
        'metadata'  => [
            'controllersNamespace' => 'App\Modules\SalaryDeduct\Controllers',
        ],
    ],
    //法务管理  新增modules 【改动1】
    'legal_affairs' => [
        'className' => App\Modules\LegalAffairs\Module::class,
        'path' => APP_PATH . '/modules/LegalAffairs/Module.php',
        'metadata'  => [
            'controllersNamespace' => 'App\Modules\LegalAffairs\Controllers',
        ],
    ],
    //资金管理
    'funds_management' => [
        'className' => App\Modules\FundsManagement\Module::class,
        'path' => APP_PATH . '/modules/FundsManagement/Module.php',
        'metadata'  => [
            'controllersNamespace' => 'App\Modules\FundsManagement\Controllers',
        ],
    ],
    //仓库管理
    'warehouse' => [
        'className' => App\Modules\Warehouse\Module::class,
        'path' => APP_PATH . '/modules/Warehouse/Module.php',
        'metadata'  => [
            'controllersNamespace' => 'App\Modules\Warehouse\Controllers',
        ],
    ],
    //代理支付
    'agency_payment' => [
        'className' => App\Modules\AgencyPayment\Module::class,
        'path' => APP_PATH . '/modules/AgencyPayment/Module.php',
        'metadata'  => [
            'controllersNamespace' => 'App\Modules\AgencyPayment\Controllers',
        ],
    ],

    //批量审批加班
    'overtime' => [
        'className' => App\Modules\Overtime\Module::class,
        'path' => APP_PATH . '/modules/Overtime/Module.php',
        'metadata'  => [
            'controllersNamespace' => 'App\Modules\Overtime\Controllers',
        ],
    ],

    //批量审批加班(外协)
    'osovertime' => [
        'className' => App\Modules\Osovertime\Module::class,
        'path' => APP_PATH . '/modules/Osovertime/Module.php',
        'metadata'  => [
            'controllersNamespace' => 'App\Modules\Osovertime\Controllers',
        ],
    ],

    'sim_manage' => [
        'className' => App\Modules\SimManage\Module::class,
        'path' => APP_PATH . '/modules/SimManage/Module.php',
        'metadata'  => [
            'controllersNamespace' => 'App\Modules\SimManage\Controllers',
        ],
    ],

    //电子发票
    'invoice' => [
        'className' => App\Modules\Invoice\Module::class,
        'path'      => APP_PATH . '/modules/Invoice/Module.php',
        'metadata'  => [
            'controllersNamespace' => 'App\Modules\Invoice\Controllers',
        ],
    ],

    //纸质单据
    'paper_document' => [
        'className' => App\Modules\PaperDocument\Module::class,
        'path'      => APP_PATH . '/modules/PaperDocument/Module.php',
        'metadata'  => [
            'controllersNamespace' => 'App\Modules\PaperDocument\Controllers',
        ],
    ],
];
