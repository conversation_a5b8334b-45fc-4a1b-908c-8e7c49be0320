<?php

use Phalcon\Mvc\Router;

$router = new Router();

//多模块路由设置
$router->add('/:module/:controller/:action/:params', [
    'module' => 1,
    'controller' => 2,
    'action' => 3,
    'params' => 4,
]);

//多模块路由设置
$router->add('/school/:controller/:action/:params', [
    'module' => 'school',
    'controller' => 'Call',
    'action' => 'entrance',
    'school_controller' => 1,
    'school_action' => 2,
    'params' => 3,
]);


// 运维健康检查
$router->add(
    '/healthy/:action',
    [
        'module'     => 'common',
        'controller' => 'Healthy',
        'action'     => 1,
    ]
);

$router->handle();

return $router;
