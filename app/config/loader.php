<?php

$loader = new \Phalcon\Loader();
$config = $di->getConfig();

/**
 * We're a registering a set of directories taken from the configuration file
 */
$loader->registerNamespaces(
    [
        'App\Library' => $config->application->libraryDir,
        'App\Traits' => $config->application->traitDir,
        'App\Plugins' => $config->application->pluginsDir,
        'App\Controllers' => $config->application->controllersDir,
        'App\Models' => $config->application->modelsDir,
        'App\Util' => $config->application->utilDir,
        'App\Repository' => $config->application->repositoryDir,
        'App\Modules\User\Controllers' => $config->application->modulesDir . 'User/controllers/',
        'App\Modules\User\Models'      => $config->application->modulesDir . 'User/models/',
        'App\Modules\User\Services'    => $config->application->modulesDir . 'User/services/',
        'App\Modules\Workflow\Controllers' =>  $config->application->modulesDir . 'Workflow/controllers/',
        'App\Modules\Workflow\Models'      =>  $config->application->modulesDir . 'Workflow/models/',
        'App\Modules\Workflow\Services' =>  $config->application->modulesDir . 'Workflow/services/',
        'App\Modules\Common\Controllers' => $config->application->modulesDir . 'Common/controllers/',
        'App\Modules\Common\Models'      =>  $config->application->modulesDir . 'Common/models/',
        'App\Modules\Common\Services'      =>  $config->application->modulesDir . 'Common/services/',
        'App\Modules\Contract\Controllers' => $config->application->modulesDir . 'Contract/controllers/',
        'App\Modules\Contract\Models' => $config->application->modulesDir . 'Contract/models/',
        'App\Modules\Contract\Services' => $config->application->modulesDir . 'Contract/services/',
        'App\Modules\Vendor\Controllers' => $config->application->modulesDir . 'Vendor/controllers/',
        'App\Modules\Vendor\Models' => $config->application->modulesDir . 'Vendor/models/',
        'App\Modules\Vendor\Services' => $config->application->modulesDir . 'Vendor/services/',
        'App\Modules\Asset\Controllers' => $config->application->modulesDir . 'Asset/controllers/',
        'App\Modules\Asset\Models' => $config->application->modulesDir . 'Asset/models/',
        'App\Modules\Asset\Services' => $config->application->modulesDir . 'Asset/services/',
        'App\Modules\Wms\Controllers' => $config->application->modulesDir . 'Wms/controllers/',
        'App\Modules\Wms\Models' => $config->application->modulesDir . 'Wms/models/',
        'App\Modules\Wms\Services' => $config->application->modulesDir . 'Wms/services/',

        'App\Modules\Loan\Controllers' => $config->application->modulesDir . 'Loan/controllers/',
        'App\Modules\Loan\Models' => $config->application->modulesDir . 'Loan/models/',
        'App\Modules\Loan\Services' => $config->application->modulesDir . 'Loan/services/',

        'App\Modules\Share\Controllers' => $config->application->modulesDir . 'Share/controllers/',
        'App\Modules\Share\Models' => $config->application->modulesDir . 'Share/models/',
        'App\Modules\Share\Services' => $config->application->modulesDir . 'Share/services/',

        'App\Modules\Organization\Controllers' => $config->application->modulesDir . 'Organization/controllers/',
        'App\Modules\Organization\Models' => $config->application->modulesDir . 'Organization/models/',
        'App\Modules\Organization\Services' => $config->application->modulesDir . 'Organization/services/',

        'App\Modules\Ticket\Controllers' => $config->application->modulesDir . 'Ticket/controllers/',
        'App\Modules\Ticket\Models' => $config->application->modulesDir . 'Ticket/models/',
        'App\Modules\Ticket\Services' => $config->application->modulesDir . 'Ticket/services/',

        'App\Modules\Purchase\Controllers' => $config->application->modulesDir . 'Purchase/controllers/',
        'App\Modules\Purchase\Models' => $config->application->modulesDir . 'Purchase/models/',
        'App\Modules\Purchase\Services' => $config->application->modulesDir . 'Purchase/services/',

        'App\Modules\Reimbursement\Controllers' => $config->application->modulesDir . 'Reimbursement/controllers/',
        'App\Modules\Reimbursement\Models' => $config->application->modulesDir . 'Reimbursement/models/',
        'App\Modules\Reimbursement\Services' => $config->application->modulesDir . 'Reimbursement/services/',

        'App\Modules\Wages\Controllers' => $config->application->modulesDir . 'Wages/controllers/',
        'App\Modules\Wages\Models' => $config->application->modulesDir . 'Wages/models/',
        'App\Modules\Wages\Services' => $config->application->modulesDir . 'Wages/services/',

        'App\Modules\Hc\Controllers' => $config->application->modulesDir . 'Hc/controllers/',
        'App\Modules\Hc\Models' => $config->application->modulesDir . 'Hc/models/',
        'App\Modules\Hc\Services' => $config->application->modulesDir . 'Hc/services/',

        'App\Modules\Ledger\Controllers' => $config->application->modulesDir . 'Ledger/controllers/',
        'App\Modules\Ledger\Models' => $config->application->modulesDir . 'Ledger/models/',
        'App\Modules\Ledger\Services' => $config->application->modulesDir . 'Ledger/services/',


        'App\Modules\Payment\Controllers' => $config->application->modulesDir . 'Payment/controllers/',
        'App\Modules\Payment\Models' => $config->application->modulesDir . 'Payment/models/',
        'App\Modules\Payment\Services' => $config->application->modulesDir . 'Payment/services/',


        'App\Modules\ReserveFund\Controllers' => $config->application->modulesDir . 'ReserveFund/controllers/',
        'App\Modules\ReserveFund\Models' => $config->application->modulesDir . 'ReserveFund/models/',
        'App\Modules\ReserveFund\Services' => $config->application->modulesDir . 'ReserveFund/services/',


        'App\Modules\Budget\Controllers' => $config->application->modulesDir . 'Budget/controllers/',
        'App\Modules\Budget\Models' => $config->application->modulesDir . 'Budget/models/',
        'App\Modules\Budget\Services' => $config->application->modulesDir . 'Budget/services/',

        'App\Modules\OrdinaryPayment\Controllers' => $config->application->modulesDir . 'OrdinaryPayment/controllers/',
        'App\Modules\OrdinaryPayment\Models' => $config->application->modulesDir . 'OrdinaryPayment/models/',
        'App\Modules\OrdinaryPayment\Services' => $config->application->modulesDir . 'OrdinaryPayment/services/',

        'App\Modules\Deposit\Controllers' => $config->application->modulesDir . 'Deposit/controllers/',
        'App\Modules\Deposit\Models' => $config->application->modulesDir . 'Deposit/models/',
        'App\Modules\Deposit\Services' => $config->application->modulesDir . 'Deposit/services/',

        'App\Modules\Salary\Controllers' => $config->application->modulesDir . 'Salary/controllers/',
        'App\Modules\Salary\Models' => $config->application->modulesDir . 'Salary/models/',
        'App\Modules\Salary\Services' => $config->application->modulesDir . 'Salary/services/',

        'App\Modules\AccessData\Controllers' => $config->application->modulesDir . 'AccessData/controllers/',
        'App\Modules\AccessData\Models' => $config->application->modulesDir . 'AccessData/models/',
        'App\Modules\AccessData\Services' => $config->application->modulesDir . 'AccessData/services/',

        'App\Modules\Transfer\Controllers' => $config->application->modulesDir . 'Transfer/controllers/',
        'App\Modules\Transfer\Models' => $config->application->modulesDir . 'Transfer/models/',
        'App\Modules\Transfer\Services' => $config->application->modulesDir . 'Transfer/services/',

        'App\Modules\JobTransfer\Controllers' => $config->application->modulesDir . 'JobTransfer/controllers/',
        'App\Modules\JobTransfer\Models' => $config->application->modulesDir . 'JobTransfer/models/',
        'App\Modules\JobTransfer\Services' => $config->application->modulesDir . 'JobTransfer/services/',

        'App\Modules\Training\Controllers' => $config->application->modulesDir . 'Training/controllers/',
        'App\Modules\Training\Models' => $config->application->modulesDir . 'Training/models/',
        'App\Modules\Training\Services' => $config->application->modulesDir . 'Training/services/',

        'App\Modules\CrmQuotation\Controllers' => $config->application->modulesDir . 'CrmQuotation/controllers/',
        'App\Modules\CrmQuotation\Models' => $config->application->modulesDir . 'CrmQuotation/models/',
        'App\Modules\CrmQuotation\Services' => $config->application->modulesDir . 'CrmQuotation/services/',

        'App\Modules\Rpc\Controllers' => $config->application->modulesDir . 'Rpc/controllers/',
        'App\Modules\Rpc\Models' => $config->application->modulesDir . 'Rpc/models/',
        'App\Modules\Rpc\Services' => $config->application->modulesDir . 'Rpc/services/',

        'App\Modules\BankFlow\Controllers' => $config->application->modulesDir . 'BankFlow/controllers/',
        'App\Modules\BankFlow\Models' => $config->application->modulesDir . 'BankFlow/models/',
        'App\Modules\BankFlow\Services' => $config->application->modulesDir . 'BankFlow/services/',

        'App\Modules\Pay\Controllers' => $config->application->modulesDir . 'Pay/controllers/',
        'App\Modules\Pay\Models' => $config->application->modulesDir . 'Pay/models/',
        'App\Modules\Pay\Services' => $config->application->modulesDir . 'Pay/services/',

        'App\Modules\AccidentReport\Controllers' => $config->application->modulesDir . 'AccidentReport/controllers/',
        'App\Modules\AccidentReport\Models' => $config->application->modulesDir . 'AccidentReport/models/',
        'App\Modules\AccidentReport\Services' => $config->application->modulesDir . 'AccidentReport/services/',

        'App\Modules\Shop\Controllers' => $config->application->modulesDir . 'Shop/controllers/',
        'App\Modules\Shop\Models' => $config->application->modulesDir . 'Shop/models/',
        'App\Modules\Shop\Services' => $config->application->modulesDir . 'Shop/services/',

        'App\Modules\Material\Controllers' => $config->application->modulesDir . 'Material/controllers/',
        'App\Modules\Material\Models' => $config->application->modulesDir . 'Material/models/',
        'App\Modules\Material\Services' => $config->application->modulesDir . 'Material/services/',

        'App\Modules\WithholdingTax\Controllers' => $config->application->modulesDir . 'WithholdingTax/controllers/',
        'App\Modules\WithholdingTax\Models' => $config->application->modulesDir . 'WithholdingTax/models/',
        'App\Modules\WithholdingTax\Services' => $config->application->modulesDir . 'WithholdingTax/services/',

        'App\Modules\WorkflowManagement\Controllers' => $config->application->modulesDir . 'WorkflowManagement/controllers/',
        'App\Modules\WorkflowManagement\Models' => $config->application->modulesDir . 'WorkflowManagement/models/',
        'App\Modules\WorkflowManagement\Services' => $config->application->modulesDir . 'WorkflowManagement/services/',

        'App\Modules\School\Controllers' => $config->application->modulesDir . 'School/controllers/',
        'App\Modules\School\Models' => $config->application->modulesDir . 'School/models/',
        'App\Modules\School\Services' => $config->application->modulesDir . 'School/services/',

        'App\Modules\Extinguisher\Controllers' => $config->application->modulesDir . 'Extinguisher/controllers/',
        'App\Modules\Extinguisher\Models' => $config->application->modulesDir . 'Extinguisher/models/',
        'App\Modules\Extinguisher\Services' => $config->application->modulesDir . 'Extinguisher/services/',

        'App\Modules\SapManage\Controllers' => $config->application->modulesDir . 'SapManage/controllers/',
        'App\Modules\SapManage\Models' => $config->application->modulesDir . 'SapManage/models/',
        'App\Modules\SapManage\Services' => $config->application->modulesDir . 'SapManage/services/',

        'App\Modules\Kpi\Controllers' => $config->application->modulesDir . 'Kpi/controllers/',
        'App\Modules\Kpi\Models' => $config->application->modulesDir . 'Kpi/models/',
        'App\Modules\Kpi\Services' => $config->application->modulesDir . 'Kpi/services/',

        'App\Modules\Setting\Controllers' => $config->application->modulesDir . 'Setting/controllers/',
        'App\Modules\Setting\Models' => $config->application->modulesDir . 'Setting/models/',
        'App\Modules\Setting\Services' => $config->application->modulesDir . 'Setting/services/',

        'App\Modules\TalentReview\Controllers' => $config->application->modulesDir . 'TalentReview/controllers/',
        'App\Modules\TalentReview\Models' => $config->application->modulesDir . 'TalentReview/models/',
        'App\Modules\TalentReview\Services' => $config->application->modulesDir . 'TalentReview/services/',

        'App\Modules\Cheque\Controllers' => $config->application->modulesDir . 'Cheque/controllers/',
        'App\Modules\Cheque\Models' => $config->application->modulesDir . 'Cheque/models/',
        'App\Modules\Cheque\Services' => $config->application->modulesDir . 'Cheque/services/',

        'App\Modules\AdministrationTicket\Controllers' => $config->application->modulesDir . 'AdministrationTicket/controllers/',
        'App\Modules\AdministrationTicket\Models' => $config->application->modulesDir . 'AdministrationTicket/models/',
        'App\Modules\AdministrationTicket\Services' => $config->application->modulesDir . 'AdministrationTicket/services/',

        'App\Modules\Third\Controllers' => $config->application->modulesDir . 'Third/controllers/',
        'App\Modules\Third\Services' => $config->application->modulesDir . 'Third/services/',

        'App\Modules\AssetWorkOrder\Controllers' => $config->application->modulesDir . 'AssetWorkOrder/controllers/',
        'App\Modules\AssetWorkOrder\Services' => $config->application->modulesDir . 'AssetWorkOrder/services/',

        'App\Modules\SalaryDeduct\Controllers' => $config->application->modulesDir . 'SalaryDeduct/controllers/',
        'App\Modules\SalaryDeduct\Services' => $config->application->modulesDir . 'SalaryDeduct/services/',
         //新增modules 【改动2】
        'App\Modules\LegalAffairs\Controllers' => $config->application->modulesDir . 'LegalAffairs/controllers/',
        'App\Modules\LegalAffairs\Services' => $config->application->modulesDir . 'LegalAffairs/services/',
        //资金管理
        'App\Modules\FundsManagement\Controllers' => $config->application->modulesDir . 'FundsManagement/controllers/',
        'App\Modules\FundsManagement\Services' => $config->application->modulesDir . 'FundsManagement/services/',
        //仓库管理
        'App\Modules\Warehouse\Controllers' => $config->application->modulesDir . 'Warehouse/controllers/',
        'App\Modules\Warehouse\Services' => $config->application->modulesDir . 'Warehouse/services/',
        //代理支付
        'App\Modules\AgencyPayment\Controllers' => $config->application->modulesDir . 'AgencyPayment/controllers/',
        'App\Modules\AgencyPayment\Services' => $config->application->modulesDir . 'AgencyPayment/services/',

        //批量审批加班
        'App\Modules\Overtime\Controllers' => $config->application->modulesDir . 'Overtime/controllers/',
        'App\Modules\Overtime\Services'    => $config->application->modulesDir . 'Overtime/services/',
        'App\Modules\Overtime\Models'      => $config->application->modulesDir . 'Overtime/models/',

        //批量审批加班(外协)
        'App\Modules\Osovertime\Controllers' => $config->application->modulesDir . 'Osovertime/controllers/',
        'App\Modules\Osovertime\Services'    => $config->application->modulesDir . 'Osovertime/services/',
        'App\Modules\Osovertime\Models'      => $config->application->modulesDir . 'Osovertime/models/',

        //SIM卡管理
        'App\Modules\SimManage\Controllers' => $config->application->modulesDir . 'SimManage/controllers/',
        'App\Modules\SimManage\Services'    => $config->application->modulesDir . 'SimManage/services/',
        'App\Modules\SimManage\Models'      => $config->application->modulesDir . 'SimManage/models/',

        //电子发票管理
        'App\Modules\Invoice\Controllers' => $config->application->modulesDir . 'Invoice/controllers/',
        'App\Modules\Invoice\Services'    => $config->application->modulesDir . 'Invoice/services/',
        'App\Modules\Invoice\Models'      => $config->application->modulesDir . 'Invoice/models/',

        //纸质单据管理
        'App\Modules\PaperDocument\Controllers' => $config->application->modulesDir . 'PaperDocument/controllers/',
        'App\Modules\PaperDocument\Services'    => $config->application->modulesDir . 'PaperDocument/services/',
        'App\Modules\PaperDocument\Models'      => $config->application->modulesDir . 'PaperDocument/models/',

        //合同差异化配置
        'App\Modules\Contract\Services\My' => $config->application->modulesDir . 'Contract/services/my',
    ]
)->registerDirs(
    [
        $config->application->tasksDir,
        $config->application->utilDir,
    ]
)->registerFiles(
    [
        $config->application->libraryDir . '/function.php',
    ]
)->register();
