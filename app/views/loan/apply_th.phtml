<!doctype HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=no">
    <style>
        body {
            font: 3.2mm/1.2 Tahoma, sans-serif;
            color: #000;
            -webkit-print-color-adjust: exact;
        }

        body, ul, p {
            margin: 0;
            padding: 0;
        }

        ul, li {
            list-style: none;
        }

        .clearfix::after {
            display: block;
            content: "";
            clear: both;
        }

        .break-after {
            page-break-after: always;
            page-break-inside: avoid;
        }

        .tal {
            text-align: left !important;
        }

        .table {
            width: 100%;
            border-left: .5mm solid #666;
            border-right: .5mm solid #666;
            border-collapse: collapse;
            border-spacing: 0;
        }

        .table th, .table td {
            border: .3mm solid #999;
            padding: 2mm;
            text-align: center;
            word-break: break-all;
        }

        .table .bt {
            border-top: .5mm solid #666;
        }

        .table .bbn {
            border-bottom: none;
        }

        .table .cell-title {
            font-weight: 600;
            text-align: left;
        }

        .borrow-wrap {
            width: 210mm;
            height: 297mm;
            padding: 4mm;
            margin-bottom: 4mm;
            box-sizing: border-box;
        }

        .checkbox-item {
            display: inline-block;
            width: 2.5mm;
            height: 2.5mm;
            margin: 0 3mm;
            border: .3mm solid #999;
            vertical-align: bottom;
        }

        .checkbox-item.selected {
            background-color: #000;
            border-color: #000;
        }

        .signature {
            margin-top: 5mm;
            padding-left: 60%;
        }

        @font-face {
            font-family: mFont;
            src: url("<?php echo BASE_PATH.'/public/fonts/THSarabun Bold.ttf' ;?>");
        }
        body {
            font-family: mFont;
        }
    </style>
</head>

<body>
<div class="borrow-wrap">
    <table class="table">
        <tr style="font-size:3.4mm; font-weight:600;">
            <td colspan="2" class="tal bt">ใบยื่นขอเงินกู้พนักงาน</td>
            <td class="bt">เลขที่เงินกู้</td>
            <td class="bt"><?php echo $lno ?></td>
        </tr>
        <tr>
            <td class="bt">หัวข้อ</td>
            <td colspan="3" class="bt"><?php echo $lname ?></td>
        </tr>
        <tr>
            <td style="width:20%;">ชื่อ-นามสกุล</td>
            <td style="width:30%;"><?php echo $create_name ?></td>
            <td style="width:20%;">รหัสพนักงาน</td>
            <td style="width:30%;"><?php echo $create_id ?></td>
        </tr>
        <tr>
            <td>เบอร์โทรศัพท์</td>
            <td><?php echo $create_phone ?></td>
            <td>อีเมล</td>
            <td><?php echo $create_email ?></td>
        </tr>
        <tr>
            <td>บริษัท</td>
            <td><?php echo $create_company_name ?></td>
            <td>เวลายื่นคำร้อง</td>
            <td><?php echo $create_date ?></td>
        </tr>
        <tr>
            <td>แผนก</td>
            <td><?php echo $create_display_department_name ?></td>
            <td>ตำแหน่ง</td>
            <td><?php echo $create_job_title_name ?></td>
        </tr>
        <tr>
            <td>ค่าใช้จ่ายของต้นทุนส่วนกลาง</td>
            <td colspan="3" class="bt"><?php echo $cost_center_name ?></td>
        </tr>
        <tr>
            <td>สาเหตุในการกู้</td>
            <td colspan="3" class="bt">
                <?php
                $typeArr = ["","ค่าเดินทาง","จัดซื้อ","ทำงานนอกสถานที่ระยะยาว","กิจกรรมในบริษัท","อื่นๆ","Outsource"];
                echo $typeArr[$type];
                ?>
            </td>
        </tr>
        <tr>
            <td>วิธีการรับเงิน</td>
            <td>
                <?php
                $payTypeArr =["","เงินสด","โอนเงินทางธนาคาร"];
                echo $payTypeArr[$pay_type];
                ?>
            </td>
            <td>สกุลเงิน</td>
            <td>
                <?php
                echo $currency_text;
                ?>
            </td>
        </tr>
    </table>

    <table class="table">
        <?php
            if($pay_type==2){
                echo '
                    <tr>
                        <td colspan="6" class="cell-title bt">ข้อมูลธนาคาร</td>
                    </tr>
                    <tr>
                        <td style="width:15%;">ชื่อบัญชีผู้รับเงิน</td>
                        <td style="width:20%;">'.$bank["name"].'</td>
                        <td style="width:15%;">เลขที่บัญชี</td>
                        <td style="width:20%;">'.$bank["account"].'</td>
                        <td style="width:15%;">บัญชีธนาคาร</td>
                        <td style="width:15%;">'.$bank["bank_type"].'</td>
                    </tr>';
            }
            ?>
        <tr>
            <td colspan="6" class="cell-title bt">รายละเอียด</td>
        </tr>
        <tr>
            <td>ชื่อรายการ</td>
            <td colspan="2"><?php echo $event_name ?></td>
            <td>ยอดเงิน</td>
            <td colspan="2"><?php echo number_format($amount, 2) ?></td>
        </tr>
        <tr>
            <td>วันที่คาดว่าจะเสร็จสิ้น</td>
            <td colspan="2"><?php echo $finished_at ?></td>
            <td>วันที่คาดว่าจะชำระหนี้</td>
            <td colspan="2"><?php echo $back_at ?></td>
        </tr>
        <tr>
            <td>รายละเอียดการขอเงินกู้</td>
            <td colspan="5"><?php echo $event_info ?></td>
        </tr>
        <?php
            if(!empty($travel)){
                echo
                '<tr>
                    <td>《ใบเบิกทำงานนอกสถานที่》</td>
                    <td colspan="5">'.$travel['serial_no'].'</td>
                </tr>';
            }
        ?>
        <?php

        if(!empty($pay))
        {
            $payTypeArr = ["","เงินสด","TTB","SCB"];
            $signTypeArr= ["","ใช่","ไม่ใช่"];


        echo '
        <tr>
            <td colspan="6" class="cell-title bt">ข้อมูลการชำระเงิน</td>
        </tr>
        <tr>
            <td>ช่องทางการชำระเงิน</td>
            <td colspan="5" class="tal">
                '.$payTypeArr[$pay['pay_type']].'
            </td>
        </tr>
        <tr>
            <td>ธนาคารที่ชำระเงิน</td>
            <td>'.$pay['pay_bank_name'].'</td>
            <td>บัญชีที่ชำระเงิน</td>
            <td>'.$pay['pay_bank_account'].'</td>
            <td>วันที่ชำระเงิน</td>
            <td>'.$pay['pay_date'].'</td>
        </tr>
        <tr>
            <td>ผู้กู้ยืนยันการลงชื่อหรือไม่</td>
            <td>
                '.$signTypeArr[$pay['is_sign']].'
            </td>
            <td>คนเซ็นรับ</td>
            <td>'.$pay['sign_name'].'</td>
            <td>วันที่รับมอบ</td>
            <td>'.$pay['sign_date'].'</td>
        </tr>
        <tr>
            <td class="bbn">หมายเหตุ</td>
            <td colspan="5" class="bbn">'.$pay['mark'].'</td>
        </tr>';
        }?>
    </table>

    <table class="table" style="border-bottom:.5mm solid #666;">
        <tr>
            <td colspan="6" class="cell-title bt">ขั้นตอนอนุมัติ</td>
        </tr>
        <tr>
            <td style="width:15%;">ลำดับขั้นตอน</td>
            <td style="width:20%;">เวลาเสร็จสิ้น</td>
            <td style="width:15%;">ID ผู้ดำเนินการ</td>
            <td style="width:20%;">ชื่อผู้ดำเนินการ</td>
            <td style="width:15%;">ผลการดำเนินการ</td>
            <td style="width:15%;">จัดการความคิดเห็น</td>
        </tr>
        <?php
        if(!empty($auth_logs)){

            $auth_logs = array_reverse($auth_logs);
            foreach ($auth_logs as $k=>$item){
                echo '
                         <tr>
                            <td>ผู้อนุมัติคนที่'.($k+1).'</td>
                            <td>'.$item['audit_at_datetime'].'</td>
                            <td>'.$item['staff_id'].'</td>
                            <td>'.$item['staff_name'].'</td>
                            <td>'.$item['action_name'].'</td>
                            <td>'.$item['info'].'</td>
                        </tr>
                    ';
            }
        }
        ?>
        <tr>
            <td>ลงชื่อในหนังสือรับรองหรือไม่</td>
            <td colspan="5" class="tal">
                ใช่
            </td>
        </tr>
        <tr>
            <td colspan="6" class="cell-title bt" style="text-align:center;">หนังสือรับรอง</td>
        </tr>
        <tr>
            <td colspan="6" class="tal" style="line-height:1.6;">
                <?php echo $loan_download_template_content; ?>
                <div class="signature">
                    <span>ลายเซ็นผู้ค้ำประกัน:</span>
                    <span><?php echo $create_name ?></span>
                </div>
            </td>
        </tr>
    </table>
</div>
</body>
</html>