<!doctype HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=no">
    <style>
        body {
            font: 3.2mm/1.2 Tahoma, sans-serif;
            color: #000;
            -webkit-print-color-adjust: exact;
        }

        body, ul, p {
            margin: 0;
            padding: 0;
        }

        ul, li {
            list-style: none;
        }

        .clearfix::after {
            display: block;
            content: "";
            clear: both;
        }

        .break-after {
            page-break-after: always;
            page-break-inside: avoid;
        }

        .tal {
            text-align: left !important;
        }

        .table {
            width: 100%;
            border-left: .5mm solid #666;
            border-right: .5mm solid #666;
            border-collapse: collapse;
            border-spacing: 0;
        }

        .table th, .table td {
            border: .3mm solid #999;
            padding: 2mm;
            text-align: center;
            word-break: break-all;
        }

        .table .bt {
            border-top: .5mm solid #666;
        }

        .table .bbn {
            border-bottom: none;
        }

        .table .cell-title {
            font-weight: 600;
            text-align: left;
        }

        .borrow-wrap {
            width: 210mm;
            height: 297mm;
            padding: 4mm;
            margin-bottom: 4mm;
            box-sizing: border-box;
        }

        .checkbox-item {
            display: inline-block;
            width: 2.5mm;
            height: 2.5mm;
            margin: 0 3mm;
            border: .3mm solid #999;
            vertical-align: bottom;
        }

        .checkbox-item.selected {
            background-color: #000;
            border-color: #000;
        }

        .signature {
            margin-top: 5mm;
            padding-left: 60%;
        }
    </style>
</head>

<body>
<div class="borrow-wrap">
    <table class="table">
        <tr style="font-size:3.4mm; font-weight:600;">
            <td colspan="2" class="tal bt">员工借款申请单</td>
            <td class="bt">借款单号</td>
            <td class="bt"><?php echo $lno ?></td>
        </tr>
        <tr>
            <td class="bt">标题</td>
            <td colspan="3" class="bt"><?php echo $lname ?></td>
        </tr>
        <tr>
            <td style="width:20%;">申请人姓名</td>
            <td style="width:30%;"><?php echo $create_name ?></td>
            <td style="width:20%;">申请人工号</td>
            <td style="width:30%;"><?php echo $create_id ?></td>
        </tr>
        <tr>
            <td>申请人电话</td>
            <td><?php echo $create_phone ?></td>
            <td>申请人邮箱</td>
            <td><?php echo $create_email ?></td>
        </tr>
        <tr>
            <td>申请人所属公司</td>
            <td><?php echo $create_company_name ?></td>
            <td>发起时间</td>
            <td><?php echo $create_date ?></td>
        </tr>
        <tr>
            <td>申请人部门</td>
            <td><?php echo $create_display_department_name ?></td>
            <td>申请人职位</td>
            <td><?php echo $create_job_title_name ?></td>
        </tr>
        <tr>
            <td>费用所属成本中心</td>
            <td colspan="3" class="bt"><?php echo $cost_center_name ?></td>
        </tr>
        <tr>
            <td>借款事由</td>
            <td colspan="3" class="bt">
                <?php
                    $typeArr = ["","差旅费","采购","长期驻外","公务活动","其他","外协事项"];
                    echo $typeArr[$type];
                ?>
            </td>
        </tr>
        <tr>
            <td>付款方式</td>
            <td>
                <?php
                    $payTypeArr =["","现金","银行转账"];
                    echo $payTypeArr[$pay_type];
                ?>
            </td>
            <td>币种</td>
            <td>
                <?php
                    $currencyArr=["","泰铢","美元"];
                    echo $currencyArr[$currency];
                ?>
            </td>
        </tr>
    </table>

    <table class="table">
        <?php
            if($pay_type==2){
                echo '
                    <tr>
                        <td colspan="6" class="cell-title bt">银行信息</td>
                    </tr>
                    <tr>
                        <td style="width:15%;">收款人户名</td>
                        <td style="width:20%;">'.$bank["name"].'</td>
                        <td style="width:15%;">收款人账号</td>
                        <td style="width:20%;">'.$bank["account"].'</td>
                        <td style="width:15%;">收款人开户银行</td>
                        <td style="width:15%;">'.$bank["bank_type"].'</td>
                    </tr>';
            }
            ?>
        <tr>
            <td colspan="6" class="cell-title bt">事项说明</td>
        </tr>
        <tr>
            <td>事项名称</td>
            <td colspan="2"><?php echo $event_name ?></td>
            <td>金额</td>
            <td colspan="2"><?php echo number_format($amount,2) ?></td>
        </tr>
        <tr>
            <td>工作完成时间</td>
            <td colspan="2"><?php echo $finished_at ?></td>
            <td>预计还款时间</td>
            <td colspan="2"><?php echo $back_at ?></td>
        </tr>
        <tr>
            <td>费用使用说明及估算过程</td>
            <td colspan="5"><?php echo $event_info ?></td>
        </tr>
        <?php
            if(!empty($travel)){
                echo
                '<tr>
                    <td>《出差申请单》</td>
                    <td colspan="5">'.$travel['travel_id'].'</td>
                </tr>';
            }
        ?>
        <?php

        if(!empty($pay))
        {
            $payTypeArr = ["","现金","TTB","SCB"];

            $signTypeArr= ["","是","否"];


        echo '
        <tr>
            <td colspan="6" class="cell-title bt">支付信息</td>
        </tr>
        <tr>
            <td>付款方式</td>
            <td colspan="5" class="tal">
                '.$payTypeArr[$pay['pay_type']].'
            </td>
        </tr>
        <tr>
            <td>付款银行</td>
            <td>'.$pay['pay_bank_name'].'</td>
            <td>付款账号</td>
            <td>'.$pay['pay_bank_account'].'</td>
            <td>付款日期</td>
            <td>'.$pay['pay_date'].'</td>
        </tr>
        <tr>
            <td>借款人是否已签收</td>
            <td>
                '.$signTypeArr[$pay['is_sign']].'
            </td>
            <td>签收人</td>
            <td>'.$pay['sign_name'].'</td>
            <td>签收日期</td>
            <td>'.$pay['sign_date'].'</td>
        </tr>
        <tr>
            <td class="bbn">备注</td>
            <td colspan="5" class="bbn">'.$pay['mark'].'</td>
        </tr>';
        }?>
    </table>

    <table class="table" style="border-bottom:.5mm solid #666;">
        <tr>
            <td colspan="6" class="cell-title bt">审批流程</td>
        </tr>
        <tr>
            <td style="width:15%;">步骤名称</td>
            <td style="width:20%;">完成时间</td>
            <td style="width:15%;">处理人工号</td>
            <td style="width:20%;">处理人姓名</td>
            <td style="width:15%;">处理结果</td>
            <td style="width:15%;">处理意见</td>
        </tr>
        <tr>
            <td>审批人1</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
        <tr>
            <td>是否签保证书</td>
            <td colspan="5" class="tal">
                是
            </td>
        </tr>
        <tr>
            <td colspan="6" class="cell-title bt" style="text-align:center;">保证书</td>
        </tr>
        <tr>
            <td colspan="6" class="tal" style="line-height:1.6;">
                <p>1.借款人员同意公司在其本人不能按时偿还借款时，有权从其个人工资、福利或奖金收入中扣还，若申请人本人工资、福利或奖金收入金额不够抵扣，将继续扣除申请人上级领导人工资、福利或者奖金收入，依此类推；</p>
                <p> 1.หากผู้กู้ไม่สามารถชำระคืนเงินกู้ตรงเวลา บริษัท มีสิทธิ์หักจากค่าจ้าง เงินเดือน
                    รายได้หรือโบนัสหากผู้เบิกเงินมีรายได้ไม่พอหัก หัวหน้าผู้บังคับบัญชาจะต้องถูกหักเงินค่าจ้าง เงินเดือน
                    รายได้หรือโบนัสแทนผู้เบิกเงิน</p>
                <p>2.同意按照借款总额0.05%的日利息支付因拖欠还款导致的公司利息损失；</p>
                <p>2.พนักงานยินยอมจะชำระดอกเบี้ยหากเลยกำหนดเวลา</p>
                <p> 3.保证不将公司借款用于私人目的；</p>
                <p> 3.ต้องรับรองว่าเงินสำรองที่เบิกไปนั้นไม่ได้ไปใช้ในกิจธุระส่วนตัว</p>
                <p>4. 保证随时接受公司财务部门的核查；</p>
                <p>4. ต้องยินยอมให้แผนกบัญชีตรวจสอบเงินสำรองที่เบิกไปทุกเวลาที่ต้องการตรวจสอบ</p>
                <p>5.保证不论由于何种原因岗位调整或离职，均承诺在规定时间内向公司还清借款。</p>
                <p> 5. เพื่อรับรองว่าการลาออกไม่ว่าเหตุผลใดๆก็ตาม จะคืนเงินบริษัทภายในกำหนด</p>
                <div class="signature">
                    <span>保证人签字:</span>
                    <span><?php echo $create_name ?></span>
                </div>
            </td>
        </tr>
    </table>
</div>
</body>
</html>