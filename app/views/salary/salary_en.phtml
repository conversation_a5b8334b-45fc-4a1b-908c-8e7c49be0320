<!doctype HTML>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
	<meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=no">
	<style>
    body {
      font: 3.2mm/1.2 Tahoma, sans-serif;
      color: #000;
			-webkit-print-color-adjust: exact;
    }
    .table {
      width: 100%;
      border: .5mm solid #666;
      border-collapse: collapse;
      border-spacing: 0;
      margin-top: 5mm;
      table-layout: fixed;
    }
    .table th, .table td {
      border: .3mm solid #999;
      padding: 2mm;
      text-align: center;
      word-break: break-all;
    }
    .body-wrap {
      height: 210mm;
      width: 297mm;
      padding: 4mm;
      box-sizing: border-box;
      margin: 0 auto;
    }
    .bold {
			font-weight: 600;
    }
	</style>
</head>

<body>
  <div class="body-wrap">
    <table class="table">
      <tr>
        <td colspan="6" class="bold">Salary payment application</td>
        <td colspan="2" class="bold">Numbering</td>
        <td colspan="2"><?php echo $xzno ?></td>
      </tr>
      <tr>
        <td colspan="10" class="bold">Basic Information</td>
      </tr>
      <tr>
        <td colspan="2" class="bold">Applicant's Employee ID</td>
        <td colspan="3"><?php echo $create_id ?></td>
        <td colspan="2" class="bold">Applicant's name</td>
        <td colspan="3"><?php echo $create_name ?></td>
      </tr>
      <tr>
        <td colspan="2" class="bold">company</td>
        <td colspan="3"><?php echo $create_company_name ?></td>
        <td colspan="2" class="bold">Applicant's department</td>
        <td colspan="3"><?php echo $create_department_name ?></td>
      </tr>
      <tr>
        <td colspan="2" class="bold">Branch</td>
        <td colspan="3"><?php echo $create_store_name ?></td>
        <td colspan="2" class="bold">Applicant's center</td>
        <td colspan="3"><?php echo $create_center_code ?></td>
      </tr>
      <tr>
        <td colspan="2" class="bold">Applicant time</td>
        <td colspan="3"><?php echo $apply_date ?></td>
        <td colspan="2" class="bold">Due date</td>
        <td colspan="3"><?php echo $pay_start_date."--".$pay_end_date ?></td>
      </tr>
      <tr>
        <td colspan="10" class="bold">Payment info</td>
      </tr>
      <tr>
        <td colspan="2" class="bold">Payment method</td>
        <td colspan="3"><?php echo $pay_type_text ?></td>
        <td colspan="2" class="bold">Currency</td>
        <td colspan="3"><?php echo $currency_text ?></td>
      </tr>
      <tr>
        <td colspan="10" class="bold">Salary information</td>
      </tr>
      <tr>
        <td colspan="2" class="bold">type</td>
        <td colspan="2" class="bold">pay month</td>
        <td colspan="2" class="bold">pay period</td>
        <td colspan="2" class="bold">Company</td>
        <td colspan="2" class="bold">Amount</td>
      </tr>
      <tr>
          <td colspan="2"><?php echo $salary_type_text ?></td>
          <td colspan="2"><?php echo $salary_month ?></td>
          <td colspan="2"><?php echo $salary_start_date."--".$salary_end_date ?></td>
          <td colspan="2"><?php echo $salary_company_name ?></td>
          <td colspan="2"><?php echo number_format($salary_amount,2) ?></td>
      </tr>
      <tr>
        <td colspan="10" class="bold">Review process</td>
      </tr>
      <tr>
        <td colspan="2" class="bold">Step name</td>
        <td colspan="2" class="bold">Complete time</td>
        <td class="bold">Process Labor Employee ID</td>
        <td colspan="3" class="bold">Process Labor name</td>
        <td class="bold">Process result</td>
        <td class="bold">Handling opinions</td>
      </tr>
        <?php
        foreach ($auth_logs as $k=>$item){
            echo '<tr>';
            echo ' <td colspan="2">Approver'.($k+1).'</td>';
            echo ' <td colspan="2">'.$item['audit_at_datetime'].'</td>';
            echo ' <td>'.$item['staff_id'].'</td>';
            echo ' <td colspan="3">'.$item['staff_name'].'</td>';
            echo ' <td>'.$item['action_name'].'</td>';
            echo ' <td>'.$item['info'].'</td>';
            echo '</tr>';
        }
        ?>
        <?php
        if ($pay_status != 1) {
        ?>
        <tr>
        <td colspan="10" class="bold">Payment Information</td>
      </tr>
      <tr>
        <td colspan="2" class="bold">Payment bank</td>
        <td colspan="3"><?php echo $payer_bank ?></td>
        <td colspan="2" class="bold">Payment account</td>
        <td colspan="3"><?php echo $payer_no ?></td>
      </tr>
      <tr>
        <td colspan="2" class="bold">Payment date</td>
        <td colspan="3"><?php echo $pay_date ?></td>
        <td colspan="2" class="bold">Signatory</td>
        <td colspan="3"><?php echo $create_name ?></td>
      </tr>
      <tr>
        <td colspan="2" class="bold">Remarks</td>
        <td colspan="8"><?php echo $pay_remark ?></td>
      </tr>
            <?php
        }
        ?>
    </table>
	</div>
</body>
</html>