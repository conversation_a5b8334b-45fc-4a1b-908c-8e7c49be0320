<!doctype HTML>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
	<meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=no">
	<style>
    body {
      font: 3.2mm/1.2 Tahoma, sans-serif;
      color: #000;
			-webkit-print-color-adjust: exact;
    }
    body, ul {
      margin: 0;
      padding: 0;
    }
    ul, li {
      list-style: none;
    }
    p {
      margin: 1mm 0 0;
    }
    .clearfix::after {
      display: block;
      content: "";
      clear: both;
    }
    .break-after {
      page-break-after: always;
      page-break-inside: avoid;
    }
    .table {
      width: 100%;
      border: .5mm solid #666;
      border-collapse: collapse;
      border-spacing: 0;
    }
    .table th, .table td {
      border: .3mm solid #999;
      padding: 2mm;
      text-align: center;
      word-break: break-all;
    }
    .body-wrap {
      width: 210mm;
      height: 297mm;
      padding: 4mm;
      box-sizing: border-box;
    }
    .logo {
      float: right;
      width: 40mm;
    }
    .table-header {
      margin-top: 13mm;
      margin-bottom: 8mm;
			font-size: 5mm;
			font-weight: 600;
			text-align: center;
    }
    li {
      float: left;
      width: 50%;
      margin-bottom: 2mm;
    }
	</style>
</head>

<body>
  <div class="body-wrap">
    <div class="clearfix">
      <div style="float:left; width:100mm;">
          <p><?php if (!empty($company_name)) {
                  echo $company_name;
                  echo ' ', str_replace('_', ' ', env('company_abbreviation', ''));
              }
              ?></p>
          <p><?php echo $sap_company_address; ?></p>
          <p>Tax ID. <?php echo $sap_tax_id; ?></p>
      </div>
      <img class="logo" src="data:image/jpg;base64,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">
    </div>
    <div class="table-header" style="margin-top:30mm;">ใบคำขอการจ่ายเงิน</div>

    <ul class="clearfix">
      <li>หมายเลข：<?php echo $ppno; ?></li>
      <li>วันที่ยื่นขอ：<?php echo $apply_date; ?></li>
      <li>ชื่อ-นามสกุล：<?php echo $create_name; ?></li>
      <li>รหัสพนักงาน：<?php echo $create_id; ?></li>
      <li>แผนก：<?php echo $create_department; ?></li>
      <li>วิธีจัดซื้อ：<?php echo $method; ?></li>
        <?php
            if($is_link_pa){
                echo '<li>ใบ PR ที่เกี่ยวข้อง：'.$pano.'</li>';
            }else{
                echo '<li>เอกสารที่เกี่ยวข้อง ใบ PO：'.$pono.'</li>';
            }
        ?>
        <li>เอกสารที่เกี่ยวข้อง ใบแจ้งรับเข้าคลัง：<?php echo implode(',',array_column($inbound_no,'no')); ?></li>
        <li>ค่าใช้จ่ายของบริษัท：<?php echo $cost_company_name; ?></li>
        <li>ค่าใช้จ่ายแผนก：<?php echo $department_name; ?></li>
        <li>ค่าใช้จ่ายสาขา：<?php echo $store_id; ?></li>
      <li>ช่องทางการชำระเงิน：<?php echo $payment_method; ?></li>
      <li>สกุลเงิน：<?php echo $currency_text; ?></li>
      <li>ระยะเวลาของสินเชื่อ：<?php echo $loan_time; ?></li>
      <li>ชื่อซัพพลายเออร์：<?php echo $vendor; ?></li>
      <li>ผู้ติดต่อ ซัพพลายเออร์：<?php echo $vendor_contact; ?></li>
      <li>โทรศัพท์/โทรศัพท์มือถือ：<?php echo $vendor_phone; ?></li>
      <li>Email ซัพพลายเออร์：<?php echo $vendor_email; ?></li>
      <li>ชำระที่：<?php echo $payment_to; ?></li>
        <li>ชื่อธนาคารของซัพพลายเออร์：<?php echo $bank_name ?></li>
        <li>ชื่อบัญชีของซัพพลายเออร์：<?php echo $bank_account_name ?></li>
        <li>เลขบัญชีของซัพพลายเออร์：<?php echo $bank_no ?></li>
        <li>SWIFT CODE:<?php echo $swift_code ?></li>
      <li>หมายเหตุ：<?php echo $remark; ?></li>
    </ul>

    <table class="table" style="margin-top: 5mm;">
        <tr>
            <th>เลขที่ใบแจ้งหนี้</th>
            <th>วันที่ใบแจ้งหนี้</th>
            <th>จำนวนเงิน</th>
            <th>การจัดประเภทงบประมาณ</th>
            <th>ชื่อผลิตภัณฑ์</th>
            <th>จำนวน</th>
            <th>หน่วย</th>
            <th>ราคา/หน่วย (ไม่รวมภาษี)</th>
            <th>ราคา(ไม่รวมภาษี)</th>
            <th>จำนวนภาษี</th>
            <th>ราคารวมภาษี</th>
            <th>WHT type</th>
            <th>WHT tax rate</th>
            <th>WHT amount</th>
            <th>ยอดชำระตามจริง</th>
        </tr>


        <?php
        foreach ($receipt_v1 as $v1_val) {
            echo "<tr>
            <td>{$v1_val['ticket_number']}</td>
            <td>{$v1_val['ticket_date']}</td>
            <td>".number_format($v1_val['ticket_amount'],2)."</td>
            <td>{$v1_val['budget_text']}</td>
            <td>{$v1_val['product_name']}</td>
            <td>{$v1_val['total']}</td>
            <td>{$v1_val['unit']}</td>
            <td>".number_format($v1_val['not_tax_price'],6)."</td>
            <td>".number_format($v1_val['total_price'],2)."</td>
            <td>".number_format($v1_val['tax_ratio'],2)."</td>
            <td>".number_format($v1_val['tax_total_price'],2)."</td>
            <td>{$v1_val['wht_type_name']}</td>
            <td>{$v1_val['wht_ratio']}%</td>
            <td>".number_format($v1_val['wht_amount'],2)."</td>
            <td>".number_format($v1_val['real_amount'],2)."</td>
          </tr>";
        }
        ?>
    </table>

      <table class="table">
          <tr>
              <th colspan="5">รวม</th>
          </tr>

          <tr>
              <td>ราคารวม (ไม่รวมภาษี)</td>
              <td><?php echo number_format($not_tax_amount,2) ?></td>

              <td>Total WHT amount</td>
              <td><?php echo number_format($wht_amount,2) ?></td>
          </tr>

          <tr>
              <td>ภาษีมูลค่าเพิ่ม</td>
              <td><?php echo number_format($vat7_amount,2) ?></td>

              <td>ยอดสุทธิ</td>
              <td><?php echo number_format($real_amount,2) ?></td>
          </tr>

          <tr>
              <td>ราคารวม (รวมภาษี)</td>
              <td><?php echo number_format($amount,2) ?></td>

              <td>รวมเงินทั้งสิ้น</td>
              <td><?php echo number_format($receipt_amount,2) ?></td>
          </tr>

          <tr>
              <td></td>
              <td></td>

              <td>ยอดชำระเงินครั้งนี้</td>
              <td><?php echo number_format($cur_amount,2) ?></td>
          </tr>
      </table>

      <table class="table">
          <tr>
              <th colspan="6">ขั้นตอนการตรวจสอบ</th>
          </tr>

          <tr>
              <td>ลำดับขั้นตอน</td>
              <td>เวลาเสร็จสิ้น</td>
              <td>ID ผู้ดำเนินการ</td>
              <td>ชื่อผู้ดำเนินการ</td>
              <td>ผลการดำเนินการ</td>
              <td>จัดการความคิดเห็น</td>
          </tr>

          <?php

          $auth_logs = array_reverse($auth_logs);
          foreach ($auth_logs as $k=>$v) {
              echo "<tr>
                        <td>ผู้อนุมัติคนที่".intval($k+1)."</td>
                        <td>{$v['audit_at_datetime']}</td>
                        <td>{$v['staff_id']}</td>
                        <td>{$v['staff_name']}</td>
                        <td>{$v['action_name']}</td>
                        <td>{$v['info']}</td>
                    </tr>";
          }
          ?>
      </table>

	</div>
</body>
</html>