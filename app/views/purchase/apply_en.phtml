<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <meta http-equiv="X-UA-Compatible" content="ie=edge"/>
    <title>title</title>
    <style>
        body,
        ul,
        li {
            margin: 0;
            padding: 0;
        }

        body {
            font-size: 16px;
        }

        .warp {
            width: 95%;
            margin: auto;
        }

        .title {
            font-size: 28px;
            text-align: center;
            padding: 120px 0 60px;
        }

        ul {
            width: 100%;
            overflow: hidden;
            margin-bottom: 50px;
        }

        li {
            list-style: none;
            width: 50%;
            float: left;
            margin-bottom: 20px;
        }

        table {
            width: 100%;
        }

        td {
            padding: 5px;
        }
    </style>
</head>
<body>
<div class="warp">
    <div class="title">Purchase Requisition</div>
    <ul>
        <li>Numbering：<?php echo $pano; ?></li>
        <li>Applicant's name：<?php echo $create_name; ?></li>
        <li>Applicant's Employee ID：<?php echo $create_id; ?></li>
        <li>Applicant department：<?php echo $create_department_name; ?></li>
        <li>Applicant's number：<?php echo $create_mobile; ?></li>
        <li>Expense Department：<?php echo $cost_department_name; ?></li>
        <li>Expense center：<?php echo $cost_center_code; ?></li>
        <li>Date Required： <?php echo $apply_date; ?></li>
        <li>Expense Network：<?php echo $cost_store_name; ?></li>

        <li>The reason of application：<?php echo $apply_reason; ?></li>
        <li>Currency：<?php echo $currency_text ?></li>
    </ul>
    <table cellpadding="0" cellspacing="0" border="1">
        <tr align="center">
            <th>NO.</th>
            <th>Budget classification</th>
            <th>Product Name</th>
            <th>Product Descripition</th>
            <th>number</th>
            <th>Unit</th>
            <th>Unit price（No VAT）</th>
            <th>Price（No VAT）</th>
            <th>VAT rate</th>
            <th>VAT amount</th>
            <th>Price（Including VAT）</th>
            <th>Remarks</th>
        </tr>
        <?php
        foreach ($products_v1 as $k=> $v) {
            echo "<tr align='center'>
            <td>" . intval($k + 1) . "</td>
            <td>{$v['budget_text']}</td>
            <td>{$v['product_name']}</td>
            <td>{$v['desc']}</td>
            <td>{$v['total']}</td>
            <td>{$v['unit']}</td>
            <td>".number_format($v['price'], 6)."</td>
            <td>".number_format($v['total_price'], 2)."</td>
            <td>{$v['vat7_rate']}%</td>
            <td>".number_format($v['vat7'], 2)."</td>
            <td>".number_format($v['all_total'], 2)."</td>
            <td>{$v['remark']}</td>
          </tr>";
        }
        ?>
    </table>
    <p style="margin-top: 30px">Grand Total：<?php echo number_format($amount,2); ?></p>
    <table
            cellpadding="0"
            cellspacing="0"
            border="1"
            style="margin-top: 50px"
    >
        <tr align="center">
            <td colspan="6">Approval Flow</td>
        </tr>
        <tr align="center">
            <td>Process Name</td>
            <td>Finish Time</td>
            <td>Checker ID</td>
            <td>Checker Name</td>
            <td>Approval Result</td>
            <td>Approval Opinion</td>
        </tr>
        <?php
        foreach ($auth_logs as $k => $v) {
            echo "<tr align='center'>
            <td>Checker" . intval($k + 1) . "</td>
            <td>{$v['audit_at_datetime']}</td>
            <td>{$v['staff_id']}</td>
            <td>{$v['staff_name']}</td>
            <td>{$v['action_name']}</td>
            <td>{$v['info']}</td>
          </tr>";
        }
        ?>
    </table>
</div>
</body>
</html>
