<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <meta http-equiv="X-UA-Compatible" content="ie=edge"/>
    <title>title</title>
    <style>
        body,
        ul,
        li {
            margin: 0;
            padding: 0;
        }

        body {
            font-size: 16px;
        }

        .warp {
            width: 95%;
            margin: auto;
        }

        .title {
            font-size: 28px;
            text-align: center;
            padding: 120px 0 60px;
        }

        ul {
            width: 100%;
            overflow: hidden;
            margin-bottom: 50px;
        }

        li {
            list-style: none;
            width: 50%;
            float: left;
            margin-bottom: 20px;
        }

        table {
            width: 100%;
        }

        td {
            padding: 5px;
        }
    </style>
</head>
<body>
<div class="warp">
    <div class="title">采购申请单</div>
    <ul>
        <li>编号：<?php echo $pano; ?></li>
        <li>申请人姓名：<?php echo $create_name; ?></li>
        <li>申请人工号：<?php echo $create_id; ?></li>
        <li>申请人部门：<?php echo $create_department_name; ?></li>
        <li>申请人电话：<?php echo $create_mobile; ?></li>
        <li>费用部门：<?php echo $cost_department_name; ?></li>
        <li>费用所属中心：<?php echo $cost_center_code; ?></li>
        <li>需求日期： <?php echo $apply_date; ?></li>
        <li>费用网点：<?php echo $cost_store_name; ?></li>

        <li>申请原因：<?php echo $apply_reason; ?></li>
        <li>币种：<?php echo $currency_text ?></li>
    </ul>
    <table cellpadding="0" cellspacing="0" border="1">
        <tr align="center">
            <th>序号</th>
            <th>预算分类</th>
            <th>产品名称</th>
            <th>产品描述</th>
            <th>数量</th>
            <th>单位</th>
            <th>不含税单价</th>
            <th>不含税金额</th>
            <th>VAT 税率</th>
            <th>VAT 金额</th>
            <th>含税金额</th>
            <th>备注</th>
        </tr>
        <?php
        foreach ($products_v1 as $k=>$v) {
            echo "<tr align='center'>
            <td>" . intval($k + 1) . "</td>
            <td>{$v['budget_text']}</td>
            <td>{$v['product_name']}</td>
            <td>{$v['desc']}</td>
            <td>{$v['total']}</td>
            <td>{$v['unit']}</td>
            <td>".number_format($v['price'], 6)."</td>
            <td>".number_format($v['total_price'], 2)."</td>
            <td>{$v['vat7_rate']}%</td>
            <td>".number_format($v['vat7'], 2)."</td>
            <td>".number_format($v['all_total'], 2)."</td>
            <td>{$v['remark']}</td>
          </tr>";
        }
        ?>
    </table>
    <p style="margin-top: 30px">金额总计：<?php echo number_format($amount, 2); ?></p>
    <table
            cellpadding="0"
            cellspacing="0"
            border="1"
            style="margin-top: 50px"
    >
        <tr align="center">
            <td colspan="6">审批流程</td>
        </tr>
        <tr align="center">
            <td>步骤名称</td>
            <td>完成时间</td>
            <td>处理人工号</td>
            <td>处理人姓名</td>
            <td>处理结果</td>
            <td>处理意见</td>
        </tr>
        <?php
        foreach ($auth_logs as $k => $v) {
            echo "<tr align='center'>
            <td>审批人" . intval($k + 1) . "</td>
            <td>{$v['audit_at_datetime']}</td>
            <td>{$v['staff_id']}</td>
            <td>{$v['staff_name']}</td>
            <td>{$v['action_name']}</td>
            <td>{$v['info']}</td>
          </tr>";
        }
        ?>
    </table>
</div>
</body>
</html>
