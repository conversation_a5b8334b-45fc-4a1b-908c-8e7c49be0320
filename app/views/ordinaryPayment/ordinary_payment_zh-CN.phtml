<!doctype HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=no">
    <style>
        body {
            font: 3.2mm/1.2 Tahoma, sans-serif;
            color: #000;
            -webkit-print-color-adjust: exact;
        }
        .table {
            width: 100%;
            border: .5mm solid #666;
            border-collapse: collapse;
            border-spacing: 0;
            margin-top: 5mm;
            table-layout: fixed;
            overflow: wrap;
        }
        .table th, .table td {
            border: .3mm solid #999;
            padding: 2mm;
            text-align: center;
            word-wrap: break-word;
        }
        .body-wrap {
            height: 210mm;
            width: 297mm;
            padding: 4mm;
            box-sizing: border-box;
            margin: 0 auto;
        }
        .table-header {
            font-weight: 600;
        }
    </style>
</head>

<body>
<div class="body-wrap">
    <table class="table">
        <tr>
            <td colspan="6" class="table-header">普通付款申请</td>
            <td colspan="2" class="table-header">编号</td>
            <td colspan="2"><?php echo $apply_no ?></td>
        </tr>
        <tr>
            <td colspan="10">基本信息</td>
        </tr>
        <tr>
            <td colspan="2">发起人姓名</td>
            <td colspan="3"><?php echo $create_name ?></td>
            <td colspan="2">发起人工号</td>
            <td colspan="3"><?php echo $create_id ?></td>
        </tr>
        <tr>
            <td colspan="2">申请人姓名</td>
            <td colspan="3"><?php echo $apply_name ?></td>
            <td colspan="2">申请人工号</td>
            <td colspan="3"><?php echo $apply_id ?></td>
        </tr>
        <tr>
            <td colspan="2">申请人公司</td>
            <td colspan="3"><?php echo $apply_company_name ?></td>
            <td colspan="2">申请人部门</td>
            <td colspan="3"><?php echo $apply_node_department_name ?></td>
        </tr>
        <tr>
            <td colspan="2">费用所属部门</td>
            <td colspan="3"><?php echo $cost_department_name ?></td>
            <td colspan="2">费用所属网点</td>
            <td colspan="3"><?php echo $cost_store_type_text ?></td>
        </tr>
        <tr>
            <td colspan="2">费用所属公司</td>
            <td colspan="3"><?php echo $cost_company_name ?></td>
            <td colspan="2"></td>
            <td colspan="3"></td>
        </tr>
        <tr>
            <td colspan="10">费用信息</td>
        </tr>
        <tr>
            <td colspan="2">付款币种</td>
            <td colspan="3"><?php echo $currency_text ?></td>
            <td colspan="2">付款方式</td>
            <td colspan="3"><?php echo $payment_method_text ?></td>
        </tr>
        <tr>
            <td colspan="2">应付日期</td>
            <td colspan="3"><?php echo $should_pay_date ?></td>
            <td colspan="2">备注</td>
            <td colspan="3"><?php echo $remark ?></td>
        </tr>
        <tr>
            <td colspan="10">金额详情</td>
        </tr>
        <tr>
            <td>序号</td>
            <td>付款分类</td>
            <td>费用类型</td>
            <td>费用发生期间</td>
            <td>不含税金额</td>
            <td>VAT税额</td>
            <td>含税金额</td>
            <td>WHT类别</td>
            <td>WHT税率</td>
            <td>WHT金额</td>
        </tr>
        <?php foreach ($amount_detail_item as $key=>$item) {
            $key ++;
            echo "
             <tr>
                <td>{$key}</td>
                <td>{$item['budget_name']}</td>
                <td>{$item['product_name']}</td>
                <td>{$item['cost_start_date']} - {$item['cost_end_date']}</td>
                <td>".number_format($item['amount_no_tax'], 2)."</td>
                <td>".number_format($item['amount_vat'], 2)."</td>
                <td>".number_format($item['amount_have_tax'], 2)."</td>
                <td>{$item['wht_category_name']}</td>
                <td>{$item['wht_rate_name']}</td>
                <td>".number_format($item['amount_wht'], 2)."</td>
            </tr>
            ";
        }?>

        <tr>
            <td colspan="2">含税金额总计</td>
            <td><?php echo number_format($amount_total_have_tax,2) ?></td>
            <td colspan="2">WHT金额总计	</td>
            <td><?php echo number_format($amount_total_wht,2) ?></td>
            <td>折扣</td>
            <td><?php echo number_format($amount_discount,2) ?></td>
            <td>实付金额总计</td>
            <td><?php echo number_format($amount_total_actually,2) ?></td>
        </tr>
        <?php
        if(1==$payee_type){
           echo "
        <tr>
            <td colspan='10'>供应商信息</td>
        </tr>
        <tr>
            <td colspan='2'>供应商名称</td>
            <td colspan='3'> {$supplier_info['supplier_name']}</td>
        <td colspan='2'>供应商联系人</td>
        <td colspan='3'>{$supplier_info['supplier_contacts']}</td>
        </tr>
        <tr>
            <td colspan='2'>供应商联系方式</td>
            <td colspan='3'>{$supplier_info['supplier_tel']}</td>
            <td colspan='2'>银行名称</td>
            <td colspan='3'>{$supplier_info['supplier_bk_name']}</td>
        </tr>
        <tr>
            <td colspan='2'>银行账户号</td>
            <td colspan='3'>{$supplier_info['supplier_bk_account']}</td>
            <td colspan='2'>收款人银行账户名称</td>
            <td colspan='3'>{$supplier_info['supplier_bk_account_name']}</td>
        </tr>
        ";
        }elseif(2==$payee_type){
            echo "
        <tr>
            <td colspan='10'>收款人信息</td>
        </tr>
            <tr>
            <td colspan='1'>收款人工号</td>
            <td colspan='1'>银行名称</td>
            <td colspan='1'>银行账户名称</td>
            <td colspan='2'>银行账号</td>
            <td colspan='1'>联系人</td>
            <td colspan='2'>联系方式</td>
            <td colspan='2'>金额</td>
        </tr>
        ";
            foreach ($personal_detail as $key1=>$value1){
                echo "
            <tr>
            <td colspan='1'>{$value1['staff_info_id']}</td>
            <td colspan='1'>{$value1['bank_name']}</td>
            <td colspan='1'>{$value1['bank_no_name']}</td>
            <td colspan='2'>{$value1['bank_no']}</td>
            <td colspan='1'>{$value1['name']}</td>
            <td colspan='2'>{$value1['mobile']}</td>
            <td colspan='2'>{$value1['amount']}</td>
        </tr>";
            }
        } ?>
        <tr>
            <td colspan="10">审批流程</td>
        </tr>
        <tr>
            <td colspan="2">步骤名称</td>
            <td colspan="2">完成时间</td>
            <td>处理人工号</td>
            <td>处理人姓名</td>
            <td>处理结果</td>
            <td colspan='3'>处理意见</td>
        </tr>
        <?php
        if (!empty($auth_logs)) {
            $auth_logs = array_reverse($auth_logs);

            foreach ($auth_logs as $k => $log){
                $rank_no = $k + 1;
                $log['staff_id'] = $log['staff_id'] ?? '';
                $log['staff_name'] = $log['staff_name'] ?? '';
                echo "
                          <tr>
                            <td colspan='2'>审批人{$rank_no}</td>
                            <td colspan='2'>{$log['audit_at_datetime']}</td>
                            <td>{$log['staff_id']}</td>
                            <td>{$log['staff_name']}</td>
                            <td>{$log['action_name']}</td>
                            <td colspan='3'>{$log['info']}</td>
                          </tr>";
            }
        }
        ?>
    </table>
</div>
</body>
</html>