<!doctype HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=no">
    <style>
        body {
            font: 3.2mm/1.2 Tahoma, sans-serif;
            color: #000;
            -webkit-print-color-adjust: exact;
        }
        .table {
            width: 100%;
            border: .5mm solid #666;
            border-collapse: collapse;
            border-spacing: 0;
            margin-top: 5mm;
            table-layout: fixed;
            overflow: wrap;
        }
        .table th, .table td {
            border: .3mm solid #999;
            padding: 2mm;
            text-align: center;
            word-wrap: break-word;
        }
        .body-wrap {
            height: 210mm;
            width: 297mm;
            padding: 4mm;
            box-sizing: border-box;
            margin: 0 auto;
        }
        .table-header {
            font-weight: 600;
        }
    </style>
</head>

<body>
<div class="body-wrap">
    <table class="table">
        <tr>
            <td colspan="6" class="table-header">ยื่นขอการชำระเงินทั่วไป</td>
            <td colspan="2" class="table-header">หมายเลข</td>
            <td colspan="2"><?php echo $apply_no ?></td>
        </tr>
        <tr>
            <td colspan="10">ข้อมูลพื้นฐาน</td>
        </tr>
        <tr>
            <td colspan="2">ชื่อ-นามสกุล ผู้ยื่นขอ</td>
            <td colspan="3"><?php echo $create_name ?></td>
            <td colspan="2">ID พนักงานผู้ยื่นขอ</td>
            <td colspan="3"><?php echo $create_id ?></td>
        </tr>
        <tr>
            <td colspan="2">ชื่อ-นามสกุล</td>
            <td colspan="3"><?php echo $apply_name ?></td>
            <td colspan="2">รหัสพนักงาน</td>
            <td colspan="3"><?php echo $apply_id ?></td>
        </tr>
        <tr>
            <td colspan="2">บริษัท</td>
            <td colspan="3"><?php echo $apply_company_name ?></td>
            <td colspan="2">แผนก</td>
            <td colspan="3"><?php echo $apply_node_department_name ?></td>
        </tr>
        <tr>
            <td colspan="2">ค่าใช้จ่ายแผนก</td>
            <td colspan="3"><?php echo $cost_department_name ?></td>
            <td colspan="2">ค่าใช้จ่ายสาขา</td>
            <td colspan="3"><?php echo $cost_store_type_text ?></td>
        </tr>
        <tr>
            <td colspan="2">ค่าใช้จ่ายของบริษัท</td>
            <td colspan="3"><?php echo $cost_company_name ?></td>
            <td colspan="2"></td>
            <td colspan="3"></td>
        </tr>
        <tr>
            <td colspan="10">ข้อมูลค่าใช้จ่าย</td>
        </tr>
        <tr>
            <td colspan="2">สกุลเงินที่ใช้ในการชำระ</td>
            <td colspan="3"><?php echo $currency_text ?></td>
            <td colspan="2">ช่องทางการชำระเงิน</td>
            <td colspan="3"><?php echo $payment_method_text ?></td>
        </tr>
        <tr>
            <td colspan="2">วันครบกำหนดชำระเงิน</td>
            <td colspan="3"><?php echo $should_pay_date ?></td>
            <td colspan="2">หมายเหตุ</td>
            <td colspan="3"><?php echo $remark ?></td>
        </tr>
        <tr>
            <td colspan="10">รายละเอียดยอดเงิน</td>
        </tr>
        <tr>
            <td>อันดับ</td>
            <td>ประเภทชำระเงิน</td>
            <td>ประเภทค่าใช้จ่าย</td>
            <td>ค่าใช้จ่ายช่วงระยะเวลาที่มีผล</td>
            <td>ราคาไม่รวมภาษี</td>
            <td>VAT amount</td>
            <td>ราคารวมภาษี</td>
            <td>WHT type</td>
            <td>WHT tax rate</td>
            <td>WHT amount</td>
        </tr>
        <?php foreach ($amount_detail_item as $key=>$item) {
            $key ++;
            echo "
             <tr>
                <td>{$key}</td>
                <td>{$item['budget_name']}</td>
                <td>{$item['product_name']}</td>
                <td>{$item['cost_start_date']} - {$item['cost_end_date']}</td>
                <td>".number_format($item['amount_no_tax'], 2)."</td>
                <td>".number_format($item['amount_vat'], 2)."</td>
                <td>".number_format($item['amount_have_tax'], 2)."</td>
                <td>{$item['wht_category_name']}</td>
                <td>{$item['wht_rate_name']}</td>
                <td>".number_format($item['amount_wht'], 2)."</td>
            </tr>
            ";
        }?>

        <tr>
            <td colspan="2">ราคารวม (รวมภาษี)</td>
            <td><?php echo number_format($amount_total_have_tax,2) ?></td>
            <td colspan="2">ยอดเงินWHTรวม</td>
            <td><?php echo number_format($amount_total_wht,2) ?></td>
            <td>ส่วนลด</td>
            <td><?php echo number_format($amount_discount,2) ?></td>
            <td>ยอดเงินรวมที่ชำระจริง</td>
            <td><?php echo number_format($amount_total_actually,2) ?></td>
        </tr>
        <?php
        if(1==$payee_type){
            echo "
        <tr>
            <td colspan='10'>ข้อมูลซัพพลายเออร์</td>
        </tr>
        <tr>
            <td colspan='2'>ชื่อซัพพลายเออร์</td>
            <td colspan='3'> {$supplier_info['supplier_name']}</td>
        <td colspan='2'>ผู้ติดต่อ ซัพพลายเออร์</td>
        <td colspan='3'>{$supplier_info['supplier_contacts']}</td>
        </tr>
        <tr>
            <td colspan='2'>ช่องทางติดต่อซัพพลายเออร์</td>
            <td colspan='3'>{$supplier_info['supplier_tel']}</td>
            <td colspan='2'>ธนาคาร</td>
            <td colspan='3'>{$supplier_info['supplier_bk_name']}</td>
        </tr>
        <tr>
            <td colspan='2'>ชเลขบัญชี</td>
            <td colspan='3'>{$supplier_info['supplier_bk_account']}</td>
            <td colspan='2'>ชื่อบัญชีธนาคารของผู้รับเงิน</td>
            <td colspan='3'>{$supplier_info['supplier_bk_account_name']}</td>
        </tr>
        ";
        }elseif(2==$payee_type){
            echo "
        <tr>
            <td colspan='10'>ข้อมูลผู้รับเงิน</td>
        </tr>
            <tr>
            <td colspan='1'>ID พนักงานผู้รับเงิน</td>
            <td colspan='1'>ชื่อธนาคาร</td>
            <td colspan='1'>ชื่อบัญชี</td>
            <td colspan='2'>เลขบัญชี</td>
            <td colspan='1'>ผู้ติดต่อ</td>
            <td colspan='2'>ข้อมูลในการติดต่อ</td>
            <td colspan='2'>ยอดเงิน</td>
        </tr>
        ";
            foreach ($personal_detail as $key1=>$value1){
                echo "
            <tr>
            <td colspan='1'>{$value1['staff_info_id']}</td>
            <td colspan='1'>{$value1['bank_name']}</td>
            <td colspan='1'>{$value1['bank_no_name']}</td>
            <td colspan='2'>{$value1['bank_no']}</td>
            <td colspan='1'>{$value1['name']}</td>
            <td colspan='2'>{$value1['mobile']}</td>
            <td colspan='2'>{$value1['amount']}</td>
        </tr>";
            }
        } ?>

        <tr>
            <td colspan="10">ข้อมูลซัพพลายเออร์</td>
        </tr>
        <tr>
            <td colspan="2">ชื่อซัพพลายเออร์</td>
            <td colspan="3"><?php echo $supplier_info['supplier_name'] ?? '' ?></td>
            <td colspan="2">ผู้ติดต่อ ซัพพลายเออร์</td>
            <td colspan="3"><?php echo $supplier_info['supplier_contacts'] ?? '' ?></td>
        </tr>
        <tr>
            <td colspan="2">ช่องทางติดต่อซัพพลายเออร์</td>
            <td colspan="3"><?php echo $supplier_info['supplier_tel'] ?? '' ?></td>
            <td colspan="2">ธนาคาร</td>
            <td colspan="3"><?php echo $supplier_info['supplier_bk_name'] ?? '' ?></td>
        </tr>
        <tr>
            <td colspan="2">เลขบัญชี</td>
            <td colspan="3"><?php echo $supplier_info['supplier_bk_account'] ?? '' ?></td>
            <td colspan="2">ชื่อบัญชีธนาคารของผู้รับเงิน</td>
            <td colspan="3"><?php echo $supplier_info['supplier_bk_account_name'] ?? '' ?></td>
        </tr>
        <tr>
            <td colspan="10">ขั้นตอนการตรวจสอบ</td>
        </tr>
        <tr>
            <td colspan="2">ลำดับขั้นตอน</td>
            <td colspan="2">เวลาเสร็จสิ้น</td>
            <td>ID ผู้ดำเนินการ</td>
            <td>ชื่อผู้ดำเนินการ</td>
            <td>ผลการดำเนินการ</td>
            <td colspan='3'>จัดการความคิดเห็น</td>
        </tr>
        <?php
        if (!empty($auth_logs)) {
            $auth_logs = array_reverse($auth_logs);

            foreach ($auth_logs as $k => $log){
                $rank_no = $k + 1;
                $log['staff_id'] = $log['staff_id'] ?? '';
                $log['staff_name'] = $log['staff_name'] ?? '';
                echo "
                          <tr>
                            <td colspan='2'>ผู้อนุมัติคนที่{$rank_no}</td>
                            <td colspan='2'>{$log['audit_at_datetime']}</td>
                            <td>{$log['staff_id']}</td>
                            <td>{$log['staff_name']}</td>
                            <td>{$log['action_name']}</td>
                            <td colspan='3'>{$log['info']}</td>
                          </tr>";
            }
        }
        ?>
    </table>
</div>
</body>
</html>