<!doctype HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=no">
    <style>
        body {
            font: 3.2mm/1.2 Tahoma, sans-serif;
            color: #000;
            -webkit-print-color-adjust: exact;
        }
        .table {
            width: 100%;
            border: .5mm solid #666;
            border-collapse: collapse;
            border-spacing: 0;
            margin-top: 5mm;
            table-layout: fixed;
            overflow: wrap;
        }
        .table th, .table td {
            border: .3mm solid #999;
            padding: 2mm;
            text-align: center;
            word-wrap: break-word;
        }
        .body-wrap {
            height: 210mm;
            width: 297mm;
            padding: 4mm;
            box-sizing: border-box;
            margin: 0 auto;
        }
        .table-header {
            font-weight: 600;
        }
    </style>
</head>

<body>
<div class="body-wrap">
    <table class="table">
        <tr>
            <td colspan="6" class="table-header">Ordinary payment application</td>
            <td colspan="2" class="table-header">Numbering</td>
            <td colspan="2"><?php echo $apply_no ?></td>
        </tr>
        <tr>
            <td colspan="10">Basic Information</td>
        </tr>
        <tr>
            <td colspan="2">Sponsor‘s name</td>
            <td colspan="3"><?php echo $create_name ?></td>
            <td colspan="2">Sponsor‘s Employee ID</td>
            <td colspan="3"><?php echo $create_id ?></td>
        </tr>
        <tr>
            <td colspan="2">Applicant's name</td>
            <td colspan="3"><?php echo $apply_name ?></td>
            <td colspan="2">Applicant's Employee ID</td>
            <td colspan="3"><?php echo $apply_id ?></td>
        </tr>
        <tr>
            <td colspan="2">company</td>
            <td colspan="3"><?php echo $apply_company_name ?></td>
            <td colspan="2">Applicant's department</td>
            <td colspan="3"><?php echo $apply_node_department_name ?></td>
        </tr>
        <tr>
            <td colspan="2">Expense Department</td>
            <td colspan="3"><?php echo $cost_department_name ?></td>
            <td colspan="2">Expense Network</td>
            <td colspan="3"><?php echo $cost_store_type_text ?></td>
        </tr>
        <tr>
            <td colspan="2">Expense Company</td>
            <td colspan="3"><?php echo $cost_company_name ?></td>
            <td colspan="2"></td>
            <td colspan="3"></td>
        </tr>
        <tr>
            <td colspan="10">expense information</td>
        </tr>
        <tr>
            <td colspan="2">Payment currency</td>
            <td colspan="3"><?php echo $currency_text ?></td>
            <td colspan="2">Payment method</td>
            <td colspan="3"><?php echo $payment_method_text ?></td>
        </tr>
        <tr>
            <td colspan="2">Due date</td>
            <td colspan="3"><?php echo $should_pay_date ?></td>
            <td colspan="2">Remarks</td>
            <td colspan="3"><?php echo $remark ?></td>
        </tr>
        <tr>
            <td colspan="10">Amount details</td>
        </tr>
        <tr>
            <td>NO.</td>
            <td>Payment classification </td>
            <td>types of expense</td>
            <td>Period of expense</td>
            <td>Price（No VAT）</td>
            <td>VAT amount</td>
            <td>Price（Including VAT）</td>
            <td>WHT type</td>
            <td>WHT tax rate</td>
            <td>WHT amount</td>
        </tr>
        <?php foreach ($amount_detail_item as $key=>$item) {
            $key ++;
            echo "
             <tr>
                <td>{$key}</td>
                <td>{$item['budget_name']}</td>
                <td>{$item['product_name']}</td>
                <td>{$item['cost_start_date']} - {$item['cost_end_date']}</td>
                <td>".number_format($item['amount_no_tax'], 2)."</td>
                <td>".number_format($item['amount_vat'], 2)."</td>
                <td>".number_format($item['amount_have_tax'], 2)."</td>
                <td>{$item['wht_category_name']}</td>
                <td>{$item['wht_rate_name']}</td>
                <td>".number_format($item['amount_wht'], 2)."</td>
            </tr>
            ";
        }?>

        <tr>
            <td colspan="2">Total price（Including VAT）</td>
            <td><?php echo number_format($amount_total_have_tax,2) ?></td>
            <td colspan="2">Total WHT amount</td>
            <td><?php echo number_format($amount_total_wht,2) ?></td>
            <td>Discount</td>
            <td><?php echo number_format($amount_discount,2) ?></td>
            <td>Total amount actually paid</td>
            <td><?php echo number_format($amount_total_actually,2) ?></td>
        </tr>
<?php
if(1==$payee_type){
    echo "
     <tr>
            <td colspan='10'>Supplier information</td>
        </tr>
        <tr>
            <td colspan='2'>Supplier name</td>
            <td colspan='3'>{$supplier_info['supplier_name']}</td>
            <td colspan='2'>Supplier Contact Person</td>
            <td colspan='3'>{$supplier_info['supplier_contacts']}</td>
        </tr>
        <tr>
            <td colspan='2'>Supplier contact information</td>
            <td colspan='3'>{$supplier_info['supplier_tel']}</td>
            <td colspan='2'>Bank name</td>
            <td colspan='3'>{$supplier_info['supplier_bk_name']}</td>
        </tr>
        <tr>
            <td colspan='2'>Bank account number</td>
            <td colspan='3'>{$supplier_info['supplier_bk_account']}</td>
            <td colspan='2'>Name of payee's bank account</td>
            <td colspan='3'>{$supplier_info['supplier_bk_account_name']}</td>
        </tr>
        ";
        } else if (2 == $payee_type) {
    echo "
             <tr>
             <td colspan='10'>Payee information</td>
             </tr>
            <tr>
            <td colspan='1'>Payee's number</td>
            <td colspan='1'>Bank name</td>
            <td colspan='1'>Bank account name</td>
            <td colspan='2'>Account number</td>
            <td colspan='1'>Contact Person</td>
            <td colspan='2'>Contact</td>
            <td colspan='2'>Amount</td>
        </tr>
        ";
    foreach ($personal_detail as $key1 => $value1) {
        echo "
            <tr>
            <td colspan='1'>{$value1['staff_info_id']}</td>
            <td colspan='1'>{$value1['bank_name']}</td>
            <td colspan='1'>{$value1['bank_no_name']}</td>
            <td colspan='2'>{$value1['bank_no']}</td>
            <td colspan='1'>{$value1['name']}</td>
            <td colspan='2'>{$value1['mobile']}</td>
            <td colspan='2'>{$value1['amount']}</td>
        </tr>";
    }

} ?>
        <tr>
            <td colspan="10">Review process</td>
        </tr>
        <tr>
            <td colspan="2">Step name</td>
            <td colspan="2">Complete time</td>
            <td>Process Labor Employee ID</td>
            <td>Process Labor name</td>
            <td>Process result</td>
            <td colspan='3'>Handling opinions</td>
        </tr>
        <?php
        if (!empty($auth_logs)) {
            $auth_logs = array_reverse($auth_logs);

            foreach ($auth_logs as $k => $log){
                $rank_no = $k + 1;
                $log['staff_id'] = $log['staff_id'] ?? '';
                $log['staff_name'] = $log['staff_name'] ?? '';
                echo "
                          <tr>
                            <td colspan='2'>Approver{$rank_no}</td>
                            <td colspan='2'>{$log['audit_at_datetime']}</td>
                            <td>{$log['staff_id']}</td>
                            <td>{$log['staff_name']}</td>
                            <td>{$log['action_name']}</td>
                            <td colspan='3'>{$log['info']}</td>
                          </tr>";
            }
        }
        ?>
    </table>
</div>
</body>
</html>