<!doctype HTML>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
	<meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=no">
	<style>
    body {
      font: 3.2mm/1.2 Tahoma, sans-serif;
      color: #000;
			-webkit-print-color-adjust: exact;
    }
    body, ul {
      margin: 0;
      padding: 0;
    }
    ul, li {
      list-style: none;
    }
    p {
      margin: 1mm 0 0;
    }
    .clearfix::after {
      display: block;
      content: "";
      clear: both;
    }
    .break-after {
      page-break-after: always;
      page-break-inside: avoid;
    }
    .table {
      width: 100%;
      border: .5mm solid #666;
      border-collapse: collapse;
      border-spacing: 0;
      margin-top: 5mm;
    }
    .table th, .table td {
      border: .3mm solid #999;
      padding: 2mm;
      text-align: center;
      word-break: normal;
    }
    .body-wrap {
      height: 210mm;
      width: 297mm;
      padding: 4mm;
      box-sizing: border-box;
      margin: 0 auto;
    }
    .logo {
      float: right;
      display: block;
      width: 40mm;
    }
    .table-header {
      margin-top: 13mm;
      margin-bottom: 8mm;
			font-size: 5mm;
			font-weight: 600;
			text-align: center;
    }
    li {
      float: left;
      width: 50%;
      margin-bottom: 2mm;
    }
	</style>
</head>

<body>
  <div class="body-wrap">
    <div class="table-header">Branch rent payment </div>
    <ul class="clearfix">
      <li>Numbering: <?php echo $apply_no ?></li>
      <li></li>
      <li>Applicant's Employee ID: <?php echo $create_id ?></li>
      <li>Applicant's name: <?php echo $create_name ?></li>
      <li>Applicant's company: <?php echo $create_company_name ?></li>
      <li>Applicant's department: <?php echo $create_department_name ?></li>
      <li>Expense Company: <?php echo $cost_company_name ?></li>
      <li>Expense Department: <?php echo $cost_center_department_name ?></li>
      <li>Expense center: <?php echo $cost_center_name ?></li>
      <li>Expense Network: <?php echo $cost_store_type_text ?></li>
      <li>Payment method: <?php echo $payment_method_text ?></li>
      <li>Payment currency: <?php echo $currency_text ?></li>
      <li>Remarks: <?php echo $remark ?></li>
    </ul>

    <table class="table">
      <tr>
        <td colspan="11">Amount details</td>
      </tr>
      <tr>
        <th>Branch Name</th>
        <th>types of expense</th>
        <th>Expense start date</th>
        <th>Expense end date</th>
        <th>amount</th>
        <th>WHT amount</th>
        <th>The amount actually paid</th>
        <th>Bank name</th>
        <th>Bank account name</th>
        <th>Bank account number</th>
        <th>Contact phone number</th>
      </tr>
        <?php
        foreach ($amount_detail_item as $item) {
            echo "     
                    <tr>
                    <td>{$item['store_name']}</td>
                    <td>{$item['cost_type_text']}</td>
                    <td>{$item['cost_start_date']}</td>
                    <td>{$item['cost_end_date']}</td>
                    <td>".number_format($item['amount'],2)."</td>
                    <td>".number_format($item['wht_amount'],2)."</td>
                    <td>".number_format($item['actually_amount'],2)."</td>
                    <td>{$item['bank_name']}</td>
                    <td>{$item['bank_account_name']}</td>
                    <td>{$item['bank_account_no']}</td>
                    <td>{$item['contact_phone']}</td>
                    </tr>";
        }
        ?>
      <tr>
        <td colspan="11">Grand Total</td>
      </tr>
      <tr>
        <td colspan="3">Grand Total</td>
        <td colspan="3"></td>
        <td colspan="4">Total WHT amount	</td>
        <td></td>
      </tr>
      <tr>
        <td colspan="3">Total amount actually paid</td>
        <td colspan="3"></td>
        <td colspan="4"></td>
        <td></td>
      </tr>
      <tr>
        <td colspan="11">Review process</td>
      </tr>
      <tr>
        <td colspan="3">Step name</td>
        <td colspan="3">Complete time</td>
        <td>Process Labor Employee ID</td>
        <td colspan="2">Process Labor name</td>
        <td>Process result</td>
        <td>Handling opinions</td>
      </tr>
        <?php
        if (!empty($auth_logs)) {
            $auth_logs = array_reverse($auth_logs);

            foreach ($auth_logs as $k => $log){
                $rank_no = $k + 1;

                $log['staff_id'] = $log['staff_id'] ?? '';
                $log['staff_name'] = $log['staff_name'] ?? '';

                echo "
                          <tr>
                            <td colspan='3'>Approver{$rank_no}</td>
                            <td colspan='3'>{$log['audit_at_datetime']}</td>
                            <td>{$log['staff_id']}</td>
                            <td colspan='2'>{$log['staff_name']}</td>
                            <td>{$log['action_name']}</td>
                            <td>{$log['info']}</td>
                          </tr>";
            }
        }
        ?>
    </table>
	</div>
</body>
</html>