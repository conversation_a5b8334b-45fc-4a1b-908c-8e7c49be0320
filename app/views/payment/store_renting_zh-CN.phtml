<!doctype HTML>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
	<meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=no">
	<style>
    body {
      font: 3.2mm/1.2 Tahoma, sans-serif;
      color: #000;
			-webkit-print-color-adjust: exact;
    }
    body, ul {
      margin: 0;
      padding: 0;
    }
    ul, li {
      list-style: none;
    }
    p {
      margin: 1mm 0 0;
    }
    .clearfix::after {
      display: block;
      content: "";
      clear: both;
    }
    .break-after {
      page-break-after: always;
      page-break-inside: avoid;
    }
    .table {
      width: 100%;
      border: .5mm solid #666;
      border-collapse: collapse;
      border-spacing: 0;
      margin-top: 5mm;
    }
    .table th, .table td {
      border: .3mm solid #999;
      padding: 2mm;
      text-align: center;
      word-break: break-all;
    }
    .body-wrap {
      height: 210mm;
      width: 297mm;
      padding: 4mm;
      box-sizing: border-box;
      margin: 0 auto;
    }
    .logo {
      float: right;
      display: block;
      width: 40mm;
    }
    .table-header {
      margin-top: 13mm;
      margin-bottom: 8mm;
			font-size: 5mm;
			font-weight: 600;
			text-align: center;
    }
    li {
      float: left;
      width: 50%;
      margin-bottom: 2mm;
    }
	</style>
</head>

<body>
  <div class="body-wrap">
    <div class="table-header">网点租房付款</div>
    <ul class="clearfix">
      <li>编号：<?php echo $apply_no ?></li>
      <li></li>
      <li>申请人工号：<?php echo $create_id ?></li>
      <li>申请人姓名：<?php echo $create_name ?></li>
      <li>申请人所属公司：<?php echo $create_company_name ?></li>
      <li>申请人所属部门：<?php echo $create_department_name ?></li>
      <li>费用所属公司：<?php echo $cost_company_name ?></li>
      <li>费用所属部门：<?php echo $cost_center_department_name ?></li>
      <li>费用所属中心：<?php echo $cost_center_name ?></li>
      <li>费用所属网点：<?php echo $cost_store_type_text ?></li>
      <li>付款方式：<?php echo $payment_method_text ?></li>
      <li>付款币种：<?php echo $currency_text ?></li>
      <li>备注：<?php echo $remark ?></li>
    </ul>

    <table class="table">
      <tr>
        <td colspan="11">金额详情</td>
      </tr>
      <tr>
        <th>网点名称</th>
        <th>费用类型</th>
        <th>费用开始日期</th>
        <th>费用结束日期</th>
        <th>金额</th>
        <th>WHT金额</th>
        <th>实付金额</th>
        <th>银行名称</th>
        <th>银行账户名称</th>
        <th>银行账户号</th>
        <th>联系人电话</th>
      </tr>
        <?php
            foreach ($amount_detail_item as $item) {
                echo "     
                    <tr>
                    <td>{$item['store_name']}</td>
                    <td>{$item['cost_type_text']}</td>
                    <td>{$item['cost_start_date']}</td>
                    <td>{$item['cost_end_date']}</td>
                    <td>".number_format($item['amount'],2)."</td>
                    <td>".number_format($item['wht_amount'],2)."</td>
                    <td>".number_format($item['actually_amount'],2)."</td>
                    <td>{$item['bank_name']}</td>
                    <td>{$item['bank_account_name']}</td>
                    <td>{$item['bank_account_no']}</td>
                    <td>{$item['contact_phone']}</td>
                    </tr>";
            }
        ?>
      <tr>
        <td colspan="11">金额总计</td>
      </tr>
      <tr>
        <td colspan="3">金额总计</td>
        <td colspan="3"><?php echo number_format($total_amount,2) ?></td>
        <td colspan="4">WHT金额总计	</td>
        <td><?php echo number_format($wht_total_amount,2) ?></td>
      </tr>
      <tr>
        <td colspan="3">实付金额总计</td>
        <td colspan="3"><?php echo number_format($actually_total_amount,2) ?></td>
        <td colspan="4"></td>
        <td></td>
      </tr>
      <tr>
        <td colspan="11">审批流程</td>
      </tr>
      <tr>
        <td colspan="3">步骤名称</td>
        <td colspan="3">完成时间</td>
        <td>处理人工号</td>
        <td colspan="2">处理人姓名</td>
        <td>处理结果</td>
        <td>处理意见</td>
      </tr>
        <?php
            if (!empty($auth_logs)) {
                $auth_logs = array_reverse($auth_logs);

                foreach ($auth_logs as $k => $log){
                    $rank_no = $k + 1;

                    $log['staff_id'] = $log['staff_id'] ?? '';
                    $log['staff_name'] = $log['staff_name'] ?? '';

                    echo "
                          <tr>
                            <td colspan='3'>审批人{$rank_no}</td>
                            <td colspan='3'>{$log['audit_at_datetime']}</td>
                            <td>{$log['staff_id']}</td>
                            <td colspan='2'>{$log['staff_name']}</td>
                            <td>{$log['action_name']}</td>
                            <td>{$log['info']}</td>
                          </tr>";
                }
            }
        ?>
    </table>
	</div>
</body>
</html>