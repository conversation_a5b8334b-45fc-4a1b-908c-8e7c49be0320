
<!doctype HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=no">
    <style>
        body {
            font: 3.2mm/1.2 Tahoma, sans-serif;
            color: #000;
            -webkit-print-color-adjust: exact;
        }
        body, ul, p {
            margin: 0;
            padding: 0;
        }
        ul, li {
            list-style: none;
        }
        .clearfix::after {
            display: block;
            content: "";
            clear: both;
        }
        .break-after {
            page-break-after: always;
            page-break-inside: avoid;
        }
        .tal {
            text-align: left!important;
        }
        .table {
            width: 100%;
            border-left: .5mm solid #666;
            border-right: .5mm solid #666;
            border-collapse: collapse;
            border-spacing: 0;
        }
        .table th, .table td {
            border: .3mm solid #999;
            padding: 2mm;
            text-align: center;
            word-break: break-all;
        }
        .table .bt {
            border-top: .5mm solid #666;
        }
        .table .bbn {
            border-bottom: none;
        }
        .table .cell-title {
            font-weight: 600;
            text-align: left;
        }
        .borrow-wrap {
            width: 210mm;
            height: 297mm;
            padding: 4mm;
            margin-bottom: 4mm;
            box-sizing: border-box;
        }
        .checkbox-item {
            display: inline-block;
            width: 2.5mm;
            height: 2.5mm;
            margin: 0 3mm;
            border: .3mm solid#999;
            vertical-align: bottom;
        }
        .checkbox-item.selected {
            background-color: #000;
            border-color: #000;
        }
        .signature {
            margin-top: 5mm;
            padding-left: 60%;
        }


        .borrow-header {
            width: 100%;
            margin-bottom: 2mm;
        }
        .borrow-header__left {
            color: #555;
            vertical-align: bottom;
        }

        .borrow-header__center {
            font-size: 6mm;
            font-weight: 450;
            text-align: center;
        }

        .borrow-header__right {
            text-align: right;
        }

        .borrow-header .qr {
            width: 20mm;
            height: 20mm;
        }

    </style>
</head>

<body>
<div class="borrow-wrap">
    <table class="borrow-header">
        <tr>
            <td style="width:35%; font-size: 3.4mm; font-weight: 600;" class="borrow-header__left">
                <?php echo $field['id'] ?>: <?php echo $no ?>
            </td>
            <td style="width:30%;" class="borrow-header__center"><?php echo $field['title']?></td>
            <td style="width:35%; text-align: right;" class="borrow-header__right">
                <?php echo $is_additional ? sprintf('(%s)', $field['additional']) : '' ?>
                <img src="<?php echo $qr_code ?>" class="qr" alt="二维码" />
            </td>
        </tr>
    </table>
    <table class="table">
        <tr style="font-size:3.4mm; font-weight:600;">
            <td colspan="2" class="tal bt"><?php echo $field['title']  ?></td>
            <td class="bt"><?php echo $field['id']  ?></td>
            <td class="bt"><?php echo $no ?></td>
        </tr>
        <tr>
            <th colspan="4"><?php echo $field['base_info']  ?></td>
        </tr>
        <tr>
            <td style="width:20%;"><?php echo $field['created_name']  ?></td>
            <td style="width:30%;"><?php echo htmlentities($created_name);  ?></td>
            <td style="width:20%;"><?php echo $field['created_id']  ?></td>
            <td style="width:30%;"><?php echo $created_id  ?></td>
        </tr>
        <tr>
            <td><?php echo $field['created_company_name']  ?></td>
            <td><?php echo htmlentities($created_company_name);  ?></td>
            <td><?php echo $field['created_department_name']  ?></td>
            <td><?php echo htmlentities($created_department_name);  ?></td>
        </tr>
        <tr>
            <td><?php echo $field['apply_name']  ?></td>
            <td><?php echo htmlentities($apply_name);  ?></td>
            <td><?php echo $field['apply_id']  ?></td>
            <td><?php echo $apply_id  ?></td>
        </tr>
        <tr>
            <td><?php echo $field['apply_company_name']  ?></td>
            <td><?php echo htmlentities($apply_company_name);  ?></td>
            <td><?php echo $field['apply_department_name']  ?></td>
            <td><?php echo htmlentities($apply_department_name);  ?></td>
        </tr>
        <tr>
            <td><?php echo $field['apply_store_name']  ?></td>
            <td><?php echo htmlentities($apply_store_name);  ?></td>
            <td><?php echo $field['apply_mobile']  ?></td>
            <td><?php echo $apply_mobile  ?></td>
        </tr>
        <tr>
            <td><?php echo $field['apply_center_code']  ?></td>
            <td><?php echo $apply_center_code  ?></td>
            <td><?php echo $field['date']  ?></td>
            <td><?php echo $start_at."-".$end_at  ?></td>
        </tr>
        <tr>
            <td><?php echo $field['cost_company_name']  ?></td>
            <td><?php echo htmlentities($cost_company_name) ?></td>
            <td><?php echo $field['country_code_project']  ?></td>
            <td><?php echo $field[$country_code]  ?></td>
        </tr>
        <tr>
            <th colspan="4"><?php echo $field['bank_info']  ?></td>
        </tr>
        <tr>
            <td><?php echo $field['bank_name']  ?></td>
            <td><?php echo htmlentities($bank_name);  ?></td>
            <td><?php echo $field['currency_text']  ?></td>
            <td><?php echo $currency_text ?></td>
        </tr>
        <tr>
            <td><?php echo $field['bank_account']  ?></td>
            <td><?php echo $bank_account ?></td>
            <td><?php echo $field['bank_type'] ?></td>
            <td><?php echo $bank_type ?></td>
        </tr>
    </table>

    <table class="table">
        <tr>
            <th colspan="<?php echo count($head) ?>"><?php echo $field['detail']  ?></td>
        </tr>
        <tr>
            <th><?php echo $field['detail_id']  ?></th>
            <th><?php echo $field['category_a']  ?></th>
            <th><?php echo $field['category_b']  ?></th>
            <th><?php echo $field['info']  ?></th>

            <?php
                if(!empty($field_flag['water_flag'])){
                    echo '<th>'.$field['cost_store_name'].'</th>';
                }


                if(!empty($field_flag['travel_flag'])){
                    echo '<th>'.$field['start_at'].'</th>';
                    echo '<th>'.$field['travel_start'].'</th>';
                    echo '<th>'.$field['travel_end'].'</th>';
                }

                if(!empty($field_flag['fuel_flag'])){
                    echo '<th>'.$field['fuel_start'].'</th>';
                    echo '<th>'.$field['fuel_end'].'</th>';
                    echo '<th>'.$field['fuel_mileage'].'</th>';
                }
            ?>
            <th><?php echo $field['tax_not']  ?></th>
            <th><?php echo $field['wht_tax_amount']  ?></th>
            <th><?php echo $field['tax']  ?></th>
            <th><?php echo $field['payable_amount']  ?></th>
<!--            <th>--><?php //echo $field['invoices_ids']  ?><!--</th>-->
        </tr>

        <?php
            foreach ($expense as $k=>$item){
                echo "<tr>";
                    echo "<td>".($k+1)."</td>";
                    echo "<td>".$item['category_a_text']."</td>";
                    echo "<td>".$item['category_b_text']."</td>";
                    echo "<td>".htmlentities($item['info'])."</td>";

                    if(!empty($field_flag['water_flag'])){
                        echo "<td>".$item['cost_store_name']."</td>";
                    }

                    if(!empty($field_flag['travel_flag'])){
                        echo "<td>".$item['travel_start_at']."-".$item['travel_end_at']."</td>";
                        echo "<td>".$item['travel_start']."</td>";
                        echo "<td>".$item['travel_end']."</td>";
                    }
                    if(!empty($field_flag['fuel_flag'])){
                        echo "<td>".$item['fuel_start']."</td>";
                        echo "<td>".$item['fuel_end']."</td>";
                        echo "<td>".$item['fuel_mileage']."</td>";
                    }
                    echo "<td>".number_format($item['tax_not'],2)."</td>";
                    echo "<td>".number_format($item['wht_tax_amount'],2)."</td>";
                    echo "<td>".number_format($item['tax'],2)."</td>";
                    echo "<td>".number_format($item['payable_amount'],2)."</td>";
                    //echo "<td>".$item['invoices_ids']."</td>";
                echo "</tr>";
            }
        ?>
    </table>

    <table class="table">
        <tr>
            <td colspan="2"><?php echo $field['stat']  ?></td>
            <td colspan="2"><?php echo $field['amount']  ?></td>
        </tr>
        <tr>
            <td colspan="2"><?php echo $field['travel']  ?></td>
            <td colspan="2"><?php echo $travel_amount  ?></td>
        </tr>
        <tr>
            <td colspan="2"><?php echo $field['local']?></td>
            <td colspan="2"><?php echo $local_amount  ?></td>
        </tr>
        <tr>
            <td style="width:20%;"><?php echo $field['payable_amount_all']  ?></td>
            <td style="width:30%;"><?php echo number_format($payable_amount_all,2)  ?></td>
            <td style="width:20%;"><?php echo $field['loan_amount']  ?></td>
            <td style="width:30%;"><?php echo number_format($loan_amount,2)  ?></td>
        </tr>
        <tr>
            <td><?php echo $field['other_amount']  ?></td>
            <td><?php echo number_format($other_amount,2)  ?></td>
            <td><?php echo $field['real_amount']  ?></td>
            <td><?php echo number_format($real_amount,2)  ?></td>
        </tr>
    </table>

    <?php
        if($pay_status !=1){
            $str = "";
            $str .= '<table class="table">';
                $str .= '<tr>';
                    $str.='<td colspan="4">'.$field['pay_info'].'</td>';
                $str .= '<tr>';
                $str .= '<tr>';
                    $str.='<td>'.$field['is_pay'].'</td>';
                    $str.='<td colspan="3">'.$pay_status_text.'</td>';
                $str .= '<tr>';

                $str .= '<tr>';
                    $str.='<td style="width:16%;">'.$field['pay_bank_account'].'</td>';
                    $str.='<td style="width:34%;">'.$pay_bank_account.'</td>';
                    $str.='<td style="width:16%;">'.$field['pay_bank_name'].'</td>';
                    $str.='<td style="width:34%;">'.$pay_bank_name.'</td>';
                $str .= '<tr>';

                $str .= '<tr>';
                    $str.='<td style="width:16%;">'.$field['sign_name'].'</td>';
                    $str.='<td style="width:34%;">'.htmlentities($apply_name).'</td>';
                    $str.='<td style="width:16%;">'.$field['pay_at'].'</td>';
                    $str.='<td style="width:34%;">'.$pay_at.'</td>';
                $str .= '<tr>';

                $str .= '<tr>';
                    $str.='<td>'.htmlentities($field['remark']).'</td>';
                    $str.='<td colspan="3">'.htmlentities($remark).'</td>';
                $str .= '<tr>';
            $str .= '</table>';

            echo $str;
        }
    ?>

    <table class="table" style="border-bottom:.5mm solid #666;">
        <tr>
            <th colspan="6"><?php echo $field['auth_logs']  ?></td>
        </tr>
        <tr>
            <td style="width:15%;"><?php echo $field['step']  ?></td>
            <td style="width:20%;"><?php echo $field['finished_at']  ?></td>
            <td style="width:15%;"><?php echo $field['deal_id']  ?></td>
            <td style="width:20%;"><?php echo $field['deal_name']  ?></td>
            <td style="width:15%;"><?php echo $field['deal_res']  ?></td>
            <td style="width:15%;"><?php echo $field['deal_mark']  ?></td>
        </tr>

        <?php
        foreach ($auth_logs as $k=>$item){
            echo "<tr>";
                echo "<td>".$field['approve'].($k+1)."</td>";
                echo "<td>".$item['audit_at_datetime']."</td>";
                echo "<td>".$item['staff_id']."</td>";
                echo "<td>".htmlentities($item['staff_name'])."</td>";
                echo "<td>".$item['action_name']."</td>";
                echo "<td>".htmlentities($item['info'])."</td>";
            echo "</tr>";
        }
        ?>
    </table>
</div>
</body>
</html>