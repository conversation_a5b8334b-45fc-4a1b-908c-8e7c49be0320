<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:w="urn:schemas-microsoft-com:office:word"
      xmlns="http://www.w3.org/TR/REC-html40">
<head>
    <meta http-equiv=Content-Type content="text/html; charset=utf-8">
    <meta name=ProgId content=Word.Document>
    <meta name=Generator content="Microsoft Word 14">
    <meta name=Originator content="Microsoft Word 14">
    <title></title><!--[if gte mso 9]>-->
    <xml>
        <w:WordDocument>
            <w:BrowserLevel>MicrosoftInternetExplorer4</w:BrowserLevel>
            <w:DisplayHorizontalDrawingGridEvery>0</w:DisplayHorizontalDrawingGridEvery>
            <w:DisplayVerticalDrawingGridEvery>2</w:DisplayVerticalDrawingGridEvery>
            <w:DocumentKind>DocumentNotSpecified</w:DocumentKind>
            <w:DrawingGridVerticalSpacing>7.8 磅</w:DrawingGridVerticalSpacing>
            <w:PunctuationKerning></w:PunctuationKerning>
            <w:View>Web</w:View>
            <w:Compatibility>
                <w:DontGrowAutofit/>
                <w:BalanceSingleByteDoubleByteWidth/>
                <w:DoNotExpandShiftReturn/>
                <w:UseFELayout/>
            </w:Compatibility>
            <w:Zoom>0</w:Zoom>
        </w:WordDocument>
    </xml>

    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=no">
    <style>
        body {
            font: 3.2mm/1.2 Tahoma, sans-serif;
            color: #000;
            -webkit-print-color-adjust: exact;
        }

        .table {
            width: 90%;
            border: .5mm solid #666;
            border-collapse: collapse;
            border-spacing: 0;
            margin-top: 5mm;
            table-layout: fixed;
        }

        .table th, .table td {
            border: .3mm solid #999;
            padding: 2mm;
            /*text-align: left;*/
            word-break: break-all;
        }

        .body-wrap {
            height: 297mm;
            width: 210mm;
            padding: 4mm;
            box-sizing: border-box;
            margin: 0 auto;
        }

        .table-header {
            font-weight: 600;
        }

        .title {
            font-weight: 600;
            font-size: 18px;
            height: 8mm;
        }

        .exprience {
            height: 10mm;
        }
    </style>
</head>

<body>
<div class="body-wrap">
    <table class="table">
        <tr>
            <td class="title" colspan="10">基本信息</td>
        </tr>
        <tr>
            <td colspan="2">职位名称</td>
            <td colspan="8"><?php echo $job_name ?? '' ?></td>
        </tr>
        <tr>
            <td colspan="2">公开职位名称</td>
            <td colspan="8"><?php echo $public_job_name ?? '' ?></td>
        </tr>
        <tr>
            <td colspan="2">组织&部门</td>
            <td colspan="8"><?php echo $department_name ?? '' ?></td>
        </tr>
        <tr>
            <td colspan="2">职组</td>
            <td colspan="8"><?php echo $group_name ?? '' ?></td>
        </tr>
        <tr>
            <td colspan="2">子职组</td>
            <td colspan="8"><?php echo $group_child_name ?? '' ?></td>
        </tr>
        <tr>
            <td colspan="2">职级</td>
            <td colspan="8"><?php echo $job_level ?? '' ?></td>
        </tr>
        <tr>
            <td colspan="2">职位性质</td>
            <td colspan="8"><?php echo $position_type_text ?? '' ?></td>
        </tr>
        <tr>
            <td colspan="2">成本类型</td>
            <td colspan="8"><?php echo $cost_type_text ?? '' ?></td>
        </tr>
        <tr>
            <td colspan="2">汇报职位</td>
            <td colspan="8"><?php echo $report_job_name ?? '' ?></td>
        </tr>
        <tr>
            <td colspan="2">JD名称</td>
            <td colspan="8"><?php echo $jd_name ?? '' ?></td>
        </tr>
        <tr>
            <td colspan="2">在职HC</td>
            <td colspan="8"><?php echo $work_hc_nums ?? '' ?></td>
        </tr>
        <tr>
            <td colspan="2">剩余HC</td>
            <td colspan="8"><?php echo $surplus_hc_nums ?? '' ?></td>
        </tr>
        <tr>
            <td colspan="2">计划HC</td>
            <td colspan="8"><?php echo $plan_hc_nums ?? '' ?></td>
        </tr>
        <tr>
            <td colspan="2">JD描述</td>
            <td colspan="8"><?php echo $jd_desc ?? '' ?></td>
        </tr>
        <tr>
            <td colspan="2">JD描述 备注</td>
            <td colspan="8"><?php echo $jd_desc_supply ?? '' ?></td>
        </tr>


        <tr>
            <td class="title" colspan="10">职位要求</td>
        </tr>
        <tr>
            <td colspan="2">教育背景</td>
            <td colspan="8"><?php echo $job_requirements_jybj ?? '' ?></td>
        </tr>
        <tr>
            <td colspan="2" class="exprience">专业经历</td>
            <td colspan="8" class="exprience"><?php echo $job_requirements_zyjl ?? '' ?></td>
        </tr>

        <tr>
            <td colspan="2">其他</td>
            <td colspan="8"><?php echo $job_requirements_other ?? ''  ?></td>
        </tr>

        <?php if(isset($job_competency_zy) || isset($job_competency_ld) || isset($job_competency_hx)){ ?>
        <tr>
            <td class="title" colspan="10">胜任力要求</td>
        </tr>

        <?php if(isset($job_competency_zy)){?>
        <tr>
            <!--            <td colspan="2">专业胜任力</td>-->
            <!--            <td colspan="6">描述</td>-->
            <td colspan="8" align="center" >专业胜任力</td>

            <td colspan="2">评级</td>
        </tr>
        <?php foreach ($job_competency_zy as $key => $item) {
            echo "
             <tr>
              
                <td colspan='2'>{$item["name"]}</td>
                <td colspan='6'>{$item['description']}</td>
                <td colspan='2'>{$item['level']}</td>
            </tr>
               
            ";
        } ?>
        <?php } ?>

        <?php if(isset($job_competency_ld)){?>
        <tr>
            <!--            <td colspan="2">领导胜任力</td>-->
            <!--            <td colspan="6">描述</td>-->
            <td colspan="8" align="center" >领导胜任力</td>
            <td colspan="2">评级</td>
        </tr>
        <?php foreach ($job_competency_ld as $key => $item) {
            echo "
             <tr>
              
                <td colspan='2'>{$item["name"]}</td>
                <td colspan='6'>{$item['description']}</td>
                <td colspan='2'>{$item['level']}</td>
            </tr>
               
            ";
        } ?>
        <?php } ?>

        <?php if(isset($job_competency_hx)){?>
        <tr>
            <!--            <td colspan="2">核心胜任力</td>-->
            <!--            <td colspan="8">描述</td>-->
            <td colspan="10" align="center">核心胜任力</td>

        </tr>
        <?php foreach ($job_competency_hx as $key => $item) {
            echo "
             <tr>
                <td colspan='2'>{$item["name"]}</td>
                <td colspan='6'>{$item['description']}</td>
                <td colspan='2'>" . (isset($item['level']) ? $item['level'] : '') . "</td>
            </tr>
               
            ";
        } ?>
        <?php }
        } ?>
    </table>
</div>
</body>
</html>