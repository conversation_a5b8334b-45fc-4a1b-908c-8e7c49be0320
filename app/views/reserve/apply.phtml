
<!doctype HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=no">
    <style>
        body {
            font: 3.2mm/1.2 Tahoma, sans-serif;
            color: #000;
            -webkit-print-color-adjust: exact;
        }
        body, ul, p {
            margin: 0;
            padding: 0;
        }
        ul, li {
            list-style: none;
        }
        .clearfix::after {
            display: block;
            content: "";
            clear: both;
        }
        .break-after {
            page-break-after: always;
            page-break-inside: avoid;
        }
        .tal {
            text-align: left!important;
        }
        .table {
            width: 100%;
            border-left: .5mm solid #666;
            border-right: .5mm solid #666;
            border-collapse: collapse;
            border-spacing: 0;
        }
        .table th, .table td {
            border: .3mm solid #999;
            padding: 2mm;
            text-align: center;
            word-break: break-all;
        }
        .table .bt {
            border-top: .5mm solid #666;
        }
        .table .bbn {
            border-bottom: none;
        }
        .table .cell-title {
            font-weight: 600;
            text-align: left;
        }
        .borrow-wrap {
            width: 210mm;
            height: 297mm;
            padding: 4mm;
            margin-bottom: 4mm;
            box-sizing: border-box;
        }
        .checkbox-item {
            display: inline-block;
            width: 2.5mm;
            height: 2.5mm;
            margin: 0 3mm;
            border: .3mm solid#999;
            vertical-align: bottom;
        }
        .checkbox-item.selected {
            background-color: #000;
            border-color: #000;
        }
        .signature {
            margin-top: 5mm;
            padding-left: 60%;
        }
        .AutoNewline
        {
            Word-break: break-all;/*必须*/
        }
    </style>
</head>

<body>
<div class="borrow-wrap">
    <table class="table">
        <tr style="font-size:3.4mm; font-weight:600;">
            <td colspan="2" class="tal bt"><?php echo $field['reserve_fund_apply_name'] ?></td>
            <td class="bt"><?php echo $field['id']  ?></td>
            <td class="bt"><?php echo $rfano ?></td>
        </tr>
        <tr>
            <th colspan="4"><?php echo $field['base_info']  ?></td>
        </tr>

        <tr>
            <td><?php echo $field['apply_name']  ?></td>
            <td><?php echo $create_name  ?></td>
            <td><?php echo $field['apply_id']  ?></td>
            <td><?php echo $create_id  ?></td>
        </tr>
        <tr>
            <td><?php echo $field['apply_company_name']  ?></td>
            <td><?php echo $create_company_name  ?></td>
            <td><?php echo $field['apply_department_name']  ?></td>
            <td><?php echo $create_department_name  ?></td>
        </tr>
        <tr>
            <td><?php echo $field['apply_store_name']  ?></td>
            <td><?php echo $create_store_name  ?></td>
            <td><?php echo $field['apply_date']  ?></td>
            <td><?php echo $apply_date  ?></td>
        </tr>

        <tr>
            <th colspan="4"><?php echo $field['reserve_fund_detail']  ?></td>
        </tr>
        <tr>
            <td><?php echo $field['bank_name']  ?></td>
            <td><?php echo $payee_username  ?></td>
            <td><?php echo $field['currency_text']  ?></td>
            <td><?php echo $currency_text ?></td>
        </tr>
        <tr>
            <td><?php echo $field['bank_account']  ?></td>
            <td><?php echo $payee_account ?></td>
            <td><?php echo $field['bank_type'] ?></td>
            <td><?php echo $payee_bank_text ?></td>
        </tr>
        <tr>
            <td><?php echo $field['apply_money']  ?></td>
            <td><?php echo number_format($amount,2) ?></td>
            <td></td>
            <td></td>
        </tr>
        <tr>
            <td><?php echo $field['reserve_apply_reason']  ?></td>
            <td colspan="3"><?php echo $apply_reason ?></td>
        </tr>
    </table>


    <?php
        if($pay_status !=1){
            $str = "";
            $str .= '<table class="table">';
                $str .= '<tr>';
                    $str.='<td colspan="4">'.$field['pay_info'].'</td>';
                $str .= '<tr>';
                $str .= '<tr>';
                    $str.='<td>'.$field['is_pay'].'</td>';
                    $str.='<td colspan="3">'.$pay_status_text.'</td>';
                $str .= '<tr>';

                $str .= '<tr>';
                    $str.='<td style="width:16%;">'.$field['pay_bank_account'].'</td>';
                    $str.='<td style="width:34%;">'.$pay_account.'</td>';
                    $str.='<td style="width:16%;">'.$field['pay_bank_name'].'</td>';
                    $str.='<td style="width:34%;">'.$pay_bank.'</td>';
                $str .= '<tr>';

                $str .= '<tr>';
                    $str.='<td style="width:16%;">'.$field['pay_at'].'</td>';
                    $str.='<td style="width:34%;">'.$pay_at.'</td>';
                    $str.='<td style="width:16%;"></td>';
                    $str.='<td style="width:34%;"></td>';
            $str .= '<tr>';

            $str .= '<tr>';
                    $str.='<td>'.$field['remark'].'</td>';
                    $str.='<td colspan="3">'.$operation_remark.'</td>';
                $str .= '<tr>';
            $str .= '</table>';

            echo $str;
        }
    ?>

    <table class="table" style="border-bottom:.5mm solid #666;">
        <tr>
            <th colspan="6"><?php echo $field['auth_logs']  ?></td>
        </tr>
        <tr>
            <td style="width:15%;"><?php echo $field['step']  ?></td>
            <td style="width:20%;"><?php echo $field['finished_at']  ?></td>
            <td style="width:15%;"><?php echo $field['deal_id']  ?></td>
            <td style="width:20%;"><?php echo $field['deal_name']  ?></td>
            <td style="width:15%;"><?php echo $field['deal_res']  ?></td>
            <td style="width:15%;"><?php echo $field['deal_mark']  ?></td>
        </tr>

        <?php
        foreach ($auth_logs as $k=>$item){
            echo "<tr>";
                echo "<td>".$field['approve'].($k+1)."</td>";
                echo "<td>".$item['audit_at_datetime']."</td>";
                echo "<td>".$item['staff_id']."</td>";
                echo "<td>".$item['staff_name']."</td>";
                echo "<td>".$item['action_name']."</td>";
                echo "<td>".$item['info']."</td>";
            echo "</tr>";
        }
        ?>
    </table>
</div>
</body>
</html>