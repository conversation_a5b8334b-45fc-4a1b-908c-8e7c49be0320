<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>资产管理同意书</title>
    <style>

        html,
        body,
        p,
        i {
            margin: 0;
            padding: 0;
            font-size: 4mm;
            font-family: simsun;
        }

        span {
            position: relative;
            display: inline-block;
            text-align: center;
        }

        em {
            font-style: normal;
        }

        i {
            font-style: normal;
            position: absolute;
            width: 100%;
            border-bottom: 1px dotted #000;
            bottom: 0;
            left: 0;
        }

        .box {
            width: 210mm;
            padding: 4mm 1mm;
            box-sizing: border-box;
            position: relative;
        }

        .title {
            height: 15mm;
            line-height: 15mm;
            font-size: 21px;
            font-weight: bold;
            padding-left: 4mm;
        }
        .line {
            border-bottom: 1mm solid #000;
            margin-top: -2mm;
        }
        .code {
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            margin: 5mm 0;
        }

        .ref-date {
            float: right;
            width: 55mm;
            text-align: right;
        }

        .ref {
            text-align: left;
            font-size: 16px;
            line-height: 1.5;
        }

        .date {
            text-align: left;
            font-size: 16px;
            margin: 0 0 6mm 0;
            line-height: 1.5;
        }

        .date span {
            width: auto;
        }

        .person-message {
            text-indent: 16mm;
            clear: both;
        }

        .person-message em {
            text-indent: 0;
            min-width: 45mm;
            padding: 0 4mm;
            margin: 1mm 0;
            border-bottom: 1px dotted #000;
            display: inline-block;
            text-align: center;
            vertical-align: bottom;
        }

        .img {
            width: 100%;
        }

        .photo {
            width: 40mm;
            height: 30mm;
            margin: 7mm 0 0 16mm;
        }

        .photo img {
            display: block;
            width: 100%;
            height: 100%;
        }

        .assets-apply-list {
            margin: 2mm 0 2mm 16mm;
        }
        .assets table {
            border-top: 1px solid Black;
            border-left: 1px solid Black;
            width: 100%;
            margin-bottom: 36mm;
        }

        .assets table tr td, .assets table tr th{
            border-right:1px solid Black;
            border-bottom: 1px solid Black;
            font-size: 14px;
            text-align: center;
            height: 8mm;
        }

        .bulletin-1 {
            line-height: 1.5;
            text-indent: 14mm;
            margin: 5mm 0;
            font-size: 16px;
        }

        .bulletin-2 {
            line-height: 1.5;
            text-indent: 14mm;
            margin-bottom: 6mm;
            font-size: 16px;
        }

        .sign {
            text-align: right;
            margin-bottom: 1mm;
        }

        /* .sign span,
        .sign-1 span {
            width: 50mm;
        } */

        .sign-1 {
            text-align: right;
            padding-right: 22mm;
            font-size: 21px;
        }
        .bottom {
            text-align: center;
            margin-top: 20mm;
            font-size: 16px;
            padding: 0 16mm;
        }

        .content {
            clear: both;
            margin-bottom: 3mm;
        }

        .content .content-id {
            width: 100%;
        }

        .bulletin-22 {
            line-height: 7mm;
            text-indent: 16mm;
            margin-bottom: 10mm;
        }

        .content-id table {
            width: 100%;
        }

        .content-id table tr td {
            position: relative;
            width: 50%;
        }

        .content-id table tr td .i-right {
            position: relative;
            font-style: normal;
            display: inline-block;
            width: 75%;
            top: 14px;
            border-bottom: 1px dotted #000;
        }

        .content-id table tr td .i-left {
            font-style: normal;
            position: absolute;
            width: 75%;
            line-height: 35px;
            border-bottom: 1px dotted #000;
            bottom: 0;
            left: 20mm;
        }

        .content-id span {
            display: inline-block;
            width: 48%;
            line-height: 45px;
            font-weight: 400;
        }

        .content table {
            border: 0px;
        }

        .content-id table tr td {
            text-align: left;
        }
        .content-autograph {
            clear: both;
        }
        .content-autograph table {
            border: 0px;
            margin-left: 100mm;
            width: 90mm;
        }
        .content-autograph table tr {
            height: 8mm;
        }
        .content-autograph table tr td {
            position: relative;
            min-width: 20mm;
            max-width: 33mm;
        }
        .word-break {
            word-wrap: break-word;
            word-break: break-all;
        }
    </style>
</head>

<body>

<div class="box">
    <div>
        <p style="font-size: 16px; text-align: right;margin-bottom: 2mm;"><?= $company_name ?></p>
        <p style="font-size: 16px; text-align: right;margin-bottom: 2mm">日期<?= $date ?></p>
    </div>
    <main class="content">
        <table style="width: 100%;">
            <tr>
                <td style="text-align: left;">
                    <div style="font-size: 16px;">
                        <span style="font-weight: bold;">姓名:</span>
                        <span><?= $name ?></span>
                    </div>
                </td>
                <td></td>
                <td style="text-align: left;">
                    <div style="font-size: 16px;">
                        <span style="font-weight: bold;">身份证:</span>
                        <span><?= $identity ?></span>
                    </div>
                </td>
            </tr>
            <tr>
                <td style="text-align: left;">
                    <div style="font-size: 16px;">
                        <span style="font-weight: bold;">职位:</span>
                        <span><?= $job_title ?></span>
                    </div>
                </td>
                <td></td>
                <td style="text-align: left;">
                    <div style="font-size: 16px;">
                        <span style="font-weight: bold;">工号:</span>
                        <span><?= $staff_info_id ?></span>
                    </div>
                </td>
            </tr>
            <tr>
                <td style="text-align: left;">
                    <div style="font-size: 16px;">
                        <span style="font-weight: bold;">部门:</span>
                        <span><?= $node_department_name ?></span>
                    </div>
                </td>
                <td></td>
                <td style="text-align: left;">
                    <div style="font-size: 16px;">
                        <span style="font-weight: bold;">所属网点:</span>
                        <span><?= $sys_store_name ?></span>
                    </div>
                </td>
            </tr>
        </table>
        <p class="bulletin-1">
            本人入职<?= $company_name ?>公司后，如领取公司下发的资产，本人承诺会好好管理自己资产，不会转移给公司外的员工使用，资产已接收则视为资产完好，如需转移公司下发的个人资产、公共资产给公司内的其他人使用，要在BY操作资产转移流程，保证系统和实物对应，如不按要求操作，则系统上资产持有人对资产负责；在离职前未归还系统名下资产、没有完好归还、归还时无法满足正常使用，需要维修等产生的费用。以上情况，本人同意对公司承担赔偿责任；本人亦同意公司从薪酬中直接扣除上述资产的公司购买费用或维修等费用来赔偿公司的损失。

        </p>
        <p class="bulletin-2">
            此同意书是由本人的意愿，已阅读并检查了本书的内容，发现完全符合本人的意图，因此签署为证据以及把本同意书算成本人签约的一部分，确定任何情况下都不会撤销本同意书。
        </p>
        <div class="assets">
            <table style="border-top: 1px solid Black;border-left: 1px solid Black;" cellpadding="0" cellspacing="0">
                <thead>
                <tr>
                    <th>序号</th>
                    <th>资产名称</th>
                    <th>Asset编码</th>
                    <th>SN编码</th>
                    <th>数 量</th>
                    <th>申请日期</th>
                    <th>价格 <?= $currency_symbol ?></th>
                    <th>扣款金额 <?= $currency_symbol ?></th>
                </tr>
                </thead>

                <?php foreach ($items as $k => $item) { ?>
                    <tr>
                        <td style="width: 13mm;" class="word-break"><?= $item['serial_number'] ?></td>
                        <td style="width: 75mm;" class="word-break"><?= $item['name_zh'] ?></td>
                        <td style="width: 42mm;" class="word-break"><?= $item['asset_code'] ?></td>
                        <td style="width: 35mm;"><?= $item['sn_code'] ?></td>
                        <td style="width: 15mm;" class="word-break"><?= $item['nums'] ?></td>
                        <td style="width: 25mm;"><?= $item['receipted_at'] ?></td>
                        <td style="width: 25mm;" class="word-break"><?= $item['price'] ?></td>
                        <td style="width: 25mm;" class="word-break"><?= $item['deduction_price'] ?></td>
                    </tr>
                <?php } ?>
            </table>
        </div>
    </main>
</div>
<!-- 页脚 -->

</body>

</html>

