<?php

namespace App\Traits;

use <PERSON><PERSON><PERSON><PERSON>\JWT\Builder;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Parser;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Signer\Hmac\Sha512;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Signer\Key;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Token;
use <PERSON><PERSON><PERSON><PERSON>\JWT\ValidationData;
use <PERSON>\Uuid\Uuid;

trait TokenTrait
{
    private static $token_platform = 'pc';
    private static $token_type = 1;// token类型: 1-内部用户; 2-外部用户签约过程管理

    /**
     * Returns the JWT token object
     *
     * @param string $token
     *
     * @return Token
     */
    protected function parseToken($token)
    {
        return (new Parser())->parse($token);
    }

    /**
     * Returns the default audience for the tokens
     *
     * @return string
     */
    protected function getTokenAudience()
    {
        /** @var string $audience */
        $audience = env('TOKEN_AUDIENCE', 'https://phalconphp.com');

        return $audience;
    }

    /**
     * Returns the time the token is issued at
     *
     * @return int
     */
    protected function getTokenTimeIssuedAt()
    {
        return time();
    }

    /**
     * Returns the time drift i.e. token will be valid not before
     *
     * @return int
     */
    protected function getTokenTimeNotBefore()
    {
        return (time() + env('TOKEN_NOT_BEFORE', 0));
    }

    /**
     * Returns the expiry time for the token
     *
     * @return int
     */
    protected function getTokenTimeExpiration()
    {
        $expiration = $this->getTokenValidityPeriod();
        return (time() + $expiration);
    }

    /**
     * Returns the Validity period for the token
     *
     * @return int
     */
    protected function getTokenValidityPeriod()
    {
        // 内部用户: pc 和 mobile 平台的过期时间分别管理
        if (self::$token_type == 1) {
            $expiration_key = self::$token_platform == 'pc' ? 'TOKEN_EXPIRATION' : 'MOBILE_TOKEN_EXPIRATION';
        } else {
            $expiration_key = 'SIGN_TOKEN_EXPIRATION';
        }

        return env($expiration_key, 86400);
    }

    /**
     * Set the JWT token platform
     *
     * @param string $platform
     * @return void
     */
    protected function setTokenPlatform(string $platform = 'pc')
    {
        self::$token_platform = $platform;
    }

    /**
     * Set the JWT token type
     *
     * @param int $token_type
     * @return void
     */
    protected function setTokenType(int $token_type = 1)
    {
        self::$token_type = $token_type;
    }

    /**
     * @return Key
     */
    protected function getKey()
    {
        return new Key(env('JWT_KEY', 'changeme！'));
    }

    /**
     * @return mixed
     */
    protected function getIssuer()
    {
        return env('TOKEN_ISSUER', 'https://phalconphp.com');
    }

    /**
     * @return string
     * @throws \Exception
     */
    public function getTokenId(): string
    {
        return Uuid::uuid4()->toString();
    }

    /**
     * @param $user
     * @return Token
     * @throws \Exception
     */
    protected function generateToken($user)
    {
        $signer = new Sha512();
        $builder = new Builder();
        return $builder
            ->issuedBy($this->getIssuer())
            ->permittedFor($this->getTokenAudience())
            ->identifiedBy($this->getTokenId(), true)
            ->issuedAt($this->getTokenTimeIssuedAt())
            ->canOnlyBeUsedAfter($this->getTokenTimeNotBefore())
            ->expiresAt($this->getTokenTimeExpiration())
            ->withClaim('uid', $user['id'])
            ->withClaim('platform', self::$token_platform)
            ->getToken($signer, $this->getKey());
    }

    /**
     * @param Token $token
     * @return mixed
     */
    protected function validateToken($token)
    {
        $signer  = new Sha512();
        $data = new ValidationData(); // It will use the current time to validate (iat, nbf and exp)
        $data->setIssuer($this->getIssuer());
        $data->setAudience($this->getTokenAudience());

        return $token->verify($signer, $this->getKey()) && $token->validate($data);
    }
}