<?php

namespace App\Models;

use App\Library\BaseModel;

/**
 * @name Base
 * @desc 基类模型
 * <AUTHOR>
 */
class Base extends BaseModel
{
    protected $_db = null; //数据库实例

    public function get_db($db)
    {
        return $this->getDI()->get($db);
    }

    /**
     * @param array|null $data
     * @param null $whiteList
     * @return mixed
     */
    public function i_create(array $data = null, $whiteList = null)
    {
        if ((is_array($data) or is_object($data)) && count($data) > 0) {
            $attributes = $this->getModelsMetaData()->getAttributes($this);
        }
        return $this->create($data, $whiteList);
    }

    /**
     * 封装phalcon model的update方法，实现仅更新数据变更字段，而非所有字段更新
     *
     * @param array|null $data
     * @param null $whiteList
     * @return bool
     */
    public function i_update(array $data = null, $whiteList = null)
    {
        if ((is_array($data) or is_object($data)) && count($data) > 0) {
            $attributes = $this->getModelsMetaData()->getAttributes($this);
            $this->skipAttributesOnUpdate(array_diff($attributes, array_keys($data)));
        }
        return $this->update($data, $whiteList);
    }

    /**
     * Notes: 获取json字符串格式的错误信息，方便直接打入日志中
     *
     * @return false|string
     */
    public function getErrorMessagesJson()
    {
        $errors = [];
        foreach ($this->getMessages() as $message) {
            $errors[] = $message;
        }
        return json_encode($errors, JSON_UNESCAPED_UNICODE);
    }

    /**
     * Notes: 获取字符串格式的错误信息，方便输出信息拼接
     *
     * @return string
     */
    public function getErrorMessagesString()
    {
        $errors = [];
        $messages = $this->getMessages() ?? [];
        foreach ($messages as $message) {
            $errors[] = $message;
        }
        return implode(';', $errors);
    }
}
