<?php
namespace App\Models\backyard;

use App\Models\Base;

/**
 * KPI模版表
 * Class KpiTemplateModel
 * @package App\Models\backyard
 */
class KpiTemplateModel extends Base
{
    /**
     * Initialize method for model.
     */
    public function initialize()
    {
        $this->setConnectionService('db_backyard');
        $this->setSource('kpi_template');
        $this->hasMany(
            'id',
            KpiTemplateIndicatorsRelModel::class,
            'template_id', [
                "alias" => "indicators",
            ]
        );
    }
}
