<?php


namespace App\Models\backyard;

use App\Models\Base;

class HrStaffAnnexInfoModel extends Base
{
    // 身份证
    const TYPE_ID_CARD = 1;
    // 备用银行卡
    const TYPE_BANK_CARD = 2;
    // 社保卡号
    const TYPE_SOCIAL_SECURITY = 3;
    // 公积金
    const TYPE_FUND = 4;
    // 医保
    const TYPE_MEDICAL_INSURANCE = 5;
    // 税卡号
    const TYPE_TAX_CARD = 6;

    // ph 备用银行卡
    const BACK_BANK_CARD = 'backup_bank_card';

    // 待审核
    const AUDIT_STATE_NOT_REVIEWED = 0;
    // 审核通过
    const AUDIT_STATE_PASSED = 1;
    // 审核未通过
    const AUDIT_STATE_REJECT = 2;
    // 待上传
    const AUDIT_STATE_TO_BE_UPLOAD = 999;

    // ai 审核通过
    const AI_AUDIT_STATE_PASSED = 1;
    // ai 审核拒绝
    const AI_AUDIT_STATE_REJECT = 2;

    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_rbackyard');
        // setSource()方法指定数据库表
        $this->setSource('hr_staff_annex_info');
    }
}