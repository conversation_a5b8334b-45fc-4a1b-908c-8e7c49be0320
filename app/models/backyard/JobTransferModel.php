<?php

namespace App\Models\backyard;

use App\Models\Base;

/**
 * 员工转岗
 * Class JobTransferModel
 * @package App\Models\backyard
 */
class JobTransferModel extends Base
{
    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_rbackyard');
        // setSource()方法指定数据库表
        $this->setSource('job_transfer');
    }

    //转岗申请来源
    public const TRANSFER_TYPE_FRONT_LINE = 1;      //一线转岗
    public const TRANSFER_TYPE_NOT_FRONT_LINE = 2;  //非一线转岗
    public const TRANSFER_TYPE_SPECIAL = 3;         //特殊转岗

    //转岗状态
    const JOB_TRANSFER_STATE_TO_BE_TRANSFERRED = 1; //待转岗
    const JOB_TRANSFER_STATE_NOT_TRANSFERRED = 2;   //未转岗
    const JOB_TRANSFER_STATE_COMPLETED = 3;         //已转岗
    const JOB_TRANSFER_STATE_TRANSFER_ERR = 4;      //转岗失败

    const VEHICLE_SOURCE_PERSONAL_CODE = 1;            // 使用个人车辆
    const VEHICLE_SOURCE_RENTAL_CODE = 2;              // 租用公司车辆
    const VEHICLE_SOURCE_BORROW_CODE = 3;              // 借用车辆

    //转岗原因
    const JOB_TRANSFER_REASON_STORE_INTEGRATION = 1;   //网点融合转岗
    const JOB_TRANSFER_REASON_STORE_NEWLY_OPENED = 2;  //新开网点转岗
    const JOB_TRANSFER_REASON_ADJUST_SERVICE_AREA = 3; //调整服务区域转岗
    const JOB_TRANSFER_REASON_STORE_UPGRADE = 4;       //升级网点转岗
    const JOB_TRANSFER_REASON_INSUFFICIENT_STAFF = 5;  //人员不够
    const JOB_TRANSFER_REASON_OTHERS = 99;             //其他
}