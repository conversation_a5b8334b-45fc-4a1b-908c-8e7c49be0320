<?php
namespace App\Models\backyard;

use App\Models\Base;

/**
 * KPI推送通知到上级-用于限制目标制定人打卡
 * Class KpiLeaderMsgSendLogModel
 * @package App\Models\backyard
 */
class KpiLeaderMsgSendLogModel extends Base
{
    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_backyard');
        // setSource()方法指定数据库表
        $this->setSource('kpi_leader_msg_send_log');
    }
}