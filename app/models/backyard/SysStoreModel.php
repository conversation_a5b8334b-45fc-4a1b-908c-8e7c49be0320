<?php

namespace App\Models\backyard;

use App\Models\Base;

/**
 * 网点表
 * Class SysStoreModel
 * @package App\Models\backyard
 */
class SysStoreModel extends Base
{
    const STATE_ACTIVATION_YES = 1;
    const STATE_ACTIVATION_NO = 2;
    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_rbackyard');
        // setSource()方法指定数据库表
        $this->setSource('sys_store');
    }
}