<?php

namespace App\Models\backyard;

use App\Library\Enums\StaffInfoEnums;
use App\Models\Base;

/**
 * 员工表
 * Class HrStaffInfoModel
 * @package App\Models\backyard
 */
class HrStaffInfoModel extends Base
{
    public const STATE_ON_JOB = 1;               //在职
    public const STATE_RESIGN = 2;               //离职
    public const STATE_SUSPENSION = 3;           //停职

    //员工属性
    const FORMAL_OS = 0; //外协
    const FORMAL_NORMAL = 1; //正式在编
    const FORMAL_INTERN = 4; //实习生
    const FORMAL_FRANCHISEE = 2; //加盟商
    const FORMAL_FRANCHISEE_OTHER = 3; //其他加盟商

    //是否子账号
    const IS_NOT_SUB_STAFF = 0;// 非子账号
    const IS_SUB_STAFF = 1;// 是子账号

    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_rbackyard');
        // setSource()方法指定数据库表
        $this->setSource('hr_staff_info');
    }

    /**
     * 获取国家code
     * @return int
     */
    public function getWorkingCountryCode(): int
    {
        $countryCode = 0;
        switch (strtolower(env('country_code', 'Th'))) {
            case 'th':
                $countryCode = StaffInfoEnums::WORKING_COUNTRY_TH;
                break;
            case 'ph':
                $countryCode = StaffInfoEnums::WORKING_COUNTRY_PH;
                break;
            case 'my':
                $countryCode = StaffInfoEnums::WORKING_COUNTRY_MY;
                break;
            case 'la':
                $countryCode = StaffInfoEnums::WORKING_COUNTRY_LA;
                break;
            case 'vn':
                $countryCode = StaffInfoEnums::WORKING_COUNTRY_VN;
                break;
            case 'id':
                $countryCode = StaffInfoEnums::WORKING_COUNTRY_ID;
                break;
            default:
                break;
        }
        return $countryCode;
    }
}