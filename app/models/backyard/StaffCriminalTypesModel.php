<?php

namespace App\Models\backyard;

use App\Models\Base;

/**
 * 员工犯罪记录类型
 * Class StaffCriminalTypesModel
 * @package App\Models\backyard
 */
class StaffCriminalTypesModel extends Base
{
    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_rbackyard');
        // setSource()方法指定数据库表
        $this->setSource('staff_criminal_types');
    }
}