<?php


namespace App\Models\backyard;

use App\Models\Base;

class RolesModel extends Base
{
    const ROLE_CENTRAL_CONTROL_DISP_OFFICER = 26; //汽运中控专员
    const ROLE_HRBP = 68; //HRBP
    const ROLE_HR_SERVICE = 77; //hr Service
    const ROLE_CENTRAL_CONTROL_DISPATCHING_MANAGER = 27; //汽运中控经理

    /**
     * @description 获取
     * @return array
     */
    public static function getRolesForViewableWorkflow(): array
    {
        return [
            self::ROLE_CENTRAL_CONTROL_DISP_OFFICER,
            self::ROLE_HRBP,
            self::ROLE_HR_SERVICE,
            self::ROLE_CENTRAL_CONTROL_DISPATCHING_MANAGER,
        ];
    }


    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_rbackyard');
        // setSource()方法指定数据库表
        $this->setSource('roles');
    }
}