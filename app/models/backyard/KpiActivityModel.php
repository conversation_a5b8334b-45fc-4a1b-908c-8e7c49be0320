<?php
namespace App\Models\backyard;

use App\Models\Base;

/**
 * KPI目标活动
 * Class KpiActivityModel
 * @package App\Models\backyard
 */
class KpiActivityModel extends Base
{
    /**
     * Initialize method for model.
     */
    public function initialize()
    {
        $this->setConnectionService('db_backyard');
        $this->setSource('kpi_activity');
        $this->hasMany(
            'id',
            KpiValuesModel::class,
            'activity_id', [
                "alias" => "values",
            ]
        );
    }
}
