<?php

namespace App\Models\backyard;

use App\Models\Base;

/**
 * 员工学历表是和简历id关联，需要先查简历id和工号的关联
 * Class HrResumeEducationModel
 * @package App\Models\backyard
 */
class HrResumeEducationModel extends Base
{
    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_rbackyard');
        // setSource()方法指定数据库表
        $this->setSource('hr_resume_education');
    }
}