<?php

namespace app\models\backyard;

use App\Models\Base;


/**
 * 工具号表
 * Class ToolStaffInfoModel
 */
class ToolStaffInfoModel extends Base
{
    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_backyard');
        // setSource()方法指定数据库表
        $this->setSource('tool_staff_info');
    }

    /**
     * 获取工具号员工信息
     * @param $staffInfoId
     * @return array
     */
    public function getToolStaffInfo($staffInfoId): array
    {
        if (!$staffInfoId) {
            return [];
        }

        $toolStaffInfo = self::findFirst([
            'columns'    => 'staff_info_id, sys_department_id, node_department_id,name,job_title',
            'conditions' => 'staff_info_id = :staff_info_id:',
            'bind'       => [
                'staff_info_id' => $staffInfoId,
            ],
        ]);

        return $toolStaffInfo ? $toolStaffInfo->toArray() : [];
    }
}