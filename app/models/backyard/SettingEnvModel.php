<?php

namespace App\Models\backyard;

use App\Models\Base;

/**
 * BY系统设置表
 * Class SettingEnvModel
 * @package App\Models\backyard
 */
class SettingEnvModel extends Base
{
    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        parent::initialize();

        $this->setConnectionService('db_backyard');

        // setSource()方法指定数据库表
        $this->setSource('setting_env');
    }

    public function getValByCode(string $code, $default = '')
    {
        if (empty($code)) {
            return $default;
        }

        $res = self::findFirst([
            'conditions' => 'code = :code:',
            'bind' => ['code' => $code],
            'columns' => ['set_val']
        ]);

        return !empty($res) ? $res->set_val : $default;
    }

    /**
     * 获取配置项
     * @param  $code
     * @param null $separator
     * @return array|false|string|string[]
     */
    public function getSetVal($code, $separator = null)
    {
        if (empty($code)) {
            return '';
        }
        $setting = SettingEnvModel::findFirst([
            'conditions' => "code = :code:",
            'bind'       => ['code' => $code],
            'columns'    => ['set_val'],
        ]);

        if ($separator !== null) {
            return !empty($setting) ? explode($separator, $setting->set_val) : [];
        }

        return !empty($setting) ? $setting->set_val : '';
    }

    /**
     * 获取多个配置项
     * @param array $codes
     * @return array
     */
    public function listByCode(array $codes = []): array
    {
        if (empty($codes) || !is_array($codes)) {
            return [];
        }

        return $this->find([
            'conditions' => 'code in ({codes:array})',
            'bind'       => ['codes' => $codes],
            'columns'    => ['set_val', 'code'],
        ])->toArray();
    }
}