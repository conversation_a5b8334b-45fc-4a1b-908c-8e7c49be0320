<?php
namespace App\Models\fle;
use App\Models\Base;


class StaffAccountModel  extends Base
{
    public function initialize()
    {
        $this->setConnectionService('db_fle');
        // setSource()方法指定数据库表
        $this->setSource('staff_account');
    }

    //员工登录服务端类型
    public static $equipment_type = [
        'kit'         => 1,
        'backyard'    => 3,
    ];

    /**
     * 获取员工的语言环境
     * @param $staff_info_id
     * @return mixed|string
     */
    public static function getAcceptLanguage($staff_info_id)
    {
        $staffAccount = self::findFirst([
            'conditions' => ' staff_info_id = :staff_id: and equipment_type in({equipment_types:array})',
            'bind'       => ['staff_id'        => $staff_info_id,
                             'equipment_types' => [self::$equipment_type['kit'], self::$equipment_type['backyard']],
            ],
            'order'      => 'updated_at desc',
            'columns'    => 'accept_language',
        ]);

        if ($staffAccount) {
            $staffAccount = $staffAccount->toArray();
            if (in_array($staffAccount['accept_language'], ['th', 'th-CN'])) {
                $language = 'th';
            } else {
                $language = $staffAccount['accept_language'] ?? 'en';
            }
        } else {
            $language = 'en';
        }
        return $language;
    }
}