<?php
namespace App\Models\oa;

use App\Library\Enums\GlobalEnums;
use App\Library\Enums\MaterialEnums;
use App\Models\Base;
use App\Modules\Material\Models\MaterialAttachmentModel;


class MaterialPackageAllotModel extends Base
{
    /**
     * Initialize method for model.
     */
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        $this->setSource('material_package_allot');

        //获取调拨单-附件信息
        $this->hasMany(
            'id',
            MaterialAttachmentModel::class,
            'oss_bucket_key', [
                'params' => [
                    'conditions' => 'oss_bucket_type = ' . MaterialEnums::OSS_MATERIAL_TYPE_PACKAGE_ALLOT . ' and deleted = ' . GlobalEnums::IS_NO_DELETED
                ],
                'alias'  => 'Attachments',
            ]
        );

        //获取调拨单-调拨信息
        $this->hasMany(
            'id',
            MaterialPackageAllotSkuModel::class,
            'allot_id', [
                'params' => [
                    'order' => 'barcode ASC'
                ],
                'alias'  => 'Sku',
            ]
        );

        //获取调拨单-快递信息
        $this->hasMany(
            'id',
            MaterialPackageAllotExpressModel::class,
            'allot_id', [
                'params' => [],
                'alias'  => 'Express',
            ]
        );
    }

}
