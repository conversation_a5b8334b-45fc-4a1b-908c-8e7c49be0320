<?php
namespace App\Models\oa;

use App\Library\Enums\GlobalEnums;
use App\Library\Enums\MaterialEnums;
use App\Models\Base;
use App\Modules\Material\Models\MaterialAttachmentModel;

class MaterialAssetReturnModel extends Base
{
    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_oa');

        // setSource()方法指定数据库表
        $this->setSource('material_asset_return');

        //退回资产-申请附件
        $this->hasMany(
            'id',
            MaterialAttachmentModel::class,
            'oss_bucket_key', [
                'params' => [
                    'conditions' => 'oss_bucket_type = ' . MaterialEnums::OSS_MATERIAL_TYPE_RETURN_APPLY . ' and deleted=' . GlobalEnums::IS_NO_DELETED
                ],
                'alias' => 'ApplyAttachment',
            ]
        );

        //退回资产-处理附件
        $this->hasMany(
            'id',
            MaterialAttachmentModel::class,
            'oss_bucket_key', [
                'params' => [
                    'conditions' => 'oss_bucket_type = ' . MaterialEnums::OSS_MATERIAL_TYPE_RETURN_HANDLE . ' and deleted=' . GlobalEnums::IS_NO_DELETED
                ],
                'alias' => 'HandleAttachment',
            ]
        );

        //退回资产-操作记录表
        $this->hasMany(
            'id',
            MaterialAssetReturnRecordModel::class,
            'return_id', [
                'params' => [],
                'alias' => 'ReturnRecord',
            ]
        );

        //退回申请-关联的退回类型
        $this->hasOne(
            'type_id',
            MaterialSetReturnRepairTypeModel::class,
            'id',
            [
                'alias' => 'TypeInfo'
            ]
        );

        //退回申请-关联的处理部门
        $this->hasOne(
            'now_group_id',
            MaterialSetReturnRepairGroupModel::class,
            'id',
            [
                'alias' => 'GroupInfo'
            ]
        );
    }

}
