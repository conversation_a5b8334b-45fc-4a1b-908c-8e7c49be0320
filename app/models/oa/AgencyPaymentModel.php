<?php
namespace App\Models\oa;

use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Models\Base;

/**
 * 代理支付批次表
 * Class AgencyPaymentModel
 * @package App\Models\oa
 */
class AgencyPaymentModel extends Base
{
    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_oa');

        // setSource()方法指定数据库表
        $this->setSource('agency_payment');

        //代理支付明细
        $this->hasMany(
            'id',
            AgencyPaymentDetailModel::class,
            'agency_payment_id', [
               'alias' => 'Details',
            ]
        );

        //附件
        $this->hasMany(
            'id',
            SysAttachmentModel::class,
            'oss_bucket_key',
            [
                'params' => [
                    'conditions' => 'oss_bucket_type in( ' . Enums::OSS_BUCKET_TYPE_AGENCY_PAYMENT_ATTACHMENT . ',' . Enums::OSS_BUCKET_TYPE_AGENCY_PAYMENT_SUPPLEMENT_ATTACHMENT. ')' . ' and deleted = ' . GlobalEnums::IS_NO_DELETED
                ],
                'alias' => 'Attachments',
            ]
        );
    }
}
