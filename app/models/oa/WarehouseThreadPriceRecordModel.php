<?php
/**
 * 仓库管理-仓库线索-报价记录
 */

namespace App\Models\oa;

use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Models\Base;

/**
 *
 * Class WarehouseThreadPriceRecordModel
 *
 * @package App\Models\oa
 */
class WarehouseThreadPriceRecordModel extends Base
{
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        $this->setSource('warehouse_thread_price_record');

        //附件
        $this->hasMany(
            'id',
            SysAttachmentModel::class,
            'oss_bucket_key', [
                'params' => [
                    'conditions' => 'oss_bucket_type = ' . Enums::OSS_BUCKET_TYPE_WAREHOUSE_THREAD_PRICE_FILE . ' and sub_type = ' . Enums::OSS_SUB_TYPE_WAREHOUSE_THREAD_PRICE . ' and deleted = ' . GlobalEnums::IS_NO_DELETED
                ],
                'alias' => 'Attachment',
            ]
        );

        //上次报价-附件
        $this->hasMany(
            'id',
            SysAttachmentModel::class,
            'oss_bucket_key', [
                'params' => [
                    'conditions' => 'oss_bucket_type = ' . Enums::OSS_BUCKET_TYPE_WAREHOUSE_THREAD_PRICE_FILE . ' and sub_type = ' . Enums::OSS_SUB_TYPE_WAREHOUSE_THREAD_LAST_PRICE . ' and deleted = ' . GlobalEnums::IS_NO_DELETED
                ],
                'alias' => 'LastPriceAttachment',
            ]
        );
    }

}
