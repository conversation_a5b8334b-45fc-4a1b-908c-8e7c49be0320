<?php
namespace App\Models\oa;

use App\Models\Base;

/**
 * 支付模块-FlashPay-关联在线支付
 * Class PaymentOnlinePayModel
 * @package App\Models\oa
 */
class PaymentOnlinePayModel extends Base
{
    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_oa');

        // setSource()方法指定数据库表
        $this->setSource('payment_online_pay');
    }
}
