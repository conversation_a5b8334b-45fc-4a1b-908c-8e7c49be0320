<?php
/**
 * Created by PhpStorm.
 * Date: 2023/11/24
 * Time: 15:25
 */

namespace App\Models\oa;

use App\Models\Base;

class ContractElectronicSignLogModel extends Base
{
    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        // setSource()方法指定数据库表
        $this->setSource('contract_electronic_sign_log');

    }

}
