<?php

namespace App\Models\oa;

use App\Models\Base;

/**
 * 贪污案件表-各种附件表
 */
class CorruptionAnnexModel extends Base
{

    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        // setSource()方法指定数据库表
        $this->setSource('corruption_annex');
    }

    const ANNEX_TYPE_1 = 1; // 案件主表附件
    const ANNEX_TYPE_2 = 2; // 审讯计划表附件
    const ANNEX_TYPE_3 = 3; // 结案表附件
    const ANNEX_TYPE_4 = 4; // 报警授权表附件
    const ANNEX_TYPE_5 = 5; // 报警信息附件
    const ANNEX_TYPE_6 = 6; // 赔偿信息表附件
    const ANNEX_TYPE_7 = 7; // 赔偿信息付款凭证附件

}
