<?php
/**
 * Created by PhpStorm.
 * Date: 2023/7/7
 * Time: 15:25
 */

namespace App\Models\oa;

use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Models\Base;

class ContractElectronicSignInfoModel extends Base
{
    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        // setSource()方法指定数据库表
        $this->setSource('contract_electronic_sign_info');

        $this->hasOne(
            'electronic_no',
            ContractElectronicModel::class,
            'no',
            [
                'alias' => 'ElectronicContract',
            ]
        );

        // 甲方上传的相关资料
        $this->hasMany(
            'id',
            SysAttachmentModel::class,
            'oss_bucket_key', [
                'alias' => 'RelatedFileList',
                'params' => [
                    'conditions' => 'oss_bucket_type = :oss_bucket_type: AND deleted = :deleted:',
                    'bind' => ['oss_bucket_type' => Enums::OSS_BUCKET_TYPE_ELECTRONIC_CONTRACT_CLIENT_RELATED_FILE, 'deleted' => GlobalEnums::IS_NO_DELETED],
                    'order' => 'id ASC'
                ]
            ]
        );
    }
}
