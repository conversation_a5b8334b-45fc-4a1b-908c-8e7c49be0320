<?php

namespace App\Models\oa;

use App\Models\Base;
use App\Library\ErrCode;
use App\Library\Enums;
use App\Library\Enums\BankFlowEnums;
use App\Library\Enums\ChequeEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\Exception\BusinessException;
use App\Modules\BankFlow\Models\BankFlowOaModel;
use App\Modules\Payment\Models\PaymentStoreRentingDetail;
use App\Modules\Payment\Models\PaymentStoreRentingPay;
use App\Modules\Payment\Services\StoreRentingListService;
use App\Modules\Payment\Models\PaymentStoreRenting;
use App\Library\CInterface\BankFlowModelInterface;

class PaymentStoreRentingDetailModel extends Base implements BankFlowModelInterface
{
    private static $apply_no_by_code;
    private static $cheque_code;
    private static $detail_cheque_code;
    private static $detail_by_all_cheque_code;
    private static $apply_no_all; //本次上传对应的全部业务单申请单

    public function initialize()
    {
        $this->setConnectionService('db_oa');

        $this->setSource('payment_store_renting_detail');

        $this->hasOne(
            'store_renting_id',
            PaymentStoreRenting::class,
            'id',
            [
                'alias' => 'Main'
            ]
        );

    }

    /**
     * 通过单号获得Model
     * @param string $no
     * @return object
     */
    public function getModelByNo(string $no)
    {
        if (empty($no)) {
            throw new BusinessException('传入的支票号不能为空', ErrCode::$CHEQUE_ACCOUNT_RELEASE_STATUS_SAVE_ERROR);
        }
        $bank_flow_oa = BankFlowOaModel::findFirst([
            'conditions' => 'no = :no: and is_cancel=0',
            'bind'       => ['no' => $no]
        ]);
        if (empty($bank_flow_oa)) {
            throw new BusinessException('传入的支票号查询数据不存在' . $no, ErrCode::$CHEQUE_ACCOUNT_RELEASE_STATUS_SAVE_ERROR);
        }
        $bank_flow_oa_rel = BankFlowOaRelModel::find([
            'conditions' => 'bank_flow_oa_id = :bank_flow_oa_id:',
            'bind'       => ['bank_flow_oa_id' => $bank_flow_oa->id]
        ])->toArray();

        if (empty($bank_flow_oa_rel)) {
            throw new BusinessException('行ID数据不存在' . $no, ErrCode::$CHEQUE_ACCOUNT_RELEASE_STATUS_SAVE_ERROR);
        }

        self::$cheque_code = implode(',', array_column($bank_flow_oa_rel, 'detail_id'));

        return self::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => [
                'id' => $bank_flow_oa->oa_value
            ]
        ]);
    }

    /**
     * 通过单号数组获得Model=只获得审核通过
     * @param array $cheque_code_arr 传入的支票号_行id 数据集合
     * @param bool $has_pay
     * @param bool $is_row
     * @return mixed
     */
    public function getModelByNos(array $cheque_code_arr, bool $has_pay = false, bool $is_row = false)
    {
        if (!is_array($cheque_code_arr) || empty($cheque_code_arr)) {
            return ['msg' => 'cheque_code_empty_data'];
        }
        $cheque_arr             = $cheque_ids = $detail_ids = $oa_cheque_code = [];
        $detail_cheque_code_arr = [];
        foreach ($cheque_code_arr as $value) {
            $cheque                          = explode('_', $value);
            $cheque_arr[]                    = $cheque[0];
            $cheque_all                      = explode(',', $cheque[1]);
            $cheque_ids[$cheque[0]]          = $cheque_all;
            $detail                          = explode(',', $cheque[1])[0];
            $detail_ids[]                    = $detail;
            $oa_cheque_code[$detail]         = $value;
            $detail_cheque_code_arr[$detail] = $cheque[0];
        }
        $cheque_account_arr = StoreRentingListService::getInstance()->getPaymentStoreRentingApplyNo(array_values(array_unique($cheque_arr)));

        foreach ($cheque_account_arr as $account_arr) {

            if ($account_arr['oa_type'] != BankFlowEnums::BANK_FLOW_OA_TYPE_PAYMENT_STORE_RENTING) {
                return ['msg' => 'cheque_code_by_apply_no_type_err'];
            }
            if ($account_arr['use_status'] != ChequeEnums::CHECKS_NUMBER_USE_STATUS_TWO || $account_arr['exchange_status'] != ChequeEnums::CHECKS_EXCHANGE_STATUS_ING) {
                return ['msg' => 'cheque_code_status_err'];
            }
        }
        $oa_biz_no_arr         = array_column($cheque_account_arr, 'oa_biz_no', 'cheque_code');
        $cheque_code_by_oa_arr = array_column($cheque_account_arr, 'cheque_code', 'oa_biz_no');
        foreach ($oa_biz_no_arr as $key => $oa_biz_no) {
            $oa[$oa_biz_no][] = $cheque_ids[$key];
        }
        //校验当前填写的支票号对应的业务详情id必须是当前业务下的行id
        $country               = get_country_code();
        $payment_store_renting = PaymentStoreRentingModel::find(
            [
                'conditions' => 'apply_no in ({apply_no:array})',
                'bind'       => [
                    'apply_no' => array_values(array_unique($oa_biz_no_arr))
                ]
            ]);

        if (empty($payment_store_renting->toArray())) {
            return ['msg' => 'empty_data'];
        }
        foreach ($payment_store_renting as $store_renting) {
            if ($country == GlobalEnums::PH_COUNTRY_CODE && $store_renting->pay_status != Enums::LOAN_PAY_STATUS_PAY) {
                return ['msg' => 'payment_pay_status_err'];
            } elseif ($country == GlobalEnums::MY_COUNTRY_CODE && $store_renting->pay_status != Enums::LOAN_PAY_STATUS_PENDING) {
                return ['msg' => 'payment_pay_status_err'];
            }

            $current_detail_all_arr = $store_renting->getDetails()->toArray();
            $detail_all_arr         = array_column($current_detail_all_arr, 'id');
            $xls_cheque_ids         = $cheque_ids[$cheque_code_by_oa_arr[$store_renting->apply_no]];
            if (!empty(array_diff($xls_cheque_ids, $detail_all_arr))) {
                return ['msg' => 'payment_pay_detail_id_err'];
            }
        }
        self::$apply_no_all              = $oa_biz_no_arr;
        self::$detail_by_all_cheque_code = $oa_cheque_code;//行id对应的支票_行id
        self::$detail_cheque_code        = $detail_cheque_code_arr;//行id对应的支票
        self::$apply_no_by_code          = $oa;//业务单apply_no对应行id

        $data = [];
        if (!empty($detail_ids)) {
            $data = self::find(
                [
                    'conditions' => 'id in ({id:array})',
                    'bind'       => [
                        'id' => $detail_ids
                    ]
                ]
            );
        }
        return $data;
    }

    /**
     * 通过model获得银行流水格式化数据
     * @return mixed
     */
    public function getFormatData()
    {
        //通过业务单号获取到对应的支票号_行号
        $on = self::$detail_by_all_cheque_code[$this->id] ?? '';
        //得到对应的行号 eg 支票_行ID,行ID
        $detail_ids = explode(',', explode('_', $on)[1]);
        //查询当前租房付款的全部详情
        $main        = self::getMain();
        $details_arr = PaymentStoreRentingDetail::find([
            'conditions' => 'id in ({ids:array})',
            'bind'       => [
                'ids' => $detail_ids,
            ]
        ])->toArray();

        $amount = 0;
        foreach ($details_arr as $details) {
            //过滤本次符合行id的数据  累加金额
            $amount = bcadd($amount, $details['actually_amount'], 2); //actually_amount 实付金额
        }
        return [
            'oa_value'   => $this->id,
            'oa_type'    => BankFlowEnums::BANK_FLOW_OA_TYPE_CHEQUE_ACCOUNT,
            'no'         => $on,
            'amount'     => $amount,
            'currency'   => $main->currency,
            'status'     => $main->approval_status,
            'pay_status' => $main->pay_status,
            'apply_no'   => $main->apply_no,
        ];
    }


    /**
     * model 批量关联银行流水
     *
     * @param $ids
     * @param array $data
     * @return mixed
     * @throws BusinessException
     */
    public function batch_link($ids, $data)
    {
        $apply_no_by_code_arr = self::$apply_no_by_code;//当前一个租房付款的上传的详情id

        $detail_cheque_code_arr = self::$detail_cheque_code;
        $cheque_code_arr        = [];
        foreach ($ids as $id) {
            $cheque_code_arr[] = $detail_cheque_code_arr[$id];
        }
        $cheque_code_arr = array_values(array_unique($cheque_code_arr));
        $current_time    = date('Y-m-d H:i:s', time());
        $cheque_account  = ChequeAccountModel::find(
            [
                'conditions' => 'cheque_code in ({cheque_code:array}) and exchange_status = :exchange_status:',
                'bind'       => [
                    'cheque_code'     => $cheque_code_arr,//支票号
                    'exchange_status' => ChequeEnums::CHECKS_EXCHANGE_STATUS_ING
                ]
            ]
        );
        if (empty($cheque_account->toArray())) {
            throw new BusinessException('支票数据不存在' . json_encode($cheque_code_arr, JSON_UNESCAPED_UNICODE), ErrCode::$CHEQUE_ACCOUNT_RELEASE_STATUS_SAVE_ERROR);
        }

        foreach ($cheque_account as $cheque_account_detail) {
            $cheque_account_detail->exchange_status = ChequeEnums::CHECKS_EXCHANGE_STATUS_END;//已承兑
            $cheque_account_detail->exchange_date   = $data['date'];//承兑日期
            $cheque_account_detail->updated_at      = $current_time;
            if ($cheque_account_detail->save() === false) {
                throw new BusinessException('支票数据修改失败' . get_data_object_error_msg($cheque_account_detail) . '; 待更新数据: ' . json_encode($cheque_account_detail->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$CHEQUE_ACCOUNT_RELEASE_STATUS_SAVE_ERROR);
            }
        }

        //得到全部业务单号
        $apply_no_all = array_values(array_unique(self::$apply_no_all));
        //查询主业务单
        $store_renting_obj = PaymentStoreRentingModel::find([
            'conditions' => 'apply_no in ({apply_no:array})',
            'bind'       => [
                'apply_no' => $apply_no_all,
            ]
        ]);
        $country           = get_country_code();
        foreach ($store_renting_obj as $store_renting) {
            $detail_all = call_user_func_array('array_merge', $apply_no_by_code_arr[$store_renting->apply_no]);
            $detail_ids = array_values(array_unique($detail_all));//传入的行id

            //当前主单对应的全部详情
            $current_detail_all = $store_renting->getDetails();
            if ($country == GlobalEnums::PH_COUNTRY_CODE) {
                //如果是菲律宾 状态直接修改为已支付
                $pay_status = Enums::LOAN_PAY_STATUS_PAY;
            } else {
                //剩余的详情数据
                $current_detail_all_arr = $current_detail_all->toArray();
                $arr_diff               = array_diff(array_column($current_detail_all_arr, 'id'), $detail_ids);
                //差集里面是否有未付款的单子
                $res_pay_status      = false;
                $current_diff_detail = array_column($current_detail_all_arr, null, 'id');
                foreach ($arr_diff as $diff_detail) {
                    //剩余里面存在待支付
                    if ($current_diff_detail[$diff_detail]['pay_status'] == Enums::PAYMENT_PAY_STATUS_PENDING) {
                        $res_pay_status = true;
                    }
                }
                //如果存在未支付 状态为部分付款 ，如果不存在就是全部付款 剩余的状态如果有未付款就是部分
                if (count($arr_diff) > 0 && $res_pay_status) {
                    $pay_status = Enums::PAYMENT_PAY_STATUS_PART_PAY;//部分付款
                } else {
                    $pay_status = Enums::LOAN_PAY_STATUS_PAY;//已付款
                }
            }

            //开始修改主订单状态
            $store_renting->pay_status = $pay_status;
            $store_renting->updated_at = $current_time;
            if ($store_renting->save() === false) {
                throw new BusinessException('支票数据修改租房付款失败' . get_data_object_error_msg($store_renting) . '; 待更新数据: ' . json_encode($store_renting->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$PAYMENT_STORE_RENTING_DETAIL_SAVE_ERROR);
            }
            //在去修改本次上传对应的行id数据 变更状态为已支付
            foreach ($current_detail_all as $current_detail) {
                if (in_array($current_detail->id, $detail_ids)) {
                    $current_detail->pay_status = Enums::LOAN_PAY_STATUS_PAY; //已支付
                    $current_detail->updated_at = $current_time;
                    if ($current_detail->save() === false) {
                        throw new BusinessException('修改租房付款详情数据失败' . get_data_object_error_msg($current_detail) . '; 待更新数据: ' . json_encode($current_detail->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$PAYMENT_STORE_RENTING_DETAIL_SAVE_ERROR);
                    }
                }
            }
        }
        //开始添加bank_flow_oa数据
        //区分写入 原因是在行上关联，会有一个支票对应不同的行id，差异说明不一样，获取的数据也不一样
        if (!empty($data['is_link'])) {
            //数据格式不同行关联
            //提交的数据有多少行 就写几次数据（$data['link_data']['data'] ）
            $rel = new BankFlowOaRelModel();
            foreach ($data['link_data']['data'] as $link) {
                if ($link['oa_type'] == BankFlowEnums::BANK_FLOW_OA_TYPE_CHEQUE_ACCOUNT) {
                    //找到提交对应的数据
                    $order_info                 = array_column($data['order_info'], null, 'oa_value');
                    $order_info                 = $order_info[$link['oa_value']];
                    $no                         = explode('_', $link['no']);
                    $flow_oa                    = [];
                    $flow_oa['bank_flow_id']    = $order_info['bank_flow_id'];
                    $flow_oa['oa_type']         = $order_info['oa_type'];
                    $flow_oa['oa_value']        = $link['oa_value'];
                    $flow_oa['no']              = $no[0] ?? '';
                    $flow_oa['amount']          = $order_info['amount'];
                    $flow_oa['currency']        = $order_info['currency'];
                    $flow_oa['real_amount']     = $order_info['real_amount'];
                    $flow_oa['diff_amount']     = $order_info['diff_amount'] ?? 0;
                    $flow_oa['diff_info']       = $link['diff_info'];
                    $flow_oa['created_id']      = $data['created_id'] ?? '';
                    $flow_oa['updated_id']      = $data['create_id'];
                    $flow_oa['bank_account_id'] = $order_info['bank_account_id'];
                    $flow_oa['date']            = $data['data']['date'];
                    $flow_oa['created_at']      = $current_time;
                    $flow_oa['updated_at']      = $current_time;
                    $bank_flow_oa_model         = new BankFlowOaModel();
                    $bool                       = $bank_flow_oa_model->i_create($flow_oa);
                    if ($bool === false) {
                        throw new BusinessException('网点租房付款-支付失败'. get_data_object_error_msg($bank_flow_oa_model) . '; 待更新数据: ' . json_encode($flow_oa, JSON_UNESCAPED_UNICODE), ErrCode::$PAYMENT_STORE_RENTING_DETAIL_ADD_ERROR);
                    }

                    $detail_arr       = explode(',', $no[1]);
                    $bank_flow_oa_rel = [];
                    foreach ($detail_arr as $detail_id) {
                        $bank_flow_oa_rel[] = [
                            'bank_flow_oa_id' => $bank_flow_oa_model->id,
                            'detail_id'       => $detail_id,
                            'created_at'      => $current_time,
                        ];
                    }
                    $bank_flow_oa_rel_add_res = $rel->batch_insert($bank_flow_oa_rel);
                    if ($bank_flow_oa_rel_add_res === false) {
                        throw new BusinessException('银行流水关联系统业务明细表写入失败'. '; 待添加的数据: ' . json_encode($bank_flow_oa_rel, JSON_UNESCAPED_UNICODE), ErrCode::$PAYMENT_STORE_RENTING_DETAIL_ADD_ERROR);
                    }
                    $item                           = [];
                    $item['is_pay']                 = Enums::LOAN_PAY_STATUS_PAY;    //是否已付款
                    $item['pay_bank_name']          = $data['data']['bank_name'];
                    $item['pay_bank_account']       = $data['data']['bank_account'];
                    $item['pay_date']               = $data['data']['date'];
                    $item['create_id']              = $data['create_id'];
                    $item['payment_batch_id']       = $bank_flow_oa_model->id; //付款批次标识
                    $item['create_name']            = $data['create_name'];
                    $item['create_department_name'] = $data['create_department_name'];
                    $item['create_job_title_name']  = $data['create_job_title_name'];
                    $item['created_at']             = $current_time;
                    $item['remark']                 = $data['ticket_no'] ?? '';
                    $item['pay_from']               = 2;
                    $item['store_renting_id']       = $link['oa_value'];
                    $pay                            = new PaymentStoreRentingPay();
                    $bool                           = $pay->i_create($item);
                    if ($bool === false) {
                        throw new BusinessException('付款管理 - 网点租房付款支付信息添加失败'. get_data_object_error_msg($pay) . '; 添加数据: ' . json_encode($item, JSON_UNESCAPED_UNICODE), ErrCode::$PAYMENT_STORE_RENTING_DETAIL_ADD_ERROR);
                    }
                }
            }

        } else {
            //数据格式不同  头关联
            foreach ($data['bank_flow_oa'] as $bank_flow) {
                if ($bank_flow['oa_type'] == BankFlowEnums::BANK_FLOW_OA_TYPE_CHEQUE_ACCOUNT) {
                    $no                              = explode('_', $bank_flow['no']);
                    $bank_flow_oa                    = [];
                    $bank_flow_oa['bank_flow_id']    = $bank_flow['bank_flow_id'];
                    $bank_flow_oa['oa_type']         = $bank_flow['oa_type'];
                    $bank_flow_oa['oa_value']        = $bank_flow['oa_value'];
                    $bank_flow_oa['no']              = $no[0] ?? '';
                    $bank_flow_oa['amount']          = $bank_flow['amount'];
                    $bank_flow_oa['currency']        = $bank_flow['currency'];
                    $bank_flow_oa['real_amount']     = $bank_flow['real_amount'];
                    $bank_flow_oa['diff_amount']     = $bank_flow['diff_amount'];
                    $bank_flow_oa['diff_info']       = $bank_flow['diff_info'] ?? '';
                    $bank_flow_oa['created_id']      = $bank_flow['created_id'];
                    $bank_flow_oa['bank_account_id'] = $bank_flow['bank_account_id'];
                    $bank_flow_oa['date']            = $bank_flow['date'];
                    $bank_flow_oa['updated_id']      = $data['bank_flow_oa'][0]['updated_id'];
                    $bank_flow_oa['created_at']      = $current_time;
                    $bank_flow_oa['updated_at']      = $current_time;
                    $bank_flow_oa_model              = new BankFlowOaModel();
                    $bool                            = $bank_flow_oa_model->i_create($bank_flow_oa);
                    if ($bool === false) {
                        throw new BusinessException('网点租房付款-添加bank_flow_oa 失败,数据为:' . get_data_object_error_msg($bank_flow_oa_model) . '; 添加数据: ' . json_encode($bank_flow_oa, JSON_UNESCAPED_UNICODE), ErrCode::$PAYMENT_STORE_RENTING_DETAIL_ADD_ERROR);
                    }

                    if (!empty($no[1])) {
                        $details          = explode(',', $no[1]);
                        $bank_flow_oa_rel = [];
                        foreach ($details as $detail_id) {
                            $bank_flow_oa_rel[] = [
                                'bank_flow_oa_id' => $bank_flow_oa_model->id,
                                'detail_id'       => $detail_id,
                                'created_at'      => $current_time,
                            ];
                        }
                        $rel                      = new BankFlowOaRelModel();
                        $bank_flow_oa_rel_add_res = $rel->batch_insert($bank_flow_oa_rel);
                        if ($bank_flow_oa_rel_add_res === false) {
                            throw new BusinessException('银行流水关联系统业务明细表写入失败 添加数据: ' . json_encode($bank_flow_oa_rel, JSON_UNESCAPED_UNICODE), ErrCode::$PAYMENT_STORE_RENTING_DETAIL_ADD_ERROR);
                        }
                    }
                    //开始添加payment_store_renting_pay 数据
                    $item                           = [];
                    $item['is_pay']                 = Enums::LOAN_PAY_STATUS_PAY;    //是否已付款
                    $item['pay_bank_name']          = $data['data']['bank_name'];
                    $item['pay_bank_account']       = $data['data']['bank_account'];
                    $item['pay_date']               = $data['data']['date'];
                    $item['create_id']              = $data['create_id'];
                    $item['payment_batch_id']       = $bank_flow_oa_model->id; //付款批次标识
                    $item['create_name']            = $data['create_name'];
                    $item['create_department_name'] = $data['create_department_name'];
                    $item['create_job_title_name']  = $data['create_job_title_name'];
                    $item['created_at']             = $current_time;
                    $item['remark']                 = $data['ticket_no'] ?? '';
                    $item['pay_from']               = 2;
                    $item['store_renting_id']       = $bank_flow['oa_value'];
                    $pay                            = new PaymentStoreRentingPay();
                    $bool_pay                       = $pay->i_create($item);
                    if ($bool_pay === false) {
                        throw new BusinessException('付款管理 - 网点租房付款支付信息添加失败' . get_data_object_error_msg($pay) . '; 添加数据: ' . json_encode($item, JSON_UNESCAPED_UNICODE), ErrCode::$PAYMENT_STORE_RENTING_DETAIL_ADD_ERROR);
                    }
                }
            }
        }
        return true;
    }


    /**
     * 关联银行流水撤销
     * @param array $user
     * @return mixed
     */
    public function cancel(array $user)
    {
        //支票号后对应的行id
        $detail_ids           = explode(',', self::$cheque_code);
        $store_renting_detail = PaymentStoreRentingDetailModel::find([
            'conditions' => 'id in ({ids:array})',
            'bind'       => ['ids' => $detail_ids]
        ]);
        $renting_detail_arr   = $store_renting_detail->toArray();
        if (empty($store_renting_detail)) {
            throw new BusinessException('租房付款详情数据不能为空' . $this->id, ErrCode::$PAYMENT_STORE_RENTING_DETAIL_DATA_ERROR);
        }
        $renting = PaymentStoreRentingModel::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => ['id' => $renting_detail_arr[0]['store_renting_id']]
        ]);

        if (empty($renting)) {
            throw new BusinessException('租房付款主单数据不能为空' . $this->id, ErrCode::$PAYMENT_STORE_RENTING_DETAIL_DATA_ERROR);
        }

        if (get_country_code() == GlobalEnums::MY_COUNTRY_CODE) {
            //计算是部分支付还是整体支付
            $current_detail_all_arr = $renting->getDetails()->toArray();
            $arr_diff               = array_diff(array_column($current_detail_all_arr, 'id'), $detail_ids);
            //差集里面是否有未付款的单子
            $res_pay_status      = false;
            $current_diff_detail = array_column($current_detail_all_arr, null, 'id');
            foreach ($arr_diff as $diff_detail) {
                //剩余里面存在已支付的
                if ($current_diff_detail[$diff_detail]['pay_status'] == Enums::PAYMENT_PAY_STATUS_PAY) {
                    $res_pay_status = true;
                }
            }
            //剩余的数据里面全部是已经支付的 撤回变更我部分支付
            if (!empty($arr_diff) && $res_pay_status) {
                //部分支付
                $pay_status = Enums::PAYMENT_PAY_STATUS_PART_PAY;
            } else {
                //待支付
                $pay_status = Enums::LOAN_PAY_STATUS_PENDING;
            }

            $renting->pay_status = $pay_status;
            $renting->updated_at = date('Y-m-d H:i:s', time());
            if ($renting->save() === false) {
                throw new BusinessException('支票详情关联银行流水租房付款撤回修改主表状态失败' . get_data_object_error_msg($renting) . '; 待修改的数据: ' . json_encode($renting->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$PAYMENT_STORE_RENTING_DETAIL_CANCEL_ERROR);
            }
            //修改行id对应的详情
            foreach ($store_renting_detail as $detail) {
                $detail->pay_status = Enums::LOAN_PAY_STATUS_PENDING;
                $detail->updated_at = date('Y-m-d H:i:s', time());
                if ($detail->save() === false) {
                    throw new BusinessException('支票详情关联银行流水租房付款撤回修改详情行失败' . get_data_object_error_msg($detail) . '; 待修改的数据: ' . json_encode($detail->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$PAYMENT_STORE_RENTING_DETAIL_CANCEL_ERROR);
                }
            }

        } else {
            //修改行id对应的详情 ph撤回的时候  只能撤回行状态  不撤回主状态
            foreach ($store_renting_detail as $detail) {
                $detail->pay_status = Enums::LOAN_PAY_STATUS_PENDING;
                $detail->updated_at = date('Y-m-d H:i:s', time());
                if ($detail->save() === false) {
                    throw new BusinessException('支票详情关联银行流水租房付款撤回修改详情行失败' . get_data_object_error_msg($detail) . '; 待修改的数据: ' . json_encode($detail->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$PAYMENT_STORE_RENTING_DETAIL_CANCEL_ERROR);
                }
            }
        }

        $bank_flow_oa_rel = BankFlowOaRelModel::find([
            'conditions' => 'detail_id in ({detail_id:array})',
            'bind'       => ['detail_id' => $detail_ids]
        ]);

        $bank_flow_oa_rel_arr = $bank_flow_oa_rel->toArray();
        if (empty($bank_flow_oa_rel_arr)) {
            throw new BusinessException('支票详情关联银行流水数据错误' . json_encode($bank_flow_oa_rel_arr, JSON_UNESCAPED_UNICODE), ErrCode::$PAYMENT_STORE_RENTING_DETAIL_DATA_ERROR);
        }

        $bank_flow_oa = BankFlowOaModel::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => ['id' => $bank_flow_oa_rel_arr[0]['bank_flow_oa_id']]
        ]);

        if (empty($bank_flow_oa)) {
            throw new BusinessException('银行付款流水关联系统单号数据错误' . $bank_flow_oa_rel_arr[0]['bank_flow_oa_id'], ErrCode::$PAYMENT_STORE_RENTING_DETAIL_DATA_ERROR);
        }

        //删除对应的 bank_flow_oa_rel 表数据
        if ($bank_flow_oa_rel->delete() === false) {
            throw new BusinessException('支票详情关联银行流水租房付款撤回修改银行流水关联系统业务明细表失败' . get_data_object_error_msg($bank_flow_oa_rel) . '; 待删除的数据: ' . json_encode($bank_flow_oa_rel_arr, JSON_UNESCAPED_UNICODE), ErrCode::$PAYMENT_STORE_RENTING_DETAIL_CANCEL_ERROR);
        }

        //还原支票状态
        $cheque_code    = $bank_flow_oa->no;
        $cheque_account = ChequeAccountModel::findFirst([
            'conditions' => 'cheque_code = :cheque_code:',
            'bind'       => ['cheque_code' => $cheque_code]
        ]);
        if (!empty($cheque_account)) {
            $cheque_account->exchange_status = ChequeEnums::CHECKS_EXCHANGE_STATUS_ING;//待承兑
            $cheque_account->exchange_date   = null;//承兑日期
            $cheque_account->updated_at      = date('Y-m-d H:i:s', time());
            if ($cheque_account->save() === false) {
                throw new BusinessException('支票详情关联银行流水租房付款撤回修改支票失败'. get_data_object_error_msg($cheque_account) . '; 待修改的数据: ' . json_encode($cheque_account->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$PAYMENT_STORE_RENTING_DETAIL_CANCEL_ERROR);
            }
        }
        return true;
    }


    public function batch_confirm($ids, $data)
    {

    }

    public function link(array $data)
    {

    }

    /**
     * 支付模块修改收款方银行信息 -> 同步更新业务侧收款方银行信息
     *
     * @param array $data
     * @param array $user
     * @return bool
     */
    public function syncUpdatePayeeInfo(array $data, array $user = [])
    {
        return true;
    }
}
