<?php

namespace App\Models\oa;

use App\Models\Base;

class SettingEnvModel extends Base
{
    /**
     * Initialize method for model.
     */
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        $this->setSource('setting_env');
    }

    public static function getValByCode($code, $default = '')
    {
        $setting_model = static::findFirst([
            'conditions' => 'code = :code:',
            'bind' => ['code' => $code]
        ]);

        return !empty($setting_model) ? $setting_model->val : $default;
    }
}
