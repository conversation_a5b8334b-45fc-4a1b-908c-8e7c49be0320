<?php
/**
 * Created by PhpStorm.
 * Date: 2023/4/13
 * Time: 15:25
 */

namespace App\Models\oa;

use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Models\Base;
use App\Modules\User\Models\AttachModel;

class ContractPlatFormModel extends Base
{
    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        // setSource()方法指定数据库表
        $this->setSource('contract_platform');

        $this->hasMany(
            'id',
            AttachModel::class,
            'oss_bucket_key', [
                'params' => [
                    'conditions' => 'oss_bucket_type = ' . Enums::OSS_BUCKET_TYPE_GPMD_CONTRACT_FILE . ' and deleted = ' . GlobalEnums::IS_NO_DELETED
                ],
                "alias" => "File",
            ]
        );

        $this->hasMany(
            'id',
            AttachModel::class,
            'oss_bucket_key', [
                'params' => [
                    'conditions' => 'oss_bucket_type = ' . Enums::OSS_BUCKET_TYPE_GPMD_CONTRACT_ATTACHMENT . ' and deleted = ' . GlobalEnums::IS_NO_DELETED
                ],
                "alias" => "Attach",
            ]
        );
    }

}
