<?php

namespace App\Models\oa;

use App\Library\Enums\GlobalEnums;
use App\Models\Base;
use App\Modules\Material\Models\MaterialAttachmentModel;
use App\Library\Enums;

/**
 * 耗材申请
 * @Date: 2/15/23 10:57 AM
 * @author: peak pan
 **/
class MaterialWmsApplyModel extends Base
{
    /**
     * Initialize method for model.
     */
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        $this->setSource('material_wms_apply');

        $this->hasMany(
            'id',
            MaterialWmsApplyProductModel::class,
            'apply_id', [
                'params' => [
                    'conditions' => 'is_deleted=' . GlobalEnums::IS_NO_DELETED
                ],
                'alias'  => 'Products',
            ]
        );

        //获取耗材申请-附件信息
        $this->hasMany(
            'id',
            MaterialAttachmentModel::class,
            'oss_bucket_key', [
                'params' => [
                    'conditions' => 'oss_bucket_type = ' . Enums::OSS_MATERIAL_TYPE_WMS_APPLY . ' and deleted=' . GlobalEnums::IS_NO_DELETED
                ],
                'alias'  => 'Attachment',
            ]
        );
    }


}
