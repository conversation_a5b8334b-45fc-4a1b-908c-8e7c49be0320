<?php
namespace App\Models\oa;

use App\Library\Enums;
use App\Models\Base;

/**
 * 支付模块-FlashPay在线支付-配置表
 * Class PaymentFlashPayConfigModel
 * @package App\Models\oa
 */
class PaymentFlashPayConfigModel extends Base
{
    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_oa');

        // setSource()方法指定数据库表
        $this->setSource('payment_flash_pay_config');
    }
}
