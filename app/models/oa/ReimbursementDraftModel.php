<?php

namespace App\Models\oa;

use App\Models\Base;

/**
 * @package App\Models\oa
 */
class ReimbursementDraftModel extends Base
{
    public $id;
    public $apply_no;
    public $content;
    public $data_source;
    public $created_id;
    public $created_at;
    public $updated_id;
    public $updated_at;

    public function initialize()
    {
        $this->setConnectionService('db_oa');
        $this->setSource('reimbursement_draft');
    }

}
