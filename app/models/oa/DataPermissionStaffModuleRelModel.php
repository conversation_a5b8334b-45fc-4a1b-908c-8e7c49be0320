<?php

namespace App\Models\oa;

use App\Models\Base;

/**
 *
 * Class DataPermissionStaffModuleRelModel
 *
 * @package App\Models\oa
 */
class DataPermissionStaffModuleRelModel extends Base
{
    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        // setSource()方法指定数据库表
        $this->setSource('data_permission_staff_module_rel');

        $this->hasMany(
            'id',
            DataPermissionStaffManageDepartmentListModel::class,
            'staff_module_rel_id', [
                'alias' => 'StaffManageDepartmentList',
            ]
        );

    }
}
