<?php
/**
 * 子公司-会计规则主表
 */

namespace App\Models\oa;

use App\Models\Base;

/**
 *
 * Class AccountingRuleModel
 *
 * @package App\Models\oa
 */
class AccountingRuleModel extends Base
{
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        $this->setSource('accounting_rule');

        $this->hasMany(
            'id',
            AccountingRuleDepartmentListModel::class,
            'accounting_rule_id', [
                'alias' => 'RuleDepartmentList',
            ]
        );
    }

}
