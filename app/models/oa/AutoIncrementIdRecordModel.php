<?php

namespace App\Models\oa;

use App\Models\Base;

/**
 *
 * Class AutoIncrementIdRecordModel
 * @package App\Models\oa
 */
class AutoIncrementIdRecordModel extends Base
{
    const OPERATE_TYPE_1 = 1;
    const OPERATE_TYPE_2 = 2;

    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        // setSource()方法指定数据库表
        $this->setSource('auto_increment_id_record');
    }


}