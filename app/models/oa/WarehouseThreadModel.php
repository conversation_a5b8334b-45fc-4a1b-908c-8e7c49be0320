<?php
/**
 * 仓库管理-仓库线索
 */

namespace App\Models\oa;

use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\WarehouseEnums;
use App\Models\Base;

/**
 *
 * Class WarehouseThreadModel
 *
 * @package App\Models\oa
 */
class WarehouseThreadModel extends Base
{
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        $this->setSource('warehouse_thread');

        //关联需求信息
        $this->hasOne(
            'requirement_id',
            WarehouseRequirementModel::class,
            'id',
            [
                'alias' => 'Requirement'
            ]
        );

        //关联仓库信息
        $this->hasOne(
            'warehouse_main_id',
            ContractWarehouseModel::class,
            'id',
            [
                'alias' => 'Warehouse'
            ]
        );

        //关联报价信息
        $this->hasOne(
            'id',
            WarehouseThreadPriceRecordModel::class,
            'thread_id',
            [
                'alias' => 'Price'
            ]
        );

        //仓库管理-仓库线索管理-平面图-附件
        $this->hasMany(
            'id',
            SysAttachmentModel::class,
            'oss_bucket_key', [
                'params' => [
                    'conditions' => 'oss_bucket_type = ' . Enums::OSS_BUCKET_TYPE_WAREHOUSE_THREAD_PLAN_FILE . ' and deleted = ' . GlobalEnums::IS_NO_DELETED
                ],
                'alias' => 'PlanAttachment',
            ]
        );

        //仓库管理-仓库线索管理-附件
        $this->hasMany(
            'id',
            SysAttachmentModel::class,
            'oss_bucket_key', [
                'params' => [
                    'conditions' => 'oss_bucket_type = ' . Enums::OSS_BUCKET_TYPE_WAREHOUSE_THREAD_INFO_FILE . ' and deleted = ' . GlobalEnums::IS_NO_DELETED
                ],
                'alias' => 'Attachment',
            ]
        );

        //仓库线索-验证记录-待验证
        $this->hasOne(
            'id',
            WarehouseThreadVerifyRecordModel::class,
            'thread_id',
            [
                'params' => [
                    'conditions' => 'status = ' . WarehouseEnums::THREAD_VERIFY_STATUS_VERIFY
                ],
                'alias' => 'Verify'
            ]
        );
    }
}
