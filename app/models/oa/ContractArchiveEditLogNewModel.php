<?php
/**
 * Created by PhpStorm.
 * Date: 2025
 * Time: 15:25
 */

namespace App\Models\oa;

use App\Models\Base;

class ContractArchiveEditLogNewModel extends Base
{
    public $id;
    public $contract_no;
    public $before_data;
    public $after_data;
    public $created_id;
    public $created_name;
    public $created_at;

    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        // setSource()方法指定数据库表
        $this->setSource('contract_archive_edit_log_new');
    }

}
