<?php
namespace App\Models\oa;

use App\Library\Enums;
use App\Models\Base;

/**
 * 借款归还记录表
 * Class LoanReturnModel
 * @package App\Models\oa
 */
class LoanReturnModel extends Base
{
    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_oa');

        // setSource()方法指定数据库表
        $this->setSource('loan_return');

        $this->hasMany(
            'id',
            SysAttachmentModel::class,
            'oss_bucket_key',
            [
                'params' => [
                    'conditions' => 'oss_bucket_type = ' . Enums::OSS_BUCKET_TYPE_LOAN_BACK . ' and deleted = ' . Enums\GlobalEnums::IS_NO_DELETED
                ],
                'alias' => 'Backs',
            ]
        );
    }
}
