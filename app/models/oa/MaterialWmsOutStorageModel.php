<?php

namespace App\Models\oa;

use App\Library\Enums;
use App\Models\Base;
use App\Library\Enums\GlobalEnums;
use App\Modules\Material\Models\MaterialAttachmentModel;

/**
 * 耗材出库单
 **/
class MaterialWmsOutStorageModel extends Base
{

    /**
     * Initialize method for model.
     */
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        $this->setSource('material_wms_out_storage');


        $this->hasMany(
            'id',
            MaterialWmsOutStorageProductModel::class,
            'wms_id', [
                'params' => [
                    'conditions' => ' this_time_num > 0 and is_deleted=' . GlobalEnums::IS_NO_DELETED
                ],
                'alias'  => 'Products',
            ]
        );

        $this->hasOne(
            'apply_id',
            MaterialWmsApplyModel::class,
            'id', [
                'params' => [
                    'conditions' => 'is_deleted = '. GlobalEnums::IS_NO_DELETED
                ],
                'alias' => 'MaterialWmsApply',
            ]
        );

        //获取耗材申请-附件信息
        $this->hasMany(
            'apply_id',
            MaterialAttachmentModel::class,
            'oss_bucket_key', [
                'params' => [
                    'conditions' => 'oss_bucket_type = ' . Enums::OSS_MATERIAL_TYPE_WMS_APPLY . ' and deleted=' . GlobalEnums::IS_NO_DELETED
                ],
                'alias'  => 'Attachment',
            ]
        );
    }

}
