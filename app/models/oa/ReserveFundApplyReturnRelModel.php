<?php
namespace App\Models\oa;

use App\Library\Enums\GlobalEnums;
use Phalcon\Mvc\Model\Behavior\SoftDelete;

use App\Models\Base;
/**
 * 备用金申请单与归还单关联关系表（一笔归还单对应多笔申请单）
 * Class ReserveFundApplyReturnRelModel
 * @package App\Models\oa
 */
class ReserveFundApplyReturnRelModel extends Base
{
    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_oa');

        // setSource()方法指定数据库表
        $this->setSource('reserve_fund_apply_return_rel');

        $this->addBehavior(
            new SoftDelete(
                [
                    'field' => 'is_deleted',
                    'value' => GlobalEnums::IS_DELETED,
                ]
            )
        );
    }
}
