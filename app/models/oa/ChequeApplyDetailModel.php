<?php

namespace App\Models\oa;

use App\Library\CInterface\BankFlowModelInterface;
use App\Library\Enums;
use App\Library\Enums\BankFlowEnums;
use App\Library\ErrCode;
use App\Models\Base;
use App\Library\Exception\BusinessException;

/**
 * 支票申请明细
 **/
class ChequeApplyDetailModel extends Base implements BankFlowModelInterface
{

    public function initialize()
    {
        $this->setConnectionService('db_oa');
        $this->setSource('cheque_apply_detail');

        $this->hasOne(
            'cheque_id',
            ChequeApplyModel::class,
            'id',
            [
                'alias' => 'ChequeApply'
            ]
        );
    }

    /**
     * 通过单号获得Model
     *
     * @param string $no
     * @return object
     */
    public function getModelByNo(string $no)
    {
        //bank_flow_oa表里面的关联单号 eg: ZPGM202302030001_97
        $detail_id = explode('_', $no)[1];
        return self::findFirst([
            'conditions' => 'id = :id:',
            'bind' => ['id' => $detail_id]
        ]);
    }

    /**
     * 通过单号数组获得Model=只获得审核通过，待支付的
     *
     * @param array $no
     * @param bool $has_pay
     * @param bool $is_row
     * @return object
     */
    public function getModelByNos(array $no, bool $has_pay = false, bool $is_row = false)
    {

        if (!is_array($no) || empty($no)) {
            return ['msg' => 'cheque_code_empty_data'];

        }
        $apply_no = $detail_ids = $apply_no_arr = [];
        foreach ($no as $value) {
            //$no 为申请单号_行id 字符串
            if (!strstr($value, '_')) {
                return ['msg' => 'cheque_apply_detail_not_glide_line'];
            }
            $no = explode('_', $value);
            $apply_no[] = $no[0]; //支票申请业务端apply_no
            $detail_ids[] = $no[1]; //支票申请业务业务详情id
            $apply_no_arr[$no[1]] = $no[0];
        }

        if (count($apply_no) != count(array_unique($detail_ids))) {
            //传入的单号不合法
            return ['msg' => 'cheque_apply_detail_id_err'];
        }

        $detail_obj = ChequeApplyDetailModel::find([
            'conditions' => 'id in ({detail_ids:array}) and cheque_amount > 0 and  pay_status = :pay_status:',
            'bind' => ['detail_ids' => $detail_ids, 'pay_status' => Enums::LOAN_PAY_STATUS_PENDING]
        ]);

        if (count($detail_obj->toArray()) != count($detail_ids)) {
            //传入的支票数量cheque_amount必须大于0
            return ['msg' => 'pay_data_list_upload_cheque_not_zero'];
        }

        foreach ($detail_obj as $detail) {
            $main_obj = $detail->getChequeApply();
            if ($apply_no_arr[$detail->id] != $main_obj->apply_no) {
                return ['msg' => 'cheque_apply_detail_id_and_apply_no_err'];

            }
        }
        return $detail_obj;

    }


    /**
     * 通过model获得银行流水格式化数据
     *
     * @return mixed
     */
    public function getFormatData()
    {
        $cheque_apply = self::getChequeApply();
        return [
            'oa_value' => $this->id,
            'oa_type' => BankFlowEnums::BANK_FLOW_OA_TYPE_CHEQUE_APPLY,
            'no' => $cheque_apply->apply_no . '_' . $this->id,
            'amount' => $this->cheque_amount,
            'currency' => $this->currency,
            'status' => $cheque_apply->status,
            'pay_status' => $this->pay_status ?? Enums::LOAN_PAY_STATUS_PENDING //未支付支票申请为0 为统一变更为1  为未支付
        ];
    }

    /**
     * model 批量关联银行流水
     *
     * @param $ids
     * @param array $data
     * @return mixed
     * @throws BusinessException
     */
    public function batch_link($ids, $data)
    {

        //行上上传会有多条
        $current_time = date('Y-m-d H:i:s', time());
        // $ids 支票详情id
        $cheque_apply_detail = ChequeApplyDetailModel::find([
            'conditions' => 'id in ({ids:array})',
            'bind' => ['ids' => $ids]
        ]);

        if (empty($cheque_apply_detail->toArray())) {
            throw new BusinessException('支票详情支付-数据不能为空', ErrCode::$CHEQUE_APPLY_DETAIL_SAVE_ERROR);
        }

        if ($data['is_link']) {
            $bank_flow = $data['data']; //行关联
        } else {
            $bank_flow = $data['flow_info']; //头关联
        }
        foreach ($cheque_apply_detail as $detail) {
            $detail->pay_status = Enums::LOAN_PAY_STATUS_PAY;
            $detail->pay_operate_at = $current_time;//支付操作时间
            $detail->bank_flow_id = $bank_flow['id'];//银行流水id
            $detail->bank_flow_at = $bank_flow['date'] . ' ' . $bank_flow['time'];//流水时间
            $detail->updated_at = $current_time;
            if ($detail->save() === false) {
                throw new BusinessException('银行流水管理-流水管理-支付流水-上传文件-关联(支票详情)系统单号修改失败, 原因可能是: ' . get_data_object_error_msg($detail) . '; 待更新数据: ' . json_encode($detail->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$CHEQUE_APPLY_DETAIL_SAVE_ERROR);
            }
        }
        return true;
    }


    /**
     * 关联银行流水撤销
     *
     * @param array $user
     * @return mixed
     */
    public function cancel(array $user)
    {
        $detail_id = $this->id;
        if (!empty($detail_id)) {
            $cheque_apply_detail = ChequeApplyDetailModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $detail_id]
            ]);
            if (!empty($cheque_apply_detail)) {
                $cheque_apply_detail->pay_status = Enums::LOAN_PAY_STATUS_PENDING;//待支付
                $cheque_apply_detail->bank_flow_id = 0;//流水ID
                $cheque_apply_detail->bank_flow_at = null;//流水交易日期
                if ($cheque_apply_detail->save() === false) {
                    throw new BusinessException('支票详情关联银行流水撤回失败原因可能是:' . get_data_object_error_msg($cheque_apply_detail) . '; 待更新数据: ' . json_encode($cheque_apply_detail->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$CHEQUE_APPLY_DETAIL_SAVE_ERROR);
                }
            }
        }
        return true;
    }


    public function batch_confirm($ids, $data)
    {

    }

    public function link(array $data)
    {
    }

    /**
     * 支付模块修改收款方银行信息 -> 同步更新业务侧收款方银行信息
     *
     * @param array $data
     * @param array $user
     * @return bool
     */
    public function syncUpdatePayeeInfo(array $data, array $user = [])
    {
        return true;
    }

}
