<?php
namespace App\Models\oa;

use App\Models\Base;
/**
 * 网点包材库存数据统计-入库、出库及库存情况历史归档表
 * Class MaterialPackageStockArchiveModel
 */
class MaterialPackageStockArchiveModel extends Base
{
    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_arch_oa');

        // setSource()方法指定数据库表
        $this->setSource('material_package_stock_archive');

    }
}
