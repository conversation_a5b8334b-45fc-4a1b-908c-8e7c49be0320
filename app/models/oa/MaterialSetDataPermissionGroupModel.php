<?php
namespace App\Models\oa;

use App\Models\Base;

class MaterialSetDataPermissionGroupModel extends Base
{
    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_oa');

        // setSource()方法指定数据库表
        $this->setSource('material_set_data_permission_group');

        $this->hasMany(
            'id',
            MaterialSetDataPermissionGroupStaffModel::class,
            'group_id', [
                'alias' => 'Staffs',
            ]
        );

        $this->hasMany(
            'id',
            MaterialSetDataPermissionGroupDepartmentModel::class,
            'group_id', [
                'alias' => 'Departments',
            ]
        );
    }
}
