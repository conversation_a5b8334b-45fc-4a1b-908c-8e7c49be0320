<?php
namespace App\Models\oa;

use App\Models\Base;

class MaterialSetReturnRepairMapModel extends Base
{
    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_oa');

        // setSource()方法指定数据库表
        $this->setSource('material_set_return_repair_map');

        $this->hasMany(
            'id',
            MaterialSetReturnRepairMapBarcodeModel::class,
            'map_id', [
                'alias' => 'Barcode',
            ]
        );
        $this->hasMany(
            'id',
            MaterialSetReturnRepairMapCategoryModel::class,
            'map_id', [
                'alias' => 'Category',
            ]
        );

        $this->hasMany(
        'id',
            MaterialSetReturnRepairMapDepartmentModel::class,
        'map_id', [
            'alias' => 'Department',
        ]
    );
    }
}
