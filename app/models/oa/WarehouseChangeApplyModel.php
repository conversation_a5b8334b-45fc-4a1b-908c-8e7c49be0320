<?php
/**
 * 仓库管理-仓库变更申请主表
 */

namespace App\Models\oa;

use App\Library\Enums\ContractEnums;
use App\Models\Base;

/**
 *
 * Class WarehouseChangeApplyModel
 *
 * @package App\Models\oa
 */
class WarehouseChangeApplyModel extends Base
{
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        $this->setSource('warehouse_change_apply');

        $this->hasMany(
            'id',
            WarehouseContractSnapshotModel::class,
            'change_apply_id', [
                'alias' => 'ContractSnapshot',
                'params' => [
                    'columns' => ['contract_no', 'contract_type', 'contract_begin_date', 'contract_end_date', 'contract_status', 'archive_status']
                ]
            ]
        );

        $this->hasMany(
            'id',
            WarehouseStoreSnapshotModel::class,
            'change_apply_id', [
                'alias' => 'MainStoreSnapshot',
                'params' => [
                    'conditions' => 'store_flag = :store_flag: AND use_status = :use_status:',
                    'bind' => ['store_flag' => ContractEnums::WAREHOUSE_STORE_FLAG_MAIN, 'use_status' => ContractEnums::WAREHOUSE_STORE_USE_STATUS_ING],
                    'columns' => ['store_id', 'store_name', 'store_category', 'province_name', 'city_name', 'district_name', 'region_name', 'postal_code', 'detail_address', 'begin_date', 'end_date']
                ]
            ]
        );

        $this->hasMany(
            'id',
            WarehouseStoreSnapshotModel::class,
            'change_apply_id', [
                'alias' => 'ShareStoreSnapshot',
                'params' => [
                    'conditions' => 'store_flag = :store_flag: AND use_status = :use_status:',
                    'bind' => ['store_flag' => ContractEnums::WAREHOUSE_STORE_FLAG_SHARE, 'use_status' => ContractEnums::WAREHOUSE_STORE_USE_STATUS_ING],
                    'columns' => ['store_id', 'store_name', 'store_category', 'province_name', 'city_name', 'district_name', 'region_name', 'postal_code', 'detail_address', 'begin_date', 'end_date']
                ]
            ]
        );

        $this->hasMany(
            'id',
            WarehouseChangeRecordModel::class,
            'change_apply_id', [
                'alias' => 'CurrentChangeRecord',
                'params' => [
                    'columns' => ['created_at', 'created_id', 'created_name', 'before_data', 'after_data']
                ]
            ]
        );

        $this->hasOne(
            'id',
            WarehouseChangeRecordModel::class,
            'change_apply_id', [
                'alias' => 'ChangeRecordModel',
            ]
        );
    }


}
