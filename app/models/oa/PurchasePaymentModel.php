<?php

namespace App\Models\oa;

use App\Library\Enums\PayEnums;
use App\Models\Base;
use App\Library\Enums;
use App\Library\ErrCode;
use App\Library\Enums\ChequeEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\BankFlowEnums;
use App\Modules\Cheque\Services\ChequeService;
use App\Library\Exception\BusinessException;
use App\Library\CInterface\BankFlowModelInterface;

class PurchasePaymentModel extends Base implements BankFlowModelInterface
{
    private static $apply_no_by_code;
    private static $cheque_code;

    public function initialize()
    {
        $this->setConnectionService('db_oa');

        $this->setSource('purchase_payment');
    }

    /**
     * @param $condition
     * @return mixed
     */
    public static function getFirst($condition)
    {
        return parent::findFirst($condition);
    }

    /**
     * @param null $parameters
     * @return mixed
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    /**
     * 通过单号获得Model
     * @param string $no
     * @return mixed
     */
    public function getModelByNo(string $no)
    {
        $cheque_arr             = ChequeService::getInstance()->chequeCodeByRel([$no]);
        self::$apply_no_by_code = $no;
        return self::findFirst([
            'conditions' => 'ppno = :ppno:',
            'bind'       => ['ppno' => array_keys($cheque_arr)[0]]
        ]);
    }


    /**
     * 通过单号数组获得Model=只获得审核通过
     * @param array $cheque_code_arr
     * @param bool $has_pay
     * @param bool $is_row
     * @return object
     */
    public function getModelByNos(array $cheque_code_arr, bool $has_pay = false, bool $is_row = false)
    {
        if (!is_array($cheque_code_arr) || empty($cheque_code_arr)) {
            return ['msg' => 'cheque_code_empty_data'];
        }
        $apply_no_arr      = ChequeService::getInstance()->chequeCodeByRel($cheque_code_arr);
        self::$cheque_code = $apply_no_arr;
        $data              = $ppno_arr = [];
        if (!empty($apply_no_arr)) {

            $pay_status = Enums::LOAN_PAY_STATUS_PENDING;
            $country    = get_country_code();
            if ($country == GlobalEnums::PH_COUNTRY_CODE) {
                $pay_status = Enums::LOAN_PAY_STATUS_PAY;
            }
            $ppno_arr = array_keys($apply_no_arr);
            $data = self::find(
                [
                    'conditions' => 'ppno in ({ppno:array}) and status = :status: and pay_status = :pay_status:',
                    'bind'       => [
                        'ppno'       => $ppno_arr,
                        'status'     => Enums::CONTRACT_STATUS_APPROVAL,
                        'pay_status' => $pay_status
                    ]
                ]
            );
            if (count($ppno_arr) != count($data->toArray())) {
                return ['msg' => 'payment_pay_status_err'];
            }
        }
        return $data;
    }


    /**
     * 通过model获得银行流水格式化数据
     * @return mixed
     */
    public function getFormatData()
    {
        $cheque_code = self::$cheque_code;

        return [
            'oa_value'   => $this->id,
            'oa_type'    => BankFlowEnums::BANK_FLOW_OA_TYPE_CHEQUE_ACCOUNT_PURCHASE,
            'no'         => $cheque_code[$this->ppno]['cheque_code'] ?? '',
            'amount'     => bcdiv($this->cur_amount, 1000, 2),
            'currency'   => $this->currency,
            'status'     => $this->status,
            'pay_status' => $this->pay_status,
            'ppno'       => $this->ppno,
        ];
    }

    /**
     * model 批量关联银行流水
     *
     * @param $ids
     * @param array $data
     * @return mixed
     * @throws BusinessException
     */
    public function batch_link($ids, $data)
    {
        //对行关联做特殊处理 因为行上可以关联多行
        $no = [];
        if (!empty($data['is_link'])) {
            foreach ($data['order_info'] as $order_info) {
                if ($order_info['oa_type'] == BankFlowEnums::BANK_FLOW_OA_TYPE_CHEQUE_ACCOUNT_PURCHASE) {
                    $no[] = $order_info['no'];
                }
            }
        } else {
            $no = [$data['order_info']['no']];
        }

        if (empty($no)) {
            throw new BusinessException('批量关联银行流水_支票数据不存在' . json_encode($no, JSON_UNESCAPED_UNICODE), ErrCode::$PURCHASE_PAYMENT_DETAIL_DATA_ERROR);
        }

        $current_time       = date('Y-m-d H:i:s', time());
        $cheque_account_obj = ChequeAccountModel::find(
            [
                'conditions' => 'cheque_code in ({cheque_code:array}) and exchange_status = :exchange_status:',
                'bind'       => [
                    'cheque_code'     => $no,//当前上传数据的支票号
                    'exchange_status' => ChequeEnums::CHECKS_EXCHANGE_STATUS_ING
                ]
            ]
        );

        if (empty($cheque_account_obj->toArray())) {
            throw new BusinessException('批量关联银行流水_支票状态数据不对' . json_encode($no, JSON_UNESCAPED_UNICODE), ErrCode::$PURCHASE_PAYMENT_DETAIL_DATA_ERROR);
        }

        foreach ($cheque_account_obj as $cheque_account) {
            $cheque_account->exchange_status = ChequeEnums::CHECKS_EXCHANGE_STATUS_END;//已承兑
            $cheque_account->exchange_date   = $data['date'];//承兑日期
            $cheque_account->updated_at      = $current_time;
            if ($cheque_account->save() === false) {
                throw new BusinessException('批量关联银行流水_支票数据修改失败'. get_data_object_error_msg($cheque_account) . '; 待修改的数据: ' . json_encode($cheque_account->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$PAYMENT_PURCHASE_ADD_ERROR);
            }
        }

        if (get_country_code() == GlobalEnums::MY_COUNTRY_CODE) {
            $purchase_payment_obj = self::find(
                [
                    'conditions' => 'id in ({id:array})',
                    'bind'       => [
                        'id' => array_values(array_unique($ids))//当前业务单id
                    ]
                ]
            );
            if (empty($purchase_payment_obj->toArray())) {
                throw new BusinessException('批量关联银行流水_采购_主数据数据不能为空' . json_encode($ids, JSON_UNESCAPED_UNICODE), ErrCode::$PURCHASE_PAYMENT_DETAIL_DATA_ERROR);
            }
            foreach ($purchase_payment_obj as $purchase_payment) {
                $purchase_payment->pay_status = Enums::LOAN_PAY_STATUS_PAY;
                $purchase_payment->updated_at = $current_time;
                if ($purchase_payment->save() === false) {
                    throw new BusinessException('批量关联银行流水_采购_修改状态失败' . get_data_object_error_msg($purchase_payment) . '; 待修改的数据: ' . json_encode($purchase_payment->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$PAYMENT_PURCHASE_ADD_ERROR);
                }
            }
        }
        return true;
    }


    /**
     * 关联银行流水撤销
     * @param array $user
     * @return mixed
     */
    public function cancel($user)
    {
        $no = self::$apply_no_by_code;
        //判断现有的状态
        if (empty($this)) {
            throw new BusinessException('撤销采购数据错误', ErrCode::$SYSTEM_ERROR);
        }

        $current_time   = date('Y-m-d H:i:s', time());
        $cheque_account = ChequeAccountModel::findFirst([
            'conditions' => 'cheque_code = :cheque_code:',
            'bind'       => ['cheque_code' => $no]
        ]);
        if (!empty($cheque_account)) {
            $cheque_account->exchange_status = ChequeEnums::CHECKS_EXCHANGE_STATUS_ING;//待承兑
            $cheque_account->exchange_date   = null;//承兑日期
            $cheque_account->updated_at      = $current_time;
            if ($cheque_account->save() === false) {
                throw new BusinessException('支票详情关联银行流水采购撤回修改支票失败' . $no, ErrCode::$PAYMENT_PURCHASE_CANCEL_ERROR);
            }
        }
        if (get_country_code() == GlobalEnums::MY_COUNTRY_CODE) {
            $purchase_payment = self::findFirst([
                'conditions' => 'ppno = :ppno:',
                'bind'       => ['ppno' => $this->ppno]
            ]);
            if (!empty($purchase_payment)) {
                $purchase_payment->pay_status = Enums::LOAN_PAY_STATUS_PENDING; //待支付
                $purchase_payment->updated_at = $current_time;
                if ($purchase_payment->save() === false) {
                    throw new BusinessException('支票详情关联银行流水采购撤回修改采购单失败' . get_data_object_error_msg($purchase_payment) . '; 待修改的数据: ' . json_encode($purchase_payment->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$PAYMENT_PURCHASE_CANCEL_ERROR);
                }
            }
        }
        return true;
    }

    public function batch_confirm($ids, $data)
    {
    }

    public function link(array $data)
    {

    }

    /**
     * 支付模块修改收款方银行信息 -> 同步更新业务侧收款方银行信息
     *
     * @param array $data
     * @param array $user
     * @return bool
     */
    public function syncUpdatePayeeInfo(array $data, array $user = [])
    {
        return true;
    }

}
