<?php
namespace App\Models\oa;

use App\Library\Enums\MaterialEnums;
use App\Models\Base;

class MaterialAssetReturnRecordModel extends Base
{
    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_oa');

        // setSource()方法指定数据库表
        $this->setSource('material_asset_return_record');
    }


    /**
     * 针对单个退回处理
     * @param integer $type 操作类型，1新增，2处理，3接收，4拒收，5转交，6撤销，7创建入库，8完成入库，9完成工单
     * @param object $item 要更新可审批编辑数据对象信息
     * @param array $update_data 更新的操作数据组
     * @param array $user 当前登陆操作人信息
     * @return bool
     */
    public function dealEditField($type, $item, $update_data, $user)
    {
        $edit_field = array_keys(MaterialEnums::$return_record_need_view_fields);
        $logData = [];//日志组
        foreach ($edit_field as $filed) {
            //未传递的字段不需要变更
            if (!isset($update_data[$filed])) {
                continue;
            }

            $logData[] = [
                $filed => $update_data[$filed]
            ];
        }

        //记录审批操作过程中更新日志
        if ($logData) {
            $log = new MaterialAssetReturnRecordModel();
            return $log->save([
                'return_id' => $item->id,
                'type' => $type,
                'staff_info_id' => $user['id'],
                'staff_info_name' => $user['name'],
                'node_department_id' => $user['node_department_id'],
                'node_department_name' => $user['department'],
                'job_title' => $user['job_title_id'],
                'job_name' => $user['job_title'],
                'content' => json_encode($logData, JSON_UNESCAPED_UNICODE),
                'created_at' => date('Y-m-d H:i:s'),
            ]);
        }
        return true;
    }

    /**
     * 针对批量退回处理
     * @param integer $type 操作类型，1新增，2处理，3接收，4拒收，5转交，6撤销，7创建入库，8完成入库，9完成工单
     * @param array $return_ids 退回申请单ID组
     * @param array $update_data 更新的操作数据组
     * @param array $user 当前登陆操作人信息
     * @return bool
     */
    public function dealEditFieldBatch($type, $return_ids, $update_data, $user)
    {
        //log内容
        $log_arr = [];
        //修改的会有多个字段
        $edit_field = array_keys(MaterialEnums::$return_record_need_view_fields);
        foreach ($edit_field as $filed) {
            //未传递的字段不需要变更
            if (!isset($update_data[$filed])) {
                continue;
            }

            $log_arr[] = [
                $filed => $update_data[$filed]
            ];
        }

        //内容为空,没有需要记录的
        if (empty($log_arr)) {
            return false;
        }

        //所有log
        $all_log_data = [];
        foreach ($return_ids as $return_id) {
            $all_log_data[] = [
                'return_id' => $return_id,
                'type' => $type,
                'staff_info_id' => $user['id'],
                'staff_info_name' => $user['name'],
                'node_department_id' => $user['node_department_id'],
                'node_department_name' => $user['department'],
                'job_title' => $user['job_title_id'],
                'job_name' => $user['job_title'],
                'content' => json_encode($log_arr, JSON_UNESCAPED_UNICODE),
                'created_at' => date('Y-m-d H:i:s'),
            ];
        }
        //批量写入
        $create_result = $this->batch_insert($all_log_data);
        if (!$create_result) {
            return false;
        }
        return true;
    }
}
