<?php

namespace App\Models\oa;

use App\Library\Enums\GlobalEnums;
use App\Library\Enums\MaterialEnums;
use App\Models\Base;
use App\Modules\Material\Models\MaterialAttachmentModel;

class MaterialLeaveAssetsDetailModel extends Base
{
    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_oa');

        // setSource()方法指定数据库表
        $this->setSource('material_leave_assets_detail');

        $this->hasMany(
            'id',
            MaterialAttachmentModel::class,
            'oss_bucket_key', [
                'params' => [
                    'conditions' => 'oss_bucket_type = ' . MaterialEnums::OSS_MATERIAL_TYPE_LEAVE_ASSET_DETAIL . ' and deleted = ' . GlobalEnums::IS_NO_DELETED
                ],
                "alias" => "Attach",
            ]
        );
    }

}
