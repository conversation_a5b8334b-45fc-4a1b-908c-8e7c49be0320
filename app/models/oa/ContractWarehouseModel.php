<?php
/**
 * 合同管理-仓库管理
 */

namespace App\Models\oa;

use App\Library\Enums\ContractEnums;
use App\Models\Base;
use App\Modules\Contract\Models\ContractStoreRentingModel;

/**
 * Class ContractWarehouseModel
 *
 * @package App\Models\oa
 */
class ContractWarehouseModel extends Base
{
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        $this->setSource('contract_warehouse');

        $this->hasOne(
            'id',
            WarehouseStoreModel::class,
            'warehouse_main_id', [
                'alias' => 'MainStoreInfo',
                'params' => [
                    'conditions' => 'store_flag = :store_flag: AND use_status = :use_status:',
                    'bind' => ['store_flag' => ContractEnums::WAREHOUSE_STORE_FLAG_MAIN, 'use_status' => ContractEnums::WAREHOUSE_STORE_USE_STATUS_ING],
                ]
            ]
        );

        $this->hasMany(
            'id',
            WarehouseStoreModel::class,
            'warehouse_main_id', [
                'alias' => 'ShareStoreList',
                'params' => [
                    'conditions' => 'store_flag = :store_flag: AND use_status = :use_status:',
                    'bind' => ['store_flag' => ContractEnums::WAREHOUSE_STORE_FLAG_SHARE, 'use_status' => ContractEnums::WAREHOUSE_STORE_USE_STATUS_ING],
                ]
            ]
        );

        $this->hasMany(
            'id',
            WarehouseStoreModel::class,
            'warehouse_main_id', [
                'alias' => 'AllStoreList',
                'params' => [
                    'conditions' => 'use_status = :use_status:',
                    'bind' => ['use_status' => ContractEnums::WAREHOUSE_STORE_USE_STATUS_ING],
                ]
            ]
        );

        $this->hasMany(
            'id',
            WarehouseChangeRecordModel::class,
            'warehouse_main_id', [
                'alias' => 'EffectivedChangeRecord',
                'params' => [
                    'conditions' => 'is_effective = :is_effective:',
                    'bind' => ['is_effective' => ContractEnums::WAREHOUSE_DATA_CHANGE_EFFECTIVED],
                    'columns' => ['created_at', 'created_id', 'created_name', 'before_data', 'after_data', 'oa_audit_no', 'change_apply_id'],
                    'order' => 'id DESC',
                ]
            ]
        );

        $this->hasMany(
            'id',
            WarehouseChangeApplyModel::class,
            'warehouse_main_id', [
                'alias' => 'ChangeApplyList',
                'params' => [
                    'columns' => ['id AS change_apply_id', 'oa_audit_no', 'biz_type', 'approval_status'],
                    'order' => 'id DESC',
                ]
            ]
        );

        // 获取一条使用中的网点信息
        $this->hasOne(
            'id',
            WarehouseStoreModel::class,
            'warehouse_main_id', [
                'alias' => 'OneStoreInfo',
                'params' => [
                    'conditions' => 'use_status = :use_status:',
                    'bind' => ['use_status' => ContractEnums::WAREHOUSE_STORE_USE_STATUS_ING],
                ]
            ]
        );

        // 获取一条使用中的网点信息
        $this->hasOne(
            'warehouse_id',
            ContractStoreRentingModel::class,
            'warehouse_id', [
                'alias' => 'OneRentalContract',
            ]
        );

        // 获取关联的租房合同列表
        $this->hasMany(
            'warehouse_id',
            ContractStoreRentingModel::class,
            'warehouse_id', [
                'alias' => 'RentalContractList',
            ]
        );
    }

}
