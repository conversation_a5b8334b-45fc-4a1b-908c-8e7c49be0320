<?php

namespace App\Models\oa;

use App\Models\Base;

class MaterialLeaveAssetsRemindSetModel extends Base
{
    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_oa');

        // setSource()方法指定数据库表
        $this->setSource('material_leave_assets_remind_set');

        //离职资产-短信邮件设置详情表
        $this->hasMany(
            'id',
            MaterialLeaveAssetsRemindSetDetailModel::class,
            'remind_set_id', [
                'params' => [],
                'alias' => 'Details',
            ]
        );
    }
}
