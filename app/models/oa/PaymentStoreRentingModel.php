<?php

namespace App\Models\oa;

use App\Models\Base;
use App\Modules\Payment\Models\PaymentStoreRentingDetail;


class PaymentStoreRentingModel extends Base
{

    public function initialize()
    {
        $this->setConnectionService('db_oa');

        $this->setSource('payment_store_renting');

        $this->hasMany(
            'id',
            PaymentStoreRentingDetail::class,
            'store_renting_id',
            [
                'alias' => 'Details'
            ]
        );
    }

}
