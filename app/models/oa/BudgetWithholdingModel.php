<?php
/**
 * 预算管理-费用预提
 */

namespace App\Models\oa;
use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Models\Base;

/**
 * Class BudgetWithholdingModel
 * @package App\Models\oa
 */
class BudgetWithholdingModel extends Base
{
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        $this->setSource('budget_withholding');

        //费用明细
        $this->hasMany(
            'id',
            BudgetWithholdingDetailModel::class,
            'budget_withholding_id', [
                'alias' => 'Details',
            ]
        );

        //附件
        $this->hasMany(
            'id',
            SysAttachmentModel::class,
            'oss_bucket_key', [
                'params' => [
                    'conditions' => 'oss_bucket_type = '.Enums::OSS_BUCKET_TYPE_BUDGET_WITHHOLDING_ADD.' and deleted = ' . GlobalEnums::IS_NO_DELETED
                ],
                'alias' => 'Attachments',
            ]
        );
    }
}
