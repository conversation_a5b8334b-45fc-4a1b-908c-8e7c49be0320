<?php
/**
 * 仓库管理-仓库线索-验证记录
 */

namespace App\Models\oa;

use App\Models\Base;
use App\Library\Enums;
use App\Library\Enums\GlobalEnums;

/**
 *
 * Class WarehouseThreadVerifyRecordModel
 *
 * @package App\Models\oa
 */
class WarehouseThreadVerifyRecordModel extends Base
{
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        $this->setSource('warehouse_thread_verify_record');

        //仓库管理-仓库线索-验证记录-大门照片附件
        $this->hasMany(
            'id',
            SysAttachmentModel::class,
            'oss_bucket_key', [
                'params' => [
                    'conditions' => 'oss_bucket_type = ' . Enums::OSS_BUCKET_TYPE_WAREHOUSE_THREAD_VERIFY_DOOR_FILE . ' and deleted = ' . GlobalEnums::IS_NO_DELETED
                ],
                'alias' => 'DoorAttachment',
            ]
        );

        //仓库管理-仓库线索-验证记录-门口道路照片附件
        $this->hasMany(
            'id',
            SysAttachmentModel::class,
            'oss_bucket_key', [
                'params' => [
                    'conditions' => 'oss_bucket_type = ' . Enums::OSS_BUCKET_TYPE_WAREHOUSE_THREAD_VERIFY_ROAD_FILE . ' and deleted = ' . GlobalEnums::IS_NO_DELETED
                ],
                'alias' => 'RoadAttachment',
            ]
        );
    }

}
