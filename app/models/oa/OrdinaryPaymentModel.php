<?php

namespace App\Models\oa;

use App\Library\Enums;
use App\Library\Enums\PayEnums;
use App\Models\Base;
use App\Library\ErrCode;
use App\Library\Enums\BankFlowEnums;
use App\Library\Enums\ChequeEnums;
use App\Library\Enums\GlobalEnums;
use App\Modules\Cheque\Services\ChequeService;
use App\Library\Exception\BusinessException;
use App\Library\CInterface\BankFlowModelInterface;
use App\Modules\OrdinaryPayment\Models\OrdinaryPaymentExtend;

class OrdinaryPaymentModel extends Base implements BankFlowModelInterface
{
    private static $apply_no_by_code;

    public function initialize()
    {
        $this->setConnectionService('db_oa');

        $this->setSource('ordinary_payment');

        $this->hasOne(
            'id',
            OrdinaryPaymentExtend::class,
            'ordinary_payment_id', [
                'alias' => 'Pay'
            ]
        );
    }

    /**
     * @param $condition
     * @return mixed
     */
    public static function getFirst($condition)
    {
        return parent::findFirst($condition);
    }

    /**
     * @param null $parameters
     * @return mixed
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }


    /**
     * 通过单号获得Model
     * @param string $no
     * @return object
     */
    public function getModelByNo(string $no)
    {
        //根据bank_flow_oa表里面的 no  获取  业务申请单号=> no 这样格式的数据
        $cheque_arr = ChequeService::getInstance()->chequeCodeByRel([$no]);
        self::$apply_no_by_code = $no;
        //查询业务主数据
        return self::findFirst([
            'conditions' => 'apply_no = :apply_no:',
            'bind'       => ['apply_no' => array_keys($cheque_arr)[0]]
        ]);

    }

    /**
     * 通过单号数组获得Model=只获得审核通过
     * @param array $cheque_code_arr
     * @param bool $has_pay
     * @param bool $is_row 行上上传为true 头上上传为 false
     * @return mixed
     */
    public function getModelByNos(array $cheque_code_arr, bool $has_pay = false, bool $is_row = false)
    {

        if (!is_array($cheque_code_arr) || empty($cheque_code_arr)) {
            return ['msg' => 'cheque_code_empty_data'];
        }

        //根据传入的支票号返回业务单号
        $apply_no_arr           = ChequeService::getInstance()->chequeCodeByRel(array_values($cheque_code_arr), 1);
        $main                   = [];
        self::$apply_no_by_code = $apply_no_arr;
        if (!empty($apply_no_arr)) {

            $pay_status = Enums::LOAN_PAY_STATUS_PENDING;
            $country    = get_country_code();
            if ($country == GlobalEnums::PH_COUNTRY_CODE) {
                $pay_status = Enums::LOAN_PAY_STATUS_PAY;
            }

            if ($is_row) {
                //一个普通付款可以对应对个相同或是不同的支票号
                $oa_biz_no_arr   = array_values(array_unique(array_column($apply_no_arr, 'oa_biz_no')));
                $cheque_code_arr = array_values(array_unique(array_column($apply_no_arr, 'cheque_code')));
                $builder         = $this->modelsManager->createBuilder();
                $builder->from(['op' => OrdinaryPaymentModel::class]);
                $builder->leftJoin(PaymentModel::class, 'op.apply_no = p.no', 'p');
                $builder->leftJoin(PaymentCheckModel::class, 'p.id = pc.payment_id', 'pc');
                $builder->inWhere('pc.ticket_no', $cheque_code_arr);
                $builder->inWhere('op.apply_no', $oa_biz_no_arr);
                $builder->andWhere('op.approval_status = :approval_status: and op.pay_status = :pay_status: and pc.is_deleted = :is_deleted:', ['approval_status' => Enums::CONTRACT_STATUS_APPROVAL, 'pay_status' => $pay_status, 'is_deleted' => GlobalEnums::IS_NO_DELETED]);
                $builder->columns('op.apply_no, pc.ticket_no, pc.amount, op.id, op.currency, op.pay_status, op.approval_status');
                $main = $builder->getQuery()->execute()->toArray();
                if (count($oa_biz_no_arr) != count(array_unique(array_column($main, 'apply_no')))) {
                    return ['msg' => 'payment_pay_status_err'];
                }
            } else {
                $oa_biz_no_arr = array_column($apply_no_arr, 'cheque_code', 'oa_biz_no');
                $main          = self::find([
                    'columns'    => ['apply_no, amount_total_actually as amount, id, currency, pay_status, approval_status'],
                    'conditions' => 'apply_no in ({apply_no:array}) and approval_status = :approval_status: and pay_status = :pay_status:',
                    'bind'       => [
                        'apply_no'        => array_column($apply_no_arr, 'oa_biz_no'),
                        'approval_status' => Enums::CONTRACT_STATUS_APPROVAL,
                        'pay_status'      => $pay_status
                    ]
                ])->toArray();
                if (empty($main)) {
                    return ['msg' => 'empty_data'];
                }
                foreach ($main as &$value) {
                    $value['ticket_no'] = $oa_biz_no_arr[$value['apply_no']] ?? '';
                }

                //普通付款 在头上不可以多个支票号关联一个业务号
                if (count(array_keys($oa_biz_no_arr)) != count($apply_no_arr)) {
                    return ['msg' => 'bank_flow_expense_update_biz_info_error_010'];
                }

                if (count(array_keys($oa_biz_no_arr)) != count($main)) {
                    return ['msg' => 'payment_pay_status_err'];
                }
            }
        }

        return $main;
    }

    /**
     * 通过model获得银行流水格式化数据
     * @param array $date   业务数据
     * @return array
     */
    public function getFormatData($date = [])
    {
        $arr = array_column(self::$apply_no_by_code, 'total_amount', 'cheque_code');
        return [
            'oa_value'   => $date['id'],
            'oa_type'    => BankFlowEnums::BANK_FLOW_OA_TYPE_CHEQUE_ACCOUNT_ORDINARY_PAYMENT,
            'no'         => $date['ticket_no'],
            'amount'     => floatval($arr[$date['ticket_no']]),
            'currency'   => $date['currency'],
            'status'     => $date['approval_status'],
            'pay_status' => $date['pay_status'],
            'apply_no'   => $date['apply_no'],
        ];
    }

    /**
     * model 批量关联银行流水
     *
     * @param $ids
     * @param array $data
     * @return mixed
     * @throws BusinessException
     */
    public function batch_link($ids, $data)
    {
        //对行关联做特殊处理 因为行上可以关联多行
        $no = [];
        if (!empty($data['is_link'])) {
            //上传的数据是混合数据 对oa_value分类处理
            foreach ($data['order_info'] as $order_info) {
                if ($order_info['oa_type'] == BankFlowEnums::BANK_FLOW_OA_TYPE_CHEQUE_ACCOUNT_ORDINARY_PAYMENT) {
                    $no[] = $order_info['no'];
                }
            }
        } else {
            $no = [$data['order_info']['no']];
        }

        if (empty($no)) {
            throw new BusinessException('支票数据不存在' . json_encode($no, JSON_UNESCAPED_UNICODE), ErrCode::$ORDINARY_PAYMENT_DATA_BATCH_LINK_ERROR);
        }

        $current_time       = date('Y-m-d H:i:s', time());
        $cheque_account_obj = ChequeAccountModel::find(
            [
                'conditions' => 'cheque_code in ({cheque_code:array}) and exchange_status = :exchange_status:',
                'bind'       => [
                    'cheque_code'     => $no,//当前上传数据的支票号
                    'exchange_status' => ChequeEnums::CHECKS_EXCHANGE_STATUS_ING
                ]
            ]
        );
        if (empty($cheque_account_obj->toArray())) {
            throw new BusinessException('支票状态数据不对' . json_encode($no, JSON_UNESCAPED_UNICODE), ErrCode::$ORDINARY_PAYMENT_DATA_BATCH_LINK_ERROR);
        }

        foreach ($cheque_account_obj as $cheque_account) {
            $cheque_account->exchange_status = ChequeEnums::CHECKS_EXCHANGE_STATUS_END;//已承兑
            $cheque_account->exchange_date   = $data['date'];//承兑日期
            $cheque_account->updated_at      = $current_time;
            if ($cheque_account->save() === false) {
                throw new BusinessException('支票数据修改失败' . json_encode($cheque_account->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$PAYMENT_PAYMENT_SAVE_BATCH_LINK_ERROR);
            }
        }

        if (get_country_code() == GlobalEnums::MY_COUNTRY_CODE) {
            $ordinary_payment_obj = self::find(
                [
                    'conditions' => 'id in ({id:array})',
                    'bind'       => [
                        'id' => array_values(array_unique($ids))//当前业务单id
                    ]
                ]
            );
            if (empty($ordinary_payment_obj->toArray())) {
                throw new BusinessException('业务端主数据数据不能为空' . json_encode($ids, JSON_UNESCAPED_UNICODE), ErrCode::$ORDINARY_PAYMENT_DATA_BATCH_LINK_ERROR);
            }
            foreach ($ordinary_payment_obj as $ordinary_payment) {
                $ordinary_payment->pay_status = Enums::LOAN_PAY_STATUS_PAY;
                $ordinary_payment->updated_at = $current_time;
                if ($ordinary_payment->save() === false) {
                    throw new BusinessException('支票数据修改普通付款失败' . get_data_object_error_msg($ordinary_payment) . '; 待更新数据: ' . json_encode($ordinary_payment->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$PAYMENT_PAYMENT_SAVE_BATCH_LINK_ERROR);
                }
            }
        }
        return true;
    }


    /**
     * 关联银行流水撤销
     *
     * @param array $user
     * @return mixed
     * @throws BusinessException
     */
    public function cancel(array $user)
    {
        $no = self::$apply_no_by_code;//支票号
        //判断现有的状态
        if (empty($this)) {
            throw new BusinessException('数据不能为空', ErrCode::$SYSTEM_ERROR);
        }
        $current_time   = date('Y-m-d H:i:s', time());
        $cheque_account = ChequeAccountModel::findFirst([
            'conditions' => 'cheque_code = :cheque_code:',
            'bind'       => ['cheque_code' => $no]
        ]);
        if (!empty($cheque_account)) {
            $cheque_account->exchange_status = ChequeEnums::CHECKS_EXCHANGE_STATUS_ING;//待承兑
            $cheque_account->exchange_date   = null;//承兑日期
            $cheque_account->updated_at      = $current_time;
            if ($cheque_account->save() === false) {
                throw new BusinessException('支票详情关联银行流水普通付款撤回修改支票失败' . get_data_object_error_msg($cheque_account) . '; 待更新数据: ' . json_encode($cheque_account->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$SYSTEM_ERROR);
            }
        }
        if (get_country_code() == GlobalEnums::MY_COUNTRY_CODE) {
            $ordinary_payment = self::findFirst([
                'conditions' => 'apply_no = :apply_no:',
                'bind'       => ['apply_no' => $this->apply_no]
            ]);
            if (!empty($ordinary_payment)) {
                $ordinary_payment->pay_status = Enums::LOAN_PAY_STATUS_PENDING; //待支付
                $ordinary_payment->updated_at = $current_time;
                if ($ordinary_payment->save() === false) {
                    throw new BusinessException('支票详情关联银行流水普通付款撤回修改普通付款单失败 '. get_data_object_error_msg($ordinary_payment) . '; 待更新数据: ' . json_encode($ordinary_payment->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$SYSTEM_ERROR);
                }
            }
        }

        return true;
    }

    public function batch_confirm($ids, $data)
    {

    }

    public function link(array $data)
    {
    }

    /**
     * 支付模块修改收款方银行信息 -> 同步更新业务侧收款方银行信息
     *
     * @param array $data
     * @param array $user
     * @return bool
     */
    public function syncUpdatePayeeInfo(array $data, array $user = [])
    {
        return true;
    }
}
