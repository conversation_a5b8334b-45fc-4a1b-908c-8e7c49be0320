<?php
/**
 * 仓库管理-仓库需求
 */

namespace App\Models\oa;

use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Models\Base;

/**
 *
 * Class WarehouseRequirementModel
 *
 * @package App\Models\oa
 */
class WarehouseRequirementModel extends Base
{
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        $this->setSource('warehouse_requirement');

        //仓库管理-仓库需求管理-附件
        $this->hasMany(
            'id',
            SysAttachmentModel::class,
            'oss_bucket_key', [
                'params' => [
                    'conditions' => 'oss_bucket_type = ' . Enums::OSS_BUCKET_TYPE_WAREHOUSE_REQUIREMENT_INFO_FILE . ' and deleted = ' . GlobalEnums::IS_NO_DELETED
                ],
                'alias' => 'Attachment',
            ]
        );

        //仓库管理-仓库需求管理-确认记录
        $this->hasMany(
            'id',
            WarehouseRequirementConfirmRecordModel::class,
            'requirement_id', [
                'params' => [],
                'alias'  => 'ConfirmList',
            ]
        );
    }

}
