<?php

namespace App\Models\oa;

use App\Library\Enums\GlobalEnums;
use App\Models\Base;

class MaterialAssetReturnStorageModel extends Base
{
    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_oa');

        // setSource()方法指定数据库表
        $this->setSource('material_asset_return_storage');

        //退回资产-申请附件
        $this->hasMany(
            'id',
            MaterialAssetReturnStorageProductModel::class,
            'return_storage_id', [
                'params' => [
                    'conditions' => 'is_deleted=' . GlobalEnums::IS_NO_DELETED
                ],
                'alias' => 'Products',
            ]
        );
    }
}
