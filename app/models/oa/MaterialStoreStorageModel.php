<?php
namespace App\Models\oa;

use App\Models\Base;

/**
 * 物料/资产网点分仓规则配置表
 * Class MaterialStoreStorageModel
 * @package App\Models\oa
 */
class MaterialStoreStorageModel extends Base
{
    /**
     * Initialize method for model.
     */
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        $this->setSource('material_store_storage');

        $this->hasMany(
            'id',
            MaterialStoreStorageRuleModel::class,
            'store_storage_id', [
                'alias' => 'Rules',
            ]
        );
    }

}
