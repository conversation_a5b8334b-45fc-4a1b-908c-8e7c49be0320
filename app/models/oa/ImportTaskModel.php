<?php

namespace App\Models\oa;

use App\Models\Base;


class ImportTaskModel extends Base
{
    /**
     * @var int
     */
    public $type;
    /**
     * @var int
     */
    public $status;
    /**
     * @var string
     */
    public $task_remark;
    /**
     * @var string
     */
    public $file_url;
    /**
     * @var string
     */
    public $args_json;
    /**
     * @var false|string
     */
    public $created_at;
    /**
     * @var false|string
     */
    public $updated_at;
    /**
     * @var int
     */
    public $staff_info_id;
    public $language;

    /**
     * @var string
     */
    public $staff_name;

    /**
     * @var string
     */
    public $other_condition;

    public function initialize()
    {
        $this->setConnectionService('db_oa');
        // setSource()方法指定数据库表
        $this->setSource('import_task');
    }
}