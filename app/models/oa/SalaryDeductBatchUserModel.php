<?php
namespace App\Models\oa;

use App\Models\Base;

/**
 * 薪资抵扣总数据-批次员工明细表
 * Class SalaryDeductBatchUserModel
 * @package App\Models\oa
 */
class SalaryDeductBatchUserModel extends Base
{
    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_oa');

        // setSource()方法指定数据库表
        $this->setSource('salary_deduct_batch_user');
    }
}
