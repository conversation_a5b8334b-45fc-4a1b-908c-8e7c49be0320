<?php
/**
 * Created by PhpStorm.
 * Date: 2021/10/22
 * Time: 10:58
 */

namespace App\Library\Enums;

/**
 * 供应商枚举
 * Class enums
 */
final class VendorEnums
{
    //供应商模块
    const VENDOR_MODULE_OTHER = 0;           //其他(未指定枚举的模块)
    const VENDOR_PURCHASE = 1;               //采购模块
    const VENDOR_ORDINARY_PAYMENT = 2;       //普通付款模块
    const VALIDATE_VENDOR_MODULE = self::VENDOR_MODULE_OTHER . ',' . self::VENDOR_PURCHASE . ',' . self::VENDOR_ORDINARY_PAYMENT;

    //搜索供应商来源
    const VENDOR_SEARCH_OTHER = 0;           //其他(未指定枚举的)
    const VENDOR_SEARCH_PURCHASE_ORDER = 1;  //采购-采购订单新增
    const VENDOR_SEARCH_PURCHASE_PAYMENT = 2;//采购-采购付款申请单新增
    const VENDOR_SEARCH_PURCHASE_SAMPLE = 3; //采购-样品确认新增
    const VENDOR_SEARCH_ASSETS_ACCOUNT = 4;  //资产台账创建
    const VENDOR_SEARCH_CONTRACT_ADD = 5;    //其他合同新增
    const VALIDATE_VENDOR_SEARCH_SOURCE = self::VENDOR_SEARCH_OTHER . ',' . self::VENDOR_SEARCH_PURCHASE_ORDER . ',' . self::VENDOR_SEARCH_PURCHASE_PAYMENT . ',' . self::VENDOR_SEARCH_PURCHASE_SAMPLE . ',' . self::VENDOR_SEARCH_ASSETS_ACCOUNT . ',' . self::VENDOR_SEARCH_CONTRACT_ADD;


    const VENDOR_PURCHASE_CATE_SUPPLY = 1; //主供产品
    const VENDOR_PURCHASE_CATE_SERVICE = 2;//服务类型

    const VENDOR_GRADE_TYPE_1 = 1;         //等级变更
    const VENDOR_GRADE_TYPE_2 = 2;         //状态变更

    //供应商分级管理审批动作
    const VENDOR_ACTION_REGISTER = 1;      //注册操作
    const VENDOR_ACTION_AUTHENTICATION = 2;//认证操作
    const VENDOR_ACTION_CANCELLATION = 3;  //注销操作
    const VENDOR_ACTION_BLOCK = 4;         //拉黑操作
    const VENDOR_ACTION_NORMA = 5;         //调整为正常
    const VENDOR_ACTION_SUSPEND = 6;       //暂停操作

    public const PAYMENT_METHOD_BANK = 1;        //银行转账
    public const PAYMENT_METHOD_THIRD_PAY = 2;   //第三方支付
    public const PAYMENT_METHOD_BOTH = 3;        //两种支付方式都有

    //供应商等级
    public const VENDOR_GRADE_NOT_REGISTER = 1;  //未注册
    public const VENDOR_GRADE_REGISTER = 2;      //注册
    public const VENDOR_GRADE_AUTHENTICATION = 3;//认证
    public const VENDOR_GRADE_CANCELLATION = 4;  //注销
    public const VENDOR_GRADE_BLOCK = 5;         //拉黑

    //供应商等级状态
    public const VENDOR_GRADE_STATUS_DRAFT = 1;  //草稿
    public const VENDOR_GRADE_STATUS_NORMAL = 2; //正常
    public const VENDOR_GRADE_STATUS_SUSPEND = 3;//暂停
    public const VENDOR_GRADE_STATUS_INVALID = 4;//作废

    //供应商  采购类别

    public const VENDOR_PURCHASE_TYPE_1 = 1; //集团采购
    public const VENDOR_PURCHASE_TYPE_2 = 2; //本地采购

    //暂停时长
    public const SUSPEND_MONTH_ONE = 1;      //1个月
    public const SUSPEND_MONTH_TWO = 2;      //2个月
    public const SUSPEND_MONTH_THREE = 3;    //3个月
    public const SUSPEND_MONTH_FOUR = 4;     //4个月
    public const SUSPEND_MONTH_FIVE = 5;     //5个月
    public const SUSPEND_MONTH_SIX = 6;      //6个月


    //支付方式
    public static $payment_method = [
        self::PAYMENT_METHOD_BANK      => 'supplier.payment_method.1',
        self::PAYMENT_METHOD_THIRD_PAY => 'supplier.payment_method.2',
        self::PAYMENT_METHOD_BOTH      => 'supplier.payment_method.3',
    ];

    //供应商等级
    public static $vendor_grade_item = [
        self::VENDOR_GRADE_NOT_REGISTER   => 'supplier.grade.not_register',
        self::VENDOR_GRADE_REGISTER       => 'supplier.grade.register',
        self::VENDOR_GRADE_AUTHENTICATION => 'supplier.grade.authentication',
        self::VENDOR_GRADE_CANCELLATION   => 'supplier.grade.cancellation',
        self::VENDOR_GRADE_BLOCK          => 'supplier.grade.block',
    ];

    //供应商等级状态
    public static $vendor_grade_status_item = [
        self::VENDOR_GRADE_STATUS_DRAFT   => 'supplier.grade_status.draft',
        self::VENDOR_GRADE_STATUS_NORMAL  => 'supplier.grade_status.normal',
        self::VENDOR_GRADE_STATUS_SUSPEND => 'supplier.grade_status.suspend',
        self::VENDOR_GRADE_STATUS_INVALID => 'supplier.grade_status.invalid',
    ];

    //供应商采购类型
    public static $vendor_purchase_type_item = [
        self::VENDOR_PURCHASE_TYPE_1 => 'supplier.purchase_type_1',
        self::VENDOR_PURCHASE_TYPE_2 => 'supplier.purchase_type_2',
    ];

    //暂停时长
    public static $vendor_suspend_item = [
        self::SUSPEND_MONTH_ONE   => 'supplier.suspend_month.1',
        self::SUSPEND_MONTH_TWO   => 'supplier.suspend_month.2',
        self::SUSPEND_MONTH_THREE => 'supplier.suspend_month.3',
        self::SUSPEND_MONTH_FOUR  => 'supplier.suspend_month.4',
        self::SUSPEND_MONTH_FIVE  => 'supplier.suspend_month.5',
        self::SUSPEND_MONTH_SIX   => 'supplier.suspend_month.6',
    ];

    //应用模块

    public static $application_module_arr = [
        self::VENDOR_PURCHASE         => 'application_module.1',
        self::VENDOR_ORDINARY_PAYMENT => 'application_module.2',
    ];


    // 公司性质，1：自然人；2：公司
    const COMPANY_NATURE_INDIVIDUAL = 1;
    const COMPANY_NATURE_COMPANY = 2;

    const CERTIFICATE_TYPE_ID = 3; //身份证
    const CERTIFICATE_TYPE_PASSPORT = 4; //护照
}