<?php

namespace App\Library\Enums;

final class DepositEnums
{
    const PAGE_NUM = 1;  //当前页码
    const PAGE_SIZE = 20;//每页数量
    const DEPOSIT_DOWNLOAD_LIMIT = 30000;

    const DEPOSIT_IS_DELETED_NO = 1;
    const DEPOSIT_IS_DELETED_YES = 0;
    const DEPOSIT_PAY_AT = '2022-11-01';//押金各个模块银行流水日期

    const DEPOSIT_REIMBURSEMENT = 1;         //报销
    const DEPOSIT_ORDINARY_PAYMENT = 2;      //普通付款
    const DEPOSIT_PURCHASE_PAYMENT = 3;      //采购
    const DEPOSIT_PAYMENT_STORE_RENTING = 4; //租房
    const DEPOSIT_TYPE_RULE = self::DEPOSIT_REIMBURSEMENT . ',' . self::DEPOSIT_ORDINARY_PAYMENT . ',' . self::DEPOSIT_PURCHASE_PAYMENT . ',' . self::DEPOSIT_PAYMENT_STORE_RENTING;

    const DEPOSIT_PREFIX = 'YJGH';//押金规划 数据code前缀
    const IS_EXPORT = 'export';
    const STORE_HEADER_OFFICE_NAME = 'Header Office';
    const EXPORT_TYPE_ONE = 1;       //导出类型  按照uid导出全部
    const EXPORT_TYPE_ZERO = 0;      //导出类型   导出全部
    const LIST_TYPE_DATA_EXPORT = 6; //导出数据

    const OBJECT_LEVEL = 1;              //默认等级
    const ATTACHMENT_LAST_NUB_SIZE = 20; //最大附件上传个数
    // 列表类型
    const LIST_TYPE_APPLY = 1;           //押金申请
    const LIST_TYPE_AUDIT = 2;           //归还审核
    const LIST_TYPE_DATA = 4;            //数据查询
    const LIST_TYPE_CONSULTED_REPLY = 5; //征询回复
    const LIST_TYPE_EXPORT_DATA = 6;     //数据导出

    const REIMBURSEMENT_FIX = 'BX';               //报销
    const ORDINARY_PAYMENT_FIX = 'PT';            //普通付款
    const PURCHASE_PAYMENT_FIX = 'PA';            //采购
    const PAYMENT_STORE_RENTING_FIX = 'FK';       //租房

    public static $deposit_no_fix_arr = [
        self::REIMBURSEMENT_FIX         => self::DEPOSIT_REIMBURSEMENT,
        self::ORDINARY_PAYMENT_FIX      => self::DEPOSIT_ORDINARY_PAYMENT,
        self::PURCHASE_PAYMENT_FIX      => self::DEPOSIT_PURCHASE_PAYMENT,
        self::PAYMENT_STORE_RENTING_FIX => self::DEPOSIT_PAYMENT_STORE_RENTING,
    ];

    //和之前的id类型做映射
    public static $deposit_reimbursement_mapping_order_type = [
        self::DEPOSIT_REIMBURSEMENT         => 1,
        self::DEPOSIT_ORDINARY_PAYMENT      => 3,
        self::DEPOSIT_PURCHASE_PAYMENT      => 2,
        self::DEPOSIT_PAYMENT_STORE_RENTING => 4,
    ];

    public static $deposit_modules = [
        self::DEPOSIT_REIMBURSEMENT         => 'deposit_reimbursement.1',
        self::DEPOSIT_ORDINARY_PAYMENT      => 'deposit_ordinary_payment.2',
        self::DEPOSIT_PURCHASE_PAYMENT      => 'deposit_purchase_payment.3',
        self::DEPOSIT_PAYMENT_STORE_RENTING => 'deposit_payment_store_renting.4',
    ];

    public static $deposit_arr = [
        ['value' => self::DEPOSIT_REIMBURSEMENT, 'label' => 'deposit_reimbursement.1'],
        ['value' => self::DEPOSIT_ORDINARY_PAYMENT, 'label' => 'deposit_ordinary_payment.2'],
        ['value' => self::DEPOSIT_PURCHASE_PAYMENT, 'label' => 'deposit_purchase_payment.3'],
        ['value' => self::DEPOSIT_PAYMENT_STORE_RENTING, 'label' => 'deposit_payment_store_renting.4'],
    ];

    //归还状态
    const DEPOSIT_RETURN_STATUS_DEFAULT = 0;      //数据默认状态
    const DEPOSIT_RETURN_STATUS_NOT = 1;          //未归还
    const DEPOSIT_RETURN_STATUS_ING = 2;          //归还中
    const DEPOSIT_RETURN_STATUS_LAST_FILE = 3;    //已归还
    const DEPOSIT_RETURN_STATUS_INTERVENTION = 4; //法务介入中
    const DEPOSIT_RETURN_STATUS_DETERMINE = 5;    //法务介入中已确认

    //归还状态集合字符
    const DEPOSIT_VALIDATE_RETURN_STATUS_STR = DepositEnums::DEPOSIT_RETURN_STATUS_NOT . ',' . DepositEnums::DEPOSIT_RETURN_STATUS_ING . ',' . DepositEnums::DEPOSIT_RETURN_STATUS_LAST_FILE . ',' . DepositEnums::DEPOSIT_RETURN_STATUS_INTERVENTION . ',' . DepositEnums::DEPOSIT_RETURN_STATUS_DETERMINE;

    public static $contract_return_list = [
        self::DEPOSIT_RETURN_STATUS_NOT          => 'deposit_return_status_not.1',
        self::DEPOSIT_RETURN_STATUS_ING          => 'deposit_return_status_not.2',
        self::DEPOSIT_RETURN_STATUS_LAST_FILE    => 'deposit_return_status_not.3',
        self::DEPOSIT_RETURN_STATUS_INTERVENTION => 'deposit_return_status_not.4',
        self::DEPOSIT_RETURN_STATUS_DETERMINE    => 'deposit_return_status_not.5',
    ];

    public static $contract_return_arr = [
        ['value' => self::DEPOSIT_RETURN_STATUS_NOT, 'label' => 'deposit_return_status_not.1'],
        ['value' => self::DEPOSIT_RETURN_STATUS_ING, 'label' => 'deposit_return_status_not.2'],
        ['value' => self::DEPOSIT_RETURN_STATUS_LAST_FILE, 'label' => 'deposit_return_status_not.3'],
        ['value' => self::DEPOSIT_RETURN_STATUS_INTERVENTION, 'label' => 'deposit_return_status_not.4'],
        ['value' => self::DEPOSIT_RETURN_STATUS_DETERMINE, 'label' => 'deposit_return_status_not.5'],
    ];

    //供应商
    public static $vendor_arr = [
        ['cost_company_id' => '8000000', 'cost_company_name' => 'vendor'],
    ];

    //log里面的操作内容
    const DEPOSIT_EDIT_LOG_NO = 1;                //更新合同
    const DEPOSIT_EDIT_LOG_STATUS = 2;            //更改状态
    const DEPOSIT_EDIT_LOG_APPLY = 3;             //转交
    public static $DEPOSIT_EDIT_LOG = [
        self::DEPOSIT_EDIT_LOG_NO     => 'deposit_edit_log_type.1',
        self::DEPOSIT_EDIT_LOG_STATUS => 'deposit_edit_log_type.2',
        self::DEPOSIT_EDIT_LOG_APPLY  => 'deposit_edit_log_type.3',
    ];

    // 编辑枚举
    public static $deposit_edit_edit = [
        ['value' => self::DEPOSIT_EDIT_LOG_NO, 'label' => 'deposit_return_status_not.1'],
        ['value' => self::DEPOSIT_EDIT_LOG_STATUS, 'label' => 'deposit_return_status_not.2'],
    ];

    const ORGANIZATION_TYPE_STORE = 1;          //网点
    const ORGANIZATION_TYPE_COM = 2;            //总部
    const DEPOSIT_TASK_DOWNLOAD_LIMIT = 1000000;//脚本执行导出最大限制
    const DEPOSIT_TASK_TYPE_STATUS_DAY = 1;     //导出每天
    const DEPOSIT_TASK_TYPE_STATUS_MONTH = 2;   //导出每月

    public static $organization_type = [
        self::ORGANIZATION_TYPE_STORE => 'budget_organization_type.1',
        self::ORGANIZATION_TYPE_COM   => 'budget_organization_type.2',
    ];
    public static $cost_store_list   = [
        ['value' => self::ORGANIZATION_TYPE_STORE, 'label' => 'budget_organization_type.1'],
        ['value' => self::ORGANIZATION_TYPE_COM, 'label' => 'budget_organization_type.2'],
    ];

}
