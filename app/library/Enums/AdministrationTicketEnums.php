<?php

namespace App\Library\Enums;

final class AdministrationTicketEnums
{

    const TICKET_DOWNLOAD_LIMIT = 100000; //最大导出数量
    const ADMINISTRATION_QUESTION_TYPE = 1;  //问题类型

    const TICKET_CREATED_TYPE_SUBMIT = 1; //提交人回复
    const TICKET_CREATED_TYPE_ANSWER = 2;//回复人回复

    //行政工单处理状态  1待回复 2已回复 3已关闭
    const ADMINISTRATION_TICKET_STATUS_ING = 1;
    const ADMINISTRATION_TICKET_STATUS_REPLY = 2;
    const ADMINISTRATION_TICKET_STATUS_CLOSE = 3;

    public static $administration_ticket_status = [
        self::ADMINISTRATION_TICKET_STATUS_ING => 'administration_ticket_status.' . self::ADMINISTRATION_TICKET_STATUS_ING,
        self::ADMINISTRATION_TICKET_STATUS_REPLY => 'administration_ticket_status.' . self::ADMINISTRATION_TICKET_STATUS_REPLY,
        self::ADMINISTRATION_TICKET_STATUS_CLOSE => 'administration_ticket_status.' . self::ADMINISTRATION_TICKET_STATUS_CLOSE
    ];

}
