<?php
namespace App\Library\Enums;
/**
 * 灭火器常用枚举数据
 * Class enums
 */
final class ExtinguisherEnums{

    //灭火器支持的网点类型
    public static $extinguisher_store_category = [
        1=>'SP',
        4=>'SHOP(PICKUP ONLY)',
        5=>'SHOP(PICKUP&DELIVERY)',
        7=>'USHOP',
        8=>'HUB',
        10=>'BDC',
        12=>'B-HUB'
    ];

    // 区域定义
    public static $areas = [
        1 => 'BKK',
        2 => 'CEN+EAST',
        3 => 'CEN+WEST',
        4 => 'NORTH',
        5 => 'NORTH EAST',
        6 => 'SOUCH',
        7 => 'CEN',
    ];

    //分拣区定义
    public static $sorting_no_map = [
        'B' => 'BKK',
        'C' => 'Central',
        'N' => 'North',
        'NE' => 'NorthEast',
        'S' => 'South',
    ];

    //灭火器类型
    public static $extinguisher_type = [
        1,
        2,
        3
    ];

    //检查状态1未检查，2已检查
    const EXTINGUISHER_CHECKOUT_STATUS_NOT = 1;
    const EXTINGUISHER_CHECKOUT_STATUS_DETERMINED = 2;
    //灭火器检查状态
    public static $extinguisher_check_status = [
        self::EXTINGUISHER_CHECKOUT_STATUS_NOT        => 'extinguisher_checkout_status_1',
        self::EXTINGUISHER_CHECKOUT_STATUS_DETERMINED => 'extinguisher_checkout_status_2'
    ];
    //灭火器检查结果
    public static $extinguisher_check_result = [
        1 => 'extinguisher_checkout_result_1',
        2 => 'extinguisher_checkout_result_2'
    ];

    //检查单问题
    public static $extinguisher_check_list = [
        'extinguisher_problem_1',
        'extinguisher_problem_2',
        'extinguisher_problem_3',
        'extinguisher_problem_4',
        'extinguisher_problem_5',
        'extinguisher_problem_6',
        'extinguisher_problem_7',
        'extinguisher_problem_8'
    ];
}
