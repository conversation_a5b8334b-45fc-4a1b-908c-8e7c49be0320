<?php

namespace App\Library\Enums;

/**
 * 预算科目统常用枚举数据
 * Class enums
 */
final class BudgetObjectEnums
{

    /********************* 取数需求工单审批: 错误码区间 11000 ~ 11999 end **********************/
    /**
     * @Message ('科目添加失败')
     */
    public static $BUDGET_OBJECT_ADD_EXISTED = 11000;
    /**
     * @Message ('科目添加失败')
     */
    public static $BUDGET_OBJECT_ADD_ERROR = 11001;
    /**
     * @Message ('科目添加失败，不可重复复核')
     */
    public static $BUDGET_OBJECT_EDIT_ERROR = 11002;
    /**
     * @Message ('科目添加失败，不可缺少')
     */
    public static $BUDGET_OBJECT_ORDER_EMPTY_ERROR = 11003;
    /**
     * @Message ('科目添加失败，不可重复复核')
     */
    public static $BUDGET_OBJECT_ORDER_ADD_ERROR = 11004;

    /********************* 取数需求工单审批: 错误码区间 11000 ~ 11999 end **********************/

    // budget_object_order 预算科目关联订单类型
    const BUDGET_OBJECT_ORDER_TYPE_1 = 1; //1 报销
    const BUDGET_OBJECT_ORDER_TYPE_2 = 2; //2 采购
    const BUDGET_OBJECT_ORDER_TYPE_3 = 3; //3 普通付款
    public static $budget_object_order_type_source = [
        self::BUDGET_OBJECT_ORDER_TYPE_1,
        self::BUDGET_OBJECT_ORDER_TYPE_2,
        self::BUDGET_OBJECT_ORDER_TYPE_3,
    ];
    public static $budget_object_order_type_text   = [
        self::BUDGET_OBJECT_ORDER_TYPE_1 => 'object_type_001',
        self::BUDGET_OBJECT_ORDER_TYPE_2 => 'object_type_002',
        self::BUDGET_OBJECT_ORDER_TYPE_3 => 'object_type_003',
    ];

    //科目状态 1 有效 2 失效
    const BUDGET_OBJECT_PRODUCT_VALID = 1;
    const BUDGET_OBJECT_PRODUCT_INVALID = 2;
    public static $budget_object_product_text = [
        self::BUDGET_OBJECT_PRODUCT_VALID   => 'budget_object_product_valid',
        self::BUDGET_OBJECT_PRODUCT_INVALID => 'budget_object_product_invalid',
    ];
    const BUDGET_OBJECT_PRODUCT_IS_ALL_EDIT = 1; //是批量删除还是单个修改 1 单个修改

    const IS_PURCHASE_NO = 1; //是否采购使用，否
    const IS_PURCHASE_YES = 2;//是否采购使用，是
    const IS_PURCHASE_RULE = self::IS_PURCHASE_NO . ',' . self::IS_PURCHASE_YES;

    //是否采购使用：1否，2是
    public static $is_purchase = [
        self::IS_PURCHASE_NO  => 'budget_is_purchase_1',
        self::IS_PURCHASE_YES => 'budget_is_purchase_2',
    ];
}
