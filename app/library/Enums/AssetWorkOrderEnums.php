<?php
namespace App\Library\Enums;

/**
 * 资产工单-枚举文件
 * Class AssetWorkOrderEnums
 * @package App\Library\Enums
 */
final class AssetWorkOrderEnums
{
    const ASSET_WORK_ORDER_EXPORT_LIMIT = 10000;

    const ORDER_STATUS_WAIT_REPLY = 1; //待回复
    const ORDER_STATUS_REPLY = 2;  //已回复
    const ORDER_STATUS_CLOSED =3;  //已关闭

    /**
     * 工单状态
     * @var array
     */
    public static $order_status = [
        self::ORDER_STATUS_WAIT_REPLY => 'administration_ticket_status.' . self::ORDER_STATUS_WAIT_REPLY,
        self::ORDER_STATUS_REPLY => 'administration_ticket_status.' . self::ORDER_STATUS_REPLY,
        self::ORDER_STATUS_CLOSED => 'administration_ticket_status.' . self::ORDER_STATUS_CLOSED,
    ];
}
