<?php

namespace App\Library\Enums;
use App\Library\Enums;

final class ByWorkflowEnums
{
    //待审批
    const BY_OPERATE_WAIT_AUDIT = 1;
    //同意
    const BY_OPERATE_PASS = 2;
    //驳回
    const BY_OPERATE_REJECT = 3;
    //撤销
    const BY_OPERATE_CANCEL = 4;
    //申请单列表
    const BY_BIZ_TYPE_ASSET = 46;

    const BY_BIZ_TYPE_JT = 18;
    const BY_BIZ_TYPE_JT_OA = 20; //该类型已经没有入口，仅显示历史数据
    //耗材申请单biz_type
    const BY_BIZ_TYPE_WMS = 50;

    // 仓库管理
    // 变更状态审批
    const BY_BIZ_TYPE_WAREHOUSE_CHANGE_STATUS = 66;

    // 变更网点审批
    const BY_BIZ_TYPE_WAREHOUSE_CHANGE_STORE = 67;
    // 报价审核
    const BY_BIZ_TYPE_WAREHOUSE_THREAD_PRICE = 83;

    //代理支付
    const BY_BIZ_TYPE_AGENCY_PAYMENT = 71;

    // 预算管理-费用预提
    const BY_BIZ_TYPE_BUDGET_WITHHOLDING = 89;

    // BY 审批业务翻译 translate
    const BY_BIZ_TYPE_TRANSLATE_PREFIX = 'workflow_type_';
    public static $by_workflow_biz_type_item = [
        self::BY_BIZ_TYPE_WAREHOUSE_CHANGE_STATUS => self::BY_BIZ_TYPE_TRANSLATE_PREFIX . self::BY_BIZ_TYPE_WAREHOUSE_CHANGE_STATUS,
        self::BY_BIZ_TYPE_WAREHOUSE_CHANGE_STORE => self::BY_BIZ_TYPE_TRANSLATE_PREFIX . self::BY_BIZ_TYPE_WAREHOUSE_CHANGE_STORE,
    ];

    // BY审批业务类型 与 OA 业务类型的映射关系
    public static $by_oa_biz_type_map = [
        self::BY_BIZ_TYPE_WAREHOUSE_CHANGE_STATUS => Enums::WF_WAREHOUSE_STATUS_CHANGE_BIZ_TYPE,
        self::BY_BIZ_TYPE_WAREHOUSE_CHANGE_STORE => Enums::WF_WAREHOUSE_STORE_CHANGE_BIZ_TYPE,
    ];

    const WF_APPROVAL_OVERTIME = 4;//by 审批类型 加班
    const WF_APPROVAL_OS_OVERTIME = 77;//by 审批类型 加班(外协)

    const BY_BIZ_TYPE_JT_SPECIAL = 70; //特殊转岗申请(BY审批)


}
