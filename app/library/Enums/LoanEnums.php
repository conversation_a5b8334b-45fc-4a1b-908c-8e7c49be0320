<?php
namespace App\Library\Enums;

/**
 * 借款管理版块相关枚举定义
 * Class enums
 */
final class LoanEnums
{
    //借款单状态:1无需归还,2未开始归还,3部分归还,4已还清,5超额归还
    const LOAN_STATUS_NO_NEED_RETURN = 1;//无需归还
    const LOAN_STATUS_NOT_START_RETURN = 2;//未开始归还
    const LOAN_STATUS_PARTIAL_RETURN = 3;//部分归还
    const LOAN_STATUS_PAID_OFF = 4;//已还清
    const LOAN_STATUS_OVER_RETURN = 5;//超额归还

    //归还单处理进度
    const LOAN_RETURN_BACK_PROGRESS_ING = 1;//处理中
    const LOAN_RETURN_BACK_PROGRESS_DONE = 2;//已完成

    //借款归还状态
    const LOAN_BACK_STATUS_NOT = 1;//未归还
    const LOAN_BACK_STATUS_ING = 2;//归还中
    const LOAN_BACK_STATUS_BACK = 3;//已归还

    //新旧数据标识
    const IS_NEW_NO = 0;//旧数据单笔归还
    const IS_NEW_YES = 1;//新数据走多笔归还

    //归还方式
    const LOAN_BACK_TYPE_TRANSFER = 1;//转账归还
    const LOAN_BACK_TYPE_SALARY = 2;//薪资抵扣

    //借款归还申请单编号前缀
    const LOAN_RETURN_BACK_APPLY_NO_PREFIX = 'RP';

    //借款单状态
    public static $loan_status = [
        self::LOAN_STATUS_NO_NEED_RETURN => 'loan_return_status.' . self::LOAN_STATUS_NO_NEED_RETURN,
        self::LOAN_STATUS_NOT_START_RETURN => 'loan_return_status.' . self::LOAN_STATUS_NOT_START_RETURN,
        self::LOAN_STATUS_PARTIAL_RETURN => 'loan_return_status.' . self::LOAN_STATUS_PARTIAL_RETURN,
        self::LOAN_STATUS_PAID_OFF => 'loan_return_status.' . self::LOAN_STATUS_PAID_OFF,
        self::LOAN_STATUS_OVER_RETURN => 'loan_return_status.' . self::LOAN_STATUS_OVER_RETURN
    ];

    //归还单处理进度
    public static $loan_return_back_progress = [
        self::LOAN_RETURN_BACK_PROGRESS_ING => 'loan_return_back_progress.' . self::LOAN_RETURN_BACK_PROGRESS_ING,
        self::LOAN_RETURN_BACK_PROGRESS_DONE => 'loan_return_back_progress.' . self::LOAN_RETURN_BACK_PROGRESS_DONE
    ];

    //归还方式
    public static $loan_return_back_type = [
        self::LOAN_BACK_TYPE_TRANSFER => 'loan_return_back_type.' . self::LOAN_BACK_TYPE_TRANSFER,
        self::LOAN_BACK_TYPE_SALARY => 'loan_return_back_type.' . self::LOAN_BACK_TYPE_SALARY
    ];
    //借款归还提醒
    const MESSAGE_CATEGORY_LOAN_RETURN_NOTICE = 108;
}
