<?php

namespace App\Library\Enums;

use App\Library\Enums;

final class ChequeEnums
{
    const CHECKS_DOWNLOAD_LIMIT = 30000;

    const LIST_TYPE_APPLY = 1;           //支票申请
    const LIST_TYPE_AUDIT = 2;           //支票审核
    const LIST_TYPE_DATA = 4;            //支票查询
    const LIST_TYPE_DATA_EXPORT = 6;     //导出
    const LIST_TYPE_CONSULTED_REPLY = 5; //征询回复
    const EXPORT_TYPE_ZERO = 0;
    const TOTAL_COUNT = 0;//数据为空的时候默认条数

    //票单编号前缀
    const CHECKS_CHECK_NUMBER_PREFIX = 'ZPGM';
    const CHECKS_CHECK_NUMBER_APPLY_NO_KEY = 'get_cheque_number_apply_no';
    const START_NUMBER_COUNT = 10; //支票长度
    const CHEQUE_ACCOUNT_ID = 0;
    const CODE_NUMBER_START = 1;             //处理支票的起始位
    const CHEQUE_FLOW_LEVEL = 1;             //押金业务在支付的时候审批为一级审核人的时候处理支票数据
    const CHEQUE_REPEATED_SUBMISSION = 5;
    const CHEQUE_LIMIT = 1;

    //todo 以后这个统一在一个地方
    //申请状态 1 待审核 2 已驳回 3 已通过 4已撤回
    const CHECKS_STATUS_ENUMS_ONE = 1;
    const CHECKS_STATUS_ENUMS_TWO = 2;
    const CHECKS_STATUS_ENUMS_THREE = 3;
    const CHECKS_STATUS_ENUMS_FOUR = 4;

    public static $checks_status     = [
        self::CHECKS_STATUS_ENUMS_ONE   => 'checks_status.' . self::CHECKS_STATUS_ENUMS_ONE,
        self::CHECKS_STATUS_ENUMS_TWO   => 'checks_status.' . self::CHECKS_STATUS_ENUMS_TWO,
        self::CHECKS_STATUS_ENUMS_THREE => 'checks_status.' . self::CHECKS_STATUS_ENUMS_THREE,
        self::CHECKS_STATUS_ENUMS_FOUR  => 'checks_status.' . self::CHECKS_STATUS_ENUMS_FOUR,
    ];
    public static $checks_status_arr = [
        ['value' => self::CHECKS_STATUS_ENUMS_ONE, 'label' => 'checks_status.' . self::CHECKS_STATUS_ENUMS_ONE],
        ['value' => self::CHECKS_STATUS_ENUMS_TWO, 'label' => 'checks_status.' . self::CHECKS_STATUS_ENUMS_TWO],
        ['value' => self::CHECKS_STATUS_ENUMS_THREE, 'label' => 'checks_status.' . self::CHECKS_STATUS_ENUMS_THREE],
        ['value' => self::CHECKS_STATUS_ENUMS_FOUR, 'label' => 'checks_status.' . self::CHECKS_STATUS_ENUMS_FOUR],
    ];

    //占用状态 1 未使用 2 已使用
    const CHECKS_APPLY_INFORMATION_USE_STATUS_NO = 1;
    const CHECKS_APPLY_INFORMATION_USE_STATUS_YES = 2;

    public static $checks_use_status     = [
        self::CHECKS_APPLY_INFORMATION_USE_STATUS_NO  => 'checks_use_status.' . self::CHECKS_APPLY_INFORMATION_USE_STATUS_NO,
        self::CHECKS_APPLY_INFORMATION_USE_STATUS_YES => 'checks_use_status.' . self::CHECKS_APPLY_INFORMATION_USE_STATUS_YES,
    ];
    public static $checks_use_status_arr = [
        [
            'value' => self::CHECKS_APPLY_INFORMATION_USE_STATUS_NO,
            'label' => 'checks_use_status.' . self::CHECKS_APPLY_INFORMATION_USE_STATUS_NO,
        ],
        [
            'value' => self::CHECKS_APPLY_INFORMATION_USE_STATUS_YES,
            'label' => 'checks_use_status.' . self::CHECKS_APPLY_INFORMATION_USE_STATUS_YES,
        ],
    ];

    //todo 以后这个统一在一个地方 不建议使用ONE、TWO
    //使用状态 1待使用 2已使用 3已作废 4已替换
    const CHECKS_NUMBER_USE_STATUS_ONE = 1;
    const CHECKS_NUMBER_USE_STATUS_TWO = 2;
    const CHECKS_NUMBER_USE_STATUS_THREE = 3;
    const CHECKS_NUMBER_USE_STATUS_FOUR = 4;


    public static $checks_number_use_status = [
        self::CHECKS_NUMBER_USE_STATUS_ONE   => 'checks_number_status.' . self::CHECKS_NUMBER_USE_STATUS_ONE,
        self::CHECKS_NUMBER_USE_STATUS_TWO   => 'checks_number_status.' . self::CHECKS_NUMBER_USE_STATUS_TWO,
        self::CHECKS_NUMBER_USE_STATUS_THREE => 'checks_number_status.' . self::CHECKS_NUMBER_USE_STATUS_THREE,
        self::CHECKS_NUMBER_USE_STATUS_FOUR  => 'checks_number_status.' . self::CHECKS_NUMBER_USE_STATUS_FOUR,
    ];

    public static $checks_number_use_status_arr = [
        [
            'value' => self::CHECKS_NUMBER_USE_STATUS_ONE,
            'label' => 'checks_number_status.' . self::CHECKS_NUMBER_USE_STATUS_ONE,
        ],
        [
            'value' => self::CHECKS_NUMBER_USE_STATUS_TWO,
            'label' => 'checks_number_status.' . self::CHECKS_NUMBER_USE_STATUS_TWO,
        ],
        [
            'value' => self::CHECKS_NUMBER_USE_STATUS_THREE,
            'label' => 'checks_number_status.' . self::CHECKS_NUMBER_USE_STATUS_THREE,
        ],
        [
            'value' => self::CHECKS_NUMBER_USE_STATUS_FOUR,
            'label' => 'checks_number_status.' . self::CHECKS_NUMBER_USE_STATUS_FOUR,
        ],
    ];


    //承兑状态 1待承兑 2已承兑 3未承兑
    const CHECKS_EXCHANGE_STATUS_ING = 1;
    const CHECKS_EXCHANGE_STATUS_END = 2;
    const CHECKS_EXCHANGE_STATUS_NO = 3;

    public static $checks_exchange_status     = [
        self::CHECKS_EXCHANGE_STATUS_ING => 'checks_exchange_status.' . self::CHECKS_EXCHANGE_STATUS_ING,
        self::CHECKS_EXCHANGE_STATUS_END => 'checks_exchange_status.' . self::CHECKS_EXCHANGE_STATUS_END,
        self::CHECKS_EXCHANGE_STATUS_NO  => 'checks_exchange_status.' . self::CHECKS_EXCHANGE_STATUS_NO,
    ];
    public static $checks_exchange_status_arr = [
        [
            'value' => self::CHECKS_EXCHANGE_STATUS_ING,
            'label' => 'checks_exchange_status.' . self::CHECKS_EXCHANGE_STATUS_ING,
        ],
        [
            'value' => self::CHECKS_EXCHANGE_STATUS_END,
            'label' => 'checks_exchange_status.' . self::CHECKS_EXCHANGE_STATUS_END,
        ],
        [
            'value' => self::CHECKS_EXCHANGE_STATUS_NO,
            'label' => 'checks_exchange_status.' . self::CHECKS_EXCHANGE_STATUS_NO,
        ],
    ];


    //1 作废 2 替换
    const CHECKS_NUMBER_TYPE_VOID = 1;
    const CHECKS_NUMBER_TYPE_REPLACE = 2;

    public static $checks_number_type = [
        self::CHECKS_NUMBER_TYPE_VOID    => 'checks_number_type.' . self::CHECKS_NUMBER_TYPE_VOID,
        self::CHECKS_NUMBER_TYPE_REPLACE => 'checks_number_type.' . self::CHECKS_NUMBER_TYPE_REPLACE,
    ];
    public static $OSS_REPLACE_TYPE   = [
        self::CHECKS_NUMBER_TYPE_VOID    => Enums::OSS_ABOLISH_INFO_TYPE_CHEQUE_APPLY_ADD,
        self::CHECKS_NUMBER_TYPE_REPLACE => Enums::OSS_REPLACE_INFO_TYPE_CHEQUE_APPLY_ADD,
    ];

    public static $checks_number_type_arr = [
        ['value' => self::CHECKS_NUMBER_TYPE_VOID, 'label' => 'checks_number_type.' . self::CHECKS_NUMBER_TYPE_VOID],
        [
            'value' => self::CHECKS_NUMBER_TYPE_REPLACE,
            'label' => 'checks_number_type.' . self::CHECKS_NUMBER_TYPE_REPLACE,
        ],
    ];


    //支票申请详情状态
    const CHEQUE_APPLY_DETAIL_PENDING = 1;   //待支付
    const CHEQUE_APPLY_DETAIL_DONE = 2;      // 已支付
    public static $cheque_apply_detail_status = [
        self::CHEQUE_APPLY_DETAIL_PENDING => 'cheque_apply_detail_pending',
        self::CHEQUE_APPLY_DETAIL_DONE    => 'cheque_apply_detail_done',
    ];

    //支票释放
    const CHEQUE_ACCOUNT_RELEASE_PENDING = 1;//未释放
    const CHEQUE_ACCOUNT_RELEASE_DONE = 2;   // 已释放
    public static $cheque_account_release_arr = [
        self::CHEQUE_ACCOUNT_RELEASE_PENDING => 'cheque_account_release_pending',
        self::CHEQUE_ACCOUNT_RELEASE_DONE    => 'cheque_account_release_done',
    ];

}
