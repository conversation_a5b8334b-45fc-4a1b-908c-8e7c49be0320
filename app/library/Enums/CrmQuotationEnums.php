<?php

namespace App\Library\Enums;

use App\Library\Enums;

/**
 * Class CrmQuotationEnums
 * @package App\Library\Enums
 */
final class CrmQuotationEnums
{
    const PRODUCT_TYPE_FIXED_VALUE = 1;   //固定折扣
    const PRODUCT_TYPE_SPECIAL_VALUE = 2; //特殊折扣
    public static $product_type_items = [
        self::PRODUCT_TYPE_FIXED_VALUE   => 'product_type_fixed_value',
        self::PRODUCT_TYPE_SPECIAL_VALUE => 'product_type_special_value',
    ];
    const SETTLEMENT_PERIOD_WEEK = 1;                        //周结
    const SETTLEMENT_PERIOD_HALF_MONTH = 3;                  //半月结
    const SETTLEMENT_PERIOD_MONTH = 4;                       //月结
    public static $settlement_period_items = [
        self::SETTLEMENT_PERIOD_WEEK       => 'settlement_period_week',
        self::SETTLEMENT_PERIOD_HALF_MONTH => 'settlement_period_half_month',
        self::SETTLEMENT_PERIOD_MONTH      => 'settlement_period_month',
    ];
    //同步MS的channel
    const SYNC_CHANNEL_NETWORK = 0;
    const SYNC_CHANNEL_SALES_PMD = 1;
    const SYNC_CHANNEL_SHOP = 2;
    const SYNC_CHANNEL_NETWORK_BULKY = 4;
    const SYNC_CHANNEL_BULKY_BUSINESS = 6;
    const SYNC_CHANNEL_FLASH_HOME = 8;
    //同步MS的类型
    const SYNC_FLASH_HOME_CATEGORY_FREIGHT = 30;             //运费折扣(标准产品-固定折扣)
    const SYNC_FLASH_HOME_CATEGORY_BULKY = 31;               //大件产品
    const SYNC_FLASH_HOME_CATEGORY_FRUIT = 32;               //水果件产品-固定折扣
    const SYNC_FLASH_HOME_CATEGORY_FRUIT_SPECIAL = 34;       //水果件产品-特殊折扣
    const SYNC_FLASH_HOME_CATEGORY_FRUIT_PACKAGE = 35;       //水果件产品-带包材折扣
    const SYNC_FLASH_HOME_FH_SPECIAL_PRICE_LIST = 36;        //flash home 特殊价格表
    const SYNC_FLASH_HOME_FH_EXCLUDE_UPCOUNTRY_ENABLED = 37; //flash home 是否免偏远地区费
    const SYNC_FLASH_HOME_FH_COD_POUNDAGE = 38;              //flash home COD手续费
    const SYNC_FLASH_HOME_FH_WEIGHING_CATEGORY = 39;         //flash home 计费方式
    const SYNC_FLASH_HOME_FH_WEIGHT_OR_LWH_RESTRICT = 40;    //flash home重量或尺寸计费特殊配置
    const SYNC_FLASH_HOME_FH_VOLUME_WEIGHT_RESTRICT = 41;    //flash home体积重计费特殊配置
    const SYNC_FLASH_HOME_FH_RETURN_DISCOUNT_RATE = 43;    //flash home退件折扣率

    //同步MS的状态
    const SYNC_STATUS_YES = 1;                               //已同步
    const SYNC_STATUS_NO = 2;                                //未同步

    //是否可配置客户id
    const CAN_CONFIGURE_CONSUMER_YES = 1;                    //是
    const CAN_CONFIGURE_CONSUMER_NO = 2;                     //否

    //折扣类型
    const QUOTED_PRICE_TYPE_1 = 1;                           //固定折扣
    const QUOTED_PRICE_TYPE_2 = 2;                           //阶梯折扣
    const QUOTED_PRICE_TYPE_3 = 3;                           //特殊折扣
    const QUOTED_PRICE_TYPE_4 = 4;                           //固定折扣80%off活动

    //计费方式 CRM的
    const CALCULATION_METHOD_WEIGHT = 1;                     //仅按重量
    const CALCULATION_METHOD_SIZE = 2;                       //仅按尺寸
    const CALCULATION_METHOD_VOLUME_WEIGHT = 3;              //按体积/重量
    const CALCULATION_METHOD_WEIGHT_SIZE = 4;                //按重量/尺寸

    //计费方式=按重量/尺寸  特殊配置
    const CALCULATION_SPECIAL_CONFIG_WEIGHT = 1;             //重量
    const CALCULATION_SPECIAL_CONFIG_WEIGHT_SIZE = 2;        //重量和尺寸

    //计费方式 MS的
    const MS_CALCULATION_METHOD_WEIGHT_SIZE = 0;             //按重量/尺寸
    const MS_CALCULATION_METHOD_WEIGHT = 1;                  //仅按重量
    const MS_CALCULATION_METHOD_SIZE = 2;                    //仅按尺寸
    const MS_CALCULATION_METHOD_VOLUME_WEIGHT = 3;           //按体积/重量
    const MS_CALCULATION_METHOD_ONLY_VOLUME_WEIGHT = 4;      //仅按体积重 crm没有这个

    //计费方式 CRM和MS枚举的对应关系
    public static $calculation_method_relation = [
        self::CALCULATION_METHOD_WEIGHT        => self::MS_CALCULATION_METHOD_WEIGHT,
        self::CALCULATION_METHOD_SIZE          => self::MS_CALCULATION_METHOD_SIZE,
        self::CALCULATION_METHOD_VOLUME_WEIGHT => self::MS_CALCULATION_METHOD_VOLUME_WEIGHT,
        self::CALCULATION_METHOD_WEIGHT_SIZE   => self::MS_CALCULATION_METHOD_WEIGHT_SIZE,
    ];

    //MS定义的category 计费方式
    const PRICE_RULE_CATEGORY_7 = 7;                         //计费方式 有计费方式就传这个
    const PRICE_RULE_CATEGORY_9 = 9;                         //计费方式  按体积/重量  重量
    const PRICE_RULE_CATEGORY_8 = 8;                         //计费方式  按重量/尺寸  重量
    const PRICE_RULE_CATEGORY_17 = 17;                       //计费方式  按重量/尺寸  尺寸

    //列表中显示的部门类型对应的部门名称 迁移自老代码app/modules/CrmQuotation/services/BaseService.php的静态属性
    //固定名称改成翻译,FFM和OTHER代码中没有,可能存在历史数据或其他情况,先留着
    //新增了department_type_network_bulky,department_type_bulky_business,department_type_flash_home
    public static $department_type_view_name = [
        Enums::DEPARTMENT_TYPE_SALES                      => 'crm_department_type_sales',
        Enums::DEPARTMENT_TYPE_PMD                        => 'crm_department_type_pmd',
        Enums::DEPARTMENT_TYPE_SHOP                       => 'crm_department_type_shop',
        Enums::DEPARTMENT_TYPE_NETWORK                    => 'crm_department_type_network',
        5                                                 => 'crm_department_type_ffm',
        6                                                 => 'crm_department_type_other',
        Enums::DEPARTMENT_TYPE_NETWORK_BULKY              => 'crm_department_type_network_bulky',
        Enums::DEPARTMENT_TYPE_BULKY_BUSINESS_DEVELOPMENT => 'crm_department_type_bulky_business',
        Enums::DEPARTMENT_TYPE_FLASH_HOME                 => 'crm_department_type_flash_home',
        Enums::DEPARTMENT_TYPE_JVB                        => 'crm_department_type_jvb_operations',
    ];
    //是否需要同步到crm
    const NEED_SYNC_CRM_YES = 1;
    const NEED_SYNC_CRM_NO = 0;

    //是否同步过MS
    const MS_SYNC_STATUS_YES = 1;                            //已同步
    const MS_SYNC_STATUS_NO = 2;                             //未同步
}
