<?php

namespace App\Library\Enums;

use App\Library\Enums;

final class MaterialWmsEnums
{
    //耗材申请单导出时间锁
    const MATERIAL_WMS_APPLY_EXPORT_LOCK = 'material_wms_apply_export_lock';
    //耗材申请单数据查询导出时间锁
    const MATERIAL_WMS_APPLY_DATA_EXPORT_LOCK = 'material_wms_apply_data_export_lock';
    //耗材出库导出时间锁
    const MATERIAL_WMS_OUT_STORAGE_EXPORT_LOCK = 'material_wms_out_storage_export_lock';
    //耗材出库审核添加时间锁
    const MATERIAL_WMS_OUT_AUDIT_ADD_LOCK = 'material_wms_out_audit_add_lock';
    //耗材管理耗材审核批量审核上传保存时间锁
    const material_wms_apply_all_audit_export_lock  = 'material_wms_apply_all_audit_export';

    //耗材申请单编号前缀
    const MATERIAL_WMS_APPLY_NO_PREFIX = 'MA';
    //申请单列表
    const WMS_LIST_TYPE_APPLY = 1;
    //审批列表
    const WMS_LIST_TYPE_APPLY_AUDIT = 2;
    //数据查询列表
    const WMS_LIST_TYPE_APPLY_DATA = 3;
    //数据来源，1:by申请
    const SOURCE_TYPE_BY = 1;
    const SOURCE_TYPE_OA = 2;

    //是否提交到scm 1 否  2是
    const IS_SUBMIT_SCM_DENY = 1;
    const IS_SUBMIT_SCM_CORRECT = 2;

    //是否执行过拆单 0 没拆单 1 已拆单
    const IS_SPLIT_DENY = 0;
    const IS_SPLIT_CORRECT = 1;

    //是否代替申请：0初始化，1否，2是
    const IS_INSTEAD_APPLY_NO = 1;
    const IS_INSTEAD_APPLY_YES = 2;

    const MATERIAL_WMS_UPDATE_LOG_TYPE_ADD = 1;//创建
    const MATERIAL_WMS_UPDATE_LOG_TYPE_SAVE = 2;//编辑
    CONST MATERIAL_WMS_UPDATE_LOG_TYPE_APPOINT = 3;//指定仓库
    CONST MATERIAL_WMS_UPDATE_LOG_TYPE_SPLIT = 4;//拆分
    CONST MATERIAL_WMS_UPDATE_LOG_TYPE_EXAMINE = 5;//提交审核
    CONST MATERIAL_WMS_UPDATE_LOG_TYPE_OUTBOUND = 6;//出库

    public static $wms_update_log_type = [
        self::MATERIAL_WMS_UPDATE_LOG_TYPE_ADD      => 'material_wms_update_log_type.' . self::MATERIAL_WMS_UPDATE_LOG_TYPE_ADD,
        self::MATERIAL_WMS_UPDATE_LOG_TYPE_SAVE     => 'material_wms_update_log_type.' . self::MATERIAL_WMS_UPDATE_LOG_TYPE_SAVE,
        self::MATERIAL_WMS_UPDATE_LOG_TYPE_APPOINT  => 'material_wms_update_log_type.' . self::MATERIAL_WMS_UPDATE_LOG_TYPE_APPOINT,
        self::MATERIAL_WMS_UPDATE_LOG_TYPE_SPLIT    => 'material_wms_update_log_type.' . self::MATERIAL_WMS_UPDATE_LOG_TYPE_SPLIT,
        self::MATERIAL_WMS_UPDATE_LOG_TYPE_EXAMINE  => 'material_wms_update_log_type.' . self::MATERIAL_WMS_UPDATE_LOG_TYPE_EXAMINE,
        self::MATERIAL_WMS_UPDATE_LOG_TYPE_OUTBOUND => 'material_wms_update_log_type.' . self::MATERIAL_WMS_UPDATE_LOG_TYPE_OUTBOUND,
    ];

    const MATERIAL_WMS_OUT_STORAGE_NO_PREFIX = 'MOR'; //耗材出库单编号前缀
    const STATUS_WAIT_APPROVE = 1;//待审核
    const STATUS_APPROVED_WAIT_OUT = 2;//待出库
    const STATUS_OUT = 3;//已出库
    const STATUS_CANCEL = 4;//已作废
    const STATUS_DRAFT = 5;//草稿
    //出库状态
    public static $wms_out_storage_status = [
        self::STATUS_WAIT_APPROVE      => 'material_wms_out_storage_status.' . self::STATUS_WAIT_APPROVE,
        self::STATUS_APPROVED_WAIT_OUT => 'material_wms_out_storage_status.' . self::STATUS_APPROVED_WAIT_OUT,
        self::STATUS_OUT               => 'material_wms_out_storage_status.' . self::STATUS_OUT,
        self::STATUS_CANCEL            => 'material_wms_out_storage_status.' . self::STATUS_CANCEL,
        self::STATUS_DRAFT             => 'material_wms_out_storage_status.' . self::STATUS_DRAFT,
    ];

    //耗材状态
    public static $wms_apply_status = [
        Enums::WF_STATE_PENDING  => 'wms_apply_status.1',
        Enums::WF_STATE_REJECTED => 'wms_apply_status.2',
        Enums::WF_STATE_APPROVED => 'wms_apply_status.3',
        Enums::WF_STATE_CANCEL   => 'wms_apply_status.4',
    ];

    //审批审核类型
    const AUDIT_TYPE_WAIT = 0;        //待处理
    const AUDIT_TYPE_PROCESSED = 1;   //已处理
    const MATERIAL_WMS_IS_BATCH_LOCK = 1;   //批量上传审核锁
    const MATERIAL_WMS_IS_BATCH_UN_LOCK = 0;   //批量上传审核解锁

    const MATERIAL_WMS_AUDIT_MAX_NUMBER = 5000;   //批量上传审核最大行限制
    const MATERIAL_WMS_AUDIT_MAX_EXPORT = 60000;   //待处理最大导出数量6万
    const MATERIAL_WMS_AUDIT_APPROVAL_RESULT_AGREE  = 'Agree'; //审核结果同意
    const MATERIAL_WMS_AUDIT_APPROVAL_RESULT_REJECT  =  'Reject';//审核结果驳回
    const MATERIAL_WMS_AUDIT_NUM_RULE = '/^([1-9]\d{0,4}|[0])$/';

    const MATERIAL_WMS_SPLIT_TYPE_AUTOMATIC = 1;//自动拆单
    const MATERIAL_WMS_SPLIT_TYPE_HAND = 2;//手动拆单

    const MATERIAL_STORE_STORAGE_RULE_LEVEL_ONE = 1; //表material_store_storage_rule查询level最小的一条数据
    const MATERIAL_STORE_STORAGE_RULE_LEVEL_ALL = 0; //表material_store_storage_rule全部级别数据

    // 耗材申请-批量上传 最多限制条数
    const MATERIAL_WMS_APPLY_MAX_LIMIT = 5000;

    //运单状态：1未妥投、2已妥投
    const MATERIAL_WMS_BOX_STATUS_NOT_DELIVERED = 1;
    const MATERIAL_WMS_BOX_STATUS_DELIVERED = 2;

    //调拨单-单号前缀
    const MATERIAL_PACKAGE_NO_PREFIX = 'MT';

    //调拨状态：1待调出、2待调入、3已完成、4已取消
    const MATERIAL_PACKAGE_ALLOT_STATUS_OUT = 1;
    const MATERIAL_PACKAGE_ALLOT_STATUS_IN = 2;
    const MATERIAL_PACKAGE_ALLOT_STATUS_DONE = 3;
    const MATERIAL_PACKAGE_ALLOT_STATUS_CANCEL = 4;
    public static $package_allot_status = [
        self::MATERIAL_PACKAGE_ALLOT_STATUS_OUT    => 'material_package_allot_status.' . self::MATERIAL_PACKAGE_ALLOT_STATUS_OUT,
        self::MATERIAL_PACKAGE_ALLOT_STATUS_IN     => 'material_package_allot_status.' . self::MATERIAL_PACKAGE_ALLOT_STATUS_IN,
        self::MATERIAL_PACKAGE_ALLOT_STATUS_DONE   => 'material_package_allot_status.' . self::MATERIAL_PACKAGE_ALLOT_STATUS_DONE,
        self::MATERIAL_PACKAGE_ALLOT_STATUS_CANCEL => 'material_package_allot_status.' . self::MATERIAL_PACKAGE_ALLOT_STATUS_CANCEL,
    ];

    //调拨理由：1自用、2送客户
    const MATERIAL_PACKAGE_ALLOT_REASON_TYPE_SELF = 1;
    const MATERIAL_PACKAGE_ALLOT_REASON_TYPE_CUSTOMER = 2;
    const VALIDATE_MATERIAL_PACKAGE_ALLOT_REASON_TYPE = self::MATERIAL_PACKAGE_ALLOT_REASON_TYPE_SELF . ',' . self::MATERIAL_PACKAGE_ALLOT_REASON_TYPE_CUSTOMER;
    public static $package_allot_reason_type = [
        self::MATERIAL_PACKAGE_ALLOT_REASON_TYPE_SELF     => 'material_package_allot_reason_type.' . self::MATERIAL_PACKAGE_ALLOT_REASON_TYPE_SELF,
        self::MATERIAL_PACKAGE_ALLOT_REASON_TYPE_CUSTOMER => 'material_package_allot_reason_type.' . self::MATERIAL_PACKAGE_ALLOT_REASON_TYPE_CUSTOMER,
    ];

    //调拨配送方式: 1快递配送、2 自行配送
    const MATERIAL_PACKAGE_ALLOT_DELIVERY_WAY_EXPRESS = 1;
    const MATERIAL_PACKAGE_ALLOT_DELIVERY_WAY_SELF = 2;
    const VALIDATE_MATERIAL_PACKAGE_ALLOT_DELIVERY_WAY = self::MATERIAL_PACKAGE_ALLOT_DELIVERY_WAY_EXPRESS . ',' . self::MATERIAL_PACKAGE_ALLOT_DELIVERY_WAY_SELF;
    public static $package_allot_delivery_way = [
        self::MATERIAL_PACKAGE_ALLOT_DELIVERY_WAY_EXPRESS => 'material_package_allot_delivery_way.' . self::MATERIAL_PACKAGE_ALLOT_DELIVERY_WAY_EXPRESS,
        self::MATERIAL_PACKAGE_ALLOT_DELIVERY_WAY_SELF    => 'material_package_allot_delivery_way.' . self::MATERIAL_PACKAGE_ALLOT_DELIVERY_WAY_SELF,
    ];

    //超时未调出：是、否
    const MATERIAL_PACKAGE_ALLOT_TIMEOUT_YES = 1;
    const MATERIAL_PACKAGE_ALLOT_TIMEOUT_NO = 2;
    public static $package_allot_timeout_out = [
        self::MATERIAL_PACKAGE_ALLOT_TIMEOUT_YES => 'material_package_allot_timeout.' . self::MATERIAL_PACKAGE_ALLOT_TIMEOUT_YES,
        self::MATERIAL_PACKAGE_ALLOT_TIMEOUT_NO  => 'material_package_allot_timeout.' . self::MATERIAL_PACKAGE_ALLOT_TIMEOUT_NO,
    ];

    //取消操作来源：1 OA、2 BY
    const MATERIAL_PACKAGE_ALLOT_CANCEL_REASON_OA = 1;
    const MATERIAL_PACKAGE_ALLOT_CANCEL_REASON_BY = 2;

    //导出限制条数10000
    const MATERIAL_PACKAGE_ALLOT_DOWNLOAD_LIMIT = 10000;
}
