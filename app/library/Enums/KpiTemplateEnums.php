<?php
namespace App\Library\Enums;
final class KpiTemplateEnums
{
    //删除标志
    const IS_DELETED_YES = 1;//已删除
    //KPI指标最多支持添加50条指标
    const MAX_INDICATORS_COUNT = 50;
    //KPI指标重要度之和必须100%
    const IMPORTANCE_SUM = 100;
    //重要度请输入1-100间的数字保留2位小数
    const IMPORTANCE_RULE = '/^([1-9]|[1-9]\\d|100)$/';
    const STATUS_DRAFT = 1;//草稿
    const STATUS_ENABLE = 2; //启用
    public static $kpi_template_status = [
        self::STATUS_DRAFT => 'kpi_template_status.1',
        self::STATUS_ENABLE => 'kpi_template_status.2',
    ];
    //员工指标来源-引用模版
    const CHANNEL_TEMPLATE = 1;
}