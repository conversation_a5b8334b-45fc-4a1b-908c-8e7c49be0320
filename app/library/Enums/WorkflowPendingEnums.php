<?php
/**
 * 审批流待办枚举配置
 * Class enums
 */

namespace App\Library\Enums;

use App\Library\Enums;

final class WorkflowPendingEnums
{
    // 取多久之前的待办统计: 单位分钟
    const PENDING_DATA_TIMEOUT_MIN = 30;

    // 哪些业务无需发待办审批数
    public static $no_statistics_required_biz_item = [
//        Enums::WF_ACCESS_DATA_WORK_ORDER_TYPE,// 取数工单业务
        Enums::WF_PAY_TYPE,// 支付模块的审批待办不需统计
    ];

    // 待办取数策略: 白名单待审批数处理场景
    const GET_PENDING_DATA_STRATEGY_FILTER_WHITELIST = 1;                          // 待审批人不在白名单的(过滤掉白名单)
    const GET_PENDING_DATA_STRATEGY_ONLY_WHITELIST = 2;                            // 待审批人在白名单的
    const GET_PENDING_DATA_STRATEGY_ALL = 3;                                       // 所有待审批的


    // 工作所在国家ID/短信国家码ID 与 短信国家码的映射关系
    public static $country_id_and_sms_nation_map = [
        StaffInfoEnums::WORKING_COUNTRY_TH => 'TH',
        StaffInfoEnums::WORKING_COUNTRY_ZH => 'CN',
        StaffInfoEnums::WORKING_COUNTRY_MY => 'MY',
        StaffInfoEnums::WORKING_COUNTRY_PH => 'PH',
        StaffInfoEnums::WORKING_COUNTRY_VN => 'VN',
        StaffInfoEnums::WORKING_COUNTRY_LA => 'LA',
        StaffInfoEnums::WORKING_COUNTRY_ID => 'ID',
    ];

    // 系统所属国家 和 短信国家简称映射关系: 短信文案用
    public static $sys_country_code_sms_country_map = [
        GlobalEnums::TH_COUNTRY_CODE => 'TH',  // 泰国
        GlobalEnums::PH_COUNTRY_CODE => 'PH',  // 菲律宾
        GlobalEnums::MY_COUNTRY_CODE => 'MY',  // 马来
        GlobalEnums::LA_COUNTRY_CODE => 'LAO', // 老挝
        GlobalEnums::VN_COUNTRY_CODE => 'VN',  // 越南
        GlobalEnums::ID_COUNTRY_CODE => 'ID',  // 印尼
    ];

    // 短信发送状态
    const SMS_SEND_STATUS_WAITING = 0;                                             // 待发送(默认)
    const SMS_SEND_STATUS_SUCCESS = 1;                                             // 发送成功
    const SMS_SEND_STATUS_FAIL = 2;                                                // 发送失败
    const SMS_SEND_STATUS_IGNORE = 3;                                              // 无需发送

    // 各环境各国域名(泰国除外)
    // dev
    const DEV_TH_DOMAIN = 'https://dev01-th-oaapi.fex.pub';
    const DEV_PH_DOMAIN = 'https://dev01-ph-oaapi.fex.pub';
    const DEV_MY_DOMAIN = 'https://dev01-my-oaapi.fex.pub';
    const DEV_LA_DOMAIN = 'https://dev01-la-oaapi.fex.pub';
    const DEV_VN_DOMAIN = 'https://dev01-vn-oaapi.fex.pub';
    const DEV_ID_DOMAIN = 'https://dev01-id-oaapi.fex.pub';

    // tra
    const TRA_TH_DOMAIN = 'https://oa-api-tra.flashexpress.com';
    const TRA_PH_DOMAIN = 'https://oa-api-tra.flashexpress.ph';
    const TRA_MY_DOMAIN = 'https://oa-api-tra.flashexpress.my';
    const TRA_LA_DOMAIN = 'https://oa-api-tra.flashexpress.la';
    const TRA_VN_DOMAIN = 'https://oa-api-tra.flashexpress.vn';
    const TRA_ID_DOMAIN = 'https://oa-api-tra.flashexpress.id';

    // pro
    const PRO_TH_DOMAIN = 'https://oa-api.flashexpress.com';
    const PRO_PH_DOMAIN = 'https://oa-api.flashexpress.ph';
    const PRO_MY_DOMAIN = 'https://oa-api.flashexpress.my';
    const PRO_LA_DOMAIN = 'https://oa-api.flashexpress.la';
    const PRO_VN_DOMAIN = 'https://oa-api.flashexpress.vn';
    const PRO_ID_DOMAIN = 'https://oa-api.flashexpress.id';

    // 各环境需要同步短信白名单的api地址
    const HTTP_SYNC_WHITELIST_DATA_AUTH_CODE = 'da9df928c9a53329ff455247556032db'; // 泰国OA白名单数据同步其他国家调用认证code
    const SYNC_WHITELIST_API = '/common/workflow_pending/syncStaffWhitelist';
    public static $need_sync_sms_whitelist_api_list = [
        'dev'      => [
            GlobalEnums::PH_COUNTRY_CODE => self::DEV_PH_DOMAIN . self::SYNC_WHITELIST_API,
            GlobalEnums::MY_COUNTRY_CODE => self::DEV_MY_DOMAIN . self::SYNC_WHITELIST_API,
            GlobalEnums::LA_COUNTRY_CODE => self::DEV_LA_DOMAIN . self::SYNC_WHITELIST_API,
            GlobalEnums::VN_COUNTRY_CODE => self::DEV_VN_DOMAIN . self::SYNC_WHITELIST_API,
            GlobalEnums::ID_COUNTRY_CODE => self::DEV_ID_DOMAIN . self::SYNC_WHITELIST_API,
        ],
        'training' => [
            GlobalEnums::PH_COUNTRY_CODE => self::TRA_PH_DOMAIN . self::SYNC_WHITELIST_API,
            GlobalEnums::MY_COUNTRY_CODE => self::TRA_MY_DOMAIN . self::SYNC_WHITELIST_API,
            GlobalEnums::LA_COUNTRY_CODE => self::TRA_LA_DOMAIN . self::SYNC_WHITELIST_API,
            GlobalEnums::VN_COUNTRY_CODE => self::TRA_VN_DOMAIN . self::SYNC_WHITELIST_API,
            GlobalEnums::ID_COUNTRY_CODE => self::TRA_ID_DOMAIN . self::SYNC_WHITELIST_API,
        ],
        'pro'      => [
            GlobalEnums::PH_COUNTRY_CODE => self::PRO_PH_DOMAIN . self::SYNC_WHITELIST_API,
            GlobalEnums::MY_COUNTRY_CODE => self::PRO_MY_DOMAIN . self::SYNC_WHITELIST_API,
            GlobalEnums::LA_COUNTRY_CODE => self::PRO_LA_DOMAIN . self::SYNC_WHITELIST_API,
            GlobalEnums::VN_COUNTRY_CODE => self::PRO_VN_DOMAIN . self::SYNC_WHITELIST_API,
            GlobalEnums::ID_COUNTRY_CODE => self::PRO_ID_DOMAIN . self::SYNC_WHITELIST_API,
        ],
    ];

    // 各环境需要汇总白名单待办数据的api地址
    const HTTP_GET_DATA_AUTH_CODE = 'da39a3ee5e6b4b0d3255bfef95601890afd80709';    // 泰国OA取其他国家接口调用认证code
    const WHITELIST_PENDING_API = '/common/workflow_pending/getWhitelistData';
    public static $need_collect_whitelist_pending_data_api_list = [
        'dev'      => [
            GlobalEnums::PH_COUNTRY_CODE => self::DEV_PH_DOMAIN . self::WHITELIST_PENDING_API,
            GlobalEnums::MY_COUNTRY_CODE => self::DEV_MY_DOMAIN . self::WHITELIST_PENDING_API,
            GlobalEnums::LA_COUNTRY_CODE => self::DEV_LA_DOMAIN . self::WHITELIST_PENDING_API,
            GlobalEnums::VN_COUNTRY_CODE => self::DEV_VN_DOMAIN . self::WHITELIST_PENDING_API,
            GlobalEnums::ID_COUNTRY_CODE => self::DEV_ID_DOMAIN . self::WHITELIST_PENDING_API,
        ],
        'training' => [
            GlobalEnums::PH_COUNTRY_CODE => self::TRA_PH_DOMAIN . self::WHITELIST_PENDING_API,
            GlobalEnums::MY_COUNTRY_CODE => self::TRA_MY_DOMAIN . self::WHITELIST_PENDING_API,
            GlobalEnums::LA_COUNTRY_CODE => self::TRA_LA_DOMAIN . self::WHITELIST_PENDING_API,
            GlobalEnums::VN_COUNTRY_CODE => self::TRA_VN_DOMAIN . self::WHITELIST_PENDING_API,
            GlobalEnums::ID_COUNTRY_CODE => self::TRA_ID_DOMAIN . self::WHITELIST_PENDING_API,
        ],
        'pro'      => [
            GlobalEnums::PH_COUNTRY_CODE => self::PRO_PH_DOMAIN . self::WHITELIST_PENDING_API,
            GlobalEnums::MY_COUNTRY_CODE => self::PRO_MY_DOMAIN . self::WHITELIST_PENDING_API,
            GlobalEnums::LA_COUNTRY_CODE => self::PRO_LA_DOMAIN . self::WHITELIST_PENDING_API,
            GlobalEnums::VN_COUNTRY_CODE => self::PRO_VN_DOMAIN . self::WHITELIST_PENDING_API,
            GlobalEnums::ID_COUNTRY_CODE => self::PRO_ID_DOMAIN . self::WHITELIST_PENDING_API,
        ],
    ];

    // pending 各国 push api
    const WORKFLOW_PENDING_PUSH_API = '/common/workflow_pending/push';
    const WORKFLOW_PENDING_PUSH_API_PRIVATE_KEY = '}|{*(odp912_kl(_^%$#@`';
    public static $workflow_pending_push_api_list = [
        'dev'      => [
            GlobalEnums::TH_COUNTRY_CODE => self::DEV_TH_DOMAIN . self::WORKFLOW_PENDING_PUSH_API,
            GlobalEnums::PH_COUNTRY_CODE => self::DEV_PH_DOMAIN . self::WORKFLOW_PENDING_PUSH_API,
            GlobalEnums::MY_COUNTRY_CODE => self::DEV_MY_DOMAIN . self::WORKFLOW_PENDING_PUSH_API,
            GlobalEnums::LA_COUNTRY_CODE => self::DEV_LA_DOMAIN . self::WORKFLOW_PENDING_PUSH_API,
            GlobalEnums::VN_COUNTRY_CODE => self::DEV_VN_DOMAIN . self::WORKFLOW_PENDING_PUSH_API,
            GlobalEnums::ID_COUNTRY_CODE => self::DEV_ID_DOMAIN . self::WORKFLOW_PENDING_PUSH_API,
        ],
        'training' => [
            GlobalEnums::TH_COUNTRY_CODE => self::TRA_TH_DOMAIN . self::WORKFLOW_PENDING_PUSH_API,
            GlobalEnums::PH_COUNTRY_CODE => self::TRA_PH_DOMAIN . self::WORKFLOW_PENDING_PUSH_API,
            GlobalEnums::MY_COUNTRY_CODE => self::TRA_MY_DOMAIN . self::WORKFLOW_PENDING_PUSH_API,
            GlobalEnums::LA_COUNTRY_CODE => self::TRA_LA_DOMAIN . self::WORKFLOW_PENDING_PUSH_API,
            GlobalEnums::VN_COUNTRY_CODE => self::TRA_VN_DOMAIN . self::WORKFLOW_PENDING_PUSH_API,
            GlobalEnums::ID_COUNTRY_CODE => self::TRA_ID_DOMAIN . self::WORKFLOW_PENDING_PUSH_API,
        ],
        'pro'      => [
            GlobalEnums::TH_COUNTRY_CODE => self::PRO_TH_DOMAIN . self::WORKFLOW_PENDING_PUSH_API,
            GlobalEnums::PH_COUNTRY_CODE => self::PRO_PH_DOMAIN . self::WORKFLOW_PENDING_PUSH_API,
            GlobalEnums::MY_COUNTRY_CODE => self::PRO_MY_DOMAIN . self::WORKFLOW_PENDING_PUSH_API,
            GlobalEnums::LA_COUNTRY_CODE => self::PRO_LA_DOMAIN . self::WORKFLOW_PENDING_PUSH_API,
            GlobalEnums::VN_COUNTRY_CODE => self::PRO_VN_DOMAIN . self::WORKFLOW_PENDING_PUSH_API,
            GlobalEnums::ID_COUNTRY_CODE => self::PRO_ID_DOMAIN . self::WORKFLOW_PENDING_PUSH_API,
        ],
    ];

    // 阿里云中国区短信模板相关配置
    const ALI_SMS_CN_CHANNEL_CODE = 'CN';
    const ALI_SMS_CN_SERVICE_PROVIDER = 3;
    const ALI_SMS_CN_SEND_NAME = 'flash_cn';

    // 仅OA待办的模板
    const OA_ALI_SMS_TMP_CN_CODE = 'SMS_472290055';
    const OA_PENDING_SMS_CONTENT_TMP = 'Pending OA tasks ${pending_data}.${send_datetime}';

    // 仅BY待办的模板
    const BY_ALI_SMS_TMP_CN_CODE = 'SMS_472100068';
    const BY_PENDING_SMS_CONTENT_TMP = 'Pending BY tasks ${pending_data}.${send_datetime}';

    // OA 和 BY 均有待办的模板
    const BOTH_ALI_SMS_TMP_CN_CODE = 'SMS_472335061';
    const BOTH_PENDING_SMS_CONTENT_TMP = 'Pending BY tasks ${by_pending_data} ; Pending OA tasks ${oa_pending_data}.${send_datetime}';

    // 采购模块（申请单、订单）- 采购经理/集团采购总监/资产经理 - 待审批 - 提醒模版
    const PURCHASE_ALI_SMS_TMP_CODE_CN = 'SMS_472365070';
    const PURCHASE_SMS_CONTENT_TMP_EN = 'hi, you have pending approval: ${biz_type}, it will expire at ${remind_time}, please go to the ${country_code}OA system to approve in time.';

    // 采购模块（订单）- 财务 - 待审批 - 提醒模板
    const PURCHASE_FINANCE_SMS_TMP_CODE_CN = 'SMS_472450062';
    const PURCHASE_FINANCE_SMS_CONTENT_TMP_EN = 'hi, the approval time for POs by the Finance Dept will soon exceed the limit-24 hours, please go to the ${country_code}OA system to approve in time';

    // 采购模块（申请单) - 给资产部领导 - 提醒模板
    const PURCHASE_ASSET_ALI_SMS_TMP_CODE_CN = 'SMS_472465059';
    const PURCHASE_ASSET_SMS_CONTENT_TMP_EN = 'Last week, ${country_code} Asset Dept had ${apply_num} PUR approval time expired. Please check the email for a detailed list.';

    // 采购模块（申请单、订单) - 给采购部领导 - 提醒模板
    const PURCHASE_LEADER_ALI_SMS_TMP_CODE_CN = 'SMS_472330061';
    const PURCHASE_LEADER_SMS_CONTENT_TMP_EN = 'Last week, ${country_code} procurement department had approval timeout for ${apply_num} PURs and ${order_num} POs. Please check your mailbox for the detailed list.';

    // 采购模块（订单) - 给财务部领导 - 提醒模板
    const PURCHASE_FINANCE_LEADER_ALI_SMS_TMP_CODE_CN = 'SMS_472245069';
    const PURCHASE_FINANCE_LEADER_SMS_CONTENT_TMP_EN = 'Last week, the Financial Dept of ${country_code} had the approval time of ${apply_num} pen PO expired. Please check the email for the detailed list.';


    // 测试环境收信号码配置 短信国家码 => 该国手机号
    public static $receive_sms_test_mobiles = [
        'CN' => '18515062691', // 中国 18875245137, +86 18515062691
        'TH' => '0935821403',  // 泰国 0639403384, +66 935821403
        'PH' => '9605487103',  // 菲律宾 09055678093, +63 9605487103
        'MY' => '162486488',   // 马来 0166031262, +60 162486488
        'LA' => '2023628956',  // 老挝 +856 2023628956
        'VN' => '335269038',   // 越南 +84 335269038
        'ID' => '81362663999', // 印尼 +62 81362663999
    ];

    //v10942 保留, 测短信用
    //可用手机号：（同事自己的手机号，请注意保密）
    //方英     越南 ：         +84 335269038
    //Lim      马来            +60 16 248 6488
    //Jennie   菲律宾        + 63 9605487103
    //欣然      泰国：     +66 935821403
    //HRBP     印尼  +62 81362663999
    //HRBP     老挝  +856 2023628956
    //中国： +86  18515062691


    // 审批流待办需要从 BY 取数的
    const BY_APPROVAL_TYPE_WMS = 9;                                                //公共资产
    const BY_APPROVAL_TYPE_ASSETS = 16;                                            //个人资产
    const BY_APPROVAL_TYPE_PUBLIC_ASSETS = 19;                                     //公共资产
    const BY_APPROVAL_TYPE_JT_OA = 20;                                             //转岗申请OA渠道
    const BY_APPROVAL_TYPE_HC_BUDGET = 24;                                         //HC预算
    public static $by_pending_audit_type_list = [
        self::BY_APPROVAL_TYPE_WMS,
        self::BY_APPROVAL_TYPE_ASSETS,
        self::BY_APPROVAL_TYPE_PUBLIC_ASSETS,
        self::BY_APPROVAL_TYPE_JT_OA,
        self::BY_APPROVAL_TYPE_HC_BUDGET,
        ByWorkflowEnums::BY_BIZ_TYPE_WAREHOUSE_CHANGE_STATUS,
        ByWorkflowEnums::BY_BIZ_TYPE_WAREHOUSE_CHANGE_STORE,
        ByWorkflowEnums::BY_BIZ_TYPE_ASSET,
        ByWorkflowEnums::BY_BIZ_TYPE_WMS,
        ByWorkflowEnums::BY_BIZ_TYPE_JT,
        ByWorkflowEnums::BY_BIZ_TYPE_AGENCY_PAYMENT,
        ByWorkflowEnums::BY_BIZ_TYPE_BUDGET_WITHHOLDING,
        ByWorkflowEnums::BY_BIZ_TYPE_WAREHOUSE_THREAD_PRICE,
    ];

    // BY获取OA红点, OA里对接了BY审批流的业务
    public static $by_pending_audit_type_for_oa_reddot = [
        self::BY_APPROVAL_TYPE_HC_BUDGET,
        ByWorkflowEnums::BY_BIZ_TYPE_WAREHOUSE_CHANGE_STATUS,
        ByWorkflowEnums::BY_BIZ_TYPE_WAREHOUSE_CHANGE_STORE,
        ByWorkflowEnums::BY_BIZ_TYPE_AGENCY_PAYMENT,
        ByWorkflowEnums::BY_BIZ_TYPE_BUDGET_WITHHOLDING,
        ByWorkflowEnums::BY_BIZ_TYPE_WAREHOUSE_THREAD_PRICE,
    ];

}
