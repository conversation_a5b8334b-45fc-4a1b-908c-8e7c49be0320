<?php
/**
 * OA 审批流配置
 * Class enums
 */

namespace App\Library\Enums;

use App\Library\Enums;

final class OAWorkflowEnums
{
    /**************************************workflow主表 begin*************************************/
    // 审批流启用状态 enable_status
    const WORKFLOW_ENABLE_STATUS_NOT = 0;
    const WORKFLOW_ENABLE_STATUS_ING = 1;
    const WORKFLOW_ENABLE_STATUS_INVALID = 2;
    public static $workflow_enable_status_items = [
        self::WORKFLOW_ENABLE_STATUS_NOT     => 'workflow_enable_status_not',
        self::WORKFLOW_ENABLE_STATUS_ING     => 'workflow_enable_status_ing',
        self::WORKFLOW_ENABLE_STATUS_INVALID => 'workflow_enable_status_invalid',
    ];

    // 审批节点审批人取值类型
    const AUDITOR_TYPE_STAFF_INFO_ID = 1;  //指定工号

    /**************************************workflow主表 end*************************************/

    /**************************************workflow节点 begin*************************************/
    // 节点类型 workflow_node表type字段
    const WORKFLOW_NODE_TYPE_START = 1;    //开始
    const WORKFLOW_NODE_TYPE_AUDIT = 3;    //审批
    const WORKFLOW_NODE_TYPE_END = 6;      //结束

    //开始节点和结束节点固定名称翻译key -- 审批流画布展示使用 根据需求文档,特定节点翻译成特定的文本
    const WORKFLOW_NODE_START_NODE_NAME = 'workflow_node_start_node_name';
    const WORKFLOW_NODE_START_NODE_DESC = 'workflow_node_start_node_desc';
    const WORKFLOW_NODE_END_NODE_NAME = 'workflow_node_end_node_name';
    const WORKFLOW_NODE_END_NODE_DESC = 'workflow_node_end_node_desc';
    //节点审批人规则 -- 审批流画布展示使用 : 描述节点寻人逻辑
    public static $auditor_type_items = [
        Enums::AUDITOR_TYPE_STAFF_INFO_ID                                => [
            'text'   => 'auditor_type_text_1',  //指定员工
            'remark' => 'auditor_type_remark_1',//指定员工
        ],
        Enums::AUDITOR_TYPE_STAFF_ROLE                                   => [
            'text'   => 'auditor_type_text_2',  //指定角色
            'remark' => 'auditor_type_remark_2',//指定角色
        ],
        Enums::AUDITOR_TYPE_SUBMITTER_DIRECT_SUPERIOR                    => [
            'text'   => 'auditor_type_text_3',  //提交人的直线上级
            'remark' => 'auditor_type_remark_3',//直线上级
        ],
        Enums::AUDITOR_TYPE_SUBMITTER_DEPARTMENT_MANAGER                 => [
            'text'   => 'auditor_type_text_4',  //部门经理(入参根据各模块的getWorkflowParams)
            'remark' => 'auditor_type_remark_4',//部门经理(入参根据各模块的getWorkflowParams)
        ],
        Enums::AUDITOR_TYPE_SUBMITTER_NODE_DEPARTMENT                    => [
            'text'   => 'auditor_type_text_5',  //二级部门负责人
            'remark' => 'auditor_type_remark_5',//二级部门负责人
        ],
        Enums::AUDITOR_TYPE_SUBMITTER_DEPARTMENT                         => [
            'text'   => 'auditor_type_text_6',  //一级部门负责人
            'remark' => 'auditor_type_remark_6',//一级部门负责人
        ],
        Enums::AUDITOR_TYPE_COO                                          => [
            'text'   => 'auditor_type_text_7',  //组织负责人
            'remark' => 'auditor_type_remark_7',//组织负责人
        ],
        Enums::AUDITOR_TYPE_BRANCH_MANAGER                               => [
            'text'   => 'auditor_type_text_8',  //网点主管
            'remark' => 'auditor_type_remark_8',//网点主管
        ],
        Enums::AUDITOR_TYPE_BRANCH_LEVEL1                                => [
            'text'   => 'auditor_type_text_9',  //网点审批Level1，Supervisor
            'remark' => 'auditor_type_remark_9',//网点审批Level1，Supervisor
        ],
        Enums::AUDITOR_TYPE_BRANCH_LEVEL2                                => [
            'text'   => 'auditor_type_text_10',  //网点审批Level2, Manager
            'remark' => 'auditor_type_remark_10',//网点审批Level2, Manager
        ],
        Enums::AUDITOR_TYPE_BRANCH_LEVEL3                                => [
            'text'   => 'auditor_type_text_11',  //网点审批level3, Senior Manager
            'remark' => 'auditor_type_remark_11',//网点审批level3, Senior Manager
        ],
        Enums::AUDITOR_TYPE_BRANCH_LEVEL4                                => [
            'text'   => 'auditor_type_text_12',  //网点审批level4, Director
            'remark' => 'auditor_type_remark_12',//网点审批level4, Director
        ],
        Enums::AUDITOR_TYPE_BRANCH_MANAGER_BY_POSITION                   => [
            'text'   => 'auditor_type_text_13',  //网点主管根据职位
            'remark' => 'auditor_type_remark_13',//网点主管根据职位
        ],
        Enums::AUDITOR_TYPE_BRANCH_BUSINESS_MANAGER                      => [
            'text'   => 'auditor_type_text_14',  //Network业务线Manager(片区经理)
            'remark' => 'auditor_type_remark_14',//Network业务线Manager(片区经理)
        ],
        Enums::AUDITOR_TYPE_BRANCH_BUSINESS_SENIOR_MANAGER               => [
            'text'   => 'auditor_type_text_15',  //Network业务线SENIOR_MANAGER(大区经理)
            'remark' => 'auditor_type_remark_15',//Network业务线SENIOR_MANAGER(大区经理)
        ],
        Enums::AUDITOR_TYPE_COMPANY_MANAGER                              => [
            'text'   => 'auditor_type_text_16',  //公司负责人
            'remark' => 'auditor_type_remark_16',//公司负责人
        ],
        Enums::AUDITOR_TYPE_SHOP_BRANCH_SENIOR_MANAGER                   => [
            'text'   => 'auditor_type_text_17',  //Shop业务线SENIOR_MANAGER(Area Manager)
            'remark' => 'auditor_type_remark_17',//Shop业务线SENIOR_MANAGER(Area Manager)
        ],
        Enums::AUDITOR_TYPE_ACCESS_DATA_SYS_SUBMITTER_DEPARTMENT_MANAGER => [
            'text'   => 'auditor_type_text_18',  //取数工单系统:提交人所属部门负责人
            'remark' => 'auditor_type_remark_18',//取数工单系统-提交人所属部门负责人
        ],
        Enums::AUDITOR_TYPE_ACCESS_DATA_SYS_DATA_DEPARTMENT_MANAGER      => [
            'text'   => 'auditor_type_text_19',  //取数工单系统:数据部门负责人
            'remark' => 'auditor_type_remark_19',//取数工单系统-数据部门负责人
        ],
        Enums::AUDITOR_TYPE_ACCESS_DATA_SYS_RELATED_DEPARTMENT_MANAGER   => [
            'text'   => 'auditor_type_text_20',  //取数工单系统:相关部门负责人
            'remark' => 'auditor_type_remark_20',//取数工单系统-相关部门负责人
        ],
        Enums::AUDITOR_TYPE_JOB                                          => [
            'text'   => 'auditor_type_text_21',  //获取指定职位
            'remark' => 'auditor_type_remark_21',//获取指定职位
        ],
        Enums::AUDITOR_TYPE_DM_APPLY                                     => [
            'text'   => 'auditor_type_text_22',  //组织架构中的申请人管辖片区的负责人
            'remark' => 'auditor_type_remark_22',//组织架构中管辖片区的负责人
        ],
        Enums::AUDITOR_TYPE_COMPANY_C_LEVEL_GROUP_CEO_MANAGER            => [
            'text'   => 'auditor_type_text_23',  //公司负责人>组织负责人>Group CEO
            'remark' => 'auditor_type_remark_23',//公司负责人>组织负责人>Group CEO
        ],
        Enums::AUDITOR_TYPE_HC_HRBP                                      => [
            'text'   => 'auditor_type_text_24',  //员工所在部门的管辖HRBP
            'remark' => 'auditor_type_remark_24',//员工所在部门的管辖HRBP
        ],
        Enums::AUDITOR_TYPE_CROSS_DEPARTMENT_ID                          => [
            'text'   => 'auditor_type_text_25',  //跨部门一级部门负责人
            'remark' => 'auditor_type_remark_25',//跨部门一级部门负责人
        ],
        Enums::AUDITOR_TYPE_HUB_AREA_MANAGER                             => [
            'text'   => 'auditor_type_text_26',  //员工所属区域的对应负责人
            'remark' => 'auditor_type_remark_26',//员工所属区域的对应负责人
        ],
        Enums::AUDITOR_TYPE_DYNAMIC_UID                                  => [
            'text'   => 'auditor_type_text_27',  //(采购样品确认)动态获取使用部门工号或样品确认工号
            'remark' => 'auditor_type_remark_27',//(采购样品确认)动态获取使用部门工号或样品确认工号
        ],
        Enums::AUDITOR_TYPE_NODE_AM_BY_ORG                               => [
            'text'   => 'auditor_type_text_28',  //OA组织架构大区经理
            'remark' => 'auditor_type_remark_28',//OA组织架构大区经理
        ],
        Enums::AUDITOR_TYPE_DESIGNATIVE_ORG                              => [
            'text'   => 'auditor_type_text_29',  //指定的组织架构的负责人
            'remark' => 'auditor_type_remark_29',//指定的组织架构的负责人
        ],
        Enums::AUDITOR_TYPE_GROUP_ORG                                    => [
            'text'   => 'auditor_type_text_30',  //财务分组审批人员
            'remark' => 'auditor_type_remark_30',//财务分组审批人员
        ],
        Enums::AUDITOR_TYPE_ORDINARY_PAYMENT_DM_ORG                      => [
            'text'   => 'auditor_type_text_31',  //OA组织架构片区经理
            'remark' => 'auditor_type_remark_31',//OA组织架构片区经理
        ],
    ];

    /**************************************workflow节点 end*************************************/


    /**************************************workflow流转 begin*************************************/
    //审批流条件表达式方法取值类型 -- 审批流画布展示使用 : 表达式中每个变量的类型
    const VALUATE_CODE_TYPE_VALUE = 1;     //原始值
    const VALUATE_CODE_TYPE_ENUM = 2;      //枚举
    //const VALUATE_CODE_TYPE_BOOL = 3;//布尔 当前没有布尔条件,没有用empty判断的,全部用枚举代替
    const VALUATE_CODE_TYPE_DEPARTMENT = 4;//部门id
    const VALUATE_CODE_TYPE_STAFF = 5;     //员工id

    //审批流流转条件 -- 审批流画布展示条件文本时使用 : workflow_node_relate.valuate_formula字段条件表达式解析成文本
    public static $workflow_valuate_code_list = [];

    public static function getValuateCodeList()
    {
        self::$workflow_valuate_code_list = [
            //部门
            'getSubmitterDepartment' => [
                'text'       => 'valuate_code_getsubmitterdepartment',
                'value_type' => self::VALUATE_CODE_TYPE_DEPARTMENT,
            ],
            //美元金额
            'getUSD'                 => [
                'text'       => 'valuate_code_getusd',
                'value_type' => self::VALUATE_CODE_TYPE_VALUE,
            ],
            //泰铢金额
            'getTHB'                 => [
                'text'       => 'valuate_code_getthb',
                'value_type' => self::VALUATE_CODE_TYPE_VALUE,
            ],
            //人民币金额
            'getCNY'                 => [
                'text'       => 'valuate_code_getcny',
                'value_type' => self::VALUATE_CODE_TYPE_VALUE,
            ],
            //币种
            'getCurrency'            => [
                'text'       => 'valuate_code_getcurrency',
                'value_type' => self::VALUATE_CODE_TYPE_ENUM,
                'value_enum' => GlobalEnums::$currency_item,
            ],
            //金额
            'getAmount'              => [
                'text'       => 'valuate_code_getamount',
                'value_type' => self::VALUATE_CODE_TYPE_VALUE,
            ],
            //总金额
            'getTotalAmount'         => [
                'text'       => 'valuate_code_gettotalamount',
                'value_type' => self::VALUATE_CODE_TYPE_VALUE,
            ],
            //比较申请人跟指定工号的职等
            'compareJobLevel'        => [
                'text'       => 'valuate_code_comparejoblevel',
                'value_type' => self::VALUATE_CODE_TYPE_ENUM,
                'value_enum' => [
                    '1'  => 'valuate_code_comparejoblevel_big',
                    '0'  => 'valuate_code_comparejoblevel_equal',
                    '-1' => 'valuate_code_comparejoblevel_little',
                ],
            ],
            //采购申请类型是否广告类
            'isAd'                   => [
                'text'       => 'valuate_code_isad',
                'value_type' => self::VALUATE_CODE_TYPE_ENUM,
                'value_enum' => [
                    '1' => 'valuate_code_isad_yes',
                    '0' => 'valuate_code_isad_no',
                ],
            ],
            //采购申请类型是否是固定资产类
            'isAsset'                => [
                'text'       => 'valuate_code_isasset',
                'value_type' => self::VALUATE_CODE_TYPE_ENUM,
                'value_enum' => [
                    '1' => 'valuate_code_isasset_yes',
                    '0' => 'valuate_code_isasset_no',
                ],
            ],
            //业务付款-是否是网点
            'isStore'                => [
                'text'       => 'valuate_code_isstore',
                'value_type' => self::VALUATE_CODE_TYPE_ENUM,
                'value_enum' => [
                    '1' => 'valuate_code_isstore_network',
                    '0' => 'valuate_code_isstore_head',
                ],
            ],
            //租房类型
            'getContractType'        => [
                'text'       => 'valuate_code_getcontracttype',
                'value_type' => self::VALUATE_CODE_TYPE_ENUM,
                'value_enum' => Enums::$rent_by_items //需要翻译
            ],
            //语种
            'getLang'                => [
                'text'       => 'valuate_code_getlang',
                'value_type' => self::VALUATE_CODE_TYPE_ENUM,
                'value_enum' => Enums::$store_contract_lang,
            ],
            //是否需要部门审核人审批
            'getManagerApprove'      => [
                'text'       => 'valuate_code_getmanagerapprove',
                'value_type' => self::VALUATE_CODE_TYPE_ENUM,
                'value_enum' => [
                    '1' => 'valuate_code_getmanagerapprove_yes',
                    '0' => 'valuate_code_getmanagerapprove_no',
                ],
            ],
            //是否是探亲费或团建费
            'isFamilyOrMorale'       => [
                'text'       => 'valuate_code_isFamilyOrMorale',
                'value_type' => self::VALUATE_CODE_TYPE_ENUM,
                'value_enum' => [
                    '1' => 'valuate_code_isfamilyormorale_yes',
                    '0' => 'valuate_code_isfamilyormorale_no',
                ],
            ],
            //报销类型(报销实质)
            'getReimbursementType'   => [
                'text'       => 'valuate_code_getreimbursementtype',
                'value_type' => self::VALUATE_CODE_TYPE_ENUM,
                'value_enum' => Enums::$reimbursement_type_items //需要翻译
            ],
            //公司
            'getCompanyId'           => [
                'text'       => 'valuate_code_getcompanyid',
                'value_type' => self::VALUATE_CODE_TYPE_DEPARTMENT,
            ],
            //提交人
            'getSubmitterId'         => [
                'text'       => 'valuate_code_getsubmitterid',
                'value_type' => self::VALUATE_CODE_TYPE_STAFF,
            ],
            //普通付款类型
            'getOrdinaryPaymentType' => [
                'text'       => 'valuate_code_getordinarypaymenttype',
                'value_type' => self::VALUATE_CODE_TYPE_ENUM,
                'value_enum' => Enums::$reimbursement_type_items //需要翻译
            ],
            //供应商应用模块
            'getVendorModules'       => [
                'text'       => 'valuate_code_getvendormodules',
                'value_type' => self::VALUATE_CODE_TYPE_ENUM,
                'value_enum' => [ //在表vendor_application_module中维护,这里写到枚举中和表保持一致
                    '1' => 'application_module.1',
                    '2' => 'application_module.2',
                ],
            ],
            //是否只走采购审批人
            'getOnlyPurchaseAudit'   => [
                'text'       => 'valuate_code_getonlypurchaseaudit',
                'value_type' => self::VALUATE_CODE_TYPE_ENUM,
                'value_enum' => [
                    '1' => 'valuate_code_getonlypurchaseaudit_yes',
                    '0' => 'valuate_code_getonlypurchaseaudit_no',
                ],
            ],
            //供应商采购类型
            'getPurchaseType'        => [
                'text'       => 'valuate_code_getpurchasetype',
                'value_type' => self::VALUATE_CODE_TYPE_ENUM,
                'value_enum' => VendorEnums::$vendor_purchase_type_item,
            ],
            //当前国家币种金额
            'getExchangeAmount'      => [
                'text'       => 'valuate_code_getexchangeamount',
                'value_type' => self::VALUATE_CODE_TYPE_VALUE,
            ],
            //合同类型
            'getTemplateId'          => [
                'text'       => 'valuate_code_gettemplateid',
                'value_type' => self::VALUATE_CODE_TYPE_ENUM,
                'value_enum' => [ //在表contract_category中维护,这里写到枚举中和表保持一致, 只维护了用到的21和22
                    '21' => 'contract_type.21',
                    '22' => 'contract_type.22',
                ],
            ],
            //是否主合同
            'getIsMaster'            => [
                'text'       => 'valuate_code_getismaster',
                'value_type' => self::VALUATE_CODE_TYPE_ENUM,
                'value_enum' => [
                    '1' => 'valuate_code_getismaster_yes',
                    '0' => 'valuate_code_getismaster_no',
                ],
            ],
            //(报价)职位类型
            'getJobTitleType'        => [
                'text'       => 'valuate_code_getjobtitletype',
                'value_type' => self::VALUATE_CODE_TYPE_ENUM,
                'value_enum' => [ //职位,不需要翻译
                    'BS' => 'Branch supervisor',
                    'DM' => 'District Manager',
                    'AM' => 'Area Manager',
                ],
            ],
            //(报价)产品类型
            'getProductType'         => [
                'text'       => 'valuate_code_getproducttype',
                'value_type' => self::VALUATE_CODE_TYPE_ENUM,
                'value_enum' => CrmQuotationEnums::$product_type_items //新增翻译
            ],
            //(报价)是否存在cod产品
            'getProductCod'          => [
                'text'       => 'valuate_code_getproductcod',
                'value_type' => self::VALUATE_CODE_TYPE_ENUM,
                'value_enum' => [
                    '1' => 'valuate_code_getproductcod_yes',
                    '0' => 'valuate_code_getproductcod_no',
                ],
            ],
            //(报价)是否存在结算方式产品
            'getProductSettlement'   => [
                'text'       => 'valuate_code_getproductsettlement',
                'value_type' => self::VALUATE_CODE_TYPE_ENUM,
                'value_enum' => [
                    '1' => 'valuate_code_getproductsettlement_yes',
                    '0' => 'valuate_code_getproductsettlement_no',
                ],
            ],
            //(报价)结算周期
            'getSettlementPeriod'    => [
                'text'       => 'valuate_code_getsettlementperiod',
                'value_type' => self::VALUATE_CODE_TYPE_ENUM,
                'value_enum' => CrmQuotationEnums::$settlement_period_items //新增翻译
            ],
            //(报价)是否存在物料产品
            'getProductMaterial'     => [
                'text'       => 'valuate_code_getproductmaterial',
                'value_type' => self::VALUATE_CODE_TYPE_ENUM,
                'value_enum' => [
                    '1' => 'valuate_code_getproductmaterial_yes',
                    '0' => 'valuate_code_getproductmaterial_no',
                ],
            ],
            //(报价)固定折扣值
            'getLowestDiscountRate'  => [
                'text'       => 'valuate_code_getlowestdiscountrate',
                'value_type' => self::VALUATE_CODE_TYPE_VALUE,
            ],
            //(报价)是否存在特殊价格表网点计费价格
            'getIsSpecial'           => [
                'text'       => 'valuate_code_getisspecial',
                'value_type' => self::VALUATE_CODE_TYPE_ENUM,
                'value_enum' => [
                    '1' => 'valuate_code_getisspecial_yes',
                    '0' => 'valuate_code_getisspecial_no',
                ],
            ],
            //(报价)折扣有效期
            'getValidDays'           => [
                'text'       => 'valuate_code_getvaliddays',
                'value_type' => self::VALUATE_CODE_TYPE_VALUE,
            ],
            //采购申请单行数据是否存在资产类的barcode
            'getHasAssetsBarcode'    => [
                'text'       => 'valuate_code_gethasassetsbarcode',
                'value_type' => self::VALUATE_CODE_TYPE_ENUM,
                'value_enum' => [
                    '1' => 'valuate_code_gethasassetsbarcode_yes',
                    '0' => 'valuate_code_gethasassetsbarcode_no',
                ],
            ],
            //是否包含外包外协
            'getOutWorker'           => [
                'text'       => 'valuate_code_getoutworker',
                'value_type' => self::VALUATE_CODE_TYPE_ENUM,
                'value_enum' => ReimbursementEnums::$out_worker_items //新增翻译
            ],
            //是否有罚金
            'getFine'                => [
                'text'       => 'valuate_code_getfine',
                'value_type' => self::VALUATE_CODE_TYPE_ENUM,
                'value_enum' => ReimbursementEnums::$is_fine_items //新增翻译
            ],
            //创建人
            'getCreateId'            => [
                'text'       => 'valuate_code_getcreateid',
                'value_type' => self::VALUATE_CODE_TYPE_STAFF,
            ],
            //是否包含(付款分类=员工福利费 且 费用类型=其他)
            'getIsHaveWelfare'       => [
                'text'       => 'valuate_code_getishavewelfare',
                'value_type' => self::VALUATE_CODE_TYPE_ENUM,
                'value_enum' => [
                    '1' => 'valuate_code_getishavewelfare_yes',
                    '0' => 'valuate_code_getishavewelfare_no',
                ],
            ],
            //验收单类别
            'getAcceptanceCate'      => [
                'text'       => 'valuate_code_getacceptancecate',
                'value_type' => self::VALUATE_CODE_TYPE_ENUM,
                'value_enum' => PurchaseEnums::$acceptance_order_type,
            ],
            //判断网点类型 1 network
            'getStoreCategory'       => [
                'text'       => 'valuate_code_is_network',
                'value_type' => self::VALUATE_CODE_TYPE_ENUM,
                'value_enum' => [
                    '1' => 'valuate_code_is_network_yes',
                    '0' => 'valuate_code_is_network_no',
                ],
            ],
        ];
        return self::$workflow_valuate_code_list;
    }

    /**************************************workflow流转 end*************************************/

    /**************************************审批流抄送 begin*************************************/
    //抄送状态
    const WORKFLOW_CC_STATUS_UNREAD = 1;   //未读
    const WORKFLOW_CC_STATUS_READ = 2;     //已读
    public static $workflow_cc_status_items = [
        self::WORKFLOW_CC_STATUS_UNREAD => 'workflow_cc_status_unread',
        self::WORKFLOW_CC_STATUS_READ   => 'workflow_cc_status_read',
    ];
    /**************************************审批流抄送 begin*************************************/

}
