<?php

namespace App\Library\Enums;

/**
 * Class DownloadCenterEnum
 * @package App\Library\Enums
 * @date 2022/8/24
 */
final class DownloadCenterEnum
{
    // 默认语言
    const DETAUL_LANGUAGE = 'en';

    // 分页/分批取数配置
    const INIT_PAGE_NUM = 1; // 初始批次/页码
    const DEFAUL_PER_PAGE_SIZE = 10000; // 默认每批次/每页取数条数

    // 任务处理状态
    const TASK_STATUS_PENDING = 0; // 待处理
    const TASK_STATUS_SUCCESS = 1; // 执行成功

    // 导出方式
    const EXPORT_METHOD_SYNC = 1;// 同步导出
    const EXPORT_METHOD_ASYNC = 2;// 异步导出

    const ACTION_NAME_SPACE_SEPARATOR = ' '; // action_name 的空格 使用这个
    const TYPE_TALENT_REVIEW_STAFF_SCOPE_1 = 201; // 人才盘点-我的部门下载
    const TYPE_TALENT_REVIEW_STAFF_SCOPE_2 = 202; // 人才盘点-我的管辖下载
    const TYPE_TALENT_REVIEW_STAFF_SCOPE_3 = 203; // 人才盘点-公司全员下载

    // 银行流水模块
    const DOWNLOAD_CENTER_BANK_FLOW_INIT_DOWN = 301; // 银行流水-流水上传-下载
    const DOWNLOAD_CENTER_BANK_FLOW_GAT_EXPORT = 302; // 银行流水-流水管理-收款流水-导出
    const DOWNLOAD_CENTER_BANK_FLOW_PAY_EXPORT = 303; // 银行流水-流水管理-付款流水-导出

    // 资产模块
    const MATERIAL_WMS_PLAN_INFO_INVENTORY_REPORT = 400; // 物料/资产管理 - 库存盘点 - 盘点单名称 - 导出盘点报表
    const MATERIAL_ASSET_ACCOUNT = 401; // 物料/资产管理 - 资产台账 - 导出台账
    const MATERIAL_ASSET_OUT_STORAGE = 402; // 物料/资产管理 - 资产领用出库 - 导出
    const MATERIAL_ASSET_APPLY_AUDIT = 403;// 物料/资产管理 - 资产领用审批 - 导出
    const MATERIAL_INVENTORY_ASSET_EXPORT = 404;//物料/资产管理 - 资产盘点 - 盘点报表 - 导出
    const MATERIAL_LEAVE_ASSETS_EXPORT = 405;//物料资产管理-离职资产处理-导出 - 汇总表
    const MATERIAL_LEAVE_ASSETS_DETAIL_EXPORT = 406;//物料资产管理-离职资产处理-导出 - 明细表
    const MATERIAL_LEAVE_ASSETS_TRANSFER_EXPORT = 407;//物料资产管理-离职资产处理-导出 - 明细表(在途资产)
    const MATERIAL_ASSET_RETURN_WAIT_EXPORT = 408;//物料/资产管理 - 资产退回处理-待处理-导出
    const MATERIAL_ASSET_RETURN_TRANSFER_EXPORT = 409;//物料/资产管理 - 资产退回处理-已转交-导出
    const MATERIAL_ASSET_RETURN_DONE_EXPORT = 410;//物料/资产管理 - 资产退回处理-已处理-导出
    const MATERIAL_ASSET_RETURN_STORAGE_EXPORT = 411;//物料/资产管理 - 资产退回处理-退回入库-导出

    // 财务模块
    const FINANCE_REIMBURSEMENT_DATA = 500; // 报销管理 - 数据查询/报销审核 - 导出Excel
    const FINANCE_ORDINARY_PAYMENT_DATA = 501; // 普通付款 - 数据查询 - 导出
    const FINANCE_REIMBURSEMENT_DETAIL_STORE_SUPPORT_V2 = 502; // 报销管理-详情-报销实质-网点支援详情-导出

    // 组织架构模块
    const ORGANIZATION_JOB_DEPARTMENT_INFO = 600; // 组织架构 - 职位体系 - 职位管理 - 导出部门职位信息

    const MATERIAL_WMS_APPLY = 450; //耗材 - 耗材申请-导出
    const MATERIAL_WMS_OUT_STORAGE = 451; //耗材 - 耗材领用出库-导出
    const MATERIAL_WMS_APPLY_DATA = 452; //耗材 - 耗材申请-数据查询-导出
    const MATERIAL_WMS_PACKAGE_ALLOT = 453;//耗材 - 包材调拨单-导出
    const MATERIAL_WMS_PACKAGE_STOCK = 454;//耗材 - 进销存-导出


    const  INTERIOR_STAFF_ENTRY_SIZE = 460;//员工商城-员工工服尺码-导出

    // 支付模块
    const PAYMENT_ONLINE = 700;

    //转岗模块
    const JOB_TRANSFER_DATA_EXPORT = 800; //转岗管理-转岗数据-导出列表
    const JOB_TRANSFER_DATA_FOR_PAYROLL_EXPORT = 801; //转岗管理-转岗数据-payroll导出列表


    // 代理支付
    const AGENCY_PAYMENT_APPLY = 1100;//我的申请
    const AGENCY_PAYMENT_APPLY_DETAIL_EXPORT = 1104;//我的申请 - 付款明细-导出
    const AGENCY_PAYMENT_AUDIT = 1101;//我的审核
    const AGENCY_PAYMENT_AUDIT_DETAIL_EXPORT = 1105;//我的审核 - 付款明细-导出
    const AGENCY_PAYMENT_PAY = 1102;//代理支付
    const AGENCY_PAYMENT_PAY_DETAIL_EXPORT = 1106;//我的审核 - 付款明细-导出
    const AGENCY_PAYMENT_DATA = 1103;//数据查询
    const AGENCY_PAYMENT_DATA_DETAIL_EXPORT = 1107;//数据查询 - 付款明细-导出

    // 合同模块
    const CONTRACT_ELECTRONIC_EXPORT = 900;//电子合同制作-导出
    const CONTRACT_ELECTRONIC_EXPORT_ALL = 901;//数据查询-其他合同-电子合同列表-导出
    const CONTRACT_OTHER_EXPORT = 902;//合同管理-数据查询-其他合同-审批数据查询-列表导出

    // 仓库管理
    const FINANCE_WAREHOUSE_MANAGEMENT_INFO = 1300; // 仓库信息-导出
    const FINANCE_WAREHOUSE_REQUIREMENT_EXPORT = 1301;//仓库需求管理-导出
    const FINANCE_WAREHOUSE_REQUIREMENT_SEARCH_EXPORT = 1302;//处理仓库需求-待寻找-导出
    const FINANCE_WAREHOUSE_REQUIREMENT_CONFIRM_EXPORT = 1303;//处理仓库需求-待确认-导出
    const FINANCE_WAREHOUSE_REQUIREMENT_SETTLE_EXPORT = 1304;//处理仓库需求-待入驻-导出
    const FINANCE_WAREHOUSE_REQUIREMENT_RENEWED_EXPORT = 1308;//处理仓库需求-待续约-导出

    const FINANCE_WAREHOUSE_THREAD_EXPORT = 1305;//仓库线索管理-导出
    const FINANCE_WAREHOUSE_THREAD_PRICE_EXPORT = 1306;//处理仓库线索-待报价-导出
    const FINANCE_WAREHOUSE_THREAD_SIGN_EXPORT = 1307;//处理仓库线索-待签约-导出

    //薪酬扣款审批
    const WAGES_PAYMENT_APPROVAL_EXPORT = 1000;//OA-薪酬扣款审批-数据查询-导出

    //SIM卡管理
    const COMPANY_MOBILE_EXPORT = 1200;//OA-SIM卡管理-数据查询-导出
    const COMPANY_MOBILE_SIM_EXPORT = 1201;//OA-SIM卡管理-SIM卡池-导出
    const COMPANY_MOBILE_FEEDBACK_EXPORT = 1202;//OA-SIM卡管理-异常反馈-导出

    //网点租房付款-数据查询-批量下载
    const PAYMENT_STORE_RENTING_BATCH_DOWNLOAD = 1400;

    //财务预算管理
    const BUDGET_WITHHOLDING_EXPORT = 1500;//费用预提-导出

    // 下载中心的业务相关配置
    public static $download_center_excel_setting_item = [
        // 资产 - 库存盘点单报表下载
        self::MATERIAL_WMS_PLAN_INFO_INVENTORY_REPORT => [
            'file_name' => 'Consumable_Inventory_Result_Report_{YmdHis}.xlsx',
            'task_name' => 'material_export wms_inventory_report',
        ],

        // 资产 - 台账下载
        self::MATERIAL_ASSET_ACCOUNT => [
            'file_name' => 'material_asset_list_{YmdHis}.xlsx',
            'task_name' => 'material_export asset_account',
        ],

        // 资产 - 领用申请审批下载
        self::MATERIAL_ASSET_APPLY_AUDIT => [
            'file_name' => 'assets_apply_audit_{YmdHis}.xlsx',
            'task_name' => 'material_export asset_apply_audit',
        ],

        // 资产 - 领用出库下载
        self::MATERIAL_ASSET_OUT_STORAGE => [
            'file_name' => 'assets_outbound_{YmdHis}.xlsx',
            'task_name' => 'material_export asset_out_storage',
        ],

        //资产盘点 - 盘点报表 - 下载
        self::MATERIAL_INVENTORY_ASSET_EXPORT => [
            'file_name' => 'inventory_asset_detail_export-{YmdHis}.xlsx',
            'task_name' => 'material_export inventory_asset_detail',
        ],

        //物料资产管理-离职资产处理-导出 - 汇总表
        self::MATERIAL_LEAVE_ASSETS_EXPORT => [
            'file_name' => '离职资产汇总表&leave asses summary&รายการสินทรัพย์ ที่ออกจากตำแหน่ง_{YmdHis}.xlsx',
            'task_name' => 'material_export leave_asset_export',
        ],

        //物料资产管理-离职资产处理-导出 - 明细表
        self::MATERIAL_LEAVE_ASSETS_DETAIL_EXPORT => [
            'file_name' => '离职资产明细表&leave asses detail&รายละเอียดสินทรัพย์ ที่ออกจากงาน_{YmdHis}.xlsx',
            'task_name' => 'material_export leave_asset_export',
        ],

        //物料资产管理-离职资产处理-导出 - 明细表(在途资产)
        self::MATERIAL_LEAVE_ASSETS_TRANSFER_EXPORT => [
            'file_name' => 'leave_asset_transfer_detail_{YmdHis}.xlsx',
            'task_name' => 'material_export leave_asset_export',
        ],
        //资产退回处理-待处理-导出
        self::MATERIAL_ASSET_RETURN_WAIT_EXPORT => [
            'file_name' => 'material_asset_return_wait_export-{YmdHis}.xlsx',
            'task_name' => 'material_export return_asset_detail',
        ],

        //资产退回处理-已转交-导出
        self::MATERIAL_ASSET_RETURN_TRANSFER_EXPORT => [
            'file_name' => 'material_asset_return_transfer_export-{YmdHis}.xlsx',
            'task_name' => 'material_export return_asset_detail',
        ],

        //资产退回处理-已处理-导出
        self::MATERIAL_ASSET_RETURN_DONE_EXPORT => [
            'file_name' => 'material_asset_return_done_export-{YmdHis}.xlsx',
            'task_name' => 'material_export return_asset_detail',
        ],

        //资产退回处理-退回入库-导出
        self::MATERIAL_ASSET_RETURN_STORAGE_EXPORT => [
            'file_name' => 'material_asset_return_storage_export-{YmdHis}.xlsx',
            'task_name' => 'material_export return_asset_storage_detail',
        ],

        // 财务 - 银行流水下载
        self::DOWNLOAD_CENTER_BANK_FLOW_INIT_DOWN => [
            'file_name' => 'bank_statement_list_{YmdHis}.xlsx',
            'task_name' => 'bank_flow download_bank_flow',
        ],

        // 财务 - 银行流水-流水管理-收款流水-导出
        self::DOWNLOAD_CENTER_BANK_FLOW_GAT_EXPORT => [
            'file_name' => 'bank_get_flow_list_{YmdHis}.xlsx',
            'task_name' => 'bank_flow download_bank_get_flow',
        ],

        // 财务 - 银行流水-流水管理-付款流水-导出
        self::DOWNLOAD_CENTER_BANK_FLOW_PAY_EXPORT => [
            'file_name' => 'bank_pay_flow_list_{YmdHis}.xlsx',
            'task_name' => 'bank_flow download_bank_pay_flow',
        ],

        // 财务 - 报销数据下载
        self::FINANCE_REIMBURSEMENT_DATA => [
            'file_name' => 'reimbursement_{YmdHis}.xlsx',
            'task_name' => 'financial_export reimbursement',
        ],

        // 财务 - 报销实质-网点支援详情下载
        self::FINANCE_REIMBURSEMENT_DETAIL_STORE_SUPPORT_V2 => [
            'file_name' => 'reimbursement_support_{YmdHis}.xlsx',
            'task_name' => 'financial_export reimbursement_detail_support_v2',
        ],

        // 财务 - 普通付款数据下载
        self::FINANCE_ORDINARY_PAYMENT_DATA => [
            'file_name' => 'Ordinary_payment_{YmdHis}.xlsx',
            'task_name' => 'financial_export ordinary_payment',
        ],

        // 财务 - 仓库信息下载
        self::FINANCE_WAREHOUSE_MANAGEMENT_INFO => [
            'file_name' => 'warehouse_info_{YmdHis}.xlsx',
            'task_name' => 'financial_export warehouse_info',
        ],

        //仓库需求管理-导出
        self::FINANCE_WAREHOUSE_REQUIREMENT_EXPORT => [
            'file_name' => 'warehouse_requirement_{YmdHis}.xlsx',
            'task_name' => 'financial_export warehouse_requirement',
        ],

        //处理仓库需求-待寻找-导出
        self::FINANCE_WAREHOUSE_REQUIREMENT_SEARCH_EXPORT => [
            'file_name' => 'warehouse_requirement_search_{YmdHis}.xlsx',
            'task_name' => 'financial_export warehouse_requirement',
        ],

        //处理仓库需求-待确认-导出
        self::FINANCE_WAREHOUSE_REQUIREMENT_CONFIRM_EXPORT => [
            'file_name' => 'warehouse_requirement_confirm_{YmdHis}.xlsx',
            'task_name' => 'financial_export warehouse_requirement',
        ],

        //处理仓库需求-待入驻-导出
        self::FINANCE_WAREHOUSE_REQUIREMENT_SETTLE_EXPORT => [
            'file_name' => 'warehouse_requirement_settle_{YmdHis}.xlsx',
            'task_name' => 'financial_export warehouse_requirement',
        ],

        //处理仓库需求-待续约-导出
        self::FINANCE_WAREHOUSE_REQUIREMENT_RENEWED_EXPORT => [
            'file_name' => 'warehouse_requirement_renewed_{YmdHis}.xlsx',
            'task_name' => 'financial_export warehouse_requirement',
        ],

        //仓库线索管理-导出
        self::FINANCE_WAREHOUSE_THREAD_EXPORT => [
            'file_name' => 'warehouse_thread_{YmdHis}.xlsx',
            'task_name' => 'financial_export warehouse_thread',
        ],

        //处理仓库线索-待报价-导出
        self::FINANCE_WAREHOUSE_THREAD_PRICE_EXPORT => [
            'file_name' => 'warehouse_thread_price_{YmdHis}.xlsx',
            'task_name' => 'financial_export warehouse_thread',
        ],

        //处理仓库线索-待签约-导出
        self::FINANCE_WAREHOUSE_THREAD_SIGN_EXPORT => [
            'file_name' => 'warehouse_thread_sign_{YmdHis}.xlsx',
            'task_name' => 'financial_export warehouse_thread',
        ],


        // 组织架构 - 导出职位部门信息
        self::ORGANIZATION_JOB_DEPARTMENT_INFO => [
            'file_name' => 'Org_job_department_{YmdHis}.xlsx',
            'task_name' => 'export plan_hc_num',
        ],

        // 耗材 - 耗材申请-导出
        self::MATERIAL_WMS_APPLY => [
            'file_name' => 'material_wms_list_{YmdHis}.xlsx',
            'task_name' => 'material_export wms_apply',
        ],

        // 耗材 - 耗材领用出库-导出
        self::MATERIAL_WMS_OUT_STORAGE => [
            'file_name' => 'material_wms_out_storage_list_{YmdHis}.xlsx',
            'task_name' => 'material_export wms_out_storage',
        ],

        // 耗材 - 耗材申请-数据查询-导出
        self::MATERIAL_WMS_APPLY_DATA => [
            'file_name' => 'material_wms_list_data_{YmdHis}.xlsx',
            'task_name' => 'material_export wms_apply_data',
        ],

        // 耗材 - 包材调拨单-导出
        self::MATERIAL_WMS_PACKAGE_ALLOT => [
            'file_name' => 'material_wms_package_allot_{YmdHis}.xlsx',
            'task_name' => 'material_export wms_package_allot',
        ],

        // 耗材 - 进销存-导出
        self::MATERIAL_WMS_PACKAGE_STOCK => [
            'file_name' => 'material_wms_package_stock_{YmdHis}.xlsx',
            'task_name' => 'material_export wms_package_stock',
        ],

        // 支付模块-在线支付/我的支付-银行支付-在线支付-下载
        self::PAYMENT_ONLINE => [
            'file_name' => 'payment_online_list_{YmdHis}.xlsx',
            'task_name' => 'payment online_export',
        ],

        // 代理支付-我的申请-导出
        self::AGENCY_PAYMENT_APPLY => [
            'file_name' => 'agency_payment_apply_list_{YmdHis}.xlsx',
            'task_name' => 'financial_export agency_payment',
        ],
        // 代理支付-我的申请-查看-付款明细-导出
        self::AGENCY_PAYMENT_APPLY_DETAIL_EXPORT => [
            'file_name' => 'agency_payment_apply_detail_list_{YmdHis}.xlsx',
            'task_name' => 'financial_export agency_payment',
        ],
        // 代理支付-我的审核-导出
        self::AGENCY_PAYMENT_AUDIT => [
            'file_name' => 'agency_payment_audit_list_{YmdHis}.xlsx',
            'task_name' => 'financial_export agency_payment',
        ],
        // 代理支付-我的审核-查看-付款明细-导出
        self::AGENCY_PAYMENT_AUDIT_DETAIL_EXPORT => [
            'file_name' => 'agency_payment_audit_detail_list_{YmdHis}.xlsx',
            'task_name' => 'financial_export agency_payment',
        ],
        // 代理支付-代理支付-导出
        self::AGENCY_PAYMENT_PAY => [
            'file_name' => 'agency_payment_pay_list_{YmdHis}.xlsx',
            'task_name' => 'financial_export agency_payment',
        ],
        // 代理支付-代理支付-查看-付款明细-导出
        self::AGENCY_PAYMENT_PAY_DETAIL_EXPORT => [
            'file_name' => 'agency_payment_pay_detail_list_{YmdHis}.xlsx',
            'task_name' => 'financial_export agency_payment',
        ],
        // 代理支付-数据查询-导出
        self::AGENCY_PAYMENT_DATA => [
            'file_name' => 'agency_payment_data_list_{YmdHis}.xlsx',
            'task_name' => 'financial_export agency_payment',
        ],
        // 代理支付-数据查询-查看-付款明细-导出
        self::AGENCY_PAYMENT_DATA_DETAIL_EXPORT => [
            'file_name' => 'agency_payment_data_detail_list_{YmdHis}.xlsx',
            'task_name' => 'financial_export agency_payment',
        ],

        // 员工商城-员工工服尺码-导出
        self::INTERIOR_STAFF_ENTRY_SIZE => [
            'file_name' => 'interior_staff_entry_size_data_{YmdHis}.xlsx',
            'task_name' => 'interior_export entry_size_data',
        ],
        // 合同管理-电子合同制作-导出
        self::CONTRACT_ELECTRONIC_EXPORT => [
            'file_name' => 'contract_electronic_list_{YmdHis}.xlsx',
            'task_name' => 'financial_export contract_electronic',
        ],
        // 合同管理-数据查询-其他合同-电子合同列表-导出
        self::CONTRACT_ELECTRONIC_EXPORT_ALL => [
            'file_name' => 'contract_electronic_all_list_{YmdHis}.xlsx',
            'task_name' => 'financial_export contract_electronic',
        ],
        // 合同管理-数据查询-其他合同-导出
        self::CONTRACT_OTHER_EXPORT => [
            'file_name' => 'contract_other_list_{YmdHis}.xlsx',
            'task_name' => 'financial_export contractOtherExport',
        ],
        // 薪酬扣款审批-数据查询-导出
        self::WAGES_PAYMENT_APPROVAL_EXPORT => [
            'file_name' => 'Wages_{YmdHis}.xlsx',
            'task_name' => 'wages dataExport',
        ],

        // 转岗管理-数据查询-导出
        self::JOB_TRANSFER_DATA_EXPORT => [
            'file_name' => 'Transfer_{YmdHis}.xlsx',
            'task_name' => 'export job_transfer_list',
        ],
        // 转岗管理-数据查询-导出payroll
        self::JOB_TRANSFER_DATA_FOR_PAYROLL_EXPORT => [
            'file_name' => 'Transfer_payroll_{YmdHis}.xlsx',
            'task_name' => 'export job_transfer_payroll_list',
        ],
        //SIM卡管理
        self::COMPANY_MOBILE_EXPORT=>[
            'file_name' => 'SIM_call_number_{YmdHis}.xlsx',
            'task_name' => 'sim_manage companyMobileExport',
        ],
         //SIM卡池管理
        self::COMPANY_MOBILE_SIM_EXPORT=>[
            'file_name' => 'SIM_SN_number_{YmdHis}.xlsx',
            'task_name' => 'sim_manage simCardExport',
        ],

        //网点租房付款-数据查询-批量下载
        self::PAYMENT_STORE_RENTING_BATCH_DOWNLOAD=>[
            'file_name' => 'rental_paymenyt_batch_download_{YmdHis}.zip',
            'task_name' => 'financial_export payment_store_renting_batch_download',
        ],
        
        //SIM管理 异常反馈
        self::COMPANY_MOBILE_FEEDBACK_EXPORT=>[
            'file_name' => 'SIM_feedback_number_{YmdHis}.xlsx',
            'task_name' => 'sim_manage feedbackNumberExport',
        ],

        // 预算管理-费用预提-导出
        self::BUDGET_WITHHOLDING_EXPORT => [
            'file_name' => 'budget_withholding_export_{YmdHis}.xlsx',
            'task_name' => 'financial_export budget_withholding',
        ],

    ];
}
