<?php
namespace App\Library\Enums;

/**
 * SCM相关枚举文件
 * Class enums
 */
final class ScmEnums
{
    //scm出库状态
    const OUT_BOUND_ORDER_STATUS_CANCEL = 10;//已作废
    const OUT_BOUND_ORDER_STATUS_DRAFT = 20;//草稿
    const OUT_BOUND_ORDER_STATUS_AUDIT = 30;//待审核
    const OUT_BOUND_ORDER_STATUS_REJECT_AUDIT = 40;//审核不过
    const OUT_BOUND_ORDER_STATUS_FINISH = 50;//已审核
    const OUT_BOUND_ORDER_STATUS_ALLOCATION = 60;//库存分配失败
    const OUT_BOUND_ORDER_STATUS_OUTBOUND_ING = 70;//出库中
    const OUT_BOUND_ORDER_STATUS_PICKING = 80;//拣货完成
    const OUT_BOUND_ORDER_STATUS_PACK  = 90;//已打包
    const OUT_BOUND_ORDER_STATUS_DELIVERED = 100;//已出库
    const OUT_BOUND_ORDER_STATUS_COMPLETED = 110;//已完成

    //出库状态
    public static $scm_out_status = [
        self::OUT_BOUND_ORDER_STATUS_CANCEL => 'scm_storage_out_status.' . self::OUT_BOUND_ORDER_STATUS_CANCEL,
        self::OUT_BOUND_ORDER_STATUS_DRAFT => 'scm_storage_out_status.' . self::OUT_BOUND_ORDER_STATUS_DRAFT,
        self::OUT_BOUND_ORDER_STATUS_AUDIT => 'scm_storage_out_status.' . self::OUT_BOUND_ORDER_STATUS_AUDIT,
        self::OUT_BOUND_ORDER_STATUS_REJECT_AUDIT => 'scm_storage_out_status.' . self::OUT_BOUND_ORDER_STATUS_REJECT_AUDIT,
        self::OUT_BOUND_ORDER_STATUS_FINISH => 'scm_storage_out_status.' . self::OUT_BOUND_ORDER_STATUS_FINISH,
        self::OUT_BOUND_ORDER_STATUS_ALLOCATION => 'scm_storage_out_status.' . self::OUT_BOUND_ORDER_STATUS_ALLOCATION,
        self::OUT_BOUND_ORDER_STATUS_OUTBOUND_ING => 'scm_storage_out_status.' . self::OUT_BOUND_ORDER_STATUS_OUTBOUND_ING,
        self::OUT_BOUND_ORDER_STATUS_PICKING => 'scm_storage_out_status.' . self::OUT_BOUND_ORDER_STATUS_PICKING,
        self::OUT_BOUND_ORDER_STATUS_PACK => 'scm_storage_out_status.' . self::OUT_BOUND_ORDER_STATUS_PACK,
        self::OUT_BOUND_ORDER_STATUS_DELIVERED => 'scm_storage_out_status.' . self::OUT_BOUND_ORDER_STATUS_DELIVERED,
        self::OUT_BOUND_ORDER_STATUS_COMPLETED     => 'scm_storage_out_status.' . self::OUT_BOUND_ORDER_STATUS_COMPLETED,
    ];
}
