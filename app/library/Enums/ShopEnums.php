<?php
/**
 * Created by PhpStorm.
 * Date: 2021/10/22
 * Time: 10:58
 */

namespace App\Library\Enums;

use App\Library\BaseService;

/**
 * 商城枚举配置
 * Class enums
 */
final class ShopEnums
{
    //订单状态
    const ORDER_STATUS_WARITING_SUBMIT_CODE = 0;     //(待支付，已经下单，待提交)
    const ORDER_STATUS_WARTING_SEND_CODE = 1;        //1待发货
    const ORDER_STATUS_PRE_SALE_CODE = 2;            //2预定中
    const ORDER_STATUS_SEND_CODE = 3;                //已发货（待收货）
    const ORDER_STATUS_CUSTOMER_RECEIVED_CODE = 4;   //已完成（已签收）
    const ORDER_STATUS_CUSTOMER_CANCEL_CODE = 5;     //已取消
    const ORDER_STATUS_WAIT_PAY_CODE = 6;            //待付款
    const ORDER_STATUS_PAYING_CODE = 7;              //支付中
    const ORDER_STATUS_SYSTEM_CANCEL_CODE = 8;       //系统自动取消（下订单之后的1小时不支付的系统自动取消）


    const OUT_STATUS_SUCCEED = 1;   //出库单状态成功
    const OUT_STATUS_FAIL = 2;      //出库单状态失败

    const ORDER_STATUS_AUTO_RECEIVED_DAYS = 7;   //自动收货时间

    const PAY_METHOD_WAGE_DEDUCTION = 1;//工资抵扣
    const PAY_METHOD_FLASH_PAY = 2;     //FlashPay在线支付
    const PAY_METHOD_OFFLINE_PAY = 3;   //线下支付


    const ORDER_TYPE_FREE = 1;//免费
    const ORDER_TYPE_PAY = 2; //自费

    const FUND_STATUS_INIT = 0;     //初始状态
    const FUND_STATUS_REFUNDING = 1;//退款中
    const FUND_STATUS_DONE = 2;     //退款成功

    const FUND_TYPE = 1;                                           //手工确认
    const FUND_TYPE_BATCH = 2;                                     //批量确认

    //员工商城退款凭证oss类型
    const INTERIOR_ORDER_REFUND_OSS_TYPE = 'INTERIOR_ORDER_REFUND';
    //汇总列表最大导出数量限制
    const ORDER_SUMMARY_DOWNLOAD_LIMIT = 300000;

    // 商品状态
    const GOODS_STATUS_ON_SALE = 1;                                //  在售
    const GOODS_STATUS_HALT_SALE = 0;                              // 停售
    public static $goods_status_item = [
        self::GOODS_STATUS_ON_SALE   => 'mall_goods_sale_state_1',
        self::GOODS_STATUS_HALT_SALE => 'mall_goods_sale_state_0',
    ];

    // 商品相关的Env配置code
    const LIMIT_BUY_GOODS_IDS_CODE = 'interior_buy_limit_goods_id';// 限购商品的spu ids
    const LIMIT_BUY_NUM_CODE = 'interior_buy_limit_num';           // 每人每月限购的件数
    const PAY_HANDLING_FEE = 'interior_flash_pay_handling_fee';    // pay支付手续费

    // 限购状态
    const LIMIT_BUY_STATUS_YES = 1;
    const LIMIT_BUY_STATUS_NO = 0;
    public static $limit_buy_status_item = [
        self::LIMIT_BUY_STATUS_YES => 'mall_limit_buy_state_1',
        self::LIMIT_BUY_STATUS_NO  => 'mall_limit_buy_state_0',
    ];

    // 免费状态
    const FREE_BUY_STATUS_YES = 1;
    const FREE_BUY_STATUS_NO = 0;
    public static $free_buy_status_item = [
        self::FREE_BUY_STATUS_YES => 'mall_free_buy_state_1',
        self::FREE_BUY_STATUS_NO  => 'mall_free_buy_state_0',
    ];


    const GOODS_TYPE_WORK_CLOTHES = 1;   // 工服
    const GOODS_TYPE_UNCLAIMED_PIECE = 2;// 无头件
    public static $goods_type = [
        self::GOODS_TYPE_WORK_CLOTHES    => 'goods_type_work_clothes',
        self::GOODS_TYPE_UNCLAIMED_PIECE => 'goods_type_unclaimed_piece',
    ];

    const GOODS_PAYMENT_VOUCHER_STATUS_ING = 1;     //待上传支付凭证
    const GOODS_PAYMENT_VOUCHER_STATUS_ABNORMAL = 2;//支付凭证异常
    const GOODS_PAYMENT_VOUCHER_STATUS_UPLOADED = 3;//已上传支付凭证
    public static $payment_voucher_status = [
        self::GOODS_PAYMENT_VOUCHER_STATUS_ING      => 'goods_payment_voucher_status_ing',
        self::GOODS_PAYMENT_VOUCHER_STATUS_ABNORMAL => 'goods_payment_voucher_status_abnormal',
        self::GOODS_PAYMENT_VOUCHER_STATUS_UPLOADED => 'goods_payment_voucher_status_uploaded',
    ];

    public static function getOrderStatus($lang = '')
    {
        return [
            self::ORDER_STATUS_WARTING_SEND_CODE      => BaseService::getTranslation($lang)->t('shop_order_status_deliver_goods'),
            self::ORDER_STATUS_PRE_SALE_CODE          => BaseService::getTranslation($lang)->t('shop_order_status_pre_sale'),
            self::ORDER_STATUS_SEND_CODE              => BaseService::getTranslation($lang)->t('shop_order_status_send'),
            self::ORDER_STATUS_CUSTOMER_RECEIVED_CODE => BaseService::getTranslation($lang)->t('shop_order_status_customer_received'),
            self::ORDER_STATUS_CUSTOMER_CANCEL_CODE   => BaseService::getTranslation($lang)->t('shop_order_status_customer_cancel'),
            self::ORDER_STATUS_WAIT_PAY_CODE          => BaseService::getTranslation($lang)->t('shop_order_status_wait_pay'),
            self::ORDER_STATUS_PAYING_CODE            => BaseService::getTranslation($lang)->t('shop_order_status_paying'),
            self::ORDER_STATUS_SYSTEM_CANCEL_CODE     => BaseService::getTranslation($lang)->t('shop_order_status_system_cancel'),
        ];
    }

    public static function payMethod($lang = '')
    {
        return [
            self::PAY_METHOD_WAGE_DEDUCTION => BaseService::getTranslation($lang)->t('pay_method_wage_deduction'),
            self::PAY_METHOD_FLASH_PAY      => BaseService::getTranslation($lang)->t('pay_method_flash_pay'),
            self::PAY_METHOD_OFFLINE_PAY    => BaseService::getTranslation($lang)->t('pay_method_offline_pay'),
        ];
    }

    /**
     * 订单类型
     * @param string $lang
     * @return array
     */
    public static function orderType($lang = '')
    {
        return [
            self::ORDER_TYPE_FREE => BaseService::getTranslation($lang)->t('order_type_free'),
            self::ORDER_TYPE_PAY  => BaseService::getTranslation($lang)->t('order_type_pay'),
        ];
    }

    /**
     * 退款款项状态
     * @param string $lang
     * @return array
     */
    public static function fundStatus($lang = '')
    {
        return [
            self::FUND_STATUS_REFUNDING => BaseService::getTranslation($lang)->t('fund_status_refunding'),
            self::FUND_STATUS_DONE      => BaseService::getTranslation($lang)->t('fund_status_done'),
        ];
    }

    /**
     * 退款记录确认方式
     * @param string $lang
     * @return array
     */
    public static function fundType($lang = '')
    {
        return [
            self::FUND_TYPE       => BaseService::getTranslation($lang)->t('fund_type_one'),
            self::FUND_TYPE_BATCH => BaseService::getTranslation($lang)->t('fund_type_batch'),
        ];
    }


    //批量下单工服申请状态
    const BATCH_ORDER_APPLY_STATUS_VERIFY = 1;      //待核对
    const BATCH_ORDER_APPLY_STATUS_PASS = 2;        //核对通过
    const BATCH_ORDER_APPLY_STATUS_REPEAL = 3;      //已作废
    const BATCH_ORDER_APPLY_STATUS_CANCEL = 4;      //取消发放
    public static $batch_order_apply_status_arr = [
        self::BATCH_ORDER_APPLY_STATUS_VERIFY => 'batch_order_apply_status_verify',
        self::BATCH_ORDER_APPLY_STATUS_PASS   => 'batch_order_apply_status_pass',
        self::BATCH_ORDER_APPLY_STATUS_REPEAL => 'batch_order_apply_status_repeal',
        self::BATCH_ORDER_APPLY_STATUS_CANCEL => 'batch_order_apply_status_cancel',
    ];
    //OA-员工商城-批量工服确认-核对
    public static $batch_order_apply_check_status_arr = [
        self::BATCH_ORDER_APPLY_STATUS_PASS   => 'batch_order_apply_status_pass',
        self::BATCH_ORDER_APPLY_STATUS_CANCEL => 'batch_order_apply_status_cancel',
    ];

    //来源
    const BATCH_ORDER_APPLY_SOURCE_BY = 1;          //by到岗确认
    const BATCH_ORDER_APPLY_SOURCE_OA = 2;          //OA手工创建
    public static $batch_order_apply_source_arr = [
        self::BATCH_ORDER_APPLY_SOURCE_BY => 'batch_order_apply_source_by',
        self::BATCH_ORDER_APPLY_SOURCE_OA => 'batch_order_apply_source_oa',
    ];

    //是否补录数据
    const BATCH_ORDER_IS_REISSUE_YES = 1;
    const BATCH_ORDER_IS_REISSUE_NO = 2;
    public static $batch_order_is_reissue = [
        self::BATCH_ORDER_IS_REISSUE_YES => 'batch_order_is_reissue_yes',//是
        self::BATCH_ORDER_IS_REISSUE_NO  => 'batch_order_is_reissue_no', // 否
    ];

    const BATCH_ORDER_CHECK_TYPE_WAIT = 1;    //待核对
    const BATCH_ORDER_CHECK_TYPE_FINISH = 2;  //已核对

    //批量下单工服申请单编号前缀
    const INTERIOR_BATCH_ORDER_APPLY_NO_PREFIX = 'BGF';


    //是否补录数据
    const BATCH_IS_ORDER_STATUS_SUCCESS = 1;  //成功
    const BATCH_IS_ORDER_STATUS_FAIL = 2;     //失败
    const BATCH_IS_ORDER_STATUS_NOT_ORDER = 3;//未下单
    public static $batch_is_order_status = [
        self::BATCH_IS_ORDER_STATUS_SUCCESS   => 'batch_is_order_status_success',
        self::BATCH_IS_ORDER_STATUS_FAIL      => 'batch_is_order_status_fail',
        self::BATCH_IS_ORDER_STATUS_NOT_ORDER => 'batch_is_order_status_not_order',
    ];

    const INTERIOR_BATCH_ORDER_APPLY_DETAIL_MAX_NUM = 500; //详情最大覆盖导入限制
    const INTERIOR_BATCH_ORDER_MAX_NUM = 50000;            //列表批量导出最大限制

    const INTERIOR_BATCH_PAGE_SOURCE_REPEAL = 1; //从批量下单工服入口进去作废
    const INTERIOR_BATCH_PAGE_SOURCE_CANCEL = 2; //从批量工服审核入口进去取消

    const INTERIOR_GOODS_PURCHASE_TYPE_PURCHASE = 1;//购买权限
    const INTERIOR_GOODS_PURCHASE_TYPE_FREE = 2;    //免费权限

    const DATA_PERMISSION_ALL_RES_STATUS = 1;       //全量用户
    const DATA_PERMISSION_DEPARTMENT_RES_STATUS = 2;//管辖部门数据
    const DATA_PERMISSION_ALL_STORE_STATUS = 3;     //符合网点


    //支付凭证审核状态 1待核对 2通过 3不通过
    const PAYMENT_VOUCHER_AUDIT_STATUS_WAIT = 1;    //待核对
    const PAYMENT_VOUCHER_AUDIT_STATUS_FINISH = 2;  //通过
    const PAYMENT_VOUCHER_AUDIT_STATUS_FAIL = 3;    //不通过
    public static $payment_voucher_audit_status = [
        self::PAYMENT_VOUCHER_AUDIT_STATUS_WAIT   => 'payment_voucher_audit_status_wait',
        self::PAYMENT_VOUCHER_AUDIT_STATUS_FINISH => 'payment_voucher_audit_status_finish',
        self::PAYMENT_VOUCHER_AUDIT_STATUS_FAIL   => 'payment_voucher_audit_status_fail',
    ];

    //是否自动定价1是，0否
    const IS_AUTO_PRICE_NO = 0;
    const IS_AUTO_PRICE_YES = 1;
    public static $is_auto_price = [
        self::IS_AUTO_PRICE_NO  => 'view_no',
        self::IS_AUTO_PRICE_YES => 'view_yes',
    ];

    // 商城后台管理-无头件每次最多导入行数
    const SHOP_GOODS_HEADLESS_MAX_IMPORT_LIMIT = 1000;

}
