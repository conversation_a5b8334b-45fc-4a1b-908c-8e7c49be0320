<?php
namespace App\Library\Enums;

use App\Library\Enums;

/**
 * 代理支付版块相关枚举定义
 * Class enums
 */
final class AgencyPaymentEnums
{
    const DOWNLOAD_LIMIT = 10000;//导出限制条数
    const LIST_TYPE_WAIT_HANDLE = 1;//待处理
    const LIST_TYPE_HAD_HANDLE = 2;//待处理
    const APPLY_IS_SUBMIT_NO = 0;//是否提交-否
    const APPLY_IS_SUBMIT_YES = 1;//是否提交-是
    const SOURCE_TYPE_ADD = 1;//手工新增
    const SOURCE_TYPE_API = 2;//接口传输
    const IS_FINAL_YES = 1;//接口传输是否终态组，1是
    const DETAIL_ONE_ADD_MAX_NUM = 1000;//付款明细单次新增最多条数
    const DETAIL_MAX_NUM = 5000;//单个批次下付款明细最多条数
    const DETAIL_BATCH_SAVE_EXCEL_RESULT = 17;//付款明细行导入新增excel结果列索引值
    const DETAIL_BATCH_PAY_EXCEL_RESULT = 6;//付款明细行导入支付excel结果列索引值
    const AMOUNT_RULE = '/^\d+(?:\.\d{1,2})?$/';
    const DATE_RULE = '/\d{2}[-\/]\d{2}[-\/]\d{4}/';

    //申请状态：-1暂存（系统性），0待提交，1待审核，2拒绝（驳回），3同意，4撤回
    const AGENCY_PAYMENT_STATUS_STAGING = -1;//暂存
    const AGENCY_PAYMENT_STATUS_WAIT_SUBMIT = 0; //待提交
    public static $agency_payment_status = [
        self::AGENCY_PAYMENT_STATUS_WAIT_SUBMIT => 'agency_payment_status.0',
        Enums::WF_STATE_PENDING => 'agency_payment_status.1',
        Enums::WF_STATE_REJECTED => 'agency_payment_status.2',
        Enums::WF_STATE_APPROVED => 'agency_payment_status.3',
        Enums::WF_STATE_CANCEL => 'agency_payment_status.4',
    ];

    //支付状态
    const AGENCY_PAYMENT_PAY_STATUS_PENDING = 1; //待支付
    const AGENCY_PAYMENT_PAY_STATUS_PAY = 2;//已支付
    const AGENCY_PAYMENT_PAY_STATUS_NOTPAY = 3;//未支付
    const AGENCY_PAYMENT_PAY_STATUS_ING = 4;//支付中
    const AGENCY_PAYMENT_PAY_STATUS_DEPART = 5;//部分支付
    public static $agency_payment_pay_status = [
        self::AGENCY_PAYMENT_PAY_STATUS_PENDING => 'agency_payment_pay_status.1',
        self::AGENCY_PAYMENT_PAY_STATUS_PAY => 'agency_payment_pay_status.2',
        self::AGENCY_PAYMENT_PAY_STATUS_NOTPAY => 'agency_payment_pay_status.3',
        self::AGENCY_PAYMENT_PAY_STATUS_ING => 'agency_payment_pay_status.4',
        self::AGENCY_PAYMENT_PAY_STATUS_DEPART => 'agency_payment_pay_status.5'
    ];
    //进入支付模块的单据是否已推送，0未推送，1已推送
    const AGENCY_PAYMENT_IS_PUSH_PAY_MODULE_DEFAULT = 0;
    const AGENCY_PAYMENT_IS_PUSH_PAY_MODULE_SUCCESS = 1;

    //是否支付，1是，2否
    const IS_PAY_YES = 1;
    const IS_PAY_NO = 2;
}
