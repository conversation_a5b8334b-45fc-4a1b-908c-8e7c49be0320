<?php
namespace App\Library\Enums;
/**
 * 普通付款常用枚举数据
 * Class enums
 */
final class OrdinaryPaymentEnums
{

    /**
     * @Message ('补充附件类型')
     */
    const OSS_BUCKET_TYPE_ORDINARY_PAYMENT_ATTACHMENT_FILE = 31;

    /**
     * @Message ('先款后票且需要补充发票')
     */
    const IS_SUPPLEMENT_INVOICE_DEFAULT = 0;
    const IS_SUPPLEMENT_INVOICE_YES = 1;
    const IS_SUPPLEMENT_INVOICE_NO = 2;
    public static $supplement_invoice_status = [
        self::IS_SUPPLEMENT_INVOICE_DEFAULT => 'supplement_invoice_status_default',
        self::IS_SUPPLEMENT_INVOICE_YES => 'supplement_invoice_status_yes',
        self::IS_SUPPLEMENT_INVOICE_NO => 'supplement_invoice_status_no'
    ];

    const ID_DEFAULT_ZERO = 0;//获取普通付款env配置里面的第一列
    const ID_DEFAULT_ONE = 1;//获取普通付款审批流配置env配置里面的第一列
    const ID_DEFAULT_TWO = 2;//获取普通付款审批流配置env配置里面的第二列
    const ID_DEFAULT_THREE = 3;//获取普通付款审批流配置env配置里面的第三列

    const BELONGTOSTORE_STORE_IDS = [1,2,10,14,13,4,5,7,8,9,12];//普通付款对Network Operations部门、Network Bulky、Shop、Hub这些部门对逻辑处理

    const FLOW_ID_ARRAY_KEY = ['headquarters'=>[-1],'hub'=>[8,9,12],'network'=>[1,2,14],'shop'=>[4,5,7],'network_bulky'=>[10,13]];//普通付款需求映射关系 根据网点类型 映射所属部门

    const FLOW_ID_LA_ARRAY_KEY = ['headquarters'=>[-1,10,13,4,5,7],'hub'=>[8,9,12],'network'=>[1,2]];//普通付款老挝需求映射关系 根据网点类型 映射所属部门

    const FLOW_ID_PH_ARRAY_KEY = ['headquarters'=>[-1],'hub'=>[8,9,12],'network'=>[1,2,10,13,14]];//普通付款菲律宾需求映射关系 根据网点类型 映射所属部门

    //是否保理付款：0-默认空/未选择过，1否，2是
    const IS_FACTORING_DEFAULT = 0;
    const IS_FACTORING_NO = 1;
    const IS_FACTORING_YES = 2;

    //清关状态
    const QUERY_CLEARANCE_QUEST_NO = 0; //不清关
    const QUERY_CLEARANCE_QUEST_YES = 1; //清关
    const QUERY_CLEARANCE_QUEST_NULL = 2; //空

    public static $clearance_state = [
        self::QUERY_CLEARANCE_QUEST_NO => 'is_vendor_no',
        self::QUERY_CLEARANCE_QUEST_YES => 'is_vendor_yes',
        self::QUERY_CLEARANCE_QUEST_NULL => '',
    ];
}
