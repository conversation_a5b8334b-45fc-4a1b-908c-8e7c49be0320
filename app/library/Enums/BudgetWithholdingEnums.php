<?php

namespace App\Library\Enums;

use App\Library\Enums;

/**
 * 预算费用预提统常用枚举数据
 * Class enums
 */
final class BudgetWithholdingEnums
{
    const BUDGET_WITHHOLDING_NO_PREFIX = 'YT';
    //使用状态：1-未使用、2-已使用
    const USE_STATUS_UNUSED = 1;
    const USE_STATUS_USED = 2;
    const USE_STATUS_VALIDATE = self::USE_STATUS_UNUSED . ',' . self::USE_STATUS_USED;
    public static $use_status = [
        self::USE_STATUS_UNUSED => 'budget_withholding_use_status.' . self::USE_STATUS_UNUSED,
        self::USE_STATUS_USED   => 'budget_withholding_use_status.' . self::USE_STATUS_USED,
    ];

    //预提状态：1-待审核、2-已驳回、3-已通过、 4-已撤回、5-已关闭
    const STATUS_CLOSED = 5;
    public static $status = [
        Enums::WF_STATE_PENDING  => 'budget_withholding_status.' . Enums::WF_STATE_PENDING,
        Enums::WF_STATE_REJECTED => 'budget_withholding_status.' . Enums::WF_STATE_REJECTED,
        Enums::WF_STATE_APPROVED => 'budget_withholding_status.' . Enums::WF_STATE_APPROVED,
        Enums::WF_STATE_CANCEL   => 'budget_withholding_status.' . Enums::WF_STATE_CANCEL,
        self::STATUS_CLOSED      => 'budget_withholding_status.' . self::STATUS_CLOSED,
    ];

    //费用是否拆分到网点：1无法拆分、2拆分到网点
    const IS_SPLIT_NO = 1;
    const IS_SPLIT_YES = 2;
    const IS_SPLIT_VALIDATE = self::IS_SPLIT_NO . ',' . self::IS_SPLIT_YES;
    public static $is_split = [
        self::IS_SPLIT_NO  => 'budget_withholding_is_split.' . self::IS_SPLIT_NO,
        self::IS_SPLIT_YES => 'budget_withholding_is_split.' . self::IS_SPLIT_YES,
    ];

    //关闭时是否抛红冲凭证：0否，1是
    const IS_SEND_RED_VOUCHER_NO = 0;
    const IS_SEND_RED_VOUCHER_YES = 1;

    //否允许当月费用不关联预提单：1允许，2不允许；
    const ALLOW_CURRENT_MONTH_YES = 1;
    const ALLOW_CURRENT_MONTH_NO = 2;

}
