<?php
namespace App\Library\Enums;
/**
 * 物料/资产管理-通用设置枚举数据
 * Class enums
 */
final class MaterialSettingEnums
{
    const PC_CODE_IS_CHECK_NO = 0;
    const PC_CODE_IS_CHECK_YES = 1;

    const STORE_STORAGE_TYPE_REGION = 1;//分仓类型：1大区
    const STORE_STORAGE_TYPE_PIECE = 2;//分仓类型：2片区
    const STORE_STORAGE_TYPE_STORE = 3;//分仓类型：3网点

    //分仓类型
    public static $material_store_storage_type = [
        self::STORE_STORAGE_TYPE_REGION => 'material_store_storage_type.1',
        self::STORE_STORAGE_TYPE_PIECE => 'material_store_storage_type.2',
        self::STORE_STORAGE_TYPE_STORE => 'material_store_storage_type.3'
    ];

    const  STORE_STORAGE_OPERATE_TYPE_ADD = 1;//分仓规则-新建操作
    const  STORE_STORAGE_OPERATE_TYPE_SAVE = 2;//分仓规则-编辑操作

    //分仓网点是否开启
    const STORE_STORAGE_CLOSE = 0;//未开启
    const STORE_STORAGE_OPEN = 1;//开启

    //覆盖导入-最大限制数量
    const STORE_STORAGE_IMPORT_LIMIT = 500;
}
