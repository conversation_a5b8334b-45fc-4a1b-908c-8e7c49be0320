<?php

namespace App\Library\Enums;

final class ImportCenterEnums
{
    // 类型
    const TYPE_BARCODE_ADD = 1; // barcode批量导入新增
    const TYPE_BARCODE_UPDATE = 2; // barcode批量导入更新
    const TYPE_HC_PLAN_NUMBER = 3; // 导入HC计划人数
    const TYPE_ASSET_ACCOUNT_SCRAP_UPDATE = 4;  //资产台账批量报废
    const TYPE_ASSET_ACCOUNT_FINANCE_UPDATE = 5; //资产台导入修改财务
    const TYPE_LEAVE_ASSET_BATCH_EDIT = 7; //离职资产-资产部处理-批量编辑
    const TYPE_SALARY_DEDUCT = 6;//薪资抵扣上传实扣金额
    const TYPE_MATERIAL_WMS_AUDIT_UPDATE = 8; //耗材批量审核
    const TYPE_MATERIAL_ASSET_AUDIT = 10;//资产领用申请-批量审核
    const TYPE_MATERIAL_ASSET_OUT_STORAGE_ADD = 11;//资产领用出库-导入新增
    const TYPE_MATERIAL_STORE_STORAGE_ADD = 9;//分仓规则-覆盖导入
    const TYPE_JOB_TRANSFER_BATCH_UPLOAD = 12; //批量上传特殊转岗
    const TYPE_DEPOSIT_BATCH_FORWARD = 13;//押金管理-数据查询-批量转交
    const TYPE_SIM_COMPANY_MOBILE_ADD = 14;//SIM卡管理-手机号码导入-新增
    const TYPE_SIM_COMPANY_MOBILE_EDIT = 15;//SIM卡管理-手机号码导入-修改
    const TYPE_SIM_CARD_ADD = 16;//SIM卡管理-SIM卡-新增
    const TYPE_SIM_CARD_EDIT = 17;//SIM卡管理-SIM卡-导入
    const TYPE_JOB_POSITION = 20;//上传职位性质
    const TYPE_SHOP_GOODS_HEADLESS = 21;// 员工商城商品批量导入(无头件)

    const TYPE_PAYMENT_STORE_RENTING_IMPORT = 22;//网点租房付款 - 我的申请 - 创建 - 批量导入数据
    const TYPE_MATERIAL_WMS_BATCH_ADD = 23;// 物料/资产管理 - 耗材申请 - 批量新增
    const TYPE_SIM_COMPANY_MOBILE_FEEDBACK_EDIT = 24;////SIM卡管理-异常反馈导入修改

    const TYPE_BANK_FLOW_INIT_UPLOAD = 25; // 银行流水管理-流水上传
    const TYPE_BANK_FLOW_PAY_FLOW_UPLOAD_EXPENSE = 26; // 银行流水管理-流水管理-付款流水-上传费用类型
    const TYPE_BANK_FLOW_GET_FLOW_UPLOAD_EXPENSE = 27; // 银行流水管理-流水管理-收款流水-上传费用类型

    const TYPE_WAREHOUSE_REQUIREMENT = 28;// 仓库管理-仓库需求-导入


    // 状态
    const STATUS_WAITING = 0; // 待处理
    const STATUS_DOING = 1; // 处理中
    const STATUS_DONE = 2; // 处理成功
    const STATUS_FAILED = 3; // 处理失败

    // 是否删除
    const DELETED = 1; // 已删除
    const NOT_DELETED = 0; // 未删除

    // 导入方式
    const IMPORT_METHOD_SYNC = 1;// 同步导入
    const IMPORT_METHOD_ASYNC = 2;// 异步导入

    // 调用方式
    const CALL_METHOD_METHOD_API = 'api';// 接口api调用
    const CALL_METHOD_METHOD_TASK = 'task';// 任务脚本调用

    // 任务描述
    public static $task_remark = [
        self::TYPE_BARCODE_ADD                  => 'import.center.type.barcode.add',
        self::TYPE_BARCODE_UPDATE               => 'import.center.type.barcode.update',
        self::TYPE_ASSET_ACCOUNT_SCRAP_UPDATE   => 'import_type_asset_account_scrap_update',
        self::TYPE_ASSET_ACCOUNT_FINANCE_UPDATE => 'import_type_asset_account_finance_update',
        self::TYPE_MATERIAL_WMS_AUDIT_UPDATE    => 'import_material_wms_audit_update',//耗材批量审核
        self::TYPE_LEAVE_ASSET_BATCH_EDIT => 'import_type_leave_asset_batch_edit',
        self::TYPE_SALARY_DEDUCT                => 'import.center.type.salary_deduct_import',
        self::TYPE_MATERIAL_ASSET_AUDIT => 'import.center.type.asset_batch_audit',//资产领用申请-批量审核
        self::TYPE_MATERIAL_ASSET_OUT_STORAGE_ADD => 'import.center.type.asset_out_storage',//资产领用出库-导入新增
        self::TYPE_MATERIAL_STORE_STORAGE_ADD => 'import.center.type.material_store_storage_import',//分仓规则-覆盖导入
        self::TYPE_DEPOSIT_BATCH_FORWARD => 'import.center.type.deposit_batch_forward',//押金管理-数据查询-批量转交
        self::TYPE_SHOP_GOODS_HEADLESS => 'import_center_type_' . self::TYPE_SHOP_GOODS_HEADLESS,//员工商城-商品后台管理-无头件批量导入
        self::TYPE_PAYMENT_STORE_RENTING_IMPORT => 'import.center.type.payment_store_renting_import',//网点租房付款 - 我的申请 - 创建 - 批量导入数据
        self::TYPE_MATERIAL_WMS_BATCH_ADD => 'import_center_type_' . self::TYPE_MATERIAL_WMS_BATCH_ADD,// 物料/资产管理 - 耗材申请 - 批量新增

        self::TYPE_BANK_FLOW_INIT_UPLOAD => 'import_center_type_' . self::TYPE_BANK_FLOW_INIT_UPLOAD,
        self::TYPE_BANK_FLOW_PAY_FLOW_UPLOAD_EXPENSE => 'import_center_type_' . self::TYPE_BANK_FLOW_PAY_FLOW_UPLOAD_EXPENSE,
        self::TYPE_BANK_FLOW_GET_FLOW_UPLOAD_EXPENSE => 'import_center_type_' . self::TYPE_BANK_FLOW_GET_FLOW_UPLOAD_EXPENSE,

        self::TYPE_WAREHOUSE_REQUIREMENT => 'import_center_type_' . self::TYPE_WAREHOUSE_REQUIREMENT,

    ];

    // 相关导入模块读取文件的类型设置
    public static $task_excel_columns_type_config = [
        // 员工商城-商品管理-批量导入(无头件)
        self::TYPE_SHOP_GOODS_HEADLESS => [
            0 => \Vtiful\Kernel\Excel::TYPE_STRING,// 商品编号
            1 => \Vtiful\Kernel\Excel::TYPE_STRING,// 商品中文名称
            2 => \Vtiful\Kernel\Excel::TYPE_STRING,// 商品英文名称
            3 => \Vtiful\Kernel\Excel::TYPE_STRING,// 商品当地语言名称
            4 => \Vtiful\Kernel\Excel::TYPE_STRING,// 尺码
            5 => \Vtiful\Kernel\Excel::TYPE_STRING,// barcode
            6 => \Vtiful\Kernel\Excel::TYPE_STRING,// 商品价格
        ],

        // 流水管理-付款流水-批量更新费用类型
        self::TYPE_BANK_FLOW_PAY_FLOW_UPLOAD_EXPENSE => [
            12 => \Vtiful\Kernel\Excel::TYPE_STRING,// 费用类型
            13 => \Vtiful\Kernel\Excel::TYPE_STRING,// 转出银行
            14 => \Vtiful\Kernel\Excel::TYPE_STRING,// 转入银行
            16 => \Vtiful\Kernel\Excel::TYPE_STRING,// 单号
        ],

        // 流水管理-收款流水-批量更新费用类型
        self::TYPE_BANK_FLOW_GET_FLOW_UPLOAD_EXPENSE => [
            12 => \Vtiful\Kernel\Excel::TYPE_STRING,// 费用类型
            13 => \Vtiful\Kernel\Excel::TYPE_STRING,// 转出银行
            14 => \Vtiful\Kernel\Excel::TYPE_STRING,// 转入银行
        ],

        self::TYPE_WAREHOUSE_REQUIREMENT => [
            5  => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP,// 预计入驻日期
//            11 => \Vtiful\Kernel\Excel::TYPE_STRING,   // 经度
//            12 => \Vtiful\Kernel\Excel::TYPE_STRING,   // 纬度
        ],

    ];
}
