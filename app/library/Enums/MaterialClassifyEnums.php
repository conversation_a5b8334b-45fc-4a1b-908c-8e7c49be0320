<?php
namespace App\Library\Enums;

/**
 * 资产所有枚举数据
 * Class enums
 */

final class MaterialClassifyEnums
{
    /**
     * @Message ('物料分类添加失败')
     */
    public static $MATERIAL_CATEGORY_ADD_EXISTED = 11000;

    /**
     * @Message ('财务分类添加失败')
     */
    public static $MATERIAL_FINANCE_CATEGORY_ADD_EXISTED = 11001;

    /**
     * @Message ('分类修改失败')
     */
    public static $MATERIAL_CATEGORY_SAVE_FAIL = 11004;

    /**
     * @Message ('添加标准型号失败')
     */
    public static $MATERIAL_SAU_BARCODE_ADD_EXISTED = 11002;

    /**
     * @Message ('修改标准型号失败')
     */
    public static $MATERIAL_SAU_BARCODE_SAVE_EXISTED = 11003;



    const OSS_MATERIAL_TYPE_BAK = 1;//物料添加图片和附件类型

    const MATERIAL_ATTRIBUTE_MEASUREMENT = [
        ['id'=>1, 'name'=>'mm'],
        ['id'=>2, 'name'=>'cm'],
        ['id'=>3, 'name'=>'m']
    ];//尺寸
    const MATERIAL_ATTRIBUTE_WEIGHT = [
        ['id'=>1, 'name'=>'g'],
        ['id'=>2, 'name'=>'kg']
    ];//重量

    const IS_DELETED_NO = 0;//未删除
    const IS_DELETED_YES = 1;//已删除

    const UNIT_SCM = 1;//SCM基本单位
    const UNIT_SAP = 2;//SAP单位

    const MATERIAL_CATEGORY_ENUMS = 1; //物料分类
    const MATERIAL_FINANCE_CATEGORY_ENUMS = 2;//财务分类

    const MATERIAL_CATEGORY_OFF = 1; //是否更新至SCM/是否启用资产码/是否启用SN/是否验收/是否更新至SAP，否
    const MATERIAL_CATEGORY_NO = 2;//是否更新至SCM/是否启用资产码/是否启用SN/是否验收/是否更新至SAP，是

    const MATERIAL_CATEGORY_TYPE_ASSET = 1;//物料类型1，资产
    const MATERIAL_CATEGORY_TYPE_WMS = 2;//物料类型2，耗材
    const MATERIAL_CATEGORY_TYPE_SERVICE = 3;//物料类型3，服务类

    //资产台账-物料类型筛选项验证
    const MATERIAL_CATEGORY_TYPE_VALIDATE = self::MATERIAL_CATEGORY_TYPE_ASSET . ',' . self::MATERIAL_CATEGORY_TYPE_WMS;

    //是否限制员工互转
    const MATERIAL_CATEGORY_TRANSFER_FORBID_YES = 1; //是
    const MATERIAL_CATEGORY_TRANSFER_FORBID_NO = 2; //否
    const MATERIAL_CATEGORY_TRANSFER_FORBID_VALIDATE = self::MATERIAL_CATEGORY_TRANSFER_FORBID_YES . ',' . self::MATERIAL_CATEGORY_TRANSFER_FORBID_NO;
    public static $transfer_forbid_t = [
        self::MATERIAL_CATEGORY_TRANSFER_FORBID_YES => 'transfer_forbid_yes',
        self::MATERIAL_CATEGORY_TRANSFER_FORBID_NO => 'transfer_forbid_no',
    ];

    public static $is_off = [
        self::MATERIAL_CATEGORY_OFF => 'accident_report_whether_2',
        self::MATERIAL_CATEGORY_NO => 'accident_report_whether_1',
    ];

    const MATERIAL_START_USING = 1; //启用
    const MATERIAL_PROHIBITED_USE = 2;//禁用

    public static $status_switch = [
        self::MATERIAL_START_USING => 'start_using',
        self::MATERIAL_PROHIBITED_USE => 'prohibited_use',
    ];

    const GET_ATTRIBUTE_VALUE_ARR_KEY= 'get_attribute_value_arr_key';//物料属性数据缓存key
    const GET_ATTRIBUTE_VALUE_ARR_TIME= 180;//物料属性数据缓存180秒更新

    const MATERIAL_SAU_EXPORT_LOCK='material_sau_export_lock'; //标准型号导出时间锁

    const MATERIAL_CATEGORY_ONE= 'material_category.1';//资产
    const MATERIAL_FINANCE_TWO= 'material_finance.2';//耗材
    const MATERIAL_FINANCE_THREE = 'material_finance.3';//服务类

    //物料类型-枚举配置项-筛选框
    public static $material_type_list = [
        ['id'=>self::MATERIAL_CATEGORY_ENUMS,'name'=>self::MATERIAL_CATEGORY_ONE],
        ['id'=>self::MATERIAL_FINANCE_CATEGORY_ENUMS,'name'=>self::MATERIAL_FINANCE_TWO],
        ['id'=>self::MATERIAL_CATEGORY_TYPE_SERVICE,'name'=>self::MATERIAL_FINANCE_THREE],
    ];

    //物料类型-接口返回渲染-为啥配置两个，因为历史都有在用，就都保留了，这里挪了下位置。
    public static $material_category_arr_type = [
        self::MATERIAL_CATEGORY_TYPE_ASSET => self::MATERIAL_CATEGORY_ONE,
        self::MATERIAL_CATEGORY_TYPE_WMS => self::MATERIAL_FINANCE_TWO,
        self::MATERIAL_CATEGORY_TYPE_SERVICE => self::MATERIAL_FINANCE_THREE
    ];

    //可申请/购买
    const MATERIAL_USE_SCENE_APPLY = 1;//可申请
    const MATERIAL_USE_SCENE_BUY = 2;//可购买
    const MATERIAL_USE_SCENE_APPLY_AND_BUY = 3;//可申请可购买
    //可申请/购买翻译
    public static $material_use_scene = [
        self::MATERIAL_USE_SCENE_APPLY => 'material_use_scene.' . self::MATERIAL_USE_SCENE_APPLY,
        self::MATERIAL_USE_SCENE_BUY => 'material_use_scene.' . self::MATERIAL_USE_SCENE_BUY,
        self::MATERIAL_USE_SCENE_APPLY_AND_BUY => 'material_use_scene.' . self::MATERIAL_USE_SCENE_APPLY_AND_BUY
    ];

    //是否包含职位：1包含，2不包含
    const IS_CONTAIN_JOB_YES = 1;
    const IS_CONTAIN_JOB_NO = 2;
    //是否包含职位翻译
    public static $material_is_contain_job = [
        self::IS_CONTAIN_JOB_YES => 'material_is_contain_job.' . self::IS_CONTAIN_JOB_YES,
        self::IS_CONTAIN_JOB_NO => 'material_is_contain_job.' . self::IS_CONTAIN_JOB_NO,
    ];

    //标准型号操作记录-操作类型
    const OPERATE_TYPE_ADD = 1;//新增
    const OPERATE_TYPE_UPDATE = 2;//更新
    const OPERATE_TYPE_DELETE = 3;//删除
    const OPERATE_TYPE_PURCHASE_STAFF = 4;//采购员

    //同步至SCM-数据来源
    const TO_SCM_SOURCE_TYPE_SAU = 0;//0标准型号，1财务分类
    const TO_SCM_SOURCE_TYPE_FINANCE = 1;//1财务分类

    //同步至SCM-同步操作类型
    const TO_SCM_SYNC_TYPE_ADD = 0;//新增
    const TO_SCM_SYNC_TYPE_UPDATE = 1;//更新
    const TO_SCM_SYNC_TYPE_DELETE = 2;//删除
    const TO_SCM_SYNC_TYPE_STATUS = 3;//变更状态

    //同步至SCM-同步状态
    const TO_SCM_SYNC_STATUS_WAIT = 0;//未同步
    const TO_SCM_SYNC_STATUS_SUCCESS = 1;//成功
    const TO_SCM_SYNC_STATUS_FAIL = 2;//失败
    //查询标准型号的名称
    public static $language_fields = [
        'zh' => 'zh',
        'zh-CN' => 'zh',
        'en' => 'en',
    ];
    //标准型号最大可导出数据量
    const STANDARD_MAX_DOWNLOAD = 20000;

    /**
     * 单位换算
     * @var array
     */
    public static $reduced_unit = [
        'mm' => 1,
        'cm' => 10,
        'm' => 1000,
        'g' => 1,
        'kg' => 1000
    ];

    //价格正则表达式
    const PRICE_VALIDATION = '/^[1-9]\\d{0,11}(\\.\\d{1,6})?$|^0(\\.\\d{1,6})?$/';
    /**
     * 标准型号各信息最大限度
     * @var array
     */
    public static $sau_validation = [
        'name' => 100,
        'model'=> 100,
        'price' => self::PRICE_VALIDATION,
        'mark' => 1000,
    ];

    const FINANCE_CATEGORY_CODE_G010 = 'G010';//财务二级分类code-G010
    const FINANCE_CATEGORY_CODE_G020 = 'G020';//财务二级分类code-G020
    const FINANCE_CATEGORY_CODE_G030 = 'G030';//财务二级分类code-G030

    /**
     * 需要同步至SAP的财务分类code组
     * @var array
     */
    public static $send_to_sap_finance_category_code = [
        self::FINANCE_CATEGORY_CODE_G010,
        self::FINANCE_CATEGORY_CODE_G020,
        self::FINANCE_CATEGORY_CODE_G030,
    ];

    /**
     * 财务分类code与SAP的code的对应关系组
     * @var array
     */
    public static $finance_category_code_to_sap_code = [
        self::FINANCE_CATEGORY_CODE_G010 => '3020',
        self::FINANCE_CATEGORY_CODE_G020 => '3030',
        self::FINANCE_CATEGORY_CODE_G030 => '3040',
    ];

    const CATEGORY_LEVEL_TWO = 2;//分类级别-2级

    const IS_SEND_SAP_WAIT = 1;//待同步
    const IS_SEND_SAP_SUCCESS = 2;//同步成功
    const IS_SEND_SAP_FAIL = 3;//同步失败

    const SAP_IS_EXISTED = 1;//sap已存在
    const REQUEST_SAP_LOG_MATERIAL = 8;//新资产-标准型号
    const SAP_ACTION_CODE_CREATE = '01';//SAP 新增
    const SAP_ACTION_CODE_UPDATE = '02';//SAP 更新

    //各个国家的Flash Express的公司ID
    const FLASH_EXPRESS = [
        GlobalEnums::TH_COUNTRY_CODE => 1,
        GlobalEnums::MY_COUNTRY_CODE => 315,
        GlobalEnums::PH_COUNTRY_CODE => 80001
    ];

    //配送方式
    const DELIVERY_WAY_EXPRESS = 1; //快递
    const DELIVERY_WAY_SELF = 2;//自取
    public static $delivery_way_arr = [
        self::DELIVERY_WAY_EXPRESS => 'delivery_way_express',
        self::DELIVERY_WAY_SELF => 'delivery_way_self',
    ];
}
