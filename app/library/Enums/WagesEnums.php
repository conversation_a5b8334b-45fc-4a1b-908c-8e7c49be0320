<?php

namespace App\Library\Enums;

final class WagesEnums
{
    //申请类型
    const APPLY_TYPE_WAGES = 1; //薪酬扣款
    const APPLY_TYPE_CROWD_SOURCING = 2; //众包
    const APPLY_TYPE_PERSONAL_AGENT = 3;//快递个人代理

    //众包数据处理状态
    const WAGES_EXECUTE_STATUS_PENDING = 0; //待处理
    const WAGES_EXECUTE_STATUS_ING = 1; //处理中
    const WAGES_EXECUTE_STATUS_SUCCESS = 2; //处理成功
    const WAGES_EXECUTE_STATUS_FAIL = 3; //处理失败

    //费用类型
    const CROWD_SOURCING_COST_TYPE = 16;//众包费用
    const PERSONAL_AGENT_COST_TYPE = 17;//快递个人代理

    //是否是个人代理
    const IS_PERSONAL_AGENT_YES = 1;//是

    //众包接口路由
    public static $crowd_sourcing_config = [
        'crowd_sourcing_pay_stats' => '/svc/driver/crowdsourcing/task/oa/trade/state/update',//支付结果同步到众包
        'crowd_sourcing_payee_info' => '/svc/driver/crowdsourcing/task/oa/bank/info',//查询收款人信息
    ];

    //快递接口路由
    public static $bi_route_list = [
        'pay_status' => 'proxyInvoice.get_pay_return',//支付结果同步到快递
    ];
}
