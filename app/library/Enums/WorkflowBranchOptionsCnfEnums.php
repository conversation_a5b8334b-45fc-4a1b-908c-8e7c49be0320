<?php

namespace App\Library\Enums;

class WorkflowBranchOptionsCnfEnums
{
    //条件等式右边操作配置
    const OPTION_TYPE_NULL = 0; //无
    const OPTION_TYPE_API = 1; //通过接口获取
    const OPTION_TYPE_SPEC_OPTION_LIST = 2; //指定下拉列表

    //Api接口Type枚举
    const API_TYPE_DEPARTMENT = 1;
    const API_TYPE_JOB_TITLE = 2;
    const API_TYPE_STORE = 3;
    const API_TYPE_ROLES = 4;
    const API_TYPE_REGION = 5;
    const API_TYPE_PIECE = 6;

    //API接口
    const ACCESS_URL_JOB_TITLE = '/workflow_management/sys/getJobTitleList';
    const ACCESS_URL_STORE = '/workflow_management/sys/getStoreList';
    const ACCESS_URL_ROLES = '/workflow_management/sys/getRolesList';
    const ACCESS_URL_REGION = '/workflow_management/sys/getRegionList';
    const ACCESS_URL_PIECE = '/workflow_management/sys/getPieceList';


    //前端组件类型
    const UNIT_TYPE_SELECT = 'select'; //一维数组下拉
    const UNIT_TYPE_CASCADER = 'cascader'; //树状结构下拉

    const UNIT_TYPE_INPUT = 'input'; //字符型输入框
    const UNIT_TYPE_INPUT_NUMBER = 'input-number'; //数字型输入框

    //提交参数类型，用于告诉前端，提交过来的是数值，还是对象。
    //默认提交过来的是数值
    const SUBMIT_PARAM_TYPE_OBJECT = 'object';
}