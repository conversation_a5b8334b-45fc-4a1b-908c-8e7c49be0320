<?php

namespace App\Library\Enums;

/**
 * 这里只定义用到的状态, 全量状态参考winHr项目枚举
 * Class HrStaffContractEnums
 * @package App\Library\Enums
 * @date 2022/10/31
 */
class HrStaffContractEnums
{
    //合同类型
    const CONTRACT_ZCXY = 5; //资产协议
    const CONTRACT_ZCXY_MY = 1;//马来资产协议
    //合同状态
    const CONTRACT_STATUS_CHECK = 40;//待复核
    const CONTRACT_STATUS_ARCHIVE = 50; //待归档
    const CONTRACT_STATUS_ARCHIVED = 60; //已归档
    const CONTRACT_STATUS_RELEASED = 70; //已解除
    const CONTRACT_STATUS_TO_BE_RENEWED = 80; //待续约
    const CONTRACT_STATUS_RENEWED = 81; //已续约
    const CONTRACT_STATUS_EXPIRED = 90; //已到期
    const CONTRACT_STATUS_IN_EFFECT = 85;//合同生效中

}