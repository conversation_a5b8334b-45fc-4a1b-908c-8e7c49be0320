<?php

namespace App\Library\Enums;

final class OrganizationDepartmentEnums
{
    //组织架构关联关系状态，大区，片区，网点均使用
    const STATE_REMOVE = 0;
    const STATE_NORMAL = 1;

    //组织架构上级关联关系状态，大区，片区，网点均使用
    const LEVEL_STATE_REMOVE = 0;
    const LEVEL_STATE_NORMAL = 1;

    //组织架构返回数据枚举
    const ALL_AUTH = 'ALL';
    const NO_AUTH = 'NO';

    //组织架构数据返回枚举
    const ALL_STORE = '-2';
    const NO_STORE = '0';
    const NO_DEPARTMENT = 0;
    const NO_REGION = 0;

    // 组织(部门)等级
    const ORGANIZATION_LEVEL_1 = 1;

    //组织类型type：1公司 2公司下部门 3组织下部门 4组织 5GroupCeo
    const ORGANIZATION_GROUP_CEO_TYPE = 5;
    const ORGANIZATION_CLEVEL_TYPE = 4;
    const ORGANIZATION_BU_TYPE = 1;
    const ORGANIZATION_BU_DEPARTMENT_TYPE = 2;
    const ORGANIZATION_CLEVEL_DEPARTMENT_TYPE = 3;

    const GROUP_BOSS_LEVEL = 99;

    const MANAGER_POSITION_STATE_YES = 1;//正职
    const MANAGER_POSITION_STATE_NO = 2;//代理
    const CONFIRM_YES = 1;//确认
    const CONFIRM_NO = 0;//未确认
    const SHOW_MANAGER_POSITION_STATE_YES = 1;//是否显示正职/代理
    const SHOW_MANAGER_POSITION_STATE_NO = 0;//是否显示正职/代理

    const SYNC_MS_UPDATE_TYPE_STORE = 4;

    const TYPE_REGION = 1; //大区
    const TYPE_PIECE = 2; //片区
    const TYPE_FRANCHISEE_REGION = 3; //加盟商大区
    const TYPE_FRANCHISEE_PIECE = 4; //加盟商片区

    const NEXT_TYPE_PIECE = 4;//下级到片区
    const NEXT_TYPE_STORE = 5;//下级到网点

    const CURRENT_TYPE_REGION = 3; //当前级别大区
    const CURRENT_TYPE_PIECE = 4;//当前级别片区

    const EDIT_ORGANIZATION_INFO_YES = true;//编辑组织信息
    const EDIT_ORGANIZATION_INFO_NO = false;//编辑组织信息

    const HAS_CHILDREN_YES = true;//是否有下级
    const HAS_CHILDREN_NO = false;

    const DO_NOT_EXIST_HC = 0; //不存在HC
    const EXIST_HC = 1; //存在HC

    const DO_NOT_EXIST_SHARE_POSITIONS = 0; //不存在共享职位
    const EXIST_SHARE_POSITIONS = 1; //存在共享职位
}