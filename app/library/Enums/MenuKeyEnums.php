<?php
namespace App\Library\Enums;

final class MenuKeyEnums
{
    // 各模块的回复子菜单: 待回复 -> 菜单权限(一级菜单) [回复子菜单无需权限控制，有大菜单权限即有其权限]
    const CONTRACT_REPLY_UNPROCESSED = 'menu.contract';//合同管理～合同回复～待处理
    const STORE_RENTING_PAYMENT_REPLY_UNPROCESSED = 'menu.payment';//租房付款管理～付款回复～待处理～网点租房付款
    const ORDINARY_PAYMENT_REPLY_UNPROCESSED = 'menu.ordinary_payment';//普通付款～付款回复～待处理～普通付款
    const RESERVE_REPLY_UNPROCESSED = 'menu.reserve';//网点备用金～备用金回复～待处理～备用金申请/归还
    const VENDOR_REPLY_UNPROCESSED = 'menu.contract.vendor';//供应商管理～供应商回复～待处理
    const VENDOR_GRADE_REPLY_UNPROCESSED = 'action.vendor.grade';//供应商管理～供应商回复～待处理

    const BUDGET_ADJUST_REPLY_UNPROCESSED = 'menu.budget';//财务预算管理~预算调整回复
    const PURCHASE_MANAGEMENT_REPLY_UNPROCESSED = 'menu.purchase'; //采购管理 含采购模块下的所有子模块
    const LOAN_MANAGEMENT_REPLY_UNPROCESSED = 'menu.loan'; //借款管理
    const REIMBURSEMENT_MANAGEMENT_REPLY_UNPROCESSED = 'menu.reimbursement'; //报销管理
    const PAY_MANAGEMENT_REPLY_UNPROCESSED = 'menu.pay'; //支付管理
    const CHEQUE_CONSULTATION_UNPROCESSED = 'menu.cheque'; //支票回复
    const DEPOSIT_CONSULTATION_UNPROCESSED = 'menu.deposit'; //押金回复

    // 各模块的支付子菜单: 待支付 -> 菜单权限
    const SALARY_PAY_UNPROCESSED = 'menu.salary.payment';//薪酬扣款审批～我的支付～待处理
    const WAGE_PAY_UNPROCESSED = 'menu.wage.payment';//薪资发放审批～我的支付～待处理
    const STORE_RENTING_PAYMENT_PAY_UNPROCESSED = 'action.payment.pay.store_renting';//租房付款管理～付款支付～待处理～网点租房付款
    const ORDINARY_PAYMENT_PAY_UNPROCESSED = 'menu.ordinary_payment.pay';//普通付款～付款支付～待处理～普通付款
    const RESERVE_PAY_UNPROCESSED = 'menu.reserve.pay';//网点备用金～备用金支付～待处理
    const PURCHASE_PAYMENT_PAY_UNPROCESSED = 'menu.purchase.pay';//采购管理-采购支付-采购付款单支付
    const LOAN_PAY_UNPROCESSED = 'menu.loan.pay';//借款管理-借款申请单-支付
    const REIMBURSEMENT_PAY_UNPROCESSED = 'menu.reimbursement.pay';//报销管理-支付

    // 各模块的审核/其他待办子菜单(含子菜单内的细分业务): 待处理 -> 菜单权限
    const CONTRACT_OTHER_WAIT_AUDIT = 'action.contract.audit.search';//合同管理～合同审核～待审核～其他合同
    const CONTRACT_STORE_RENTING_WAIT_AUDIT = 'action.storeRentingContract.getAuditList';//合同管理～合同审核～待审核～网点租房合同
    const CONTRACT_PLATFORM_WAIT_AUDIT = 'action.contract_platform.get_audit_list.list';//合同管理～合同审核～待审核～gpmd合同
    const CONTRACT_ELECTRONIC_WAIT_AUDIT = 'menu.contract.electronic.audit';//合同管理～其他合同～电子合同审核-待审核

    const VENDOR_WAIT_AUDIT = 'action.vendor.audit.approve';//供应商管理～供应商审核～待处理
    const VENDOR_WAIT_GRADE_AUDIT = 'action.vendor.audit.grade_approve';//供应商管理～供应商审核～待处理

    const WMS_WAIT_AUDIT = 'menu.wms.audit';//物料/资产管理～物料/设备领用审批～待审核
    const ASSET_WAIT_AUDIT = 'menu.asset.audit';//物料/资产管理～资产领用审批～待审核
    const MATERIAL_ASSET_APPLY_AUDIT ='menu.material.asset_apply_audit';//物料/资产管理～资产领用审批菜单~待审核
    const MATERIAL_WMS_OUT_STORAGE_DRAFT = 'menu.material.wms_out_storage';//物料/资产管理～耗材出库
    const KPI_LEADER_WAIT_AUDIT = 'menu.kpi.leader';//KPI目标管理～下级KPI管理
    const KPI_PPM_WAIT_AUDIT = 'menu.kpi.staff';//KPI目标管理～KPI员工管理
    const SALARY_WAIT_AUDIT = 'menu.salary.audit';//薪酬扣款审批～我的审核～待处理
    const WAGE_WAIT_AUDIT = 'menu.wage.audit';//薪资发放审批～我的审核～待处理
    const LOAN_WAIT_AUDIT = 'menu.loan.audit';// 借款管理~借款审核～待处理～借款申请（借款归还）
    const PURCHASE_APPLY_WAIT_AUDIT = 'menu.audit.apply';//采购管理～采购审核～待处理～采购申请单
    const PURCHASE_ORDER_WAIT_AUDIT = 'menu.audit.order';//采购管理～采购审核～待处理～采购订单
    const PURCHASE_PAYMENT_WAIT_AUDIT = 'menu.audit.payment';//采购管理～采购审核～待处理～采购付款申请单
    const PURCHASE_ACCEPTANCE_WAIT_AUDIT= 'menu.audit.acceptance';//采购管理～采购审核～待处理～验收单
    const PURCHASE_SAMPLE_WAIT_AUDIT= 'menu.audit.sample';//采购管理～采购审核～待处理～样品确认
    const REIMBURSEMENT_WAIT_AUDIT = 'menu.reimbursement.audit';//报销管理～报销审核～待处理
    const STORE_RENTING_PAYMENT_WAIT_AUDIT = 'action.payment.audit.store_renting';//租房付款管理～付款审核～待处理～网点租房付款
    const ORDINARY_PAYMENT_WAIT_AUDIT = 'menu.ordinary_payment.audit';//普通付款～付款审核～待处理～普通付款
    const ACCESS_DATA_WAIT_AUDIT = 'action.access_data_sys.audit';//取数工单系统～待审批
    const ACCESS_DATA_PROCESS_WAIT_AUDIT = 'action.access_data_sys.process';//取数工单系统～申请处理～待处理
    const RESERVE_APPLY_WAIT_AUDIT = 'menu.reserve.audit.apply';//网点备用金～备用金审核～待处理～备用金申请
    const RESERVE_RETURN_WAIT_AUDIT ='menu.reserve.audit.return';//网点备用金～备用金审核～待处理～备用金归还
    const CRM_WAIT_AUDIT = 'menu.quotation.audit';//报价管理～报价审核～待审核
    const PAY_WAIT_AUDIT = 'menu.pay.pay';//支付管理～我的支付～待处理
    const PAY_BANK_WAIT_AUDIT = 'menu.pay.finalpay';//支付管理～银行支付～待处理
    const PAY_ONLINE_AUDIT = 'menu.pay.online';//支付管理-在线支付
    const BUDGET_ADJUST_WAIT_AUDIT = 'menu.budget.adjust.audit';//财务预算管理~预算调整审核～待处理
    const BUDGET_WAIT_AUDIT = 'action.budget.import_approve';//财务预算管理～预算导入审核～待处理
    const BUDGET_WITHHOLDING_WAIT_AUDIT = 'action.budget.withholding.audit';//财务预算管理~预提审核～待处理
    const CHEQUE_AUDIT_WAIT_AUDIT = 'menu.cheque.audit';//支票管理～支票审核～待处理
    const DEPOSIT_AUDIT_WAIT_AUDIT = 'menu.deposit.deposit_audit';//押金管理～归还审核～待处理

    const ASSET_TO_BE_RECEIVER_COUNT = 'menu.material.asset_transfer.to_me';//资产转移～待接收
    const CONTRACT_REMIND_WAIT_DEAL = 'menu.contract.contractRemind.storeRentingContract';//合同管理-网点合同等进度管理-网点合同签署提醒~待处理
    const MATERIAL_LEAVE_ASSET_COUNT = 'menu.material.leave_asset_manager';//物料/资产管理-离职资产-上级处理~待处理
    const MATERIAL_WMS_AUDIT_WAIT_AUDIT = 'menu.material.wms_apply_audit';//耗材申请～耗材审核～待处理
    const WORKFLOW_CARBON_COPY_COUNT = 'menu.carbon_copy.my';//抄送管理-我的抄送

    const REIMBURSEMENT_APPLY_PENDING = 'menu.reimbursement.apply';// 报销管理-我的申请
    const WAREHOUSE_REQUIREMENT_SEARCH = 'menu.warehouse.requirement.search';//处理仓库需求-待寻找
    const WAREHOUSE_REQUIREMENT_CONFIRM = 'menu.warehouse.requirement.confirm';//处理仓库需求-待确认
    const WAREHOUSE_REQUIREMENT_SETTLE = 'menu.warehouse.requirement.settle';//处理仓库需求-待入驻
    const WAREHOUSE_REQUIREMENT_RENEWED = 'menu.warehouse.requirement.renewed';//处理仓库需求-待续约
    const WAREHOUSE_THREAD_PRICE = 'menu.warehouse.thread.handle.price';//处理仓库线索-待报价
    const WAREHOUSE_THREAD_SIGN = 'menu.warehouse.thread.handle.sign';//处理仓库线索-待签约
    const WAREHOUSE_THREAD_VERIFY = 'menu.warehouse.thread.handle.verify';//处理仓库线索-待验证

    // 待办数据来自BY系统的
    const TRANSFER_WAIT_AUDIT = 'menu.transfer.audit';//转岗管理-转岗审批
    const TRANSFER_BP_WAIT_AUDIT = 'menu.transfer.bp_audit'; //转岗管理-BP转岗审批
    const HC_WAIT_AUDIT = 'menu.hc.audit';//HC预算管理～HC预算审核
    const WAREHOUSE_CHANGE_AUDIT = 'menu.warehouse_manage.audit';// 仓库信息变更审批
    const WAREHOUSE_PRICE_WAIT = 'menu.warehouse.price.wait';//仓库报价审核-待处理

    // 待办数据来自School系统的
    const SCHOOL_PLAN_WAIT_AUDIT = 'menu.school.learning_plan';//员工培训系统～学习计划～待审批
    const AGENCY_PAYMENT_AUDIT = 'menu.agency_payment.audit';//代理支付-我的审核
    const AGENCY_PAYMENT_PAY = 'menu.agency_payment.pay';//代理支付-支付

    //加班 批量审批 by系统
    const BATCH_APPROVAL_OVERTIME = 'menu.ot_approval';//批量审批加班
    const BATCH_APPROVAL_OS_OVERTIME = 'menu.os_ot_approval';//批量审批加班(外协)

    const SIM_CARD_FEEDBACK = 'menu.sim_card_feedback';//异常号码反馈工单
    const COMPANY_MOBILE_MANAGE = 'menu.company_mobile_manage';//SIM卡管理

    const PAPER_DOCUMENT_MY_LIST =  'menu.paper_document.my_list'; //纸质单据-我的
    const PAPER_DOCUMENT_CONFIRM_LIST =  'menu.paper_document.audit_list'; //纸质单据-待确认

    // 需要统计小红点的菜单
    public static $left_menu_hot_keys = [
        self::CONTRACT_OTHER_WAIT_AUDIT,
        self::CONTRACT_ELECTRONIC_WAIT_AUDIT,
        self::CONTRACT_STORE_RENTING_WAIT_AUDIT,
        self::CONTRACT_PLATFORM_WAIT_AUDIT,
        self::CONTRACT_REPLY_UNPROCESSED,
        self::VENDOR_WAIT_AUDIT,
        self::VENDOR_WAIT_GRADE_AUDIT,
        self::VENDOR_REPLY_UNPROCESSED,
        self::VENDOR_GRADE_REPLY_UNPROCESSED,
        self::WMS_WAIT_AUDIT,
        self::ASSET_WAIT_AUDIT,
        self::MATERIAL_ASSET_APPLY_AUDIT,
        self::MATERIAL_WMS_OUT_STORAGE_DRAFT,
        self::LOAN_WAIT_AUDIT,
        self::LOAN_PAY_UNPROCESSED,
        self::LOAN_MANAGEMENT_REPLY_UNPROCESSED,
        self::PURCHASE_APPLY_WAIT_AUDIT,
        self::PURCHASE_ORDER_WAIT_AUDIT,
        self::PURCHASE_PAYMENT_WAIT_AUDIT,
        self::PURCHASE_PAYMENT_PAY_UNPROCESSED,
        self::PURCHASE_ACCEPTANCE_WAIT_AUDIT,
        self::PURCHASE_SAMPLE_WAIT_AUDIT,
        self::PURCHASE_MANAGEMENT_REPLY_UNPROCESSED,
        self::REIMBURSEMENT_APPLY_PENDING,
        self::REIMBURSEMENT_WAIT_AUDIT,
        self::REIMBURSEMENT_PAY_UNPROCESSED,
        self::REIMBURSEMENT_MANAGEMENT_REPLY_UNPROCESSED,
        self::STORE_RENTING_PAYMENT_WAIT_AUDIT,
        self::STORE_RENTING_PAYMENT_PAY_UNPROCESSED,
        self::STORE_RENTING_PAYMENT_REPLY_UNPROCESSED,
        self::WAGE_WAIT_AUDIT,
        self::WAGE_PAY_UNPROCESSED,
        self::SALARY_WAIT_AUDIT,
        self::SALARY_PAY_UNPROCESSED,
        self::ORDINARY_PAYMENT_WAIT_AUDIT,
        self::ORDINARY_PAYMENT_PAY_UNPROCESSED,
        self::ORDINARY_PAYMENT_REPLY_UNPROCESSED,
        self::ACCESS_DATA_WAIT_AUDIT,
        self::ACCESS_DATA_PROCESS_WAIT_AUDIT,
        self::RESERVE_APPLY_WAIT_AUDIT,
        self::RESERVE_RETURN_WAIT_AUDIT,
        self::RESERVE_PAY_UNPROCESSED,
        self::RESERVE_REPLY_UNPROCESSED,
        self::PAY_WAIT_AUDIT,
        self::PAY_BANK_WAIT_AUDIT,
        self::PAY_ONLINE_AUDIT,
        self::PAY_MANAGEMENT_REPLY_UNPROCESSED,
        self::BUDGET_ADJUST_WAIT_AUDIT,
        self::BUDGET_ADJUST_REPLY_UNPROCESSED,
        self::BUDGET_WAIT_AUDIT,
        self::BUDGET_WITHHOLDING_WAIT_AUDIT,
        self::KPI_LEADER_WAIT_AUDIT,
        self::KPI_PPM_WAIT_AUDIT,
        self::SCHOOL_PLAN_WAIT_AUDIT,
        self::CRM_WAIT_AUDIT,
        self::HC_WAIT_AUDIT,
        self::TRANSFER_WAIT_AUDIT,
        self::TRANSFER_BP_WAIT_AUDIT,
        self::CHEQUE_AUDIT_WAIT_AUDIT,
        self::CHEQUE_CONSULTATION_UNPROCESSED,
        self::DEPOSIT_CONSULTATION_UNPROCESSED,
        self::DEPOSIT_AUDIT_WAIT_AUDIT,
        self::ASSET_TO_BE_RECEIVER_COUNT,
        self::CONTRACT_REMIND_WAIT_DEAL,
        self::MATERIAL_LEAVE_ASSET_COUNT,
        self::MATERIAL_WMS_AUDIT_WAIT_AUDIT,
        self::WORKFLOW_CARBON_COPY_COUNT,
        self::WAREHOUSE_CHANGE_AUDIT,
        self::WAREHOUSE_REQUIREMENT_SEARCH,
        self::WAREHOUSE_REQUIREMENT_CONFIRM,
        self::WAREHOUSE_REQUIREMENT_SETTLE,
        self::WAREHOUSE_REQUIREMENT_RENEWED,
        self::WAREHOUSE_THREAD_PRICE,
        self::WAREHOUSE_THREAD_SIGN,
        self::WAREHOUSE_THREAD_VERIFY,
        self::WAREHOUSE_PRICE_WAIT,
        self::AGENCY_PAYMENT_AUDIT,
        self::AGENCY_PAYMENT_PAY,
        self::BATCH_APPROVAL_OVERTIME,
        self::BATCH_APPROVAL_OS_OVERTIME,
        self::SIM_CARD_FEEDBACK,
        self::COMPANY_MOBILE_MANAGE,
        self::PAPER_DOCUMENT_MY_LIST,
        self::PAPER_DOCUMENT_CONFIRM_LIST,
    ];

    // 需要统计小红点的菜单, 来自BY外层小红点取数(指定菜单: 19706 附录一)
    public static $left_menu_hot_keys_from_by_outer = [
        self::SCHOOL_PLAN_WAIT_AUDIT,
        self::HC_WAIT_AUDIT,
        self::WAREHOUSE_CHANGE_AUDIT,
        self::BUDGET_WAIT_AUDIT,
        self::BUDGET_ADJUST_WAIT_AUDIT,
        self::BUDGET_ADJUST_REPLY_UNPROCESSED,
        self::BUDGET_WITHHOLDING_WAIT_AUDIT,
        self::CONTRACT_OTHER_WAIT_AUDIT,
        self::CONTRACT_STORE_RENTING_WAIT_AUDIT,
        self::CONTRACT_PLATFORM_WAIT_AUDIT,
        self::CONTRACT_REPLY_UNPROCESSED,
        self::VENDOR_WAIT_AUDIT,
        self::VENDOR_WAIT_GRADE_AUDIT,
        self::VENDOR_REPLY_UNPROCESSED,
        self::VENDOR_GRADE_REPLY_UNPROCESSED,
        self::PURCHASE_APPLY_WAIT_AUDIT,
        self::PURCHASE_ORDER_WAIT_AUDIT,
        self::PURCHASE_PAYMENT_WAIT_AUDIT,
        self::PURCHASE_ACCEPTANCE_WAIT_AUDIT,
        self::PURCHASE_SAMPLE_WAIT_AUDIT,
        self::PURCHASE_MANAGEMENT_REPLY_UNPROCESSED,
        self::LOAN_WAIT_AUDIT,
        self::LOAN_MANAGEMENT_REPLY_UNPROCESSED,
        self::REIMBURSEMENT_WAIT_AUDIT,
        self::REIMBURSEMENT_MANAGEMENT_REPLY_UNPROCESSED,
        self::SALARY_WAIT_AUDIT,
        self::WAGE_WAIT_AUDIT,
        self::CRM_WAIT_AUDIT,
        self::ACCESS_DATA_WAIT_AUDIT,
        self::CHEQUE_AUDIT_WAIT_AUDIT,
        self::CHEQUE_CONSULTATION_UNPROCESSED,
        self::STORE_RENTING_PAYMENT_WAIT_AUDIT,
        self::STORE_RENTING_PAYMENT_REPLY_UNPROCESSED,
        self::ORDINARY_PAYMENT_WAIT_AUDIT,
        self::ORDINARY_PAYMENT_REPLY_UNPROCESSED,
        self::DEPOSIT_AUDIT_WAIT_AUDIT,
        self::DEPOSIT_CONSULTATION_UNPROCESSED,
        self::RESERVE_APPLY_WAIT_AUDIT,
        self::RESERVE_RETURN_WAIT_AUDIT,
        self::RESERVE_REPLY_UNPROCESSED,
        self::CONTRACT_ELECTRONIC_WAIT_AUDIT,
        self::WAREHOUSE_PRICE_WAIT,
        self::AGENCY_PAYMENT_AUDIT,
    ];

    // 红点key集合, 新增的红点key需先在此配置默认值
    public static $menu_red_dot_list = [
        'contract_pending_count' => 0,
        'contract_consultation_pending_count' => 0,
        'contract_electronic_pending_count' => 0,
        'contract_electronic_customer_sign_pending_count' => 0,
        'contract_inquired_pending_count' => 0,
        'contract_inquired_replied_count' => 0,
        'store_renting_wait_my_consultation_num' => 0,
        'contract_platform_wait_my_consultation_num' => 0,
        'store_renting_wait_my_audit_num' => 0,
        'store_renting_contract_inquired_pending_count' => 0,
        'store_renting_contract_inquired_replied_count' => 0,
        'vendor_waiting_audit_count' => 0,
        'vendor_inquired_pending_count' => 0,
        'vendor_inquired_replied_count' => 0,
        'vendor_reply_pending_count' => 0,
        'vendor_grade_reply_pending_count' => 0,
        'wms_pending_count' => 0,
        'asset_pending_count' => 0,
        'material_asset_apply_pending_count' => 0,
        'salary_check_count' => 0,
        'salary_pay_count' => 0,
        'wage_wait_my_audit_num' => 0,
        'wage_wait_my_pay_num' => 0,
        'loan_pending_count' => 0,
        'loan_payment_pending_count' => 0,
        'loan_inquired_pending_count' => 0,
        'loan_inquired_replied_count' => 0,
        'loan_back_pending_count' => 0,
        'loan_back_inquired_pending_count' => 0,
        'loan_back_inquired_replied_count' => 0,
        'loan_consultation_pending_count' => 0,
        'purchase_apply_pending_count' => 0,
        'purchase_apply_inquired_pending_count' => 0,
        'purchase_apply_inquired_replied_count' => 0,
        'purchase_apply_consultation_pending_count' => 0,
        'purchase_order_pending_count' => 0,
        'purchase_order_inquired_pending_count' => 0,
        'purchase_order_inquired_replied_count' => 0,
        'purchase_order_consultation_pending_count' => 0,
        'purchase_pay_pending_count' => 0,
        'purchase_pay_inquired_pending_count' => 0,
        'purchase_pay_inquired_replied_count' => 0,
        'purchase_pay_consultation_pending_count' => 0,
        'purchase_pay_payment_pending_count' => 0,
        'purchase_acceptance_pending_count' => 0,
        'purchase_acceptance_inquired_pending_count' => 0,
        'purchase_acceptance_inquired_replied_count' => 0,
        'purchase_acceptance_consultation_pending_count' => 0,
        'purchase_sample_pending_count' => 0,
        'purchase_sample_inquired_pending_count' => 0,
        'purchase_sample_inquired_replied_count' => 0,
        'purchase_sample_consultation_pending_count' => 0,
        'reimbursement_pending_count' => 0,
        'reimbursement_inquired_pending_count' => 0,
        'reimbursement_inquired_replied_count' => 0,
        'reimbursement_payment_pending_count' => 0,
        'reimbursement_consultation_pending_count' => 0,
        'reimbursement_apply_pending_count' => 0,
        'payment_store_renting_waiting_approval_count' => 0,
        'payment_store_renting_waiting_pay_count' => 0,
        'payment_store_renting_inquired_pending_count' => 0,
        'payment_store_renting_inquired_replied_count' => 0,
        'payment_consultation_pending_count' => 0,
        'ordinary_payment_waiting_approval_count' => 0,
        'ordinary_payment_inquired_pending_count' => 0,
        'ordinary_payment_inquired_replied_count' => 0,
        'ordinary_payment_waiting_pay_count' => 0,
        'ordinary_payment_consultation_pending_count' => 0,
        'access_work_order_waiting_audit_count' => 0,
        'access_work_order_waiting_processed_count' => 0,
        'reserve_fund_apply_waiting_audit_count' => 0,
        'reserve_fund_apply_inquired_pending_count' => 0,
        'reserve_fund_apply_inquired_replied_count' => 0,
        'reserve_fund_return_waiting_audit_count' => 0,
        'reserve_fund_return_inquired_pending_count' => 0,
        'reserve_fund_return_inquired_replied_count' => 0,
        'reserve_fund_apply_waiting_pay_count' => 0,
        'reserve_fund_apply_consultation_pending_count' => 0,
        'reserve_fund_return_consultation_pending_count' => 0,
        'crm_wait_my_audit_num' => 0,
        'pay_waiting_audit_count' => 0,
        'pay_inquired_pending_count' => 0,
        'pay_inquired_replied_count' => 0,
        'pay_bank_waiting_audit_count' => 0,
        'pay_waiting_pending_count' => 0,
        'pay_online_count' => 0,
        'budget_adjust_pending_count' => 0,
        'budget_adjust_inquired_pending_count' => 0,
        'budget_adjust_inquired_replied_count' => 0,
        'budget_adjust_waiting_reply_count' => 0,
        'budget_audit_pending_count' => 0,
        'budget_withholding_audit_pending_count' => 0,
        'kpi_waiting_submit_audit_count' => 0,
        'school_plan_pending_count' => 0,
        'hc_pending_count' => 0,
        'jt_pending_count' => 0,
        'cheque_audit_pending_count' => 0,
        'cheque_audit_inquired_pending_count' => 0,
        'cheque_audit_inquired_replied_count' => 0,
        'cheque_waiting_pending_count' => 0,
        'deposit_consultation_count' => 0,
        'asset_to_be_receiver_count' => 0,
        'contract_contract_remind_count' => 0,
        'material_leave_asset_count' => 0,
        'material_wms_audit_pending_count' => 0,
        'material_wms_out_storage_draft' => 0,
        'workflow_carbon_copy_count' => 0,
        'deposit_audit_pending_count' => 0,
        'deposit_audit_inquired_pending_count' => 0,
        'deposit_audit_inquired_replied_count' => 0,
        'vendor_grade_waiting_audit_count' => 0,
        'vendor_grade_inquired_pending_count' => 0,
        'vendor_grade_inquired_replied_count' => 0,
        'contract_platform_pending_count' => 0,
        'contract_platform_inquired_pending_count' => 0,
        'contract_platform_inquired_replied_count' => 0,
        'warehouse_change_audit_pending_count' => 0,
        'agency_payment_audit_pending_count' => 0,
        'agency_payment_pay_pending_count' => 0,
        'batch_approval_overtime' => 0,
        'warehouse_requirement_search' => 0,
        'warehouse_requirement_confirm' => 0,
        'warehouse_requirement_settle' => 0,
        'warehouse_thread_price' => 0,
        'warehouse_thread_sign' => 0,
        'warehouse_thread_verify' => 0,
        'warehouse_price_wait' => 0,
    ];
}
