<?php

namespace App\Library\Enums;

/**
 * 预算调整统常用枚举数据
 * Class enums
 */
final class BudgetAdjustEnums
{
    // 预算调整明细编辑状态
    const BUDGET_ADJUST_DETAIL_STATUS_VALID = 0;    //0 有效
    const BUDGET_ADJUST_DETAIL_STATUS_INVALID = 1;  //1 作废

    // 预算调整状态
    const BUDGET_ADJUST_STATUS_PENDING = 1;
    const BUDGET_ADJUST_STATUS_REJECTED = 2;
    const BUDGET_ADJUST_STATUS_APPROVAL = 3;
    const BUDGET_ADJUST_STATUS_CANCEL = 4;
    public static $budget_adjust_status = [
        self::BUDGET_ADJUST_STATUS_PENDING  => 'budget_adjust.status.1',
        self::BUDGET_ADJUST_STATUS_REJECTED => 'budget_adjust.status.2',
        self::BUDGET_ADJUST_STATUS_APPROVAL => 'budget_adjust.status.3',
        self::BUDGET_ADJUST_STATUS_CANCEL   => 'budget_adjust.status.4',
    ];

    // 预算导入审核状态
    const BUDGET_IMPORT_STATUS_PENDING = 1;
    const BUDGET_IMPORT_STATUS_REJECTED = 2;
    const BUDGET_IMPORT_STATUS_APPROVAL = 3;
    const BUDGET_IMPORT_STATUS_CANCEL = 4;
    public static $budget_import_status = [
        self::BUDGET_IMPORT_STATUS_PENDING  => 'budget_import.status.1',
        self::BUDGET_IMPORT_STATUS_REJECTED => 'budget_import.status.2',
        self::BUDGET_IMPORT_STATUS_APPROVAL => 'budget_import.status.3',
        self::BUDGET_IMPORT_STATUS_CANCEL   => 'budget_import.status.4',
    ];
    // 导入类型
    const BUDGET_ADJUST_IMPORT_TYPE_INPUT = 1;      // 预算追加
    const BUDGET_ADJUST_IMPORT_TYPE_OUTPUT = 2;     // 预算释放
    const BUDGET_ADJUST_IMPORT_TYPE_INOUT = 3;      // 预算调整
    public static $budget_adjust_import_method = [
        self::BUDGET_ADJUST_IMPORT_TYPE_INPUT  => 'budget_adjust_input_method',
        self::BUDGET_ADJUST_IMPORT_TYPE_OUTPUT => 'budget_adjust_output_method',
        self::BUDGET_ADJUST_IMPORT_TYPE_INOUT  => 'budget_adjust_inout_method',
    ];

    // 转入转出
    const BUDGET_ADJUST_INOUT_TYPE_INPUT = 1;       // 转入
    const BUDGET_ADJUST_INOUT_TYPE_OUTPUT = 2;      // 转出

    // 旧版本预算调整审批流
    const WF_BUDGET_ADJUST_WF_ID = 72;              // 预算调整

    // 新版本预算调整审批流（按类型做审批流分类）
    public const WF_BUDGET_ADJUST_IN_FLOW = 119;    // 追加
    public const WF_BUDGET_ADJUST_OUT_FLOW = 120;   // 释放
    public const WF_BUDGET_ADJUST_INOUT_FLOW = 121; // 转移

    //是否支付
    const IS_SHARE_NO = 1;                          //否
    const IS_SHARE_YES = 2;                         //是
    public static $adjust_is_share_key = [
        self::IS_SHARE_NO  => 'payment_is_pay_no',
        self::IS_SHARE_YES => 'payment_is_pay_yes',
    ];
}