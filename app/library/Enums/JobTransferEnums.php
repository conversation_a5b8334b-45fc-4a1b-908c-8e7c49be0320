<?php


namespace app\library\Enums;


final class JobTransferEnums
{
    /***** 转岗模块菜单权限配置 Start *****/
    const PERMISSION_DEFAULT_TOP_MENU_ID = 0;// 默认顶级菜单ID
    const PERMISSION_JOB_TRANSFER_MENU_ID = 261;// 转岗管理

    // 申请模块
    const PERMISSION_JOB_TRANSFER_APPLY_MENU_ID = 262;// 转岗管理-申请
    const PERMISSION_JOB_TRANSFER_APPLY_ADD_ID = 263;// 转岗管理-批量转岗申请
    const PERMISSION_JOB_TRANSFER_APPLY_SEARCH_ID = 264;// 转岗管理-批量转岗查询
    const PERMISSION_JOB_TRANSFER_APPLY_VIEW_ID = 265;// 转岗管理-批量转岗查看

    // 审核模块
    const PERMISSION_JOB_TRANSFER_AUDIT_MENU_ID = 268;// 转岗管理-审核
    const PERMISSION_JOB_TRANSFER_AUDIT_AUDIT_ID = 269;// 转岗管理-批量转岗审核
    const PERMISSION_JOB_TRANSFER_AUDIT_SEARCH_ID = 270;// 转岗管理-批量转岗查询
    const PERMISSION_JOB_TRANSFER_AUDIT_VIEW_ID = 271;// 转岗管理-批量转岗查看

    // 数据模块
    const PERMISSION_JOB_TRANSFER_SEARCH_MENU_ID = 274;// 转岗管理-查询
    const PERMISSION_JOB_TRANSFER_SEARCH_SEARCH_ID = 275;// 转岗管理-转岗数据查询
    const PERMISSION_JOB_TRANSFER_SEARCH_VIEW_ID = 276;// 转岗管理-转岗数据查看
    const PERMISSION_JOB_TRANSFER_SEARCH_EXPORT_ID = 278;// 转岗管理-转岗数据导出
    const PERMISSION_JOB_TRANSFER_SEARCH_TRANSFER_ID = 280;// 转岗管理-转岗数据立即转岗
    const PERMISSION_JOB_TRANSFER_SEARCH_EDIT_ID = 1178;// 转岗管理-数据查询-编辑
    const PERMISSION_JOB_TRANSFER_SEARCH_EXPORT_PAYROLL_ID = 1182;// 转岗管理-数据查询-导出(payroll)
    const PERMISSION_JOB_TRANSFER_SEARCH_ACTIVATE_ID = 1183;// 转岗管理-数据查询-激活确认单

    // 特殊申请模块
    const PERMISSION_JOB_TRANSFER_SPECIAL_APPLY_MENU_ID = 1184;// 特殊转岗申请
    //const PERMISSION_JOB_TRANSFER_SPECIAL_APPLY_ADD_ID = 1185; // 特殊转岗申请-转岗申请
    const PERMISSION_JOB_TRANSFER_SPECIAL_APPLY_SEARCH_ID = 1186; // 特殊转岗申请-查询
    //const PERMISSION_JOB_TRANSFER_SPECIAL_APPLY_VIEW_ID = 1187; // 特殊转岗申请-查看

    // BP审核模块
    const PERMISSION_JOB_TRANSFER_BP_AUDIT_MENU_ID = 1192;// BP转岗审核
    const PERMISSION_JOB_TRANSFER_BP_AUDIT_ADD_ID = 1193; // BP转岗审核-审批
    const PERMISSION_JOB_TRANSFER_BP_AUDIT_SEARCH_ID = 1194; // BP转岗审核-列表查询
    const PERMISSION_JOB_TRANSFER_BP_AUDIT_VIEW_ID = 1195; // BP转岗审核-查看详情

    // 转岗模块全部权限
    static $job_transfer_all_permission = [
        self::PERMISSION_JOB_TRANSFER_MENU_ID,
        self::PERMISSION_JOB_TRANSFER_APPLY_MENU_ID,
        self::PERMISSION_JOB_TRANSFER_APPLY_ADD_ID,
        self::PERMISSION_JOB_TRANSFER_APPLY_SEARCH_ID,
        self::PERMISSION_JOB_TRANSFER_APPLY_VIEW_ID,

        self::PERMISSION_JOB_TRANSFER_AUDIT_MENU_ID,
        self::PERMISSION_JOB_TRANSFER_AUDIT_AUDIT_ID,
        self::PERMISSION_JOB_TRANSFER_AUDIT_SEARCH_ID,
        self::PERMISSION_JOB_TRANSFER_AUDIT_VIEW_ID,

        self::PERMISSION_JOB_TRANSFER_SEARCH_MENU_ID,
        self::PERMISSION_JOB_TRANSFER_SEARCH_SEARCH_ID,
        self::PERMISSION_JOB_TRANSFER_SEARCH_VIEW_ID,
        self::PERMISSION_JOB_TRANSFER_SEARCH_EXPORT_ID,
        self::PERMISSION_JOB_TRANSFER_SEARCH_TRANSFER_ID,
        self::PERMISSION_JOB_TRANSFER_SEARCH_EDIT_ID,
        self::PERMISSION_JOB_TRANSFER_SEARCH_EXPORT_PAYROLL_ID,
        self::PERMISSION_JOB_TRANSFER_SEARCH_ACTIVATE_ID,

        //特殊转岗手动赋权
        //self::PERMISSION_JOB_TRANSFER_SPECIAL_APPLY_MENU_ID,
        //self::PERMISSION_JOB_TRANSFER_SPECIAL_APPLY_ADD_ID,
        //self::PERMISSION_JOB_TRANSFER_SPECIAL_APPLY_SEARCH_ID,
        //self::PERMISSION_JOB_TRANSFER_SPECIAL_APPLY_VIEW_ID,

        self::PERMISSION_JOB_TRANSFER_BP_AUDIT_MENU_ID,
        self::PERMISSION_JOB_TRANSFER_BP_AUDIT_ADD_ID,
        self::PERMISSION_JOB_TRANSFER_BP_AUDIT_SEARCH_ID,
        self::PERMISSION_JOB_TRANSFER_BP_AUDIT_VIEW_ID,
    ];

    // 菜单 层级 关系
    static $menu_ancestry_map = [
        // 子菜单 => 父菜单
        self::PERMISSION_JOB_TRANSFER_MENU_ID               => self::PERMISSION_DEFAULT_TOP_MENU_ID,
        self::PERMISSION_JOB_TRANSFER_APPLY_MENU_ID         => self::PERMISSION_JOB_TRANSFER_MENU_ID,
        self::PERMISSION_JOB_TRANSFER_AUDIT_MENU_ID         => self::PERMISSION_JOB_TRANSFER_MENU_ID,
        self::PERMISSION_JOB_TRANSFER_SEARCH_MENU_ID        => self::PERMISSION_JOB_TRANSFER_MENU_ID,
        self::PERMISSION_JOB_TRANSFER_SPECIAL_APPLY_MENU_ID => self::PERMISSION_JOB_TRANSFER_MENU_ID,
        self::PERMISSION_JOB_TRANSFER_BP_AUDIT_MENU_ID      => self::PERMISSION_JOB_TRANSFER_MENU_ID,

        self::PERMISSION_JOB_TRANSFER_APPLY_ADD_ID    => self::PERMISSION_JOB_TRANSFER_APPLY_MENU_ID,
        self::PERMISSION_JOB_TRANSFER_APPLY_SEARCH_ID => self::PERMISSION_JOB_TRANSFER_APPLY_MENU_ID,
        self::PERMISSION_JOB_TRANSFER_APPLY_VIEW_ID   => self::PERMISSION_JOB_TRANSFER_APPLY_MENU_ID,

        self::PERMISSION_JOB_TRANSFER_AUDIT_AUDIT_ID  => self::PERMISSION_JOB_TRANSFER_AUDIT_MENU_ID,
        self::PERMISSION_JOB_TRANSFER_AUDIT_SEARCH_ID => self::PERMISSION_JOB_TRANSFER_AUDIT_MENU_ID,
        self::PERMISSION_JOB_TRANSFER_AUDIT_VIEW_ID   => self::PERMISSION_JOB_TRANSFER_AUDIT_MENU_ID,

        self::PERMISSION_JOB_TRANSFER_SEARCH_SEARCH_ID         => self::PERMISSION_JOB_TRANSFER_SEARCH_MENU_ID,
        self::PERMISSION_JOB_TRANSFER_SEARCH_VIEW_ID           => self::PERMISSION_JOB_TRANSFER_SEARCH_MENU_ID,
        self::PERMISSION_JOB_TRANSFER_SEARCH_EXPORT_ID         => self::PERMISSION_JOB_TRANSFER_SEARCH_MENU_ID,
        self::PERMISSION_JOB_TRANSFER_SEARCH_TRANSFER_ID       => self::PERMISSION_JOB_TRANSFER_SEARCH_MENU_ID,
        self::PERMISSION_JOB_TRANSFER_SEARCH_EDIT_ID           => self::PERMISSION_JOB_TRANSFER_SEARCH_MENU_ID,
        self::PERMISSION_JOB_TRANSFER_SEARCH_EXPORT_PAYROLL_ID => self::PERMISSION_JOB_TRANSFER_SEARCH_MENU_ID,
        self::PERMISSION_JOB_TRANSFER_SEARCH_ACTIVATE_ID       => self::PERMISSION_JOB_TRANSFER_SEARCH_MENU_ID,

        //self::PERMISSION_JOB_TRANSFER_SPECIAL_APPLY_ADD_ID    => self::PERMISSION_JOB_TRANSFER_SPECIAL_APPLY_MENU_ID,
        //self::PERMISSION_JOB_TRANSFER_SPECIAL_APPLY_SEARCH_ID => self::PERMISSION_JOB_TRANSFER_SPECIAL_APPLY_MENU_ID,
        //self::PERMISSION_JOB_TRANSFER_SPECIAL_APPLY_VIEW_ID   => self::PERMISSION_JOB_TRANSFER_SPECIAL_APPLY_MENU_ID,

        self::PERMISSION_JOB_TRANSFER_BP_AUDIT_ADD_ID    => self::PERMISSION_JOB_TRANSFER_BP_AUDIT_MENU_ID,
        self::PERMISSION_JOB_TRANSFER_BP_AUDIT_SEARCH_ID => self::PERMISSION_JOB_TRANSFER_BP_AUDIT_MENU_ID,
        self::PERMISSION_JOB_TRANSFER_BP_AUDIT_VIEW_ID   => self::PERMISSION_JOB_TRANSFER_BP_AUDIT_MENU_ID,
    ];

    /***** 转岗模块菜单权限配置 End *****/


    /***** 转岗模块需分配权限的组织架构配置 Start *****/
    const PERMISSION_ASSIGN_ORGANIZATION_TYPE = 'organization';
    const ORGANIZATION_STORE_MANAGER = 'store_manager'; // 网点负责人
    const ORGANIZATION_PIECE_AREA_MANAGER = 'piece_area_manager';// 片区负责人
    const ORGANIZATION_REGION_MANAGER = 'region_manager';// 大区负责人
    const ORGANIZATION_DEPARTMENT_MANAGER = 'department_manager';// 部门负责人
    const ORGANIZATION_STAFF_MANAGER = 'staff_manager'; //上级
    const ORGANIZATION_SPEC_JOB_TITLE = 'spec_job_title'; //指定职位

    /***** 转岗模块需分配权限的组织架构配置 End *****/


    /***** 转岗模块组织架构主体 与 菜单权限的映射关系 Start *****/
    //OA-组织架构各组织负责人以及负责人助理以及AM助理（指定职位）权限
    const DEFAULT_ORGANIZATION_PERMISSION = [
        self::PERMISSION_JOB_TRANSFER_MENU_ID,

        self::PERMISSION_JOB_TRANSFER_APPLY_MENU_ID,
        self::PERMISSION_JOB_TRANSFER_APPLY_ADD_ID,
        self::PERMISSION_JOB_TRANSFER_APPLY_SEARCH_ID,
        self::PERMISSION_JOB_TRANSFER_APPLY_VIEW_ID,

        self::PERMISSION_JOB_TRANSFER_AUDIT_MENU_ID,
        self::PERMISSION_JOB_TRANSFER_AUDIT_AUDIT_ID,
        self::PERMISSION_JOB_TRANSFER_AUDIT_SEARCH_ID,
        self::PERMISSION_JOB_TRANSFER_AUDIT_VIEW_ID,

        self::PERMISSION_JOB_TRANSFER_SEARCH_MENU_ID,  // 转岗管理-查询
        self::PERMISSION_JOB_TRANSFER_SEARCH_SEARCH_ID,// 转岗管理-转岗数据查询
        self::PERMISSION_JOB_TRANSFER_SEARCH_VIEW_ID,  // 转岗管理-转岗数据查看
        self::PERMISSION_JOB_TRANSFER_SEARCH_EXPORT_ID,// 转岗管理-转岗数据导出
    ];

    //大区经理、片区经理、网点主管、分拨经理权限
    const BASIC_ROLE_PERMISSION = [
        self::PERMISSION_JOB_TRANSFER_MENU_ID,

        self::PERMISSION_JOB_TRANSFER_APPLY_MENU_ID,
        self::PERMISSION_JOB_TRANSFER_APPLY_ADD_ID,
        self::PERMISSION_JOB_TRANSFER_APPLY_SEARCH_ID,
        self::PERMISSION_JOB_TRANSFER_APPLY_VIEW_ID,
    ];

    static $organization_permission_map = [
        // 网点负责人权限
        self::ORGANIZATION_STORE_MANAGER      => self::DEFAULT_ORGANIZATION_PERMISSION,
        //指定职位
        self::ORGANIZATION_SPEC_JOB_TITLE     => self::DEFAULT_ORGANIZATION_PERMISSION,
        // 片区负责人权限
        self::ORGANIZATION_PIECE_AREA_MANAGER => self::DEFAULT_ORGANIZATION_PERMISSION,
        // 大区负责人权限
        self::ORGANIZATION_REGION_MANAGER     => self::DEFAULT_ORGANIZATION_PERMISSION,
        // 部门负责人权限
        self::ORGANIZATION_DEPARTMENT_MANAGER => self::DEFAULT_ORGANIZATION_PERMISSION,

        self::ORGANIZATION_STAFF_MANAGER => [
            self::PERMISSION_JOB_TRANSFER_MENU_ID,

            self::PERMISSION_JOB_TRANSFER_SEARCH_MENU_ID,  // 转岗管理-查询
            self::PERMISSION_JOB_TRANSFER_SEARCH_SEARCH_ID,// 转岗管理-转岗数据查询
            self::PERMISSION_JOB_TRANSFER_SEARCH_VIEW_ID,  // 转岗管理-转岗数据查看
            self::PERMISSION_JOB_TRANSFER_SEARCH_EXPORT_ID,// 转岗管理-转岗数据导出
        ],
    ];
    /***** 转岗模块组织架构主体 与 菜单权限的映射关系 End *****/


    /***** 转岗模块需分配权限的角色配置 Start *****/
    const PERMISSION_ASSIGN_ROLES_TYPE = 'roles';
    const ROLES_HRBP_ID = 68;// HRBP
    const ROLES_HR_MANAGEMENT_ID = 17;// HR Management
    const ROLES_PAYROLL_ID = 42;// Payroll
    const ROLES_HRIS_MANAGER_ID = 41;// HRIS管理员
    const ROLES_SUPER_MANAGER_ID = 99;// 超级管理员
    const ROLES_AREA_MANAGER_ID = 56; // 大区经理
    const ROLES_DISTRICT_MANAGER_ID = 57; // 片区经理
    const ROLES_DOT_ADMINISTRATOR = 18; //网点主管
    const ROLES_DISTRIBUTION_MANAGER = 60; //分拨经理
    const ROLES_TA_ID = 121; //网络TA

    static $roles_ids = [
        self::ROLES_HRBP_ID,
        self::ROLES_HR_MANAGEMENT_ID,
        self::ROLES_PAYROLL_ID,
        self::ROLES_HRIS_MANAGER_ID,
        self::ROLES_SUPER_MANAGER_ID,
        self::ROLES_AREA_MANAGER_ID,
        self::ROLES_DISTRICT_MANAGER_ID,
        self::ROLES_DOT_ADMINISTRATOR,
        self::ROLES_DISTRIBUTION_MANAGER,
        self::ROLES_TA_ID,
    ];

    /***** 转岗模块需分配权限的角色配置 End *****/


    /***** 转岗模块 角色主体 与 菜单权限的映射关系 Start *****/
    static $roles_permission_map = [
        // HRBP
        self::ROLES_HRBP_ID => [
            self::PERMISSION_JOB_TRANSFER_MENU_ID,

            self::PERMISSION_JOB_TRANSFER_BP_AUDIT_MENU_ID,   // BP转岗审核
            self::PERMISSION_JOB_TRANSFER_BP_AUDIT_ADD_ID,    // BP转岗审核-审批
            self::PERMISSION_JOB_TRANSFER_BP_AUDIT_SEARCH_ID, // BP转岗审核-列表查询
            self::PERMISSION_JOB_TRANSFER_BP_AUDIT_VIEW_ID,   // BP转岗审核-查看详情

            self::PERMISSION_JOB_TRANSFER_SEARCH_MENU_ID,
            self::PERMISSION_JOB_TRANSFER_SEARCH_SEARCH_ID,
            self::PERMISSION_JOB_TRANSFER_SEARCH_VIEW_ID,
            self::PERMISSION_JOB_TRANSFER_SEARCH_EDIT_ID,
            self::PERMISSION_JOB_TRANSFER_SEARCH_EXPORT_ID,
            self::PERMISSION_JOB_TRANSFER_SEARCH_TRANSFER_ID,
            self::PERMISSION_JOB_TRANSFER_SEARCH_ACTIVATE_ID,
        ],

        // HR management
        self::ROLES_HR_MANAGEMENT_ID => [
            self::PERMISSION_JOB_TRANSFER_MENU_ID,

            self::PERMISSION_JOB_TRANSFER_SEARCH_MENU_ID,
            self::PERMISSION_JOB_TRANSFER_SEARCH_SEARCH_ID,
            self::PERMISSION_JOB_TRANSFER_SEARCH_VIEW_ID,
            self::PERMISSION_JOB_TRANSFER_SEARCH_EDIT_ID,
            self::PERMISSION_JOB_TRANSFER_SEARCH_EXPORT_ID,
        ],

        // Payroll
        self::ROLES_PAYROLL_ID => [
            self::PERMISSION_JOB_TRANSFER_MENU_ID,

            self::PERMISSION_JOB_TRANSFER_SEARCH_MENU_ID,
            self::PERMISSION_JOB_TRANSFER_SEARCH_SEARCH_ID,
            self::PERMISSION_JOB_TRANSFER_SEARCH_VIEW_ID,
            self::PERMISSION_JOB_TRANSFER_SEARCH_EXPORT_ID,
            self::PERMISSION_JOB_TRANSFER_SEARCH_EXPORT_PAYROLL_ID,
        ],

        // HRIS 管理员
        self::ROLES_HRIS_MANAGER_ID => [
            self::PERMISSION_JOB_TRANSFER_MENU_ID,

            self::PERMISSION_JOB_TRANSFER_SEARCH_MENU_ID,
            self::PERMISSION_JOB_TRANSFER_SEARCH_SEARCH_ID,
            self::PERMISSION_JOB_TRANSFER_SEARCH_VIEW_ID,
            self::PERMISSION_JOB_TRANSFER_SEARCH_EDIT_ID,
            self::PERMISSION_JOB_TRANSFER_SEARCH_EXPORT_ID,
            self::PERMISSION_JOB_TRANSFER_SEARCH_TRANSFER_ID,
            self::PERMISSION_JOB_TRANSFER_SEARCH_ACTIVATE_ID,
        ],

        // 超级管理员
        self::ROLES_SUPER_MANAGER_ID => [
            self::PERMISSION_JOB_TRANSFER_MENU_ID,

            self::PERMISSION_JOB_TRANSFER_APPLY_MENU_ID,
            self::PERMISSION_JOB_TRANSFER_APPLY_ADD_ID,
            self::PERMISSION_JOB_TRANSFER_APPLY_SEARCH_ID,
            self::PERMISSION_JOB_TRANSFER_APPLY_VIEW_ID,

            self::PERMISSION_JOB_TRANSFER_AUDIT_MENU_ID,
            self::PERMISSION_JOB_TRANSFER_AUDIT_AUDIT_ID,
            self::PERMISSION_JOB_TRANSFER_AUDIT_SEARCH_ID,
            self::PERMISSION_JOB_TRANSFER_AUDIT_VIEW_ID,

            self::PERMISSION_JOB_TRANSFER_SEARCH_MENU_ID,
            self::PERMISSION_JOB_TRANSFER_SEARCH_SEARCH_ID,
            self::PERMISSION_JOB_TRANSFER_SEARCH_VIEW_ID,
            self::PERMISSION_JOB_TRANSFER_SEARCH_EXPORT_ID,
            self::PERMISSION_JOB_TRANSFER_SEARCH_TRANSFER_ID,
            self::PERMISSION_JOB_TRANSFER_SEARCH_EDIT_ID,
            self::PERMISSION_JOB_TRANSFER_SEARCH_ACTIVATE_ID,

            self::PERMISSION_JOB_TRANSFER_BP_AUDIT_MENU_ID,
            self::PERMISSION_JOB_TRANSFER_BP_AUDIT_ADD_ID,
            self::PERMISSION_JOB_TRANSFER_BP_AUDIT_SEARCH_ID,
            self::PERMISSION_JOB_TRANSFER_BP_AUDIT_VIEW_ID,
        ],

        // 大区经理
        self::ROLES_AREA_MANAGER_ID      => self::BASIC_ROLE_PERMISSION,
        // 片区经理
        self::ROLES_DISTRICT_MANAGER_ID  => self::BASIC_ROLE_PERMISSION,
        // 网点主管
        self::ROLES_DOT_ADMINISTRATOR    => self::BASIC_ROLE_PERMISSION,
        // 分拨经理
        self::ROLES_DISTRIBUTION_MANAGER => self::BASIC_ROLE_PERMISSION,

        // 网络TA
        self::ROLES_TA_ID => [
            self::PERMISSION_JOB_TRANSFER_MENU_ID,

            self::PERMISSION_JOB_TRANSFER_SEARCH_MENU_ID,
            self::PERMISSION_JOB_TRANSFER_SEARCH_SEARCH_ID,
            self::PERMISSION_JOB_TRANSFER_SEARCH_VIEW_ID,
            self::PERMISSION_JOB_TRANSFER_SEARCH_EXPORT_ID,
            self::PERMISSION_JOB_TRANSFER_SEARCH_TRANSFER_ID,
            self::PERMISSION_JOB_TRANSFER_SEARCH_ACTIVATE_ID,
        ],

    ];
    /***** 转岗模块 角色主体 与 菜单权限的映射关系 End *****/


    //转岗状态
    const JOB_TRANSFER_STATE_PENDING = 1;   //待转岗
    const JOB_TRANSFER_STATE_NO = 2;        //未转岗
    const JOB_TRANSFER_STATE_SUCCESS = 3;   //转岗成功
    const JOB_TRANSFER_STATE_FAIL = 4;      //转岗失败
    public static $job_transfer_state = [
        self::JOB_TRANSFER_STATE_PENDING => 'job_transfer_state.1', //待转岗
        self::JOB_TRANSFER_STATE_NO      => 'job_transfer_state.2', //未转岗
        self::JOB_TRANSFER_STATE_SUCCESS => 'job_transfer_state.3', //转岗成功
        self::JOB_TRANSFER_STATE_FAIL    => 'job_transfer_state.4', //转岗失败
    ];

    const BTN_SHOW_STATE_HIDE = 0; //button显示状态，隐藏
    const BTN_SHOW_STATE_SHOW = 1; //button显示状态，显示

    //人脸确认状态
    const CONFIRM_STATE_PENDING = 1;   //待确认
    const CONFIRM_STATE_PASS = 2;      //确认通过
    const CONFIRM_STATE_REJECT = 3;   //确认驳回

    //车类型
    const CAR_TYPE_VAN = 1; //van
    const CAR_TYPE_BIKE = 2; //bike

    //获取转岗审批下拉
    const STATE_TYPE_APPLY = 1; //申请，涵盖（待审批、已同意、已驳回、已撤销）
    const STATE_TYPE_TOTAL = 2; //查询，涵盖（待审批、已同意、已驳回、已撤销，已超时）

    //职位ID
    const JOB_VAN_TITLE_ID = 110;
    const JOB_BIKE_TITLE_ID = 13;
    const JOB_TRICYCLE_COURIER_TITLE_ID = 1000;
    const JOB_VAN_PROJECT_TITLE_ID = 1015;
    const JOB_VAN_FEEDER_TITLE_ID = 1497;
    const JOB_BOAT_COURIER_TITLE_ID = 452;
    const JOB_COURIER_AND_INSTALLATION_STAFF_TITLE_ID = 1663;
    const JOB_PICKUP_DRIVER = 1844;
    const JOB_EV_TITLE_ID = 1930;
    const JOB_CAR_TITLE_ID = 1199;

    // 车类型
    const VEHICLE_TYPE_CATEGORY_LIST = [
        1 => 'Pick-up (sedan)',
        2 => 'Van',
        3 => 'Lorry',
        4 => 'Van Project - Van',
        5 => 'Panel Van',
        6 => 'Window Van',
        7 => 'Car',
        8 => 'MPV',
        9 => 'Van Project - Lorry',
    ];

    /**
     * 职位对应车型号关系
     */
    const JOB_TITLE_VEHICLE_CATEGORY_ITEM = [
        self::JOB_VAN_TITLE_ID => [
            ['value' => 3, 'label' => self::VEHICLE_TYPE_CATEGORY_LIST[3]],
            ['value' => 4, 'label' => self::VEHICLE_TYPE_CATEGORY_LIST[4]],
            ['value' => 5, 'label' => self::VEHICLE_TYPE_CATEGORY_LIST[5]],
            ['value' => 6, 'label' => self::VEHICLE_TYPE_CATEGORY_LIST[6]],
            ['value' => 9, 'label' => self::VEHICLE_TYPE_CATEGORY_LIST[9]],
        ],
        self::JOB_CAR_TITLE_ID => [
            ['value' => 7, 'label' => self::VEHICLE_TYPE_CATEGORY_LIST[7]],
            ['value' => 8, 'label' => self::VEHICLE_TYPE_CATEGORY_LIST[8]],
        ],
    ];

    //转岗操作，编辑转岗信息
    const JOB_TRANSFER_EDIT_TRANSFER_INFO = 6;

    //转岗申请数据来源
    const JOB_TRANSFER_DATA_SOURCE_OA = 2; // 来源于OA申请

    //薪资类型 1=薪资不变 2=薪资结构
    const SALARY_TYPE_NOT_CHANGE = 1;
    const SALARY_TYPE_SALARY_STRUCTURE = 2;
    //期数
    const PROJECT_NUM_1 = 1;
    const PROJECT_NUM_2 = 2;
    const PROJECT_NUM_3 = 3;
    const PROJECT_NUM_4 = 4;
}
