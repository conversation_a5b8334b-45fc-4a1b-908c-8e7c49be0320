<?php
namespace App\Library\Enums;
final class MaterialAssetOutStorageEnums
{
    //资产出库单导出时间锁
    const MATERIAL_ASSET_OUT_STORAGE_EXPORT_LOCK='material_asset_out_storage_export_lock';
    const MATERIAL_ASSET_OUT_EXPORT_LIMIT = 30000;

    //资产出库单编号前缀
    const MATERIAL_ASSET_OUT_STORAGE_NO_PREFIX = 'AOR';

    const STATUS_WAIT_APPROVE = 1;//出库状态-待审核
    const STATUS_APPROVED_WAIT_OUT = 2;//出库状态-已审核待出库
    const STATUS_OUT = 3;//已出库
    const STATUS_CANCEL = 4;//已作废

    const THIS_TIME_NUM = 1;//已经出库发放数量默认1

    //出库状态
    public static $asset_out_storage_status = [
        self::STATUS_WAIT_APPROVE => 'material_asset_out_storage_status.'.self::STATUS_WAIT_APPROVE,
        self::STATUS_APPROVED_WAIT_OUT => 'material_asset_out_storage_status.'.self::STATUS_APPROVED_WAIT_OUT,
        self::STATUS_OUT => 'material_asset_out_storage_status.'.self::STATUS_OUT,
        self::STATUS_CANCEL => 'material_asset_out_storage_status.'.self::STATUS_CANCEL,
    ];
    const THIS_TIME_NUMBER_RULE = '/^[1-9][0-9]{0,2}$|^1000$/';
}
