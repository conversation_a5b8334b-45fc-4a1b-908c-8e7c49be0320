<?php

namespace App\Library\Enums;

/**
 * 配置项枚举数据
 * Class enums
 */
final class SettingEnums
{
    const SETTING_ENUMS_IS_EDIT = 2;

    /*********************
     * 财务管理审批人员枚举 *
     * *******************
     * */
    //是否包含子级部门
    const IS_NO_INCLUDE_SUB = 0;                                 //不包含
    const IS_INCLUDE_SUB = 1;                                    //包含

    //数据来源 0申请 1 计算
    const SOURCE_TYPE_1 = 0;
    const SOURCE_TYPE_2 = 1;

    //是否配置管辖部门 1未配置2已配置
    const CONFIGURE_STATE_1 = 1;
    const CONFIGURE_STATE_2 = 2;
    public static $configure_state = [
        self::CONFIGURE_STATE_1 => 'financial_configure_state_1',
        self::CONFIGURE_STATE_2 => 'financial_configure_state_2',
    ];

    //分组类型
    const WORKFLOW_STAFF_MANAGE_GROUP_TYPE_FINANCE = 1;          //财务部门普通分组
    const WORKFLOW_STAFF_MANAGE_GROUP_TYPE_SPECIAL = 2;          //财务部门特殊分组


    /******** 系统配置管理 - 业务配置 Start ********/
    // 发票抬头管理
    // 发票抬头对应BU类型 0-不限(即 全部BU)
    const INVOICE_HEADER_RELATED_COMPANY_TYPE_ALL = 0;

    // 发票抬头的对应公司 与 费用所属公司: 1一致; 2不一致
    const INVOICE_HEADER_COMPANY_AND_COST_COMPANY_SAME = 1;
    const INVOICE_HEADER_COMPANY_AND_COST_COMPANY_NOT_SAME = 2;


    /******** 系统配置管理 - 业务配置 End ********/


    /*** 词库翻译同步配置 Start ****/
    // i18n词库文件地址
    const I18N_TRANSLATION_SYNC_API = 'https://ard-static.flashexpress.com/oa-api/lang';

    // 从i18n词库拉取的语言
    const I18N_TRANSLATION_SUPPORT_LANGUAGES = [
        'en',
        'th',
        'zh-CN',
    ];

    // 翻译版本缓存有效期
    const I18N_TRANSLATION_CATCH_PERIOD = 86400 * 365;

    // oa-api http拉取翻译鉴权码
    const HTTP_PULL_LANGUAGE_AUTH_CODE = 'a3874d318320d494559ac1a0a16cc8ca';

    /*** 词库翻译同步配置 End ****/

    /*** 数据配置 - 通用数据配置  Start ***/
    // 数据权限分类的配置维度 configure_reference
    const DATA_PERMISSION_CATEGORY_CONFIGURE_REFERENCE_JOB = 1;  // 根据职位配置
    const DATA_PERMISSION_CATEGORY_CONFIGURE_REFERENCE_STAFF = 2;// 根据管辖人

    // 多实体的权限类型
    const DATA_PERMISSION_TYPE_DEFAULT = 'single_entity';        // 默认单实体方式, 即单表的字段作为取数条件
    const DATA_PERMISSION_TYPE_MULTI_ENTIEY = 'multi_entity';    // 多实体方式, 即业务模块多个表的字段作为取数条件

    /*** 数据配置 - 通用数据配置  END ***/


}
