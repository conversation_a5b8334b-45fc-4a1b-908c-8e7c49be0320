<?php

namespace App\Library\Enums;

/**
 * by审批流 审批人查找逻辑相关枚举
 */
class WorkflowManageEnums
{
    //审批流管理-添加审批人-指定职位限定条件
    //1=不限 2=与申请人所在部门相同 3=与申请人在相同1级部门下 4=指定部门
    const SPECIFY_POSITION_QUALIFY_COND_NO_LIMIT = 1;
    const SPECIFY_POSITION_QUALIFY_COND_SAME_DEPARTMENT = 2;
    const SPECIFY_POSITION_QUALIFY_COND_SAME_LEVEL_DEPARTMENT = 3;
    const SPECIFY_POSITION_QUALIFY_COND_SPEC_DEPARTMENT = 4;

    //审批节点对应审批人
    const WF_NODE_DESIGNATE_OTHER = 2; //指定的员工工号
    const WF_NODE_MANAGER         = 3; //上级
    const WF_NODE_DEPARTMENT_MANAGER = 4; //部门负责人
    const WF_NODE_SUPERVISOR      = 5; //supervisor
    const WF_NODE_DM              = 6; //DM
    const WF_NODE_RM              = 7; //RM
    const WF_NODE_AM              = 8; //AM
    const WF_NODE_GROUP_BOSS      = 9; //Group Boss
    const WF_NODE_CEO             = 10; //CEO
    const WF_NODE_COO             = 11; //COO
    const WF_NODE_CFO             = 12; //CFO
    const WF_NODE_CPO             = 13; //CPO
    const WF_NODE_SPEC_DEPARTMENT_MANAGER = 14; //指定部门负责人
    const WF_NODE_HRBP            = 15; //部门对应的HRBP
    const WF_NODE_STORE_MANAGER   = 16; //网点负责人
    const WF_NODE_MULTI_DEPARTMENT_MANAGER   = 17; //连续多个部门负责人
    const WF_NODE_DM_BY_ORG       = 18; //根据组织架构去找DM
    const WF_NODE_AM_BY_ORG       = 19; //根据组织架构去找AM
    const WF_NODE_SS              = 20; // shop supervisor
    const WF_NODE_SOM             = 21; //shop operations manager
    const WF_FIRST_LEVEL_DEPARTMENT_MANAGER = 23;//部门是BU级别，找当前BU负责人；如果是 C-Level级别 找当前c-level负责人；如果是普通部门则直接找一级部门负责人
    const WF_NODE_JOB_IDS         = 22; // 根据网点类型获取职位下的人
    const WF_APPLICANT            = 24; // 审核人等于申请人
    const WF_NODE_FIRST_BU_DEPARTMENT_MANAGER = 25; // 申请人部门属于BU找 BU负责人 ； 如果部门属于cLevel找 clevel负责人； 否则找GroupCEO
    const WF_NODE_CLEVEL_MANAGER  = 26; // 找部门clevel负责人
    const WF_NODE_FIRST_BU_DEPARTMENT_MANAGER_BY_FORM = 27; // FORM部门属于BU找 BU负责人 ； 如果部门属于cLevel找 clevel负责人； 否则找GroupCEO
    const WF_NODE_BP_HEAD         = 28; //找BP Head
    const WF_NODE_JOB_TITLE_SAME_DEPT = 29; //指定职位-与申请人所在部门相同
    const WF_NODE_JOB_TITLE_SAME_FIRST_DEPT = 30; //指定职位-与申请人所在一级部门相同

    const WF_NODE_ROLE      = 31; //根据指定角色查找审批人
    const WF_NODE_HR_SERVICE = 32; //对应管辖范围的HR Service角色

    const WF_NODE_AUDITOR_TYPE_COMPLEX = 99; //存在多个节点逻辑复合
    // 编号100以上为不通用的逻辑
    const WF_NODE_SPEC_JOB_TITLE = 101; //申请人上级或上上级，有Sales Manager职位
    const WF_NODE_DI = 102; // 审批人动态传入
    const WF_NODE_SUPERVISOR_MANAGER = 103; //找网点正主管如果找不到则找申请人的上2级上级

    const WF_NODE_ASSET_HUB_MANAGER = 105; //根据申请人所在网点 找对应网点的 xx职位的人
    const WF_NODE_HUB_HEADQUARTER_JOB_TITLE = 106; //属于Hub Headquarter的 xx职位的人
    const WF_NODE_DATA_SUPPORT_JOB_TITLE = 107; //属于属于Data Support小组的 xx职位的人
    const WF_NODE_JOB_TITLE = 108; //属获取职位下的人

    const WF_NODE_DEPARTMENT_MANAGER_V3 = 110; //根据申请人所在部门查找申请人组织负责人 [1 ~ 4级部门负责人 or BU or Clevel]
    const WF_NODE_DEPARTMENT_MANAGER_V3_BY_FORM = 111; //根据FORM提交的部门查找申请人组织负责人 [1 ~ 4级部门负责人 or BU or Clevel]
    const WF_NODE_DEPARTMENT_JOB_TITLE_FORM = 112; //获取指定部门指定职位的人
    const WF_NODE_MANAGER_FROM         = 113; //根据表单找上级
    const WF_NODE_STORE_MANAGER_FROM   = 114; //根据表单找网点负责人
    const WF_NODE_DM_BY_ORG_FROM       = 115; //根据表单组织架构去找DM
    const WF_NODE_AM_BY_ORG_FROM       = 116; //根据表单组织架构去找AM
    const WF_NODE_HRBP_FROM            = 117; //根据表单找部门对应的HRBP
    const WF_NODE_HASHTABLE_FROM       = 118; //(废弃)根据表单找分拨经理
    const WF_NODE_HUB_AREA_MANAGER     = 119; //根据表单-工作网点、组织架构去找分拨经理
    const WF_NODE_HUB_STORE_MANAGER = 122; //分拨经理(For: 分拨外协补卡)
    const WF_NODE_HUB_SUB_DEPARTMENT_BY_NAME = 123; //分拨标准化or行政
    const WF_NODE_STORE_MANAGER_MUL_FROM= 124; //根据表单找网点负责人(多个网点版本)
    const WF_NODE_DM_BY_ORG_MUL_FROM    = 125; //根据表单组织架构去找DM(多个网点版本)
    const WF_NODE_AM_BY_ORG_MUL_FROM    = 126; //根据表单组织架构去找AM(多个网点版本)
    const WF_NODE_DEPARTMENT_MANAGER_MUL_BY_FORM = 127; //根据FORM提交的部门查找申请人组织负责人(多部门) [1 ~ 4级部门负责人 or BU or Clevel]
    const WF_NODE_HRBP_MUL_FROM         = 128; //根据表单找部门对应的HRBP(多个部门、网点版本)
    const WF_NODE_BU_C_LEVEL_FROM       = 129; //FORM部门属于BU找 BU负责人 ； 如果部门属于cLevel找 clevel负责人； 否则找GroupCEO(多个部门)
    const WF_NODE_HR_SERVICE_FROM       = 130; //根据表单找部门对应的HR SERVICE
    const WF_NODE_STAFF_SELF_FROM       = 132; //根据表单-员工本人

    const SCOPE_ALL = 1; //数据范围 全部
    const SCOPE_PART = 2; //数据范围 部分

    //关于 OA 库 workflow_config 设置
    const WF_OA_APPROVAL_VALUE  = 15; //审批人等于申请人自动通过
    const WF_OA_AUTO_CNF_PASS   = 16; //1-审批人重复自动通过 2-审批人连续重复自动通过 3-审批人手动处理
    const WF_OA_AUTO_CNF_OT_TYPE   = 17; //审批超时类型 1=无超时 2=设置整体超时时间 3=设置单一节点超时时间 4=单独设置每个节点超时时间 5=设置表单中字段为超时时间
    const WF_OA_AUTO_CNF_OT_DAYS   = 18; //超时天数
    const WF_OA_AUTO_CNF_OT_POLICY   = 19; //超时后处理方式，1=自动通过 4=自动驳回 5=超时关闭
    const WF_OA_OT_FORM = 20; //表单超时字段

    //审批超时类型
    const WF_OVERTIME_TYPE_NONE = 1; //无超时
    const WF_OVERTIME_TYPE_OVERALL = 2; //设置整体超时时间
    const WF_OVERTIME_TYPE_EACH_NODE = 3; //设置单一节点超时时间
    const WF_OVERTIME_TYPE_INDIVIDUAL_NODE = 4; //单独设置每个节点超时时间
    const WF_OVERTIME_TYPE_FORM = 5; //设置表单中字段为超时时间

    //超时最大天数
    const OVERTIME_CONFIG_MAX_DAYS = 31;

    //超时后处理方式
    const WF_HANDLING_AFTER_OT_AUTO_PASS = 1; //自动通过
    const WF_HANDLING_AFTER_OT_AUTO_HANDLE_MANAGER = 2; //转交上级
    const WF_HANDLING_AFTER_OT_AUTO_HANDLE_SPEC_STAFF = 3; //转交指定员工
    const WF_HANDLING_AFTER_OT_AUTO_REJECT = 4; //自动驳回
    const WF_HANDLING_AFTER_OT_AUTO_CLOSED = 5; //超时关闭
    const WF_HANDLING_AFTER_OT_AUTO_HANDLE = 6; //自动转交

    //超时类型 1=设置表单字段为超时时间，2=最大审批自然日（天数）
    const WF_OVERTIME_SUB_TYPE_FORM_COLUMN = 1;
    const WF_OVERTIME_SUB_TYPE_AUDIT_DAYS = 2;

    //是否开启超时时效 0=关闭, 1=开启
    const WF_OVERTIME_SWITCH_CLOSED = 0;
    const WF_OVERTIME_SWITCH_OPEN = 1;

    //超时处理
    const WF_HANDLE_POLICY_SPEC_STAFF = 1; //转交指定工号
    const WF_HANDLE_POLICY_UPPER_MANAGER = 2; //转交上级

    //是否开启转交跳过特殊审批人 0=不开启 1=开启
    const WF_OT_HANDLE_TERMINATION_CLOSE = 0;
    const WF_OT_HANDLE_TERMINATION_OPEN = 1;
}