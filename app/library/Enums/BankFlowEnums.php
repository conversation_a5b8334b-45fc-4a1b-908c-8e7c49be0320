<?php

namespace App\Library\Enums;

use App\Library\Enums;

/**
 * 银行流水枚举配置
 * Class enums
 */
final class BankFlowEnums
{
    // 扩展表item
    const BANK_FLOW_META_BANK_FLOW_FROM_BANK = 'from_bank_acct_id';
    const BANK_FLOW_META_BANK_FLOW_TO_BANK = 'to_bank_acct_id';
    const BANK_FLOW_META_BANK_FLOW_REMARK = 'bank_flow_remark';
    // 扩展表不参与编辑的字段
    public static $bank_flow_meta_not_in = [
        self::BANK_FLOW_META_BANK_FLOW_REMARK,
    ];
    // 银行流水类型:1:收款;2:付款
    const BANK_FLOW_TYPE_GET_INCOME = 1;
    const BANK_FLOW_TYPE_PAY_OUTLAY = 2;

    // 银行流水确认状态: 流水关联了系统单即为已确认
    const BANK_FLOW_UNCONFIRMED_STATUS = 1;
    const BANK_FLOW_CONFIRMED_STATUS = 2;

    // 银行流水-费用类型翻译前缀
    public static $bank_flow_expense_type_prefix = [
        GlobalEnums::TH_COUNTRY_CODE => 'bank_flow.expense_type.',
        GlobalEnums::PH_COUNTRY_CODE => 'bank_flow.ph.expense_type.',
        GlobalEnums::MY_COUNTRY_CODE => 'bank_flow.my.expense_type.',
    ];
    // 银行流水上传文件的列名
    public static $bank_flow_file_header = [
        GlobalEnums::TH_COUNTRY_CODE => [
            self::BANK_CODE_SCB => [
                'account number',
                'date',
                'time',
                'transaction code',
                'channel',
                'cheque number',
                'debit amount',
                'credit amount',
                'balance sign',
                'balance amount',
                'description',
            ],

            self::BANK_CODE_K_BANK => [
                'effective date',
                'time',
                'description',
                'cheque no.',
                'debit amount',
                'credit amount',
                'balance',
                'teller code',
                'service branch',
                'entry date',
                'channel',
                'detail',
            ],

            self::BANK_CODE_HSBC        => [
                'account name',
                'account number',
                'bank name',
                'currency',
                'location',
                'bic',
                'iban',
                'account status',
                'account type',
                'closing ledger balance',
                'closing ledger brought forward from',
                'closing available balance',
                'closing available brought forward from',
                'current ledger balance',
                'current ledger as at',
                'current available balance',
                'current available as at',
                'bank reference',
                'narrative',
                'customer reference',
                'trn type',
                'value date',
                'credit amount',
                'debit amount',
                'time',
                'post date',
                'balance',
            ],

            // TMB == TTB
            self::BANK_CODE_TMB         => [
                'account number',
                'transaction date',
                'effective date',
                'transaction code',
                'cheque number',
                'withdrawal',
                'deposit',
                'balance',
                'teller id',
                'transaction description',
                'company name',
                'time',
                'currency',
                'channel',
                'statement enrichment',
                'cross reference no.',
                'additional 1',
                'additional 2',
                'additional 3',
                'additional 4',
                'additional 5',
                'additional 6',
            ],
            self::BANK_CODE_BAY         => [
                'postingdate',
                'txncode',
                'txn_ref',
                'bkdt',
                'debit',
                'credit',
                'balance',
                'bankrefno',
                'teller',
                'item',
                'time',
                'info',
                'batchrefno',
            ],
            self::BANK_CODE_KTB         => [
                'date',
                'teller id',
                'transaction code',
                'description',
                'cheque no.',
                'amount',
                'tax',
                'balance',
                'init br.',
            ],
            self::BANK_CODE_COMMON_BANK => [
                'account number',
                'time',
                'opening balance',
                'withdrawal',
                'deposit',
                'balance',
                'remark',
            ],
        ],
        GlobalEnums::PH_COUNTRY_CODE => [
            self::BANK_CODE_UNION          => [
                'transaction date',//交易日期
                'posted date',     //交易时间
                'transaction id',
                'transaction description',//描述
                'check number',           //支票号
                'debits',                 //付款金额
                'credits',                //收款金额
                'ending balance',         //余额
                'reference number',
                'remarks',
                'remarks 1',
                'remarks 2',
                'branch',
                'sender name',
                'sender address',
                'sender bank',
                'credited amount',
                'original amount',
                'net amount',
                'remittance ref 1',
                'remittance ref 2',
                'remittance ref 3',
                'remittance ref 4',
                'remittance ref 5',
                'reversed transaction',
                'reversal transaction ref no',
                'biller name',
                'payment channel',
                'bills payment ref 1',
                'bills payment ref 2',
                'bills payment ref 3',
                'bills payment ref 4',
                'bills payment ref 5',
                'bills payment ref 6',
                'bills payment ref 7',
                'bills payment ref 8',
                'bills payment ref 9',
                'bills payment ref 10',
            ],
            self::BANK_CODE_SECURITY       => [
                'posting date',           //交易日期
                'transaction description',//描述
                'debit amount',           //付款金额
                'credit amount',          //收款金额
                'running balance',        //余额
                'narrative',
            ],
            self::BANK_CODE_BDO            => [
                'posting date',//交易日期
                'branch',
                'description',    //描述
                'debit',          //付款金额
                'credit',         //收款金额
                'running balance',//余额
                'check number',   //支票号
            ],
            self::BANK_CODE_PH_COMMON_BANK => [
                'account number', //银行账号
                'transaction',    //交易时间
                'cheque',         //支票号
                'opening balance',//期初金额
                'withdrawal',     //收款金额
                'deposit',        //付款金额
                'balance',        //余额
                'remark',         //备注
            ],
        ],
        GlobalEnums::MY_COUNTRY_CODE => [
            self::BANK_CODE_MY_COMMON_BANK => [
                'account number', //银行账号
                'transaction',    //交易时间
                'cheque',         //支票号
                'opening balance',//期初金额
                'withdrawal',     //收款金额
                'deposit',        //付款金额
                'balance',        //余额
                'remark',         //备注
            ],
            self::BANK_CODE_CIMB           => [
                'account number',//银行账号
                'record sequence number',
                'transaction date',//交易日期
                'transaction code',
                'transaction code description',//描述
                'originating branch code',
                'document reference number',
                'transaction amount',     //交易金额
                'transaction amount type',//D付款金额 C收款金额
                'balance',                //账户余额
                'balance type',
                'transaction time',
                'customer reference',
                'filler',
                'record type',
                'recipient reference',//支票号码
                'other payment details',
                'sender name',
            ],
            self::BANK_CODE_MAY_BANK       => [
                'account number',//银行账号
                'account type',
                'account name',
                'account currency',//币种
                'date from',
                'date to',
                'total debit',
                'total credit',
                'begin balance',
                'end balance',
                'transaction date',
                'transaction time',
                'posting date',           //交易日期
                'processing time',        //交易时间
                'transaction description',//描述
                'transaction ref',
                'debit', //付款金额
                'credit',//收款金额
                'source code',
                'teller id',
                'branch/channel',
                'transaction code',
                'end balance',//账户余额
                'filler',
                'virtual account',
                'transaction description 2',
                'transaction description 3',
                'transaction description 4',
                'expiry date',
            ],
            self::BANK_CODE_HSBC_MY        => [
                'account name',
                'account number',//银行账号
                'bank name',
                'currency',//币种
                'location',
                'bic',
                'iban',
                'account status',
                'account type',
                'closing ledger balance',
                'closing ledger brought forward from',
                'closing available balance',
                'closing available brought forward from',
                'current ledger balance',
                'current ledger as at',
                'current available balance',
                'current available as at',
                'bank reference',//描述
                'narrative',
                'customer reference',
                'trn type',
                'credit amount',//收款金额
                'debit amount', //付款金额
                'balance',      //账户余额
                'post date',    //交易日期
            ],
            self::BANK_CODE_OCBC_MY        => [
                'account no.',     //银行账号
                'account currency',//币种
                'opening balance',
                'closing book balance',//账户余额
                'closing available balance',
                'total credit amount',
                'total credit count',
                'statement value date',
                'total debit count',
                'total debit amount',
                'hold amount',
                'statement date',
                'post date',    //交易日期
                'debit amount', //付款金额
                'credit amount',//收款金额
                'transaction type code',
                'supplementary details',
                'statement details info',//描述
                'our ref',
                'ref for account owner',
            ],
        ],
    ];

    // 流水上传需要特殊转义的
    // 流水上传模板id
    // 泰国
    const BANK_CODE_TMB = 1;
    const BANK_CODE_SCB = 2;
    const BANK_CODE_HSBC = 3;
    const BANK_CODE_K_BANK = 4;
    const BANK_CODE_BAY = 5;
    const BANK_CODE_KTB = 9;
    const BANK_CODE_COMMON_BANK = 0;
    // 菲律宾
    const BANK_CODE_UNION = 1;
    const BANK_CODE_SECURITY = 3;
    const BANK_CODE_BDO = 4;
    const BANK_CODE_PH_COMMON_BANK = 5;
    const BANK_CODE_METRO_BANK = 6;

    // 马来-各银行流水模版枚举值（等同于bank_list表ID值）
    const BANK_CODE_MY_COMMON_BANK = 0;//手工模版
    const BANK_CODE_CIMB = 1;          //CIMB银行
    const BANK_CODE_MAY_BANK = 2;      //Maybank银行
    const BANK_CODE_HSBC_MY = 3;       //HSBC MY银行
    const BANK_CODE_OCBC_MY = 4;       //OCBC MY银行


    // 泰国 银行账号名称属于Flash Pay, 产品要求账户公司名称=Flash Pay Co., Ltd的账号即可视为银行账号属于Flash Pay
    const BANK_ACCOUNT_NAME_PAY = 'Flash Pay Co., Ltd';
    /**
     * 获取各国-流水上传-使用手工模版的银行列表
     *
     * @var array
     */
    public static $common_bank_id = [
        GlobalEnums::TH_COUNTRY_CODE => [6, 7, 8, 10, 11, 12, 13],
        GlobalEnums::PH_COUNTRY_CODE => [2, 6, 7],
        GlobalEnums::MY_COUNTRY_CODE => [5, 6, 7, 8],
    ];

    //各模板读取数据其实行(从表头开始), 没有配置的默认0
    public static $data_begin_index = [
        GlobalEnums::TH_COUNTRY_CODE => [
            self::BANK_CODE_KTB => 10,
        ],
        GlobalEnums::PH_COUNTRY_CODE => [
            self::BANK_CODE_UNION          => 15,
            self::BANK_CODE_SECURITY       => 3,
            self::BANK_CODE_BDO            => 3,
            self::BANK_CODE_PH_COMMON_BANK => 0,
        ],
        GlobalEnums::MY_COUNTRY_CODE => [
            self::BANK_CODE_CIMB => 6,
        ],
    ];

    public static $flow_file_columns_type = [
        GlobalEnums::TH_COUNTRY_CODE => [
            // TMB == TTB
            self::BANK_CODE_TMB         => [
                0  => \Vtiful\Kernel\Excel::TYPE_STRING,   // 银行账号, 可能会有前导0
                1  => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP,// 交易日期
                11 => \Vtiful\Kernel\Excel::TYPE_STRING,   // 交易时间
            ],
            self::BANK_CODE_SCB         => [
                0 => \Vtiful\Kernel\Excel::TYPE_STRING,   // 银行账号, 可能会有前导0
                1 => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP,// 交易日期
                2 => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP,// 交易时间
            ],
            self::BANK_CODE_HSBC        => [
                1  => \Vtiful\Kernel\Excel::TYPE_STRING,   // 银行账号, 可能会有前导0
                24 => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP,// 交易时间
                25 => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP,// 交易日期
            ],
            self::BANK_CODE_K_BANK      => [
                0 => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP,// 交易日期
                1 => \Vtiful\Kernel\Excel::TYPE_STRING,   // 交易时间
            ],
            self::BANK_CODE_BAY         => [
                0  => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP,// 交易日期
                10 => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP,// 交易时间
            ],
            self::BANK_CODE_KTB         => [
                0 => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP,// 交易日期 + 交易时间
            ],
            self::BANK_CODE_COMMON_BANK => [
                0 => \Vtiful\Kernel\Excel::TYPE_STRING,   // 银行账号, 可能会有前导0
                1 => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP,// 交易日期
            ],
        ],
        GlobalEnums::PH_COUNTRY_CODE => [
            self::BANK_CODE_UNION          => [
                0 => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP,// 交易日期
                1 => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP,// 交易时间
            ],
            self::BANK_CODE_BDO            => [
                0 => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP,// 交易日期
            ],
            self::BANK_CODE_SECURITY       => [
                0 => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP,// 交易日期
            ],
            self::BANK_CODE_PH_COMMON_BANK => [
                0 => \Vtiful\Kernel\Excel::TYPE_STRING,   // 银行账号, 可能会有前导0
                1 => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP,// 交易日期
            ],
        ],
        GlobalEnums::MY_COUNTRY_CODE => [
            self::BANK_CODE_MY_COMMON_BANK => [
                0 => \Vtiful\Kernel\Excel::TYPE_STRING,   // 银行账号, 可能会有前导0
                1 => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP,// 交易日期
            ],
            self::BANK_CODE_CIMB           => [
                2 => \Vtiful\Kernel\Excel::TYPE_STRING,// 交易日期
            ],
            self::BANK_CODE_MAY_BANK       => [
                12 => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP,//交易日期
                13 => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP,// 交易时间
            ],
            self::BANK_CODE_HSBC_MY        => [
                24 => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP,//交易日期
            ],
            self::BANK_CODE_OCBC_MY        => [
                12 => \Vtiful\Kernel\Excel::TYPE_STRING,//交易日期
            ],
        ],
    ];

    /**
     * 获取使用公共模板的银行id
     *
     * @return array|mixed
     * @date 2022/4/18
     */
    public static function get_common_bank_id()
    {
        $country_code = get_country_code();
        if (isset(self::$common_bank_id[$country_code])) {
            return self::$common_bank_id[$country_code];
        } else {
            return [];
        }
    }

    /**
     * 获取公共银行模板
     *
     * @return array|mixed
     * @date 2022/4/18
     */
    public static function get_common_template_id()
    {
        $country_code = get_country_code();
        if ($country_code == GlobalEnums::TH_COUNTRY_CODE) {
            return self::BANK_CODE_COMMON_BANK;
        } elseif ($country_code == GlobalEnums::PH_COUNTRY_CODE) {
            return self::BANK_CODE_PH_COMMON_BANK;
        } elseif ($country_code == GlobalEnums::MY_COUNTRY_CODE) {
            return self::BANK_CODE_MY_COMMON_BANK;
        }
        return 0;
    }

    /**
     * 根据国家获取模板列属性
     *
     * @return array|mixed
     * @date 2022/4/18
     */
    public static function get_flow_file_columns_type()
    {
        $country_code = get_country_code();
        if (isset(self::$flow_file_columns_type[$country_code])) {
            return self::$flow_file_columns_type[$country_code];
        } else {
            return [];
        }
    }

    /**
     * 获取模板的表头
     *
     * @return array|mixed
     * @date 2022/4/18
     */
    public static function get_bank_flow_file_header()
    {
        $country_code = get_country_code();
        return self::$bank_flow_file_header[$country_code] ?? [];
    }

    /**
     * 获取模板读取起始行号,默认值0
     *
     * @return array|mixed
     * @date 2022/4/18
     */
    public static function get_data_begin_index()
    {
        $country_code = get_country_code();
        return self::$data_begin_index[$country_code] ?? 0;
    }

    public static function get_expense_type_prefix()
    {
        $country_code = get_country_code();
        return self::$bank_flow_expense_type_prefix[$country_code] ?? self::$bank_flow_expense_type_prefix['TH'];
    }

    const BANK_FLOW_OA_TYPE_LOAN = 1;
    const BANK_FLOW_OA_TYPE_REIMBURSEMENT = 2;
    const BANK_FLOW_OA_TYPE_PURCHASE_PAYMENT = 3;
    const BANK_FLOW_OA_TYPE_PAYMENT_STORE_RENTING = 4;
    const BANK_FLOW_OA_TYPE_ORDINARY_PAYMENT = 5;
    const BANK_FLOW_OA_TYPE_RESERVE_FUND = 6;
    const BANK_FLOW_OA_TYPE_WAGE = 7;
    const BANK_FLOW_OA_TYPE_SALARY = 8;
    const BANK_FLOW_OA_TYPE_PARCEL_CLAIM = 9;
    const BANK_FLOW_OA_TYPE_CHEQUE_APPLY = 10;
    const BANK_FLOW_OA_TYPE_CHEQUE_ACCOUNT = 11;
    const BANK_FLOW_OA_TYPE_CHEQUE_ACCOUNT_OTHER = 12;
    const BANK_FLOW_OA_TYPE_AGENCY_PAYMENT = 13;//代理支付

    const BANK_FLOW_ASSOCIATE_OA_ORDER_ALLOWED_MAX_COUNT = 5000;// 流水关联系统单号每次最多允许的数量

    //银行流水模块标识(bank_flow_oa.oa_type) 和 支付模块的(payment.oa_type) 映射 系统模块表的key(sys_module.name_key)
    public static $oa_type_id_module_key = [
        self::BANK_FLOW_OA_TYPE_LOAN                  => SysConfigEnums::SYS_MODULE_LOAN,
        self::BANK_FLOW_OA_TYPE_REIMBURSEMENT         => SysConfigEnums::SYS_MODULE_REIMBURSEMENT,
        self::BANK_FLOW_OA_TYPE_PURCHASE_PAYMENT      => SysConfigEnums::SYS_MODULE_PURCHASE,
        self::BANK_FLOW_OA_TYPE_PAYMENT_STORE_RENTING => SysConfigEnums::SYS_MODULE_PAYMENT,
        self::BANK_FLOW_OA_TYPE_ORDINARY_PAYMENT      => SysConfigEnums::SYS_MODULE_ORDINARY_PAYMENT,
        self::BANK_FLOW_OA_TYPE_RESERVE_FUND          => SysConfigEnums::SYS_MODULE_RESERVE,
        self::BANK_FLOW_OA_TYPE_WAGE                  => SysConfigEnums::SYS_MODULE_LOAN,
        self::BANK_FLOW_OA_TYPE_SALARY                => SysConfigEnums::SYS_MODULE_LOAN,
        self::BANK_FLOW_OA_TYPE_PARCEL_CLAIM          => SysConfigEnums::SYS_MODULE_LOAN,
        self::BANK_FLOW_OA_TYPE_AGENCY_PAYMENT        => SysConfigEnums::SYS_MODULE_AGENCY_PAYMENT,
    ];

    // oa_type 与 业务模块审批流biz_type的映射关系
    public static $oa_type_and_wf_biz_type_map = [
        self::BANK_FLOW_OA_TYPE_LOAN                  => Enums::WF_LOAN_TYPE,
        self::BANK_FLOW_OA_TYPE_REIMBURSEMENT         => Enums::WF_REIMBURSEMENT_TYPE,
        self::BANK_FLOW_OA_TYPE_PURCHASE_PAYMENT      => Enums::WF_PURCHASE_PAYMENT,
        self::BANK_FLOW_OA_TYPE_PAYMENT_STORE_RENTING => Enums::WF_PAYMENT_STORE_RENTING_TYPE,
        self::BANK_FLOW_OA_TYPE_ORDINARY_PAYMENT      => Enums::ORDINARY_PAYMENT_BIZ_TYPE,
        self::BANK_FLOW_OA_TYPE_RESERVE_FUND          => Enums::WF_RESERVE_FUND_APPLY,
        self::BANK_FLOW_OA_TYPE_WAGE                  => Enums::WF_WAGES_TYPE,
        self::BANK_FLOW_OA_TYPE_SALARY                => Enums::WF_SALARY_APPLY,
    ];

    const BANK_FLOW_OA_TYPE_CHEQUE_ACCOUNT_ZP = 'ZP';        //使用支票支付普通付款和采购
    const BANK_FLOW_OA_TYPE_CHEQUE_ACCOUNT_ZPZFFK = 'ZPZFFK';//使用支票支付租房付款
    const BANK_FLOW_OA_TYPE_CHEQUE_ACCOUNT_ZPGM = 'ZPGM';    //支票详情购买费用

    public static $name_to_oa_type_id = [
        'JK'                                          => self::BANK_FLOW_OA_TYPE_LOAN,                  //借款
        'BX'                                          => self::BANK_FLOW_OA_TYPE_REIMBURSEMENT,         //报销
        'CGFK'                                        => self::BANK_FLOW_OA_TYPE_PURCHASE_PAYMENT,      //采购付款申请单
        'FK'                                          => self::BANK_FLOW_OA_TYPE_PAYMENT_STORE_RENTING, //网点租房合同付款
        'PTFK'                                        => self::BANK_FLOW_OA_TYPE_ORDINARY_PAYMENT,      //普通付款
        'BYJ'                                         => self::BANK_FLOW_OA_TYPE_RESERVE_FUND,          //备用金
        'XCKK'                                        => self::BANK_FLOW_OA_TYPE_WAGE,                  //薪酬扣款
        'XZFF'                                        => self::BANK_FLOW_OA_TYPE_SALARY,                //薪资发放
        'PK'                                          => self::BANK_FLOW_OA_TYPE_PARCEL_CLAIM,          //赔款
        self::BANK_FLOW_OA_TYPE_CHEQUE_ACCOUNT_ZPGM   => self::BANK_FLOW_OA_TYPE_CHEQUE_APPLY,
        self::BANK_FLOW_OA_TYPE_CHEQUE_ACCOUNT_ZPZFFK => self::BANK_FLOW_OA_TYPE_CHEQUE_ACCOUNT,
        self::BANK_FLOW_OA_TYPE_CHEQUE_ACCOUNT_ZP     => self::BANK_FLOW_OA_TYPE_CHEQUE_ACCOUNT_OTHER,
        'OAIC'                                        => self::BANK_FLOW_OA_TYPE_AGENCY_PAYMENT, //代理支付
    ];


    public static $oa_type_id_to_lang_key = [
        self::BANK_FLOW_OA_TYPE_LOAN                  => 'oa_type_1',
        self::BANK_FLOW_OA_TYPE_REIMBURSEMENT         => 'oa_type_2',
        self::BANK_FLOW_OA_TYPE_PURCHASE_PAYMENT      => 'oa_type_3',
        self::BANK_FLOW_OA_TYPE_PAYMENT_STORE_RENTING => 'oa_type_4',
        self::BANK_FLOW_OA_TYPE_ORDINARY_PAYMENT      => 'oa_type_5',
        self::BANK_FLOW_OA_TYPE_RESERVE_FUND          => 'oa_type_6',
        self::BANK_FLOW_OA_TYPE_WAGE                  => 'oa_type_7',
        self::BANK_FLOW_OA_TYPE_SALARY                => 'oa_type_8',
        self::BANK_FLOW_OA_TYPE_AGENCY_PAYMENT        => 'oa_type_13',
    ];

    const BANK_FLOW_DEDUCT_RATE = 7;        //可抵扣税率，百分之7
    const BANK_FLOW_DEDUCT_DEFAULT = 0.0943;
    const OUTLET_COMPENSATION = 1;                      //客户赔款
    const CUSTOMER_COMPENSATION = 2;                    //网点赔款
    const CURRENCY_MONEY_SYMBOL = 1;                    //THB
    const PK_DEFAULT_STATUS = 1;                        //默认状态
    const PK_IS_CANCEL = 0;

    // 流水管理:收款/付款上传文件类型
    const FLOW_UPLOAD_FILE_TYPE_UNASSOCIATED_ORDER = 1; // 未关联系统单号
    const FLOW_UPLOAD_FILE_TYPE_ASSOCIATED_ORDER = 2;   // 关联系统单号
    public static $flow_upload_file_type_item = [
        self::FLOW_UPLOAD_FILE_TYPE_UNASSOCIATED_ORDER,
        self::FLOW_UPLOAD_FILE_TYPE_ASSOCIATED_ORDER,
    ];

    // 费用类型批量更新场景, 若是关联系统单号：每次最多允许上传的条数
    const EXPENSE_UPDATE_ASSOCIATE_OA_ORDER_ALLOWED_MAX_COUNT = 50000;
    const EXPENSE_UPDATE_ASSOCIATE_OA_ORDER_ALLOWED_SYNC_MAX_COUNT = 0; // 原 500, 改后即全部走异步

    // 费用类型批量更新场景, 若非关联系统单号：每次最多允许上传的条数
    const EXPENSE_UPDATE_UNASSOCIATED_ALLOWED_MAX_COUNT = 100000;
    const EXPENSE_UPDATE_UNASSOCIATED_ALLOWED_SYNC_MAX_COUNT = 0;  // 原 5000, 改后即全部走异步


    // 流水上传-导出流水, 流水管理-付款流水-导出, 流水管理-收款流水-导出 超过此数量走异步导出
    const BANK_FLOW_SYNC_MAX_COUNT = 10000;
    // 流水上传-导出流水, 流水管理-付款流水-导出, 流水管理-收款流水-导出 超过此数量不能导出(异步也不行)
    const BANK_FLOW_ASYNC_MAX_COUNT = 200000;

    // 费用类型: 内部转账的ID (泰国的!)
    const EXPENSE_TYPE_INTERNAL_TRANSFER_ID = 1;

    // 流水类型bank_flow表type字段
    const BANK_FLOW_TYPE_VALUE_GET = 1;
    const BANK_FLOW_TYPE_VALUE_PAY = 2;
    // 关联流水是否取消
    const BANK_FLOW_OA_IS_CANCEL_YES = 1;
    const BANK_FLOW_OA_IS_CANCEL_NO = 0;


    //使用支票支付模块
    const BANK_FLOW_OA_TYPE_CHEQUE_ACCOUNT_ORDINARY_PAYMENT = 1201;//普通付款
    const BANK_FLOW_OA_TYPE_CHEQUE_ACCOUNT_PURCHASE = 1202;        //采购管理
    public static $oa_type_model = [
        self::BANK_FLOW_OA_TYPE_PURCHASE_PAYMENT => self::BANK_FLOW_OA_TYPE_CHEQUE_ACCOUNT_PURCHASE,
        self::BANK_FLOW_OA_TYPE_ORDINARY_PAYMENT => self::BANK_FLOW_OA_TYPE_CHEQUE_ACCOUNT_ORDINARY_PAYMENT,
    ];
    //新的支票关联的普通付款 租房，采购和之前的映射关系
    public static $cheque_account_type_relationship = [
        self::BANK_FLOW_OA_TYPE_CHEQUE_ACCOUNT_PURCHASE         => self::BANK_FLOW_OA_TYPE_PURCHASE_PAYMENT,
        self::BANK_FLOW_OA_TYPE_CHEQUE_ACCOUNT_ORDINARY_PAYMENT => self::BANK_FLOW_OA_TYPE_ORDINARY_PAYMENT,
        self::BANK_FLOW_OA_TYPE_CHEQUE_ACCOUNT                  => self::BANK_FLOW_OA_TYPE_PAYMENT_STORE_RENTING,
    ];


    /**
     * 银行账户相关枚举
     */
    // 账户状态 1-正常；2-暂停
    const ACCOUNT_STATUS_NORMAL = 1;
    const ACCOUNT_STATUS_PAUSE = 2;
    public static $account_status_item = [
        self::ACCOUNT_STATUS_NORMAL => 'fund_bank_account_status_' . self::ACCOUNT_STATUS_NORMAL,
        self::ACCOUNT_STATUS_PAUSE  => 'fund_bank_account_status_' . self::ACCOUNT_STATUS_PAUSE,

    ];

}
