<?php

namespace App\Library\Enums;

use App\Library\Exception\BusinessException;
use App\Modules\Common\Models\CurrencyModel;
use App\Modules\Common\Models\ExchangeRateModel;

/**
 * 全局枚举配置
 * Class enums
 */
final class GlobalEnums
{
    //网点-1 name
    const STORE_HEADER_OFFICE_ID = '-1';
    const STORE_HEADER_OFFICE_NAME = 'Head Office';
    // 国家标识
    const TH_COUNTRY_CODE = 'TH';
    const PH_COUNTRY_CODE = 'PH';
    const MY_COUNTRY_CODE = 'MY';
    const VN_COUNTRY_CODE = 'VN';
    const LA_COUNTRY_CODE = 'LA';
    const ID_COUNTRY_CODE = 'ID';

    // 系统默认币种
    public static $sys_default_currency;
    public static $sys_default_currency_symbol;
    public static $sys_default_currency_symbol_simple;

    // 用于各业务模块校验币种参数的枚举
    const VALIDATE_CURRENCY_PARAMS = '1,2,3,4,5,6,7,8,9';

    // 币种数字代码与翻译
    const CURRENCY_THB = 1;//泰铢
    const CURRENCY_USD = 2;//美元
    const CURRENCY_CNY = 3;//人民币
    const CURRENCY_PHP = 4;//菲律宾披索
    const CURRENCY_LAK = 5;//基普
    const CURRENCY_MYR = 6;//林吉特
    const CURRENCY_IDR = 7;//IDR
    const CURRENCY_VND = 8;//VND
    const CURRENCY_EUR = 9;//欧元

    // （兼容旧版本获取币种名称方式）
    public static $currency_item = [
        self::CURRENCY_THB => 'payment_currency.1',
        self::CURRENCY_USD => 'payment_currency.2',
        self::CURRENCY_CNY => 'payment_currency.3',
        self::CURRENCY_PHP => 'payment_currency.4',
        self::CURRENCY_LAK => 'payment_currency.5',
        self::CURRENCY_MYR => 'payment_currency.6',
        self::CURRENCY_IDR => 'payment_currency.7',
        self::CURRENCY_VND => 'payment_currency.8',
        self::CURRENCY_EUR => 'payment_currency.9',
    ];

    // 币种数字代码与字符代码映射表
    public static $currency_symbol_map = [];

    // 币种汇率
    const EXCHANGE_RATE_DEFAULT = 1;// 默认汇率, 需兼容历史数据汇率 0.000 的情况(默认币种)
    public static $exchange_rate_map = [];

    // 付款方式
    const PAYMENT_METHOD_CASH = 1; // 现金
    const PAYMENT_METHOD_BANK_TRANSFER = 2; // 银行转账
    const PAYMENT_METHOD_CHECK = 3; // 支票
    const VALIDATE_PAYMENT_METHOD_PARAMS = '1,2,3';
    public static $payment_method_item = [
        self::PAYMENT_METHOD_CASH => 'global.payment.method.1',
        self::PAYMENT_METHOD_BANK_TRANSFER => 'global.payment.method.2',
        self::PAYMENT_METHOD_CHECK => 'global.payment.method.3',
    ];

    // TODO 其他新增全局配置
    const BUDGET_STATUS_OFF = 0;
    const BUDGET_STATUS_ON = 1; // 打开预算

    const PAYMENT_WHT_CATEGORY_1 = 1; // 房租
    const PAYMENT_WHT_CATEGORY_2 = 2; // 专业服务

    // 邮件提醒收件人配置的key
    const EMAIL_ORIGINAL_SPONSOR_EMAIL_CODE = 'original_sponsor_email_addressee';
    const EMAIL_NOTICE_WAITING_AUDIT_CODE = 'waiting_audit_email_addressee';
    const EMAIL_NOTICE_WAITING_PAY_CODE = 'waiting_pay_email_addressee';
    const EMAIL_NOTICE_WAITING_REPLY_CODE = 'waiting_reply_email_addressee';
    const EMAIL_NOTICE_WORKFLOW_COMMENT_CODE = 'workflow_comment_email_address';
    const EMAIL_NOTICE_WAITING_REJECT_CODE = 'waiting_reject_email_addressee';
    const EMAIL_NOTICE_BUDGET_CHANGE = 'hc_budget_change_address';
    const OA_DASHBOARD_PAGE_URL_CODE = 'oa_dashboard_page_url';
    const CRM_DASHBOARD_PAGE_URL_CODE = 'crm_dashboard_page_url';
    const OA_DASHBOARD_PAGE_URL_DEFAULT = 'https://oa.flashexpress.com/#/dashboard';
    const EMAIL_NOTICE_TEMPLATE_LANGUAGE = [
        'common' => [
            'first_lang' => 'zh-CN',
            'second_lang' => 'en'
        ],

        'th' => [
            'first_lang' => 'zh-CN',
            'second_lang' => 'th'
        ],
    ];

    //租房付款写死的银行信息, v17415调整后不再使用, 先注释, 以便以后追溯历史数据
    // 付款银行 目前已有部分业务取值通过setting_env 按国家配置, 此处通用配置是权宜之计，后续统一由setting_env维护
    // 泰国
    //const PAYMENT_PAY_BANK_01 = 'Thai Military Bank';
    //const PAYMENT_PAY_BANK_02 = 'Siam Commercial Bank';
    //
    //// 马来
    //const PAYMENT_PAY_BANK_03 = 'CIMB';
    //const PAYMENT_PAY_BANK_04 = 'Public Bank';
    //
    //// 菲律宾
    //const PAYMENT_PAY_BANK_05 = 'Union Bank 1213';
    //const PAYMENT_PAY_BANK_06 = 'Union Bank 0429';
    //
    //public static $payment_pay_bank_account = [
    //    self::PAYMENT_PAY_BANK_01 => '**********',
    //    self::PAYMENT_PAY_BANK_02 => '**********',
    //    self::PAYMENT_PAY_BANK_03 => '**********',
    //    self::PAYMENT_PAY_BANK_04 => '**********',
    //    self::PAYMENT_PAY_BANK_05 => '************',
    //    self::PAYMENT_PAY_BANK_06 => '************',
    //];

    // 借款归还展示查看状态
    const LOAN_STATUS_NOT_SHOW = 0;
    const LOAN_STATUS_SHOW = 1;

    // 借款数据查询列表展示报销查看
    const LOAN_REL_REIMBURSEMENT_NOT_SHOW = 0;
    const LOAN_REL_REIMBURSEMENT_SHOW = 1;

    // 借款申请事由 (1-差旅费，2-采购，3-长期驻外，4-公务活动，5-其他, 6-外协事项)
    const LOAN_APPLY_TYPE_TRAVEL = 1;
    const LOAN_APPLY_TYPE_PURCHASE = 2;
    const LOAN_APPLY_TYPE_FOREIGN = 3;
    const LOAN_APPLY_TYPE_BUSINESS = 4;
    const LOAN_APPLY_TYPE_OTHER = 5;
    const LOAN_APPLY_TYPE_OUTSOURCING = 6;
    public static $loan_apply_type = [
        self::LOAN_APPLY_TYPE_TRAVEL => 'loan_apply_type_travel',
        self::LOAN_APPLY_TYPE_PURCHASE => 'loan_apply_type_travel',
        self::LOAN_APPLY_TYPE_FOREIGN => 'loan_apply_type_foreign',
        self::LOAN_APPLY_TYPE_BUSINESS => 'loan_apply_type_business',
        self::LOAN_APPLY_TYPE_OTHER => 'loan_apply_type_other',
        self::LOAN_APPLY_TYPE_OUTSOURCING => 'loan_apply_type_outsourcing'
    ];

    // 可修改VAT金额 的国家
    const CAN_MODIFY_VAT_AMOUNT_COUNTRY_LIST = [
        self::TH_COUNTRY_CODE,
        self::PH_COUNTRY_CODE,
        self::MY_COUNTRY_CODE,
        self::LA_COUNTRY_CODE,
        self::VN_COUNTRY_CODE,
        self::ID_COUNTRY_CODE
    ];

    // 报销实质/预算科目: 差旅费
    const BUDGET_TRAVEL_LEVEL_CODE = '001';

    // 报销实质：差旅费 - 油费明细ID
    const BUDGET_TRAVEL_OIL_PRODUCT_ID = 480;

    // 马来备用金判断金额最大值
    const RESERVE_FUND_MY_MAX_AMOUNT = 500;
    const RESERVE_FUND_PH_MAX_AMOUNT_1 = 10000;
    const RESERVE_FUND_PH_MAX_AMOUNT_2 = 20000;
    //菲律宾 Warehouse Procurement Department
    const PH_WAREHOUSE_PROCUREMENT_DEPARTMENT = 5554;
    // Malaysia Network Management部门ID
    const RESERVE_FUND_NETWORK_MANAGEMENT = 316;
    // Flash Philippines Hub部门ID
    const RESERVE_FUND_PHILIPPINES_HUB  = 126;
    // Area MANAGER
    const RESERVE_FUND_AREA_MANAGER_JOB_TITLE  = 79;
    // District Manager
    const RESERVE_FUND_DISTRICT_MANAGER_JOB_TITLE  = 269;

    // 报销实质：发票类型
    const REIMBURSEMENT_TICKET_TYPE_ELECTRIC = 1; // 电子发票
    const REIMBURSEMENT_TICKET_TYPE_PAPER = 2; // 纸质发票
    public static $reimbursement_ticket_type = [
        self::REIMBURSEMENT_TICKET_TYPE_ELECTRIC => 'ticket_type_electric',
        self::REIMBURSEMENT_TICKET_TYPE_PAPER => 'ticket_type_paper'
    ];

    // 报销实质：发票编号是否存在
    const REIMBURSEMENT_TICKET_NOT_EXIST = 0; // 否
    const REIMBURSEMENT_TICKET_EXIST = 1; // 是

    // 差旅费ID
    const BUDGET_TRAVEL_ID = 1;

    // 默认英文的国家
    public static $default_en_country_list = [
        self::VN_COUNTRY_CODE
    ];

    // 税种名称
    const VAT_RATE_NAME = 'VAT';
    const SST_RATE_NAME = 'SST';
    const WHT_RATE_NAME = 'WHT';

    //业务类型 - 报销,普通付款
    const BUSINESS_TYPE_HISTORY_DATA = 0;// 历史数据, 空
    const BUSINESS_TYPE_OPERATE = 1;// 经营类
    const BUSINESS_TYPE_ADMINISTRATIVE = 2;// 行政类
    public static $business_type_item = [
        self::BUSINESS_TYPE_HISTORY_DATA => '',
        self::BUSINESS_TYPE_OPERATE => 'global.business.type.1',
        self::BUSINESS_TYPE_ADMINISTRATIVE => 'global.business.type.2',
    ];

    // 审批流是否废弃状态
    const WORKFLOW_ABANDON_STATE_YES = 1;// 已废弃
    const WORKFLOW_ABANDON_STATE_NO = 0;// 未废弃

    /**各业务模块审核 / 支付 / 征询回复 子菜单的tab类型定义 start**/
    const AUDIT_TAB_PENDING = 1; // 待处理
    const AUDIT_TAB_PROCESSED = 2; // 已处理
    const AUDIT_TAB_CONSULTED = 3; // 征询中
    const AUDIT_TAB_REPLIED = 4; // 征询已回复
    const AUDIT_TAB_STATE_ITEM = [
        self::AUDIT_TAB_PENDING,
        self::AUDIT_TAB_PROCESSED,
        self::AUDIT_TAB_CONSULTED,
        self::AUDIT_TAB_REPLIED,
    ];

    // 审批中的Tab类型
    const AUDIT_TAB_PENDING_STATE_ITEM = [
        self::AUDIT_TAB_PENDING,
        self::AUDIT_TAB_CONSULTED,
        self::AUDIT_TAB_REPLIED,
    ];

    // 各业务意见征询回复模块 各tab的取数分类
    const CONSULTED_REPLY_STATE_PENDING = 0; // 征询待处理(待回复)
    const CONSULTED_REPLY_STATE_PROCESSED  = 1; // 征询已处理(已回复)

    // 征询回复tab取数分类
    const CONSULTED_REPLY_STATE_ITEM = [
        self::CONSULTED_REPLY_STATE_PENDING,
        self::CONSULTED_REPLY_STATE_PROCESSED
    ];

    // 意见征询表的数据分类
    const CONSULTED_ACTION_TYPE = 1; // 征询类型
    const CONSULTED_REPLY_ACTION_TYPE = 2; // 回复类型

    const CONSULTED_STAFF_JOB_STATE_ON = 1; // 征询员工在职状态
    const CONSULTED_STAFF_JOB_STATE_OFF = 2; // 征询员工非在职状态

    const PAYMENT_TAB_PENDING = 1; // 待处理
    const PAYMENT_TAB_PROCESSED = 2; // 已处理
    const PAYMENT_TAB_ONLINE = 3;//在线支付
    /**各业务模块审核 / 征询回复 子菜单的tab类型定义 end **/

    // 软删除常量定义 is_deleted 0-未删; 1-已删;
    const IS_NO_DELETED = 0;
    const IS_DELETED = 1;

    // 是否下载
    const IS_CAN_DOWNLOAD = 1;//可下载
    const IS_NOT_DOWNLOAD = 0;//不可下载

    //是否撤回
    const IS_CAN_EDIT = 1;//可编辑
    const IS_NOT_EDIT = 0;//不可编辑

    // 系统列表页默认的分页配置
    const DEFAULT_PAGE_NUM = 1;
    const DEFAULT_PAGE_SIZE = 20;
    const DEFAULT_MAX_PAGE_SIZE = 1000;



    /******** 财务类业务模块共用的枚举定义 Start *******/
    // 财务各业务模块的发票类型枚举
    const FINANCIAL_INVOICE_TYPE_0 = 0;// 默认:无
    const FINANCIAL_INVOICE_TYPE_1 = 1;
    const FINANCIAL_INVOICE_TYPE_2 = 2;
    const FINANCIAL_INVOICE_TYPE_PREFIX = 'financial_invoice_type_';
    public static $financial_invoice_type_item = [
        self::FINANCIAL_INVOICE_TYPE_1 => self::FINANCIAL_INVOICE_TYPE_PREFIX.self::FINANCIAL_INVOICE_TYPE_1,
        self::FINANCIAL_INVOICE_TYPE_2 => self::FINANCIAL_INVOICE_TYPE_PREFIX.self::FINANCIAL_INVOICE_TYPE_2,
    ];

    // 财务各业务模块共用的: 是否有增值税票 0-默认(即空的, 没勾选的); 1-否; 2-是;
    const IS_WITH_VAT_INVOICE_DEFAULT = 0;
    const IS_WITH_VAT_INVOICE_NO = 1;
    const IS_WITH_VAT_INVOICE_YES = 2;
    public static $is_with_vat_invoice_item = [
        self::IS_WITH_VAT_INVOICE_NO,
        self::IS_WITH_VAT_INVOICE_YES
    ];

    // 相关业务模块是否能重新提交的时间节点(业务时间: 单据创建时间在该时间之后的, 方可使用重新提交功能)
    const BIZ_MODULE_CAN_RECOMMIT_DATETIME = '2023-01-01 00:00:00';


    // EXCEL 单元格容纳最多字符数
    const EXCEL_CELLS_CHARACTERS_MAX = 32767;

    /******** 财务类业务模块共用的枚举定义 End *******/


    const TOP_DEPARTMENT_ID = 999;
    const TOP_DEPARTMENT_NAME = 'GroupCEO';

    const STORE_CATEGORY_HUB = [8, 9, 12];
    const STORE_CATEGORY_SHOP = [4, 5, 7];
    const STORE_CATEGORY_NETWORK = [1, 2, 14];
    const STORE_CATEGORY_NETWORK_BULKY = [10, 13];


    /****** 消息类型 Start *****/
    const MESSAGE_CATEGORY_OA_WORKFLOW_COMMENT_NOTICE = 126; // 审批流评论消息


    /****** 消息类型 End *****/


    // 初始化与系统相关的默认项
    public static function init()
    {
        // 初始化币种编码
        self::setCurrency();
        // 币种相关的
        self::setExchangeRate();

        // TODO 1: 新增的全局配置 且 跟系统相关的: 即配置项为系统级通用配置，但各国又有区别的，统一在此完成初始化
        // TODO 2: 若配置项为静态配置，且各国都一样, 则无需初始化，不要在该类中定义方法, 直接配置枚举使用即可
        // TODO 3: 若配置取自setting_env 表, 请到EnumsService层进行维护

    }

    // 获取默认币种
    public static function setCurrency()
    {
        $countryCode = get_country_code();
        $currencyList = CurrencyModel::getAllCurrency();
        self::$currency_symbol_map = array_column($currencyList, 'currency_symbol', 'id');
        $currency_code_map = array_column($currencyList, 'id', 'country_code');
        self::$sys_default_currency = isset($currency_code_map[$countryCode]) ? $currency_code_map[$countryCode] : self::CURRENCY_THB;
        self::$sys_default_currency_symbol = isset(self::$currency_symbol_map[self::$sys_default_currency]) ? self::$currency_symbol_map[self::$sys_default_currency] : self::$currency_symbol_map[self::CURRENCY_THB];
        //货币符号
        $currency_symbol_simple_map = array_column($currencyList, 'currency_symbol_simple', 'id');
        self::$sys_default_currency_symbol_simple = isset($currency_symbol_simple_map[self::$sys_default_currency]) ? $currency_symbol_simple_map[self::$sys_default_currency] : $currency_symbol_simple_map[self::CURRENCY_THB];
    }

    // 获取默认币种汇率
    private static function setExchangeRate()
    {
        // 币种与默认币种汇率
        $rateList = ExchangeRateModel::getRateByCodeId(self::$sys_default_currency);
        foreach ($rateList as $item) {
            $baseSymbol = self::$currency_symbol_map[$item['base_currency']];
            $formatSymbol = self::$currency_symbol_map[$item['format_currency']];
            self::$exchange_rate_map[$formatSymbol.'_TO_'.$baseSymbol] = $item['rate'];
        }
    }

    /**
     * 金额通过默认币种转换  == 四舍五入，保留两位小数
     *
     * @param $amount
     * @param $from_currency
     * @param $to_currency
     * @return mixed
     * @throws BusinessException
     */
    public static function getAmountByDefaultRate($amount, $from_currency, $to_currency)
    {
        //如果币种类型，相同直接返回
        if ($from_currency == $to_currency) {
            return $amount;
        }

        $from_symbol = self::$currency_symbol_map[$from_currency];
        $to_symbol = self::$currency_symbol_map[$to_currency];
        $base_symbol = self::$currency_symbol_map[self::$sys_default_currency];

        $from_rate = self::$exchange_rate_map[$from_symbol . "_TO_" . $base_symbol] ?? 0;
        $to_rate = self::$exchange_rate_map[$to_symbol . "_TO_" . $base_symbol] ?? 0;

        if (empty($from_rate) || empty($to_rate)) {
            throw new BusinessException('not found rate from=' . $from_currency . " to " . $to_currency);
        }

        //四舍五入，保留2位小数
        return round($amount * $from_rate / $to_rate, 2);
    }

    /**
     * 获取系统默认币种
     */
    public static function getSysDefaultCurrency()
    {
        if (is_null(GlobalEnums::$sys_default_currency)) {
            GlobalEnums::setCurrency();
        }
        return GlobalEnums::$sys_default_currency;
    }

}

