<?php
namespace App\Library\Enums;
/**
 * 采购业务枚举配置
 * Class enums
 */
final class PurchaseEnums
{
    // 采购类型
    // 库存类
    const PURCHASE_TYPE_STOCK = 18;
    // 服务类
    const PURCHASE_TYPE_SERVICE = 19;
    // 辅助类
    const PURCHASE_TYPE_COST = 84;
    // 混合类
    const PURCHASE_TYPE_MIX = 1000;

    // 是否有预扣税: 0-无; 1-有
    const IS_WITHHOLDING_TAX_NO = 0;
    const IS_WITHHOLDING_TAX_YES = 1;

    // 是否需要验收 0-不需要; 1-需要
    const IS_CHECK_FLAG_NO = 0;
    const IS_CHECK_FLAG_YES = 1;

    //采购申请单-业务类型
    const APPLY_BUSINESS_TYPE_ADMINISTRATION = 1;//采购申请-业务类型-行政类
    const APPLY_BUSINESS_TYPE_OPERATE = 2;//采购申请-业务类型-运营类

    //采购类型 1采购订单2采购申请单
    const ACCEPTANCE_TYPE_PO = 1;//采购订单验收
    const ACCEPTANCE_TYPE_PU = 2;//采购申请单验收
    const ACCEPTANCE_CHECK_RESULT_PASS = 1;//通过
    const ACCEPTANCE_CHECK_RESULT_NO= 2;//不通过

    //送样原因
    const SAMPLE_SEND_REASON_1 = 1;//新产品
    const SAMPLE_SEND_REASON_2 = 2;//产品设计要求
    const SAMPLE_SEND_REASON_3 = 3;//新供应商
    const SAMPLE_SEND_REASON_4 = 4;//材质变更
    const SAMPLE_SEND_REASON_5 = 5;//产品改进
    const SAMPLE_SEND_REASON_6 = 6;//质量改进

    //验收单验收类别
    const ACCEPTANCE_TYPE_1 = 1;//自动分拣机系统
    const ACCEPTANCE_TYPE_2 = 2;//输送设备
    const ACCEPTANCE_TYPE_3 = 3;//安检设备
    const ACCEPTANCE_TYPE_4 = 4;//CCTV安装
    const ACCEPTANCE_TYPE_5 = 5;//装修
    const ACCEPTANCE_TYPE_6 = 6;//常规服务类采购（测试服务、通话）
    const ACCEPTANCE_TYPE_7 = 7;//短信服务
    const ACCEPTANCE_TYPE_8 = 8;//总部采购（清洁，办公，医疗用品）
    const ACCEPTANCE_TYPE_9 = 9;//CCTV安装及拆除项目-HUB


    const IS_SUPPLEMENT_INVOICE_NO = 1;//是否上传发票 否
    const IS_SUPPLEMENT_INVOICE_YES = 2;//是否上传发票 是

    const IS_SUPPLEMENT_ATTACH_YES = 1;//已补充
    const IS_SUPPLEMENT_ATTACH_NO = 2;//未补充
    const IS_SUPPLEMENT_ATTACH_NO_NEED = 3;//无需补充

    public static $supplement_invoice_status = [
        self::IS_SUPPLEMENT_INVOICE_YES => 'supplement_invoice_status_yes',
        self::IS_SUPPLEMENT_INVOICE_NO  => 'supplement_invoice_status_no'
    ];
    public static $supplement_attach_status = [
        self::IS_SUPPLEMENT_ATTACH_YES     => 'supplement_attach_status_yes',
        self::IS_SUPPLEMENT_ATTACH_NO      => 'supplement_attach_status_no',
        self::IS_SUPPLEMENT_ATTACH_NO_NEED => 'supplement_attach_status_no_need',
    ];


    //采购申请单-业务类型枚举
    public static $apply_business_type = [
        self::APPLY_BUSINESS_TYPE_ADMINISTRATION => 'purchase_apply_business_type.'.self::APPLY_BUSINESS_TYPE_ADMINISTRATION,
        self::APPLY_BUSINESS_TYPE_OPERATE => 'purchase_apply_business_type.'.self::APPLY_BUSINESS_TYPE_OPERATE,
    ];

    // 采购订单vat/wht金额总计字段修改: 相关联的字段也需同步变更
    public static $purchase_order_base_total_field_item = [
        // vat总计
        'taxation' => [
            'taxation',
            'amount',// 含税金额总计 (含vat 不含wht/pnd)
            'total_amount',// 含税金额总计 (含vat 含wht/pnd)
        ],

        // wht金额总计
        'wht_total_amount' => [
            'wht_total_amount',
            'amount',// 含税金额总计 (含vat 不含wht/pnd)
            'total_amount',// 含税金额总计 (含vat 含wht/pnd)
        ]
    ];

    // 采购订单相关税率关联的金额字段: 税率可变更时, 金额字段也需同步获取进行存储
    public static $purchase_order_product_rate_field_item = [
        'vat7' => [
            'vat7_rate',
            'vat7',
            'all_total',
            'all_total_no_wht',
            'taxation',//vat 金额总计
            'amount',// 含税金额总计 (含vat 不含wht/pnd)
            'total_amount',// 含税金额总计 (含vat 含wht/pnd)
        ],
        'wht_amount' => [
            'wht_rate',
            'wht_amount',
            'all_total_no_wht',
            'all_total',
            'amount',// 含税金额总计 (含vat 不含wht)
            'total_amount',// 含税金额总计 (含vat 含wht)
            'wht_total_amount',

        ]
    ];

    public static $purchase_order_product_rate_field_main =[
        'vat7' => [
            'taxation',//vat 金额总计
            'amount',// 含税金额总计 (含vat 不含wht/pnd)
            'total_amount',// 含税金额总计 (含vat 含wht/pnd)
            'taxation'
        ],
        'wht_amount' => [
            'amount',// 含税金额总计 (含vat 不含wht)
            'total_amount',// 含税金额总计 (含vat 含wht)
            'wht_total_amount',
        ]
    ];


    // 采购付款单wht金额总计字段修改，关联的如下字段也需同步取值存储
    public static $purchase_payment_base_total_field_item = [
        'wht_amount' => [
            'wht_amount',
            //'receipt_amount',
            'cur_amount',
            //'real_amount'
        ]
    ];

    // 采购付款单相关税率关联的金额字段: 税率可变更时, 金额字段也需同步获取进行存储
    public static $purchase_payment_product_rate_field_item = [
        'wht_amount' => [
            'wht_amount',
            'cur_amount',
        ],
        'deductible_vat_tax' => [
            'deductible_vat_tax',
            'deductible_vat_amount',
        ],
        'ticket_amount'=>[
            'deductible_vat_tax',
            'deductible_vat_amount',
            'cur_amount',
            'deductible_vat_amount',
            'receipt_amount',
            'ticket_amount_tax',
            'ticket_tax'
        ],
    ];

    //采购付款单行数据影响主表金额数据
    public static $purchase_payment_product_rate_field_main = [
        'wht_amount' => [
            'wht_amount',
            'cur_amount',
        ],
        'deductible_vat_tax' => [
            'deductible_vat_tax',
            'deductible_vat_amount',
        ],
        'ticket_amount'=>[
            'cur_amount',
            'deductible_vat_amount',
            'receipt_amount',
            'ticket_amount_tax'
        ],
    ];
    //验收结果枚举
    public static $check_result_type = [
        self::ACCEPTANCE_CHECK_RESULT_PASS=>'acceptance_check_result_pass',
        self::ACCEPTANCE_CHECK_RESULT_NO=>'acceptance_check_result_no',
    ];

    //验收类型枚举
    public static $acceptance_type = [
        self::ACCEPTANCE_TYPE_PO=>'acceptance_type_po',
        self::ACCEPTANCE_TYPE_PU=>'acceptance_type_pu',
    ];

    //样品送样枚举
    public static $send_reason = [
        self::SAMPLE_SEND_REASON_1=>'sample_send_reason_1',
        self::SAMPLE_SEND_REASON_2=>'sample_send_reason_2',
        self::SAMPLE_SEND_REASON_3=>'sample_send_reason_3',
        self::SAMPLE_SEND_REASON_4=>'sample_send_reason_4',
        self::SAMPLE_SEND_REASON_5=>'sample_send_reason_5',
        self::SAMPLE_SEND_REASON_6=>'sample_send_reason_6',

    ];

    //验收类别枚举
    public static $acceptance_order_type = [
        self::ACCEPTANCE_TYPE_1 => 'acceptance_type_1',
        self::ACCEPTANCE_TYPE_2 => 'acceptance_type_2',
        self::ACCEPTANCE_TYPE_3 => 'acceptance_type_3',
        self::ACCEPTANCE_TYPE_4 => 'acceptance_type_4',
        self::ACCEPTANCE_TYPE_5 => 'acceptance_type_5',
        self::ACCEPTANCE_TYPE_6 => 'acceptance_type_6',
        self::ACCEPTANCE_TYPE_7 => 'acceptance_type_7',
        self::ACCEPTANCE_TYPE_8 => 'acceptance_type_8',
        self::ACCEPTANCE_TYPE_9 => 'acceptance_type_9',
    ];
    //采购申请单/订单 是否关闭 0.老数据; 1.关闭 2.未关闭
    const IS_CLOSE_HISTORY = 0;
    const IS_CLOSE_YES = 1;
    const IS_CLOSE_NO = 2;
    public static $is_close_list = [
        self::IS_CLOSE_YES => 'purchase_is_close_yes',
        self::IS_CLOSE_NO => 'purchase_is_close_no',
    ];

    //采购订单pdf格式
    const PURCHASE_ORDER_PDF_FORMAT_EXCLUDING_TERMS = 1;//采购订单（不含条款）
    const PURCHASE_ORDER_PDF_FORMAT_INCLUDING_TERMS = 2;//采购订单（含条款）
    public static $pdf_format = [
        self::PURCHASE_ORDER_PDF_FORMAT_EXCLUDING_TERMS => 'purchase_order_pdf.' . self::PURCHASE_ORDER_PDF_FORMAT_EXCLUDING_TERMS,
        self::PURCHASE_ORDER_PDF_FORMAT_INCLUDING_TERMS => 'purchase_order_pdf.' . self::PURCHASE_ORDER_PDF_FORMAT_INCLUDING_TERMS,
    ];

    //是否人工关闭：0否，1是
    const IS_HAND_CLOSE_NO = 0;
    const IS_HAND_CLOSE_YES = 1;
    //采购申请单/订单 是否可以执行关闭动作 1.可以 0.不可以
    const CAN_CLOSE_YES = 1;
    const CAN_CLOSE_NO = 0;
    //采购申请单 执行状态execute_status
    const APPLY_EXECUTE_STATUS_HISTORY = 0;//历史数据
    const APPLY_EXECUTE_STATUS_NO = 1;//未生成po
    const APPLY_EXECUTE_STATUS_PARTLY = 2;//部分生成po
    const APPLY_EXECUTE_STATUS_DONE = 3;//完全生成po
    public static $apply_execute_status_list = [
        self::APPLY_EXECUTE_STATUS_NO => 'apply_execute_status_no',
        self::APPLY_EXECUTE_STATUS_PARTLY => 'apply_execute_status_partly',
        self::APPLY_EXECUTE_STATUS_DONE => 'apply_execute_status_done',
    ];
    //采购订单 执行状态execute_status
    const ORDER_EXECUTE_STATUS_NO = 1;//未交付
    const ORDER_EXECUTE_STATUS_PARTLY = 2;//部分交付
    const ORDER_EXECUTE_STATUS_DONE = 3;//完全交付
    public static $order_execute_status_list = [
        self::ORDER_EXECUTE_STATUS_NO => 'order_execute_status_no',
        self::ORDER_EXECUTE_STATUS_PARTLY => 'order_execute_status_partly',
        self::ORDER_EXECUTE_STATUS_DONE => 'order_execute_status_done',
    ];

    // 采购申请单/订单/及其附属的产品明细的数量是否可修改标识: 1-可修改; 0-不可修改
    const IS_CAN_UPDATE_YES = 1;
    const IS_CAN_UPDATE_NO = 0;

    //是否所有产品关联过付款申请单，0否，1是
    const IS_CITE_NO = 0;
    const IS_CITE_YES = 1;

    // 采购申请单是否关联过采购订单
    const IS_LINK_PO_NO = 0;
    const IS_LINK_PO_YES = 1;

    // 采购付款申请单,付款形式
    const PAYMENT_PAY_METHOD_PERCENT = 1; //按百分比
    const PAYMENT_PAY_METHOD_NUMBER = 2; //按收货数量
    public static $payment_pay_method_list = [
        self::PAYMENT_PAY_METHOD_PERCENT => 'payment_pay_method_percent',
        self::PAYMENT_PAY_METHOD_NUMBER => 'payment_pay_method_number',
    ];
    const VALIDATE_PAYMENT_PAY_METHOD = self::PAYMENT_PAY_METHOD_PERCENT . ',' . self::PAYMENT_PAY_METHOD_NUMBER;

    //判断是否允许修改计划交货日期的时间限定值
    const CAN_EDIT_DELIVERY_DATE = '2022-08-01';

    //采购订单-入库状态-已完成
    const PURCHASE_ORDER_STOCK_STATUS_DONE = 2;
    //未完成
    const PURCHASE_ORDER_STOCK_STATUS_UNDONE = 1;

    //采购入库单-入库状态-待处理
    const PURCHASE_STORAGE_STATUS_WAIT = 1;
    //采购入库单-入库状态-审核已通过
    const PURCHASE_STORAGE_STATUS_PASS = 2;
    //采购入库单-入库状态-已入库
    const PURCHASE_STORAGE_STATUS_IN_STORAGE = 3;
    //采购入库单-入库状态-已入库(非SCM)
    const PURCHASE_STORAGE_STATUS_UN_SCM_DONE = 6;

    // 采购付款是否支付的选项: 1-是; 2-否
    const PAYMENT_PAY_OPTION_PAID = 1;
    const PAYMENT_PAY_OPTION_UNPAID = 2;

    //采购付款 采购方式 1货到付款 2预付
    const PAYMENT_PAY_METHOD_1 = 1;
    const PAYMENT_PAY_METHOD_2 = 2;

    //采购员设置-导入最大限制
    const PURCHASE_SET_LIMIT = 1000;

}
