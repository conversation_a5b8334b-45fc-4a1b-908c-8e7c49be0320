<?php

namespace App\Library\Enums;


/***
 * 贪污案件管理常量文件
 */
final class CorruptionEnums
{
    /**
     * 贪污类型
     * 1-公款 2-COD 3-备用金 4-偷窃包裹 5-虚假里程 0-其他
     */
    const CORRUPTION_TYPE_0 = 0;
    const CORRUPTION_TYPE_1 = 1;
    const CORRUPTION_TYPE_2 = 2;
    const CORRUPTION_TYPE_3 = 3;
    const CORRUPTION_TYPE_4 = 4;
    const CORRUPTION_TYPE_5 = 5;
    public static $corruption_typ_all = [
        self::CORRUPTION_TYPE_0 => 'corruption_type_0', // 其他
        self::CORRUPTION_TYPE_1 => 'corruption_type_1', // 公款
        self::CORRUPTION_TYPE_2 => 'corruption_type_2', // COD
        self::CORRUPTION_TYPE_3 => 'corruption_type_3', // 备用金
        self::CORRUPTION_TYPE_4 => 'corruption_type_4', // 偷窃包裹
        self::CORRUPTION_TYPE_5 => 'corruption_type_5'  // 虚假里程
    ];

    /**
     * 筛选下拉框状态
     * 1. 待处理包含以下状态：1-待定级 2-审讯计划待制定 3-待审讯 4-审讯中 5-待结案 6-报警待授权 7-待报警
     * 8-待追偿 9- 待赔偿
     * 2. 已处理包含以下状态：10-案件关闭 11-无法追偿 12-已追偿 13-已撤销
     */
    const CORRUPTION_STATUS_ALL = 99;
    const CORRUPTION_STATUS_0 = 0;
    const CORRUPTION_STATUS_1 = 1;
    const CORRUPTION_STATUS_2 = 2;
    const CORRUPTION_STATUS_3 = 3;
    const CORRUPTION_STATUS_4 = 4;
    const CORRUPTION_STATUS_5 = 5;
    const CORRUPTION_STATUS_6 = 6;
    const CORRUPTION_STATUS_7 = 7;
    const CORRUPTION_STATUS_8 = 8;
    const CORRUPTION_STATUS_9 = 9;
    const CORRUPTION_STATUS_10 = 10;
    const CORRUPTION_STATUS_11 = 11;
    const CORRUPTION_STATUS_12 = 12;
    // 待处理页签下的筛选
    public static $pending_corruption_status = [
        self::CORRUPTION_STATUS_ALL => 'corruption_status_all', // 全部
        self::CORRUPTION_STATUS_0   => 'corruption_status_0',   // 待定级
        self::CORRUPTION_STATUS_1   => 'corruption_status_1',   // 审讯计划待制定
        self::CORRUPTION_STATUS_2   => 'corruption_status_2',   // 待审讯
        self::CORRUPTION_STATUS_3   => 'corruption_status_3',   // 审讯中
        self::CORRUPTION_STATUS_4   => 'corruption_status_4',   // 待结案
        self::CORRUPTION_STATUS_5   => 'corruption_status_5',   // 报警待授权
        self::CORRUPTION_STATUS_6   => 'corruption_status_6',   // 待报警
        self::CORRUPTION_STATUS_7   => 'corruption_status_7',   // 待追偿
        self::CORRUPTION_STATUS_8   => 'corruption_status_8',   // 待赔偿
    ];
    // 已处理页签下的筛选
    public static $end_corruption_status = [
        self::CORRUPTION_STATUS_ALL => 'corruption_status_all', // 全部
        self::CORRUPTION_STATUS_9   => 'corruption_status_9',   // 案件关闭
        self::CORRUPTION_STATUS_10  => 'corruption_status_10',  // 无法追偿
        self::CORRUPTION_STATUS_11  => 'corruption_status_11',  // 已追偿
        self::CORRUPTION_STATUS_12  => 'corruption_status_12',  // 已撤销
    ];

    // 上报来源 1-E-Mail 2-FBI 3-Line 4-电话 0-其他
    const SOURCE_0 = 0;
    const SOURCE_1 = 1;
    const SOURCE_2 = 2;
    const SOURCE_3 = 3;
    const SOURCE_4 = 4;
    public static $all_source = [
        self::SOURCE_0 => 'source_0',
        self::SOURCE_1 => 'source_1',
        self::SOURCE_2 => 'source_2',
        self::SOURCE_3 => 'source_3',
        self::SOURCE_4 => 'source_4',
    ];

    // 是否审讯 0-否 1-是
    const TRIAL_YES = 1;
    const TRIAL_NO = 0;
    public static $all_trial = [
        self::TRIAL_YES => 'trial_yes',
        self::TRIAL_NO  => 'trial_no',
    ];

    //'案件定级  1-简单案件 2-中型案件 3-大型案件 4-超大型案件',
    const CORRUPTION_GRADE_1 = 1;
    const CORRUPTION_GRADE_2 = 2;
    const CORRUPTION_GRADE_3 = 3;
    const CORRUPTION_GRADE_4 = 4;
    public static $all_corruption_grade = [
        self::CORRUPTION_GRADE_1 => 'corruption_grade_1',
        self::CORRUPTION_GRADE_2 => 'corruption_grade_2',
        self::CORRUPTION_GRADE_3 => 'corruption_grade_3',
        self::CORRUPTION_GRADE_4 => 'corruption_grade_4',
    ];

    // 审讯状态 审讯状态 2->正常审讯 1-嫌疑人不配合
    const INTERROGATION_RECORD_STATUS_2 = 2;
    const INTERROGATION_RECORD_STATUS_1 = 1;
    public static $all_interrogation_record = [
        self::INTERROGATION_RECORD_STATUS_2 => 'interrogation_record_status_2',
        self::INTERROGATION_RECORD_STATUS_1 => 'interrogation_record_status_1',
    ];

    // '审讯结果 1-腐败 2-可疑腐败行为 3-「缺陷/不服从/忽视」4-抢劫',
    const UPSHOT_1 = 1;
    const UPSHOT_2 = 2;
    const UPSHOT_3 = 3;
    const UPSHOT_4 = 4;
    public static $all_upshot = [
        self::UPSHOT_1 => 'upshot_1',
        self::UPSHOT_2 => 'upshot_2',
        self::UPSHOT_3 => 'upshot_3',
        self::UPSHOT_4 => 'upshot_4',
    ];

    // '内部处罚 1-无需处罚 2-口头警告 3-警告信 4-自行离职 5-开除 6加入黑名单',
    const PUNISH_1 = 1;
    const PUNISH_2 = 2;
    const PUNISH_3 = 3;
    const PUNISH_4 = 4;
    const PUNISH_5 = 5;
    const PUNISH_6 = 6;
    public static $all_punish = [
        self::PUNISH_1 => 'punish_1',
        self::PUNISH_2 => 'punish_2',
        self::PUNISH_3 => 'punish_3',
        self::PUNISH_4 => 'punish_4',
        self::PUNISH_5 => 'punish_5',
        self::PUNISH_6 => 'punish_6',
    ];

    // '是否报警 1->是 2-否',
    const CALL_POLICE_YES = 1;
    const CALL_POLICE_NO = 2;
    public static $call_police = [
        self::CALL_POLICE_YES => 'call_police_yes',
        self::CALL_POLICE_NO  => 'call_police_no',
    ];


    // 报警负责人
    const POLICE_STAFF_LPA_SPECIALIST = 1;  // LPA Specialist
    const POLICE_STAFF_NETWORK = 2; //Network
    public static $all_police_staff = [
        self::POLICE_STAFF_LPA_SPECIALIST =>'police_staff_lpa_specialist',
        self::POLICE_STAFF_NETWORK => 'police_staff_network'
    ];

    //  '是否损失 1->是 2-否',
    const LOSS_YES = 1;
    const LOSS_NO = 2;
    public static $all_loss = [
        self::LOSS_YES => "loss_yes",
        self::LOSS_NO  => "loss_no",
    ];

    // '追偿方式 1-LPA追款 2-警方立案追款 3-法庭追款',
    const RECOVERY_1 = 1;
    const RECOVERY_2 = 2;
    const RECOVERY_3 = 3;
    public static $all_recovery = [
        self::RECOVERY_1 => 'recovery_1',
        self::RECOVERY_2 => 'recovery_2',
        self::RECOVERY_3 => 'recovery_3',
    ];

    // '是否赔偿1->是 2->否',
    const REPARATION_YES = 1;
    const REPARATION_NO = 2;
    public static $all_reparation = [
        self::REPARATION_YES => 'reparation_yes',
        self::REPARATION_NO  => 'reparation_no',
    ];

    // '无法追回原因 1-证据不足 2-法院驳回 3-无明确责任人 4-员工不付钱（入狱）',
    const COMPENSATE_REASON_1 = 1;
    const COMPENSATE_REASON_2 = 2;
    const COMPENSATE_REASON_3 = 3;
    const COMPENSATE_REASON_4 = 4;
    public static $all_compensate_reason = [
        self::COMPENSATE_REASON_1 => 'compensate_reason_1',
        self::COMPENSATE_REASON_2 => 'compensate_reason_2',
        self::COMPENSATE_REASON_3 => 'compensate_reason_3',
        self::COMPENSATE_REASON_4 => 'compensate_reason_4',
    ];

    // '付款形式 1->分期付款 2->全额付款',
    const INSTALLMENT = 1;
    const FULL_PAYMENT = 2;
    public static $all_payment_type = [
        self::INSTALLMENT  => "installment",
        self::FULL_PAYMENT => "full_payment",
    ];

    //'支付方式 1-现金当面支付 2-银行账户转账 3-工资或提成抵扣 4-其他',
    const PAYMENT_METHOD_CASH = 1;
    const PAYMENT_METHOD_TRANSFER = 2;
    const PAYMENT_METHOD_SALARY = 3;
    const PAYMENT_METHOD_OTHER = 4;
    public static $all_payment_method = [
        self::PAYMENT_METHOD_CASH     => 'payment_method_cash',
        self::PAYMENT_METHOD_TRANSFER => 'payment_method_transfer',
        self::PAYMENT_METHOD_SALARY   => 'payment_method_salary',
        self::PAYMENT_METHOD_OTHER    => 'payment_method_other',
    ];

    // 日期类型 已处理TAB
    const DATE_TYPE_CREATE_DATE = 1; // 按创建日期
    const DATE_TYPE_CLOSE_DATE = 2;  // 按结案日期
    public static $date_type = [
        self::DATE_TYPE_CREATE_DATE => 'date_type_create_date',
        self::DATE_TYPE_CLOSE_DATE  => 'date_type_close_date',
    ];

    // todo 这个翻译还没加
    const CORRUPTION_LOG_STATUS_1 = 1;
    const CORRUPTION_LOG_STATUS_2 = 2;
    const CORRUPTION_LOG_STATUS_3 = 3;
    const CORRUPTION_LOG_STATUS_4 = 4;
    const CORRUPTION_LOG_STATUS_5 = 5;
    const CORRUPTION_LOG_STATUS_6 = 6;
    const CORRUPTION_LOG_STATUS_7 = 7;
    const CORRUPTION_LOG_STATUS_8 = 8;
    const CORRUPTION_LOG_STATUS_9 = 9;
    const CORRUPTION_LOG_STATUS_10 = 10;
    const CORRUPTION_LOG_STATUS_11 = 11;
    const CORRUPTION_LOG_STATUS_12 = 12;
    const CORRUPTION_LOG_STATUS_13 = 13;
    const CORRUPTION_LOG_STATUS_14 = 14;

    public static $all_corruption_log_status = [
        self::CORRUPTION_LOG_STATUS_1  => 'corruption_log_status_1',    // 创建案件
        self::CORRUPTION_LOG_STATUS_2  => 'corruption_log_status_2',    // 案件定级
        self::CORRUPTION_LOG_STATUS_3  => 'corruption_log_status_3',    // 指定审讯计划
        self::CORRUPTION_LOG_STATUS_4  => 'corruption_log_status_4',    // 开始审讯
        self::CORRUPTION_LOG_STATUS_5  => 'corruption_log_status_5',    // 审讯完成
        self::CORRUPTION_LOG_STATUS_6  => 'corruption_log_status_6',    // 结案
        self::CORRUPTION_LOG_STATUS_7  => 'corruption_log_status_7',    // 报警授权
        self::CORRUPTION_LOG_STATUS_8  => 'corruption_log_status_8',    // 报警完成
        self::CORRUPTION_LOG_STATUS_9  => 'corruption_log_status_9',    // 填写追偿信息
        self::CORRUPTION_LOG_STATUS_10 => 'corruption_log_status_10',   // 赔偿支付
        self::CORRUPTION_LOG_STATUS_11 => 'corruption_log_status_11',   // 案件撤销
        self::CORRUPTION_LOG_STATUS_12 => 'corruption_log_status_12',   // 案件关闭
        self::CORRUPTION_LOG_STATUS_13 => 'corruption_log_status_13',   // 已追偿
        self::CORRUPTION_LOG_STATUS_14 => 'corruption_log_status_14',   // 无法追偿

    ];

    // 是否超时
    const CORRUPTION_IS_TIME_OUT_NO = 0;
    const CORRUPTION_IS_TIME_OUT_YES = 1;
    public static $all_is_time_out = [
        self::CORRUPTION_IS_TIME_OUT_NO  => 'is_time_out_no',
        self::CORRUPTION_IS_TIME_OUT_YES => 'is_time_out_yes',
    ];

    // 发送方式
    const SEND_TYPE_EXPRESS = 1;            // 快递
    const SEND_TYPE_HANDOVER = 2;           // 当面交接
    public static $all_send_method = [
        self::SEND_TYPE_EXPRESS  => 'send_type_express',
        self::SEND_TYPE_HANDOVER => 'send_type_handover',
    ];

    // 是否hold
    const HOLD_NO = 0;
    const HOLD_YES = 1;
    public static $all_whether_to_hold = [
        self::HOLD_NO  => 'hold_no',
        self::HOLD_YES => 'hold_yes',
    ];

    // 是否停职
    const SUSPENSION_NO = 0;
    const SUSPENSION_YES = 1;
    public static $all_whether_to_suspend = [
        self::SUSPENSION_NO  => 'suspension_no',
        self::SUSPENSION_YES => 'suspension_yes',
    ];

    // 停职原因  EHS立案调查
    const STOP_DUTY_REASON_8 = 8;

    // 工资发放备注
    const PAYMENT_MARKUP_5 = 5;

    // 案件超时天数
    const IS_TIME_OUT_DAYS = 7;

    // 贪污类型
    const CORRUPTION_TYPE = 'corruption_type';
    // 待处理页签下的筛选状态
    const PENDING_CORRUPTION_STATUS = 'pending_corruption_status';
    // 已经处理页签下的筛选状态
    const END_CORRUPTION_STATUS = 'end_corruption_status';
    // 上报来源
    const SOURCE = 'source';
    // 是否审讯
    const TRIAL = 'trial';
    // 案件级别
    const CORRUPTION_GRADE = 'corruption_grade';
    // 审讯状态
    const INTERROGATION_RECORD_STATUS = 'interrogation_record_status';
    // 审讯结果
    const UPSHOT = 'upshot';
    // 内部处罚
    const PUNISH = 'punish';
    // 是否报警
    const CALL_POLICE = 'call_police';
    // 是否有损失
    const LOSS = 'loss';
    // 追偿方式
    const RECOVERY = 'recovery';
    // 是否赔偿
    const REPARATION = 'reparation';
    // 无法追回原因
    const COMPENSATE_REASON = 'compensate_reason';
    //付款形式
    const PAYMENT_TYPE = 'payment_type';
    // 支付方式
    const PAYMENT_METHOD = 'payment_method';
    // 日期类型
    const DATE_TYPE = 'date_type';
    // 报警负责人
    const POLICE_STAFF = 'police_staff';
    // 发送方式
    const SEND_METHOD = 'send_method';
    // 是否hold
    const WHETHER_TO_HOLD = 'whether_to_hold';
    // 是否停职
    const WHETHER_TO_SUSPEND = 'whether_to_suspend';


    // 展示按钮
    const IS_VIEW_BUTTON = 1;
    // 不展示按钮
    const IS_HIDE_BUTTON = 0;

    const FROM_1 = 1; // 案件定级
    const FROM_2 = 2; // 审讯计划
    const FROM_3 = 3; // 审讯记录
    const FROM_4 = 4; // 结案
    const FROM_5 = 5; // 报警授权
    const FROM_6 = 6; // 报警信息
    const FROM_7 = 7; // 追偿信息
    const FROM_8 = 8; // 赔偿信息


}