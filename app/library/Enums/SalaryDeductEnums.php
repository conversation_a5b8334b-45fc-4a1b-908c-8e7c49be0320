<?php
namespace App\Library\Enums;

final class SalaryDeductEnums
{
    const SALARY_DEDUCT_PREFIX = 'DK';//批次号前缀
    const SALARY_DEDUCT_USER_ORDER_LOAN_PREFIX = 'DKJ';//借款批次号前缀
    const SALARY_DEDUCT_USER_ORDER_RESERVE_PREFIX = 'DKB';//备用金批次号前缀

    //实扣状态：1未上传，2处理中，3待payroll终审,4成功扣款
    const SALARY_DEDUCT_BATCH_STATUS_UN_UPLOAD = 1;
    const SALARY_DEDUCT_BATCH_STATUS_ING = 2;
    const SALARY_DEDUCT_BATCH_STATUS_PAYROLL = 3;
    const SALARY_DEDUCT_BATCH_STATUS_DONE = 4;
    //查询规则
    const SALARY_DEDUCT_BATCH_STATUS = self::SALARY_DEDUCT_BATCH_STATUS_UN_UPLOAD . ',' . self::SALARY_DEDUCT_BATCH_STATUS_ING . ',' . self::SALARY_DEDUCT_BATCH_STATUS_PAYROLL . ',' . self::SALARY_DEDUCT_BATCH_STATUS_DONE;

    //扣款状态：1成功，2未开始处理，3失败
    const SALARY_DEDUCT_USER_STATUS_SUCCESS = 1;
    const SALARY_DEDUCT_USER_STATUS_UN_START = 2;
    const SALARY_DEDUCT_USER_STATUS_FAIL = 3;
    //查询规则
    const SALARY_DEDUCT_USER_STATUS = self::SALARY_DEDUCT_USER_STATUS_SUCCESS . ',' . self::SALARY_DEDUCT_USER_STATUS_UN_START . ',' . self::SALARY_DEDUCT_USER_STATUS_FAIL;

    //实扣状态：1未生成，2已完成
    const SALARY_DEDUCT_USER_ORDER_STATUS_UN_CREATE = 1;
    const SALARY_DEDUCT_USER_ORDER_STATUS_DONE = 2;
    //查询规则
    const SALARY_DEDUCT_USER_ORDER_STATUS = self::SALARY_DEDUCT_USER_ORDER_STATUS_UN_CREATE . ',' . self::SALARY_DEDUCT_USER_ORDER_STATUS_DONE;

    //抵扣类型：1限额，2全额
    const SALARY_DEDUCT_TYPE_LIMIT = 1;
    const SALARY_DEDUCT_TYPE_ALL = 2;

    //单据类型：1借款，2备用金
    const SALARY_DEDUCT_USER_ORDER_SOURCE_TYPE_LOAN = 1;
    const SALARY_DEDUCT_USER_ORDER_SOURCE_TYPE_RESERVE_FUND = 2;

    const EXPORT_TYPE_DEDUCT = 1;//应扣
    const EXPORT_TYPE_ACTUAL_DEDUCT = 2;//实扣
    //导出类型：1应扣，2实扣
    const SALARY_DEDUCT_EXPORT_TYPE_RULE = self::EXPORT_TYPE_DEDUCT . ',' . self::EXPORT_TYPE_ACTUAL_DEDUCT;

    //实扣金额验证规则【正负浮点数可以输入0，且保留2位小数】
    const SALARY_DEDUCT_AMOUNT_RULE = '/^-?\d+(?:\.\d{1,2})?$/';

    //生成本期抵扣状态
    const SALARY_DEDUCT_TASK_STATUS_1 = 1;//未生成可点击
    const SALARY_DEDUCT_TASK_STATUS_2 = 2;//生成中
    const SALARY_DEDUCT_TASK_STATUS_3 = 3;//已生成
    const SALARY_DEDUCT_TASK_STATUS_4 = 4;//过期未生成
    const SALARY_DEDUCT_TASK_STATUS_5 = 5;//未到期不可生成

    const SALARY_DEDUCT_TASK_TYPE_1 = 1;//任务类型 1-10日
    const SALARY_DEDUCT_TASK_TYPE_2 = 2;//任务类型 1-25日

    /**
     * 薪资抵扣总数据-批次表-实扣状态
     * @var array
     */
    public static $salary_deduct_batch_status = [
        self::SALARY_DEDUCT_BATCH_STATUS_UN_UPLOAD => 'salary_deduct_batch_status.' . self::SALARY_DEDUCT_BATCH_STATUS_UN_UPLOAD,
        self::SALARY_DEDUCT_BATCH_STATUS_ING => 'salary_deduct_batch_status.' . self::SALARY_DEDUCT_BATCH_STATUS_ING,
        self::SALARY_DEDUCT_BATCH_STATUS_PAYROLL => 'salary_deduct_batch_status.' . self::SALARY_DEDUCT_BATCH_STATUS_PAYROLL,
        self::SALARY_DEDUCT_BATCH_STATUS_DONE => 'salary_deduct_batch_status.' . self::SALARY_DEDUCT_BATCH_STATUS_DONE
    ];

    /**
     * 薪资抵扣总数据-批次员工明细表-实扣状态
     * @var array
     */
    public static $salary_deduct_batch_user_status = [
        self::SALARY_DEDUCT_USER_STATUS_SUCCESS => 'salary_deduct_batch_user_status.' . self::SALARY_DEDUCT_USER_STATUS_SUCCESS,
        self::SALARY_DEDUCT_USER_STATUS_UN_START => 'salary_deduct_batch_user_status.' . self::SALARY_DEDUCT_USER_STATUS_UN_START,
        self::SALARY_DEDUCT_USER_STATUS_FAIL => 'salary_deduct_batch_user_status.' . self::SALARY_DEDUCT_USER_STATUS_FAIL
    ];

    /**
     * 薪资抵扣总数据-批次员工-单据明细表-实扣状态
     * @var array
     */
    public static $salary_deduct_batch_user_order_status = [
        self::SALARY_DEDUCT_USER_ORDER_STATUS_UN_CREATE => 'salary_deduct_batch_user_order_status.' . self::SALARY_DEDUCT_USER_ORDER_STATUS_UN_CREATE,
        self::SALARY_DEDUCT_USER_ORDER_STATUS_DONE => 'salary_deduct_batch_user_order_status.' . self::SALARY_DEDUCT_USER_ORDER_STATUS_DONE,
    ];

    /**
     * 抵扣类型
     * @var array
     */
    public static $salary_deduct_type = [
        self::SALARY_DEDUCT_TYPE_LIMIT => 'salary_deduct_type.' . self::SALARY_DEDUCT_TYPE_LIMIT,
        self::SALARY_DEDUCT_TYPE_ALL => 'salary_deduct_type.' . self::SALARY_DEDUCT_TYPE_ALL,
    ];
}
