<?php

namespace App\Library\Enums;

final class ReserveFundReturnEnums
{
    //归还类型：1:归还，2:坏账，3:薪资抵扣
    const RETURN_TYPE = 1;              //归还
    const RETURN_BAD_TYPE = 2;          //坏账
    const RETURN_SALARY_DEDUCT_TYPE = 3;//薪资抵扣

    //审核状态-1待审核，2拒绝（驳回），3同意，4撤回
    const STATUS_WAIT = 1;              //待审核
    const STATUS_REJECT = 2;            //被驳回
    const STATUS_PASS = 3;              //通过
    const STATUS_CANCEL = 4;            //撤回

    const BACK_STATUS_INIT = 0;        //初始化状态
    const BACK_STATUS_NOT = 1;         //未归还
    const BACK_STATUS_ING = 2;         //归还中
    const BACK_STATUS_BACK = 3;        //已归还
    const BACK_STATUS_CONFIRM = 4;     //待确认
    const BACK_STATUS_NOT_RECOVER = 5; //未追回


    const PAY_STATUS_PENDING = 1; //待支付
    const PAY_STATUS_PAY = 2;     //已支付
    const PAY_STATUS_NOTPAY = 3;  //未支付

    const APPLY_TYPE_APPLY = 1; //归还申请归还
    const APPLY_TYPE_QUERY = 2; //查询列表归还

    /**
     * 归还状态
     * @var array
     */
    public static $back_status = [
        self::BACK_STATUS_NOT         => 'loan_back_status.1',
        self::BACK_STATUS_ING         => 'loan_back_status.2',
        self::BACK_STATUS_BACK        => 'loan_back_status.3',
        self::BACK_STATUS_CONFIRM     => 'reserve_return_status.4',
        self::BACK_STATUS_NOT_RECOVER => 'reserve_return_status.5',
    ];
}