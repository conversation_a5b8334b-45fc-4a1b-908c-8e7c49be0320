<?php

namespace App\Library\Enums;

final class PaperDocumentEnums
{
    const CONFIRM_STATE_PENDING_SUBMIT = 1; // 待提交
    const CONFIRM_STATE_PENDING_CONFIRM = 2; // 待确认
    const CONFIRM_STATE_COMPLETE = 3; // 已齐全
    const CONFIRM_STATE_PENDING_FILL = 4; // 待补齐

    //是否需要补交
    const ADDITIONAL_STATE_NO = 0; //不需要
    const ADDITIONAL_STATE_YES = 1; //需要

    //Tab
    const TAB_PENDING = 1; //待处理 Tab
    const TAB_FINISHED = 2; //已处理 Tab

    const IS_ADDITIONAL_NO = 0; // 非补交
    const IS_ADDITIONAL_YES = 1; // 补交
    const IS_ADDITIONAL_ALL = 2; // 全部

    // 默认补交原因
    const DEFAULT_SUBMIT_REASON = 'Submit supplementary paper documents';

    // 默认齐全原因
    const DEFAULT_COMPLETE_REASON = 'The paper documents are complete';

}