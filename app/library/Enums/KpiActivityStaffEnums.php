<?php
namespace App\Library\Enums;
final class KpiActivityStaffEnums
{
    //删除标志
    const IS_DELETED_NO = 0;//未删除
    const IS_DELETED_YES = 1;//已删除

    //类型：1PBP；2leader
    const LEADER_TYPE_PBP = 1;
    const LEADER_TYPE_LEADER = 2;

    //当前阶段:0待参与活动,1待制定目标，2待确认目标，3确认完毕，目标执行
    const STAGE_DEFAULT = 0;//待参与活动
    const STAGE_LEADER = 1; //待制定目标
    const STAGE_STAFF = 2;//待确认目标
    const STAGE_DONE = 3; //确认完毕，目标执行

    //站内信消息分类
    const MESSAGE_CATEGORY_KPI_STAFF_CONFIRM = 50;//KPI绩效员工确认消息分类
    const MESSAGE_CATEGORY_KPI_LEADER = 54;//KPI绩效Leader制定kpi消息分类

    //当前阶段配置
    public static $kpi_activity_staff_stage = [
        self::STAGE_LEADER => 'kpi_activity_staff_stage.' . self::STAGE_LEADER,
        self::STAGE_STAFF => 'kpi_activity_staff_stage.' . self::STAGE_STAFF,
        self::STAGE_DONE => 'kpi_activity_staff_stage.' . self::STAGE_DONE,
    ];

    //阶段状态
    const STAGE_LEADER_1 = 1; //待提交
    const STAGE_LEADER_2 = 2; //被驳回
    const STAGE_PPM_1 = 3;//待审核
    const STAGE_STAFF_1 = 4; //待确认
    const STAGE_STAFF_2 = 5; //已确认
    public static $kpi_activity_staff_stage_status = [
        self::STAGE_LEADER_1 => 'kpi_activity_staff_stage_status.1',
        self::STAGE_LEADER_2 => 'kpi_activity_staff_stage_status.2',
        self::STAGE_PPM_1 => 'kpi_activity_staff_stage_status.3',
        self::STAGE_STAFF_1 => 'kpi_activity_staff_stage_status.4',
        self::STAGE_STAFF_2 => 'kpi_activity_staff_stage_status.5',
    ];

    //KPI_PPM setting_env code
    const PPM_ENV_CODE = 'kpi_ppm';

    //网点-1 name
    const STORE_HEADER_OFFICE_ID = '-1';
    const STORE_HEADER_OFFICE_NAME = 'Head Office';

    //上级站内信html样式
    const MSG_STYLE_BEGIN = "<div style='font-size: 40px'>";
    const MSG_STYLE_END = "</div>";

    //鉴权类型0=>PPM，1=>直线上级，2=>部门负责人，3=>HRBP
    const PERMISSION_PPM = 0;
    const PERMISSION_LEADER = 1;
    const PERMISSION_DEPARTMENT = 2;
    const PERMISSION_HRBP = 3;

    //是否变更过KPI指定人:1否，2是
    const IS_CHANGE_LEADER_NO = 1;
    const IS_CHANGE_LEADER_YES = 2;

    // 是否推送通知上级:0否，1是
    const IS_SEND_LEADER_NO = 0;
    const IS_SEND_LEADER_YES = 1;

    /**
     * 是否变更过KPI指定人
     * @var array
     */
    public static $kpi_activity_staff_is_change_leader = [
        self::IS_CHANGE_LEADER_NO => 'kpi_activity_staff_is_change_leader.' . self::IS_CHANGE_LEADER_NO,
        self::IS_CHANGE_LEADER_YES => 'kpi_activity_staff_is_change_leader.' . self::IS_CHANGE_LEADER_YES,
    ];

    // 开启kpi活动的首次推送
    const REMIND_LEADER_TITLE = 'kpi_send_msg_to_leader_title';//提醒上级填写标题翻译key
    const REMIND_LEADER_CONTENT = 'kpi_send_msg_to_leader_content';//提醒上级填写内容翻译key 未开启限制打卡的内容
    const REMIND_LEADER_CONTENT_1 = 'kpi_send_msg_to_leader_content_1';//提醒上级填写内容翻译key 已开启限制打卡的内容

    // kpi制定人变更通知
    const REMIND_LEADER_CHANGE_TITLE = 'kpi_send_msg_to_leader_change_title';       //kpi 制定人变更通知title
    const REMIND_LEADER_CHANGE_CONTENT = 'kpi_send_msg_to_leader_change_content';   //kpi 制定人变更通知content

    const REMIND_STAFF_TITLE = 'kpi_send_msg_to_staff_title';//提醒员工确认标题翻译key
    const REMIND_STAFF_CONTENT = 'kpi_send_msg_to_staff_content';//提醒员工确认内容翻译key

    // 员工kpi中-一键催办上级
    const STAFF_KPI_REMIND_LEADER_TITLE = 'staff_kpi_send_msg_to_leader_title';         //员工kpi-一键催办目标指定人 title
    const STAFF_KPI_REMIND_LEADER_CONTENT = 'staff_kpi_send_msg_to_leader_content';     //员工kpi-一键催办目标指定人 未开启限制打卡的文案
    const STAFF_KPI_REMIND_LEADER_CONTENT_1 = 'staff_kpi_send_msg_to_leader_content_1'; //员工kpi-一键催办目标指定人 开启限制打卡的文案
    const STAFF_KPI_REMIND_LEADER_CONTENT_2 = 'staff_kpi_send_msg_to_leader_content_2'; //员工kpi-一键催办目标指定人 kpi_leader_msg_send_log表中 获取不到最小发送时间的文案

    const MAX_CHANGE_MANGER_NUMS = 2000;

}