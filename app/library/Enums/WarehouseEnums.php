<?php
/**
 * 仓库枚举
 */

namespace App\Library\Enums;

final class WarehouseEnums
{
    //同步导出最大行数
    const SYNC_EXPORT_MAX_COUNT = 10000;

    //面积正则：允许输入大于0小于等于999999.99的数字，支持2位小数
    const AREA_RULE = '/^(?:[1-9]\d{0,5}(?:\.\d{1,2})?|0\.\d{1,2})$/';

    //报价 - 仓库面积正则：允许输入大于0小于等于999999999.99的数字，支持2位小数
    const AREA_RULE_PRICE = '/^(?:[1-9]\d{0,8}(?:\.\d{1,2})?|0\.\d{1,2})$/';

    //价格正则：允许输入大于等于0小于等于999999999.99的数字，支持2位小数
    const PRICE = '/^\d{0,9}(?:\.[0-9]{1,2})?$/';

    // 停车位数量正则
    const PARKING_NUM_RULE = '/^([1-9]\d{0,3}|[1-9])$/';

    /*** 仓库需求相关枚举 ***/
    //需求编号前缀
    const REQUIREMENT_NO_PREFIX = 'WR';

    //网点开业状态：1-待开业、2-营业中
    const REQUIREMENT_OPENING_STATUS_WAIT = 1;
    const REQUIREMENT_OPENING_STATUS_ING = 2;
    const REQUIREMENT_OPENING_STATUS_VALIDATE = self::REQUIREMENT_OPENING_STATUS_WAIT . ',' . self::REQUIREMENT_OPENING_STATUS_ING;
    public static $opening_status = [
        self::REQUIREMENT_OPENING_STATUS_WAIT => 'warehouse_requirement_opening_status.' . self::REQUIREMENT_OPENING_STATUS_WAIT,
        self::REQUIREMENT_OPENING_STATUS_ING  => 'warehouse_requirement_opening_status.' . self::REQUIREMENT_OPENING_STATUS_ING,
    ];

    //仓库类型：1-拆分新建、2-到期更换、3-共享仓、4-优化仓
    const REQUIREMENT_WAREHOUSE_TYPE_ADD = 1;
    const REQUIREMENT_WAREHOUSE_TYPE_REPLACE = 2;
    const REQUIREMENT_WAREHOUSE_TYPE_SHARE = 3;
    const REQUIREMENT_WAREHOUSE_TYPE_OPTIMIZE = 4;
    const REQUIREMENT_WAREHOUSE_TYPE_VALIDATE = self::REQUIREMENT_WAREHOUSE_TYPE_ADD . ',' . self::REQUIREMENT_WAREHOUSE_TYPE_REPLACE . ',' . self::REQUIREMENT_WAREHOUSE_TYPE_SHARE . ',' . self::REQUIREMENT_WAREHOUSE_TYPE_OPTIMIZE;
    public static $warehouse_type = [
        self::REQUIREMENT_WAREHOUSE_TYPE_ADD      => 'warehouse_requirement_warehouse_type.' . self::REQUIREMENT_WAREHOUSE_TYPE_ADD,
        self::REQUIREMENT_WAREHOUSE_TYPE_REPLACE  => 'warehouse_requirement_warehouse_type.' . self::REQUIREMENT_WAREHOUSE_TYPE_REPLACE,
        self::REQUIREMENT_WAREHOUSE_TYPE_SHARE    => 'warehouse_requirement_warehouse_type.' . self::REQUIREMENT_WAREHOUSE_TYPE_SHARE,
        self::REQUIREMENT_WAREHOUSE_TYPE_OPTIMIZE => 'warehouse_requirement_warehouse_type.' . self::REQUIREMENT_WAREHOUSE_TYPE_OPTIMIZE,
    ];

    // TH仓库类型常量标识
    const REQUIREMENT_WAREHOUSE_TYPE_RENEWED = 'renewed';
    const REQUIREMENT_WAREHOUSE_TYPE_CHANGE_ADDR = 'change_addr';
    const REQUIREMENT_WAREHOUSE_TYPE_TEMPORARY = 'temporary';
    const REQUIREMENT_WAREHOUSE_TYPE_SITE = 'site';
    const REQUIREMENT_WAREHOUSE_TYPE_NEW_STORE = 'new_store';


    //优先级：1-P0、2-P1、3-P2、4-P3
    const REQUIREMENT_PRIORITY_LEVEL_P0 = 1;
    const REQUIREMENT_PRIORITY_LEVEL_P1 = 2;
    const REQUIREMENT_PRIORITY_LEVEL_P2 = 3;
    const REQUIREMENT_PRIORITY_LEVEL_P3 = 4;
    const REQUIREMENT_PRIORITY_LEVEL_VALIDATE = self::REQUIREMENT_PRIORITY_LEVEL_P0 . ',' . self::REQUIREMENT_PRIORITY_LEVEL_P1 . ',' . self::REQUIREMENT_PRIORITY_LEVEL_P2 . ',' . self::REQUIREMENT_PRIORITY_LEVEL_P3;
    public static $priority_level = [
        self::REQUIREMENT_PRIORITY_LEVEL_P0 => 'warehouse_requirement_priority_level.' . self::REQUIREMENT_PRIORITY_LEVEL_P0,
        self::REQUIREMENT_PRIORITY_LEVEL_P1 => 'warehouse_requirement_priority_level.' . self::REQUIREMENT_PRIORITY_LEVEL_P1,
        self::REQUIREMENT_PRIORITY_LEVEL_P2 => 'warehouse_requirement_priority_level.' . self::REQUIREMENT_PRIORITY_LEVEL_P2,
        self::REQUIREMENT_PRIORITY_LEVEL_P3 => 'warehouse_requirement_priority_level.' . self::REQUIREMENT_PRIORITY_LEVEL_P3,
    ];

    //需求状态：1-待寻找、2-待确认、3-待报价、4-报价审核中、5-待签约、6-待付款、7-待入驻、8-已入驻、9-已作废、10-待续约
    const REQUIREMENT_STATUS_SEARCH = 1;
    const REQUIREMENT_STATUS_CONFIRM = 2;
    const REQUIREMENT_STATUS_PRICE = 3;
    const REQUIREMENT_STATUS_PRICE_AUDIT = 4;
    const REQUIREMENT_STATUS_SIGN = 5;
    const REQUIREMENT_STATUS_PAY = 6;
    const REQUIREMENT_STATUS_SETTLE = 7;
    const REQUIREMENT_STATUS_SETTLED = 8;
    const REQUIREMENT_STATUS_CANCEL = 9;
    const REQUIREMENT_STATUS_PENDING_RENEWED = 10;

    public static $status = [
        self::REQUIREMENT_STATUS_SEARCH      => 'warehouse_requirement_status.' . self::REQUIREMENT_STATUS_SEARCH,
        self::REQUIREMENT_STATUS_CONFIRM     => 'warehouse_requirement_status.' . self::REQUIREMENT_STATUS_CONFIRM,
        self::REQUIREMENT_STATUS_PRICE       => 'warehouse_requirement_status.' . self::REQUIREMENT_STATUS_PRICE,
        self::REQUIREMENT_STATUS_PRICE_AUDIT => 'warehouse_requirement_status.' . self::REQUIREMENT_STATUS_PRICE_AUDIT,
        self::REQUIREMENT_STATUS_SIGN        => 'warehouse_requirement_status.' . self::REQUIREMENT_STATUS_SIGN,
        self::REQUIREMENT_STATUS_PAY         => 'warehouse_requirement_status.' . self::REQUIREMENT_STATUS_PAY,
        self::REQUIREMENT_STATUS_SETTLE      => 'warehouse_requirement_status.' . self::REQUIREMENT_STATUS_SETTLE,
        self::REQUIREMENT_STATUS_SETTLED     => 'warehouse_requirement_status.' . self::REQUIREMENT_STATUS_SETTLED,
        self::REQUIREMENT_STATUS_CANCEL      => 'warehouse_requirement_status.' . self::REQUIREMENT_STATUS_CANCEL,
    ];
    const REQUIREMENT_STATUS_VALIDATE = self::REQUIREMENT_STATUS_SEARCH . ',' . self::REQUIREMENT_STATUS_CONFIRM . ',' . self::REQUIREMENT_STATUS_PRICE . ',' . self::REQUIREMENT_STATUS_PRICE_AUDIT . ',' .
    self::REQUIREMENT_STATUS_SIGN . ',' . self::REQUIREMENT_STATUS_PAY . ',' . self::REQUIREMENT_STATUS_SETTLE . ',' . self::REQUIREMENT_STATUS_SETTLED . ',' . self::REQUIREMENT_STATUS_CANCEL . ',' . self::REQUIREMENT_STATUS_PENDING_RENEWED;

    public static $th_status = [
        self::REQUIREMENT_STATUS_SEARCH          => 'warehouse_requirement_status.' . self::REQUIREMENT_STATUS_SEARCH,
        self::REQUIREMENT_STATUS_CONFIRM         => 'warehouse_requirement_status.' . self::REQUIREMENT_STATUS_CONFIRM,
        self::REQUIREMENT_STATUS_PRICE           => 'warehouse_requirement_status.' . self::REQUIREMENT_STATUS_PRICE,
        self::REQUIREMENT_STATUS_PRICE_AUDIT     => 'warehouse_requirement_status.' . self::REQUIREMENT_STATUS_PRICE_AUDIT,
        self::REQUIREMENT_STATUS_PENDING_RENEWED => 'warehouse_requirement_status.' . self::REQUIREMENT_STATUS_PENDING_RENEWED,
        self::REQUIREMENT_STATUS_SIGN            => 'warehouse_requirement_status.' . self::REQUIREMENT_STATUS_SIGN,
        self::REQUIREMENT_STATUS_PAY             => 'warehouse_requirement_status.' . self::REQUIREMENT_STATUS_PAY,
        self::REQUIREMENT_STATUS_SETTLE          => 'warehouse_requirement_status.' . self::REQUIREMENT_STATUS_SETTLE,
        self::REQUIREMENT_STATUS_SETTLED         => 'warehouse_requirement_status.' . self::REQUIREMENT_STATUS_SETTLED,
        self::REQUIREMENT_STATUS_CANCEL          => 'warehouse_requirement_status.' . self::REQUIREMENT_STATUS_CANCEL,
    ];

    /**
     * 获取需求状态
     */
    public static function getRequirementStatus()
    {
        return get_country_code() == GlobalEnums::TH_COUNTRY_CODE ? self::$th_status : self::$status;
    }

    /*** 仓库需求确认相关枚举 ***/
    //确认结果：1-满足、2-不满足、3-谈判失败
    const REQUIREMENT_CONFIRM_STATUS_OK = 1;
    const REQUIREMENT_CONFIRM_STATUS_DONT = 2;
    const REQUIREMENT_CONFIRM_STATUS_NEGOTIATIONS_FAILED = 3;
    public static $confirm_status = [
        self::REQUIREMENT_CONFIRM_STATUS_OK   => 'warehouse_requirement_confirm_status.' . self::REQUIREMENT_CONFIRM_STATUS_OK,
        self::REQUIREMENT_CONFIRM_STATUS_DONT => 'warehouse_requirement_confirm_status.' . self::REQUIREMENT_CONFIRM_STATUS_DONT,
        self::REQUIREMENT_CONFIRM_STATUS_NEGOTIATIONS_FAILED => 'warehouse_requirement_confirm_status.' . self::REQUIREMENT_CONFIRM_STATUS_NEGOTIATIONS_FAILED,
    ];


    /*** 仓库线索相关枚举 ***/
    //线索编号前缀
    const THREAD_NO_PREFIX = 'WT';
    //仓库是否有CR：1-有，2-无、仓库电供应：1-有，2-无、仓库水供应：1-有，2-无
    const THREAD_HAS_YES = 1;//有
    const THREAD_HAS_NO = 2;//无
    public static $thread_has = [
        self::THREAD_HAS_YES => 'warehouse_thread_has.' . self::THREAD_HAS_YES,
        self::THREAD_HAS_NO  => 'warehouse_thread_has.' . self::THREAD_HAS_NO,
    ];
    const THREAD_HAS_VALIDATE = self::THREAD_HAS_YES . ',' . self::THREAD_HAS_NO;

    //合同期单位：1-年、2-月
    const THREAD_CONTRACT_PERIOD_UNIT_YEAR = 1;
    const THREAD_CONTRACT_PERIOD_UNIT_MONTH = 2;
    public static $contract_period_unit = [
        self::THREAD_CONTRACT_PERIOD_UNIT_YEAR  => 'warehouse_contract_period_unit.' . self::THREAD_CONTRACT_PERIOD_UNIT_YEAR,
        self::THREAD_CONTRACT_PERIOD_UNIT_MONTH => 'warehouse_contract_period_unit.' . self::THREAD_CONTRACT_PERIOD_UNIT_MONTH,
    ];
    const THREAD_CONTRACT_PERIOD_UNIT_VALIDATE = self::THREAD_CONTRACT_PERIOD_UNIT_YEAR . ',' . self::THREAD_CONTRACT_PERIOD_UNIT_MONTH;

    //房东类型：1-公司、2-个人
    const THREAD_LANDLORD_TYPE_COMPANY = 1;
    const THREAD_LANDLORD_TYPE_PERSONAL = 2;
    public static $landlord_type = [
        self::THREAD_LANDLORD_TYPE_COMPANY  => 'warehouse_landlord_type.' . self::THREAD_LANDLORD_TYPE_COMPANY,
        self::THREAD_LANDLORD_TYPE_PERSONAL => 'warehouse_landlord_type.' . self::THREAD_LANDLORD_TYPE_PERSONAL,
    ];
    const THREAD_LANDLORD_TYPE_VALIDATE = self::THREAD_LANDLORD_TYPE_COMPANY . ',' . self::THREAD_LANDLORD_TYPE_PERSONAL;

    //房东资质文件是否齐全：1-齐全、2-不齐全
    const THREAD_LANDLORD_APTITUDE_COMPLETE = 1;
    const THREAD_LANDLORD_APTITUDE_INCOMPLETE = 2;
    public static $landlord_aptitude = [
        self::THREAD_LANDLORD_APTITUDE_COMPLETE   => 'warehouse_landlord_aptitude.' . self::THREAD_LANDLORD_APTITUDE_COMPLETE,
        self::THREAD_LANDLORD_APTITUDE_INCOMPLETE => 'warehouse_landlord_aptitude.' . self::THREAD_LANDLORD_APTITUDE_INCOMPLETE,
    ];
    const THREAD_LANDLORD_APTITUDE_VALIDATE = self::THREAD_LANDLORD_APTITUDE_COMPLETE . ',' . self::THREAD_LANDLORD_APTITUDE_INCOMPLETE;


    //线索状态：1-待确认、2-确认中、3-待报价、4-报价审核中、5-待签约、6-签约流程中、7-待付款、8-待入驻、9-已入驻、10-被淘汰、11-待关联、12-已作废
    const THREAD_STATUS_CONFIRM = 1;
    const THREAD_STATUS_CONFIRM_ING = 2;
    const THREAD_STATUS_PRICE = 3;
    const THREAD_STATUS_PRICE_AUDIT = 4;
    const THREAD_STATUS_SIGN = 5;
    const THREAD_STATUS_SIGN_ING = 6;
    const THREAD_STATUS_PAY = 7;
    const THREAD_STATUS_SETTLE = 8;
    const THREAD_STATUS_SETTLED = 9;
    const THREAD_STATUS_ELIMINATE = 10;
    const THREAD_STATUS_ASSOCIATION = 11;
    const THREAD_STATUS_CANCEL = 12;

    public static $thread_status = [
        self::THREAD_STATUS_CONFIRM     => 'warehouse_thread_status.' . self::THREAD_STATUS_CONFIRM,
        self::THREAD_STATUS_CONFIRM_ING => 'warehouse_thread_status.' . self::THREAD_STATUS_CONFIRM_ING,
        self::THREAD_STATUS_PRICE       => 'warehouse_thread_status.' . self::THREAD_STATUS_PRICE,
        self::THREAD_STATUS_PRICE_AUDIT => 'warehouse_thread_status.' . self::THREAD_STATUS_PRICE_AUDIT,
        self::THREAD_STATUS_SIGN        => 'warehouse_thread_status.' . self::THREAD_STATUS_SIGN,
        self::THREAD_STATUS_SIGN_ING    => 'warehouse_thread_status.' . self::THREAD_STATUS_SIGN_ING,
        self::THREAD_STATUS_PAY         => 'warehouse_thread_status.' . self::THREAD_STATUS_PAY,
        self::THREAD_STATUS_SETTLE      => 'warehouse_thread_status.' . self::THREAD_STATUS_SETTLE,
        self::THREAD_STATUS_SETTLED     => 'warehouse_thread_status.' . self::THREAD_STATUS_SETTLED,
        self::THREAD_STATUS_ELIMINATE   => 'warehouse_thread_status.' . self::THREAD_STATUS_ELIMINATE,
        self::THREAD_STATUS_ASSOCIATION => 'warehouse_thread_status.' . self::THREAD_STATUS_ASSOCIATION,
        self::THREAD_STATUS_CANCEL      => 'warehouse_thread_status.' . self::THREAD_STATUS_CANCEL,
    ];
    const THREAD_STATUS_VALIDATE = self::THREAD_STATUS_CONFIRM . ',' . self::THREAD_STATUS_CONFIRM_ING . ',' . self::THREAD_STATUS_PRICE . ',' . self::THREAD_STATUS_PRICE_AUDIT . ',' . self::THREAD_STATUS_SIGN . ',' . self::THREAD_STATUS_SIGN_ING . ',' .
    self::THREAD_STATUS_PAY . ',' . self::THREAD_STATUS_SETTLE . ',' . self::THREAD_STATUS_SETTLED . ',' . self::THREAD_STATUS_ELIMINATE . ',' . self::THREAD_STATUS_ASSOCIATION . ',' . self::THREAD_STATUS_CANCEL;



    /*** 仓库线索报价相关枚举 ***/
    //线索报价编号前缀
    const THREAD_PRICE_NO_PREFIX = 'WP';
    //数据来源：0-仓库管理-处理仓库线索-待报价-发起报价、1-仓库管理-仓库信息管理-续签报价
    // 2-处理仓库需求-待续约-续约报价
    const THREAD_PRICE_SOURCE_TYPE_THREAD = 0;
    const THREAD_PRICE_SOURCE_TYPE_WAREHOUSE = 1;
    const THREAD_PRICE_SOURCE_TYPE_RM_RENEWED = 2;
    const THREAD_PRICE_SOURCE_VALIDATE = self::THREAD_PRICE_SOURCE_TYPE_THREAD . ',' . self::THREAD_PRICE_SOURCE_TYPE_WAREHOUSE . ',' . self::THREAD_PRICE_SOURCE_TYPE_RM_RENEWED;

    //申请类型：1-续签、2-新签约、3-到期更换、4-扩大面积
    const THREAD_APPLY_TYPE_RENEW = 1;
    const THREAD_APPLY_TYPE_NEW = 2;
    const THREAD_APPLY_TYPE_REPLACE = 3;
    const THREAD_APPLY_TYPE_EXTEND = 4;
    public static $thread_price_apply_type = [
        self::THREAD_APPLY_TYPE_RENEW   => 'warehouse_thread_price_apply_type.' . self::THREAD_APPLY_TYPE_RENEW,
        self::THREAD_APPLY_TYPE_NEW     => 'warehouse_thread_price_apply_type.' . self::THREAD_APPLY_TYPE_NEW,
        self::THREAD_APPLY_TYPE_REPLACE => 'warehouse_thread_price_apply_type.' . self::THREAD_APPLY_TYPE_REPLACE,
        self::THREAD_APPLY_TYPE_EXTEND  => 'warehouse_thread_price_apply_type.' . self::THREAD_APPLY_TYPE_EXTEND,
    ];
    const THREAD_APPLY_TYPE_VALIDATE = self::THREAD_APPLY_TYPE_RENEW . ',' . self::THREAD_APPLY_TYPE_NEW . ',' . self::THREAD_APPLY_TYPE_REPLACE . ',' . self::THREAD_APPLY_TYPE_EXTEND;

    // 租金支付方式
    const THREAD_RENT_PAYMENT_METHOD_DAY = 1;
    const THREAD_RENT_PAYMENT_METHOD_MONTH = 2;
    const THREAD_RENT_PAYMENT_METHOD_YEAR = 3;
    const THREAD_RENT_PAYMENT_METHOD_HALF_YEAR = 4;
    const THREAD_RENT_PAYMENT_METHOD_ONE_TIME = 5;
    public static $thread_rent_payment_method = [
        self::THREAD_RENT_PAYMENT_METHOD_DAY       => 'warehouse_thread_rent_payment_method_' . self::THREAD_RENT_PAYMENT_METHOD_DAY,
        self::THREAD_RENT_PAYMENT_METHOD_MONTH     => 'warehouse_thread_rent_payment_method_' . self::THREAD_RENT_PAYMENT_METHOD_MONTH,
        self::THREAD_RENT_PAYMENT_METHOD_YEAR      => 'warehouse_thread_rent_payment_method_' . self::THREAD_RENT_PAYMENT_METHOD_YEAR,
        self::THREAD_RENT_PAYMENT_METHOD_HALF_YEAR => 'warehouse_thread_rent_payment_method_' . self::THREAD_RENT_PAYMENT_METHOD_HALF_YEAR,
        self::THREAD_RENT_PAYMENT_METHOD_ONE_TIME  => 'warehouse_thread_rent_payment_method_' . self::THREAD_RENT_PAYMENT_METHOD_ONE_TIME,
    ];

    // 水费支付类型
    const THREAD_WATER_BILL_PAYMENT_TYPE_AGENCY = 1;
    const THREAD_WATER_BILL_PAYMENT_TYPE_LANDLORD = 2;
    public static $thread_water_bill_payment_type = [
        self::THREAD_WATER_BILL_PAYMENT_TYPE_AGENCY   => 'warehouse_thread_water_bill_payment_type_' . self::THREAD_WATER_BILL_PAYMENT_TYPE_AGENCY,
        self::THREAD_WATER_BILL_PAYMENT_TYPE_LANDLORD => 'warehouse_thread_water_bill_payment_type_' . self::THREAD_WATER_BILL_PAYMENT_TYPE_LANDLORD,
    ];

    // 电费支付类型
    const THREAD_ELECTRICITY_BILL_PAYMENT_TYPE_AGENCY = 1;
    const THREAD_ELECTRICITY_BILL_PAYMENT_TYPE_LANDLORD = 2;
    public static $thread_electricity_bill_payment_type = [
        self::THREAD_ELECTRICITY_BILL_PAYMENT_TYPE_AGENCY   => 'warehouse_thread_electricity_bill_payment_type_' . self::THREAD_ELECTRICITY_BILL_PAYMENT_TYPE_AGENCY,
        self::THREAD_ELECTRICITY_BILL_PAYMENT_TYPE_LANDLORD => 'warehouse_thread_electricity_bill_payment_type_' . self::THREAD_ELECTRICITY_BILL_PAYMENT_TYPE_LANDLORD,
    ];

    // 水费使用计费方式
    const THREAD_WATER_BILLING_METHOD_UNIT = 1;// 按单位
    const THREAD_WATER_BILLING_METHOD_UNITY = 2;// 统一收费
    public static $thread_water_billing_method = [
        self::THREAD_WATER_BILLING_METHOD_UNIT  => 'warehouse_thread_water_billing_method_' . self::THREAD_WATER_BILLING_METHOD_UNIT,
        self::THREAD_WATER_BILLING_METHOD_UNITY => 'warehouse_thread_water_billing_method_' . self::THREAD_WATER_BILLING_METHOD_UNITY,
    ];

    // 电费使用计费方式
    const THREAD_ELECTRICITY_BILLING_METHOD_UNIT = 1;
    const THREAD_ELECTRICITY_BILLING_METHOD_UNITY = 2;
    public static $thread_electricity_billing_method = [
        self::THREAD_ELECTRICITY_BILLING_METHOD_UNIT  => 'warehouse_thread_electricity_billing_method_' . self::THREAD_ELECTRICITY_BILLING_METHOD_UNIT,
        self::THREAD_ELECTRICITY_BILLING_METHOD_UNITY => 'warehouse_thread_electricity_billing_method_' . self::THREAD_ELECTRICITY_BILLING_METHOD_UNITY,
    ];

    // 有无安装灭火器
    const THREAD_INSTALL_FIRE_EXTINGUISHERS_YES = 1;
    const THREAD_INSTALL_FIRE_EXTINGUISHERS_NO = 2;
    public static $thread_install_fire_extinguishers = [
        self::THREAD_INSTALL_FIRE_EXTINGUISHERS_YES => 'warehouse_thread_install_fire_extinguishers_' . self::THREAD_INSTALL_FIRE_EXTINGUISHERS_YES,
        self::THREAD_INSTALL_FIRE_EXTINGUISHERS_NO  => 'warehouse_thread_install_fire_extinguishers_' . self::THREAD_INSTALL_FIRE_EXTINGUISHERS_NO,
    ];

    // 承担方
    const RESPONSIBLE_PARTY_LANDLORD = 1;
    const RESPONSIBLE_PARTY_LESSEE = 2;

    // 扣缴税责任承担方
    public static $thread_withholding_tax_liability_bearer = [
        self::RESPONSIBLE_PARTY_LANDLORD => 'warehouse_thread_responsible_party_' . self::RESPONSIBLE_PARTY_LANDLORD,
        self::RESPONSIBLE_PARTY_LESSEE   => 'warehouse_thread_responsible_party_' . self::RESPONSIBLE_PARTY_LESSEE,
    ];

    // 土地税责任承担方
    public static $thread_land_tax_liability_bearer = [
        self::RESPONSIBLE_PARTY_LANDLORD => 'warehouse_thread_responsible_party_' . self::RESPONSIBLE_PARTY_LANDLORD,
        self::RESPONSIBLE_PARTY_LESSEE   => 'warehouse_thread_responsible_party_' . self::RESPONSIBLE_PARTY_LESSEE,
    ];

    // 印花税责任承担方
    public static $thread_stamp_duty_liability_bearer = [
        self::RESPONSIBLE_PARTY_LANDLORD => 'warehouse_thread_responsible_party_' . self::RESPONSIBLE_PARTY_LANDLORD,
        self::RESPONSIBLE_PARTY_LESSEE   => 'warehouse_thread_responsible_party_' . self::RESPONSIBLE_PARTY_LESSEE,
    ];




    //上次报价信息来源：0-无、1-报价单、2-网点租房合同
    const THREAD_LAST_PRICE_NONE = 0;
    const THREAD_LAST_PRICE_PRICE = 1;
    const THREAD_LAST_PRICE_CONTRACT = 2;
    const THREAD_LAST_PRICE_VALIDATE = self::THREAD_LAST_PRICE_NONE . ',' . self::THREAD_LAST_PRICE_PRICE . ',' . self::THREAD_LAST_PRICE_CONTRACT;

    /*** 仓库线索验证相关枚举 ***/
    const THREAD_VERIFY_MENU_ID = 1412;//仓库管理-处理仓库线索-待验证
    const ADD_PERMISSION_IDS = [
        1233,//仓库管理
        1386,//处理仓库线索
        self::THREAD_VERIFY_MENU_ID,//仓库管理-处理仓库线索-待验证
        1418,//仓库管理-处理仓库线索-待验证-去验证
        1419,//仓库管理-处理仓库线索-待验证-查看
    ];

    //电供应情况：1-正常、2-无法使用、水供应情况：1-正常、2-无法使用、照明情况：1-正常、2-无法使用
    const THREAD_VERIFY_STATE_FORMAL = 1;
    const THREAD_VERIFY_STATE_UNAVAILABLE = 2;
    public static $thread_verify_state = [
        self::THREAD_VERIFY_STATE_FORMAL      => 'warehouse_thread_verify_state.' . self::THREAD_VERIFY_STATE_FORMAL,
        self::THREAD_VERIFY_STATE_UNAVAILABLE => 'warehouse_thread_verify_state.' . self::THREAD_VERIFY_STATE_UNAVAILABLE,
    ];
    const THREAD_VERIFY_STATE_VALIDATE = self::THREAD_VERIFY_STATE_FORMAL . ',' . self::THREAD_VERIFY_STATE_UNAVAILABLE;

    //状态：1-待验证、2-已提交、3-取消验证
    const THREAD_VERIFY_STATUS_VERIFY = 1;
    const THREAD_VERIFY_STATUS_SUBMIT = 2;
    const THREAD_VERIFY_STATUS_CANCEL = 3;

    // 仓库需求每批异步导入最多条数
    const WAREHOUSE_REQUIREMENT_IMPORT_MAX_COUNT = 2000;

    // 网点最多数量
    const SYS_STORE_DEFAULT_MAX_TOTAL = 50000;
}
