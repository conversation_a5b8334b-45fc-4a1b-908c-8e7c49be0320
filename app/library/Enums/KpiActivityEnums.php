<?php
namespace App\Library\Enums;
final class KpiActivityEnums
{
    //年度配置
    public static $kpi_year = [
        '2022',
        '2023',
        '2024',
        '2025',
        '2026',
        '2027',
        '2028',
        '2029',
        '2030',
        '2031',
        '2032',
    ];
    //年度验证规则
    const YEAR_VALIDATE_RULE = '2022,2032';
    //排序
    const SORT_ASC = 1;//ASC

    //季度配置
    const FIRST_QUARTER = 1; //第一季度
    const SECOND_QUARTER = 2;//第二季度
    const THIRD_QUARTER = 3; //第三季度
    const FOURTH_QUARTER = 4; //第四季度

    //季度配置
    public static $kpi_activity_quarter = [
        self::FIRST_QUARTER => 'kpi_activity_quarter.1',
        self::SECOND_QUARTER => 'kpi_activity_quarter.2',
        self::THIRD_QUARTER => 'kpi_activity_quarter.3',
        self::FOURTH_QUARTER => 'kpi_activity_quarter.4',
    ];

    //周期配置
    const PERIOD_FIRST_HALF_YEAR = 1;//上半年
    const PERIOD_SECOND_HALF_YEAR = 2;//下半年
    const PERIOD_ALL_YEAR = 3;//全年

    //价值观条数
    const ACTIVITY_VALUES_MAX = 100;//最多100条
    /**
     * 周期配置
     * @var array
     */
    public static $kpi_activity_period = [
        self::PERIOD_FIRST_HALF_YEAR => 'kpi_activity_period.' . self::PERIOD_FIRST_HALF_YEAR,
        self::PERIOD_SECOND_HALF_YEAR => 'kpi_activity_period.' . self::PERIOD_SECOND_HALF_YEAR,
        self::PERIOD_ALL_YEAR => 'kpi_activity_period.' . self::PERIOD_ALL_YEAR,
    ];


    const STATUS_ING = 1;//进行中
    const STATUS_DONE = 2; //已完成
    /**
     * 活动状态配置
     * @var array
     */
    public static $kpi_activity_status = [
        self::STATUS_ING => 'kpi_activity_status.1',
        self::STATUS_DONE => 'kpi_activity_status.2',
    ];

    //正整数规则
    const POSITIVE_INTEGER_RULE = '/^\+?[1-9][0-9]*$/';

    const ACTIVITY_TIME_NODE_OFF = 0;  //关闭 活动限制打卡 开关
    const ACTIVITY_TIME_NODE_ON = 1;    // 开启 活动限制打卡 开关
}