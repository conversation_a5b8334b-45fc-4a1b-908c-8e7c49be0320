<?php
namespace App\Library\Enums;
final class InventoryCheckEnums
{
    //分页
    const PAGE_NUM = 1;//当前页码
    const PAGE_SIZE = 20;//每页数量
    //资产盘点最大下载量
    const ASSETS_DOWNLOAD_LIMIT = 300000;
    //资产盘点下载每页条数
    const ASSETS_DOWNLOAD_PAGE_SIZE = 2000;
    //资产明细下载最大条数
    const INVENTORY_ASSET_EXPORT_MAX = 1000;
    //删除标志
    const IS_DELETED_NO = 0;//未删除
    const IS_DELETED_YES = 1;//已删除
    //新旧资产盘点单标识：0旧资产盘点，1新资产盘点
    const IS_NEW_NO = 0;
    const IS_NEW_YES = 1;
    //任务生成状态: 0待生成，1生成中，2已生成
    const TASK_WAIT = 0;
    const TASK_ING = 1;
    const TASK_DONE = 2;
    //盘点策略-0全员盘点，1指定某人盘点，2指定网点主管盘点
    const PLOY_ALL = 0;
    const PLOY_STAFF = 1;
    const PLOY_SYS_STORE_MANGER = 2;
    //盘点单编号前缀
    const INVENTORY_CHECK_SERIAL_NUMBER_PREFIX = 'IC';

    //上传图片类型 资产盘点（员工盘点资产记录表关联图片）
    const OSS_BUCKET_TYPE_INVENTORY_CHECK = 2;

    //盘点单状态
    const INVENTORY_CHECK_STATUS_NOT_STARTED = 1; //未开始
    const INVENTORY_CHECK_STATUS_ING = 2;  //进行中
    const INVENTORY_CHECK_STATUS_DONE = 3; //已完成
    const INVENTORY_CHECK_STATUS_TERMINAL = 4;//已终止
    const INVENTORY_CHECK_STATUS_RULE = self::INVENTORY_CHECK_STATUS_NOT_STARTED . ',' . self::INVENTORY_CHECK_STATUS_ING . ',' . self::INVENTORY_CHECK_STATUS_DONE . ',' . self::INVENTORY_CHECK_STATUS_TERMINAL;
    public static $inventory_check_status = [
        self::INVENTORY_CHECK_STATUS_NOT_STARTED => 'inventory_check_status.1',
        self::INVENTORY_CHECK_STATUS_ING => 'inventory_check_status.2',
        self::INVENTORY_CHECK_STATUS_DONE => 'inventory_check_status.3',
        self::INVENTORY_CHECK_STATUS_TERMINAL => 'inventory_check_status.4'
    ];

    //是否限制打卡/是否盘点名下资产为空的员工：1否，2是
    const INVENTORY_CHECK_WHETHER_NO = 1;
    const INVENTORY_CHECK_WHETHER_YES = 2;
    public static $inventory_check_whether = [
        self::INVENTORY_CHECK_WHETHER_NO => 'inventory_check_whether.1',
        self::INVENTORY_CHECK_WHETHER_YES => 'inventory_check_whether.2'
    ];

    //是否验证规则
    const INVENTORY_CHECK_WHETHER_RULE = self::INVENTORY_CHECK_WHETHER_NO . ',' . self::INVENTORY_CHECK_WHETHER_YES;

    //盘点单盘点范围
    const INVENTORY_CHECK_TYPE_DEFAULT = 0;//默认盘点所有在职员工名下的所有资产
    const INVENTORY_CHECK_TYPE_DEPARTMENT = 1;//根据组织架构
    const INVENTORY_CHECK_TYPE_STAFF = 2;//根据员工工号
    const INVENTORY_CHECK_TYPE_JOB = 3;//根据职位

    //员工盘点任务状态
    const INVENTORY_CHECK_STAFF_STATUS_WAIT = 1; //待盘点
    const INVENTORY_CHECK_STAFF_ING = 2;  //盘点中
    const INVENTORY_CHECK_STAFF_DONE = 3; //已完成
    const INVENTORY_CHECK_STAFF_TERMINAL = 4; //已终止
    public static $inventory_check_staff_status = [
        self::INVENTORY_CHECK_STAFF_STATUS_WAIT => 'inventory_check_staff_status.1',
        self::INVENTORY_CHECK_STAFF_ING => 'inventory_check_staff_status.2',
        self::INVENTORY_CHECK_STAFF_DONE => 'inventory_check_staff_status.3',
        self::INVENTORY_CHECK_STAFF_TERMINAL => 'inventory_check_staff_status.4'
    ];

    //盘点结果-旧资产盘点【18092废弃】
    const INVENTORY_CHECK_STAFF_ASSET_TYPE_DEFAULT = 0; //初始化
    const INVENTORY_CHECK_STAFF_ASSET_TYPE_GET = 1;  //盘到
    const INVENTORY_CHECK_STAFF_ASSET_TYPE_NO = 2; //没盘到
    const INVENTORY_CHECK_STAFF_ASSET_TYPE_HAND_ADD = 3; //手动新增
    const INVENTORY_CHECK_STAFF_ASSET_TYPE_CODE_GET = 4; //扫码盘点
    const INVENTORY_CHECK_STAFF_ASSET_TYPE_CODE_ADD = 5; //扫码新增
    const INVENTORY_CHECK_STAFF_ASSET_TYPE_BATCH = 6; //多项资产确认

    //资产盘点类型【18092废弃】
    public static $inventory_check_staff_asset_type = [
        self::INVENTORY_CHECK_STAFF_ASSET_TYPE_GET => 'inventory_check_staff_asset_type.1',
        self::INVENTORY_CHECK_STAFF_ASSET_TYPE_NO => 'inventory_check_staff_asset_type.2',
        self::INVENTORY_CHECK_STAFF_ASSET_TYPE_HAND_ADD => 'inventory_check_staff_asset_type.3',
        self::INVENTORY_CHECK_STAFF_ASSET_TYPE_CODE_GET => 'inventory_check_staff_asset_type.4',
        self::INVENTORY_CHECK_STAFF_ASSET_TYPE_CODE_ADD => 'inventory_check_staff_asset_type.5',
        self::INVENTORY_CHECK_STAFF_ASSET_TYPE_BATCH => 'inventory_check_staff_asset_type.6',
    ];

    //资产盘点-盘点通知
    const MESSAGE_CATEGORY_INVENTORY_CHECK = 44;//消息类别资产盘点-通知
    const MESSAGE_CATEGORY_INVENTORY_CHECK_REMIND = 109;//消息类别资产盘点-提醒

    //资产盘点-盘点通知-消息标题
    public static $inventory_notice_title = [
        GlobalEnums::TH_COUNTRY_CODE => 'ประกาศเรื่องการตรวจนับทรัพย์สิน 资产盘点通知',
        GlobalEnums::VN_COUNTRY_CODE => 'Thông báo kiểm kê tài sản 资产盘点通知',
        GlobalEnums::LA_COUNTRY_CODE => 'ແຈ້ງການກວດເຊັກຊັບສີນ 资产盘点通知',
        GlobalEnums::MY_COUNTRY_CODE => 'Asset Inventory Notice 资产盘点通知',
        GlobalEnums::PH_COUNTRY_CODE => 'Asset Inventory Notice 资产盘点通知',
        GlobalEnums::ID_COUNTRY_CODE => 'Asset Inventory Notice 资产盘点通知'
    ];

    //资产盘点-盘点报表-提醒-消息标题
    public static $inventory_remind_title = [
        GlobalEnums::TH_COUNTRY_CODE => 'แจ้งเตือนการตรวจนับทรัพย์สิน 资产盘点提醒',
        GlobalEnums::VN_COUNTRY_CODE => 'Nhắc nhở kiểm kê tài sản 资产盘点提醒',
        GlobalEnums::LA_COUNTRY_CODE => 'ແຈ້ງເຕືອນການກວດເຊັກຊັບສີນ 资产盘点提醒',
        GlobalEnums::MY_COUNTRY_CODE => 'Asset Inventory Reminder 资产盘点提醒',
        GlobalEnums::PH_COUNTRY_CODE => 'Asset Inventory Reminder 资产盘点提醒',
        GlobalEnums::ID_COUNTRY_CODE => 'Asset Inventory Reminder 资产盘点提醒'
    ];

    //资产盘点-盘点报表-盘点明细-盘点结果
    const INVENTORY_CHECK_STAFF_ASSET_STATUS_WAIT = 1;//待盘
    const INVENTORY_CHECK_STAFF_ASSET_STATUS_MATCH = 2;//账实相符
    const INVENTORY_CHECK_STAFF_ASSET_STATUS_NOT_MATCH = 3;//账实不符
    const INVENTORY_CHECK_STAFF_ASSET_STATUS_LOSE = 4;//盘亏
    const INVENTORY_CHECK_STAFF_ASSET_STATUS_PROFIT = 5;//盘盈
    const INVENTORY_CHECK_STAFF_ASSET_STATUS_LOW_GET = 6;//低值盘到
    const INVENTORY_CHECK_STAFF_ASSET_STATUS_LOW_LOSE = 7;//低值盘亏
    const INVENTORY_CHECK_STAFF_ASSET_STATUS_LOW_PROFIT = 8;//低值盘盈
    const INVENTORY_CHECK_STAFF_ASSET_STATUS_RULE = self::INVENTORY_CHECK_STAFF_ASSET_STATUS_WAIT . ',' . self::INVENTORY_CHECK_STAFF_ASSET_STATUS_MATCH . ',' . self::INVENTORY_CHECK_STAFF_ASSET_STATUS_NOT_MATCH . ',' . self::INVENTORY_CHECK_STAFF_ASSET_STATUS_LOSE . ',' . self::INVENTORY_CHECK_STAFF_ASSET_STATUS_PROFIT . ',' . self::INVENTORY_CHECK_STAFF_ASSET_STATUS_LOW_GET . ',' . self::INVENTORY_CHECK_STAFF_ASSET_STATUS_LOW_LOSE . ',' . self::INVENTORY_CHECK_STAFF_ASSET_STATUS_LOW_PROFIT;
    public static $inventory_check_staff_asset_status = [
        self::INVENTORY_CHECK_STAFF_ASSET_STATUS_WAIT => 'inventory_check_staff_asset_status.1',
        self::INVENTORY_CHECK_STAFF_ASSET_STATUS_MATCH => 'inventory_check_staff_asset_status.2',
        self::INVENTORY_CHECK_STAFF_ASSET_STATUS_NOT_MATCH => 'inventory_check_staff_asset_status.3',
        self::INVENTORY_CHECK_STAFF_ASSET_STATUS_LOSE => 'inventory_check_staff_asset_status.4',
        self::INVENTORY_CHECK_STAFF_ASSET_STATUS_PROFIT => 'inventory_check_staff_asset_status.5',
        self::INVENTORY_CHECK_STAFF_ASSET_STATUS_LOW_GET => 'inventory_check_staff_asset_status.6',
        self::INVENTORY_CHECK_STAFF_ASSET_STATUS_LOW_LOSE => 'inventory_check_staff_asset_status.7',
        self::INVENTORY_CHECK_STAFF_ASSET_STATUS_LOW_PROFIT => 'inventory_check_staff_asset_status.8',
    ];

    const INVENTORY_TASK_LIST_ALL = 0;//全部
    const INVENTORY_TASK_LIST_TYPE_PENDING = 1;//待处理/待盘点
    const INVENTORY_TASK_LIST_TYPE_PROCESSED = 2;//已处理/已盘点
    //资产盘点-盘点报表-盘点明细-盘点结果-BY端回显
    public static $inventory_check_staff_asset_status_by = [
        self::INVENTORY_CHECK_STAFF_ASSET_STATUS_WAIT => 'inventory_check_staff_asset_status.1',//待盘
        self::INVENTORY_CHECK_STAFF_ASSET_STATUS_MATCH => 'inventory_check_staff_asset_status_by.1',//盘到
        self::INVENTORY_CHECK_STAFF_ASSET_STATUS_NOT_MATCH => 'inventory_check_staff_asset_status_by.2',//信息有误
        self::INVENTORY_CHECK_STAFF_ASSET_STATUS_LOSE => 'inventory_check_staff_asset_status_by.3',//未盘到
        self::INVENTORY_CHECK_STAFF_ASSET_STATUS_PROFIT => 'inventory_check_staff_asset_status_by.4',//新增
    ];

    //点击盘到时必须上传图片、盘点图片仅允许拍照上传
    const INVENTORY_OPEN_NO = 1;//不开启
    const INVENTORY_OPEN_YES = 2;//开启
    public static $inventory_open = [
        self::INVENTORY_OPEN_NO => 'inventory_open.' . self::INVENTORY_OPEN_NO,
        self::INVENTORY_OPEN_YES => 'inventory_open.' . self::INVENTORY_OPEN_YES,
    ];

    //扫码埋点：默认值0非扫码，1:扫码盘到、2扫码新增
    const IS_SCAN_DEFAULT = 0;
    const IS_SCAN_GET = 1;
    const IS_SCAN_ADD = 2;

    //盘点单编号前缀
    const PLAN_CHECK_SERIAL_NUMBER_PREFIX = 'MI';

    //物料盘点

    public static $is_clock = [
        0 => 'N',
        1 => 'Y',
    ];

    //物料盘点频率

    const  PLAN_NOUN_FIND_ONE = 1;
    const  PLAN_NOUN_FIND_ALL = 2;
    public static $plan_noun = [
        self::PLAN_NOUN_FIND_ONE => 'plan_wms_plan_noun.1',
        self::PLAN_NOUN_FIND_ALL => 'plan_wms_plan_noun.2',
    ];

    public static $plan_noun_enums = [
        ['id'=>self::PLAN_NOUN_FIND_ONE,'name'=>'plan_wms_plan_noun.1'],
        ['id'=>self::PLAN_NOUN_FIND_ALL,'name'=>'plan_wms_plan_noun.2']
    ];
    const  PLAN_STORE_TYPE = 1;
    const  PLAN_SCOPE_DEPARTMENT = 2;
    const  PLAN_SCOPE_SITE = 3;
    public static $plan_scope_enums = [
        ['id'=>self::PLAN_STORE_TYPE,'name'=>'plan_wms_stroe_type'],
        ['id'=>self::PLAN_SCOPE_DEPARTMENT,'name'=>'plan_wms_department'],
        ['id'=>self::PLAN_SCOPE_SITE,'name'=>'plan_wms_find_stroe']
    ];
    public static $plan_scope_enums_key = [
        self::PLAN_STORE_TYPE=>'plan_wms_stroe_type',
        self::PLAN_SCOPE_DEPARTMENT=>'plan_wms_department',
        self::PLAN_SCOPE_SITE=>'plan_wms_find_stroe'
    ];

    const  PLAN_MATERIAL_PACKAGE_SKU_STATUS = 1; //启用状态

    //物料盘点单状态
    const PLAN_NOUN_STATUS_NOT_STARTED = 1; //未开始
    const PLAN_NOUN_STATUS_ING = 2;  //进行中
    const PLAN_NOUN_STATUS_REVOKE = 3; //已撤销
    const PLAN_NOUN_STATUS_END = 4; //已结束

    public static $wms_plan_status = [
        self::PLAN_NOUN_STATUS_NOT_STARTED => 'plan_wms_status_not_started.1',
        self::PLAN_NOUN_STATUS_ING => 'plan_wms_status_ing.2',
        self::PLAN_NOUN_STATUS_REVOKE => 'plan_wms_status_revoke.3',
        self::PLAN_NOUN_STATUS_END => 'plan_wms_status_end.4',
    ];

    const PLAN_NOUN_INFO_STATUS_NOT_STARTED = 1; //未开始
    const PLAN_NOUN_INFO_STATUS_ING = 2;  //待盘点
    const PLAN_NOUN_INFO_STATUS_REVOKE = 3; //进行中
    const PLAN_NOUN_INFO_STATUS_END = 4; //已经结束

    public static $wms_plan_info_status = [
        self::PLAN_NOUN_INFO_STATUS_NOT_STARTED => 'plan_wms_status_not_started.1',
        self::PLAN_NOUN_INFO_STATUS_ING => 'plan_wms_status_wait_for.2',
        self::PLAN_NOUN_INFO_STATUS_REVOKE => 'plan_wms_status_ing.2',
        self::PLAN_NOUN_INFO_STATUS_END => 'plan_wms_status_end.4',
    ];
    const PLAN_IMPORT_STORE_MAX = 500;//添加计划-批量导入网点-最大条数


    const STAFF_STATE_IN = 1; //在职
    const STAFF_STATE_LEAVE = 2;  //离职
    const STAFF_STATE_STOP = 3; //停职
    public static $staff_state = [
        self::STAFF_STATE_IN => 'staff_state.1',
        self::STAFF_STATE_LEAVE => 'staff_state.2',
        self::STAFF_STATE_STOP => 'staff_state.3'
    ];

    const DEFAULT_LANG = 'th';
    const DEFAULT_VALUES = 0;
    const DEFAULT_VALUES_END = 1;
    const  MATERIAL_WMS_PLANTASK_INFO_NOT = 1;//已盘点
    const  MATERIAL_WMS_PLANTASK_INFO_YES = 2;//未盘点
    const  CATEGORY_ID = 6;//代理商网点类型








}
