<?php

namespace App\Library\Enums;

final class InvoiceEnums
{
    //业务数据类型
    const ORDINARY_PAYMENT = 1;
    const PURCHASE_PAYMENT = 2;
    const AGENCY_PAYMENT = 3;
    const CREDIT_ORDINARY_PAYMENT = 4;
    const CREDIT_PURCHASE_PAYMENT = 5;
    const CREDIT_AGENCY_PAYMENT = 6;

    const ORDINARY_PAYMENT_TXT = 'ordinary_payment';
    const PURCHASE_PAYMENT_TXT = 'purchase_payment';
    const AGENCY_PAYMENT_TXT = 'agency_payment';
    const CREDIT_ORDINARY_PAYMENT_TXT = 'credit_ordinary_payment';
    const CREDIT_PURCHASE_PAYMENT_TXT = 'credit_purchase_payment';
    const CREDIT_AGENCY_PAYMENT_TXT = 'credit_agency_payment';

    public static function getModuleTypeMap($datatype)
    {
        $labels = [
            self::ORDINARY_PAYMENT_TXT        => self::ORDINARY_PAYMENT,
            self::PURCHASE_PAYMENT_TXT        => self::PURCHASE_PAYMENT,
            self::AGENCY_PAYMENT_TXT          => self::AGENCY_PAYMENT,
            self::CREDIT_ORDINARY_PAYMENT_TXT => self::ORDINARY_PAYMENT,
            self::CREDIT_PURCHASE_PAYMENT_TXT => self::PURCHASE_PAYMENT,
            self::CREDIT_AGENCY_PAYMENT_TXT   => self::AGENCY_PAYMENT,
        ];
        return $labels[$datatype] ?? '';
    }

    public static function getInvoiceTypeMap($datatype)
    {
        $labels = [
            self::ORDINARY_PAYMENT_TXT        => self::INVOICE_TYPE_NORMAL,
            self::PURCHASE_PAYMENT_TXT        => self::INVOICE_TYPE_NORMAL,
            self::AGENCY_PAYMENT_TXT          => self::INVOICE_TYPE_NORMAL,
            self::CREDIT_ORDINARY_PAYMENT_TXT => self::INVOICE_TYPE_CREDIT,
            self::CREDIT_PURCHASE_PAYMENT_TXT => self::INVOICE_TYPE_CREDIT,
            self::CREDIT_AGENCY_PAYMENT_TXT   => self::INVOICE_TYPE_CREDIT,
        ];
        return $labels[$datatype] ?? '';
    }

    //州编码
    const STATE_CODE_NOT_APPLICABLE = '17';

    //计量单位
    const MEASUREMENT_TYPE_UNIT = 'XUN';                    //含义“unit”

    //分类代码
    const CLASSIFICATION_CODE_CONSOLIDATED_E_INVOICE = '004';    //合并电子发票(Consolidated e-Invoice)
    const CLASSIFICATION_CODE_IMPORTATION_GOODS = '034';    //自开票 - 进口货物(Self-billed - Importation of goods)
    const CLASSIFICATION_CODE_IMPORTATION_SERVICES = '035'; //自开票 - 进口服务(Self-billed - Importation of services)
    const CLASSIFICATION_CODE_OTHERS = '036'; //自付费用 - 其他(Self-billed - Others)

    //税种
    const TAX_TYPE_SERVICE = '02';                          //服务税
    const TAX_TYPE_NOT_APPLICABLE = '06';                   //不适用（Not Applicable）

    //补税税率Tax Rate
    const TAX_RATE_FIXED_RATE = 8;                          //百分比（0 ～ 100之间的整数）
    const TAX_RATE_DEFAULT = 0;                             //百分比（0 ～ 100之间的整数）

    // 基础API URL
    const SANDBOX_BASE_URL = 'https://preprod-api.myinvois.hasil.gov.my';
    const PROD_BASE_URL = 'https://api.myinvois.hasil.gov.my';

    // 服务特定API端点
    const API_IDENTITY_PATH = '/connect/token';
    const API_DOCUMENT_PATH = '/api/v1.0/documents';
    const API_DOCUMENT_TYPE_PATH = '/api/v1.0/documenttypes';
    const API_DOCUMENT_SUBMISSION_PATH = '/api/v1.0/documentsubmissions';
    const API_NOTIFICATION_PATH = '/api/v1.0/notifications';
    const API_TAXPAYER_PATH = '/api/v1.0/taxpayer';
    const API_TAXPAYERS_PATH = '/api/v1.0/taxpayers';

    // 正向
    const INVOICE_TYPE_NORMAL = 1;

    // 贷向
    const INVOICE_TYPE_CREDIT = 2;

    // 电子发票提交状态
    const INVOICE_SUBMISSION_STATUS_PENDING = 1;
    const INVOICE_SUBMISSION_STATUS_VALID = 2;
    const INVOICE_SUBMISSION_STATUS_INVALID = 3;
    const INVOICE_SUBMISSION_STATUS_CANCELED = 4;
    const INVOICE_SUBMISSION_HOLD = 5;

    // my 官方 api 返回的状态
    const INVOICE_SUBMISSION_STATUS_PENDING_TXT = 'Submitted';
    const INVOICE_SUBMISSION_STATUS_VALID_TXT = 'Valid';
    const INVOICE_SUBMISSION_STATUS_INVALID_TXT = 'Invalid';
    const INVOICE_SUBMISSION_STATUS_CANCELED_TXT = 'Cancelled';

    // my 官方状态 转换为系统电子发票提交状态
    public static $mapApiStatusToSystemStatus = [
        self::INVOICE_SUBMISSION_STATUS_PENDING_TXT  => self::INVOICE_SUBMISSION_STATUS_PENDING,     // 仍在处理中
        self::INVOICE_SUBMISSION_STATUS_VALID_TXT    => self::INVOICE_SUBMISSION_STATUS_VALID,       // 成功
        self::INVOICE_SUBMISSION_STATUS_INVALID_TXT  => self::INVOICE_SUBMISSION_STATUS_INVALID,     // 失败
        self::INVOICE_SUBMISSION_STATUS_CANCELED_TXT => self::INVOICE_SUBMISSION_STATUS_CANCELED,    // 取消
    ];



    // 供应商 TIN 编码
    const INVOICE_SUPPLIER_TIN_ABROAD = 'EI00000000030';
    const INVOICE_SUPPLIER_TIN_DOMESTIC = 'EI00000000010';

    /**
     * 获取特定服务的沙箱API URL
     *
     * @param string $servicePath
     * @return string
     */
    public static function getSandboxUrl(string $servicePath): string
    {
        return self::SANDBOX_BASE_URL . $servicePath;
    }

    /**
     * 获取特定服务的生产环境API URL
     *
     * @param string $servicePath
     * @return string
     */
    public static function getProdUrl(string $servicePath): string
    {
        return self::PROD_BASE_URL . $servicePath;
    }
}