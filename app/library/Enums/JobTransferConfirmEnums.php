<?php

namespace App\Library\Enums;

final class JobTransferConfirmEnums
{
    public const CONFIRM_STATE_INVALID = 0;        //已失效（含历史数据+撤销数据）
    public const CONFIRM_STATE_PENDING_CONFIRM = 1;//待确认
    public const CONFIRM_STATE_CONFIRM_PASS = 2;   //确认通过
    public const CONFIRM_STATE_CONFIRM_REJECT = 3; //确认驳回
    public const CONFIRM_STATE_CONFIRM_CANCEL = 4; //确认撤销
    public const CONFIRM_STATE_OVER_TIME = 5;      //确认超时
}