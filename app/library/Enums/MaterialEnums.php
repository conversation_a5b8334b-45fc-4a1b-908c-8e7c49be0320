<?php
namespace App\Library\Enums;
final class MaterialEnums
{
    //分页
    const PAGE_NUM = 1;//当前页码
    const PAGE_SIZE = 20;//每页数量

    //采购购入的方式-- 对应material_assets表的source_type
    const SOURCE_TYPE_PURCHASE = 0;//采购入库通知单
    const SOURCE_TYPE_ADD = 1;//手工添加

    //资产来源 -- 对应material_assets表的source_code
    const SOURCE_PURCHASE = 1;//采购购入
    const SOURCE_INVENTORY_PROFIT = 2;//盘盈
    const SOURCE_SELF_BUY = 3;//自采
    //资产来源验证串
    const SOURCE_CODE_VALIDATE = self::SOURCE_PURCHASE.','.self::SOURCE_INVENTORY_PROFIT.','.self::SOURCE_SELF_BUY;

    //资产状态 -- 对应material_assets表的status
    const ASSET_STATUS_DRAFT = 1;//草稿
    const ASSET_STATUS_UNUSED = 2;//闲置
    const ASSET_STATUS_USING = 3;//使用中
    const ASSET_STATUS_ALLOT = 4;//调拨中
    const ASSET_STATUS_REPAIRED = 5;//已报修
    const ASSET_STATUS_REPAIRING= 6;//维修中
    const ASSET_STATUS_TO_BE_SCRAPPED = 7;//待报废
    const ASSET_STATUS_HANDLED = 8;//已处置
    const ASSET_STATUS_SCRAPPED= 9;//已报废
    const ASSET_STATUS_LOST = 10;//已丢失
    const ASSET_STATUS_OUT_STORAGE = 11;//出库中
    const ASSET_STATUS_OUT_STORAGE_REJECT = 12;//出库拒绝
    const ASSET_STATUS_RETURN_ING = 13;//退回中
    const ASSET_STATUS_IN_STORAGE = 14;//入库中
    const ASSET_STATUS_UNUSED_IN_STORE = 15;//闲置（在网点）

    // 资产状态验证串
    const ASSET_STATUS_VALIDATE = self::ASSET_STATUS_UNUSED . ',' . self::ASSET_STATUS_USING . ',' . self::ASSET_STATUS_ALLOT . ',' . self::ASSET_STATUS_REPAIRED . ',' . self::ASSET_STATUS_REPAIRING . ',' .
    self::ASSET_STATUS_TO_BE_SCRAPPED . ',' . self::ASSET_STATUS_HANDLED . ',' . self::ASSET_STATUS_SCRAPPED . ',' . self::ASSET_STATUS_LOST . ',' . self::ASSET_STATUS_OUT_STORAGE . ',' . self::ASSET_STATUS_OUT_STORAGE_REJECT . ','. self::ASSET_STATUS_RETURN_ING . ',' .self::ASSET_STATUS_IN_STORAGE . ',' .self::ASSET_STATUS_UNUSED_IN_STORE;
    // 我的资产-列表-状态验证串
    const MY_ASSET_STATUS_VALIDATE = self::ASSET_STATUS_USING.',' .self::ASSET_STATUS_ALLOT.','.self::ASSET_STATUS_REPAIRED.',' .self::ASSET_STATUS_REPAIRING.','.self::ASSET_STATUS_TO_BE_SCRAPPED.','.self::ASSET_STATUS_LOST.','.self::ASSET_STATUS_RETURN_ING.','.self::ASSET_STATUS_UNUSED_IN_STORE;


    //资产使用权 -- 对应material_assets表的ownership
    const OWNERSHIP_SELF = 1;//自有
    const OWNERSHIP_FINANCE_LEASE = 2;//融资租赁
    const OWNERSHIP_GENERAL_LEASE = 3;//普通租赁
    //使用权验证串
    const OWNERSHIP_VALIDATE = self::OWNERSHIP_SELF.','.self::OWNERSHIP_FINANCE_LEASE.','.self::OWNERSHIP_GENERAL_LEASE;

    //资产使用方向 -- 对应material_assets表的use
    const USE_PERSONAL = 1;//个人使用
    const USE_PUBLIC = 2;//公共使用
    //使用方向验证串
    const USE_VALIDATE = self::USE_PERSONAL.','.self::USE_PUBLIC;

    //正品/残品 -- 对应material_assets表的quality_status
    const QUALITY_STATUS_GOOD = 1;//正品
    const QUALITY_STATUS_BAD = 2;//残品
    //正品/残品验证串
    const QUALITY_STATUS_VALIDATE = self::QUALITY_STATUS_GOOD . ',' . self::QUALITY_STATUS_BAD;
    //与SCM侧对应关系
    public static $scm_quality_status = [
        self::QUALITY_STATUS_GOOD => 'normal',
        self::QUALITY_STATUS_BAD => 'bad'
    ];

    //资产设置-用途 -- 对应material_assets表的use
    const ASSET_SET_USE_LIST = 1;//资产台账列表
    const ASSET_SET_USE_EXPORT = 2;//资产台账导出
    const ASSET_TRANSFER_SET_USE_LIST = 3;//资产转移-业务数据查询列表

    //资产台账列表-默认显示字段 -- 对应material_assets表的字段
    const LIST_DEFAULT_SHOW_COLUMNS = 'status,name,asset_code,model,category_name,finance_category_name,node_department_name,store_name,staff_id,state,sn_code,bar_code';
    //资产台账导出-默认导出字段 -- 对应material_assets表的字段
    const EXPORT_DEFAULT_SHOW_COLUMNS = "";
    //资产转移-资产部数据查询列表-默认显示字段
    const ASSET_TRANSFER_LIST_DEFAULT_SHOW_COLUMNS = 'name,asset_code,sn_code,old_asset_code,model,use_text,status_text,transfer_type_text,from_staff_name,from_node_department_name,from_store_name,from_company_name,from_pc_code,transfer_at,transfer_operator_name,to_staff_name,to_node_department_name,to_store_name,to_company_name,to_pc_code,express_no,operator_name,transfer_remark,operator_remark,finished_at';

    //采购价（不含税）、净值请输入0-999999999999.99间的数字保留2位小数
    const PRICE_RULE = '/^[1-9]\\d{0,11}(\\.\\d{1,2})?$|^0(\\.\\d{1,2})?$/';
    //物料添加图片和附件类型-从MaterialClassifyEnums复制过来的，放到一起好维护
    const OSS_MATERIAL_TYPE_BAK = 1;
    //上传图片类型 资产盘点（员工盘点资产记录表关联图片）
    const OSS_BUCKET_TYPE_INVENTORY_CHECK = 2;
    //资产附件信息类型-资产台账
    const OSS_MATERIAL_TYPE_ASSET = 3;
    //资产申请-附件
    const OSS_MATERIAL_TYPE_ASSET_APPLY = 4;
    //资产附件信息类型-离职资产详情
    const OSS_MATERIAL_TYPE_LEAVE_ASSET_DETAIL = 5;
    //资产附件信息类型-离职资产
    const OSS_MATERIAL_TYPE_LEAVE_ASSET = 6;
    //资产附件信息类型-退回资产-申请退回附件
    const OSS_MATERIAL_TYPE_RETURN_APPLY = 7;
    //资产附件信息类型-退回资产-处理附件
    const OSS_MATERIAL_TYPE_RETURN_HANDLE = 8;
    //耗材调拨单-创建
    const OSS_MATERIAL_TYPE_PACKAGE_ALLOT = 9;
    //耗材申请附件-从Enums中复制过来的，放到一起好维护
    const OSS_MATERIAL_TYPE_WMS_APPLY = 54;

    //操作类型 -- 对应material_asset_update_log表的type
    const OPERATE_TYPE_ADD = 1;//新增
    const OPERATE_TYPE_UPDATE = 2;//修改
    const OPERATE_TYPE_BATCH_ADD = 3;//批量导入新增
    const OPERATE_TYPE_BATCH_UPDATE = 4;//批量导入修改
    const OPERATE_TYPE_TRANSFER = 5;//转移
    const OPERATE_TYPE_BATCH_TRANSFER = 6;//批量转移
    const OPERATE_TYPE_OUT = 7;//领用出库

    const OPERATE_TYPE_USER_TRANSFER = 8;//员工互转
    const OPERATE_TYPE_CANCEL_TRANSFER = 9;//撤销转移
    const OPERATE_TYPE_REJECT_TRANSFER = 10;//拒绝转移
    const OPERATE_TYPE_RECEPTION_TRANSFER = 11;//接收转移
    const OPERATE_TYPE_BATCH_IMPORT_TRANSFER = 12;//批量导入转移
    const OPERATE_TYPE_LEAVE_ASSET = 13;//离职资产主管接收
    const OPERATE_TYPE_BATCH_STOCK_RETURN = 14;//批量退库
    const OPERATE_TYPE_SCM_CALLBACK_SN = 15; //scm回传sn码

    const OPERATE_TYPE_BATCH_CANCEL = 16; //批量报废
    const OPERATE_TYPE_IMPORT_FINANCE = 17; //导入修改财务
    const OPERATE_TYPE_LEAVE_PERSONAL_AGENT = 18;//正式工转个人代理
    const OPERATE_TYPE_APPLY_RETURN = 19;//申请退回
    const OPERATE_TYPE_APPLY_RETURN_CANCEL = 20;//撤销退回
    const OPERATE_TYPE_APPLY_RETURN_REJECT = 21;//批量拒绝退回申请
    const OPERATE_TYPE_APPLY_RETURN_FINISH = 22;//退回资产完成工单
    const OPERATE_TYPE_APPLY_RETURN_STORAGE_CREATE = 23;//退回入库创建
    const OPERATE_TYPE_APPLY_RETURN_STORAGE_DONE_NOT_SCM = 24;//退回入库完成（非SCM）
    const OPERATE_TYPE_APPLY_RETURN_STORAGE_DONE_SCM = 25;//退回入库完成（SCM）
    const OPERATE_TYPE_APPLY_RETURN_STORAGE_CANCEL = 26;//退回入库撤回

    //转移类型 -- 对应material_asset_transfer_batch表type字段
    const TRANSFER_TYPE_ONE = 1;//单个转移
    const TRANSFER_TYPE_BATCH = 2;//批量转移

    //转移头状态 -- 对应material_asset_transfer_batch表status字段
    const TRANSFER_BATCH_STATUS_UNRECEIVED = 1;//待接收
    const TRANSFER_BATCH_STATUS_RECEIVED = 2;//已接收
    const TRANSFER_BATCH_STATUS_DEPART_RECEIVED = 3;//部分接收
    const TRANSFER_BATCH_STATUS_REJECTED = 4;//已拒绝

    //转移行状态 -- 对应material_asset_transfer_log表status字段
    const TRANSFER_LOG_STATUS_UNRECEIVED = 1;//待接收
    const TRANSFER_LOG_STATUS_RECEIVED = 2;//已接收
    const TRANSFER_LOG_STATUS_REJECTED = 3;//已拒绝
    const TRANSFER_LOG_STATUS_CANCEL = 4;//已取消
    //我转出列表 状态字段验证字符串
    const MY_ASSET_FROM_ME_STATUS_VALIDATE = self::TRANSFER_LOG_STATUS_UNRECEIVED . ',' . self::TRANSFER_LOG_STATUS_RECEIVED . ',' . self::TRANSFER_LOG_STATUS_REJECTED . ',' . self::TRANSFER_LOG_STATUS_CANCEL;
    //转移数据-状态1:待接收,2:已接收,3:已拒绝,4:已取消
    public static $transfer_log_status = [
        self::TRANSFER_LOG_STATUS_UNRECEIVED => 'material_transfer_status.unreceived',
        self::TRANSFER_LOG_STATUS_RECEIVED => 'material_transfer_status.received',
        self::TRANSFER_LOG_STATUS_REJECTED => 'material_transfer_status.rejected',
        self::TRANSFER_LOG_STATUS_CANCEL => 'material_transfer_status.cancel',
    ];
    //转移来源
    const TRANSFER_TYPE_ASSET_DEPARTMENT = 1;//资产部转移
    const TRANSFER_TYPE_USER = 2;//员工互转
    const TRANSFER_TYPE_OUT_STORAGE = 3;//出库转移
    const TRANSFER_TYPE_USER_SYNC = 4;//使用人信息自动刷新
    const TRANSFER_TYPE_BATCH_STOCK_RETURN = 5;//批量退库
    const TRANSFER_TYPE_LEAVE_ASSET_AUTO = 6; //离职资产转移
    const TRANSFER_TYPE_LEAVE_ASSET_AUTO_PERSONAL_AGENT = 7;//正式工转个人代理
    const TRANSFER_TYPE_RETURN = 8;//退回入库

    public static $transfer_type = [
        self::TRANSFER_TYPE_ASSET_DEPARTMENT => 'material_transfer_type.department',
        self::TRANSFER_TYPE_USER => 'material_transfer_type.user',
        self::TRANSFER_TYPE_OUT_STORAGE => 'material_transfer_type.storage',
        self::TRANSFER_TYPE_USER_SYNC => 'material_transfer_type.user_sync',
        self::TRANSFER_TYPE_BATCH_STOCK_RETURN => 'material_transfer_type.stock_return',
        self::TRANSFER_TYPE_LEAVE_ASSET_AUTO => 'material_transfer_type.leave',
        self::TRANSFER_TYPE_LEAVE_ASSET_AUTO_PERSONAL_AGENT => 'material_transfer_type.leave_personal_agent',
        self::TRANSFER_TYPE_RETURN => 'material_transfer_type.return',
    ];
    const TRANSFER_TYPE_VALIDATE = self::TRANSFER_TYPE_USER . ',' . self::TRANSFER_TYPE_OUT_STORAGE;

    //转移导出pdf类型
    const TRANSFER_EXPORT_PDF_TYPE_APPLY = 1;//资产申请
    const TRANSFER_EXPORT_PDF_TYPE_AGREE = 2;//资产管理同意书
    //资产批量转移锁前缀
    const MATERIAL_ASSET_BATCH_TRANSFER_LOCK = 'material_asset_batch_transfer_lock';
    //资产批量撤销锁前缀
    const MATERIAL_ASSET_BATCH_CANCEL_LOCK = 'material_asset_batch_cancel_lock';
    //资产批量提醒锁前缀
    const MATERIAL_ASSET_BATCH_REMIND_LOCK = 'material_asset_batch_remind_lock';
    //资产批量拒绝锁前缀
    const MATERIAL_ASSET_BATCH_REJECT_LOCK = 'material_asset_batch_reject_lock';
    //资产批量接收锁前缀
    const MATERIAL_ASSET_BATCH_RECEPTION_LOCK = 'material_asset_batch_reception_lock';
    //资产转移回调
    const MATERIAL_DEAL_SCM_LOCK = 'material_deal_scm_lock';
    //转交方式
    const TRANSFER_METHOD_FACE = 1; //当面转交
    const TRANSFER_METHOD_POST = 2; //邮寄转交
    const TRANSFER_METHOD_VALIDATE = self::TRANSFER_METHOD_FACE . ',' . self::TRANSFER_METHOD_POST;
    public static $transfer_method = [
        self::TRANSFER_METHOD_FACE => 'asset_transfer_method.face',
        self::TRANSFER_METHOD_POST => 'asset_transfer_method.post',
    ];
    //全部转移记录-列表类型
    const ALL_TRANSFER_TYPE_ALL = 3;//全部
    const ALL_TRANSFER_TYPE_FROM_ME = 1;//我转出
    const ALL_TRANSFER_TYPE_TO_ME = 2;//转给我
    //使用方向验证串
    const ALL_TRANSFER_TYPE_VALIDATE = self::ALL_TRANSFER_TYPE_ALL . ',' . self::ALL_TRANSFER_TYPE_FROM_ME . ',' . self::ALL_TRANSFER_TYPE_TO_ME;
    public static $all_transfer_list_type = [
        //self::ALL_TRANSFER_TYPE_ALL => 'all_transfer_list_type.all',
        self::ALL_TRANSFER_TYPE_FROM_ME => 'all_transfer_list_type.from_me',
        self::ALL_TRANSFER_TYPE_TO_ME => 'all_transfer_list_type.to_me',
    ];

    //资产台账导出时间锁
    const MATERIAL_ASSET_EXPORT_LOCK = 'material_asset_export_lock';
    //资产台账批量导入新增时间锁
    const MATERIAL_ASSET_ADD_LOCK = 'material_asset_add_lock';
    //资产台账批量导入更新时间锁
    const MATERIAL_ASSET_UPDATE_LOCK = 'material_asset_update_lock';
    //资产台账批量导入转移时间锁
    const MATERIAL_ASSET_BATCH_IMPORT_TRANSFER_LOCK = 'material_asset_batch_import_transfer_lock';
    //资产台账批量转移时间锁
    const MATERIAL_ASSET_TRANSFER_LOCK = 'material_asset_transfer_lock';
    //资产台账批量退库时间锁
    const MATERIAL_ASSET_STOCK_RETURN_LOCK = 'material_asset_batch_stock_return_lock';

    //资产台账批量导出操作条数限制
    const MATERIAL_ASSET_EXPORT_LIMIT = 100000;
    //资产台账批量添加操作条数限制
    const MATERIAL_ASSET_BATCH_LIMIT = 12000;
    //资产台账批量更新操作条数限制
    const MATERIAL_ASSET_BATCH_UPDATE_LIMIT = 2000;
    //资产台账批量导入转移条数限制
    const MATERIAL_ASSET_BATCH_IMPORT_TRANSFER_LIMIT = 500;
    //批量导入数量正则（1-999）
    const MATERIAL_ASSET_BATCH_ADD_QUANTITY = '/(^[1-9]\d{0,2}$)/';
    //批量导入日期格式正则
    const MATERIAL_ASSET_BATCH_ADD_DATE = '/^\d{4}-\d{2}-\d{2}$/';
    //我的资产导出锁前缀
    const MY_MATERIAL_ASSET_EXPORT_LOCK = 'my_material_asset_export_lock';
    //我的资产导出最大数量限制
    const MY_MATERIAL_ASSET_EXPORT_LIMIT = 30000;
    //资产业务数据查询导出锁前缀
    const MATERIAL_ASSET_TRANSFER_SEARCH_EXPORT_LOCK = 'material_asset_search_export_lock';
    //资产转移-数据查询-导出操作条数限制
    const MATERIAL_ASSET_SEARCH_EXPORT_LIMIT = 100000;

    //资产列表-可设置的显示字段 -- 对应material_assets表的字段
    public static $list_set_columns = [
        'status',//使用状态
        'asset_code',//资产编码
        'name',//资产名称
        'model',//规格型号
        'category_name',//物料分类
        'finance_category_name',//财务分类
        'purchase_price',//采购价
        'node_department_name',//使用部门
        'store_name',//使用网点
        'staff_id',//使用人工号
        'job_name',//职位
        'state',//在职状态
        'sn_code',//SN码
        'bar_code',//barcode
        'use',//使用方向
        'old_asset_code',//旧资产编码默认
        'use_land',//使用地信息
        'real_arrival_date',//入库日期
        'leave_date',//离职日期,
        'company_name',//所属公司
        'hire_type',//雇佣类型
        'contract_company_text',//使用人的协议签署公司
    ];
    //资产转移-资产部数据查询列表-可设置的显示字段
    public static $asset_transfer_list_set_columns = [
        'name',//资产名称
        'asset_code',//资产编码
        'sn_code',//SN码
        'old_asset_code',//旧资产编码
        'model',//规格型号
        'use_text',//使用方向
        'status_text',//转移状态
        'transfer_type_text',//类型 资产部转移,员工互转,等
        'from_staff_name',//调出人
        'from_node_department_name',//调出部门
        'from_store_name',//调出网点
        'from_company_name',//调出公司
        'from_contract_company_name',//调出人协议签署公司
        'from_pc_code',//调出成本中心
        'transfer_at',//申请时间
        'transfer_operator_name',//调出操作人
        'to_staff_name',//调入人
        'to_node_department_name',//调入部门
        'to_store_name',//调入网点
        'to_company_name',//调入公司
        'to_contract_company_name',//调入人协议签署公司
        'to_pc_code',//调入成本中心,
        'express_no',//快递单号
        'operator_name',//操作人
        'transfer_remark',//转移原因
        'operator_remark',//操作备注
        'finished_at',//完成日期
    ];
    //资产台账-使用状态1:草稿,2:闲置,3:使用中,4:调拨中,5:已报修,6:维修中,7:待报废,8:已处置,9:已报废,10:已丢失,11:出库中,12:出库拒绝,13退回中,14入库中,15闲置（在网点）
    public static $asset_status = [
        self::ASSET_STATUS_UNUSED => 'material_asset_status.'.self::ASSET_STATUS_UNUSED,
        self::ASSET_STATUS_USING => 'material_asset_status.'.self::ASSET_STATUS_USING,
        self::ASSET_STATUS_ALLOT => 'material_asset_status.'.self::ASSET_STATUS_ALLOT,
        self::ASSET_STATUS_REPAIRED => 'material_asset_status.'.self::ASSET_STATUS_REPAIRED,
        self::ASSET_STATUS_REPAIRING => 'material_asset_status.'.self::ASSET_STATUS_REPAIRING,
        self::ASSET_STATUS_TO_BE_SCRAPPED => 'material_asset_status.'.self::ASSET_STATUS_TO_BE_SCRAPPED,
        self::ASSET_STATUS_HANDLED => 'material_asset_status.'.self::ASSET_STATUS_HANDLED,
        self::ASSET_STATUS_SCRAPPED => 'material_asset_status.'.self::ASSET_STATUS_SCRAPPED,
        self::ASSET_STATUS_LOST => 'material_asset_status.'.self::ASSET_STATUS_LOST,
        self::ASSET_STATUS_OUT_STORAGE => 'material_asset_status.'.self::ASSET_STATUS_OUT_STORAGE,
        self::ASSET_STATUS_OUT_STORAGE_REJECT => 'material_asset_status.'.self::ASSET_STATUS_OUT_STORAGE_REJECT,
        self::ASSET_STATUS_RETURN_ING => 'material_asset_status.'.self::ASSET_STATUS_RETURN_ING,
        self::ASSET_STATUS_IN_STORAGE => 'material_asset_status.'.self::ASSET_STATUS_IN_STORAGE,
        self::ASSET_STATUS_UNUSED_IN_STORE => 'material_asset_status.' . self::ASSET_STATUS_UNUSED_IN_STORE
    ];
    //资产台账-来源方式1:来源采购购入,2:盘盈,3:自采
    public static $asset_source = [
        self::SOURCE_PURCHASE => 'material_asset_source.'.self::SOURCE_PURCHASE,
        self::SOURCE_INVENTORY_PROFIT => 'material_asset_source.'.self::SOURCE_INVENTORY_PROFIT,
        self::SOURCE_SELF_BUY => 'material_asset_source.'.self::SOURCE_SELF_BUY
    ];
    //资产台账-所有权1:自有,2:融资租赁,3:普通租赁
    public static $ownership = [
        self::OWNERSHIP_SELF => 'material_asset_ownership.'.self::OWNERSHIP_SELF,
        self::OWNERSHIP_FINANCE_LEASE => 'material_asset_ownership.'.self::OWNERSHIP_FINANCE_LEASE,
        self::OWNERSHIP_GENERAL_LEASE => 'material_asset_ownership.'.self::OWNERSHIP_GENERAL_LEASE
    ];
    //资产台账-使用方向1:个人使用,2:公共使用
    public static $use = [
        self::USE_PERSONAL => 'material_asset_use.'.self::USE_PERSONAL,
        self::USE_PUBLIC => 'material_asset_use.'.self::USE_PUBLIC
    ];
    //资产台账-1正品、2残品
    public static $quality_status = [
        self::QUALITY_STATUS_GOOD => 'material_asset_quality_status.' . self::QUALITY_STATUS_GOOD,
        self::QUALITY_STATUS_BAD => 'material_asset_quality_status.' . self::QUALITY_STATUS_BAD,
    ];

    //离职资产
    // 资产状态可选择验证串
    const LEAVE_ASSET_STATUS_VALIDATE = self::ASSET_STATUS_UNUSED . ',' . self::ASSET_STATUS_USING . ',' . self::ASSET_STATUS_ALLOT . ',' . self::ASSET_STATUS_REPAIRED . ',' . self::ASSET_STATUS_REPAIRING . ',' .
    self::ASSET_STATUS_TO_BE_SCRAPPED . ',' . self::ASSET_STATUS_HANDLED . ',' . self::ASSET_STATUS_SCRAPPED . ',' . self::ASSET_STATUS_LOST . ',' . self::ASSET_STATUS_OUT_STORAGE . ',' . self::ASSET_STATUS_OUT_STORAGE_REJECT . ',' . self::ASSET_STATUS_RETURN_ING . ',' . self::ASSET_STATUS_UNUSED_IN_STORE;

    //生成业务单号
    const LEAVE_ASSET_NO_PREFIX = 'RA';
    const LEAVE_ASSET_NO_REDIS_KEY = 'leave_assets_create_no';
    const LEAVE_ASSET_NO_LENGTH = 4;
    //主管处理状态
    const MANAGER_STATUS_TODO = 1; //待处理
    const MANAGER_STATUS_DEAL = 2; //已处理
    const MANAGER_STATUS_INVALID = 3; //已作废
    public static $manager_status_list = [
        self::MANAGER_STATUS_TODO => 'manager_status_list_todo',
        self::MANAGER_STATUS_DEAL => 'manager_status_list_deal',
        self::MANAGER_STATUS_INVALID => 'manager_status_list_invalid',
    ];
    //主管作废原因翻译key
    const MANAGER_STATUS_INVALID_REMARK_MANAGER_CHANGE = 'manager_status_invalid_remark_manager_change'; //主管上级变动
    const MANAGER_STATUS_INVALID_REMARK_MANAGER_LEAVE = 'manager_status_invalid_remark_manager_leave'; //主管离职
    const MANAGER_STATUS_INVALID_REMARK_CANCEL_LEAVE = 'manager_status_invalid_remark_cancel_leave'; //撤销离职
    //主管处理进度
    const MANAGER_PROGRESS_NOT = 1;//未处理
    const MANAGER_PROGRESS_DOING = 2;//处理中
    const MANAGER_PROGRESS_DONE = 3;//已处理
    const MANAGER_PROGRESS_NO_NEED = 4;//无需处理
    const MANAGER_PROGRESS_NO_NEED_LEAVE = 5;//无需处理(上级离职)
    const MANAGER_PROGRESS_NO_NEED_CHANGED = 6;//无需处理(换上级)
    const MANAGER_PROGRESS_NO_NEED_ASSET = 7;//资产部处理(资产部（工号）)
    public static $manager_progress_list = [
        self::MANAGER_PROGRESS_NOT => 'manager_progress_not',
        self::MANAGER_PROGRESS_DOING => 'manager_progress_doing',
        self::MANAGER_PROGRESS_DONE => 'manager_progress_done',
        self::MANAGER_PROGRESS_NO_NEED => 'manager_progress_no_need',
        self::MANAGER_PROGRESS_NO_NEED_LEAVE => 'manager_progress_no_need_leave',
        self::MANAGER_PROGRESS_NO_NEED_CHANGED => 'manager_progress_no_need_changed',
        self::MANAGER_PROGRESS_NO_NEED_ASSET => 'manager_progress_no_need_asset'
    ];
    const MANAGER_PROGRESS_VALIDATE = self::MANAGER_PROGRESS_NOT . ',' . self::MANAGER_PROGRESS_DOING . ',' . self::MANAGER_PROGRESS_DONE . ',' .
    self::MANAGER_PROGRESS_NO_NEED . ',' . self::MANAGER_PROGRESS_NO_NEED_LEAVE . ',' . self::MANAGER_PROGRESS_NO_NEED_CHANGED . ',' . self::MANAGER_PROGRESS_NO_NEED_ASSET;
    //资产部处理状态
    const ASSET_DEPARTMENT_STATUS_TODO = 1;//待处理
    const ASSET_DEPARTMENT_STATUS_DOING = 2;//处理中
    const ASSET_DEPARTMENT_STATUS_DONE = 3;//已处理
    public static $asset_department_status_list = [
        self::ASSET_DEPARTMENT_STATUS_TODO => 'asset_department_status_todo',
        self::ASSET_DEPARTMENT_STATUS_DOING => 'asset_department_status_doing',
        self::ASSET_DEPARTMENT_STATUS_DONE => 'asset_department_status_done',
    ];
    const ASSET_DEPARTMENT_STATUS_VALIDATE = self::ASSET_DEPARTMENT_STATUS_TODO . ',' . self::ASSET_DEPARTMENT_STATUS_DOING . ',' . self::ASSET_DEPARTMENT_STATUS_DONE;
    //资产部处理进度(单据进度)
    const ASSET_DEPARTMENT_PROGRESS_NOT = 1;//未处理
    const ASSET_DEPARTMENT_PROGRESS_DOING = 2;//资产处理中
    const ASSET_DEPARTMENT_PROGRESS_DEDUCT = 3;//待扣款
    const ASSET_DEPARTMENT_PROGRESS_DONE = 4;//已完成
    public static $asset_department_progress_list = [
        self::ASSET_DEPARTMENT_PROGRESS_NOT => 'asset_department_progress_not',
        self::ASSET_DEPARTMENT_PROGRESS_DOING => 'asset_department_progress_doing',
        self::ASSET_DEPARTMENT_PROGRESS_DEDUCT => 'asset_department_progress_deduct',
        self::ASSET_DEPARTMENT_PROGRESS_DONE => 'asset_department_progress_done',
    ];
    const ASSET_DEPARTMENT_PROGRESS_VALIDATE = self::ASSET_DEPARTMENT_PROGRESS_NOT . ',' . self::ASSET_DEPARTMENT_PROGRESS_DOING . ',' . self::ASSET_DEPARTMENT_PROGRESS_DEDUCT . ',' . self::ASSET_DEPARTMENT_PROGRESS_DONE;
    //资产部处理情况(资产部处理详情时选择的)
    const ASSET_HANDLING_STATUS_NO_RETURN = 1; //未还
    const ASSET_HANDLING_STATUS_GOOD_RETURN = 2; //已还完好
    const ASSET_HANDLING_STATUS_BAD_RETURN = 3; //已还受损坏
    const ASSET_HANDLING_STATUS_LACK_RETURN = 4; //归还缺少零件
    const ASSET_HANDLING_STATUS_BELONG_OTHER = 5; //在他人名下
    const ASSET_HANDLING_STATUS_LOW_VALUE_NON_RETURN = 6;//低值易耗不强制归还
    public static $asset_handling_status_list = [
        self::ASSET_HANDLING_STATUS_NO_RETURN => 'asset_handling_status_not_return',
        self::ASSET_HANDLING_STATUS_GOOD_RETURN => 'asset_handling_status_good_return',
        self::ASSET_HANDLING_STATUS_BAD_RETURN => 'asset_handling_status_bad_return',
        self::ASSET_HANDLING_STATUS_LACK_RETURN => 'asset_handling_status_lack_return',
        self::ASSET_HANDLING_STATUS_BELONG_OTHER => 'asset_handling_status_belong_other',
        self::ASSET_HANDLING_STATUS_LOW_VALUE_NON_RETURN => 'asset_handling_status_low_value_non_return'
    ];
    const ASSET_HANDLING_STATUS_VALIDATE = self::ASSET_HANDLING_STATUS_NO_RETURN . ',' . self::ASSET_HANDLING_STATUS_GOOD_RETURN . ',' . self::ASSET_HANDLING_STATUS_BAD_RETURN . ',' . self::ASSET_HANDLING_STATUS_LACK_RETURN . ',' . self::ASSET_HANDLING_STATUS_BELONG_OTHER . ',' . self::ASSET_HANDLING_STATUS_LOW_VALUE_NON_RETURN;

    //资产状况-公共资产
    const LEAVE_ASSET_PUBLIC_STATE_INTACT = 1;//完好
    const LEAVE_ASSET_PUBLIC_STATE_LOST = 2;//丢失
    const LEAVE_ASSET_PUBLIC_STATE_DAMAGE = 3;//已还受损坏
    public static $leave_asset_public_state_list = [
        self::LEAVE_ASSET_PUBLIC_STATE_INTACT => 'leave_asset_personal_state_intact',
        self::LEAVE_ASSET_PUBLIC_STATE_LOST => 'leave_asset_personal_state_lost',
        self::LEAVE_ASSET_PUBLIC_STATE_DAMAGE => 'leave_asset_personal_state_damage',
    ];
    //资产状况-个人资产
    const LEAVE_ASSET_PERSONAL_STATE_NORMAL = 20;//正常使用
    const LEAVE_ASSET_PERSONAL_STATE_NO_RETURN = 21;//资产未归还
    const LEAVE_ASSET_PERSONAL_STATE_ASSET_LOST = 22;//资产丢失
    const LEAVE_ASSET_PERSONAL_STATE_LOSS = 23;//资产损失无法使用
    const LEAVE_ASSET_PERSONAL_STATE_SCREEN_LOSS = 24;//屏幕损失
    const LEAVE_ASSET_PERSONAL_STATE_CAMERA_LOSS = 25;//摄像头损坏
    const LEAVE_ASSET_PERSONAL_STATE_CHARGER_LOSS = 26;//没归还资产充电器
    const LEAVE_ASSET_PERSONAL_STATE_HAVE_PASSWORD = 27;//有密码不能使用
    const LEAVE_ASSET_PERSONAL_STATE_UNIDENTIFIED = 28;//收到但无法确认资产正常使用
    public static $leave_asset_personal_state_list = [
        self::LEAVE_ASSET_PERSONAL_STATE_NORMAL => 'leave_asset_public_state_normal',
        self::LEAVE_ASSET_PERSONAL_STATE_NO_RETURN => 'leave_asset_public_state_no_return',
        self::LEAVE_ASSET_PERSONAL_STATE_ASSET_LOST => 'leave_asset_public_state_asset_lost',
        self::LEAVE_ASSET_PERSONAL_STATE_LOSS => 'leave_asset_public_state_loss',
        self::LEAVE_ASSET_PERSONAL_STATE_SCREEN_LOSS => 'leave_asset_public_state_screen_loss',
        self::LEAVE_ASSET_PERSONAL_STATE_CAMERA_LOSS => 'leave_asset_public_state_camera_loss',
        self::LEAVE_ASSET_PERSONAL_STATE_CHARGER_LOSS => 'leave_asset_public_state_charger_loss',
        self::LEAVE_ASSET_PERSONAL_STATE_HAVE_PASSWORD => 'leave_asset_public_state_have_password',
        self::LEAVE_ASSET_PERSONAL_STATE_UNIDENTIFIED => 'leave_asset_personal_state_unidentified',
    ];

    const LEAVE_ASSET_STATE_VALIDATE = self::LEAVE_ASSET_PUBLIC_STATE_INTACT . ',' . self::LEAVE_ASSET_PUBLIC_STATE_LOST . ',' . self::LEAVE_ASSET_PUBLIC_STATE_DAMAGE . ',' . self::LEAVE_ASSET_PERSONAL_STATE_NORMAL
    . ',' . self::LEAVE_ASSET_PERSONAL_STATE_NO_RETURN . ',' . self::LEAVE_ASSET_PERSONAL_STATE_ASSET_LOST . ',' . self::LEAVE_ASSET_PERSONAL_STATE_LOSS . ',' . self::LEAVE_ASSET_PERSONAL_STATE_SCREEN_LOSS . ',' . self::LEAVE_ASSET_PERSONAL_STATE_CAMERA_LOSS
    . ',' . self::LEAVE_ASSET_PERSONAL_STATE_CHARGER_LOSS . ',' . self::LEAVE_ASSET_PERSONAL_STATE_HAVE_PASSWORD . ',' . self::LEAVE_ASSET_PERSONAL_STATE_UNIDENTIFIED;
    //by端必填备注和图片的 个人资产+公共状态(损坏类的), 个人和公共的归还状态别重复 !!!
    const LEAVE_ASSET_STATE_MUST_VALIDATE = self::LEAVE_ASSET_PUBLIC_STATE_DAMAGE . ',' . self::LEAVE_ASSET_PERSONAL_STATE_LOSS . ',' . self::LEAVE_ASSET_PERSONAL_STATE_SCREEN_LOSS . ',' . self::LEAVE_ASSET_PERSONAL_STATE_CAMERA_LOSS . ',' . self::LEAVE_ASSET_PERSONAL_STATE_CHARGER_LOSS . ',' . self::LEAVE_ASSET_PERSONAL_STATE_HAVE_PASSWORD;
    //[资产状况]和[资产处理情况]的映射关系
    public static $leave_state_asset_handling_status_relation = [
        //公共资产
        self::LEAVE_ASSET_PUBLIC_STATE_INTACT => self::ASSET_HANDLING_STATUS_GOOD_RETURN, //完好=>已还完好
        self::LEAVE_ASSET_PUBLIC_STATE_LOST => self::ASSET_HANDLING_STATUS_NO_RETURN, //丢失=>未还
        self::LEAVE_ASSET_PUBLIC_STATE_DAMAGE => self::ASSET_HANDLING_STATUS_BAD_RETURN, //已还受损坏=>已还受损坏
        //个人资产
        self::LEAVE_ASSET_PERSONAL_STATE_NORMAL => self::ASSET_HANDLING_STATUS_GOOD_RETURN, //正常使用=>已还完好
        self::LEAVE_ASSET_PERSONAL_STATE_NO_RETURN => self::ASSET_HANDLING_STATUS_NO_RETURN, //资产未归还=>未还
        self::LEAVE_ASSET_PERSONAL_STATE_ASSET_LOST => self::ASSET_HANDLING_STATUS_NO_RETURN, //资产丢失=>未还
        self::LEAVE_ASSET_PERSONAL_STATE_LOSS => self::ASSET_HANDLING_STATUS_BAD_RETURN, //资产损失无法使用=>已还受损坏
        self::LEAVE_ASSET_PERSONAL_STATE_SCREEN_LOSS => self::ASSET_HANDLING_STATUS_BAD_RETURN, //屏幕损坏=>已还受损坏
        self::LEAVE_ASSET_PERSONAL_STATE_CAMERA_LOSS => self::ASSET_HANDLING_STATUS_BAD_RETURN, //摄像头损坏=>已还受损坏
        self::LEAVE_ASSET_PERSONAL_STATE_CHARGER_LOSS => self::ASSET_HANDLING_STATUS_LACK_RETURN, //没归还资产充电器=>归还缺少零件
    ];

    //主管处理-列表类型
    const LIST_TYPE_PENDING = 1;//待处理
    const LIST_TYPE_DONE = 2;//已处理

    //主管数据标识
    const MANAGER_TAG_YES = 1; //是
    const MANAGER_TAG_NO = 2; //不是

    //离职资产-数据标识
    const LEAVE_ASSET_DATA_TAG_SELF = 1;//员工自有
    const LEAVE_ASSET_DATA_TAG_MANAGER = 2;//主管添加
    const LEAVE_ASSET_DATA_TAG_ASSET_DEPARTMENT = 3;//资产部添加

    //主管编辑是否提交
    const LEAVE_ASSET_SAVE_IS_SUBMIT_YES = 1;
    const LEAVE_ASSET_SAVE_IS_SUBMIT_NO = 0;

    //主管是否完成编辑/资产部是否完成编辑
    const LEAVE_ASSET_MANAGER_FINISH_YES = 1;
    const LEAVE_ASSET_MANAGER_FINISH_NO = 0;

    //资产部处理
    //是否足额扣款
    const IS_FULL_AMOUNT_YES = 1;
    const IS_FULL_AMOUNT_NO = 2;
    public static $is_full_amount_list = [
        self::IS_FULL_AMOUNT_YES => 'is_full_amount_yes',
        self::IS_FULL_AMOUNT_NO => 'is_full_amount_no',
    ];
    const IS_FULL_AMOUNT_VALIDATE = self::IS_FULL_AMOUNT_YES . ',' . self::IS_FULL_AMOUNT_NO;
    //是否有效
    const LEAVE_ASSET_IS_VALID_YES = 1;
    const LEAVE_ASSET_IS_VALID_NO = 2;
    public static $leave_asset_is_valid_list = [
        self::LEAVE_ASSET_IS_VALID_YES => 'leave_asset_is_valid_yes',
        self::LEAVE_ASSET_IS_VALID_NO => 'leave_asset_is_valid_no',
    ];
    const LEAVE_ASSET_IS_VALID_VALIDATE = self::LEAVE_ASSET_IS_VALID_YES . ',' . self::LEAVE_ASSET_IS_VALID_NO;
    //名下资产是否为0
    const ALL_ASSET_NUMBER_IS_ZERO_YES = 1;
    const ALL_ASSET_NUMBER_IS_ZERO_NO = 2;
    public static $all_asset_number_is_zero = [
        self::ALL_ASSET_NUMBER_IS_ZERO_YES => 'all_asset_number_is_zero_yes',
        self::ALL_ASSET_NUMBER_IS_ZERO_NO => 'all_asset_number_is_zero_no',
    ];
    const ALL_ASSET_NUMBER_IS_ZERO_VALIDATE = self::ALL_ASSET_NUMBER_IS_ZERO_YES . ',' . self::ALL_ASSET_NUMBER_IS_ZERO_NO;

    //离职资产操作记录类型leave_assets_operation_record
    const LEAVE_ASSETS_RECORD_TYPE_ADD = 1; //新增
    const LEAVE_ASSETS_RECORD_TYPE_EDIT = 2; //编辑
    const LEAVE_ASSETS_RECORD_TYPE_UPDATE = 3; //更新
    const LEAVE_ASSETS_RECORD_TYPE_CANCEL = 4; //撤销离职
    public static $leave_assets_record_type_list = [
        self::LEAVE_ASSETS_RECORD_TYPE_ADD => 'leave_asset_record_add',
        self::LEAVE_ASSETS_RECORD_TYPE_EDIT => 'leave_asset_record_edit',
        self::LEAVE_ASSETS_RECORD_TYPE_UPDATE => 'leave_asset_record_update',
        self::LEAVE_ASSETS_RECORD_TYPE_CANCEL => 'leave_asset_record_cancel',
    ];
    const LEAVE_ASSETS_RECORD_TYPE_VALIDATE = self::LEAVE_ASSETS_RECORD_TYPE_ADD . ',' . self::LEAVE_ASSETS_RECORD_TYPE_EDIT . ',' . self::LEAVE_ASSETS_RECORD_TYPE_UPDATE . ',' . self::LEAVE_ASSETS_RECORD_TYPE_CANCEL;
    //操作记录子类型
    const LEAVE_ASSETS_RECORD_SUB_TYPE_ADD = 1; //添加
    const LEAVE_ASSETS_RECORD_SUB_TYPE_EDIT = 2; //编辑
    const LEAVE_ASSETS_RECORD_SUB_TYPE_DELETE = 3; //删除
    public static $leave_assets_record_sub_type_list = [
        self::LEAVE_ASSETS_RECORD_SUB_TYPE_ADD => 'leave_asset_record_sub_add',
        self::LEAVE_ASSETS_RECORD_SUB_TYPE_EDIT => 'leave_asset_record_sub_edit',
        self::LEAVE_ASSETS_RECORD_SUB_TYPE_DELETE => 'leave_asset_record_sub_delete',
    ];
    //操作记录翻译字段时的前缀
    const TRANSLATE_RECORD_LOG_FIELDS_PREFIX = 'record_log_';
    //操作记录需要记录的字段
    public static $record_need_view_fields = [
        //详情表字段
        'id', //id
        'barcode', //barcode
        'asset_code', //资产编码
        'sn_code', //sn码
        'model', //规格型号
        'asset_status', //使用状态
        'asset_state', //资产状况
        'purchase_price', //采购价
        'net_value', //净值
        'deduct_amount', //扣款金额
        'deduct_reason', //扣款原因
        'asset_handling_status', //资产处理情况
        //主表字段
        'main_fields_deduct_amount', //已扣金额
        'main_fields_return_amount', //已还金额
        'main_fields_loss_amount', //损失金额
        'main_fields_asset_department_status', //资产处理状态
        'main_fields_asset_department_remark', //操作备注
    ];
    //操作记录需要翻译值的字段名和枚举
    public static function recordNeedViewValue()
    {
        $leave_asset_state_list = MaterialEnums::$leave_asset_personal_state_list + MaterialEnums::$leave_asset_public_state_list;
        $record_need_view_value = [
            //详情表字段
            'asset_status' => self::$asset_status, //使用状态
            'asset_state' => $leave_asset_state_list, //资产状况
            'asset_handling_status' => self::$asset_handling_status_list, //资产处理情况
            //主表字段
            'main_fields_asset_department_status' => self::$asset_department_status_list, //资产处理状态
        ];
        return $record_need_view_value;
    }
    //操作记录撤销离职翻译
    const LEAVE_ASSETS_RECORD_CANCEL_LEAVE_KEY = 'leave_assets_record_cancel_leave';
    //导出最大数据量
    const LEAVE_ASSETS_EXPORT_MAX = 20000;
    //资产部处理-详情页/编辑页-转移列表-数据是否标红
    const LEAVE_ASSETS_TRANSFER_IS_RED_YES = 1;
    const LEAVE_ASSETS_TRANSFER_IS_RED_NO = 0;
    //导出类型
    const LEAVE_ASSETS_EXPORT_TYPE_COUNT = 1; //导出汇总表
    const LEAVE_ASSETS_EXPORT_TYPE_DETAIL = 2; //导出明细表
    const LEAVE_ASSETS_EXPORT_TYPE_TRANSFER = 3; //导出明细表(在途资产)
    //导出类型对应的异步导出类型
    public static $excel_type = [
        self::LEAVE_ASSETS_EXPORT_TYPE_COUNT => DownloadCenterEnum::MATERIAL_LEAVE_ASSETS_EXPORT,
        self::LEAVE_ASSETS_EXPORT_TYPE_DETAIL => DownloadCenterEnum::MATERIAL_LEAVE_ASSETS_DETAIL_EXPORT,
        self::LEAVE_ASSETS_EXPORT_TYPE_TRANSFER => DownloadCenterEnum::MATERIAL_LEAVE_ASSETS_TRANSFER_EXPORT
    ];
    const LEAVE_ASSETS_EXPORT_TYPE_VALIDATE = self::LEAVE_ASSETS_EXPORT_TYPE_COUNT . ',' . self::LEAVE_ASSETS_EXPORT_TYPE_DETAIL . ',' . self::LEAVE_ASSETS_EXPORT_TYPE_TRANSFER;
    //离职资产上级确认任务配置
    //规则类型1.固定工号 2. 3.网点主管 4.片区负责人 5.大区负责人 6.工作交接人
    const LEAVE_ASSET_SET_RULE_FIXED = 1; //固定工号
    const LEAVE_ASSET_SET_RULE_SUPERIOR = 2; //直线上级
    const LEAVE_ASSET_SET_RULE_STORE_MANAGER = 3; //网点主管
    const LEAVE_ASSET_SET_RULE_PIECE_MANAGER = 4; //片区负责人
    const LEAVE_ASSET_SET_RULE_REGION_MANAGER = 5; //大区负责人
    const LEAVE_ASSET_SET_RULE_HANDOVER_STAFF = 6; //工作交接人
    public static $leave_assets_set_rule_list = [
        self::LEAVE_ASSET_SET_RULE_FIXED => 'leave_asset_set_rule_fixed',
        self::LEAVE_ASSET_SET_RULE_SUPERIOR => 'leave_asset_set_rule_superior',
        self::LEAVE_ASSET_SET_RULE_STORE_MANAGER => 'leave_asset_set_rule_store_manager',
        self::LEAVE_ASSET_SET_RULE_PIECE_MANAGER => 'leave_asset_set_rule_piece_manager',
        self::LEAVE_ASSET_SET_RULE_REGION_MANAGER => 'leave_asset_set_rule_region_manager',
        self::LEAVE_ASSET_SET_RULE_HANDOVER_STAFF => 'leave_asset_set_rule_handover_staff',
    ];
    const LEAVE_ASSET_SET_RULE_VALIDATE = self::LEAVE_ASSET_SET_RULE_FIXED . ',' . self::LEAVE_ASSET_SET_RULE_SUPERIOR . ',' . self::LEAVE_ASSET_SET_RULE_STORE_MANAGER . ',' . self::LEAVE_ASSET_SET_RULE_PIECE_MANAGER . ',' . self::LEAVE_ASSET_SET_RULE_REGION_MANAGER . ',' . self::LEAVE_ASSET_SET_RULE_HANDOVER_STAFF;
    //离职资产上级任务-启用禁用
    const LEAVE_ASSET_SET_IS_ENABLE_YES = 1;
    const LEAVE_ASSET_SET_IS_ENABLE_NO = 2;
    public static $leave_asset_set_is_enable_list = [
        self::LEAVE_ASSET_SET_IS_ENABLE_YES => 'leave_asset_set_is_enable_yes',
        self::LEAVE_ASSET_SET_IS_ENABLE_NO => 'leave_asset_set_is_enable_no',
    ];
    const LEAVE_ASSET_SET_IS_ENABLE_VALIDATE = self::LEAVE_ASSET_SET_IS_ENABLE_YES . ',' . self::LEAVE_ASSET_SET_IS_ENABLE_NO;
    //离职资产短信邮件设置-类型
    const LEAVE_ASSET_SET_REMIND_TYPE_MESSAGE = 1;//短信
    const LEAVE_ASSET_SET_REMIND_TYPE_EMAIL = 2;//邮箱
    public static $leave_asset_set_remind_type_list = [
        self::LEAVE_ASSET_SET_REMIND_TYPE_MESSAGE => 'leave_asset_set_remind_type_message',
        self::LEAVE_ASSET_SET_REMIND_TYPE_EMAIL => 'leave_asset_set_remind_type_email',
    ];
    const LEAVE_ASSET_SET_REMIND_TYPE_VALIDATE = self::LEAVE_ASSET_SET_REMIND_TYPE_MESSAGE . ',' . self::LEAVE_ASSET_SET_REMIND_TYPE_EMAIL;
    //by端主管处理-主管是否处理过
    const IS_HAS_PROCESS_NO = 0;
    const IS_HAS_PROCESS_YES = 1;
    //是否新数据 v15994全是新数据(OA新资产的)
    const LEAVE_ASSET_IS_NEW_YES = 1;
    //by主管处理-列表是否加载完毕
    const LEAVE_ASSET_LIST_IS_OVER_NO = 0;
    const LEAVE_ASSET_LIST_IS_OVER_YES = 1;
    //离职资产-消息类型
    const LEAVE_ASSET_MESSAGE_CATEGORY = 90;
    const LEAVE_ASSET_AUTO_TRANSFER_PERSONAL_AGENT_CATEGORY = 122;//正式员工转个人代理资产自动转移
    const MESSAGE_CATEGORY_TRANSFER_NOTICE = 123;// 资产台账-导入转移-资产批量变更提醒
    const MATERIAL_ASSET_RETURN_MESSAGE_CATEGORY_REJECT = 133;//退回资产处理-资产退回拒收提醒
    const MATERIAL_ASSET_RETURN_MESSAGE_CATEGORY_FINISHED = 134;//退回资产处理-资产退回申请完成提醒
    const MATERIAL_PACKAGE_ALLOT_OUT_NOTICE = 166;//耗材调拨单-耗材调出提醒
    const MATERIAL_PACKAGE_ALLOT_IN_NOTICE = 167;//耗材调拨单-耗材调入提醒
    const MATERIAL_PACKAGE_ALLOT_CANCEL_NOTICE = 168;//耗材调拨单-耗材调拨取消提醒

    //资产部批量编辑-操作类型
    const BATCH_UPLOAD_OPERATION_TYPE_EDIT = 1;
    const BATCH_UPLOAD_OPERATION_TYPE_UPDATE = 2;

    //离职资产 扣费金额,净值填,已扣金额,已还金额,损失金额写的最大值
    const MAX_AMOUNT_VALUE = '999999999999.99';
    //离职资产 发送短信选择的国家
    public static $leave_assets_country_nation_map = [
        GlobalEnums::TH_COUNTRY_CODE => 'TH',
        GlobalEnums::PH_COUNTRY_CODE => 'PH',
        GlobalEnums::MY_COUNTRY_CODE => 'MY',
        GlobalEnums::VN_COUNTRY_CODE => 'VN',
        GlobalEnums::ID_COUNTRY_CODE => 'ID',
        GlobalEnums::LA_COUNTRY_CODE => 'LA',
    ];

    //资产台账-BY端使用方向1:个人资产,2:公共资产 (仅翻译不同,产品把by端的个人使用=个人资产,公共使用=公共资产)
    public static $use_by = [
        self::USE_PERSONAL => 'material_asset_use_by.' . self::USE_PERSONAL,
        self::USE_PUBLIC => 'material_asset_use_by.' . self::USE_PUBLIC
    ];
    //资产转移 是否自动接收auto_recipient
    const MATERIAL_TRANSFER_AUTO_RECIPIENT_YES = 1;
    const MATERIAL_TRANSFER_AUTO_RECIPIENT_NO = 0;
    //资产转移发送by站内信分类
    const MATERIAL_TRANSFER_MESSAGE_CATEGORY_REMIND1 = 80; //我转出-批量提醒
    const MATERIAL_TRANSFER_MESSAGE_CATEGORY_REMIND2 = 81; //业务数据查询-批量提醒
    const MATERIAL_TRANSFER_MESSAGE_CATEGORY_REJECT = 82; //批量拒绝
    const MATERIAL_TRANSFER_MESSAGE_CATEGORY_AUTO_RECEPTION = 83; //批量接收
    const MATERIAL_TRANSFER_MESSAGE_CATEGORY_PENDING_RECEIPT_REMINDER = 129;//待接收资产超过资产转移间隔时间提醒
    // OA 资产接收提醒 - 员工互转或者资产部转移操作提醒
    const MATERIAL_TRANSFER_MESSAGE_CATEGORY_TRANSFER_REMINDER = 130;

    //资产同意书,资产申请书最大数量限制
    const MATERIAL_EXPORT_PDF_MAX = 1000;

    //离职资产-金额日志主表操作类型
    const MATERIAL_MAIN_AMOUNT_LOG_OPERATION_SAVE = 1; //编辑
    const MATERIAL_MAIN_AMOUNT_LOG_OPERATION_ADD = 2; //添加
    const MATERIAL_MAIN_AMOUNT_LOG_OPERATION_DELETE = 3; //删除
    const MATERIAL_MAIN_AMOUNT_LOG_OPERATION_EXPORT = 4; //导入
    //离职资产-金额日志明细表操作类型
    const MATERIAL_DETAIL_AMOUNT_LOG_OPERATION_SAVE = 1; //编辑
    const MATERIAL_DETAIL_AMOUNT_LOG_OPERATION_ADD = 2; //添加
    const MATERIAL_DETAIL_AMOUNT_LOG_OPERATION_DELETE = 3; //删除
    const MATERIAL_DETAIL_AMOUNT_LOG_OPERATION_EXPORT_ADD = 4; //导入添加
    const MATERIAL_DETAIL_AMOUNT_LOG_OPERATION_EXPORT_SAVE = 5; //导入编辑

    //退回导出最大条数
    const RETURN_ASSET_EXPORT_MAX = 10000;

    //资产退回&维修
    const MATERIAL_SET_RETURN_REPAIR_IS_UNDERTAKE_NO = 1;//是否兜底组，1否
    const MATERIAL_SET_RETURN_REPAIR_IS_UNDERTAKE_YES = 2;//是否兜底组，2是
    const MATERIAL_SET_RETURN_REPAIR_IS_UNDERTAKE_VALIDATE = self::MATERIAL_SET_RETURN_REPAIR_IS_UNDERTAKE_NO . ',' . self::MATERIAL_SET_RETURN_REPAIR_IS_UNDERTAKE_YES;
    public static $material_set_return_repair_is_undertake = [
        self::MATERIAL_SET_RETURN_REPAIR_IS_UNDERTAKE_NO => 'return_repair_is_undertake.1',
        self::MATERIAL_SET_RETURN_REPAIR_IS_UNDERTAKE_YES => 'return_repair_is_undertake.2',
    ];
    //退回类型
    const MATERIAL_SET_RETURN_REPAIR_TYPE_STATUS_INVALID = 1;//状态，1无效
    const MATERIAL_SET_RETURN_REPAIR_TYPE_STATUS_VALID  = 2;//状态,2有效
    const MATERIAL_SET_RETURN_REPAIR_TYPE_STATUS_VALIDATE = self::MATERIAL_SET_RETURN_REPAIR_TYPE_STATUS_INVALID . ',' . self::MATERIAL_SET_RETURN_REPAIR_TYPE_STATUS_VALID;
    public static $material_set_return_repair_type_status = [
        self::MATERIAL_SET_RETURN_REPAIR_TYPE_STATUS_INVALID => 'return_repair_type_status.1',
        self::MATERIAL_SET_RETURN_REPAIR_TYPE_STATUS_VALID => 'return_repair_type_status.2',
    ];

    //退回方式
    const RETURN_METHOD_FACE = 1; //当面退回
    const RETURN_METHOD_POST = 2; //邮寄退回
    const RETURN_METHOD_VALIDATE = self::TRANSFER_METHOD_FACE . ',' . self::TRANSFER_METHOD_POST;
    public static $return_method = [
        self::RETURN_METHOD_FACE => 'material_asset_return_method.' . self::RETURN_METHOD_FACE,
        self::RETURN_METHOD_POST => 'material_asset_return_method.' . self::RETURN_METHOD_POST,
    ];

    //退回资产操作记录类型material_asset_return_record
    const RETURN_ASSETS_RECORD_TYPE_ADD = 1; //新增
    const RETURN_ASSETS_RECORD_TYPE_HANDLE = 2; //处理
    const RETURN_ASSETS_RECORD_TYPE_RECEIPT = 3; //接收
    const RETURN_ASSETS_RECORD_TYPE_REJECT = 4; //拒收
    const RETURN_ASSETS_RECORD_TYPE_TRANSFER = 5; //转交
    const RETURN_ASSETS_RECORD_TYPE_CANCEL = 6; //撤销
    const RETURN_ASSETS_RECORD_TYPE_STORAGE_CREATE = 7; //创建SCM退库单
    const RETURN_ASSETS_RECORD_TYPE_STORAGE_DONE = 8; //完成入库
    const RETURN_ASSETS_RECORD_TYPE_FINISH = 9; //完成工单
    const RETURN_ASSETS_RECORD_TYPE_STORAGE_CANCEL = 10; //撤销SCM退库单
    const RETURN_ASSETS_RECORD_TYPE_STORAGE_DELETE = 11; //删除SCM退库单
    public static $return_assets_record_type_list = [
        self::RETURN_ASSETS_RECORD_TYPE_ADD => 'material_return_asset_record_type.' . self::RETURN_ASSETS_RECORD_TYPE_ADD,
        self::RETURN_ASSETS_RECORD_TYPE_HANDLE => 'material_return_asset_record_type.' . self::RETURN_ASSETS_RECORD_TYPE_HANDLE,
        self::RETURN_ASSETS_RECORD_TYPE_RECEIPT => 'material_return_asset_record_type.' . self::RETURN_ASSETS_RECORD_TYPE_RECEIPT,
        self::RETURN_ASSETS_RECORD_TYPE_REJECT => 'material_return_asset_record_type.' . self::RETURN_ASSETS_RECORD_TYPE_REJECT,
        self::RETURN_ASSETS_RECORD_TYPE_TRANSFER => 'material_return_asset_record_type.' . self::RETURN_ASSETS_RECORD_TYPE_TRANSFER,
        self::RETURN_ASSETS_RECORD_TYPE_CANCEL => 'material_return_asset_record_type.' . self::RETURN_ASSETS_RECORD_TYPE_CANCEL,
        self::RETURN_ASSETS_RECORD_TYPE_STORAGE_CREATE => 'material_return_asset_record_type.' . self::RETURN_ASSETS_RECORD_TYPE_STORAGE_CREATE,
        self::RETURN_ASSETS_RECORD_TYPE_STORAGE_DONE => 'material_return_asset_record_type.' . self::RETURN_ASSETS_RECORD_TYPE_STORAGE_DONE,
        self::RETURN_ASSETS_RECORD_TYPE_FINISH => 'material_return_asset_record_type.' . self::RETURN_ASSETS_RECORD_TYPE_FINISH,
        self::RETURN_ASSETS_RECORD_TYPE_STORAGE_CANCEL => 'material_return_asset_record_type.' . self::RETURN_ASSETS_RECORD_TYPE_STORAGE_CANCEL,
        self::RETURN_ASSETS_RECORD_TYPE_STORAGE_DELETE => 'material_return_asset_record_type.' . self::RETURN_ASSETS_RECORD_TYPE_STORAGE_DELETE,
    ];
    //操作记录需要记录的字段
    public static $return_record_need_view_fields = [
        'return_reason' => 'material_asset_return.return_reason', //退回原因
        'asset_handling_status' => 'material_asset_return.asset_handling_status', //资产处理进度
        'asset_handling_attachment' => 'material_asset_return.asset_handling_attachment',//处理附件
        'asset_handling_remark' => 'material_asset_return.asset_handling_remark',//资产处理备注
        'cancel_remark' => 'material_asset_return.cancel_remark', //撤回原因
        'reject_remark' => 'material_asset_return.reject_remark', //拒收原因
        'transfer_reason' => 'material_asset_return.transfer_reason', //转交原因
        'storage_remark' => 'material_asset_return.storage_remark', //入库备注
        'cancel_storage_remark' => 'material_asset_return.cancel_storage_remark',//撤销入库原因
        'storage_deleted' => 'material_asset_return.storage_delete',//入库删除
    ];

    //退回状态 -- 对应表material_asset_return中status字段
    const RETURN_STATUS_UNRECEIVED = 1;//待接收
    const RETURN_STATUS_RECEIVED = 2;//已接收
    const RETURN_STATUS_REJECTED = 3;//已拒收
    const RETURN_STATUS_CANCEL = 4;//已撤回
    const RETURN_STATUS_FINISHED = 5;//已完成
    //退回状态1待接收，2已接收，3已拒收，4已撤回，5已完成
    public static $return_status = [
        self::RETURN_STATUS_UNRECEIVED => 'material_asset_return_status.' . self::RETURN_STATUS_UNRECEIVED,
        self::RETURN_STATUS_RECEIVED => 'material_asset_return_status.' . self::RETURN_STATUS_RECEIVED,
        self::RETURN_STATUS_REJECTED => 'material_asset_return_status.' . self::RETURN_STATUS_REJECTED,
        self::RETURN_STATUS_CANCEL => 'material_asset_return_status.' . self::RETURN_STATUS_CANCEL,
        self::RETURN_STATUS_FINISHED => 'material_asset_return_status.' . self::RETURN_STATUS_FINISHED,
    ];

    //退回申请-资产处理进度-定义了部分程序需要用到的常量具体值参考  material_asset_return_asset_handling_status表 配置
    const RETURN_HANDLING_STATUS_SEARCH_YES = 1;//是否前端自行寻找1是
    const RETURN_HANDLING_STATUS_UNRECEIVED = 1;//待接收
    const RETURN_HANDLING_STATUS_RECEIVED = 2;//已接收待检修
    const RETURN_HANDLING_STATUS_REJECTED = 3;//已拒收
    const RETURN_HANDLING_STATUS_CANCEL = 4;//已撤回
    const RETURN_HANDLING_STATUS_IN_STORAGE = 5;//入库中
    const RETURN_HANDLING_STATUS_WAIT_STORAGE = 6;//已接收待入库
    const RETURN_HANDLING_STATUS_FINISHED = 7;//完成入库

    //退回申请-退回入库-状态 1待处理，2已审核，3已入库(SCM)，4已入库(非SCM)，5已撤回
    const RETURN_STORAGE_STATUS_WAIT = 1;//待处理
    const RETURN_STORAGE_STATUS_AUDIT = 2;//已审核
    const RETURN_STORAGE_STATUS_SCM = 3;//已入库(SCM)
    const RETURN_STORAGE_STATUS_NOT_SCM = 4;//已入库(非SCM)
    const RETURN_STORAGE_STATUS_CANCEL = 5;//已撤回
    const RETURN_STORAGE_STATUS_VALIDATE = self::RETURN_STORAGE_STATUS_WAIT . ',' . self::RETURN_STORAGE_STATUS_AUDIT . ',' . self::RETURN_STORAGE_STATUS_SCM . ',' . self::RETURN_STORAGE_STATUS_NOT_SCM . ',' . self::RETURN_STORAGE_STATUS_CANCEL;
    public static $return_storage_status = [
        self::RETURN_STORAGE_STATUS_WAIT => 'material_asset_return_storage_status.' . self::RETURN_STORAGE_STATUS_WAIT,
        self::RETURN_STORAGE_STATUS_AUDIT => 'material_asset_return_storage_status.' . self::RETURN_STORAGE_STATUS_AUDIT,
        self::RETURN_STORAGE_STATUS_SCM => 'material_asset_return_storage_status.' . self::RETURN_STORAGE_STATUS_SCM,
        self::RETURN_STORAGE_STATUS_NOT_SCM => 'material_asset_return_storage_status.' . self::RETURN_STORAGE_STATUS_NOT_SCM,
        self::RETURN_STORAGE_STATUS_CANCEL => 'material_asset_return_storage_status.' . self::RETURN_STORAGE_STATUS_CANCEL,
    ];

    //退回提醒-标头
    public static $return_send_message_title = [
        self::MATERIAL_ASSET_RETURN_MESSAGE_CATEGORY_REJECT => [
            'การแจ้งเตือนการปฏิเสธการคืนสินทรัพย์ 资产退回拒收提醒',
            'Asset return rejection reminder 资产退回拒收提醒'
        ],
        self::MATERIAL_ASSET_RETURN_MESSAGE_CATEGORY_FINISHED => [
            'แจ้งการขอคืนทรัพย์สินเสร็จสิ้น 资产退回申请完成提醒',
            'Notification of completion of asset return application 资产退回申请完成提醒'
        ]
    ];

    //退回转交-邮件信息
    public static $return_transfer_email_info = [
        'zh' => [
            'title' => '退回资产转交提醒',
            'content' => '您好，员工申请的退回资产，被{%group_name%}的{%staff_id%}{%staff_name%}转交至您所在的退回维修组{%now_group_name%}，请您及时去OA-物料/资产管理-退回资产处理查看！{%url%}。也可以下载附件进行查看资产明细。<br/>',
            'header' => [
                '资产码',
                '旧资产码',
                'SN码',
                '资产名称',
                '转交人工号',
                '转交人姓名',
                '转交人部门',
                '转交日期',
                '转交原因',
                '申请人工号',
                '申请人姓名',
                '申请人部门',
                '申请日期',
            ]
        ],
        'th' => [
            'title' => 'การแจ้งเตือนการโอนสินทรัพย์คืน',
            'content' => 'สวัสดี ทรัพย์สินที่พนักงานยื่นขอส่งคืน ถูกโอนไปยังกลุ่มการซ่อมบำรุงและส่งคืนของคุณ {%group_name%}โดย {%staff_id%}{%staff_name%} จาก {%now_group_name%}โปรดไปที่ OA-การจัดการasset-ตรวจสอบดำเนินการส่งคืนทรัพย์สิน {%url%}คุณยังสามารถดาวน์โหลดไฟล์แนบเพื่อดูรายละเอียดทรัพย์สินได้',
            'header' => [
                'รหัสสินทรัพย์',
                'รหัสสินทรัพย์เก่า',
                'รหัสเอสเอ็น',
                'ชื่อสินทรัพย์',
                'โอนหมายเลขคู่มือ',
                'ชื่อค/โอ',
                'แผนกผู้โอน',
                'วันที่โอน',
                'เหตุผลในการโอน',
                'สมัครหมายเลขคู่มือ',
                'ชื่อผู้สมัคร',
                'ฝ่ายผู้สมัคร',
                'วันที่สมัคร',
            ]
        ],
        'en' => [
            'title' => 'Reminder for asset return and transfer',
            'content' => 'Hello, the returned assets applied by the employee have been transferred by {%staff_id%}{%staff_name%}of{%group_name%}to your return maintenance group{%now_group_name%}，please go to OA-Material/Asset Management-Returned Asset Processing to check! {%url%}in time. You can also download the attachment to view the asset details.',
            'header' => [
                'Asset code',
                'Old asset code',
                'SN code',
                'Asset name',
                'Transferee ID',
                'Transferee name',
                'Transferee department',
                'Transfer date',
                'Transfer reason',
                'Applicant ID',
                'Applicant name',
                'Applicant department',
                'Application date'
            ]
        ]
    ];


    //资产数据管控 - 是否看全部数据
    const MATERIAL_SET_DATA_PERMISSION_IS_SEE_ALL_NO = 1;//是否看全部数据，1否
    const MATERIAL_SET_DATA_PERMISSION_IS_SEE_ALL_YES = 2;//是否看全部数据，2是
    const MATERIAL_SET_DATA_PERMISSION_IS_SEE_ALL_VALIDATE = self::MATERIAL_SET_DATA_PERMISSION_IS_SEE_ALL_NO . ',' . self::MATERIAL_SET_DATA_PERMISSION_IS_SEE_ALL_YES;
    public static $material_set_data_permission_is_see_all = [
        self::MATERIAL_SET_DATA_PERMISSION_IS_SEE_ALL_NO => 'data_permission_is_see_all.1',
        self::MATERIAL_SET_DATA_PERMISSION_IS_SEE_ALL_YES => 'data_permission_is_see_all.2',
    ];
}
