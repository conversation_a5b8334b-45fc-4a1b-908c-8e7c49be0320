<?php

namespace App\Library;

final class ErrCode {
    /**
     * @Message ('默认')
     */
    public static $DEFAULT = 0;

    /**
     * @Message ('成功')
     */
    public static $SUCCESS = 1;

    /**
     * @Message ('验证错误，可对外抛出的错误')
     */
    public static $VALIDATE_ERROR = 2;

    //系统故障系列
    /**
     * @Message ('系统内部错误')
     */
    public static $SYSTEM_ERROR = 3;

    //MySQL级别错误
    /**
     * @Message ('MySQL内部错误')
     */
    public static $MYSQL_ERROR = 4;

    /**
     * @Message ('业务处理错误')
     */
    public static $BUSINESS_ERROR = 5;

    /**
     * @Message ('访问频繁')
     */
    public static $FREQUENT_VISIT_ERROR = 6;

    /**
     * @Message ('系统维护中')
     */
    public static $SYSTEM_MAINTENANCE_ERROR = 7;


    /**
     * @var int
     */
    public static $INVALID_DATA_FORMAT_ERROR = 8;

    /**
     * @Message ('签约链接无效')
     */
    public static $REQUEST_SIGN_LINK_INVALID = 10;

    /**
     * @Message ('未登录授权,不可访问')
     */
    public static $REQUEST_UNAUTHORIZED_ERROR = 401;

    /**
     * @Message ('禁止访问')
     */
    public static $REQUEST_FORBIDDEN_ERROR = 403;

    /**     1000以下预留，业务错误请从1000开始使用     **/

    //业务操作系列，可以细化定义具体业务报错
    /**             合同部分开始占用1000～2000                    **/

    /**
     * @Message ('生成合同失败')
     */
    public static $CONTRACT_CREATE_ERROR = 1000;

    /**
     * @Message ('生成合同获取模版信息失败')
     */
    public static $CONTRACT_CREATE_GET_TEMPLATE_ERROR = 1001;

    /**
     * @Message ('更新合同失败')
     */
    public static $CONTRACT_UPDATE_ERROR = 1010;

    /**
     * @Message ('获取合同信息失败')
     */
    public static $CONTRACT_GET_INFO_ERROR = 1011;

    /**
     * @Message ('更新合同获取模版信息失败')
     */
    public static $CONTRACT_UPDATE_GET_TEMPLATE_ERROR = 1012;

    /**
     * @Message ('更新合同获取合同附表信息失败')
     */
    public static $CONTRACT_OTHER_GET_INFO_ERROR = 1013;

    /**
     * @Message ('撤销合同失败')
     */
    public static $CONTRACT_CANCEL_ERROR = 1014;

    /**
     * @Message ('无权查看当前合同信息')
     */
    public static $CONTRACT_GET_INFO_NO_AUTH_ERROR = 1020;

    /**
     * @Message ('无合同文件')
     */
    public static $CONTRACT_GET_NO_CONTRACT_FILE_ERROR = 1021;

    /**
     * @Message ('生成合同生成工作流失败')
     */
    public static $CONTRACT_CREATE_WORK_FLOW_ERROR = 1030;

    /**
     * @Message ('获取工作流批次失败')
     */
    public static $CONTRACT_GET_WORK_REQUEST_ERROR = 1031;

    /**
     * @Message ('审批流通过失败')
     */
    public static $CONTRACT_WORK_FLOW_APPROVE_ERROR = 1032;

    /**
     * @Message ('审批流拒绝失败')
     */
    public static $CONTRACT_WORK_FLOW_REJECT_ERROR = 1033;

    /**
     * @Message ('获取归档信息失败')
     */
    public static $CONTRACT_ARCHIVE_GET_INFO_ERROR = 1040;

    /**
     * @Message ('更新归档信息失败')
     */
    public static $CONTRACT_ARCHIVE_UPDATE_INFO_ERROR = 1041;

    /**
     * @Message ('下载审批流记录失败')
     */
    public static $CONTRACT_WORKFLOW_LOG_DOWNLOAD_ERROR = 1042;

    /**
     * @Message ('获取MS客户id失败')
     */
    public static $CONTRACT_GET_CONSUMER_ERROR = 1043;

    /**
     * @Message ('更新合同账户ID失败')
     */
    public static $CONTRACT_CONFIGURE_CONSUMER_ERROR = 1044;

    /**
     * @Message ('同步MS失败')
     */
    public static $CONTRACT_SYNC_MS_ERROR = 1045;

    /**
     * @Message ('同步MS失败')
     */
    public static $CONTRACT_SYNC_MS_REPETITIVE = 1046;

    /**             供应商部分开始占用2000～3000                    **/
    /**
     * @Message ('创建供应商失败')
     */
    public static $VENDOR_CREATE_ERROR = 2000;

    /**
     * @Message ('供应商应用模块创建失败')
     */
    public static $VENDOR_APPLICATION_MODULE_CREATE_ERROR = 2001;


    /**
     * @Message ('供应商附件创建失败')
     */
    public static $VENDOR_ATTACHMENT_CREATE_ERROR = 2002;

    /**
     * @Message ('供应商审批流创建失败')
     */
    public static $VENDOR_WORKFLOW_CREATE_ERROR = 2003;

    /**
     * @Message ('供应商审批流查询失败')
     */
    public static $VENDOR_WORKFLOW_GET_ERROR = 2004;

    /**
     * @Message ('供应商审批流驳回操作失败')
     */
    public static $VENDOR_WORKFLOW_REJECTED_ERROR = 2005;

    /**
     * @Message ('供应商信息获取失败')
     */
    public static $VENDOR_INFO_GET_ERROR = 2006;

    /**
     * @Message ('供应商日志创建失败')
     */
    public static $VENDOR_LOG_CREATE_ERROR = 2007;

    /**
     * @Message ('更新供应商审批状态失败')
     */
    public static $VENDOR_UPDATE_AUDIT_STATUS_ERROR= 2010;

    /**
     * @Message ('更新供应商失败')
     */
    public static $VENDOR_UPDATE_INFO_ERROR = 2011;

    /**
     * @Message ('供应商审批通过版本表写入失败')
     */
    public static $VENDOR_UPDATE_APPROVAL_VERSION_INFO_ERROR = 2012;

    /**
     * @Message ('供应商信息不存在')
     */
    public static $VENDOR_DELETE_NO_DATA_ERROR = 2013;
    /**
     * @Message ('供应商未审批完成')
     */
    public static $VENDOR_DELETE_STATUS_ERROR = 2014;
    /**
     * @Message ('供应商删除失败')
     */
    public static $VENDOR_DELETE_RESULT_ERROR = 2015;


    /**                 资产审批                 **/
    /**
     * @Message ('创建资产映射失败')
     */
    public static $ASSET_AUDIT_CREATE_ERROR = 3000;

    /**
     * @Message ('获取资产审批信息失败')
     */
    public static $ASSET_AUDIT_GET_INFO_ERROR = 3010;

    /**
     * @Message ('无权查看当前资产审批信息')
     */
    public static $ASSET_AUDIT_GET_INFO_NO_AUTH_ERROR = 3020;

    /**
     * @Message ('创建资产审批流失败')
     */
    public static $ASSET_CREATE_WORK_FLOW_ERROR = 3030;


    /**                 物料审批                 **/
    /**
     * @Message ('创建物料审批失败')
     */
    public static $WMS_AUDIT_CREATE_ERROR = 4000;

    /**
     * @Message ('获取物料审批信息失败')
     */
    public static $WMS_AUDIT_GET_INFO_ERROR = 3010;

    /**
     * @Message ('无权查看当前物料审批信息')
     */
    public static $WMS_AUDIT_GET_INFO_NO_AUTH_ERROR = 4020;

    /**
     * @Message ('创建物料审批流失败')
     */
    public static $WMS_CREATE_WORK_FLOW_ERROR = 4030;

    // 借款申请部分开始占用5000～6000
    /**
     * @Message ('创建借款申请审批流失败')
     */
    public static $LOAN_CREATE_WORK_FLOW_ERROR = 5000;

    // 付款管理 - 网点租房付款业务: 错误码区间 7000 ~ 8000
    /**
     * @Message （'创建网点租房付款审批流失败'）
     */
    public static $STORE_RENTING_PAYMENT_CREATE_WORK_FLOW_ERROR = 7000;

    /**
     * @Message （'获取网点租房付款信息失败'）
     */
    public static $STORE_RENTING_PAYMENT_GET_INFO_ERROR = 7001;

    /**
     * @Message （'获取网点租房付款审批流拒绝'）
     */
    public static $STORE_RENTING_PAYMENT_WORK_FLOW_REJECT_ERROR = 7002;


    /**
     * @Message （'网点租房付款申请撤回失败, 其审批状态非待审核'）
     */
    public static $STORE_RENTING_PAYMENT_NOT_PENDING_CANCEL_ERROR = 7003;

    /**
     * @Message ('网点租房付款获取工作流批次失败')
     */
    public static $STORE_RENTING_PAYMENT_GET_WORK_REQUEST_ERROR = 7004;

    /**
     * @Message ('更新网点租房主表失败')
     */
    public static $STORE_RENTING_PAYMENT_UPDATE_ERROR = 7005;

    /**
     * @Message ('该工号无网点租房付款支付权限')
     */
    public static $STORE_RENTING_PAYMENT_PAY_AUTH_FAIL_ERROR = 7006;

    /**
     * @Message ('网点租房付款支付失败')
     */
    public static $STORE_RENTING_PAYMENT_PAY_CREATE_FAIL_ERROR = 7007;

    /**
     * @Message ('网点租房付款支付，支付状态同步失败')
     */
    public static $STORE_RENTING_PAYMENT_PAY_UPDATE_FAIL_ERROR = 7008;

    /**
     * @Message ('网点租房付款支付，审批未通过，不能支付')
     */
    public static $STORE_RENTING_PAYMENT_PAY_APPROVAL_STATUS_ERROR = 7009;

    /**
     * @Message ('网点租房付款下载失败，请重试')
     */
    public static $STORE_RENTING_PAYMENT_DOWNLOAD_FAIL_ERROR = 7010;

    /**
     * @Message ('网点租房付款支付，金额详情参数为空，不能更新WHT字段')
     */
    public static $STORE_RENTING_PAYMENT_APPROVAL_WHT_UPDATE_ERROR = 7011;

    /**
     * @Message ('网点租房付款支付，审批未通过，不能下载')
     */
    public static $STORE_RENTING_PAYMENT_DOWNLOAD_APPROVAL_STATUS_ERROR = 7012;

    /**
     * @Message ('更新网点租房金额详情表失败')
     */
    public static $STORE_RENTING_PAYMENT_AMOUNT_DETAIL_UPDATE_ERROR = 7013;

    /**
     * @Message ('预算超出月度预算，不影响提交')
     */
    public static $BUDGET_OVERAMOUNT_MONTH = 6001;

    /**
     * @Message ('预算超出季度预算，影响提交')
     */
    public static $BUDGET_OVERAMOUNT_QUARTERLY = 6002;


    /********************* 普通付款 申请: 错误码区间 8000 ~ 8999 start**********************/

    //创建申请-基本信息创建失败
    public static $ORDINARY_PAYMENT_CREATE_MAIN_ERROR = 8000;

    //创建申请-金额详情数据失败
    public static $ORDINARY_PAYMENT_CREATE_DETAIL_ERROR = 8001;

    //创建申请 金额详情-附件数据失败
    public static $ORDINARY_PAYMENT_CREATE_DETAIL_FILE_ERROR = 8002;

    //创建申请 供应商数据失败
    public static $ORDINARY_PAYMENT_CREATE_EXTEND_ERROR = 8003;

    //创建申请 审批流生成失败
    public static $ORDINARY_PAYMENT_CREATE_WORK_FLOW_ERROR = 8004;

    //无付款支付操作权限
    public static $ORDINARY_PAYMENT_PAY_AUTH_ERROR = 8005;

    //获取工作流批次失败
    public static $ORDINARY_PAYMENT_GET_WORK_REQUEST_ERROR = 8006;

    //普通付款-基本信息获取失败
    public static $ORDINARY_PAYMENT_GET_INFO_ERROR = 8007;

    //待审核状态可撤销
    public static $ORDINARY_PAYMENT_STATUS_CANCEL_ERROR = 8008;

    //撤回失败-审批流失败
    public static $ORDINARY_PAYMENT_CANCEL_ERROR = 8009;

    //撤回失败-主表操作失败
    public static $ORDINARY_PAYMENT_CANCEL_MAIN_ERROR = 8010;

    //驳回失败-审批流失败
    public static $ORDINARY_PAYMENT_REJECT_ERROR = 8011;

    //驳回失败-主表更新操作失败
    public static $ORDINARY_PAYMENT_REJECT_MAIN_ERROR = 8012;


    //审批通过-主表更新失败
    public static $ORDINARY_PAYMENT_APPROL_MAIN_UPDATE_ERROR = 8013;

    //审批通过-金额详情表更新失败
    public static $ORDINARY_PAYMENT_APPROL_DETAIL_UPDATE_ERROR = 8014;


    //支付状态"未支付" 下载失败
    public static $ORDINARY_PAYMENT_PAY_STATUS_DOWNLOAD_ERROR = 8016;

    //下载文件上传oss失败
    public static $ORDINARY_PAYMENT_DOWNLOAD_UPLOAD_OSS_ERROR = 8017;


    //审核未通过或非待支付状态，不可进行支付操作
    public static $ORDINARY_PAYMENT_PAY_NOT_ALLOW_EDIT_ERROR = 8018;

    //付款支付-支付信息入库失败
    public static $ORDINARY_PAYMENT_PAY_EXTEND_DATA_INSERT_ERROR = 8019;

    //付款支付-支付状态同步主表失败
    public static $ORDINARY_PAYMENT_PAY_STATUS_SYNC_ERROR = 8020;

    //普通付款-查看详情数据获取失败
    public static $ORDINARY_PAYMENT_GET_DETAIL_INFO_ERROR = 8021;


    /********************* 普通付款 申请: 错误码区间 8000 ~ 8999 end **********************/
    //不存在员工id
    public static $JOB_TRANSFER_STAFF_NOT_EXIST_ERROR = 9000;

    //员工不在职
    public static $JOB_TRANSFER_STAFF_NOT_ON_JOB_ERROR = 9001;

    //转岗人与被转岗人不能是同一个人
    public static $JOB_TRANSFER_STAFF_BE_SAME_ERROR = 9002;

    //存在待转岗申请
    public static $JOB_TRANSFER_EXIST_TRANSFER_ERROR = 9003;

    //部门错误
    public static $JOB_TRANSFER_DEPARTMENT_ERROR = 9004;

    //不存在HRBP
    public static $JOB_TRANSFER_NOT_EXIST_HRBP_ERROR = 9005;

    //申请人与调岗人不在相同的大区或片区
    public static $JOB_TRANSFER_NOT_IN_PIECE_OR_REGION_ERROR = 9006;

    //申请人与调岗人不在同一个网点
    public static $JOB_TRANSFER_NOT_IN_SAME_STORE_ERROR = 9007;

    //无申请权限
    public static $JOB_TRANSFER_NO_PERMISSION_ERROR = 9008;

    //生成批次号失败
    public static $JOB_TRANSFER_GENERATE_BATCH_NUM_ERROR = 9009;

    //非一线员工
    public static $JOB_TRANSFER_NOT_FRONT_LINE_ERROR = 9010;
    /********************* 取数需求工单审批: 错误码区间 10000 ~ 10999 start **********************/

    /**
     * @Message （'创建取数工单审批流失败'）
     */
    public static $ACCESS_DATA_SYS_CREATE_WORK_FLOW_ERROR = 10000;

    /**
     * @Message （'获取取数工单信息失败'）
     */
    public static $ACCESS_DATA_SYS_GET_INFO_ERROR = 10001;

    /**
     * @Message ('该员工不在取数工单系统中')
     */
    public static $ACCESS_DATA_SYS_STAFF_NULL_ERROR = 10002;

    /**
     * @Message （'取数工单系统审批流驳回操作失败'）
     */
    public static $ACCESS_DATA_SYS_WORK_FLOW_REJECT_ERROR = 10003;

    /**
     * @Message ('取数工单获取工作流失败')
     */
    public static $ACCESS_DATA_SYS_GET_WORK_REQUEST_ERROR = 10004;

    /**
     * @Message ('更新取数工单主表失败')
     */
    public static $ACCESS_DATA_WORK_ORDER_UPDATE_ERROR = 10005;

    /**
     * @Message ('该工号无取数工单列表权限')
     */
    public static $ACCESS_DATA_SYS_LIST_AUTH_FAIL_ERROR = 10006;

    /**
     * @Message ('该工号无取数工单审批权限')
     */
    public static $ACCESS_DATA_SYS_AUDIT_AUTH_FAIL_ERROR = 10007;

    /**
     * @Message ('取数工单系统，数据上传附件更新失败')
     */
    public static $ACCESS_DATA_SYS_ATTACH_UPDATE_FAIL_ERROR = 10008;

    /**
     * @Message ('取数工单系统，审批未通过，不能上传数据')
     */
    public static $ACCESS_DATA_SYS_APPROVAL_STATUS_ERROR = 10009;

    /**
     * @Message ('取数工单系统，上传数据类型错误')
     */
    public static $ACCESS_DATA_SYS_UPLOAD_DATA_TYPE_ERROR = 10010;

    /**
     * @Message ('取数工单系统，上传数据失败')
     */
    public static $ACCESS_DATA_SYS_UPLOAD_DATA_FAIL_ERROR = 10011;

    /**
     * @Message ('取数工单已复核通过，不可重新上传')
     */
    public static $ACCESS_DATA_SYS_UPLOAD_DATA_FAIL_REVIEWED_ERROR = 10012;

    /**
     * @Message ('取数工单数据已上传，待复核，不可重新上传')
     */
    public static $ACCESS_DATA_SYS_UPLOAD_DATA_FAIL_WAITING_REVIEW_ERROR = 10013;


    /**
     * @Message ('取数工单数据未上传，不可复核')
     */
    public static $ACCESS_DATA_SYS_DATA_NOT_UPLOADED_REVIEW_ERROR = 10014;

    /**
     * @Message ('取数工单数据已复核，不可重复复核')
     */
    public static $ACCESS_DATA_SYS_AGAIN_REVIEWED_ERROR = 10015;

    /**
     * @Message ('取数工单数据已复核，不可重复复核')
     */
    public static $ACCESS_DATA_SYS_APPLY_FORM_ATTACHE_ERROR = 10016;

    /********************* 取数需求工单审批: 错误码区间 10000 ~ 10999 end **********************/



    /********************* 银行流水: 错误码区间 11000 ~ 11999 start **********************/
    public static $BANK_FLOW_HAVE_DIFF_AMOUNT = 11000;

    /**
     * @Message ('银行流水上传失败')
     */
    public static $BANK_FLOW_UPLOAD_ERROR = 11001;

    /**
     * @Message ('银行流水附件上传失败')
     */
    public static $BANK_FLOW_ATTACHMENT_UPLOAD_ERROR = 11002;

    /**
     * @Message ('银行流水附件删除失败')
     */
    public static $BANK_FLOW_ATTACHMENT_REMOVE_ERROR = 11003;

    /**
     * @Message ('银行流水费用类型编辑保存失败')
     */
    public static $BANK_FLOW_EXPENSE_TYPE_SAVE_ERROR = 11004;

    /**
     * @Message ('银行流水费用类型编辑保存, 已有费用类型扩展字段删除失败')
     */
    public static $BANK_FLOW_EXPENSE_TYPE_SAVE_EXIST_EXPAND_DEL_ERROR = 11005;


    /**
     * @Message ('银行流水费用类型编辑保存, 费用类型扩展字段批量添加失败')
     */
    public static $BANK_FLOW_EXPENSE_TYPE_SAVE_EXPAND_INSERT_ERROR = 11006;

    /********************* 银行流水: 错误码区间 11000 ~ 11999 end **********************/

    /********************* 报价单管理: 错误码区间 12000 ~ 12999 start **********************/
    public static $QUOTATION_NOT_EXIST = 12000;
    public static $QUOTATION_UPDATE_ERROR = 12001;
    public static $QUOTATION_STATE_ERROR = 12002;
    public static $QUOTATION_CREATE_WORKFLOW_ERROR = 12003;
    /********************* 报价单管理: 错误码区间 12000 ~ 12999 end **********************/

    /**
     * @Message ('bank_flow_parcel_claim表里面已经存在一个相同的运单了')
     */
    public static $BANK_FLOW_PARCEL_CLAIM_FORM_ATTACHE_ERROR = 11007;

    /**
     * @Message ('bank_flow_parcel_claim表写入失败')
     */
    public static $BANK_FLOW_PARCEL_CLAIM_ADD_ERROR = 11008;

    /**
     * @Message ('bank_flow_oa表批量写入失败[from付款流水批量上传]')
     */
    public static $BANK_FLOW_BATCH_LINKED_OA_ORDER_INSERT_ERROR = 11009;


    /********************* 资产盘点: 错误码区间 13000 ~ 13999 start **********************/
    public static $INVENTORY_CHECK_CREATE_ERROR = 13000;
    public static $INVENTORY_CHECK_DEL_ERROR = 13001;
    /********************* 资产盘点: 错误码区间 13000 ~ 13999 end **********************/

    /**
     * @Message ('生成资产编码流水号失败')
     */
    public static $CREATE_SERIAL_CODE_ERROR = 14001;
    /**
     * @Message ('生成资产编码流水号超限')
     */
    public static $CREATE_SERIAL_CODE_EXCEED = 14002;
    /**
     * @Message ('生成资产编码失败')
     */
    public static $CREATE_ASSET_CODE_ERROR = 14003;
    /**
     * @Message ('生成资产数据失败')
     */
    public static $CREATE_ASSET_DATA_ERROR = 14004;
    /**
     * @Message ('scm回调更新资产数据失败')
     */
    public static $UPDATE_ASSET_DATA_ERROR = 14005;
    /**
     * @Message ('记录scm回调匹配失败的资产失败')
     */
    public static $CREATE_UNMATCHED_DATA_ERROR = 14006;
    /**
     * @Message ('删除资产数据失败')
     */
    public static $DELETE_ASSET_DATA_ERROR = 14007;

    /********************* 支付模块: 错误码区间 15000 ~ 15999 start **********************/
    public static $PAYMENT_WITHDRAW_ERROR = 15000;
    public static $PAYMENT_UPDATE_PAY_ERROR = 15001;
    public static $PAYMENT_UPDATE_PAY_STATUS_ERROR = 15002;
    public static $PAYMENT_UPDATE_PAY_DATA_ERROR = 15003;
    public static $PAYMENT_FINAL_PAY_ERROR = 15004;
    public static $PAYMENT_FINAL_PAYMENT_SAVE_ERROR = 15005;

    /********************* 支付模块: 错误码区间 15000 ~ 15999 end **********************/
    /********************* 系统配置模块: 错误码区间 17000 ~ 17999 start **********************/
    public static $LOAN_STAFF_ADD_ERROR = 16000;
    public static $LOAN_STAFF_EDIT_ERROR = 16001;

    /********************* 系统配置模块: 错误码区间 17000 ~ 17999 end **********************/

    /********************* 资产台账: 错误码区间 16000 ~ 16999 start **********************/
    public static $MATERIAL_ASSET_ACCOUNT_SET_ERROR = 16000;
    public static $MATERIAL_ASSET_ACCOUNT_ADD_ERROR = 16001;
    public static $MATERIAL_ASSET_ACCOUNT_SAVE_ERROR = 16002;
    public static $MATERIAL_ASSET_ACCOUNT_TRANSFER_ERROR = 16003;
    public static $MATERIAL_ASSET_ACCOUNT_BATCH_ADD_ERROR = 16004;
    public static $MATERIAL_ASSET_ACCOUNT_BATCH_SAVE_ERROR = 16005;
    public static $MATERIAL_ASSET_ACCOUNT_BATCH_TRANSFER_ERROR = 16006;

    public static $MATERIAL_ASSET_BATCH_SCRAP_UPDATE_ERROR = 16007;
    public static $MATERIAL_ASSET_BATCH_SCRAP_UPDATE_LOG_ERROR = 16008;
    public static $MATERIAL_ASSET_BATCH_FINANCE_UPDATE_ERROR = 16009;
    public static $MATERIAL_ASSET_BATCH_FINANCE_UPDATE_LOG_ERROR = 16010;
    /********************* 资产台账: 错误码区间 16000 ~ 16999 end **********************/
    /********************* 资产标准型号: 错误码区间 18000 ~ 18999 start **********************/
    public static $STANDARD_IMPORT_EXCEL_DATA_INDEX_ERROR = 18000;
    public static $STANDARD_IMPORT_EXCEL_DATA_INSERT_ID_ERROR = 18001;
    public static $STANDARD_IMPORT_EXCEL_DATA_INSERT_ERROR = 18002;
    public static $STANDARD_IMPORT_EXCEL_DATA_UPDATE_ERROR = 18003;
    public static $STANDARD_IMPORT_EXCEL_DATA_UPDATE_IMG_ERROR = 18004;
    public static $STANDARD_IMPORT_EXCEL_DATA_INSERT_IMG_ERROR = 18005;
    public static $STANDARD_IMPORT_EXCEL_DATA_UPDATE_SYNC_SCM_ERROR = 18006;
    public static $STANDARD_IMPORT_EXCEL_DATA_INSERT_SYNC_SCM_ERROR = 18007;
    public static $STANDARD_IMPORT_EXCEL_DATA_UPDATE_EDIT_LOG_ERROR = 18008;
    public static $STANDARD_IMPORT_EXCEL_DATA_INSERT_EDIT_LOG_ERROR = 18009;

    /********************* 资产标准型号: 错误码区间 18000 ~ 18999 start **********************/


    /********************* 资产领用出库: 错误码区间 17000 ~ 17999 start **********************/
    public static $MATERIAL_ASSET_OUT_STORAGE_ADD_ERROR = 17000;
    public static $MATERIAL_ASSET_OUT_STORAGE_CANCEL_ERROR = 17001;
    public static $MATERIAL_ASSET_OUT_STORAGE_SCM_RECALL_ERROR = 17002;
    public static $MATERIAL_ASSET_OUT_STORAGE_RELATED_ERROR = 17003;
    public static $MATERIAL_ASSET_OUT_STORAGE_TRANSFER_BATCH_ADD_ERROR = 17004;
    public static $MATERIAL_ASSET_OUT_STORAGE_TRANSFER_LOG_ADD_ERROR = 17005;
    /********************* 资产领用出库: 错误码区间 17000 ~ 17999 end **********************/
    /********************* 物料/资产管理-通用配置: 错误码区间 19000 ~ 19999 start **********************/
    public static $MATERIAL_SETTING_PC_CODE_ADD_ERROR = 19000;
    public static $MATERIAL_SETTING_PC_CODE_UPDATE_ERROR = 19001;

    /********************* 物料/资产管理-通用配置: 错误码区间 19000 ~ 19999 end **********************/

    /********************* 支票管理-通用配置: 错误码区间 20000 ~ 20099 start **********************/
    public static $CHEQUE_GET_CHEQUE_APPLY_INFO_ERROR = 20000;
    public static $CHEQUE_GET_ACCOUNT_EDIT_UPDATE_ERROR = 20001;
    public static $CHEQUE_GET_ACCOUNT_EDIT_SAVE_LOG_ERROR = 20002;
    public static $CHEQUE_GET_ACCOUNT_EDIT_SAVE_LOG_ATTACHMENTS_ERROR = 20003;
    public static $CHEQUE_CANCEL_WORKFLOW_ERROR = 20004;
    public static $CHEQUE_ACCOUNT_EMPTY_DATA_ERROR = 20005;
    public static $CHEQUE_CONTRACT_WITHDRAWAL_FAILED_ERROR = 20006;
    public static $CHEQUE_CHEQUE_APPLY_ADD_ERROR = 20007;
    public static $CHEQUE_CREATE_REQUEST_ADD_ERROR = 20008;
    public static $CHEQUE_CHEQUE_APPLY_DETAIL_ADD_ERROR = 20009;
    public static $CHEQUE_APPLY_CHEQUE_BATCH_ADD_ERROR = 20010;
    public static $CHEQUE_APPLY_CHEQUE_ACCOUNT_ADD_ERROR = 20011;
    public static $CHEQUE_GET_VALIDATE_ERROR = 20012;
    public static $CHEQUE_ACCOUNT_UPDATE_ERROR = 20013;
    public static $CHEQUE_CHEQUE_APPLY_ADD_DETAIL_ERROR = 20014;
    public static $CHEQUE_FLOW_SERVICE_WORKFLOW_REQUEST_ERROR = 20015;
    public static $CHEQUE_DEL_CHEQUE_ACCOUNT_BUSINESS_REL_ERROR = 20016;
    /*********************  支票管理-通用配置: 错误码区间 20000 ~ 20099 end **********************/

    /********************* 押金管理-通用配置: 错误码区间 20100 ~ 20199 start **********************/
    public static $DEPOSIT_SERVICE_ADD_APPLY_ERROR = 20100;
    public static $DEPOSIT_SERVICE_ADD_LOSS_APPLY_ERROR = 20101;
    public static $DEPOSIT_SERVICE_ADD_APPLY_ATTACHMENT_ERROR = 20102;
    public static $DEPOSIT_SERVICE_ADD_APPLY_DEPOSIT_RETURN_ERROR = 20103;
    public static $DEPOSIT_SERVICE_DEPOSIT_EDIT_ERROR = 20104;
    public static $DEPOSIT_SERVICE_DEPOSIT_EDIT_ATTACHMENT_ERROR = 20105;
    /*********************  支票管理-通用配置: 错误码区间 20100 ~ 20199 end **********************/


    /********************* 资产领用申请: 错误码区间 18000 ~ 18999 start **********************/
    public static $MATERIAL_ASSET_APPLY_ADD_ERROR = 18000;
    public static $MATERIAL_ASSET_APPLY_CANCEL_ERROR = 18001;
    public static $MATERIAL_ASSET_APPLY_REJECT_ERROR = 18002;
    public static $MATERIAL_ASSET_APPLY_PASS_ERROR = 18003;
    /********************* 资产领用申请: 错误码区间 18000 ~ 18999 end **********************/

    /********************* 资产转移: 错误码区间 20200 ~ 20299 start **********************/
    public static $ASSET_TRANSFER_ASSETS_UPDATE_ERROR = 20201;
    public static $ASSET_TRANSFER_ACCOUNT_BATCH_ADD_ERROR = 20202;
    public static $ASSET_TRANSFER_ACCOUNT_LOG_ADD_ERROR = 20203;
    public static $ASSET_TRANSFER_CANCEL_ASSETS_UPDATE_ERROR = 20204;
    public static $ASSET_TRANSFER_CANCEL_ACCOUNT_LOG_ERROR = 20205;
    public static $ASSET_TRANSFER_SAVE_ASSET_LOG_ERROR = 20206;
    public static $ASSET_TRANSFER_SAVE_ASSET_ERROR = 20207;
    public static $ASSET_TRANSFER_SAVE_LOG_ERROR = 20208;
    public static $ASSET_TRANSFER_REMIND_REDIS_EXPIRE_ERROR = 20209;
    public static $ASSET_TRANSFER_REMIND_REDIS_SADD_ERROR = 20210;
    public static $ASSET_TRANSFER_MY_ASSETS_EXPORT_ERROR = 20211;
    public static $ASSET_TRANSFER_RECEIVER_DETAIL_ERROR = 20212;


    /********************* 资产转移: 错误码区间 20200 ~ 20299 end **********************/

    /********************* 行政工单-通用配置: 错误码区间 20200 ~ 20299 start **********************/
    public static $ADMINISTRATION_TICKET_SAVE_ERROR = 20300;
    public static $ADMINISTRATION_TICKET_LOG_ADD_ERROR = 20301;
    public static $ADMINISTRATION_TICKET_ATTACHMENTS_ADD_ERROR = 20302;
    public static $ADMINISTRATION_ORDER_SAVE_ERROR = 20302;


    /*********************  支票管理-通用配置: 错误码区间 20100 ~ 20199 end **********************/


    /********************* 耗材领用，出库错误码区间 20201 ~ 20399 start **********************/
    public static $MATERIAL_WMS_APPLY_ADD_ERROR = 20201;
    public static $MATERIAL_WMS_APPLY_CANCEL_ERROR = 20202;
    public static $MATERIAL_WMS_APPLY_REJECT_ERROR = 20203;
    public static $MATERIAL_WMS_APPLY_PASS_ERROR = 20204;
    public static $MATERIAL_WMS_APPLY_BATCH_AUDIT_EDIT_ERROR = 20205;
    public static $MATERIAL_WMS_OUT_STORAGE_ADD_ERROR = 20301;
    public static $MATERIAL_WMS_OUT_STORAGE_CANCEL_ERROR = 20302;
    public static $MATERIAL_WMS_OUT_STORAGE_REJECT_ERROR = 20303;
    public static $MATERIAL_WMS_OUT_STORAGE_PASS_ERROR = 20304;
    public static $MATERIAL_WMS_OUT_STORAGE_SCM_RECALL_ERROR = 20305;
    public static $MATERIAL_WMS_OUT_STORAGE_RELATED_ERROR = 20306;
    public static $MATERIAL_WMS_OUT_STORAGE_SAVE_ERROR = 20307;
    public static $MATERIAL_WMS_OUT_STORAGE_ABOLISH_ERROR = 20308;
    /********************* 耗材领用，出库错误码区间 20201 ~ 20399 end **********************/


    /*********************  离职资产: 错误码区间 20400 ~ 20499 start **********************/
    public static $LEAVE_ASSET_EDIT_BATCH_UPDATE_DETAIL = 20400;
    public static $LEAVE_ASSET_CURRENCY_IS_EMPTY = 20401;
    public static $LEAVE_ASSET_CREATE_MAIN_ERROR = 20402;
    public static $LEAVE_ASSET_CREATE_DETAIL_ERROR = 20403;
    public static $LEAVE_ASSET_DEPARTMENT_DEAL_SAVE_ERROR = 20404;
    public static $LEAVE_ASSET_DEPARTMENT_DEAL_SUBMIT_ERROR = 20405;
    public static $LEAVE_ASSET_MANAGER_SET_DETAIL_SAVE_ERROR = 20406;
    public static $LEAVE_ASSET_MANAGER_SET_DETAIL_UPDATE_ERROR = 20407;
    public static $LEAVE_ASSET_REMIND_SET_DETAIL_SAVE_ERROR = 20408;
    public static $LEAVE_ASSET_REMIND_SET_DETAIL_UPDATE_ERROR = 20409;
    public static $LEAVE_ASSET_REMIND_SET_DETAIL_DELETE_ERROR = 20410;
    public static $LEAVE_ASSET_CANCEL_SAVE_ERROR = 20411;
    public static $LEAVE_ASSET_AUTO_TRANSFER_SAVE_ASSET = 20412;
    public static $LEAVE_ASSET_AUTO_TRANSFER_SAVE_ASSET_LOG = 20413;
    public static $LEAVE_ASSET_AUTO_TRANSFER_SAVE_TRANSFER_BATCH = 20414;
    public static $LEAVE_ASSET_AUTO_TRANSFER_SAVE_TRANSFER_LOG = 20415;
    public static $LEAVE_ASSET_MANAGER_CHANGE_SAVE_ERROR = 20416;
    public static $LEAVE_ASSET_RECORD_LOG_SAVE_ERROR = 20417;

    public static $LEAVE_ASSET_LEAVE_STAFF_NOT_EXIST = 20420;
    public static $LEAVE_ASSET_CREATE_LEAVE_STAFF_NOT_CONFORM = 20421;
    public static $LEAVE_ASSET_CREATE_LEAVE_ASSET_ALREADY_EXIST = 20422;
    public static $LEAVE_ASSET_MANAGER_CHANGE_NOT_EXIST = 20423;
    public static $LEAVE_ASSET_MANAGER_CHANGE_NOT_SET_RULE_SUPERIOR = 20424;

    /*********************  离职资产: 错误码区间 20400 ~ 20499 end **********************/

    /********************* 数据配置 配置权限 20501 ~ 20599 end **********************/
    public static $DATA_PERMISSION_MODULE_CONFIG_POWER_ADD_ERROR = 20501;
    public static $DATA_PERMISSION_MODULE_CONFIG_POWER_SAVE_ERROR = 20502;
    public static $DATA_PERMISSION_MODULE_CONFIG_POWER_DEL_ERROR = 20503;
    /********************* 数据配置 配置权限 20501 ~ 20599 end **********************/


    /********************* 采购申请单 错误码区间 20601 ~ 20699 start **********************/
    public static $PURCHASE_DATA_APPLY_ADD_ERROR = 20601;
    /********************* 采购申请单 错误码区间 20601 ~ 20699 end   **********************/

    /********************* FlashPay在线支付: 错误码区间start **********************/
    public static $VERIFY_FAILED = 23000;
    public static $OUT_TRADE_NO_ERROR = 23001;
    public static $SYNC_FAILED = 23002;
    /********************* FlashPay在线支付: 错误码区间end **********************/
    /********************* 特殊分组: 错误码区间start **********************/
    public static $STAFF_SPECIAL_GROUP_ADD_ERROR = 27000;
    public static $STAFF_SPECIAL_GROUP_DEL_ERROR = 27001;
    /********************* 特殊分组: 错误码区间end **********************/

    /********************* 员工商城: 错误码区间start **********************/
    public static $INTERIOR_GOODS_ADD_ERROR = 27500;
    public static $INTERIOR_GOODS_SAVE_ERROR = 27501;
    public static $INTERIOR_GOODS_DEL_ERROR = 27502;
    /********************* 员工商城: 错误码区间end **********************/

    /********************* 支票模块与银行流水联动: 错误码区间start **********************/
    public static $CHEQUE_APPLY_DETAIL_SAVE_ERROR = 27700;
    public static $CHEQUE_APPLY_SAVE_ERROR = 27701;
    public static $CHEQUE_APPLY_DETAIL_DEL_ERROR = 27702;
    public static $PAYMENT_STORE_RENTING_DETAIL_SAVE_ERROR = 27703;
    public static $CHEQUE_ACCOUNT_RELEASE_STATUS_SAVE_ERROR = 27704;
    public static $PAYMENT_STORE_RENTING_DETAIL_ADD_ERROR = 27705;
    public static $PAYMENT_STORE_RENTING_DETAIL_CANCEL_ERROR = 27706;
    public static $PAYMENT_PURCHASE_ADD_ERROR = 27707;
    public static $PAYMENT_PURCHASE_CANCEL_ERROR = 27708;
    public static $PAYMENT_PAYMENT_SAVE_BATCH_LINK_ERROR = 27709;
    public static $ORDINARY_PAYMENT_DATA_BATCH_LINK_ERROR = 27710;
    public static $PAYMENT_STORE_RENTING_DETAIL_DATA_ERROR = 27711;
    public static $PURCHASE_PAYMENT_DETAIL_DATA_ERROR = 27712;
    /********************* 员工商城: 错误码区间end **********************/



    /********************* 员工商城自动下单、批量下单: 错误码区间start **********************/
    public static $INTERIOR_STAFF_ENTRY_SIZE_ADD_ERROR = 27100;
    public static $INTERIOR_STAFF_ENTRY_SIZE_DEL_ERROR = 27101;
    public static $INTERIOR_STAFF_ENTRY_SIZE_EDIT_ERROR = 27102;
    public static $INTERIOR_BATCH_APPLY_ADD_ERROR = 27103;
    public static $INTERIOR_BATCH_APPLY_DEL_ERROR = 27104;
    public static $INTERIOR_BATCH_APPLY_EDIT_ERROR = 27105;
    public static $INTERIOR_BATCH_APPLY_CHECK_EDIT_ERROR = 27106;
    public static $INTERIOR_BATCH_APPLY_CHECK_DEL_ERROR = 27107;
    /********************* 员工商城自动下单、批量下单:: 错误码区间end **********************/
    
    /********************* 员工商城收款核对:错误码区间start **********************/
    public static $INTERIOR_COLLECTION_CHECK_SAVE_ERROR = 27201;


    /********************* 员工商城收款核对:错误码区间end **********************/


    /********************* 众包接口: 错误码区间start **********************/
    public static $CROWD_SOURCING_NO_ADD_ERROR = 28800;
    public static $CROWD_SOURCING_NO_SEARCH_DATA_NULL_ERROR = 28801;
    /********************* 员工商城: 错误码区间end **********************/

    /**
     * @Message ('该工号支付权限')
     */
    public static $COMMON_STAFF_PAY_AUTH_FAIL_ERROR = 30001;


    /********************* 报销管理: 错误码区间 21000 ~ 22000 start **********************/
    public static $REIMBURSEMENT_ABOVE_QUOTA_VALIDATION   = 21000;
    public static $REIMBURSEMENT_SIGN_SUBMIT_APPLY_ERROR  = 21001;
    public static $REIMBURSEMENT_SIGN_SUBMIT_STATUS_ERROR = 21002;
    public static $REIMBURSEMENT_SIGN_SUBMIT_DATA_NULL    = 21003;
    /********************* 报销管理: 错误码区间 21000 ~ 22000 end **********************/


}
