<?php

namespace App\Library\PdfWatermark;

class Pdf extends \SplFileObject
{
    private $file;

    public function __construct($file)
    {
        parent::__construct($file);

        $wrapper = new MimeStreamWrapper();
        $wrapper->setPath($file);
        $fileInfo = new \finfo(FILEINFO_MIME);

        $mimeType = @$fileInfo->file($wrapper->getStreamPath(), FILEINFO_MIME_TYPE, $wrapper->getContext());


        if ($mimeType !== 'application/pdf' && $mimeType !== 'application/octet-stream') {
            throw new \InvalidArgumentException('File does not seem to be a PDF: ' . $mimeType);
        }

        $this->file = $file;
    }
}
