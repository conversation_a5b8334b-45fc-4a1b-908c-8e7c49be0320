<?php

namespace App\Library;

use Phalcon\Db\Adapter\Pdo\Mysql;
use Phalcon\Events\Event;
use Phalcon\Events\Manager;

class Database extends Mysql
{
    public function __construct(array $descriptor, $di)
    {
        parent::__construct($descriptor);
        $this->initListener($di);
    }

    /**
     * @param $di
     */
    protected function initListener($di)
    {
        $config = $di->getConfig();

        //新建一个事件管理器
        $em = new Manager();
        //只在非线上环境才记录sql日志
        if (in_array(RUNTIME, ['dev']) && $config->application->trace_sql_log) {
            //从di中获取共享的profiler实例 分析底层sql性能，并记录日志
            $em->attach(
                'db',
                function (Event $event, $connection) use ($di) {
                    $profiler = $di->getProfiler();
                    if ($event->getType() == 'beforeQuery') {
                        //在sql发送到数据库前启动分析
                        $profiler->startProfile($connection->getSQLStatement());
                    }
                    if ($event->getType() == 'afterQuery') {
                        //在sql执行完毕后停止分析
                        $profiler->stopProfile();
                        //获取分析结果
                        $profile      = $profiler->getLastProfile();
                        $sql          = $profile->getSQLStatement();
                        $params       = $connection->getSqlVariables();
                        $connectionId = $connection->getConnectionId();
                        (is_array($params) && count($params)) && $params = json_encode($params);
                        $executeTime = $profile->getTotalElapsedSeconds();
                        //记录sql日志
                        $logger = $di->getSqlLogger();
                        $logger->debug("{$connectionId}: {$sql} ({$params})({$executeTime})");
                    }
                }
            );
            $this->setEventsManager($em);
        }
    }
}