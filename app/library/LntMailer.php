<?php

namespace App\Library;

use app\models\backyard\StaffPayrollCompanyInfoModel;
use Phalcon\Mvc\User\Component;

class LntMailer extends Mailer
{
    private $mailer;

    public function __construct($config)
    {
        $transport = (new \Swift_SmtpTransport($config->lnt_mail->smtp_host, $config->lnt_mail->smtp_port, 'ssl'))
            ->setUsername($config->lnt_mail->username)
            ->setPassword($config->lnt_mail->password);

        $this->mailer = new \Swift_Mailer($transport);
    }

    /**
     * @param $to
     * @param string $title
     * @param string $content
     * @param array $attachments
     * @param array $cc
     * @return int
     */
    public function send($to, $title = '', $content = '', $attachments = [], $cc = [])
    {
        //公司名称 和 注册号 落款
        $companyConfigInfo            = StaffPayrollCompanyInfoModel::findFirst([
            'columns'    => 'company_name, company_registration_no,business_registration_number',
            'conditions' => 'id = :id:',
            'bind'       => ['id' => StaffPayrollCompanyInfoModel::LNT],
        ]);
        $companyConfigInfo            = empty($companyConfigInfo) ? [] : $companyConfigInfo->toArray();
        $company_name                 = $companyConfigInfo['company_name'] ?? '';
        $business_registration_number = !empty($companyConfigInfo['business_registration_number']) ? '(' . $companyConfigInfo['business_registration_number'] . ')' : '';
        $company_registration_no      = !empty($companyConfigInfo['company_registration_no']) ? 'Company Registration No. : ' . $companyConfigInfo['company_registration_no'] . $business_registration_number : '';
        $email_signature              = '<br/><br/><br/>' . $company_name . '<br/>' . $company_registration_no;
        $content                      .= $email_signature;
        $message                      = (new \Swift_Message())
            ->setFrom(['<EMAIL>' => 'LNT'])
            ->setReplyTo('<EMAIL>')
            ->setTo($to)
            ->setSubject($title)
            ->setBody($content, 'text/html');
        if (!empty($cc)) {
            $message->setCc($cc);
        }
        if (!empty($attachments)) {
            foreach ($attachments as $attachment) {
                $message->attach(\Swift_Attachment::fromPath($attachment));
            }
        }
        if (!$this->mailer->getTransport()->ping()) {
            $this->mailer->getTransport()->stop();
            $this->mailer->getTransport()->start();
        }
        return $this->mailer->send($message);
    }


}