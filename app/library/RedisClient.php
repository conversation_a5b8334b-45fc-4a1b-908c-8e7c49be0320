<?php

namespace App\Library;

use Phalcon\Mvc\User\Component;

class RedisClient extends Component
{
    private static $instance;

    private $redisModel;


    private function __construct()
    {
        $this->redisModel = $this->getDI()->get('redis');
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || self::$instance instanceof self) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    public function getClient()
    {
        return $this->redisModel;
    }
}
