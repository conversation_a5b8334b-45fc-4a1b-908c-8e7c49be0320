<?php

namespace App\Library;

use app\models\backyard\StaffPayrollCompanyInfoModel;
use Phalcon\Mvc\User\Component;

class Mailer extends Component
{
    const MAIL_QUEUE = 'EMAIL_QUEUE';
    private $mailer ;

    public function __construct($config)
    {
        $transport = (new \Swift_SmtpTransport($config->mail->smtp_host, $config->mail->smtp_port, 'ssl'))
            ->setUsername($config->mail->username)
            ->setPassword($config->mail->password)
        ;

        // Create the Mailer using your created Transport
        $this->mailer = new \Swift_Mailer($transport);
        /*
        //Config Mailer
        $this->mailer = new PHPMailer(true);
        $this->mailer->Host = $config->mail->smtp_host;
        $this->mailer->Port = $config->mail->smtp_port;
        $this->mailer->Username = $config->mail->username;
        $this->mailer->Password = $config->mail->password;
        $this->mailer->SMTPAuth = true;
        $this->mailer->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS;
        $this->mailer->isSMTP();
        $this->mailer->Encoding = PHPMailer::ENCODING_BASE64;
        $this->mailer->CharSet = PHPMailer::CHARSET_UTF8;
        */
    }

    /**
     * @param $to
     * @param string $title
     * @param string $content
     * @param array $attachments
     * @param array $cc
     * @return int
     */
    public function send($to, $title = '', $content = '', $attachments = [], $cc = [])
    {
        $content = $this->getEmailSignature($content);
        $message = (new \Swift_Message())
            ->setFrom([$this->mailer->getTransport()->getUserName() => 'FlashExpress'])
            ->setReplyTo('<EMAIL>')
            ->setTo($to)
            ->setSubject($title)
            ->setBody($content, 'text/html')
        ;
        if (!empty($cc)) {
            $message->setCc($cc);
        }
        if (!empty($attachments)) {
            foreach ($attachments as $attachment) {
                $message->attach(\Swift_Attachment::fromPath($attachment));
            }
        }
        if (!$this->mailer->getTransport()->ping()) {
            $this->mailer->getTransport()->stop();
            $this->mailer->getTransport()->start();
        }
        return $this->mailer->send($message);
        /*
        //Content
        //$this->mailer->isHTML(true);
        $this->mailer->Subject = $title;
        $this->mailer->msgHTML($content);
        $this->mailer->AltBody = $content;
        if(!empty($attachments)){
            foreach($attachments as $a){
                $this->mailer->addAttachment($a);
            }
        }
        //Recipients
        $this->mailer->setFrom('<EMAIL>', 'FlashExpress');
        if (is_array($to)){
            foreach ($to as $t){
                $this->mailer->addAddress($t);
            }
        }else{
            $this->mailer->addAddress($to);
        }
        if (!empty($cc)){
            foreach($cc as $c){
                $this->mailer->addCC($c);
            }
        }

        $result = $this->mailer->send();
        $this->mailer->clearAllRecipients();

        return $result;
        */
    }

    /**
     * @param $to
     * @param string $title
     * @param string $content
     * @param array $attachments
     * @param array $cc
     * @return mixed
     */
    public function sendAsync($to, $title = '', $content = '', $attachments = [], $cc = [])
    {
        $task = [
            'emails' =>$to,
            'subject' => $title,
            'content' => $content,
            'attachments' => $attachments,
            'cc' => $cc,
        ];

        return $this->redis->lpush(self::MAIL_QUEUE, json_encode($task));
    }

    /**
     *
     */
    public function receiveAsync()
    {
        $task = $this->redis->rpop(self::MAIL_QUEUE);
        if ($task) {
            $task = json_decode($task, true);
        }

        return $task;
    }

    /**
     * 查看邮件元素个数
     * @return mixed
     */
    public function getQueueLen()
    {
        return $this->redis->llen(self::MAIL_QUEUE);
    }

    /**
     * 查看队列元素
     * @return mixed
     */
    public function getQueueItem()
    {
        return $this->redis->lrange(self::MAIL_QUEUE, 0, -1);
    }

    /**
     * 清空邮件队列
     * @return mixed
     */
    public function clearQueue()
    {
        return $this->redis->ltrim(self::MAIL_QUEUE, -1, 0);
    }

    /**
     * @param string $content
     * @return string
     */
    public function getEmailSignature(string $content): string
    {
        if (get_country_code() == 'MY') {
            $companyConfigInfo       = StaffPayrollCompanyInfoModel::findFirst([
                'columns'    => 'company_name, company_registration_no,business_registration_number',
                'conditions' => 'id = :id:',
                'bind'       => ['id' => StaffPayrollCompanyInfoModel::FLASH_EXPRESS],
            ]);
            $companyConfigInfo       = empty($companyConfigInfo) ? [] : $companyConfigInfo->toArray();
            $company_name            = $companyConfigInfo['company_name'] ?? '';
            $business_registration_number = !empty($companyConfigInfo['business_registration_number']) ? '('.$companyConfigInfo['business_registration_number'].')' : '';
            $company_registration_no = !empty($companyConfigInfo['company_registration_no']) ? 'Company Registration No. : '.$companyConfigInfo['company_registration_no'] . $business_registration_number: '';
            $email_signature         = '<br/><br/><br/>' . $company_name . '<br/>' . $company_registration_no;
            $content                 .= $email_signature;
        }
        return $content;
    }
}