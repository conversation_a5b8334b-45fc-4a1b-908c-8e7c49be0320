<?php
namespace App\Library\CInterface;

interface BankFlowModelInterface
{
    /**
     * 通过单号获得Model
     * @param string $no
     * @return mixed
     */
    public function getModelByNo(string $no);

    /**
     * 通过单号数组获得Model=只获得审核通过，待支付的
     * @param array $no
     * @return mixed
     */
    public function getModelByNos(array $no);


    /**
     * 通过model获得银行流水格式化数据
     * @return mixed
     */
    public function getFormatData();


    /**
     * model 关联银行流水  bank_name=银行名字  bank_account=银行账号   date=银行流水日期   签收日期=银行流水日期
     * @param array $data
     * @return mixed
     */
    public function link(array $data);



    /**
     * model 批量关联银行流水
     * @param array ids  必选是intval后的
     * @param array $data
     * @return mixed
     */
    public function batch_link($ids,$data);


    /**
     * 关联银行流水撤销
     * @param array $user
     * @return mixed
     */
    public function cancel(array $user);



    /**
     * model 批量确认银行流水
     * @param array ids  必选是intval后的
     * @param array $data
     * @return mixed
     */
    public function batch_confirm($ids,$data);
}