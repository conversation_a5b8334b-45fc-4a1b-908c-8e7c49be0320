<?php
namespace App\Library\CInterface;

interface ParcelClaimModelInterface
{

    /**
     * 通过单号数组获得Model=只获得审核通过，待支付的
     * @param array $no
     * @return mixed
     */
    public function getModelByNos(array $no);


    /**
     * 通过model获得银行流水格式化数据
     * @return mixed
     */
    public function getFormatData();


    /**
     * model 提交模型
     * @param array $data
     * @return mixed
     */
    public function link(array $data);

    /**
     * 关联银行流水撤销
     * @param array $user
     * @return mixed
     */
    public function cancel(array $user);

    /**
     * model 批量关联银行流水
     * @param array ids  必选是intval后的
     * @param array $data
     * @return mixed
     */
    public function batch_link($ids,$data);

    /**
     * model 批量确认银行流水
     * @param array ids  必选是intval后的
     * @param array $data
     * @return mixed
     */
    public function batch_confirm($ids,$data);
}
