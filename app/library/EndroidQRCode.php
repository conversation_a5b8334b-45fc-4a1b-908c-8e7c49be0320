<?php
/**
 * 二维码
 */

namespace App\Library;

use Endroid\QrCode\QrCode;
use Endroid\QrCode\ErrorCorrectionLevel;
use App\Library\Exception\BusinessException;

class EndroidQRCode
{
    private $text   = '';
    private $size   = '';
    private $margin = '';

    private $logo_path   = '';
    private $logo_width  = '';
    private $logo_height = '';

    public function __construct($text = '', $size = 300, $margin = 10)
    {
        $this->text   = $text;
        $this->size   = $size;
        $this->margin = $margin;
        return $this;
    }

    public function setLogo(string $path, int $width = 10, int $height = 10): self
    {
        $this->logo_path   = $path;
        $this->logo_width  = $width;
        $this->logo_height = $height;
        return $this;
    }

    public function generate()
    {
        if (empty($this->text)) {
            throw new BusinessException('QR Code Text cannot be empty', ErrCode::$BUSINESS_ERROR);
        }

        $qrCode = new QrCode($this->text);
        $qrCode->setSize($this->size);
        $qrCode->setMargin($this->margin);
        $qrCode->setErrorCorrectionLevel(ErrorCorrectionLevel::HIGH());

        if (!empty($this->logo_path)) {
            $qrCode->setLogoPath($this->logo_path);
            $qrCode->setLogoWidth($this->logo_width);
            $qrCode->setLogoHeight($this->logo_height);
        }

        return $qrCode;
    }

    public function getBase64Image(): string
    {
        return $this->generate()->writeDataUri();
    }


}