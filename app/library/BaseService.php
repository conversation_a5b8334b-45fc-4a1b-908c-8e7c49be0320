<?php
namespace App\Library;

use App\Library\Enums\WarehouseEnums;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Models\oa\ContractElectronicModel;
use App\Models\oa\ContractElectronicSignInfoModel;
use App\Models\oa\LoanReturnModel;
use App\Modules\Budget\Models\BudgetAdjustModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Contract\Models\Contract;
use App\Modules\CrmQuotation\Models\CrmQuotationApplyModel;
use App\Modules\Deposit\Models\DepositReturnModel;
use App\Modules\Deposit\Models\DepositModel;
use App\Modules\Loan\Models\Loan;
use App\Modules\OrdinaryPayment\Models\OrdinaryPayment;
use App\Modules\Pay\Models\Payment;
use App\Modules\Payment\Models\PaymentStoreRenting;
use App\Modules\AccessData\Models\AccessDataModel;
use App\Modules\Purchase\Models\PurchaseApply;
use App\Modules\Purchase\Models\PurchaseOrder;
use App\Modules\Purchase\Models\PurchasePayment;
use App\Modules\Purchase\Models\PurchaseSampleModel;
use App\Modules\Reimbursement\Models\Reimbursement;
use App\Modules\ReserveFund\Models\ReserveFundApply;
use App\Modules\ReserveFund\Models\ReserveFundReturn;
use App\Modules\Salary\Models\PaySalaryApply;
use App\Modules\User\Models\StaffInfoModel;
use App\Modules\Wages\Models\WagesModel;
use App\Util\RedisKey;
use GuzzleHttp\Exception\GuzzleException;
use Phalcon\Mvc\User\Component;
use Phalcon\Translate\Adapter\NativeArray;
use App\Modules\Vendor\Models\Vendor;
use App\Library\Enums\GlobalEnums;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Contract\Models\ContractStoreRentingModel;
use App\Models\backyard\HrJobTitleModel;
use App\Models\backyard\SysDepartmentModel;
use App\Models\backyard\SysStoreModel;
use Exception;

/**
 * Class BaseService
 * @property Logger $logger
 * @package App\Library
 */
class BaseService extends Component
{
    protected static $t;
    protected static $language;
    protected static $default_language = 'en';
    protected static $allow_language = ['en', 'th', 'cn', 'zh', 'zh-CN'];
    /**
     * 部门
     * @var array
     */
    protected static $departmentNameBox = [];
    /**
     * 职位
     * @var array
     */
    protected static $jobTitleNameBox = [];
    /**
     * 网点
     * @var array
     */
    protected static $storeNameBox = [];

    /**
     * 设置语言
     * @param $locale
     * @return void
     */
    public static function setLanguage($locale)
    {
        static::$language = $locale ? $locale : 'zh-CN';
        static::$t = self::getTranslation(static::$language);
    }

    /**
     * 加载语言包
     * @param string $lang
     * @return NativeArray 语言包
     */
    public static function getTranslation($lang='th')
    {
        if($lang == 'zh')
            $lang = 'zh-CN';
        $translationFile = APP_PATH . "/language/" . $lang . ".php";

        if (file_exists($translationFile)) {
            $messages = include $translationFile;
        } else {
            $messages = include APP_PATH . "/language/en.php";
        }

        return new NativeArray(
            [
                "content" =>  $messages,
            ]
        );
    }

    /**
     * @param $header
     * @param $rows
     * @param string $fileName
     * @param bool $is_return_oss_file_data
     * @return array
     * @throws BusinessException
     * @throws GuzzleException
     */
    public function exportExcel($header, $rows, $fileName = 'excel.xlsx', $is_return_oss_file_data = false)
    {
        if (!strstr($fileName,'.xlsx')){
            $fileName = $fileName.'.xlsx';
        }

        $config = [
            'path' => sys_get_temp_dir(),
        ];

        $excel = new \Vtiful\Kernel\Excel($config);

        // 此处会自动创建一个工作表
        $fileObject = $excel->fileName($fileName);
        $filePath = $fileObject->header($header)->data($rows)->output();
        $file = OssHelper::uploadFile($filePath);
        $url = $file['object_url'];

        // 输出文件
        $result = ['code' => ErrCode::$SUCCESS, 'message' => 'success', 'data' => $url];

        if ($is_return_oss_file_data) {
            $result['oss_file_data'] = $file;
        }

        return $result;
    }

    /**
     * 检查锁
     *
     * @param String $key
     * @return Bool
     */
    public function checkLock($key){
        return RedisClient::getInstance()->getClient()->exists($key);
    }

    /**
     * 加锁
     *
     * @param String $key
     * @param Void $val
     * @param Integer $time
     * @return Bool
     */
    public function setLock($key,$val = 1,$time = 3600)
    {
        return RedisClient::getInstance()->getClient()->setex($key, $time , $val);
    }

    /**
     * 获取key值
     * @param $key
     * @return mixed
     */
    public function getCache($key)
    {
        return RedisClient::getInstance()->getClient()->get($key);
    }

    /**
     * 删除key值
     * @param $key
     * @return mixed
     */
    public function delCache($key)
    {
        return RedisClient::getInstance()->getClient()->delete($key);
    }

    /**
     * 设置key值
     *
     * @param string $key
     * @param string $value 字符串
     * @param int $expiration
     * @return mixed
     */
    public function setCache(string $key, string $value = '', int $expiration = 60)
    {
        return RedisClient::getInstance()->getClient()->set($key, $value, $expiration);
    }

    /**
     * 解锁
     *
     * @param String $key
     * @return Bool
     */
    public function unLock($key)
    {
        return RedisClient::getInstance()->getClient()->delete($key);
    }

    /**
     * 写入队列
     *
     * @param string $queue_name
     * @param array $data
     * @return mixed
     */
    public function setRedisQueueData(string $queue_name, array $data)
    {
        return RedisClient::getInstance()->getClient()->lpush($queue_name, json_encode($data));
    }

    /**
     * 读取队列
     *
     * @param string $queue_name
     * @return mixed
     */
    public function getRedisQueueData(string $queue_name)
    {
        $data = RedisClient::getInstance()->getClient()->rpop($queue_name);
        return !empty($data) ? json_decode($data, true) : $data;
    }

    /**
     * 发送邮件给审核者
     * @param $request
     * @param $nodeAuditors
     * @param integer $flag   0待审核的，1是待支付
     * @return string|true
     */
    public function sendEmailToAuditors($request, $nodeAuditors, $flag = 0)
    {
        $from_channel = $flag ? 'pay' : 'workflow';

        try {
            if (empty($request)) {
                throw new ValidationException("no request");
            }

            // 只在测试环境，给测试人员发邮件。
            $test_flag = in_array(env('runtime'), ['dev','test','tra', 'training']);

            // 开发/测试/tra环境收件人取指定配置的
            if ($test_flag) {
                $email_addressee_code = $flag ? GlobalEnums::EMAIL_NOTICE_WAITING_PAY_CODE : GlobalEnums::EMAIL_NOTICE_WAITING_AUDIT_CODE;
                $email_addressee_val = EnvModel::getEnvByCode($email_addressee_code);
                $emails = !empty($email_addressee_val) ? explode(',', $email_addressee_val) : [];
            } else {
                // 生产取工号对应的
                $emails = (new StaffInfoModel())->getEmails($nodeAuditors);
            }

            // 如果收件人邮箱为空，则不发
            if (empty($emails)) {
                throw new ValidationException("no emails addressee, stop send. flag[$flag], biz_type[{$request->biz_type}], node_auditors[".json_encode($nodeAuditors, JSON_UNESCAPED_UNICODE).']');
            }

            // 语言初始化
            $orgLang = self::$language;

            // 邮件模板语言定义
            $language_setting = $this->getNoticeEmailTemplateLang();
            $first_lang = $language_setting['first_lang'];
            $second_lang = $language_setting['second_lang'];

            // 切为系统语言
            self::setLanguage($orgLang);

            // 各环境各国家dashboard页地址
            $url = EnvModel::getEnvByCode(GlobalEnums::OA_DASHBOARD_PAGE_URL_CODE);
            $url = !empty($url) ? $url : GlobalEnums::OA_DASHBOARD_PAGE_URL_DEFAULT;

            switch ($request->biz_type) {
                //合同(包含销售合同)
                case Enums::WF_CONTRACT_TYPE1:
                case Enums::WF_CONTRACT_TYPE20:
                case Enums::WF_CONTRACT_TYPE21:
                case Enums::WF_CONTRACT_TYPE22:
                case Enums::WF_CONTRACT_TYPE23:
                case Enums::WF_CONTRACT_TYPE24:
                    $item = Contract::findFirst([
                        'id = :id:',
                        'bind' => ['id' => $request->biz_value]
                    ]);
                    if (empty($item)) {
                        throw new \Exception("no item, type = {$request->biz_type}, id = {$request->biz_value}", ErrCode::$SYSTEM_ERROR);
                    }
                    $item = $item->toArray();

                    // 合同分类map
                    $contract_category_map = EnumsService::getInstance()->getContractCategorysMap();

                    // 直属分类信息
                    $contract_category_info = $contract_category_map[$item['template_id']] ?? [];
                    $template_id = $contract_category_info['translation_key'] ?? '';

                    // 若直属分类是二级分类, 则需拼接一级分类
                    if (isset($contract_category_info['level']) && $contract_category_info['level'] > 1) {
                        $parent_template_id = $contract_category_map[$contract_category_info['ancestry_id']]['translation_key'];

                        $item['template_title'] = $first_lang->_($parent_template_id) . '/' . $first_lang->_($template_id);
                        $item['template_second_title'] = $second_lang->_($parent_template_id) . '/' . $second_lang->_($template_id);
                    } else {
                        $item['template_title'] = $first_lang->_($template_id);
                        $item['template_second_title'] = $second_lang->_($template_id);
                    }

                    $first_title = $item['template_title'] . '-' . $item['cno'];
                    $second_title = $item['template_second_title'] . '-' . $item['cno'];
                    $whereKey = "email_contract_where";
                    break;
                //借款
                case Enums::WF_LOAN_TYPE:
                    $item = Loan::findFirst([
                        'id = :id:',
                        'bind' => ['id' => $request->biz_value]
                    ]);

                    if (empty($item)) {
                        throw new \Exception("no item,type=8,id=" . $request->biz_value);
                    }
                    $first_title = $first_lang->_('loan_name', ["year" => date("Y", strtotime($item->created_at)), "month" => date("m", strtotime($item->created_at))]) . "-" . $item->lno;
                    $second_title = $second_lang->_('loan_name', ["year" => date("Y", strtotime($item->created_at)), "month" => date("m", strtotime($item->created_at))]) . "-" . $item->lno;
                    $whereKey = "email_loan_where";
                    break;
                //采购申请单
                case Enums::WF_PURCHASE_APPLY:
                    $item = PurchaseApply::findFirst([
                        'id=:id:',
                        'bind'=>['id'=>$request->biz_value]
                    ]);
                    if (empty($item)) {
                        throw new \Exception("no item,type=9,id=" . $request->biz_value);
                    }

                    $first_biz_title = $first_lang->_("purchase_apply_name");
                    $second_biz_title = $second_lang->_("purchase_apply_name");

                    $first_title = $first_biz_title . '-' . $item->pano;
                    $second_title = $second_biz_title . '-' . $item->pano;
                    $whereKey = "email_purchase_where";
                    break;
                //采购申请单
                case Enums::WF_PURCHASE_ORDER:
                    $item = PurchaseOrder::findFirst([
                        'id=:id:',
                        'bind'=>['id'=>$request->biz_value]
                    ]);
                    if (empty($item)) {
                        throw new \Exception("no item,type=10,id=" . $request->biz_value);
                    }

                    $first_biz_title = $first_lang->_("purchase_order_name");
                    $second_biz_title = $second_lang->_("purchase_order_name");

                    $first_title = $first_biz_title . '-' . $item->pono;
                    $second_title = $second_biz_title . '-' . $item->pono;
                    $whereKey = "email_purchase_where";
                    break;
                //采购付款申请单
                case Enums::WF_PURCHASE_PAYMENT:
                    $item = PurchasePayment::findFirst([
                        'id=:id:',
                        'bind'=>['id'=>$request->biz_value]
                    ]);
                    if (empty($item)) {
                        throw new \Exception("no item,type=11,id=" . $request->biz_value);
                    }

                    $first_biz_title = $first_lang->_("purchase_payment_name");
                    $second_biz_title = $second_lang->_("purchase_payment_name");

                    $first_title = $first_biz_title . '-' . $item->ppno;
                    $second_title = $second_biz_title . '-' . $item->ppno;
                    $whereKey = "email_purchase_where";
                    break;
                //采购样品确认
                case Enums::WF_PURCHASE_SAMPLE:
                    $item = PurchaseSampleModel::findFirst([
                        'id=:id:',
                        'bind'=>['id'=>$request->biz_value]
                    ]);
                    if (empty($item)) {
                        throw new \Exception("no item,type=92,id=" . $request->biz_value);
                    }

                    $first_biz_title = $first_lang->_("purchase_sample_name");
                    $second_biz_title = $second_lang->_("purchase_sample_name");

                    $first_title = $first_biz_title . '-' . $item->no;
                    $second_title = $second_biz_title . '-' . $item->no;
                    $whereKey = "email_purchase_where";
                    break;
                //报销
                case Enums::WF_REIMBURSEMENT_TYPE:
                    $item = Reimbursement::findFirst([
                        'id = :id:',
                        'bind' => ['id' => $request->biz_value]
                    ]);

                    if (empty($item)) {
                        throw new \Exception("no item,type=13,id=" . $request->biz_value);
                    }

                    $first_title = $first_lang->_('email_reimbursement_name') . "-" . $item->no;
                    $second_title = $second_lang->_('email_reimbursement_name'). "-" .$item->no;
                    $whereKey = "email_reimbursement_where";
                    break;
                case Enums::WF_WAGES_TYPE:
                    $item = WagesModel::findFirst([
                        'id = :id:',
                        'bind' => ['id' => $request->biz_value]
                    ]);

                    if (empty($item)) {
                        throw new \Exception("no item,type=15,id=" . $request->biz_value);
                    }

                    $first_title = $first_lang->_('email_wages_name') . "-" . $item->no;
                    $second_title = $second_lang->_('email_wages_name'). "-" .$item->no;

                    if($flag==0){
                        $whereKey = "email_wages_where";
                    }else{
                        $whereKey = "email_wages_where_pay";
                    }
                    break;

                // 网点租房付款
                case Enums::WF_PAYMENT_STORE_RENTING_TYPE:
                    $item = PaymentStoreRenting::findFirst([
                        'id = :id:',
                        'bind' => ['id' => $request->biz_value]
                    ]);

                    if (empty($item)) {
                        throw new \Exception("no item, type = 29, id = " . $request->biz_value);
                    }

                    $first_title = $first_lang->_('payment_store_renting_email_apply_name') . "-" . $item->apply_no;
                    $second_title = $second_lang->_('payment_store_renting_email_apply_name') . "-" . $item->apply_no;

                    if ($flag == 0) {
                        $whereKey = "payment_store_renting_email_where";
                    } else {
                        $whereKey = "payment_store_renting_email_pay_where";
                    }

                    break;

                // 普通付款
                case Enums::ORDINARY_PAYMENT_BIZ_TYPE:
                    $item = OrdinaryPayment::findFirst([
                        'id = :id:',
                        'bind' => ['id' => $request->biz_value]
                    ]);

                    if (empty($item)) {
                        throw new \Exception("no item, type = 30, id = " . $request->biz_value);
                    }

                    $first_title = $first_lang->_('ordinary_payment_pdf_file_name') . "-" . $item->apply_no;
                    $second_title = $second_lang->_('ordinary_payment_pdf_file_name') . "-" . $item->apply_no;
                    if($flag==0){
                        $whereKey = "ordinary_payment_audit_email_where";//审核人员操作位置提醒的翻译key
                    }else{
                        $whereKey = "ordinary_payment_pay_email_where"; //支付人员操作位置提醒的翻译key
                    }

                    break;
                //押金归还
                case Enums::DEPOSIT_RETURN_BIZ_TYPE:
                    $item = DepositReturnModel::findFirst([
                        'id=:id:',
                        'bind' => ['id' => $request->biz_value]
                    ]);
                    if (empty($item)) {
                        throw new \Exception("no item,type=".Enums::DEPOSIT_RETURN_BIZ_TYPE.",id=" . $request->biz_value);
                    }
                    $deposit_item = DepositModel::findFirst([
                        'id=:id:',
                        'bind' => ['id' => $item->deposit_id]
                    ]);
                    if (empty($deposit_item)) {
                        throw new \Exception("no item,type=".Enums::DEPOSIT_RETURN_BIZ_TYPE.",id=" . $request->biz_value);
                    }

                    $first_title = $first_lang->_('deposit_return_pdf_file_name') . "-" . $deposit_item->business_no;
                    $second_title = $second_lang->_('deposit_return_pdf_file_name') . "-" . $deposit_item->business_no;

                    $whereKey = "deposit_return_email_to_auditors_content";

                    break;

                // 薪资发放审批
                case Enums::WF_SALARY_APPLY:
                    $item = PaySalaryApply::findFirst([
                        'id = :id:',
                        'bind' => ['id' => $request->biz_value ]
                    ]);

                    if (empty($item)) {
                        throw new \Exception("no item, type = 43, id = " . $request->biz_value);
                    }

                    $first_title = $first_lang->_('pay_salary_apply_name') . "-" . $item->xzno;
                    $second_title = $second_lang->_('pay_salary_apply_name') . "-" . $item->xzno;
                    if($flag==0){
                        $whereKey = "pay_salary_apply_audit_email_where";//审核人员操作位置提醒的翻译key
                    }else{
                        $whereKey = "pay_salary_apply_pay_email_where"; //支付人员操作位置提醒的翻译key
                    }

                    break;

                // 取数需求工单
                case Enums::WF_ACCESS_DATA_WORK_ORDER_TYPE:
                    $item = AccessDataModel::findFirst([
                        'id = :id:',
                        'bind' => ['id' => $request->biz_value]
                    ]);

                    if (empty($item)) {
                        throw new \Exception("no item, type = 50, id = " . $request->biz_value);
                    }

                    $first_title = $first_lang->_('access_data_work_order_email_apply_name') . "-" . $item->work_order_no;
                    $second_title = $second_lang->_('access_data_work_order_email_apply_name') . "-" . $item->work_order_no;
                    $whereKey = 'access_data_work_order_email_where';

                    break;
                case Enums::WF_RESERVE_FUND_APPLY:
                    $item = ReserveFundApply::findFirst([
                        'id = :id:',
                        'bind' => ['id' => $request->biz_value]
                    ]);

                    if (empty($item)) {
                        throw new \Exception("no item, type = 51, id = " . $request->biz_value);
                    }

                    $first_title = $first_lang->_('reserve_fund_email_apply_name') . "-" . $item->rfano;
                    $second_title = $second_lang->_('reserve_fund_email_apply_name') . "-" . $item->rfano;

                    if ($flag == 0) {
                        $whereKey = "reserve_fund_email_apply_where";
                    } else {
                        $whereKey = "reserve_fund_email_apply_pay_where";
                    }

                    break;
                case Enums::WF_RESERVE_FUND_RETURN:
                    $item = ReserveFundReturn::findFirst([
                        'id = :id:',
                        'bind' => ['id' => $request->biz_value]
                    ]);

                    if (empty($item)) {
                        throw new \Exception("no item, type = 52, id = " . $request->biz_value);
                    }

                    $first_title = $first_lang->_('reserve_fund_email_return_name') . "-" . $item->rrno;
                    $second_title = $second_lang->_('reserve_fund_email_return_name') . "-" . $item->rrno;
                    $whereKey = 'reserve_fund_email_return_where';
                    break;
                case Enums::WF_VENDOR_BIZ_TYPE:
                    // 供应商信息审核
                    $item = Vendor::getFirst([
                        'id = :id:',
                        'bind' => ['id' => $request->biz_value],
                        'columns' => ['vendor_id']
                    ]);
                    if (empty($item)) {
                        throw new \Exception("workflow biz data is bull, type = 59, id = " . $request->biz_value);
                    }

                    $first_title = $first_lang->_('vendor_audit_email_apply_name') . '-' . $item->vendor_id;
                    $second_title = $second_lang->_('vendor_audit_email_apply_name') . '-' . $item->vendor_id;
                    $whereKey = 'vendor_audit_email_return_where';
                    break;

                // 网点租房合同
                case Enums::WF_STORE_RENTING_CONTRACT_TYPE:
                    $item = ContractStoreRentingModel::findFirst([
                        'id = :id:',
                        'bind' => ['id' => $request->biz_value]
                    ]);

                    if (empty($item)) {
                        throw new \Exception("no item,type=14,id = " . $request->biz_value);
                    }

                    $first_title = $first_lang->_('email_store_rent_contract_name') . '-' . $item->contract_id;
                    $second_title = $second_lang->_('email_store_rent_contract_name') . '-' . $item->contract_id;
                    $whereKey = "email_store_rent_contract_audit_where";
                    break;

                // 借款归还
                case Enums::WF_LOAN_BACK_TYPE:
                    $item = LoanReturnModel::findFirst([
                        'id = :id:',
                        'bind' => ['id' => $request->biz_value]
                    ]);

                    if (empty($item)) {
                        throw new \Exception("no item,type=58,id=" . $request->biz_value);
                    }

                    $loan_model = Loan::findFirst([
                        'id = :id:',
                        'bind' => ['id' => $item->loan_id],
                        'columns' => ['lno']
                    ]);

                    $first_title = $first_lang->_('loan_back_name') . '-' . $loan_model->lno ?? '';
                    $second_title = $second_lang->_('loan_back_name') . '-' . $loan_model->lno ?? '';
                    $whereKey = "email_loan_back_audit_where";
                    break;

                // 预算导入
                case Enums::BUDGET_OB_TYPE:
                    $first_title = $first_lang->_('budget_object_import');
                    $second_title = $second_lang->_('budget_object_import');
                    $whereKey = "budget_object_import_audit_where";
                    break;

                // 预算调整
                case Enums::WF_BUDGET_ADJUST_TYPE:
                    $item = BudgetAdjustModel::findFirst([
                        'id = :id:',
                        'bind' => ['id' => $request->biz_value]
                    ]);
                    if (empty($item)) {
                        throw new \Exception("no item,type=91,id=" . $request->biz_value);
                    }
                    $first_title = $first_lang->_('budget_adjust_method_text') . '-' . $item->adjust_no;
                    $second_title = $second_lang->_('budget_adjust_method_text') . '-' . $item->adjust_no;
                    $whereKey = "budget_adjust_audit_where";
                    break;
                //支付管理（13784需求～当第一支付人提交后，提醒第二支付人待审核数据）
                case Enums::WF_PAY_TYPE:
                    $item = Payment::findFirst([
                        'id = :id:',
                        'bind' => ['id' => $request->biz_value]
                    ]);
                    if (empty($item)) {
                        throw new \Exception("no item,type=".Enums::WF_PAY_TYPE.",id=" . $request->biz_value);
                    }
                    $first_title = $first_lang->_('payment_email_title') . '-' . $item->no;
                    $second_title = $second_lang->_('payment_email_title') . '-' . $item->no;
                    $whereKey = "payment_email_where";
                    break;

                // CRM 报价审批
                case Enums::WF_CRM_QUOTATION:
                    $item = CrmQuotationApplyModel::findFirst([
                        'id = :id:',
                        'bind' => ['id' => $request->biz_value]
                    ]);
                    if (empty($item)) {
                        throw new BusinessException('no item,type=53,id=' . $request->biz_value);
                    }
                    $first_title = $first_lang->_('email_crm_quotation_name') . '-' . $item->quoted_price_list_sn;
                    $second_title = $second_lang->_('email_crm_quotation_name') . '-' . $item->quoted_price_list_sn;
                    $whereKey = 'email_crm_quotation_where';
                    break;

                // 电子合同-合同内容审核
                case Enums::WF_CONTRACT_ELECTRONIC_BIZ_TYPE:
                    $item = ContractElectronicModel::findFirst([
                        'id = :id:',
                        'bind' => ['id' => $request->biz_value]
                    ]);
                    if (empty($item)) {
                        throw new BusinessException('no item,type=' . Enums::WF_CONTRACT_ELECTRONIC_BIZ_TYPE . ',id=' . $request->biz_value);
                    }
                    $first_title = $first_lang->_('email_electronic_contract_name') . '-' . $item->no;
                    $second_title = $second_lang->_('email_electronic_contract_name') . '-' . $item->no;
                    $whereKey = 'email_electronic_contract_content_audit_path';
                    break;

                // 电子合同-客户签字审核
                case Enums::WF_CONTRACT_ELECTRONIC_REVIEW_SIGN_BIZ_TYPE:
                    $item = ContractElectronicSignInfoModel::findFirst([
                        'id = :id:',
                        'bind' => ['id' => $request->biz_value]
                    ]);
                    if (empty($item)) {
                        throw new BusinessException('no item,type=' . Enums::WF_CONTRACT_ELECTRONIC_REVIEW_SIGN_BIZ_TYPE . ',id=' . $request->biz_value);
                    }
                    $first_title = $first_lang->_('email_electronic_contract_name') . '-' . $item->electronic_no;
                    $second_title = $second_lang->_('email_electronic_contract_name') . '-' . $item->electronic_no;
                    $whereKey = 'email_electronic_contract_sign_audit_path';
                    break;
                default:
                    throw new BusinessException("no type=" . $request->biz_type);
            }

            if ($flag == 0) {
                $waitKey = "wait_audit";
                $contentKey = "email_to_auditors_content";
            } else {
                $waitKey = "wait_pay";
                $contentKey = "email_to_pay_content";
            }

            $first_wait = $first_lang->_($waitKey);
            $second_wait = $second_lang->_($waitKey);
            if ($whereKey == 'email_purchase_where') {
                $first_where = $first_lang->_($whereKey, ["name" => $first_biz_title ?? '']);
                $second_where = $second_lang->_($whereKey, ["name" => $second_biz_title ?? '']);
            } else {
                $first_where = $first_lang->_($whereKey);
                $second_where = $second_lang->_($whereKey);
            }

            $title =  $first_wait . $second_wait . "：" . $first_title . " " . $second_title;
            $first_content = $first_lang->_($contentKey, ["title" => $first_title, "where" => $first_where]);
            $second_content = $second_lang->_($contentKey, ["title" => $second_title, "where" => $second_where]);

            $html = <<<EOF
    hi,<br/>
    <span style="margin-left: 16px"></span>{$first_content}<a href="{$url}" target="_blank">{$url}</a><br/>
    <span style="margin-left: 16px"></span>{$second_content}<a href="{$url}" target="_blank">{$url}</a>

EOF;

            // 邮件发送
            $log = ["emails" => $emails, "title" => $title, "html" => $html];
            $send_res = $this->mailer->sendAsync($emails, $title, $html);
            if ($send_res) {
                $this->logger->info("sendEmailToAuditors 发送成功！[$from_channel] " . json_encode($log, JSON_UNESCAPED_UNICODE));
            } else {
                $this->logger->warning("sendEmailToAuditors 发送失败！[$from_channel] " . json_encode($log, JSON_UNESCAPED_UNICODE));
            }

        } catch (ValidationException $e) {
            $this->logger->info("sendEmailToAuditors 校验异常！[$from_channel] 原因可能是：" . $e->getMessage());

        } catch (BusinessException $e) {
            $this->logger->info("sendEmailToAuditors 业务异常！[$from_channel] 原因可能是：" . $e->getMessage());

        } catch (\Exception $e) {
            $this->logger->warning("sendEmailToAuditors 发送异常！[$from_channel] 原因可能是：" . $e->getMessage());
        }
    }

    /**
     * 获取业务编号
     * @param $prefix
     * @param $key
     * @param integer $num_length 流水号长度
     * @param string $date 日期
     * @return string
     * @throws ValidationException
     */
    public static function genSerialNo($prefix, $key, $num_length = 4, $date = '')
    {
        if (static::getSerialNo($key)) {           //有计数器
            $no = static::incrSerialNo($key);
        } else {                                    //没有计数器（一直都没有，有但是存在宕机）
            $no = static::setSerialNo($key);
        }

        // 仓库需求ID每日计数总量验证
        $warehouse_requirement_id_perday_max_count = 9999;
        if ($prefix == WarehouseEnums::REQUIREMENT_NO_PREFIX && $no > $warehouse_requirement_id_perday_max_count) {
            $curr_count = "{$warehouse_requirement_id_perday_max_count}->{$no}";
            throw new ValidationException(static::$t->_('warehouse_requirement_id_error', ['curr_count' => $curr_count]), ErrCode::$VALIDATE_ERROR);
        }

        $date = $date ? $date : date('Ymd');
        return $prefix . $date . sprintf('%0' . $num_length . 's', $no);
    }

    /**
     * 判断计数器是否存在
     * @param $key
     * @return bool|int
     */
    private static function getSerialNo($key)
    {
        return RedisClient::getInstance()->getClient()->exists($key);
    }

    /**
     * 计数器不存在的情况下
     * @param $key
     * @return bool|int
     */
    private static function setSerialNo($key)
    {
        $no = 0;
        $today = date('Y-m-d');
        $expireTime = strtotime($today.' 23:59:59');
        ++$no;
        RedisClient::getInstance()->getClient()->setex($key,$expireTime-time() , $no);

        return $no;
    }

    /**
     * 计数器存在的情况下
     * @param $key
     * @return int
     */
    private static function incrSerialNo($key)
    {
        return RedisClient::getInstance()->getClient()->incrBy($key, 1);
    }

    /**
     * 获得名字和昵称拼接的字符串
     * @param $name
     * @param $nick_name
     * @return string
     */
    public function getNameAndNickName($name, $nick_name = '')
    {
        $str = $name;
        if (!empty($nick_name)) {
            $str .= '(' . $nick_name . ')';
        }

        return $str;
    }


    /**
     * 获得员工未读消息数的redis-key
     * @param $staff_id
     * @return string
     */
    public function getStaffUnReadRedisKey($staff_id)
    {
        return RedisKey::STAFF_UN_READ_NUM . "_" . $staff_id;
    }

    /**
     * 删除员工对应的未读消息数
     * @param $staff_ids
     */
    public function delUnReadNumsKeyByStaffIds($staff_ids){
        //缓存开关，是否缓存，false不开启缓存
        $is_unread_msg_cache = env('is_unread_msg_cache',false);

        //如果不缓存，直接不删除，直接返回，不频繁删除redis
        if(empty($staff_ids) || !$is_unread_msg_cache ){
            return;
        }

        $keys = [];

        foreach ($staff_ids as $staff_id){
            $keys[] = $this->getStaffUnReadRedisKey($staff_id);
        }

        RedisClient::getInstance()->getClient()->del($keys);
    }


    /**
     * 基于方法的获取分布式原子锁
     *
     * 使用方法：
     * 在上层调用
     * $this->atomicLock(function () use () {}, someKey, 20)
     * 没有获取到锁 返回false
     * 获取到锁 执行方法体
     *
     * @param $func
     * @param string $key 全局唯一值 按业务定义
     * @param int $expire 默认十秒钟 按业务执行时间评估
     * @param bool $isDel
     * @return bool 是否获取到锁
     * @throws Exception
     */
    public function atomicLock($func, string $key, $expire = 10, $isDel = true)
    {
        $redis = $this->getDI()->get("redis");
        $LUASCRIPT = <<<LUA
local key = KEYS[1]
local ttl = ARGV[1]
if (redis.call('setnx', key, 1) == 1) then
    return redis.call('expire', key, ttl)
elseif (redis.call('ttl', key) == -1) then
    return redis.call('expire', key, ttl)
end
    return 0
LUA;
        $isLock = $redis->eval($LUASCRIPT, [$key, $expire], 1);
        if ($isLock) {
            try {
                $result = $func();
            } catch (Exception $e) {
                $redis->del($key);
                throw $e;
            }
            if ($isDel) {
                $redis->del($key);
            }
            return $result;
        }

        // 没有获得锁
        return false;
    }

    /**
     * 获取通知邮件模板语言
     * @return mixed
     */
    protected function getNoticeEmailTemplateLang()
    {
        $country_code = strtolower(env('country_code'));
        $setting_language_item = GlobalEnums::EMAIL_NOTICE_TEMPLATE_LANGUAGE[$country_code] ?? GlobalEnums::EMAIL_NOTICE_TEMPLATE_LANGUAGE['common'];

        $data = [];
        foreach ($setting_language_item as $key => $lang) {
            $data[$key] = self::getTranslation($lang);
        }

        return $data;
    }

    /**
     * 根据当前的选择的语言获取cn en 和th  方便定位语言查询
     *
     * @Date: 2022-05-17 22:48
     * @param string $default_lang
     * @return mixed|string
     * @author: peak pan
     */
    public static function getLanguage($default_lang = 'th')
    {
        $language = strtolower(self::$language);
        $lang_arr = [
            'en' => 'en',
            'th' => 'th',
            'zh-cn' => 'zh',
            'zh' => 'zh'
        ];

        return $lang_arr[$language] ?? $default_lang;
    }

    /**
     * 是否可重新提交: 原单据数据为空, 不可提交
     *
     * @param $exists_model
     * @param int $biz_id
     * @return bool
     * @throws ValidationException
     */
    public function isCanRecommitByModelData($exists_model, int $biz_id)
    {
        if (empty($exists_model)) {
            throw new ValidationException(static::$t->_('data_not_exist_by_id', ['id' => $biz_id]), ErrCode::$VALIDATE_ERROR);
        }

        return true;
    }

    /**
     * 是否可重新提交: 提交的单号 和 原单号不一致, 不可提交
     *
     * @param string $exists_no
     * @param string $recommit_no
     * @return bool
     * @throws ValidationException
     */
    public function isCanRecommitByNo(string $exists_no, string $recommit_no)
    {
        if ($exists_no != $recommit_no) {
            throw new ValidationException(static::$t->_('data_number_not_match', ['no' => $recommit_no]), ErrCode::$VALIDATE_ERROR);
        }

        return true;
    }

    /**
     * 是否可重新提交: 当前用户非原单据创建人的, 不可提交
     *
     * @param int $create_id
     * @param int $user_id
     * @return bool
     * @throws ValidationException
     */
    public function isCanRecommitByCreateId(int $create_id, int $user_id)
    {
        if ($create_id != $user_id) {
            throw new ValidationException(static::$t->_('data_create_id_not_match', ['create_id' => $user_id]), ErrCode::$VALIDATE_ERROR);
        }

        return true;
    }

    /**
     * 是否可重新提交: 当前用户非原单据申请人的, 不可提交
     *
     * @param int $apply_id
     * @param int $user_id
     * @return bool
     * @throws ValidationException
     */
    public function isCanRecommitByApplyId(int $apply_id, int $user_id)
    {
        if (get_country_code() == GlobalEnums::TH_COUNTRY_CODE && $apply_id != $user_id) {
            throw new ValidationException(static::$t->_('reimbursement_save_error_026', ['apply_id' => $user_id]), ErrCode::$VALIDATE_ERROR);
        }

        return true;
    }

    /**
     * 是否可重新提交:  非 撤回 和 驳回 状态的, 不可提交
     *
     * @param int $status 单据的审批状态
     * @param int $biz_id 业务单据ID
     * @return bool
     * @throws ValidationException
     */
    public function isCanRecommitByStatus(int $status, int $biz_id)
    {
        if (!in_array($status, [Enums::WF_STATE_REJECTED, Enums::WF_STATE_CANCEL])) {
            throw new ValidationException(static::$t->_('data_not_can_recommit', ['id' => $biz_id]), ErrCode::$VALIDATE_ERROR);
        }

        return true;
    }

    /**
     * 是否可重新提交: 原单据创建时间早于指定时间的, 不可提交
     *
     * @param string $created_at 单据创建时间
     * @return bool
     * @throws ValidationException
     */
    public function isCanRecommitByCreateTime(string $created_at)
    {
        if ($created_at < GlobalEnums::BIZ_MODULE_CAN_RECOMMIT_DATETIME) {
            throw new ValidationException(static::$t->_('data_not_can_recommit_because_old', ['start_time' => GlobalEnums::BIZ_MODULE_CAN_RECOMMIT_DATETIME]), ErrCode::$VALIDATE_ERROR);
        }

        return true;
    }

    public function format_user($user)
    {
        if (empty($user)) {
            return [];
        }

        return [
            'id' => $user->id,
            'name' => $user->name,
            'organization_type' => $user->organization_type,
            'organization_id' => $user->organization_id,
            'department' => $user->getDepartment()->name ?? '',
            'department_id' => $user->department_id,
            'job_title' => $user->getJobTitle()->name ?? '',
            'job_title_id' => $user->job_title,
            'nick_name' => $user->nick_name ?? '',
            'state' => $user->state,
            'email' => $user->email,
            'personal_email' => $user->personal_email,
            'wait_leave_state' => $user->wait_leave_state ?? 0,
            'sys_department_id' => $user->sys_department_id ?? 0, // 一级部门
            'node_department_id' => $user->node_department_id ?? 0, // 直属部门
            'identity' => $user->identity ?? '', // 身份证/护照
            'sys_store_id' => $user->sys_store_id ?? '', // 网点id
            'leave_date' => $user->leave_date ?? '', // 离职日期
            'hire_date' => $user->hire_date,//入职日期
            'hire_type' => $user->hire_type,//雇佣类型
        ];
    }

    /**
     * 支持同时添加多个工作表
     *
     * @param $header  eg [[一个标头],[第二个标头]....]
     * @param $items   eg [[第一个标题对应的数据],[第二个标题对应的数据]....]
     * @param string $fileName
     * @return array
     * @throws BusinessException
     * @throws GuzzleException
     */
    public function exportManyExcel($header, $items, $fileName = 'excel.xlsx')
    {
        if (!strstr($fileName, '.xlsx')) {
            $fileName = $fileName . '.xlsx';
        }

        $config = [
            'path' => sys_get_temp_dir(),
        ];
        $excel  = new \Vtiful\Kernel\Excel($config);

        // 此处会自动创建一个工作表
        $fileObject = $excel->fileName($fileName);
        $sheet_num  = count($header);
        if ($sheet_num > 1) {
            foreach ($header as $key => $sheet) {
                if (empty($key)) {
                    $fileObject->header($sheet)->data($items[$key]);
                } else {
                    $fileObject->addSheet('sheet' . ($key + 1))->header($sheet)->data($items[$key]);
                }
            }
        } else {
            $fileObject->header($header[0])->data($items[0]);
        }
        $filePath = $fileObject->output();
        $file     = OssHelper::uploadFile($filePath);
        $url      = $file['object_url'];

        // 输出文件
        return ['code' => ErrCode::$SUCCESS, 'message' => 'success', 'data' => $url];
    }


    /**
     * 获取部门名称
     * @param $department_id
     * @return mixed|string
     */
    public function showDepartmentName($department_id)
    {
        if (empty(self::$departmentNameBox)) {
            self::$departmentNameBox = $this->baseGetDepartmentList();
        }
        return self::$departmentNameBox[$department_id] ?? '';
    }

    /**
     * 获取职位名称
     * @param $job_title_id
     * @return mixed|string
     */
    public function showJobTitleName($job_title_id)
    {
        if (empty(self::$jobTitleNameBox)) {
            self::$jobTitleNameBox = $this->baseGetJobTitleList();
        }
        return self::$jobTitleNameBox[$job_title_id] ?? '';
    }

    /**
     * 获取网点名称
     * @param $store_id
     * @return mixed|string
     */
    public function showStoreName($store_id)
    {
        if (empty(self::$storeNameBox)) {
            self::$storeNameBox = $this->baseGetStoreList();
        }
        return self::$storeNameBox[$store_id] ?? '';
    }

    public function baseGetJobTitleList(): array
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('id, job_name as name');
        $builder->from(HrJobTitleModel::class);
        return array_column($builder->getQuery()->execute()->toArray(), 'name', 'id');
    }

    public function baseGetDepartmentList(): array
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('id, name');
        $builder->from(SysDepartmentModel::class);
        return array_column($builder->getQuery()->execute()->toArray(), 'name', 'id');
    }

    public function baseGetStoreList(): array
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('id, name');
        $builder->from(SysStoreModel::class);
        $store = array_column($builder->getQuery()->execute()->toArray(), 'name', 'id');
        $store[GlobalEnums::STORE_HEADER_OFFICE_ID] = GlobalEnums::STORE_HEADER_OFFICE_NAME;
        return $store;
    }


}
