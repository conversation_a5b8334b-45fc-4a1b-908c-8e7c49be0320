<?php

namespace App\Library;

use App\Modules\User\Services\UserService;
use Exception;
use Phalcon\Http\ResponseInterface;
use Phalcon\Mvc\Controller;

/**
 * Class BaseService
 * @property Logger $logger
 * @package App\Library
 */
class BaseController extends Controller
{
    /**
     * @var
     */
    protected $locale;

    /**
     * @var
     */
    protected $t;

    /**
     * @var array
     */
    protected $user;

    /**
     * 自定义初始化
     */
    public function initialize()
    {
        $this->locale = $this->request->getBestLanguage();
        $this->t      = BaseService::getTranslation($this->locale);
        $this->user   = $this->getLoginUser();
        BaseService::setLanguage($this->locale);
    }


    /**
     * 用户信息初始化
     */
    protected function getLoginUser()
    {
        $uid = $this->request->getUid();
        if ($uid) {
            $us   = new UserService();
            $user = $us->getUserById($uid);
            return $this->format_user($user);
        } else {
            return [];
        }
    }


    //rpc调用 无法拼接对应的字段 影响审批流调用 svc用
    public function format_user($user)
    {
        if (empty($user)) {
            return [];
        }

        return [
            'id'                 => $user->id,
            'name'               => $user->name,
            'organization_type'  => $user->organization_type,
            'organization_id'    => $user->organization_id,
            'department'         => $user->getDepartment()->name ?? '',
            'department_id'      => $user->department_id,
            'job_title'          => $user->getJobTitle()->name ?? '',
            'job_title_id'       => $user->job_title,
            'nick_name'          => $user->nick_name ?? '',
            'state'              => $user->state,
            'wait_leave_state'   => $user->wait_leave_state ?? 0,
            'sys_department_id'  => $user->sys_department_id ?? 0,  // 一级部门
            'node_department_id' => $user->node_department_id ?? 0, // 直属部门
            'identity'           => $user->identity ?? '',          // 身份证/护照
            'sys_store_id'       => $user->sys_store_id ?? '',      // 网点id
            'leave_date'         => $user->leave_date ?? '',        // 离职日期
            'hire_date'          => $user->hire_date ?? '',         //入职日期
            'hire_type'          => $user->hire_type,               //雇佣类型
        ];
    }

    /**
     * @return array
     */
    protected function getPaginationParams()
    {
        $page     = MAX(1, $this->request->get('pageNum'));
        $pageSize = $this->request->get('pageSize') ?? $this->request->get('pageSize') ?? 20;

        return [abs(intval($page)), intval($pageSize)];
    }

    /**
     * 返回请求json串
     *
     * @param $code
     * @param $message
     * @param null $data
     * @param int $statusCode
     * @return \Phalcon\Http\Response|ResponseInterface
     */
    protected function returnJson($code, $message, $data = null, $statusCode = Response::OK)
    {
        $result = [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
        if (get_runtime_env() == 'dev') {
            $result['tid'] = molten_get_traceid();
        }
        $this->response->setStatusCode($statusCode);
        $this->response->setJsonContent($result);

        return $this->response;
    }

    /**
     * @param string $stream
     * @return string
     */
    public function returnStream($stream = '')
    {
        return $stream;
    }

    /**
     * 基于方法的获取分布式原子锁
     *
     * 使用方法：
     * 在上层调用
     * $this->atomicLock(function () use () {}, someKey, 20)
     * 没有获取到锁 返回false
     * 获取到锁 执行方法体
     *
     * @param $func
     * @param string $key 全局唯一值 按业务定义
     * @param int $expire 默认十秒钟 按业务执行时间评估
     * @param bool $isDel
     * @return bool 是否获取到锁
     * @throws Exception
     */
    public function atomicLock($func, string $key, $expire = 10, $isDel = true)
    {
        $redis     = $this->getDI()->get("redis");
        $LUASCRIPT = <<<LUA
local key = KEYS[1]
local ttl = ARGV[1]
if (redis.call('setnx', key, 1) == 1) then
    return redis.call('expire', key, ttl)
elseif (redis.call('ttl', key) == -1) then
    return redis.call('expire', key, ttl)
end
    return 0
LUA;
        $isLock    = $redis->eval($LUASCRIPT, [$key, $expire], 1);
        if ($isLock) {
            try {
                $result = $func();
            } catch (Exception $e) {
                $redis->del($key);
                throw $e;
            }
            if ($isDel) {
                $redis->del($key);
            }
            return $result;
        }

        // 没有获得锁
        return false;
    }

}
