<?php

namespace App\Library;

use MQ\Model\TopicMessage;
use MQ\MQClient;
use MQ\MQConsumer;
use Phalcon\Mvc\User\Component;

/**
 * Class BaseTask
 * @property Logger $logger
 * @package App\Library
 */
class RocketMQ extends Component
{
    private $client;
    private $producer;
    private $consumer;
    private $instanceId;
    private $topic;
    private $shardingKey = null;
    private $tag = null;
    private $groupId;

    const TAG_STAFF_UPDATE_PASSWORD = 'STAFF_UPDATE_PASSWORD';//员工更新密码
    const TAG_BATCH_APPROVAL_OVERTIME = 'BATCH_APPROVAL_OVERTIME';//批量审批加班 by消费
    const TAG_BATCH_APPROVAL_OS_OVERTIME = 'BATCH_APPROVAL_OS_OVERTIME';//批量审批加班 by消费(外协)
    const TAG_HR_STAFF_UPDATE = 'HR_STAFF_UPDATE';//员工信息更新
    /**
     * message-push/
     *
     * RocketMQ constructor.
     *
     * @param $tg
     */
    public function __construct($tg)
    {
        if (!$this->getSysTopicGroup($tg)) {
            return null;
        }

        $this->client = new MQClient(
            // 设置HTTP接入域名（此处以公共云生产环境为例）
            $this->config->rocket_mq->rocket_mq_http_endpoint,
            // AccessKey 阿里云身份验证，在阿里云服务器管理控制台创建
            $this->config->rocket_mq->rocket_mq_access,
            // SecretKey 阿里云身份验证，在阿里云服务器管理控制台创建
            $this->config->rocket_mq->rocket_mq_secret
        );

        // Topic所属实例ID，默认实例为空NULL
        $this->instanceId = $this->config->rocket_mq->rocket_mq_instance_id;
    }

    /**
     * @param $tg
     * @return string
     */
    private function getSysTopicGroup($tg)
    {
        if (empty($tg)) {
            return false;
        }
        switch ($tg) {
            case 'create-approval':
                //添加批量转岗
                $this->topic = env('rmq_topic_by_create_approval', 'dev-by-create-approval');
                $this->groupId = env('rmq_groupid_by_create_approval', 'GID-dev-by-create-approval');
                break;
            case 'do-job-transfer':
                //立即转岗
                $this->topic = env('rmq_topic_by_job_transfer', 'dev-by-job-transfer');
                $this->groupId = env('rmq_groupid_by_job_transfer', 'GID-dev-by-job-transfer');
                break;
            case 'user-update-password':
                // 用户变更密码: 消费
                $this->topic = env('rmq_topic_po_async_process', 'dev-po-async-process');
                $this->tag = self::TAG_STAFF_UPDATE_PASSWORD;
                $this->groupId = env('rmq_groupid_user_update_password', 'GID-dev-oa-user-relogin');
                break;
            case 'message_push':
                //消息push
                $this->topic = env('rmq_topic_oa_reddot_message','dev-no-oa-reddot-message');
                $this->groupId = env('rmq_groupid_oa_reddot_message_push','GID-dev-no-oa-reddot-message-push');
                break;

            case 'overtime-batch-approval'://批量审批加班
                $this->topic = env('rmq_topic_no_hris_common', get_runtime() . '-no-hris-common');
                $this->groupId = env('rmq_groupid_overtime-batch-approval', 'GID-'. get_runtime() .'-overtime-batch-approval');
                $this->tag = self::TAG_BATCH_APPROVAL_OVERTIME;
                break;
            case 'os-ot-batch-approval'://批量审批加班(外协)
                $this->topic = env('rmq_topic_no_hris_common', get_runtime() . '-no-hris-common');
                $this->groupId = env('rmq_groupid_os-ot-batch-approval', 'GID-'. get_runtime() .'-os-ot-batch-approval');
                $this->tag = self::TAG_BATCH_APPROVAL_OS_OVERTIME;
                break;
            case 'update-staff-info'://更改员工信息
                $this->topic   = get_runtime() . '-po-async-process';
                $this->groupId = 'GID-' . get_runtime() . '-po-hris-staff-update';
                $this->tag     = self::TAG_HR_STAFF_UPDATE;
                break;
            default:
                return false;
        }

        return true;
    }

    public function setShardingKey($shardingKey)
    {
        $this->shardingKey = $shardingKey;
    }


    /**
     * @return MQClient
     */
    public function getClient()
    {
        return $this->client;
    }

    /**
     * @return MQConsumer
     */
    public function getConsumer()
    {
        return $this->client->getConsumer($this->instanceId, $this->topic, $this->groupId, $this->tag);
    }

    /**
     * rmq: 向指定队列 - 写入消息数据
     *
     * @param array $data 消息数据结构  示例： ['data'=>'xxxx']
     * @param int $timeInMillis
     * @return mixed
     */
    public function sendToMsg(array $data, $timeInMillis = 0)
    {
        try {
            if (empty($data)) {
                return false;
            }

            $params = [];
            $params['locale'] = 'en';
            $params['data'] = $data;

            $this->logger->info('oa rmq start: , params: ' . json_encode($params));

            // 2. send message
            $messageBody = json_encode($params);
            $messageBody = base64_encode($messageBody);
            $this->producer = $this->client->getProducer($this->instanceId, $this->topic);

            //3 topic body
            $publishMessage = new TopicMessage(
                $messageBody// 消息内容
            );
            // 设置属性
            //$publishMessage->putProperty("a", $i);
            // 设置消息KEY
            //$publishMessage->setMessageKey("MessageKey");
            // 定时消息, 定时时间为10s后
            $publishMessage->setStartDeliverTime(time() * 1000 + $timeInMillis * 1000);

            //同一个 topic 区分 tag
            if (!empty($this->tag)) {
                $publishMessage->setMessageTag($this->tag);
            }

            $result = $this->producer->publishMessage($publishMessage);
            $this->logger->info('oa rmq end: ' . $result->getMessageId() ?? '');
            return $result->getMessageId() ?? '';
        } catch (\Exception $e) {
            $this->logger->warning('oa rmq exception: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * rmq: 向指定队列 - 写入消息数据
     *
     * @param $data
     * @param int $timeInMillis
     * @return mixed
     */
    public function sendOrderlyMsg($data, $timeInMillis = 0)
    {
        try {
            if (empty($data)) {
                return false;
            }
            $messageBody = json_encode($data);
            $this->logger->info('oa rmq sendOrderlyMsg params: ' . ($messageBody));

            // 2. send message
            $this->producer = $this->client->getProducer($this->instanceId, $this->topic);

            //3 topic body
            $publishMessage = new TopicMessage(
                $messageBody// 消息内容
            );
            // 设置属性
            //$publishMessage->putProperty("a", $i);
            // 设置消息KEY
            //$publishMessage->setMessageKey("MessageKey");
            // 定时消息, 定时时间为10s后
            $publishMessage->setStartDeliverTime(time() * 1000 + $timeInMillis * 1000);

            //同一个 topic 区分 tag
            if (!empty($this->tag)) {
                $publishMessage->setMessageTag($this->tag);
            }
            //设置分区key
            if ($this->shardingKey) {
                $publishMessage->setShardingKey($this->shardingKey);
            }

            $result = $this->producer->publishMessage($publishMessage);
            $this->logger->info('oa rmq end: ' . $result->getMessageId() ?? '');
            return $result->getMessageId() ?? '';
        } catch (\Exception $e) {
            $this->logger->warning('oa rmq exception: ' . $e->getMessage());
            return false;
        }
    }




}
