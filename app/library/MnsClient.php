<?php

namespace App\Library;

use AliyunMNS\Client;
use <PERSON>yunM<PERSON>\Exception\MnsException;
use <PERSON>yunMNS\Requests\SendMessageRequest;

class MnsClient extends BaseService
{

    private $accessId;
    private $accessKey;
    private $endPoint;
    private $client;


    public function __construct($accessId, $accessKey, $endPoint)
    {
        $this->accessId  = $accessId;
        $this->accessKey = $accessKey;
        $this->endPoint  = $endPoint;
        $this->client    = new Client($this->endPoint, $this->accessId, $this->accessKey);
    }


    /**
     * mq: 向指定队列 - 写入消息数据
     *
     * @param string $queue_name 队列名称
     * @param array $data 消息数据结构  示例： ['params1'=>'value1']
     *
     * @return mixed
     */
    public function sendToMNS(string $queueName, array $data)
    {
        try {
            if (!is_array($data) || empty($data)) {
                return false;
            }
            //参数
            $params = ['locale' => 'th', 'data' => $data];
            //消息体
            $messageBody = json_encode($params);
            $messageBody = base64_encode($messageBody);
            //发送消息到队列
            $request = new SendMessageRequest($messageBody);
            $queue   = $this->client->getQueueRef($queueName, false);
            $res     = $queue->sendMessage($request);
            //消息ID
            $message_id = $res->getMessageId();
            $log_data   = [
                'request'    => $params,
                'res_msg_id' => $message_id,
            ];
            $this->logger->info("============= sendToMNS:{$queueName}:success:" . json_encode($log_data));

            return $message_id;
        } catch (MnsException $e) {

            $this->logger->error("============= sendToMNS:{$queueName}:error: " . $e->getMessage());
            return false;
        }


    }


}
