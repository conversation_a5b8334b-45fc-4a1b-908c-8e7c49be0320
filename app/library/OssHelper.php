<?php

namespace App\Library;

use App\Library\Exception\BusinessException;
use GuzzleHttp\Exception\GuzzleException;

class OssHelper
{
    /**
     * @param $path
     * @param string $prefix
     * @param $file_name
     * @param boolean $is_act_private 是否私有化 true是，false否
     * @return array
     * @throws BusinessException
     * @throws GuzzleException
     */
    public static function uploadFile($path, $prefix = '', $file_name = '', $is_act_private = false): array
    {
        $filename = !empty($file_name) ? $file_name : basename($path);

        if (env('break_away_from_ms') || $is_act_private) {
            $upload = self::uploadFileHcm($filename, $is_act_private);
        } else {
            $upload = self::uploadFileFle($filename, $prefix);
        }

        $client   = new \GuzzleHttp\Client();
        $headers = [
            'Content-Type'        => $upload['content_type'],
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];
        if ($is_act_private) {
            $headers['x-oss-object-acl'] = 'private';
        }
        $response = $client->request('PUT', $upload['put_url'], [
            'headers' => $headers,
            'body'    => file_get_contents($path),
        ]);

        if ($response->getStatusCode() == 200) {
            unlink($path);
        }

        return [
            'file_name'   => $filename,
            'bucket_name' => $upload['bucket_name'],
            'object_key'  => $upload['object_key'],
            'object_url'  => $upload['object_url'],
        ];
    }

    /**
     * 上传文件至OSS
     * @param string $filename 文件名
     * @param boolean $is_act_private 是否私有化 true是，false否
     * @return mixed
     * @throws BusinessException
     */
    public static function uploadFileHcm($filename, $is_act_private = false)
    {
        $apiClient = new ApiClient('hcm_rpc', '', 'commonBuildPutObjectUrl', 'en');
        $apiClient->setParams([['biz_type' => 'OA', 'filename' => $filename, 'options' => ['is_act_private' => $is_act_private]]]);
        $return = $apiClient->execute();
        if (isset($return['result'])) {
            return $return['result'];
        }
        throw new BusinessException('upload file failed hcm', ErrCode::$BUSINESS_ERROR);
    }

    /**
     * 下载OSS地址
     * @param string $object_key oss 对象 key 值
     * @param integer $timeout 有效期时常
     * @return mixed
     * @throws BusinessException
     */
    public static function downloadFileHcm($object_key, $timeout = 300)
    {
        $apiClient = new ApiClient('hcm_rpc', '', 'commonSignObjectUrl', 'en');
        $apiClient->setParams([['object_key' => $object_key, 'timeout' => $timeout]]);
        $return = $apiClient->execute();
        if (isset($return['result'])) {
            return $return['result'];
        }
        throw new BusinessException('download file failed hcm', ErrCode::$BUSINESS_ERROR);
    }

    /**
     * @param $filename
     * @param string $prefix
     * @return mixed
     * @throws BusinessException
     */
    public static function uploadFileFle($filename, $prefix = '')
    {
        //调用fle提供的OSS接口
        $ac = new ApiClient('fle', 'com.flashexpress.fle.svc.api.OssSvc', 'buildPutObjectUrl');
        $ac->setParams(
            [
                'WORK_ORDER',
                $filename,
                $prefix,
            ]
        );

        $return = $ac->execute();
        if (isset($return['result'])) {
            return $return['result'];
        }
        throw new BusinessException('upload file failed', ErrCode::$BUSINESS_ERROR);
    }

}
