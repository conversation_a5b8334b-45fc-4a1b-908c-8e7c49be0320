<?php

namespace App\Library;

/**
 * 角色枚举数据
 * Class enums
 */
final class RoleEnums
{
    const ROLE_HRBP = 68;            //hrbp
    const ROLE_HRIS = 41;            //HRIS管理员
    const ROLE_PAYROLL = 42;         //Payroll
    const ROLE_SUPER_ADMIN = 99;     //超管
    const ROLE_HR_SYSTEM_ADMIN = 115;//HR系统管理员
    const ROLE_SYSTEM_ADMIN = 14;    //系统管理员
    const ROLE_HR_MANAGEMENT = 17;   //HR Management

    const ROLE_STORE_SUPERVISOR  = 18;  //网点主管
    const ROLE_STORE_WAREHOUSE_KEEPER  = 2;  //网点仓管
    const ROLE_TA  = 121;           //TA
    const ROLE_AREA_MANAGER = 56; //大区经理
}
