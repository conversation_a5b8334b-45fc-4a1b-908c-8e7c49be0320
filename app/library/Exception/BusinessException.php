<?php

namespace App\Library\Exception;

use App\Library\ErrCode;

class BusinessException extends \Exception
{
    /**
     * BusinessException constructor.
     *
     * @param string $message
     * @param int|null $code
     * @param Throwable|null $previous
     */
    public function __construct(string $message = '', $code = null, ?Throwable $previous = null)
    {
        parent::__construct($message, !is_null($code) ? $code : ErrCode::$BUSINESS_ERROR, $previous);
    }

}
