<?php

namespace App\Library\Exception;

use App\Library\ErrCode;

class AuthorizationException extends \Exception
{
    /**
     * AuthorizationException constructor.
     *
     * @param string $message
     * @param int|null $code
     * @param Throwable|null $previous
     */
    public function __construct(string $message = '', $code = null, ?Throwable $previous = null)
    {
        parent::__construct($message, !is_null($code) ? $code : ErrCode::$REQUEST_FORBIDDEN_ERROR, $previous);
    }

}
