<?php

namespace App\Library\Exception;

use App\Library\ErrCode;

class AuthenticationException extends \Exception
{
    /**
     * AuthenticationException constructor.
     *
     * @param string $message
     * @param int|null $code
     * @param Throwable|null $previous
     */
    public function __construct(string $message = '', $code = null, ?Throwable $previous = null)
    {
        parent::__construct($message, !is_null($code) ? $code : ErrCode::$REQUEST_UNAUTHORIZED_ERROR, $previous);
    }

}
