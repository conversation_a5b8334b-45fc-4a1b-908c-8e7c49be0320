<?php

namespace App\Library;

use JsonRPC\Client;

class ApiClient extends BaseService
{
    /**
     * @var Client
     */
    private $client;

    /**
     * @var
     */
    private $sys;

    /**
     * @var
     */
    private $module;

    /**
     * @var
     */
    private $method;

    /**
     * @var string
     */
    private $locale;

    /**
     * @var string
     */
    private $src;

    /**
     * @var
     */
    private $params;

    /**
     * @var
     */
    private $headers = [];

    /**
     * ApiClient constructor.
     * @param $sys
     * @param $module
     * @param $method
     * @param string $locale
     * @param string $src
     */
    public function __construct($sys, $module, $method, $locale = 'zh-CN', $src = '')
    {
        $this->sys = $sys;
        $this->module = $module;
        $this->method = $method;
        $this->locale = $locale;
        $this->src = $src;
        $url = $this->getSysEndpoint($sys) . '/' . $module;
        $this->client = new Client($url);
    }

    /**
     * @param $sys
     * @return string
     */
    private function getSysEndpoint($sys)
    {
        switch ($sys) {
            case 'fle':
                $endpoint = env('fle_rpc_endpoint','http://192.168.0.228:8090/fle-svc');
                break;
            case 'by':
                $endpoint = env('by_rpc_endpoint', 'http://192.168.0.228:9090/api/_/svc/call');
                break;
            case 'hr_rpc':
                $endpoint = env('hr_rpc_endpoint', 'http://192.168.0.228:9090/svc/call');
                break;
            case 'hris':
                $endpoint = env('hris_rpc_endpoint', 'http://192.168.0.230:8091/hr-svc/call');
                break;
            case 'crm':
                $endpoint = env('crm_rpc_endpoint', 'http://192.168.7.77:8090/svc/call');
                break;
            case 'coupon_rpc':
                $endpoint = env('coupon_rpc','http://192.168.0.230:8080') . '/svc/call';
                break;
            case 'bi':
                $endpoint = env('bi_rpc_endpoint', 'http://192.168.0.230:8090');
                break;
            case 'bi_svc':
                $endpoint = env('bi_rpc_endpoint_svc', 'http://192.168.0.230:8001');
                break;
            case 'bi_new_svc':
                $endpoint = env('bi_rpc_endpoint_new_svc', 'http://192.168.0.230:8001');
                break;
            case 'sms':
                $endpoint = env('api_sms_url', 'http://192.168.0.230:8003/rpc/sms/');
                break;
            case 'school':
                $endpoint = env('school_rpc','http://192.168.7.64:7506/rpc/svc/call');
                break;
            case 'hcm_rpc':
                $endpoint = env('hcm_rpc','http://192.168.7.64:7506/rpc/svc/call');
                break;
            case 'ard_api_svc':
                $endpoint = env('ard_api_svc','http://192.168.0.230:8001');
                break;
            default:
                $endpoint = '';
        }

        return $endpoint;
    }



    /**
     * @param $params
     */
    public function setParams($params)
    {
        $this->params = $params;
    }


    /**
     * @param $params
     */
    public function setHeader($headers)
    {
        $this->headers = $headers;
    }

    /**
     * @return bool|mixed|null
     */
    public function execute()
    {
        $logger = $this->logger;

        $base_params = ['locale' => $this->locale];
        if (!empty($this->src)) {
            $base_params['src'] = $this->src;
        }

        $params = [$base_params];
        foreach ($this->params as $param){
            array_push($params, $param);
        }

        $result = [];
        try {
            // 获取Api回调数据
            $result['result'] = $this->client->execute($this->method, $params, ['src_sys' => 'OA'], molten_get_traceid(), $this->headers);
            $info = [
                'request' => [
                    'sys' => $this->sys,
                    'mod' => $this->module,
                    'method' => $this->method,
                    'params' => $params
                ],

                'response' => $result
            ];

            $logger->info($info);
        } catch (\Exception $e) {
            $logger_type = 'warning';

            $result['error'] = $e->getMessage();
            $result['code'] = $e->getCode();

            // 此类三方错误码, 无需告警
            if ((in_array($this->method, ['qrLogin']) && in_array($result['code'], [100502, 100503, 100508]))) {
                $logger_type = 'info';
            }

            $info = [
                'request' => [
                    'sys' => $this->sys,
                    'mod' => $this->module,
                    'method' => $this->method,
                    'params' => $params
                ],

                'response' => $result
            ];

            $logger->$logger_type($info);
        }

        return $result;
    }

}