<?php

namespace App\Library;

use Phalcon\Mvc\Model;

class BaseModel extends Model
{
    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        // 不对空字段进行validation校验
        self::setup(['notNullValidations' => false]);
    }

    /**
     * 获取日志类
     */
    public function getLogger()
    {
        return $this->getDI()->get('logger');
    }

    /**
     * @param array $data
     * @param string $db_di_name
     * @return bool
     */
    public function batch_insert(array $data, string $db_di_name = 'db_oa')
    {
        if (empty($data)) {
            return false;
        }
        $keys = array_keys(reset($data));
        $keys = array_map(function ($key) {
            return "`{$key}`";
        }, $keys);
        $keys = implode(',', $keys);
        $sql  = "INSERT INTO " . $this->getSource() . " ({$keys}) VALUES ";

        foreach ($data as $v) {
            $v      = array_map(function ($value) {
                if ($value === null) {
                    return 'NULL';
                } else {
                    $value = addslashes($value); //处理特殊符号，如单引号
                    return "'{$value}'";
                }
            }, $v);
            $values = implode(',', array_values($v));
            $sql    .= " ({$values}), ";
        }
        $sql = rtrim(trim($sql), ',');

        //DI中注册的数据库服务名称为"db"
        $result = $this->getDI()->get($db_di_name)->execute($sql);
        if (!$result) {
            return false;
        }
        return $result;
    }

    /**
     * 根据主键id进行批量查询
     *
     * @param $ids
     * @param string $columns
     * @param string $index
     * @return array
     */
    public function getDataByIds($ids, $columns = '*', $index = 'id'): array
    {
        if (!$ids) {
            return [];
        }

        $ret = $this->query()
            ->columns($columns)
            ->where("{$index} IN ({ids:array})")
            ->bind(
                [
                    'ids' => array_values($ids),
                ]
            )
            ->execute()
            ->toArray();

        return $ret ? array_column($ret, null, $index) : [];
    }
}