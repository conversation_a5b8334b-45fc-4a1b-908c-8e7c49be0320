<?php

namespace App\Library;

/**
 * 取数需求系统常用枚举数据
 * Class enums
 */
final class AccessDataEnums
{
    // 默认支持语言: 涉及翻译
    const DEFAULT_SUPPORT_LANGUAGE = 'zh-CN';

    // redis分布式编号前缀
    const DISTRIBUTED_NO_PREFIX = 'RD';

    // 模块标识
    const MODULE_TYPE_APPLY = 1;     // 我的申请
    const MODULE_TYPE_AUDIT = 2;     // 审批
    const MODULE_TYPE_PROCESS = 3;   // 申请处理
    const MODULE_TYPE_DATA = 4;      // 所有申请
    const MODULE_TYPE_PERMISSION = 5;// 权限配置
    static $module_type_item = [
        self::MODULE_TYPE_APPLY,
        self::MODULE_TYPE_AUDIT,
        self::MODULE_TYPE_PROCESS,
        self::MODULE_TYPE_DATA,
        self::MODULE_TYPE_PERMISSION,
    ];

    // 分页配置
    const PAGINATION_PER_PAGE_DEFAULT_LENGTH = 20;
    const PAGINATION_DEFAULT_PAGE = 1;

    // 列表数据默认展示N天内的
    const LIST_DATA_MAX_VISIBLE_DAYS = 30;
    const LIST_ALL_DATA_MAX_VISIBLE_DAYS = 90;

    // 取数模块翻译前缀标识
    const ACCESS_DATA_TRANSLATION_PREFIX = 'access_data_';

    // 数据用途
    const DATA_PURPOSE_TRANSLATION_PREFIX = self::ACCESS_DATA_TRANSLATION_PREFIX . 'data_purpose_';
    const DATA_PURPOSE_VIEW_CODE = 1;                   // 查看
    const DATA_PURPOSE_VIEW_ANALYSIS_CODE = 2;          // 查看并分析
    const DATA_PURPOSE_SEND_EXTERNAL_CUSTOMER_CODE = 3; // 发送给外部客户
    static $data_purpose_item = [
        self::DATA_PURPOSE_VIEW_CODE                   => self::DATA_PURPOSE_TRANSLATION_PREFIX . self::DATA_PURPOSE_VIEW_CODE,
        self::DATA_PURPOSE_VIEW_ANALYSIS_CODE          => self::DATA_PURPOSE_TRANSLATION_PREFIX . self::DATA_PURPOSE_VIEW_ANALYSIS_CODE,
        self::DATA_PURPOSE_SEND_EXTERNAL_CUSTOMER_CODE => self::DATA_PURPOSE_TRANSLATION_PREFIX . self::DATA_PURPOSE_SEND_EXTERNAL_CUSTOMER_CODE,
    ];

    // 数据类型
    const DATA_TYPE_TRANSLATION_PREFIX = self::ACCESS_DATA_TRANSLATION_PREFIX . 'data_type_';
    const DATA_TYPE_COMMON_CODE = 0;//普通数据
    const DATA_TYPE_CORE_CODE = 1;  //敏感数据
    static $data_type_item = [
        self::DATA_TYPE_COMMON_CODE => self::DATA_TYPE_TRANSLATION_PREFIX . self::DATA_TYPE_COMMON_CODE,
        self::DATA_TYPE_CORE_CODE   => self::DATA_TYPE_TRANSLATION_PREFIX . self::DATA_TYPE_CORE_CODE,
    ];

    // 数据涉密范围
    const CONFIDENTIAL_SCOPE_TRANSLATION_PREFIX = self::ACCESS_DATA_TRANSLATION_PREFIX . 'confidential_scope_';
    const CONFIDENTIAL_SCOPE_CUS_ADDR_CODE = 1;    // 客户地址
    const CONFIDENTIAL_SCOPE_CUS_PHONE_CODE = 2;   // 客户电话
    const CONFIDENTIAL_SCOPE_CUS_NAME_CODE = 3;    // 客户姓名
    const CONFIDENTIAL_SCOPE_CUS_EMAIL_CODE = 4;   // 客户邮箱
    const CONFIDENTIAL_SCOPE_CUS_ID_CODE = 5;      // 客户身份证
    const CONFIDENTIAL_SCOPE_COD_FEE_CODE = 100;   //COD金额
    const CONFIDENTIAL_SCOPE_SHIPPING_CODE = 101;  // 运费
    const CONFIDENTIAL_SCOPE_INSURANCE_CODE = 102; // 保险费
    const CONFIDENTIAL_SCOPE_OTHER_FEE_CODE = 103; // 其他费用
    const CONFIDENTIAL_SCOPE_STATE_CODE = 200;     // 在职状态
    const CONFIDENTIAL_SCOPE_LEVEL_CODE = 201;     // 职级
    const CONFIDENTIAL_SCOPE_RANK_CODE = 202;      // 职等
    const CONFIDENTIAL_SCOPE_SALARY_CODE = 203;    // 薪资
    const CONFIDENTIAL_SCOPE_COMMISSION_CODE = 204;// 提成
    const CONFIDENTIAL_SCOPE_STAFF_ID_CODE = 205;  //员工身份证号
    const CONFIDENTIAL_SCOPE_NULL_CODE = 10000;    // 不包含涉密信息

    static $confidential_scope_item = [
        self::CONFIDENTIAL_SCOPE_CUS_ADDR_CODE   => self::CONFIDENTIAL_SCOPE_TRANSLATION_PREFIX . self::CONFIDENTIAL_SCOPE_CUS_ADDR_CODE,
        self::CONFIDENTIAL_SCOPE_CUS_PHONE_CODE  => self::CONFIDENTIAL_SCOPE_TRANSLATION_PREFIX . self::CONFIDENTIAL_SCOPE_CUS_PHONE_CODE,
        self::CONFIDENTIAL_SCOPE_CUS_NAME_CODE   => self::CONFIDENTIAL_SCOPE_TRANSLATION_PREFIX . self::CONFIDENTIAL_SCOPE_CUS_NAME_CODE,
        self::CONFIDENTIAL_SCOPE_CUS_EMAIL_CODE  => self::CONFIDENTIAL_SCOPE_TRANSLATION_PREFIX . self::CONFIDENTIAL_SCOPE_CUS_EMAIL_CODE,
        self::CONFIDENTIAL_SCOPE_CUS_ID_CODE     => self::CONFIDENTIAL_SCOPE_TRANSLATION_PREFIX . self::CONFIDENTIAL_SCOPE_CUS_ID_CODE,
        self::CONFIDENTIAL_SCOPE_COD_FEE_CODE    => self::CONFIDENTIAL_SCOPE_TRANSLATION_PREFIX . self::CONFIDENTIAL_SCOPE_COD_FEE_CODE,
        self::CONFIDENTIAL_SCOPE_SHIPPING_CODE   => self::CONFIDENTIAL_SCOPE_TRANSLATION_PREFIX . self::CONFIDENTIAL_SCOPE_SHIPPING_CODE,
        self::CONFIDENTIAL_SCOPE_INSURANCE_CODE  => self::CONFIDENTIAL_SCOPE_TRANSLATION_PREFIX . self::CONFIDENTIAL_SCOPE_INSURANCE_CODE,
        self::CONFIDENTIAL_SCOPE_OTHER_FEE_CODE  => self::CONFIDENTIAL_SCOPE_TRANSLATION_PREFIX . self::CONFIDENTIAL_SCOPE_OTHER_FEE_CODE,
        self::CONFIDENTIAL_SCOPE_STATE_CODE      => self::CONFIDENTIAL_SCOPE_TRANSLATION_PREFIX . self::CONFIDENTIAL_SCOPE_STATE_CODE,
        self::CONFIDENTIAL_SCOPE_LEVEL_CODE      => self::CONFIDENTIAL_SCOPE_TRANSLATION_PREFIX . self::CONFIDENTIAL_SCOPE_LEVEL_CODE,
        self::CONFIDENTIAL_SCOPE_RANK_CODE       => self::CONFIDENTIAL_SCOPE_TRANSLATION_PREFIX . self::CONFIDENTIAL_SCOPE_RANK_CODE,
        self::CONFIDENTIAL_SCOPE_SALARY_CODE     => self::CONFIDENTIAL_SCOPE_TRANSLATION_PREFIX . self::CONFIDENTIAL_SCOPE_SALARY_CODE,
        self::CONFIDENTIAL_SCOPE_COMMISSION_CODE => self::CONFIDENTIAL_SCOPE_TRANSLATION_PREFIX . self::CONFIDENTIAL_SCOPE_COMMISSION_CODE,
        self::CONFIDENTIAL_SCOPE_STAFF_ID_CODE   => self::CONFIDENTIAL_SCOPE_TRANSLATION_PREFIX . self::CONFIDENTIAL_SCOPE_STAFF_ID_CODE,
        self::CONFIDENTIAL_SCOPE_NULL_CODE       => self::CONFIDENTIAL_SCOPE_TRANSLATION_PREFIX . self::CONFIDENTIAL_SCOPE_NULL_CODE,
    ];


    // 转交部门标识列表
    const TRANSFER_DEPARTMENT_TRANSLATION_PREFIX = self::ACCESS_DATA_TRANSLATION_PREFIX . 'transfer_department_';
    const TRANSFER_COO_DEPARTMENT_ID = 1;      // COO
    const TRANSFER_FINANCE_DEPARTMENT_ID = 2;  // 财务
    const TRANSFER_HR_DEPARTMENT_ID = 3;       //HR
    const TRANSFER_NULL_DEPARTMENT_ID = 10000; // 不需转交相关部门审批
    static $transfer_department_item = [
        self::TRANSFER_COO_DEPARTMENT_ID     => self::TRANSFER_DEPARTMENT_TRANSLATION_PREFIX . self::TRANSFER_COO_DEPARTMENT_ID,
        self::TRANSFER_FINANCE_DEPARTMENT_ID => self::TRANSFER_DEPARTMENT_TRANSLATION_PREFIX . self::TRANSFER_FINANCE_DEPARTMENT_ID,
        self::TRANSFER_HR_DEPARTMENT_ID      => self::TRANSFER_DEPARTMENT_TRANSLATION_PREFIX . self::TRANSFER_HR_DEPARTMENT_ID,
        self::TRANSFER_NULL_DEPARTMENT_ID    => self::TRANSFER_DEPARTMENT_TRANSLATION_PREFIX . self::TRANSFER_NULL_DEPARTMENT_ID,
    ];

    // 数据部门指标主题
    const INDICATOR_THEME_TRANSLATION_PREFIX = self::ACCESS_DATA_TRANSLATION_PREFIX . 'indicator_theme_';
    const INDICATOR_THEME_SINGLE_CODE = 1;            // 单量
    const INDICATOR_THEME_MARKETING_ACTIVITY_CODE = 2;//市场活动
    const INDICATOR_THEME_CUS_OPERATION_CODE = 3;     //客户运营
    const INDICATOR_THEME_FINANCE_CODE = 4;           //财务
    const INDICATOR_THEME_HRIS_CODE = 5;              //HRIS
    const INDICATOR_THEME_HUMAN_EFFECT_CODE = 6;      //人效
    const INDICATOR_THEME_KAM_CODE = 7;               //KAM
    const INDICATOR_THEME_QAQC_CODE = 8;              //服务质量（QAQC）
    const INDICATOR_THEME_CUS_SERVICE_CODE = 9;       //客服
    const INDICATOR_THEME_AGING_CODE = 10;            //时效
    const INDICATOR_THEME_ROUTE_CODE = 11;            //车线
    const INDICATOR_THEME_STORE_OPERATION_CODE = 12;  //网点运营
    const INDICATOR_THEME_HUB_CODE = 13;              //HUB
    static $indicator_theme_item = [
        self::INDICATOR_THEME_SINGLE_CODE             => self::INDICATOR_THEME_TRANSLATION_PREFIX . self::INDICATOR_THEME_SINGLE_CODE,
        self::INDICATOR_THEME_MARKETING_ACTIVITY_CODE => self::INDICATOR_THEME_TRANSLATION_PREFIX . self::INDICATOR_THEME_MARKETING_ACTIVITY_CODE,
        self::INDICATOR_THEME_CUS_OPERATION_CODE      => self::INDICATOR_THEME_TRANSLATION_PREFIX . self::INDICATOR_THEME_CUS_OPERATION_CODE,
        self::INDICATOR_THEME_FINANCE_CODE            => self::INDICATOR_THEME_TRANSLATION_PREFIX . self::INDICATOR_THEME_FINANCE_CODE,
        self::INDICATOR_THEME_HRIS_CODE               => self::INDICATOR_THEME_TRANSLATION_PREFIX . self::INDICATOR_THEME_HRIS_CODE,
        self::INDICATOR_THEME_HUMAN_EFFECT_CODE       => self::INDICATOR_THEME_TRANSLATION_PREFIX . self::INDICATOR_THEME_HUMAN_EFFECT_CODE,
        self::INDICATOR_THEME_KAM_CODE                => self::INDICATOR_THEME_TRANSLATION_PREFIX . self::INDICATOR_THEME_KAM_CODE,
        self::INDICATOR_THEME_QAQC_CODE               => self::INDICATOR_THEME_TRANSLATION_PREFIX . self::INDICATOR_THEME_QAQC_CODE,
        self::INDICATOR_THEME_CUS_SERVICE_CODE        => self::INDICATOR_THEME_TRANSLATION_PREFIX . self::INDICATOR_THEME_CUS_SERVICE_CODE,
        self::INDICATOR_THEME_AGING_CODE              => self::INDICATOR_THEME_TRANSLATION_PREFIX . self::INDICATOR_THEME_AGING_CODE,
        self::INDICATOR_THEME_ROUTE_CODE              => self::INDICATOR_THEME_TRANSLATION_PREFIX . self::INDICATOR_THEME_ROUTE_CODE,
        self::INDICATOR_THEME_STORE_OPERATION_CODE    => self::INDICATOR_THEME_TRANSLATION_PREFIX . self::INDICATOR_THEME_STORE_OPERATION_CODE,
        self::INDICATOR_THEME_HUB_CODE                => self::INDICATOR_THEME_TRANSLATION_PREFIX . self::INDICATOR_THEME_HUB_CODE,
    ];

    // 审批驳回原因
    const AUDIT_REJECTED_REASON_TRANSLATION_PREFIX = self::ACCESS_DATA_TRANSLATION_PREFIX . 'audit_rejected_';
    const AUDIT_REJECTED_REASON_001 = 1;  // 已有重复需求
    const AUDIT_REJECTED_REASON_002 = 2;  // 需求描述不清晰
    const AUDIT_REJECTED_REASON_003 = 3;  // 数据量太大
    const AUDIT_REJECTED_REASON_004 = 4;  // 用途描述不清晰
    const AUDIT_REJECTED_REASON_005 = 100;// 其他（自定义输入）
    static $audit_rejected_reason_item = [
        self::AUDIT_REJECTED_REASON_001 => self::AUDIT_REJECTED_REASON_TRANSLATION_PREFIX . self::AUDIT_REJECTED_REASON_001,
        self::AUDIT_REJECTED_REASON_002 => self::AUDIT_REJECTED_REASON_TRANSLATION_PREFIX . self::AUDIT_REJECTED_REASON_002,
        self::AUDIT_REJECTED_REASON_003 => self::AUDIT_REJECTED_REASON_TRANSLATION_PREFIX . self::AUDIT_REJECTED_REASON_003,
        self::AUDIT_REJECTED_REASON_004 => self::AUDIT_REJECTED_REASON_TRANSLATION_PREFIX . self::AUDIT_REJECTED_REASON_004,
        self::AUDIT_REJECTED_REASON_005 => self::AUDIT_REJECTED_REASON_TRANSLATION_PREFIX . self::AUDIT_REJECTED_REASON_005,
    ];


    /*** 取数系统中的状态 和 进度(对应文案): 是取数工单的 审批终态 + 数据上传状态 + 数据复核状态 三者的不同阶段的原子状态综合逻辑得出***/

    /*** 审批终态使用审批流的公用状态，特此说明 ***/
    const AUDIT_PENDING_STATUS = 1;       // 待审批
    const AUDIT_REJECT_STATUS = 2;        // 驳回
    const AUDIT_APPROVE_STATUS = 3;       // 通过

    // 工单审批阶段
    const WORK_ORDER_AUDIT_STAGE_001 = 1; //需求部门审批
    const WORK_ORDER_AUDIT_STAGE_003 = 3; //数据部门审批
    const WORK_ORDER_AUDIT_STAGE_005 = 5; //相关部门审批
    static $work_order_audit_stage_item = [
        self::WORK_ORDER_AUDIT_STAGE_001,
        self::WORK_ORDER_AUDIT_STAGE_003,
        self::WORK_ORDER_AUDIT_STAGE_005,
    ];

    // 数据上传状态
    const DATA_NOT_UPLOADED_STATUS = 0;   //待上传
    const DATA_UPLOADED_STATUS = 1;       //已上传

    // 数据复核状态 Passed
    const DATA_NOT_REVIEWED_STATUS = 0;   //待复核
    const DATA_PASSED_REVIEW_STATUS = 1;  //复核通过
    const DATA_REVIEW_REJECTED_STATUS = 2;//复核驳回

    // 业务进度状态码及文案(对应取数系统列表页 -> 进度列/进度下拉框)[审批终态 + 审批阶段 + 数据上传状态 + 数据复核状态]
    const WORK_ORDER_PROCESS_STATUS_TRANSLATION_PREFIX = self::ACCESS_DATA_TRANSLATION_PREFIX . 'work_order_process_';
    const WORK_ORDER_PROCESS_STATUS_001 = 1;//已提交(审批中 + 审批阶段是需求部门)
    const WORK_ORDER_PROCESS_STATUS_002 = 2;//部门审批通过(审批中 + 审批阶段是数据部门)
    const WORK_ORDER_PROCESS_STATUS_003 = 3;//审批中(审批中 + 审批阶段是相关部门)
    const WORK_ORDER_PROCESS_STATUS_004 = 4;//取数中(审批通过 + 数据未上传 | 复核驳回)
    const WORK_ORDER_PROCESS_STATUS_005 = 5;//待复核(审批通过 + 数据已上传 + 待复核)
    const WORK_ORDER_PROCESS_STATUS_006 = 6;//已完成且已复核(审批通过 + 复核通过)
    const WORK_ORDER_PROCESS_STATUS_007 = 7;//部门审批驳回(审批驳回 + 审批阶段是需求部门 | 数据部门)
    const WORK_ORDER_PROCESS_STATUS_008 = 8;//相关部门审批驳回(审批驳回 + 审批阶段是相关部门)
    static $work_order_process_status_item = [
        self::WORK_ORDER_PROCESS_STATUS_001 => self::WORK_ORDER_PROCESS_STATUS_TRANSLATION_PREFIX . self::WORK_ORDER_PROCESS_STATUS_001,
        self::WORK_ORDER_PROCESS_STATUS_002 => self::WORK_ORDER_PROCESS_STATUS_TRANSLATION_PREFIX . self::WORK_ORDER_PROCESS_STATUS_002,
        self::WORK_ORDER_PROCESS_STATUS_003 => self::WORK_ORDER_PROCESS_STATUS_TRANSLATION_PREFIX . self::WORK_ORDER_PROCESS_STATUS_003,
        self::WORK_ORDER_PROCESS_STATUS_004 => self::WORK_ORDER_PROCESS_STATUS_TRANSLATION_PREFIX . self::WORK_ORDER_PROCESS_STATUS_004,
        self::WORK_ORDER_PROCESS_STATUS_005 => self::WORK_ORDER_PROCESS_STATUS_TRANSLATION_PREFIX . self::WORK_ORDER_PROCESS_STATUS_005,
        self::WORK_ORDER_PROCESS_STATUS_006 => self::WORK_ORDER_PROCESS_STATUS_TRANSLATION_PREFIX . self::WORK_ORDER_PROCESS_STATUS_006,
        self::WORK_ORDER_PROCESS_STATUS_007 => self::WORK_ORDER_PROCESS_STATUS_TRANSLATION_PREFIX . self::WORK_ORDER_PROCESS_STATUS_007,
        self::WORK_ORDER_PROCESS_STATUS_008 => self::WORK_ORDER_PROCESS_STATUS_TRANSLATION_PREFIX . self::WORK_ORDER_PROCESS_STATUS_008,
    ];

    // 业务状态码及文案(对应列表页 -> 状态列)[审批终态 + 数据上传状态 + 数据复核状态]
    const WORK_ORDER_AUDIT_STATUS_TRANSLATION_PREFIX = self::ACCESS_DATA_TRANSLATION_PREFIX . 'work_order_audit_status_';
    const WORK_ORDER_AUDIT_STATUS_001 = 1;//审批中(审批中)
    const WORK_ORDER_AUDIT_STATUS_002 = 2;//处理中(审批通过 + 复核未通过)
    const WORK_ORDER_AUDIT_STATUS_003 = 3;//已完成(审批驳回 | 复核通过)
    static $work_order_audit_status_item = [
        self::WORK_ORDER_AUDIT_STATUS_001 => self::WORK_ORDER_AUDIT_STATUS_TRANSLATION_PREFIX . self::WORK_ORDER_AUDIT_STATUS_001,
        self::WORK_ORDER_AUDIT_STATUS_002 => self::WORK_ORDER_AUDIT_STATUS_TRANSLATION_PREFIX . self::WORK_ORDER_AUDIT_STATUS_002,
        self::WORK_ORDER_AUDIT_STATUS_003 => self::WORK_ORDER_AUDIT_STATUS_TRANSLATION_PREFIX . self::WORK_ORDER_AUDIT_STATUS_003,
    ];

    // 阶段状态码及文案(对应详情页 -> 顶部处理阶段)[审批终态 + 审批阶段 + 数据上传状态 + 数据复核状态]
    const DETAIL_PAGE_TOP_PROCESS_TRANSLATION_PREFIX = self::ACCESS_DATA_TRANSLATION_PREFIX . 'detail_page_top_process_';
    const DETAIL_PAGE_TOP_PROCESS_STATUS_001 = 1;//取数申请(未提交申请)
    const DETAIL_PAGE_TOP_PROCESS_STATUS_002 = 2;//需求部门审批(审批中 + 审批阶段是需求部门)
    const DETAIL_PAGE_TOP_PROCESS_STATUS_003 = 3;//数据部门审批(审批中 + 审批阶段是数据部门 )
    const DETAIL_PAGE_TOP_PROCESS_STATUS_004 = 4;//相关部门审批(审批中 + 审批阶段是勾选的相关部门)
    const DETAIL_PAGE_TOP_PROCESS_STATUS_005 = 5;//数据处理中(审批通过 + 复核未通过)
    const DETAIL_PAGE_TOP_PROCESS_STATUS_006 = 6;//完成(审批驳回  + 审批阶段 | 审批通过 + 复核通过)
    static $detail_page_top_process_status_item = [
        self::DETAIL_PAGE_TOP_PROCESS_STATUS_001 => self::DETAIL_PAGE_TOP_PROCESS_TRANSLATION_PREFIX . self::DETAIL_PAGE_TOP_PROCESS_STATUS_001,
        self::DETAIL_PAGE_TOP_PROCESS_STATUS_002 => self::DETAIL_PAGE_TOP_PROCESS_TRANSLATION_PREFIX . self::DETAIL_PAGE_TOP_PROCESS_STATUS_002,
        self::DETAIL_PAGE_TOP_PROCESS_STATUS_003 => self::DETAIL_PAGE_TOP_PROCESS_TRANSLATION_PREFIX . self::DETAIL_PAGE_TOP_PROCESS_STATUS_003,
        self::DETAIL_PAGE_TOP_PROCESS_STATUS_004 => self::DETAIL_PAGE_TOP_PROCESS_TRANSLATION_PREFIX . self::DETAIL_PAGE_TOP_PROCESS_STATUS_004,
        self::DETAIL_PAGE_TOP_PROCESS_STATUS_005 => self::DETAIL_PAGE_TOP_PROCESS_TRANSLATION_PREFIX . self::DETAIL_PAGE_TOP_PROCESS_STATUS_005,
        self::DETAIL_PAGE_TOP_PROCESS_STATUS_006 => self::DETAIL_PAGE_TOP_PROCESS_TRANSLATION_PREFIX . self::DETAIL_PAGE_TOP_PROCESS_STATUS_006,
    ];

    // 数据部门id
    const DATA_DEPARTMENT_ID = 14;

    // 员工权职标签
    const STAFF_DUTY_TRANSLATION_PREFIX = self::ACCESS_DATA_TRANSLATION_PREFIX . 'staff_duty_';
    const STAFF_DUTY_MANAGER = 1;//负责人
    const STAFF_DUTY_GENERAL = 0;//普通员工
    static $staff_duty_item = [
        self::STAFF_DUTY_MANAGER => self::STAFF_DUTY_TRANSLATION_PREFIX . self::STAFF_DUTY_MANAGER,
        self::STAFF_DUTY_GENERAL => self::STAFF_DUTY_TRANSLATION_PREFIX . self::STAFF_DUTY_GENERAL,
    ];


}
