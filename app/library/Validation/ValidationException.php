<?php

namespace App\Library\Validation;

use App\Library\ErrCode;

class ValidationException extends \Exception
{
    /**
     * ValidationException constructor.
     *
     * @param string $message
     * @param int|null $code
     * @param Throwable|null $previous
     */
    public function __construct(string $message = '', $code = null, ?Throwable $previous = null)
    {
        parent::__construct($message, !is_null($code) ? $code : ErrCode::$VALIDATE_ERROR, $previous);
    }

}
