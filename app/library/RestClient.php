<?php

namespace App\Library;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\RequestOptions;

class RestClient extends BaseService
{
    const METHOD_GET = 'get';
    const METHOD_POST = 'post';
    const METHOD_PUT = 'put';
    const METHOD_DELETE = 'delete';

    /**
     * @var Client
     */
    private $client;

    /**
     * RestClient constructor.
     * @param $sys
     * @param int $timeout
     */
    public function __construct($sys, int $timeout = 5)
    {
        $base         = $this->getSysEndpoint($sys);
        $config       = [
            'base_uri' => $base,
            'timeout'  => $timeout,
        ];
        $this->client = new Client($config);
    }

    /**
     * @param $sys
     * @return string
     */
    private function getSysEndpoint($sys): string
    {
        switch ($sys) {
            case 'fle_fra':
                $endpoint = env('fle_fra', 'https://dev-01-th-fra-svc.fex.pub');
                break;
            case 'nws': //nws
                $endpoint = env('nws_rest_endpoint','http://dev-01-th-nws-svc.fex.pub');
                break;
            case 'pms': //pms
                $endpoint = env('pms_rest_endpoint');
                break;
            case 'oms': //oms
                $endpoint = env('oms_rest_endpoint');
                break;
            default:
                $endpoint = $sys;
                break;
        }

        return $endpoint;
    }

    /**
     * @param $method
     * @param $path
     * @param array $params
     * @param array $headers
     * @param bool $http_errors
     * @return array|mixed
     */
    public function execute($method, $path, array $params = [], array $headers = [], bool $http_errors = true)
    {
        $data = [];
        try {
            $opts = [];
            if (!empty($headers)) {
                $opts[RequestOptions::HEADERS] = $headers;
            }
            $opts[RequestOptions::HTTP_ERRORS] = $http_errors;
            switch ($method) {
                case self::METHOD_GET:
                    $opts[RequestOptions::QUERY] = $params;
                    $response                    = $this->client->get($path, $opts);
                    break;
                case self::METHOD_POST:
                    $opts[RequestOptions::JSON] = $params;
                    $response                   = $this->client->post($path, $opts);
                    break;
                case self::METHOD_PUT:
                    $opts[RequestOptions::JSON] = $params;
                    $response                   = $this->client->put($path, $opts);
                    break;
                case self::METHOD_DELETE:
                    $opts[RequestOptions::JSON] = $params;
                    $response                   = $this->client->delete($path, $opts);
                    break;
                default:
                    throw new \RuntimeException('Method wrong');
            }

            $this->logger->info([
                'request'  => func_get_args(),
                'response' => [
                    'code'    => $response->getStatusCode(),
                    'headers' => $response->getHeaders(),
                    'body'    => (string)$response->getBody(),
                ],
            ]);

            $data = json_decode($response->getBody(), true);
        } catch (RequestException $e) {
            if ($e->hasResponse()) {
                $response = $e->getResponse();
                $data     = json_decode($response->getBody()->getContents(), true);
                $this->logger->info([
                    'base'    => (string)$this->client->getConfig('base_uri'),
                    'request' => func_get_args(),
                    'params'  => $params,
                    'result'  => $data,
                ]);
            } else {
                $this->logger->error([
                    'base'      => (string)$this->client->getConfig('base_uri'),
                    'request'   => func_get_args(),
                    'params'    => $params,
                    'exception' => $e->getMessage(),
                    'file'      => $e->getFile(),
                    'line'      => $e->getLine(),
                ]);
            }
        } catch (GuzzleException $e) {
            $this->logger->error([
                'base'      => (string)$this->client->getConfig('base_uri'),
                'request'   => func_get_args(),
                'params'    => $params,
                'exception' => $e->getMessage(),
                'file'      => $e->getFile(),
                'line'      => $e->getLine(),
            ]);
        }

        return $data;
    }


}