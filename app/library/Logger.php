<?php

namespace App\Library;

use Phalcon\Logger\Factory;

class Logger
{
    private $adapter;

    public function __construct($config)
    {
        $this->adapter = Factory::load($config);
    }

    /**
     * @param $type
     * @param null $message
     * @param null $context
     * @return void
     */
    public function log($type, $message = null, $context = null)
    {
        $formatter = new \Phalcon\Logger\Formatter\Line();
        $formatter->setFormat('%message%');
        $this->adapter->setFormatter($formatter);

        $debugInfo             = debug_backtrace(0);
        $log_info              = [];
        $log_info['type']      = $formatter->getTypeString($type);
        $log_info['timestamp'] = get_datetime_with_milliseconds();
        if (isset($debugInfo[2])) {
            $log_info['class']    = $debugInfo[2]['class'];
            $log_info['function'] = $debugInfo[2]['function'];
            $log_info['filepath'] = $debugInfo[1]['file'];
            $log_info['line']     = $debugInfo[1]['line'];
        } else {
            $log_info['class']    = __CLASS__;
            $log_info['function'] = __FUNCTION__;
            $log_info['filepath'] = __FILE__;
            $log_info['line']     = __LINE__;
        }
        $log_info['data'] = ['biz_content' => $message];

        //系统中增加traceid
        $log_info['traceid'] = molten_get_traceid();

        $this->adapter->log($type, json_encode($log_info, JSON_UNESCAPED_UNICODE), $context);

        // 日志报警输出到飞书机器人
        $log_type = strtolower($log_info['type']);
        if (env('fs_alarm_switch', false) && stripos(env('fs_alarm_log_levels'), $log_type) !== false) {
            $this->sendFsAlarmNotice($log_info, $log_type);
        }
    }

    /**
     * Sends/Writes a critical message to the log
     *
     * @param mixed $message
     * @param array|null $context
     */
    public function critical($message, array $context = null)
    {
        $this->log(\Phalcon\Logger::CRITICAL, $message, $context);
    }

    /**
     * Sends/Writes an emergency message to the log
     *
     * @param mixed $message
     * @param array|null $context
     */
    public function emergency($message, array $context = null)
    {
        $this->log(\Phalcon\Logger::EMERGENCY, $message, $context);
    }

    /**
     * Sends/Writes a debug message to the log
     *
     * @param mixed $message
     * @param array|null $context
     */
    public function debug($message, array $context = null)
    {
        $this->log(\Phalcon\Logger::DEBUG, $message, $context);
    }

    /**
     * Sends/Writes an error message to the log
     *
     * @param mixed $message
     * @param array|null $context
     */
    public function error($message, array $context = null)
    {
        $this->log(\Phalcon\Logger::ERROR, $message, $context);
    }

    /**
     * Sends/Writes an info message to the log
     *
     * @param mixed $message
     * @param array|null $context
     */
    public function info($message, array $context = null)
    {
        $this->log(\Phalcon\Logger::INFO, $message, $context);
    }

    /**
     * Sends/Writes a notice message to the log
     *
     * @param mixed $message
     * @param array|null $context
     */
    public function notice($message, array $context = null)
    {
        $this->log(\Phalcon\Logger::NOTICE, $message, $context);
    }

    /**
     * Sends/Writes a warning message to the log
     *
     * @param mixed $message
     * @param array|null $context
     */
    public function warning($message, array $context = null)
    {
        $this->log(\Phalcon\Logger::WARNING, $message, $context);
    }

    /**
     * Sends/Writes an alert message to the log
     *
     * @param mixed $message
     * @param array|null $context
     */
    public function alert($message, array $context = null)
    {
        $this->log(\Phalcon\Logger::ALERT, $message, $context);
    }

    public function __call($name, $arguments)
    {
        $this->adapter->$name(...$arguments);
    }

    /**
     * 飞书报警
     * @param $log_data mixed 日志数据
     * @param $log_type string 日志类型
     * @return mixed
     */
    public function sendFsAlarmNotice($log_data, string $log_type)
    {
        if ($log_type == 'notice') {
            $api_key = 'fs_notice_alarm_key';
        } else {
            $api_key = 'fs_alarm_key';
        }

        $fs_bot_api_key = env($api_key, '');
        if (empty($fs_bot_api_key)) {
            return '';
        }

        if (is_array($log_data)) {
            $log_data = json_encode($log_data, JSON_UNESCAPED_UNICODE);
        }

        // 消息内容
        $msg_content = sprintf('OA_%s [%s] [%s] [%s] [%s] ！ %s', get_country_code(), $log_type, gethostname(), RUNTIME,
            date('Y-m-d H:i:s'), $log_data);

        send_feishu_text_msg($msg_content, $fs_bot_api_key);
    }

}
