<?php

namespace App\Modules\CrmQuotation\Controllers;


use App\Library\Validation\ValidationException;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Modules\CrmQuotation\Services\ApplyService;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

/**
 * crm 报价审批
 *
 * Class ApplyController
 *
 * @package App\Modules\CrmQuotation\Controllers
 *
 */
class ApplyController extends BaseController
{

    /**
     *
     * 我的申请列表
     *
     *
     * @Permission(action='crm.apply.list')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function listAction()
    {
        $params = $this->request->get();

        Validation::validate($params, [
            'pageNum'      => 'Required|Int',
            'pageSize'     => 'Required|Int',
            'audit_status' => 'Required|IntIn:1,2,3',
        ]);

        // 申请列表
        $data = (new ApplyService())->getList($params, $this->user['id']);

        return $this->returnJson($data['code'], $data['message'], $data['data']);
    }


    /**
     *
     * 我的申请列表
     *
     *
     * @Permission(action='crm.apply.list')
     * @return Response|ResponseInterface
     */
    public function applyUserAction()
    {
        $list = (new ApplyService())->customersList();

        return $this->returnJson(ErrCode::$SUCCESS, '', $list);
    }


}
