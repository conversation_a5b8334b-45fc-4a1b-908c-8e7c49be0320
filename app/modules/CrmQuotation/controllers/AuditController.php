<?php

namespace App\Modules\CrmQuotation\Controllers;

use App\Library\Exception\BusinessException;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Library\ErrCode;
use App\Modules\CrmQuotation\Services\ApplyService;
use App\Modules\CrmQuotation\Services\CrmFlowService;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class AuditController extends BaseController
{

    /**
     * 审核
     * @Permission(action='crm.apply.audit')
     *
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function auditAction()
    {
        $id   = $this->request->get('id', 'int');
        $note = $this->request->get('note', 'trim');
        $flag = $this->request->get('flag', 'int');

        Validation::validate([
            'id'   => $id,
            'note' => $note,
            'flag' => $flag,
        ], [
            'id'   => 'Required|Int',
            'note' => 'Required|Str',
            'flag' => 'Required|Int',
        ]);

        $lock_key = md5('crm_quotation_audit_' . $flag . '_' . $id);
        $res      = $this->atomicLock(function () use ($id, $note, $flag) {
            $crmFlowService = new CrmFlowService();
            if (empty($flag)) {
                return $crmFlowService->approve($id, $note, $this->user);
            } else {
                return $crmFlowService->reject($id, $note, $this->user);
            }
        }, $lock_key, 20);

        if ($res) {
            $code    = ErrCode::$SUCCESS;
            $message = $this->t['success'];
        } else {
            $code    = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
        }

        return $this->returnJson($code, $message);
    }


    /**
     * 我的审核-查看
     *
     * @Permission(action='crm.apply.view')
     * @return Response|ResponseInterface
     * @throws BusinessException
     * @throws ValidationException
     */
    public function detailAction()
    {
        $id = $this->request->get('id', 'int');
        Validation::validate([
            'id' => $id,
        ], [
            'id' => 'Required|Int',
        ]);

        $data = (new ApplyService())->detail($id, '', $this->user, 1);

        return $this->returnJson(ErrCode::$SUCCESS, '', $data);
    }


}
