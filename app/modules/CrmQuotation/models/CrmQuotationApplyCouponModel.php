<?php

namespace App\Modules\CrmQuotation\Models;

use App\Library\Enums;
use App\Models\Base;

class CrmQuotationApplyCouponModel extends Base
{
    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        // setSource()方法指定数据库表
        $this->setSource('crm_quotation_apply_coupon');
    }


    public function refresh()
    {
        if ($this->getWriteConnection()->isUnderTransaction()) {
            $this->setReadConnectionService('db_oa');
        }
        return parent::refresh();
    }

    /**
     * 软删除
     *
     * @param string $quoted_price_list_sn
     * @return mixed
     * Author: guanlijian
     * Date: 2021/6/1 14:54
     */
    public function delete(string $quoted_price_list_sn = '')
    {
        $is_deleted = 1;
        $deleted_at = gmdate('Y-m-d H:i:s');
        $sql        = " -- 
            UPDATE crm_quotation_apply_coupon
            SET deleted_at = '{$deleted_at}' , is_deleted ='{$is_deleted}'
            WHERE quoted_price_list_sn = '{$quoted_price_list_sn}'
         ";
        return $this->getDI()->get('db_oa')->execute($sql);
    }

}