<?php

namespace App\Modules\CrmQuotation\Services;

class BaseService extends \App\Library\BaseService
{

    const DEPARTMENT_SALES = 1;
    const DEPARTMENT_PMD = 2;
    const DEPARTMENT_SHOP = 3;
    const DEPARTMENT_NETWORK = 4;
    const DEPARTMENT_FFM = 5;
    const DEPARTMENT_OTHER = 6;
    public static $department = [
        self::DEPARTMENT_SALES   => 'Sales',
        self::DEPARTMENT_PMD     => 'PMD',
        self::DEPARTMENT_SHOP    => 'Shop',
        self::DEPARTMENT_NETWORK => 'Network',
        self::DEPARTMENT_FFM     => 'FFM',
        self::DEPARTMENT_OTHER   => 'OTHER',
    ];

    /**
     *
     * @var string[]
     */
    public static $validate_cancel_params = [
        'create_id'            => 'Required|Int',
        'quoted_price_list_sn' => 'Required|StrLenGeLe:1,20',
    ];

    /**
     * @var string[]
     */
    public static $validate_add_params = [
        'create_id'                    => 'Required|Int',
        'create_name'                  => 'Required|StrLenGeLe:0,50',
        'customer_id'                  => 'Required|StrLenGeLe:0,50',
        'quoted_price_list_sn'         => 'Required|StrLenGeLe:1,50',
        'customer_name'                => 'Required',
        'customer_mobile'              => 'Required|Int',
        'customer_type_category'       => 'Required|Int',
        'apply_user_sn'                => 'Required|Int',
        'apply_user_name'              => 'Required|StrLenGeLe:0,50',
        'apply_user_department'        => 'Required|Int',
        'apply_user_department_type'   => 'Required|IntIn:1,2,3,4,7,8,9,10,11',
//        'product_category' => 'Required|IntIn:1,2',
//        'quoted_price_type' => 'Required|IntIn:1,2,3',
        'total_discount_rate'          => 'Required',
//        'return_discount_rate' => 'Required',
        'calculation_method'           => 'Required|IntGe:0',
        'calculation_method_value'     => 'Required|IntGe:0',
        'credit_period'                => 'Required|IntGe:0',
//        'cod_fee_rate' => 'Required',
        'coupon_info[*].coupon_type'   => 'IntGe:0',
        'coupon_info[*].coupon_num'    => 'IntGe:0',
        'coupon_info[*].coupon_expire' => 'IntGe:0',
//        'lowest_discount_rate' => 'Required',
        'discount_valid_type'          => 'Required|IntGe:0',
        'valid_time'                   => 'Required',
        'invalid_time'                 => 'Required',
        'valid_days'                   => 'Required|Int',
        'price_type'                   => 'Required|Int',
        'return_fee_method'            => 'Required',
        'valid_promise_num'            => 'Required|IntGe:0',
        'settlement_type'              => 'Required|IntGe:0',
    ];

    /**
     *
     * @var string[]
     *
     */
    public static $validate_audit_log = [
        'quoted_price_list_sn' => 'Required|StrLenGeLe:0,20',
    ];
}