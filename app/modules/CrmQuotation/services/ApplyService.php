<?php

namespace App\Modules\CrmQuotation\Services;


use App\Library\ApiClient;
use App\Library\Enums\CrmQuotationEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Contract\Models\SysDepartmentModel;
use App\Modules\Contract\Services\ContractConsumerService;
use App\Modules\Contract\Services\ContractFlowService;
use App\Modules\CrmQuotation\Models\CrmQuotationApplyModel;
use App\Library\Enums;
use App\Modules\Salary\Models\PaySalaryApply;
use App\Modules\User\Services\UserService;
use App\Modules\Workflow\Models\WorkflowAuditLogModel;
use App\Modules\Workflow\Models\WorkflowRequestModel;
use App\Modules\Workflow\Models\WorkflowRequestNodeAt;
use App\Modules\Workflow\Services\WorkflowServiceV2;
use App\Modules\CrmQuotation\Services\Coupon as CrmCouponServices;
use Exception;

use function Symfony\Component\String\b;

class ApplyService extends BaseService
{


    /**
     *
     *
     * 添加crm报价审批
     *
     * @param $params
     *
     * @return bool
     * @throws BusinessException
     * @throws ValidationException
     */
    public function addApply($params)
    {
        $job_title_enum = [
            Enums::CRM_JOB_TITLE_BS,
            Enums::CRM_JOB_TITLE_DM,
            Enums::CRM_JOB_TITLE_AM,
            Enums::CRM_JOB_TITLE_SALES,
            Enums::CRM_JOB_TITLE_SALES_2,
            Enums::CRM_JOB_TITLE_SALES_SUPERVISOR,
            Enums::CRM_JOB_TITLE_SALES_MANAGER,
        ];

        $maps = [
            GlobalEnums::TH_COUNTRY_CODE => [
                40   => Enums::DEPARTMENT_TYPE_SALES,                     //Sales部门
                22   => Enums::DEPARTMENT_TYPE_PMD,                       //PMD部门
                13   => Enums::DEPARTMENT_TYPE_SHOP,                      //Shop部门
                4    => Enums::DEPARTMENT_TYPE_NETWORK,                   //Network部门
                34   => Enums::DEPARTMENT_TYPE_NETWORK_BULKY,             //Network Bulky部门
                545  => Enums::DEPARTMENT_TYPE_BULKY_BUSINESS_DEVELOPMENT,//Bulky Business Development
                18   => Enums::DEPARTMENT_TYPE_FLASH_HOME,                //Bulky Business Development
                1340 => Enums::DEPARTMENT_TYPE_JVB,                       // JVB Operations部门
            ],
            GlobalEnums::PH_COUNTRY_CODE => [
                125  => Enums::DEPARTMENT_TYPE_NETWORK,//Network部门(不包含二级部门5490)
                5088 => Enums::DEPARTMENT_TYPE_SALES,  //Sales部门
                5490 => Enums::DEPARTMENT_TYPE_USER,   //虚拟的USER 部门
            ],
            GlobalEnums::MY_COUNTRY_CODE => [
                15097 => Enums::DEPARTMENT_TYPE_SALES,  //Sales部门
                388   => Enums::DEPARTMENT_TYPE_PMD,    //PMD部门
                337   => Enums::DEPARTMENT_TYPE_SHOP,   //Shop部门
                316   => Enums::DEPARTMENT_TYPE_NETWORK,//Network部门
            ],
        ];

        $credit_period_maps                   = [
            0 => 0,
            1 => 7,
            2 => 15,
            3 => 30,
        ];
        $country_code                         = get_country_code();
        $params['apply_user_department_type'] = $maps[$country_code][$params['apply_user_department']] ?? '';
        $params['credit_period']              = $credit_period_maps[$params['credit_period']];
        $params['valid_time']                 = date('Y-m-d H:i:s',
            strtotime($params['valid_time']) + 3600 * get_sys_time_offset());
        Validation::validate($params, self::$validate_add_params);

        $params['cod_fee_min_value'] = $params['cod_fee_min_value'] ?? 0;
        $params['region_name']       = $params['region_name'] ?? '';

        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            $crmQuotation = $this->getCrmQuotation($params['quoted_price_list_sn']);

            $us   = new UserService();
            $user = $us->getUserById($params['create_id']);
            $user = [
                'id'                => $user->id,
                'name'              => $user->name,
                'organization_type' => $user->organization_type,
                'organization_id'   => $user->organization_id,
                'department'        => $user->getDepartment()->name ?? '',
                'department_id'     => $user->department_id,
                'job_title'         => $user->getJobTitle()->name ?? '',
                'job_title_id'      => $user->job_title,
                'nick_name'         => $user->nick_name ?? '',
            ];
            if (!$crmQuotation) {
                // 新建审批
                $crmQuotationModel = new CrmQuotationApplyModel();
                $params['crmno']   = 'CRM' . date('YmdHis');
                $params['state']   = Enums::CONTRACT_STATUS_PENDING;

                /**
                 * 10252需求
                 * crm开发给的逻辑
                 * quoted_price_type 1固定折扣 3特殊折扣
                 * lowest_discount_rate 非空就是标准产品
                 * 产品给的逻辑
                 * 标准产品-固定折扣: 李俊(34014)审批流不依赖职位判断,所以无需验证范围
                 * 标准产品-特殊折扣: 只有Eva(119100)才能提交,无需判断职位
                 * 产品确认: 提交报价单的时候会做校验，不符合这五类申请人(34014,119100,职位Branch supervisor（职位id=16）,District Manager （职位id=269）,Area Manager （职位id=79） )不允许提交的
                 *
                 * 12175增加三个职位可以提交 销售专员(1170),销售主管(1169),销售经理(1168)
                 */


                if (empty($params['invalid_time'])) {
                    unset($params['invalid_time']);
                } else {
                    $params['invalid_time'] = date('Y-m-d H:i:s',
                        strtotime($params['invalid_time']) + 3600 * get_sys_time_offset());
                }
                if (empty($params['quoted_price_type'])) {
                    unset($params['quoted_price_type']);
                }
                if (empty($params['price_list'])) {
                    unset($params['price_list']);
                } else {
                    $params['price_list'] = json_encode($params['price_list']);
                }
                if (empty($params['customer_type'])) {
                    unset($params['customer_type']);
                }
                if (empty($params['lowest_discount_rate'])) {
                    unset($params['lowest_discount_rate']);
                }
                if (empty($params['cod_fee_rate'])) {
                    unset($params['cod_fee_rate']);
                }
                if (empty($params['return_discount_rate'])) {
                    unset($params['return_discount_rate']);
                }
                if (is_null($params['faraway_fee'])) {
                    $params['faraway_fee'] = -1;
                }
                if (empty($params['is_bulky_products'])) {
                    $params['is_bulky_products'] = 1;
                }
                if (empty($params['bulky_products_discount'])) {
                    unset($params['bulky_products_discount']);
                }
                if (empty($params['bp_discount_rate'])) {
                    unset($params['bp_discount_rate']);
                }
                if (empty($params['large_remote_area_fee'])) {
                    unset($params['large_remote_area_fee']);
                }

                if (empty($params['bp_price_list'])) {
                    unset($params['bp_price_list']);
                } else {
                    $params['bp_price_list'] = json_encode($params['bp_price_list']);
                }
                //16264【OA-TH】新增Flash Home报价审批流 增加bind_store_id和rebate_disc
                if (empty($params['bind_store_id'])) {
                    unset($params['bind_store_id']);
                }
                if (empty($params['rebate_disc'])) {
                    unset($params['rebate_disc']);
                }
                //16226【CRM/OA/MS-TH】申请计费方式优化（一期） 增加special_config和calculation_method_size_value
                //计费方式特殊配置
                if (empty($params['special_config'])) {
                    unset($params['special_config']);
                }
                //计费方式 尺寸
                if (empty($params['calculation_method_size_value'])) {
                    unset($params['calculation_method_size_value']);
                }

                if (empty($params['free_strategy_discount'])) {
                    $params['free_strategy_discount'] = 0;
                }
                if (empty($params['free_strategy_rate'])) {
                    $params['free_strategy_rate'] = 0.00;
                }

                $params['sync_status'] = 2;
                $bool                  = $crmQuotationModel->i_create($params);
                if ($bool === false) {
                    $messages = $crmQuotationModel->getMessages();
                    throw new BusinessException('crm报价审批单创建失败=' . implode(",", $messages),
                        ErrCode::$CONTRACT_CREATE_ERROR);
                }
                //优惠券
                if (isset($params['coupon_info']) && !empty($params['coupon_info'])) {
                    $bool = (new CrmCouponServices())->save($params['coupon_info'], $params['quoted_price_list_sn']);
                    if ($bool === false) {
                        $messages = $crmQuotation->getMessages();
                        throw new BusinessException('crm报价审批单重新提交失败=' . implode(",", $messages),
                            ErrCode::$CONTRACT_CREATE_ERROR);
                    }
                }
                $bool = (new CrmFlowService())->createRequest($crmQuotationModel->id, $user);
                if ($bool === false) {
                    throw new BusinessException('CRM报价审批申请 创建失败=' . json_encode($params, JSON_UNESCAPED_UNICODE),
                        ErrCode::$CONTRACT_CREATE_ERROR);
                }
            } else {
                //审批通过不允许重新提交
                if ($crmQuotation->state == Enums::CONTRACT_STATUS_APPROVAL || $crmQuotation->state == Enums::CONTRACT_STATUS_PENDING) {
                    throw new ValidationException('crm报价审批单创建失败-此报价单已通过审批或在审批中:quoted_price_list_sn=' . $params['quoted_price_list_sn'],
                        ErrCode::$CONTRACT_CREATE_ERROR);
                }
                // 重新提交审批
                $params['state'] = Enums::CONTRACT_STATUS_PENDING;


                if (empty($params['invalid_time'])) {
                    //unset($params['invalid_time']);
                    $params['invalid_time'] = null;
                } else {
                    $params['invalid_time'] = date('Y-m-d H:i:s',
                        strtotime($params['invalid_time']) + 3600 * get_sys_time_offset());
                }
                if (empty($params['quoted_price_type'])) {
                    $params['quoted_price_type'] = 0;
                }
                if (empty($params['price_list'])) {
                    //unset($params['price_list']);
                    $params['price_list'] = null;
                } else {
                    $params['price_list'] = json_encode($params['price_list']);
                }
                if (empty($params['customer_type'])) {
                    unset($params['customer_type']);
                }
                if (empty($params['lowest_discount_rate'])) {
                    //unset($params['lowest_discount_rate']);
                    $params['lowest_discount_rate'] = 0.00;
                }
                if (empty($params['cod_fee_rate'])) {
                    //unset($params['cod_fee_rate']);
                    $params['cod_fee_rate'] = 0.00;
                }
                if (empty($params['return_discount_rate'])) {
                    //unset($params['return_discount_rate']);
                    $params['return_discount_rate'] = 0.00;
                }
                if (is_null($params['faraway_fee'])) {
                    $params['faraway_fee'] = -1;
                }
                if (empty($params['is_bulky_products'])) {
                    $params['is_bulky_products'] = 1;
                }
                if (empty($params['bulky_products_discount'])) {
                    //unset($params['bulky_products_discount']);
                    $params['bulky_products_discount'] = null;
                }
                if (empty($params['bp_discount_rate'])) {
                    //unset($params['bp_discount_rate']);
                    $params['bp_discount_rate'] = 0.00;
                }
                if (empty($params['large_remote_area_fee'])) {
                    //unset($params['large_remote_area_fee']);
                    $params['large_remote_area_fee'] = 1;
                }

                if (empty($params['free_strategy_discount'])) {
                    $params['free_strategy_discount'] = 0;
                }
                if (empty($params['free_strategy_rate'])) {
                    $params['free_strategy_rate'] = 0.00;
                }

                if (empty($params['bp_price_list'])) {
                    //unset($params['bp_price_list']);
                    $params['bp_price_list'] = null;
                } else {
                    $params['bp_price_list'] = json_encode($params['bp_price_list']);
                }
                //16226【CRM/OA/MS-TH】申请计费方式优化（一期） 增加special_config和calculation_method_size_value
                //计费方式特殊配置
                if (empty($params['special_config'])) {
                    $params['special_config'] = 0;
                }
                //计费方式 尺寸
                if (empty($params['calculation_method_size_value'])) {
                    $params['calculation_method_size_value'] = 0;
                }

                //16264【OA-TH】新增Flash Home报价审批流 增加bind_store_id和rebate_disc
                if (empty($params['bind_store_id'])) {
                    $params['bind_store_id'] = 0;
                }
                if (empty($params['rebate_disc'])) {
                    $params['rebate_disc'] = 0;
                }

                $params['created_at'] = gmdate('Y-m-d H:i:s');
                $bool                 = $crmQuotation->i_update($params);

                //优惠券
                if (isset($params['coupon_info']) && !empty($params['coupon_info'])) {
                    $bool_coupon = (new CrmCouponServices())->save($params['coupon_info'],
                        $params['quoted_price_list_sn']);
                    if ($bool_coupon === false) {
                        $messages = $crmQuotation->getMessages();
                        throw new BusinessException('crm报价审批单重新提交失败=' . implode(",", $messages),
                            ErrCode::$CONTRACT_CREATE_ERROR);
                    }
                } else {
                    $bool_coupon = (new CrmCouponServices())->deleteCoupon($params['quoted_price_list_sn']);
                    if ($bool_coupon === false) {
                        $messages = $crmQuotation->getMessages();
                        throw new BusinessException('crm报价审批单重新提交失败:删除coupon失败=' . implode(",", $messages),
                            ErrCode::$CONTRACT_CREATE_ERROR);
                    }
                }
                if ($bool === false) {
                    $messages = $crmQuotation->getMessages();
                    throw new BusinessException('crm报价审批单重新提交失败=' . implode(",", $messages),
                        ErrCode::$CONTRACT_CREATE_ERROR);
                }

                $bool = (new CrmFlowService())->recommit($crmQuotation, $user);

                if ($bool === false) {
                    throw new BusinessException('CRM报价审批申请 创建失败=' . json_encode($params, JSON_UNESCAPED_UNICODE),
                        ErrCode::$CONTRACT_CREATE_ERROR);
                }
            }
            $db->commit();
        } catch (Exception $e) {
            $db->rollBack();

            throw $e;
        }

        return true;
    }

    /**
     *
     * 获取报价审批 通过 报价单号
     *
     * @param $quotedSn
     * @param bool $forUpdate
     */
    public function getCrmQuotation($quotedSn, $forUpdate = true)
    {
        return CrmQuotationApplyModel::findFirst([
            'conditions' => 'quoted_price_list_sn = :quoted_sn: ',
            'bind'       => [
                'quoted_sn' => $quotedSn,
            ],
            'for_update' => $forUpdate,
        ]);
    }


    /**
     *
     * 获得审核列表
     *
     * @param $condition
     * @param $uid
     * @return array
     */
    public function getList($condition, $uid = 0)
    {
        $condition['uid'] = $uid;
        $page_size        = empty($condition['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $condition['pageSize'];
        $page_num         = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['pageNum'];
        $offset           = $page_size * ($page_num - 1);

        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];

        try {
            ini_set('memory_limit', '1024M');

            $builder = $this->modelsManager->createBuilder();
            $builder->from(['c' => CrmQuotationApplyModel::class]);
            $builder = $this->getCondition($builder, $condition);
            $count   = (int)$builder->columns('COUNT(DISTINCT(c.id)) AS count')->getQuery()->getSingleResult()->count;

            $items = [];
            if ($count) {
                $builder->columns('c.*');
                $builder->orderBy('c.id desc');
                $builder->groupBy('c.id');
                $builder->limit($page_size, $offset);
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->formatList($items);
            }

            $data = [
                'items'      => $items,
                'pagination' => [
                    'current_page' => $page_num,
                    'per_page'     => $page_size,
                    'total_count'  => $count,
                ],
            ];
        } catch (Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning('crmQuotation-list-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }


    /**
     * 条件构建
     *
     * @param $builder
     * @param $condition
     * @return mixed
     */
    private function getCondition($builder, $condition)
    {
        $audit_status     = $condition['audit_status'] ?? 0;
        $quotation_status = $condition['quotation_status'] ?? 0;

        $crmno           = $condition['crmno'] ?? '';           //薪资发放编号
        $start_date      = $condition['start_date'] ?? '';      //创建时间，开始时间
        $end_date        = $condition['end_date'] ?? '';        //创建时间，结束时间
        $customer_name   = $condition['customer_name'] ?? '';
        $apply_user_name = $condition['apply_user_name'] ?? '';
        $customer_id     = $condition['customer_id'] ?? '';

        if (!empty($crmno)) {
            $builder->andWhere('c.quoted_price_list_sn = :quoted_price_list_sn:', ['quoted_price_list_sn' => $crmno]);
        }

        if (!empty($quotation_status)) {
            $builder->andWhere('c.state = :state:', ['state' => $quotation_status]);
        }

        if (!empty($start_date)) {
            $builder->andWhere('c.created_at >= :apply_date_start:', ['apply_date_start' => $start_date . " 00:00:00"]);
        }

        if (!empty($end_date)) {
            $builder->andWhere('c.created_at <= :apply_date_end:', ['apply_date_end' => $end_date . " 23:59:59"]);
        }

        if (!empty($customer_name)) {
            $builder->andWhere('c.customer_name = :name: or c.customer_id = :name:', ['name' => $customer_name]);
        }

        if (!empty($apply_user_name)) {
            $builder->andWhere('c.apply_user_sn = :name: or c.apply_user_name = :name:', ['name' => $apply_user_name]);
        }

        if (!empty($condition['region_name'])) {
            $builder->andWhere('c.region_name = :region_name:', ['region_name' => $condition['region_name']]);
        }

        if (!empty($customer_id)) {
            $builder->andWhere('c.customer_id = :customer_id:', ['customer_id' => $customer_id]);
        }

        $builder->leftjoin(WorkflowRequestModel::class,
            'r.biz_type = ' . Enums::WF_CRM_QUOTATION . ' and r.biz_value = c.id and  r.is_abandon = 0', 'r');
        switch ($audit_status) {
            case 1:
                $sql = "FIND_IN_SET(:uid:,r.current_node_auditor_id) and r.state=1";
                $builder->andWhere($sql, ['uid' => $condition['uid']]);
                break;
            //已同意
            case 3:
                $builder->leftjoin(WorkflowAuditLogModel::class, 'l.request_id = r.id', 'l');
                $builder->andWhere('l.staff_id = :uid: and l.audit_action = ' . Enums::WF_NODE_APPLY,
                    ['uid' => $condition['uid']]);
                //对于付款员工来说，处理完就是终态，所以不用考虑
                break;
            //已驳回
            case 2:
                $builder->leftjoin(WorkflowAuditLogModel::class, 'l.request_id=r.id', 'l');
                $builder->andWhere('l.staff_id = :uid: and l.audit_action = ' . Enums::WF_STATE_REJECTED,
                    ['uid' => $condition['uid']]);
                break;
            default:
                break;
        }

        return $builder;
    }


    /**
     *
     * format 数据
     *
     *
     * @param $list
     * @return array
     *
     */
    private function formatList($list)
    {
        $result = [];
        foreach ($list as $item) {
            $rate = '';

            if ($item['quoted_price_type'] && $item['quoted_price_type'] == CrmQuotationEnums::QUOTED_PRICE_TYPE_3) {
                if (get_country_code() == GlobalEnums::MY_COUNTRY_CODE) {
                    $rate = $item['lowest_discount_rate'];
                } else {
                    $rate = $item['total_discount_rate'];
                }
            } else {
                if ($item['quoted_price_type'] == CrmQuotationEnums::QUOTED_PRICE_TYPE_2 || $item['quoted_price_type'] == CrmQuotationEnums::QUOTED_PRICE_TYPE_1) {
                    $rate = $item['total_discount_rate'] > $item['lowest_discount_rate'] ? $item['total_discount_rate'] : $item['lowest_discount_rate'];
                } else {
                    if ($item['quoted_price_type'] == CrmQuotationEnums::QUOTED_PRICE_TYPE_4) {
                        //固定折扣80%off活动
                        $rate = $item['lowest_discount_rate'];
                    }
                }
            }

            $result[] = [
                'id'                         => $item['id'],
                'crmno'                      => $item['crmno'],
                'created_at'                 => date('Y-m-d H:i:s',
                    strtotime($item['created_at']) + 3600 * get_sys_time_offset()),
                'quoted_price_list_sn'       => $item['quoted_price_list_sn'],
                'customer_id'                => $item['customer_id'],
                'customer_name'              => $item['customer_name'],
                'discount_valid_type'        => $item['discount_valid_type'],
                'quoted_price_type'          => $item['quoted_price_type'],
                'quoted_price_type_text'     => $item['quoted_price_type'] ? static::$t->_('quoted_price_type_' . $item['quoted_price_type']) : '',
                'total_discount_rate'        => $item['total_discount_rate'],
                'apply_user_sn'              => $item['apply_user_sn'],
                'apply_user_name'            => $item['apply_user_name'],
                'apply_user_department'      => $item['apply_user_department'],
                'apply_user_department_type' => $item['apply_user_department_type'],
                'apply_user_department_text' => isset(CrmQuotationEnums::$department_type_view_name[$item['apply_user_department_type']])
                    ? static::$t->_(CrmQuotationEnums::$department_type_view_name[$item['apply_user_department_type']])
                    : '',
                'lowest_discount_rate'       => $rate,
                'state'                      => $item['state'],
                'state_text'                 => static::$t->_('contract_status.' . $item['state']),

                // 归属大区
                'region_name'                => $item['region_name'],
            ];
        }

        return $result;
    }


    /**
     * @param $quoted_price_list_sn
     * @param $create_id
     * @param $note
     * @return mixed
     * @throws BusinessException
     * @throws ValidationException
     */
    public function cancel($quoted_price_list_sn, $create_id, $note)
    {
        Validation::validate([
            'create_id'            => $create_id,
            'quoted_price_list_sn' => $quoted_price_list_sn,
        ], self::$validate_cancel_params);

        $crmFlowService = new CrmFlowService();
        $applyInfo      = CrmQuotationApplyModel::findFirst([
            'conditions' => ' quoted_price_list_sn = :quotation_sn: ',
            'bind'       => [
                'quotation_sn' => $quoted_price_list_sn,
            ],
        ]);
        $us             = new UserService();
        $user           = $us->getUserById($create_id);
        $user           = [
            'id'                => $user->id,
            'name'              => $user->name,
            'organization_type' => $user->organization_type,
            'organization_id'   => $user->organization_id,
            'department'        => $user->getDepartment()->name ?? '',
            'department_id'     => $user->department_id,
            'job_title'         => $user->getJobTitle()->name ?? '',
            'job_title_id'      => $user->job_title,
            'nick_name'         => $user->nick_name ?? '',
        ];
        return $crmFlowService->cancel($applyInfo->id, $note, $user);
    }

    /**
     *
     *
     * @param $id
     * @param $quoted_price_list_sn
     * @param $user
     * @param int $source
     * @return array
     * @throws ValidationException
     * @throws BusinessException
     */
    public function detail($id, $quoted_price_list_sn, $user, $source = 1)
    {
        if (!empty($id)) {
            $item = CrmQuotationApplyModel::findFirst([
                'conditions' => 'id = :id: ',
                'bind'       => ['id' => $id],
            ]);
        } else {
            $item = CrmQuotationApplyModel::findFirst([
                'conditions' => 'quoted_price_list_sn = :quoted_no: ',
                'bind'       => ['quoted_no' => $quoted_price_list_sn],
            ]);
        }

        if (empty($item)) {
            throw new BusinessException('数据不存在 id = ' . $id, ErrCode::$BUSINESS_ERROR);
        }

        $id = $item->id;
        //报价管理模块
        if ($source == 1) {
            $service = new CrmFlowService();
            $request = $service->getRequest($id);
            if (empty($request)) {
                throw new BusinessException('CRM审批流不存在 id = ' . $id, ErrCode::$BUSINESS_ERROR);
            }
            //是否可以访问
            $flag = $service->isCanView($request, $user['id']);
            if ($flag !== true) {
                throw new ValidationException($flag, ErrCode::$VALIDATE_ERROR);
            }
        }
        //$source=2时销售合同审批的查看; $source=3时是新增合同时的查看,跟随【报价管理】-【详情操作】的权限即可,不用验证审批人
        if ($source == 2) {
            $contract_info = ContractConsumerService::getInstance()->getContractByQuotation($quoted_price_list_sn);
            if (empty($contract_info)) {
                throw new BusinessException('合同审批-查看报价单-获取合同失败', ErrCode::$CONTRACT_GET_INFO_NO_AUTH_ERROR);
            }
            $contract_req = (new ContractFlowService())->getRequest($contract_info['id']);
            if (empty($contract_req->id)) {
                throw new BusinessException('合同审批-查看报价单-获取合同审批流失败', ErrCode::$CONTRACT_GET_WORK_REQUEST_ERROR);
            }
            //是否可以访问
            $flag = ContractConsumerService::getInstance()->isCanView($contract_req);
            if ($flag !== true) {
                throw new ValidationException($flag, ErrCode::$VALIDATE_ERROR);
            }
        }

        $apiClient = new ApiClient('crm', '', 'getDetailBySn', static::$language);
        $apiClient->setParams([
            [
                'quoted_price_list_sn' => $item->quoted_price_list_sn,
                'flash_user_id'        => $item->apply_user_sn,
            ],
        ]);
        $apiClient->setHeader([
            'Accept-Language' => static::$language,
        ]);
        $result = $apiClient->execute();
        if ($result) {
            $data = $result['result'];
        }

        $data['audit_log'] = $this->getAuditLogs($request ?? null);

        return $data;
    }


    /**
     * @param $req
     * @return array
     */
    private function getAuditLogs($req)
    {
        return (new WorkflowServiceV2())->getAuditLogs($req);
    }

    /**
     *
     * @param $quotationNo
     * @return array
     * @throws ValidationException
     */
    public function auditLogsByQuotationNo($quotationNo)
    {
        Validation::validate([
            'quoted_price_list_sn' => $quotationNo,
        ], self::$validate_audit_log);

        $applyInfo = CrmQuotationApplyModel::findFirst([
            'conditions' => ' quoted_price_list_sn = :quotation_sn: ',
            'bind'       => [
                'quotation_sn' => $quotationNo,
            ],
        ]);
        if ($applyInfo) {
            $request = (new CrmFlowService())->getRequest($applyInfo->id);
            return $this->getAuditLogs($request);
        }

        return [];
    }


    /**
     *
     * 申请人ID 申请人名称
     *
     */
    public function customersList()
    {
        ini_set('memory_limit', '1024M');

        $list = CrmQuotationApplyModel::find([
            'columns' => 'apply_user_sn, apply_user_name',
        ])->toArray();
        $list = array_values(array_unique(array_merge(
            array_column($list, 'apply_user_sn'),
            array_column($list, 'apply_user_name')
        )));
        return $list;
    }


    /**
     * 部门的查找
     *
     * @param $param
     * @return array
     */
    public function SysDepartmentList($param)
    {
        if (!empty($param) && !empty($param['ids'])) {
            return SysDepartmentModel::find([
                'conditions' => 'id in ({deptIds:array})',
                'bind'       => [
                    'deptIds' => explode(',', $param['ids']),
                ],
            ])->toArray();
        } elseif (!empty($param) && !empty($param['ancestry'])) {
            return SysDepartmentModel::find([
                'conditions' => 'ancestry_v3 like :ancestry_chain:',
                'bind'       => [
                    'ancestry_chain' => $param['ancestry'] . '/%',
                ],
            ])->toArray();
        }
        return [];
    }

}