<?php
/**
 * Created by : PhpStorm
 * User: guan<PERSON>jian
 * Date: 2021/5/31
 * Time: 17:04
 */

namespace App\Modules\CrmQuotation\Services;

use App\Modules\CrmQuotation\Models\CrmQuotationApplyCouponModel as CouponModel;

class Coupon extends BaseService
{
    /**
     * 优惠券保存
     *
     * @param array $params
     * @param string $quoted_price_list_sn
     * @return bool
     * Author: guanlijian
     * Date: 2021/5/31 14:14
     */
    public function save(array $params = [], $quoted_price_list_sn = ''): bool
    {
        if (empty($params)) {
            return true;
        }
        try {
            //删除旧数据
            if (!empty($quoted_price_list_sn)) {
                $where  = [
                    'conditions' => 'quoted_price_list_sn = :quoted_price_list_sn: AND is_deleted = :is_deleted:',
                    'bind'       => [
                        'quoted_price_list_sn' => $quoted_price_list_sn,
                        'is_deleted'           => 0,
                    ],
                ];
                $coupon = (new CouponModel())->find($where);
                //如果有数据，则删除
                if (!empty($coupon)) {
                    $delete = (new CouponModel())->delete($quoted_price_list_sn);
                    if ($delete === false) {
                        return false;
                    }
                }
            }
            //保存新数据
            $saveInfo = [];
            foreach ($params as $k => $v) {
                $saveInfo[$k]['quoted_price_list_sn'] = $quoted_price_list_sn;
                $saveInfo[$k]['coupon_type']          = intval($v['coupon_type']);
                $saveInfo[$k]['coupon_num']           = intval($v['coupon_num']);
                $saveInfo[$k]['coupon_expire']        = intval($v['coupon_expire']);
            }
            $save = (new CouponModel())->batch_insert($saveInfo);
            if ($save === false) {
                return false;
            }
        } catch (\Throwable $t) {
            $log = ['error' => $t->getMessage(), 'code' => $t->getCode(), 'params' => $params];
            $this->logger->error('优惠券保存报错！' . json_encode($log, JSON_UNESCAPED_UNICODE));
            return false;
        }
        return true;
    }

    /**
     *获取优惠券
     *
     * @param string $quoted_price_list_sn
     * Author: guanlijian
     * Date: 2021/5/31 14:25
     */
    public function getNormal($quoted_price_list_sn = '')
    {
        try {
            if (empty($quoted_price_list_sn)) {
                return [];
            }
            $where  = [
                'conditions' => 'quoted_price_list_sn = :quoted_price_list_sn: AND is_deleted = :is_deleted:',
                'bind'       => [
                    'quoted_price_list_sn' => $quoted_price_list_sn,
                    'is_deleted'           => 0,
                ],
            ];
            $result = CouponModel::find($where);
            $result = empty($result) ? [] : $result->toArray();
        } catch (\Throwable $t) {
            $log = [
                'error'                => $t->getMessage(),
                'code'                 => $t->getCode(),
                'quoted_price_list_sn' => $quoted_price_list_sn,
            ];
            $this->logger->error('查询优惠券报错！' . json_encode($log, JSON_UNESCAPED_UNICODE));
            $result = [];
        }
        return $result;
    }

    /**
     * 删除coupon信息
     *
     * @param $quoted_price_list_sn
     * @return bool
     * @date 2022/3/17
     */
    public function deleteCoupon($quoted_price_list_sn)
    {
        //删除旧数据
        $where  = [
            'conditions' => 'quoted_price_list_sn = :quoted_price_list_sn: AND is_deleted = :is_deleted:',
            'bind'       => [
                'quoted_price_list_sn' => $quoted_price_list_sn,
                'is_deleted'           => 0,
            ],
        ];
        $coupon = (new CouponModel())->find($where);
        //如果有数据，则删除
        if (!empty($coupon)) {
            $delete = (new CouponModel())->delete($quoted_price_list_sn);
            if ($delete === false) {
                return false;
            }
        }
        return true;
    }
}