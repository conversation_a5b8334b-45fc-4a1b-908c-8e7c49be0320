<?php
/**
 * 权限管理 - 短信白名单设置
 */

namespace App\Modules\User\Controllers;

use App\Library\ErrCode;
use App\Library\BaseController;
use App\Library\Validation\Validation;
use App\Modules\User\Services\SmsWhitelistService;

class SmsWhitelistController extends BaseController
{
    /**
     * 获取短信国家码枚举
     * @Permission(action='permission.sms_whitelist')
     */
    public function getSmsNationEnumsAction()
    {
        $data = SmsWhitelistService::getInstance()->getSmsNationEnums();
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }

    /**
     * 工号检索
     * @Permission(action='permission.sms_whitelist')
     */
    public function getStaffAction()
    {
        $staff_id = $this->request->get('staff_id', 'trim');
        Validation::validate(['staff_id' => $staff_id], SmsWhitelistService::$staff_validation);
        $data = SmsWhitelistService::getInstance()->getStaffInfo($staff_id);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }

    /**
     * 获取短信白名单列表
     * @Permission(action='permission.sms_whitelist')
     */
    public function getListAction()
    {
        // 参数校验
        $params = $this->request->get();
        $data = SmsWhitelistService::getInstance()->getList($params);
        return $this->returnJson($data['code'], $data['message'], $data['data']);
    }

    /**
     * 移除短信白名单
     * @Permission(action='permission.sms_whitelist')
     */
    public function removeAction()
    {
        // 参数校验
        $staff_id = $this->request->get('staff_id', 'trim');
        Validation::validate(['staff_id' => $staff_id], SmsWhitelistService::$staff_validation);

        $data = SmsWhitelistService::getInstance()->removeOne($staff_id, $this->user);
        return $this->returnJson($data['code'], $data['message']);
    }

    /**
     * 保存短信白名单
     * @Permission(action='permission.sms_whitelist')
     */
    public function saveAction()
    {
        // 参数校验
        $params = $this->request->get();
        SmsWhitelistService::saveValidation($params);
        $data = SmsWhitelistService::getInstance()->saveOne($params, $this->user);
        return $this->returnJson($data['code'], $data['message']);
    }

}
