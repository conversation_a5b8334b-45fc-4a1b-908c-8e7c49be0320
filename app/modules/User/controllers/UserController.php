<?php

namespace App\Modules\User\Controllers;

use App\Library\BaseController;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Modules\User\Services\UserService;
use App\Traits\TokenTrait;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class UserController extends BaseController
{
    use TokenTrait;

    /**
     * 登录接口
     */
    public function loginAction()
    {
        // 生产环境, 禁止密码登录
        if (get_runtime_env() == 'pro' && !in_array(get_country_code(), [GlobalEnums::VN_COUNTRY_CODE, GlobalEnums::ID_COUNTRY_CODE])) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $this->t['login_limit']);
        }

        $account = $this->request->get('account','trim');
        $password = $this->request->get('password', 'trim');

        $validation = [
            'account'=> 'Required|StrLenGe:1|StrLenLe:10',
            'password' => 'Required|StrLenGe:1|StrLenLe:100'
        ];
        Validation::validate(['account'=>$account,'password'=>$password],$validation);

        /*
        $api = new ApiClient('fle', 'com.flashexpress.fle.svc.api.StaffAuthSvc', 'generateTokenByFbi',$this->locale);

        $api->setParams([['login'=>$account,'password'=>$password,]]);
        $result = $api->execute();
        if (isset($result['error'])){
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $result['error']);
        }

        $us = new UserService();
        $user = $us->getUserById($result['result']['id']);
        */
        $us = new UserService();
        $user = $us->getUserById($account);
        if (empty($user)) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, 'Account doesn\'t exist');
        }
        if ($user->state !=1){
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, 'Working status is invalid');
        }
        if (!$us->checkUserPassword($user, $password)){
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, 'Incorrect Account or Password');
        }

        try {
            $token = $this->generateToken($user->toArray());
            $token = $token->__toString();

            // token 缓存
            $us->saveUserToken(UserService::USER_LOGIN_PLATFORM_PC, $account, $token);

            return $this->returnJson(ErrCode::$SUCCESS, 'ok', ['token_type'=>'Bearer','token' => $token]);
        } catch (\Exception $e) {
            $this->logger->error('登录异常: ' . $e->getMessage());
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $this->t['retry_later']);
        }
    }

    /**
     * 登出接口
     * @Token
     */
    public function logoutAction()
    {
        $res = (new UserService())->clearUserToken(UserService::USER_LOGIN_PLATFORM_PC, $this->user['id'], $this->request->getToken());
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 我的信息
     * @Token
     */
    public function myAction()
    {
        $us = new UserService();

        $permissions = $us->getUserPermissions($this->user['id'], false);

        if ($permissions){
            $permissions = array_column($permissions->toArray(), 'key');
        }else{
            $permissions = [];
        }
        $staffInfo = explode(',',env('polling_staff',10000));
        $in = in_array($this->user['id'],$staffInfo);
        $polling = env('polling',false);
        $return = [
            'info' => [
                'staff_id' => $this->user['id'],
                'name' => $this->user['name'],
                'department' => $this->user['department'],
                'job_title' => $this->user['job_title'],
                'organization_type' => $this->user['organization_type'],
                'polling' => $in ||$polling,
                'hire_type' => $this->user['hire_type']
            ],
            'wf_info' => [], //$us->getStaffWfInfo($this->user['id']),
            'permissions' => $permissions,
        ];
        return $this->returnJson(ErrCode::$SUCCESS, '', $return);
    }

    /**
     * @Token
     * @return Response|ResponseInterface
     */
    public function wfAction()
    {
        $us = new UserService();
        $data = $us->getStaffWfInfo($this->user);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }

    /**
     * 获取登录二维码
     * @api https://yapi.flashexpress.pub/project/133/interface/api/84842
     */
    public function getQrCodeAction()
    {
        $params = trim_array($this->request->get());
        $validation = [
            'os'=> 'Required|StrLenGe:1|>>>:params error[os]',
            'browser'=> 'Required|StrLenGe:1|>>>:params error[browser]',
            'clientsd'=> 'Required|StrLenGe:1|>>>:params error[clientsd]',
            'clientid'=> 'Required|StrLenGe:1|>>>:params error[clientid]',
        ];
        Validation::validate($params, $validation);

        $res = (new UserService())->getLoginQrCode($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 查看扫码结果
     * @api https://yapi.flashexpress.pub/project/133/interface/api/84845
     */
    public function queryAuthAction()
    {
        $params = trim_array($this->request->get());
        $validation = [
            'qrtoken'=> 'Required|StrLenGe:1|>>>:params error[qrtoken]',
            'clientsd'=> 'Required|StrLenGe:1|>>>:params error[clientsd]',
            'clientid'=> 'Required|StrLenGe:1|>>>:params error[clientid]',
        ];
        Validation::validate($params, $validation);

        $res = (new UserService())->queryLoginQrCodeStatus($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * BY OA 跳 手机浏览器 生成临时票据 ticket
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/85169
     */
    public function getTicketAction()
    {
        $res = (new UserService())->generateTicket($this->user['id'], $this->request->getToken());
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * BY OA 跳 手机浏览器, ticket 置换 新token, 在 手机浏览器免登录OA
     * @api https://yapi.flashexpress.pub/project/133/interface/api/85172
     */
    public function getNewTokenAction()
    {
        $params = trim_array($this->request->get());
        $validation = [
            'ticket'=> 'Required|StrLenGe:1|>>>:params error[ticket]',
            'old_token'=> 'StrLenGe:0|>>>:params error[old_token]',
        ];
        Validation::validate($params, $validation);

        $res = (new UserService())->getNewToken($params['ticket'], $params['old_token'] ?? '');
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

}

