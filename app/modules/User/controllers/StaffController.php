<?php


namespace App\Modules\User\Controllers;


use App\Library\BaseController;
use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Budget\Services\BudgetService;
use App\Modules\Common\Services\StoreService;
use App\Modules\Organization\Models\SysDepartmentModel;
use App\Modules\Organization\Models\SysStoreModel;
use App\Modules\Organization\Services\DepartmentService;
use App\Modules\Reimbursement\Services\AddService;
use App\Modules\Reimbursement\Services\BaseService;
use App\Modules\User\Services\StaffService;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;
use App\Repository\HrStaffRepository;

class StaffController extends BaseController
{

    /**
     * @Token
     * @return Response|ResponseInterface
     */
    public function searchAction()
    {
        $q            = $this->request->get('q');
        $departmentId = $this->request->get('department_id', 'int');
        $storeId      = $this->request->get('store_id', 'trim');
        $state        = $this->request->get('state', 'trim');
        [$page, $pageSize] = $this->getPaginationParams();
        $condition = ['state' => $state];

        $list = StaffService::getInstance()->getStaffsByDepOrStore($q, $departmentId, $storeId, $page, $pageSize, $condition);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $list);
    }

    /**
     * @Token
     * 搜索员工信息
     * @return Response|ResponseInterface
     */
    public function searchStaffAction()
    {
        $q = $this->request->get('q', 'trim');
        [$page, $pageSize] = $this->getPaginationParams();
        $list = (new HrStaffRepository())->searchStaffs($q, $page, $pageSize);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $list);
    }

    /**
     * @Token
     * @return Response|ResponseInterface
     */
    public function departmentListAction()
    {
        $is_deleted = $this->request->get('is_deleted');
        $is_deleted = empty($is_deleted) ? GlobalEnums::IS_NO_DELETED : $is_deleted;
        $data = StaffService::getInstance()->departmentList($is_deleted);
        $sourceType = $this->request->get('source_type');
        if ($sourceType == BudgetService::ORDER_TYPE_1) {
            $departmentId = $this->request->get('department_id');
            foreach ($data as $datum) {
                if ($this->isHasNodeDepartment([$datum], $departmentId)) {
                    $data = [$datum];
                    break;
                }
            }
        }
        return $this->returnJson(ErrCode::$SUCCESS,'success',$data);
    }

    private function isHasNodeDepartment($departments, $departmentId)
    {
        foreach ($departments as $department) {
            if ($department['id'] == $departmentId) {
                return true;
            } else if (isset($department['children']) && $department['children']) {
                $result = $this->isHasNodeDepartment($department['children'], $departmentId);
                if ($result) {
                    return true;
                }
            }
        }
        return false;
    }


    /**
     * @Token
     * @return Response|ResponseInterface
     */
    public function storeListAction()
    {
        $param = $this->request->get();
        $list = (new StoreService())->getAllStoreList(true, $param);
        return $this->returnJson(ErrCode::$SUCCESS,'success', $list);
    }

    /**
     * Token
     */
    public function detailAction()
    {
        $data = $this->request->get();
        try {
            Validation::validate($data, BaseService::$validate_user);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = AddService::getInstance()->getUser($data['id'], false);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * @Token
     * @return Response|ResponseInterface
     * */
    public function getDepartmentListAction(){
        $param= $this->request->get();
        $res =  StaffService::getInstance()->getDepartmentList($param);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '',$res['data']);
        }
        return $this->returnJson($res['code'],$res['message']);
    }

    /**
     * 搜索员工信息(精简版)
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/87758
     * @return Response|ResponseInterface
     */
    public function getStaffListAction()
    {
        $keywords = $this->request->get('keywords', 'trim');
        $pageSize = $this->request->get('pageSize');
        $conditions = [
            'formal' => StaffInfoEnums::FORMAL_IN,
            'is_sub_staff' => StaffInfoEnums::IS_SUB_STAFF_NO,
            'page_size' => !empty($pageSize) ? (int)$pageSize : GlobalEnums::DEFAULT_PAGE_SIZE
        ];
        $list = (new HrStaffRepository())->getStaffInfoListByConditions($keywords, $conditions);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $list);
    }
}