<?php
/**
 * 审批流 - 节点审批人设置
 */

namespace App\Modules\User\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Library\BaseController;
use App\Modules\User\Services\AuditorSettingService;

class AuditorSettingController extends BaseController
{
    /**
     * 获取审批流列表
     * @Permission(action='permission.workflow_setting.auditor_setting')
     */
    public function getWorkflowListAction()
    {
        try {
            // 参数校验
            $params = $this->request->get();
            $validate_params = [
                'flow_name' => 'StrLenGeLe:0,100|>>>:param error[flow_name]',
                'node_name' => 'StrLenGeLe:0,100|>>>:param error[node_name]',
                'auditor_id' => 'StrLenGeLe:0,32|>>>:param error[auditor_id]',
            ];

            Validation::validate($params, $validate_params);

            if (empty($params['flow_name']) && empty($params['node_name']) && empty($params['auditor_id'])) {
                throw new ValidationException($this->t['workflow_setting_search_param_error'], ErrCode::$VALIDATE_ERROR);
            }

        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $data = AuditorSettingService::getInstance()->getWorkflowList($params);
        return $this->returnJson($data['code'], $data['message'], $data['data']);
    }

    /**
     * 获取审批流详情
     * @Permission(action='permission.workflow_setting.auditor_setting')
     */
    public function getWorkflowNodeInfoAction()
    {
        try {
            // 参数校验
            $params = $this->request->get();
            $validate_params = [
                'flow_id' => 'Required|IntGe:1|>>>:param error[flow_id]',
                'node_id' => 'Required|IntGe:1|>>>:param error[node_id]',
                'sub_node_id' => 'Required|IntGe:0|>>>:param error[sub_node_id]',
            ];

            Validation::validate($params, $validate_params);

        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $data = AuditorSettingService::getInstance()->getWorkflowNodeDetail($params);
        return $this->returnJson($data['code'], $data['message'], $data['data']);
    }


    /**
     * 更新审批人
     * @Permission(action='permission.workflow_setting.auditor_setting')
     */
    public function saveWorkflowNodeInfoAction()
    {
        try {
            // 参数校验
            $params = $this->request->get();

            $this->getDI()->get('logger')->info('审批流 - 审批人修改 - 请求参数: ' . json_encode($params, JSON_UNESCAPED_UNICODE));
            $this->getDI()->get('logger')->info('审批流 - 审批人修改 - 当前用户: ' . json_encode($this->user, JSON_UNESCAPED_UNICODE));

            $validate_params = [
                'flow_id' => 'Required|IntGe:1|>>>:param error[flow_id]',
                'node_id' => 'Required|IntGe:1|>>>:param error[node_id]',
                'sub_node_id' => 'Required|IntGe:0|>>>:param error[sub_node_id]',
            ];

            Validation::validate($params, $validate_params);

            if (!empty($params['auditor_ids']) && preg_match('/[^,0-9]+/', $params['auditor_ids'])) {
                throw new ValidationException('param error[auditor_ids]');
            }

        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $data = AuditorSettingService::getInstance()->saveWorkflowNodeAuditor($params);
        $this->getDI()->get('logger')->info('审批流 - 审批人修改 - 响应结果: ' . json_encode($data, JSON_UNESCAPED_UNICODE));

        return $this->returnJson($data['code'], $data['message'], $data['data']);
    }
}
