<?php


namespace App\Modules\User\Controllers;


use App\Library\BaseController;
use App\Library\ErrCode;
use App\Modules\User\Services\PermissionService;
use App\Modules\User\Services\UserService;

class PermissionController extends BaseController
{

    /**
     * @Permission(action='user.permission.grant')
     */
    public function grantAction()
    {
        $uid = $this->request->get('staff_id', 'int');
        $pids = $this->request->get('pids');
        if (empty($uid)){
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, 'Staff_id can not be empty',[]);
        }
        $us = new UserService();

        $user = $us->getUserById($uid);
        if (!$user){
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, 'Staff not exists',[]);
        }

        $res = $us->setUserPermissions($uid, $pids, $this->user);
        if ($res){
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', []);
        }else{
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, 'error', []);
        }
    }

    /**
     * @Permission(action='user.permission.grant')
     */
    public function listAction()
    {
        $uid = $this->request->get('staff_id', 'int');
        $us = new UserService();

        $permissions = $us->getUserPermissions($uid);
        if ($permissions){
            $permissions = $permissions->toArray();
            $permissions = array_filter($permissions,function ($item){
                return $item['type'] == 2;
            });
            $permissions = array_column($permissions, 'id');
        }else{
            $permissions = [];
        }

        return $this->returnJson(ErrCode::$SUCCESS, '', $permissions);
    }


    /**
     * 角色赋值权限
     * @Permission(action='user.permission.grant')
     */
    public function role_grantAction()
    {
        $role_id = $this->request->get('role_id', 'int');
        $pids = $this->request->get('pids');
        //这个id有个是0
        if (!isset($role_id)){
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, 'role_id can not be empty',[]);
        }
        $us = new UserService();

        $role = $us->getRoleById($role_id);
        if (!$role){
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, 'role not exists',[]);
        }

        $res = $us->setUserPermissionsByRoleId($role_id, $pids,$this->user);
        if ($res){
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', []);
        }else{
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, 'error', []);
        }
    }

    /**
     * @Permission(action='user.permission.grant')
     */
    public function role_listAction()
    {
        $role_id = $this->request->get('role_id', 'int');
        $us = new UserService();
        $permissions = $us->getRolePermissionIdsByRoleIdAndType($role_id);
        return $this->returnJson(ErrCode::$SUCCESS, '', $permissions);
    }




    /**
     * @Permission(action='user.permission.grant')
     */
    public function allAction()
    {
        $tree = (bool)$this->request->get('is_tree');

        $ps = new PermissionService();

        $permissions = $ps->getAllPermissions();
        if ($tree){
            $permissions = $permissions ? $permissions->toArray():[];
            $permissions = list_to_tree($permissions, 'id', 'ancestry', 'children');
            $permissions = array_values($permissions);
        }

        return $this->returnJson(ErrCode::$SUCCESS, '', $permissions);
    }


    /**
     * 获得所有角色
     * @Token
     */
    public function getRolesAction(){
        $roles = (new UserService())->getAllRoles();
        return $this->returnJson(ErrCode::$SUCCESS, '', $roles);
    }

    /**
    * 按照产品数据返回对应的xls导出的数据连接
    * @Date: 2022-03-22 16:29
    * @author: peak pan
    * @return: pw=flzxsqcysyhljt
    **/

    public function getRolesListAction()
    {
        $pw = $this->request->get('pw');
        if(md5($pw)=='f19f13791890fe50a956e61060ce5e9f'){
            $lock_key = md5('oa_getRolesList_' . $this->user['id']);
            $res = $this->atomicLock(function(){
                $user_xls=new UserService();
                return $user_xls->download();
            }, $lock_key, 30);

            if ($res['code'] == ErrCode::$SUCCESS) {
                return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
            }
            return $this->returnJson($res['code'], $res['message']);
        }else{
        echo  '密码错误';
        exit;
        }

     }





}
