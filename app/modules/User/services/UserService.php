<?php

namespace App\Modules\User\Services;

use App\Library\ApiClient;
use App\Library\BaseService;
use App\Library\Enums;
use App\Library\Enums\MenuKeyEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\PayEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\RedisClient;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use app\models\backyard\HcmStaffPermission;
use App\Models\backyard\SettingEnvModel;
use App\Models\bi\RolesModel;
use App\Models\oa\StaffLoginRecordModel;
use App\Modules\AccidentReport\Models\HrStaffInfoPosition;
use App\Modules\AgencyPayment\Services\AgencyPaymentPayService;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Contract\Services\ContractRemindListService;
use App\Modules\Contract\Services\ContractStoreRentingService;
use \App\Modules\Loan\Services\ListService AS LoanListService;
use App\Modules\Material\Services\LeaveAssetsManagerService;
use App\Modules\Material\Services\AssetTransferListService;
use App\Modules\Material\Services\WmsOutStorageService;
use App\Modules\Osovertime\Services\ApprovalOsOvertimeService;
use App\Modules\Overtime\Services\ApprovalOvertimeService;
use App\Modules\PaperDocument\Services\ConfirmationService;
use App\Modules\Pay\Services\OnlineService;
use \App\Modules\Reimbursement\Services\ListService AS ReimbursementListService;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Hc\Services\BudgetService;
use App\Modules\Kpi\Services\ActivityStaffService;
use App\Modules\OrdinaryPayment\Services\OrdinaryPaymentListService;
use App\Modules\Pay\Services\PayService;
use App\Modules\Pay\Services\FinalPayService;
use App\Modules\Organization\Models\HrJobTitleModel;
use App\Modules\Purchase\Services\PaymentService;
use App\Modules\School\Services\AuditService;
use App\Modules\SimManage\Services\CompanyMobileService;
use App\Modules\SimManage\Services\FeedbackService;
use App\Modules\Third\Services\ByWorkflowService;
use App\Modules\JobTransfer\Services\ListService as JobTransferListService;
use App\Modules\Transfer\Services\ListService;
use App\Modules\Salary\Services\ApplyService;
use App\Modules\User\Models\BiRoleModel;
use App\Modules\User\Models\BiStaffInfoPositionModel;
use App\Modules\User\Models\PermissionModel;
use App\Modules\User\Models\RoleModel;
use App\Modules\User\Models\RolePermissionModel;
use App\Modules\User\Models\StaffInfoModel;
use App\Modules\User\Models\HrStaffInfoModel;
use App\Models\backyard\SysDepartmentModel;
use App\Modules\Payment\Services\StoreRentingListService;
use App\Modules\User\Models\StaffPermissionModel;
use App\Modules\Wages\Models\WagesModel;
use App\Modules\Warehouse\Services\RequirementService;
use App\Modules\Warehouse\Services\ThreadService;
use App\Modules\Workflow\Models\WorkflowRequestModel;
use App\Modules\Workflow\Models\WorkflowAuditLogModel;
use App\Modules\Workflow\Services\CarbonCopyService;
use App\Modules\Workflow\Services\WorkflowServiceV2;
use App\Modules\Workflow\Services\FYRService;
use App\Repository\HrStaffRepository;
use App\Util\RedisExpire;
use App\Util\RedisKey;
use Exception;
use Phalcon\Mvc\Model;
use Phalcon\Mvc\Model\ResultsetInterface;
use Stringy\StaticStringy;
use App\Util\PasswordHash;
use App\Modules\AccessData\Services\AccessDataFlowService;
use App\Modules\AccessData\Services\AccessDataDetailService;
use App\Modules\ReserveFund\Services\ApplyService AS ReserveFundApplyService;
use App\Library\Enums\ByWorkflowEnums;
use App\Traits\TokenTrait;

class UserService extends BaseService
{
    use TokenTrait;

    // 用户登录平台
    const USER_LOGIN_PLATFORM_PC = 'pc';
    const USER_LOGIN_PLATFORM_MOBILE = 'mobile';

    // OA 扫码登录设备类型枚举
    protected static $oa_qr_equipment_type = 15;

    // OA 二维码扫码成功状态码
    protected static $oa_qr_authorized_state = 2;

    // OA红点请求来源: app-用户手动操作
    const OA_REDDOT_GET_SRC_APP = 'app';
    const OA_REDDOT_GET_SRC_PUSH = 'push';

    // OA红点获取渠道(BY外层OA红点总数, 获取指定模块的红点数)
    private static $instance;

    const OA_REDDOT_CHANNEL_BY_OUTER = 'by_outer';
    const OA_REDDOT_CHANNEL_BY_MENU = 'by_menu';

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * @param $user
     * @param $password
     * @return bool
     */
    public function checkUserPassword($user, $password)
    {
        $hash = new PasswordHash(10, false);
        $bool = $hash->CheckPassword($password, $user->encrypted_password);
        if (!$bool) {
            return false;
        }

        return true;
    }

    /**
     * nick_name toArray没有。
     * @param $id
     * @return mixed
     */
    public function getUserById($id)
    {
        $item = StaffInfoModel::findFirst(
            [
                'conditions' => 'id = ?1',
                'bind' => [
                    1 => $id,
                ]
            ]
        );

        if (!empty($item)) {
            $bi_staff = $this->getUserByIdInRbi($id);
            if (!empty($bi_staff)) {
                $item->node_department_id = $bi_staff->node_department_id;
                $item->sys_department_id = $bi_staff->sys_department_id;
                $item->nick_name = $bi_staff->nick_name ?? '';
                $item->state = $bi_staff->state;
                $item->job_title = $bi_staff->job_title;
                $item->wait_leave_state = $bi_staff->wait_leave_state;
                $item->identity = $bi_staff->identity ?? '';
                $item->sys_store_id = $bi_staff->sys_store_id;
                $item->email = $bi_staff->email;
                $item->personal_email = $bi_staff->personal_email;
            }
        }

        return $item;
    }

    public function getRoleById($id)
    {
        return RoleModel::findFirst(
            [
                'conditions' => 'id = ?1',
                'bind' => [
                    1 => $id,
                ]
            ]
        );
    }




    /**
     * @param $uid
     */
    public function getUserDepartment($uid)
    {
    }

    /**
     * @param $uid
     */
    public function getUserJobTitle($uid)
    {
    }

    /**
     * @param $uid
     * @param $pids
     * @param $login_user
     * @return bool
     */
    public function setUserPermissions($uid, $pids, $login_user = [])
    {
        $sp = StaffPermissionModel::findFirst(
            [
                'conditions' => 'staff_id = ?1',
                'bind' => [
                    1 => $uid,
                ]
            ]
        );

        // 权限设置日志
        $permission_set_log = [
            'staff_id' => $uid,
            'old_permission_ids' => '',
            'new_permission_ids' => '',
            'updated_id' => $login_user['id'],
            'updated_at' => date('Y-m-d H:i:s')
        ];

        if ($sp) {
            $permission_set_log['old_permission_ids'] = $sp->permission_ids;
            $sp->permission_ids = implode(',', $pids);
        } else {
            $sp = new StaffPermissionModel();
            $sp->staff_id = $uid;
            $sp->permission_ids = implode(',', $pids);
        }

        $sp->is_granted = 1;
        $sp->last_updated_at = date('Y-m-d H:i:s');

        // 写日志
        $permission_set_log['new_permission_ids'] = $sp->permission_ids;
        $this->logger->info(['menu_permission_set' => $permission_set_log]);

        return $sp->save();
    }


    /**
     * @param $role_id
     * @param $pids
     * @param $user
     * @return bool
     */
    public function setUserPermissionsByRoleId($role_id, $pids,$user)
    {
        $sp = RolePermissionModel::findFirst(
            [
                'conditions' => 'role_id = ?1',
                'bind' => [
                    1 => $role_id,
                ]
            ]
        );
        $before = '';
        $pids_str = implode(',', $pids);
        if ($sp) {
            $before = $sp->permission_ids;
            $sp->permission_ids = $pids_str;
        } else {
            $sp = new RolePermissionModel();
            $sp->role_id = $role_id;
            $sp->permission_ids = $pids_str;
        }
        $sp->is_granted = 1;
        $sp->last_updated_at = date('Y-m-d H:i:s');

        $this->logger->info("员工=".$user['id']."===修改role_id=".$role_id."===before=".$before."===after=".$pids_str);
        return $sp->save();
    }


    /**
     * @param $uid
     * @param bool $is_find_role
     * @param bool $is_all_permission
     * @return ResultsetInterface
     */
    public function getUserPermissions($uid, $is_find_role = false, $is_all_permission = false)
    {
        $ps = new PermissionService();
        $permissions = null;
        if ($is_all_permission || $uid == env('sa_id', 10000)) {
            $permissions = $ps->getAllPermissions();
        } else {
            $granted = StaffPermissionModel::findFirst(
                [
                    'conditions' => 'staff_id = ?1',
                    'bind' => [
                        1 => $uid,
                    ]
                ]
            );
            if ($granted) {
                $pids = array_values(array_filter(array_unique(explode(',', $granted->permission_ids))));
                $permissions = $ps->getPermissionsByIds($pids);
            }

            // 如果找角色，并且如果按工号找出来的是空，去查角色，并按角色找
            if ($is_find_role && (empty($permissions) || empty($permissions->toArray()))) {
                $role_ids = BiStaffInfoPositionModel::getRoleIds($uid);
                $pids = $this->getRolePermissionIds($role_ids);
                if ($pids) {
                    $permissions = $ps->getPermissionsByIds($pids);
                }
            }
        }

        return $permissions;
    }


    /**
     * 根据角色ids获得对应权限。
     * @param $role_ids
     * @return array
     */
    public function getRolePermissionIds($role_ids){
        if(empty($role_ids)){
            return [];
        }
        $permissions = RolePermissionModel::find(
            [
                'conditions' => 'role_id in ({ids:array})',
                'bind' => ['ids'=>$role_ids]
            ]
        )->toArray();

        $ans = [];
        if(!empty($permissions)){
            foreach ($permissions as $permission){
                if(!empty($permission['permission_ids'])){
                    $ans = array_merge($ans,explode(",",$permission['permission_ids']));
                }
            }
        }
        return array_values(array_filter(array_unique($ans)));
    }


    /**
     * 根据角色id去获得对应权限，只要type=2操作的
     * @param $role_id
     * @return array
     */
    public function getRolePermissionIdsByRoleIdAndType($role_id){
        $role_permission = RolePermissionModel::findFirst(
            [
                'conditions' => 'role_id = :role_id:',
                'bind' => ['role_id'=>$role_id]
            ]
        );
        if(empty($role_permission)){
            return [];
        }
        $ps = new PermissionService();
        $pids = explode(',', $role_permission->permission_ids);
        //这如果是空的，pids = [""]，所以不会报错
        $permissions = $ps->getPermissionsByIds($pids);
        if ($permissions){
            $permissions = $permissions->toArray();
            $permissions = array_filter($permissions,function ($item){
                return $item['type'] == 2;
            });
            $permissions = array_column($permissions, 'id');
        }else{
            $permissions = [];
        }
        return $permissions;
    }


    /**
     * @param $uid
     * @param $permission_type
     * @return array
     */
    public function getUserPermissionKeys($uid, $permission_type = 'action')
    {
        $permissions = $this->getUserPermissions($uid,false);
        $permissions = $permissions ? $permissions->toArray() : [];
        $keys = [];

        $permission_type_len = mb_strlen($permission_type) + 1;
        foreach ($permissions as $k => $v) {
            if (StaticStringy::startsWith($v['key'], $permission_type)) {
                $keys[] = StaticStringy::substr($v['key'], $permission_type_len);
            }
        }

        return $keys;
    }

    /**
     * @param $uid
     * @param $type
     * @return
     */
    public function getAuditRequestCount($uid, $type)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('distinct r.id');
        $builder->from(['r' => WorkflowRequestModel::class]);
        switch ($type) {
            case '14-1':
                // 租房合同我创建的
                $builder->where(
                    'r.create_staff_id = :uid: and r.biz_type = :biz_type:',
                    ['uid' => $uid, 'biz_type' => Enums::WF_STORE_RENTING_CONTRACT_TYPE]
                );
                break;
            case '14-2':
                // 租房合同我审核的 （通过不通过都有）
                $builder->join(WorkflowAuditLogModel::class, 'l.request_id=r.id', 'l');
                $builder->where(
                    'l.staff_id = :uid: and l.staff_id != :uid: and r.biz_type = :biz_type:',
                    ['uid' => $uid, 'biz_type' => Enums::WF_STORE_RENTING_CONTRACT_TYPE]
                );
                break;
            case '14-3':
                // 租房合同审核拒绝的
                $builder->join(WorkflowAuditLogModel::class, 'l.request_id=r.id', 'l');
                $builder->where(
                    'l.staff_id = :uid: and  r.biz_type = :biz_type: and l.audit_action = ' . Enums::WF_ACTION_REJECT,
                    ['uid' => $uid, 'biz_type' => Enums::WF_STORE_RENTING_CONTRACT_TYPE]
                );
                break;
        }
        return $builder->getQuery()->execute()->count();
    }


    /**
     * @param $id
     * @return mixed
     */
    public function getUserByIdInRbi($id)
    {
        return HrStaffInfoModel::findFirst(
            [
                'conditions' => 'staff_info_id = ?1',
                'bind' => [
                    1 => intval($id),
                ]
            ]
        );
    }

    /**
     * @param $ids
     * @return Model
     */
    public function getUserListByStaffIds($ids)
    {
        return HrStaffInfoModel::find([
                'conditions' => 'staff_info_id IN ({ids:array})',
                'bind' => ['ids' => array_values($ids)]
        ]);
    }

    /**
     * 获取需要待审核的数: 来自OA系统审批流
     * @param $uid
     * @param $type
     * @param int $process_state 1-待处理; 3-已征询
     * @param string $extra 额外字段,各模块自定义
     * @return int|mixed
     */
    public function getWaitAuditNum($uid, $type, int $process_state = 1, $extra = '')
    {
        // 通用的取数类型 - 待审批: 审批流 有 意见征询的
        $common_audit_type_item = [
            Enums::WF_STORE_RENTING_CONTRACT_TYPE,
            Enums::WF_STORE_RENTING_CONTRACT_RENEWAL_TYPE,
            Enums::WF_CONTRACT_GPMD_BIZ_TYPE,
            Enums::WF_LOAN_BACK_TYPE,
            Enums::WF_PURCHASE_APPLY,
            Enums::WF_PURCHASE_ORDER,
            Enums::WF_PURCHASE_ACCEPTANCE,
            Enums::WF_PURCHASE_SAMPLE,
            Enums::WF_PAYMENT_STORE_RENTING_TYPE,
            Enums::ORDINARY_PAYMENT_BIZ_TYPE,
            Enums::WF_RESERVE_FUND_APPLY,
            Enums::WF_RESERVE_FUND_RETURN,
            Enums::WF_CRM_QUOTATION,
            Enums::WF_VENDOR_BIZ_TYPE,
            Enums::WF_VENDOR_GRADE_BIZ_TYPE,
            Enums::WF_BUDGET_ADJUST_TYPE,
            Enums::WF_CHEQUE_APPLY_BIZ_TYPE,
            Enums::DEPOSIT_RETURN_BIZ_TYPE,
            Enums::WF_PURCHASE_PAYMENT,
            Enums::WF_LOAN_TYPE,
            Enums::WF_REIMBURSEMENT_TYPE
        ];

        // 其他的取数类型 - 待审批: 审批流 无 意见征询的
        $audit_no_fyr_type_item = [
            Enums::WF_SALARY_APPLY,
            Enums::WF_WAGES_TYPE,
            Enums::BUDGET_OB_TYPE,
            Enums::WF_STORE_RENTING_CONTRACT_INVALID_TYPE,
            Enums::WF_STORE_RENTING_CONTRACT_TERMINAL_TYPE,
            Enums::WF_CONTRACT_ELECTRONIC_BIZ_TYPE,
            Enums::WF_CONTRACT_ELECTRONIC_REVIEW_SIGN_BIZ_TYPE,
        ];

        if (in_array($type, $common_audit_type_item)) {
            return WorkflowServiceV2::getInstance()->getBizPendingCountByProcessState([$type], $uid, true, $process_state);

        } else if (in_array($type, $audit_no_fyr_type_item)) {
            return WorkflowServiceV2::getInstance()->getBizPendingCountByProcessState([$type], $uid, false, $process_state);

        } else {
            // 其他需要各自独立实现的取数逻辑
            switch ($type) {
                // 其他合同，加了个特殊的需要去掉租房合同
                case Enums::WF_OTHER_CONTRACT_TYPE:
                    $biz_type = [
                        Enums::WF_CONTRACT_TYPE1,
                        Enums::WF_CONTRACT_TYPE20,
                        Enums::WF_CONTRACT_TYPE21,
                        Enums::WF_CONTRACT_TYPE22,
                        Enums::WF_CONTRACT_TYPE23,
                        Enums::WF_CONTRACT_TYPE24
                    ];
                    return WorkflowServiceV2::getInstance()->getBizPendingCountByProcessState($biz_type, $uid, true, $process_state);

                case Enums::WF_PAY_TYPE:
                    // 付款模块审批
                    $pay_module_status = (new EnumsService())->getSystemPayModuleStatus();
                    if ($pay_module_status) {
                        if ($extra == 'pay') {
                            // 我的支付
                            return PayService::getInstance()->getAuditPendingCount($type, $uid, $process_state);

                        } else if ($extra == 'bank') {
                            // 银行支付
                            return FinalPayService::getInstance()->getAuditPendingCount($type, $uid);
                        }
                    }

                case Enums::WF_ACCESS_DATA_WORK_ORDER_TYPE:
                    // 取数工单系统待审批 [因会签类型节点,单独统计待审批数]
                    return (new AccessDataFlowService())->getUserWaitingAuditCount($uid, $process_state);

                default:
            }
        }

        return 0;
    }

    /**
     * 获取需要待审核的数: 来自BY系统审批流
     * @param $uid
     * @param $type
     * @return int|mixed
     */

    public function getWaitAuditNumFromBy($uid, $type)
    {
        switch ($type) {
            case Enums::WF_HC_AUDIT:
                // hc预算--TODO by出一个单独获取总数的接口
                $params = [];
                $params['tab'] = 1;
                $params['type'] = 2;
                $params['status'] = 0;
                $list = BudgetService::getInstance()->budgetList($params, $uid);
                return $list['total_count'];
            case Enums::WF_JOB_TRANSFER_TYPE:
                // 转岗
                if (in_array(get_country_code(), [GlobalEnums::TH_COUNTRY_CODE, GlobalEnums::MY_COUNTRY_CODE, GlobalEnums::PH_COUNTRY_CODE])) { //泰国用新版转岗
                    return JobTransferListService::getInstance()->getAuditCount([
                        'approval_state' => 1,
                        'approval_id'    => $uid,
                        'type'           => Enums::WF_JOB_TRANSFER_TYPE,
                    ]);
                } else {
                    return ListService::getInstance()->getAuditCount(['approval_state' => 1,'approval_id' => $uid]);
                }
                break;
            case Enums::WF_JOB_TRANSFER_BP_TYPE:
                return JobTransferListService::getInstance()->getAuditCount([
                    'approval_state' => 1,
                    'approval_id'    => $uid,
                    'type'           => Enums::WF_JOB_TRANSFER_BP_TYPE,
                ]);
                return ListService::getInstance()->getAuditCount(['approval_state' => 1,'approval_id' => $uid]);
            case ByWorkflowEnums::BY_BIZ_TYPE_WAREHOUSE_CHANGE_STATUS:
                // 仓库信息变更审批: 状态变更 + 网点变更
                $pending_result = (new ByWorkflowService())->pendingCount([
                    'approval_id' => $uid,
                    'biz_type' => [ByWorkflowEnums::BY_BIZ_TYPE_WAREHOUSE_CHANGE_STATUS, ByWorkflowEnums::BY_BIZ_TYPE_WAREHOUSE_CHANGE_STORE],
                ]);
                return array_sum($pending_result);
            case ByWorkflowEnums::BY_BIZ_TYPE_AGENCY_PAYMENT:
                //代理支付- 待审核
                $pending_result = (new ByWorkflowService())->pendingCount([
                    'approval_id' => $uid,
                    'biz_type' => [ByWorkflowEnums::BY_BIZ_TYPE_AGENCY_PAYMENT],
                ]);
                return array_sum($pending_result);
            case ByWorkflowEnums::BY_BIZ_TYPE_BUDGET_WITHHOLDING:
                //预算管理-费用预提- 待审核
                $pending_result = (new ByWorkflowService())->pendingCount([
                    'approval_id' => $uid,
                    'biz_type' => [ByWorkflowEnums::BY_BIZ_TYPE_BUDGET_WITHHOLDING],
                ]);
                return array_sum($pending_result);
            case ByWorkflowEnums::WF_APPROVAL_OVERTIME:
                $count = ApprovalOvertimeService::getInstance()->getWfCount($uid);
                return $count;
            case ByWorkflowEnums::WF_APPROVAL_OS_OVERTIME:
                $count = ApprovalOsOvertimeService::getInstance()->getWfCount($uid);
                return $count;
            case ByWorkflowEnums::BY_BIZ_TYPE_WAREHOUSE_THREAD_PRICE:
                //仓库报价审核-待处理 - 待审核
                $pending_result = (new ByWorkflowService())->pendingCount([
                    'approval_id' => $uid,
                    'biz_type'    => [ByWorkflowEnums::BY_BIZ_TYPE_WAREHOUSE_THREAD_PRICE],
                ]);
                return array_sum($pending_result);
            default:
                break;
        }

        return 0;
    }


    /**
     * 获得该员工，该意见征询待回复数量
     * @param $uid
     * @param $type
     * @return mixed
     */
    public function getConsultationWaitReplyNum($uid, $type)
    {
        if (empty($uid) || empty($type)) {
            return 0;
        }

        // version-1: 仅审批 不含支付逻辑 且 符合通用取数逻辑的
        // version-2: v18276 中, 审批完需支付的业务模块, 在取征询待回复红点时, 只取 待审批 + 待回复 (不再取审批终态 + 待支付的), 跟 通用取数逻辑保持一致
        // 有支付环节的模块(6): 借款申请/报销申请/采购付款申请/网点备用金申请/普通付款/租房付款
        $common_audit_type_item = [
            // version-1
            Enums::WF_VENDOR_BIZ_TYPE,
            Enums::WF_VENDOR_GRADE_BIZ_TYPE,
            Enums::WF_PURCHASE_APPLY,
            Enums::WF_PURCHASE_ORDER,
            Enums::WF_PURCHASE_ACCEPTANCE,
            Enums::WF_PURCHASE_SAMPLE,
            Enums::WF_BUDGET_ADJUST_TYPE,
            Enums::WF_RESERVE_FUND_RETURN,
            Enums::WF_CHEQUE_APPLY_BIZ_TYPE,
            Enums::DEPOSIT_RETURN_BIZ_TYPE,
            Enums::WF_CONTRACT_GPMD_BIZ_TYPE,

            // version-2
            Enums::WF_LOAN_TYPE,
            Enums::WF_REIMBURSEMENT_TYPE,
            Enums::WF_PURCHASE_PAYMENT,
            Enums::WF_RESERVE_FUND_APPLY,
            Enums::ORDINARY_PAYMENT_BIZ_TYPE,
            Enums::WF_PAYMENT_STORE_RENTING_TYPE,
        ];

        if (in_array($type, $common_audit_type_item)) {
            return FYRService::getInstance()->getBizConsultationPendingCount([$type], $uid);

        } else {
            switch ($type) {
                // 其他合同
                case Enums::WF_OTHER_CONTRACT_TYPE:
                    //销售合同类
                    $biz_type_item = [
                        Enums::WF_CONTRACT_TYPE1,
                        Enums::WF_CONTRACT_TYPE20,
                        Enums::WF_CONTRACT_TYPE21,
                        Enums::WF_CONTRACT_TYPE22,
                        Enums::WF_CONTRACT_TYPE23,
                        Enums::WF_CONTRACT_TYPE24
                    ];

                    return FYRService::getInstance()->getBizConsultationPendingCount($biz_type_item, $uid);

                // 租房合同
                case Enums::WF_STORE_RENTING_CONTRACT_TYPE:
                    $biz_type_item = [
                        Enums::WF_STORE_RENTING_CONTRACT_TYPE,// 租房合同申请
                        Enums::WF_STORE_RENTING_CONTRACT_RENEWAL_TYPE // 租房合同续签
                    ];

                    return ContractStoreRentingService::getInstance()->getConsultationPendingCount($biz_type_item, $uid);

                // 支付模块
                case Enums::WF_PAY_TYPE:
                    return PayService::getInstance()->getConsultationPendingCount($type, $uid);
            }
        }

        return 0;
    }

    /**
     * @param array $staffIds
     * @return array
     */
    public function getUserInfos($staffIds)
    {
        $data = [];
        foreach ($staffIds as $staffId) {
            $m = $this->getUserById($staffId);
            if (!empty($m)) {
                $data[$staffId] = [
                    'id' => $staffId,
                    'name' => $this->getNameAndNickName($m->name ?? '', $m->nick_name ?? ''),
                    'department' => $m->getDepartment()->name ?? '',
                    'job_title' => $m->getJobTitle()->name ?? '',
                    'state' => $m->state ?? '1',
                    'wait_leave_state' => $m->wait_leave_state ?? '0',
                ];
            } else {
                $data[$staffId] = [
                    'id' => $staffId,
                    'name' => '',
                    'department' => '',
                    'job_title' => '',
                    'state' => '1',
                    'wait_leave_state' => '0',
                ];
            }
        }

        return $data;
    }

    /**
     * 待付款数统计 + 采购付款申请单，报销
     * @param int $user_id
     * @param int $type
     * @return int
     */
    public function getWaitingPayCount(int $user_id, int $type = 0)
    {
        $count = 0;

        //支付模块开启后,旧模块还有待支付的老数据,统计数量不能直接返回0
        //$pay_module_status = (new EnumsService())->getPayModuleStatus();
        switch ($type) {
            // 付款管理 - 网点租房付款业务
            case Enums::WF_PAYMENT_STORE_RENTING_TYPE:
                $count = StoreRentingListService::getInstance()->getWaitingPayCount($user_id);
                break;

            // 普通付款
            case Enums::ORDINARY_PAYMENT_BIZ_TYPE:
                $count = OrdinaryPaymentListService::getInstance()->getWaitingPayCount($user_id);
                break;

            //薪资发放审批
            case Enums::WF_SALARY_APPLY:
                $count = ApplyService::getInstance()->getWaitingPayCount($user_id);
                break;

            //薪资扣款审批
            case Enums::WF_WAGES_TYPE:
                $count = $this->getSalaryPayCount($user_id);
                break;

            //备用金待支付
            case Enums::WF_RESERVE_FUND_APPLY:
                $count = ReserveFundApplyService::getInstance()->getWaitingPayCount($user_id);
                break;

            //采购付款单待支付
            case Enums::WF_PURCHASE_PAYMENT:
                $count = PaymentService::getInstance()->getPayPendingCount($user_id);
                break;

            //借款申请待支付
            case Enums::WF_LOAN_TYPE:
                $count = LoanListService::getInstance()->getPayPendingCount($user_id);
                break;

            //报销申请待支付
            case Enums::WF_REIMBURSEMENT_TYPE:
                $count = ReimbursementListService::getInstance()->getPayPendingCount($user_id);
                break;
            //代理支付-待支付
            case Enums::WF_AGENCY_PAYMENT_BIZ_TYPE:
                $count = AgencyPaymentPayService::getInstance()->getPayPendingCount(['id' => $user_id]);
                break;
            default:
        }

        return $count;
    }

    /**
     * 薪酬扣款审批-我的支付count
     * @param $uid
     * @return mixed
     */
    public function getSalaryPayCount($uid)
    {
        $wage_pay_staff_id = (new \App\Modules\Wages\Services\BaseService())->getWagesPayStaffIds();
        if (empty($uid) || !in_array($uid, $wage_pay_staff_id)) {
            return 0;
        }

        $builder = $this->modelsManager->createBuilder();
        $column_str = 'count(w.id) as total';
        $builder->columns($column_str);
        $builder->from(['w' => WagesModel::class]);
        $builder->andWhere('w.pay_status = 1');
        $builder->andWhere('w.is_pay_module = :is_pay_module:', ['is_pay_module' => PayEnums::BIZ_DATA_IS_PAY_MODULE_NO]);
        $builder->andWhere('w.status =' . Enums::WF_STATE_APPROVED);
        $total_info = $builder->getQuery()->getSingleResult();
        return intval($total_info->total);
    }

    /**
     * 返回当前用户待回复数
     *
     * @param $user
     * @param bool $is_all_permission
     * @param string $channel
     * @return array|mixed
     */
    public function getStaffWfInfo($user, $is_all_permission = false, string $channel = '')
    {
        $staff_id = $user['id'];
        $redis_key = $this->getStaffUnReadRedisKey($staff_id);
        $json_str = '';

        //缓存开关，是否缓存，false不开启缓存
        $is_unread_msg_cache = env('is_unread_msg_cache', false);
        if ($is_unread_msg_cache) {
            $json_str = RedisClient::getInstance()->getClient()->get($redis_key);
        }

        //缓存时间
        $expire_time_seconds = env('staff_un_read_num_seconds', RedisExpire::HALF_HOUR);

        //对特定的人没有缓存，跟轮询的人一样
        $staff_ids = explode(',', env('polling_staff', 10000));
        $in = in_array($staff_id, $staff_ids);

        // 菜单待办统计key配置(各业务待办小红点)
        $wf_info = MenuKeyEnums::$menu_red_dot_list;

        //为空，或者没开启缓存，或者需要轮询的人
        if (empty($json_str) || $in) {
            //当前登录者权限列表组
            $permissions = $this->getUserPermissions($staff_id, false, $is_all_permission);
            if ($permissions) {
                $permissions = array_column($permissions->toArray(), 'key');
            } else {
                return $wf_info;
            }

            //拥有小红点功能的菜单组
            // 来自BY外层的红点取数(获取指定模块的红点)
            if ($channel == static::OA_REDDOT_CHANNEL_BY_OUTER) {
                $menu_red_hot = MenuKeyEnums::$left_menu_hot_keys_from_by_outer;
            } else {
                // 默认所有菜单的红点
                $menu_red_hot = MenuKeyEnums::$left_menu_hot_keys;
            }

            $has_menu_red_hot_permission = array_intersect($menu_red_hot, $permissions);
            if (!$has_menu_red_hot_permission) {
                //没有可以查看的左侧菜单权限
                return $wf_info;
            }

            foreach ($has_menu_red_hot_permission as $red_key) {
                switch ($red_key) {
                    case MenuKeyEnums::CONTRACT_OTHER_WAIT_AUDIT:
                        //其他合同申请待审核（不包括租房合同）
                        $wf_info['contract_pending_count'] = $this->getWaitAuditNum($staff_id, Enums::WF_OTHER_CONTRACT_TYPE, GlobalEnums::AUDIT_TAB_PENDING);

                        // 其他合同申请 - 征询中
                        $wf_info['contract_inquired_pending_count'] = $this->getWaitAuditNum($staff_id, Enums::WF_OTHER_CONTRACT_TYPE, GlobalEnums::AUDIT_TAB_CONSULTED);

                        // 其他合同申请 - 征询已回复
                        $wf_info['contract_inquired_replied_count'] = $this->getWaitAuditNum($staff_id, Enums::WF_OTHER_CONTRACT_TYPE, GlobalEnums::AUDIT_TAB_REPLIED);
                        break;
                    case MenuKeyEnums::CONTRACT_ELECTRONIC_WAIT_AUDIT:
                        // 电子合同 - 合同内容审核 - 待审核
                        $wf_info['contract_electronic_pending_count'] = $this->getWaitAuditNum($staff_id, Enums::WF_CONTRACT_ELECTRONIC_BIZ_TYPE, GlobalEnums::AUDIT_TAB_PENDING);

                        // 电子合同 - 客户签字审核 - 待审核
                        $wf_info['contract_electronic_customer_sign_pending_count'] = $this->getWaitAuditNum($staff_id, Enums::WF_CONTRACT_ELECTRONIC_REVIEW_SIGN_BIZ_TYPE, GlobalEnums::AUDIT_TAB_PENDING);
                        break;

                    case MenuKeyEnums::CONTRACT_STORE_RENTING_WAIT_AUDIT:
                        //合同管理～合同回复～网点租房合同~待处理
                        //15476 合同审核增加了作废审核+终止审核 这两种没有征询
                        $contract_apply_num = $this->getWaitAuditNum($staff_id, Enums::WF_STORE_RENTING_CONTRACT_TYPE, GlobalEnums::AUDIT_TAB_PENDING);
                        $contract_invalid_num = $this->getWaitAuditNum($staff_id, Enums::WF_STORE_RENTING_CONTRACT_INVALID_TYPE, GlobalEnums::AUDIT_TAB_PENDING);
                        $contract_terminal_num = $this->getWaitAuditNum($staff_id, Enums::WF_STORE_RENTING_CONTRACT_TERMINAL_TYPE, GlobalEnums::AUDIT_TAB_PENDING);
                        $contract_renewal_num = $this->getWaitAuditNum($staff_id, Enums::WF_STORE_RENTING_CONTRACT_RENEWAL_TYPE, GlobalEnums::AUDIT_TAB_PENDING);

                        $wf_info['store_renting_wait_my_audit_num'] = $contract_apply_num + $contract_invalid_num + $contract_terminal_num + $contract_renewal_num;

                        // 征询中(申请 + 续签)
                        $rent_contract_apply_inquired_count = $this->getWaitAuditNum($staff_id, Enums::WF_STORE_RENTING_CONTRACT_TYPE, GlobalEnums::AUDIT_TAB_CONSULTED);
                        $rent_contract_renewal_inquired_count = $this->getWaitAuditNum($staff_id, Enums::WF_STORE_RENTING_CONTRACT_RENEWAL_TYPE, GlobalEnums::AUDIT_TAB_CONSULTED);
                        $wf_info['store_renting_contract_inquired_pending_count'] = $rent_contract_apply_inquired_count + $rent_contract_renewal_inquired_count;

                        // 征询已回复(申请 + 续签)
                        $rent_contract_apply_replied_count = $this->getWaitAuditNum($staff_id, Enums::WF_STORE_RENTING_CONTRACT_TYPE, GlobalEnums::AUDIT_TAB_REPLIED);
                        $rent_contract_renewal_replied_count = $this->getWaitAuditNum($staff_id, Enums::WF_STORE_RENTING_CONTRACT_RENEWAL_TYPE, GlobalEnums::AUDIT_TAB_REPLIED);
                        $wf_info['store_renting_contract_inquired_replied_count'] = $rent_contract_apply_replied_count + $rent_contract_renewal_replied_count;
                        break;

                    case MenuKeyEnums::CONTRACT_PLATFORM_WAIT_AUDIT:
                        //gpmd 合同待审核
                        $wf_info['contract_platform_pending_count'] = $this->getWaitAuditNum($staff_id, Enums::WF_CONTRACT_GPMD_BIZ_TYPE, GlobalEnums::AUDIT_TAB_PENDING);

                        // 征询中
                        $wf_info['contract_platform_inquired_pending_count'] = $this->getWaitAuditNum($staff_id, Enums::WF_CONTRACT_GPMD_BIZ_TYPE, GlobalEnums::AUDIT_TAB_CONSULTED);

                        // 征询已回复
                        $wf_info['contract_platform_inquired_replied_count'] = $this->getWaitAuditNum($staff_id, Enums::WF_CONTRACT_GPMD_BIZ_TYPE, GlobalEnums::AUDIT_TAB_REPLIED);
                        break;

                    case MenuKeyEnums::CONTRACT_REPLY_UNPROCESSED:
                        //合同管理～其他合同~待回复
                        $wf_info['contract_consultation_pending_count'] = $this->getConsultationWaitReplyNum($staff_id, Enums::WF_OTHER_CONTRACT_TYPE);

                        // 网点租房合同，待回复
                        $wf_info['store_renting_wait_my_consultation_num'] = $this->getConsultationWaitReplyNum($staff_id, Enums::WF_STORE_RENTING_CONTRACT_TYPE);
                        
                        // gpmd合同，待回复
                        $wf_info['contract_platform_wait_my_consultation_num'] = $this->getConsultationWaitReplyNum($staff_id, Enums::WF_CONTRACT_GPMD_BIZ_TYPE);
                        break;

                    case MenuKeyEnums::VENDOR_WAIT_AUDIT:
                        // 供应商信息 - 待审核数
                        $wf_info['vendor_waiting_audit_count'] = $this->getWaitAuditNum($staff_id,Enums::WF_VENDOR_BIZ_TYPE, GlobalEnums::AUDIT_TAB_PENDING);

                        // 已征询数
                        $wf_info['vendor_inquired_pending_count'] = $this->getWaitAuditNum($staff_id, Enums::WF_VENDOR_BIZ_TYPE, GlobalEnums::AUDIT_TAB_CONSULTED);

                        // 征询已回复
                        $wf_info['vendor_inquired_replied_count'] = $this->getWaitAuditNum($staff_id, Enums::WF_VENDOR_BIZ_TYPE, GlobalEnums::AUDIT_TAB_REPLIED);
                        break;

                    case MenuKeyEnums::VENDOR_WAIT_GRADE_AUDIT:
                        // 供应商分级管理 ~ 待审核数
                        $wf_info['vendor_grade_waiting_audit_count'] = $this->getWaitAuditNum($staff_id, Enums::WF_VENDOR_GRADE_BIZ_TYPE, GlobalEnums::AUDIT_TAB_PENDING);

                        // 征询中
                        $wf_info['vendor_grade_inquired_pending_count'] = $this->getWaitAuditNum($staff_id, Enums::WF_VENDOR_GRADE_BIZ_TYPE, GlobalEnums::AUDIT_TAB_CONSULTED);

                        // 征询已回复
                        $wf_info['vendor_grade_inquired_replied_count'] = $this->getWaitAuditNum($staff_id, Enums::WF_VENDOR_GRADE_BIZ_TYPE, GlobalEnums::AUDIT_TAB_REPLIED);
                        break;

                    case MenuKeyEnums::VENDOR_REPLY_UNPROCESSED:
                        //供应商管理~供应商回复～待处理
                        $wf_info['vendor_reply_pending_count'] = $this->getConsultationWaitReplyNum($staff_id,Enums::WF_VENDOR_BIZ_TYPE);
                        break;

                    case MenuKeyEnums::VENDOR_GRADE_REPLY_UNPROCESSED:
                        //供应商管理~供应商回复～待处理
                        $wf_info['vendor_grade_reply_pending_count'] = $this->getConsultationWaitReplyNum($staff_id, Enums::WF_VENDOR_GRADE_BIZ_TYPE);
                        break;

                    case MenuKeyEnums::WMS_WAIT_AUDIT:
                    case MenuKeyEnums::ASSET_WAIT_AUDIT:
                    case MenuKeyEnums::MATERIAL_ASSET_APPLY_AUDIT:
                        //物料/设备领用审批和资产领用审批数目
                        $wsm_asset_num_arr = $this->getWmsAndAssetNum($staff_id, ByWorkflowEnums::BY_BIZ_TYPE_ASSET);

                        //物料/设备领用审批待审核
                        $wf_info['wms_pending_count'] = $wsm_asset_num_arr['wms_count'];

                        //资产领用审批待审核
                        $wf_info['asset_pending_count'] = $wsm_asset_num_arr['asset_count'];

                        //新资产申请审批待审核
                        $wf_info['material_asset_apply_pending_count'] = $wsm_asset_num_arr['asset_v2_count'];
                        break;

                    case MenuKeyEnums::SALARY_WAIT_AUDIT:
                        //薪酬扣款审批-待审核
                        $wf_info['salary_check_count'] = $this->getWaitAuditNum($staff_id, Enums::WF_WAGES_TYPE, GlobalEnums::AUDIT_TAB_PENDING);
                        break;

                    case MenuKeyEnums::SALARY_PAY_UNPROCESSED:
                        //薪酬扣款审批-待支付
                        $wf_info['salary_pay_count'] = $this->getWaitingPayCount($staff_id, Enums::WF_WAGES_TYPE);
                        break;

                    case MenuKeyEnums::WAGE_WAIT_AUDIT:
                        //薪资发放审批-待审核
                        $wf_info['wage_wait_my_audit_num'] = $this->getWaitAuditNum($staff_id, Enums::WF_SALARY_APPLY, GlobalEnums::AUDIT_TAB_PENDING);
                        break;

                    case MenuKeyEnums::WAGE_PAY_UNPROCESSED:
                        //薪资发放审批-待支付
                        $wf_info['wage_wait_my_pay_num'] = $this->getWaitingPayCount($staff_id, Enums::WF_SALARY_APPLY);
                        break;

                    case MenuKeyEnums::LOAN_WAIT_AUDIT:
                        //借款申请待审核
                        $wf_info['loan_pending_count'] = $this->getWaitAuditNum($staff_id, Enums::WF_LOAN_TYPE, GlobalEnums::AUDIT_TAB_PENDING);

                        // 借款申请 - 已征询数
                        $wf_info['loan_inquired_pending_count'] = $this->getWaitAuditNum($staff_id, Enums::WF_LOAN_TYPE, GlobalEnums::AUDIT_TAB_CONSULTED);

                        // 借款申请 - 征询已回复
                        $wf_info['loan_inquired_replied_count'] = $this->getWaitAuditNum($staff_id, Enums::WF_LOAN_TYPE, GlobalEnums::AUDIT_TAB_REPLIED);

                        // 借款归还-审批待处理
                        $wf_info['loan_back_pending_count'] = $this->getWaitAuditNum($staff_id, Enums::WF_LOAN_BACK_TYPE, GlobalEnums::AUDIT_TAB_PENDING);

                        // 借款归还-征询中
                        $wf_info['loan_back_inquired_pending_count'] = $this->getWaitAuditNum($staff_id, Enums::WF_LOAN_BACK_TYPE, GlobalEnums::AUDIT_TAB_CONSULTED);

                        // 借款归还-征询已回复
                        $wf_info['loan_back_inquired_replied_count'] = $this->getWaitAuditNum($staff_id, Enums::WF_LOAN_BACK_TYPE, GlobalEnums::AUDIT_TAB_REPLIED);
                        break;

                    case MenuKeyEnums::LOAN_PAY_UNPROCESSED:
                        //借款申请待支付
                        $wf_info['loan_payment_pending_count'] = $this->getWaitingPayCount($staff_id, Enums::WF_LOAN_TYPE);
                        break;

                    case MenuKeyEnums::LOAN_MANAGEMENT_REPLY_UNPROCESSED:
                        // 借款管理 - 回复 - 待处理
                        $wf_info['loan_consultation_pending_count'] = $this->getConsultationWaitReplyNum($staff_id, Enums::WF_LOAN_TYPE);
                        break;

                    case MenuKeyEnums::PURCHASE_APPLY_WAIT_AUDIT:
                        //采购申请单待审核
                        $wf_info['purchase_apply_pending_count'] = $this->getWaitAuditNum($staff_id, Enums::WF_PURCHASE_APPLY, GlobalEnums::AUDIT_TAB_PENDING);

                        // 征询中
                        $wf_info['purchase_apply_inquired_pending_count'] = $this->getWaitAuditNum($staff_id, Enums::WF_PURCHASE_APPLY, GlobalEnums::AUDIT_TAB_CONSULTED);

                        // 征询已回复
                        $wf_info['purchase_apply_inquired_replied_count'] = $this->getWaitAuditNum($staff_id, Enums::WF_PURCHASE_APPLY, GlobalEnums::AUDIT_TAB_REPLIED);
                        break;

                    case MenuKeyEnums::PURCHASE_ORDER_WAIT_AUDIT:
                        //采购订单待审核
                        $wf_info['purchase_order_pending_count'] = $this->getWaitAuditNum($staff_id, Enums::WF_PURCHASE_ORDER, GlobalEnums::AUDIT_TAB_PENDING);

                        // 征询中
                        $wf_info['purchase_order_inquired_pending_count'] = $this->getWaitAuditNum($staff_id, Enums::WF_PURCHASE_ORDER, GlobalEnums::AUDIT_TAB_CONSULTED);

                        // 征询已回复
                        $wf_info['purchase_order_inquired_replied_count'] = $this->getWaitAuditNum($staff_id, Enums::WF_PURCHASE_ORDER, GlobalEnums::AUDIT_TAB_REPLIED);
                        break;

                    case MenuKeyEnums::PURCHASE_PAYMENT_WAIT_AUDIT:
                        //采购付款申请单 待审核
                        $wf_info['purchase_pay_pending_count'] = $this->getWaitAuditNum($staff_id, Enums::WF_PURCHASE_PAYMENT, GlobalEnums::AUDIT_TAB_PENDING);

                        // 征询中
                        $wf_info['purchase_pay_inquired_pending_count'] = $this->getWaitAuditNum($staff_id, Enums::WF_PURCHASE_PAYMENT, GlobalEnums::AUDIT_TAB_CONSULTED);

                        // 征询已回复
                        $wf_info['purchase_pay_inquired_replied_count'] = $this->getWaitAuditNum($staff_id, Enums::WF_PURCHASE_PAYMENT, GlobalEnums::AUDIT_TAB_REPLIED);
                        break;

                    case MenuKeyEnums::PURCHASE_PAYMENT_PAY_UNPROCESSED:
                        //采购付款申请单 待支付
                        $wf_info['purchase_pay_payment_pending_count'] = $this->getWaitingPayCount($staff_id, Enums::WF_PURCHASE_PAYMENT);
                        break;

                    case MenuKeyEnums::PURCHASE_ACCEPTANCE_WAIT_AUDIT:
                        //采购管理～采购审核～待处理～验收单
                        $wf_info['purchase_acceptance_pending_count'] = $this->getWaitAuditNum($staff_id, Enums::WF_PURCHASE_ACCEPTANCE, GlobalEnums::AUDIT_TAB_PENDING);

                        // 征询中
                        $wf_info['purchase_acceptance_inquired_pending_count'] = $this->getWaitAuditNum($staff_id, Enums::WF_PURCHASE_ACCEPTANCE, GlobalEnums::AUDIT_TAB_CONSULTED);

                        // 征询已回复
                        $wf_info['purchase_acceptance_inquired_replied_count'] = $this->getWaitAuditNum($staff_id, Enums::WF_PURCHASE_ACCEPTANCE, GlobalEnums::AUDIT_TAB_REPLIED);
                        break;

                    case MenuKeyEnums::PURCHASE_SAMPLE_WAIT_AUDIT:
                        // 采购管理～采购审核～待处理～样品确认
                        $wf_info['purchase_sample_pending_count'] = $this->getWaitAuditNum($staff_id, Enums::WF_PURCHASE_SAMPLE, GlobalEnums::AUDIT_TAB_PENDING);

                        // 征询中
                        $wf_info['purchase_sample_inquired_pending_count'] = $this->getWaitAuditNum($staff_id, Enums::WF_PURCHASE_SAMPLE, GlobalEnums::AUDIT_TAB_CONSULTED);

                        // 征询已回复
                        $wf_info['purchase_sample_inquired_replied_count'] = $this->getWaitAuditNum($staff_id, Enums::WF_PURCHASE_SAMPLE, GlobalEnums::AUDIT_TAB_REPLIED);
                        break;

                    case MenuKeyEnums::PURCHASE_MANAGEMENT_REPLY_UNPROCESSED:
                        // 采购管理模块下的所有子模块回复 - 待处理
                        //采购管理～采购回复～采购申请单～待处理
                        $wf_info['purchase_apply_consultation_pending_count'] = $this->getConsultationWaitReplyNum($staff_id, Enums::WF_PURCHASE_APPLY);

                        //采购管理～采购回复～采购订单～待处理
                        $wf_info['purchase_order_consultation_pending_count'] = $this->getConsultationWaitReplyNum($staff_id, Enums::WF_PURCHASE_ORDER);

                        //采购管理～采购回复～采购付款申请单～待处理
                        $wf_info['purchase_pay_consultation_pending_count'] = $this->getConsultationWaitReplyNum($staff_id, Enums::WF_PURCHASE_PAYMENT);

                        //采购管理～采购回复～验收单～待处理
                        $wf_info['purchase_acceptance_consultation_pending_count'] = $this->getConsultationWaitReplyNum($staff_id, Enums::WF_PURCHASE_ACCEPTANCE);

                        // 采购管理～采购回复～样品确认～待处理
                        $wf_info['purchase_sample_consultation_pending_count'] = $this->getConsultationWaitReplyNum($staff_id, Enums::WF_PURCHASE_SAMPLE);
                        break;

                    case MenuKeyEnums::REIMBURSEMENT_WAIT_AUDIT:
                        //报销-审核-待处理
                        $wf_info['reimbursement_pending_count'] = $this->getWaitAuditNum($staff_id, Enums::WF_REIMBURSEMENT_TYPE, GlobalEnums::AUDIT_TAB_PENDING);

                        // 征询中
                        $wf_info['reimbursement_inquired_pending_count'] = $this->getWaitAuditNum($staff_id, Enums::WF_REIMBURSEMENT_TYPE, GlobalEnums::AUDIT_TAB_CONSULTED);

                        // 征询已回复
                        $wf_info['reimbursement_inquired_replied_count'] = $this->getWaitAuditNum($staff_id, Enums::WF_REIMBURSEMENT_TYPE, GlobalEnums::AUDIT_TAB_REPLIED);
                        break;

                    case MenuKeyEnums::REIMBURSEMENT_PAY_UNPROCESSED:
                        //报销-支付-待处理
                        $wf_info['reimbursement_payment_pending_count'] = $this->getWaitingPayCount($staff_id, Enums::WF_REIMBURSEMENT_TYPE);
                        break;

                    case MenuKeyEnums::REIMBURSEMENT_MANAGEMENT_REPLY_UNPROCESSED:
                        // 报销管理～报销回复～待处理
                        $wf_info['reimbursement_consultation_pending_count'] = $this->getConsultationWaitReplyNum($staff_id, Enums::WF_REIMBURSEMENT_TYPE);
                        break;

                    case MenuKeyEnums::REIMBURSEMENT_APPLY_PENDING:
                        // 报销管理～我的申请～待办
                        $wf_info['reimbursement_apply_pending_count'] = ReimbursementListService::getInstance()->getUserPendingCount($staff_id, $channel);
                        break;

                    case MenuKeyEnums::STORE_RENTING_PAYMENT_WAIT_AUDIT:
                        //网点租房付款-待审核
                        $wf_info['payment_store_renting_waiting_approval_count'] = $this->getWaitAuditNum($staff_id, Enums::WF_PAYMENT_STORE_RENTING_TYPE, GlobalEnums::AUDIT_TAB_PENDING);

                        // 征询中
                        $wf_info['payment_store_renting_inquired_pending_count'] = $this->getWaitAuditNum($staff_id, Enums::WF_PAYMENT_STORE_RENTING_TYPE, GlobalEnums::AUDIT_TAB_CONSULTED);

                        // 征询已回复
                        $wf_info['payment_store_renting_inquired_replied_count'] = $this->getWaitAuditNum($staff_id, Enums::WF_PAYMENT_STORE_RENTING_TYPE, GlobalEnums::AUDIT_TAB_REPLIED);
                        break;

                    case MenuKeyEnums::STORE_RENTING_PAYMENT_PAY_UNPROCESSED:
                        //网点租房付款-待支付
                        $wf_info['payment_store_renting_waiting_pay_count'] = $this->getWaitingPayCount($staff_id, Enums::WF_PAYMENT_STORE_RENTING_TYPE);
                        break;

                    case MenuKeyEnums::STORE_RENTING_PAYMENT_REPLY_UNPROCESSED:
                        //租房付款管理～付款回复
                        $wf_info['payment_consultation_pending_count'] =$this->getConsultationWaitReplyNum($staff_id, Enums::WF_PAYMENT_STORE_RENTING_TYPE);
                        break;

                    case MenuKeyEnums::ORDINARY_PAYMENT_WAIT_AUDIT:
                        //普通付款-待审核
                        $wf_info['ordinary_payment_waiting_approval_count'] = $this->getWaitAuditNum($staff_id, Enums::ORDINARY_PAYMENT_BIZ_TYPE, GlobalEnums::AUDIT_TAB_PENDING);

                        // 征询中
                        $wf_info['ordinary_payment_inquired_pending_count'] = $this->getWaitAuditNum($staff_id, Enums::ORDINARY_PAYMENT_BIZ_TYPE, GlobalEnums::AUDIT_TAB_CONSULTED);

                        // 征询已回复
                        $wf_info['ordinary_payment_inquired_replied_count'] = $this->getWaitAuditNum($staff_id, Enums::ORDINARY_PAYMENT_BIZ_TYPE, GlobalEnums::AUDIT_TAB_REPLIED);
                        break;

                    case MenuKeyEnums::ORDINARY_PAYMENT_PAY_UNPROCESSED:
                        //普通付款-待支付
                        $wf_info['ordinary_payment_waiting_pay_count'] = $this->getWaitingPayCount($staff_id, Enums::ORDINARY_PAYMENT_BIZ_TYPE);
                        break;

                    case MenuKeyEnums::ORDINARY_PAYMENT_REPLY_UNPROCESSED:
                        //普通付款～付款回复～待处理～普通付款
                        $wf_info['ordinary_payment_consultation_pending_count'] =$this->getConsultationWaitReplyNum($staff_id, Enums::ORDINARY_PAYMENT_BIZ_TYPE);
                        break;

                    case MenuKeyEnums::RESERVE_APPLY_WAIT_AUDIT:
                        // 网点备用金～审核～备用金申请
                        $wf_info['reserve_fund_apply_waiting_audit_count'] = $this->getWaitAuditNum($staff_id, Enums::WF_RESERVE_FUND_APPLY, GlobalEnums::AUDIT_TAB_PENDING);

                        // 征询中
                        $wf_info['reserve_fund_apply_inquired_pending_count'] = $this->getWaitAuditNum($staff_id, Enums::WF_RESERVE_FUND_APPLY, GlobalEnums::AUDIT_TAB_CONSULTED);

                        // 征询已回复
                        $wf_info['reserve_fund_apply_inquired_replied_count'] = $this->getWaitAuditNum($staff_id, Enums::WF_RESERVE_FUND_APPLY, GlobalEnums::AUDIT_TAB_REPLIED);
                        break;

                    case MenuKeyEnums::RESERVE_RETURN_WAIT_AUDIT:
                        // 网点备用金～审核～备用金归还
                        $wf_info['reserve_fund_return_waiting_audit_count'] = $this->getWaitAuditNum($staff_id, Enums::WF_RESERVE_FUND_RETURN, GlobalEnums::AUDIT_TAB_PENDING);

                        // 征询中
                        $wf_info['reserve_fund_return_inquired_pending_count'] = $this->getWaitAuditNum($staff_id, Enums::WF_RESERVE_FUND_RETURN, GlobalEnums::AUDIT_TAB_CONSULTED);

                        // 征询待处理
                        $wf_info['reserve_fund_return_inquired_replied_count'] = $this->getWaitAuditNum($staff_id, Enums::WF_RESERVE_FUND_RETURN, GlobalEnums::AUDIT_TAB_REPLIED);
                        break;

                    case MenuKeyEnums::RESERVE_PAY_UNPROCESSED:
                        //备用金申请-待支付
                        $wf_info['reserve_fund_apply_waiting_pay_count'] = $this->getWaitingPayCount($staff_id, Enums::WF_RESERVE_FUND_APPLY);
                        break;

                    case MenuKeyEnums::RESERVE_REPLY_UNPROCESSED:
                        //备用金管理～备用金回复～待处理～备用金申请
                        $wf_info['reserve_fund_apply_consultation_pending_count'] =$this->getConsultationWaitReplyNum($staff_id, Enums::WF_RESERVE_FUND_APPLY);

                        //备用金管理～备用金回复～待处理～备用金归还
                        $wf_info['reserve_fund_return_consultation_pending_count'] =$this->getConsultationWaitReplyNum($staff_id, Enums::WF_RESERVE_FUND_RETURN);
                        break;

                    case MenuKeyEnums::PAY_WAIT_AUDIT:
                        // 支付管理 - 我的支付 - 待审核的数量
                        $wf_info['pay_waiting_audit_count'] = $this->getWaitAuditNum($staff_id,Enums::WF_PAY_TYPE, GlobalEnums::AUDIT_TAB_PENDING, 'pay');

                        // 征询中
                        $wf_info['pay_inquired_pending_count'] = $this->getWaitAuditNum($staff_id, Enums::WF_PAY_TYPE, GlobalEnums::AUDIT_TAB_CONSULTED, 'pay');

                        // 征询已回复
                        $wf_info['pay_inquired_replied_count'] = $this->getWaitAuditNum($staff_id, Enums::WF_PAY_TYPE, GlobalEnums::AUDIT_TAB_REPLIED, 'pay');
                        break;

                    case MenuKeyEnums::PAY_BANK_WAIT_AUDIT:
                        // 支付管理 - 银行支付 - 待审核的数量
                        $wf_info['pay_bank_waiting_audit_count'] = $this->getWaitAuditNum($staff_id,Enums::WF_PAY_TYPE, GlobalEnums::AUDIT_TAB_PENDING, 'bank');
                        break;

                    case MenuKeyEnums::PAY_MANAGEMENT_REPLY_UNPROCESSED:
                        // 支付管理 - 回复 - 待处理
                        $wf_info['pay_waiting_pending_count'] = $this->getConsultationWaitReplyNum($staff_id,Enums::WF_PAY_TYPE);
                        break;
                    case MenuKeyEnums::PAY_ONLINE_AUDIT:
                        //支付管理-在线支付
                        $wf_info['pay_online_count'] = OnlineService::getInstance()->getCount();
                        break;
                    case MenuKeyEnums::BUDGET_WAIT_AUDIT:
                        //财务预算管理～预算导入审核～待处理
                        $wf_info['budget_audit_pending_count'] = $this->getWaitAuditNum($staff_id,Enums::BUDGET_OB_TYPE, GlobalEnums::AUDIT_TAB_PENDING);
                        break;

                    case MenuKeyEnums::BUDGET_ADJUST_WAIT_AUDIT:
                        //财务预算管理～预算调整审核～待处理
                        $wf_info['budget_adjust_pending_count'] = $this->getWaitAuditNum($staff_id,Enums::WF_BUDGET_ADJUST_TYPE, GlobalEnums::AUDIT_TAB_PENDING);

                        // 征询中
                        $wf_info['budget_adjust_inquired_pending_count'] = $this->getWaitAuditNum($staff_id, Enums::WF_BUDGET_ADJUST_TYPE, GlobalEnums::AUDIT_TAB_CONSULTED);

                        // 征询已回复
                        $wf_info['budget_adjust_inquired_replied_count'] = $this->getWaitAuditNum($staff_id, Enums::WF_BUDGET_ADJUST_TYPE, GlobalEnums::AUDIT_TAB_REPLIED);
                        break;

                    case MenuKeyEnums::BUDGET_ADJUST_REPLY_UNPROCESSED:
                        //财务预算管理～预算调整审回复
                        $wf_info['budget_adjust_waiting_reply_count'] = $this->getConsultationWaitReplyNum($staff_id,Enums::WF_BUDGET_ADJUST_TYPE);
                        break;
                    case MenuKeyEnums::BUDGET_WITHHOLDING_WAIT_AUDIT:
                        //财务预算管理～预提审核～待处理
                        $wf_info['budget_withholding_audit_pending_count'] = $this->getWaitAuditNumFromBy($staff_id, ByWorkflowEnums::BY_BIZ_TYPE_BUDGET_WITHHOLDING);
                        break;

                    case MenuKeyEnums::ACCESS_DATA_WAIT_AUDIT:
                        // 取数工单系统 - 待审批
                        $wf_info['access_work_order_waiting_audit_count'] = $this->getWaitAuditNum($staff_id, Enums::WF_ACCESS_DATA_WORK_ORDER_TYPE, GlobalEnums::AUDIT_TAB_PENDING);
                        break;

                    case MenuKeyEnums::ACCESS_DATA_PROCESS_WAIT_AUDIT:
                        // 取数工单系统 - 待处理
                        $wf_info['access_work_order_waiting_processed_count'] = $this->getWaitProcessedNum($staff_id, Enums::WF_ACCESS_DATA_WORK_ORDER_TYPE);
                        break;

                    case MenuKeyEnums::CRM_WAIT_AUDIT:
                        // 报价管理 - 报价审核
                        $wf_info['crm_wait_my_audit_num'] = $this->getWaitAuditNum($staff_id, Enums::WF_CRM_QUOTATION, GlobalEnums::AUDIT_TAB_PENDING);
                        break;

                    case MenuKeyEnums::KPI_LEADER_WAIT_AUDIT:
                        //下级KPI管理菜单 直接上级待办数量
                        $wf_info['kpi_waiting_submit_audit_count'] = $this->getWaitingSubmitKPINum($staff_id);
                        break;

                    case MenuKeyEnums::SCHOOL_PLAN_WAIT_AUDIT:
                        //培训系统待-学习计划-待审批数量
                        $wf_info['school_plan_pending_count'] = AuditService::getInstance()->getAuditCount($user);
                        break;

                    case MenuKeyEnums::HC_WAIT_AUDIT:
                        //hc预算管理-待审核
                        $wf_info['hc_pending_count'] = $this->getWaitAuditNumFromBy($staff_id, Enums::WF_HC_AUDIT);
                        break;

                    case MenuKeyEnums::TRANSFER_WAIT_AUDIT:
                        //转岗
                        $wf_info['jt_pending_count'] = $this->getWaitAuditNumFromBy($staff_id, Enums::WF_JOB_TRANSFER_TYPE);
                        break;
                    case MenuKeyEnums::TRANSFER_BP_WAIT_AUDIT:
                        //转岗
                        $wf_info['jt_bp_pending_count'] = $this->getWaitAuditNumFromBy($staff_id, Enums::WF_JOB_TRANSFER_BP_TYPE);
                        break;
                    case MenuKeyEnums::CHEQUE_AUDIT_WAIT_AUDIT:
                        //待处理
                        $wf_info['cheque_audit_pending_count'] = $this->getWaitAuditNum($staff_id, Enums::WF_CHEQUE_APPLY_BIZ_TYPE, GlobalEnums::AUDIT_TAB_PENDING);

                        // 征询中
                        $wf_info['cheque_audit_inquired_pending_count'] = $this->getWaitAuditNum($staff_id, Enums::WF_CHEQUE_APPLY_BIZ_TYPE, GlobalEnums::AUDIT_TAB_CONSULTED);

                        // 征询已回复
                        $wf_info['cheque_audit_inquired_replied_count'] = $this->getWaitAuditNum($staff_id, Enums::WF_CHEQUE_APPLY_BIZ_TYPE, GlobalEnums::AUDIT_TAB_REPLIED);
                        break;

                    case MenuKeyEnums::CHEQUE_CONSULTATION_UNPROCESSED:
                        // 支票管理 - 回复 - 待处理
                        $wf_info['cheque_waiting_pending_count'] = $this->getConsultationWaitReplyNum($staff_id,Enums::WF_CHEQUE_APPLY_BIZ_TYPE);
                        break;

                    case MenuKeyEnums::DEPOSIT_CONSULTATION_UNPROCESSED:
                        //押金我的回复
                        $wf_info['deposit_consultation_count'] = $this->getConsultationWaitReplyNum($staff_id, Enums::DEPOSIT_RETURN_BIZ_TYPE);
                        break;

                    case MenuKeyEnums::DEPOSIT_AUDIT_WAIT_AUDIT:
                        //押金-待处理
                        $wf_info['deposit_audit_pending_count'] = $this->getWaitAuditNum($staff_id, Enums::DEPOSIT_RETURN_BIZ_TYPE, GlobalEnums::AUDIT_TAB_PENDING);

                        // 押金-征询中数
                        $wf_info['deposit_audit_inquired_pending_count'] = $this->getWaitAuditNum($staff_id, Enums::DEPOSIT_RETURN_BIZ_TYPE, GlobalEnums::AUDIT_TAB_CONSULTED);

                        // 押金-征询已回复数
                        $wf_info['deposit_audit_inquired_replied_count'] = $this->getWaitAuditNum($staff_id, Enums::DEPOSIT_RETURN_BIZ_TYPE, GlobalEnums::AUDIT_TAB_REPLIED);
                        break;

                    case MenuKeyEnums::ASSET_TO_BE_RECEIVER_COUNT:
                        //资产转移-待接收数量
                        $wf_info['asset_to_be_receiver_count'] = AssetTransferListService::getInstance()->getReceiverListCount('', [], $user);
                        break;

                    case MenuKeyEnums::MATERIAL_WMS_AUDIT_WAIT_AUDIT:
                        //耗材-待处理
                        $wsm_num_arr                                 = $this->getWmsAndAssetNum($staff_id, ByWorkflowEnums::BY_BIZ_TYPE_WMS);
                        $wf_info['material_wms_audit_pending_count'] = $wsm_num_arr['wms_v2_count'];
                        break;
                    case MenuKeyEnums::MATERIAL_WMS_OUT_STORAGE_DRAFT:
                        //物料/资产管理～耗材出库
                        $wf_info['material_wms_out_storage_draft'] = WmsOutStorageService::getInstance()->getRedHotNum();
                        break;
                    case MenuKeyEnums::CONTRACT_REMIND_WAIT_DEAL:
                        $wf_info['contract_contract_remind_count'] = ContractRemindListService::getInstance()->getListCount(['task_status' => Enums\ContractEnums::TASK_STATUS_TODO]);
                        break;

                    case MenuKeyEnums::MATERIAL_LEAVE_ASSET_COUNT:
                        $count_arr = LeaveAssetsManagerService::getInstance()->getLeaveAssetsManagerCount($user);
                        $wf_info['material_leave_asset_count'] = $count_arr['data']['num'] ?? 0;
                        break;
                    case MenuKeyEnums::WORKFLOW_CARBON_COPY_COUNT:
                        $count = CarbonCopyService::getInstance()->getListCount(['status' => Enums\OAWorkflowEnums::WORKFLOW_CC_STATUS_UNREAD], $user);
                        $wf_info['workflow_carbon_copy_count'] = $count;
                        break;
                    case MenuKeyEnums::WAREHOUSE_CHANGE_AUDIT:
                        $wf_info['warehouse_change_audit_pending_count'] = $this->getWaitAuditNumFromBy($staff_id, ByWorkflowEnums::BY_BIZ_TYPE_WAREHOUSE_CHANGE_STATUS);
                        break;
                    case MenuKeyEnums::WAREHOUSE_REQUIREMENT_SEARCH:
                        //待寻找小红点数=仓库需求状态为待寻找的仓库需求数量
                        $wf_info['warehouse_requirement_search'] = RequirementService::getInstance()->getDataTotal([], RequirementService::REQUIREMENT_SEARCH_LIST);
                        break;
                    case MenuKeyEnums::WAREHOUSE_REQUIREMENT_CONFIRM:
                        //待确认小红点数=仓库需求状态为待确认的仓库需求数量
                        $wf_info['warehouse_requirement_confirm'] = RequirementService::getInstance()->getDataTotal([], RequirementService::REQUIREMENT_CONFIRM_LIST);
                        break;
                    case MenuKeyEnums::WAREHOUSE_REQUIREMENT_SETTLE:
                        //待入驻小红点数=仓库需求状态为待入驻的仓库需求数量
                        $wf_info['warehouse_requirement_settle'] = RequirementService::getInstance()->getDataTotal([], RequirementService::REQUIREMENT_SETTLE_LIST);
                        break;

                    case MenuKeyEnums::WAREHOUSE_REQUIREMENT_RENEWED:
                        //待续约小红点数=仓库需求状态为待续约的仓库需求数量
                        $wf_info['warehouse_requirement_renewed'] = RequirementService::getInstance()->getDataTotal([], RequirementService::REQUIREMENT_RENEWED_LIST);
                        break;

                    case MenuKeyEnums::WAREHOUSE_THREAD_PRICE:
                        //待报价小红点数=仓库线索状态为待报价的仓库线索数量
                        $wf_info['warehouse_thread_price'] = ThreadService::getInstance()->getDataTotal([], $user, ThreadService::THREAD_PRICE_LIST);
                        break;
                    case MenuKeyEnums::WAREHOUSE_THREAD_SIGN:
                        //待签约小红点数=仓库线索状态为待签约的仓库线索数量
                        $wf_info['warehouse_thread_sign'] = ThreadService::getInstance()->getDataTotal([], $user, ThreadService::THREAD_SIGN_LIST);
                        break;
                    case MenuKeyEnums::WAREHOUSE_THREAD_VERIFY:
                        //待验证小红点数=仓库验证状态为待验证并且验证责任人等于当前登录人的仓库验证数量
                        $wf_info['warehouse_thread_verify'] = ThreadService::getInstance()->getDataTotal([], $user, ThreadService::THREAD_VERIFY_LIST);
                        break;
                    case MenuKeyEnums::WAREHOUSE_PRICE_WAIT:
                        //仓库报价审核-待处理小红点数=当前登录人为审批人的报价审核单数量
                        $wf_info['warehouse_price_wait'] = $this->getWaitAuditNumFromBy($staff_id, ByWorkflowEnums::BY_BIZ_TYPE_WAREHOUSE_THREAD_PRICE);
                        break;
                    case MenuKeyEnums::AGENCY_PAYMENT_AUDIT:
                        $wf_info['agency_payment_audit_pending_count'] = $this->getWaitAuditNumFromBy($staff_id, ByWorkflowEnums::BY_BIZ_TYPE_AGENCY_PAYMENT);
                        break;
                    case MenuKeyEnums::AGENCY_PAYMENT_PAY:
                        $wf_info['agency_payment_pay_pending_count'] = $this->getWaitingPayCount($staff_id, Enums::WF_AGENCY_PAYMENT_BIZ_TYPE);
                        break;

                    case MenuKeyEnums::BATCH_APPROVAL_OVERTIME:
                        $wf_info['batch_approval_overtime'] = $this->getWaitAuditNumFromBy($staff_id, ByWorkflowEnums::WF_APPROVAL_OVERTIME);
                        break;
                    case MenuKeyEnums::BATCH_APPROVAL_OS_OVERTIME:
                        $wf_info['batch_os_overtime'] = $this->getWaitAuditNumFromBy($staff_id, ByWorkflowEnums::WF_APPROVAL_OS_OVERTIME);
                        break;
                    case MenuKeyEnums::COMPANY_MOBILE_MANAGE:
                        $wf_info['sim_card_manage_pending_count'] = CompanyMobileService::getInstance()->todoData(true);
                        break;
                    case MenuKeyEnums::SIM_CARD_FEEDBACK:
                        $wf_info['sim_card_feedback_pending_count'] = FeedbackService::getInstance()->todoData();
                        break;
                    case MenuKeyEnums::PAPER_DOCUMENT_MY_LIST:
                        $wf_info['paper_doc_pending_submit_count'] = ConfirmationService::getInstance()->getWaitSubmitNum($staff_id);
                        break;
                    case MenuKeyEnums::PAPER_DOCUMENT_CONFIRM_LIST:
                        $wf_info['paper_doc_pending_confirmation_count'] = ConfirmationService::getInstance()->getWaitConfirmNum($staff_id);
                        break;
                    default:
                        break;
                }
            }

            //如果缓存
            if ($is_unread_msg_cache) {
                RedisClient::getInstance()->getClient()->setex($redis_key, $expire_time_seconds, json_encode($wf_info));
            }

        } else {
            $wf_info = json_decode($json_str, 1);
        }

        return $wf_info;
    }

    /**
     * 获取工单待处理的数量
     * @param int $uid
     * @param int $biz_type
     * @return mixed
     */
    public function getWaitProcessedNum(int $uid, int $biz_type)
    {
        $count = 0;
        switch ($biz_type) {
            // 取数工单系统
            case Enums::WF_ACCESS_DATA_WORK_ORDER_TYPE:
                $count = AccessDataDetailService::getInstance()->getUserWaitingProcessedCount($uid);
                break;
            default:
        }

        return $count;
    }

    /**
     * 获取直接上级-KPI待制定目标
     * @param int $uid 用户ID
     * @return int
     */
    public function getWaitingSubmitKPINum(int $uid)
    {
        return ActivityStaffService::getInstance()->getWaitingSubmitKPICount($uid);
    }


    /**
     * 获得所有角色
     * @return array
     */
    public function getAllRoles(){
        $ans = [];
        $roles = BiRoleModel::find(
            [
                'conditions'=>'status = 1'
            ]
        )->toArray();
        $lang = strtolower(substr(static::$language,0,2));
        if(!in_array($lang,['zh','th','en'])){
            $lang = 'en';
        }
        if($lang == 'zh'){
            $key = 'name';
        }else{
            $key = 'name_'.$lang;
        }
        foreach ($roles as $role){
            $tmp = [];
            $tmp['label'] = $role[$key];
            $tmp['value'] = $role['id'];
            $ans[] = $tmp;
        }
        return $ans;
    }

    /**
     * From 路遥：已经在by追加了新的获取资产、物料的待审批数的svc接口，请替换根据列表获取待审批count数的接口调用
     * @param int $uid 当前登陆者用户ID
     * @param int $type  区分分类 50 表示耗材  46表示资产
     * @return array
     */
    public function getWmsAndAssetNum(int $uid, $type = 0)
    {
        $data = ['asset_count' => 0, 'wms_count' => 0, 'asset_v2_count' => 0, 'wms_v2_count' => 0];
        $ac = new ApiClient('by', '', 'get_audit_pending_count', static::$language);
        $ac->setParams(
            [
                [
                    'staff_approval_id' => $uid,
                    'type' => $type
                ]
            ]
        );
        $res = $ac->execute();
        $code = $res['result']['code'] ?? $res['code'];
        if ($code == 1) {
            //请求成功
            $data = $res['result']['data'] ?? $data;
        }
        return $data;
    }


    /**
     * 按照产品数据返回对应的xls导出的数据连接
     * @Date: 2022-03-22 16:35
     * @return:
     **@author: peak pan
     */
    public function getdata($page,$page_size)
    {
        ini_set('memory_limit', '4000M');
        $page_size = empty($page_size) ? 10000 : $page_size;
        $page_num  = empty($page) ? 1 : $page;
        $offset    = $page_size * ($page_num - 1);
        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            'staff.staff_info_id AS staff_id',
            'staff.name as name_',
            'department.name as department_name',
            'job.job_name'
        ]);
        $builder->from(['staff' => HrStaffInfoModel::class]);
        $builder->leftjoin(SysDepartmentModel::class, 'staff.sys_department_id = department.id', 'department');
        $builder->leftjoin(HrJobTitleModel::class, 'staff.job_title = job.id', 'job');
        $builder->where('staff.state!=2');//17627   and staff.staff_info_id=39470 jl    39470 kd
        $builder->limit($page_size,$offset);
        $listdate = $builder->getQuery()->execute()->toArray();
        $rs_pos=[];
        $st = [1,2,4,109];
        $permission_ids_in =[140,561,203,179,239];
        $shool = 628;

        $is_ok = 0;
        foreach ($listdate as $rs) {
            $hrStaffInfoarr = new  HrStaffInfoPosition();
                $hrStaffInfoList = $hrStaffInfoarr->find(
                    [
                        "conditions" => 'staff_info_id = :staff_info_id:',
                        "bind" => ['staff_info_id' => $rs['staff_id']]
                    ]
                )->toArray();

                if (!empty($hrStaffInfoList)) {
                    $rs_pos = array_column($hrStaffInfoList, 'position_category');
                    foreach (array_diff($rs_pos,$st) as $diff_itme) {
                        if (!in_array($diff_itme, $st)) {
                            $is_ok = 1;
                        }else{
                            $is_ok = 0;
                        }
                    }
                    $rs['roles_name'] =$rs_pos;
                } else {
                    //无角色
                    $is_ok = 1;
                    $rs['roles_name'] ='--';
                }
            if(is_array($rs['roles_name'])){
             $roles = new BiRoleModel();
             $Rolesarr = $roles->find(
                [
                    "conditions" => 'id in ({id:array})',
                    "bind" => ['id' => $rs['roles_name']]
                ]
            )->toArray();
             if(!empty($Rolesarr)){
                 $rs['roles_name'] = implode(',',array_column($Rolesarr,'name'));
             }
            }
            if ($is_ok==1) {
                //正常的数
                $permission_arr = [];
                $StaffPermission = new StaffPermissionModel();
                $permission_ids = $StaffPermission->findFirst([
                    'conditions' => 'staff_id = :staff_id:',
                    'bind' => ['staff_id' => $rs['staff_id']],
                    'columns' => ['permission_ids']
                ]);

                if(!empty($permission_ids->permission_ids)){
                    $permission_ids_a =  $permission_ids->toArray();
                }else{
                    continue;
                }
                $permission_arr = explode(',', $permission_ids_a['permission_ids']);

                $permission = new PermissionModel();
                $permissionarr = $permission->find(
                    [
                        "conditions" => 'ancestry in ({ancestry:array})',
                        "bind" => ['ancestry' => $permission_ids_in],
                    ]
                )->toArray();
                if (!empty($permissionarr)) {
                    $onepermissionarr = $permission->find(
                        [
                            "conditions" => 'id in ({id:array})',
                            "bind" => ['id' => $permission_ids_in],
                        ]
                    )->toArray();
                    $one_permission = array_column($onepermissionarr, 'name','id');

                    $permissionArr = array_column($permissionarr, 'id');
                    $permissionArr_new= array_column($permissionarr, null, 'id');
                    foreach ($permissionArr_new  as $key=>$itm_v){
                        $titlt = empty($one_permission[$itm_v['ancestry']])?'':$one_permission[$itm_v['ancestry']].'-';
                        $permissionArr_name_id[$key] = $titlt.$itm_v['name'];
                    }
                    $new_arr_shool = ['628'=>'员工培训系统'];
                    $permissionArr_name_ids = $permissionArr_name_id+$new_arr_shool;
                }
                $rsname=[];
                foreach ($permissionArr_name_ids as $key => $vlua) {
                    if (in_array($key, $permission_arr)) {
                        $rs[$key] = 1;
                    } else {
                        $rs[$key] = 0;
                    }
                    $rsname[]=$vlua;
                }
                $rts[] = array_values($rs);
            }
        }
        $name_arr = array_merge(['工号','姓名','部门','职位','角色'],$rsname);
       return ['header'=>$name_arr,'new_data'=>$rts] ;

    }

    /**
     * 下载
     * @Token
     * @Date: 2022-03-22 22:34
     * @return:
     **@author: peak pan
     */

    public function download($page,$page_size)
    {
        $data = $this->getdata($page,$page_size);
        $new_data = [];
        $i = 0;
        foreach ($data['new_data'] as $key => $val) {
            ++$i;
            $new_data[$key][0] = $val[0];
            $new_data[$key][1] = $val[1];
            $new_data[$key][2] = $val[2];
            $new_data[$key][3] = $val[3];
            $new_data[$key][4] = $val[4];
            $new_data[$key][5] = $val[5];
            $new_data[$key][6] = $val[6];
            $new_data[$key][7] = $val[7];
            $new_data[$key][8] = $val[8];
            $new_data[$key][9] = $val[9];
            $new_data[$key][10] = $val[10];
            $new_data[$key][11] = $val[11];
            $new_data[$key][12] = $val[12];
            $new_data[$key][13] = $val[13];
            $new_data[$key][14] = $val[14];
            $new_data[$key][15] = $val[15];
            $new_data[$key][16] = $val[16];
            $new_data[$key][17] = $val[17];
            $new_data[$key][18] = $val[18];
            $new_data[$key][19] = $val[19];
            $new_data[$key][20] = $val[20];
            $new_data[$key][21] = $val[21];
            $new_data[$key][22] = $val[22];
        }
        $file_name = 'oa导出用户配置详情数据'.date('YmdHis');
        $header = $data['header'];
        return $this->exportExcel($header, $new_data, $file_name);
    }

    /**
     * 用户信息初始化
     * @param $uid
     * @return array
     */
    public function getLoginUser($uid)
    {
        if ($uid) {
            $user = $this->getUserById($uid);
            return $this->format_user($user);
        } else {
            return [];
        }
    }

    /**
     * 获取登录二维码
     *
     * @param array $params
     * @return array
     * @throws ValidationException
     */
    public function getLoginQrCode(array $params)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';

        $api_params = [
            'ip' => get_client_real_ip(),
            'os' => $params['os'],
            'equipment_type' => self::$oa_qr_equipment_type,//扫码来源
            'browser' => $params['browser'],
            'clientsd' => $params['clientsd'],
            'clientid' => $params['clientid'],
            'qrlat' => !empty($params['qrlat']) ? $params['qrlat'] : '',
            'qrlng' => !empty($params['qrlng']) ? $params['qrlng'] : '',
            'position_fail_reason' => !empty($params['position_fail_reason']) ? $params['position_fail_reason'] : '',
        ];

        $api = new ApiClient('fle', 'com.flashexpress.fle.svc.api.StaffAuthSvc', 'getQrcode', static::$language);
        $api->setParams([$api_params]);
        $result = $api->execute();
        if (isset($result['error'])) {
            throw new ValidationException($result['error'], ErrCode::$VALIDATE_ERROR);
        }

        $data = [
            'qrtoken' => $result['result']['qrtoken'] ?? '',
            'qrtoken_base64' => $result['result']['qrtoken_base64'] ?? '',
            'qrflag' => $result['result']['qrflag'] ?? false
        ];

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 查询登录二维码状态
     *
     * @param array $params
     * @return array
     * @throws BusinessException
     * @throws ValidationException
     */
    public function queryLoginQrCodeStatus(array $params)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';

        $api_params = [
            'qrtoken' => $params['qrtoken'],
            'clientsd' => $params['clientsd'],
            'clientid' => $params['clientid'],
        ];

        $api = new ApiClient('fle', 'com.flashexpress.fle.svc.api.StaffAuthSvc', 'qrLogin', static::$language);
        $api->setParams([$api_params]);
        $result = $api->execute();
        if (isset($result['error'])) {
            throw new ValidationException($result['error'], ErrCode::$VALIDATE_ERROR);
        }

        $data = [
            'equipment_type' => $result['result']['equipment_type'],
            'qrstate' => $result['result']['qrstate'],
            'staff_id' => $result['result']['id'] ?? '',
            'token_type' => 'Bearer',
            'token' => '',
        ];

        // 扫码登录成功: 生成token
        if ($data['qrstate'] == self::$oa_qr_authorized_state) {
            $token = $this->generateToken($result['result']);
            $data['token'] = $token->__toString();

            // token 缓存
            $this->saveUserToken(self::USER_LOGIN_PLATFORM_PC, $data['staff_id'], $data['token']);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * BY系统访问OA系统权限校验
     *
     * @param array $params
     * @return array
     */
    public function staffAuthentication(array $params)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';
        $data = [];

        try {
            // 参数校验
            $validate = [
                'staff_id'=> 'Required|StrLenGeLe:1,32|>>>:params error[staff_id]',
                'platform'=> 'Required|IntIn:1,2|>>>:params error[platform]',//1-by;2-kit
                'timestamp'=> 'Required|StrLenGeLe:1,10|>>>:params error[timestamp]',
                'sign'=> 'Required|StrLenGeLe:1,128|>>>:params error[sign]',
            ];
            Validation::validate($params, $validate);

            // 验签
            $api_key = (new SettingEnvModel())->getValByCode('oa_login_private_key', '');
            if (empty($api_key)) {
                throw new BusinessException('私钥未配置', ErrCode::$BUSINESS_ERROR);
            }
            $api_sign_params = build_params_sign($params, $api_key);
            if ($params['sign'] != $api_sign_params['sign']) {
                throw new ValidationException(static::$t->_('oa_auth_error_001'), ErrCode::$VALIDATE_ERROR);
            }

            // 员工是否存在
            $staff_info = (new HrStaffRepository())->getStaffById($params['staff_id']);
            if (empty($staff_info)) {
                throw new ValidationException(static::$t->_('oa_auth_error_002'), ErrCode::$VALIDATE_ERROR);
            }

            if ($staff_info['formal'] != StaffInfoEnums::FORMAL_IN) {
                throw new ValidationException(static::$t->_('oa_auth_error_003'), ErrCode::$VALIDATE_ERROR);
            }

            // 鉴权通过: 生成token
            $this->setTokenPlatform(self::USER_LOGIN_PLATFORM_MOBILE);
            $token = $this->generateToken(['id' => $staff_info['staff_info_id']]);
            $data['token'] = $token->__toString();
            $data['token_type'] = 'Bearer';
            $data['oa_url'] = EnumsService::getInstance()->getSettingEnvValue(GlobalEnums::OA_DASHBOARD_PAGE_URL_CODE);

            // token 缓存
            $this->saveUserToken(self::USER_LOGIN_PLATFORM_MOBILE, $staff_info['staff_info_id'], $data['token']);

            // 记录BY跳OA的登录记录
            $record_model = new StaffLoginRecordModel();
            $record_data = [
                'staff_id' => $staff_info['staff_info_id'],
                'staff_name' => $staff_info['name'],
                'platform' => $params['platform'],
                'login_at' => date('Y-m-d H:i:s'),
            ];
            $record_model->i_create($record_data);

        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
            $this->logger->warning('by/kit跳转OA鉴权异常, ' . $e->getMessage());
        } catch (Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('by/kit跳转OA鉴权异常, ' . $e->getMessage());
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 缓存用户token
     *
     * @param string $platform pc / mobile
     * @param string $staff_id
     * @param string $token
     * @return bool
     * @throws BusinessException
     */
    public function saveUserToken(string $platform, string $staff_id, string $token)
    {
        if ($platform == self::USER_LOGIN_PLATFORM_PC) {
            $cache_key = RedisKey::SYS_USER_SESSION_PC_PREFIX . $staff_id;
        } else {
            $cache_key = RedisKey::SYS_USER_SESSION_MOBILE_PREFIX . $staff_id;
        }

        $expiration = $this->getTokenValidityPeriod();
        $log_data = [
            'cache_key' => $cache_key,
            'expiration' => $expiration,
            'platform' => $platform,
            'staff_id' => $staff_id,
            'token' => $token
        ];
        $log_data = json_encode($log_data, JSON_UNESCAPED_UNICODE);

        // 设置 hash cache
        $hset_res = RedisClient::getInstance()->getClient()->hset($cache_key, md5($token), date('Y-m-d H:i:s', time() + $expiration));
        RedisClient::getInstance()->getClient()->expire($cache_key, $expiration);
        if (!$hset_res) {
            throw new BusinessException('用户登录失败=' . $log_data, ErrCode::$BUSINESS_ERROR);
        }

        $this->logger->info('用户登录成功=' . $log_data);
        return true;
    }

    /**
     * 验证用户token
     *
     * @param string $staff_id
     * @param string $token
     * @param string $platform
     * @return bool
     * @throws BusinessException
     */
    public function checkUserToken(string $staff_id, string $token, string $platform = 'pc')
    {
        if ($platform == self::USER_LOGIN_PLATFORM_PC) {
            $cache_key = RedisKey::SYS_USER_SESSION_PC_PREFIX . $staff_id;
        } else {
            $cache_key = RedisKey::SYS_USER_SESSION_MOBILE_PREFIX . $staff_id;
        }
        if (RUNTIME == 'dev') {
            return true;
        }
        // 获取用户token值
        $cache_value = RedisClient::getInstance()->getClient()->hget($cache_key, md5($token));

        if (empty($cache_value)) {
            $log_data = [
                'platform' => $platform,
                'staff_id' => $staff_id,
                'token' => $token,
                'cache_key' => $cache_key,
                'cache_value' => $cache_value
            ];

            $this->logger->info('用户token校验异常,data=' . json_encode($log_data, JSON_UNESCAPED_UNICODE));
            throw new BusinessException('用户token校验异常', ErrCode::$BUSINESS_ERROR);
        }

        return true;
    }

    /**
     * 清除用户token缓存
     *
     * @param string $platform
     * @param string $staff_id
     * @param string $token
     * @return array
     */
    public function clearUserToken(string $platform, string $staff_id, string $token = '')
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';

        try {
            if ($platform == self::USER_LOGIN_PLATFORM_PC) {
                $cache_key = RedisKey::SYS_USER_SESSION_PC_PREFIX . $staff_id;
            } else {
                $cache_key = RedisKey::SYS_USER_SESSION_MOBILE_PREFIX . $staff_id;
            }

            $log = "cache_key={$cache_key}";

            // 删除用户缓存
            $redis_client = RedisClient::getInstance()->getClient();
            if (!empty($token)) {
                $token_k = md5($token);
                $log .= ", md5_token=$token_k, token={$token}";
                if (empty($redis_client->hget($cache_key, $token_k))) {
                    throw new ValidationException("用户token缓存不存在, 无需清除, {$log}", ErrCode::$VALIDATE_ERROR);
                }

                $del_res = $redis_client->hdel($cache_key, $token_k);
            } else {
                if (empty($redis_client->hgetall($cache_key))) {
                    throw new ValidationException("用户token缓存不存在, 无需清除, {$log}", ErrCode::$VALIDATE_ERROR);
                }

                $del_res = $redis_client->del($cache_key);
            }

            $log .= ", del_res={$del_res}";
            if (!$del_res) {
                throw new BusinessException("用户token缓存删除失败, {$log}", ErrCode::$BUSINESS_ERROR);
            }

            $this->logger->info("用户token缓存删除成功, {$log}");
        } catch (ValidationException $e) {
            $this->logger->info('用户token缓存删除提醒, ' . $e->getMessage());

        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->notice('用户token缓存删除异常, ' . $e->getMessage());
        } catch (Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('用户token缓存删除异常, ' . $e->getMessage());
        }

        return [
            'code' => $code,
            'message' => $message,
        ];
    }

    /**
     * BY OA 跳 手机浏览器 生成临时票据 ticket
     *
     * @param string $staff_id
     * @param string $token
     * @return array
     */
    public function generateTicket(string $staff_id, string $token)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';
        $data = [];

        try {
            $ticket_prefix = RedisKey::SYS_USER_TMP_TICKET_PREFIX . $staff_id;

            $log = "ticket_prefix={$ticket_prefix}";

            // 临时票据
            $ticket = md5(uniqid($ticket_prefix));

            // 临时票据 与 用户 / 当前token 的关系
            $cache_value = json_encode(['staff_id' => $staff_id, 'mobile_token' => $token]);

            $log .= "; ticket={$ticket}, cache_value={$cache_value}";
            if (!$this->setCache($ticket, $cache_value, env('MOBILE_TICKET_EXPIRATION', 86400))) {
                throw new BusinessException("ticket与token关系存储失败, {$log}", ErrCode::$BUSINESS_ERROR);
            }

            $data = [
                'ticket' => $ticket
            ];

            $this->logger->info("用户ticket获取成功, {$log}");

        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning('获取ticket异常, 原因可能是, ' . $e->getMessage());
        } catch (Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('获取ticket异常, 原因可能是, ' . $e->getMessage());
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * BY OA 跳 手机浏览器 生成临时票据 ticket, 同时 清除 浏览器中上一次的token
     *
     * @param string $ticket
     * @param string $old_token 浏览器中上一次的token
     * @return array
     */
    public function getNewToken(string $ticket, string $old_token = '')
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';
        $data = [];

        try {
            // ticket 校验
            $cache_val = json_decode($this->getCache($ticket), true);
            if (empty($cache_val)) {
                throw new ValidationException(static::$t->_('oa_ticket_error_001'), ErrCode::$VALIDATE_ERROR);
            }

            // 移动端token校验[ticket 绑定的 token]
            $this->checkUserToken($cache_val['staff_id'], $cache_val['mobile_token'], self::USER_LOGIN_PLATFORM_MOBILE);

            $staff_id = $cache_val['staff_id'];
            $log = "用户={$staff_id}, ticket=$ticket";

            // 生成浏览器的新token
            $data = [
                'token_type' => 'Bearer',
                'token' => '',
            ];
            $this->setTokenPlatform(self::USER_LOGIN_PLATFORM_PC);
            $token = $this->generateToken(['id' => $staff_id]);
            $data['token'] = $token->__toString();

            // 浏览器 新 token 缓存
            $this->saveUserToken(self::USER_LOGIN_PLATFORM_PC, $staff_id, $data['token']);

            $log .= ", 新token={$data['token']}";

            // 清除ticket
            $this->delCache($ticket);

            // 清除移动端token
            $this->clearUserToken(self::USER_LOGIN_PLATFORM_MOBILE, $staff_id, $cache_val['mobile_token']);

            // 清除浏览器老token
            if (!empty($old_token)) {
                $this->clearUserToken(self::USER_LOGIN_PLATFORM_PC, $staff_id, $old_token);
            }

            $this->logger->info("用户新token换取成功, {$log}");
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning('用户新token换取异常, ' . $e->getMessage());
        } catch (Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('用户新token换取异常, ' . $e->getMessage());
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 获取用户OA待办红点总数
     *
     * @param int $staff_id 用户工号
     * @param string $src 请求来源: app-用户操作app时拉取; push-by收到push时拉取
     * @return array
     */
    public function getUserReddotTotalCount(int $staff_id, string $src = '')
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';
        $data = [
            'total_count' => 0
        ];

        try {
            $log = "staff_id={$staff_id}, src={$src}";

            if (!in_array($src, [static::OA_REDDOT_GET_SRC_APP, static::OA_REDDOT_GET_SRC_PUSH])) {
                throw new ValidationException('src 渠道标识错误[app/push], 红点返回 0', ErrCode::$VALIDATE_ERROR);
            }

            // 静态高优用户配置(人工维护)
            $high_priority_staff_ids = EnumsService::getInstance()->getSettingEnvValueIds('oa_redhot_high_priority_staff_ids');
            if (in_array($staff_id, $high_priority_staff_ids)) {
                $log .= ', 静态高优用户';
            } else {
                // 动态高优用户配置(脚本动态维护)
                if (RedisClient::getInstance()->getClient()->sismember(RedisKey::OA_REDDOT_DYNAMIC_HIGH_PRIORITY_STAFF_CACHE_KEY, $staff_id)) {
                    $log .= ', 动态高优用户';

                } else {
                    $log .= ', 普通用户';

                    // 获取OA红点的开关状态
                    if (!$this->config->application->common_user_oa_redhot_switch) {
                        throw new ValidationException('获取OA红点的开关未开启, 红点返回 0', ErrCode::$VALIDATE_ERROR);
                    }

                    // 判断是否是预热缓存中的待办人[待办人缓存定期重置 + push实时追加缓存]
                    if (!RedisClient::getInstance()->getClient()->sismember(RedisKey::OA_REDDOT_PENDING_PREHOT_CACHE_KEY, $staff_id)) {
                        throw new ValidationException('非预热缓存中的待办人, 红点返回 0', ErrCode::$VALIDATE_ERROR);
                    }
                }
            }

            // 符合获取OA红点条件
            $staff_wf = $this->getStaffWfInfo(['id' => $staff_id], false, static::OA_REDDOT_CHANNEL_BY_OUTER);
            $data['total_count'] = array_sum($staff_wf);

            $log .= ", total_count={$data['total_count']}";
            $this->logger->info('用户获取OA红点数成功, ' . $log);

        } catch (ValidationException $e) {
            $this->logger->info($e->getMessage() . ', ' . $log);

        } catch (Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->notice("用户获取OA红点数异常, {$log}, 原因可能是: " . $e->getMessage());
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 红点待办人工号维护到预热缓存
     *
     * @param $staff_id
     * @param string $prehot_biz
     * @return bool
     */
    public function appendReddotPrehotCache($staff_id, string $prehot_biz = 'oa_audit_reddot')
    {
        try {
            switch ($prehot_biz) {
                // BY 中的OA审批待办
                case RedisKey::OA_REDDOT_PREHOT_CACHE_SCENCE_AUDIT_PENDING:
                    $prehot_cache_key = RedisKey::OA_REDDOT_PENDING_PREHOT_CACHE_KEY;
                    break;

                // BY 中的报销申请待办
                case RedisKey::OA_REDDOT_PREHOT_CACHE_SCENCE_REIMBURSEMENT_APPLY:
                    $prehot_cache_key = RedisKey::OA_REIMBURSEMENT_APPLY_PREHOT_CACHE_KEY;
                    break;
                default:
                    throw new ValidationException("prehot_biz 参数未定义, 请检查[{$prehot_biz}]", ErrCode::$VALIDATE_ERROR);
            }

            if (!RedisClient::getInstance()->getClient()->sismember($prehot_cache_key, $staff_id)) {
                RedisClient::getInstance()->getClient()->sadd($prehot_cache_key, $staff_id);

                $this->logger->info("OA红点待办人, {$staff_id} 写入预热缓存成功[{$prehot_biz}]");
            } else {
                $this->logger->info("OA红点待办人, {$staff_id} 预热缓存中已存在, 无需重复写入[{$prehot_biz}]");
            }
        } catch (Exception $e) {
            $this->logger->notice("OA红点待办人, {$staff_id} 写入预热缓存异常[{$prehot_biz}], 请知悉, 原因可能是: " . $e->getMessage());
        }

        return true;
    }

}
