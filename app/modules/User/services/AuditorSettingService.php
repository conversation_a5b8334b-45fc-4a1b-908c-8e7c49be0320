<?php

namespace App\Modules\User\Services;

use App\Library\Enums\OAWorkflowEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Library\BaseService;
use App\Modules\Workflow\Models\WorkflowModel;
use App\Modules\Workflow\Models\WorkflowNodeModel;
use App\Modules\Workflow\Models\WorkflowSubNodeModel;

class AuditorSettingService extends BaseService
{

    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 获取审批流列表（含各审批流节点）
     *
     * @param $params array
     * @return mixed
     */
    public function getWorkflowList(array $params)
    {
        $page_size = empty($params['pageSize']) ? 20 : $params['pageSize'];
        $page_num = empty($params['pageNum']) ? 1 : $params['pageNum'];
        $offset = $page_size * ($page_num - 1);

        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $data = [
            'items' => [],
            'pagination' => [
                'pageNum' => $page_num,
                'pageSize' => $page_size,
                'total_count' => 0,
            ]
        ];

        try {
            $builder = $this->modelsManager->createBuilder();
            $builder->columns([
                'node.flow_id AS flow_id',
                'flow.name AS flow_name',
                'node.id AS node_id',
                'node.name AS node_name',
                'node.type AS node_type',
                'node.auditor_type AS node_auditor_type',
                'node.auditor_id AS node_auditor_id',
                'sub_node.id AS sub_node_id',
                'sub_node.name AS sub_node_name',
                'sub_node.auditor_type AS sub_node_auditor_type',
                'sub_node.auditor_id AS sub_node_auditor_id',
            ]);
            $builder->from(['flow' => WorkflowModel::class]);
            $builder->leftjoin(WorkflowNodeModel::class, 'flow.id = node.flow_id', 'node');
            $builder->leftjoin(WorkflowSubNodeModel::class, 'node.id = sub_node.parent_node_id', 'sub_node');
            $builder->andWhere('flow.enable_status = :enable_status:', ['enable_status' => OAWorkflowEnums::WORKFLOW_ENABLE_STATUS_ING]);

            if (!empty($params['flow_name'])) {
                $builder->andWhere('flow.name LIKE :flow_name:', ['flow_name' => "%{$params['flow_name']}%"]);
            }

            if (!empty($params['node_name'])) {
                $builder->andWhere('node.name LIKE :node_name: OR sub_node.name LIKE :sub_node_name:',
                    [
                        'node_name' => "%{$params['node_name']}%",
                        'sub_node_name' => "%{$params['node_name']}%"
                    ]
                );
            }

            if (!empty($params['auditor_id'])) {
                $builder->andWhere('node.auditor_id LIKE :auditor_id: OR sub_node.auditor_id LIKE :sub_node_auditor_id:',
                    [
                        'auditor_id' => "%{$params['auditor_id']}%",
                        'sub_node_auditor_id' => "%{$params['auditor_id']}%"
                    ]
                );
            }

            $count = $builder->getQuery()->execute()->count();

            if ($count > 0) {
                $builder->orderBy('node.flow_id DESC, node.id ASC, sub_node.id ASC');
                $builder->limit($page_size, $offset);
                $items = $builder->getQuery()->execute()->toArray();
                foreach ($items as $k => $v) {
                    $items[$k]['node_type'] = in_array($v['node_type'], [3, 4]) ? '3' : $v['node_type'];
                    $items[$k]['node_auditor_id'] = $v['node_auditor_id'] ?? '';
                    $items[$k]['sub_node_id'] = $v['sub_node_id'] ?? '0';
                    $items[$k]['sub_node_name'] = $v['sub_node_name'] ?? '';
                    $items[$k]['sub_node_auditor_type'] = $v['sub_node_auditor_type'] ?? '0';
                    $items[$k]['sub_node_auditor_id'] = $v['sub_node_auditor_id'] ?? '';
                }
            }

            $data['items'] = $items ?? [];
            $data['pagination']['total_count'] = (string)$count;

        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->getDI()->get('logger')->warning('权限管理 - 审批人设置 - 列表异常: ' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data
        ];
    }

    /**
     * 获取审批节点详情
     * @param $params array
     * @return mixed
     */
    public function getWorkflowNodeDetail(array $params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];

        try {
            $builder = $this->modelsManager->createBuilder();
            $builder->columns([
                'node.flow_id AS flow_id',
                'flow.name AS flow_name',
                'node.id AS node_id',
                'node.name AS node_name',
                'node.type AS node_type',
                'node.auditor_type AS node_auditor_type',
                'node.auditor_id AS node_auditor_id',
                'sub_node.id AS sub_node_id',
                'sub_node.name AS sub_node_name',
                'sub_node.auditor_type AS sub_node_auditor_type',
                'sub_node.auditor_id AS sub_node_auditor_id',
            ]);
            $builder->from(['flow' => WorkflowModel::class]);
            $builder->leftjoin(WorkflowNodeModel::class, 'flow.id = node.flow_id', 'node');
            $builder->leftjoin(WorkflowSubNodeModel::class, 'node.id = sub_node.parent_node_id', 'sub_node');
            $builder->where('node.id = :node_id:', ['node_id' => $params['node_id']]);
            $builder->andWhere('node.flow_id = :flow_id:', ['flow_id' => $params['flow_id']]);

            if (!empty($params['sub_node_id'])) {
                $builder->andWhere('sub_node.id = :sub_node_id:', ['sub_node_id' => $params['sub_node_id']]);
            }

            $data = $builder->getQuery()->getSingleResult();

            if (empty($data)) {
                throw new ValidationException("审批流节点不存在[{$params['flow_id']}-{$params['node_id']}-{$params['sub_node_id']}]", ErrCode::$VALIDATE_ERROR);
            }

            $data = $data->toArray();
            $data['sub_node_id'] = $data['sub_node_id'] ?? '0';
            $data['sub_node_name'] = $data['sub_node_name'] ?? '';
            $data['sub_node_auditor_type'] = $data['sub_node_auditor_type'] ?? '0';
            $data['sub_node_auditor_id'] = $data['sub_node_auditor_id'] ?? '';

            if (!in_array($data['node_type'], [3, 4]) || ($data['node_auditor_type'] != 1 && $data['sub_node_auditor_type'] != 1)) {
                throw new ValidationException("该类型节点不允许编辑[{$params['flow_id']}-{$params['node_id']}-{$data['node_type']}-{$data['node_auditor_type']}]", ErrCode::$VALIDATE_ERROR);
            }

            $data['node_auditor_id'] = $data['node_auditor_id'] ?? '';

        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();

        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->getDI()->get('logger')->warning('权限管理 - 审批人设置 - 详情异常: ' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $code == ErrCode::$SUCCESS ? $data : []
        ];
    }

    /**
     * 更新审批节点审批人
     * @param $params array
     * @return mixed
     */
    public function saveWorkflowNodeAuditor(array $params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';

        try {
            $node_model = WorkflowNodeModel::findFirst([
                'conditions' => 'id = :node_id: AND flow_id = :flow_id:',
                'bind' => ['node_id' => $params['node_id'], 'flow_id' => $params['flow_id']],
            ]);

            // 子审批流
            $sub_node_model = null;
            $is_sub_node = isset($params['sub_node_id']) && !empty($params['sub_node_id']);
            if ($is_sub_node) {
                $sub_node_model = WorkflowSubNodeModel::findFirst([
                    'conditions' => 'id = :sub_node_id: AND flow_id = :flow_id: AND parent_node_id = :parent_node_id:',
                    'bind' => ['sub_node_id' => $params['sub_node_id'], 'flow_id' => $params['flow_id'], 'parent_node_id' => $params['node_id']],
                ]);
            }

            if (empty($node_model) || ($is_sub_node && empty($sub_node_model))) {
                throw new ValidationException("审批流节点不存在[{$params['flow_id']}-{$params['node_id']}-{$params['sub_node_id']}]", ErrCode::$VALIDATE_ERROR);
            }

            if (!in_array($node_model->type, [3, 4]) || (!$is_sub_node && $node_model->auditor_type != 1) || ($is_sub_node && $sub_node_model->auditor_type != 1)) {
                throw new ValidationException("该类型节点不允许编辑[{$params['flow_id']}-{$params['node_id']}-{$node_model->type}-{$node_model->auditor_type}]", ErrCode::$VALIDATE_ERROR);
            }

            $this->getDI()->get('logger')->info('审批流 - 审批人修改 - 修改前数据[一级节点]: ' . json_encode($node_model->toArray(), JSON_UNESCAPED_UNICODE));
            $this->getDI()->get('logger')->info('审批流 - 审批人修改 - 修改前数据[子节点]: ' . json_encode(!empty($sub_node_model) ? $sub_node_model->toArray() : [], JSON_UNESCAPED_UNICODE));

            $save_data = [
                'auditor_id' => $params['auditor_ids'] ?? ''
            ];

            if ($is_sub_node) {
                $save_res = $sub_node_model->save($save_data);
            } else {
                $save_res = $node_model->save($save_data);
            }

            if (!$save_res) {
                throw new BusinessException('保存失败', ErrCode::$BUSINESS_ERROR);
            }

            $this->getDI()->get('logger')->info('审批流 - 审批人修改 - 修改后数据[一级节点]: ' . json_encode($node_model->toArray(), JSON_UNESCAPED_UNICODE));
            $this->getDI()->get('logger')->info('审批流 - 审批人修改 - 修改后数据[子节点]: ' . json_encode(!empty($sub_node_model) ? $sub_node_model->toArray() : [], JSON_UNESCAPED_UNICODE));

        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->getDI()->get('logger')->warning('权限管理 - 审批人变更异常: ' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => []
        ];
    }


}