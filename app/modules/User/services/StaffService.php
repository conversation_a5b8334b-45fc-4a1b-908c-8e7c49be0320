<?php


namespace App\Modules\User\Services;


use App\Library\BaseService;
use App\Library\Enums;
use App\Library\Enums\StaffInfoEnums;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Organization\Models\SysDepartmentModel;
use App\Modules\Organization\Models\SysStoreModel;
use App\Modules\User\Models\HrJobTitleModel;
use App\Modules\User\Models\HrStaffInfoModel;
use App\Library\ErrCode;
use App\Repository\DepartmentRepository;
use App\Repository\HrStaffRepository;

class StaffService extends BaseService
{

    private static $instance = null;

    private function __construct()
    {
    }

    public static function getInstance()
    {
        if (!self::$instance) {
            self::$instance = new self();
        }

        return self::$instance;
    }


    /**
     * @param string $q
     * @param int $departmentId
     * @param string $storeId
     * @param int $pageNum
     * @param int $pageSize
     * @param array $condition
     * @return mixed
     */
    public function getStaffsByDepOrStore($q = '', $departmentId = 0, $storeId = '', $pageNum = 1, $pageSize = 20, $condition = [])
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['si' => HrStaffInfoModel::class])
            ->leftJoin(HrJobTitleModel::class, 'si.job_title=sjt.id', 'sjt')
            ->leftJoin(SysDepartmentModel::class, 'si.sys_department_id=d.id ', 'd')
            ->leftJoin(SysStoreModel::class, 'si.sys_store_id=s.id ', 's');

        // 过滤掉子账号
        $builder->where('si.is_sub_staff = 0');

        //特殊业务 不需要在职状态的限制
        if (isset($condition['state']) && $condition['state'] == 'all') {
        } else {
            $builder->andWhere('si.state = :staff_state:',['staff_state' => StaffInfoEnums::STAFF_STATE_IN]);
        }

        if (!empty($q)) {
            $builder->andWhere('si.name like :q: or si.name_en like :q: or si.staff_info_id like :q: or si.nick_name like :q:', ['q' => '%' . $q . '%']);
        }

        if (!empty($departmentId)) {
            $builder->andWhere('si.sys_department_id =:department_id: or si.node_department_id = :department_id:', ['department_id' => $departmentId]);
        }

        if (!empty($storeId)) {
            $builder->andWhere('si.sys_store_id =:store_id:', ['store_id' => $storeId]);
        }

        if (isset($condition['formal']) && !empty($condition['formal'])) {
            $builder->inWhere('si.formal', [StaffInfoEnums::FORMAL_IN, StaffInfoEnums::FORMAL_TRAINEE]);
        }

        if (isset($condition['staff_info_id']) && !empty($condition['staff_info_id'])) {
            $builder->andWhere('si.staff_info_id =:staff_info_id:', ['staff_info_id' => $condition['staff_info_id']]);
        }

        $count = (int)$builder->columns('COUNT(si.id) AS total')->getQuery()->getSingleResult()->total;

        $list = [];
        if ($count) {
            $columns=[
                'si.staff_info_id as id','si.name','si.name_en','sjt.job_name as job_title','si.nick_name',
                'si.sys_department_id','d.name as department_name',
                'si.sys_store_id','s.name as store_name,si.identity'
            ];
            $builder->columns($columns);
            $builder->limit($pageSize, $pageSize * ($pageNum - 1));
            $builder->orderBy('si.id');
            $list = $builder->getQuery()->execute()->toArray();
            foreach ($list as $k => $v) {
                if ($v['sys_store_id'] == '-1') {
                    $list[$k]['store_name'] = "Header Office";
                }

                //列表加昵称
                $list[$k]['name'] = $this->getNameAndNickName($v['name'], $v['nick_name']);
            }
        }

        return [
            'items' => $list,
            'pagination' => [
                'current_page' => $pageNum,
                'per_page' => $pageSize,
                'total_count' => $count,
            ]
        ];
    }

    /**
     * 获取部门列表树状列表
     * @param int $deleted 是否删除 0未删除 1已删除 2全部
     * @return array
     *
     */
    public function departmentList($deleted = Enums\GlobalEnums::IS_NO_DELETED)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('id,ancestry,name as label,type');
        $builder->from(SysDepartmentModel::class);
        if ($deleted == Enums\GlobalEnums::IS_NO_DELETED) {
            $builder->where('deleted = 0');
        }
        //$builder->notInWhere('id',[Enums::DEPARTMENT_ID_IT_PRODUCTS]);// 2021-09-09  江奇要求先放开
        //$builder->andWhere('company_id = :company_id:',['company_id' => 1]);
        //不要IT&PRODUCTS部门
        $builder->orderby('name ASC');
        $list = $builder->getQuery()->execute()->toArray();
        return array_values(list_to_tree($list,'id','ancestry','children',''));
        /*$data = [
        ];
        foreach ($list as $k=>$v){
            //公司下的部门
            if($v['type'] == 1){
                $data = array_merge($data,array_values(list_to_tree($list,'id','ancestry','children',$v['id'])));
            }

            //组织下的部
            if($v['type'] == 3 && empty($v['ancestry'])){
                $tArr = [];
                $tArr['id'] = $v['id'];
                $tArr['label'] = $v['label'];
                $tArr['ancestry'] = $v['ancestry'];
                $tArr['children'] =  array_values(list_to_tree($list,'id','ancestry','children',$v['id']));
                $data = array_merge($data,[$tArr]);
            }
        }*/
    }

    /**
     * @param $departmentId
     * @param $level
     * @return mixed
     */
    public function getParentDepartment($departmentId,$level)
    {
        $department = SysDepartmentModel::findFirst($departmentId);
        $parents = SysDepartmentModel::query()
            ->inWhere('id',explode('/',$department->ancestry_v3))
            ->execute()
            ->toArray();
        foreach ($parents as $parent) {
            if ($parent['level'] == $level){
                return $parent;
            }
        }
    }

    /**
     * 按照特定条件搜索员工信息
     * @param array $params['name'=>'员工工号或员工姓名']
     * @return array
     */
    public function searchStaff($params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            $builder = $this->modelsManager->createBuilder();
            $columns=[
                'si.staff_info_id as staff_id','si.name as staff_name','si.state','si.wait_leave_state', 'si.leave_date','si.email', 'si.hire_type',
                'sjt.id as job_id','sjt.job_name',
                'si.sys_store_id','s.name as store_name',
                'si.node_department_id, d.name as node_department_name',
                'si.contract_company_id',
                'd.company_id, d.company_name',
            ];
            $builder->columns($columns)
                ->from(['si'=>HrStaffInfoModel::class])
                ->leftJoin(HrJobTitleModel::class,'cast(si.job_title as UNSIGNED)=sjt.id','sjt')
                ->leftJoin(SysDepartmentModel::class,'si.node_department_id=d.id ','d')
                ->leftJoin(SysStoreModel::class,'si.sys_store_id=s.id ','s')
                ->where('si.is_sub_staff=:is_sub_staff: and si.formal in ({formals:array})', ['is_sub_staff'=>Enums\StaffInfoEnums::IS_SUB_STAFF_NO, 'formals'=>[Enums\StaffInfoEnums::FORMAL_IN, Enums\StaffInfoEnums::FORMAL_TRAINEE]]);
            //按照员工名或工号检索
            $staff_name_or_id = $params['name'] ?? '';
            if (!empty($staff_name_or_id)) {
                $builder->andWhere('si.staff_info_id LIKE :staff_name_or_id: OR si.name LIKE :staff_name_or_id:', ['staff_name_or_id'=>'%'.$staff_name_or_id.'%']);
            }
            //按照工号检索
            if (isset($params['staff_id']) && !empty($params['staff_id'])) {
                if (is_array($params['staff_id'])) {
                    //传递了工号，按照指定工号筛选
                    $builder->andWhere('si.staff_info_id in ({staff_id:array})', ['staff_id'=> $params['staff_id']]);
                } else {
                    //传递了工号，按照指定工号筛选
                    $builder->andWhere('si.staff_info_id = :staff_id:', ['staff_id'=> $params['staff_id']]);
                }
            }

            if (!empty($params['hire_type_list']) && is_array($params['hire_type_list'])) {
                $builder->inWhere('si.hire_type', $params['hire_type_list']);
            }

            //在职状态搜索员工状态 1 在职 2 离职 3 停职
            $state = $params['state'] ?? 0;
            if (!empty($state)) {
                $builder->andWhere('si.state = :state:', ['state' => $state]);
            }
            $builder->limit($params['limit'] ?? Enums\MaterialEnums::PAGE_SIZE);
            $data = $builder->getQuery()->execute()->toArray();
            //判断[成本中心]是否必填 , 只针对 资产领用出库-新增 时 TODO 后续提出去
            $setting_department = EnvModel::getEnvByCode('material_asset_pccode_required','');
            $setting_department = empty($setting_department)?[]:explode(',',$setting_department);
            //查询公司名称
            $company_ids = array_values(array_unique(array_column($data, 'company_id')));
            $company_data = (new DepartmentRepository())->getDepartmentByIds($company_ids);
            $company_kv = array_column($company_data, 'name', 'id');
            //协议签署公司
            $contractList = EnumsService::getInstance()->getPayrollCompanyInfo();
            if (!empty($data)) {
                //雇佣类型
                $hire_type_enum = EnumsService::getInstance()->getHireTypeEnum();
                foreach ($data as &$item) {
                    if ($item['sys_store_id'] == -1) {
                        $item['store_name'] = 'Head Office';
                    }
                    $item['store_name'] = is_null($item['store_name']) ? '' : $item['store_name'];
                    $item['show_name'] = $item['staff_id'].'('.$item['staff_name'].')';
                    //资产领用出库-新增 判断[成本中心]是否必填
                    $item['pc_code_required'] = in_array($item['node_department_id'],$setting_department)?true:false;
                    //在职状态
                    $item['state_text'] = static::$t[StaffInfoEnums::$staff_state[($item['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES) ? StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE : $item['state']]];
                    //公司名称重新赋值
                    $item['company_name'] = $company_kv[$item['company_id']] ?? $item['company_name'];
                    $item['hire_type_text'] = $hire_type_enum[$item['hire_type']] ?? '';
                    $item['contract_company'] = $contractList[$item['contract_company_id']] ?? '';
                }
            }
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('search-staff-list-failed:' . $real_message . ' select :' . json_encode($params));
        }
        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }


    /**
     * @param $condition
     * @return mixed
     */
    public function getDepartmentList($condition)
    {
        $code      = ErrCode::$SUCCESS;
        $message   = $real_message = '';
        $data      = [];
        $page_size = empty($condition['pageSize']) ? 20 : $condition['pageSize'];

        $page_num = empty($condition['pageNum']) ? 1 : $condition['pageNum'];

        $offset = $page_size * ($page_num - 1);
        try {
            $builder = $this->modelsManager->createBuilder();
            $columns = ['name', 'manager_id', 'manager_name'];
            $builder->from(SysDepartmentModel::class);
            $builder->andWhere('deleted = :deleted:', ['deleted' => 0]);

            if (!empty($condition['staff_id'])) {
                $builder->andWhere('manager_id = :manager_id:', ['manager_id' => $condition['staff_id']]);
            }

            if (!empty($condition['department_id'])) {
                $builder->andWhere('id = :department_id:', ['department_id' => $condition['department_id']]);
            }
            $count_info = $builder->columns('COUNT(distinct(manager_id)) as t_count')->getQuery()->getSingleResult();
            $count      = $count_info->t_count ?? 0;
            if($count>0){
                $builder->columns($columns);
                $builder->groupBy('manager_id');

                $builder->limit($page_size, $offset);
                $data = $builder->getQuery()->execute()->toArray();
            }

        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('search-depart-list-failed:' . $real_message . ' select :' . json_encode($condition));
        }
        return [
            'code'    => $code,
            'message' => $message,
            'data'    =>[
                'items'=> $data??[],
                'pagination' => [
                    'current_page' => $page_num,
                    'per_page' => $page_size,
                    'total_count' => (int)$count,
                ]
            ]
        ];

    }

    /**
     * 获取员工在职状态(待离职用99)
     * @param $staff_id
     * @return int
     * @date 2023/4/2
     */
    public function getStaffState($staff_id)
    {
        // 查员工最新在职状态
        $staff_info = (new HrStaffRepository())->getStaffById($staff_id);
        if (empty($staff_info)) {
            return 0;
        }
        $staff_state = $staff_info['state'];
        if ($staff_info['state'] == StaffInfoEnums::STAFF_STATE_IN) {
            if ($staff_info['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES) {
                $staff_state = StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE;
            }
        }
        return $staff_state;
    }










}
