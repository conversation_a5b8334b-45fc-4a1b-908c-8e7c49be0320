<?php

namespace App\Modules\User\Services;

use \Exception;
use App\Library\ErrCode;
use App\Library\BaseService;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\Validation\Validation;
use App\Repository\HrStaffRepository;
use App\Library\Enums\WorkflowPendingEnums;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Models\oa\SmsPendingWhitelistModel;

class SmsWhitelistService extends BaseService
{
    /**
     * 白名单工号校验规则
     */
    public static $staff_validation = [
        'staff_id' => 'Required|IntGe:1|>>>:params error[staff_id]',
    ];

    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 获取短信国家码ID
     */
    private static function getSmsNationIds()
    {
        return implode(',', array_keys(WorkflowPendingEnums::$country_id_and_sms_nation_map));
    }

    /**
     * 白名单保存校验规则
     *
     * @param array $params
     * @throws ValidationException
     */
    public static function saveValidation(array $params)
    {
        $validation = [
            'staff_id' => 'Required|StrLenGeLe:1,30|>>>:params error[staff_id]',
            'country_id' => 'Required|StrIn:' . self::getSmsNationIds() . '|>>>:params error[country_id]',
            'mobile' => 'Required|StrLenGeLe:0,16|>>>:params error[mobile]',
            'remark' => 'Required|StrLenGeLe:0,50|>>>:params error[remark]',
        ];



        if (mb_strlen($params['mobile']) > 0) {
            $validation['mobile'] = 'Required|Regexp:/^[\d]{9,16}$/|>>>:' . static::$t->_('mobile_input_error');
        }

        Validation::validate($params, $validation);
    }

    /**
     * 生成短信国家码枚举
     * @return array
     */
    public function getSmsNationEnums()
    {
        $result = [];
        foreach (WorkflowPendingEnums::$country_id_and_sms_nation_map as $key => $value) {
            $result[] = [
                'value' => (string) $key,
                'label' => $value
            ];
        }

        return $result;
    }

    /**
     * 获取白名单列表
     *
     * @param $params array
     * @return array
     */
    public function getList(array $params)
    {
        $page_size = $params['pageSize'] ?? GlobalEnums::DEFAULT_PAGE_SIZE;
        $page_num = $params['pageNum'] ?? GlobalEnums::DEFAULT_PAGE_NUM;
        $offset = $page_size * ($page_num - 1);

        $code = ErrCode::$SUCCESS;
        $message = 'success';

        $data = [
            'items' => [],
            'pagination' => [
                'pageNum' => (int) $page_num,
                'pageSize' => (int) $page_size,
                'total_count' => 0,
            ]
        ];

        try {
            $builder = $this->modelsManager->createBuilder();
            $builder->from(SmsPendingWhitelistModel::class);
            $builder->where('is_del = :is_del:', ['is_del' => GlobalEnums::IS_NO_DELETED]);
            if (!empty($params['staff_id'])) {
                $builder->andWhere('staff_id = :staff_id:', ['staff_id' => $params['staff_id']]);
            }

            if (!empty($params['mobile'])) {
                $builder->andWhere('mobile = :mobile:', ['mobile' => $params['mobile']]);
            }

            if (!empty($params['country_id'])) {
                $builder->andWhere('country_id = :country_id:', ['country_id' => $params['country_id']]);
            }

            $count = (int) $builder->columns('COUNT(id) AS total')->getQuery()->getSingleResult()->total;

            $items = [];
            if ($count) {
                $builder->columns([
                    'staff_id',
                    'mobile',
                    'country_id',
                    'sms_nation',
                    'remark'
                ]);
                $builder->orderBy('mobile ASC');
                $builder->limit($page_size, $offset);
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleList($items);
            }

            $data['items'] = $items;
            $data['pagination']['total_count'] = $count;

        } catch (Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('权限管理-短信白名单列表异常: ' . $e->getMessage());
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 处理列表数据
     *
     * @param array $items
     * @return array
     */
    private function handleList(array $items)
    {
        $staff_ids = array_column($items, 'staff_id');
        if (empty($staff_ids)) {
            return $items;
        }

        // 实时获取员工姓名/在职状态/工作所在地
        $hr_staff_repository = new HrStaffRepository();
        $staff_info_list = $hr_staff_repository->getStaffListByStaffIds($staff_ids);
        $staff_other_items =  $hr_staff_repository->getStaffItems($staff_ids, [StaffInfoEnums::HR_STAFF_ITEMS_WORKING_COUNTRY]);
        $staff_other_items = array_column($staff_other_items, 'value', 'staff_info_id');

        foreach ($items as $k => &$v) {
            $v['staff_name'] = '';
            $v['state_label'] = '';
            $v['working_country_label'] = '';

            $_staff_info = $staff_info_list[$v['staff_id']] ?? [];
            if (!empty($_staff_info)) {
                $v['staff_name'] = $_staff_info['name'];

                $_state = $_staff_info['state'] == StaffInfoEnums::STAFF_STATE_IN && $_staff_info['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES ? StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE : $_staff_info['state'];
                $v['state_label'] = StaffInfoEnums::$staff_state[$_state] ? static::$t->_(StaffInfoEnums::$staff_state[$_state]) : '';
            }

            $_working_country_lang_key = StaffInfoEnums::$working_country[$staff_other_items[$v['staff_id']] ?? 0] ?? null;
            $v['working_country_label'] = $_working_country_lang_key ? static::$t->_($_working_country_lang_key) : '';
        }

        return $items;
    }

    /**
     * 删除白名单
     *
     * @param string $staff_id
     * @param array $user
     * @return array
     */
    public function removeOne(string $staff_id, array $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';

        try {
            $exist_model = SmsPendingWhitelistModel::findFirst([
                'conditions' => 'staff_id = :staff_id:',
                'bind' => ['staff_id' => $staff_id]
            ]);
            if (empty($exist_model)) {
                throw new ValidationException(static::$t->_('data_not_exist_by_id', ['id' => $staff_id]), ErrCode::$VALIDATE_ERROR);
            }

            $exist_model->is_del = GlobalEnums::IS_DELETED;
            $exist_model->updated_at = date('Y-m-d H:i:s');
            $exist_model->updated_id = $user['id'];

            if ($exist_model->save() === false) {
                throw new BusinessException('短信白名单移除失败, 原因可能是:' . get_data_object_error_msg($exist_model) . '数据:' . json_encode($exist_model->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }

            // 泰国同步其他国家
            $this->sendWhiteListData($exist_model->toArray());

        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning('sms_whitelist_remove_fail' . $e->getMessage());
        } catch (Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('sms_whitelist_remove_fail' . $e->getMessage());
        }

        return [
            'code' => $code,
            'message' => $message
        ];
    }

    /**
     * 保存白名单
     *
     * @param array $params
     * @param array $user
     * @return array
     */
    public function saveOne(array $params, array $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';

        try {
            // 验证工号是否在HRIS中存在
            $staff_info = (new HrStaffRepository())->getStaffById($params['staff_id']);
            if (empty($staff_info)) {
                throw new ValidationException(static::$t->_('re_staff_info_id_not_exist'), ErrCode::$VALIDATE_ERROR);
            }

            // 查看是否已存在
            $model = SmsPendingWhitelistModel::findFirst([
                'conditions' => 'staff_id = :staff_id:',
                'bind' => ['staff_id' => $params['staff_id']]
            ]);

            // 不存在则创建
            if (empty($model)) {
                $model = new SmsPendingWhitelistModel();
                $model->created_at = date('Y-m-d H:i:s');
                $model->created_id = $user['id'];
            }

            $model->staff_id = $staff_info['staff_info_id'];
            $model->staff_name = $staff_info['name'];
            $model->mobile = $params['mobile'];
            $model->country_id = $params['country_id'];
            $model->sms_nation = WorkflowPendingEnums::$country_id_and_sms_nation_map[$params['country_id']] ?? '';
            $model->remark = $params['remark'];
            $model->is_del = GlobalEnums::IS_NO_DELETED;
            $model->updated_at = date('Y-m-d H:i:s');
            $model->updated_id = $user['id'];

            if ($model->save() === false) {
                throw new BusinessException('短信白名单保存失败, 原因可能是:' . get_data_object_error_msg($model) . '数据:' . json_encode($model->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }

            // 泰国同步其他国家
            $this->sendWhiteListData($model->toArray());

        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning('sms_whitelist_save_fail' . $e->getMessage());
        } catch (Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('sms_whitelist_save_fail' . $e->getMessage());
        }

        return [
            'code' => $code,
            'message' => $message
        ];
    }

    /**
     * 白名单数据由泰国同步到其他国家
     *
     * @param array $sms_whitelist_data
     * @throws BusinessException
     */
    private function sendWhiteListData(array $sms_whitelist_data)
    {

        $runtime_env = get_runtime_env();
        $other_country_api_list = WorkflowPendingEnums::$need_sync_sms_whitelist_api_list[$runtime_env] ?? [];
        if (empty($other_country_api_list)) {
            throw new BusinessException('短信白名单同步API未配置' . $runtime_env , ErrCode::$BUSINESS_ERROR);
        }

        // 泰国同步到其他国家
        $header = [
            'content-type: application/json',
            'pending-auth-code: ' . WorkflowPendingEnums::HTTP_SYNC_WHITELIST_DATA_AUTH_CODE
        ];

        $post_json_data = json_encode(['data' => base64_encode(serialize($sms_whitelist_data))]);
        foreach ($other_country_api_list as $country_code => $country_api) {
            $log = '短信白名单同步';
            $log .= ', 数据=' . json_encode($sms_whitelist_data, JSON_UNESCAPED_UNICODE);
            $log .= ", 待同步国家[$country_code], api request: $country_api";
            $curl_result = curl_request($country_api, $post_json_data, 'post', $header);
            $log .= ", api response: {$curl_result}";

            // 返回的json数据解码
            $api_result = json_decode($curl_result, true);
            if (isset($api_result['code']) && $api_result['code'] == ErrCode::$SUCCESS) {
                $this->logger->info($log . ', 同步成功');
            } else {
                $this->logger->warning($log . ', 同步失败');
            }
        }
    }

    /**
     * 存储同步过来的白名单数据(接收来自泰国的数据)
     *
     * @param string $data
     * @return array
     */
    public function saveSyncData(string $data)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';

        try {
            $params = unserialize(base64_decode($data));
            $this->logger->info('已接收待处理的短信白名单数据=' . json_encode($params, JSON_UNESCAPED_UNICODE));

            if (empty($params['staff_id']) || empty($params['country_id']) || empty($params['sms_nation'])) {
                throw new ValidationException('同步的数据不合规, 请检查', ErrCode::$VALIDATE_ERROR);
            }

            // 查看是否已存在
            $model = SmsPendingWhitelistModel::findFirst([
                'conditions' => 'staff_id = :staff_id:',
                'bind' => ['staff_id' => $params['staff_id']]
            ]);

            // 不存在则创建
            if (empty($model)) {
                $model = new SmsPendingWhitelistModel();
            }

            $model->staff_id = $params['staff_id'];
            $model->staff_name = $params['staff_name'];
            $model->mobile = $params['mobile'];
            $model->country_id = $params['country_id'];
            $model->sms_nation = $params['sms_nation'];
            $model->remark = $params['remark'];
            $model->is_del = $params['is_del'];
            $model->created_at = $params['created_at'];
            $model->created_id = $params['created_id'];
            $model->updated_at = $params['updated_at'];
            $model->updated_id = $params['updated_id'];

            if ($model->save() === false) {
                throw new BusinessException('短信白名单保存失败(来自同步), 原因可能是:' . get_data_object_error_msg($model) . '数据:' . json_encode($model->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }

        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
            $this->logger->warning('sms_whitelist_sync_save_fail' . $message);
        } catch (Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = $e->getMessage();
            $this->logger->error('sms_whitelist_sync_save_fail' . $message);
        }

        return [
            'code' => $code,
            'message' => $message
        ];
    }

    /**
     * 获取员工基本信息(姓名)
     *
     * @param int $staff_id
     * @return array
     */
    public function getStaffInfo(int $staff_id)
    {
        $result = [];

        $staff_info = (new HrStaffRepository())->getStaffById($staff_id);
        if (!empty($staff_info)) {
            $result = [
                'staff_id' => $staff_info['staff_info_id'],
                'staff_name' => trim($staff_info['name'])
            ];
        }

        return $result;
    }

}
