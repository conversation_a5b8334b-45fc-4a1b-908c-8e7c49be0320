<?php


namespace App\Modules\User\Models;


use App\Library\BaseModel;

class TripModel extends BaseModel
{
    public function initialize()
    {
        $this->setConnectionService('db_rbackyard');
        $this->setSource('business_trip');
        $this->hasMany(
            'id',
            TripImgModel::class,
            'business_trip_id',
            [
                'reusable' => true,
                'alias' => 'pic',
            ]
        );
    }
}
