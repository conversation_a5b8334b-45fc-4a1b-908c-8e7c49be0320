<?php


namespace App\Modules\User\Models;


use App\Models\Base;

class AttachModel extends Base
{

    public function initialize()
    {
        $this->setConnectionService('db_oa');
        $this->setSource('sys_attachment');
    }

    public function batchInsert($data)
    {
        if (empty($data)) {
            return false;
        }
        $keys = array_keys(reset($data));
        $keys = array_map(function ($key) {
            return "`{$key}`";
        }, $keys);
        $keys = implode(',', $keys);
        $sql = "INSERT INTO " . $this->getSource() . " ({$keys}) VALUES ";
        foreach ($data as $v) {
            $v = array_map(function ($value) {
                if ($value === null) {
                    return 'NULL';
                } else {
                    $value = addslashes($value); //处理特殊符号，如单引号
                    return "'{$value}'";
                }
            }, $v);
            $values = implode(',', array_values($v));
            $sql .= " ({$values}), ";
        }
        $sql = rtrim(trim($sql), ',');
        //DI中注册的数据库服务名称为"db"
        $result = $this->getDI()->get('db_oa')->execute($sql);
        if (!$result) {
            return false;
        }
        return $result;
    }
}