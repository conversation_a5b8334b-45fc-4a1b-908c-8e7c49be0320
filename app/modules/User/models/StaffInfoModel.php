<?php


namespace App\Modules\User\Models;


use App\Library\BaseModel;

class StaffInfoModel extends BaseModel
{

    public function initialize()
    {
        $this->setConnectionService('db_fle');
        $this->setSource('staff_info');
        $this->belongsTo(
            'department_id',
            DepartmentModel::class,
            'id',
            [
                'reusable' => true,
                'alias' => 'Department',
            ]
        );
        $this->belongsTo(
            'job_title',
            StaffInfoJobTitleModel::class,
            'id',
            [
                'reusable' => true,
                'alias' => 'JobTitle',
            ]
        );
    }


    //获取单个员工信息
    public function user_info($staff_id)
    {
        $sql = "SELECT * FROM `staff_info` s
                WHERE s.`id` = {$staff_id} ";
        return $this->getDI()->get('db_fle')->fetchOne($sql);

    }


    //批量获取员工信息  目前只有 名字 哈哈,+Email
    public function users_info($staff_ids)
    {
        $staff_ids = array_filter(array_unique($staff_ids));
        if (empty($staff_ids))
            return false;

        $str = implode(',', $staff_ids);
        $sql = "select id,`name`,department_id,email from staff_info where id in ({$str})";

        return $this->getDI()->get('db_fle')->fetchAll($sql);
    }

    /**
     * 获得邮箱
     * @param $staff_ids
     * @return array
     */
    public function getEmails($staff_ids)
    {
        $users = $this->users_info($staff_ids);
        if (empty($users)) {
            return [];
        }
        $emails = array_filter(array_column($users, 'email'));

        //对于17152的邮箱**************************改成********************邮箱
        foreach ($emails as $k => $v) {
            if ($v == '<EMAIL>') {
                $emails[$k] = '<EMAIL>';
            }
        }
        return $emails;
    }


    public function getUserIdToEmail($staff_ids)
    {
        $users = $this->users_info($staff_ids);
        if (empty($users)) {
            return [];
        }
        $emails = array_column($users, "email",'id');
        //对于17152的邮箱**************************改成********************邮箱
        foreach ($emails as $k => $v) {
            if ($v == "<EMAIL>") {
                $emails[$k] = "<EMAIL>";
            }
        }
        return $emails;
    }



}