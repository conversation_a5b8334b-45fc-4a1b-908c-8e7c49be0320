<?php

namespace App\Modules\User\Models;

use App\Library\BaseModel;

class DepartmentModel extends BaseModel
{
    public function initialize()
    {
        $this->setConnectionService('db_rbackyard');
        $this->setSource('sys_department');
    }


    public function get_department($ancestry = null){
        $sql = "select id, `name`,ancestry from sys_department where deleted = 0 and ancestry is not null ";
//        if(empty($ancestry))
//            $sql .= " ancestry = 1";
//        else
//            $sql .= "ancestry = {$ancestry}";

        return $this->getDI()->get('db_rbackyard')->fetchAll($sql);
    }

    /**
     * @param $condition
     * @return mixed
     */
    public static function getFirst($condition)
    {
        return parent::findFirst($condition);
    }


    /**
     * 通过当前部门id获得一级部门id
     * @param $department_id
     * @return integer
     */
    public static function getFirstDepartmentIdByDepartmentId($department_id){
        $item = static::findFirst([
            "conditions"=>'id=:id:',
            "bind"=>['id'=>$department_id]
        ]);

        if(empty($item)){
            return 0;
        }
        $arr = explode("/",$item->ancestry_v3);
        if(in_array($item->type,array(1,4,5)))//公司 group ceo组织 和 子组织
            return $item->id;

        $return_id = 0;
        if($item->type == 2 || $item->type == 3){//公司部门 或者 组织部门

            $top_dep = static::findFirst([
                'conditions' => "id in ({ids:array}) and deleted = 0 and type = {$item->type} and level = 1",
                'bind' => ['ids' => $arr]
            ]);

            $return_id = empty($top_dep) ? 0 : $top_dep->id;
        }

        return $return_id;

    }

    /**
     * 通过当前部门id获得二级部门id
     * @param $department_id
     * @return integer
     */
    public static function getSecondDepartmentIdByDepartmentId($department_id){
        $item = static::findFirst([
            "conditions"=>'id=:id:',
            "bind"=>['id'=>$department_id]
        ]);

        if(empty($item)){
            return 0;
        }
        $arr = explode("/",$item->ancestry_v3);
        if(in_array($item->type,array(1,4,5)))//公司 group ceo组织 和 子组织
            return $item->id;

        $return_id = 0;
        if($item->type == 2 || $item->type == 3){//公司部门 或者 组织部门

            $top_dep = static::findFirst([
                'conditions' => "id in ({ids:array}) and deleted = 0 and type = {$item->type} and level = 2",
                'bind' => ['ids' => $arr]
            ]);

            $return_id = empty($top_dep) ? 0 : $top_dep->id;
        }

        return $return_id;

    }

    /**
     * 根据当前 部门 获取 所有子部门id
     * @param $department_id 顶级部门id集合
     * @return array
     */
    public static function get_sub_department($department_id)
    {
        if (empty($department_id)) {
            return [];
        }
        if (!is_array($department_id)) {
            $department_id = [$department_id];
        }
        $department_id = array_values($department_id);

        $info = static::find([
            "conditions" => 'id in ({id:array})',
            "bind"       => ['id' => $department_id],
        ])->toArray();

        if (empty($info)) {
            return false;
        }

        $return = [];
        foreach ($info as $in) {
            if ($in['ancestry_v3'] == $in['id'] && in_array($in['type'], [1, 4, 5]))//排除公司类型部门
            {
                continue;
            }
            $item = static::find([
                "conditions" => 'id != :id: and ancestry_v3 LIKE :code:',
                "bind"       => ['id' => $in['id'], 'code' => $in['ancestry_v3'].'/%'],
            ])->toArray();
            if (!empty($item)) {
                $sub_ids           = array_column($item, 'id');
                $return[$in['id']] = $sub_ids;
            }
        }
        return $return;
    }

    /**
     * 通过当前部门id获得公司id
     * @param $department_id
     * @return integer
     */
    public static function getCompanyIdByDepartmentId($department_id)
    {
        $item = static::findFirst([
            "conditions"=>'id=:id:',
            "bind"=>['id'=>$department_id]
        ]);

        return empty($item->company_id) ? 0 : $item->company_id;
    }

    /**
     * 根据部门链获取某部门的父级部门id
     * @param string $ancestry_v3
     * @return mixed
     */
    public static function getParentIdsByDepartmentId(string $ancestry_v3)
    {
        if (empty($ancestry_v3)) {
            return [];
        }

        $ancestry_v3 = array_filter(explode('/', $ancestry_v3));

        return parent::find([
            'conditions' => 'id IN ({ids:array}) AND type IN (1,2,3) AND level IN (0, 1) AND deleted = 0',
            'bind' => ['ids' => $ancestry_v3],
            'columns' => ['id', 'name', 'type', 'level', 'ancestry', 'company_id'],
        ])->toArray();
    }

    /**
     * 根据部门id 判断当前部门是否是二级及以下的子部门
     * @param $department_id
     * @return bool
     * */
    public static function isSubSecondDepartment($department_id)
    {
        $item = static::findFirst([
            "conditions" => 'id=:id: AND type IN (2,3) AND level >= 2',
            "bind"       => ['id' => $department_id]
        ]);
        if (empty($item)) {
            return false;
        }
        return true;
    }

}