<?php

namespace App\Modules\User\Models;

use App\Library\BaseModel;

class HrStaffInfoModel extends BaseModel
{
    public function initialize()
    {
        $this->setConnectionService('db_rbackyard');
        $this->setSource('hr_staff_info');
        $this->belongsTo(
            'job_title',
            HrJobTitleModel::class,
            'id',
            [
                'params' => [
                    'conditions' => 'status = 1'
                ],
                'alias' => 'JobTitle',
                'reusable' => true
            ]
        );
        $this->belongsTo(
            'sys_department_id',
            DepartmentModel::class,
            'id',
            [
                'alias' => 'Department',
                'reusable' => true
            ]
        );
    }
}