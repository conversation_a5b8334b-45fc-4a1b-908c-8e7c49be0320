<?php

namespace App\Modules\User\Models;

use App\Models\Base;

class BiStaffInfoPositionModel extends Base
{
    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_rbi');
        // setSource()方法指定数据库表
        $this->setSource('hr_staff_info_position');
    }


    public static function getRoleIds($uid){
        $arr = self::find(
            [
                'conditions'=> 'staff_info_id = :uid:',
                'bind'=> ['uid'=>$uid]
            ]
        )->toArray();
        return array_column($arr,"position_category");
    }
}