<?php

namespace App\Modules\Asset\Models;

use App\Models\Base;

class StaffAuditUnion extends Base
{

    public $id;

    public $id_union;


    public $staff_id_union;


    public $type_union;


    public $status_union;


    public $store_id;


    public $data;


    public $summary;


    public $table;


    public $created_at;


    public $origin_id;

    public $approval_id;

    /**
     * Initialize method for model.
     */
    public function initialize()
    {
        $this->setConnectionService("db_backyard");
        $this->setSource("staff_audit_union");
    }

}
