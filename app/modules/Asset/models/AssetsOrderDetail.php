<?php
namespace App\Modules\Asset\Models;

use App\Models\Base;

class AssetsOrderDetail extends Base
{

    /**
     *
     * @var integer
     */
    public $id;

    /**
     *
     * @var integer
     */
    public $order_id;

    /**
     *
     * @var integer
     */
    public $goods_id;

    /**
     *
     * @var integer
     */
    public $recomment_num;

    /**
     *
     * @var integer
     */
    public $approval_num;

    /**
     *
     * @var string
     */
    public $updated_at;

    /**
     *
     * @var string
     */
    public $created_at;


    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_backyard');
        // setSource()方法指定数据库表
        $this->setSource('assets_order_detail');
    }

}
