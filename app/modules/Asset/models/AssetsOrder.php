<?php
namespace App\Modules\Asset\Models;

use App\Models\Base;

class AssetsOrder extends Base
{

   public $id;

    public $order_union_id;

    public $organization_id;

    public $staff_info_id;

    public $approve_user;

    public $status;

    public $wf_role;

    public $reject_reason;

    public $reason;

    public $shipping_user;

    public $consignee_phone;

    public $consignee_address;

    public $province_code;

    public $city_code;

    public $district_code;

    public $postal_code;

    public $updated_at;

    public $created_at;


    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_backyard');
        // setSource()方法指定数据库表
        $this->setSource('assets_order');
    }

}
