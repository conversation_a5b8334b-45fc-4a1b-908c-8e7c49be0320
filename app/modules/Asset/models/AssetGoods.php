<?php

namespace App\Modules\Asset\Models;

use App\Models\Base;

class AssetGoods extends Base
{
    public $id;

    public $bar_code;

    public $goods_name_en;

    public $goods_name_th;

    public $goods_name_zh;

    public $type;

    public $deleted;

    public $created_at;

    public $udpated_at;

    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_backyard');
        // setSource()方法指定数据库表
        $this->setSource('assets_goods');
    }
}
