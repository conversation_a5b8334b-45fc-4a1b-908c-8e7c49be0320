<?php

namespace App\Modules\Asset\Services;

use App\Library\ApiClient;
use App\Library\Enums;
use App\Modules\Asset\Models\AssetGoods;
use App\Modules\Asset\Models\AssetsOrder;
use App\Modules\Asset\Models\AssetsOrderDetail;
use App\Modules\Asset\Models\StaffAuditUnion;

class ListService extends BaseService
{
    // 前端资产类型值
    const ASSETS_TYPE_PERSONAL = "personal_assets";
    const ASSETS_TYPE_PUBLIC = "public_assets";
    const ASSETS_TYPE_ENUMS_MAPS = [
        self::ASSETS_TYPE_PERSONAL => Enums::WF_ASSET_TYPE,
        self::ASSETS_TYPE_PUBLIC => Enums::WF_ASSET_PUBLIC_TYPE
    ];

    private static $language_fields = [
        'zh-CN' => 'goods_name_zh',
        'en' => 'goods_name_en',
        'th' => 'goods_name_th'
    ];

    public static $not_must_params = [
        'serial_no',
        'goods_id',
        'store_id',
        'created_at_start',
        'created_at_end',
    ];

    public static $validate_list_search = [
        'pageSize' => 'IntGt:0',                           //每页条数
        'pageNum' => 'IntGt:0',                            //页码
        'status' => 'IntIn:1,2,3',                         //资产申请状态
        'serial_no' => 'StrLenGeLe:1,50',                  //审批编号
        'goods_id' => 'StrLenGeLe:1,50',                   //资产名称
        'store_id' => 'StrLenGeLe:1,50',                   //网点名称
        'created_at_start' => 'DateTime',                  //资产申请开始时间
        'created_at_end' => 'DateTime',                    //资产申请结束时间
        'assets_type' => 'IntIn:1,2',                      //资产类型 1 个人 2 公共
    ];

    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return ListService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 资产列表
     * @param $locale
     * @return array
     */
    public function getAssetGoodsList($locale, $status)
    {
        $columns = 'ag.bar_code as id, ag.' . (self::$language_fields[$locale] ?? 'goods_name_zh') . ' as goods_name';
        $builder = $this->modelsManager->createBuilder();
        $builder->columns($columns);
        $builder->from(["ag" => AssetGoods::class]);
        $builder->groupBy('ag.bar_code');
        $builder->where('ag.deleted = 0');
        $builder->limit(0, 10000);
        $items = $builder->getQuery()->execute()->toArray();
        return [
            'items' => $items,
        ];
    }


    /**
     * 资产审核列表
     * @param $condition
     * @param $uid
     * @param $channe 1 菜单统计小红点使用  0 列表使用
     * @return array
     */
    public function getAuditList($condition, $uid, $channe = 0)
    {
        $page_num = empty($condition['pageNum']) ? 1 : $condition['pageNum'];
        $page_size = empty($condition['pageSize']) ? 20 : $condition['pageSize'];
        $page_size = ($page_size > 100) ? 100 : $page_size;
        $ac = new ApiClient('by', '', 'get_audit_list', static::$language);
        if (isset($condition['source_type']) && !empty($condition['source_type'])) {
            $type = self::ASSETS_TYPE_ENUMS_MAPS[$condition['source_type']];
        } else {
            $type = '16_19';
        }
        $ac->setParams(
            [
                [
                    'type' => $type,
                    'page_num' => $page_num,
                    'page_size' => $page_size,
                    'source' => $channe,
                    'start_date' => $condition['created_at_start'] ?? '',
                    'end_date' => $condition['created_at_end'] ?? '',
                    'status' => $condition['status'] ?? 1,
                    'serial_no' => $condition['serial_no'] ?? '',
                    'goods_id' => $condition['goods_id'] ?? '',
                    'store_id' => $condition['store_id'] ?? '',
                    'staff_approval_id' => $uid,
                ]
            ]
        );
        $res = $ac->execute();
        $items = empty($res['result']['dataList']) ? [] : $res['result']['dataList'];
        $items = $this->handleItems($items);
        $totalCount = $res['result']['pagination']['count'] ?? 0;
        return [
            'items' => $items,
            'pagination' => [
                'current_page' => $page_num,
                'per_page' => $page_size,
                'total_count' => intval($totalCount),
            ]
        ];
    }


    /**
     * 资产审核列表下载
     * @param $condition
     * @param $uid
     * @return array
     */
    public function download($condition, $uid)
    {
        set_time_limit(0);

        $params['created_at_start'] = $condition['created_at_start'] ?? '';
        $params['created_at_end'] = $condition['created_at_end'] ?? '';
        $params['status'] = $condition['status'] ?? '';
        $params['serial_no'] = $condition['serial_no'] ?? '';
        $params['goods_id'] = $condition['goods_id'] ?? '';
        $params['store_id'] = $condition['store_id'] ?? '';
        $params['source_type'] = $condition['source_type'] ?? '';
        $params['pageNum'] = 1;
        $params['pageSize'] = 10000;
        $res = $this->getAuditList($params, $uid);
        $data = $res['items'] ?? [];
        $new_data = [];
        $i = 0;
        foreach ($data as $key => $val) {
            ++$i;
            $new_data[$key][0] = $i;
            $new_data[$key][1] = $val['serial_no'];
            $new_data[$key][2] = $val['created_date'];
            $new_data[$key][3] = $val['status_title'];
            $new_data[$key][4] = $val['staff_name'];
            $new_data[$key][5] = $val['reason'];
            $new_data[$key][6] = $val['store_name'];
        }
        $file_name = static::$t->_('asset_collection_approval_form') . date('YmdHis');
        $header = [
            static::$t->_('global.no'),
            static::$t->_('global.approval_no'),
            static::$t->_('global.approval_update'),
            static::$t->_('global.approval_status'),
            static::$t->_('global.applicant'),
            static::$t->_('global.equipment_reason'),
            static::$t->_('global.store_name')
        ];
        return $this->exportExcel($header, $new_data, $file_name);
    }

    /**
     * @param $items
     * @return array
     */
    private function handleItems($items)
    {
        if (empty($items) || !is_array($items)) {
            return [];
        }
        foreach ($items as &$item) {
            $status = isset($item['status']) ? Enums::$asset_audit_status[$item['status']] ?? '' : '';
            $item['status_title'] = static::$t->_($status);
            $item['store_name'] = $this->getStoreName($item['store_name']);
        }
        return $items;
    }
}
