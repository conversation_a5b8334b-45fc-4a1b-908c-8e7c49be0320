<?php

namespace App\Modules\Asset\Services;

use App\Library\ApiClient;
use App\Library\Enums;
use App\Library\Enums\ContractEnums;
use App\Library\ErrCode;
use App\Modules\Asset\Models\AssetsOrder;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;

class DetailService extends BaseService
{
    public static $validate_detail = [
        'id' => 'Required|IntGe:1',                                  //ID
    ];

    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return DetailService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * @param $params
     * @param $uid
     * @return array
     */
    public function getAuditDetail($params, $uid)
    {

        $id = $params['id'] ?? 0;
        $wfRole = $params['wf_role'] ?? "as_";
        //首先查询一下数据,获取一下相关信息
        $assets_order_info  = AssetsOrder::findFirst([
                                                         'id = :id:',
                                                         'bind' => ['id' =>$id],
                                                         "columns" => "id,created_at",
                                                     ]);
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $ac = new ApiClient('by', '', 'get_audit_detail', static::$language);
        $ac->setParams(
            [
                [
                    "type" => strpos($wfRole, "asp_") !== false ? Enums::WF_ASSET_PUBLIC_TYPE : Enums::WF_ASSET_TYPE,
                    "id" => strpos($wfRole, "asp_") !== false ? "asp_" . $id : "as_" . $id,
                    "staff_id" => $uid,
                    "date_created"=>$assets_order_info->created_at ?? "",//申请时间
                ]
            ]
        );
        $res = $ac->execute();

        $data =  $res['result']['data'] ?? [];
        if(!empty($data)){
            $data['head']['store_name'] = $this->getStoreName($data['head']['store_name']);
        }
        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * @param $data
     * @return array
     */
    private function handleData($data)
    {
        if (empty($data) || !is_array($data)) {
            return [];
        }
        $status = ContractEnums::$contract_archive_status[$data['status']] ?? '';
        $data['status_title'] = static::$t->_($status);
//        $data['attachment'] = empty($data['attachment']) ? '' : explode(',', $data['attachment']);
//        $data['attachment'] = $this->getRemotePath($data['attachment']);
        return $data;
    }
}
