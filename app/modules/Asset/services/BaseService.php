<?php

namespace App\Modules\Asset\Services;

use App\Library\Enums;
use App\Library\RedisClient;
use App\Modules\Contract\Models\Contract;
use App\Util\RedisExpire;
use App\Util\RedisKey;

class BaseService extends \App\Library\BaseService
{
    public static function handleParams($params, $not_must)
    {
        if (empty($not_must) || !is_array($not_must)) {
            return $params;
        }
        foreach ($not_must as $value) {
            if (isset($params[$value]) && empty($params[$value])) {
                unset($params[$value]);
            }
        }
        return $params;
    }

    /**
     * @param $type
     * @param bool $is_update
     * @return array
     */
    public static function getValidateParams($type, $is_update = false)
    {
        $rules = [];
        $type = $type ? : 0;
        if (isset(self::$validate_other[$type])) {
            $rules = self::$validate_other[$type];
        }
        if ($is_update === true) {
            $rules = array_merge($rules, self::$validate_update);
        }

        return array_merge(self::$validate_currency, $rules);
    }

    /**
     * 如果网点名字为空，则赋值Header Office
     * @param $storeName
     * @return string
     */
    public function getStoreName($storeName){
        if(empty($storeName)){
            return "Head Office";
        }
        return $storeName;
    }

}
