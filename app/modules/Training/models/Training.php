<?php

namespace  App\Modules\Training\Models;

use App\Models\Base;

class Training extends Base
{

    public function initialize()
    {
        $this->setConnectionService('db_backyard');
        // setSource()方法指定数据库表
        $this->setSource('training');
    }


    public function refresh()
    {
        if ($this->getWriteConnection()->isUnderTransaction()) {
            $this->setReadConnectionService('db_backyard');
        }
        return parent::refresh();
    }

}