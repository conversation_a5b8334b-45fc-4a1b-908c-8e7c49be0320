<?php


namespace App\Modules\Workflow\Models;


use App\Library\BaseModel;

class WorkflowModel extends BaseModel
{
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        $this->setSource('workflow');
    }

    /**
     * 主键ID
     * @var int
     */
    private $id;

    /**
     * 审批流名称
     * @var string
     */
    private $name;

    /**
     * 关联审批类型
     * @var int
     */
    private $biz_type;

    /**
     * 审批流描述
     * @var string
     */
    private $description;

    /**
     * 创建时间
     * @var
     */
    private $created_at;
    /**
     * @var
     */
    private $updated_at;

    /**
     * 是否启用 1-已启用 2-已停用
     * @var int
     */
    private $state;

    /**
     * 是否删除 0-未删除 1-已删除
     * @var int
     */
    private $deleted;

    /**
     * 保存前端的审批流请求数据
     * @var string
     */
    private $workflow_request;

    /**
     * 审批流版本
     * @var string
     */
    private $version;

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @param mixed $id
     */
    public function setId($id): void
    {
        $this->id = $id;
    }

    /**
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * @param mixed $name
     */
    public function setName($name): void
    {
        $this->name = $name;
    }

    /**
     * @return int
     */
    public function getBizType(): int
    {
        return $this->biz_type;
    }

    /**
     * @param mixed $biz_type
     */
    public function setBizType($biz_type): void
    {
        $this->biz_type = $biz_type;
    }

    /**
     * @return string
     */
    public function getDescription(): string
    {
        return $this->description;
    }

    /**
     * @param mixed $description
     */
    public function setDescription($description): void
    {
        $this->description = $description;
    }

    /**
     * @return mixed
     */
    public function getCreatedAt()
    {
        return $this->created_at;
    }

    /**
     * @param mixed $created_at
     */
    public function setCreatedAt($created_at): void
    {
        $this->created_at = $created_at;
    }

    /**
     * @return mixed
     */
    public function getUpdatedAt()
    {
        return $this->updated_at;
    }

    /**
     * @param mixed $updated_at
     */
    public function setUpdatedAt($updated_at): void
    {
        $this->updated_at = $updated_at;
    }

    /**
     * @return int
     */
    public function getState(): int
    {
        return $this->state;
    }

    /**
     * @param mixed $state
     */
    public function setState($state): void
    {
        $this->state = $state;
    }

    /**
     * @return int
     */
    public function getDeleted(): int
    {
        return $this->deleted;
    }

    /**
     * @param mixed $deleted
     */
    public function setDeleted($deleted): void
    {
        $this->deleted = $deleted;
    }

    /**
     * @return string
     */
    public function getWorkflowRequest(): string
    {
        return $this->workflow_request;
    }

    /**
     * @param string $workflow_request
     */
    public function setWorkflowRequest(string $workflow_request): void
    {
        $this->workflow_request = $workflow_request;
    }

    /**
     * @return string
     */
    public function getVersion(): string
    {
        return $this->version;
    }

    /**
     * @param string $version
     */
    public function setVersion(string $version): void
    {
        $this->version = $version;
    }
}