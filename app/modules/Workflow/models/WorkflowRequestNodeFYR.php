<?php


namespace App\Modules\Workflow\Models;


use App\Library\BaseModel;
use App\Library\Enums;
use App\Modules\User\Models\AttachModel;

class WorkflowRequestNodeFYR extends BaseModel
{
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        $this->setSource('workflow_request_node_fyr');

        $this->hasMany(
            'id',
            AttachModel::class,
            'oss_bucket_key', [
                'params' => [
                    'conditions' => "oss_bucket_type = ".Enums::OSS_BUCKET_TYPE_FRY_ATTACH." and deleted=0"
                ],
                "alias" => "Attachments",
            ]
        );

    }
}