<?php

namespace App\Modules\Workflow\Models;
use App\Library\BaseModel;

class WorkflowSubNodeModel extends BaseModel
{
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        $this->setSource('workflow_sub_node');
    }

    /**
     * @var int
     */
    private $id;

    /**
     * 工作流ID
     * @var int
     */
    private $flow_id;

    /**
     * 父节点名称
     * @var int
     */
    private $parent_node_id;

    /**
     * 子节点名称
     * @var string
     */
    private $name;

    /**
     * 节点审批类型 0-或签（默认）；1-审批人会签；2-子节点会签
     * @var int
     */
    private $node_audit_type;

    /**
     * 审核逻辑关系: 0-通用的自动跳过，自动同意逻辑；3-当审批人含提交人时，直接自动同意
     * @var int
     */
    private $audit_type;

    /**
     * 审核人类型
     * 1 员工ID,2 角色，3直接上级，4提交人所在部门的经理，5二级部门负责人，6一级部门负责人，
     * 7COO||CPO节点，8网点主管，9-12获取网点不同级别的人（不用了），13网点主管根据职位
     * @var int
     */
    private $auditor_type;

    /**
     * 审核人/角色ID
     * @var string
     */
    private $auditor_id;

    /**
     * 可以修改字段的值
     * @var string
     */
    private $can_edit_field;

    /**
     * 创建时间
     * @var string
     */
    private $created_at;

    /**
     * 节点标签定义: 0-未定义; 3-PM节点; 4-APS北京; 5-AP泰国
     * @var int
     */
    private $node_tag;

    /**
     * @return mixed
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @return mixed
     */
    public function getFlowId()
    {
        return $this->flow_id;
    }

    /**
     * @param mixed $flow_id
     */
    public function setFlowId($flow_id): void
    {
        $this->flow_id = $flow_id;
    }

    /**
     * @return mixed
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * @param mixed $name
     */
    public function setName($name): void
    {
        $this->name = $name;
    }

    /**
     * @return mixed
     */
    public function getParentNodeId()
    {
        return $this->parent_node_id;
    }

    /**
     * @param mixed $parent_node_id
     */
    public function setParentNodeId($parent_node_id): void
    {
        $this->parent_node_id = $parent_node_id;
    }

    /**
     * @return mixed
     */
    public function getNodeAuditType()
    {
        return $this->node_audit_type;
    }

    /**
     * @param mixed $node_audit_type
     */
    public function setNodeAuditType($node_audit_type): void
    {
        $this->node_audit_type = $node_audit_type;
    }

    /**
     * @return mixed
     */
    public function getAuditType()
    {
        return $this->audit_type;
    }

    /**
     * @param mixed $audit_type
     */
    public function setAuditType($audit_type): void
    {
        $this->audit_type = $audit_type;
    }

    /**
     * @return int
     */
    public function getAuditorType(): int
    {
        return $this->auditor_type;
    }

    /**
     * @param int $auditor_type
     */
    public function setAuditorType(int $auditor_type): void
    {
        $this->auditor_type = $auditor_type;
    }

    /**
     * @return mixed
     */
    public function getAuditorId()
    {
        return $this->auditor_id;
    }

    /**
     * @param mixed $auditor_id
     */
    public function setAuditorId($auditor_id): void
    {
        $this->auditor_id = $auditor_id;
    }

    /**
     * @return mixed
     */
    public function getCanEditField()
    {
        return $this->can_edit_field;
    }

    /**
     * @param mixed $can_edit_field
     */
    public function setCanEditField($can_edit_field): void
    {
        $this->can_edit_field = $can_edit_field;
    }

    /**
     * @return mixed
     */
    public function getCreatedAt()
    {
        return $this->created_at;
    }

    /**
     * @param mixed $created_at
     */
    public function setCreatedAt($created_at): void
    {
        $this->created_at = $created_at;
    }

    /**
     * @return mixed
     */
    public function getNodeTag()
    {
        return $this->node_tag;
    }

    /**
     * @param mixed $node_tag
     */
    public function setNodeTag($node_tag): void
    {
        $this->node_tag = $node_tag;
    }
}
