<?php


namespace App\Modules\Workflow\Services;


use App\Library\BaseService;
use App\Library\Enums;
use App\Library\Exception\BusinessException;
use App\Modules\User\Models\StaffInfoModel;
use App\Modules\Workflow\Models\WorkflowAuditLogModel;
use App\Modules\Workflow\Models\WorkflowModel;
use App\Modules\Workflow\Models\WorkflowNodeModel;
use App\Modules\Workflow\Models\WorkflowRequestModel;


class WorkflowService extends BaseService
{

    /**
     * 创建审批申请
     * @param $data
     * @param $user
     * @return \Phalcon\Mvc\Model\Resultset|\Phalcon\Mvc\Phalcon\Mvc\Model
     * @throws BusinessException
     */
    public function createRequest($data, $user)
    {
        //创建审批申请
        $flowId = $data['flow_id'];
        $startNode = $this->getFlowNodeByType($flowId, Enums::WF_NODE_APPLY);
        $request = new WorkflowRequestModel();
        $request->name = $data['name'];
        $request->biz_type = $data['biz_type'];
        $request->biz_value = $data['id'];
        $request->flow_id = $flowId;
        $request->current_flow_node_id = $startNode->id;
        $request->state = Enums::WF_STATE_PENDING;
        $request->created_at = date('Y-m-d H:i:s');
        $request->create_staff_id = $user['id'];
        $request->viewer_ids = $user['id'];
        $request->save();

        //启动工作流
        $this->process($request, $user , Enums::WF_ACTION_APPROVE, '');

        return $request->id;
    }


    /**
     * 审批通过操作
     * @param $request
     * @param $user
     * @param string $note
     * @return bool|string
     * @throws BusinessException
     */
    public function doApprove($request, $user, $note='')
    {
        return $this->process($request, $user, Enums::WF_ACTION_APPROVE, $note);
    }

    /**
     * 审批驳回操作
     * @param $request
     * @param $user
     * @param $note
     * @return bool|string
     * @throws BusinessException
     */
    public function doReject($request, $user, $note)
    {
        return $this->process($request, $user, Enums::WF_ACTION_REJECT, $note);
    }


    /**
     * 工作流处理
     * @param WorkflowRequestModel $request
     * @param $user
     * @param $action
     * @param $info
     * @return bool|WorkflowRequestModel
     * @throws BusinessException
     */
    protected function process($request, $user, $action, $info)
    {
        $flowId=$request->flow_id;
        $flowNodes = $this->getFlowNodes($flowId);
        $currentNode = $this->getCurrentNode($flowNodes, $request->current_flow_node_id);
        $nodeAuditors = empty($request->current_node_auditor_id) ? []:explode(',', $request->current_node_auditor_id);
        if (!empty($nodeAuditors)){
            if (!in_array($user['id'], $nodeAuditors)){
                throw new BusinessException('Auditor exception');
            }
        }
        switch ($action){
            case Enums::WF_ACTION_APPROVE:
                $next = 'approve_next_node_id';
                break;
            case Enums::WF_ACTION_REJECT:
                $next = 'reject_next_node_id';
                break;
        }
        if (in_array($currentNode->type, [Enums::WF_NODE_APPLY,Enums::WF_NODE_ACTION])){
            //审核日志记录
            $this->saveAuditLog($request, $user, $action, $info);
        }

        $nextNode = $this->getCurrentNode($flowNodes, $currentNode->$next);
        $request->current_flow_node_id = $nextNode->id;
        $request->updated_at = date('Y-m-d H:i:s');
        switch ($nextNode->type){
            case Enums::WF_NODE_APPLY:
                $this->process($request, $user, Enums::WF_ACTION_APPROVE, '');
                break;
            case Enums::WF_NODE_COND:
                $result = $this->parseCond($request,$nextNode->expression);
                $this->process($request, $user, $result, '');
                break;
            case Enums::WF_NODE_ACTION:
                $nodeAuditors = $this->getNodeAuditors($nextNode);
                $request->current_node_auditor_id = implode($nodeAuditors);

                //发送邮件
                $this->sendEmailToAuditors($request,$nodeAuditors);

                $request->viewer_ids = $request->viewer_ids . ',' . implode($nodeAuditors);
                break;
            case Enums::WF_NODE_APPROVED:
                $request->state = Enums::WF_STATE_APPROVED;
                $request->current_node_auditor_id = '';
                $request->approved_at = date('Y-m-d H:i:s');
                break;
            case Enums::WF_NODE_REJECTED:
                $request->state = Enums::WF_STATE_REJECTED;
                $request->current_node_auditor_id = '';
                $request->rejected_at = date('Y-m-d H:i:s');
                $request->rejeact_info = $info;
                break;
        }
        $request->save();

        return $request;
    }

    /**
     * @param $request
     * @return \Phalcon\Mvc\Model
     */
    public function getCurrentAuditor($request)
    {
        return StaffInfoModel::findFirst(
            [
                'conditions' => 'id = :uid:',
                'bind' => [
                    'uid' => $request->current_node_auditor_id,
                ],
            ]
        );
    }

    /**
     * 保存申请的审批日志
     * @param WorkflowRequestModel $request
     * @param $user
     * @param $action
     * @param $info
     */
    public function saveAuditLog($request, $user, $action, $info)
    {
        $log = new WorkflowAuditLogModel();
        $log->save(
            [
                'request_id' => $request->id,
                'flow_id' => $request->flow_id,
                'flow_node_id' => $request->current_flow_node_id,
                'staff_id' => $user['id'],
                'staff_name' => $user['name'],
                'staff_department' => $user['department'],
                'audit_action' => $action,
                'audit_info' => $info,
                'audit_at' =>date('Y-m-d H:i:s'),
            ]
        );
    }

    /**
     * 获取申请的审批日志
     * @param $request
     * @return array
     */
    public function getAuditLogs($request)
    {
        $data = [];
        $logs = WorkflowAuditLogModel::find(
            [
                'conditions' => 'request_id= :req_id:',
                'bind' => [
                    'req_id' => $request->id,
                ],
                'order' => 'audit_at desc',
            ]
        );
        $fyrs = new FYRService();
        if ($logs){
            $logs = $logs->toArray();
            $last = current($logs);
            $lastTime = strtotime($last['audit_at']);
            $start  = array_pop($logs);
            foreach ($logs as $log){
                $data[] = [
                    'staff_id' => $log['staff_id'],
                    'staff_name'=> $log['staff_name'],
                    'staff_department' => $log['staff_department'],
                    'action_name' => self::$t->_(Enums::$actions[$log['audit_action']]),
                    'audit_at' => $log['audit_at'],
                    'action' => (int)$log['audit_action'],
                    'info' => $log['audit_info'],
                    'fyr_list' => $fyrs->getLogs($log['request_id'],$log['flow_id'],$log['flow_node_id']),
                ];
            }
            //申请节点处理
            $data[] = [
                'staff_id' => $start['staff_id'],
                'staff_name'=> $start['staff_name'],
                'staff_department' => $start['staff_department'],
                'action_name' => self::$t->_('flow_audit_action.0'),
                'audit_at' => $start['audit_at'],
                'action' => 0,
                'info' =>'',
                'fyr_list' => [],
            ];
            $current = $this->getCurrentAuditor($request);
            if ($current){
                $current = [
                    'staff_id' => $current->id,
                    'staff_name'=> $current->name,
                    'staff_department' => $current->getDepartment()->name,
                    'action_name' => self::$t->_('contract_status.1'),
                    'audit_at' => format_duration($lastTime, time()),
                    'action' => 3,
                    'info' => '',
                    'fyr_list' => $fyrs->getLogs($request->id,$request->flow_id,$request->current_flow_node_id),
                ];
                array_unshift($data, $current);
            }
        }

        return $data;
    }

    /**
     * 获取节点审核人ID
     * @param $node
     * @return array
     */
    public function getNodeAuditors($node)
    {
        if (!empty($node->auditor_id )){
            switch ($node->auditor_type){
                case 1:
                    $auditors = empty($node->auditor_id) ? []:explode(',', $node->auditor_id);
                    break;
                case 2:
                    $auditors = [];//根据角色获取审核人ID
                    break;
                default:
                    $auditors = [];
                    break;
            }
        }else{
            $auditors = [];
        }

        return $auditors;
    }

    /** 获取当前审核节点
     * @param $nodes
     * @param $currentNodeId
     * @return array
     */
    public function getCurrentNode($nodes, $currentNodeId)
    {
        foreach ($nodes as $k=>$v){
            if ($v->id == $currentNodeId)
                return $v;
        }
    }


    /**
     * 获取审批流
     * @param $flowId
     * @return \Phalcon\Mvc\Model
     */
    public function getFlow($flowId)
    {
        return WorkflowModel::findFirst($flowId);
    }

    /**
     * 获取全部审批流
     * @return \Phalcon\Mvc\Model\ResultsetInterface
     */
    public function getAllFlow()
    {
        return WorkflowModel::find();
    }

    /**
     * 获取审批流全部节点
     * @param $flowId
     * @return \Phalcon\Mvc\Model\ResultsetInterface
     */
    public function getFlowNodes($flowId)
    {
        return WorkflowNodeModel::find(
            [
                'conditions' => 'flow_id = ?1',
                'bind' => [
                    1 => $flowId,
                ]
            ]
        );
    }

    /**
     * @param $flowId
     * @param $type
     * @return array|mixed
     */
    public function getFlowNodeByType($flowId, $type)
    {
        $nodes = $this->getFlowNodes($flowId);
        foreach ($nodes as $k=>$v){
            if ($v->type == $type){
                return $v;
            }
        }
    }


    /**
     * 解析条件判断节点
     * @param WorkflowRequestModel $request
     * @param $expression
     * @return int
     * @throws BusinessException
     */
    public function parseCond($request, $expression)
    {
        $expression = explode(',',$expression);

        list($class, $method) = $expression;
        if (!class_exists($class)){
            throw new BusinessException('Handler Class not exists');
        }
        $class = new $class;
        if (!method_exists($class, $method)){
            throw new BusinessException('Handler Method not exists');
        }

        $result = $class->$method($request);

        return $result ? Enums::WF_ACTION_APPROVE:Enums::WF_ACTION_REJECT;

    }
}