<?php

namespace App\Modules\Workflow\Services;

use App\Library\BaseService;
use App\Library\Enums;
use App\Library\Exception\BusinessException;
use App\Library\RocketMQ;
use App\Models\oa\WorkflowRequestNodePushMsg;
use App\Modules\Common\Services\EnumsService;
use App\Modules\User\Services\UserService;
use App\Util\RedisKey;

class WorkflowEventService extends BaseService
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    /**
     * 获取 WorkflowEventService 实例
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    static $pass = 1; //审批终审通过
    static $reject = 2; //审批驳回
    const TYPE_AUDIT = 1;//审批
    const TYPE_FYR = 2;//征询
    const AUDIT_ACTION_ASK = 5;//发起征询
    const AUDIT_ACTION_REPLY = 6;//回复征询
    //不允许push的业务
    public static $not_biz_type = [
        Enums::WF_PAY_TYPE,
    ];

    public function handleEvent($event, $params)
    {
        switch ($event) {
            case self::$pass:
                $this->handlePass($params);
                break;
            case self::$reject:
                $this->handleReject($params);
                break;
        }
    }

    public function handlePass($params)
    {
        //发邮件
        (new WorkflowMailService())->sendEmail($params['request'], Enums::SEND_MAIL_TYPE_PASS);
    }

    public function handleReject($params)
    {
        //给申请人发邮件
        (new WorkflowMailService())->sendEmail($params['request'], Enums::SEND_MAIL_TYPE_REJECT);
        //给申请人发短信
        WorkflowSmsService::getInstance()->sendSms($params['request'], Enums::SEND_MAIL_TYPE_REJECT);
    }

    /**
     * 审批/征询相关动作-push记录 && 推送MQ
     *
     * @param object $request workflow_request对象信息
     * @param int $audit_action 当前状态，1待审批，2驳回 ，3通过，4撤回，5发起征询，6回复征询
     * @param int $type 来源：1审批，2征询
     * @return bool
     * @throws BusinessException
     */
    public function pushEvent($request, $audit_action, $type = self::TYPE_AUDIT)
    {
        if (isset($request->current_node_auditor_id) && !in_array($request->biz_type, self::$not_biz_type)) {
            $current_node_auditor_ids = array_unique(array_filter(explode(',', $request->current_node_auditor_id)));

            // 将待办人实时维护到预热缓存
            foreach ($current_node_auditor_ids as $auditor_id) {
                UserService::getInstance()->appendReddotPrehotCache($auditor_id, RedisKey::OA_REDDOT_PREHOT_CACHE_SCENCE_AUDIT_PENDING);
            }

            // push 开关
            $oa_redhot_push_switch = EnumsService::getInstance()->getSettingEnvValue('oa_redhot_push_switch', 0);
            if ($oa_redhot_push_switch != 1) {
                return true;
            }

            $push_msg_data = [];
            $rmq = new RocketMQ('message_push');
            foreach ($current_node_auditor_ids as $auditor_id) {
                $push_msg_data[] = [
                    'request_id' => $request->id,
                    'no' => preg_replace('/[^0-9a-zA-Z]/', '', $request->name),
                    'biz_type' => $request->biz_type,
                    'biz_value' => $request->biz_value,
                    'flow_id' => $request->flow_id,
                    'node_id' => $request->current_flow_node_id,
                    'auditor_id' => $auditor_id,
                    'audit_action' => $audit_action,
                    'type' => $type,
                    'status' => 0,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                $params = [
                    'request_id' => $request->id,
                    'auditor_id' => $auditor_id,
                    'audit_action' => $audit_action,
                    'push_at' => date('Y-m-d H:i:s')
                ];
                $rid = $rmq->sendToMsg($params, env('rmq_delayed_time_push', 0));
                $this->logger->info(sprintf('WorkflowEventService pushEvent:%s, params:%s', $rid, json_encode($params)));
            }

            if (!empty($push_msg_data) && (new WorkflowRequestNodePushMsg())->batch_insert($push_msg_data) === false) {
                throw new BusinessException('审批/征询/回复相关动作-push数据入库失败, data=' . json_encode($push_msg_data, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }
        }

        return true;
    }

}