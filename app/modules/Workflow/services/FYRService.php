<?php

namespace App\Modules\Workflow\Services;

use App\Library\BaseService;
use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Models\oa\ChequeApplyModel;
use App\Models\oa\ContractPlatFormModel;
use App\Models\oa\WorkflowRequestNodeFyrMiddleModel;
use App\Modules\Budget\Models\BudgetAdjustModel;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Contract\Models\Contract;
use App\Modules\Contract\Models\ContractStoreRentingModel;
use App\Modules\Deposit\Models\DepositReturnModel;
use App\Modules\Deposit\Models\DepositModel;
use App\Modules\Loan\Models\Loan;
use App\Modules\Pay\Models\Payment;
use App\Modules\Payment\Models\PaymentStoreRenting;
use App\Modules\Purchase\Models\PurchaseAcceptanceModel;
use App\Modules\Purchase\Models\PurchaseApply;
use App\Modules\Purchase\Models\PurchaseOrder;
use App\Modules\Purchase\Models\PurchasePayment;
use App\Modules\Purchase\Models\PurchaseSampleModel;
use App\Modules\Purchase\Models\Vendor;
use App\Modules\Reimbursement\Models\Reimbursement;
use App\Modules\ReserveFund\Models\ReserveFundApply;
use App\Modules\ReserveFund\Models\ReserveFundReturn;
use App\Modules\User\Models\AttachModel;
use App\Modules\User\Models\StaffInfoModel;
use App\Modules\Workflow\Models\WorkflowNodeModel;
use App\Modules\Workflow\Models\WorkflowRequestModel;
use App\Modules\Workflow\Models\WorkflowRequestNodeAt;
use App\Modules\Workflow\Models\WorkflowRequestNodeFYR;
use App\Modules\OrdinaryPayment\Models\OrdinaryPayment;
use Phalcon\Mvc\Model;
use App\Repository\oa\SysAttachmentRepository;

class FYRService extends BaseService
{
    // 征询模块, 相关附件最大允许上传数量
    private const ATTACHMENT_MAX_UPLOAD_COUNT = 10;

    // 征询校验规则
    public static $ask_common_validation = [
        'id' => 'Required|IntGe:1',
        'note' => 'Required|StrLenGeLe:1,10000',
        'to_staff' => 'Required|Arr|ArrLenGeLe:1,20',
    ];

    // 回复校验规则
    private static $reply_common_validation = [
        'id' => 'Required|IntGe:1',
        'note' => 'Required|StrLenGeLe:1,10000',
    ];

    private static $instance;

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 征询提交参数校验
     *
     * @param array $params
     * @throws ValidationException
     */
    public function askValidation(array $params)
    {
        // 部分模块的子业务扩展参数校验
        if (isset($params['biz_type'])) {
            self::$ask_common_validation['biz_type'] = 'Required|IntGe:1';
        }

        // 有附件的校验: 最多不超过 10 个
        if (!empty($params['attachments'])) {
            self::$ask_common_validation['attachments'] = 'Required|Arr|ArrLenLe:' . self::ATTACHMENT_MAX_UPLOAD_COUNT . '|>>>:' . static::$t->_('consultation_reply_submit_msg_4', ['max_num' => self::ATTACHMENT_MAX_UPLOAD_COUNT]);
        }

        Validation::validate($params, self::$ask_common_validation);
    }

    /**
     * 回复提交参数校验
     *
     * @param array $params
     * @throws ValidationException
     */
    public function replyValidation(array $params)
    {
        // 有附件的校验: 最多不超过 10 个
        if (!empty($params['attachments'])) {
            self::$reply_common_validation['attachments'] = 'Required|Arr|ArrLenLe:' . self::ATTACHMENT_MAX_UPLOAD_COUNT . '|>>>:' . static::$t->_('consultation_reply_submit_msg_4', ['max_num' => self::ATTACHMENT_MAX_UPLOAD_COUNT]);
        }

        Validation::validate($params, self::$reply_common_validation);
    }

    /**
     * @param $request
     * @param $uid
     * @return Model
     */
    public function getRequestToByReplyAsk($request, $uid)
    {
        if (empty($request)) {
            return null;
        }

        return WorkflowRequestNodeAt::findFirst([
            'request_id=:request_id: and is_reply=0 and staff_id=:uid:',
            'bind' => [
                'request_id' => $request->id ?? 0,
                'uid' => $uid,
            ],
            'order' => 'id asc'
        ]);
    }

    /**
     * @param $requestId
     * @param $flowId
     * @param $nodeId
     * @return array
     */
    public function getLogs($requestId, $flowId, $nodeId)
    {
        $list = WorkflowRequestNodeFYR::find([
            'conditions' => 'request_id = :request_id: and flow_id = :flow_id: and flow_node_id = :node_id:',
            'bind' => ['request_id' => $requestId, 'flow_id' => $flowId, 'node_id' => $nodeId],
            'order' => 'id desc'
        ]);

        $logs = [];
        foreach ($list as $item) {
            $log = $item->toArray();
            if (!empty($log['extra'])) {
                $log['extra'] = array_values(json_decode($log['extra'], true));
            }

            $log['attachments'] = $item->getAttachments()->toArray();
            $logs[] = $log;
        }

        return $logs;
    }

    /**
     * 创建意见征询
     *
     * @param $request
     * @param $note
     * @param $informers
     * @param $user
     * @param $attachments
     * @return mixed
     */
    public function createAsk($request, $note, $informers, $user, $attachments = [])
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';

        $db = $this->getDI()->get('db_oa');
        $db->begin();

        try {
            // 征询人 不是 审批流当前待审批节点的审批人: 不可发起征询
            if (!in_array($user['id'], array_filter(explode(',', $request->current_node_auditor_id)))) {
                throw new ValidationException(static::$t->_('counselor_is_not_current_pending_node_approver', ['request_id' => $request->id, 'node_id' => $request->current_flow_node_id]), ErrCode::$VALIDATE_ERROR);
            }

            // [1] 获取征询人所在节点信息(征询人所在一级节点审批类型/所在子节点ID/直属节点审批人/干系审批人)
            $current_node_info = $this->getConsultantNodeAuditorInfo($request, $user['id']);

            // [1] 维护征询中间表数据
            // 维护征询中间表征询相关的审批人/回复相关的审批人字段(审批待处理/征询中/征询已回复列表取数用到)
            $this->saveFyrMiddleDataByFyr($request, $current_node_info, $user);

            // [2] 添加征询记录
            // [2.1] 征询写入
            $fyr = new WorkflowRequestNodeFYR();
            $fyr->request_id = $request->id;
            $fyr->flow_id = $request->flow_id;
            $fyr->flow_node_id = $request->current_flow_node_id;
            $fyr->flow_sub_node_id = $current_node_info['sub_node_id'];
            $fyr->staff_id = $user['id'];
            $fyr->staff_name = $this->getNameAndNickName($user['name'], $user['nick_name']);
            $fyr->staff_department = $user['department'];
            $fyr->staff_job_title = $user['job_title'];
            $fyr->action_type = GlobalEnums::CONSULTED_ACTION_TYPE;
            $fyr->info = $note;
            $fyr->extra = json_encode($informers, JSON_UNESCAPED_UNICODE);
            $fyr->created_at = date('Y-m-d H:i:s');
            $fyr->staff_state = $this->getStaffJobState($user);
            $fyr->node_related_auditor_ids = $current_node_info['node_related_auditor_ids'];
            $fyr->node_auditor_ids = $current_node_info['node_auditor_ids'];
            $fyr->node_node_audit_type = $current_node_info['node_audit_type'];
            if ($fyr->save() === false) {
                throw new BusinessException('意见征询: 新征询记录添加失败, 数据: ' . json_encode($fyr->toArray(), JSON_UNESCAPED_UNICODE) . '; 原因可能是: ' . get_data_object_error_msg($fyr), ErrCode::$BUSINESS_ERROR);
            }

            // [2.2] 征询附件写入
            if (!empty($attachments)) {
                $am = new AttachModel();
                $attach_arr = [];
                foreach ($attachments as $attachment) {
                    $attach_arr[] = [
                        'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_FRY_ATTACH,
                        'oss_bucket_key' => $fyr->id,
                        'bucket_name' => $attachment['bucket_name'],
                        'object_key' => $attachment['object_key'],
                        'file_name' => $attachment['file_name']
                    ];
                }

                if ($am->batchInsert($attach_arr) === false) {
                    throw new BusinessException('意见征询: 征询附件批量添加失败, 数据: ' . json_encode($attach_arr, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                }
            }

            // [3] 被征询记录写入(待回复)
            foreach ($informers as $informer) {
                $at = new WorkflowRequestNodeAt();
                $at->fyr_id = $fyr->id;
                $at->request_id = $request->id;
                $at->flow_id = $request->flow_id;
                $at->flow_node_id = $request->current_flow_node_id;
                $at->staff_id = $informer['id'];
                $at->staff_name = $informer['name'];
                $at->staff_department = $informer['department'];
                $at->staff_job_title = $informer['job_title'];
                $at->is_reply = GlobalEnums::CONSULTED_REPLY_STATE_PENDING;
                $at->create_staff_id = $user['id'];
                $at->created_at = date('Y-m-d H:i:s');
                if ($at->save() === false) {
                    throw new BusinessException('意见征询: 被征询记录添加失败, 数据: ' . json_encode($at->toArray(), JSON_UNESCAPED_UNICODE) . '; 原因可能是: ' . get_data_object_error_msg($at), ErrCode::$BUSINESS_ERROR);
                }
            }

            $db->commit();

            $staff_ids = array_column($informers, 'id');
            $this->sendEmailToStaffs($request, $staff_ids, GlobalEnums::CONSULTED_ACTION_TYPE, $user);

            //删除这些的未读消息key
            $this->delUnReadNumsKeyByStaffIds($staff_ids);

            //19706 当前审批节点对单据发起意见征询，给被征询人发送push
            $request->current_node_auditor_id = implode(',', $staff_ids);
            WorkflowEventService::getInstance()->pushEvent($request, WorkflowEventService::AUDIT_ACTION_ASK, WorkflowEventService::TYPE_FYR);
        } catch (ValidationException $e) {
            $db->rollback();

            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            $db->rollback();

            // 业务错误，不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');

            $this->logger->warning('createAsk-failed-business-exception: ' . $e->getMessage());

        } catch (\Exception $e) {
            $db->rollback();

            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('createAsk-failed-exception: ' . $e->getMessage());
        }

        return [
            'code' => $code,
            'message' => $message,
        ];
    }

    /**
     * 意见征询回复
     *
     * @param $ask_id
     * @param $note
     * @param $user
     * @param $attachments
     * @return mixed
     */
    public function createReply($ask_id, $note, $user, $attachments = [])
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';

        $db = $this->getDI()->get('db_oa');
        $db->begin();

        try {
            $at = WorkflowRequestNodeAt::findFirst($ask_id);
            if (empty($at)) {
                throw new ValidationException(static::$t->_('consultation_reply_submit_msg_1', ['asked_id' => $ask_id]), ErrCode::$VALIDATE_ERROR);
            }

            if ($at->staff_id != $user['id']) {
                throw new ValidationException(static::$t->_('consultation_reply_submit_msg_2', ['asked_id' => $ask_id]), ErrCode::$VALIDATE_ERROR);
            }

            if ($at->is_reply == GlobalEnums::CONSULTED_REPLY_STATE_PROCESSED) {
                throw new ValidationException(static::$t->_('consultation_reply_submit_msg_3', ['asked_id' => $ask_id]), ErrCode::$VALIDATE_ERROR);
            }

            // [1] 回复状态更新
            $at->is_reply = GlobalEnums::CONSULTED_REPLY_STATE_PROCESSED;
            $at->reply_at = date('Y-m-d H:i:s');
            if ($at->save() === false) {
                throw new BusinessException('意见征询回复: 被征询表回复状态更新失败, 数据: ' . json_encode($at->toArray(), JSON_UNESCAPED_UNICODE) . '; 原因可能是: ' . get_data_object_error_msg($at), ErrCode::$BUSINESS_ERROR);
            }

            // [2] 回复详情
            $fyr = new WorkflowRequestNodeFYR();
            $fyr->parent_fyr_id = $at->fyr_id;
            $fyr->request_id = $at->request_id;
            $fyr->flow_id = $at->flow_id;
            $fyr->flow_node_id = $at->flow_node_id;
            $fyr->staff_id = $user['id'];
            $fyr->staff_name = $this->getNameAndNickName($user['name'], $user['nick_name']);
            $fyr->staff_department = $user['department'];
            $fyr->staff_job_title = $user['job_title'];
            $fyr->action_type = GlobalEnums::CONSULTED_REPLY_ACTION_TYPE;
            $fyr->info = $note;
            $fyr->extra = '';
            $fyr->created_at = date('Y-m-d H:i:s');
            $fyr->staff_state = $this->getStaffJobState($user);
            $fyr->node_related_auditor_ids = '';
            if ($fyr->save() === false) {
                throw new BusinessException('意见征询回复: 征询表回复详情添加失败, 数据: ' . json_encode($fyr->toArray(), JSON_UNESCAPED_UNICODE) . '; 原因可能是: ' . get_data_object_error_msg($fyr), ErrCode::$BUSINESS_ERROR);
            }

            // [3] 回复附件
            if (!empty($attachments)) {
                $am = new AttachModel();
                $attach_arr = [];
                foreach ($attachments as $attachment) {
                    $attach_arr[] = [
                        'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_FRY_ATTACH,
                        'oss_bucket_key' => $fyr->id,
                        'bucket_name' => $attachment['bucket_name'],
                        'object_key' => $attachment['object_key'],
                        'file_name' => $attachment['file_name'],
                    ];
                }

                if ($am->batchInsert($attach_arr) === false) {
                    throw new BusinessException('意见征询回复: 回复相关附件批量添加失败, 数据: ' . json_encode($attach_arr, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                }
            }

            // 维护征询中间表数据
            $request = WorkflowRequestModel::findFirst($at->request_id);
            $this->saveFyrMiddleDataByReply($at, $request, $user);

            $db->commit();

            //只删除我自己的，其他人有可能还未回复，可以再回复
            $this->delUnReadNumsKeyByStaffIds([$at->staff_id]);

            // 审批中的回复需要邮件通知 v18276
            if ($request->state == Enums::WF_STATE_PENDING) {
                $this->sendEmailToStaffs($request, [$at->create_staff_id], GlobalEnums::CONSULTED_REPLY_ACTION_TYPE, $user);
            }

            //19706 当被征询人对单据回复了意见征询，给被征询人发送push
            $request->current_node_auditor_id = $user['id'];
            WorkflowEventService::getInstance()->pushEvent($request, WorkflowEventService::AUDIT_ACTION_REPLY, WorkflowEventService::TYPE_FYR);
        } catch (ValidationException $e) {
            $db->rollback();

            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            $db->rollback();

            // 业务错误，不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');

            $this->logger->warning('createReply-failed-business-exception: ' . $e->getMessage());

        } catch (\Exception $e) {
            $db->rollback();

            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('createReply-failed-exception: ' . $e->getMessage());
        }

        return [
            'code' => $code,
            'message' => $message,
        ];
    }

    /**
     * 征询 和 回复 的邮件通知
     *
     * @param $request
     * @param $informers
     * @param $type
     * @param $staff
     */
    public function sendEmailToStaffs($request, $informers, $type, $staff)
    {
        try {
            if (empty($request)) {
                throw new ValidationException('request null', ErrCode::$VALIDATE_ERROR);
            }

            // 开发/测试环境收件人取指定配置的
            if (in_array(env('runtime'), ['dev', 'test', 'tra', 'training'])) {
                $emails = EnumsService::getInstance()->getSettingEnvValueIds(GlobalEnums::EMAIL_NOTICE_WAITING_REPLY_CODE);
            } else {
                // 生产取工号对应的
                $emails = (new StaffInfoModel())->getEmails($informers);
            }

            // 如果收件人邮箱为空，则不发
            if (empty($emails)) {
                throw new ValidationException("no emails addressee, stop send.biz_type[{$request->biz_type}], node_auditors[" . json_encode($informers, JSON_UNESCAPED_UNICODE) . ']');
            }

            $en_lang = self::getTranslation('en');
            $is_en = 0;

            // 邮件模板语言定义
            $language_setting = $this->getNoticeEmailTemplateLang();
            $first_lang = $language_setting['first_lang'];
            $second_lang = $language_setting['second_lang'];

            // 各环境各国家dashboard页地址
            $url = EnvModel::getEnvByCode(GlobalEnums::OA_DASHBOARD_PAGE_URL_CODE);
            $url = !empty($url) ? $url : GlobalEnums::OA_DASHBOARD_PAGE_URL_DEFAULT;

            switch ($request->biz_type) {
                //合同
                case Enums::WF_CONTRACT_TYPE1:
                case Enums::WF_CONTRACT_TYPE20:
                case Enums::WF_CONTRACT_TYPE21:
                case Enums::WF_CONTRACT_TYPE22:
                case Enums::WF_CONTRACT_TYPE23:
                case Enums::WF_CONTRACT_TYPE24:
                    $item = Contract::findFirst([
                        'id = :id:',
                        'bind' => ['id' => $request->biz_value]
                    ]);
                    if (empty($item)) {
                        throw new BusinessException("no item, type = {$request->biz_type}, id = {$request->biz_value}", ErrCode::$BUSINESS_ERROR);
                    }
                    $item = $item->toArray();

                    // 合同分类map
                    $contract_category_map = EnumsService::getInstance()->getContractCategorysMap();

                    // 直属分类信息
                    $contract_category_info = $contract_category_map[$item['template_id']] ?? [];
                    $template_id = $contract_category_info['translation_key'] ?? '';

                    // 若直属分类是二级分类, 则需拼接一级分类
                    if (isset($contract_category_info['level']) && $contract_category_info['level'] > 1) {
                        $parent_template_id = $contract_category_map[$contract_category_info['ancestry_id']]['translation_key'];

                        $item['template_title'] = $first_lang->_($parent_template_id) . '/' . $first_lang->_($template_id);
                        $item['template_second_title'] = $second_lang->_($parent_template_id) . '/' . $second_lang->_($template_id);
                    } else {
                        $item['template_title'] = $first_lang->_($template_id);
                        $item['template_second_title'] = $second_lang->_($template_id);
                    }

                    $first_name = $item['template_title'] . '-' . $item['cno'];
                    $second_name = $item['template_second_title'] . '-' . $item['cno'];
                    $where_key = 'email_contract_reply_where';
                    $audit_menu_path_key = 'email_contract_where';
                    break;

                //借款
                case Enums::WF_LOAN_TYPE:
                    $item = Loan::findFirst([
                        'id = :id:',
                        'bind' => ['id' => $request->biz_value]
                    ]);

                    if (empty($item)) {
                        throw new BusinessException("no item, type = {$request->biz_type}, id = {$request->biz_value}", ErrCode::$BUSINESS_ERROR);
                    }

                    $first_name = $first_lang->_('loan_name', ['year' => date('Y', strtotime($item->created_at)), 'month' => date('m', strtotime($item->created_at))]) . '-' . $item->lno;
                    $second_name = $second_lang->_('loan_name', ['year' => date('Y', strtotime($item->created_at)), 'month' => date('m', strtotime($item->created_at))]) . '-' . $item->lno;
                    $where_key = 'email_loan_reply_where';
                    $audit_menu_path_key = 'email_loan_where';
                    break;

                //采购申请单
                case Enums::WF_PURCHASE_APPLY:
                    $item = PurchaseApply::findFirst([
                        'id = :id:',
                        'bind' => ['id' => $request->biz_value]
                    ]);
                    if (empty($item)) {
                        throw new BusinessException("no item, type = {$request->biz_type}, id = {$request->biz_value}", ErrCode::$BUSINESS_ERROR);
                    }

                    $first_biz_title = $first_lang->_('purchase_apply_name');
                    $second_biz_title = $second_lang->_('purchase_apply_name');

                    $first_name = $first_biz_title . '-' . $item->pano;
                    $second_name = $second_biz_title . '-' . $item->pano;

                    $where_key = 'email_purchase_pr_reply_where';
                    $audit_menu_path_key = 'email_purchase_where';
                    break;

                //采购订单
                case Enums::WF_PURCHASE_ORDER:
                    $item = PurchaseOrder::findFirst([
                        'id = :id:',
                        'bind' => ['id' => $request->biz_value]
                    ]);
                    if (empty($item)) {
                        throw new BusinessException("no item, type = {$request->biz_type}, id = {$request->biz_value}", ErrCode::$BUSINESS_ERROR);
                    }

                    $first_biz_title = $first_lang->_('purchase_order_name');
                    $second_biz_title = $second_lang->_('purchase_order_name');

                    $first_name = $first_biz_title . '-' . $item->pono;
                    $second_name = $second_biz_title . '-' . $item->pono;

                    $where_key = 'email_purchase_po_reply_where';
                    $audit_menu_path_key = 'email_purchase_where';
                    break;

                //采购付款申请单
                case Enums::WF_PURCHASE_PAYMENT:
                    $item = PurchasePayment::findFirst([
                        'id = :id:',
                        'bind' => ['id' => $request->biz_value]
                    ]);
                    if (empty($item)) {
                        throw new BusinessException("no item, type = {$request->biz_type}, id = {$request->biz_value}", ErrCode::$BUSINESS_ERROR);
                    }

                    $first_biz_title = $first_lang->_('purchase_payment_name');
                    $second_biz_title = $second_lang->_('purchase_payment_name');

                    $first_name = $first_biz_title . '-' . $item->ppno;
                    $second_name = $second_biz_title . '-' . $item->ppno;

                    $where_key = 'email_purchase_pp_reply_where';
                    $audit_menu_path_key = 'email_purchase_where';
                    break;

                //采购验收单
                case Enums::WF_PURCHASE_ACCEPTANCE:
                    $item = PurchaseAcceptanceModel::findFirst([
                        'id = :id:',
                        'bind' => ['id' => $request->biz_value]
                    ]);
                    if (empty($item)) {
                        throw new BusinessException("no item, type = {$request->biz_type}, id = {$request->biz_value}", ErrCode::$BUSINESS_ERROR);
                    }

                    $first_biz_title = $first_lang->_('purchase_acceptance_name');
                    $second_biz_title = $second_lang->_('purchase_acceptance_name');

                    $first_name = $first_biz_title . '-' . $item->no;
                    $second_name = $second_biz_title . '-' . $item->no;

                    $where_key = 'email_purchase_acceptance_reply_where';
                    $audit_menu_path_key = 'email_purchase_where';
                    break;

                //采购样品确认
                case Enums::WF_PURCHASE_SAMPLE:
                    $item = PurchaseSampleModel::findFirst([
                        'id = :id:',
                        'bind' => ['id' => $request->biz_value]
                    ]);
                    if (empty($item)) {
                        throw new BusinessException("no item, type = {$request->biz_type}, id = {$request->biz_value}", ErrCode::$BUSINESS_ERROR);
                    }

                    $first_biz_title = $first_lang->_('purchase_sample_name');
                    $second_biz_title = $second_lang->_('purchase_sample_name');

                    $first_name = $first_biz_title . '-' . $item->no;
                    $second_name = $second_biz_title . '-' . $item->no;

                    $where_key = 'email_purchase_sample_reply_where';
                    $audit_menu_path_key = 'email_purchase_where';
                    break;

                //供应商征询(信息 和 等级)
                case Enums::WF_VENDOR_BIZ_TYPE:
                case Enums::WF_VENDOR_GRADE_BIZ_TYPE:
                    $item = Vendor::findFirst([
                        'id = :id:',
                        'bind' => ['id' => $request->biz_value]
                    ]);
                    if (empty($item)) {
                        throw new BusinessException("no item, type = {$request->biz_type}, id = {$request->biz_value}", ErrCode::$BUSINESS_ERROR);
                    }

                    $first_name = $first_lang->_("vendor_name") . '-' . $item->vendor_id;
                    $second_name = $second_lang->_("vendor_name") . '-' . $item->vendor_id;
                    $where_key = 'vendor_pp_reply_where';
                    $audit_menu_path_key = 'vendor_audit_email_return_where';
                    break;

                //报销
                case Enums::WF_REIMBURSEMENT_TYPE:
                    $item = Reimbursement::findFirst([
                        'id = :id:',
                        'bind' => ['id' => $request->biz_value]
                    ]);

                    if (empty($item)) {
                        throw new BusinessException("no item, type = {$request->biz_type}, id = {$request->biz_value}", ErrCode::$BUSINESS_ERROR);
                    }
                    $first_name = $first_lang->_('email_reimbursement_name') . '-' . $item->no;
                    $second_name = $second_lang->_('email_reimbursement_name') . '-' . $item->no;
                    $where_key = 'email_reimbursement_reply_where';
                    $audit_menu_path_key = 'email_reimbursement_where';
                    break;

                // 网点备用金申请
                case Enums::WF_RESERVE_FUND_APPLY:
                    $item = ReserveFundApply::findFirst([
                        'id = :id:',
                        'bind' => ['id' => $request->biz_value]
                    ]);

                    if (empty($item)) {
                        throw new BusinessException("no item, type = {$request->biz_type}, id = {$request->biz_value}", ErrCode::$BUSINESS_ERROR);
                    }
                    $first_name = $first_lang->_('email_reserve_fund_apply_name') . '-' . $item->rfano;
                    $second_name = $second_lang->_('email_reserve_fund_apply_name') . '-' . $item->rfano;
                    $where_key = 'email_reserve_fund_apply_reply_where';
                    $audit_menu_path_key = 'reserve_fund_email_apply_where';
                    break;

                // 网点备用金归还
                case Enums::WF_RESERVE_FUND_RETURN:
                    $item = ReserveFundReturn::findFirst([
                        'id = :id:',
                        'bind' => ['id' => $request->biz_value]
                    ]);
                    if (empty($item)) {
                        throw new BusinessException("no item, type = {$request->biz_type}, id = {$request->biz_value}", ErrCode::$BUSINESS_ERROR);
                    }

                    $first_name = $first_lang->_('email_reserve_fund_return_name') . '-' . $item->rrno;
                    $second_name = $second_lang->_('email_reserve_fund_return_name') . '-' . $item->rrno;
                    $where_key = 'email_reserve_fund_return_reply_where';
                    $audit_menu_path_key = 'reserve_fund_email_return_where';
                    break;

                //普通付款
                case Enums::ORDINARY_PAYMENT_BIZ_TYPE:
                    $item = OrdinaryPayment::findFirst([
                        'id = :id:',
                        'bind' => ['id' => $request->biz_value]
                    ]);

                    if (empty($item)) {
                        throw new BusinessException("no item, type = {$request->biz_type}, id = {$request->biz_value}", ErrCode::$BUSINESS_ERROR);
                    }

                    $first_name = $first_lang->_('email_ordinary_payment_subject') . '-' . $item->apply_no;
                    $second_name = $second_lang->_('email_ordinary_payment_subject') . '-' . $item->apply_no;
                    $where_key = 'email_ordinary_payment_body';
                    $audit_menu_path_key = 'ordinary_payment_audit_email_where';
                    break;

                //押金归还
                case Enums::DEPOSIT_RETURN_BIZ_TYPE:
                    $item = DepositReturnModel::findFirst([
                        'id=:id:',
                        'bind' => ['id' => $request->biz_value]
                    ]);
                    if (empty($item)) {
                        throw new BusinessException("no item, type = {$request->biz_type}, id = {$request->biz_value}", ErrCode::$BUSINESS_ERROR);
                    }

                    $deposit_item = DepositModel::findFirst([
                        'id=:id:',
                        'bind' => ['id' => $item->deposit_id]
                    ]);
                    if (empty($deposit_item)) {
                        throw new BusinessException("no deposit_item, type = {$request->biz_type}, id = {$item->deposit_id}", ErrCode::$BUSINESS_ERROR);
                    }

                    $first_name = $first_lang->_('deposit_return_pdf_file_name') . '-' . $deposit_item->business_no;
                    $second_name = $second_lang->_('deposit_return_pdf_file_name') . '-' . $deposit_item->business_no;
                    $where_key = 'deposit_return_reply_menu_path';
                    $audit_menu_path_key = 'deposit_return_email_to_auditors_content';
                    break;

                // 网点租房付款
                case Enums::WF_PAYMENT_STORE_RENTING_TYPE:
                    $item = PaymentStoreRenting::findFirst([
                        'id = :id:',
                        'bind' => ['id' => $request->biz_value]
                    ]);

                    if (empty($item)) {
                        throw new BusinessException("no item, type = {$request->biz_type}, id = {$request->biz_value}", ErrCode::$BUSINESS_ERROR);
                    }

                    $first_name = $first_lang->_('payment_store_renting_email_apply_name') . '-' . $item->apply_no;
                    $second_name = $en_lang->_('payment_store_renting_email_apply_name') . '-' . $item->apply_no;
                    $where_key = 'email_payment_body';
                    $audit_menu_path_key = 'payment_store_renting_email_where';
                    break;

                // 网点租房合同
                case Enums::WF_STORE_RENTING_CONTRACT_TYPE:
                case Enums::WF_STORE_RENTING_CONTRACT_RENEWAL_TYPE:
                    $item = ContractStoreRentingModel::findFirst([
                        'id = :id:',
                        'bind' => ['id' => $request->biz_value]
                    ]);

                    if (empty($item)) {
                        throw new BusinessException("no item, type = {$request->biz_type}, id = {$request->biz_value}", ErrCode::$BUSINESS_ERROR);
                    }

                    $first_name = $first_lang->_('email_store_rent_contract_name') . '-' . $item->contract_id;
                    $second_name = $second_lang->_('email_store_rent_contract_name') . '-' . $item->contract_id;
                    $where_key = 'email_store_rent_contract_ask_where';
                    $audit_menu_path_key = 'email_store_rent_contract_audit_where';
                    break;

                // GPMD集团平台合同
                case Enums::WF_CONTRACT_GPMD_BIZ_TYPE:
                    $item = ContractPlatFormModel::findFirst([
                        'id = :id:',
                        'bind' => ['id' => $request->biz_value]
                    ]);

                    if (empty($item)) {
                        throw new BusinessException("no item, type = {$request->biz_type}, id = {$request->biz_value}", ErrCode::$BUSINESS_ERROR);
                    }

                    $first_name = $first_lang->_('gpmd_platform_contract_module_name') . '-' . $item->cno;
                    $second_name = $second_lang->_('gpmd_platform_contract_module_name') . '-' . $item->cno;
                    $where_key = 'gpmd_platform_contract_reply_menu_path';
                    $audit_menu_path_key = 'gpmd_platform_contract_audit_menu_path';
                    break;

                //支付模块
                case Enums::WF_PAY_TYPE:
                    $item = Payment::findFirst([
                        'id = :id:',
                        'bind' => ['id' => $request->biz_value]
                    ]);

                    if (empty($item)) {
                        throw new BusinessException("no item, type = {$request->biz_type}, id = {$request->biz_value}", ErrCode::$BUSINESS_ERROR);
                    }

                    $first_name = $first_lang->_('email_pay_name') . '-' . $item->no;
                    $second_name = $second_lang->_('email_pay_name') . '-' . $item->no;
                    $where_key = 'email_pay_reply_where';
                    $audit_menu_path_key = 'payment_email_where';
                    $is_en = 1;
                    break;

                // 预算调整
                case Enums::WF_BUDGET_ADJUST_TYPE:
                    $item = BudgetAdjustModel::findFirst([
                        'id = :id:',
                        'bind' => ['id' => $request->biz_value]
                    ]);
                    if (empty($item)) {
                        throw new BusinessException("no item, type = {$request->biz_type}, id = {$request->biz_value}", ErrCode::$BUSINESS_ERROR);
                    }

                    $first_name = $first_lang->_('budget_adjust_method_text') . '-' . $item->adjust_no;
                    $second_name = $second_lang->_('budget_adjust_method_text') . '-' . $item->adjust_no;
                    $where_key = 'budget_adjust_reply_menu_path';
                    $audit_menu_path_key = 'budget_adjust_audit_where';
                    break;

                // 支票申请审批
                case Enums::WF_CHEQUE_APPLY_BIZ_TYPE:
                    $item = ChequeApplyModel::findFirst([
                        'id = :id:',
                        'bind' => ['id' => $request->biz_value]
                    ]);
                    if (empty($item)) {
                        throw new BusinessException("no item, type = {$request->biz_type}, id = {$request->biz_value}", ErrCode::$BUSINESS_ERROR);
                    }

                    $first_name = $first_lang->_('cheque_module_name') . '-' . $item->apply_no;
                    $second_name = $second_lang->_('cheque_module_name') . '-' . $item->apply_no;
                    $where_key = 'cheque_reply_menu_path';
                    $audit_menu_path_key = 'cheque_audit_menu_path';
                    break;

                default:
                    throw new BusinessException('no type=' . $request->biz_type, ErrCode::$BUSINESS_ERROR);
            }

            switch ($type) {
                // 征询通知: 给被征询人发邮件
                case GlobalEnums::CONSULTED_ACTION_TYPE:
                    $title_key = 'email_ask_title';
                    $content_key = 'email_ask_content';
                    $first_title = $first_lang->_($title_key, ['name' => $first_name]);
                    $second_title = $second_lang->_($title_key, ['name' => $second_name]);

                    $first_where = $first_lang->_($where_key);
                    $second_where = $second_lang->_($where_key);

                    $first_content = $first_lang->_($content_key, ['name' => $first_name, 'where' => $first_where]);
                    $second_content = $second_lang->_($content_key, ['name' => $second_name, 'where' => $second_where]);
                    if (1 == $is_en) {
                        $second_title = $en_lang->_($title_key, ['name' => $second_name]);
                        $second_where = $en_lang->_($where_key);
                        $second_content = $en_lang->_($content_key, ['name' => $second_name, 'where' => $second_where]);
                    }

                    // 征询通知的模版
                    $title = "★★★{$first_title}/{$second_title}";
                    break;

                // 回复通知: 给征询人发邮件
                case GlobalEnums::CONSULTED_REPLY_ACTION_TYPE:
                    $title_key = 'email_reply_title';
                    $content_key = 'email_reply_content';
                    $first_title = $first_lang->_($title_key, ['name' => $first_name]);
                    $second_title = $second_lang->_($title_key, ['name' => $second_name]);

                    // 采购模块的子业务名称替换
                    if ($audit_menu_path_key == 'email_purchase_where') {
                        $first_where = $first_lang->_($audit_menu_path_key, ['name' => $first_biz_title ?? '']);
                        $second_where = $second_lang->_($audit_menu_path_key, ['name' => $second_biz_title ?? '']);
                    } else {
                        $first_where = $first_lang->_($audit_menu_path_key);
                        $second_where = $second_lang->_($audit_menu_path_key);
                    }

                    $first_content = $first_lang->_($content_key, ['name' => $first_name, 'where' => $first_where]);
                    $second_content = $second_lang->_($content_key, ['name' => $second_name, 'where' => $second_where]);
                    if (1 == $is_en) {
                        $second_title = $en_lang->_($title_key, ['name' => $second_name]);

                        $second_where = $en_lang->_($audit_menu_path_key);
                        $second_content = $en_lang->_($content_key, ['name' => $second_name, 'where' => $second_where]);
                    }

                    // 回复通知的模板
                    $title = "{$first_title}/{$second_title}";
                    break;

                default:
                    throw new BusinessException("params type[{$type}] error", ErrCode::$BUSINESS_ERROR);
            }

            $content = <<<EOF
    hi,<br/>
    <span style="margin-left: 16px"></span>{$first_content}<a href="{$url}" target="_blank">{$url}</a><br/>
    <span style="margin-left: 16px"></span>{$second_content}<a href="{$url}" target="_blank">{$url}</a>

EOF;

            $log = ['emails' => $emails, 'title' => $title, 'content' => $content];
            $send_res = $this->mailer->sendAsync($emails, $title, $content);
            if ($send_res) {
                $this->logger->info("sendEmailToAuditors 入队成功！[FYR-{$type}]" . json_encode($log, JSON_UNESCAPED_UNICODE));
            } else {
                $this->logger->warning("sendEmailToAuditors 入队失败！[FYR-{$type}]" . json_encode($log, JSON_UNESCAPED_UNICODE));
            }

        } catch (ValidationException $e) {
            $this->logger->info("sendEmailToAuditors 校验异常！[FYR-{$type}] 原因可能是：" . $e->getMessage());

        } catch (BusinessException $e) {
            $this->logger->notice("sendEmailToAuditors 入队异常！[FYR-{$type}] 原因可能是：" . $e->getMessage());

        } catch (\Exception $e) {
            $this->logger->warning("sendEmailToAuditors 入队异常！[FYR-{$type}] 原因可能是：" . $e->getMessage());
        }
    }

    /**
     * 员工在职状态归并
     *
     * @param $staff_info
     * @return mixed
     */
    public function getStaffJobState($staff_info)
    {
        // 1 在职; 2 非在职
        return isset($staff_info['state']) && $staff_info['state'] == 1 ? 1 : 2;
    }

    /**
     * 获取征询人所在节点的干系审批人 和 节点审批人
     *
     * 相关审批人 取值逻辑如下: 根据征询人所在节点类型
     * 或签: 节点所有人
     * 会签: 征询人
     *
     * @param $req
     * @param $uid
     * @return mixed
     */
    public function getConsultantNodeAuditorInfo($req, $uid)
    {
        $result = [
            'node_audit_type' => 0, // 征询人一级审批节点类型
            'sub_node_id' => 0, // 征询人所在子节点ID, 默认0
            'node_related_auditor_ids' => '',// 征询节点干系审批人
            'node_auditor_ids' => '',// 征询节点审批人(征询人所在节点的审批人)
        ];

        if (empty($req) || empty($uid) || empty($req->current_node_auditor_id) || empty($req->current_flow_node_id)) {
            return $result;
        }

        $node = WorkflowNodeModel::findFirst([
            'conditions' => 'id = :id:',
            'bind' => ['id' => $req->current_flow_node_id]
        ]);

        if (empty($node)) {
            return $result;
        }

        $result['node_audit_type'] = $node->node_audit_type;

        // 一级节点或签
        if ($node->node_audit_type == Enums::WF_NODE_AUDIT_TYPE_OR_SIGN) {
            $result['node_related_auditor_ids'] = $req->current_node_auditor_id;
            $result['node_auditor_ids'] = $req->current_node_auditor_id;

        } else if ($node->node_audit_type == Enums::WF_NODE_AUDIT_TYPE_AUDITOR_COUNTERSIGN) {
            // 一级节点会签
            $result['node_related_auditor_ids'] = $uid;
            $result['node_auditor_ids'] = $req->current_node_auditor_id;

        } else if ($node->node_audit_type == Enums::WF_NODE_AUDIT_TYPE_SUB_NODE_COUNTERSIGN) {
            // 子节点会签, 查找固化的子节点配置
            $sub_node_solidified_auditors = WorkflowServiceV2::getInstance()->getWorkflowSubNodeSolidifiedAuditors($req, $node);

            $sub_node_id = 0;
            $related_auditor_ids = '';
            foreach ($sub_node_solidified_auditors as $id => $sub_auditor_ids) {
                if (in_array($uid, $sub_auditor_ids)) {
                    $sub_node_id = $id;
                    $related_auditor_ids = implode(',', $sub_auditor_ids);
                    break;
                }
            }

            $result['node_related_auditor_ids'] = $related_auditor_ids;
            $result['node_auditor_ids'] = $related_auditor_ids;
            $result['sub_node_id'] = $sub_node_id;
        }

        return $result;
    }

    /**
     * 获取业务工单审批征询的回复处理列表
     *
     * @param object $builder
     * @param int $reply_process_state 回复处理标识 0-待处理 1-已处理
     * @param array $biz_type 业务类型
     * @param int $user_id 当前用户
     * @param array $biz_table_info 业务表参数 包含业务主表别名、主表的支付状态字段等
     *
     * @return mixed
     */
    public function getBizOrderConsultationReplyListByProcessState(object $builder, int $reply_process_state, array $biz_type, int $user_id = 0, array $biz_table_info = [])
    {
        if (empty($biz_type) || empty($user_id) || empty($biz_table_info)) {
            return $builder;
        }

        $biz_table_alias = $biz_table_info['table_alias'] ?? 'main';
        $pay_status_field_name = $biz_table_info['pay_status_field_name'] ?? '';

        $builder->leftjoin(WorkflowRequestModel::class, "request.biz_value = {$biz_table_alias}.id", 'request');
        $builder->leftjoin(WorkflowRequestNodeAt::class, 'request.id = reply.request_id', 'reply');
        $builder->andWhere('request.biz_type IN ({biz_type:array}) AND request.is_abandon = :is_abandon:', [
            'biz_type' => $biz_type,
            'is_abandon' => GlobalEnums::WORKFLOW_ABANDON_STATE_NO
        ]);
        $builder->andWhere('reply.staff_id = :current_user_id:', ['current_user_id' => $user_id]);

        // 当前用户的回复数据
        switch ($reply_process_state) {
            // 待处理
            case GlobalEnums::CONSULTED_REPLY_STATE_PENDING:
                $builder->andWhere('reply.is_reply = :is_reply:', ['is_reply' => GlobalEnums::CONSULTED_REPLY_STATE_PENDING]);

                // 待审批
                $where = 'request.state = :pending_state:';
                $conditions = ['pending_state' => Enums::WF_STATE_PENDING];

                // 有支付, 则已审批 + 待支付
                if (!empty($pay_status_field_name)) {
                    $where .= " OR (request.state = :approval_state: AND {$biz_table_alias}.$pay_status_field_name = :pay_status_pending:)";

                    $conditions['approval_state'] = Enums::WF_STATE_APPROVED;
                    $conditions['pay_status_pending'] = Enums::PAYMENT_PAY_STATUS_PENDING;
                }

                $builder->andWhere($where, $conditions);
                $builder->orderBy('reply.created_at ASC');
                break;

            // 已处理
            case GlobalEnums::CONSULTED_REPLY_STATE_PROCESSED:
                $builder->andWhere('reply.is_reply = :is_reply:', ['is_reply' => GlobalEnums::CONSULTED_REPLY_STATE_PROCESSED]);
                $builder->orderBy('reply.reply_at DESC');
                break;
        }

        return $builder;
    }

    /**
     * 获取业务工单审批征询的回复待处理统计
     *
     * @param array $biz_type_item 业务类型
     * @param int $user_id 当前用户
     * @param $biz_table_object
     * @param string $pay_status_field_name 业务表支付状态字段名称
     *
     * @return mixed
     */
    public function getBizConsultationPendingCount(array $biz_type_item = [], int $user_id = 0, $biz_table_object = null, string $pay_status_field_name = '')
    {
        if (empty($biz_type_item) || empty($user_id)) {
            return 0;
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->from(['request' => WorkflowRequestModel::class]);
        $builder->leftjoin(WorkflowRequestNodeAt::class, 'request.id = reply.request_id', 'reply');

        // 审批流中的有效业务单据
        $builder->andWhere('request.biz_type IN ({biz_type_item:array}) AND request.is_abandon = :is_abandon:', [
            'biz_type_item' => $biz_type_item,
            'is_abandon' => GlobalEnums::WORKFLOW_ABANDON_STATE_NO
        ]);

        // 当前用户待处理的
        $builder->andWhere('reply.staff_id = :current_user_id: AND reply.is_reply = :is_reply:', [
            'current_user_id' => $user_id,
            'is_reply' => GlobalEnums::CONSULTED_REPLY_STATE_PENDING
        ]);

        // 1. 待审批
        $where = 'request.state = :pending_state:';
        $conditions = ['pending_state' => Enums::WF_STATE_PENDING];

        // 2. 有支付的业务模块: 已审批 + 待支付
        if (!empty($pay_status_field_name) && !empty($biz_table_object)) {
            $builder->leftjoin($biz_table_object, 'request.biz_value = main.id', 'main');

            $where .= " OR (request.state = :approval_state: AND main.$pay_status_field_name = :pay_status_pending:)";
            $conditions['approval_state'] = Enums::WF_STATE_APPROVED;
            $conditions['pay_status_pending'] = Enums::PAYMENT_PAY_STATUS_PENDING;
        }

        $builder->andWhere($where, $conditions);
        $builder->columns('COUNT(DISTINCT reply.id) AS total');
        return intval($builder->getQuery()->getSingleResult()->total);
    }

    /**
     * 获取征询信息
     *
     * @param int $request_id
     * @param int $fyr_id
     * @param int $uid
     * @return array
     */
    public function getFyrInfoById(int $request_id, int $fyr_id, int $uid)
    {
        if (empty($request_id) || empty($fyr_id) || empty($uid)) {
            return [];
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->from(['fyr' => WorkflowRequestNodeFYR::class]);
        $builder->leftjoin(WorkflowRequestNodeAt::class, 'reply.fyr_id = fyr.id', 'reply');
        $builder->where('fyr.id = :fyr_id: AND fyr.request_id = :request_id: AND fyr.action_type = :action_type:', [
            'fyr_id' => $fyr_id,
            'request_id' => $request_id,
            'action_type' => GlobalEnums::CONSULTED_ACTION_TYPE
        ]);
        $builder->andWhere('reply.staff_id = :staff_id:', ['staff_id' => $uid]);

        $fields = [
            'fyr.id AS fyr_id',
            'fyr.staff_id',
            'fyr.staff_name',
            'fyr.staff_department',
            'fyr.staff_job_title',
            'fyr.created_at',
            'fyr.info',
            'reply.id AS ask_id'
        ];
        $fyr_data = $builder->columns($fields)->getQuery()->getSingleResult();

        // 获取征询附件
        if (!empty($fyr_data)) {
            $fyr_data = $fyr_data->toArray();
            $fyr_data['attachments'] = SysAttachmentRepository::getInstance()->getAttachmentsByBizParams($fyr_data['fyr_id'], Enums::OSS_BUCKET_TYPE_FRY_ATTACH);
        }

        return $fyr_data ?? [];
    }

    /**
     * 征询场景: 维护征询中级表数据
     *
     * @param object $request
     * @param array $current_node_info
     * @param array $user
     * @return bool
     * @throws BusinessException
     */
    protected function saveFyrMiddleDataByFyr(object $request, array $current_node_info, array $user = [])
    {
        // 获取当前审批流当前节点的征询中间表数据
        $fyr_middle_model = WorkflowRequestNodeFyrMiddleModel::findFirst([
            'conditions' => 'request_id = :request_id: AND flow_node_id = :flow_node_id:',
            'bind' => ['request_id' => $request->id, 'flow_node_id' => $request->current_flow_node_id]
        ]);

        if (empty($fyr_middle_model)) {
            // 首次征询
            $node_fyr_auditor_ids = $current_node_info['node_related_auditor_ids'];
            $node_reply_auditor_ids = '';

            $fyr_middle_model = new WorkflowRequestNodeFyrMiddleModel();
            $fyr_middle_model->request_id = $request->id;
            $fyr_middle_model->flow_node_id = $request->current_flow_node_id;
            $fyr_middle_model->created_id = $user['id'];
            $fyr_middle_model->created_at = date('Y-m-d H:i:s');
        } else {
            // 重复征询
            // 重置征询相关审批人: 追加本次征询相关的审批人
            $exist_fyr_auditor_ids = explode(',', $fyr_middle_model->node_fyr_auditor_ids);
            $current_fyr_auditor_ids = explode(',', $current_node_info['node_related_auditor_ids']);
            $node_fyr_auditor_ids = implode(',', array_filter(array_unique(array_merge($exist_fyr_auditor_ids, $current_fyr_auditor_ids))));

            // 回复相关审批人: 剔除掉本次征询相关的审批人
            $exist_reply_auditor_ids = explode(',', $fyr_middle_model->node_reply_auditor_ids);
            $node_reply_auditor_ids = implode(',', array_filter(array_unique(array_diff($exist_reply_auditor_ids, $current_fyr_auditor_ids))));
        }

        // 公共字段维护
        $fyr_middle_model->node_fyr_auditor_ids = $node_fyr_auditor_ids;
        $fyr_middle_model->node_reply_auditor_ids = $node_reply_auditor_ids;
        $fyr_middle_model->node_audit_type = $current_node_info['node_audit_type'];
        $fyr_middle_model->node_auditor_ids = $current_node_info['node_auditor_ids'];
        $fyr_middle_model->updated_id = $user['id'];
        $fyr_middle_model->updated_at = date('Y-m-d H:i:s');
        $fyr_middle_model->updated_action_type = GlobalEnums::CONSULTED_ACTION_TYPE;

        if ($fyr_middle_model->save() === false) {
            throw new BusinessException('意见征询-征询中间表数据维护失败, 数据: ' . json_encode($fyr_middle_model->toArray(), JSON_UNESCAPED_UNICODE) . '; 原因可能是: ' . get_data_object_error_msg($fyr_middle_model), ErrCode::$BUSINESS_ERROR);
        }

        return true;
    }

    /**
     * 回复场景: 维护征询中间表数据
     *
     * @param object $at
     * @param object $request
     * @param array $user
     * @return bool
     * @throws BusinessException
     */
    protected function saveFyrMiddleDataByReply(object $at, object $request, array $user = [])
    {
        // 回复的审批流及征询节点仍处于待审批状态时, 则需维护
        if ($at->flow_node_id == $request->current_flow_node_id && $request->state == Enums::WF_STATE_PENDING) {
            // 获取回复信息关联的征询中间表数据
            $fyr_middle_model = WorkflowRequestNodeFyrMiddleModel::findFirst([
                'conditions' => 'request_id = :request_id: AND flow_node_id = :flow_node_id:',
                'bind' => ['request_id' => $at->request_id, 'flow_node_id' => $at->flow_node_id]
            ]);
            if (empty($fyr_middle_model)) {
                throw new BusinessException('征询回复-征询中间表数据不存在, 请排查, 回复数据=' . json_encode($at->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }

            // 获取回复关联的征询记录
            if ($at->fyr_id) {
                // 新数据: 通过征询时固化的征询ID找对应的征询数据
                $fyr_model = WorkflowRequestNodeFYR::findFirst($at->fyr_id);
                if (empty($fyr_model)) {
                    throw new BusinessException('征询回复-关联的征询表数据不存在, 请排查, fyr_id=' . $at->fyr_id, ErrCode::$BUSINESS_ERROR);
                }

                // 工号会签: 查发起征询的工号即可
                if ($fyr_model->node_node_audit_type == Enums::WF_NODE_AUDIT_TYPE_AUDITOR_COUNTERSIGN) {
                    $fyr_staff_ids = [$fyr_model->staff_id];
                } else {
                    // 或签 或 子节点会签: 查征询人所在直属节点的审批人工号
                    $fyr_staff_ids = explode(',', $fyr_model->node_auditor_ids);
                }

            } else {
                // 老数据: 回复表未固化 fyr_id, 实时查询该审批流审批节点的节点类型和审批人
                $current_node_info = $this->getConsultantNodeAuditorInfo($request, $at->create_staff_id);
                $fyr_staff_ids = explode(',', $current_node_info['node_related_auditor_ids']);
            }

            // 计算回复所在节点的所有征询是否已全部回复
            // 或签节点的: 节点所有人征询是否已回复; 工号会签的: 工号的征询是否已回复; 子节点会签的: 征询人子节点的所有人征询是否已回复
            $builder = $this->modelsManager->createBuilder();
            $builder->from(WorkflowRequestNodeAt::class);
            $builder->where('request_id = :request_id: AND flow_node_id = :flow_node_id: AND is_reply = :is_reply:', [
                'request_id' => $at->request_id,
                'flow_node_id' => $at->flow_node_id,
                'is_reply' => GlobalEnums::CONSULTED_REPLY_STATE_PENDING
            ]);
            $builder->inWhere('create_staff_id', $fyr_staff_ids);
            $waiting_reply_count = $builder->columns('COUNT(id) AS total')->getQuery()->getSingleResult()->total;

            // 已全部回复: 维护征询中间表回复相关审批人字段
            if ($waiting_reply_count <= 0) {
                // 回复相关审批人: 剔除掉本次征询相关的审批人
                $exist_reply_auditor_ids = explode(',', $fyr_middle_model->node_reply_auditor_ids);
                $node_reply_auditor_ids = implode(',', array_filter(array_unique(array_merge($fyr_staff_ids, $exist_reply_auditor_ids))));

                $fyr_middle_model->node_reply_auditor_ids = $node_reply_auditor_ids;
                $fyr_middle_model->updated_id = $user['id'];
                $fyr_middle_model->updated_at = date('Y-m-d H:i:s');
                $fyr_middle_model->updated_action_type = GlobalEnums::CONSULTED_REPLY_ACTION_TYPE;

                if ($fyr_middle_model->save() === false) {
                    throw new BusinessException('征询回复-征询中间表数据维护失败, 数据: ' . json_encode($fyr_middle_model->toArray(), JSON_UNESCAPED_UNICODE) . '; 原因可能是: ' . get_data_object_error_msg($fyr_middle_model), ErrCode::$BUSINESS_ERROR);
                }
            }
        }

        return true;
    }


}
