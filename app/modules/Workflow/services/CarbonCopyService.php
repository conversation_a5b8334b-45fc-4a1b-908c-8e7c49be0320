<?php


namespace App\Modules\Workflow\Services;

use App\Library\BaseService;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\OAWorkflowEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Models\oa\WorkflowCarbonCopyModel;
use App\Modules\Workflow\Models\WorkflowModel;
use App\Repository\oa\SysModuleRepository;
use App\Repository\oa\WorkflowBusinessRepository;
use App\Repository\oa\WorkflowCarbonCopyRepository;
use App\Repository\oa\WorkflowRepository;

class CarbonCopyService extends BaseService
{
    public static $not_must_list = [
        'audit_business_id',
        'module_id',
        'status'
    ];

    public static $validate_list_search = [
        'pageSize' => 'IntGt:0', //每页条数
        'pageNum' => 'IntGt:0', //页码
        'audit_business_id' => 'IntGt:0', //审批业务id
        'module_id' => 'IntGt:0', //模块id
        'status' => 'IntGt:0', //抄送状态
    ];

    public static $validate_read = [
        'id' => 'IntGt:0',
    ];

    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    /**
     * 单例
     * @return CarbonCopyService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 获取枚举
     * @return array
     */
    public function getDefault()
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            //查询审批流中使用的所有模块和审批业务
            $workflow_data = WorkflowRepository::getInstance()->getWorkflowData();
            $module_data = array_column($workflow_data, null, 'module_id');
            $business_data = array_column($workflow_data, null, 'business_id');
            //模块
            $module_items = [];
            foreach ($module_data as &$v) {
                if (empty($v['module_id'])) {
                    continue;
                }
                $module_items[] = [
                    'value' => (string)$v['module_id'],
                    'label' => static::$t[$v['module_name_key']]
                ];
            }
            //审批业务
            $business_items = [];
            foreach ($business_data as &$business_v) {
                if (empty($business_v['business_id'])) {
                    continue;
                }
                $business_items[] = [
                    'value' => (string)$business_v['business_id'],
                    'label' => static::$t[$business_v['business_name_key']]
                ];
            }
            //抄送状态
            $status_items = [];
            $status_items_enum = OAWorkflowEnums::$workflow_cc_status_items;
            foreach ($status_items_enum as $status_k => $status_v) {
                $status_items[] = [
                    'value' => (string)$status_k,
                    'label' => static::$t[$status_v]
                ];
            }
            $data['module_items'] = $module_items;
            $data['audit_business_items'] = $business_items;
            $data['status_items'] = $status_items;
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
            $this->logger->warning('抄送管理-我的抄送-查询枚举失败: ' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 抄送管理-我的抄送-列表
     * @param $params
     * @param $user
     * @return array
     */
    public function getList($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [
            'items' => [],
            'pagination' => [
                'current_page' => 0,
                'per_page' => 0,
                'total_count' => 0,
            ],
        ];
        try {
            $page_size = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $params['pageSize'];
            $page_num = empty($params['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $params['pageNum'];
            $offset = $page_size * ($page_num - 1);
            $data['pagination']['current_page'] = $page_num;
            $data['pagination']['per_page'] = $page_size;
            //查询总数
            $count = $this->getListCount($params, $user);
            //查询列表
            $items = [];
            if ($count) {
                $builder = $this->modelsManager->createBuilder();
                $columns = 'cc.id, cc.biz_type, cc.biz_value, cc.summary, cc.apply_staff_info_id, cc.apply_staff_name, cc.apply_department_id, cc.apply_department_name, cc.apply_at, cc.carbon_copy_at, cc.status, w.module_id, w.business_id';
                $builder->columns($columns);
                $builder->from(['cc' => WorkflowCarbonCopyModel::class]);
                $builder->leftJoin(WorkflowModel::class, 'w.id = cc.flow_id', 'w');

                //组合搜索条件
                $builder = $this->getCondition($builder, $params, $user);
                $builder->limit($page_size, $offset);
                $builder->orderby('cc.status ASC, cc.id DESC');
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleListItems($items);
            }

            $data['items'] = $items;
            $data['pagination']['total_count'] = $count;
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-carbon-copy-list-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 抄送管理-我的抄送-列表-总数
     * @param $condition
     * @param $user
     * @return int
     */
    public function getListCount($condition, $user)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(cc.id) as count');
        $builder->from(['cc' => WorkflowCarbonCopyModel::class]);
        $builder->leftJoin(WorkflowModel::class, 'w.id = cc.flow_id', 'w');
        //组合搜索条件
        $builder = $this->getCondition($builder, $condition, $user);
        return (int)$builder->getQuery()->getSingleResult()->count;
    }

    /**
     * 抄送管理-我的抄送-列表-条件拼接
     * @param $builder
     * @param $condition
     * @param $user
     * @return object $builder
     */
    public function getCondition($builder, $condition, $user)
    {
        $status = !empty($condition['status']) ? $condition['status'] : 0;//抄送状态
        $module_id = !empty($condition['module_id']) ? $condition['module_id'] : 0;//模块id
        $business_id = !empty($condition['business_id']) ? $condition['business_id'] : 0;//审批业务id
        //固定条件,当前登录人的抄送
        $builder->andWhere('cc.cc_staff_info_id = :cc_staff_info_id:', ['cc_staff_info_id' => $user['id']]);

        //模块id
        if (!empty($module_id)) {
            $builder->andWhere('w.module_id = :module_id:', ['module_id' => $module_id]);
        }
        //审批业务id
        if (!empty($business_id)) {
            $builder->andWhere('w.business_id = :business_id:', ['business_id' => $business_id]);
        }
        //抄送状态
        if (!empty($status)) {
            $builder->andWhere('cc.status = :status:', ['status' => $status]);
        }
        return $builder;
    }

    /**
     * 抄送管理-我的抄送-列表-列表数据处理
     * @param $items
     * @return array
     */
    private function handleListItems($items)
    {
        if (empty($items)) {
            return [];
        }
        //获取模块名称
        $module_ids = array_values(array_unique(array_column($items, 'module_id')));
        $module_items = SysModuleRepository::getInstance()->getModuleByIds($module_ids);
        $module_items = array_column($module_items, 'name_key', 'id');
        //获取业务名称
        $business_ids = array_values(array_unique(array_column($items, 'business_id')));
        $business_items = WorkflowBusinessRepository::getInstance()->getBusinessByIds($business_ids);
        $business_items = array_column($business_items, 'name_key', 'id');

        $cc_status_items = OAWorkflowEnums::$workflow_cc_status_items;

        foreach ($items as &$item) {
            //模块id文本
            $item['module_id_text'] = !empty($module_items[$item['module_id']]) ? static::$t[$module_items[$item['module_id']]] : '';
            $item['business_id_text'] = !empty($business_items[$item['business_id']]) ? static::$t[$business_items[$item['business_id']]] : '';
            $item['summary'] = $this->getSummaryFormat($item['summary']);

            //使用状态文本
            $item['status_text'] = isset($cc_status_items[$item['status']]) ? static::$t[$cc_status_items[$item['status']]] : '';
        }
        return $items;
    }

    /**
     * 抄送信息置为已读
     * @param $id
     * @param $user
     * @return array
     */
    public function read($id, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            //查询抄送数据
            $cc_info = WorkflowCarbonCopyRepository::getInstance()->getInfoById($id);
            //抄送人是否是当前登录人
            if (empty($cc_info)) {
                throw new ValidationException(static::$t->_('workflow_cc_info_empty'), ErrCode::$VALIDATE_ERROR);
            }
            if ($cc_info->cc_staff_info_id != $user['id']) {
                throw new ValidationException(static::$t->_('workflow_cc_staff_not_login_user'), ErrCode::$VALIDATE_ERROR);
            }
            //已读状态不用再修改了
            if ($cc_info->status != OAWorkflowEnums::WORKFLOW_CC_STATUS_READ) {
                //修改为已读
                $cc_info->status = OAWorkflowEnums::WORKFLOW_CC_STATUS_READ;
                if ($cc_info->save() == false) {
                    throw new BusinessException('抄送已读失败, id=' . $id . '; 可能的原因是' . get_data_object_error_msg($cc_info), ErrCode::$BUSINESS_ERROR);
                }
            }
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = ErrCode::$BUSINESS_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
            $this->logger->warning('抄送管理-我的抄送-抄送已读失败: ' . $real_message);
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
            $this->logger->warning('抄送管理-我的抄送-抄送已读失败: ' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 格式化摘要
     * @param $summary
     * @return array
     */
    public function getSummaryFormat($summary)
    {
        $format_data = [];
        $summary = json_decode($summary, true);
        if (empty($summary)) {
            return $format_data;
        }
        foreach ($summary as $k => $v) {
            $format_data[] = [
                'label' => self::$t[$k],
                'value' => $v
            ];
        }
        return $format_data;
    }
}