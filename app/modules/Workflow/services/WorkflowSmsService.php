<?php


namespace App\Modules\Workflow\Services;

use App\Library\ApiClient;
use App\Library\BaseService;
use App\Library\Enums;
use App\Library\Enums\SysConfigEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Common\Services\SmsService;
use App\Modules\Common\Services\StaffLangService;
use App\Modules\OrdinaryPayment\Models\OrdinaryPayment;
use App\Modules\Payment\Models\PaymentStoreRenting;
use App\Modules\Purchase\Models\PurchasePayment;
use App\Modules\Reimbursement\Models\Reimbursement;
use App\Repository\HrStaffRepository;
use App\Repository\oa\WorkflowRepository;
use Exception;

class WorkflowSmsService extends BaseService
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    /**
     * 获取 WorkflowSmsService 实例
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 发送短信
     * @param object $request 审批流申请数据
     * @param int $type 发送场景
     */
    public function sendSms($request, $type)
    {
        try {
            $this->logger->info('workflow_reject_send_sms , request_id=' . ($request->id ?? 0) . '; request_name=' . ($request->name ?? ''));

            //当前模块是否需要发送驳回短信
            $workflow_info = WorkflowRepository::getInstance()->getWorkflowById($request->flow_id);
            if (!empty($workflow_info) && $workflow_info['send_submitter_reject_sms'] == SysConfigEnums::SEND_SUBMITTER_REJECT_SMS_YES) {
                //获取申请人手机号
                $staff_info = (new HrStaffRepository())->getStaffById($request->create_staff_id);
                $this->logger->info('workflow_reject_send_sms , 申请人信息=' . json_encode($staff_info, JSON_UNESCAPED_UNICODE));

                if (!empty($staff_info['mobile'])) {
                    $sms_params = [];

                    // 固定使用英文翻译
                    self::setLanguage(static::$default_language);

                    //设置短信内容
                    $module_name = static::$t->_($workflow_info['module_name_key']);
                    $biz_no = preg_replace('/[^0-9a-zA-Z\-]+/', '', $request->name);

                    $sms_params['msg'] = static::$t->_('workflow_reject_send_sms_content', [
                        'module_name' => $module_name,
                        'biz_no' => $biz_no,
                        'country_code' => get_country_code()
                    ]);

                    //nation: 使用默认值：域名指向的国家

                    //电话号码
                    if (in_array(get_runtime_env(), ['dev', 'training'])) {
                        $sms_params['mobile'] = EnumsService::getInstance()->getSettingEnvValue('workflow_reject_send_sms_test_phone');
                    } else {
                        $sms_params['mobile'] = $staff_info['mobile'];
                    }
                    //符合条件 viber 发送
                    if (get_country_code() == GlobalEnums::PH_COUNTRY_CODE) {
                        $sms_params['type']             = 0;
                        $sms_params['service_provider'] = 9;// 服务商 Viber 固定值
                        $sms_params['nation']           = GlobalEnums::PH_COUNTRY_CODE;
                    }
                    //发送短信
                    SmsService::getInstance()->send($sms_params, 'oa_workflow_reject_send_sms');
                }
            }
        } catch (Exception $e) {
            $this->logger->warning("workflowSendSms 发送异常！[type:{$type}] 原因可能是：" . $e->getMessage() . ';trace=' . $e->getTraceAsString());
        }
    }

    /**
     * 根据业务数据类型获取模型对象
     * @param int $biz_type 业务数据类型
     * @return string
     * @throws ValidationException
     */
    public function getModelClass($biz_type)
    {
        switch ($biz_type) {
            case Enums::WF_PURCHASE_PAYMENT://采购付款
                $class = PurchasePayment::class;
                break;
            case Enums::WF_REIMBURSEMENT_TYPE://报销
                $class = Reimbursement::class;
                break;
            case Enums::WF_PAYMENT_STORE_RENTING_TYPE://租房付款
                $class = PaymentStoreRenting::class;
                break;
            case Enums::ORDINARY_PAYMENT_BIZ_TYPE://普通付款
                $class = OrdinaryPayment::class;
                break;
            default:
                $class = '';
                break;
        }
        if (empty($class)) {
            throw new ValidationException('class not found');
        }
        return $class;
    }

    /**
     * 给申请人/发起人发送审批通过待支付BY消息提醒
     * @param array $list 待支付提醒列表
     * @param array $biz_type_module_name 模块key
     * @return array
     */
    public function sendWaitPayMsg($list, $biz_type_module_name)
    {
        $send_staff = [];
        if (empty($list)) {
            return $send_staff;
        }
        //获取员工信息组
        $create_staff_ids = array_values(array_unique(array_column($list, 'create_staff_id')));
        $staff_list = (new HrStaffRepository())->getStaffListByStaffIds($create_staff_ids);

        //正文需要根据员工登陆BY的语言进行发送，泰语发送泰语，中文发送中文，非中文和泰语，默认发送英文。
        $country = get_country_code();
        $default_language = $country == GlobalEnums::TH_COUNTRY_CODE ? 'th' : 'en';

        //开始给待支付员工发送站内信
        $send_staff = [];
        foreach ($list as $item) {
            //员工信息不存在，非在职、待离职无需发送
            $one_staff_info = $staff_list[$item['create_staff_id']] ?? [];
            if (empty($one_staff_info) || $one_staff_info['state'] != StaffInfoEnums::STAFF_STATE_IN) {
                continue;
            }

            //获取用户上次登录的语言环境
            $staff_language = StaffLangService::getInstance()->getLatestMobileLang($item['create_staff_id'], $default_language);
            //若不是中英泰,就用英
            $staff_language = strtolower($staff_language);
            if ($staff_language == 'zh-cn') {
                $staff_language = 'zh';
            }
            if (!in_array($staff_language, ['zh', 'th', 'en'])) {
                $staff_language = 'en';
            }
            //中英泰翻译
            $translation = $this->getTranslation($staff_language);

            //站内信消息体
            $message_info['staff_info_id'] = $item['create_staff_id'];
            $message_info['message_title'] = ($country == GlobalEnums::TH_COUNTRY_CODE) ? 'แจ้งเตือนการส่งเอกสาร 单据提交提醒' : 'Document submission reminder 单据提交提醒';
            $message_info['category'] = 121;

            //审批单号
            preg_match('/[a-zA-Z\d]+/', $item['name'], $match);
            $message_info['message_content'] = $translation->_('send_submitter_wait_pay_msg.' . strtolower($country), [
                'staff_info_id' => $item['create_staff_id'],
                'staff_name' => $one_staff_info['name'],
                'apply_date' => substr($item['created_at'], 0, 10),
                'biz_type_module_name' => $translation->_($biz_type_module_name[$item['biz_type']]),
                'no' => $match[0] ?? '',
                'approve_date' => substr($item['approved_at'], 0, 10)
            ]);
            $message_info['message_content'] = addslashes("<div style='font-size: 40px'>" . $message_info['message_content'] . '</div>');
            $this->sendMessage($message_info);
            $send_staff[] = $item['create_staff_id'];
        }
        return $send_staff;
    }

    /**
     * 发送by站内信
     * @param array $message_info 站内信信息组
     * @return bool
     */
    public function sendMessage($message_info)
    {
        $this->logger->info('workflow_send_message_request: ' . json_encode(['message' => $message_info, 'date' => date('Y-m-d H:i:s')], JSON_UNESCAPED_UNICODE));
        try {
            $kit_param = [];
            $kit_param['staff_users'] = [['id' => $message_info['staff_info_id']]];
            $kit_param['message_title'] = $message_info['message_title'];
            $kit_param['message_content'] = $message_info['message_content'];
            $kit_param['category'] = $message_info['category'];

            $bi_rpc = (new ApiClient('hcm_rpc', '', 'add_kit_message'));
            $bi_rpc->setParams([$kit_param]);
            $res = $bi_rpc->execute();
            if (isset($res['result']['code']) && ($res['result']['code'] == ErrCode::$SUCCESS)) {
                return true;
            }
            $this->logger->warning('workflow_send_message_response: ' . json_encode($res, JSON_UNESCAPED_UNICODE));
        } catch (\Exception $e) {
            $this->logger->warning('workflow_send_message_error: ' . $e->getMessage() . PHP_EOL);
        }
        return false;
    }

    /**
     * 给申请人/发起人发送审批通过待支付BY消息提醒
     * @param $list
     * @param $biz_type_module_name
     * @return array
     */
    public function sendReimbursementPendingFillWaitPayMsg($list, $biz_type_module_name)
    {
        $send_staff = [];
        if (empty($list)) {
            return $send_staff;
        }
        //获取员工信息组
        $create_staff_ids = array_values(array_unique(array_column($list, 'create_staff_id')));
        $staff_list       = (new HrStaffRepository())->getStaffListByStaffIds($create_staff_ids);

        //正文需要根据员工登陆BY的语言进行发送，泰语发送泰语，中文发送中文，非中文和泰语，默认发送英文。
        $country          = get_country_code();
        $default_language = $country == GlobalEnums::TH_COUNTRY_CODE ? 'th' : 'en';

        //开始给待支付员工发送站内信
        $send_staff = [];
        foreach ($list as $item) {
            //员工信息不存在，非在职、待离职无需发送
            $one_staff_info = $staff_list[$item['create_staff_id']] ?? [];
            if (empty($one_staff_info) || $one_staff_info['state'] != StaffInfoEnums::STAFF_STATE_IN) {
                continue;
            }

            //获取用户上次登录的语言环境
            $staff_language = StaffLangService::getInstance()->getLatestMobileLang($item['create_staff_id'],
                $default_language);
            //若不是中英泰,就用英
            $staff_language = strtolower($staff_language);
            if ($staff_language == 'zh-cn') {
                $staff_language = 'zh';
            }
            if (!in_array($staff_language, ['zh', 'th', 'en'])) {
                $staff_language = 'en';
            }
            //中英泰翻译
            $translation = $this->getTranslation($staff_language);

            //站内信消息体
            $message_info['staff_info_id'] = $item['create_staff_id'];
            $message_info['message_title'] = ($country == GlobalEnums::TH_COUNTRY_CODE) ? 'แจ้งเตือนยื่นเอกสารไม่ครบถ้วน 单据补齐提醒' : 'Document resubmission reminder 单据补齐提醒';
            $message_info['category']      = 121;

            //审批单号
            preg_match('/[a-zA-Z\d]+/', $item['name'], $match);
            $message_info['message_content'] = $translation->_('send_submitter_wait_fill_msg.' . strtolower($country), [
                'staff_info_id'        => $item['create_staff_id'],
                'staff_name'           => $one_staff_info['name'],
                'apply_date'           => substr($item['created_at'], 0, 10),
                'biz_type_module_name' => $translation->_($biz_type_module_name[$item['biz_type']]),
                'no'                   => $match[0] ?? '',
                'approve_date'         => substr($item['approved_at'], 0, 10),
            ]);
            $message_info['message_content'] = addslashes("<div style='font-size: 40px'>" . $message_info['message_content'] . '</div>');
            $this->sendMessage($message_info);
            $send_staff[] = $item['create_staff_id'];
        }
        return $send_staff;
    }
}