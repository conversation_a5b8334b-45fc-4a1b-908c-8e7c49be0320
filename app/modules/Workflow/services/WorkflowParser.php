<?php


namespace App\Modules\Workflow\Services;


use App\Library\BaseService;
use App\Library\Enums;
use App\Modules\Workflow\Models\WorkflowModel;
use App\Modules\Workflow\Models\WorkflowNodeModel;

class WorkflowParser extends BaseService
{
    /**
     * @var \Phalcon\Mvc\Model
     */
    private $flow;

    /**
     * @var \Phalcon\Mvc\Model\ResultsetInterface
     */
    private $nodes;

    /**
     * @var array
     */
    private $allPath=[];

    /**
     * @var array
     */
    private $auditPath=[];

    /**
     * @var array
     */
    private $flagPath = [];

    public function __construct($flowId)
    {
        $this->flow = $this->getFlow($flowId);
        $this->nodes = $this->getFlowNodes($flowId);
    }

    /**
     * @param $flowId
     * @return array
     */
    public function parse()
    {
        $startNode = $this->getStartNode();

        $this->findAllPath($startNode);
        $this->findAuditPath();
    }

    /**
     * @return WorkflowNodeModel
     */
    public function getStartNode()
    {
        foreach ($this->nodes as $node){
            if ($node->type == Enums::WF_NODE_APPLY){
                return $node;
            }
        }
    }

    /**
     * 获取审批流
     * @param $flowId
     * @return \Phalcon\Mvc\Model
     */
    public function getFlow($flowId)
    {
        return WorkflowModel::findFirst($flowId);
    }

    /**
     * 获取审批流全部节点
     * @param $flowId
     * @return \Phalcon\Mvc\Model\ResultsetInterface
     */
    public function getFlowNodes($flowId)
    {
        return WorkflowNodeModel::find(
            [
                'conditions' => 'flow_id = ?1',
                'bind' => [
                    1 => $flowId,
                ]
            ]
        );
    }

    /**
     * @param $nodeId
     * @return mixed
     */
    public function getNode($nodeId)
    {
        foreach ($this->nodes as $node){
            if ($node->id == $nodeId){
                return $node;
            }
        }
    }

    /**
     * @param WorkflowNodeModel $startNode
     * @return void
     */
    public function findAllPath($startNode)
    {
        if ($startNode === null) {
            return;
        }
        array_push($this->flagPath, $startNode->toArray());

        if ($startNode->getApproveNextNodeId() == null && $startNode->getRejectNextNodeId() == null){
            $this->allPath[] = $this->flagPath;
        }
        //递归查询路径
        $this->findAllPath($this->getNode($startNode->getApproveNextNodeId()));
        $this->findAllPath($this->getNode($startNode->getRejectNextNodeId()));

        array_pop($this->flagPath);

    }

    /**
     *
     */
    public function findAuditPath()
    {
        foreach ($this->allPath as $path){
            $endNode = end($path);
            if ($endNode['type'] == Enums::WF_NODE_APPROVED){
                $this->auditPath[] = $path;
            }
        }

    }


    /**
     * 获取审批流基本信息和审批流程信息
     * @return array
     */
    public function getFlowInfo()
    {
        if ($this->flow){
            $flowInfo =  $this->flow->toArray();
            $flowInfo['paths'] = $this->auditPath;
        }else{
            $flowInfo = [];
        }
        return $flowInfo;
    }

}