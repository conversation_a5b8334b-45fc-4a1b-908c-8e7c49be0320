<?php


namespace App\Modules\Workflow\Services;


class SubmitterFormulaService extends WorkflowFormulaService
{
    /**
     * 申请人所在部门
     * @var int
     */
    private $department_id;

    /**
     * 申请人职位
     * @var array
     */
    private $position_id;

    /**
     * 申请人职级
     * @var int
     */
    private $job_title_grade;

    /**
     * 申请人性别
     * @var int
     */
    private $sex;

    /**
     * 申请人编制
     * @var int
     */
    private $formal;

    /**
     * 是否在试用期
     * @var int
     */
    private $probation;

    /**
     * 国籍
     * @var int
     */
    private $nationality;

    /**
     * 在职状态
     * @var int
     */
    private $state;

    /**
     * 角色
     * @var array
     */
    private $roles;

    /**
     * 网点类型
     * @var int
     */
    private $store_category;

    /**
     * 申请人所属网点
     * @var string | int
     */
    private $store_id;

    /**
     * SubmitterFormulaService constructor.
     */
    public function __construct(array $staffInfo)
    {
        $this->setDepartmentId($staffInfo['department_id']);
        $this->setPositionId($staffInfo['position_id']);
        $this->setJobTitleGrade($staffInfo['job_title_grade']);
        $this->setSex($staffInfo['sex']);
        $this->setState($staffInfo['state']);
        $this->setProbation($staffInfo['probation']);
        $this->setRoles($staffInfo['roles']);
        $this->setNationality($staffInfo['nationality']);
        $this->setStoreCategory($staffInfo['store_category']);
        $this->setStoreId($staffInfo['store_id']);
        $this->setFormal($staffInfo['formal']);
    }

    /**
     * @return mixed
     */
    public function getDepartmentId()
    {
        return $this->department_id;
    }

    /**
     * @param mixed $department_id
     */
    public function setDepartmentId($department_id): void
    {
        $this->department_id = $department_id;
    }

    /**
     * @return mixed
     */
    public function getPositionId()
    {
        return $this->position_id;
    }

    /**
     * @param mixed $position_id
     */
    public function setPositionId($position_id): void
    {
        $this->position_id = $position_id;
    }

    /**
     * @return mixed
     */
    public function getJobTitleGrade()
    {
        return $this->job_title_grade;
    }

    /**
     * @param mixed $job_title_grade
     */
    public function setJobTitleGrade($job_title_grade): void
    {
        $this->job_title_grade = $job_title_grade;
    }

    /**
     * @return int
     */
    public function getSex(): int
    {
        return $this->sex;
    }

    /**
     * @param int $sex
     */
    public function setSex(int $sex): void
    {
        $this->sex = $sex;
    }

    /**
     * @return int
     */
    public function getFormal(): int
    {
        return $this->formal;
    }

    /**
     * @param int $formal
     */
    public function setFormal(int $formal): void
    {
        $this->formal = $formal;
    }

    /**
     * @return int
     */
    public function getProbation(): int
    {
        return $this->probation;
    }

    /**
     * @param int $probation
     */
    public function setProbation(int $probation): void
    {
        $this->probation = $probation;
    }

    /**
     * @return mixed
     */
    public function getNationality()
    {
        return $this->nationality;
    }

    /**
     * @param mixed $nationality
     */
    public function setNationality($nationality): void
    {
        $this->nationality = $nationality;
    }

    /**
     * @return int
     */
    public function getState(): int
    {
        return $this->state;
    }

    /**
     * @param int $state
     */
    public function setState(int $state): void
    {
        $this->state = $state;
    }

    /**
     * @return array
     */
    public function getRoles(): array
    {
        return $this->roles;
    }

    /**
     * @param array $roles
     */
    public function setRoles(array $roles): void
    {
        $this->roles = $roles;
    }

    /**
     * @return int
     */
    public function getStoreCategory(): int
    {
        return $this->store_category;
    }

    /**
     * @param int $store_category
     */
    public function setStoreCategory(int $store_category): void
    {
        $this->store_category = $store_category;
    }

    /**
     * @return int|string
     */
    public function getStoreId()
    {
        return $this->store_id;
    }

    /**
     * @param int|string $store_id
     */
    public function setStoreId($store_id): void
    {
        $this->store_id = $store_id;
    }
}