<?php

namespace App\Modules\Workflow\Services;

use App\Library\ApiClient;
use App\Library\BaseService;
use App\Library\Enums;
use App\Library\Enums\BankFlowEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Models\oa\ChequeApplyModel;
use App\Models\oa\ContractPlatFormModel;
use App\Models\oa\LoanReturnModel;
use App\Models\oa\WorkflowRequestNodeCommentModel;
use App\Modules\BankFlow\Services\PayFlowService as BankFlowPayFlowService;
use App\Modules\Budget\Models\BudgetAdjustModel;
use App\Modules\Budget\Models\BudgetSourceMainModel;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Common\Services\StaffLangService;
use App\Modules\Contract\Models\Contract;
use App\Modules\Contract\Models\ContractStoreRentingModel;
use App\Modules\Deposit\Models\DepositReturnModel;
use App\Modules\Deposit\Models\DepositModel;
use App\Modules\Loan\Models\Loan;
use App\Modules\Pay\Models\Payment;
use App\Modules\Payment\Models\PaymentStoreRenting;
use App\Modules\Purchase\Models\PurchaseAcceptanceModel;
use App\Modules\Purchase\Models\PurchaseApply;
use App\Modules\Purchase\Models\PurchaseOrder;
use App\Modules\Purchase\Models\PurchasePayment;
use App\Modules\Purchase\Models\PurchaseSampleModel;
use App\Modules\Purchase\Models\Vendor;
use App\Modules\Reimbursement\Models\Reimbursement;
use App\Modules\ReserveFund\Models\ReserveFundApply;
use App\Modules\ReserveFund\Models\ReserveFundReturn;
use App\Modules\Salary\Models\PaySalaryApply;
use App\Modules\Setting\Services\WorkflowPayProgressMarkService;
use App\Modules\User\Models\StaffInfoModel;
use App\Modules\User\Services\UserService;
use App\Modules\Wages\Models\WagesModel;
use App\Modules\Workflow\Models\WorkflowNodeModel;
use App\Modules\Workflow\Models\WorkflowRequestNodeFYR;
use App\Modules\OrdinaryPayment\Models\OrdinaryPayment;
use App\Repository\HrStaffRepository;
use Exception;
use Phalcon\Mvc\Model;

class WorkflowCommentService extends BaseService
{
    // 提交评论公共验证规则
    public static $submit_common_validation = [
        'id' => 'Required|IntGe:1|>>>:params error[id]',
        'content' => 'Required|StrLenGeLe:1,1000|>>>:params error[1-1000 characters]',
    ];

    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 提交评论参数校验
     *
     * @param array $params
     * @param array $validation
     * @throws ValidationException
     */
    public function submitValidation(array $params, array $validation = [])
    {
        $validation = array_merge(static::$submit_common_validation, $validation);
        Validation::validate($params, $validation);
    }

    /**
     * 获取审批节点评论列表
     *
     * @param $request_id
     * @param $node_id
     * @param $sub_node_id
     * @return array
     */
    public function getLogsByNodeId($request_id, $node_id, $sub_node_id = 0)
    {
        $list = WorkflowRequestNodeCommentModel::find([
            'conditions' => 'request_id = :request_id: AND node_id = :node_id: AND sub_node_id = :sub_node_id:',
            'bind' => [
                'request_id' => $request_id,
                'node_id' => $node_id,
                'sub_node_id' => $sub_node_id
            ],
            'order' => 'id DESC'
        ]);

        static $progress_mark_list = null;
        if (is_null($progress_mark_list)) {
            $progress_mark_list = WorkflowPayProgressMarkService::getInstance()->getEnumsList(2);
            $progress_mark_list = array_column($progress_mark_list, 'label', 'value');
        }

        $logs = [];
        foreach ($list as $item) {
            // 进度标记
            $progress_mark_name = '';
            if (isset($progress_mark_list[$item->progress_mark_id])) {
                $progress_mark_name = static::$t->_('workflow_comment_progress_mark_title') . ': ' . $progress_mark_list[$item->progress_mark_id];
            }

            $logs[] = [
                'staff_id' => $item->staff_id,
                'staff_name' => $item->staff_name,
                'staff_department' => $item->staff_department,
                'staff_job_title' => $item->staff_job_title,
                'content' => $item->content,
                'progress_mark' => $progress_mark_name,
                'created_at' => $item->created_at
            ];
        }

        return $logs;
    }

    /**
     * 添加评论
     *
     * @param object $request
     * @param array $params
     * @param array $user
     * @param bool $is_send_notice
     * @return mixed
     * @throws BusinessException
     * @throws ValidationException
     */
    public function addComment(object $request, array $params, array $user, bool $is_send_notice = true)
    {
        // 审批流不存在
        if (empty($request)) {
            throw new ValidationException(static::$t->_('workflow_add_comment_error_001', ['biz_id' => $params['id']]), ErrCode::$VALIDATE_ERROR);
        }

        // 审批流是否待审批状态
        if ($request->state != Enums::WF_STATE_PENDING) {
            throw new ValidationException(static::$t->_('workflow_add_comment_error_002', ['biz_id' => $params['id']]), ErrCode::$VALIDATE_ERROR);
        }

        // 评论人非待审批人
        if (!in_array($user['id'], array_filter(explode(',', $request->current_node_auditor_id)))) {
            throw new ValidationException(static::$t->_('workflow_add_comment_error_003', ['biz_id' => $params['id']]), ErrCode::$VALIDATE_ERROR);
        }

        // 获取当前审批人所在的待审批节点
        $current_node_info = $this->getCommentNodeInfo($request, $user['id']);

        // 添加
        $comment_model = new WorkflowRequestNodeCommentModel();
        $comment_model->request_id = $request->id;
        $comment_model->flow_id = $request->flow_id;
        $comment_model->node_id = $request->current_flow_node_id;
        $comment_model->sub_node_id = $current_node_info['sub_node_id'];
        $comment_model->staff_id = $user['id'];
        $comment_model->staff_name = $this->getNameAndNickName($user['name'], $user['nick_name']);
        $comment_model->staff_department = $user['department'];
        $comment_model->staff_job_title = $user['job_title'];
        $comment_model->content = $params['content'];
        $comment_model->progress_mark_id = $params['progress_mark_id'];
        $comment_model->created_at = date('Y-m-d H:i:s');

        $this->logger->info('添加的评论:' . json_encode($comment_model->toArray(), JSON_UNESCAPED_UNICODE));
        if ($comment_model->save() === false) {
            throw new BusinessException('审批流评论添加失败,原因可能是: ' . get_data_object_error_msg($comment_model), ErrCode::$BUSINESS_ERROR);
        }

//            $this->sendEmailToStaffs($request, $staff_ids, GlobalEnums::CONSULTED_ACTION_TYPE, $user);

        return true;
    }

    /**
     * 评论后给申请人发消息通知(站内信 + 邮件)
     *
     * @param int $biz_type
     * @param int $biz_value
     * @param string $comment_content
     * @param array $user 评论人
     */
    public function sendCommentNotice(int $biz_type, int $biz_value, string $comment_content = '', array $user = [])
    {
        try {
            if (empty($biz_type) || empty($biz_value)) {
                throw new ValidationException("biz_type[{$biz_type}] OR biz_value[{$biz_value}] is null", ErrCode::$VALIDATE_ERROR);
            }

            // 邮件模板语言定义
            $th_lang = self::getTranslation('th');
            $zh_lang = self::getTranslation('zh');
            $en_lang = self::getTranslation('en');

            // 收信人信息
            $to_staff_id = '';
            $biz_no = '';
            $apply_date = '';
            $th_module_name = '';
            $zh_module_name = '';
            $en_module_name = '';

            switch ($biz_type) {
                // 报销
                case Enums::WF_REIMBURSEMENT_TYPE:
                    $item = Reimbursement::findFirst([
                        'conditions' => 'id = :id:',
                        'bind' => ['id' => $biz_value]
                    ]);
                    if (empty($item)) {
                        throw new BusinessException("no item, type = {$biz_type}, id = {$biz_value}", ErrCode::$BUSINESS_ERROR);
                    }

                    $to_staff_id = $item->created_id;
                    $apply_date = $item->apply_date;
                    $biz_no = $item->no;
                    $th_module_name = $th_lang->_('sys_module_reimbursement');
                    $zh_module_name = $zh_lang->_('sys_module_reimbursement');
                    $en_module_name = $en_lang->_('sys_module_reimbursement');
                    break;

                // 普通付款
                case Enums::ORDINARY_PAYMENT_BIZ_TYPE:
                    $item = OrdinaryPayment::findFirst([
                        'conditions' => 'id = :id:',
                        'bind' => ['id' => $biz_value]
                    ]);
                    if (empty($item)) {
                        throw new BusinessException("no item, type = {$biz_type}, id = {$biz_value}", ErrCode::$BUSINESS_ERROR);
                    }

                    $to_staff_id = $item->create_id;
                    $apply_date = date('Y-m-d', strtotime($item->created_at));
                    $biz_no = $item->apply_no;
                    $th_module_name = $th_lang->_('sys_module_ordinary_payment');
                    $zh_module_name = $zh_lang->_('sys_module_ordinary_payment');
                    $en_module_name = $en_lang->_('sys_module_ordinary_payment');
                    break;

                // 网点租房付款
                case Enums::WF_PAYMENT_STORE_RENTING_TYPE:
                    $item = PaymentStoreRenting::findFirst([
                        'conditions' => 'id = :id:',
                        'bind' => ['id' => $biz_value]
                    ]);
                    if (empty($item)) {
                        throw new BusinessException("no item, type = {$biz_type}, id = {$biz_value}", ErrCode::$BUSINESS_ERROR);
                    }

                    $to_staff_id = $item->create_id;
                    $apply_date = $item->create_date;
                    $biz_no = $item->apply_no;
                    $th_module_name = $th_lang->_('sys_module_payment');
                    $zh_module_name = $zh_lang->_('sys_module_payment');
                    $en_module_name = $en_lang->_('sys_module_payment');
                    break;

                // 借款
                case Enums::WF_LOAN_TYPE:
                    $item = Loan::findFirst([
                        'conditions' => 'id = :id:',
                        'bind' => ['id' => $biz_value]
                    ]);
                    if (empty($item)) {
                        throw new BusinessException("no item, type = {$biz_type}, id = {$biz_value}", ErrCode::$BUSINESS_ERROR);
                    }

                    $to_staff_id = $item->create_id;
                    $apply_date = $item->create_date;
                    $biz_no = $item->lno;
                    $th_module_name = $th_lang->_('sys_module_loan');
                    $zh_module_name = $zh_lang->_('sys_module_loan');
                    $en_module_name = $en_lang->_('sys_module_loan');
                    break;

                // 借款归还
                case Enums::WF_LOAN_BACK_TYPE:
                    $item = LoanReturnModel::findFirst([
                        'conditions' => 'id = :id:',
                        'bind' => ['id' => $biz_value]
                    ]);
                    if (empty($item)) {
                        throw new BusinessException("no item, type = {$biz_type}, id = {$biz_value}", ErrCode::$BUSINESS_ERROR);
                    }
                    $loan_model = Loan::findFirst([
                        'id = :id:',
                        'bind' => ['id' => $item->loan_id],
                        'columns' => ['lno']
                    ]);

                    $to_staff_id = $loan_model->create_id;
                    $apply_date = $item->back_apply_date;
                    $biz_no = $loan_model->lno;
                    $th_module_name = $th_lang->_('loan_back_name');
                    $zh_module_name = $zh_lang->_('loan_back_name');
                    $en_module_name = $en_lang->_('loan_back_name');
                    break;

                case Enums::WF_WAGES_TYPE:
                    $item = WagesModel::findFirst([
                        'conditions' => 'id = :id:',
                        'bind' => ['id' => $biz_value]
                    ]);
                    if (empty($item)) {
                        throw new BusinessException("no item, type = {$biz_type}, id = {$biz_value}", ErrCode::$BUSINESS_ERROR);
                    }

                    $to_staff_id = $item->apply_id;
                    $apply_date = $item->apply_date;
                    $biz_no = $item->no;
                    $th_module_name = $th_lang->_('email_wages_name');
                    $zh_module_name = $zh_lang->_('email_wages_name');
                    $en_module_name = $en_lang->_('email_wages_name');
                    break;

                case Enums::WF_SALARY_APPLY:
                    $item = PaySalaryApply::findFirst([
                        'conditions' => 'id = :id:',
                        'bind' => ['id' => $biz_value]
                    ]);
                    if (empty($item)) {
                        throw new BusinessException("no item, type = {$biz_type}, id = {$biz_value}", ErrCode::$BUSINESS_ERROR);
                    }

                    $to_staff_id = $item->create_id;
                    $apply_date = $item->apply_date;
                    $biz_no = $item->xzno;
                    $th_module_name = $th_lang->_('pay_salary_apply_name');
                    $zh_module_name = $zh_lang->_('pay_salary_apply_name');
                    $en_module_name = $en_lang->_('pay_salary_apply_name');
                    break;

                // 采购申请单
                case Enums::WF_PURCHASE_APPLY:
                    $item = PurchaseApply::findFirst([
                        'conditions' => 'id = :id:',
                        'bind' => ['id' => $biz_value]
                    ]);
                    if (empty($item)) {
                        throw new BusinessException("no item, type = {$biz_type}, id = {$biz_value}", ErrCode::$BUSINESS_ERROR);
                    }

                    $to_staff_id = $item->create_id;
                    $apply_date = $item->apply_date;
                    $biz_no = $item->pano;
                    $th_module_name = $th_lang->_('purchase_apply_name');
                    $zh_module_name = $zh_lang->_('purchase_apply_name');
                    $en_module_name = $en_lang->_('purchase_apply_name');
                    break;

                // 采购订单
                case Enums::WF_PURCHASE_ORDER:
                    $item = PurchaseOrder::findFirst([
                        'conditions' => 'id = :id:',
                        'bind' => ['id' => $biz_value]
                    ]);
                    if (empty($item)) {
                        throw new BusinessException("no item, type = {$biz_type}, id = {$biz_value}", ErrCode::$BUSINESS_ERROR);
                    }

                    $to_staff_id = $item->create_id;
                    $apply_date = $item->apply_date;
                    $biz_no = $item->pono;
                    $th_module_name = $th_lang->_('purchase_order_name');
                    $zh_module_name = $zh_lang->_('purchase_order_name');
                    $en_module_name = $en_lang->_('purchase_order_name');
                    break;

                // 采购付款申请单
                case Enums::WF_PURCHASE_PAYMENT:
                    $item = PurchasePayment::findFirst([
                        'conditions' => 'id = :id:',
                        'bind' => ['id' => $biz_value]
                    ]);
                    if (empty($item)) {
                        throw new BusinessException("no item, type = {$biz_type}, id = {$biz_value}", ErrCode::$BUSINESS_ERROR);
                    }

                    $to_staff_id = $item->create_id;
                    $apply_date = $item->apply_date;
                    $biz_no = $item->ppno;
                    $th_module_name = $th_lang->_('purchase_payment_name');
                    $zh_module_name = $zh_lang->_('purchase_payment_name');
                    $en_module_name = $en_lang->_('purchase_payment_name');
                    break;

                // 供应商征询(信息 和 等级)
                case Enums::WF_VENDOR_BIZ_TYPE:
                case Enums::WF_VENDOR_GRADE_BIZ_TYPE:
                    $item = Vendor::findFirst([
                        'conditions' => 'id = :id:',
                        'bind' => ['id' => $biz_value]
                    ]);
                    if (empty($item)) {
                        throw new BusinessException("no item, type = {$biz_type}, id = {$biz_value}", ErrCode::$BUSINESS_ERROR);
                    }

                    $to_staff_id = $item->create_id;
                    $apply_date = date('Y-m-d', strtotime($item->created_at));
                    $biz_no = $item->vendor_id;
                    $th_module_name = $th_lang->_('sys_module_vendor');
                    $zh_module_name = $zh_lang->_('sys_module_vendor');
                    $en_module_name = $en_lang->_('sys_module_vendor');
                    break;

                // 网点备用金申请
                case Enums::WF_RESERVE_FUND_APPLY:
                    $item = ReserveFundApply::findFirst([
                        'conditions' => 'id = :id:',
                        'bind' => ['id' => $biz_value]
                    ]);
                    if (empty($item)) {
                        throw new BusinessException("no item, type = {$biz_type}, id = {$biz_value}", ErrCode::$BUSINESS_ERROR);
                    }

                    $to_staff_id = $item->create_id;
                    $apply_date = $item->apply_date;
                    $biz_no = $item->rfano;
                    $th_module_name = $th_lang->_('workflow_business_reserve_apply');
                    $zh_module_name = $zh_lang->_('workflow_business_reserve_apply');
                    $en_module_name = $en_lang->_('workflow_business_reserve_apply');
                    break;

                // 网点备用金归还
                case Enums::WF_RESERVE_FUND_RETURN:
                    $item = ReserveFundReturn::findFirst([
                        'conditions' => 'id = :id:',
                        'bind' => ['id' => $biz_value]
                    ]);
                    if (empty($item)) {
                        throw new BusinessException("no item, type = {$biz_type}, id = {$biz_value}", ErrCode::$BUSINESS_ERROR);
                    }

                    $to_staff_id = $item->create_id;
                    $apply_date = date('Y-m-d', strtotime($item->created_at));
                    $biz_no = $item->rrno;
                    $th_module_name = $th_lang->_('workflow_business_reserve_return');
                    $zh_module_name = $zh_lang->_('workflow_business_reserve_return');
                    $en_module_name = $en_lang->_('workflow_business_reserve_return');
                    break;

                // 押金归还
                case Enums::DEPOSIT_RETURN_BIZ_TYPE:
                    $item = DepositReturnModel::findFirst([
                        'conditions' => 'id = :id:',
                        'bind' => ['id' => $biz_value]
                    ]);
                    if (empty($item)) {
                        throw new BusinessException("no item, type = {$biz_type}, id = {$biz_value}", ErrCode::$BUSINESS_ERROR);
                    }

                    $deposit_item = DepositModel::findFirst([
                        'conditions' => 'id = :id:',
                        'bind' => ['id' => $item->deposit_id]
                    ]);
                    if (empty($deposit_item)) {
                        throw new BusinessException("no item, type = {$biz_type}, id = {$item->deposit_id}", ErrCode::$BUSINESS_ERROR);
                    }

                    $to_staff_id = $item->create_id;
                    $apply_date = $item->return_date;
                    $biz_no = $deposit_item->business_no;
                    $th_module_name = $th_lang->_('sys_module_deposit');
                    $zh_module_name = $zh_lang->_('sys_module_deposit');
                    $en_module_name = $en_lang->_('sys_module_deposit');
                    break;

                // 预算导入
                case Enums::BUDGET_OB_TYPE:
                    $item = BudgetSourceMainModel::findFirst([
                        'conditions' => 'id = :id:',
                        'bind' => ['id' => $biz_value]
                    ]);
                    if (empty($item)) {
                        throw new BusinessException("no item, type = {$biz_type}, id = {$biz_value}", ErrCode::$BUSINESS_ERROR);
                    }

                    $to_staff_id = $item->create_id;
                    $apply_date = $item->apply_date;
                    $biz_no = $item->no;
                    $th_module_name = $th_lang->_('budget_object_import');
                    $zh_module_name = $zh_lang->_('budget_object_import');
                    $en_module_name = $en_lang->_('budget_object_import');
                    break;

                // 预算调整
                case Enums::WF_BUDGET_ADJUST_TYPE:
                    $item = BudgetAdjustModel::findFirst([
                        'conditions' => 'id = :id:',
                        'bind' => ['id' => $biz_value]
                    ]);
                    if (empty($item)) {
                        throw new BusinessException("no item, type = {$biz_type}, id = {$biz_value}", ErrCode::$BUSINESS_ERROR);
                    }

                    $to_staff_id = $item->apply_id;
                    $apply_date = $item->apply_date;
                    $biz_no = $item->adjust_no;
                    $th_module_name = $th_lang->_('budget_adjust_method_text');
                    $zh_module_name = $zh_lang->_('budget_adjust_method_text');
                    $en_module_name = $en_lang->_('budget_adjust_method_text');
                    break;

                // 其他合同
                case Enums::WF_CONTRACT_TYPE1:
                case Enums::WF_CONTRACT_TYPE20:
                case Enums::WF_CONTRACT_TYPE21:
                case Enums::WF_CONTRACT_TYPE22:
                case Enums::WF_CONTRACT_TYPE23:
                case Enums::WF_CONTRACT_TYPE24:
                    $item = Contract::findFirst([
                        'conditions' => 'id = :id:',
                        'bind' => ['id' => $biz_value]
                    ]);
                    if (empty($item)) {
                        throw new BusinessException("no item, type = {$biz_type}, id = {$biz_value}", ErrCode::$BUSINESS_ERROR);
                    }

                    $to_staff_id = $item->create_id;
                    $apply_date = date('Y-m-d', strtotime($item->created_at));
                    $biz_no = $item->cno;
                    $th_module_name = $th_lang->_('sys_module_other_contract');
                    $zh_module_name = $zh_lang->_('sys_module_other_contract');
                    $en_module_name = $en_lang->_('sys_module_other_contract');
                    break;

                // 网点租房合同
                case Enums::WF_STORE_RENTING_CONTRACT_TYPE:
                case Enums::WF_STORE_RENTING_CONTRACT_RENEWAL_TYPE:
                    $item = ContractStoreRentingModel::findFirst([
                        'conditions' => 'id = :id:',
                        'bind' => ['id' => $biz_value]
                    ]);
                    if (empty($item)) {
                        throw new BusinessException("no item, type = {$biz_type}, id = {$biz_value}", ErrCode::$BUSINESS_ERROR);
                    }

                    $to_staff_id = $item->manage_id;
                    $apply_date = date('Y-m-d', strtotime($item->created_at));
                    $biz_no = $item->contract_id;
                    $th_module_name = $th_lang->_('email_store_rent_contract_name');
                    $zh_module_name = $zh_lang->_('email_store_rent_contract_name');
                    $en_module_name = $en_lang->_('email_store_rent_contract_name');
                    break;
                default:
                    throw new BusinessException("type 未配置 - {$biz_type}", ErrCode::$BUSINESS_ERROR);
            }

            // 收信人
            $this->logger->info('收信人工号=' . $to_staff_id);
            if (empty($to_staff_id)) {
                throw new BusinessException('收信人工号为空', ErrCode::$BUSINESS_ERROR);
            }

            // 收信人信息
            $staff_info = (new UserService())->getLoginUser($to_staff_id);
            if (empty($staff_info)) {
                throw new BusinessException('收信人信息为空', ErrCode::$BUSINESS_ERROR);
            }

            // 收信人邮箱地址: 开发/测试环境收件人取指定配置的
            if (in_array(get_runtime_env(), ['dev', 'training'])) {
                $emails = EnumsService::getInstance()->getSettingEnvValueIds(GlobalEnums::EMAIL_NOTICE_WORKFLOW_COMMENT_CODE);
            } else {
                // 生产取工号对应: 优先企业邮箱, 为空则个人邮箱
                $emails = !empty($staff_info['email']) ? [$staff_info['email']] : [$staff_info['personal_email']];
            }

            $emails = array_filter(array_unique($emails));
            $this->logger->info('收信人邮箱=' . json_encode($emails, JSON_UNESCAPED_UNICODE));

            $country_code = get_country_code();

            // 评论人信息
            $comment_staff_info = $user['name'] . '-' . $user['id'] . '-' . $user['job_title'];

            // 1. 邮件
            if (!empty($emails)) {
                // OA 地址
                $oa_page_url = EnvModel::getEnvByCode(GlobalEnums::OA_DASHBOARD_PAGE_URL_CODE);
                $oa_page_url = !empty($oa_page_url) ? $oa_page_url : GlobalEnums::OA_DASHBOARD_PAGE_URL_DEFAULT;

                $email_content = $zh_lang->_('workflow_comment_email_notice_content', [
                    'staff_info_id' => $to_staff_id,
                    'staff_info_name' => $staff_info['name'],
                    'apply_date' => $apply_date,
                    'module_name' => $zh_module_name,
                    'oa_biz_no' => $biz_no,
                    'comment_staff_info' => $comment_staff_info,
                    'comment_content' => $comment_content,
                    'oa_sys_url' => $oa_page_url
                ]);

                if ($country_code == GlobalEnums::TH_COUNTRY_CODE) {
                    $email_title = $country_code . '--' . $th_lang->_('workflow_comment_email_notice_title');
                    $email_content .= $th_lang->_('workflow_comment_email_notice_content', [
                            'staff_info_id' => $to_staff_id,
                            'staff_info_name' => $staff_info['name'],
                            'apply_date' => $apply_date,
                            'module_name' => $th_module_name,
                            'oa_biz_no' => $biz_no,
                            'comment_staff_info' => $comment_staff_info,
                            'comment_content' => $comment_content,
                            'oa_sys_url' => $oa_page_url
                        ]);

                } else {
                    $email_title = $country_code . '--' . $en_lang->_('workflow_comment_email_notice_title');
                    $email_content .= $en_lang->_('workflow_comment_email_notice_content', [
                            'staff_info_id' => $to_staff_id,
                            'staff_info_name' => $staff_info['name'],
                            'apply_date' => $apply_date,
                            'module_name' => $en_module_name,
                            'oa_biz_no' => $biz_no,
                            'comment_staff_info' => $comment_staff_info,
                            'comment_content' => $comment_content,
                            'oa_sys_url' => $oa_page_url
                        ]);
                }
                $email_title .= ' ' . $zh_lang->_('workflow_comment_email_notice_title');

                $email_log = ['emails' => $emails, 'title' => $email_title, 'content' => $email_content];
                $send_res = $this->mailer->sendAsync($emails, $email_title, $email_content);
                if ($send_res) {
                    $this->logger->info('sendCommentNotice 邮件 入队成功！' . json_encode($email_log, JSON_UNESCAPED_UNICODE));
                } else {
                    $this->logger->notice('sendCommentNotice 邮件 入队失败！' . json_encode($email_log, JSON_UNESCAPED_UNICODE));
                }
            }

            // 2. 站内信
            if ($country_code == GlobalEnums::TH_COUNTRY_CODE) {
                $msg_title = $th_lang->_('workflow_comment_msg_notice_title') . ' ' . $zh_lang->_('workflow_comment_msg_notice_title');
            } else {
                $msg_title = $en_lang->_('workflow_comment_msg_notice_title') . ' ' . $zh_lang->_('workflow_comment_msg_notice_title');
            }

            // 最近登录BY的语言
            $staff_language = StaffLangService::getInstance()->getLatestMobileLang($to_staff_id, '');
            $this->logger->info("{$to_staff_id} 最近登录BY的语言: {$staff_language}");

            if ($staff_language == 'th') {
                $msg_content_lang = $th_lang;
                $msg_module_name = $th_module_name;
            } else if ($staff_language == 'zh') {
                $msg_content_lang = $zh_lang;
                $msg_module_name = $zh_module_name;
            } else {
                $msg_content_lang = $en_lang;
                $msg_module_name = $en_module_name;
            }
            $msg_content = $msg_content_lang->_('workflow_comment_msg_notice_content', [
                'staff_info_id' => $to_staff_id,
                'staff_info_name' => $staff_info['name'],
                'apply_date' => $apply_date,
                'module_name' => $msg_module_name,
                'oa_biz_no' => $biz_no,
                'comment_staff_info' => $comment_staff_info,
                'comment_content' => $comment_content
            ]);

            $by_msg_param = [
                'staff_users' => [['id' => $to_staff_id]],
                'message_title' => $msg_title,
                'message_content' => $msg_content,
                'category' => GlobalEnums::MESSAGE_CATEGORY_OA_WORKFLOW_COMMENT_NOTICE
            ];

            $bi_rpc = (new ApiClient('hcm_rpc', '', 'add_kit_message'));
            $bi_rpc->setParams([$by_msg_param]);
            $res = $bi_rpc->execute();
            if (isset($res['result']['code']) && ($res['result']['code'] == ErrCode::$SUCCESS)) {
                $this->logger->info('sendCommentNotice 站内信 发送成功！' . json_encode($by_msg_param, JSON_UNESCAPED_UNICODE));
            } else {
                $this->logger->notice('sendCommentNotice 站内信 发送失败！' . json_encode($by_msg_param, JSON_UNESCAPED_UNICODE));
            }

        } catch (ValidationException $e) {
            $this->logger->info('sendCommentNotice 异常, 原因可能是：' . $e->getMessage());

        } catch (BusinessException $e) {
            $this->logger->notice('sendCommentNotice 异常, 原因可能是：' . $e->getMessage());

        } catch (Exception $e) {
            $this->logger->warning('sendCommentNotice 异常, 原因可能是：' . $e->getMessage());
        }
    }

    /**
     * 获取待审批人所在节点
     *
     * @param $req
     * @param $uid
     * @return mixed
     */
    public function getCommentNodeInfo($req, $uid)
    {
        $result = [
            'node_id' => 0,
            'sub_node_id' => 0
        ];

        if (empty($req) || empty($uid) || empty($req->current_node_auditor_id) || empty($req->current_flow_node_id)) {
            return $result;
        }

        $node = WorkflowNodeModel::findFirst([
            'conditions' => 'id = :id:',
            'bind' => ['id' => $req->current_flow_node_id]
        ]);

        if (empty($node)) {
            return $result;
        }

        $result['node_id'] = $node->id;

        // 一级节点或签
        if ($node->node_audit_type == Enums::WF_NODE_AUDIT_TYPE_SUB_NODE_COUNTERSIGN) {
            // 子节点会签, 查找固化的子节点配置
            $sub_node_solidified_auditors = WorkflowServiceV2::getInstance()->getWorkflowSubNodeSolidifiedAuditors($req, $node);

            $sub_node_id = 0;
            foreach ($sub_node_solidified_auditors as $id => $sub_auditor_ids) {
                if (in_array($uid, $sub_auditor_ids)) {
                    $sub_node_id = $id;
                    break;
                }
            }

            $result['sub_node_id'] = $sub_node_id;
        }

        return $result;
    }

}
