<?php


namespace App\Modules\Workflow\Services;


interface FlowActionInterface
{

    /**
     * @param $id
     * @param $note
     * @param $user
     * @return mixed
     */
    public function approve($id, $note, $user);

    /**
     * @param $id
     * @param $note
     * @param $user
     * @return mixed
     */
    public function reject($id, $note, $user);

    /**
     * @param $id
     * @param $note
     * @param $user
     * @return mixed
     */
    public function cancel($id,$note, $user);

    /**
     * @param $id
     * @return mixed
     */
    public function getRequest($id);

    /**
     * @param $id
     * @param $user
     * @return mixed
     */
    public function createRequest($id, $user);

    /**
     * @param null $model
     * @return int
     */
    public function getFlowId($model=null);

}