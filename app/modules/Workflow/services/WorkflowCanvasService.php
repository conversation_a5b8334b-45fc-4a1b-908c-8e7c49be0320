<?php


namespace App\Modules\Workflow\Services;

use App\Library\BaseService;
use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\OAWorkflowEnums;
use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Workflow\Models\WorkflowModel;
use App\Repository\DepartmentRepository;
use App\Repository\HrJobTitleRepository;
use App\Repository\HrStaffRepository;
use App\Repository\oa\SysModuleRepository;
use App\Repository\oa\WorkflowNodeRelateRepository;
use App\Repository\oa\WorkflowNodeRepository;
use App\Repository\oa\WorkflowRepository;
use App\Repository\oa\WorkflowStaffManagerRepository;
use App\Repository\oa\WorkflowSubNodeRepository;

class WorkflowCanvasService extends BaseService
{
    public static $not_must_list = [
        'flow_id',
        'module_id',
    ];

    public static $validate_list_search = [
        'pageSize' => 'IntGt:0', //每页条数
        'pageNum' => 'IntGt:0', //页码
        'flow_id' => 'IntGt:0', //审批流id
        'module_id' => 'IntGt:0', //模块id
    ];

    public static $validate_search_workflow_name = [
        'workflow_name' => 'StrLenGeLe:1,45'
    ];

    public static $validate_workflow_detail = [
        'flow_id' => 'Required|IntGt:0'
    ];

    public static $validate_node_detail = [
        'node_id' => 'Required|IntGt:0'
    ];

    //换行
    public static $str_br = '*br*';
    //空格
    public static $str_space = '*nbsp**nbsp**nbsp**nbsp*';
    //并且 (翻译key)
    public static $str_and = 'workflow_canvas_operator_and';
    //或者 (翻译key)
    public static $str_or = 'workflow_canvas_operator_or';
    //大于 (翻译key)
    public static $greater_than = 'workflow_canvas_operator_greater_than';
    //小于 (翻译key)
    public static $less_than = 'workflow_canvas_operator_less_than';
    //等于 (翻译key)
    public static $equal_than = 'workflow_canvas_operator_equal_than';
    //不等于 (翻译key)
    public static $unequal_than = 'workflow_canvas_operator_unequal_than';
    //大于等于 (翻译key)
    public static $greater_equal = 'workflow_canvas_operator_greater_equal';
    //小于等于 (翻译key)
    public static $less_equal = 'workflow_canvas_operator_less_equal';
    //in_array函数翻译 (翻译key)  在...中 / 不在...中
    public static $in_array_prefix = 'workflow_canvas_operator_in_prefix';
    public static $not_in_array_prefix = 'workflow_canvas_operator_not_in_prefix';

    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    /**
     * 单例
     * @return WorkflowCanvasService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 获取枚举
     * @return array
     */
    public function getDefault()
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            //查询所有模块
            $module_data = SysModuleRepository::getInstance()->getModule();
            $module_items = [];
            foreach ($module_data as &$v) {
                $module_items[] = [
                    'value' => $v['id'],
                    'label' => static::$t[$v['name_key']]
                ];
            }
            //获取开启状态枚举
            $enable_status_items = [];
            $status_items_enum = OAWorkflowEnums::$workflow_enable_status_items;
            foreach ($status_items_enum as $status_k => $status_v) {
                $enable_status_items[] = [
                    'value' => $status_k,
                    'label' => static::$t[$status_v]
                ];
            }
            $data['module_items'] = $module_items;
            $data['enable_status_items'] = $enable_status_items;
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
            $this->logger->warning('审批流画布-查询枚举失败: ' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 模糊搜索审批流名称
     * @param $workflow_name
     * @return array
     */
    public function getWorkflowNameItems($workflow_name)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            //模糊查询审批流名称
            $data = WorkflowRepository::getInstance()->getWorkflowNameItems($workflow_name);
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
            $this->logger->warning('审批流画布-查询审批流名称失败: ' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 离职资产-资产部处理-列表
     * @param $params
     * @return array
     */
    public function getList($params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [
            'items' => [],
            'pagination' => [
                'current_page' => 0,
                'per_page' => 0,
                'total_count' => 0,
            ],
        ];
        try {
            $page_size = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $params['pageSize'];
            $page_num = empty($params['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $params['pageNum'];
            $offset = $page_size * ($page_num - 1);
            $data['pagination']['current_page'] = $page_num;
            $data['pagination']['per_page'] = $page_size;
            //查询总数
            $count = $this->getListCount($params);
            //查询列表
            if ($count > 0) {
                $builder = $this->modelsManager->createBuilder();
                $columns = 'id as flow_id, name as workflow_name, module_id, description as workflow_condition, enable_status';
                $builder->columns($columns);
                $builder->from(WorkflowModel::class);
                //组合搜索条件
                $builder = $this->getCondition($builder, $params);
                $builder->limit($page_size, $offset);
                $builder->orderby('id desc');
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleListItems($items);
            }
            $data['items'] = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-workflow-canvas-list-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 审批流画布列表-总数
     * @param $condition
     * @return int
     */
    public function getListCount($condition)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(WorkflowModel::class);
        $builder->columns('count(id) as count');
        //组合搜索条件
        $builder = $this->getCondition($builder, $condition);
        return (int)$builder->getQuery()->getSingleResult()->count;
    }

    /**
     * 审批流画布列表-条件拼接
     * @param $builder
     * @param $condition
     * @return  $builder
     */
    public function getCondition($builder, $condition)
    {
        $flow_id = !empty($condition['flow_id']) ? $condition['flow_id'] : 0;//审批流id
        $module_id = !empty($condition['module_id']) ? $condition['module_id'] : 0;//模块id
        $enable_status = $condition['enable_status'];//开启状态
        //审批流id
        if (!empty($flow_id)) {
            $builder->andWhere('id = :flow_id:', ['flow_id' => $flow_id]);
        }
        //模块id
        if (!empty($module_id)) {
            $builder->andWhere('module_id = :module_id:', ['module_id' => $module_id]);
        }
        //开启状态
        if (isset($enable_status) && is_numeric($enable_status)) {
            $builder->andWhere('enable_status = :enable_status:', ['enable_status' => $enable_status]);
        }
        return $builder;
    }

    /**
     * 审批流画布列表-列表数据处理
     * @param $items
     * @return array
     */
    private function handleListItems($items)
    {
        if (empty($items)) {
            return [];
        }
        $module_ids = array_values(array_unique(array_column($items, 'module_id')));
        $module_data = [];
        if (!empty($module_ids)) {
            $module_data = SysModuleRepository::getInstance()->getModuleByIds($module_ids);
            $module_data = array_column($module_data, null, 'id');
        }
        foreach ($items as &$item) {
            //模块id文本
            $item['module_id_text'] = !empty($module_data[$item['module_id']]['name_key']) ? static::$t[$module_data[$item['module_id']]['name_key']] : '';
            //使用状态文本
            $item['enable_status_text'] = isset(OAWorkflowEnums::$workflow_enable_status_items[$item['enable_status']]) ? static::$t[OAWorkflowEnums::$workflow_enable_status_items[$item['enable_status']]] : '';
        }
        return $items;
    }

    /**
     * 获取审批流详情
     * @param $params
     * @return array
     * from_node_id
     * to_node_id
     */
    public function getDetailTree($params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            //查询审批流详情
            $relate_data = WorkflowNodeRelateRepository::getInstance()->getWorkflowNodeRelateByFlowId($params['flow_id']);
            //查询结束节点
            $end_node = WorkflowNodeRepository::getInstance()->getEndNodeByFlowId($params['flow_id']);

            $kv_flow_node = [];
            if (!empty($relate_data)) {
                //1.判断是否存在节点下多个条件指向的节点有合并的有分开的,同时取结束节点
                $is_merge_flow = true;
                //取setting_env,特殊的审批流不能合并节点展示
                $special_flow_id = EnumsService::getInstance()->getSettingEnvValueIds('workflow_canvas_special_flow_ids');
                if (in_array($params['flow_id'], $special_flow_id)) {
                    //是特殊节点, 直接不展示
                    $is_merge_flow = false;
                } else {
                    //不是特殊节点, 根据特定特征判断是否可以合并节点展示
                    $kv_relate = [];
                    foreach ($relate_data as $relate_v) {
                        $kv_relate[$relate_v['from_node_id']][] = $relate_v['to_node_id'];
                    }
                    foreach ($kv_relate as $to_node_v) {
                        //这版只支持一个节点下所有条件合并, 或者所有条件不合并
                        //一个from_node_id下对应多个to_node_id, 且去重后不是1也不是全部 说明同时存在合并节点和非合并节点, 这种审批流不做节点合并
                        $unique_count = count(array_unique($to_node_v));
                        $to_node_count = count($to_node_v);
                        if ($unique_count != $to_node_count && $unique_count > 1) {
                            $is_merge_flow = false;
                            break;
                        }
                    }
                }

                //2.标记有两个节点同时指到的节点,标记为根节点
                $all_node_id = [];
                $base_node_id = [];
                foreach ($relate_data as $key => $v) {
                    if (in_array($v['to_node_id'], $all_node_id)) {
                        $base_node_id[] = $v['to_node_id'];
                    }
                    $all_node_id[] = $v['to_node_id'];
                }
                //3.构造结构
                //记录条件优先级
                $valuate_priority = [];
                foreach ($relate_data as $key => $v) {
                    //节点数据
                    if (!isset($kv_flow_node[$v['from_node_id']])) {
                        $kv_flow_node[$v['from_node_id']]['id'] = $v['id'];
                        $kv_flow_node[$v['from_node_id']]['type'] = $v['type'];
                        $kv_flow_node[$v['from_node_id']]['name'] = $v['name'];
                        if ($v['node_audit_type'] == Enums::WF_NODE_AUDIT_TYPE_SUB_NODE_COUNTERSIGN) {
                            $current_desc = static::$t['node_audit_type_sub_desc_text'];
                        } else {
                            $current_desc = isset(OAWorkflowEnums::$auditor_type_items[$v['auditor_type']]['remark']) ? static::$t[OAWorkflowEnums::$auditor_type_items[$v['auditor_type']]['remark']] : '';
                        }
                        $kv_flow_node[$v['from_node_id']]['desc'] = $current_desc;
                        //判断from_node_id是否开始节点(被多个节点流转过来)
                        if ($v['type'] == OAWorkflowEnums::WORKFLOW_NODE_TYPE_START) {
                            $kv_flow_node[$v['from_node_id']]['is_start_node'] = true;
                            $kv_flow_node[$v['from_node_id']]['name'] = static::$t[OAWorkflowEnums::WORKFLOW_NODE_START_NODE_NAME];
                            $kv_flow_node[$v['from_node_id']]['desc'] = static::$t[OAWorkflowEnums::WORKFLOW_NODE_START_NODE_DESC];
                        } else {
                            $kv_flow_node[$v['from_node_id']]['is_start_node'] = $is_merge_flow && in_array($v['from_node_id'], $base_node_id) ? true : false;
                        }
                        //如果条件为空, 就把to_node_id和is_end_node放到节点上
                        if (empty($v['valuate_formula'])) {
                            $kv_flow_node[$v['from_node_id']]['to_node_id'] = $v['to_node_id'];
                            //判断是否to_node_id是否结束节点(被多个节点流转过来)
                            //if ($v['to_node_id'] == $end_node['id']) {
                            //    $kv_flow_node[$v['from_node_id']]['is_end_node'] = true;
                            //} else {
                                $kv_flow_node[$v['from_node_id']]['is_end_node'] = $is_merge_flow && in_array($v['to_node_id'], $base_node_id) ? true : false;
                            //}
                            continue;
                        }
                    }
                    $valuate_priority[$v['from_node_id']] = isset($valuate_priority[$v['from_node_id']]) ? $valuate_priority[$v['from_node_id']] + 1 : 1;
                    //构造children
                    $child = [];
                    //处理表达式
                    $valuate_text = $this->getValuateText($v['valuate_formula'], $v['valuate_code']);
                    $child['valuate_formula'] = $valuate_text['result'];
                    $child['valuate_formula_sort'] = $valuate_text['sort'];
                    $child['valuate_text'] = '条件' . $valuate_priority[$v['from_node_id']];
                    $child['to_node_id'] = $v['to_node_id'];
                    $child['sort'] = $v['sort'];
                    //判断是否to_node_id是否结束节点(被多个节点流转过来)
                    //if ($v['to_node_id'] == $end_node['id']) {
                    //    $child['is_end_node'] = true;
                    //} else {
                        $child['is_end_node'] = $is_merge_flow && in_array($v['to_node_id'], $base_node_id) ? true : false;
                    //}
                    $kv_flow_node[$v['from_node_id']]['children'][] = $child;
                }
                //4.拼接结束节点
                $kv_flow_node[$end_node['id']]['id'] = $end_node['id'];
                $kv_flow_node[$end_node['id']]['type'] = $end_node['type'];
                $kv_flow_node[$end_node['id']]['name'] = static::$t[OAWorkflowEnums::WORKFLOW_NODE_END_NODE_NAME];
                $kv_flow_node[$end_node['id']]['desc'] = static::$t[OAWorkflowEnums::WORKFLOW_NODE_END_NODE_DESC];
                $kv_flow_node[$end_node['id']]['is_start_node'] = $is_merge_flow && in_array($end_node['id'], $base_node_id) ? true : false;
                //v18277结束节点is_end_node固定为true
                $kv_flow_node[$end_node['id']]['is_end_node'] = true;
            }
            $data = array_values($kv_flow_node);
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
            $this->logger->warning('审批流画布-查询画布详情失败: ' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 获取审批节点详情
     * @param $params
     * @return array
     */
    public function getDetailNode($params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            //查询审批流详情
            $node_info = WorkflowNodeRepository::getInstance()->getNodeById($params['node_id']);
            if (!empty($node_info)) {
                $data['node_id'] = $node_info['id'];
                $data['name'] = $node_info['name'];
                $data['is_can_edit'] = empty($node_info['can_edit_field']) ? false : true;
                $data['node_audit_type'] = $node_info['node_audit_type'];
                $data['node_audit_type_text'] = isset(Enums::$workflow_node_audit_type_items[$node_info['node_audit_type']]) ? static::$t[Enums::$workflow_node_audit_type_items[$node_info['node_audit_type']]] : '';
                $data['auditor_type'] = $node_info['auditor_type'];
                $data['auditor_type_text'] = isset(OAWorkflowEnums::$auditor_type_items[$node_info['auditor_type']]['remark']) ? static::$t[OAWorkflowEnums::$auditor_type_items[$node_info['auditor_type']]['remark']] : '';
                $data['auditor_id'] = $this->getAuditorIdView($node_info['auditor_type'], $node_info['auditor_id']);
                //子节点
                if ($node_info['node_audit_type'] == Enums::WF_NODE_AUDIT_TYPE_SUB_NODE_COUNTERSIGN) {
                    $sub_node_data = WorkflowSubNodeRepository::getInstance()->getSubNodeByNodeId($node_info['id']);
                    $data['sub_node_data'] = [];
                    foreach ($sub_node_data as $sub_info) {
                        $tmp = [];
                        $tmp['sub_name'] = $sub_info['name'];
                        $tmp['sub_is_can_edit'] = empty($sub_info['can_edit_field']) ? false : true;
                        $tmp['sub_auditor_type'] = $sub_info['auditor_type'];
                        $tmp['sub_auditor_type_text'] = isset(OAWorkflowEnums::$auditor_type_items[$sub_info['auditor_type']]['remark']) ? static::$t[OAWorkflowEnums::$auditor_type_items[$sub_info['auditor_type']]['remark']] : '';
                        $tmp['sub_auditor_id'] = $this->getAuditorIdView($sub_info['auditor_type'], $sub_info['auditor_id']);
                        $data['sub_node_data'][] = $tmp;
                    }
                }

            }
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
            $this->logger->warning('审批流画布-查询画布详情失败: ' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 获取审批流节点寻人时的$auditor_id转义后的值
     * @param $auditor_type
     * @param $auditor_id
     * @return string
     */
    public function getAuditorIdView($auditor_type, $auditor_id)
    {
        $auditor_id_view = '';
        //转成数组
        if (strpos($auditor_id, ',') !== false) {
            $auditor_id_arr = explode(',', $auditor_id);
        } else {
            $auditor_id_arr = [$auditor_id];
        }
        //根据不同的寻人逻辑处理
        switch ($auditor_type) {
            case Enums::AUDITOR_TYPE_STAFF_INFO_ID: //指定工号
                $auditor_id_view = $auditor_id;
                break;
            case Enums::AUDITOR_TYPE_STAFF_ROLE: //指定角色
                //角色名称,直接走翻译
                $auditor_id_view_tmp = [];
                foreach ($auditor_id_arr as $v) {
                    $auditor_id_view_tmp[] = !empty(static::$t[$v]) ? static::$t[$v] : $v;
                }
                $auditor_id_view = implode(',', $auditor_id_view_tmp);
                break;
            case Enums::AUDITOR_TYPE_JOB: //职位id
                //查职位名称
                $job_data_kv = (new HrJobTitleRepository())->getJobTitleByIds($auditor_id_arr, false);
                $auditor_id_view_tmp = [];
                foreach ($auditor_id_arr as $v) {
                    $auditor_id_view_tmp[] = isset($job_data_kv[$v]['job_name']) ? $job_data_kv[$v]['job_name'] : $v;
                }
                $auditor_id_view = implode(',', $auditor_id_view_tmp);
                break;
            case Enums::AUDITOR_TYPE_DESIGNATIVE_ORG: //指定组织架构
                //查部门id
                $department_data_kv = (new DepartmentRepository())->getDepartmentByIds($auditor_id_arr);
                $auditor_id_view_tmp = [];
                foreach ($auditor_id_arr as $v) {
                    $auditor_id_view_tmp[] = isset($department_data_kv[$v]['name']) ? $department_data_kv[$v]['name'] : $v;
                }
                $auditor_id_view = implode(',', $auditor_id_view_tmp);
                break;
            case Enums::AUDITOR_TYPE_GROUP_ORG: //指定财务分组
                //查财务分组
                $group_data_kv = WorkflowStaffManagerRepository::getInstance()->getGroupByIds($auditor_id_arr);
                $auditor_id_view_tmp = [];
                foreach ($auditor_id_arr as $v) {
                    $auditor_id_view_tmp[] = isset($group_data_kv[$v]['name']) ? $group_data_kv[$v]['name'] . "($v)" : "($v)";
                }
                $auditor_id_view = implode(',', $auditor_id_view_tmp);
                break;
            case Enums::AUDITOR_TYPE_HC_HRBP: //指定HRBP分组, 当前规则固定找HCM中的category=1的,也就是AF BP Head分类的配置
                $auditor_id_view = 'AF BP Head（1）';
                break;
        }
        return $auditor_id_view;
    }

    /**
     * 解析表达式
     * @param $valuate_formula
     * @param $valuate_code
     * @return array|string
     */
    public function getValuateText($valuate_formula, $valuate_code)
    {
        $result = ['sort' => '', 'result' => ''];
        if (empty($valuate_formula)) {
            return $result;
        }
        $str_valuate_code_text = $this->valuateCodeText($valuate_formula, $valuate_code);
        $str_operator_text = $this->describeOperator($str_valuate_code_text);
        $result['sort'] = $str_operator_text;
        $result['result'] = $this->describeCondition($str_operator_text);

        return $result;
    }

    /**
     * 获取表达式变量名和变量值
     * @param $condition
     * @param $valuate_code
     * @return string
     * @date 2023/5/16
     */
    public function valuateCodeText($condition, $valuate_code)
    {
        // 正则表达式匹配变量、逻辑运算符和值
        preg_match_all('/(.*?)(\$[a-zA-Z0-9_]+)\s*([!=><]{1,3})\s*([\'"])?([^\'"()\&\|\s]+)?/', $condition, $matches, PREG_SET_ORDER);
        // 匹配结果
        $new_condition = [];
        foreach ($matches as $match) {
            $new_condition[] = [
                'before' => $match[1],
                'variable' => $match[2],
                'operator' => $match[3],
                'value' => $match[5],
            ];

        }
        // 获取最后字符串(正则匹配完容易丢最后的)
        $matches_str = '';
        foreach ($matches as $one_match) {
            $matches_str .= $one_match[0];
        }
        $end_str = str_replace($matches_str, '', $condition);
        $valuate_code = explode(',', $valuate_code);
        //转义变量名和变量值
        $str_condition = '';
        $enum_info = OAWorkflowEnums::getValuateCodeList();
        $department_repository = new DepartmentRepository();
        $hr_staff_repository = new HrStaffRepository();
        foreach ($new_condition as $one_condition) {
            $p_index = str_ireplace('$p', '', $one_condition['variable']);
            $p_index = trim($p_index);
            if (is_numeric($p_index)) {
                $real_index = $p_index - 1;
                if (isset($valuate_code[$real_index])) {
                    $this_enum_info = $enum_info[$valuate_code[$real_index]] ?? '';
                    //找到枚举
                    if (!empty($this_enum_info)) {
                        //变量名
                        $one_condition['variable'] = static::$t[$this_enum_info['text']];
                        //变量值
                        if ($this_enum_info['value_type'] == OAWorkflowEnums::VALUATE_CODE_TYPE_ENUM) {
                            //枚举类型
                            $translate_value = !empty(static::$t[$this_enum_info['value_enum'][$one_condition['value']]]) ? static::$t[$this_enum_info['value_enum'][$one_condition['value']]] : $one_condition['value'];
                            $one_condition['value'] = $translate_value;
                        } elseif ($this_enum_info['value_type'] == OAWorkflowEnums::VALUATE_CODE_TYPE_DEPARTMENT) {
                            //部门
                            $depart_info = $department_repository->getDepartmentDetail($one_condition['value']);
                            $one_condition['value'] = !empty($depart_info['name']) ? $depart_info['name'] : $one_condition['value'];
                        } elseif ($this_enum_info['value_type'] == OAWorkflowEnums::VALUATE_CODE_TYPE_STAFF) {
                            //员工
                            $user_info = $hr_staff_repository->getStaffById($one_condition['value']);
                            $one_condition['value'] = !empty($user_info['name']) ? $user_info['name'] : $one_condition['value'];
                        }
                    }
                }
            }
            //处理before中可能存在的in_array()
            if (strpos($one_condition['before'], 'in_array') !== false) {
                $one_condition['before'] = $this->getVariable($one_condition['before'], $valuate_code, $department_repository, $hr_staff_repository);
            }
            //拼接回字符串
            $str_condition .= implode(' ', $one_condition);

        }
        //处理最后字符串中可能存在的in_array()
        if (strpos($end_str, 'in_array') !== false) {
            $end_str = $this->getVariable($end_str, $valuate_code, $department_repository, $hr_staff_repository);
        }
        //拼接完整字符串
        $str_condition = $str_condition . $end_str;
        return $str_condition;
    }

    /**
     * 处理运算符
     * @param $condition
     * @return string
     * @date 2023/5/18
     */
    public function describeOperator($condition)
    {
        //大于
        $greater_than = static::$t[self::$greater_than];
        //小于
        $less_than = static::$t[self::$less_than];
        //等于
        $equal_than = static::$t[self::$equal_than];
        //不等于
        $unequal_than = static::$t[self::$unequal_than];
        //大于等于
        $greater_equal = static::$t[self::$greater_equal];
        //小于等于
        $less_equal = static::$t[self::$less_equal];
        //并且
        $operator_and = static::$t[self::$str_and];
        //或者
        $operator_or = static::$t[self::$str_or];

        // 处理与运算
        if (strpos($condition, '&&') !== false) {
            $parts = explode('&&', $condition);
            $descriptions = array_map([__CLASS__, 'describeOperator'], $parts);
            return implode(' ' . $operator_and . ' ', $descriptions);
        }
        // 处理或运算
        if (strpos($condition, '||') !== false) {
            $parts = explode('||', $condition);
            $descriptions = array_map([__CLASS__, 'describeOperator'], $parts);
            return implode(' ' . $operator_or . ' ', $descriptions);
        }
        // 处理相等运算
        if (strpos($condition, '==') !== false) {
            list($left, $right) = explode('==', $condition);
            $left = trim($left);
            $right = trim($right);
            return $left . ' ' . $equal_than . ' ' . $right;
        }
        // 处理大于运算
        if (strpos($condition, '>') !== false) {
            list($left, $right) = explode('>', $condition);
            $left = trim($left);
            $right = trim($right);
            return $left . ' ' . $greater_than . ' ' . $right;
        }
        // 处理小于运算
        if (strpos($condition, '<') !== false) {
            list($left, $right) = explode('<', $condition);
            $left = trim($left);
            $right = trim($right);
            return $left . ' ' . $less_than . ' ' . $right;
        }
        // 处理大于等于运算
        if (strpos($condition, '>=') !== false) {
            list($left, $right) = explode('>=', $condition);
            $left = trim($left);
            $right = trim($right);
            return $left . ' ' . $greater_equal . ' ' . $right;
        }
        // 处理小于等于运算
        if (strpos($condition, '<=') !== false) {
            list($left, $right) = explode('<=', $condition);
            $left = trim($left);
            $right = trim($right);
            return $left . ' ' . $less_equal . ' ' . $right;
        }
        // 处理不等于运算
        if (strpos($condition, '!=') !== false) {
            list($left, $right) = explode('!=', $condition);
            $left = trim($left);
            $right = trim($right);
            return $left . ' ' . $unequal_than . ' ' . $right;
        }
        // 如果无法识别，返回原始表达式
        return $condition;
    }

    /**
     * 处理括号,根据不同深度拼上换行和空格
     * @param $condition
     * @return string
     * @date 2023/5/16
     */
    public function describeCondition($condition)
    {
        //按照括号处理深度
        $depth = 0;
        $cut_symbol = self::$str_space;
        //并且和或者前后加换行
        //并且
        $operator_and = static::$t[self::$str_and];
        //或者
        $operator_or = static::$t[self::$str_or];
        $replace_and = str_replace($operator_and, self::$str_br . $operator_and . self::$str_br, $condition);
        $replace_or = str_replace($operator_or, self::$str_br . $operator_or . self::$str_br, $replace_and);
        $behind_str = $replace_or;
        $new_str = '';
        while (true) {

            $left_bracket_index = stripos($behind_str, '(');
            $right_bracket_index = strpos($behind_str, ')');
            $index = false;
            //确定先找到的是左括号还是右括号
            $left_or_right = '';
            if ($left_bracket_index !== false && $right_bracket_index !== false) {
                if ($left_bracket_index < $right_bracket_index) {
                    $index = $left_bracket_index;
                    $left_or_right = 'left';
                } else {
                    $index = $right_bracket_index;
                    $left_or_right = 'right';
                }
            } elseif ($left_bracket_index !== false) {
                $index = $left_bracket_index;
                $left_or_right = 'left';
            } elseif ($right_bracket_index !== false) {
                $index = $right_bracket_index;
                $left_or_right = 'right';
            }
            if (empty($left_or_right)) {
                $new_str .= $behind_str;
                break;
            }
            //括号前值分开
            $space = str_repeat($cut_symbol, $depth);
            //括号左边的字符
            $left_of_bracket = trim(substr($behind_str, 0, $index));
            //'并且'和'或者'加空格
            $replace_and = str_replace(self::$str_br . $operator_and . self::$str_br, self::$str_br . $space . $operator_and . self::$str_br . $space, $left_of_bracket);
            $replace_or = str_replace(self::$str_br . $operator_or . self::$str_br, self::$str_br . $space . $operator_or . self::$str_br . $space, $replace_and);
            $before_str = $space . $replace_or;
            //进入相应深度
            if ($left_or_right == 'left') {
                $depth += 1;
                //相应深度的空格
                $space = str_repeat($cut_symbol, $depth);
                //括号
                $bracket = $space . substr($behind_str, $index, 1);
            } else {
                //相应深度的空格
                $space = str_repeat($cut_symbol, $depth);
                //括号
                $bracket = $space . substr($behind_str, $index, 1);
                $depth -= 1;

            }


            //括号前字符 . 换行 . 括号 . 换行
            $new_str .= $before_str . self::$str_br . $bracket . self::$str_br;


            //右侧数据给下次循环
            //括号后的值
            $after_str = trim(substr($behind_str, $index + 1));
            $behind_str = $after_str;
        }
        return $new_str;
    }

    /**
     * in_array 转文本 单独处理
     * @param string $condition 表达式
     * @param string $valuate_code 变量获取方法
     * @param object $department_repository 部门数据仓库类
     * @param object $hr_staff_repository 员工数据仓库类
     * @return string
     * @date 2023/7/10
     */
    public function getVariable($condition, $valuate_code, $department_repository, $hr_staff_repository)
    {
        //取in_array的前置字符,in_array前可能存在的!,in_array中的两个参数,in_array的后置字符
        preg_match('/(.*?)(!?)\s*in_array\s*\(\s*\"?\'?(.*?)\"?\'?\s*,\s*\"?\'?(.*?)\"?\'?\s*\)(.*)/', $condition, $matches);
        $before = $matches[1]; //in_array的前置字符
        $negation = trim($matches[2]); //in_array前可能存在的!
        $needle = trim($matches[3]); //in_array中的第一个参数
        $haystack = trim($matches[4]); //in_array中的第二个参数
        $after = $matches[5]; //in_array的后置字符
        if (empty($needle) || empty($haystack)) {
            return $condition;
        }
        $in_array_variable = '';
        $in_array_value = '';
        if (strpos($needle, '$') !== false) {
            $in_array_variable = $needle;
            $in_array_value = $haystack;
        } elseif (strpos($haystack, '$') !== false) {
            $in_array_variable = $haystack;
            $in_array_value = $needle;
        }
        //in_array中的数组处理
        if (strpos($in_array_value, '[') !== false) {
            //去除空格
            $in_array_value = trim($in_array_value);
            //去除[ ]
            $in_array_value = trim($in_array_value, '[]');
        } elseif (strpos($in_array_value, 'array') !== false) {
            //去除array
            $in_array_value = str_ireplace('array', '', $in_array_value);
            //去除空格
            $in_array_value = trim($in_array_value);
            //去除括号
            $in_array_value = trim($in_array_value, '()');
        }
        //值中只剩下逗号拼接的
        $in_array_value = explode(',', $in_array_value);
        //每个值中是否包含=>,如果包含只取后边的值
        foreach ($in_array_value as &$v) {
            if (strpos($v, '=>') !== false) {
                $v = str_replace('=>', '', $v);
                $v = trim($v);
            }
        }
        //处理值
        $enum_info = OAWorkflowEnums::getValuateCodeList();
        $p_index = str_ireplace('$p', '', $in_array_variable);
        $p_index = trim($p_index);
        if (is_numeric($p_index)) {
            $real_index = $p_index - 1;

            if (isset($valuate_code[$real_index])) {
                $this_enum_info = $enum_info[$valuate_code[$real_index]] ?? '';
                //找到枚举
                if (!empty($this_enum_info)) {
                    //变量名
                    $in_array_variable = static::$t[$this_enum_info['text']];
                    //变量值
                    foreach ($in_array_value as &$value) {
                        if ($this_enum_info['value_type'] == OAWorkflowEnums::VALUATE_CODE_TYPE_ENUM) {
                            //枚举类型
                            $value = static::$t[$this_enum_info['value_enum'][$value]];
                        } elseif ($this_enum_info['value_type'] == OAWorkflowEnums::VALUATE_CODE_TYPE_DEPARTMENT) {
                            //部门
                            $depart_info = $department_repository->getDepartmentDetail($value);
                            $value = !empty($depart_info['name']) ? $depart_info['name'] : $value;
                        } elseif ($this_enum_info['value_type'] == OAWorkflowEnums::VALUATE_CODE_TYPE_STAFF) {
                            //员工
                            $user_info = $hr_staff_repository->getStaffById($value);
                            $value = !empty($user_info['name']) ? $user_info['name'] : $value;
                        }
                    }
                }
            }
        }
        $in_array_value = implode(',', $in_array_value);
        //文本 : 在 [$in_array_value] 中
        $in_symbol_text = static::$t->_(self::$in_array_prefix, ['content' => $in_array_value]);
        if (strpos($negation, '!') !== false) {
            //文本 : 不在 [$in_array_value] 中
            $in_symbol_text = static::$t->_(self::$not_in_array_prefix, ['content' => $in_array_value]);

        }

        return $before . ' ' . $in_array_variable . ' ' . $in_symbol_text . ' ' . $after;
    }
}