<?php

namespace App\Modules\Workflow\Services;

use App\Library\BaseService;

use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Exception\BusinessException;
use App\Modules\Workflow\Models\WorkflowNodeModel;
use App\Modules\Workflow\Models\WorkflowRequestModel;
use Phalcon\Mvc\Model;

abstract class AbstractFlowService extends BaseService implements FlowActionInterface
{
    public static $validate_approve = [
        'id' => 'Required|IntGe:1',
        'note' => 'StrLenGeLe:0,1000'
    ];

    public static $validate_reject = [
        'id' => 'Required|IntGe:1',
        'note' => 'Required|StrLenGeLe:1,1000'
    ];

    public static $validate_cancel = [
        'id' => 'Required|IntGe:1',
        'note' => 'Required|StrLenGeLe:1,1000'
    ];

    /**
     * 获取当前业务审批流信息
     * @param $id
     * @param $biz_type
     * @return Model
     */
    public function getRequestByBiz($id, $biz_type)
    {
        return WorkflowRequestModel::findFirst([
            'conditions' => 'biz_type = :type: AND biz_value= :id: AND is_abandon = :is_abandon:',
            'bind' => ['type' => $biz_type, 'id' => $id, 'is_abandon' => GlobalEnums::WORKFLOW_ABANDON_STATE_NO],
            'order' => 'id DESC'
        ]);
    }


    /**
     * 是否能访问该业务表，===true可以，其他不行
     * @param $request
     * @param null $user_id
     * @return bool|string
     */
    public function isCanView($request,$user_id=null){
        //如果user_id为null，代表数据查询，可以访问
        if(empty($user_id)){
            return true;
        }

        //都没审批流，不让访问
        if(empty($request)){
            return self::$t->_("no_permission_to_view");
        }

        $viewer_ids = $request->viewer_ids;
        if(empty($viewer_ids) || !in_array($user_id,explode(",",$viewer_ids))){
            return self::$t->_("no_permission_to_view");
        }
        return true;
    }

    /**
     * 获得当前审批流是否能更改指定字段
     * @param $req
     * @param $uid
     * @return mixed
     *
     * @throws BusinessException
     */
    public function getCanEditFieldByReq($req,$uid) {
        if (empty($req) || empty($uid) || empty($req->current_node_auditor_id) || empty($req->current_flow_node_id)) {
            return false;
        }

        //如果不是待审核直接返回false
        if ($req->state != Enums::WF_STATE_PENDING) {
            return false;
        }


        //如果没在审批人里，也返回false
        if (!in_array($uid, explode(',', $req->current_node_auditor_id))) {
            return false;
        }

        $node = WorkflowNodeModel::findFirst([
            'conditions' => 'id = :id:',
            'bind' => ['id' => $req->current_flow_node_id]
        ]);
        if (empty($node)) {
            return false;
        }

        // 子节点会签, 查找待审批的子节点配置
        if ($node->node_audit_type == Enums::WF_NODE_AUDIT_TYPE_SUB_NODE_COUNTERSIGN) {
            $workflow_service_v2 = (new WorkflowServiceV2());
            $all_sub_nodes = $workflow_service_v2->getFlowSubNodes($req->flow_id, $node->id)->toArray();

            $_sub_nodes = $_all_sub_node_auditor_ids = [];
            foreach ($all_sub_nodes as $sub_node) {
                $_sub_node_auditor_ids = $workflow_service_v2->getNodeAuditors($sub_node, []);
                $_all_sub_node_auditor_ids = array_merge($_all_sub_node_auditor_ids, $_sub_node_auditor_ids);
                $_sub_nodes[$sub_node['id']]['auditor_ids'] = $_sub_node_auditor_ids;
            }

            $_node_auditor_ids['sub_nodes'] = $_sub_nodes;
            $_node_auditor_ids['auditor_ids'] = array_values(array_unique($_all_sub_node_auditor_ids));
            $flowAllNodesAuditors[$node->id] = $_node_auditor_ids;

            $pending_sub_node = $workflow_service_v2->getCurrentSubNode($req, $node, $flowAllNodesAuditors, ['id' => $uid]);
            if (!empty($pending_sub_node)) {
                return empty(trim($pending_sub_node->can_edit_field)) ? false : json_decode($pending_sub_node->can_edit_field,true);
            }
            return false;
        }

        // 一级节点: 原有逻辑
        return empty(trim($node->can_edit_field)) ? false : json_decode($node->can_edit_field,true);
    }

    /**
     * 获得当前审批节点是否是AP\APS
     * @param $req
     * @return mixed
     */
    public function isApApsNode($req)
    {
        $node = WorkflowNodeModel::findFirst(
            [
                'conditions'=>'id = :id:',
                'bind'=>['id'=>$req->current_flow_node_id]
            ]
        );

        $this->logger->info("isApApsNode: current_flow_node_id={$req->current_flow_node_id}");

        if (!empty($node)) {
            $node = $node->toArray();

            $this->logger->info('isApApsNode: node_info = ' . json_encode($node, JSON_UNESCAPED_UNICODE));

            return !empty($node['can_edit_field']) && $node['auditor_type'] == 1;
        } else {
            $this->logger->info('isApApsNode: node_info is null');

            return false;
        }
    }

    /**
     * 获得待审批审批节点标签: 标识业务处理阶段
     *
     * @param $req
     * @param $uid
     * @return mixed
     * @throws BusinessException
     */
    public function getPendingNodeTag($req, $uid)
    {
        if (empty($req) || empty($uid) || empty($req->current_node_auditor_id) || empty($req->current_flow_node_id)) {
            return false;
        }

        // 如果不是待审核直接返回false
        if ($req->state != Enums::WF_STATE_PENDING) {
            return false;
        }

        //如果没在审批人里，也返回false
        if (!in_array($uid, explode(',', $req->current_node_auditor_id))) {
            return false;
        }

        $node = WorkflowNodeModel::findFirst(
            [
                'conditions'=>'id = :id:',
                'bind'=>['id'=>$req->current_flow_node_id]
            ]
        );

        if (empty($node)) {
            return false;
        }

        // 子节点会签, 查找待审批的子节点配置
        if ($node->node_audit_type == Enums::WF_NODE_AUDIT_TYPE_SUB_NODE_COUNTERSIGN) {

            $workflow_service_v2 = (new WorkflowServiceV2());
            $all_sub_nodes = $workflow_service_v2->getFlowSubNodes($req->flow_id, $node->id)->toArray();

            $_sub_nodes = $_all_sub_node_auditor_ids = [];
            foreach ($all_sub_nodes as $sub_node) {
                $_sub_node_auditor_ids = $workflow_service_v2->getNodeAuditors($sub_node, []);
                $_all_sub_node_auditor_ids = array_merge($_all_sub_node_auditor_ids, $_sub_node_auditor_ids);
                $_sub_nodes[$sub_node['id']]['auditor_ids'] = $_sub_node_auditor_ids;
            }

            $_node_auditor_ids['sub_nodes'] = $_sub_nodes;
            $_node_auditor_ids['auditor_ids'] = array_values(array_unique($_all_sub_node_auditor_ids));
            $flowAllNodesAuditors[$node->id] = $_node_auditor_ids;

            $pending_sub_node = $workflow_service_v2->getCurrentSubNode($req, $node, $flowAllNodesAuditors, ['id' => $uid]);

            if (!empty($pending_sub_node)) {
                return $pending_sub_node->node_tag;
            }

            return false;
        }

        return $node->node_tag;
    }

    /**
     * 获得待审批节点信息
     * 说明: 可逐步替代 getCanEditFieldByReq、getPendingNodeTag 方法
     *
     * @param $req
     * @param $uid
     * @return mixed
     * @throws BusinessException
     */
    public function getPendingNodeInfo($req, $uid)
    {
        if (empty($req) || empty($uid) || empty($req->current_node_auditor_id) || empty($req->current_flow_node_id)) {
            return [];
        }

        // 审批流非待审核
        if ($req->state != Enums::WF_STATE_PENDING) {
            return [];
        }

        // 用户非待审批人
        if (!in_array($uid, explode(',', $req->current_node_auditor_id))) {
            return [];
        }

        $node = WorkflowNodeModel::findFirst([
            'conditions' => 'id = :id:',
            'bind' => ['id' => $req->current_flow_node_id]
        ]);

        if (empty($node)) {
            return [];
        }

        // 子节点会签, 查找待审批的子节点配置
        if ($node->node_audit_type == Enums::WF_NODE_AUDIT_TYPE_SUB_NODE_COUNTERSIGN) {
            $workflow_service_v2 = (new WorkflowServiceV2());
            $all_sub_nodes = $workflow_service_v2->getFlowSubNodes($req->flow_id, $node->id)->toArray();

            $_sub_nodes = $_all_sub_node_auditor_ids = [];
            foreach ($all_sub_nodes as $sub_node) {
                $_sub_node_auditor_ids = $workflow_service_v2->getNodeAuditors($sub_node, []);
                $_all_sub_node_auditor_ids = array_merge($_all_sub_node_auditor_ids, $_sub_node_auditor_ids);
                $_sub_nodes[$sub_node['id']]['auditor_ids'] = $_sub_node_auditor_ids;
            }

            $_node_auditor_ids['sub_nodes'] = $_sub_nodes;
            $_node_auditor_ids['auditor_ids'] = array_values(array_unique($_all_sub_node_auditor_ids));
            $flowAllNodesAuditors[$node->id] = $_node_auditor_ids;

            $pending_node = $workflow_service_v2->getCurrentSubNode($req, $node, $flowAllNodesAuditors, ['id' => $uid]);
        } else {
            $pending_node = $node;
        }

        return [
            'node_tag' => $pending_node->node_tag ?? '0',
            'is_comment' => $pending_node->is_comment ?? '0',
            'can_edit_field' => empty(trim($pending_node->can_edit_field)) ? false : json_decode($pending_node->can_edit_field,true)
        ];
    }
}
