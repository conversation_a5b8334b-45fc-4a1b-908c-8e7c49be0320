<?php


namespace App\Modules\Workflow\Services;

use App\Library\BaseService;
use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\CrmQuotationEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Models\oa\LoanReturnModel;
use App\Modules\Budget\Models\BudgetAdjustModel;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Contract\Models\Contract;
use App\Modules\Contract\Models\ContractStoreRentingModel;
use App\Modules\CrmQuotation\Models\CrmQuotationApplyModel;
use App\Modules\Loan\Models\Loan;
use App\Modules\OrdinaryPayment\Models\OrdinaryPayment;
use App\Modules\Payment\Models\PaymentStoreRenting;
use App\Modules\Purchase\Models\PurchaseApply;
use App\Modules\Purchase\Models\PurchaseOrder;
use App\Modules\Purchase\Models\PurchasePayment;
use App\Modules\Reimbursement\Models\Reimbursement;
use App\Modules\ReserveFund\Models\ReserveFundApply;
use App\Modules\ReserveFund\Models\ReserveFundReturn;
use App\Modules\Salary\Models\PaySalaryApply;
use App\Modules\User\Models\StaffInfoModel;
use App\Modules\Vendor\Models\Vendor;
use App\Modules\Wages\Models\WagesModel;
use App\Modules\Workflow\Models\WorkflowRequestModel;
use App\Modules\Workflow\Models\WorkflowRequestNodeAuditorModel;

class WorkflowMailService extends BaseService
{
    //redis list key
    public static $redis_list_name = 'workflow_auto_approval_ignore_send_email';

    /**
     * 发送邮件
     * @param $request
     * @param $type
     * @date 2022/6/13
     */
    public function sendEmail($request, $type)
    {
        try {
            //
            //1.获取收件人邮箱
            $emails = $this->getEmailRecipient($request, $type);
            //2.设置邮件语言
            $language_setting = $this->getNoticeEmailTemplateLang();
            $first_lang = $language_setting['first_lang'];
            $second_lang = $language_setting['second_lang'];
            //3.获取邮件内容
            if ($type == Enums::SEND_MAIL_TYPE_REJECT) {
                list($title, $content) = $this->getEmailContent($request, $first_lang, $second_lang);
            } elseif ($type == Enums::SEND_MAIL_TYPE_PASS) {
                list($title, $content) = $this->getEmailContentPass($request, $first_lang, $second_lang);
            }
            //4.发送邮件
            $log = ["emails" => $emails, "title" => $title, "html" => $content];
            $send_res = $this->mailer->sendAsync($emails, $title, $content);
            if ($send_res) {
                $this->logger->info("workflowSendEmail 发送成功！[type:{$type}] " . json_encode($log, JSON_UNESCAPED_UNICODE));
            } else {
                $this->logger->warning("workflowSendEmail 发送失败！[type:{$type}] " . json_encode($log, JSON_UNESCAPED_UNICODE));
            }
            //5.记录日志
        } catch (ValidationException $e) {
            $this->logger->info("workflowSendEmail 校验异常！[type:{$type}] 原因可能是：" . $e->getMessage());

        } catch (BusinessException $e) {
            $this->logger->info("workflowSendEmail 业务异常！[type:{$type}] 原因可能是：" . $e->getMessage());

        } catch (\Exception $e) {
            $this->logger->warning("workflowSendEmail 发送异常！[type:{$type}] 原因可能是：" . $e->getMessage());
        }
    }

    /**
     * 获取邮箱地址
     * @param $request
     * @param $type
     * @return array
     * @throws ValidationException
     * @date 2022/6/13
     */
    public function getEmailRecipient($request, $type)
    {
        // 只在测试环境，给测试人员发邮件。
        $test_flag = in_array(env('runtime'), ['dev', 'test', 'tra', 'training']);

        // 开发/测试/tra环境收件人取指定配置的
        if ($test_flag) {
            $email_addressee_code = GlobalEnums::EMAIL_NOTICE_WAITING_REJECT_CODE;
            $email_addressee_val = EnvModel::getEnvByCode($email_addressee_code);
            $emails = !empty($email_addressee_val) ? explode(',', $email_addressee_val) : [];
        } elseif ($request->biz_type == Enums::WF_CRM_QUOTATION) {
            //根据biz_value 查询单号数据
            $item = CrmQuotationApplyModel::findFirst([
                'conditions' => 'id = :id: ',
                'bind' => ['id' => $request->biz_value]
            ]);
            $email_addressee_val = EnumsService::getSettingEnvValueMap('crm_quotation_email_address');
            if (!empty($item->material_value) && (!empty($item->return_discount_rate) || !empty($item->free_strategy_discount) || !empty($item->cod_fee_rate) || !empty($item->settlement_category) || $item->quoted_price_type == CrmQuotationEnums::QUOTED_PRICE_TYPE_1)) {
                //
                $email_addressee_val = $email_addressee_val['all_mail'] ?? '';
            } else if (!empty($item->material_value)) {
                $email_addressee_val = $email_addressee_val['asset_mail'] ?? '';
            } else {
                $email_addressee_val = $email_addressee_val['other_mail'] ?? '';
            }
            $emails = !empty($email_addressee_val) ? explode(',', $email_addressee_val) : [];
        } else {
            // 通过分类获取收件人
            switch ($type) {
                case Enums::SEND_MAIL_TYPE_REJECT:
                case Enums::SEND_MAIL_TYPE_PASS:
                    $emails = (new StaffInfoModel())->getEmails([$request->create_staff_id]);
                    break;
            }
        }
        // 如果收件人邮箱为空，则不发
        if (empty($emails)) {
            throw new ValidationException("no emails addressee, stop send. type[$type], biz_type[{$request->biz_type}], request_id[" . json_encode($request->id, JSON_UNESCAPED_UNICODE) . ']');
        }
        return $emails;
    }

    /**
     * 审批驳回的邮件内容
     *
     * @param $request
     * @param $first_lang
     * @param $second_lang
     * @return array
     * @throws ValidationException
     * @date 2022/6/13
     */
    public function getEmailContent($request, $first_lang, $second_lang)
    {
        switch ($request->biz_type) {
            //合同(包含销售合同)
            case Enums::WF_CONTRACT_TYPE1:
            case Enums::WF_CONTRACT_TYPE20:
            case Enums::WF_CONTRACT_TYPE21:
            case Enums::WF_CONTRACT_TYPE22:
            case Enums::WF_CONTRACT_TYPE23:
            case Enums::WF_CONTRACT_TYPE24:
                $item = Contract::findFirst([
                    'id = :id:',
                    'bind' => ['id' => $request->biz_value]
                ]);
                if (empty($item)) {
                    throw new \Exception("no item, type = {$request->biz_type}, id = {$request->biz_value}", ErrCode::$SYSTEM_ERROR);
                }
                $item = $item->toArray();

                // 合同分类map
                $contract_category_map = EnumsService::getInstance()->getContractCategorysMap();

                // 直属分类信息
                $contract_category_info = $contract_category_map[$item['template_id']] ?? [];
                $template_id = $contract_category_info['translation_key'] ?? '';

                $item['template_title'] = $first_lang->_($template_id);
                $item['template_second_title'] = $second_lang->_($template_id);
                $first_title = $item['template_title'] . '-' . $item['cno'];
                $second_title = $item['template_second_title'] . '-' . $item['cno'];
                $whereKey = 'email_contract_apply_where';
                break;
            // 网点租房合同
            case Enums::WF_STORE_RENTING_CONTRACT_TYPE:
                $item = ContractStoreRentingModel::findFirst([
                    'id = :id:',
                    'bind' => ['id' => $request->biz_value]
                ]);

                if (empty($item)) {
                    throw new \Exception("no item,type=14,id = " . $request->biz_value);
                }

                $first_title = $first_lang->_('email_store_rent_contract_name') . '-' . $item->contract_id;
                $second_title = $second_lang->_('email_store_rent_contract_name') . '-' . $item->contract_id;
                $whereKey = "email_store_rent_contract_apply_where";
                break;
            //供应商管理
            case Enums::WF_VENDOR_BIZ_TYPE:
                // 供应商信息审核
                $item = Vendor::getFirst([
                    'id = :id:',
                    'bind' => ['id' => $request->biz_value],
                    'columns' => ['vendor_id']
                ]);
                if (empty($item)) {
                    throw new \Exception("workflow biz data is bull, type = 59, id = " . $request->biz_value);
                }

                $first_title = $first_lang->_('vendor_audit_email_apply_name') . '-' . $item->vendor_id;
                $second_title = $second_lang->_('vendor_audit_email_apply_name') . '-' . $item->vendor_id;
                $whereKey = 'email_vendor_apply_where';
                break;

            //借款申请
            case Enums::WF_LOAN_TYPE:
                $item = Loan::findFirst([
                    'id = :id:',
                    'bind' => ['id' => $request->biz_value]
                ]);

                if (empty($item)) {
                    throw new \Exception("no item,type=8,id=" . $request->biz_value);
                }
                $first_title = $first_lang->_('module_name_loan') . "-" . $item->lno;
                $second_title = $second_lang->_('module_name_loan') . "-" . $item->lno;
                $whereKey = "email_loan_apply_where";
                break;
            // 借款归还
            case Enums::WF_LOAN_BACK_TYPE:
                $item = LoanReturnModel::findFirst([
                    'id = :id:',
                    'bind' => ['id' => $request->biz_value]
                ]);

                if (empty($item)) {
                    throw new \Exception("no item,type=58,id=" . $request->biz_value);
                }

                $loan_model = Loan::findFirst([
                    'id = :id:',
                    'bind' => ['id' => $item->loan_id],
                    'columns' => ['lno']
                ]);

                $first_title = $first_lang->_('loan_back_name') . '-' . $loan_model->lno ?? '';
                $second_title = $second_lang->_('loan_back_name') . '-' . $loan_model->lno ?? '';
                $whereKey = "email_loan_back_apply_where";
                break;

            //报销
            case Enums::WF_REIMBURSEMENT_TYPE:
                $item = Reimbursement::findFirst([
                    'id = :id:',
                    'bind' => ['id' => $request->biz_value]
                ]);

                if (empty($item)) {
                    throw new \Exception("no item,type=13,id=" . $request->biz_value);
                }

                $first_title = $first_lang->_('email_reimbursement_name') . "-" . $item->no;
                $second_title = $second_lang->_('email_reimbursement_name') . "-" . $item->no;
                $whereKey = "email_reimbursement_apply_where";
                break;
            //采购申请单
            case Enums::WF_PURCHASE_APPLY:
                $item = PurchaseApply::findFirst([
                    'id=:id:',
                    'bind' => ['id' => $request->biz_value]
                ]);
                if (empty($item)) {
                    throw new \Exception("no item,type=9,id=" . $request->biz_value);
                }

                $first_biz_title = $first_lang->_("purchase_apply_name");
                $second_biz_title = $second_lang->_("purchase_apply_name");

                $first_title = $first_biz_title . '-' . $item->pano;
                $second_title = $second_biz_title . '-' . $item->pano;
                $whereKey = "email_purchase_apply_apply_where";
                break;
            //采购订单
            case Enums::WF_PURCHASE_ORDER:
                $item = PurchaseOrder::findFirst([
                    'id=:id:',
                    'bind' => ['id' => $request->biz_value]
                ]);
                if (empty($item)) {
                    throw new \Exception("no item,type=10,id=" . $request->biz_value);
                }

                $first_biz_title = $first_lang->_("purchase_order_name");
                $second_biz_title = $second_lang->_("purchase_order_name");

                $first_title = $first_biz_title . '-' . $item->pono;
                $second_title = $second_biz_title . '-' . $item->pono;
                $whereKey = "email_purchase_order_apply_where";
                break;
            //采购付款申请单
            case Enums::WF_PURCHASE_PAYMENT:
                $item = PurchasePayment::findFirst([
                    'id=:id:',
                    'bind' => ['id' => $request->biz_value]
                ]);
                if (empty($item)) {
                    throw new \Exception("no item,type=11,id=" . $request->biz_value);
                }

                $first_biz_title = $first_lang->_("purchase_payment_name");
                $second_biz_title = $second_lang->_("purchase_payment_name");

                $first_title = $first_biz_title . '-' . $item->ppno;
                $second_title = $second_biz_title . '-' . $item->ppno;
                $whereKey = "email_purchase_payment_apply_where";
                break;
            // 网点租房付款
            case Enums::WF_PAYMENT_STORE_RENTING_TYPE:
                $item = PaymentStoreRenting::findFirst([
                    'id = :id:',
                    'bind' => ['id' => $request->biz_value]
                ]);

                if (empty($item)) {
                    throw new \Exception("no item, type = 29, id = " . $request->biz_value);
                }
                $first_title = $first_lang->_('payment_store_renting_email_apply_name') . "-" . $item->apply_no;
                $second_title = $second_lang->_('payment_store_renting_email_apply_name') . "-" . $item->apply_no;
                $whereKey = "payment_store_renting_email_apply_where";
                break;

            // 普通付款
            case Enums::ORDINARY_PAYMENT_BIZ_TYPE:
                $item = OrdinaryPayment::findFirst([
                    'id = :id:',
                    'bind' => ['id' => $request->biz_value]
                ]);

                if (empty($item)) {
                    throw new \Exception("no item, type = 30, id = " . $request->biz_value);
                }

                $first_title = $first_lang->_('ordinary_payment_pdf_file_name') . "-" . $item->apply_no;
                $second_title = $second_lang->_('ordinary_payment_pdf_file_name') . "-" . $item->apply_no;
                $whereKey = "email_ordinary_payment_apply_where";
                break;
            //备用金申请
            case Enums::WF_RESERVE_FUND_APPLY:
                $item = ReserveFundApply::findFirst([
                    'id = :id:',
                    'bind' => ['id' => $request->biz_value]
                ]);

                if (empty($item)) {
                    throw new \Exception("no item, type = 51, id = " . $request->biz_value);
                }

                $first_title = $first_lang->_('email_reserve_apply_name') . "-" . $item->rfano;
                $second_title = $second_lang->_('email_reserve_apply_name') . "-" . $item->rfano;
                $whereKey = "email_reserve_apply_apply_where";
                break;
            //备用金归还
            case Enums::WF_RESERVE_FUND_RETURN:
                $item = ReserveFundReturn::findFirst([
                    'id = :id:',
                    'bind' => ['id' => $request->biz_value]
                ]);

                if (empty($item)) {
                    throw new \Exception("no item, type = 52, id = " . $request->biz_value);
                }

                $first_title = $first_lang->_('email_reserve_return_name') . "-" . $item->rrno;
                $second_title = $second_lang->_('email_reserve_return_name') . "-" . $item->rrno;
                $whereKey = 'email_reserve_return_apply_where';
                break;
            // 预算导入
            case Enums::BUDGET_OB_TYPE:
                $first_title = $first_lang->_('budget_object_import');
                $second_title = $second_lang->_('budget_object_import');
                $whereKey = "email_budget_object_import_apply_where";
                break;
            // 预算调整
            case Enums::WF_BUDGET_ADJUST_TYPE:
                $item = BudgetAdjustModel::findFirst([
                    'id = :id:',
                    'bind' => ['id' => $request->biz_value]
                ]);
                if (empty($item)) {
                    throw new \Exception("no item,type=91,id=" . $request->biz_value);
                }
                $first_title = $first_lang->_('budget_adjust_method_text') . '-' . $item->adjust_no;
                $second_title = $second_lang->_('budget_adjust_method_text') . '-' . $item->adjust_no;
                $whereKey = "email_budget_adjust_apply_where";
                break;
            // 薪资发放审批
            case Enums::WF_SALARY_APPLY:
                $item = PaySalaryApply::findFirst([
                    'id = :id:',
                    'bind' => ['id' => $request->biz_value]
                ]);
                if (empty($item)) {
                    throw new \Exception("no item, type = 43, id = " . $request->biz_value);
                }
                $first_title = $first_lang->_('pay_salary_apply_name') . "-" . $item->xzno;
                $second_title = $second_lang->_('pay_salary_apply_name') . "-" . $item->xzno;
                $whereKey = "pay_salary_apply_apply_where";
                break;
            // 薪资扣款审批
            case Enums::WF_WAGES_TYPE:
                $item = WagesModel::findFirst([
                    'id = :id:',
                    'bind' => ['id' => $request->biz_value]
                ]);
                if (empty($item)) {
                    throw new \Exception("no item,type=15,id=" . $request->biz_value);
                }
                $first_title = $first_lang->_('email_wages_name') . "-" . $item->no;
                $second_title = $second_lang->_('email_wages_name') . "-" . $item->no;
                $whereKey = "email_wages_apply_where";
                break;
            default:
                throw new ValidationException("no type=" . $request->biz_type);
        }
        //邮件标题翻译
        $waitKey = "email_rejected";
        $contentKey = "email_rejected_content";
        $first_wait = $first_lang->_($waitKey);
        $second_wait = $second_lang->_($waitKey);

        //邮件标题
        $title = $first_wait . $second_wait . "：" . $first_title . " " . $second_title;
        //邮件内容
        $first_where = $first_lang->_($whereKey);
        $second_where = $second_lang->_($whereKey);
        $first_content = $first_lang->_($contentKey, ["title" => $first_title, "where" => $first_where]);
        $second_content = $second_lang->_($contentKey, ["title" => $second_title, "where" => $second_where]);
        //oa地址
        // 各环境各国家dashboard页地址
        $url = EnvModel::getEnvByCode(GlobalEnums::OA_DASHBOARD_PAGE_URL_CODE);
        $url = !empty($url) ? $url : GlobalEnums::OA_DASHBOARD_PAGE_URL_DEFAULT;

        $html = <<<EOF
    hi,<br/>
    <span style="margin-left: 16px"></span>{$first_content}<a href="{$url}" target="_blank">{$url}</a><br/>
    <span style="margin-left: 16px"></span>{$second_content}<a href="{$url}" target="_blank">{$url}</a>

EOF;
        return [$title, $html];
    }

    /**
     * 审批通过邮件
     * @param $request
     * @param $first_lang
     * @param $second_lang
     * @return array
     * @throws ValidationException
     * @throws \Exception
     * @date 2022/6/14
     */
    public function getEmailContentPass($request, $first_lang, $second_lang)
    {
        //内容类型 1.图文内容,通过是否express公司有细微区别; 2.文字内容
        $content_type = 1;
        //获取费用所属公司
        switch ($request->biz_type) {
            //借款申请
            case Enums::WF_LOAN_TYPE:

                $item = Loan::findFirst([
                    'id = :id:',
                    'bind' => ['id' => $request->biz_value]
                ]);

                if (empty($item)) {
                    throw new \Exception("no item,type=8,id=" . $request->biz_value);
                }
                $first_title = $first_lang->_('module_name_loan') . "-" . $item->lno;
                $second_title = $second_lang->_('module_name_loan') . "-" . $item->lno;
                $whereKey = "email_loan_apply_where";
                $company_id = $item->create_company_id;
                break;
            //报销
            case Enums::WF_REIMBURSEMENT_TYPE:
                $item = Reimbursement::findFirst([
                    'id = :id:',
                    'bind' => ['id' => $request->biz_value]
                ]);
                if (empty($item)) {
                    throw new \Exception("no item,type=13,id=" . $request->biz_value);
                }

                $first_title = $first_lang->_('email_reimbursement_name') . "-" . $item->no;
                $second_title = $second_lang->_('email_reimbursement_name') . "-" . $item->no;
                $whereKey = "email_reimbursement_apply_where";
                $company_id = $item->cost_company_id;
                break;
            //采购付款申请单
            case Enums::WF_PURCHASE_PAYMENT:
                $item = PurchasePayment::findFirst([
                    'id = :id:',
                    'bind' => ['id' => $request->biz_value]
                ]);
                if (empty($item)) {
                    throw new \Exception("no item,type=11,id=" . $request->biz_value);
                }

                $first_biz_title = $first_lang->_("purchase_payment_name");
                $second_biz_title = $second_lang->_("purchase_payment_name");

                $first_title = $first_biz_title . '-' . $item->ppno;
                $second_title = $second_biz_title . '-' . $item->ppno;
                $whereKey = "email_purchase_payment_apply_where";
                $company_id = $item->cost_company_id;
                break;
            //租房付款
            case Enums::WF_PAYMENT_STORE_RENTING_TYPE:
                $item = PaymentStoreRenting::findFirst([
                    'id = :id:',
                    'bind' => ['id' => $request->biz_value]
                ]);
                if (empty($item)) {
                    throw new \Exception("no item, type = 29, id = " . $request->biz_value);
                }
                $first_title = $first_lang->_('payment_store_renting_email_apply_name') . "-" . $item->apply_no;
                $second_title = $second_lang->_('payment_store_renting_email_apply_name') . "-" . $item->apply_no;
                $company_id = $item->cost_company_id;
                $whereKey = "payment_store_renting_email_apply_where";
                break;
            //普通付款
            case Enums::ORDINARY_PAYMENT_BIZ_TYPE:
                $item = OrdinaryPayment::findFirst([
                    'id = :id:',
                    'bind' => ['id' => $request->biz_value]
                ]);
                if (empty($item)) {
                    throw new \Exception("no item, type = 30, id = " . $request->biz_value);
                }

                $first_title = $first_lang->_('ordinary_payment_pdf_file_name') . "-" . $item->apply_no;
                $second_title = $second_lang->_('ordinary_payment_pdf_file_name') . "-" . $item->apply_no;
                $whereKey = "email_ordinary_payment_apply_where";
                $company_id = $item->cost_company_id;
                break;
            //备用金申请
            case Enums::WF_RESERVE_FUND_APPLY:
                $item = ReserveFundApply::findFirst([
                    'id = :id:',
                    'bind' => ['id' => $request->biz_value]
                ]);

                if (empty($item)) {
                    throw new \Exception("no item, type = 51, id = " . $request->biz_value);
                }

                $first_title = $first_lang->_('email_reserve_apply_name') . "-" . $item->rfano;
                $second_title = $second_lang->_('email_reserve_apply_name') . "-" . $item->rfano;
                $whereKey = "email_reserve_apply_apply_where";
                $company_id = $item->create_company_id;
                break;
            //备用金归还
            case Enums::WF_RESERVE_FUND_RETURN:
                $item = ReserveFundReturn::findFirst([
                    'id = :id:',
                    'bind' => ['id' => $request->biz_value]
                ]);

                if (empty($item)) {
                    throw new \Exception("no item, type = 52, id = " . $request->biz_value);
                }

                $first_title = $first_lang->_('email_reserve_return_name') . "-" . $item->rrno;
                $second_title = $second_lang->_('email_reserve_return_name') . "-" . $item->rrno;
                $whereKey = 'email_reserve_return_apply_where';
                $content_type = 2;
                break;
            // 借款归还
            case Enums::WF_LOAN_BACK_TYPE:
                $item = LoanReturnModel::findFirst([
                    'id = :id:',
                    'bind' => ['id' => $request->biz_value]
                ]);

                if (empty($item)) {
                    throw new \Exception("no item,type=58,id=" . $request->biz_value);
                }

                $loan_model = Loan::findFirst([
                    'id = :id:',
                    'bind' => ['id' => $item->loan_id],
                    'columns' => ['lno']
                ]);

                $first_title = $first_lang->_('loan_back_name') . '-' . $loan_model->lno ?? '';
                $second_title = $second_lang->_('loan_back_name') . '-' . $loan_model->lno ?? '';
                $whereKey = "email_loan_back_apply_where";
                $content_type = 2;
                break;
            //合同(包含销售合同)
            case Enums::WF_CONTRACT_TYPE1:
            case Enums::WF_CONTRACT_TYPE20:
            case Enums::WF_CONTRACT_TYPE21:
            case Enums::WF_CONTRACT_TYPE22:
            case Enums::WF_CONTRACT_TYPE23:
            case Enums::WF_CONTRACT_TYPE24:
                $item = Contract::findFirst([
                    'id = :id:',
                    'bind' => ['id' => $request->biz_value]
                ]);
                if (empty($item)) {
                    throw new \Exception("no item, type = {$request->biz_type}, id = {$request->biz_value}", ErrCode::$SYSTEM_ERROR);
                }
                $item = $item->toArray();

                // 合同分类map
                $contract_category_map = EnumsService::getInstance()->getContractCategorysMap();

                // 直属分类信息
                $contract_category_info = $contract_category_map[$item['template_id']] ?? [];
                $template_id = $contract_category_info['translation_key'] ?? '';

                $item['template_title'] = $first_lang->_($template_id);
                $item['template_second_title'] = $second_lang->_($template_id);
                $first_title = $item['template_title'] . '-' . $item['cno'];
                $second_title = $item['template_second_title'] . '-' . $item['cno'];
                $whereKey = 'email_contract_apply_where';
                $content_type = 2;
                break;
            // 网点租房合同
            case Enums::WF_STORE_RENTING_CONTRACT_TYPE:
                $item = ContractStoreRentingModel::findFirst([
                    'id = :id:',
                    'bind' => ['id' => $request->biz_value]
                ]);

                if (empty($item)) {
                    throw new \Exception("no item,type=14,id = " . $request->biz_value);
                }

                $first_title = $first_lang->_('email_store_rent_contract_name') . '-' . $item->contract_id;
                $second_title = $second_lang->_('email_store_rent_contract_name') . '-' . $item->contract_id;
                $whereKey = "email_store_rent_contract_apply_where";
                $content_type = 2;
                break;
            //供应商管理
            case Enums::WF_VENDOR_BIZ_TYPE:
                // 供应商信息审核
                $item = Vendor::getFirst([
                    'id = :id:',
                    'bind' => ['id' => $request->biz_value],
                    'columns' => ['vendor_id']
                ]);
                if (empty($item)) {
                    throw new \Exception("workflow biz data is bull, type = 59, id = " . $request->biz_value);
                }

                $first_title = $first_lang->_('vendor_audit_email_apply_name') . '-' . $item->vendor_id;
                $second_title = $second_lang->_('vendor_audit_email_apply_name') . '-' . $item->vendor_id;
                $whereKey = 'email_vendor_apply_where';
                $content_type = 2;
                break;
            //采购申请单
            case Enums::WF_PURCHASE_APPLY:
                $item = PurchaseApply::findFirst([
                    'id=:id:',
                    'bind' => ['id' => $request->biz_value]
                ]);
                if (empty($item)) {
                    throw new \Exception("no item,type=9,id=" . $request->biz_value);
                }

                $first_biz_title = $first_lang->_("purchase_apply_name");
                $second_biz_title = $second_lang->_("purchase_apply_name");

                $first_title = $first_biz_title . '-' . $item->pano;
                $second_title = $second_biz_title . '-' . $item->pano;
                $whereKey = "email_purchase_apply_apply_where";
                $content_type = 2;
                break;
            //采购订单
            case Enums::WF_PURCHASE_ORDER:
                $item = PurchaseOrder::findFirst([
                    'id=:id:',
                    'bind' => ['id' => $request->biz_value]
                ]);
                if (empty($item)) {
                    throw new \Exception("no item,type=10,id=" . $request->biz_value);
                }

                $first_biz_title = $first_lang->_("purchase_order_name");
                $second_biz_title = $second_lang->_("purchase_order_name");

                $first_title = $first_biz_title . '-' . $item->pono;
                $second_title = $second_biz_title . '-' . $item->pono;
                $whereKey = "email_purchase_order_apply_where";
                $content_type = 2;
                break;
            // 预算导入
            case Enums::BUDGET_OB_TYPE:
                $first_title = $first_lang->_('budget_object_import');
                $second_title = $second_lang->_('budget_object_import');
                $whereKey = "email_budget_object_import_apply_where";
                $content_type = 2;
                break;
            // 预算调整
            case Enums::WF_BUDGET_ADJUST_TYPE:
                $item = BudgetAdjustModel::findFirst([
                    'id = :id:',
                    'bind' => ['id' => $request->biz_value]
                ]);
                if (empty($item)) {
                    throw new \Exception("no item,type=91,id=" . $request->biz_value);
                }
                $first_title = $first_lang->_('budget_adjust_method_text') . '-' . $item->adjust_no;
                $second_title = $second_lang->_('budget_adjust_method_text') . '-' . $item->adjust_no;
                $whereKey = "email_budget_adjust_apply_where";
                $content_type = 2;
                break;
            // 薪资发放审批
            case Enums::WF_SALARY_APPLY:
                $item = PaySalaryApply::findFirst([
                    'id = :id:',
                    'bind' => ['id' => $request->biz_value]
                ]);
                if (empty($item)) {
                    throw new \Exception("no item, type = 43, id = " . $request->biz_value);
                }
                $first_title = $first_lang->_('pay_salary_apply_name') . "-" . $item->xzno;
                $second_title = $second_lang->_('pay_salary_apply_name') . "-" . $item->xzno;
                $whereKey = "pay_salary_apply_apply_where";
                $content_type = 2;
                break;
            // 薪资扣款审批
            case Enums::WF_WAGES_TYPE:
                $item = WagesModel::findFirst([
                    'id = :id:',
                    'bind' => ['id' => $request->biz_value]
                ]);
                if (empty($item)) {
                    throw new \Exception("no item,type=15,id=" . $request->biz_value);
                }
                $first_title = $first_lang->_('email_wages_name') . "-" . $item->no;
                $second_title = $second_lang->_('email_wages_name') . "-" . $item->no;
                $whereKey = "email_wages_apply_where";
                $content_type = 2;
                break;
            // CRM 报价审批
            case Enums::WF_CRM_QUOTATION:
                $item = CrmQuotationApplyModel::findFirst([
                    'id = :id:',
                    'bind' => ['id' => $request->biz_value]
                ]);
                if (empty($item)) {
                    throw new \Exception("no item,type=53,id=" . $request->biz_value);
                }
                $first_title = $first_lang->_('email_crm_quotation_name') . "-" . $item->quoted_price_list_sn;
                $second_title = $second_lang->_('email_crm_quotation_name') . "-" . $item->quoted_price_list_sn;
                $whereKey = "email_crm_quotation_where";
                break;
            default:
                throw new ValidationException("no type=" . $request->biz_type);
        }
        $company_ids = EnumsService::getInstance()->getSysDepartmentCompanyIds();
        if (get_country_code() == GlobalEnums::TH_COUNTRY_CODE) {
            // 泰国-邮件内容
            if ($content_type == 1) {
                //邮件标题翻译
                if (isset($company_id) && $company_id == $company_ids['FlashExpress']) {
                    //邮件内容1 (图文)
                    $email_express_images = EnvModel::getEnvByCode('email_th_express_image');
                    $waitKey = "email_submitted";
                    $contentKey = "email_pass_th_express_content";
                } else {
                    //邮件内容2 (图文,跟内容1图片不同)
                    $email_express_images = EnvModel::getEnvByCode('email_th_no_express_image');
                    $waitKey = "email_submitted";
                    $contentKey = "email_pass_th_no_express_content";
                }
                $first_wait = $first_lang->_($waitKey);
                $second_wait = $second_lang->_($waitKey);

                //邮件标题
                $title = $first_wait . ' ' . $second_wait . "：" . $first_title . " " . $second_title;
                //邮件内容
                $first_content = $first_lang->_($contentKey, ["title" => $first_title]);
                $second_content = $second_lang->_($contentKey, ["title" => $second_title]);
                //oa地址
                // 各环境各国家dashboard页地址
                $url = EnvModel::getEnvByCode(GlobalEnums::OA_DASHBOARD_PAGE_URL_CODE);
                $url = !empty($url) ? $url : GlobalEnums::OA_DASHBOARD_PAGE_URL_DEFAULT;

                $html = <<<EOF
hi,<br/>
<span style="margin-left: 16px"></span>{$first_content}<br/>
<span style="margin-left: 16px"></span>{$second_content}<br/>
<span style="margin-left: 16px"></span>oa链接: <a href="{$url}" target="_blank">{$url}</a><br/><br/>
<img src="{$email_express_images}" alt="qr_image">
EOF;
            } elseif ($content_type == 2) {
                // 泰国 邮件内容3 (文字,无图片)
                //邮件标题翻译
                $waitKey = "email_pass";
                $contentKey = "email_pass_th_content_2";
                $first_wait = $first_lang->_($waitKey);
                $second_wait = $second_lang->_($waitKey);

                //邮件标题
                $title = $first_wait . $second_wait . "：" . $first_title . " " . $second_title;
                //邮件内容
                $first_where = $first_lang->_($whereKey);
                $second_where = $second_lang->_($whereKey);
                $first_content = $first_lang->_($contentKey, ["title" => $first_title, "where" => $first_where]);
                $second_content = $second_lang->_($contentKey, ["title" => $second_title, "where" => $second_where]);
                //oa地址
                // 各环境各国家dashboard页地址
                $url = EnvModel::getEnvByCode(GlobalEnums::OA_DASHBOARD_PAGE_URL_CODE);
                $url = !empty($url) ? $url : GlobalEnums::OA_DASHBOARD_PAGE_URL_DEFAULT;

                $html = <<<EOF
    hi,<br/>
    <span style="margin-left: 16px"></span>{$first_content}<a href="{$url}" target="_blank">{$url}</a><br/>
    <span style="margin-left: 16px"></span>{$second_content}<a href="{$url}" target="_blank">{$url}</a>

EOF;
            }
        } else {
            // 非泰国 内容3
            //邮件标题翻译
            $waitKey = "email_pass";
            $contentKey = "email_pass_th_content_2";
            $first_wait = $first_lang->_($waitKey);
            $second_wait = $second_lang->_($waitKey);

            //邮件标题
            $title = $first_wait . $second_wait . "：" . $first_title . " " . $second_title;
            //邮件内容
            $first_where = $first_lang->_($whereKey);
            $second_where = $second_lang->_($whereKey);
            $first_content = $first_lang->_($contentKey, ["title" => $first_title, "where" => $first_where]);
            $second_content = $second_lang->_($contentKey, ["title" => $second_title, "where" => $second_where]);
            //oa地址
            // 各环境各国家dashboard页地址
            $url = EnvModel::getEnvByCode(GlobalEnums::OA_DASHBOARD_PAGE_URL_CODE);
            $url = !empty($url) ? $url : GlobalEnums::OA_DASHBOARD_PAGE_URL_DEFAULT;

            $html = <<<EOF
    hi,<br/>
    <span style="margin-left: 16px"></span>{$first_content}<a href="{$url}" target="_blank">{$url}</a><br/>
    <span style="margin-left: 16px"></span>{$second_content}<a href="{$url}" target="_blank">{$url}</a>

EOF;
        }
        // 菲律宾报价审批特殊邮件, 其他国家不发
        if ($request->biz_type == Enums::WF_CRM_QUOTATION && get_country_code() == GlobalEnums::PH_COUNTRY_CODE) {
            $url = EnvModel::getEnvByCode(GlobalEnums::CRM_DASHBOARD_PAGE_URL_CODE);
            $title = $second_title;
            $contentKey = "email_pass_th_content_2";
            $first_where = $first_lang->_($whereKey);
            $second_where = $second_lang->_($whereKey);
            $first_content = $first_lang->_($contentKey, ["title" => $first_title, "where" => $first_where]);
            $second_content = $second_lang->_($contentKey, ["title" => $second_title, "where" => $second_where]);
            $html = <<<EOF
    hi,<br/>
    <span style="margin-left: 16px"></span>{$first_content}<a href="{$url}" target="_blank">{$url}</a><br/>
    <span style="margin-left: 16px"></span>{$second_content}<a href="{$url}" target="_blank">{$url}</a>
EOF;
        }


        return [$title, $html];
    }

    /**
     * 16911【ALL|OA】采购申请优化
     * 针对节点audit_type = 4的自动通过，无需点击审批的节点入队列
     * @param array $params 入队参数组
     * @return bool
     */
    public function redisListAdd(array $params = [])
    {
        $result = false;
        try {
            $redis_data = json_encode($params, JSON_UNESCAPED_UNICODE);
            //入队列
            $redis = $this->getDI()->get('redis');
            $redis->lpush(self::$redis_list_name, $redis_data);
            $this->logger->info('workflow_auto_approval_ignore_redis_list_add lpush:' . $redis_data);
            $result = true;
        } catch (\Exception $e) {
            $this->logger->warning('workflow_auto_approval_ignore_redis_list_add exception:' . $e->getMessage() . $e->getTraceAsString());
        }
        return $result;
    }

    /**
     * 16911【ALL|OA】采购申请优化
     * 针对节点audit_type = 4的自动通过，无需点击审批的节点消费队列、发送邮件
     * @param array $redis_data 队列内容组
     */
    public function consumptionRedisList($redis_data)
    {
        try {
            $request_id = $redis_data['request_id'] ?? 0;
            $flow_node_id = $redis_data['flow_node_id'] ?? 0;
            $sub_node_id = $redis_data['sub_node_id'] ?? 0;
            if (empty($request_id)  ||  empty($flow_node_id)) {
                throw new ValidationException('解析队列数据中request_id = ' . $request_id . '或flow_node_id = ' . $flow_node_id . '，主要参数为空', ErrCode::$VALIDATE_ERROR);
            }

            //第一步根据请求id、节点id、子节点id在审批人中找到自动通过的审批人
            $node_auditor_list = WorkflowRequestNodeAuditorModel::find([
                'columns' => 'auditor_id',
                'conditions' => 'request_id = :request_id: and flow_node_id = :flow_node_id: and sub_node_id = :sub_node_id: and audit_status = :audit_status:',
                'bind' => ['request_id' => $request_id, 'flow_node_id' => $flow_node_id, 'sub_node_id' => $sub_node_id, 'audit_status' => Enums::WF_STATE_AUTO_APPROVED]
            ])->toArray();
            if (empty($node_auditor_list)) {
                throw new ValidationException('根据request_id = ' . $request_id . '，flow_node_id = ' . $flow_node_id . '，sub_node_id = ' . $sub_node_id . '，audit_status=' . Enums::WF_STATE_AUTO_APPROVED . '，未找到符合条件的工作流申请节点审核人', ErrCode::$VALIDATE_ERROR);
            }

            //第二步找到审批人，则需要找到该请求的节点信息
            $request = WorkflowRequestModel::findFirst([
                'columns' => 'biz_type, name, biz_value',
                'conditions' => 'id = :request_id:',
                'bind' => ['request_id' => $request_id]
            ]);
            if (empty($request)) {
                throw new ValidationException('根据request_id = ' . $request_id . '，未找到工作流申请信息', ErrCode::$VALIDATE_ERROR);
            }

            //第三步获取申请人邮箱
            $node_auditors = array_column($node_auditor_list, 'auditor_id');

            $emails = (new StaffInfoModel())->getEmails($node_auditors);
            if (empty($emails)) {
                throw new ValidationException('通过申请人 ' . implode(',', $node_auditors) . '，未找到邮箱信息', ErrCode::$VALIDATE_ERROR);
            }

            //第四步设置邮件语言
            $language_setting = $this->getNoticeEmailTemplateLang();
            $first_lang = $language_setting['first_lang'];
            $second_lang = $language_setting['second_lang'];

            //第五步获取邮件内容
            [$title, $content] = $this->getAutoApprovalEmailContent($request, $first_lang, $second_lang);
            //第六步发送邮件
            $log = ['emails' => $emails, 'title' => $title, 'html' => $content];
            $send_res = $this->mailer->sendAsync($emails, $title, $content);
            if ($send_res) {
                $this->logger->info('workflowConsumptionRedisList 发送成功！' . json_encode($log, JSON_UNESCAPED_UNICODE));
            } else {
                $this->logger->warning('workflowConsumptionRedisList 发送失败！' . json_encode($log, JSON_UNESCAPED_UNICODE));
            }
        } catch (ValidationException $e) {
            $this->logger->info('workflowConsumptionRedisList 校验异常！原因可能是：' . $e->getMessage());
        } catch (\Exception $e) {
            $this->logger->warning('workflowConsumptionRedisList 发送异常原因可能是：' . $e->getMessage());
        }
    }

    /**
     *  16911【ALL|OA】采购申请优化
     * 针对节点audit_type = 4的自动通过，无需点击审批的节点，按照业务获取不同的邮件内容
     * @param object $request 审批流申请对象信息
     * @param  NativeArray $first_lang 一级语言包
     * @param NativeArray $second_lang 二级语言包
     * @return array
     * @throws ValidationException
     */
    public function getAutoApprovalEmailContent($request, $first_lang, $second_lang)
    {
        switch ($request->biz_type) {
            //采购申请单
            case Enums::WF_PURCHASE_APPLY:
                $first_biz_title = $first_lang->_('purchase_apply_name');
                $second_biz_title = $second_lang->_('purchase_apply_name');

                //获取申请单号
                preg_match('/[a-zA-Z\d]+/', $request->name, $match);
                $pano = $match[0] ?? '';

                $first_title = $first_biz_title . '-' . $pano;
                $second_title = $second_biz_title . '-' . $pano;
                $whereKey = 'email_purchase_apply_apply_where';
                break;
            //采购订单
            case Enums::WF_PURCHASE_ORDER:
                $first_biz_title = $first_lang->_('purchase_order_name');
                $second_biz_title = $second_lang->_('purchase_order_name');

                //获取申请单号
                preg_match('/[a-zA-Z\d]+/', $request->name, $match);
                $pono = $match[0] ?? '';

                $first_title = $first_biz_title . '-' . $pono;
                $second_title = $second_biz_title . '-' . $pono;
                $whereKey = 'email_purchase_order_apply_where';
                break;
            default:
                throw new ValidationException('no type=' . $request->biz_type, ErrCode::$VALIDATE_ERROR);
        }
        //邮件标题翻译
        $waitKey = 'email_auto_approval_title';
        $contentKey = 'email_auto_approval_content';
        $first_wait = $first_lang->_($waitKey);
        $second_wait = $second_lang->_($waitKey);

        //邮件标题
        $title = $first_wait . $second_wait;

        //邮件内容
        $first_where = $first_lang->_($whereKey);
        $second_where = $second_lang->_($whereKey);

        $first_content = $first_lang->_($contentKey, ['title' => $first_title, 'where' => $first_where]);
        $second_content = $second_lang->_($contentKey, ['title' => $second_title, 'where' => $second_where]);

        //oa地址
        // 各环境各国家dashboard页地址
        $url = EnvModel::getEnvByCode(GlobalEnums::OA_DASHBOARD_PAGE_URL_CODE);
        $url = !empty($url) ? $url : GlobalEnums::OA_DASHBOARD_PAGE_URL_DEFAULT;

        $html = <<<EOF
    hi,<br/>
    <span style="margin-left: 16px"></span>{$first_content}<a href="{$url}" target="_blank">{$url}</a><br/>
    <span style="margin-left: 16px"></span>{$second_content}<a href="{$url}" target="_blank">{$url}</a>

EOF;
        return [$title, $html];
    }
}