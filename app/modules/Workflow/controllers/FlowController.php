<?php


namespace App\Modules\Workflow\Controllers;


use App\Library\BaseController;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Workflow\Services\WorkflowParser;
use App\Modules\Workflow\Services\WorkflowService;
use App\Modules\Workflow\Services\WorkflowServiceV2;

class FlowController extends BaseController
{

    /**
     * @Permission(action='user.permission.grant')
     */
    public function listAction()
    {
        $wfs = new WorkflowService();
        $flows = $wfs->getAllFlow();
        $data = [];
        if ($flows){
            foreach ($flows as $flow){
                $parser = new WorkflowParser($flow->id);
                $parser->parse();
                $data[] = $parser->getFlowInfo();
            }

        }
        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $data);

    }

    /**
     * 获取审批流节点
     */
    public function getFlowNodesAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, [
                'flow_id' => 'Required|IntGe:1',
            ]);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = (new WorkflowServiceV2())->getFlowNodesAndRelations($params['flow_id']);

        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res);
    }
    /**
     * 工作流线
     */
    public function klineAction()
    {
        try {
            $params = $this->request->get();
            Validation::validate($params, [
                'flow_id' => 'Required|IntGe:1',
            ]);
            $wfs = new WorkflowServiceV2();
            $flows = $wfs->getWFLine($params['flow_id']);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $flows);
    }

}