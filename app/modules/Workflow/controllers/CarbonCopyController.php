<?php

namespace App\Modules\Workflow\Controllers;

use App\Library\BaseController;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Workflow\Services\CarbonCopyService;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class CarbonCopyController extends BaseController
{
    /**
     * 获取枚举
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/79212
     */
    public function getDefaultAction()
    {
        $res = CarbonCopyService::getInstance()->getDefault();
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 抄送管理-我的抄送-列表
     * @Permission(menu='carbon_copy.my')
     * @return Response|ResponseInterface
     * @throws ValidationException
     * @api https://yapi.flashexpress.pub/project/133/interface/api/79207
     */
    public function listAction()
    {
        $params = $this->request->get();
        $params = params_filter($params, CarbonCopyService::$not_must_list);
        Validation::validate($params, CarbonCopyService::$validate_list_search);
        $res = CarbonCopyService::getInstance()->getList($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 抄送管理-我的抄送-列表-已读
     * @Permission(menu='carbon_copy.my')
     * @return Response|ResponseInterface
     * @throws ValidationException
     * @api https://yapi.flashexpress.pub/project/133/interface/api/79227
     */
    public function readAction()
    {
        $params = $this->request->get();
        Validation::validate($params, CarbonCopyService::$validate_read);
        $res = CarbonCopyService::getInstance()->read($params['id'], $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }
}
