<?php

namespace App\Modules\Workflow\Controllers;

use App\Library\BaseController;
use App\Library\BaseService;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Workflow\Services\WorkflowCanvasService;

class CanvasController extends BaseController
{
    /**
     * 模糊搜索审批流名称
     * @Permission(action='permission.workflow_setting.canvas')
     * @throws ValidationException
     * @api https://yapi.flashexpress.pub/project/133/interface/api/70887
     */
    public function getWorkflowNameAction()
    {
        $params = $this->request->get();
        Validation::validate($params, WorkflowCanvasService::$validate_search_workflow_name);
        $res = WorkflowCanvasService::getInstance()->getWorkflowNameItems($params['workflow_name'] ?? '');
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 获取所有模块
     * @Permission(action='permission.workflow_setting.canvas')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/70882
     */
    public function getDefaultAction()
    {
        $res = WorkflowCanvasService::getInstance()->getDefault();
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 审批流画布-列表
     * @Permission(action='permission.workflow_setting.canvas')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @api https://yapi.flashexpress.pub/project/133/interface/api/70852
     */
    public function listAction()
    {
        $params = $this->request->get();
        $params = params_filter($params, WorkflowCanvasService::$not_must_list);
        Validation::validate($params, WorkflowCanvasService::$validate_list_search);
        $res = WorkflowCanvasService::getInstance()->getList($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 审批流画布-审批流详情(树结构)
     * @Permission(action='permission.workflow_setting.canvas')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @api https://yapi.flashexpress.pub/project/133/interface/api/70862
     */
    public function detailTreeAction()
    {
        $params = $this->request->get();
        Validation::validate($params, WorkflowCanvasService::$validate_workflow_detail);
        $res = WorkflowCanvasService::getInstance()->getDetailTree($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 审批流画布-节点详情
     * @Permission(action='permission.workflow_setting.canvas')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @api https://yapi.flashexpress.pub/project/133/interface/api/70872
     */
    public function detailNodeAction()
    {
        $params = $this->request->get();
        Validation::validate($params, WorkflowCanvasService::$validate_node_detail);
        $res = WorkflowCanvasService::getInstance()->getDetailNode($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }
}
