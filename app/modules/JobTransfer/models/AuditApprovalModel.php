<?php


namespace App\Modules\JobTransfer\Models;


use App\Models\Base;

class AuditApprovalModel extends Base
{
    const APPROVAL_STATUS_PENDING = 1;         //待审批
    const APPROVAL_STATUS_APPROVAL = 2;        //审批同意
    const APPROVAL_STATUS_REJECTED = 3;        //审批驳回
    const APPROVAL_STATUS_CANCEL = 4;          //审批撤销
    const APPROVAL_STATUS_TIMEOUT = 5;         //审批超时


    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_backyard');
        // setSource()方法指定数据库表
        $this->setSource('audit_approval');
    }
}