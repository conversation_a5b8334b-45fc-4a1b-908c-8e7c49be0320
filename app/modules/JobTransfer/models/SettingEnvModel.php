<?php


namespace App\Modules\JobTransfer\Models;


use App\Models\Base;

class SettingEnvModel extends Base
{
    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_backyard');
        // setSource()方法指定数据库表
        $this->setSource('setting_env');
    }

    /**
     * 获取配置项
     * @param string $code
     * @return string $return
     */
    public function getSetVal(string $code = ''): string
    {
        if (empty($code)) {
            return '';
        }

        $setting = $this->findFirst([
            'conditions' => "code = :code:",
            'bind' => ['code' => $code],
            'columns' => ['set_val']
        ]);

        return !empty($setting) ? $setting->set_val : '';
    }
}