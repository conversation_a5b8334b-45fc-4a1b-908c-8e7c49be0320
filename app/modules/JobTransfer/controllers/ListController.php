<?php

namespace App\Modules\JobTransfer\Controllers;

use App\Library\Enums;
use App\Library\Enums\DownloadCenterEnum;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Models\backyard\JobTransferModel;
use App\Modules\Common\Services\DownloadCenterService;
use App\Modules\JobTransfer\Services\AuditService;
use App\Modules\JobTransfer\Services\BaseService;
use App\Modules\JobTransfer\Services\ListService;

class ListController extends BaseController
{
    /**
     * 转岗管理-转岗申请-查询列表
     * @Permission(action='transfer.apply.search')
     */
    public function searchListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, ListService::$not_must_params);
        try {
            Validation::validate($params, ListService::$validate_list_search);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $params['submitter_id'] = $this->user['id'];
        $params['type']         = [JobTransferModel::TRANSFER_TYPE_FRONT_LINE, JobTransferModel::TRANSFER_TYPE_NOT_FRONT_LINE];
        $returnArr              = ListService::getInstance()->getApplyList($params, $this->user['id']);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $returnArr);
    }

    /**
     * 转岗管理-特殊转岗申请-查询列表
     * @Permission(action='transfer.special.search')
     */
    public function searchSpecialListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, ListService::$not_must_params);
        try {
            Validation::validate($params, ListService::$validate_list_search);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $params['type']         = [JobTransferModel::TRANSFER_TYPE_SPECIAL];
        $returnArr              = ListService::getInstance()->getApplyList($params, $this->user['id']);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $returnArr);
    }

    /**
     * 转岗管理-转岗审核-列表
     * @Permission(action='transfer.audit.search')
     */
    public function searchAuditListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, ListService::$not_must_params);
        try {
            Validation::validate($params, ListService::$validate_audit_list_search);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $params['submitter_id'] = $this->user['id'];
        $returnArr              = ListService::getInstance()->getAuditList($params, $this->user['id']);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $returnArr);
    }

    /**
     * 转岗管理-BP转岗审核-列表
     * @Permission(action='transfer.bp_audit.search')
     */
    public function searchBpAuditListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, ListService::$not_must_params);
        try {
            Validation::validate($params, ListService::$validate_audit_list_search);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $params['submitter_id'] = $this->user['id'];
        $returnArr              = ListService::getInstance()->getBpAuditList($params, $this->user['id']);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $returnArr);
    }

    /**
     * 数据查询-查询列表
     * @Permission(action='transfer.search.search')
     */
    public function searchJobTransferListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, ListService::$not_must_params);
        try {
            Validation::validate($params, ListService::$validate_apply_list_search);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        // 获取转岗数据查询list
        $returnArr = ListService::getInstance()->searchApplyList($params, $this->user['id']);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $returnArr);
    }

    /**
     * @description 数据查询-导出
     * @throws ValidationException
     * @Token
     */
    public function exportAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, ListService::$not_must_params);
        Validation::validate($params, ListService::$validate_export);

        $lockKey  = AuditService::getInstance()->getSingleAuditLockKey('export', $this->user['id']);
        $res = $this->atomicLock(function() use ($params){
            // 大于指定数量, 添加异步任务 导出
            $result = DownloadCenterService::getInstance()->addDownloadCenter($this->user['id'], DownloadCenterEnum::JOB_TRANSFER_DATA_EXPORT, $params);
            $result['data'] = [
                'export_method' => DownloadCenterEnum::EXPORT_METHOD_ASYNC,
                'file_url' => ''
            ];
            return $result;
        }, $lockKey, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * @description 数据查询-Payroll导出
     * @throws ValidationException
     * @Token
     */
    public function exportForPayrollAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, ListService::$not_must_params);
        Validation::validate($params, ListService::$validate_export);

        $lockKey  = AuditService::getInstance()->getSingleAuditLockKey('export', $params['id']);
        $res = $this->atomicLock(function() use ($params){
            $params['operate_id'] = $this->user['id'];
            $result = DownloadCenterService::getInstance()->addDownloadCenter($this->user['id'], DownloadCenterEnum::JOB_TRANSFER_DATA_FOR_PAYROLL_EXPORT, $params);
            $result['data'] = [
                'export_method' => DownloadCenterEnum::EXPORT_METHOD_ASYNC,
                'file_url' => ''
            ];
            return $result;
        }, $lockKey, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }
}