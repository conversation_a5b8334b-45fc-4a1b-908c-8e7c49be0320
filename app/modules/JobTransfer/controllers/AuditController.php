<?php

namespace App\Modules\JobTransfer\Controllers;

use App\Library\Enums\ByWorkflowEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\JobTransfer\Services\AuditService;
use App\Modules\JobTransfer\Services\BaseService;
use App\Modules\JobTransfer\Services\JobTransferService;
use App\Modules\JobTransfer\Services\ListService;

class AuditController extends BaseController
{

    private const EXPIRE_TIME = 10;

    /**
     * 审核同意
     * @Permission(action='transfer.audit.audit')
     */
    public function approvalAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, AuditService::$not_must_params);
        try {
            Validation::validate($params, AuditService::$validate_approval);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $params['status'] = ByWorkflowEnums::BY_OPERATE_PASS;
        $lockKey          = AuditService::getInstance()->getSingleAuditLockKey('approval', $params['id']);

        $res = $this->atomicLock(function() use ($params){
            return AuditService::getInstance()->audit($params, $this->user['id']);
        }, $lockKey, self::EXPIRE_TIME, false);

        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 审核驳回
     * @Permission(action='transfer.audit.audit')
     */
    public function rejectAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, AuditService::$not_must_params);
        try {
            Validation::validate($params, AuditService::$validate_reject);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $params['status'] = ByWorkflowEnums::BY_OPERATE_REJECT;
        $lockKey          = AuditService::getInstance()->getSingleAuditLockKey('reject', $params['id']);

        $res = $this->atomicLock(function () use ($params) {
            return AuditService::getInstance()->audit($params, $this->user['id']);
        }, $lockKey, self::EXPIRE_TIME, false);

        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 审核撤销
     * @Permission(action='transfer.audit.audit')
     */
    public function cancelAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, AuditService::$not_must_params);
        try {
            Validation::validate($params, AuditService::$validate_approval);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $params['status'] = ByWorkflowEnums::BY_OPERATE_CANCEL;
        $lockKey          = AuditService::getInstance()->getSingleAuditLockKey('cancel', $params['id']);

        $res = $this->atomicLock(function() use ($params){
            return AuditService::getInstance()->audit($params, $this->user['id']);
        }, $lockKey, self::EXPIRE_TIME, false);

        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * @description 批量同意
     * @Permission(action='transfer.audit.audit')
     */
    public function batchApprovalAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, AuditService::$not_must_params);
        try {
            Validation::validate($params, AuditService::$validate_batch_approval);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $params['status'] = ByWorkflowEnums::BY_OPERATE_PASS;
        $lockKey          = AuditService::getInstance()->getBatchAuditLockKey('batch_approval', $params['audit_ids']);

        $res = $this->atomicLock(function() use ($params){
            return AuditService::getInstance()->batchAudit($params, $this->user['id']);
        }, $lockKey, self::EXPIRE_TIME, false);

        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * @description 批量驳回
     * @Permission(action='transfer.audit.audit')
     */
    public function batchRejectAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, AuditService::$not_must_params);
        try {
            Validation::validate($params, AuditService::$validate_batch_reject);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $params['status'] = ByWorkflowEnums::BY_OPERATE_REJECT;
        $lockKey          = AuditService::getInstance()->getBatchAuditLockKey('batch_reject', $params['audit_ids']);

        $res = $this->atomicLock(function() use ($params){
            return AuditService::getInstance()->batchAudit($params, $this->user['id']);
        }, $lockKey, self::EXPIRE_TIME, false);

        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * @description BP审核同意
     * @Permission(action='transfer.bp_audit.audit')
     */
    public function bpApprovalAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, AuditService::$not_must_params);
        try {
            Validation::validate($params, AuditService::$validate_bp_approval);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $params['status'] = ByWorkflowEnums::BY_OPERATE_PASS;
        $lockKey          = AuditService::getInstance()->getSingleAuditLockKey('bp_approval', $params['id']);

        $res = $this->atomicLock(function () use ($params) {
            return AuditService::getInstance()->bpAudit($params, $this->user['id']);
        }, $lockKey, self::EXPIRE_TIME, false);

        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * @description BP审核驳回
     * @Permission(action='transfer.bp_audit.audit')
     */
    public function bpRejectAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, AuditService::$not_must_params);
        try {
            Validation::validate($params, AuditService::$validate_bp_reject);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $params['status'] = ByWorkflowEnums::BY_OPERATE_REJECT;
        $lockKey          = AuditService::getInstance()->getSingleAuditLockKey('bp_reject', $params['id']);

        $res = $this->atomicLock(function() use ($params){
            return AuditService::getInstance()->bpAudit($params, $this->user['id']);
        }, $lockKey, self::EXPIRE_TIME, false);

        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }
}