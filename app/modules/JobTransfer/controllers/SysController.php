<?php


namespace App\Modules\JobTransfer\Controllers;


use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\JobTransfer\Services\DetailService;
use App\Modules\JobTransfer\Services\SysService;
use App\Traits\TokenTrait;

class SysController extends BaseController
{
    /**
     * 获取转岗类型下拉
     * @Token
     */
    public function getJobTransferTypesAction()
    {
        //[1]业务处理
        $data = SysService::getInstance()->getJobTransferTypes();

        //[2]数据返回
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }

    /**
     * 获取转岗状态下拉
     * @Token
     */
    public function getJobTransferStatusAction()
    {
        //[1]业务处理
        $data = SysService::getInstance()->getJobTransferState();

        //[2]数据返回
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }

    /**
     * 获取转岗审核状态下拉
     * @Token
     */
    public function getApprovalStatusAction()
    {
        //[1]业务处理
        $params = $this->request->get();
        try {
            Validation::validate($params, DetailService::$validate_approval_state);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $data = SysService::getInstance()->getJobTransferAuditState($params);

        //[2]数据返回
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }

    /**
     * 获取转岗部门下拉
     * @Token
     */
    public function getJobTransferDepartmentAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, DetailService::$validate_department);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        //[1]业务处理
        $data = SysService::getInstance()->getDepartmentList($params);

        //[2]数据返回
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }

    /**
     * 获取转岗职位下拉
     * @Token
     */
    public function getJobTransferPositionAction()
    {
        $params = $this->request->get();
        try {

            $country_code = get_country_code();
            $validate =  GlobalEnums::MY_COUNTRY_CODE == $country_code ? DetailService::$validate_position_job : DetailService::$validate_position;

            Validation::validate($params, $validate);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        //[1]业务处理
        $data = SysService::getInstance()->getPositionList($params);

        //[2]数据返回
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }

    /**
     * 获取工作天数和轮休规则
     * @Token
     */
    public function getWorkingDayRestTypeAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, DetailService::$validate_working_day);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        //[1]业务处理
        $data = SysService::getInstance()->getWorkingDayRestType($params);

        //[2]数据返回
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }

    /**
     * 获取转岗网点下拉
     * @Token
     */
    public function getJobTransferStoreAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, DetailService::$validate_position);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        //[1]业务处理
        $data = SysService::getInstance()->getStoreList($params);

        //[2]数据返回
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }

    /**
     * 获取转岗HCId下拉
     * @Token
     */
    public function getJobTransferHcIdAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, DetailService::$validate_hc_list);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        //[1]业务处理
        $data = SysService::getInstance()->getHcList($params);

        //[2]数据返回
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }

    /**
     * 获取车辆来源下拉
     * @Token
     */
    public function getCarOwnerAction()
    {
        //[1]业务处理
        $data = SysService::getInstance()->getJobTransferCarOwner();

        //[2]数据返回
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }

    /**
     * 转岗来源下拉
     * @Token
     */
    public function getDataSourceAction()
    {
        //[1]业务处理
        $data = SysService::getInstance()->getJobTransferDataSource();

        //[2]数据返回
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }

    /**
     * 获取转岗角色下拉
     * @Token
     */
    public function getRoleListAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, DetailService::$validate_role_list);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        //[1]业务处理
        $data = SysService::getInstance()->getRoleList($params);

        //[2]数据返回
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }

    /**
     * 获取转岗原因下拉
     * @Token
     */
    public function getJobTransferReasonAction()
    {
        //[1]业务处理
        $data = SysService::getInstance()->getTransferReason();

        //[2]数据返回
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }

    /**
     * 获取批号
     * @Token
     * @throws \Exception
     */
    public function getBatchNumberAction()
    {
        //[1]业务处理
        $res = SysService::getInstance()->getBatchNumber();

        //[2]数据返回
        return $this->returnJson($res['code'], $res['message'] ?? 'success', $res['data'] ?? null);
    }

    /**
     * 转岗管理-数据查询-导出payroll 权限员工
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function getExportForPayrollPermissionAction(){
       $staffIds = SysService::getInstance()->getExportForPayrollPermissionStaffIds();
        return $this->returnJson(ErrCode::$SUCCESS, 'success', ['staff_ids' => $staffIds]);
    }

    /**
     * 获取薪资信息下拉
     * @Token
     */
    public function getSalaryTypeAction()
    {
        //[1]业务处理
        $data = SysService::getInstance()->getTransferSalaryType();

        //[2]数据返回
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }

    /**
     * 获取项目期数下拉
     * @Token
     */
    public function getProjectNumAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, DetailService::$validate_job_title);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        //[1]业务处理
        $data = SysService::getInstance()->getTransferProjectNum($params);

        //[2]数据返回
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }

    /**
     * 获取薪资范围
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @Token
     */
    public function getSalaryRangeAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, DetailService::$validate_detail);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        //[1]业务处理
        $data = SysService::getInstance()->getSalaryRange($params);

        //[2]数据返回
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }

    /**
     * 获取车类型下拉
     * 使用场景：在 HRBP审批时，获取可选择的车类型
     * @Token
     */
    public function getCarTypeAction()
    {
        //[1]业务处理
        $params = $this->request->get();
        try {
            Validation::validate($params, DetailService::$validate_detail);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $data = SysService::getInstance()->getTransferCarType($params['id']);

        //[2]数据返回
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }

    /**
     * 获取车类型下拉
     * 使用场景：提交申请之前，获取可选择的车类型
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function getCarTypeByJobTitleAction()
    {
        //[1]业务处理
        $params = $this->request->get();
        try {
            Validation::validate($params, DetailService::$validate_job_title);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $data = SysService::getInstance()->getCarTypeByJobTitle($params['job_title_id']);

        //[2]数据返回
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }

    /**
     * 获取雇佣类型下拉
     * @Token
     * @throws BusinessException
     */
    public function getHireTypeAction()
    {
        //[1]业务处理
        $params = $this->request->get();
        try {
            Validation::validate($params, DetailService::$validate_hire_type);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $data = SysService::getInstance()->getTransferHireType($params);

        //[2]数据返回
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }

    public function getTotalHireTypeAction()
    {
        //[1]业务处理
        $params = $this->request->get();
        try {
            Validation::validate($params, DetailService::$validate_hire_type);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $data = \App\Modules\WorkflowManagement\Services\SysService::getInstance()->getHireTypeEnums([5]);

        $result = array_map(function ($item) {
            return [
                'label' => $item['key'],
                'value' => $item['value'],
            ];
        } , $data);

        //[2]数据返回
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $result);
    }
}