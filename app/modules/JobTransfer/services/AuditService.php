<?php

namespace App\Modules\JobTransfer\Services;

use App\Library\ApiClient;
use App\Library\Enums;
use App\Library\Enums\ByWorkflowEnums;
use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Library\RocketMQ;
use App\Models\backyard\JobTransferModel;
use App\Modules\JobTransfer\Models\AuditApprovalModel;
use App\Modules\Organization\Models\HrStaffInfoPositionModel;
use App\Modules\JobTransfer\Models\HrHcModel;
use App\Modules\User\Models\HrStaffInfoModel;
use App\Traits\TokenTrait;
use App\Util\RedisKey;

class AuditService extends BaseService
{
    public static $not_must_params = [
    ];

    public static $validate_approval = [
        'id' => 'Required|Int', //ID
    ];

    public static $validate_batch_approval = [
        'audit_ids' => 'Required|ArrLenGe:0|>>>:' . 'miss args', //ID
    ];

    public static $validate_reject = [
        'id'            => 'Required|Int',                  //ID
        'reject_reason' => 'Required|StrLenGeLe:1,500',     //拒绝原因
    ];

    public static $validate_batch_reject = [
        'audit_ids'     => 'Required|ArrLenGe:0|>>>:' . 'miss args', //Ids
        'reject_reason' => 'Required|StrLenGeLe:1,500',              //拒绝原因
    ];

    public static $validate_bp_approval = [
        'id' => 'Required|IntGe:0|>>>:' . 'miss args',   //Ids
    ];

    public static $validate_bp_reject = [
        'id'            => 'Required|IntGe:0|>>>:' . 'miss args',    //Ids
        'reject_reason' => 'Required|StrLenGeLe:1,500',              //拒绝原因
    ];

    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return AuditService
     */
    public static function getInstance(): AuditService
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 批量申请转岗
     * @param $params
     * @param $submitter_id
     * @return mixed
     * @throws \Exception
     */
    public function batchApplyJobTransfer($paramIn, $submitter_id)
    {
        try {
            //校验申请数据
            $res = $this->checkBatchApplyJobTransfer($paramIn, $submitter_id);
            if (!empty($res)) {
                return ['fail' => $res];
            }
            $batchNumber = $paramIn['batch_number'];

            $this->logger->info('set_batch_add_number----参数：' . json_encode($batchNumber));
            $ac = new ApiClient('by', '', 'set_batch_add_number', static::$language);
            $ac->setParams([
                [
                    "batch_number" => $batchNumber,
                    "ids" => array_column($paramIn['data'], 'staff_id'),
                ]
            ]);

            $res = $ac->execute();
            $this->logger->info('setBatchAddNumber----结果' . json_encode($res));

            //批量添加申请
            $rmq = new RocketMQ('create-approval');
            $params = [
                'apply_timestamp' => time(),
                'apply_user'      => $submitter_id,
                'apply_type'      => ByWorkflowEnums::BY_BIZ_TYPE_JT,
                'apply_data'      => $paramIn['data'],
                'apply_uuid'      => $batchNumber,
            ];

            $rid = $rmq->sendToMsg($params);
        } catch (\Exception $e) {
            $this->logger->warning("batchApplyJobTransfer:" . $e->getMessage() . $e->getTraceAsString());
            return false;
        }
        return $rid;
    }

    /**
     * 批量申请转岗
     * @param $params
     * @param $submitter_id
     * @return array
     * @throws \Exception
     */
    public function checkBatchApplyJobTransfer($params, $submitter_id): array
    {
        //批量校验转岗申请
        $ac = new ApiClient('by', '', 'check_batch_apply', static::$language);
        $ac->setParams([
            [
                'data'         => $params['data'],
                'submitter_id' => $submitter_id,
                'type'         => JobTransferModel::TRANSFER_TYPE_FRONT_LINE,
            ]
        ]);
        $res = $ac->execute();

        return $res['result'] ?? [];
    }

    /**
     * @description 单个审批
     * @param $params
     * @param $user
     * @return array
     */
    public function audit($params, $user): array
    {
        //这里无法校验数据
        $ac = new ApiClient('by', '', 'audit_job_transfer_v2', static::$language);
        $ac->setParams([
            [
                'audit_id'      => $params['id'],
                'staff_id'      => $user,
                'status'        => $params['status'],
                'reject_reason' => $params['reject_reason'] ?? '',
            ],
        ]);
        $res = $ac->execute();

        $code    = $res['result']['code'] ?? $res['code'];
        $message = $res['result']['msg'] ?? $res['msg'];
        $data    = $res['data'] ?? [];
        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * @description bp单个审批
     * @param $params
     * @param $user
     * @return array
     */
    public function bpAudit($params, $user): array
    {
        if ($params['status'] == ByWorkflowEnums::BY_OPERATE_REJECT) {
            $ac = new ApiClient('by', '', 'audit_job_transfer_v2', static::$language);
            $ac->setParams([[
                'audit_id'      => $params['id'],
                'staff_id'      => $user,
                'status'        => $params['status'],
                'reject_reason' => $params['reject_reason'] ?? '',
            ]]);
            $res = $ac->execute();
        } else {
            $jobTransferInfo = JobTransferModel::findFirst($params['id']);
            if (empty($jobTransferInfo)) {
                throw new ValidationException('no valid data');
            }

            $ac = new ApiClient('by', '', 'bp_audit_job_transfer', static::$language);
            $ac->setParams([[
                'audit_id'                    => $params['id'],
                'staff_id'                    => $user,
                'status'                      => $params['status'],
                'reject_reason'               => $params['reject_reason'] ?? '',
                'after_manager_id'            => $params['after_manager_id'] ?? '',
                'after_role_ids'              => is_string($params['after_role_ids']) && strlen($params['after_role_ids']) > 0 ? explode(',',
                    $params['after_role_ids']) : [],
                'after_working_day_rest_type' => $params['after_working_day_rest_type'] ?? '',
                'after_vehicle_source'        => $params['after_vehicle_source'] ?? '',
                'after_rental_car_created_at' => $params['after_rental_car_created_at'] ?? '',
                'car_type'                    => $params['car_type'] ?? '',
                'upload_files'                => $params['upload_files'] ?: [],
                'salary_type'                 => $params['salary_type'] ?? '',
                'after_base_salary'           => $params['after_base_salary'] ?? '',
                'f_sr_id'                     => $params['f_sr_id'] ?? '',
                'project_num'                 => $params['project_num'] ?? '',
                'hire_type'                   => $params['hire_type'] ?? '',
                'hire_times'                  => $params['hire_times'] ?? '',
            ]]);
            $res = $ac->execute();
        }
        if (empty($res['result'])) {

            return [
                'code'    => ErrCode::$SYSTEM_ERROR,
                'message' => '',
                'data'    => [],
            ];
        }

        $code    = $res['result']['code'] ?? $res['code'];
        $message = $res['result']['msg'] ?? $res['msg'];
        $data    = $res['data'] ?? [];
        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * @description 批量审批
     * @param $params
     * @param $user
     * @return array
     * @throws ValidationException
     */
    public function batchAudit($params, $user): array
    {
        $result = [];
        if (empty($params['audit_ids']) || !is_array($params['audit_ids'])) {
            throw new ValidationException('miss args audit_ids');
        }
        $auditInfo = AuditApprovalModel::count([
            'conditions' => 'biz_type = :audit_type: and biz_value in({audit_ids:array}) and state = :state: and approval_id = :approval_id:',
            'bind'  => [
                'audit_type'  => ByWorkflowEnums::BY_BIZ_TYPE_JT,
                'audit_ids'   => $params['audit_ids'],
                'approval_id' => $user,
                'state'       => ByWorkflowEnums::BY_OPERATE_WAIT_AUDIT,
            ],
        ]);
        if ($auditInfo != count($params['audit_ids'])) {
            throw new ValidationException(static::$t->_('err_msg_data_updated_please_refresh'));
        }

        foreach ($params['audit_ids'] as $item) {
            $auditParams = [
                'id'            => $item,
                'status'        => $params['status'],
                'reject_reason' => $params['reject_reason'] ?? '',
            ];
            $result[] = $this->audit($auditParams, $user);
        }
        return [
            'code'    => ErrCode::$SUCCESS,
            'message' => 'success',
            'data'    => $result,
        ];
    }

    public function getSingleAuditLockKey($operation, $audit_id)
    {
        return md5(sprintf('transfer_job_transfer_%s_%d', $operation, $audit_id));
    }

    public function getBatchAuditLockKey($operation, $audit_id)
    {
        if (is_array($audit_id)) {
            $unique = implode('_', $audit_id);
        } else {
            $unique = $audit_id;
        }
        return md5(sprintf('transfer_job_transfer_%s_%d', $operation, $unique));
    }
}