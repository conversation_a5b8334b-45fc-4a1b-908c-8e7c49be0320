<?php

namespace App\Modules\JobTransfer\Services;

use App\Models\backyard\SysDepartmentModel;
use App\Models\backyard\SysManagePieceModel;
use App\Models\backyard\SysManageRegionModel;
use App\Models\backyard\SysStoreModel;
use App\Modules\Organization\Models\HrOrganizationDepartmentStoreRelationModel;
use App\Modules\Organization\Services\DepartmentService;


class ManageOrganizationService extends BaseService
{
    private static $instance;

    public const MANAGE_ORG_TYPE_DEPARTMENT = 1; //管辖部门
    public const MANAGE_ORG_TYPE_REGION = 2;//管辖大区
    public const MANAGE_ORG_TYPE_PIECE = 3;//管辖片区
    public const MANAGE_ORG_TYPE_STORE = 4;//管辖网点

    private function __construct()
    {
    }

    /**
     * @return ManageOrganizationService
     */
    public static function getInstance(): ManageOrganizationService
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * @description 获取管辖范围
     * @param $staff_info_id
     * @return array
     */
    public function getManageOrganizationByStaffInfoId($staff_info_id): array
    {
        //管辖所有部门
        $manageDepartmentIds    = $this->getManageDepartmentList($staff_info_id, true);
        $manageSubDepartmentIds = $this->getManageSubDepartmentList($manageDepartmentIds);

        //部门负责人
        $allManageStores = $this->getStoreByRelationInfo($manageSubDepartmentIds, self::MANAGE_ORG_TYPE_DEPARTMENT);

        //大区负责人
        $manageRegionIds = $this->getManageRegionsList($staff_info_id);
        $allManageStores = array_merge($allManageStores, $this->getStoreByRelationInfo($manageRegionIds, self::MANAGE_ORG_TYPE_REGION));

        //片区负责人
        $managePieceIds = $this->getManagePiecesList($staff_info_id);
        $allManageStores = array_merge($allManageStores, $this->getStoreByRelationInfo($managePieceIds, self::MANAGE_ORG_TYPE_PIECE));

        //网点负责人
        $manageStoresIds = $this->getManageStoresList($staff_info_id);
        $allManageStores = array_merge($allManageStores, $this->getStoreByRelationInfo($manageStoresIds, self::MANAGE_ORG_TYPE_STORE));

        return [$manageSubDepartmentIds, $allManageStores];
    }

    /**
     * @description 根据部门ID，查在组织架构中负责的网点
     * @param $department_id
     * @return array
     */
    public function getManageStoresByDepartmentId($department_id): array
    {
        //$department_service = new DepartmentService();
        //$department_ids     = $department_service->getChildrenListByDepartmentIdV2($department_id, true);
        $department_ids[]   = $department_id;

        //获取管辖的网点
        $allManageStores = $this->getStoreByRelationInfo($department_ids, self::MANAGE_ORG_TYPE_DEPARTMENT);
        return array_values(array_unique($allManageStores));
    }

    /**
     * 查询部门关联的网点
     * @param $items
     * @param $type
     * @return array
     */
    protected function getStoreByRelationInfo($items, $type): array
    {
        if (empty($items)) {
            return [];
        }

        $bind = [
            'deleted'        => HrOrganizationDepartmentStoreRelationModel::IS_DELETED_NO,
            'state'          => HrOrganizationDepartmentStoreRelationModel::STATE_YES,
            'level_state'    => HrOrganizationDepartmentStoreRelationModel::LEAVE_STATE_YES,
            'items_ids'      => $items
        ];
        $conditions = 'is_deleted = :deleted: and state = :state: and level_state = :level_state: and ';

        switch ($type) {
            case self::MANAGE_ORG_TYPE_DEPARTMENT:
                $conditions .= 'department_id in ({items_ids:array})';
                break;
            case self::MANAGE_ORG_TYPE_REGION:
                $conditions .= 'region_id in ({items_ids:array})';
                break;
            case self::MANAGE_ORG_TYPE_PIECE:
                $conditions .= 'piece_id in ({items_ids:array})';
                break;
            default:
                $conditions .= 'store_id in ({items_ids:array})';
                break;
        }

        $storeRelate = HrOrganizationDepartmentStoreRelationModel::find([
            'conditions' => $conditions,
            'bind'       => $bind,
        ])->toArray();

        return !empty($storeRelate) ? array_column($storeRelate, 'store_id') : [];
    }

    /**
     * 获取员工管理的大区
     * @param $staff_info_id
     * @param bool $is_contain_assistants 是否包含助理
     * @return array
     */
    protected function getManageDepartmentList($staff_info_id, $is_contain_assistants = false): array
    {
        if ($is_contain_assistants) {
            $condition = '(manager_id = :manager_id: or assistant_id = :assistant_id:) and deleted = 0';
            $bind = [
                'manager_id'   => $staff_info_id,
                'assistant_id' => $staff_info_id,
            ];
        } else {
            $condition = '(manager_id = :manager_id:) and deleted = 0';
            $bind = [
                'manager_id'   => $staff_info_id,
            ];
        }
        $manageInfo = SysDepartmentModel::find([
            'conditions' => $condition,
            'bind'       => $bind,
            'columns' => 'id,name',
        ])->toArray();
        return array_column($manageInfo, 'id');
    }

    /**
     * @description 获取管辖的全部子部门
     * @param $department_ids
     * @return array
     */
    public function getManageSubDepartmentList($department_ids): array
    {
        $result = [];
        $model  = new SysDepartmentModel();
        foreach ($department_ids as $item) {
            $tmpDepartmentIds = $model->getSpecifiedDeptAndSubDept($item);
            $result           = array_merge($result, $tmpDepartmentIds);
        }
        return array_values(array_unique($result));
    }

    /**
     * 获取员工管理的大区
     * @param $staff_info_id
     * @return array
     */
    protected function getManageRegionsList($staff_info_id): array
    {
        $manageInfo = SysManageRegionModel::find([
            'conditions' => 'manager_id = :manager_id: and deleted = 0',
            'bind'       => [
                'manager_id' => $staff_info_id,
            ],
            'columns' => 'id,name',
        ])->toArray();
        return array_column($manageInfo, 'id');
    }

    /**
     * 获取员工管理的片区
     * @param $staff_info_id
     * @return array
     */
    protected function getManagePiecesList($staff_info_id): array
    {
        $manageInfo = SysManagePieceModel::find([
            'conditions' => 'manager_id = :manager_id: and deleted = 0',
            'bind'       => [
                'manager_id' => $staff_info_id,
            ],
            'columns' => 'id,name',
        ])->toArray();
        return array_column($manageInfo, 'id');
    }

    /**
     * 获取员工管理的网点
     * @param $staff_info_id
     * @return array
     */
    protected function getManageStoresList($staff_info_id): array
    {
        $manageInfo = SysStoreModel::find([
            'conditions' => 'manager_id = :manager_id: and state = 1',
            'bind'       => [
                'manager_id' => $staff_info_id,
            ],
            'columns' => 'id,name',
        ])->toArray();
        return array_column($manageInfo, 'id');
    }

    /**
     * 校验转岗后的网点是否在转岗后的所属部门下
     * * @param int $department_id 转岗后部门
     * * @param string $store_id 转岗后网点
     * * @return bool
     */
    public function isStoreBelongDepartment($department_id, $store_id): bool
    {
        if (empty($department_id) || empty($store_id)) {
            return false;
        }

        $relation = HrOrganizationDepartmentStoreRelationModel::findFirst([
            'conditions' => 'is_deleted = :deleted: and state = :state: and level_state = :level_state: and department_id = :department_id: and store_id = :store_id:',
            'bind' => [
                'deleted'       => HrOrganizationDepartmentStoreRelationModel::IS_DELETED_NO,
                'state'         => HrOrganizationDepartmentStoreRelationModel::STATE_YES,
                'level_state'   => HrOrganizationDepartmentStoreRelationModel::LEAVE_STATE_YES,
                'department_id' => $department_id,
                'store_id'      => $store_id,
            ]
        ]);
        if (empty($relation)) {
            //Hub部门网点关联关系
            $departmentInfo = SysDepartmentModel::findFirst([
                'conditions' => 'deleted = :deleted: and relevance_store_id = :store_id:',
                'bind'       => [
                    'deleted'  => HrOrganizationDepartmentStoreRelationModel::IS_DELETED_NO,
                    'store_id' => $store_id,
                ]
            ]);
            if (empty($departmentInfo) || $departmentInfo->id != $department_id) {
                return false;
            }
        }
        return true;
    }

}