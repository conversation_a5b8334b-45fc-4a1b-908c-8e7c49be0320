<?php

namespace App\Modules\JobTransfer\Services;

use App\Library\ApiClient;
use App\Library\Enums;
use App\Library\Enums\ByWorkflowEnums;
use App\Library\Enums\GlobalEnums;
use app\library\Enums\JobTransferEnums;
use App\Library\Enums\OrganizationDepartmentEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\RoleEnums;
use App\Models\backyard\FrontlineJobTransferSalaryModel;
use App\Library\Validation\ValidationException;
use App\Models\backyard\ByWorkflowNodeModel;
use App\Models\backyard\JobTransferModel;
use app\models\backyard\SalaryBaseSettingModel;
use App\Models\backyard\SettingEnvModel;
use App\Models\oa\WorkflowNodeModel;
use App\Modules\JobTransfer\Models\AuditApprovalModel;
use App\Modules\JobTransfer\Models\HrHcModel;
use App\Modules\Organization\Models\HrStaffInfoPositionModel;
use App\Modules\Organization\Services\HrStaffInfoPositionService;
use App\Modules\User\Models\HrStaffInfoModel;
use App\Modules\Workflow\Models\WorkflowModel;
use App\Modules\WorkflowManagement\Models\ByWorkflowModel;
use App\Repository\HrStaffRepository;
use App\Repository\StaffDepartmentAreasStoreRepository;

class ListService extends BaseService
{
    public static $not_must_params = [
        'name',
        'after_date_start',
        'after_date_end',
        'approval_state',
    ];

    public static $validate_list_search = [
        'page_size'        => 'IntGt:0',//每页条数
        'page_num'         => 'IntGt:0',//页码
        'name'             => 'Str',    //工号、姓名
        'after_date_start' => 'date',   //转岗日期
        'after_date_end'   => 'date',   //转岗日期
        'approval_state'   => 'Arr',    //审批状态
    ];

    public static $validate_audit_list_search = [
        'page_size'        => 'IntGt:0',//每页条数
        'page_num'         => 'IntGt:0',//页码
        'name'             => 'Str',    //工号、姓名
        'approval_state'   => 'Int',    //审批状态
    ];

    public static $validate_apply_list_search = [
        'page_size'        => 'IntGt:0',//每页条数
        'page_num'         => 'IntGt:0',//页码
        'name'             => 'Str',    //工号、姓名
        'after_date_start' => 'date',   //转岗日期
        'after_date_end'   => 'date',   //转岗日期
        'approval_state'   => 'Arr',    //审批状态
        'state'            => 'Arr',    //转岗状态
    ];

    public static $validate_batch_add = [
        'data'                                => 'Required|ArrLenGe:1',      //转岗数据
        'data[*].staff_id'                    => 'Required|IntGt:0',         //转岗人工号
        'data[*].after_department_id'         => "Required|IntGt:0",         //转岗人后部门
        'data[*].after_position_id'           => "Required|IntGt:0",         //转岗人后职位
        'data[*].after_store_id'              => "Required|StrLenGe:1",      //转岗人后网点
        'data[*].job_handover_staff_id'       => "Required|IntGt:0",         //转岗交接人
        'data[*].after_date'                  => "Required|DateTime",        //转岗日期
        'data[*].transfer_reason'             => "Required|Int",             //转岗原因
        'data[*].reason'                      => "IfIntEq:transfer_reason,9|Required|StrLenGeLe:1,500",//转岗原因备注
    ];

    public static $validate_batch_add_my = [
        'data'                                => 'Required|ArrLenGe:1',      //转岗数据
        'data[*].staff_id'                    => 'Required|IntGt:0',         //转岗人工号
        'data[*].after_department_id'         => "Required|IntGt:0",         //转岗人后部门
        'data[*].after_position_id'           => "Required|IntGt:0",         //转岗人后职位
        'data[*].after_store_id'              => "Required|StrLenGe:1",      //转岗人后网点
        'data[*].job_handover_staff_id'       => "Required|IntGt:0",         //转岗交接人
        'data[*].after_date'                  => "Required|DateTime",        //转岗日期
        'data[*].transfer_reason'             => "Required|Int",             //转岗原因
        'data[*].reason'                      => "IfIntEq:transfer_reason,9|Required|StrLenGeLe:1,500",//转岗原因备注
        'data[*].after_car_type'              => "IntGt:0",                //转岗后车类型
    ];

    public static $validate_edit = [
        'id'                          => 'IntGt:0',     //转岗id
        'expect_date'                 => 'Date',        //转岗日期
        'after_role_ids'              => 'StrLenGe:1',  //转岗角色
        'after_manager_id'            => 'Int',         //转岗上级
        'after_working_day_rest_type' => 'Int',         //工作天数
    ];

    public static $validate_update = [
        'id'             => 'IntGt:0',                                  //转岗id
        'after_date'     => 'IfExist:after_date|Required|Date',         //转岗日期
        'after_role_ids' => 'Arr|ArrLenGe:1',
    ];

    public static $validate_batch = [
        'batch_number' => 'Required|StrLenGe:1',            //转岗批次号
    ];

    public static $validate_do_transfer = [
        'id' => 'Required|IntGe:0',                         //转岗ID
    ];

    public static $validate_search_staff_name = [
        'search_condition' => 'Required|IntGe:0',  //搜索条件
    ];

    public static $validate_export = [
        'name'             => 'Str',    //工号、姓名
        'after_date_start' => 'date',   //转岗日期
        'after_date_end'   => 'date',   //转岗日期
        'approval_state'   => 'Arr',    //审批状态
    ];

    public static $common_columns = [
        'j.id', 'serial_no', 'staff_id', 'after_date', 'j.state', 'j.approval_state','j.type', 'current_department_id', 'current_store_id',
        'current_piece_id', 'current_region_id', 'current_position_id', 'current_role_id', 'after_department_id', 'after_store_id',
        'after_piece_id', 'after_region_id', 'after_position_id', 'current_role_id', 'before_working_day_rest_type', 'j.actual_after_date',
        'j.confirm_state','j.submitter_id','j.after_company_id','j.current_company_id','j.after_manager_id','j.car_owner',
        'j.rental_car_cteated_at', 'j.after_working_day_rest_type', 'j.created_at', 'j.current_hire_type', 'j.after_hire_type'
    ];


    private static $instance;

    private function __construct()
    {
    }

    /**
     * 获取实例
     * @return ListService
     */
    public static function getInstance(): ListService
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 获取列表数据
     * @param $param
     * @param $start
     * @param $limit
     * @return mixed
     */
    public function getBaseTransferData($param, $start, $limit): array
    {
        //查询列表
        $builder = $this->getQueryBuilder($param);
        if (empty($builder)) {
            return [[], 0];
        }

        $count   = (int)$builder->columns('count(1) as count')->getQuery()->getSingleResult()->count;
        $list    = $builder->columns($param['columns'])
            ->orderBy('id desc')
            ->limit($limit, $start)->getQuery()->execute()->toArray();

        return [$list, $count];
    }

    /**
     * 获取已经真正转岗数据
     * @param $params
     * @return mixed
     */
    public function getTransferredJobDataList($params)
    {
        $params['state'] = [JobTransferModel::JOB_TRANSFER_STATE_COMPLETED];
        if (isset($params['approval_state'])) {
            unset($params['approval_state']);
        }
        $params['page_num']  = 1;
        $params['page_size'] = 1000;
        $endList             = [];
        while (true) {
            $list = $this->searchApplyList($params, $params['operate_id']);
            if (empty($list['dataList'])) {
                break;
            }
            $endList = array_merge($endList, $list['dataList']);
            ++$params['page_num'];
        }
        $jobTransferIds = array_column($endList, 'id');
        $salaryList     = [];
        $columns        = '';
        $salaryColumns  = [];
        $salaryCount    = 0;
        if (get_country_code() == GlobalEnums::TH_COUNTRY_CODE) {
            $columns       = 'concat(staff_info_id,\'-\',transfer_date) as unique,staff_info_id,transfer_date
                ,base_salary_before,experience_before,position_before,car_before,food_before,risk_before,island_before,notebook_rental_before,house_rental_before
                ,base_salary_after,experience_after,position_after,car_after,food_after,risk_after,island_after,notebook_rental_after,house_rental_after';
            $salaryColumns = [
                'base_salary_before',
                'experience_before',
                'position_before',
                'car_before',
                'food_before',
                'risk_before',
                'island_before',
                'notebook_rental_before',
                'house_rental_before',
                'base_salary_after',
                'experience_after',
                'position_after',
                'car_after',
                'food_after',
                'risk_after',
                'island_after',
                'notebook_rental_after',
                'house_rental_after',
            ];
            $salaryCount   = count($salaryColumns);
        }
        $salaryItemsData = [];
        if (get_country_code() == GlobalEnums::MY_COUNTRY_CODE) {
            $columns         = 'concat(staff_info_id,\'-\',transfer_date) as unique,staff_info_id,transfer_date,salary_before,salary_after';
            $salaryItemsData = $this->getFrontlineSalaryBaseSettingMY();
        }
        if (get_country_code() == GlobalEnums::PH_COUNTRY_CODE) {
            $columns         = 'concat(staff_info_id,\'-\',transfer_date) as unique,staff_info_id,transfer_date
            ,base_salary_before,deminimis_benefits_before,other_non_taxable_allowance_before,food_before
            ,base_salary_after,deminimis_benefits_after,other_non_taxable_allowance_after,food_after';
            $salaryItemsData = [
                'base_salary_before',
                'deminimis_benefits_before',
                'other_non_taxable_allowance_before',
                'food_before',
                'base_salary_after',
                'deminimis_benefits_after',
                'other_non_taxable_allowance_after',
                'food_after',
            ];
        }
        $salaryItemsDataCount = count($salaryItemsData);
        if ($jobTransferIds) {
            $salaryList = FrontlineJobTransferSalaryModel::find([
                'columns'    => $columns,
                'conditions' => 'transfer_id in ({ids:array}) and is_deleted=0',
                'bind'       => ['ids' => $jobTransferIds],
            ])->toArray();
        }
        $salaryMap  = array_column($salaryList, null, 'unique');
        $returnData = [];
        foreach ($endList as $item) {
            $unique     = $item['staff_id'] . '-' . $item['actual_after_date'];
            $base       = $this->getExportDataLine($item);
            $salaryBase = [];
            //th
            if (get_country_code() == GlobalEnums::TH_COUNTRY_CODE) {
                foreach ($salaryColumns as $salaryColumn) {
                    $salaryBase[] = (float)(empty($salaryMap[$unique][$salaryColumn]) ? 0 : $salaryMap[$unique][$salaryColumn]);
                }
                $checkEqual = [];
                foreach (range(0, $salaryCount / 2 - 1) as $salaryKey) {
                    if ($salaryBase[$salaryKey] == $salaryBase[$salaryKey + 9]) {
                        $checkEqual[] = 0;
                        continue;
                    }
                    $checkEqual[] = 1;
                }
                //转岗前后都相等跳过 排除 bike to van ,van to bile current_position_id,after_position_id
                if (array_sum($checkEqual) == 0
                    &&
                    !(
                        $item['current_position_id'] == JobTransferEnums::JOB_VAN_TITLE_ID &&  in_array($item['after_position_id'],[JobTransferEnums::JOB_BIKE_TITLE_ID,JobTransferEnums::JOB_TRICYCLE_COURIER_TITLE_ID])
                        || in_array($item['after_position_id'],[JobTransferEnums::JOB_BIKE_TITLE_ID,JobTransferEnums::JOB_TRICYCLE_COURIER_TITLE_ID]) && $item['after_position_id'] == JobTransferEnums::JOB_VAN_TITLE_ID
                    )
                ) {
                    continue;
                }
            }
            if (get_country_code() == GlobalEnums::MY_COUNTRY_CODE) {
                $salary = $salaryMap[$unique] ?? [];
                if (empty($salary)) {
                    $salaryBase = array_fill(0, $salaryItemsDataCount, '');
                }
                if ($salary) {
                    $salaryBefore = json_decode($salary['salary_before'], true);
                    $salaryAfter  = json_decode($salary['salary_after'], true);
                    $beforeData   = $afterData = [];
                    foreach ($salaryItemsData as $datum) {
                        $beforeData[] = isset($salaryBefore[$datum['id']]) ? (float)$salaryBefore[$datum['id']] : '';
                        $afterData[]  = isset($salaryAfter[$datum['id']]) ? (float)$salaryAfter[$datum['id']] : '';
                    }
                    $salaryBase = array_merge($beforeData, $afterData);
                }
            }
            if (get_country_code() == GlobalEnums::PH_COUNTRY_CODE) {
                $salary = $salaryMap[$unique] ?? [];
                if (empty($salary)) {
                    $salaryBase = array_fill_keys($salaryItemsData, null);
                } else {
                    foreach ($salaryItemsData as $datum) {
                        $salaryBase[] = $salary[$datum];
                    }
                }
            }
            $returnData[] = array_merge($base, $salaryBase);
        }
        return $returnData;
    }
    /**
     * @description 获取转岗数据查询list
     * @param $param
     * @param $user
     * @param $offset
     * @param $pageSize
     * @return array
     */
    public function getSearchListData($param, $user, $offset, $pageSize): array
    {
        // 根据筛选项构建Builder
        $builder = $this->getQueryDataBuilder($param);
        if (empty($builder)) {
            return [[], 0];
        }

        // 根据提交人权限构建Builder
        $builder = $this->getBuilderWithPermission($builder, $user, $param);

        // 获取total Count
        $count   = (int)$builder->columns('count(1) as count')->getQuery()->getSingleResult()->count;

        // 获取list
        $list    = $builder->columns($param['columns'])->orderBy('id desc')
            ->limit($pageSize, $offset)
            ->getQuery()
            ->execute()
            ->toArray();

        return [$list, $count];
    }

    /**
     * @description 获取申请列表查询builder
     * @param $param
     * @return mixed
     */
    public function getQueryBuilder($param)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['j' => JobTransferModel::class]);
        $builder->where('1=1');
        if (!empty($param['name'])) {
            $staff = HrStaffInfoModel::find([
                "conditions" => "staff_info_id like :name: or name like :name:",
                "columns" => "staff_info_id",
                "bind" => ["name" => "%{$param['name']}%"],
            ])->toArray();
            $staffArr = array_column($staff, 'staff_info_id');
            if (!empty($staff)) {
                $builder->inWhere('staff_id', $staffArr);
            } else {
                return false;
            }
        }
        if (!empty($param['type'])) {
            $builder->inWhere('type', $param['type']);
        }
        if (!empty($param['state'])) {
            $builder->inWhere('state', $param['state']);
        }
        if (!empty($param['after_date_start'])) {
            $builder->andWhere('after_date >= :after_date_start:', ['after_date_start' => $param['after_date_start']]);
        }
        if (!empty($param['after_date_end'])) {
            $builder->andWhere('after_date <= :after_date_end:', ['after_date_end' => $param['after_date_end']]);
        }
        if (!empty($param['actual_date_start'])) {
            $builder->andWhere('actual_after_date >= :actual_date_start:', ['actual_date_start' => $param['actual_date_start']]);
        }
        if (!empty($param['actual_date_end'])) {
            $builder->andWhere('actual_after_date <= :actual_date_end:', ['actual_date_end' => $param['actual_date_end']]);
        }
        if (!empty($param['approval_state'])) {
            $builder->inWhere('approval_state', $param['approval_state']);
        }
        if (!empty($param['submitter_id'])) {
            $builder->andWhere('submitter_id = :submitter_id:', ['submitter_id' => $param['submitter_id']]);
        }
        if (!empty($param['current_hire_type'])) {
            $builder->inWhere('current_hire_type', $param['current_hire_type']);
        }
        if (!empty($param['after_hire_type'])) {
            $builder->inWhere('after_hire_type', $param['after_hire_type']);
        }
        return $builder;
    }

    /**
     * @description 获取数据查询列表的查询builder
     * @param $param
     * @return mixed
     */
    public function getQueryDataBuilder($param)
    {
        //获取筛选参数
        $searchCondition      = $param['name'] ?? '';
        $approvalState        = $param['approval_state'] ?? [];
        $state                = $param['state'] ?? [];
        $afterDateStart       = $param['actual_date_start'];
        $afterDateEnd         = $param['actual_date_end'];
        $expectAfterDateStart = $param['after_date_start'] ?? '';
        $expectAfterDateEnd   = $param['after_date_end'] ?? '';
        $isHireTypeChange     = $param['is_hire_type_change'] ?? false;
        $hireTypeNotAgent     = $param['hire_type_not_agent'] ?? false;
        $type                 = $param['type'] ?? [];
        $beforeHireType       = $param['current_hire_type'] ?? [];
        $afterHireType        = $param['after_hire_type'] ?? [];
        $beforeStoreId        = $param['before_store_id'] ?? '';
        $afterStoreId         = $param['after_store_id'] ?? '';

        $builder = $this->modelsManager->createBuilder();
        $builder->from(['j' => JobTransferModel::class]);
        $builder->where('1 = 1');
        if (!empty($searchCondition)) {
            $staff = HrStaffInfoModel::find([
                'conditions' => 'staff_info_id like :name: or name like :name:',
                'columns'    => 'staff_info_id',
                'bind'       => [
                    'name' => '%' . $searchCondition . '%',
                ],
            ])->toArray();
            $staffArr = array_column($staff, 'staff_info_id');
            if (!empty($staff)) {
                $builder->inWhere('staff_id', $staffArr);
            } else {
                $builder->inWhere('staff_id', [0]);
            }
        }
        if (!empty($type)) {
            $builder->inWhere('type', $type);
        }
        if (!empty($approvalState)) {
            $builder->inWhere('approval_state', $approvalState);
        }
        if (!empty($state)) {
            $builder->inWhere('state', $state);
        }
        if (!empty($afterDateStart)) {
            $builder->andWhere('actual_after_date >= :after_date_start:', ['after_date_start' => $afterDateStart]);
        }
        if (!empty($afterDateEnd)) {
            $builder->andWhere('actual_after_date <= :after_date_end:', ['after_date_end' => $afterDateEnd]);
        }
        if (!empty($expectAfterDateStart)) {
            $builder->andWhere('after_date >= :expect_after_date_start:', ['expect_after_date_start' => $expectAfterDateStart]);
        }
        if (!empty($expectAfterDateEnd)) {
            $builder->andWhere('after_date <= :expect_after_date_end:', ['expect_after_date_end' => $expectAfterDateEnd]);
        }
        if (!empty($isHireTypeChange)) {
            $builder->andWhere('current_hire_type = :before_hire_type: and after_hire_type != :after_hire_type:', [
                'before_hire_type' => StaffInfoEnums::HIRE_TYPE_PERMANENT_EMPLOYEE,
                'after_hire_type'  => StaffInfoEnums::HIRE_TYPE_PERMANENT_EMPLOYEE,
            ]);
        }
        if (!empty($beforeHireType)) {
            $builder->inWhere('current_hire_type', $beforeHireType);
        }
        if (!empty($afterHireType)) {
            $builder->inWhere('after_hire_type', $afterHireType);
        }
        if (!empty($hireTypeNotAgent)) {
            $builder->notInWhere('current_hire_type', [StaffInfoEnums::HIRE_TYPE_PERSONAL_AGENCY, StaffInfoEnums::HIRE_TYPE_PART_TIME_AGENT]);
            $builder->notInWhere('after_hire_type', [StaffInfoEnums::HIRE_TYPE_PERSONAL_AGENCY, StaffInfoEnums::HIRE_TYPE_PART_TIME_AGENT]);
        }
        if (!empty($beforeStoreId)) {
            $builder->andWhere('current_store_id = :current_store_id:', ['current_store_id' => $beforeStoreId]);
        }
        if (!empty($afterStoreId)) {
            $builder->andWhere('after_store_id = :after_store_id:', ['after_store_id' => $afterStoreId]);
        }

        return $builder;
    }

    /**
     * @description 根据指定工号构建builder
     * @param $builder
     * @param $user
     * @param $params
     * @return mixed
     */
    public function getBuilderWithPermission($builder, $user, $params)
    {
        $permission = $this->getSubmitterPermission($user);
        if ($permission['all'] || isset($params['all_permission']) && $params['all_permission']) {
            return $builder;
        } else {
            $conditions = 'submitter_id = :submitter_id: or current_manager_id = :manager_id: or after_manager_id = :manager_id:';
            $bind = ['submitter_id' => $user, 'manager_id' => $user];

            if (isset($permission['part']['special_roles']['department']) && $permission['part']['special_roles']['department']) {
                $conditions .= ' or (
                    (current_department_id in({manage_department:array}) and current_store_id = \'-1\')  or 
                    (after_department_id in({manage_department:array}) and after_store_id = \'-1\')
                    )';
                $bind['manage_department'] = $permission['part']['special_roles']['department'];
            }

            if (isset($permission['part']['special_roles']['store']) && $permission['part']['special_roles']['store']) {

                if (in_array(OrganizationDepartmentEnums::ALL_STORE, $permission['part']['special_roles']['store'])) { //管辖全部网点
                    $conditions .= ' or current_store_id != \'-1\' or after_store_id != \'-1\'';
                } else { //管辖部分网点
                    $conditions .= ' or current_store_id in({manage_store:array}) or after_store_id in({manage_store:array})';
                    $bind['manage_store'] = $permission['part']['special_roles']['store'];
                }
            }

            if (isset($permission['part']['org']['department']) && $permission['part']['org']['department']) {
                $conditions .= ' or current_department_id in({org_department:array}) or after_department_id in({org_department:array})';
                $bind['org_department'] = $permission['part']['org']['department'];
            }

            if (isset($permission['part']['org']['store']) && $permission['part']['org']['store']) {
                $conditions .= ' or current_store_id in({org_store:array}) or after_store_id in({org_store:array})';
                $bind['org_store'] = $permission['part']['org']['store'];
            }

            if (isset($permission['part']['special_roles']['hire_type']) && $permission['part']['special_roles']['hire_type']) {
                $conditions .= ' or current_hire_type in({hire_type:array}) or after_hire_type in({hire_type:array})';
                $bind['hire_type'] = [StaffInfoEnums::HIRE_TYPE_PERSONAL_AGENCY, StaffInfoEnums::HIRE_TYPE_PART_TIME_AGENT];
            }

            $builder->andWhere($conditions, $bind);
        }

        return $builder;
    }

    /**
     * @description 查询审批列表
     * @param $param
     * @param $start
     * @param $limit
     * @return array
     */
    public function getAuditTransferData($param, $start, $limit): array
    {
        //查询列表
        $builder = $this->getAuditQueryBuilder($param);
        if (empty($builder)) {
            return [[], 0];
        }
        $count   = (int)$builder->columns('count(1) as count')->getQuery()->getSingleResult()->count;
        $list    = $builder->columns($param['columns'])
            ->orderBy('j.id desc')->limit($limit, $start)->getQuery()->execute()->toArray();

        return [$list, $count];
    }

    /**
     * @description 获取审批列表查询builder
     * @param $param
     * @return mixed
     */
    public function getAuditQueryBuilder($param)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['aa' => AuditApprovalModel::class]);
        $builder->join(JobTransferModel::class, 'j.id = aa.biz_value and aa.biz_type = 18', 'j');
        $builder->join(ByWorkflowNodeModel::class, 'wn.id = aa.flow_node_id', 'wn');
        if (!empty($param['name'])) {
            $staff    = HrStaffInfoModel::find([
                "conditions" => "staff_info_id like :name: or name like :name:",
                "columns"    => "staff_info_id",
                "bind"       => ["name" => "%{$param['name']}%"],
            ])->toArray();
            $staffArr = array_column($staff, 'staff_info_id');
            if (!empty($staff)) {
                $builder->inWhere('j.staff_id', $staffArr);
            } else {
                return false;
            }
        }
        $builder->andWhere('aa.deleted = 0');
        if (!empty($param['type'])) {
            $builder->inWhere('j.type', $param['type']);
        }
        if (!empty($param['approval_state'])) {
            $builder->andWhere('aa.state = :state:', ['state' => $param['approval_state']]);
        }
        if (!empty($param['approval_id'])) {
            $builder->andWhere('aa.approval_id = :approval_id:', ['approval_id' => $param['approval_id']]);
        }
        if (isset($param['can_edit_field'])) {
            if ($param['can_edit_field']) {
                $builder->andWhere('wn.can_edit_field != \'\'');
            } else {
                $builder->andWhere('wn.can_edit_field is null or wn.can_edit_field = \'\'');
            }
        }
        if (!empty($param['current_hire_type'])) {
            $builder->andWhere('current_hire_type = :current_hire_type:', ['current_hire_type' => $param['current_hire_type']]);
        }
        if (!empty($param['after_hire_type'])) {
            $builder->andWhere('after_hire_type = :after_hire_type:', ['after_hire_type' => $param['after_hire_type']]);
        }
        return $builder;
    }

    /**
     * 获取转岗在oa左侧菜单的待审批小红点数量
     * @param array $param['approval_state' => '待审批','approval_id' => '审批人ID']
     * @return int
     */
    public function getAuditCount($param): int
    {
        if ($param['type'] == Enums::WF_JOB_TRANSFER_BP_TYPE) {
            $param['can_edit_field'] = true;
        } else {
            $param['can_edit_field'] = false;
        }

        //查询列表
        $param['type'] = [JobTransferModel::TRANSFER_TYPE_FRONT_LINE];
        $builder       = $this->getAuditQueryBuilder($param);
        if (empty($builder)) {
            return 0;
        }
        $total_info    = $builder->columns('count(j.id) as total')->getQuery()->getSingleResult();
        return intval($total_info->total);
    }

    /**
     * 申请查询列表
     * @param $paramIn
     * @param $submitterId
     * @return array
     */
    public function getApplyList($paramIn, $submitterId): array
    {
        //获取传入参数
        $param['name']              = trim($paramIn['name'] ?? '') ?? '';
        $param['type']              = $paramIn['type'] ?? [];
        $param['state']             = $paramIn['state'] ?? 0;
        $param['after_date_start']  = $paramIn['after_date_start'] ?? 0;
        $param['after_date_end']    = $paramIn['after_date_end'] ?? 0;
        $param['approval_state']    = $paramIn['approval_state'] ?? [];
        $param['page_num']          = $paramIn['page_num'] ?? 1;
        $param['page_size']         = $paramIn['page_size'] ?? 20;
        $offset                     = $param['page_size'] * ($param['page_num'] - 1);
        $param['submitter_id']      = $submitterId;
        $param['columns']           = self::$common_columns;
        $param['current_hire_type'] = $paramIn['current_hire_type'];
        $param['after_hire_type']   = $paramIn['after_hire_type'];

        //查询列表
        [$items, $count] = $this->getBaseTransferData($param, $offset, $param['page_size']);
        if (!empty($items) && is_array($items)) {
            $items = $this->translateColumn($items);
        }

        return [
            'dataList' => $items ?? [],
            'count'    => $count ?? 0,
        ];
     }

    /**
     * 获取审批列表
     * @param $paramIn
     * @param $currentStaff
     * @return array
     */
    public function getAuditList($paramIn, $currentStaff): array
    {
        //获取传入参数
        $param['name']           = isset($paramIn['name']) ? trim($paramIn['name']) : '';
        $param['approval_state'] = $paramIn['approval_state'] ?? [];
        $param['page_num']       = $paramIn['page_num'] ?? 1;
        $param['page_size']      = $paramIn['page_size'] ?? 20;
        $offset                  = $param['page_size'] * ($param['page_num'] - 1);
        $param['approval_id']    = $currentStaff ?? '';
        $param['columns']        = self::$common_columns;
        $param['type']           = [JobTransferModel::TRANSFER_TYPE_FRONT_LINE];
        $param['can_edit_field']  = false;

        //查询列表
        [$items, $count] = $this->getAuditTransferData($param, $offset, $param['page_size']);

        //转义字段
        if (!empty($items) && is_array($items)) {
            $items = $this->translateColumn($items);
        }

        return [
            'dataList' => $items ?? [],
            'count'    => $count ?? 0,
        ];
    }

    /**
     * 获取审批列表
     * @param $paramIn
     * @param $currentStaff
     * @return array
     */
    public function getBpAuditList($paramIn, $currentStaff): array
    {
        //获取传入参数
        $param['name']           = isset($paramIn['name']) ? trim($paramIn['name']) : '';
        $param['approval_state'] = $paramIn['approval_state'] ?? [];
        $param['page_num']       = $paramIn['page_num'] ?? 1;
        $param['page_size']      = $paramIn['page_size'] ?? 20;
        $offset                  = $param['page_size'] * ($param['page_num'] - 1);
        $param['approval_id']    = $currentStaff ?? '';
        $param['columns']        = self::$common_columns;
        $param['type']           = [JobTransferModel::TRANSFER_TYPE_FRONT_LINE];
        $param['can_edit_field']  = true;

        //查询列表
        [$items, $count] = $this->getAuditTransferData($param, $offset, $param['page_size']);

        //转义字段
        if (!empty($items) && is_array($items)) {
            $items = $this->translateColumn($items);
        }

        return [
            'dataList' => $items ?? [],
            'count'    => $count ?? 0,
        ];
    }

    /**
     * @description
     * @param $paramIn
     * @param $user
     * @param string $columns
     * @return array
     */
    public function searchApplyList($paramIn, $user, $columns = ''): array
    {
        //获取传入参数
        $param['name']                = trim($paramIn['name'] ?? '');
        $param['approval_state']      = $paramIn['approval_state'] ?? [];
        $param['state']               = $paramIn['state'] ?? [];
        $param['actual_date_start']   = $paramIn['actual_date_start'] ?? 0;
        $param['actual_date_end']     = $paramIn['actual_date_end'] ?? 0;
        $param['before_store_id']   = $paramIn['before_store_id'] ?? '';
        $param['after_store_id']    = $paramIn['after_store_id'] ?? '';
        $param['current_hire_type']   = $paramIn['current_hire_type'] ?? [];
        $param['after_hire_type']     = $paramIn['after_hire_type'] ?? [];
        $param['page_num']            = $paramIn['page_num'] ?? 1;
        $param['page_size']           = $paramIn['page_size'] ?? 20;
        $offset                       = $param['page_size'] * ($param['page_num'] - 1);
        $param['permission']          = $paramIn['permission'] ?? [];
        $param['hire_type_not_agent'] = $paramIn['hire_type_not_agent'] ?? false;
        $param['columns']             = $columns ?: self::$common_columns;

        // 查询列表
        [$items, $count] = $this->getSearchListData($param, $user, $offset, $param['page_size']);
        if (!empty($items) && is_array($items)) {
            $items = $this->translateColumn($items);
        }

        return [
            'dataList' => $items ?? [],
            'count'    => $count ?? 0,
        ];
    }

    /**
     * 导出数据
     */
    public function getExportData($params, $submitterId): array
    {
        //获取传入参数
        $params['page_num']     = $params['page_num'] ?? 1;
        $params['page_size']    = $params['page_size'] ?? 20;
        $offset                 = $params['page_size'] * ($params['page_num'] - 1);
        //$params['submitter_id'] = $submitterId;
        $params['columns']      = self::$common_columns;

        //查询列表
        // 查询列表
        [$items, $count] = $this->getSearchListData($params, $submitterId, $offset, $params['page_size']);
        if (!empty($items) && is_array($items)) {
            $items = $this->translateColumn($items);
        }

        $export_data = [];
        foreach ($items as $item) {
            $export_data[] = $this->getExportDataLine($item);
        }

        return $export_data;
    }
    public function getExportDataLine($item): array
    {
        $result_data = [
            //工号
            $item['staff_id'],
            //姓名
            $item['staff_name'],
            //转岗审批编号
            $item['serial_no'],
            //转岗状态
            $item['state_title'],
            //申请日期
            date('Y-m-d', strtotime($item['created_at']) + $this->config->application->add_hour * 3600),
            //审批状态
            $item['approval_state_title'],
            //转岗前公司
            $item['current_company_name'] ?? '',
            //转岗前所属部门
            $item['current_department_name'] ?? '',
            //转岗前大区
            $item['current_region_name'] ?? '',
            //转岗前片区
            $item['current_piece_name'] ?? '',
            //转岗前所属网点
            $item['current_store_name'] ?? '',
            //转岗前职位
            $item['current_position_name'] ?? '',
            //转岗前雇佣类型
            $item['current_hire_type_name'] ?? '',
            //转岗后公司
            $item['after_company_name'] ?? '',
            //转岗后所属部门
            $item['after_department_name'] ?? '',
            //转岗后大区
            $item['after_region_name'] ?? '',
            //转岗后片区
            $item['after_piece_name'] ?? '',
            //转岗后所属网点
            $item['after_store_name'] ?? '',
            //转岗后职位
            $item['after_position_name'] ?? '',
            //转岗后雇佣类型
            $item['after_hire_type_name'] ?? '',
            //转岗后直线上级
            $item['after_manager_name'] ?? '',
            //转岗后工作天数轮休规则
            $item['after_working_day_rest_type_name'] ?? '',
        ];
        if (get_country_code() != GlobalEnums::TH_COUNTRY_CODE) { //转岗后车辆类型
            $result_data[] = $item['after_vehicle_source_name'] ?? '';
        }
        //转岗后用车开始时间
        $result_data[] = $item['rental_car_created_at'] ?? '';
        //期望转岗日期
        $result_data[] = $item['after_date'];
        //实际转岗日期
        $result_data[] = $item['actual_after_date'] ?? '';

        return $result_data;
    }
    /**
     * 转义字段
     * @param $data
     * @return mixed
     */
    public function translateColumn($data)
    {
        //获取部门、职位、用户信息、网点信息
        [$departmentInfo, $positionInfo, $userInfo, $storeInfo, $roles, $manager, $regionInfo, $pieceInfo] =
            JobTransferService::getInstance()->genBaseData($data,Enums::FETCH_DATA_EXPORT);

        //审批状态
        $stateList = SysService::getInstance()->getAuditState(JobTransferEnums::STATE_TYPE_TOTAL);
        //转岗状态
        $transferStateList = SysService::getInstance()->getTransferState();
        //转岗类型
        $typeList = SysService::getInstance()->getTransferType();
        //角色
        $roles = SysService::getInstance()->getAllRoleList();
        $roleColumnId = sprintf('role_name_%s', substr(static::$language, 0, 2));
        $roleList = array_column($roles, $roleColumnId, 'role_id');
        //车辆来源
        $vehicleSource = SysService::getInstance()->getVehicleSource();
        //获取转岗失败后，是否重新申请
        $transferFailList = $transferFailDetailList = [];
        foreach ($data as $v) {
            if ($v['state'] == JobTransferEnums::JOB_TRANSFER_STATE_FAIL) {
                $transferFailList[] = $v['staff_id'];
            }
        }
        if (!empty($transferFailList)) {
            //转岗失败的申请，是否存在
            $transferFailDetailList = JobTransferModel::find([
                'conditions' => 'staff_id in({staff_ids:array}) and state = 1 and approval_state in(1,2)',
                'bind' => [
                    'staff_ids' => $transferFailList,
                ],
                'columns' => 'staff_id',
            ])->toArray();
            $transferFailDetailList = array_column($transferFailDetailList, 'staff_id');
        }
        $add_hour = $this->config->application->add_hour;

        foreach ($data as &$v) {
            $v['staff_name']                        = $userInfo[$v['staff_id']]['name'] ?? '';
            $v['submitter_name']                    = $userInfo[$v['submitter_id']]['name'] ?? '';
            $v['current_department_name']           = $departmentInfo[$v['current_department_id']]['name'] ?? '';
            $v['current_position_name']             = $positionInfo[$v['current_position_id']]['job_name'] ?? '';
            $v['current_store_name']                = $storeInfo[$v['current_store_id']]['name'] ?? '';
            $v['current_piece_name']                = $pieceInfo[$v['current_piece_id']] ?? '';
            $v['current_region_name']               = $regionInfo[$v['current_region_id']] ?? '';
            $v['current_role_name']                 = $this->translateRoles($v['current_role_id'], $roleList);
            $v['approval_state_title']              = $stateList[$v['approval_state']] ?? '';
            $v['state_title']                       = $transferStateList[$v['state']] ?? '';
            $v['type_title']                        = $typeList[$v['type']] ?? '';
            $v['after_department_name']             = $departmentInfo[$v['after_department_id']]['name'] ?? '';
            $v['after_position_name']               = $positionInfo[$v['after_position_id']]['job_name'] ?? '';
            $v['after_store_name']                  = $storeInfo[$v['after_store_id']]['name'] ?? '';
            $v['after_piece_name']                  = $pieceInfo[$v['after_piece_id']] ?? '';
            $v['after_region_name']                 = $regionInfo[$v['after_region_id']] ?? '';
            $v['before_working_day_rest_type_name'] = !empty($v['before_working_day_rest_type']) ? static::$t->_(Enums::$all_working_day_rest_type[$v['before_working_day_rest_type']]) : '';
            $v['after_working_day_rest_type_name']  = !empty($v['after_working_day_rest_type']) ? static::$t->_(Enums::$all_working_day_rest_type[$v['after_working_day_rest_type']]) : '';
            $v['current_company_name']              = $departmentInfo[$v['current_company_id']]['name'] ?? '';
            $v['after_company_name']                = $departmentInfo[$v['after_company_id']]['name'] ?? '';
            $v['after_manager_name']                = $userInfo[$v['after_manager_id']]['name'] ?? '';
            $v['after_vehicle_source_name']         = $vehicleSource[$v['car_owner']] ?? '';
            $v['current_hire_type_name']            = !empty($v['current_hire_type']) ? static::$t->_('hire_type_' . $v['current_hire_type']) : '';
            $v['after_hire_type_name']              = !empty($v['after_hire_type']) ? static::$t->_('hire_type_' . $v['after_hire_type']) : '';
            $v['hire_date']                         = isset($userInfo[$v['staff_id']]['hire_date']) && $userInfo[$v['staff_id']]['hire_date']
                ? date('Y-m-d', strtotime($userInfo[$v['staff_id']]['hire_date']))
                : '';
            $v['created_at'] = isset($v['created_at'])
                ? date('Y-m-d', strtotime($v['created_at']) + $add_hour * 3600)
                : '';

            if ($v['car_owner'] == JobTransferModel::VEHICLE_SOURCE_RENTAL_CODE) {
                $v['rental_car_created_at'] = date('Y-m-d', strtotime($v['rental_car_cteated_at']));
            }
            unset($v['rental_car_cteated_at']);

            //编辑按钮是否显示
            //待转岗状态才显示编辑按钮
            $v['is_show_edit_btn'] = $v['state'] == JobTransferEnums::JOB_TRANSFER_STATE_PENDING && $v['approval_state'] == Enums\ByWorkflowEnums::BY_OPERATE_PASS
                ? JobTransferEnums::BTN_SHOW_STATE_SHOW: JobTransferEnums::BTN_SHOW_STATE_HIDE;
            //立即转岗按钮是否显示
            //转岗失败&不存在待转岗才显示立即转岗按钮
            $v['is_show_do_transfer_btn'] = $v['state'] == JobTransferEnums::JOB_TRANSFER_STATE_FAIL && (
                empty($transferFailDetailList) || !in_array($v['staff_id'], $transferFailDetailList)
            ) ? JobTransferEnums::BTN_SHOW_STATE_SHOW: JobTransferEnums::BTN_SHOW_STATE_HIDE;

            //激活确认单按钮是否显示，2种case
            //1. 一线、非一线
            //转岗确认时，驳回
            //2. 一线、非一线
            //转岗确认时，同意，重发确认单，驳回
            if ($this->isShowActivate($v)) {
                $v['is_show_activate_btn'] = JobTransferEnums::BTN_SHOW_STATE_SHOW;
            } else {
                $v['is_show_activate_btn'] = JobTransferEnums::BTN_SHOW_STATE_HIDE;
            }
        }
        return $data;
    }

    /**
     * @description 转译角色
     * @param $roleIds
     * @param $roleList
     * @return string
     */
    public function translateRoles($roleIds, $roleList)
    {
        if (!is_string($roleIds)) {
            return '';
        }
        $rolesList = explode(',', $roleIds);
        $result = array_map(function ($item) use($roleList) {
            return $roleList[$item] ?? '';
        }, $rolesList);

        $result = array_values(array_filter($result));
        return join(',', $result);
    }

    /**
     * 导出
     * @param array $paramIn
     * @param $user
     * @return array
     */
    public function export($paramIn = [], $user): array
    {
        //获取传入参数
        $param['name'] = $paramIn['name'] ?? '';
        $param['type'] = $paramIn['type'] ?? 0;
        $param['state'] = $paramIn['state'] ?? 0;
        $param['after_date_start'] = $paramIn['after_date_start'] ?? 0;
        $param['after_date_end'] = $paramIn['after_date_end'] ?? 0;
        $param['hc_id'] = $paramIn['hc_id'] ?? 0;
        $param['approval_state'] = $paramIn['approval_state'] ?? 0;
        $param['page_num'] = $paramIn['page_num'] ?? 1;
        $param['page_size'] = $paramIn['page_size'] ?? 50000;
        $offset = $param['page_size'] * ($param['page_num'] - 1);
        $param['name'] = trim($param['name']);
        $param['data_source'] = $paramIn['data_source'] ?? '';
        $param['submitter_id'] = $paramIn['submitter_id'] ?? '';

        //查询列表
        [$items, $count]  = $this->getBaseTransferData($param, $offset, $param['page_size']);

        return $this->exportData($items, $user);
    }

    /**
     * @description 编辑
     * @param array $paramIn
     * @param $user
     * @return bool
     * @throws ValidationException
     */
    public function edit($paramIn = [], $user)
    {
        $id                      = $paramIn['id'];
        $expectDate              = $paramIn['expect_date'];
        $afterManagerId          = $paramIn['after_manager_id'];
        $afterRoleIds            = $paramIn['after_role_ids'];
        $afterWorkingDayRestType = $paramIn['after_working_day_rest_type'];

        $info = JobTransferModel::findFirstById($id);
        if (empty($info)) {
            throw new \Exception('data not exist');
        }

        if ($info->state != JobTransferEnums::JOB_TRANSFER_STATE_PENDING || $info->approval_state != ByWorkflowEnums::BY_OPERATE_PASS) {
            throw new \Exception('please refresh');
        }

        $params = [
            'id'                          => $id,
            'after_date'                  => $expectDate,
            'after_manager_id'            => $afterManagerId,
            'after_role_ids'              => $afterRoleIds,
            'after_working_day_rest_type' => $afterWorkingDayRestType,
            'operator_id'                 => $user,
            'type'                        => JobTransferEnums::JOB_TRANSFER_EDIT_TRANSFER_INFO,
        ];
        $this->logger->info('edit----参数：' . json_encode($params));
        $ac = new ApiClient('by', '', 'update_job_transfer', static::$language);
        $ac->setParams([$params]);
        $res = $ac->execute();
        $this->logger->info('edit----结果' . json_encode($res));
        return true;
    }

    /**
     * 检验HC是否有效
     * @param array $paramIn
     * @return bool
     */
    public function checkHcValidate($paramIn = []): bool
    {
        $jobTransferId = $paramIn['id'] ?? 0;
        if (empty($jobTransferId)) {
            return false;
        }

        $jobInfo = JobTransferModel::findFirst($jobTransferId);
        $hcInfo  = HrHcModel::findFirst([
            'conditions' => 'hc_id = :id:',
            'bind' => [
                'id' => $jobInfo->hc_id,
            ],
        ]);

        //校验截止日期 & hc剩余人数
        if (isset($hcInfo->surplusnumber) && $hcInfo->surplusnumber > 0 &&
            strtotime($hcInfo->expirationdate) > time()) {
            return true;
        } else {
            return false;
        }
    }

    /**
     *
     * @param $items
     * @param $user
     * @return array
     */
    private function exportData($items, $user)
    {
        $new_data = [];
        if (!empty($items) && is_array($items)) {

            //获取部门、职位、用户信息、网点信息
            [$departmentInfo, $positionInfo, $userInfo, $storeInfo, $hcInfo,$manager,$regions,$pieces] =
                JobTransferService::getInstance()->genBaseData($items,Enums::FETCH_DATA_EXPORT);

            //审批状态
            $stateList = SysService::getInstance()->getAuditState();
            //转岗状态
            $transferStateList = SysService::getInstance()->getTransferState();
            //转岗类型
            $typeList = SysService::getInstance()->getTransferType();
            //获取数据源
            $sourceList = SysService::getInstance()->getTransferDataSource();
            //车辆类型
            $carType = SysService::getInstance()->getJobTransferCarOwner();
            $carTypeName = array_column($carType, 'value', 'key');
            //查询失败原因
            $ids = implode(",", array_column($items, 'id'));
            $sql = "SELECT 
                        id,pid,failure_reason
                    FROM 
                        job_transfer_operate_log 
                    WHERE 
                        id in(SELECT max(id) AS id FROM  job_transfer_operate_log WHERE operate_id in (1,2) AND pid in ({$ids}) GROUP BY pid )";
            $log_list = $this->getDI()->get('db_backyard')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            $log_list = array_column($log_list, 'failure_reason', 'pid');

            foreach ($items as $k => $item) {
                //用户姓名
                $items[$k]['staff_name'] = $userInfo[$item['staff_id']]['name'] ?? '';
                //身份信息
                $items[$k]['staff_identity'] = $userInfo[$item['staff_id']]['identity'] ?? '';
                //手机号
                $items[$k]['staff_mobile'] = $userInfo[$item['staff_id']]['mobile'] ?? '';
                //部门
                $items[$k]['current_department_name'] = $departmentInfo[$item['current_department_id']]['name'] ?? '';
                $items[$k]['after_department_name'] = $departmentInfo[$item['after_department_id']]['name'] ?? '';
                //职位
                $items[$k]['current_position_name'] = $positionInfo[$item['current_position_id']]['job_name'] ?? '';
                $items[$k]['after_position_name'] = $positionInfo[$item['after_position_id']]['job_name'] ?? '';
                //网点
                $items[$k]['current_store_name'] = $storeInfo[$item['current_store_id']]['name'] ?? '';
                $items[$k]['after_store_name'] = $storeInfo[$item['after_store_id']]['name'] ?? '';
                //审批状态
                $items[$k]['approval_state_title'] = $stateList[$item['approval_state']] ?? '';
                //转岗状态
                $items[$k]['state_title'] = $transferStateList[$item['state']] ?? '';
                //转岗类型
                $items[$k]['type_title'] = $typeList[$item['type']] ?? '';
                //来源
                $items[$k]['data_source_title'] = $sourceList[$item['data_source']] ?? '';
                //直线上级
                $items[$k]['current_manager_name'] = $item['current_manager_id'] . ' ' . ($userInfo[$item['current_manager_id']]['name'] ?? '');
                //直线上级
                $items[$k]['after_manager_name'] = $item['after_manager_id'] . ' ' . ($userInfo[$item['after_manager_id']]['name'] ?? '');
                //车辆类型
                $items[$k]['car_owner_title'] = $carTypeName[$item['car_owner']] ?? '';
                //大区
                $items[$k]['current_regions_title'] = $regions[$storeInfo[$item['current_store_id']]['manage_region']] ?? '';
                $items[$k]['after_regions_title'] = $regions[$storeInfo[$item['after_store_id']]['manage_region']] ?? '';
                //片区
                $items[$k]['current_pieces_title'] = $pieces[$storeInfo[$item['current_store_id']]['manage_piece']] ?? '';
                $items[$k]['after_pieces_title'] = $pieces[$storeInfo[$item['after_store_id']]['manage_piece']] ?? '';
                //失败原因
                $failure_reason = !empty($log_list[$item['id']]) ? json_decode( $log_list[$item['id']],true) : [];
                $items[$k]['failure_reason'] = $failure_reason[static::$language] ?? '';

                $items[$k]['before_working_day_rest_type'] = !empty($item['before_working_day_rest_type']) ? static::$t->_('working_day_rest_type_'.$item['before_working_day_rest_type']) : '';
                $items[$k]['after_working_day_rest_type'] = !empty($item['after_working_day_rest_type']) ? static::$t->_('working_day_rest_type_'.$item['after_working_day_rest_type']) : '';
            }
        }

        $positions = HrStaffInfoPositionModel::find([
            'conditions' => 'staff_info_id = :staff_info_id:',
            'columns' => 'staff_info_id, position_category',
            'bind' => [
                'staff_info_id' => $user,
            ],
        ])->toArray();
        $positionArr = array_column($positions, 'position_category');

        foreach ($items as $key => $val) {
            $new_data[$key][] = $val['staff_id'];
            $new_data[$key][] = $val['hc_id'];
            $new_data[$key][] = $val['staff_name'];
            $new_data[$key][] = $val['staff_identity'];
            $new_data[$key][] = $val['staff_mobile'];
            $new_data[$key][] = $val['serial_no'];
            $new_data[$key][] = $val['type_title'];
            $new_data[$key][] = $val['state_title'];
            $new_data[$key][] = $val['approval_state_title'];
            $new_data[$key][] = $val['data_source_title'];
            $new_data[$key][] = 'Flash express';
            $new_data[$key][] = $val['current_department_name'];
            $new_data[$key][] = $val['current_store_name'];
            $new_data[$key][] = $val['current_position_name'];
            $new_data[$key][] = $val['current_manager_name'];
            $new_data[$key][] = $val['before_working_day_rest_type'];//转岗前工作天数轮休规则
            $new_data[$key][] = 'Flash express';
            $new_data[$key][] = $val['after_department_name'];
            $new_data[$key][] = $val['after_store_name'];
            $new_data[$key][] = $val['after_position_name'];
            $new_data[$key][] = $val['after_manager_name'];
            $new_data[$key][] = $val['after_working_day_rest_type'];//转岗后工作天数轮休规则
            $new_data[$key][] = $val['after_date'];
            if (array_intersect([42, 14, 99], $positionArr)) { //Payroll、系统管理员、超级管理员角色
                $new_data[$key][] = $val['before_base_salary'] / 100;
                $new_data[$key][] = $val['before_exp_allowance'] / 100;
                $new_data[$key][] = $val['before_position_allowance'] / 100;
                $new_data[$key][] = $val['before_car_rental'] / 100;
                $new_data[$key][] = $val['before_trip_payment'] / 100;
                $new_data[$key][] = $val['before_notebook_rental'] / 100;
                $new_data[$key][] = $val['before_house_rental'] / 100;
                $new_data[$key][] = $val['before_food_allowance'] / 100;
                $new_data[$key][] = $val['before_dangerous_area'] / 100;
                $new_data[$key][] = $val['before_deminimis_benefits'] / 100;
                $new_data[$key][] = $val['before_performance_allowance'] / 100;
                $new_data[$key][] = $val['before_other_non_taxable_allowance'] / 100;
                $new_data[$key][] = $val['before_other_taxable_allowance'] / 100;
                $new_data[$key][] = $val['before_phone_subsidy'] / 100;
                $new_data[$key][] = $val['after_base_salary'] / 100;
                $new_data[$key][] = $val['after_exp_allowance'] / 100;
                $new_data[$key][] = $val['after_position_allowance'] / 100;
                $new_data[$key][] = $val['after_car_rental'] / 100;
                $new_data[$key][] = $val['after_trip_payment'] / 100;
                $new_data[$key][] = $val['after_notebook_rental'] / 100;
                $new_data[$key][] = $val['after_house_rental'] / 100;
                $new_data[$key][] = $val['after_food_allowance'] / 100;
                $new_data[$key][] = $val['after_dangerous_area'] / 100;
                $new_data[$key][] = $val['after_deminimis_benefits'] / 100;
                $new_data[$key][] = $val['after_performance_allowance'] / 100;
                $new_data[$key][] = $val['after_other_non_taxable_allowance'] / 100;
                $new_data[$key][] = $val['after_other_taxable_allowance'] / 100;
                $new_data[$key][] = $val['after_phone_subsidy'] / 100;
            }
            $new_data[$key][] = $val['car_owner_title'];
            $new_data[$key][] = $val['rental_car_cteated_at'];

            $new_data[$key][] = $val['current_regions_title'];
            $new_data[$key][] = $val['current_pieces_title'];
            $new_data[$key][] = $val['after_regions_title'];
            $new_data[$key][] = $val['after_pieces_title'];
            $new_data[$key][] = $val['failure_reason'];
        }
        $file_name = static::$t->_('Transfer_') . date('YmdHis');

        if (array_intersect([42, 14, 99], $positionArr)) {
            $header = [
                static::$t->_('job_transfer.36'),               //工号
                static::$t->_('HCId'),                          //HCId
                static::$t->_('job_transfer.38'),               //姓名
                static::$t->_('id_card_no'),                    //id_card_no
                static::$t->_('transfer_staff_phone'),          //transfer_staff_phone
                static::$t->_('job_transfer.35'),               //审批编号
                static::$t->_('job_transfer.33'),               //转岗类型
                static::$t->_('job_transfer.34'),               //转岗状态
                static::$t->_('job_transfer.32'),               //审批状态
                static::$t->_('job_transfer.39'),               //来源
                static::$t->_('job_transfer.10'),               //转岗前公司
                static::$t->_('job_transfer.11'),               //转岗前部门
                static::$t->_('job_transfer.14'),               //转岗前所属网点
                static::$t->_('job_transfer.13'),               //转岗前职位
                static::$t->_('job_transfer.12'),               //转岗前直线上级
                static::$t->_('before_working_day_rest_type'),  //转岗前工作天数轮休规则
                static::$t->_('job_transfer.20'),               //转岗后公司
                static::$t->_('job_transfer.22'),               //转岗后部门
                static::$t->_('job_transfer.30'),               //转岗后所属网点
                static::$t->_('job_transfer.28'),               //转岗后职位
                static::$t->_('job_transfer.26'),               //转岗后直线上级
                static::$t->_('after_working_day_rest_type'),   //转岗后工作天数轮休规则
                static::$t->_('job_transfer.15'),               //转岗日期
                static::$t->_('job_transfer.1'),                //转岗前基本工资
                static::$t->_('job_transfer.4'),                //转岗前经验津贴
                static::$t->_('job_transfer.8'),                //转岗前职位津贴
                static::$t->_('job_transfer.2'),                //转岗前租车津贴
                static::$t->_('job_transfer.9'),                //转岗前油补
                static::$t->_('job_transfer.7'),                //转岗前电脑补贴
                static::$t->_('job_transfer.6'),                //转岗前租房津贴
                static::$t->_('job_transfer.5'),                //转岗前餐补
                static::$t->_('job_transfer.3'),                //转岗前危险区域补贴
                static::$t->_('job_transfer.45'),               //转岗前无税金补贴
                static::$t->_('job_transfer.46'),               //转岗前绩效补贴
                static::$t->_('job_transfer.47'),               //转岗前其他无税金补贴
                static::$t->_('job_transfer.48'),               //转岗前其他纳税津贴
                static::$t->_('job_transfer.53'),               //转岗前通话补贴
                static::$t->_('job_transfer.18'),               //转岗后基本工资
                static::$t->_('job_transfer.23'),               //转岗后经验津贴
                static::$t->_('job_transfer.29'),               //转岗后职位津贴
                static::$t->_('job_transfer.19'),               //转岗后租车津贴
                static::$t->_('job_transfer.31'),               //转岗后油补
                static::$t->_('job_transfer.27'),               //转岗后电脑补贴
                static::$t->_('job_transfer.25'),               //转岗后租房津贴
                static::$t->_('job_transfer.24'),               //转岗后餐补
                static::$t->_('job_transfer.21'),               //转岗后危险区域补贴
                static::$t->_('job_transfer.49'),               //转岗后无税金补贴
                static::$t->_('job_transfer.50'),               //转岗后绩效补贴
                static::$t->_('job_transfer.51'),               //转岗后其他无税金补贴
                static::$t->_('job_transfer.52'),               //转岗后其他纳税津贴
                static::$t->_('job_transfer.54'),               //转岗后通化补贴
                static::$t->_('job_transfer.16'),               //车辆类型
                static::$t->_('job_transfer.17'),               //开始日期
                static::$t->_('job_transfer.40'),               //转岗前大区
                static::$t->_('job_transfer.41'),               //转岗前片区
                static::$t->_('job_transfer.42'),               //转岗后大区
                static::$t->_('job_transfer.43'),               //转岗后片区
                static::$t->_('job_transfer.44'),               //转岗失败原因
            ];
        } else {
            $header = [
                static::$t->_('job_transfer.36'),               //工号
                static::$t->_('HCId'),                          //HCId
                static::$t->_('job_transfer.38'),               //姓名
                static::$t->_('id_card_no'),                    //id_card_no
                static::$t->_('transfer_staff_phone'),          //transfer_staff_phone
                static::$t->_('job_transfer.35'),               //审批编号
                static::$t->_('job_transfer.33'),               //转岗类型
                static::$t->_('job_transfer.34'),               //转岗状态
                static::$t->_('job_transfer.32'),               //审批状态
                static::$t->_('job_transfer.39'),               //来源
                static::$t->_('job_transfer.10'),               //转岗前公司
                static::$t->_('job_transfer.11'),               //转岗前部门
                static::$t->_('job_transfer.14'),               //转岗前所属网点
                static::$t->_('job_transfer.13'),               //转岗前职位
                static::$t->_('job_transfer.12'),               //转岗前直线上级
                static::$t->_('before_working_day_rest_type'),  //转岗前工作天数轮休规则
                static::$t->_('job_transfer.20'),               //转岗后公司
                static::$t->_('job_transfer.22'),               //转岗后部门
                static::$t->_('job_transfer.30'),               //转岗后所属网点
                static::$t->_('job_transfer.28'),               //转岗后职位
                static::$t->_('job_transfer.26'),               //转岗后直线上级
                static::$t->_('after_working_day_rest_type'),   //转岗后工作天数轮休规则
                static::$t->_('job_transfer.15'),               //转岗日期
                static::$t->_('job_transfer.16'),               //车辆类型
                static::$t->_('job_transfer.17'),               //开始日期
                static::$t->_('job_transfer.40'),               //转岗前大区
                static::$t->_('job_transfer.41'),               //转岗前片区
                static::$t->_('job_transfer.42'),               //转岗后大区
                static::$t->_('job_transfer.43'),               //转岗后片区
                static::$t->_('job_transfer.44'),               //转岗失败原因
            ];
        }
        return $this->exportExcel($header, $new_data, $file_name);
    }

    /**
     * 获取批量添加进度
     * @param array $paramIn
     * @return array|false
     */
    public function getBatchAddProgress($paramIn = [])
    {
        $batchNumber = $paramIn['batch_number'] ?? "";

        $this->getDI()->get('logger')->info('getBatchAddProgress----参数：' . json_encode($batchNumber));
        $ac = new ApiClient('by', '', 'get_batch_add_progress', static::$language);
        $ac->setParams([
            [
                "batch_number" => $batchNumber,
            ],
        ]);

        $res = $ac->execute();
        $this->getDI()->get('logger')->info('getBatchAddProgress----结果' . json_encode($res));

        return $res['result'] ?? [];
    }

    /**
     * 获取立即转岗极端
     * @param array $paramIn
     * @param $user
     */
    public function getDoTransferProgress($paramIn = [], $user)
    {
        $transferId = $paramIn['id'] ?? "";

        $this->getDI()->get('logger')->info('getDoTransferProgress----参数：' . json_encode($transferId));
        $ac = new ApiClient('by', '', 'get_transfer_process', static::$language);
        $ac->setParams([
            [
                'transfer_id' => $transferId,
                'operator_id' => $user,
            ],
        ]);

        $res = $ac->execute();
        $this->getDI()->get('logger')->info('getDoTransferProgress----结果' . json_encode($res));

        return $res['result'] ?? [];
    }

    /**
     * @description 获取指定工号的权限
     * @param $staff_info_id
     * @return array
     */
    public function getSubmitterPermission($staff_info_id): array
    {
        //获取申请人的角色
        $staffPosition = HrStaffInfoPositionModel::find([
            'conditions' => 'staff_info_id = :staff_info_id:',
            'bind' => [
                'staff_info_id' => $staff_info_id,
            ],
        ])->toArray();
        $staffPosition = array_column($staffPosition, 'position_category');

        //获取申请人的下级
        //非目标制定人，检测是否是上级
        //$manageStaffInfo = HrStaffInfoModel::find([
        //    'conditions' => 'manger = :manger: and is_sub_staff = :is_sub_staff: and formal = :formal:',
        //    'bind'       => [
        //        'manger'       => $staff_info_id,
        //        'is_sub_staff' => StaffInfoEnums::IS_SUB_STAFF_NO,
        //        'formal'       => StaffInfoEnums::FORMAL_IN,
        //    ],
        //    'columns'    => 'staff_info_id',
        //])->toArray();
        //$manageStaffInfoIds = array_column($manageStaffInfo, 'staff_info_id');

        //权限
        //1. OA组织负责人以及负责人助理：可以查看自己申请的数据+转岗人转岗前或者转岗后属于自己负责的组织以及下级多级组织的员工的数据
        //2. (废弃)直线上级：可以查看转岗人转岗前或者转岗后属于自己直线下级的员工
        //3. HR Management[17]、HRIS管理员[41]、超级管理员[99]：可以查看所有数据
        //4. HRBP[68]、Payroll [42]、大区经理[56]：可以查看转岗人转岗前或者转岗后属于自己员工管理中管辖范围内的员工
        // 大区经理[56] 权限为光辉邮件需求追加
        //5. 其他人：可以查看自己申请的数据

        //数据结构
        //all
        //  +-- true/false
        //part
        //  +-- manage_staff
        //  +-- org
        //      +-- department
        //      +-- store
        //  +-- special_roles
        //      +-- department
        //      +-- store
        $permission = [];

        //全部权限
        $permission['all'] = false;
        //全部权限
        //HR Management[17]、HRIS管理员[41]、超级管理员[99]
        if (array_intersect($staffPosition, [RoleEnums::ROLE_HR_MANAGEMENT, RoleEnums::ROLE_HRIS, RoleEnums::ROLE_SUPER_ADMIN])) {
            $permission['all'] = true;
            return $permission;
        }

        //部分权限
        //管辖下级
        //if (!empty($manageStaffInfoIds)) {
        //    $permission['part']['manage_staff'] = $manageStaffInfoIds;
        //}

        //HrBP、Payroll获取数据管辖权限
        if (array_intersect($staffPosition, [RoleEnums::ROLE_HRBP, RoleEnums::ROLE_PAYROLL, RoleEnums::ROLE_AREA_MANAGER])) {
            $manageInfo                                        = (new StaffDepartmentAreasStoreRepository())->getStaffManageDepartmentsAreasStores($staff_info_id);
            $permission['part']['special_roles']['department'] = empty($manageInfo['department_ids']) ? [] : $manageInfo['department_ids'];
            $permission['part']['special_roles']['store']      = empty($manageInfo['stores_ids']) ? [] : $manageInfo['stores_ids'];
        }

        //TA可以查看个人代理的转岗
        if (array_intersect($staffPosition, [RoleEnums::ROLE_TA])) {
            $permission['part']['special_roles']['hire_type']  = true;
        }

        //根据组织架构获取管辖范围
        [$manageDepartment, $manageStore] = ManageOrganizationService::getInstance()->getManageOrganizationByStaffInfoId($staff_info_id);
        if (!empty($manageDepartment)) {
            $permission['part']['org']['department'] = $manageDepartment;
        }
        if (!empty($manageStore)) {
            $permission['part']['org']['store'] = $manageStore;
        }

        return $permission;
    }

    /**
     * @description 查询全部数据条数
     * @param $param
     * @param $submitter_id
     * @return int
     */
    public function getDataExportTotal($param, $submitter_id): int
    {
        // 根据提交人权限构建Builder
        $builder = $this->getQueryDataBuilder($param);
        $builder = $this->getBuilderWithPermission($builder, $submitter_id, $param);
        //echo $builder->getPhql();die;

        $totalCount = $builder->columns('count(1) as count')->getQuery()->getSingleResult()->count;
        return intval($totalCount);
    }

    /**
     * @param $lang
     * @return array
     */
    public function getExportExcelHeaderFields($lang = null): array
    {
        // 定义基础字段键名
        $baseFields = [
            'staff_info_id',                           //工号
            'job_transfer.38',                         //姓名
            'job_transfer.35',                         //转岗审批编号
            'job_transfer.34',                         //转岗状态
            'acceptance_apply_date',                   //申请日期
            'job_transfer.32',                         //审批状态
            'job_transfer.10',                         //转岗前公司
            'job_transfer.11',                         //转岗前所属部门
            'job_transfer.40',                         //转岗前大区
            'job_transfer.41',                         //转岗前片区
            'job_transfer.14',                         //转岗前所属网点
            'job_transfer.13',                         //转岗前职位
            'job_transfer.before_hire_type',           //转岗前雇佣类型
            'job_transfer.20',                         //转岗后公司
            'job_transfer.22',                         //转岗后所属部门
            'job_transfer.42',                         //转岗后大区
            'job_transfer.43',                         //转岗后片区
            'job_transfer.30',                         //转岗后所属网点
            'job_transfer.28',                         //转岗后职位
            'job_transfer.after_hire_type',            //转岗后雇佣类型
            'job_transfer.26',                         //转岗后直线上级
            'after_working_day_rest_type',             //转岗后工作天数轮休规则
        ];
        
        // 如果不是泰国，则添加转岗后车辆类型字段
        if (get_country_code() != GlobalEnums::TH_COUNTRY_CODE) {
            $baseFields[] = 'job_transfer.after_vehicle_source';
        }
        
        // 添加末尾字段
        $endFields = [
            'job_transfer.after_rental_car_created_at', //转岗后用车开始时间
            'job_transfer.after_date',                  //期望转岗日期
            'job_transfer.actual_after_date',           //实际转岗日期
        ];
        
        $allFields = array_merge($baseFields, $endFields);
        
        // 根据语言参数选择翻译方式
        if (empty($lang)) {
            return array_map(function($field) {
                return static::$t->_($field);
            }, $allFields);
        } else {
            $tmp = BaseService::getTranslation($lang);
            return array_map(function($field) use ($tmp) {
                return $tmp[$field];
            }, $allFields);
        }
    }

    /**
     * @param $lang
     * @return array
     */
    public function getExportPayrollList(): array
    {
        $baseHeader    = $this->getExportExcelHeaderFields();
        $payrollHeader = [];
        if (get_country_code() == GlobalEnums::TH_COUNTRY_CODE) {
            $payrollHeader = [
                //转岗前基本工资
                static::$t->_('job_transfer.base_salary_before'),
                //转岗前经验津贴
                static::$t->_('job_transfer.experience_before'),
                //转岗前职位津贴
                static::$t->_('job_transfer.position_before'),
                //转岗前车补
                static::$t->_('job_transfer.car_before'),
                //转岗前饭补
                static::$t->_('job_transfer.food_before'),
                //转岗前危险补贴
                static::$t->_('job_transfer.risk_before'),
                //转岗前海岛补贴
                static::$t->_('job_transfer.island_before'),
                //转岗前电脑补贴
                static::$t->_('job_transfer.7'),
                //转岗前租房津贴
                static::$t->_('job_transfer.6'),
                //转岗后基本工资
                static::$t->_('job_transfer.base_salary_after'),
                //转岗后经验津贴
                static::$t->_('job_transfer.experience_after'),
                //转岗后职位津贴
                static::$t->_('job_transfer.position_after'),
                //转岗后车补
                static::$t->_('job_transfer.car_after'),
                //转岗后饭补
                static::$t->_('job_transfer.food_after'),
                //转岗后危险补贴
                static::$t->_('job_transfer.risk_after'),
                //转岗后海岛补贴
                static::$t->_('job_transfer.island_after'),
                //转岗后电脑补贴
                static::$t->_('job_transfer.27'),
                //转岗后租房津贴
                static::$t->_('job_transfer.25'),
            ];
        }
        if (get_country_code() == GlobalEnums::MY_COUNTRY_CODE) {
            $salaryItemsData   = $this->getFrontlineSalaryBaseSettingMY();
            $jobTransferBefore = $jobTransferAfter = [];
            foreach ($salaryItemsData as $salaryItem) {
                $jobTransferBefore[] = static::$t->_('job_transfer_salary_before',
                    ['salary_item_name' => $salaryItem['pay_code']]);
                $jobTransferAfter[]  = static::$t->_('job_transfer_salary_after',
                    ['salary_item_name' => $salaryItem['pay_code']]);
            }
            $payrollHeader = array_merge($jobTransferBefore, $jobTransferAfter);
        }
        if (get_country_code() == GlobalEnums::PH_COUNTRY_CODE) {
            $payrollHeader = [
                //转岗前基本工资
                static::$t->_('job_transfer.base_salary_before'),
                //转岗前无税金补贴
                static::$t->_('job_transfer.45'),
                //转岗前其他无税金补贴
                static::$t->_('job_transfer.47'),
                //转岗前饭补
                static::$t->_('job_transfer.food_before'),
                //转岗后基本工资
                static::$t->_('job_transfer.base_salary_after'),
                //转岗后无税金补贴
                static::$t->_('job_transfer.49'),
                //转岗后其他无税金补贴
                static::$t->_('job_transfer.51'),
                //转岗后饭补
                static::$t->_('job_transfer.food_after'),
            ];
        }
        return array_merge($baseHeader, $payrollHeader);
    }

    public function getFrontlineSalaryBaseSettingMY()
    {
        if ($salaryBaseIds = (new SettingEnvModel)->getSetVal('frontline_salary_base_setting', ',')) {
            return SalaryBaseSettingModel::find([
                'columns'    => 'id,pay_code',
                'conditions' => 'id in ({ids:array})',
                'bind'       => ['ids' => $salaryBaseIds],
                'order'      => 'field(id,' . implode(',',$salaryBaseIds) . ')',
            ])->toArray();
        }
        return [];
    }

    /**
     * @param $job_transfer_info
     * @return bool
     */
    public function isShowActivate($job_transfer_info): bool
    {
        return in_array($job_transfer_info['type'],
                [JobTransferModel::TRANSFER_TYPE_FRONT_LINE, JobTransferModel::TRANSFER_TYPE_NOT_FRONT_LINE]) && (
                $job_transfer_info['state'] == JobTransferEnums::JOB_TRANSFER_STATE_NO && $job_transfer_info['confirm_state'] == JobTransferEnums::CONFIRM_STATE_REJECT ||
                $job_transfer_info['state'] == JobTransferEnums::JOB_TRANSFER_STATE_PENDING && $job_transfer_info['confirm_state'] == JobTransferEnums::CONFIRM_STATE_REJECT &&
                in_array($job_transfer_info['approval_state'],
                    [ByWorkflowEnums::BY_OPERATE_WAIT_AUDIT, ByWorkflowEnums::BY_OPERATE_PASS])
            );
    }
}
