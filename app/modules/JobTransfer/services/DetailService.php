<?php

namespace App\Modules\JobTransfer\Services;

use App\Library\ApiClient;
use App\Library\Enums;
use app\library\Enums\AuditDetailOperationsEnums;
use App\Library\Enums\ByWorkflowEnums;
use app\library\Enums\JobTransferEnums;
use App\Library\Enums\VehicleInfoEnums;
use App\Library\Validation\ValidationException;
use App\Models\backyard\AuditApplyModel;
use App\Models\backyard\ByWorkflowNodeModel;
use app\models\backyard\JobTransferExtendModel;
use App\Models\backyard\JobTransferModel;
use App\Models\backyard\JobTransferSpecialModel;
use App\Models\backyard\SettingEnvModel;
use App\Models\backyard\SysAttachmentModel;
use App\Models\backyard\SysDepartmentModel;
use App\Models\backyard\SysManagePieceModel;
use App\Models\backyard\SysManageRegionModel;
use App\Models\backyard\WorkflowFormModel;
use App\Modules\Extinguisher\Models\HrStaffInfoPositionModel;
use App\Modules\Hc\Models\SysStoreModel;
use App\Modules\Hc\Models\VehicleInfoModel;
use App\Modules\Organization\Models\HrOrganizationDepartmentStoreRelationModel;
use App\Modules\User\Models\HrJobTitleModel;
use App\Modules\User\Models\HrStaffInfoModel;
use App\Modules\WorkflowManagement\Models\ByWorkflowModel;
use App\Repository\HrStaffRepository;
use Exception;

class DetailService extends BaseService
{
    public static $validate_detail = [
        'id' => 'Required|IntGe:1',
    ];

    public static $validate_approval_state = [
        'state_type' => 'Required|IntGe:1',
    ];

    public static $validate_staff_detail = [
        'staff_id' => 'Required|IntGe:1',
    ];

    public static $validate_department = [
        'staff_id' => 'Required|IntGe:1',
    ];

    public static $validate_position = [
        'department_id' => 'Required|IntGe:1',
    ];

    public static $validate_position_job = [
        'department_id' => 'Required|IntGe:1',
        'staff_id' => "Required|IntGe:1|>>>:staff_id param error",
    ];

    public static $validate_working_day = [
        "department_id" => "Required|Int",
        "job_title_id"  => "Required|Int",
    ];

    public static $validate_hc_list = [
        "department_id" => "Required|Int",
        "store_id" => "Required|Str",
        "job_title_id" => "Required|Int",
    ];

    public static $validate_role_list = [
        'id'            => "Required|Int",
        "department_id" => "Required|Int",
        "job_title_id"  => "Required|Int",
    ];

    public static $validate_update_info = [
        'id' => 'Required|IntGe:1',
        "after_role_ids" => "Required|Str",
        "after_manager_id" =>  'Required|IntGe:1|>>>:after_manager_id error',
    ];

    public static $validate_job_title = [
        "job_title_id"  => "Required|Int",
    ];

    public static $validate_hire_type = [
        "department_id" => "Int",
        "store_id"      => "Str",
        "position_id"   => "Int",
        "staff_id"      => "Int",
    ];

    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return DetailService
     */
    public static function getInstance(): DetailService
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    public function getJobTransferInfo($auditId): JobTransferModel
    {
        $transferInfo = JobTransferModel::findFirst($auditId);
        if (empty($transferInfo)) {
            throw new Exception("can not find job transfer id: $auditId");
        }
        return $transferInfo;
    }

    /**
     * 获取详情
     * @param $params
     * @param $uid
     * @return array
     * @throws Exception
     */
    public function getAuditDetail($params, $uid): array
    {
        $id           = $params['id'] ?? 0;
        $transferInfo = $this->getJobTransferInfo($id);

        //获取审批流
        $ac = new ApiClient('by', '', 'get_apply_detail', static::$language);
        $ac->setParams([[
            'type'         => $transferInfo->data_source == JobTransferEnums::JOB_TRANSFER_DATA_SOURCE_OA
                ? ByWorkflowEnums::BY_BIZ_TYPE_JT_OA
                : ByWorkflowEnums::BY_BIZ_TYPE_JT,
            'user'         => $transferInfo->staff_id,
            'id'           => $id,
            "come_from"    => 1,
            'date_created' => $transferInfo->created_at ?? '', //申请时间
        ]]);
        $detail = $ac->execute();
        $this->logger->info('getDetail----结果' . json_encode($detail));

        //审批流
        if (isset($detail['result']['data']) && $detail['result']['data']) {
            $requestData['audit_log_info'] = $detail['result']['data']['stream'] ?? [];
        }

        //转岗日志
        if (isset($detail['result']['log']) && $detail['result']['log']) {
            $requestData['job_transfer_detail_info'] = array_reverse($detail['result']['log']);
            $add_hour = $this->config->application->add_hour;
            if($requestData['job_transfer_detail_info']){
                foreach ($requestData['job_transfer_detail_info'] as $k => $v) {
                    $requestData['job_transfer_detail_info'][$k]['created_at'] = date('Y-m-d H:i:s',strtotime($v['created_at']) + $add_hour  * 3600);
                    $requestData['job_transfer_detail_info'][$k]['updated_at'] = date('Y-m-d H:i:s',strtotime($v['updated_at']) + $add_hour  * 3600);
                }
            }
        }
        $requestData['audit_log_info']           = $requestData['audit_log_info'] ?? [];
        $requestData['job_transfer_detail_info'] = $requestData['job_transfer_detail_info'] ?? [];
        return $requestData ?? [];
    }

    /**
     * 获取特殊转岗审批流
     * @param $params
     * @param $uid
     * @return array
     * @throws Exception
     */
    public function getSpecialAuditDetail($params, $uid): array
    {
        $id           = $params['id'] ?? 0;
        $transferInfo = $this->getJobTransferInfo($id);
        $transferSpecialInfo = JobTransferSpecialModel::findFirstByBatchCode($transferInfo->batch_code);
        if (empty($transferSpecialInfo)) {
            throw new Exception("can not find job transfer id: $id");
        }

        //获取审批流
        $ac = new ApiClient('by', '', 'get_apply_detail', static::$language);
        $ac->setParams([[
            'type'         => ByWorkflowEnums::BY_BIZ_TYPE_JT_SPECIAL,
            'user'         => $transferInfo->staff_id,
            'id'           => $transferSpecialInfo->id,
            "come_from"    => 1,
            'date_created' => $transferInfo->created_at ?? '', //申请时间
        ]]);
        $detail = $ac->execute();
        $this->logger->info('getDetail----结果' . json_encode($detail));

        //获取操作流
        $ac = new ApiClient('by', '', 'get_transfer_operate_log', static::$language);
        $ac->setParams([
            [
                'id' => $id,
            ],
        ]);
        $operateDetail = $ac->execute();
        $this->logger->info('getDetail----结果' . json_encode($operateDetail));

        //审批流
        if (isset($detail['result']['data']) && $detail['result']['data']) {
            $requestData['audit_log_info'] = $detail['result']['data']['stream'] ?? [];
        }

        //转岗日志
        if (isset($operateDetail['result']['log']) && $operateDetail['result']['log']) {
            $requestData['job_transfer_detail_info'] = array_reverse($operateDetail['result']['log']);
            $add_hour = $this->config->application->add_hour;
            if($requestData['job_transfer_detail_info']){
                foreach ($requestData['job_transfer_detail_info'] as $k => $v) {
                    $requestData['job_transfer_detail_info'][$k]['created_at'] = date('Y-m-d H:i:s',strtotime($v['created_at']) + $add_hour  * 3600);
                    $requestData['job_transfer_detail_info'][$k]['updated_at'] = date('Y-m-d H:i:s',strtotime($v['updated_at']) + $add_hour  * 3600);
                }
            }
        }
        $requestData['audit_log_info']           = $requestData['audit_log_info'] ?? [];
        $requestData['job_transfer_detail_info'] = $requestData['job_transfer_detail_info'] ?? [];
        return $requestData ?? [];
    }

    /**
     * @description 查看转岗详情
     * @param $params
     * @param $user
     * @return array
     * @throws Exception
     */
    public function getJobTransferApplyDetail($params, $user): array
    {
        $id = intval($params['id']);
        $transferInfo = $this->getJobTransferInfo($id);
        $transferInfo = $transferInfo->toArray();

        // 扩展数据
        $extendInfo = JobTransferExtendModel::findFirstByTransferId($transferInfo['id']);
        if (!empty($extendInfo)) {
            $extendInfo = $extendInfo->toArray();
        }

        //只有hrbp以及hrbp之后的审批人才能看到图片
        //获取图片
        if (isset($transferInfo['senior_auditor']) && $transferInfo['senior_auditor'] ||
            in_array($transferInfo['workflow_role_name'], ['job_transfer_new_v2', 'job_transfer_new_v3'])) {
            $imageList = $this->getJobTransferImages($id);
            $imageList = array_group_by_column($imageList, 'oss_bucket_type');
        }

        //获取解析数据
        [$department_info, $job_info, $user_info, $store_info, $roles, $manager, $region_info, $piece_info] =
            JobTransferService::getInstance()->genBaseData([$transferInfo],Enums::FETCH_DATA_DETAIL);

        //获取全部角色
        $rolesList = SysService::getInstance()->getAllRoleList();

        //获取车辆来源
        $vehicleSource = SysService::getInstance()->getJobTransferCarOwner();
        $vehicleSource = array_column($vehicleSource, 'value', 'key');

        //转岗状态
        $state = SysService::getInstance()->getTransferState();

        //个人信息 工号、姓名、性别、个人邮箱、身份证/护照、个人号码
        $res                   = [];
        $res['staff_id']       = $transferInfo['staff_id'];
        $res['name']           = $user_info[$transferInfo['staff_id']]['name'];
        $res['identity']       = $user_info[$transferInfo['staff_id']]['identity'];
        $res['mobile']         = $user_info[$transferInfo['staff_id']]['mobile'];
        $res['sex']            = self::$t->_('staff_sex.'.$user_info[$transferInfo['staff_id']]['sex']);
        $res['personal_email'] = $user_info[$transferInfo['staff_id']]['personal_email'];
        $res['state']          = $transferInfo['state'];
        $res['hire_date']      = date('Y-m-d', strtotime($user_info[$transferInfo['staff_id']]['hire_date']));

        $res['contract_company_id'] = $user_info[$transferInfo['staff_id']]['contract_company_id'];

        //转岗前部门、转岗前职位、转岗前所属大区、转岗前所属片区、转岗前所属网点、转岗前角色、转岗前直线上级
        $res['old_department']  = $department_info[$transferInfo['current_department_id']]['name'];
        $res['old_store_name']  = $store_info[$transferInfo['current_store_id']]['name'];
        $res['old_piece_name']  = $piece_info[$transferInfo['current_piece_id']] ?? '';
        $res['old_region_name'] = $region_info[$transferInfo['current_region_id']] ?? '';
        $res['old_job_title']   = $job_info[$transferInfo['current_position_id']]['job_name'];
        $res['old_leader']      = $transferInfo['current_manager_id'] ?
            $transferInfo['current_manager_id'].' '.$user_info[$transferInfo['current_manager_id']]['name']
            : '';
        $res['old_role']        = $this->getTransferRoles($transferInfo['current_role_id'], $rolesList);
        $res['old_role_ids']    = $transferInfo['current_role_id'];
        $res['old_position_id'] = $transferInfo['current_position_id'];

        //转岗后部门、转岗后职位、转岗后所属大区、转岗后所属片区、转岗后所属网点、转岗后角色、转岗后直线上级
        $res['new_department']    = $department_info[$transferInfo['after_department_id']]['name'];
        $res['new_store_name']    = $store_info[$transferInfo['after_store_id']]['name'];
        $res['new_piece_name']    = $piece_info[$transferInfo['after_piece_id']] ?? '';
        $res['new_region_name']   = $region_info[$transferInfo['after_region_id']] ?? '';
        $res['new_job_title']     = $job_info[$transferInfo['after_position_id']]['job_name'];
        $res['new_leader']        = $transferInfo['after_manager_id'] . ' ' . (isset($user_info[$transferInfo['after_manager_id']]) ? $user_info[$transferInfo['after_manager_id']]['name'] : '');
        $res['new_leader_id']     = $transferInfo['after_manager_id'];
        $res['new_role']          = $this->getTransferRoles($transferInfo['after_role_ids'], $rolesList);
        $res['new_role_ids']      = $transferInfo['after_role_ids'];
        $res['new_position_id']   = $transferInfo['after_position_id'];
        $res['new_department_id'] = $transferInfo['after_department_id'];
        $res['new_store_id']      = $transferInfo['after_store_id'];

        //期望转岗日期、实际转岗日期、转岗HCID、转岗原因、转岗原因备注、转岗状态
        $res['reason']            = $transferInfo['reason'];
        $res['transfer_reason']   = self::$t->_('job_transfer_reason.'.$transferInfo['transfer_reason']);
        $res['job_transfer_img']  = $imageList['JOB_TRANSFER'] ?? [];
        $res['job_transfer_confirm_img']  = $imageList['JOB_TRANSFER_CONFIRM'] ?? [];
        $res['after_date']        = date('Y-m-d', strtotime($transferInfo['after_date']));
        $res['actual_after_date'] = $transferInfo['actual_after_date'];
        $res['hc_id']             = $transferInfo['hc_id'];
        $res['transfer_state']    = $state[$transferInfo['state']] ?? '';
        //$res['after_base_salary'] = $extendInfo['salary_range_amount'] ?? '';

        //转岗前车辆来源、转岗后车辆来源、转岗前用车开始时间、转岗后用车开始时间
        if (get_country_code() == Enums\GlobalEnums::TH_COUNTRY_CODE) {
            $res['old_vehicle_source_id'] = $transferInfo['vehicle_source'];
            $res['new_vehicle_source_id'] = $transferInfo['car_owner'];
            if (in_array($transferInfo['current_position_id'], [JobTransferEnums::JOB_EV_TITLE_ID, JobTransferEnums::JOB_VAN_PROJECT_TITLE_ID])) {
                //$res['old_vehicle_source']    = $vehicleSource[$transferInfo['vehicle_source']] ?? '';
                $res['old_rental_car_time']   = $transferInfo['current_rental_car_created_at'] ? date('Y-m-d', strtotime($transferInfo['current_rental_car_created_at'])): '';
            }
            if (in_array($transferInfo['after_position_id'], [JobTransferEnums::JOB_EV_TITLE_ID, JobTransferEnums::JOB_VAN_PROJECT_TITLE_ID])) {
                //$res['new_vehicle_source']    = $vehicleSource[$transferInfo['car_owner']] ?? '';
                $res['new_rental_car_time']   = $transferInfo['rental_car_cteated_at'] ? date('Y-m-d', strtotime($transferInfo['rental_car_cteated_at'])): '';
            }
        }

        //if (get_country_code() == Enums\GlobalEnums::MY_COUNTRY_CODE) {
            //雇佣类型、雇佣期间
            $res['old_hire_type'] = $transferInfo['current_hire_type'];
            $res['old_hire_type_name'] = !empty($transferInfo['current_hire_type'])? self::$t->_('hire_type_' . $transferInfo['current_hire_type']): '';
            $res['new_hire_type'] = $transferInfo['after_hire_type'];
            $res['new_hire_type_name'] = !empty($transferInfo['after_hire_type'])? self::$t->_('hire_type_' . $transferInfo['after_hire_type']): '';
            $res['old_hire_times'] = $transferInfo['current_hire_times'];
            $res['new_hire_times'] = $transferInfo['after_hire_times'];
        //}

        //转岗前工作天数与轮休规则、转岗后工作天数与轮休规则
        $res['new_working_day_rest_type_id'] = $transferInfo['after_working_day_rest_type'];
        $res['old_working_day_rest_type']    = !empty($transferInfo['before_working_day_rest_type'])
            ? self::$t->_('working_day_rest_type_' . $transferInfo['before_working_day_rest_type'])
            : '';
        $res['new_working_day_rest_type']    = !empty($transferInfo['after_working_day_rest_type'])
            ? self::$t->_('working_day_rest_type_' . $transferInfo['after_working_day_rest_type'])
            : '';
        if (in_array($transferInfo['current_position_id'], [JobTransferEnums::JOB_EV_TITLE_ID, JobTransferEnums::JOB_VAN_PROJECT_TITLE_ID])) {
            $res['old_project_num'] = !empty($transferInfo['current_project_num'])
                ? self::$t->_($this->getProjectNumPrefixKeyByJobTitle($transferInfo['current_position_id']) . $transferInfo['current_project_num'])
                : '';
        }
        if (in_array($transferInfo['after_position_id'], [JobTransferEnums::JOB_EV_TITLE_ID, JobTransferEnums::JOB_VAN_PROJECT_TITLE_ID])) {
            $res['new_project_num'] = !empty($transferInfo['after_project_num'])
                ? self::$t->_($this->getProjectNumPrefixKeyByJobTitle($transferInfo['after_position_id']) . $transferInfo['after_project_num'])
                : '';
        }
        //车类型
//        if (in_array($transferInfo['after_car_type'], [JobTransferEnums::CAR_TYPE_VAN, JobTransferEnums::CAR_TYPE_BIKE])) {
//            $res['after_car_type'] = $transferInfo['after_car_type'] == JobTransferEnums::CAR_TYPE_VAN ? 'van': 'bike';
//        } else {
//            $res['after_car_type'] = '';
//        }
        if (get_country_code() == Enums\GlobalEnums::MY_COUNTRY_CODE) {

            if (in_array($transferInfo['after_position_id'], $this->getShowCarTypeJobTitle())) {
                $res['new_car_type'] = JobTransferEnums::VEHICLE_TYPE_CATEGORY_LIST[$transferInfo['after_car_type']] ?? '';
            }
            if (in_array($transferInfo['current_position_id'], $this->getShowCarTypeJobTitle())) {
                $res['old_car_type'] = JobTransferEnums::VEHICLE_TYPE_CATEGORY_LIST[$transferInfo['current_car_type']] ?? '';
            }
        }

        //转岗后是否为一线
        $res['after_type'] = $this->getTransferType($transferInfo['after_department_id'], $transferInfo['after_position_id']);

        $salaryType = SysService::getInstance()->getSalaryType();

        //薪资类型
        if (!empty($transferInfo['after_manager_id'])) {
            $res['salary_type_text'] = $salaryType[$transferInfo['salary_type']] ?? '';
        } else {
            $res['salary_type_text'] = '';
        }

        //获取可编辑字段
        $res['can_edit_field'] = $this->getCanEditFieldInfo($transferInfo);

        //pdf
        $res['confirmation_url'] = $transferInfo['confirmation_url'] ?? '';

        //获取配置
        $employeesJobsConfigs = (new SettingEnvModel())->listByCode(['monthly_special_contract_employees_job', 'daily_special_contract_employees_job']);
        $employeesJobsConfigs = array_column($employeesJobsConfigs, 'set_val', 'code');
        $monthlyEmployeesJobsConfig = !empty($employeesJobsConfigs['monthly_special_contract_employees_job'])
            ? explode(',', $employeesJobsConfigs['monthly_special_contract_employees_job'])
            : [];

        $dailyEmployeesJobsConfig = !empty($employeesJobsConfigs['daily_special_contract_employees_job'])
            ? explode(',', $employeesJobsConfigs['daily_special_contract_employees_job'])
            : [];
        $res['monthly_special_contract_jobs'] = $monthlyEmployeesJobsConfig;
        $res['daily_special_contract_jobs']   = $dailyEmployeesJobsConfig;
        $res['after_sys_department_id']       = SysDepartmentModel::getSpecLevelDepartmentInChain($transferInfo['after_department_id']);

        if ($transferInfo['type'] == JobTransferModel::TRANSFER_TYPE_SPECIAL) {
            $returnArr = $this->getSpecialAuditDetail($params, $user);
        } else {
            $returnArr = $this->getAuditDetail($params, $user);
        }
        return array_merge($res, $returnArr);
    }

    /**
     * 显示车类型职位
     * @return array
     */
    protected function getShowCarTypeJobTitle(): array
    {
        return [
            JobTransferEnums::JOB_VAN_TITLE_ID,
            JobTransferEnums::JOB_CAR_TITLE_ID,
        ];
    }

    /**
     * 获取转岗图片
     * @param $id
     * @return array
     */
    public function getJobTransferImages($id): array
    {
        $images = SysAttachmentModel::find([
            'conditions' => "oss_bucket_key = :oss_bucket_key: and oss_bucket_type in({oss_bucket_type:array}) and deleted = 0",
            'bind'       => [
                'oss_bucket_key' => $id,
                'oss_bucket_type' => ['JOB_TRANSFER', 'JOB_TRANSFER_CONFIRM'],
            ],
            'columns'    => 'bucket_name,object_key,original_name,oss_bucket_type',
        ])->toArray();
        if (empty($images)) {
            return [];
        }
        $imageArr = [];
        foreach ($images as $image) {
            $imageArr[] = [
                'url'             => $this->convertImgUrl($image['bucket_name'], $image['object_key']),
                'file_name'       => $image['original_name'] ?? '',
                'name'            => $image['original_name'] ?? '',
                'oss_bucket_type' => $image['oss_bucket_type'],
            ];
        }
        return $imageArr;
    }


    public function convertImgUrl($bucket_name = '', $object_key = '')
    {
        $server_point = SettingEnvModel::findFirst("code = 'server_point'");
        if ($server_point) {
            return 'https://' . $bucket_name . $server_point->set_val . $object_key;
        }
        return 'https://' . $bucket_name . '.oss-ap-southeast-1.aliyuncs.com/' . $object_key;
    }


    /**
     * @description 查询转岗人信息
     * @param $params
     * @param $submitterId
     * @return array
     * @throws ValidationException
     */
    public function searchStaffDetail($params, $submitterId): array
    {
        if ($this->checkStaffInfo($params, $submitterId)) {
            return $this->getStaffDetailInfo($params);
        }
        return [];
    }

    /**
     * @description
     * @param $params
     * @param $submitterId
     * @return mixed
     */
    public function checkStaffInfo($params, $submitterId)
    {
        $ac = new ApiClient('by', '', 'check_apply', static::$language);
        $ac->setParams([
            [
                'submitter_id' => $submitterId,
                'staff_id'     => $params['staff_id'],
            ],
        ]);
        $res = $ac->execute();

        if (!empty($res['result']) && $res['result']['reason']) {
            throw new ValidationException($res['result']['reason']);
        }
        return true;
    }

    /**
     * 查询转岗人信息
     * @param $params
     * @return array
     * @throws ValidationException
     */
    public function getStaffDetailInfo($params): array
    {
        $staffId = $params['staff_id'] ?? 0;

        $staffInfo = HrStaffInfoModel::findFirst([
            "conditions" => "staff_info_id = :staff_info_id:",
            "columns"    => "staff_info_id as staff_id, name, node_department_id, sys_store_id, job_title, rest_type,
                week_working_day,job_title_grade_v2,hire_type, hire_times",
            "bind"       => ["staff_info_id" => $staffId],
        ]);
        if (empty($staffInfo)) {
            throw new ValidationException("can not find staff id: $staffId");
        }

        //部门
        $departmentInfo = SysDepartmentModel::findFirst([
            "conditions" => "id = :department_id:",
            "columns" => "id,name,company_id",
            "bind" => [
                "department_id" => $staffInfo->node_department_id,
            ],
        ]);
        //职位
        $positionInfo = HrJobTitleModel::findFirst([
            "conditions" => "id = :job_title:",
            "columns" => "id,job_name",
            "bind" => [
                "job_title" => $staffInfo->job_title,
            ],
        ]);
        //网点
        $storeInfo = SysStoreModel::findFirst([
            "conditions" => "id = :store_id:",
            "columns" => "id,name",
            "bind" => [
                "store_id" => $staffInfo->sys_store_id,
            ],
        ]);
        //角色
        $staffRoles = HrStaffInfoPositionModel::find([
            "conditions" => "staff_info_id = :staff_info_id:",
            "columns" => "position_category",
            "bind" => [
                "staff_info_id" => $staffId,
            ],
        ])->toArray();
        $staffRoles = array_column($staffRoles, 'position_category');
        $staffInfo->current_role_id = empty($staffRoles) ? '': implode(',', $staffRoles);

        $bind = [
            'deleted'        => HrOrganizationDepartmentStoreRelationModel::IS_DELETED_NO,
            'state'          => HrOrganizationDepartmentStoreRelationModel::STATE_YES,
            'level_state'    => HrOrganizationDepartmentStoreRelationModel::LEAVE_STATE_YES,
            'store_id'       => $staffInfo->sys_store_id,
        ];
        $storeRelateInfo =  HrOrganizationDepartmentStoreRelationModel::findFirst([
            'conditions' => 'is_deleted = :deleted: and state = :state: and level_state = :level_state: and store_id = :store_id:',
            'bind'       => $bind,
        ]);

        if (!empty($storeRelateInfo->piece_id)) {
            $pieceInfo = SysManagePieceModel::findFirst($storeRelateInfo->piece_id);
        }

        if (!empty($storeRelateInfo->region_id)) {
            $regionInfo = SysManageRegionModel::findFirst($storeRelateInfo->region_id);
        }

        $staffInfo->current_department_name = $departmentInfo->name ?? '';
        $staffInfo->current_position_name   = $positionInfo->job_name ?? '';
        $staffInfo->current_store_name      = $storeInfo->name ?? ($staffInfo->sys_store_id == Enums::HEAD_OFFICE_STORE_FLAG
            ? Enums::PAYMENT_HEADER_STORE_NAME : '');
        $staffInfo->piece_id                = $storeRelateInfo->piece_id ?? 0;
        $staffInfo->region_id               = $storeRelateInfo->region_id ?? 0;
        $staffInfo->piece_name              = $pieceInfo->name ?? '';
        $staffInfo->region_name             = $regionInfo->name ?? '';
        $staffInfo->company_id              = $departmentInfo->company_id ?? 0;
        $staffInfo->before_working_day_rest_type = $staffInfo->week_working_day . $staffInfo->rest_type;
        $staffInfo->current_job_title_grade = $staffInfo->job_title_grade_v2;
        $staffInfo->hire_type_title         = !empty($staffInfo->hire_type)? self::$t->_('hire_type_' . $staffInfo->hire_type): '';

        //转岗前职位为Van Courier、Bike Courier、Van Feeder时，要显示车辆来源及用车开始时间
        //获取快递职位
        $vehicleJobTitle = (new SettingEnvModel())->getSetVal('job_title_vehicle_type', ',');
        if (get_country_code() == Enums\GlobalEnums::TH_COUNTRY_CODE && in_array($staffInfo->job_title, $vehicleJobTitle)) {

            $info = VehicleInfoModel::findFirst([
                'conditions' => 'uid = :uid:',
                'bind'       => [
                    'uid'             => $staffId,
                ],
            ]);
            $staffInfo->vehicle_source = '';
            $staffInfo->vehicle_start_date = '';
            if (!empty($info)) {
                $vehicleSource = SysService::getInstance()->getVehicleSource();
                $staffInfo->vehicle_source_code = $info->vehicle_source;
                $staffInfo->vehicle_source = $vehicleSource[$info->vehicle_source] ?? '';
                if ($info->vehicle_source == Enums\VehicleInfoEnums::VEHICLE_SOURCE_RENTAL_CODE) {
                    $staffInfo->vehicle_start_date = $info->vehicle_start_date;
                }
                if (in_array($staffInfo->job_title, [JobTransferEnums::JOB_EV_TITLE_ID, JobTransferEnums::JOB_VAN_PROJECT_TITLE_ID])) {
                    $staffInfo->project_num = $info->project_num;
                }
            }
            $staffInfo->project_num_title = isset($staffInfo->project_num) && $staffInfo->project_num
                ? static::$t->_($this->getProjectNumPrefixKeyByJobTitle($staffInfo->job_title) . $staffInfo->project_num)
                : '';
        }

        //如果被转岗人是Van Courier、Car Courier
        //则获取车类型
        if (get_country_code() == Enums\GlobalEnums::MY_COUNTRY_CODE && in_array($staffInfo->job_title, $this->getShowCarTypeJobTitle())) {
            $vehicleInfo = VehicleInfoModel::findFirst([
                'conditions' => 'uid = :uid:',
                'bind'       => [
                    'uid' => $staffId,
                ],
            ]);
            if (!empty($vehicleInfo)) {
                $vehicleTypeCategory = VehicleInfoEnums::VEHICLE_TYPE_CATEGORY_LIST[(int)$vehicleInfo->vehicle_type_category] ?? '';
            }
            $staffInfo->current_vehicle_type_category = $vehicleTypeCategory ?? '';
        }
        return $staffInfo->toArray();
    }

    /**
     * @description
     * @param $roleIds
     * @param array $rolesList
     * @return array|string
     */
    public function getTransferRoles($roleIds, array $rolesList = [])
    {
        if (!is_string($roleIds) || strlen($roleIds) == 0) {
            return '';
        }
        $roleName    = [];
        $roleIdsList = explode(',', $roleIds);
        foreach ($roleIdsList as $role) {
            if (in_array(self::$language, ['zh-CN', 'zh'])) {
                $roleName[] = $rolesList[$role]['role_name_zh'] ?? '';
            } elseif (self::$language == 'th') {
                $roleName[] = $rolesList[$role]['role_name_th'] ?? '';
            } else {
                $roleName[] = $rolesList[$role]['role_name_en'] ?? '';
            }
        }
        return join(',', $roleName);
    }

    /**
     * 获取员工信息
     * @param $staff_info_id
     * @return array
     */
    public function getStaffInfo($staff_info_id): array
    {
        $StaffInfo = HrStaffInfoModel::findFirst([
            "conditions" => "staff_info_id = :staff_info_id: and state = 1 and formal in (1,4) and is_sub_staff = 0",
            "columns"    => "state,sys_store_id,job_title,sys_department_id,node_department_id,staff_info_id",
            "bind"       => [
                "staff_info_id" => $staff_info_id,
            ],
        ]);
        return isset($StaffInfo) && $StaffInfo ? $StaffInfo->toArray() : [];
    }

    /**
     * @description 获取指定工号待审批或待转岗的数据
     * @param $staff_info_id
     * @return array
     */
    public function getJobTransferData($staff_info_id): array
    {
        //job_transfer表中拿到数据相关员工的转岗状态
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('staff_id,id,after_department_id,after_store_id,after_position_id,approval_state,state');
        $builder->from(['r' => JobTransferModel::class]);
        $builder->where('deleted = 0');
        $builder->andWhere('staff_id = :staff_info_id:', ['staff_info_id' => $staff_info_id]);
        $builder->inWhere('approval_state', [
            Enums\ByWorkflowEnums::BY_OPERATE_WAIT_AUDIT,
            Enums\ByWorkflowEnums::BY_OPERATE_PASS,
        ]);
        $builder->andWhere('state = :state:', ['state' => JobTransferModel::JOB_TRANSFER_STATE_TO_BE_TRANSFERRED]);
        $info = $builder->getQuery()->getSingleResult();
        if (empty($info)) {
            return [];
        }
        return $info->toArray();
    }

    /**
     * @description 获取上级
     * @return array
     * @throws Exception
     */
    public function getAfterManagerInfo($params, $submitterId)
    {
        $auditId = $params['id'];
        $jobTransferInfo = $this->getJobTransferInfo($auditId);

        $ac = new ApiClient('by', '', 'getAfterManagerId', static::$language);
        $ac->setParams(
            [
                [
                    'after_store_id'      => $jobTransferInfo->after_store_id,
                    'after_department_id' => $jobTransferInfo->after_department_id,
                    'after_job_title_id'  => $jobTransferInfo->after_position_id,
                    'after_date'          => $jobTransferInfo->after_date,
                    'staff_id'            => $jobTransferInfo->staff_id,
                ],
            ]
        );
        $res = $ac->execute();

        if (empty($res['result'])) {
            return [];
        }
        return (new HrStaffRepository())->getStaffInfo($res['result']);
    }

    public function searchStaff($condition, $submitter_id)
    {
        return HrStaffInfoModel::find([
            'conditions' => '(staff_info_id like :name: or name like :name:) and state = 1 and formal = 1 and is_sub_staff = 0',
            'columns'    => 'staff_info_id,name',
            'bind'       => ['name' => $condition['search_condition'] . '%'],
            'limit'      => 10,
            'order'      => 'staff_info_id asc',
        ])->toArray();
    }

    /**
     * @description 获取可编辑字段
     * @param array $params
     */
    private function getCanEditFieldInfo($params = [])
    {
        //审批状态为待审批 && 并且查看我的待审批 && 存在可编辑字段时，
        //返回当前节点的可编辑字段
        if ($params['approval_state'] == ByWorkflowEnums::BY_OPERATE_WAIT_AUDIT &&
            $this->isExistCanEditField($params['id'], ByWorkflowModel::APPROVAL_TYPE_JT)
        ) {
            //获取可编辑字段
            $canEditField = $this->getCanEditFieldColumns($params['id'], ByWorkflowModel::APPROVAL_TYPE_JT,
                AuditDetailOperationsEnums::RESPONSE_STRUCTURE_COLUMN_NAME);


            //过滤可编辑字段
            $filterColumns = $this->getValidateFilterColumns($params);

            $canEditField = array_values(array_filter($canEditField, function ($v) use ($filterColumns) {
                return !in_array($v, $filterColumns);
            }));
        }

        return $canEditField ?? [];
    }

    /**
     * @description 转岗类型 1=一线转岗 2=非一线
     * @param $department_id
     * @param $job_title_id
     * @return int
     */
    public function getTransferType($department_id , $job_title_id): int
    {
        $positionConfig = SysService::getInstance()->getFrontLineConfig();

        //一线职位
        if (!empty($positionConfig) && in_array($job_title_id, $positionConfig)
        ) {
            return JobTransferModel::TRANSFER_TYPE_FRONT_LINE;
        }
        return JobTransferModel::TRANSFER_TYPE_NOT_FRONT_LINE;
    }

    /**
     * @description 是否存在可编辑字段
     * @param $audit_id
     * @param $audit_type
     * @return bool
     */
    private function isExistCanEditField($audit_id, $audit_type): bool
    {
        $nodeInfo = $this->getCurrentNodeByAuditId($audit_id, $audit_type);
        if (empty($nodeInfo) || empty($nodeInfo['can_edit_field'])) {
            return false;
        }
        return true;
    }

    /**
     * @description 获取当前审批节点信息
     * @param $audit_id
     * @param $audit_type
     * @return array
     */
    private function getCurrentNodeByAuditId($audit_id, $audit_type): array
    {
        $applyInfo = AuditApplyModel::findFirst([
            'conditions' => 'biz_type = :audit_type: and biz_value = :audit_value:',
            'bind' => [
                'audit_type' => $audit_type,
                'audit_value'=> $audit_id,
            ],
        ]);
        if (empty($applyInfo)) {
            return [];
        }
        $nodeInfo = ByWorkflowNodeModel::findFirst([
            'conditions' => 'id = :node_id:',
            'bind' => [
                'node_id'  => $applyInfo->current_flow_node_id,
            ],
        ]);
        return !empty($nodeInfo) ? $nodeInfo->toArray(): [];
    }

    /**
     * @description 获取当前审批人的可编辑字段
     * @param $audit_id
     * @param $audit_type
     * @param array $filter_columns
     * @return array
     */
    private function getCanEditField($audit_id, $audit_type, array $filter_columns = []): array
    {
        $nodeInfo = $this->getCurrentNodeByAuditId($audit_id, $audit_type);
        $canEditField = !empty($nodeInfo['can_edit_field']) ? $nodeInfo['can_edit_field'] : '';

        if (empty($canEditField)) {
            return [];
        }
        $canEditFieldData = (array)json_decode($canEditField, true);
        if (empty($canEditFieldData)) {
            return [];
        }
        $canEditFieldColumns = $canEditFieldData['basic'];
        $canEditFieldColumns = array_values(array_diff($canEditFieldColumns, $filter_columns));

        return WorkflowFormModel::find([
            'conditions' => 'column in({column_item:array}) and flow_id = :audit_type:',
            'bind' => [
                'column_item' => $canEditFieldColumns,
                'audit_type'  => $audit_type,
            ],
        ])->toArray();
    }

    /**
     * @description 获取可编辑字段
     * @param $audit_id
     * @param $audit_type
     * @param int $response_type
     * @return array
     */
    public function getCanEditFieldColumns($audit_id, $audit_type, int $response_type = AuditDetailOperationsEnums::RESPONSE_STRUCTURE_COLUMN_ID): array
    {
        $canEditField = $this->getCanEditField($audit_id, $audit_type);

        if ($response_type == AuditDetailOperationsEnums::RESPONSE_STRUCTURE_COLUMN_ID) {
            return array_column($canEditField, 'relate_button_id');
        } else {
            return array_column($canEditField, 'column');
        }
    }

    public function getValidateFilterColumns($transfer_info): array
    {
        switch (get_country_code()) {
            case Enums\GlobalEnums::TH_COUNTRY_CODE:
                $filterColumns = $this->getThValidateFilterColumns($transfer_info);
                break;
            case Enums\GlobalEnums::MY_COUNTRY_CODE:
                $filterColumns = $this->getMyValidateFilterColumns($transfer_info);
                break;
            case Enums\GlobalEnums::PH_COUNTRY_CODE:
                $filterColumns = $this->getPhValidateFilterColumns($transfer_info);
                break;
            default:
                $filterColumns = [];
        }
        return $filterColumns;
    }

    private function getMyValidateFilterColumns($transfer_info): array
    {
        $filterColumns[] = 'after_vehicle_source';
        $filterColumns[] = 'after_rental_car_created_at';

        //如果转岗后职位未非显示车辆来源的职位，则不显示车辆来源
        if (!in_array($transfer_info['after_position_id'], $this->getShowCarTypeJobTitle())) {
            $filterColumns[] = 'car_type';
        }

        if ($this->getTransferType($transfer_info['after_department_id'], $transfer_info['after_position_id']) == JobTransferModel::TRANSFER_TYPE_NOT_FRONT_LINE) {
            $filterColumns[] = 'salary_type';
        }
        return $filterColumns;
    }

    private function getThValidateFilterColumns($transfer_info): array
    {
        $filterColumns[] = 'car_type';

        //获取快递职位
        $vehicleJobTitle = (new SettingEnvModel())->getSetVal('job_title_vehicle_type', ',');

        //如果转岗后职位未非显示车辆来源的职位，则不显示车辆来源
        if (in_array($transfer_info['after_position_id'], [JobTransferEnums::JOB_EV_TITLE_ID, JobTransferEnums::JOB_VAN_PROJECT_TITLE_ID])) {
            $filterColumns[] = 'after_vehicle_source';

            if (!empty($transfer_info['rental_car_created_at'])) {
                $filterColumns[] = 'after_rental_car_created_at';
            }
        } if (!in_array($transfer_info['after_position_id'], $vehicleJobTitle)) {
            $filterColumns[] = 'after_vehicle_source';

            if (!isset($transfer_info['after_vehicle_source']) || $transfer_info['after_vehicle_source'] != VehicleInfoEnums::VEHICLE_SOURCE_RENTAL_CODE) {
                $filterColumns[] = 'after_rental_car_created_at';
            }
        }

        if ($this->getTransferType($transfer_info['after_department_id'], $transfer_info['after_position_id']) == JobTransferModel::TRANSFER_TYPE_NOT_FRONT_LINE) {
            $filterColumns[] = 'salary_type';
        }

        if (!in_array($transfer_info['after_position_id'], [JobTransferEnums::JOB_EV_TITLE_ID, JobTransferEnums::JOB_VAN_PROJECT_TITLE_ID])) {
            $filterColumns[] = 'project_num';
        }
        return $filterColumns;
    }


    public function getProjectNumPrefixKeyByJobTitle($job_title_id): string
    {
        if ($job_title_id == JobTransferEnums::JOB_EV_TITLE_ID) {
            return 'project_num_';
        } else {
            return 'van_courier_project_num_';
        }
    }

    private function getPhValidateFilterColumns($transfer_info)
    {
        $filterColumns[] = 'car_type';

        //获取快递职位
        $vehicleJobTitle = (new SettingEnvModel())->getSetVal('job_title_vehicle_type', ',');

        //如果转岗后职位未非显示车辆来源的职位，则不显示车辆来源
        if ($transfer_info['after_position_id'] == JobTransferEnums::JOB_EV_TITLE_ID) {
            $filterColumns[] = 'after_vehicle_source';

            if (!empty($transfer_info['rental_car_created_at'])) {
                $filterColumns[] = 'after_rental_car_created_at';
            }
        }
        if (!in_array($transfer_info['after_position_id'], $vehicleJobTitle)) {
            $filterColumns[] = 'after_vehicle_source';

            if (!isset($transfer_info['after_vehicle_source']) || $transfer_info['after_vehicle_source'] != VehicleInfoEnums::VEHICLE_SOURCE_RENTAL_CODE) {
                $filterColumns[] = 'after_rental_car_created_at';
            }
        }

        if ($this->getTransferType($transfer_info['after_department_id'],
                $transfer_info['after_position_id']) == JobTransferModel::TRANSFER_TYPE_NOT_FRONT_LINE) {
            $filterColumns[] = 'salary_type';
        }
        return $filterColumns;
    }
}
