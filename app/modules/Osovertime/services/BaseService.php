<?php

namespace App\Modules\Osovertime\Services;


class BaseService extends \App\Library\BaseService
{
    /**
     * 过滤空值 和 非必要参数
     * @param array $params
     * @param array $not_must
     * @return mixed
     */
    public static function handleParams(array $params, array $not_must)
    {
        $params = array_filter($params);

        if (empty($not_must) || !is_array($not_must)) {
            return $params;
        }

        foreach ($not_must as $value) {
            if (isset($params[$value])) {
                unset($params[$value]);
            }
        }

        return $params;
    }


}
