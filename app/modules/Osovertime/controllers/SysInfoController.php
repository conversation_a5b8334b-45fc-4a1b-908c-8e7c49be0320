<?php

namespace App\Modules\Osovertime\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Modules\Osovertime\Services\SysService;
use Exception;

/**
 * 人才盘点 - 公共资源部分
 * Class SysInfoController
 * @package App\Modules\TalentReview\Controllers
 */
class SysInfoController extends BaseController
{

    /**
     * 网点搜索
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function searchStoreAction() {
        $params = $this->request->get();
        try {

            $result = (new SysService())->searchStore($params);
            return $this->returnJson(ErrCode::$SUCCESS, 'success', $result);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error(['params' => $params, 'error' => $e->getMessage(),'file' => $e->getFile(), 'line' => $e->getLine()]);
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, 'error');
        }
    }

    /**
     * @api https://yapi.flashexpress.pub/project/133/interface/api/cat_13369
     * 列表页搜索枚举下拉
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Exception\BusinessException
     */
    public function approvalEnumAction(){
        $return         = (new SysService())->selectEnum();
        return $this->returnJson(ErrCode::$SUCCESS, '', $return);

    }



}