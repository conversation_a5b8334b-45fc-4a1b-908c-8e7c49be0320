<?php

namespace App\Modules\AccessData\Services;

use App\Library\Enums;
use App\Library\AccessDataEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Modules\AccessData\Models\AccessDataBusinessLineModel;
use App\Modules\AccessData\Models\AccessDataDepartmentModel;
use App\Modules\AccessData\Models\AccessDataModel;
use App\Modules\AccessData\Models\AccessDataStaffInfoModel;
use App\Modules\User\Models\AttachModel;
use Exception;
use Phalcon\Mvc\Model\Transaction\Failed as TxFailed;
use App\Modules\User\Models\HrStaffInfoModel;

class AccessDataUpdateService extends BaseService
{
    // 员工管理操作类型
    const STAFF_INFO_TYPE_ADD = 1;
    const STAFF_INFO_TYPE_EDIT = 2;

    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return AccessDataUpdateService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 申请处理 - 数据上传
     *
     * @param array $data
     * @param array $user
     * @return array
     */
    public function uploadDataFile(array $data, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $logger = $this->getDI()->get('logger');

        $db = $this->getDI()->get('db_oa');
        $db->begin();

        try {
            $main_model = AccessDataModel::findFirst([
                'id = :id:',
                'bind' => ['id' => $data['id']],
            ]);

            if (empty($main_model)) {
                throw new BusinessException('取数工单信息获取失败, id = ' . $data['id'],
                    ErrCode::$ACCESS_DATA_SYS_GET_INFO_ERROR);
            }

            $logger->info('取数系统 - 处理操作 - 主表更新前数据: ' . json_encode($main_model->toArray(), JSON_UNESCAPED_UNICODE));

            // 审批未通过, 不可上传
            if ($main_model->audit_status != AccessDataEnums::AUDIT_APPROVE_STATUS) {
                throw new ValidationException(self::$t['access_data_not_approve_cant_upload'],
                    ErrCode::$ACCESS_DATA_SYS_APPROVAL_STATUS_ERROR);
            }

            // 已上传+待复核, 不可上传
            if ($main_model->data_upload_status == AccessDataEnums::DATA_UPLOADED_STATUS && $main_model->review_status == AccessDataEnums::DATA_NOT_REVIEWED_STATUS) {
                throw new ValidationException(self::$t['access_data_waiting_reviewed_cant_upload'],
                    ErrCode::$ACCESS_DATA_SYS_UPLOAD_DATA_FAIL_WAITING_REVIEW_ERROR);
            }

            // 复核通过: 非数据上传者 则 不可重新上传
            if ($main_model->review_status == AccessDataEnums::DATA_PASSED_REVIEW_STATUS && $user['id'] != $main_model->data_uploader_id) {
                throw new ValidationException(self::$t['access_data_reviewed_cant_upload'],
                    ErrCode::$ACCESS_DATA_SYS_UPLOAD_DATA_FAIL_REVIEWED_ERROR);
            }

            // 附件处理
            $oss_bucket_type_item = [
                Enums::OSS_BUCKET_TYPE_ACCESS_DATA_SYS_DATA_FILE,
                Enums::OSS_BUCKET_TYPE_ACCESS_DATA_SYS_SQL_FILE,
            ];

            // [1] 已有的删除
            $attach_model = AttachModel::find([
                'conditions' => 'oss_bucket_type IN ({oss_bucket_type:array}) AND oss_bucket_key = :oss_bucket_key:',
                'bind'       => ['oss_bucket_type' => $oss_bucket_type_item, 'oss_bucket_key' => $main_model->id],
            ]);
            if (!empty($attach_model)) {
                foreach ($attach_model as $attach_sub_model) {
                    if ($attach_sub_model->delete() === false) {
                        $del_err_message = '';
                        foreach ($attach_sub_model->getMessages() as $del_err_message) {
                            $del_err_message .= $del_err_message . PHP_EOL;
                        }

                        throw new BusinessException('取数工单系统，数据上传已有附件删除失败, attach_id = ' . $attach_sub_model->id . 'err: ' . $del_err_message,
                            ErrCode::$ACCESS_DATA_SYS_ATTACH_UPDATE_FAIL_ERROR);
                    }
                }
            }

            // [2] 新的上传
            $data['data_file']['oss_bucket_type'] = Enums::OSS_BUCKET_TYPE_ACCESS_DATA_SYS_DATA_FILE;
            $data['sql_file']['oss_bucket_type']  = Enums::OSS_BUCKET_TYPE_ACCESS_DATA_SYS_SQL_FILE;

            $event_file = [
                $data['data_file'],
                $data['sql_file'],
            ];

            $event_file_item = [];
            foreach ($event_file as $k => $file) {
                $event_file_item[] = [
                    'oss_bucket_type' => $file['oss_bucket_type'],
                    'oss_bucket_key'  => $main_model->id,
                    'bucket_name'     => $file['bucket_name'],
                    'object_key'      => $file['object_key'],
                    'file_name'       => $file['file_name'],
                    'sub_type'        => 0,
                ];
            }

            $logger->info('取数系统 - 处理操作 - 上传的数据文件: ' . json_encode($event_file_item, JSON_UNESCAPED_UNICODE));

            if (empty($event_file_item)) {
                throw new ValidationException(self::$t['access_data_upload_error_repeat_upload'],
                    ErrCode::$ACCESS_DATA_SYS_UPLOAD_DATA_TYPE_ERROR);
            }

            $attach_bool = (new AttachModel())->batchInsert($event_file_item);
            if ($attach_bool === false) {
                throw new BusinessException('取数工单系统 - 处理[数据文件上传] - 附件添加失败',
                    ErrCode::$ACCESS_DATA_SYS_UPLOAD_DATA_FAIL_ERROR);
            }

            // [3.3] 主表更新
            $main_data = [
                'data_uploader_id'   => $user['id'],
                'data_upload_status' => AccessDataEnums::DATA_UPLOADED_STATUS,
                'data_upload_time'   => date('Y-m-d H:i:s'),
                'review_status'      => 0,//复核状态置为: 待复核
                'update_time'        => date('Y-m-d H:i:s'),
                'updater_id'         => $user['id'],
            ];

            if (!$main_model->indicator_theme && $data['indicator_theme']) {
                $main_data['indicator_theme'] = $data['indicator_theme'];
            }

            $bool = $main_model->i_update($main_data);
            if ($bool === false) {
                throw new BusinessException('处理[数据上传], 更新工单主表失败', ErrCode::$ACCESS_DATA_WORK_ORDER_UPDATE_ERROR);
            }

            $logger->info('取数系统 - 处理操作 - 主表更新后数据: ' . json_encode($main_model->toArray(), JSON_UNESCAPED_UNICODE));

            $db->commit();
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            //业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (TxFailed $e) {
            //数据库错误，不可对外抛出
            $code         = ErrCode::$MYSQL_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (Exception $e) {
            //系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $logger->warning('取数工单处理[上传数据]异常: ' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => [],
        ];
    }

    /**
     * 申请处理 - 复核
     *
     * @param array $data
     * @param array $user
     * @return array
     */
    public function dataReview(array $data, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $logger = $this->getDI()->get('logger');

        $db = $this->getDI()->get('db_oa');
        $db->begin();

        try {
            $main_model = AccessDataModel::findFirst([
                'id = :id:',
                'bind' => ['id' => $data['id']],
            ]);

            if (empty($main_model)) {
                throw new BusinessException('取数工单信息获取失败, id=' . $data['id'], ErrCode::$ACCESS_DATA_SYS_GET_INFO_ERROR);
            }

            $logger->info('取数系统 - 复核操作 - 主表更新前数据: ' . json_encode($main_model->toArray(), JSON_UNESCAPED_UNICODE));

            // 审批驳回/审批中, 不可复核
            if ($main_model->audit_status != AccessDataEnums::AUDIT_APPROVE_STATUS) {
                throw new ValidationException(self::$t['access_data_not_approve_cant_review'],
                    ErrCode::$ACCESS_DATA_SYS_APPROVAL_STATUS_ERROR);
            }

            // 数据未上传, 不可复核
            if ($main_model->data_upload_status == AccessDataEnums::DATA_NOT_UPLOADED_STATUS) {
                throw new ValidationException(self::$t['access_data_upload_null_cant_review'],
                    ErrCode::$ACCESS_DATA_SYS_DATA_NOT_UPLOADED_REVIEW_ERROR);
            }

            // 已复核, 不可再次复核
            if ($main_model->review_status == AccessDataEnums::DATA_PASSED_REVIEW_STATUS) {
                throw new ValidationException(self::$t['access_data_reviewed_cant_repeat_review'],
                    ErrCode::$ACCESS_DATA_SYS_AGAIN_REVIEWED_ERROR);
            }

            // 主表更新
            $main_data = [
                'reviewer_id'            => $user['id'],
                'review_status'          => $data['review_status'],
                'review_time'            => date('Y-m-d H:i:s'),
                'review_rejected_reason' => $data['review_rejected_reason'] ?? '',
                'update_time'            => date('Y-m-d H:i:s'),
                'updater_id'             => $user['id'],
            ];

            if ($data['review_status'] == AccessDataEnums::DATA_PASSED_REVIEW_STATUS) {
                $main_data['review_rejected_reason'] = '';
                $main_data['delivery_time']          = date('Y-m-d H:i:s');
            }

            $bool = $main_model->i_update($main_data);
            if ($bool === false) {
                throw new BusinessException('处理[复核操作], 更新取数工单主表失败', ErrCode::$ACCESS_DATA_WORK_ORDER_UPDATE_ERROR);
            }

            $logger->info('取数系统 - 复核操作 - 主表更新后数据: ' . json_encode($main_model->toArray(), JSON_UNESCAPED_UNICODE));

            $db->commit();
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            //业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (TxFailed $e) {
            //数据库错误，不可对外抛出
            $code         = ErrCode::$MYSQL_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (Exception $e) {
            //系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $logger->warning('取数工单处理[复核操作]异常: ' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => [],
        ];
    }

    /**
     * 员工信息添加/更新
     *
     * @param array $params
     * @param array $user
     * @param int $scenes_type
     * @return mixed
     */
    public function saveStaffInfo(array $params, array $user, $scenes_type = 1)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $logger  = $this->getDI()->get('logger');
        try {
            // 0. 验证员工在HR-IS系统中是否存在
            $hr_is_staff_info = HrStaffInfoModel::findFirst([
                'conditions' => 'staff_info_id = :staff_info_id:',
                'bind'       => ['staff_info_id' => $params['staff_id']],
            ]);
            if (empty($hr_is_staff_info)) {
                throw new ValidationException(self::$t['access_data_staff_not_exist_hr_is_sys'] . "[{$params['staff_id']}]",
                    ErrCode::$VALIDATE_ERROR);
            }

            // 1. 获取员工信息
            $staff_model = AccessDataStaffInfoModel::findFirst([
                'conditions' => 'staff_id = :staff_id:',
                'bind'       => ['staff_id' => $params['staff_id']],
            ]);
            if (!empty($staff_model) && $scenes_type == self::STAFF_INFO_TYPE_ADD) {
                throw new ValidationException(self::$t['access_data_staff_exist'] . "[{$params['staff_id']}]",
                    ErrCode::$VALIDATE_ERROR);
            }

            if (empty($staff_model)) {
                $staff_model = new AccessDataStaffInfoModel();
            } else {
                $logger->info('取数系统 - 权限管理 - 员工修改前数据: ' . json_encode($staff_model->toArray(), JSON_UNESCAPED_UNICODE));
            }

            // 2. 部门校验
            $dept_model = AccessDataDepartmentModel::findFirst([
                'conditions' => 'id = :id: AND is_del = 0',
                'bind'       => ['id' => $params['department_id']],
            ]);
            if (empty($dept_model)) {
                throw new ValidationException(self::$t['access_data_staff_department_null'] . "[{$params['department_id']}]",
                    ErrCode::$VALIDATE_ERROR);
            }

            // 需求部门, 业务线必填项
            if ($dept_model->category == AccessDataEnums::WORK_ORDER_AUDIT_STAGE_001 && empty($params['business_line_permission'])) {
                throw new ValidationException(self::$t['access_data_staff_business_line_null'],
                    ErrCode::$VALIDATE_ERROR);
            }

            // 非需求部门, 清空业务线
            if ($dept_model->category != AccessDataEnums::WORK_ORDER_AUDIT_STAGE_001 && !empty($params['business_line_permission'])) {
                throw new ValidationException(self::$t['access_data_staff_not_demand_department'],
                    ErrCode::$VALIDATE_ERROR);
            }

            // 3. 业务线权限提取
            $business_line_permission = [];
            if (is_array($params['business_line_permission'])) {
                $business_line_item = AccessDataBusinessLineModel::find([
                    'columns' => ['id', 'name', 'parent_id'],
                ])->toArray();
                $business_line_item = $business_line_item ? array_column($business_line_item, null, 'id') : [];
                foreach ($params['business_line_permission'] as $line_id) {
                    $line_info = $business_line_item[$line_id] ?? [];
                    if (!empty($line_info) && $line_info['parent_id']) {
                        $business_line_permission[] = $line_id;

                        if (!in_array($line_info['parent_id'], $business_line_permission)) {
                            $business_line_permission[] = $line_info['parent_id'];
                        }
                    }
                }
                $business_line_permission = array_unique($business_line_permission);
            }

            $business_line_permission = $business_line_permission ? implode(',', $business_line_permission) : '';

            // 4. 入库
            $staff_model->staff_id                 = $params['staff_id'];
            $staff_model->staff_name               = $params['staff_name'];
            $staff_model->department_id            = $params['department_id'];
            $staff_model->duty_flag                = $params['duty_flag'];
            $staff_model->business_line_permission = $business_line_permission;
            $staff_model->updated_id               = $user['id'] ?? 0;
            $staff_model->update_time              = gmdate('Y-m-d H:i:s');
            if ($scenes_type == self::STAFF_INFO_TYPE_ADD) {
                $staff_model->created_id = $user['id'] ?? 0;
            }

            if ($staff_model->save() === false) {
                throw new ValidationException(self::$t['access_data_staff_save_error'], ErrCode::$VALIDATE_ERROR);
            }

            $logger->info('取数系统 - 权限管理 - 员工已入库数据: ' . json_encode($staff_model->toArray(), JSON_UNESCAPED_UNICODE));
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            //业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (Exception $e) {
            //系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }


        if (!empty($real_message)) {
            $logger->warning('取数工单 - 员工添加/更新异常: ' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => [],
        ];
    }

}
