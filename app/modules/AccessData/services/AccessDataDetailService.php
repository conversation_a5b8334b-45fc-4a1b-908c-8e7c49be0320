<?php

namespace App\Modules\AccessData\Services;

use App\Library\Enums;
use App\Library\AccessDataEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Modules\AccessData\Models\AccessDataBusinessLineModel;
use App\Modules\AccessData\Models\AccessDataDepartmentModel;
use App\Modules\AccessData\Models\AccessDataDownloadLogModel;
use App\Modules\AccessData\Models\AccessDataFormModel;
use App\Modules\AccessData\Models\AccessDataModel;
use App\Modules\AccessData\Models\AccessDataStaffInfoModel;
use App\Modules\User\Models\AttachModel;
use App\Modules\User\Services\UserService;
use App\Modules\User\Models\StaffInfoModel;
use App\Modules\Workflow\Services\WorkflowServiceV2;

class AccessDataDetailService extends BaseService
{
    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return AccessDataDetailService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 详情
     *
     * @param int $id
     * @param int $uid
     * @param int $module_type
     * @param boolean $if_download
     * @return array|mixed
     * @throws ValidationException
     * @throws BusinessException
     */
    public function getDetail(int $id, int $uid = 0, int $module_type = 0, bool $if_download = false)
    {
        // 当前用户在取数系统中的信息
        $user_data_sys_info = $this->getStaffDataSysInfo(['id' => $uid]);

        $main_model = AccessDataModel::findFirst([
            'id = :id:',
            'bind' => ['id' => $id],
        ]);

        if (empty($main_model)) {
            return [];
        }

        $req = (new AccessDataFlowService())->getRequest($id);
        if (empty($req->id)) {
            throw new BusinessException('取数工单获取业务审批流失败', ErrCode::$ACCESS_DATA_SYS_GET_WORK_REQUEST_ERROR);
        }

        $data = $main_model->toArray();

        // 获取表单列表
        $form_item = AccessDataFormModel::find([
            'conditions' => 'access_data_id = :access_data_id:',
            'bind'       => ['access_data_id' => $data['id']],
            'columns'    => ['header_ddl', 'estimate_data_volume', 'id'],
        ])->toArray();

        // 获取数据部门处理数据块逻辑:审批通过 + (复核通过 或 处理模块)
        if ($data['audit_status'] == Enums::WF_STATE_APPROVED && ($data['review_status'] == AccessDataEnums::DATA_PASSED_REVIEW_STATUS || $module_type == AccessDataEnums::MODULE_TYPE_PROCESS)) {
            // 是否展示重新上传按钮 [处理模块 + 审批通过 + 数据部门员工]
            // 展示逻辑: 1.数据待上传；2.复核驳回；3.复核通过+用户是数据上传者
            $re_upload_btn_auth = false;
            if ($module_type == AccessDataEnums::MODULE_TYPE_PROCESS) {
                if ($data['data_upload_status'] == AccessDataEnums::DATA_NOT_UPLOADED_STATUS) {
                    $re_upload_btn_auth = true;
                } else {
                    if ($data['review_status'] == AccessDataEnums::DATA_REVIEW_REJECTED_STATUS) {
                        $re_upload_btn_auth = true;
                    } else {
                        if ($data['review_status'] == AccessDataEnums::DATA_PASSED_REVIEW_STATUS && $uid == $data['data_uploader_id']) {
                            $re_upload_btn_auth = true;
                        }
                    }
                }
            }

            // 获取附件
            // 需求部门的人, 只可下载数据类型文件; 其他部门的人两种文件皆可下载
            $oss_bucket_type_item = [
                Enums::OSS_BUCKET_TYPE_ACCESS_DATA_SYS_DATA_FILE,
            ];

            // 当前用户非需求部门, 可以下载SQL类型文件
            if ($user_data_sys_info['department_category'] != AccessDataEnums::WORK_ORDER_AUDIT_STAGE_001) {
                $oss_bucket_type_item[] = Enums::OSS_BUCKET_TYPE_ACCESS_DATA_SYS_SQL_FILE;
            }

            $data_file_item = AttachModel::find([
                'conditions' => 'oss_bucket_key = :oss_bucket_key: AND oss_bucket_type IN ({oss_bucket_type:array}) AND deleted = :deleted:',
                'bind'       => [
                    'oss_bucket_key'  => $data['id'],
                    'oss_bucket_type' => $oss_bucket_type_item,
                    'deleted'         => 0,
                ],
                'columns'    => ['oss_bucket_type', 'file_name', 'bucket_name', 'object_key'],
            ])->toArray();
            foreach ($data_file_item as $file) {
                switch ($file['oss_bucket_type']) {
                    case Enums::OSS_BUCKET_TYPE_ACCESS_DATA_SYS_DATA_FILE:
                        $data_file_info['file_name']   = $file['file_name'];
                        $data_file_info['bucket_name'] = $re_upload_btn_auth ? $file['bucket_name'] : '';
                        $data_file_info['object_key']  = $re_upload_btn_auth ? $file['object_key'] : '';
                        break;
                    case Enums::OSS_BUCKET_TYPE_ACCESS_DATA_SYS_SQL_FILE:
                        $sql_file_info['file_name']   = $file['file_name'];
                        $sql_file_info['bucket_name'] = $re_upload_btn_auth ? $file['bucket_name'] : '';
                        $sql_file_info['object_key']  = $re_upload_btn_auth ? $file['object_key'] : '';
                        break;
                }
            }

            $data_process_info = [
                'indicator_theme'      => $data['indicator_theme'],
                'indicator_theme_text' => $data['indicator_theme'] ? self::$t[AccessDataEnums::$indicator_theme_item[$data['indicator_theme']]] : '',
                'data_file'            => $data_file_info ?? null,
                'sql_file'             => $sql_file_info ?? null,
                're_upload_btn_auth'   => $re_upload_btn_auth,
            ];
        }

        // 获取表单附件
        $form_item_ids  = array_column($form_item, 'id');
        $data_file_item = AttachModel::find([
            'conditions' => 'oss_bucket_key IN ({oss_bucket_key:array}) AND oss_bucket_type = :oss_bucket_type: AND deleted = :deleted:',
            'bind'       => [
                'oss_bucket_key'  => $form_item_ids,
                'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_ACCESS_DATA_FORM,
                'deleted'         => 0,
            ],
            'columns'    => ['oss_bucket_key', 'oss_bucket_type', 'file_name', 'bucket_name', 'object_key'],
        ])->toArray();
        $data_file_item = $data_file_item ? array_column($data_file_item, null, 'oss_bucket_key') : [];
        foreach ($form_item as $form_k => $form_v) {
            if (!empty($data_file_item[$form_v['id']])) {
                $form_v['attachment'][] = $data_file_item[$form_v['id']];
            } else {
                $form_v['attachment'] = [];
            }

            unset($form_v['id']);
            $form_item[$form_k] = $form_v;
        }

        $data['form_item']           = $form_item;
        $data['data_process_info']   = $data_process_info ?? null;
        $data['department_category'] = $user_data_sys_info['department_category'];

        // 审批日志
        $data['auth_logs'] = $this->getAuditLogs($req, $data, $if_download);

        return $this->handleData($data);
    }

    /**
     * @param $id
     * @param $uid
     * @param $module_type
     * @return array
     */
    public function getAuditDetail($id, $uid, $module_type)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];

        try {
            $data = $this->getDetail($id, $uid, $module_type);
            if (empty($data['id'])) {
                throw new BusinessException('获取工单详情失败', ErrCode::$ACCESS_DATA_SYS_GET_INFO_ERROR);
            }
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('取数工单系统,获取工单详情异常:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 数据文件下载
     *
     * @param int $biz_id
     * @param string $user_password
     * @param string $data_type
     * @param int $user_id
     * @return mixed
     */
    public function outputDataFile(int $biz_id, string $user_password, string $data_type, int $user_id)
    {
        $return = [
            'code'    => ErrCode::$VALIDATE_ERROR,
            'message' => 'unknown error',
            'data'    => [],
        ];

        try {
            // [1] 提取系统用户
            $user_model = StaffInfoModel::findfirst($user_id);
            if (empty($user_model)) {
                $return['message'] = static::$t['access_data_staff_info_not_exist'];

                return $return;
            }

            // [2] 密码校验
            $password_check_result = (new UserService())->checkUserPassword($user_model, $user_password);
            if (!$password_check_result) {
                $return['message'] = static::$t['access_data_staff_pwd_error'];

                return $return;
            }

            // [3] 取数工单校验
            $main_model = AccessDataModel::getFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $biz_id],
            ]);

            // 未审批通过，不可下载
            if ($main_model->audit_status != AccessDataEnums::AUDIT_APPROVE_STATUS) {
                $return['message'] = static::$t['access_data_not_approve_not_download'];

                return $return;
            }

            // 数据未上传不可下载
            if ($main_model->data_upload_status == AccessDataEnums::DATA_NOT_UPLOADED_STATUS) {
                $return['message'] = static::$t['access_data_not_uploaded_not_download'];

                return $return;
            }

            // 若当前用户非数据部门，则复核通过后方可下载
            $user_data_sys_info = BaseService::getStaffDataSysInfo(['id' => $user_id]);
            if (
                $user_data_sys_info['department_category'] != AccessDataEnums::WORK_ORDER_AUDIT_STAGE_003
                &&
                $main_model->review_status != AccessDataEnums::DATA_PASSED_REVIEW_STATUS
            ) {
                $return['message'] = static::$t['access_data_not_reviewed_not_download'];

                return $return;
            }

            // [5] 附件校验
            $oss_bucket_type = $data_type == 'data_file' ? Enums::OSS_BUCKET_TYPE_ACCESS_DATA_SYS_DATA_FILE : Enums::OSS_BUCKET_TYPE_ACCESS_DATA_SYS_SQL_FILE;
            $attach          = AttachModel::findFirst([
                'conditions' => 'oss_bucket_type = :oss_bucket_type: AND oss_bucket_key = :oss_bucket_key:',
                'bind'       => ['oss_bucket_type' => $oss_bucket_type, 'oss_bucket_key' => $main_model->id],
                'columns'    => ['object_key', 'file_name'],
            ]);

            if (empty($attach->object_key)) {
                $return['message'] = static::$t['access_data_download_abnormal_attach_null'];

                return $return;
            }

            $remote_file_prefix = $this->getDI()->get('config')->application->img_prefix ?? '';
            if (empty($remote_file_prefix)) {
                $return['message'] = static::$t['access_data_download_abnormal_url_null'];

                return $return;
            }
            $file_oss_url = $remote_file_prefix . $attach->object_key;

            $http_header = @get_headers($file_oss_url, true);

            $this->getDI()->get('logger')->info('取数工单数据文件下载 - OSS header:' . json_encode($http_header,
                    JSON_UNESCAPED_UNICODE));

            $http_response_header = $http_header[0] ?? 'header null';
            if (!stripos($http_response_header, '200')) {
                $return['message'] = 'oss response: ' . $http_response_header;

                return $return;
            }

            // 文件名
            $file_name       = $attach->file_name ? $attach->file_name : '_tmpDataFile' . date('YmdHis');
            $file_name_array = explode('.', $file_name);
            $file_suffix     = array_pop($file_name_array);

            // [5] 下载日志
            $log = [
                'access_data_id'      => $main_model->id,
                'requester_staff_id'  => $main_model->requester_id ?? 0,
                'uploader_staff_id'   => $main_model->data_uploader_id ?? 0,
                'downloader_staff_id' => $user_id ?? 0,
                'file_category'       => $oss_bucket_type,
                'file_oss_url'        => $file_oss_url ?? '',
                'file_name'           => $file_name,
                'file_type'           => $file_suffix,
                'download_time'       => date('Y-m-d H:i:s'),
            ];
            (new AccessDataDownloadLogModel())->i_create($log);

            $return['code']    = Errcode::$SUCCESS;
            $return['message'] = 'success';
            $return['data']    = [
                'file_url'    => $file_oss_url,
                'file_name'   => $file_name,
                'file_suffix' => $file_suffix,
                'http_header' => $http_header,
            ];
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $return['code']    = ErrCode::$SYSTEM_ERROR;
            $return['message'] = static::$t->_('retry_later');

            $this->getDI()->get('logger')->warning('取数工单数据文件下载异常:' . $e->getMessage());
        }

        return $return;
    }

    /**
     * 处理详情数据格式
     *
     * @param $data
     * @return array
     */
    protected function handleData($data)
    {
        if (empty($data)) {
            return [];
        }

        // 数据涉密范围
        $confidential_scope = explode(',', $data['confidential_scope']);
        foreach ($confidential_scope as $scope_k) {
            $confidential_scope_item[] = [
                'code'  => $scope_k,
                'label' => self::$t[AccessDataEnums::$confidential_scope_item[$scope_k]],
            ];
        }

        // 详情页顶部进度条
        $curr_process_item = $this->calWorkOrderTopProcess($data);
        $top_process_item  = $this->getDetailTopProcess($curr_process_item);

        // 工单进度
        $work_order_process = $this->workOrderProcessMerge($data);

        return [
            'id'                        => $data['id'],
            'submitter_id'              => $data['submitter_id'],
            'submitter_name'            => $data['submitter_name'],
            'submitter_department_name' => $data['submitter_department_name'],
            'requester_id'              => $data['requester_id'] ? $data['requester_id'] : '',
            'requester_name'            => $data['requester_name'] ?? '',
            'requirement_name'          => $data['requirement_name'],
            'first_business_line_name'  => $data['first_business_line_name'],
            'second_business_line_name' => $data['second_business_line_name'],
            'data_purpose'              => $data['data_purpose'],
            'data_purpose_text'         => self::$t[AccessDataEnums::$data_purpose_item[$data['data_purpose']]],
            'requirement_background'    => $data['requirement_background'],
            'requirement_detail'        => $data['requirement_detail'],
            'confidential_scope'        => $confidential_scope_item ?? [],
            'expected_delivery_time'    => $data['expected_delivery_time'],
            'audit_status'              => $data['audit_status'],
            'audit_stage'               => $data['audit_stage'],
            'user_department_category'  => $data['department_category'],

            'form_item'          => $data['form_item'],
            'data_process_info'  => $data['data_process_info'],
            'auth_logs'          => $data['auth_logs'],
            'top_process_item'   => $top_process_item,
            'work_order_process' => $work_order_process,
        ];
    }

    /**
     * 获取审批日志
     *
     * @param $req
     * @param $detail_data
     * @param bool $if_download
     * @return array
     */
    private function getAuditLogs($req, $detail_data = [], $if_download = false)
    {
        // 强制设置语言为中文: 取数系统的特有需求
        if (WorkflowServiceV2::$language != AccessDataEnums::DEFAULT_SUPPORT_LANGUAGE) {
            WorkflowServiceV2::setLanguage(AccessDataEnums::DEFAULT_SUPPORT_LANGUAGE);
        }

        $auth_logs = (new WorkflowServiceV2())->getAuditLogs($req);

        // 取数系统全员与部门信息
        $all_staff = (new BaseService())->getDataSysAllStaffInfo();
        $all_staff = array_column($all_staff, null, 'staff_id');

        //
        $audit_log_staff = array_column($auth_logs, 'action', 'staff_id');

        // 取员工在取数系统中的姓名/部门, 职位置空
        foreach ($auth_logs as $log_k => $log_v) {
            $_staff_info = $all_staff[$log_v['staff_id']] ?? [];

            $log_v['staff_name']       = $_staff_info['staff_name'] ?? '';
            $log_v['staff_department'] = $_staff_info['department_name'] ?? '';
            $log_v['job_title']        = isset($_staff_info['duty_flag']) ? self::$t[AccessDataEnums::$staff_duty_item[$_staff_info['duty_flag']]] : '';

            $__tmp_sub_list = [];
            $sub_list       = $log_v['list'] ?? [];
            foreach ($sub_list as $sub_k => $sub_v) {
                if (isset($audit_log_staff[$sub_v['staff_id']]) && $audit_log_staff[$sub_v['staff_id']] == 1) {
                    continue;
                }

                $__staff_info = $all_staff[$sub_v['staff_id']] ?? [];

                $sub_v['staff_name']       = $__staff_info['staff_name'] ?? '';
                $sub_v['staff_department'] = $__staff_info['department_name'] ?? '';
                $sub_v['job_title']        = isset($__staff_info['duty_flag']) ? self::$t[AccessDataEnums::$staff_duty_item[$__staff_info['duty_flag']]] : '';

                $__tmp_sub_list[] = $sub_v;
            }

            if (!empty($__tmp_sub_list)) {
                $log_v['list'] = $__tmp_sub_list;
            } else {
                unset($log_v['list']);
            }

            $auth_logs[$log_k] = $log_v;
        }

        // 下载的时候不要申请
        if ($if_download) {
            $temp = [];
            foreach ($auth_logs as $k => $v) {
                // 如果申请的就跳过
                if ($v['action'] == 0) {
                    continue;
                }

                $temp[] = $v;
            }

            $auth_logs = $temp;
        }

        return $auth_logs;
    }

    /**
     * 登录用户待处理数
     *
     * @param int $user_id
     * @return mixed
     */
    public function getUserWaitingProcessedCount(int $user_id)
    {
        try {
            if (empty($user_id)) {
                return 0;
            }

            // 登录用户是否是数据部门
            $user_access_sys_info = $this->getStaffDataSysInfo(['id' => $user_id]);
            if (empty($user_access_sys_info) || $user_access_sys_info['department_category'] != AccessDataEnums::WORK_ORDER_AUDIT_STAGE_003) {
                return 0;
            }

            // 审批通过: 数据待上传 | 数据已上传+待复核/复核驳回
            return AccessDataModel::count([
                'conditions' => '
                    audit_status = :audit_status: 
                    AND 
                    (
                        data_upload_status = :data_upload_status_01:
                        OR 
                        (data_upload_status = :data_upload_status_02: AND review_status = :review_status_01:)
                        OR
                        (data_upload_status = :data_upload_status_02: AND review_status = :review_status_02:)
                    )
                    ',
                'bind'       => [
                    'audit_status'          => AccessDataEnums::AUDIT_APPROVE_STATUS,
                    'data_upload_status_01' => AccessDataEnums::DATA_NOT_UPLOADED_STATUS,
                    'data_upload_status_02' => AccessDataEnums::DATA_UPLOADED_STATUS,
                    'review_status_01'      => AccessDataEnums::DATA_NOT_REVIEWED_STATUS,
                    'review_status_02'      => AccessDataEnums::DATA_REVIEW_REJECTED_STATUS,
                ],
            ]);
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->warning('取数系统 - 获取用户待处理数异常:' . $e->getMessage());
        }

        return 0;
    }

    /**
     * 员工页默认配置
     *
     * @return mixed
     */
    public function getStaffDetailEnums()
    {
        // 部门列表
        $dept_item = AccessDataDepartmentModel::find([
            'conditions' => 'is_del = 0',
            'columns'    => ['id AS value', 'name AS label', 'category'],
        ])->toArray();


        // 员工属性
        $staff_flag = [
            [
                'value' => '0',
                'label' => self::$t['access_data_duty_flag_0'],
            ],

            [
                'value' => '1',
                'label' => self::$t['access_data_duty_flag_1'],
            ],
        ];

        // 业务线权限列表
        $all_business_line  = AccessDataBusinessLineModel::find([
            'conditions' => 'is_del = 0',
            'columns'    => ['id', 'name', 'parent_id'],
        ])->toArray();
        $all_business_line  = $all_business_line ? array_column($all_business_line, null, 'id') : [];
        $business_line_item = [];
        foreach ($all_business_line as $line) {
            if ($line['parent_id']) {
                $parent_id_info       = $all_business_line[$line['parent_id']] ?? [];
                $parent_id_name       = $parent_id_info['name'] ?? '';
                $business_line_item[] = [
                    'value' => $line['id'],
                    'label' => $parent_id_name . '-' . $line['name'],
                ];
            }
        }

        return [
            'department_item'    => $dept_item,
            'staff_flag_item'    => $staff_flag,
            'business_line_item' => $business_line_item,
        ];
    }

    /**
     * 获取员工详情
     *
     * @param int $staff_id
     * @return mixed
     */
    public function getStaffInfo(int $staff_id)
    {
        $data = [];
        try {
            $builder = $this->modelsManager->createBuilder();
            $builder->columns([
                'staff.staff_id',
                'staff.staff_name',
                'dept.id AS department_id',
                'dept.name AS department_name',
                'staff.business_line_permission',
                'staff.duty_flag',
            ]);
            $builder->from(['staff' => AccessDataStaffInfoModel::class]);
            $builder->leftjoin(AccessDataDepartmentModel::class, 'staff.department_id = dept.id', 'dept');
            $builder->andWhere('staff.staff_id = :staff_id:', ['staff_id' => $staff_id]);
            $builder->andWhere('staff.is_del = 0');
            $data = $builder->getQuery()->getSingleResult()->toArray();
            if (!empty($data)) {
                // 获取业务线列表
                $business_line_item = AccessDataBusinessLineModel::find([
                    'columns' => ['id', 'name', 'parent_id'],
                ])->toArray();
                $business_line_item = $business_line_item ? array_column($business_line_item, null, 'id') : [];

                // 业务线权限
                if (!empty($data['business_line_permission'])) {
                    $staff_business_lines = explode(',', $data['business_line_permission']);

                    $_business_line_permission = [];
                    foreach ($staff_business_lines as $line_id) {
                        $line_info = $business_line_item[$line_id] ?? [];
                        if (!empty($line_info) && $line_info['parent_id']) {
                            $_business_line_permission[] = $line_id;
                        }
                    }

                    $data['business_line_permission'] = $_business_line_permission;
                }

                // 员工属性
                $data['duty_flag_label'] = self::$t['access_data_duty_flag_' . $data['duty_flag']];
            }
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->warning('取数系统 - 获取用户信息异常:' . $e->getMessage());
        }

        return $data;
    }
}
