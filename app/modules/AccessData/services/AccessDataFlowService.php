<?php

namespace App\Modules\AccessData\Services;

use App\Library\Enums;
use App\Library\AccessDataEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Modules\AccessData\Models\AccessDataModel;
use App\Modules\Workflow\Services\AbstractFlowService;
use App\Modules\Workflow\Services\WorkflowServiceV2;
use App\Modules\Workflow\Models\WorkflowRequestNodeAuditorModel;
use Phalcon\Mvc\Model;

class AccessDataFlowService extends AbstractFlowService
{
    /**
     * 审批 - 通过操作
     *
     * @param $id
     * @param $note
     * @param $user
     * @param $update_data
     * @return array
     */
    public function approve($id, $note, $user, $update_data = [])
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $db     = $this->getDI()->get('db_oa');
        $logger = $this->getDI()->get('logger');

        try {
            $db->begin();

            $main_model = AccessDataModel::getFirst([
                'id = :id:',
                'bind' => ['id' => $id],
            ]);

            if (empty($main_model)) {
                throw new BusinessException('取数工单信息获取失败, id = ' . $id, ErrCode::$ACCESS_DATA_SYS_GET_INFO_ERROR);
            }

            $logger->info('取数系统 - 审批通过操作 - 主表更新前数据: ' . json_encode($main_model->toArray(), JSON_UNESCAPED_UNICODE));

            // 待更新的业务数据
            $waiting_update_data = [
                'audit_time'  => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s'),
                'updater_id'  => $user['id'],
            ];

            // 验证审批人身份: 非需求、数据、相关部门负责人，无审批权限
            $user_data_sys_info = (new BaseService())->getStaffDataSysInfo($user);
            if ($main_model->audit_stage == AccessDataEnums::WORK_ORDER_AUDIT_STAGE_001) {
                // 需求部门审批阶段
                if ($user_data_sys_info['department_category'] != AccessDataEnums::WORK_ORDER_AUDIT_STAGE_001) {
                    throw new ValidationException(static::$t->_('access_data_work_order_approval_processed'),
                        ErrCode::$VALIDATE_ERROR);
                }

                $waiting_update_data['audit_stage'] = AccessDataEnums::WORK_ORDER_AUDIT_STAGE_003;
            } else {
                if ($main_model->audit_stage == AccessDataEnums::WORK_ORDER_AUDIT_STAGE_003) {
                    // 数据部门审批阶段
                    if ($user_data_sys_info['department_category'] != AccessDataEnums::WORK_ORDER_AUDIT_STAGE_003) {
                        throw new ValidationException(static::$t->_('access_data_work_order_approval_processed'),
                            ErrCode::$VALIDATE_ERROR);
                    }

                    $waiting_update_data['confidential_scope']      = implode(',',
                        array_filter(array_unique($update_data['confidential_scope_item'])));
                    $waiting_update_data['transfer_department_ids'] = implode(',',
                        array_filter(array_unique($update_data['transfer_department_id_item'])));

                    $waiting_update_data['audit_stage'] = AccessDataEnums::WORK_ORDER_AUDIT_STAGE_005;
                } else {
                    if ($main_model->audit_stage == AccessDataEnums::WORK_ORDER_AUDIT_STAGE_005) {
                        // 相关部门审批阶段
                        if ($user_data_sys_info['department_category'] != AccessDataEnums::WORK_ORDER_AUDIT_STAGE_005) {
                            throw new ValidationException(static::$t->_('access_data_work_order_approval_processed'),
                                ErrCode::$VALIDATE_ERROR);
                        }
                    } else {
                        throw new ValidationException(static::$t->_('access_data_audit_stage'),
                            ErrCode::$VALIDATE_ERROR);
                    }
                }
            }

            // 工单状态验证
            if ($main_model->audit_status == AccessDataEnums::AUDIT_APPROVE_STATUS) {
                throw new ValidationException(static::$t->_('access_data_audit_has_been_approval'),
                    ErrCode::$VALIDATE_ERROR);
            }

            if ($main_model->audit_status == AccessDataEnums::AUDIT_REJECT_STATUS) {
                throw new ValidationException(static::$t->_('access_data_audit_has_been_rejected'),
                    ErrCode::$VALIDATE_ERROR);
            }

            // 业务审批流
            $work_req = $this->getRequest($id);
            if (empty($work_req->id)) {
                throw new BusinessException('取数工单业务审批流获取失败, biz_id = ' . $id,
                    ErrCode::$ACCESS_DATA_SYS_GET_WORK_REQUEST_ERROR);
            }

            // 审批
            $result = (new WorkflowServiceV2())->doApprove($work_req, $user,
                $this->getWorkflowCustomizedParams($main_model, $user, $waiting_update_data), $note);

            // 全部审批通过
            if (!empty($result->approved_at)) {
                $waiting_update_data['audit_time']   = $result->approved_at;
                $waiting_update_data['audit_status'] = AccessDataEnums::AUDIT_APPROVE_STATUS;
            }

            $main_bool = null;
            if (!empty($waiting_update_data)) {
                // 更新业务主表数据
                $main_bool = $main_model->i_update($waiting_update_data);
                if ($main_bool === false) {
                    throw new BusinessException('取数工单主表更新失败', ErrCode::$ACCESS_DATA_WORK_ORDER_UPDATE_ERROR);
                }
            }

            $logger->info('取数系统 - 审批通过操作 - 主表更新后数据: ' . json_encode($main_model->toArray(), JSON_UNESCAPED_UNICODE));

            // 全部审核通过 且 数据更新成功
            if (!empty($result->approved_at) && $main_bool) {
                // TODO 给数据部门处理人发送邮件通知, 本版无此需求，预留
//                $this->sendEmailToAuditors($work_req, '',1);
            }

            $db->commit();
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $logger->warning('取数需求工单审批[通过操作]异常: ' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
        ];
    }

    /**
     * 审批驳回
     *
     * @param $main_id
     * @param $note
     * @param $user
     * @return array
     */
    public function reject($main_id, $note, $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $db     = $this->getDI()->get('db_oa');
        $logger = $this->getDI()->get('logger');

        try {
            $db->begin();

            $main_model = AccessDataModel::getFirst([
                'id = :id:',
                'bind' => ['id' => $main_id],
            ]);

            if (empty($main_model)) {
                throw new BusinessException('取数工单信息获取失败, id = ' . $main_id, ErrCode::$ACCESS_DATA_SYS_GET_INFO_ERROR);
            }

            $logger->info('取数系统 - 审批驳回操作 - 主表更新前数据: ' . json_encode($main_model->toArray(), JSON_UNESCAPED_UNICODE));

            // 工单状态验证
            if ($main_model->audit_status == AccessDataEnums::AUDIT_APPROVE_STATUS) {
                throw new ValidationException(static::$t->_('access_data_audit_has_been_approval'),
                    ErrCode::$VALIDATE_ERROR);
            }

            if ($main_model->audit_status == AccessDataEnums::AUDIT_REJECT_STATUS) {
                throw new ValidationException(static::$t->_('access_data_audit_has_been_rejected'),
                    ErrCode::$VALIDATE_ERROR);
            }

            $work_req = $this->getRequest($main_id);
            if (empty($work_req->id)) {
                throw new BusinessException('取数工单业务审批流获取失败, id = ' . $main_id,
                    ErrCode::$ACCESS_DATA_SYS_GET_WORK_REQUEST_ERROR);
            }

            $result = (new WorkflowServiceV2())->doReject($work_req, $user,
                $this->getWorkflowCustomizedParams($main_model, $user), $note);

            if ($result === false) {
                throw new BusinessException('取数工单系统审批流驳回操作失败', ErrCode::$ACCESS_DATA_SYS_WORK_FLOW_REJECT_ERROR);
            }

            $bool = $main_model->i_update([
                'audit_status' => AccessDataEnums::AUDIT_REJECT_STATUS,
                'audit_time'   => $result->rejected_at ?? date('Y-m-d H:i:s'),
                'update_time'  => date('Y-m-d H:i:s'),
                'updater_id'   => $user['id'],
            ]);

            if ($bool === false) {
                throw new BusinessException('取数工单主表更新失败', ErrCode::$ACCESS_DATA_WORK_ORDER_UPDATE_ERROR);
            }

            $logger->info('取数系统 - 审批驳回操作 - 主表更新后数据: ' . json_encode($main_model->toArray(), JSON_UNESCAPED_UNICODE));

            $db->commit();
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $logger->warning('取数系统审批[驳回]异常: ' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
        ];
    }

    /**
     * 撤回申请
     *
     * @param $id
     * @param $note
     * @param $user
     * @return mixed|void
     */
    public function cancel($id, $note, $user)
    {
    }

    /**
     * 获取审批流信息
     *
     * @param $id
     * @return Model
     */
    public function getRequest($id)
    {
        return $this->getRequestByBiz($id, Enums::WF_ACCESS_DATA_WORK_ORDER_TYPE);
    }

    /**
     * 网点租房付款申请 - 注入审批流
     *
     * @param $biz_id
     * @param $user
     * @return \Phalcon\Mvc\Model\Resultset|\Phalcon\Mvc\Phalcon\Mvc\Model
     * @throws BusinessException
     */
    public function createRequest($biz_id, $user)
    {
        $model = AccessDataModel::findFirst([
            'id = :id:',
            'bind' => ['id' => $biz_id],
        ]);

        $data['id']       = $model->id;
        $data['name']     = $model->work_order_no . '审批申请';
        $data['biz_type'] = Enums::WF_ACCESS_DATA_WORK_ORDER_TYPE;
        $data['flow_id']  = $this->getFlowId();

        return (new WorkflowServiceV2())->createRequest($data, $user,
            $this->getWorkflowCustomizedParams($model, $user));
    }

    /**
     * 获取审批流自定义参数
     *
     * @param $main_model
     * @param $user
     * @param $customized_params
     * @return array
     */
    public function getWorkflowCustomizedParams($main_model, $user, $customized_params = [])
    {
        $transfer_department_ids = $customized_params['transfer_department_ids'] ?? $main_model->transfer_department_ids;
        return [
            'submitter_id'                  => $main_model->submitter_id,                 // 申请人用，审批流创建人
            'submitter_department_id'       => $main_model->submitter_department_id,      //提交人部门id(需求部门id)
            'submitter_department_category' => $main_model->submitter_department_category,//提交人部门标签, 可以是多个,英文逗号间隔
            'data_department_id'            => AccessDataEnums::DATA_DEPARTMENT_ID,       //数据部门id
            'audit_stage'                   => $main_model->audit_stage,                  // 审批阶段:1需求部门；2数据部门；3相关部门
            'transfer_department_id_item'   => $transfer_department_ids ? explode(',', $transfer_department_ids) : [],
        ];
    }

    /**
     * @inheritDoc
     */
    public function getFlowId($model = null)
    {
        return Enums::WF_ACCESS_DATA_WORK_ORDER_WF_ID;
    }

    /**
     * 登录用户待审批数
     *
     * @param int $user_id
     * @param int $audit_status
     * @return mixed
     */
    public function getUserWaitingAuditCount(int $user_id, int $audit_status = 1)
    {
        try {
            if (empty($user_id) || !in_array($audit_status, [1, 2, 3, 4])) {
                return 0;
            }

            return WorkflowRequestNodeAuditorModel::count([
                'conditions' => 'biz_type = :biz_type: AND auditor_id = :auditor_id: AND audit_status = :audit_status:',
                'bind'       => [
                    'biz_type'     => Enums::WF_ACCESS_DATA_WORK_ORDER_TYPE,
                    'auditor_id'   => $user_id,
                    'audit_status' => $audit_status,
                ],
            ]);
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->warning('取数系统 - 获取用户待审批数异常:' . $e->getMessage());
        }

        return 0;
    }
}
