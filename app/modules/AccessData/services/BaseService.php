<?php

namespace App\Modules\AccessData\Services;

use App\Library\AccessDataEnums;
use App\Modules\AccessData\Models\AccessDataBusinessLineModel;
use App\Modules\AccessData\Models\AccessDataStaffInfoModel;
use App\Modules\AccessData\Models\AccessDataDepartmentModel;

class BaseService extends \App\Library\BaseService
{
    // 列表类型
    const LIST_TYPE_APPLY = 1;
    const LIST_TYPE_AUDIT = 2;
    const LIST_TYPE_PROCESS = 3;
    const LIST_TYPE_DATA = 4;

    const SHORT_TEXT_LEN = '0,20';
    const LONG_TEXT_LEN = '0,50';
    const REQUIRED_SHORT_TEXT_LEN = '5,20';
    const REQUIRED_LONG_TEXT_LEN = '1,1000';

    // 非必须参数
    public static $not_must_params = [
        '_url',
        'PHPSESSID',
    ];

    // 申请验证规则
    public static $validate_apply_param = [
        // 基础数据
        'requester_id'              => 'StrLenGeLe:5,20|>>>:参数错误[requester_id]',
        'requester_name'            => 'StrLenGeLe:1,128|>>>:参数错误[requester_name]',
        'first_business_line_id'    => 'Required|IntGt:0|>>>:参数错误[first_business_line_id]',
        'first_business_line_name'  => 'StrLenGeLe:1,32|>>>:参数错误[first_business_line_name]',
        'second_business_line_id'   => 'Required|IntGt:0|>>>:参数错误[second_business_line_id]',
        'second_business_line_name' => 'StrLenGeLe:1,32|>>>:参数错误[second_business_line_name]',

        'requirement_name'                  => 'Required|StrLenGeLe:1,30|>>>:需求名称填写不合规',
        'requirement_background'            => 'Required|StrLenGeLe:1,500|>>>:需求背景填写不合规',
        'requirement_detail'                => 'Required|StrLenGeLe:1,500|>>>:需求详情填写不合规',
        'data_purpose'                      => 'Required|IntIn:1,2,3|>>>:数据用途填写不合规',
        'expected_delivery_time'            => 'Required|DateTime|>>>:期望交付时间填写不合规',

        // 表单项
        'form_item'                         => 'Required|Arr|ArrLenGeLe:1,100|>>>:表单填写不合规',
        'form_item[*]'                      => 'Required|Obj|>>>:表单填写不合规',
        'form_item[*].header_ddl'           => 'Required|StrLenGeLe:1,50000|>>>:表头及定义填写不合规',
        'form_item[*].estimate_data_volume' => 'Required|IntGt:0|>>>:请输入预估行数，仅可输入数字',

        // 涉密范围
        'confidential_scope_item'           => 'Required|Arr|ArrLenGeLe:1,100|>>>:数据涉密范围选择不合规',
    ];

    // 详情查看校验
    public static $validate_detail_param = [
        'id' => 'Required|IntGe:1|>>>:参数错误[ID]',
    ];

    // 员工搜索验证规则
    public static $validate_staff_search_param = [
        'search_word' => 'Required|StrLenGeLe:1,128|>>>:需求人输入不合规',
    ];

    // 需求部门审批通过校验参数
    public static $validate_requirement_department_approve = [
        'id' => 'Required|IntGe:1|>>>:参数错误[ID]',
    ];

    // 数据部门审批通过校验参数
    public static $validate_data_department_approve = [
        'id'                          => 'Required|IntGe:1|>>>:参数错误[ID]',
        'confidential_scope_item'     => 'Required|Arr|ArrLenGeLe:1,100|>>>:请选择数据涉密范围',
        'transfer_department_id_item' => 'Required|Arr|ArrLenGeLe:1,100|>>>:请选择需要转交的相关部门',
    ];

    // 相关部门审批通过校验参数
    public static $validate_related_department_approve = [
        'id'   => 'Required|IntGe:1|>>>:参数错误[ID]',
        'note' => 'StrLenGeLe:0,1000|>>>:相关部门审批备注填写不合规',
    ];

    // 审批驳回参数校验
    public static $validate_reject = [
        'id'                 => 'Required|IntGe:1|>>>:参数错误[ID]',
        'reject_reason_item' => 'Arr|ArrLenGeLe:1,100|>>>:请选择驳回原因',
        'note'               => 'StrLenGeLe:1,1000|>>>:其他驳回原因填写不合规',
    ];

    // 处理：上传数据参数校验
    public static $validate_data_upload = [
        'id'              => 'Required|IntGe:1|>>>:参数错误[ID]',
        'indicator_theme' => 'Required|IntGeLe:1,13|>>>:请选择正确的指标主题',
        'data_file'       => 'Required|Obj|>>>:参数错误[数据文件类型]',
        'sql_file'        => 'Required|Obj|>>>:参数错误[SQL文件类型]',
    ];

    // 处理：复核参数校验
    public static $validate_review = [
        'id'                     => 'Required|IntGe:1|>>>:参数错误[ID]',
        'review_status'          => 'Required|IntIn:1,2|>>>:参数错误[复核状态]',
        'review_rejected_reason' => 'IfIntEq:review_status,2|Required|StrLenGeLe:1,500|>>>:驳回原因不可为空',
    ];

    // 相关部门审批通过校验参数
    public static $validate_data_file_download = [
        'id'        => 'Required|IntGe:1|>>>:参数错误[ID]',
        'pwd'       => 'Required|StrLenGeLe:1,32|>>>:参数错误[密码]',
        'data_type' => 'Required|StrIn:data_file,sql_file|>>>:参数错误[数据类型]',
    ];

    /**
     * 过滤空值 和 非必要参数
     *
     * @param array $params
     * @param array $not_must
     * @return mixed
     */
    public static function handleParams(array $params, array $not_must)
    {
        $params = array_filter($params);

        if (empty($not_must) || !is_array($not_must)) {
            return $params;
        }

        foreach ($not_must as $value) {
            if (isset($params[$value])) {
                unset($params[$value]);
            }
        }

        return $params;
    }

    /**
     * 获取所有静态配置项
     */
    public function getAllEnumsSetting()
    {
        $setting = [];
        // 数据用途
        foreach (AccessDataEnums::$data_purpose_item as $purpose_code => $purpose_k) {
            $setting['data_purpose'][] = [
                'code'  => $purpose_code,
                'label' => self::$t[$purpose_k],
            ];
        }

        // 数据涉密范围
        foreach (AccessDataEnums::$confidential_scope_item as $scope_code => $scope_k) {
            $setting['confidential_scope'][] = [
                'code'  => $scope_code,
                'label' => self::$t[$scope_k],
            ];
        }

        // 指标主题
        foreach (AccessDataEnums::$indicator_theme_item as $theme_code => $theme_k) {
            $setting['indicator_theme'][] = [
                'code'  => $theme_code,
                'label' => self::$t[$theme_k],
            ];
        }

        // 驳回原因
        foreach (AccessDataEnums::$audit_rejected_reason_item as $rejected_code => $rejected_k) {
            $setting['audit_rejected_reason'][] = [
                'code'  => $rejected_code,
                'label' => self::$t[$rejected_k],
            ];
        }

        // 转交相关部门
        foreach (AccessDataEnums::$transfer_department_item as $department_code => $department_k) {
            $setting['transfer_department'][] = [
                'code'  => $department_code,
                'label' => self::$t[$department_k],
            ];
        }

        // 进度下拉框
        foreach (AccessDataEnums::$work_order_process_status_item as $process_code => $process_k) {
            $setting['work_order_process'][] = [
                'code'  => $process_code,
                'label' => self::$t[$process_k],
            ];
        }

        return $setting;
    }

    /**
     * 获取用户在工单系统中的姓名、部门等基本信息
     *
     * @param array $user_info
     * @return mixed
     */
    public function getStaffDataSysInfo(array $user_info)
    {
        try {
            if (empty($user_info['id'])) {
                throw new \Exception("not found user");
            }

            // 用户基本信息
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['staff' => AccessDataStaffInfoModel::class]);
            $builder->leftJoin(AccessDataDepartmentModel::class, "staff.department_id = dept.id", "dept");
            $builder->where('staff.staff_id = :staff_id:', ['staff_id' => $user_info['id']]);
            $builder->andWhere('staff.is_del = :s_is_del:', ['s_is_del' => 0]);
            $builder->andWhere('dept.is_del = :d_is_del:', ['d_is_del' => 0]);

            $builder->columns([
                'staff.staff_id',
                'staff.staff_name',
                'staff.duty_flag',
                'staff.business_line_permission',
                'dept.id AS department_id',
                'dept.name AS department_name',
                'dept.category AS department_category',
            ]);

            $data = $builder->getQuery()->getSingleResult();

            if (!empty($data)) {
                return $data->toArray();
            }
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->warning('取数工单系统 - 获取员工信息异常: ' . $e->getMessage());
        }

        return [];
    }

    /**
     * 获取用户业务线
     *
     * @param string $business_ids
     * @return mixed
     */
    public function getBusinessLineList(string $business_ids)
    {
        try {
            if (empty($business_ids) || !is_string($business_ids)) {
                return [];
            }

            $ids = explode(',', $business_ids);

            $list = AccessDataBusinessLineModel::find([
                'conditions' => 'id in ({ids:array}) AND is_del = :is_del:',
                'bind'       => ['ids' => $ids, 'is_del' => 0],
                'columns'    => ['id', 'name', 'level', 'parent_id'],
            ])->toArray();

            // 业务线树结构
            return $list ? $this->genBusinessLineTree($list) : [];
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->warning('取数工单系统 - 获取指定业务线异常: ' . $e->getMessage());
        }

        return [];
    }

    // 业务线树结构处理
    public function genBusinessLineTree(array $list)
    {
        if (empty($list)) {
            return [];
        }

        //第一步 构造数据
        $list = array_column($list, null, 'id');

        //第二步 遍历数据 生成树状结构
        $tree_list = [];
        foreach ($list as $key => $value) {
            $list[$key]['id'] = $value['id'];

            if (isset($list[$value['parent_id']])) {
                $list[$value['parent_id']]['sub_list'][] = &$list[$key];
            } else {
                $tree_list[] = &$list[$key];
            }

            // 删除无用的元素
            unset($list[$key]['level']);
            unset($list[$key]['parent_id']);
        }

        return $tree_list;
    }

    /**
     * 获取详情页顶部进度条及状态
     *
     * @param array $processed_stage
     * @return mixed
     */
    public function getDetailTopProcess(array $processed_stage)
    {
        $processed_stage = $processed_stage ?? [1];

        $data = [];
        foreach (AccessDataEnums::$detail_page_top_process_status_item as $process_code => $process_k) {
            $data[] = [
                'process_stage' => $process_code,
                'process_name'  => self::$t[$process_k],
                'is_processed'  => in_array($process_code, $processed_stage) ? 1 : 0,
            ];
        }

        return $data;
    }

    /**
     * 获取指定业务线及负责人信息
     *
     * @param int $business_line_id
     * @return mixed
     */
    public function getBusinessLineAndManagerInfo(int $business_line_id)
    {
        try {
            if (empty($business_line_id)) {
                return [];
            }

            $builder = $this->modelsManager->createBuilder();
            $builder->from(['line' => AccessDataBusinessLineModel::class]);
            $builder->leftJoin(AccessDataStaffInfoModel::class, "line.data_manager_id = staff.staff_id", "staff");
            $builder->where('line.id = :id:', ['id' => $business_line_id]);
            $builder->andWhere('staff.is_del = :s_is_del:', ['s_is_del' => 0]);
            $builder->andWhere('line.is_del = :d_is_del:', ['d_is_del' => 0]);

            $builder->columns([
                'line.id',
                'line.name',
                'line.level',
                'line.data_manager_id',
                'staff.staff_name AS data_manager_name',
            ]);

            $data = $builder->getQuery()->getSingleResult();

            if (!empty($data)) {
                return $data->toArray();
            }
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->warning('取数工单系统 - 获取业务线及负责人信息异常: ' . $e->getMessage());
        }

        return [];
    }

    /**
     * 获取N天前的时间
     *
     * @param int $days
     * @return mixed
     */
    public function getBeforeSomeDaysTime(int $days)
    {
        $days = $days ?? 30;
        return date('Y-m-d H:i:s', time() - $days * 86400);
    }

    /**
     * 工单状态归并处理(审批状态 + 复核状态)
     * 页面展示用
     *
     * @param array $data 取数工单数据 含审批状态, 审批阶段, 数据上传状态, 数据复核状态
     * @return mixed
     */
    public function workOrderStatusMerge(array $data)
    {
        if ($data['audit_status'] == AccessDataEnums::AUDIT_APPROVE_STATUS && $data['review_status'] != AccessDataEnums::DATA_PASSED_REVIEW_STATUS) {
            // 处理中
            $status = AccessDataEnums::WORK_ORDER_AUDIT_STATUS_002;
        } else {
            if ($data['audit_status'] == AccessDataEnums::AUDIT_REJECT_STATUS || $data['review_status'] == AccessDataEnums::DATA_PASSED_REVIEW_STATUS) {
                // 已完成
                $status = AccessDataEnums::WORK_ORDER_AUDIT_STATUS_003;
            } else {
                // 审批中
                $status = AccessDataEnums::WORK_ORDER_AUDIT_STATUS_001;
            }
        }

        return $status;
    }

    /**
     * 工单进度状态归并处理(审批状态 + 审批阶段 + 数据上传状态 + 复核状态)
     * 页面展示用
     *
     * @param array $data 取数工单数据 含审批状态, 审批阶段, 数据上传状态, 数据复核状态
     * @return mixed
     */
    public function workOrderProcessMerge(array $data)
    {
        if ($data['audit_status'] == AccessDataEnums::AUDIT_PENDING_STATUS && $data['audit_stage'] == AccessDataEnums::WORK_ORDER_AUDIT_STAGE_001) {
            // 已提交
            $process_status = AccessDataEnums::WORK_ORDER_PROCESS_STATUS_001;
        } else {
            if ($data['audit_status'] == AccessDataEnums::AUDIT_PENDING_STATUS && $data['audit_stage'] == AccessDataEnums::WORK_ORDER_AUDIT_STAGE_003) {
                // 部门审批通过
                $process_status = AccessDataEnums::WORK_ORDER_PROCESS_STATUS_002;
            } else {
                if ($data['audit_status'] == AccessDataEnums::AUDIT_PENDING_STATUS && $data['audit_stage'] == AccessDataEnums::WORK_ORDER_AUDIT_STAGE_005) {
                    // 审批中
                    $process_status = AccessDataEnums::WORK_ORDER_PROCESS_STATUS_003;
                } else {
                    if (
                        $data['audit_status'] == AccessDataEnums::AUDIT_APPROVE_STATUS
                        &&
                        (
                            $data['data_upload_status'] == AccessDataEnums::DATA_NOT_UPLOADED_STATUS
                            ||
                            $data['review_status'] == AccessDataEnums::DATA_REVIEW_REJECTED_STATUS
                        )
                    ) {
                        // 取数中
                        $process_status = AccessDataEnums::WORK_ORDER_PROCESS_STATUS_004;
                    } else {
                        if (
                            $data['audit_status'] == AccessDataEnums::AUDIT_APPROVE_STATUS
                            &&
                            $data['data_upload_status'] == AccessDataEnums::DATA_UPLOADED_STATUS
                            &&
                            $data['review_status'] == AccessDataEnums::DATA_NOT_REVIEWED_STATUS
                        ) {
                            // 待复核
                            $process_status = AccessDataEnums::WORK_ORDER_PROCESS_STATUS_005;
                        } else {
                            if (
                                $data['audit_status'] == AccessDataEnums::AUDIT_APPROVE_STATUS
                                &&
                                $data['review_status'] == AccessDataEnums::DATA_PASSED_REVIEW_STATUS
                            ) {
                                // 已完成且已复核
                                $process_status = AccessDataEnums::WORK_ORDER_PROCESS_STATUS_006;
                            } else {
                                if (
                                    $data['audit_status'] == AccessDataEnums::AUDIT_REJECT_STATUS
                                    &&
                                    (
                                        $data['audit_stage'] == AccessDataEnums::WORK_ORDER_AUDIT_STAGE_001
                                        ||
                                        $data['audit_stage'] == AccessDataEnums::WORK_ORDER_AUDIT_STAGE_003
                                    )
                                ) {
                                    // 部门审批驳回
                                    $process_status = AccessDataEnums::WORK_ORDER_PROCESS_STATUS_007;
                                } else {
                                    if ($data['audit_status'] == AccessDataEnums::AUDIT_REJECT_STATUS && $data['audit_stage'] == AccessDataEnums::WORK_ORDER_AUDIT_STAGE_005) {
                                        // 相关部门审批驳回
                                        $process_status = AccessDataEnums::WORK_ORDER_PROCESS_STATUS_008;
                                    } else {
                                        // 已提交 (其他默认)
                                        $process_status = AccessDataEnums::WORK_ORDER_AUDIT_STATUS_001;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        return $process_status;
    }

    /**
     * 计算工单详情页顶部进度状态(审批状态 + 审批阶段 + 复核状态)
     * 页面展示用
     *
     * @param array $data 取数工单数据 含审批状态, 审批阶段, 数据上传状态, 数据复核状态
     * @return mixed
     */
    public function calWorkOrderTopProcess(array $data)
    {
        // 默认已提交
        $top_process = [
            AccessDataEnums::DETAIL_PAGE_TOP_PROCESS_STATUS_001,
            AccessDataEnums::DETAIL_PAGE_TOP_PROCESS_STATUS_002,
        ];

        // 审批中 和 驳回的, 判断经过了哪些审批阶段
        if ($data['audit_status'] == AccessDataEnums::AUDIT_PENDING_STATUS || $data['audit_status'] == AccessDataEnums::AUDIT_REJECT_STATUS) {
            switch ($data['audit_stage']) {
                case AccessDataEnums::WORK_ORDER_AUDIT_STAGE_003:
                    $top_process[] = AccessDataEnums::DETAIL_PAGE_TOP_PROCESS_STATUS_003;
                    break;
                case AccessDataEnums::WORK_ORDER_AUDIT_STAGE_005:
                    $top_process[] = AccessDataEnums::DETAIL_PAGE_TOP_PROCESS_STATUS_003;
                    $top_process[] = AccessDataEnums::DETAIL_PAGE_TOP_PROCESS_STATUS_004;
                    break;
            }
        }

        // 数据处理中: 审批通过 + 复核未通过(待复核/驳回)
        if ($data['audit_status'] == AccessDataEnums::AUDIT_APPROVE_STATUS && $data['review_status'] != AccessDataEnums::DATA_PASSED_REVIEW_STATUS) {
            $top_process[] = AccessDataEnums::DETAIL_PAGE_TOP_PROCESS_STATUS_003;
            $top_process[] = AccessDataEnums::DETAIL_PAGE_TOP_PROCESS_STATUS_004;
            $top_process[] = AccessDataEnums::DETAIL_PAGE_TOP_PROCESS_STATUS_005;
        }

        // 完成: 复核完成
        if ($data['review_status'] == AccessDataEnums::DATA_PASSED_REVIEW_STATUS) {
            $top_process[] = AccessDataEnums::DETAIL_PAGE_TOP_PROCESS_STATUS_003;
            $top_process[] = AccessDataEnums::DETAIL_PAGE_TOP_PROCESS_STATUS_004;
            $top_process[] = AccessDataEnums::DETAIL_PAGE_TOP_PROCESS_STATUS_005;
            $top_process[] = AccessDataEnums::DETAIL_PAGE_TOP_PROCESS_STATUS_006;
        }

        // 完成: 审批驳回
        if ($data['audit_status'] == AccessDataEnums::AUDIT_REJECT_STATUS) {
            $top_process[] = AccessDataEnums::DETAIL_PAGE_TOP_PROCESS_STATUS_006;
        }

        return $top_process;
    }

    /**
     * 获取翻译
     *
     * @param string $key
     * @return mixed
     */
    public static function getTrans(string $key)
    {
        return self::$t[$key];
    }

    /**
     * 获取取数系统全员的姓名、部门等基本信息
     *
     * @return mixed
     */
    public function getDataSysAllStaffInfo()
    {
        try {
            // 用户基本信息
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['staff' => AccessDataStaffInfoModel::class]);
            $builder->leftJoin(AccessDataDepartmentModel::class, "staff.department_id = dept.id", "dept");
//            $builder->andWhere('staff.is_del = :s_is_del:', ['s_is_del' => 0]);
//            $builder->andWhere('dept.is_del = :d_is_del:', ['d_is_del' => 0]);

            $builder->columns([
                'staff.staff_id',
                'staff.staff_name',
                'staff.duty_flag',
                'dept.name AS department_name',
            ]);

            return $builder->getQuery()->execute()->toArray();
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->warning('取数工单系统 - 获取全员信息异常: ' . $e->getMessage());
        }

        return [];
    }

}
