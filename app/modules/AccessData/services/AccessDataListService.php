<?php

namespace App\Modules\AccessData\Services;

use App\Library\Enums;
use App\Library\AccessDataEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Modules\AccessData\Models\AccessDataBusinessLineModel;
use App\Modules\AccessData\Models\AccessDataDepartmentModel;
use App\Modules\AccessData\Models\AccessDataModel;
use App\Modules\AccessData\Models\AccessDataStaffInfoModel;
use App\Modules\Workflow\Models\WorkflowRequestNodeAuditorModel;

class AccessDataListService extends BaseService
{
    // 列表页 - 搜索条件
    public static $validate_list_search = [
        'work_order_no'    => 'StrLenGeLe:3,32|>>>:需求编号输入不合规',
        'apply_start_date' => 'date|>>>:开始日期不合规',
        'apply_end_date'   => 'date|>>>:结束日期不合规',
        'requirement_name' => 'StrLenGeLe:1,100|>>>:需求名称输入不合规',
        'manager_name'     => 'StrLenGeLe:1,100|>>>:负责人输入不合规',
        'submitter_name'   => 'StrLenGeLe:1,30|>>>:提交人输入不合规',
        'process_status'   => 'IntIn:1,2,3,4,5,6,7,8|>>>:进度选择不合规',
        'flag'             => 'IntIn:1,2|>>>:参数错误[flag]',
        'page'             => 'IntGe:1|>>>:参数错误[page]',
        'page_size'        => 'IntGe:1|>>>:参数错误[page_size]',
    ];

    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return AccessDataListService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 列表
     *
     * @param array $condition
     * @param int $uid
     * @param int $type
     * @param bool $if_download
     * @return array
     */
    public function getList(array $condition, int $uid = 0, int $type = 0, bool $if_download = false)
    {
        $condition['uid'] = $uid;
        $page_size        = empty($condition['page_size']) ? AccessDataEnums::PAGINATION_PER_PAGE_DEFAULT_LENGTH : $condition['page_size'];
        $page_num         = empty($condition['page']) ? AccessDataEnums::PAGINATION_DEFAULT_PAGE : $condition['page'];
        $offset           = $page_size * ($page_num - 1);

        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $data = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];

        try {
            // 不同类型的列表, 进行二次权限验证
            $user_data_sys_info = $this->getStaffDataSysInfo(['id' => $uid]);

            // 列表默认排序方式
            $list_order_method = 'main.submit_time DESC';

            // 展示N天内的数据
            $list_start_time = '';

            if ($type == self::LIST_TYPE_APPLY) {
                // 我的申请, 展示 30 天内的数据
                $list_start_time = $this->getBeforeSomeDaysTime(AccessDataEnums::LIST_DATA_MAX_VISIBLE_DAYS);
            } else {
                if ($type == self::LIST_TYPE_AUDIT) {
                    // 排序规则
                    switch ($user_data_sys_info['department_category']) {
                        // 需求部门审批
                        case AccessDataEnums::WORK_ORDER_AUDIT_STAGE_001:
                            $list_order_method = 'main.submit_time ASC';
                            break;
                        // 数据部门/相关部门审批
                        case AccessDataEnums::WORK_ORDER_AUDIT_STAGE_003:
                        case AccessDataEnums::WORK_ORDER_AUDIT_STAGE_005:
                            $list_order_method = 'main.expected_delivery_time ASC, main.submit_time ASC';
                            break;
                    }
                } else {
                    if ($type == self::LIST_TYPE_PROCESS) {
                        if ($condition['flag'] == 1) {
                            $list_order_method = 'main.expected_delivery_time ASC, main.submit_time ASC';
                        }
                    } else {
                        if ($type == self::LIST_TYPE_DATA) {
                            // 所有申请, 展示 90 天内的数据
                            $list_start_time = $this->getBeforeSomeDaysTime(AccessDataEnums::LIST_ALL_DATA_MAX_VISIBLE_DAYS);
                        }
                    }
                }
            }

            $builder = $this->modelsManager->createBuilder();
            $builder->columns([
                'distinct main.id',
                'main.work_order_no',
                'main.submit_time',
                'main.submitter_name',
                'main.submitter_department_name',
                'main.requirement_name',
                'main.first_business_line_name',
                'main.data_purpose',
                'main.expected_delivery_time',
                'main.audit_status',
                'main.audit_stage',
                'main.data_upload_status',
                'main.data_uploader_id',
                'main.review_status',
                'main.business_line_manager_name',
                'main.indicator_theme',
                'main.delivery_time',
            ]);
            $builder->from(['main' => AccessDataModel::class]);
            $builder->leftjoin(WorkflowRequestNodeAuditorModel::class,
                'request_node_auditor.biz_value = main.id AND request_node_auditor.biz_type = ' . Enums::WF_ACCESS_DATA_WORK_ORDER_TYPE,
                'request_node_auditor');

            if (!empty($list_start_time)) {
                $builder->where('main.submit_time >= :submit_time:', ['submit_time' => $list_start_time]);
            }

            $builder = $this->getCondition($builder, $condition, $type, $user_data_sys_info);
            $builder->orderBy($list_order_method);
            $count = $builder->getQuery()->execute()->count();
            if ($count > 0) {
                if (!$if_download) {
                    $builder->limit($page_size, $offset);
                }

                $items = $builder->getQuery()->execute()->toArray();

                $items = $this->handleItems($items);
            }

            $data['items']                     = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('取数系统 - 需求工单列表:' . $real_message);
        }

        // 无权限的情况,返回正常结构的空数据,按成功处理
        if ($code == ErrCode::$ACCESS_DATA_SYS_LIST_AUTH_FAIL_ERROR) {
            $code = ErrCode::$SUCCESS;
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 列表复合搜索条件
     *
     * @param $builder
     * @param $condition
     * @param $type
     * @param $user_data_sys_info
     * @return mixed
     * @throws BusinessException
     */
    private function getCondition($builder, $condition, $type, $user_data_sys_info)
    {
        $work_order_no              = $condition['work_order_no'] ?? '';
        $submitter_name             = $condition['submitter_name'] ?? '';
        $requirement_name           = $condition['requirement_name'] ?? '';
        $business_line_manager_name = $condition['manager_name'] ?? '';
        $process_status             = $condition['process_status'] ?? '';
        $apply_start_date           = !empty($condition['apply_start_date']) ? date('Y-m-d 0:0:0',
            strtotime($condition['apply_start_date'])) : '';
        $apply_end_date             = !empty($condition['apply_end_date']) ? date('Y-m-d 23:59:59',
            strtotime($condition['apply_end_date'])) : '';

        // 处理标识: 1 待处理, 2 已完成
        $flag = in_array($condition['flag'] ?? 0, [1, 2]) ? $condition['flag'] : 1;
        if ($type == self::LIST_TYPE_APPLY) {
            // 申请列表: 当前用户申请的
            $builder->andWhere('main.submitter_id = :uid:', ['uid' => $condition['uid']]);
        } else {
            if ($type == self::LIST_TYPE_AUDIT) {
                // 审核列表: 需当前用户待审核的
                // 终审: 审批中
                $builder->andWhere('main.audit_status = :approval_status:',
                    ['approval_status' => Enums::WF_STATE_PENDING]);

                // 需求部门审批阶段: 只审批该部门的
                if ($user_data_sys_info['department_category'] == AccessDataEnums::WORK_ORDER_AUDIT_STAGE_001) {
                    $builder->andWhere('main.submitter_department_id = :submitter_department_id:',
                        ['submitter_department_id' => $user_data_sys_info['department_id']]);
                }

                // 审批人是当前用户
                $builder->andWhere('request_node_auditor.auditor_id = :rn_auditor_id:',
                    ['rn_auditor_id' => $condition['uid']]);

                if ($flag == 1) {
                    // 待处理: 当前节点待审核
                    $audit_action_item = [
                        Enums::WF_STATE_PENDING,
                    ];
                } else {
                    if ($flag == 2) {
                        // 已处理: 当前节点审核通过 或 驳回
                        $audit_action_item = [
                            Enums::WF_STATE_APPROVED,
                            Enums::WF_STATE_REJECTED,
                        ];
                    }
                }

                $builder->inWhere('request_node_auditor.audit_status', $audit_action_item ?? [Enums::WF_STATE_PENDING]);
            } else {
                if ($type == self::LIST_TYPE_PROCESS) {
                    // 数据部门处理列表: 待处理 待完成
                    // 终审通过
                    $builder->andWhere('main.audit_status = :approval_status:',
                        ['approval_status' => Enums::WF_STATE_APPROVED]);

                    if ($flag == 1) {
                        // 待处理
                        $builder->andWhere(
                            'main.review_status != :review_status:',
                            [
                                'review_status' => AccessDataEnums::DATA_PASSED_REVIEW_STATUS,
                            ]
                        );
                    } else {
                        if ($flag == 2) {
                            // 已完成
                            $builder->andWhere(
                                'main.data_upload_status = :data_upload_status: AND main.review_status = :review_status:',
                                [
                                    'data_upload_status' => AccessDataEnums::DATA_UPLOADED_STATUS,
                                    'review_status'      => AccessDataEnums::DATA_PASSED_REVIEW_STATUS,
                                ]
                            );
                        }
                    }
                }
            }
        }

        if (!empty($apply_start_date)) {
            $builder->andWhere('main.submit_time >= :apply_start_date:', ['apply_start_date' => $apply_start_date]);
        }

        if (!empty($apply_end_date)) {
            $builder->andWhere('main.submit_time <= :apply_end_date:', ['apply_end_date' => $apply_end_date]);
        }

        if (!empty($work_order_no)) {
            $builder->andWhere('main.work_order_no LIKE :work_order_no:', ['work_order_no' => "%$work_order_no%"]);
        }

        if (!empty($submitter_name)) {
            $builder->andWhere('main.submitter_name LIKE :create_word:', ['create_word' => "%$submitter_name%"]);
        }

        if (!empty($requirement_name)) {
            $builder->andWhere('main.requirement_name LIKE :requirement_name:',
                ['requirement_name' => "%$requirement_name%"]);
        }

        if (!empty($business_line_manager_name)) {
            $builder->andWhere('main.business_line_manager_name LIKE :business_line_manager_name:',
                ['business_line_manager_name' => "%$business_line_manager_name%"]);
        }

        if (!empty($process_status)) {
            // 根据业务进度, 拆分成数据的原子状态
            switch ($process_status) {
                case AccessDataEnums::WORK_ORDER_PROCESS_STATUS_001:
                    //已提交(审批中 + 审批阶段是需求部门)
                    $builder->andWhere(
                        'main.audit_status = :s_audit_status: AND main.audit_stage = :s_audit_stage:',
                        [
                            's_audit_status' => Enums::WF_STATE_PENDING,
                            's_audit_stage'  => AccessDataEnums::WORK_ORDER_AUDIT_STAGE_001,
                        ]
                    );

                    break;
                case AccessDataEnums::WORK_ORDER_PROCESS_STATUS_002:
                    //部门审批通过(审批中 + 审批阶段是数据部门)
                    $builder->andWhere(
                        'main.audit_status = :s_audit_status: AND main.audit_stage = :s_audit_stage:',
                        [
                            's_audit_status' => Enums::WF_STATE_PENDING,
                            's_audit_stage'  => AccessDataEnums::WORK_ORDER_AUDIT_STAGE_003,
                        ]
                    );

                    break;
                case AccessDataEnums::WORK_ORDER_PROCESS_STATUS_003:
                    //审批中(审批中 + 审批阶段是相关部门)
                    $builder->andWhere(
                        'main.audit_status = :s_audit_status: AND main.audit_stage = :s_audit_stage:',
                        [
                            's_audit_status' => Enums::WF_STATE_PENDING,
                            's_audit_stage'  => AccessDataEnums::WORK_ORDER_AUDIT_STAGE_005,
                        ]
                    );

                    break;
                case AccessDataEnums::WORK_ORDER_PROCESS_STATUS_004:
                    //取数中(审批通过 + 数据未上传 | 复核驳回)
                    $builder->andWhere(
                        'main.audit_status = :s_audit_status: AND (main.data_upload_status = :s_data_upload_status: OR main.review_status = :s_review_status:)',
                        [
                            's_audit_status'       => Enums::WF_STATE_APPROVED,
                            's_data_upload_status' => AccessDataEnums::DATA_NOT_UPLOADED_STATUS,
                            's_review_status'      => AccessDataEnums::DATA_REVIEW_REJECTED_STATUS,
                        ]
                    );

                    break;
                case AccessDataEnums::WORK_ORDER_PROCESS_STATUS_005:
                    //待复核(审批通过 + 数据已上传 + 待复核)
                    $builder->andWhere(
                        'main.audit_status = :s_audit_status: AND main.data_upload_status = :s_data_upload_status: AND main.review_status = :s_review_status:',
                        [
                            's_audit_status'       => Enums::WF_STATE_APPROVED,
                            's_data_upload_status' => AccessDataEnums::DATA_UPLOADED_STATUS,
                            's_review_status'      => AccessDataEnums::DATA_NOT_REVIEWED_STATUS,
                        ]
                    );

                    break;
                case AccessDataEnums::WORK_ORDER_PROCESS_STATUS_006:
                    //已完成且已复核(审批通过 + 复核通过)
                    $builder->andWhere(
                        'main.audit_status = :s_audit_status: AND main.review_status = :s_review_status:',
                        [
                            's_audit_status'  => Enums::WF_STATE_APPROVED,
                            's_review_status' => AccessDataEnums::DATA_PASSED_REVIEW_STATUS,
                        ]
                    );

                    break;
                case AccessDataEnums::WORK_ORDER_PROCESS_STATUS_007:
                    //部门审批驳回(审批驳回 + 审批阶段是需求部门 | 数据部门)
                    $builder->andWhere(
                        'main.audit_status = :s_audit_status: AND (main.audit_stage = :audit_stage_1: OR main.audit_stage = :audit_stage_2:)',
                        [
                            's_audit_status' => Enums::WF_STATE_REJECTED,
                            'audit_stage_1'  => AccessDataEnums::WORK_ORDER_AUDIT_STAGE_001,
                            'audit_stage_2'  => AccessDataEnums::WORK_ORDER_AUDIT_STAGE_003,
                        ]
                    );

                    break;
                case AccessDataEnums::WORK_ORDER_PROCESS_STATUS_008:
                    //相关部门审批驳回(审批驳回 + 审批阶段是相关部门)
                    $builder->andWhere(
                        'main.audit_status = :s_audit_status: AND main.audit_stage = :s_audit_stage:',
                        [
                            's_audit_status' => Enums::WF_STATE_REJECTED,
                            's_audit_stage'  => AccessDataEnums::WORK_ORDER_AUDIT_STAGE_005,
                        ]
                    );

                    break;
            }
        }

        return $builder;
    }

    /**
     * 列表数据格式处理
     *
     * @param array $items
     * @return array
     */
    private function handleItems(array $items)
    {
        if (empty($items)) {
            return [];
        }

        // 提取处理人
        $staff_list = AccessDataStaffInfoModel::find([
            'columns' => ['staff_id', 'staff_name'],
        ])->toArray();
        $staff_list = $staff_list ? array_column($staff_list, 'staff_name', 'staff_id') : [];

        foreach ($items as &$item) {
            // 处理人
            $item['data_uploader_name'] = $staff_list[$item['data_uploader_id']] ?? '';

            // 数据用途
            $item['data_purpose_text'] = isset(AccessDataEnums::$data_purpose_item[$item['data_purpose']]) ? static::$t[AccessDataEnums::$data_purpose_item[$item['data_purpose']]] : '';

            // 工单状态归并
            $item['work_order_status']      = $this->workOrderStatusMerge($item);
            $item['work_order_status_text'] = isset(AccessDataEnums::$work_order_audit_status_item[$item['work_order_status']]) ? static::$t[AccessDataEnums::$work_order_audit_status_item[$item['work_order_status']]] : '';

            // 进度状态归并
            $item['work_order_process_status'] = $this->workOrderProcessMerge($item);
            $item['work_order_process_text']   = isset(AccessDataEnums::$work_order_process_status_item[$item['work_order_process_status']]) ? static::$t[AccessDataEnums::$work_order_process_status_item[$item['work_order_process_status']]] : '';

            // 指标主题
            $item['indicator_theme_text'] = isset(AccessDataEnums::$indicator_theme_item[$item['indicator_theme']]) ? static::$t[AccessDataEnums::$indicator_theme_item[$item['indicator_theme']]] : '';

            unset($item['data_purpose']);
            unset($item['data_uploader_id']);
            unset($item['indicator_theme']);
            unset($item['data_upload_status']);
            unset($item['review_status']);
            unset($item['audit_status']);
            unset($item['audit_stage']);
        }

        return $items;
    }

    /**
     * 员工列表
     *
     * @param array $params
     * @param int $uid
     * @return array
     */
    public function getStaffList(array $params, int $uid = 0)
    {
        $page_size = empty($params['pageSize']) ? AccessDataEnums::PAGINATION_PER_PAGE_DEFAULT_LENGTH : $params['pageSize'];
        $page_num  = empty($params['pageNum']) ? AccessDataEnums::PAGINATION_DEFAULT_PAGE : $params['pageNum'];
        $offset    = $page_size * ($page_num - 1);

        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $data = [
            'items'      => [],
            'pagination' => [
                'pageNum'     => $page_num,
                'pageSize'    => $page_size,
                'total_count' => 0,
            ],
        ];

        try {
            $builder = $this->modelsManager->createBuilder();
            $builder->columns([
                'staff.staff_id',
                'staff.staff_name',
                'dept.name AS department_name',
                'staff.business_line_permission',
                'staff.duty_flag',
                'staff.create_time',
            ]);
            $builder->from(['staff' => AccessDataStaffInfoModel::class]);
            $builder->leftjoin(AccessDataDepartmentModel::class, 'staff.department_id = dept.id', 'dept');
            if (!empty($params['staff_id'])) {
                $builder->andWhere('staff.staff_id = :staff_id:', ['staff_id' => $params['staff_id']]);
            }
            $builder->andWhere('staff.is_del = 0');
            $builder->andWhere('dept.is_del = 0');

            $builder->orderBy('staff.staff_id DESC');
            $count = $builder->getQuery()->execute()->count();
            if ($count) {
                $builder->limit($page_size, $offset);
                $items = $builder->getQuery()->execute()->toArray();

                // 获取业务线列表
                $business_line_item = AccessDataBusinessLineModel::find([
                    'columns' => ['id', 'name', 'parent_id'],
                ])->toArray();
                $business_line_item = $business_line_item ? array_column($business_line_item, null, 'id') : [];
                foreach ($items as $k => $v) {
                    // 员工属性
                    $v['duty_flag'] = self::$t['access_data_duty_flag_' . $v['duty_flag']];

                    // 添加时间: 东八区
                    $v['create_time'] = date('Y-m-d H:i:s', strtotime($v['create_time']) + 8 * 3600);

                    // 业务线权限
                    if (!empty($v['business_line_permission'])) {
                        $staff_business_lines = explode(',', $v['business_line_permission']);

                        $_business_line_permission = [];
                        foreach ($staff_business_lines as $line_id) {
                            $line_info = $business_line_item[$line_id] ?? [];
                            if (!empty($line_info) && $line_info['parent_id']) {
                                $parent_id_info              = $business_line_item[$line_info['parent_id']] ?? [];
                                $parent_id_name              = $parent_id_info['name'] ?? '';
                                $_business_line_permission[] = $parent_id_name . '-' . $line_info['name'];
                            }
                        }

                        $v['business_line_permission'] = implode('、', $_business_line_permission);
                    }

                    $items[$k] = $v;
                }
            }

            $data['items']                     = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('取数系统 - 员工权限配置列表:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

}
