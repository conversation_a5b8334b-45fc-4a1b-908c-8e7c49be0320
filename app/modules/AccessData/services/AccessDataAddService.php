<?php

namespace App\Modules\AccessData\Services;

use App\Library\AccessDataEnums;
use App\Library\Enums;
use App\Modules\AccessData\Models\AccessDataFormModel;
use App\Modules\User\Models\AttachModel;
use App\Util\RedisKey;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Modules\AccessData\Models\AccessDataModel;
use App\Modules\AccessData\Models\PaymentStoreRentingDetail;
use App\Modules\User\Models\StaffInfoModel;

class AccessDataAddService extends BaseService
{
    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return AccessDataAddService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 申请提交
     *
     * @param array $data
     * @param array $user
     * @return array
     */
    public function applySubmit(array $data, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $db = $this->getDI()->get('db_oa');
        $db->begin();

        try {
            // 生成取数工单编号
            $work_order_no = static::genSerialNo(AccessDataEnums::DISTRIBUTED_NO_PREFIX,
                RedisKey::ACCESS_DATA_WORK_ORDER_COUNTER);

            // 该申请编号是否存在
            $exists = AccessDataModel::getFirst([
                'conditions' => 'work_order_no = ?1',
                'columns'    => 'id',
                'bind'       => [1 => $work_order_no],
            ]);

            if (!empty($exists)) {
                throw new ValidationException(self::$t['access_data_work_order_no_existed'], ErrCode::$VALIDATE_ERROR);
            }

            // 表单项
            $form_item = $data['form_item'];

            // 重构主表待入库数据
            $data['work_order_no'] = $work_order_no;
            $apply_data            = $this->buildData($data, $user);

            // [3] 入库
            // [3.1] 主表
            $main_model = new AccessDataModel();
            $bool       = $main_model->i_create($apply_data);

            if ($bool === false) {
                throw new BusinessException('取数需求工单申请创建失败 = ' . json_encode($apply_data, JSON_UNESCAPED_UNICODE),
                    ErrCode::$CONTRACT_CREATE_ERROR);
            }

            // [3.2] 表单项
            // [3.2.1] 附件提取
            $form_item_attaches = [];
            foreach ($form_item as $item) {
                $form_item_model = new AccessDataFormModel();
                $_tmp_form       = [
                    'access_data_id'       => $main_model->id,
                    'header_ddl'           => $item['header_ddl'],
                    'estimate_data_volume' => $item['estimate_data_volume'],
                    'create_time'          => date('Y-m-d H:i:s'),
                    'update_time'          => date('Y-m-d H:i:s'),
                ];
                $bool            = $form_item_model->save($_tmp_form);
                if ($bool === false) {
                    throw new BusinessException('取数需求表单创建失败 = ' . json_encode($item, JSON_UNESCAPED_UNICODE),
                        ErrCode::$CONTRACT_CREATE_ERROR);
                }

                if (!empty($item['attachment'][0])) {
                    $file                 = $item['attachment'][0];
                    $form_item_attaches[] = [
                        'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_ACCESS_DATA_FORM,
                        'oss_bucket_key'  => $form_item_model->id,
                        'bucket_name'     => $file['bucket_name'],
                        'object_key'      => $file['object_key'],
                        'file_name'       => $file['file_name'],
                    ];
                }
            }

            // [3.2.2] 表单附件创建
            if (!empty($form_item_attaches)) {
                $attach      = new AttachModel();
                $attach_bool = $attach->batchInsert($form_item_attaches);
                if ($attach_bool === false) {
                    throw new BusinessException('取数工单申请 - 表单附件创建失败 = ' . json_encode($form_item_attaches,
                            JSON_UNESCAPED_UNICODE), ErrCode::$ACCESS_DATA_SYS_APPLY_FORM_ATTACHE_ERROR);
                }
            }

            // [3.3] 注入审批流
            $flow_bool = (new AccessDataFlowService())->createRequest($main_model->id, $user);
            if ($flow_bool === false) {
                throw new BusinessException('取数需求工单审批流创建失败', ErrCode::$ACCESS_DATA_SYS_CREATE_WORK_FLOW_ERROR);
            }

            $db->commit();
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('取数工单系统: 工单申请提交异常: ' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => [],
        ];
    }

    /**
     * 入库申请数据
     *
     * @param array $data
     * @param array $user
     * @return array
     */
    private function buildData(array $data, array $user)
    {
        if (empty($data)) {
            return [];
        }

        $apply_data = [];

        $apply_data['work_order_no']             = $data['work_order_no'];
        $apply_data['requester_id']              = $data['requester_id'] ?? 0;
        $apply_data['requester_name']            = $data['requester_name'] ?? '';
        $apply_data['first_business_line_id']    = $data['first_business_line_id'];
        $apply_data['second_business_line_id']   = $data['second_business_line_id'];
        $apply_data['first_business_line_name']  = $data['first_business_line_name'];
        $apply_data['second_business_line_name'] = $data['second_business_line_name'];

        $apply_data['requirement_name']       = $data['requirement_name'];
        $apply_data['requirement_background'] = $data['requirement_background'];
        $apply_data['requirement_detail']     = $data['requirement_detail'];

        $apply_data['data_purpose']           = $data['data_purpose'];
        $apply_data['confidential_scope']     = implode(',',
            array_filter(array_unique($data['confidential_scope_item'])));
        $apply_data['expected_delivery_time'] = $data['expected_delivery_time'];

        $apply_data['submit_time'] = date('Y-m-d H:i:s');
        $apply_data['update_time'] = date('Y-m-d H:i:s');
        $apply_data['updater_id']  = $user['id'];

        // 获取提交人数据系统基本信息
        $submitter_data_sys_info                     = $this->getStaffDataSysInfo($user);
        $apply_data['submitter_id']                  = $submitter_data_sys_info['staff_id'] ?? 0;
        $apply_data['submitter_name']                = $submitter_data_sys_info['staff_name'] ?? '';
        $apply_data['submitter_department_id']       = $submitter_data_sys_info['department_id'] ?? 0;
        $apply_data['submitter_department_name']     = $submitter_data_sys_info['department_name'] ?? '';
        $apply_data['submitter_department_category'] = $submitter_data_sys_info['department_category'] ?? '';

        // 当提交人是需求部门负责人时, 审批阶段需置为数据部门(因为需求部门审批节点会自动跳过)
        if ($submitter_data_sys_info['duty_flag'] && $submitter_data_sys_info['department_category'] == AccessDataEnums::WORK_ORDER_AUDIT_STAGE_001) {
            $apply_data['audit_stage'] = AccessDataEnums::WORK_ORDER_AUDIT_STAGE_003;
        }

        // 一级业务线负责人id和姓名
        $first_business_line_info                 = $this->getBusinessLineAndManagerInfo($apply_data['first_business_line_id']);
        $apply_data['business_line_manager_id']   = $first_business_line_info['data_manager_id'] ?? 0;
        $apply_data['business_line_manager_name'] = $first_business_line_info['data_manager_name'] ?? '';

        return $apply_data;
    }

    /**
     * 获取申请创建页 - 基本信息
     *
     * @param array $user
     * @return array
     */
    public function getCreatePageBaseInfo(array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $data = [];

        try {
            if (empty($user)) {
                throw new \Exception("not found user");
            }

            // 用户基本信息
            $user_info = $this->getStaffDataSysInfo($user);

            // 获取用户业务线
            if (!empty($user_info)) {
                $business_lines = $this->getBusinessLineList($user_info['business_line_permission']);

                unset($user_info['duty_flag']);
                unset($user_info['business_line_permission']);
                unset($user_info['department_category']);
            }

            $data = [
                'process_status'                 => $this->getDetailTopProcess([1]),
                'submitter_info'                 => $user_info ?? [],
                'business_line_list'             => $business_lines ?? [],
                'expected_delivery_time_setting' => $this->calExpectedDeliveryTimeSetting(),
            ];
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('取数工单系统, 获取申请页默认值异常: ' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    // 计算默认的期望交付时间 和 可选的开始时间
    protected function calExpectedDeliveryTimeSetting()
    {
        $data = [];

        // 当前时间
        $curr_time = gmdate('Y-m-d H:i:s', time() + 8 * 3600);

        // 当天周几 [0 - 6 对应 周日 - 周六]
        $curr_week_day = gmdate('w', time() + 8 * 3600);

        // 周日标识
        $sunday_flag = 0;

        // 当天的判断时间线
        $curr_day_time_line = gmdate('Y-m-d 20:30:00', time() + 8 * 3600);

        // 期望交付时间: 默认值
        if ($curr_week_day == $sunday_flag) {
            $data['default_expected_delivery_time'] = date('Y-m-d H:i:s', strtotime($curr_time) + 2 * 86400);
        } else {
            $data['default_expected_delivery_time'] = date('Y-m-d H:i:s', strtotime($curr_time) + 86400);
        }

        // 期望交付时间: 可选时间段的开始时间
        if ($curr_time >= $curr_day_time_line) {
            $data['min_expected_delivery_time'] = date('Y-m-d 9:30:00', strtotime($curr_time) + 86400);
        } else {
            $data['min_expected_delivery_time'] = $curr_time;
        }

        return $data;
    }

    /**
     * 员工搜索
     *
     * @param string $search_word
     * @return mixed
     */
    public function getSysStaff(string $search_word)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $data = [];

        try {
            if (empty($search_word)) {
                throw new ValidationException(self::$t['access_data_request_param_error'] . '[search_word]',
                    ErrCode::$VALIDATE_ERROR);
            }

            // 精确搜索系统员工
            $data = StaffInfoModel::find([
                'conditions' => '(id = :id: OR name = :name:) AND formal = :formal: 
                    AND state = :state: AND is_sub_staff = :is_sub_staff: 
                    AND virtual_number_enabled = :virtual_number_enabled:',
                'bind'       => [
                    'id'                     => $search_word,
                    'name'                   => $search_word,
                    'formal'                 => 1,
                    'state'                  => 1,
                    'is_sub_staff'           => 0,
                    'virtual_number_enabled' => 0,
                ],
                'columns'    => ['id AS staff_id', 'name AS staff_name'],
            ])->toArray();
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('取数系统 - 需求人检索异常: ' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }
}
