<?php
/**
 * 取数工单系统 - 2021.03
 */

namespace App\Modules\AccessData\Controllers;

use App\Library\ErrCode;
use App\Library\AccessDataEnums;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\AccessData\Services\BaseService;
use App\Modules\AccessData\Services\AccessDataAddService;
use App\Modules\AccessData\Services\AccessDataDetailService;
use App\Modules\AccessData\Services\AccessDataListService;
use App\Modules\AccessData\Services\AccessDataUpdateService;
use App\Modules\AccessData\Services\AccessDataFlowService;
use Exception;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class AccessDataController extends BaseController
{
    /**
     * 获取配置项静态数据(枚举类)
     *
     * @Token
     * @return mixed
     */
    public function getStaticSettingAction()
    {
        $data = BaseService::getAllEnumsSetting();
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }

    /**
     * 创建页 - 基本信息 默认值
     * @Permission(action='access_data_sys.apply')
     *
     * @return mixed
     * @throws ValidationException
     */
    public function getCreatePageDefaultDataAction()
    {
        // 用户权限校验
        $this->checkUserModuleAccessPermissionInfo(AccessDataEnums::MODULE_TYPE_APPLY);

        $res = AccessDataAddService::getInstance()->getCreatePageBaseInfo($this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 创建页 - 需求人检检索
     * @Permission(action='access_data_sys.apply')
     *
     * @return mixed
     * @throws ValidationException
     */
    public function searchStaffInfoAction()
    {
        $data = $this->request->get();

        $data = BaseService::handleParams($data, BaseService::$not_must_params);
        Validation::validate($data, BaseService::$validate_staff_search_param);

        // 用户权限校验
        $this->checkUserModuleAccessPermissionInfo(AccessDataEnums::MODULE_TYPE_APPLY);

        $res = AccessDataAddService::getInstance()->getSysStaff($data['search_word']);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 申请创建
     * @Permission(action='access_data_sys.apply')
     *
     * @return mixed
     * @throws Exception
     */
    public function createAction()
    {
        $data = $this->request->get();

        $data = BaseService::handleParams($data, BaseService::$not_must_params);
        $this->logger->info('取数系统 - 申请提交 - 请求参数: ' . json_encode($data, JSON_UNESCAPED_UNICODE));

        // 去除数组元素两侧空白字符
        $data = trim_array($data);

        // 校验表单列表参数格式
        if (!is_array($data['form_item'])) {
            $data['form_item'] = json_decode($data['form_item'], true);
            if (json_last_error() != JSON_ERROR_NONE) {
                throw new ValidationException(BaseService::getTrans('access_data_request_param_error') . '[form_item]',
                    ErrCode::$VALIDATE_ERROR);
            }
        }

        // 校验数据涉密范围参数格式
        if (!is_array($data['confidential_scope_item'])) {
            $data['confidential_scope_item'] = json_decode($data['confidential_scope_item'], true);

            if (json_last_error() != JSON_ERROR_NONE) {
                throw new ValidationException(BaseService::getTrans('access_data_request_param_error') . '[confidential_scope_item]',
                    ErrCode::$VALIDATE_ERROR);
            }
        }

        Validation::validate($data, BaseService::$validate_apply_param);

        // 用户权限校验
        $this->checkUserModuleAccessPermissionInfo(AccessDataEnums::MODULE_TYPE_APPLY);


        $lock_key = md5('access_data_sys_apply_submit_' . $this->user['id']);
        $res      = $this->atomicLock(function () use ($data) {
            return AccessDataAddService::getInstance()->applySubmit($data, $this->user);
        }, $lock_key);

        if (isset($res['code'])) {
            $code    = $res['code'];
            $message = $res['message'];
            $data    = $res['data'];
        } else {
            $code    = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
            $data    = [];
        }

        return $this->returnJson($code, $message, $data);
    }

    /**
     * 申请列表
     * @Permission(action='access_data_sys.apply')
     *
     * @return mixed
     * @throws ValidationException
     */
    public function applyListAction()
    {
        $params = $this->request->get();

        $params = BaseService::handleParams($params, BaseService::$not_must_params);
        Validation::validate($params, AccessDataListService::$validate_list_search);

        // 用户权限校验
        $this->checkUserModuleAccessPermissionInfo(AccessDataEnums::MODULE_TYPE_APPLY);

        $res = AccessDataListService::getInstance()->getList($params, $this->user['id'],
            AccessDataListService::LIST_TYPE_APPLY);

        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 申请详情
     * @Permission(action='access_data_sys.apply')
     *
     * @return mixed
     * @throws ValidationException
     */
    public function applyDetailAction()
    {
        $data = $this->request->get();

        Validation::validate($data, BaseService::$validate_detail_param);

        // 用户权限校验
        $this->checkUserModuleAccessPermissionInfo(AccessDataEnums::MODULE_TYPE_APPLY);

        $res = AccessDataDetailService::getInstance()->getAuditDetail($data['id'], $this->user['id'],
            AccessDataEnums::MODULE_TYPE_APPLY);

        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 待审批列表
     * @Permission(action='access_data_sys.audit')
     *
     * @return mixed
     * @throws ValidationException
     */
    public function auditListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, BaseService::$not_must_params);

        Validation::validate($params, AccessDataListService::$validate_list_search);

        // 用户权限校验
        $this->checkUserModuleAccessPermissionInfo(AccessDataEnums::MODULE_TYPE_AUDIT);

        $res = AccessDataListService::getInstance()->getList($params, $this->user['id'],
            AccessDataListService::LIST_TYPE_AUDIT);

        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }


    /**
     * 审批详情
     * @Permission(action='access_data_sys.audit')
     *
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function auditDetailAction()
    {
        $data = $this->request->get();

        Validation::validate($data, BaseService::$validate_detail_param);

        // 用户权限校验
        $this->checkUserModuleAccessPermissionInfo(AccessDataEnums::MODULE_TYPE_AUDIT);

        $res = AccessDataDetailService::getInstance()->getAuditDetail($data['id'], $this->user['id'],
            AccessDataEnums::MODULE_TYPE_AUDIT);

        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 处理列表
     * @Permission(action='access_data_sys.process')
     *
     * @return mixed
     * @throws ValidationException
     */
    public function processListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, BaseService::$not_must_params);

        Validation::validate($params, AccessDataListService::$validate_list_search);

        // 用户权限校验
        $this->checkUserModuleAccessPermissionInfo(AccessDataEnums::MODULE_TYPE_PROCESS);

        $res = AccessDataListService::getInstance()->getList($params, $this->user['id'],
            AccessDataListService::LIST_TYPE_PROCESS);

        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 处理详情
     * @Permission(action='access_data_sys.process')
     *
     * @return mixed
     * @throws ValidationException
     */
    public function processDetailAction()
    {
        $data = $this->request->get();
        Validation::validate($data, BaseService::$validate_detail_param);

        // 用户权限校验
        $this->checkUserModuleAccessPermissionInfo(AccessDataEnums::MODULE_TYPE_PROCESS);

        $res = AccessDataDetailService::getInstance()->getAuditDetail($data['id'], $this->user['id'],
            AccessDataEnums::MODULE_TYPE_PROCESS);

        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 处理
     * @Permission(action='access_data_sys.process')
     *
     * @return mixed
     * @throws ValidationException
     */
    public function processAction()
    {
        $params = $this->request->get();

        $this->logger->info('取数系统 - 处理[上传数据] - 请求参数: ' . json_encode($params, JSON_UNESCAPED_UNICODE));
        $this->logger->info('取数系统 - 处理[上传数据] - 用户登录信息: ' . json_encode($this->user, JSON_UNESCAPED_UNICODE));

        $params = BaseService::handleParams($params, BaseService::$not_must_params);

        // 去除数组元素两侧空白字符
        $params = trim_array($params);

        if (!empty($params['data_file']) && !is_array($params['data_file'])) {
            $params['data_file'] = json_decode($params['data_file'], true);

            if (json_last_error() != JSON_ERROR_NONE) {
                throw new ValidationException(BaseService::getTrans('access_data_request_param_error') . '[data_file]',
                    ErrCode::$VALIDATE_ERROR);
            }
        }

        if (!empty($params['sql_file']) && !is_array($params['sql_file'])) {
            $params['sql_file'] = json_decode($params['sql_file'], true);

            if (json_last_error() != JSON_ERROR_NONE) {
                throw new ValidationException(BaseService::getTrans('access_data_request_param_error') . '[sql_file]',
                    ErrCode::$VALIDATE_ERROR);
            }
        }

        Validation::validate($params, BaseService::$validate_data_upload);

        // 用户权限校验
        $this->checkUserModuleAccessPermissionInfo(AccessDataEnums::MODULE_TYPE_PROCESS);

        $res = AccessDataUpdateService::getInstance()->uploadDataFile($params, $this->user);

        $this->logger->info('取数系统 - 处理[上传数据] - 响应结果: ' . json_encode($res, JSON_UNESCAPED_UNICODE));

        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 复核
     * @Permission(action='access_data_sys.process')
     *
     * @return mixed
     * @throws ValidationException
     */
    public function reviewAction()
    {
        $params = $this->request->get();

        $this->logger->info('取数系统 - 复核操作 - 请求参数: ' . json_encode($params, JSON_UNESCAPED_UNICODE));
        $this->logger->info('取数系统 - 复核操作 - 用户登录信息: ' . json_encode($this->user, JSON_UNESCAPED_UNICODE));

        $params = BaseService::handleParams($params, BaseService::$not_must_params);

        // 去除数组元素两侧空白字符
        $params = trim_array($params);

        Validation::validate($params, BaseService::$validate_review);

        // 用户权限校验
        $this->checkUserModuleAccessPermissionInfo(AccessDataEnums::MODULE_TYPE_PROCESS);

        $res = AccessDataUpdateService::getInstance()->dataReview($params, $this->user);

        $this->logger->info('取数系统 - 复核操作 - 响应结果: ' . json_encode($res, JSON_UNESCAPED_UNICODE));

        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 审核通过
     * @Permission(action='access_data_sys.audit')
     */
    public function approveAction()
    {
        $params = $this->request->get();

        // 请求参数
        $this->logger->info('取数系统 - 审批通过 - 请求参数: ' . json_encode($params, JSON_UNESCAPED_UNICODE));
        $this->logger->info('取数系统 - 审批通过 - 用户登录信息: ' . json_encode($this->user, JSON_UNESCAPED_UNICODE));

        $params = BaseService::handleParams($params, BaseService::$not_must_params);

        // 去除数组元素两侧空白字符
        $params = trim_array($params);

        // 用户权限校验
        $user_data_sys_info = $this->checkUserModuleAccessPermissionInfo(AccessDataEnums::MODULE_TYPE_AUDIT);

        // 需求部门审批
        if ($user_data_sys_info['department_category'] == AccessDataEnums::WORK_ORDER_AUDIT_STAGE_001) {
            Validation::validate($params, BaseService::$validate_requirement_department_approve);
        } else {
            if ($user_data_sys_info['department_category'] == AccessDataEnums::WORK_ORDER_AUDIT_STAGE_003) {
                // 数据部门审批

                // 数据涉密范围
                if (!empty($params['confidential_scope_item']) && !is_array($params['confidential_scope_item'])) {
                    $params['confidential_scope_item'] = json_decode($params['confidential_scope_item'], true);
                    if (json_last_error() != JSON_ERROR_NONE) {
                        throw new ValidationException(BaseService::getTrans('access_data_request_param_error') . '[confidential_scope_item]',
                            ErrCode::$VALIDATE_ERROR);
                    }
                }

                // 转交部门id
                if (!empty($params['transfer_department_id_item']) && !is_array($params['transfer_department_id_item'])) {
                    $params['transfer_department_id_item'] = json_decode($params['transfer_department_id_item'], true);
                    if (json_last_error() != JSON_ERROR_NONE) {
                        throw new ValidationException(BaseService::getTrans('access_data_request_param_error') . '[transfer_department_id_item]',
                            ErrCode::$VALIDATE_ERROR);
                    }
                }

                Validation::validate($params, BaseService::$validate_data_department_approve);

                $update_data = [
                    'confidential_scope_item'     => $params['confidential_scope_item'],
                    'transfer_department_id_item' => $params['transfer_department_id_item'],
                ];
            } else {
                if ($user_data_sys_info['department_category'] == AccessDataEnums::WORK_ORDER_AUDIT_STAGE_005) {
                    // 相关部门审批
                    Validation::validate($params, BaseService::$validate_related_department_approve);

                    // 相关部门的审批备注
                    $note = $params['note'] ?? '';
                } else {
                    throw new ValidationException(BaseService::getTrans('access_data_user_department_flag_error') . '[uid:' . $this->user['id'] . ']',
                        ErrCode::$VALIDATE_ERROR);
                }
            }
        }

        $res = (new AccessDataFlowService())->approve($params['id'], $note ?? '', $this->user, $update_data ?? []);
        $this->logger->info('取数系统 - 审批通过 - 响应结果: ' . json_encode($res, JSON_UNESCAPED_UNICODE));

        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 审核驳回
     * @Permission(action='access_data_sys.audit')
     */
    public function rejectAction()
    {
        $params = $this->request->get();

        // 请求参数
        $this->logger->info('取数系统 - 审批驳回 - 请求参数: ' . json_encode($params, JSON_UNESCAPED_UNICODE));
        $this->logger->info('取数系统 - 审批驳回 - 用户登录信息: ' . json_encode($this->user, JSON_UNESCAPED_UNICODE));

        $params = BaseService::handleParams($params, BaseService::$not_must_params);

        // 去除数组元素两侧空白字符
        $params = trim_array($params);

        if (empty($params['reject_reason_item']) && empty($params['note'])) {
            throw new ValidationException(BaseService::getTrans('access_data_request_param_error') . '[note|reject_reason_item]',
                ErrCode::$VALIDATE_ERROR);
        }

        // 用户权限校验
        $user_data_sys_info = $this->checkUserModuleAccessPermissionInfo(AccessDataEnums::MODULE_TYPE_AUDIT);

        // 指定类型部门
        if (in_array($user_data_sys_info['department_category'], AccessDataEnums::$work_order_audit_stage_item)) {
            if (!empty($params['reject_reason_item']) && !is_array($params['reject_reason_item'])) {
                $params['reject_reason_item'] = json_decode($params['reject_reason_item'], true);
                if (json_last_error() != JSON_ERROR_NONE) {
                    throw new ValidationException(BaseService::getTrans('access_data_request_param_error') . '[reject_reason_item]',
                        ErrCode::$VALIDATE_ERROR);
                }
            }

            Validation::validate($params, BaseService::$validate_reject);

            $remark = '';
            if (!empty($params['reject_reason_item'])) {
                foreach ($params['reject_reason_item'] as $item_k) {
                    if ($item_k == AccessDataEnums::AUDIT_REJECTED_REASON_005) {
                        continue;
                    }

                    $remark .= BaseService::getTrans(AccessDataEnums::$audit_rejected_reason_item[$item_k]) . ', ';
                }
            }

            $remark .= $params['note'] ?? '';
            $remark = trim($remark, ', ');
        } else {
            throw new ValidationException(BaseService::getTrans('access_data_user_department_flag_error') . '[uid:' . $this->user['id'] . ']',
                ErrCode::$VALIDATE_ERROR);
        }

        $res = (new AccessDataFlowService())->reject($params['id'], $remark, $this->user);

        $this->logger->info('取数系统 - 审批驳回 - 响应结果: ' . json_encode($res, JSON_UNESCAPED_UNICODE));

        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 数据列表
     * @Permission(action='access_data_sys.data')
     *
     * @return mixed
     * @throws ValidationException
     */
    public function dataListAction()
    {
        $params = $this->request->get();

        $params = BaseService::handleParams($params, BaseService::$not_must_params);
        Validation::validate($params, AccessDataListService::$validate_list_search);

        // 用户权限校验
        $this->checkUserModuleAccessPermissionInfo(AccessDataEnums::MODULE_TYPE_DATA);

        $res = AccessDataListService::getInstance()->getList($params, $this->user['id'],
            AccessDataListService::LIST_TYPE_DATA);

        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }


    /**
     * 数据详情
     * @Permission(action='access_data_sys.data')
     *
     * @return mixed
     * @throws ValidationException
     */
    public function dataDetailAction()
    {
        $data = $this->request->get();

        Validation::validate($data, BaseService::$validate_detail_param);

        // 用户权限校验
        $this->checkUserModuleAccessPermissionInfo(AccessDataEnums::MODULE_TYPE_DATA);

        $res = AccessDataDetailService::getInstance()->getAuditDetail($data['id'], $this->user['id'],
            AccessDataEnums::MODULE_TYPE_DATA);

        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 数据文件下载
     *
     * @Token
     * @return Response
     * @throws Exception
     */
    public function downloadAction()
    {
        $params = $this->request->get();

        // 请求参数
        $this->logger->info('取数系统 - 数据文件下载 - 请求参数: ' . json_encode($params, JSON_UNESCAPED_UNICODE));
        $this->logger->info('取数系统 - 数据文件下载 - 用户登录信息: ' . json_encode($this->user, JSON_UNESCAPED_UNICODE));

        $params = BaseService::handleParams($params, BaseService::$not_must_params);

        // 去除数组元素两侧空白字符
        $params = trim_array($params);

        Validation::validate($params, BaseService::$validate_data_file_download);

        // 用户权限校验
        $this->checkUserModuleAccessPermissionInfo(AccessDataEnums::MODULE_TYPE_DATA);

        // 加锁处理
        $lock_key = md5('access_data_sys_data_file_download_' . $params['id'] . '_' . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return AccessDataDetailService::getInstance()->outputDataFile($params['id'], $params['pwd'],
                $params['data_type'], $this->user['id']);
        }, $lock_key, 30);

        if (isset($res['code']) && $res['code'] == ErrCode::$SUCCESS) {
            $_tmp_file_name = '__tmpFile_' . date('YmdHis') . '.' . $res['data']['file_suffix'];

            $this->response
                ->setHeader('Content-Type', $res['data']['http_header']['Content-Type'])
                ->setHeader('Content-Disposition', 'attachment; filename="' . $_tmp_file_name . '"');

            if (!isset($_SERVER['HTTP_ACCEPT_ENCODING']) || empty($_SERVER['HTTP_ACCEPT_ENCODING'])) {
                $this->response->setHeader('Content-Length', $res['data']['http_header']['Content-Length']);
            }

            // 如下设置 Content-Type 可以生效
//            echo file_get_contents($res['data']['file_url']);

            // 如下设置Content-Type 未生效, 被重置
            $this->response->setFileToSend($res['data']['file_url'], $_tmp_file_name);
            return $this->response;
        } else {
            if (isset($res['code'])) {
                return $this->returnJson($res['code'], $res['message']);
            } else {
                return $this->returnJson(ErrCode::$FREQUENT_VISIT_ERROR, $this->t['sys_processing']);
            }
        }
    }

    /**
     * 用户在各模块的访问权限校验
     *
     * @param int $module_type
     * @return mixed
     * @throws ValidationException
     */
    protected function checkUserModuleAccessPermissionInfo(int $module_type)
    {
        if (!in_array($module_type, AccessDataEnums::$module_type_item)) {
            throw new ValidationException(BaseService::getTrans('access_data_request_param_error') . '[module_type]',
                ErrCode::$VALIDATE_ERROR);
        }

        // 用户权限校验
        $user_data_sys_info = (new BaseService())->getStaffDataSysInfo($this->user);
        $this->logger->info("取数系统 - 用户的取数系统信息: [uid:{$this->user['id']}]" . json_encode($user_data_sys_info,
                JSON_UNESCAPED_UNICODE));

        if (empty($user_data_sys_info)) {
            $t_key = $module_type == AccessDataEnums::MODULE_TYPE_DATA ? 'access_data_user_no_data_permission' : 'access_data_staff_info_null';
            throw new ValidationException(BaseService::getTrans($t_key), ErrCode::$VALIDATE_ERROR);
        }

        // 申请模块 - 需求部门
        if ($module_type == AccessDataEnums::MODULE_TYPE_APPLY && $user_data_sys_info['department_category'] != AccessDataEnums::WORK_ORDER_AUDIT_STAGE_001) {
            throw new ValidationException(BaseService::getTrans('access_data_user_no_apply_permission'),
                ErrCode::$VALIDATE_ERROR);
        }

        // 审批模块 - 各部门负责人: 因取数系统的部门负责人调整较频繁 且 审批流的负责人是固化下来的，因此为保证左侧红点提醒与列表数对齐，列表取消权限校验(红点取数无权限校验)
        if ($module_type == AccessDataEnums::MODULE_TYPE_AUDIT && !$user_data_sys_info['duty_flag']) {
//            throw new ValidationException(BaseService::getTrans('access_data_user_no_audit_permission'), ErrCode::$VALIDATE_ERROR);
        }

        // 处理模块 - 数据部门
        if ($module_type == AccessDataEnums::MODULE_TYPE_PROCESS && $user_data_sys_info['department_category'] != AccessDataEnums::WORK_ORDER_AUDIT_STAGE_003) {
            throw new ValidationException(BaseService::getTrans('access_data_user_no_process_permission'),
                ErrCode::$VALIDATE_ERROR);
        }

        // 权限配置模块 - 数据部门 且 负责人
        if ($module_type == AccessDataEnums::MODULE_TYPE_PERMISSION && $user_data_sys_info['department_category'] != AccessDataEnums::WORK_ORDER_AUDIT_STAGE_003 && !$user_data_sys_info['duty_flag']) {
            throw new ValidationException(BaseService::getTrans('access_data_user_no_process_permission'),
                ErrCode::$VALIDATE_ERROR);
        }

        return $user_data_sys_info;
    }

    /**
     * 员工列表
     * @Permission(action='access_data_sys.staff_manage')
     *
     * @return mixed
     * @throws ValidationException
     */
    public function getStaffListAction()
    {
        $params = $this->request->get();

        Validation::validate($params, [
            'staff_id' => 'StrLenGeLe:0,30|>>>:参数错误[工号]',
        ]);

        // 用户权限校验
        $this->checkUserModuleAccessPermissionInfo(AccessDataEnums::MODULE_TYPE_PERMISSION);

        $res = AccessDataListService::getInstance()->getStaffList($params, $this->user['id']);

        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 员工页默认配置
     * @Permission(action='access_data_sys.staff_manage')
     *
     * @return mixed
     * @throws ValidationException
     */
    public function getStaffPageEnumsAction()
    {
        // 用户权限校验
        $this->checkUserModuleAccessPermissionInfo(AccessDataEnums::MODULE_TYPE_PERMISSION);

        $res = AccessDataDetailService::getInstance()->getStaffDetailEnums();
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }

    /**
     * 员工详情页
     * @Permission(action='access_data_sys.staff_manage')
     *
     * @return mixed
     * @throws ValidationException
     */
    public function getStaffInfoAction()
    {
        $params = $this->request->get();

        Validation::validate($params, [
            'staff_id' => 'Required|StrLenGeLe:1,30|>>>:参数错误[工号]',
        ]);

        // 用户权限校验
        $this->checkUserModuleAccessPermissionInfo(AccessDataEnums::MODULE_TYPE_PERMISSION);

        $res = AccessDataDetailService::getInstance()->getStaffInfo($params['staff_id']);

        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }


    /**
     * 员工添加
     * @Permission(action='access_data_sys.staff_manage')
     *
     * @return mixed
     * @throws ValidationException
     */
    public function addStaffInfoAction()
    {
        $params = $this->request->get();

        $this->getDI()->get('logger')->info('取数系统 - 权限管理[员工添加] - 提交参数: ' . json_encode($params,
                JSON_UNESCAPED_UNICODE));

        $params['staff_id']                 = (int)$params['staff_id'] ?? 0;
        $params['business_line_permission'] = $params['business_line_permission'] ? $params['business_line_permission'] : [];
        Validation::validate($params, [
            'staff_id'                 => 'Required|IntGe:1|>>>:参数错误[工号]',
            'staff_name'               => 'Required|StrLenGeLe:1,128|>>>:参数错误[姓名]',
            'department_id'            => 'Required|IntGe:1|>>>:参数错误[所属部门]',
            'duty_flag'                => 'Required|IntIn:0,1|>>>:参数错误[员工属性]',
            'business_line_permission' => 'Arr|ArrLenGe:0|>>>:参数错误[业务线权限]',
        ]);

        // 用户权限校验
        $this->checkUserModuleAccessPermissionInfo(AccessDataEnums::MODULE_TYPE_PERMISSION);

        $res = AccessDataUpdateService::getInstance()->saveStaffInfo($params, $this->user,
            AccessDataUpdateService::STAFF_INFO_TYPE_ADD);

        $this->getDI()->get('logger')->info('取数系统 - 权限管理[员工添加] - 处理结果: ' . json_encode($res, JSON_UNESCAPED_UNICODE));

        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 员工更新
     * @Permission(action='access_data_sys.staff_manage')
     *
     * @return mixed
     * @throws ValidationException
     */
    public function updateStaffInfoAction()
    {
        $params = $this->request->get();

        $this->getDI()->get('logger')->info('取数系统 - 权限管理[员工编辑] - 提交参数: ' . json_encode($params,
                JSON_UNESCAPED_UNICODE));

        $params['staff_id']                 = (int)$params['staff_id'] ?? 0;
        $params['business_line_permission'] = $params['business_line_permission'] ? $params['business_line_permission'] : [];
        Validation::validate($params, [
            'staff_id'                 => 'Required|IntGe:1|>>>:参数错误[工号]',
            'staff_name'               => 'Required|StrLenGeLe:1,128|>>>:参数错误[姓名]',
            'department_id'            => 'Required|IntGe:1|>>>:参数错误[所属部门]',
            'duty_flag'                => 'Required|IntIn:0,1|>>>:参数错误[员工属性]',
            'business_line_permission' => 'Arr|ArrLenGe:0|>>>:参数错误[业务线权限]',
        ]);

        // 用户权限校验
        $this->checkUserModuleAccessPermissionInfo(AccessDataEnums::MODULE_TYPE_PERMISSION);

        $res = AccessDataUpdateService::getInstance()->saveStaffInfo($params, $this->user,
            AccessDataUpdateService::STAFF_INFO_TYPE_EDIT);

        $this->getDI()->get('logger')->info('取数系统 - 权限管理[员工编辑] - 处理结果: ' . json_encode($res, JSON_UNESCAPED_UNICODE));

        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

}
