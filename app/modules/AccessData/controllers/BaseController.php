<?php

namespace App\Modules\AccessData\Controllers;

use App\Library\BaseController as Controller;

/**
 * @name BaseController
 * @desc 所有web模块控制器都继承自该控制器
 */
abstract class BaseController extends Controller
{
    //模块自己控制
    public function onConstruct()
    {
    }

    /**
     * 初始化
     */
    public function initialize()
    {
        parent::initialize();
        //项目作为接口，返回数据，禁用视图
        $this->view->disable(false);
    }

    /**
     * 生成自定义的Excel文件
     * @param array $head Excel 列名
     * @param array $data Excel 业务数据
     * @param array $extra_data 额外数据,
     * 示例:
     * file_desc: Excel 首行说明文案内容
     * end_column_char: 最后列字母
     * column_width: 列宽
     * header_column_font_color: 表头字体颜色
     * header_columns: 表头包含列
     * header_column_row: 表头所在行
     *
     * @return string
     */
    protected function customizeExcelToFile(array $head, array $data = [], array $extra_data = [])
    {
        $config = [
            'path' => sys_get_temp_dir() . '/',
        ];
        $excel  = new \Vtiful\Kernel\Excel($config);

        // Excel；临时文件名
        $fileName = $extra_data['file_name'] ?? time() . '_excel.xlsx';

        // 此处会自动创建一个工作表
        $fileObject = $excel->fileName($fileName);
        $fileHandle = $fileObject->getHandle();

        // 设置Excel首行文件说明文案
        $end_column_char = $extra_data['end_column_char'] ?? 'Z';
        if (!empty($extra_data['file_desc'])) {
            $format    = new \Vtiful\Kernel\Format($fileHandle);
            $wrapStyle = $format->wrap()->align(\Vtiful\Kernel\Format::FORMAT_ALIGN_VERTICAL_CENTER)->toResource();
            $fileObject->mergeCells("A1:{$end_column_char}1",
                $extra_data['file_desc'])->setRow("A1:{$end_column_char}1", 200, $wrapStyle);
        }

        // 设置列宽
        if (!empty($extra_data['column_width'])) {
            $fileObject->header($head)->data($data)->setColumn("A:{$end_column_char}", $extra_data['column_width']);
        }

        // 设置表头字体颜色
        if (!empty($extra_data['header_column_font_color'])) {
            $format     = new \Vtiful\Kernel\Format($fileHandle);
            $colorStyle = $format->fontColor($extra_data['header_column_font_color'])->toResource();
            foreach ($extra_data['header_columns'] as $column) {
                $fileObject->setRow("{$column}{$extra_data['header_column_row']}", 15, $colorStyle);
            }
        }

        // 最后的最后，输出文件
        $filePath = $fileObject->output();

        //判断文件是否生成成功了
        if (!is_file($filePath)) {
            return '';
        }

        return $filePath;
    }

}
