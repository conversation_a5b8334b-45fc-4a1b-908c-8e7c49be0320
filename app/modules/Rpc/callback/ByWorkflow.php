<?php
namespace app\modules\Rpc\callback;

use App\Library\Enums\ByWorkflowEnums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Library\BaseService;
use App\Modules\AgencyPayment\Services\AgencyPaymentApiService;
use App\Modules\Budget\Services\BudgetWithholdingService;
use App\Modules\Material\Services\WmsApplyService;

class ByWorkflow extends BaseService
{
    /**
     * by审批异步回调
     * @param string $locale 语种
     * @param array $params 请求参数组
     * @return array
     */
    public function approvalCallback($locale, $params)
    {
        try {
            $this->logger->info(['svc_approval_callback_request' => [
                'locale' => $locale,
                'params' => $params
            ]]);

            BaseService::setLanguage($locale['locale']);
            $params = trim_array($params);
            Validation::validate($params, [
                'workflow_no' => 'Required|StrLenGeLe:1,50',
                'biz_type' => 'Required|IntGe:1',
                'state' => 'Required|IntGe:1',
                'approval_id' => 'Required|IntGe:1',
                'approval_time' => 'Required',
                'reason' => 'StrLenGeLe:0,1000'
            ]);
            $res = [
                'code' => ErrCode::$SUCCESS,
                'message' => '',
                'data' => []
            ];
            switch ($params['biz_type']) {
                // 代理支付
                case ByWorkflowEnums::BY_BIZ_TYPE_AGENCY_PAYMENT:
                    //通过
                    if ($params['state'] == ByWorkflowEnums::BY_OPERATE_PASS) {
                        $res = AgencyPaymentApiService::getInstance()->pass($params);
                    }
                    break;

                // 耗材申请
                case ByWorkflowEnums::BY_BIZ_TYPE_WMS:
                    if ($params['state'] == ByWorkflowEnums::BY_OPERATE_PASS) {
                        $res = WmsApplyService::getInstance()->autoPass($params);
                    }
                    break;

                // 费用预提
                case ByWorkflowEnums::BY_BIZ_TYPE_BUDGET_WITHHOLDING:
                    if ($params['state'] == ByWorkflowEnums::BY_OPERATE_PASS) {
                        $res = BudgetWithholdingService::getInstance()->autoPass($params);
                    }
                    break;
                default:
                    break;
            }

            $this->logger->info(['svc_approval_callback_reponse' => $res]);

            return $res;
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }
}