<?php
namespace app\modules\Rpc\callback;

use App\Modules\Material\Services\BaseService;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Material\Services\InventoryCheckService;

class MaterialInventoryCheck extends BaseService
{
    /**
     * 资产盘点-资产盘点通知消息详情
     * @param string $locale 当前语种
     * @param array $params 搜索条件
     * @return array
     */
    public function getInventoryMsgDetail($locale, $params)
    {
        BaseService::setLanguage($locale['locale']);
        $params = trim_array($params);
        return InventoryCheckService::getInstance()->getInventoryMsgDetail($params);
    }

    /**
     * 资产盘点-开始盘点/去盘点
     * @param string $locale 当前语种
     * @param array $params 搜索条件
     * @return array
     */
    public function taskStart($locale, $params)
    {
        BaseService::setLanguage($locale['locale']);
        $params = trim_array($params);
        return InventoryCheckService::getInstance()->taskStart($params);
    }

    /**
     * 资产盘点-任务清单-待处理、已处理总数
     * @param string $locale 当前语种
     * @param array $params 搜索条件
     * @return array|mixed
     */
    public function getTaskCount($locale, $params)
    {
        BaseService::setLanguage($locale['locale']);
        $params = trim_array($params);
        $count = InventoryCheckService::getInstance()->getTaskCount($params);
        return ['code' => ErrCode::$SUCCESS, 'message' => 'success', 'data' => $count];
    }

    /**
     * 资产盘点-任务清单-待处理、已处理列表
     * @param string $locale 当前语种
     * @param array $params 搜索条件
     * @return array
     */
    public function getTaskList($locale, $params)
    {
        BaseService::setLanguage($locale['locale']);
        $params = trim_array($params);
        $params = BaseService::handleParams($params, InventoryCheckService::$not_must_params);
        return InventoryCheckService::getInstance()->getTaskList($params, ['staff_id' => $params['staff_id'], 'name' => $params['staff_name']]);
    }

    /**
     * 资产盘点-任务详情
     * @param string $locale 当前语种
     * @param array $params 搜索条件
     * @return array|mixed
     */
    public function getTaskInfo($locale, $params)
    {
        $data = [];
        try {
            BaseService::setLanguage($locale['locale']);
            $params = trim_array($params);
            Validation::validate($params, InventoryCheckService::$validate_task_info);
            $data = InventoryCheckService::getInstance()->getTaskInfo($params['task_id'], $params['staff_id']);
            $inventory_info = InventoryCheckService::getInstance()->getInventoryCheckInfoById($data->inventory_check_id);
            $data = $data->toArray();
            $data['is_must_upload'] = $inventory_info->is_must_upload;
            $data['is_only_take_pic'] = $inventory_info->is_only_take_pic;
            return ['code' => ErrCode::$SUCCESS, 'message' => '', 'data' => $data];
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => $data];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => $data];
        }
    }

    /**
     * 资产盘点-资产盘点清单-待盘点、已盘点总数
     * @param string $locale 当前语种
     * @param array $params 搜索条件
     * @return array|mixed
     */
    public function getTaskAssetCount($locale, $params)
    {
        BaseService::setLanguage($locale['locale']);
        $params = trim_array($params);
        $count = InventoryCheckService::getInstance()->getTaskAssetCount($params);
        return ['code' => ErrCode::$SUCCESS, 'message' => 'success', 'data' => $count];
    }

    /**
     * 资产盘点-资产盘点清单-待盘点、已盘点列表
     * @param string $locale 当前语种
     * @param array $params 搜索条件
     * @return array
     */
    public function getTaskAssetList($locale, $params)
    {
        BaseService::setLanguage($locale['locale']);
        $params = trim_array($params);
        $params = BaseService::handleParams($params, InventoryCheckService::$not_must_params);
        return InventoryCheckService::getInstance()->getTaskAssetList($params);
    }

    /**
     * 资产盘点-资产盘点清单-未盘到
     * @param string $locale 当前语种
     * @param array $params 参数组
     * @return array
     */
    public function taskAssetLose($locale, $params)
    {
        BaseService::setLanguage($locale['locale']);
        $params = trim_array($params);
        return InventoryCheckService::getInstance()->taskAssetLose($params);
    }

    /**
     * 资产盘点-资产盘点清单-批量未盘到
     * @param string $locale 当前语种
     * @param array $params 参数组
     * @return array
     */
    public function taskAssetBatchLose($locale, $params)
    {
        BaseService::setLanguage($locale['locale']);
        $params = trim_array($params);
        return InventoryCheckService::getInstance()->taskAssetBatchLose($params);
    }

    /**
     * 资产盘点-资产盘点清单-盘到
     * @param string $locale 当前语种
     * @param array $params 参数组
     * @return array
     */
    public function taskAssetMatch($locale, $params)
    {
        BaseService::setLanguage($locale['locale']);
        $params = trim_array($params);
        return InventoryCheckService::getInstance()->taskAssetMatch($params);
    }

    /**
     * 资产盘点-资产盘点清单-批量盘到
     * @param string $locale 当前语种
     * @param array $params 参数组
     * @return array
     */
    public function taskAssetBatchMatch($locale, $params)
    {
        BaseService::setLanguage($locale['locale']);
        $params = trim_array($params);
        return InventoryCheckService::getInstance()->taskAssetBatchMatch($params);
    }

    /**
     * 资产盘点-资产盘点清单-信息有误
     * @param string $locale 当前语种
     * @param array $params 参数组
     * @return array
     */
    public function taskAssetNotMatch($locale, $params)
    {
        try {
            BaseService::setLanguage($locale['locale']);
            $params = trim_array($params);
            Validation::validate($params, InventoryCheckService::getInstance()->getTaskAssetNotMatchValidation($params));
            if (empty($params['revise_asset_code']) && empty($params['revise_sn_code']) && empty($params['revise_sys_store_id']) && empty($params['revise_staff_id']) && empty($params['reason']) && empty($params['attachments'])) {
                throw new ValidationException(static::$t->_('inventory_staff_asset_not_match_invalid'), ErrCode::$VALIDATE_ERROR);
            }
            return InventoryCheckService::getInstance()->taskAssetNotMatch($params);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 资产盘点-资产盘点清单-查看信息有误
     * @param string $locale 当前语种
     * @param array $params 参数组
     * @return array
     */
    public function taskAssetViewNotMatch($locale, $params)
    {
        BaseService::setLanguage($locale['locale']);
        $params = trim_array($params);
        return InventoryCheckService::getInstance()->taskAssetViewNotMatch($params);
    }

    /**
     * 资产盘点-资产盘点清单-修改数量
     * @param string $locale 当前语种
     * @param array $params 参数组
     * @return array
     */
    public function taskAssetUpdateNum($locale, $params)
    {
        try {
            BaseService::setLanguage($locale['locale']);
            $params = trim_array($params);
            Validation::validate($params, InventoryCheckService::getInstance()->getTaskUpdateNumValidation($params));
            return InventoryCheckService::getInstance()->taskAssetUpdateNum($params);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 资产盘点-资产盘点清单-搜索资产
     * @param string $locale 当前语种
     * @param array $params 参数组
     * @return array
     */
    public function taskAssetBarcodeSearch($locale, $params)
    {
        BaseService::setLanguage($locale['locale']);
        $params = trim_array($params);
        return InventoryCheckService::getInstance()->taskAssetBarcodeSearch($params);
    }

    /**
     * 资产盘点-资产盘点清单-添加资产
     * @param string $locale 当前语种
     * @param array $params 参数组
     * @return array
     */
    public function taskAssetAdd($locale, $params)
    {
        try {
            BaseService::setLanguage($locale['locale']);
            $params = trim_array($params);
            Validation::validate($params, InventoryCheckService::getInstance()->getTaskAssetAddValidation($params));
            return InventoryCheckService::getInstance()->taskAssetAdd($params);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 资产盘点-资产盘点清单-删除资产
     * @param string $locale 当前语种
     * @param array $params 参数组
     * @return array
     */
    public function taskAssetDel($locale, $params)
    {
        BaseService::setLanguage($locale['locale']);
        $params = trim_array($params);
        return InventoryCheckService::getInstance()->taskAssetDel($params);
    }

    /**
     * 资产盘点-资产盘点清单-扫码盘点
     * @param string $locale 当前语种
     * @param array $params 参数组
     * @return array
     */
    public function taskAssetScanCode($locale, $params)
    {
        BaseService::setLanguage($locale['locale']);
        $params = trim_array($params);
        return InventoryCheckService::getInstance()->taskAssetScanCode($params);
    }

    /**
     * 资产盘点-资产盘点清单-确认无资产
     * @param string $locale 当前语种
     * @param array $params 参数组
     * @return array
     */
    public function taskAssetDone($locale, $params)
    {
        BaseService::setLanguage($locale['locale']);
        $params = trim_array($params);
        return InventoryCheckService::getInstance()->taskAssetDone($params);
    }

    /**
     * 资产盘点-资产盘点清单-更新盘点
     * @param string $locale 当前语种
     * @param array $params 参数组
     * @return array
     */
    public function taskAssetUpdate($locale, $params)
    {
        BaseService::setLanguage($locale['locale']);
        $params = trim_array($params);
        return InventoryCheckService::getInstance()->taskAssetUpdate($params);
    }

    /**
     * 资产盘点-资产盘点清单-限制打卡
     * @param string $locale 当前语种
     * @param array $params 参数组
     * @return array
     */
    public function taskCheckPunchOut($locale, $params)
    {
        BaseService::setLanguage($locale['locale']);
        $params = trim_array($params);
        return InventoryCheckService::getInstance()->taskCheckPunchOut($params);
    }
}