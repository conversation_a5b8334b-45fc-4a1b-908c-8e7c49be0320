<?php
namespace app\modules\Rpc\callback;

use App\Library\Enums\MaterialAssetApplyEnums;
use App\Library\Enums\MaterialClassifyEnums;
use App\Library\Enums\MaterialEnums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Services\StoreService;
use App\Modules\Material\Services\AssetAccountService;
use App\Modules\Material\Services\AssetApplyService;
use App\Modules\Material\Services\BaseService;
use App\Modules\Purchase\Services\BaseService as PurchaseService;

class MaterialAsset extends \App\Library\BaseService
{
    /**
     * 资产申请-选择资产（可申请资产列表）
     * @param string $locale 当前语种
     * @param array $params 搜索条件
     * @return array
     */
    public function getAssetList($locale, $params)
    {
        try {
            BaseService::setLanguage($locale['locale']);
            $params = trim_array($params);
            $params = BaseService::handleParams($params, AssetApplyService::$not_must_params);
            Validation::validate($params, AssetApplyService::$validate_search_barcode);
            return AssetApplyService::getInstance()->searchBarcode($params, $params['user_id']);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 资产申请-输入基本信息回显
     * @param string $locale 当前语种
     * @param array $params 搜索条件
     * @return array
     */
    public function getAddDefault($locale, $params)
    {
        try {
            BaseService::setLanguage($locale['locale']);
            $params = trim_array($params);
            Validation::validate($params, ['id' => 'Required|IntGt:0', 'name' => 'Required|StrLenGeLe:1,50']);
            //获取资产申请员工基本信息
            $result = AssetApplyService::getInstance()->getAddDefault($params);

            //获取员工所属公司
            $user_company_result = AssetAccountService::getInstance()->getCompanyByDepartment(['department_id' => $result['data']['node_department_id']]);
            $result['data']['company_id'] = $user_company_result['data']['company_id'];
            $result['data']['company_name'] = $user_company_result['data']['company_name'];

            //所属公司列表
            $result['data']['cost_company'] = (new PurchaseService())->getCooCostCompany();

            //使用方向
            foreach (MaterialEnums::$use as $k => $v) {
                $result['data']['use'][] = [
                    'value' => $k,
                    'label' => static::$t->_($v)
                ];
            }

            //配送方式
            foreach (MaterialClassifyEnums::$delivery_way_arr as $k => $v) {
                $result['data']['delivery_way_list'][] = [
                    'value' => $k,
                    'label' => static::$t->_($v)
                ];
            }
            return $result;
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 资产申请-选择资产使用地点
     * @param string $locale 当前语种
     * @param array $params 搜索条件
     * @return array
     */
    public function getAddressList($locale, $params)
    {
        try {
            BaseService::setLanguage($locale['locale']);
            $params = trim_array($params);
            $list = (new StoreService())->getAddressList($params);
            return ['code' => ErrCode::$SUCCESS, 'message' => '', 'data' => $list];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 资产申请-选择收货人
     * @param string $locale 当前语种
     * @param array $params 搜索条件
     * @return array
     */
    public function getConsigneeList($locale, $params)
    {
        try {
            BaseService::setLanguage($locale['locale']);
            $params = trim_array($params);
            Validation::validate($params, AssetApplyService::$validate_consignee);

            $user['id'] = $params['user_id'];
            $user['name'] = $params['user_name'];
            $user['sys_store_id'] = $params['sys_store_id'];
            return AssetApplyService::getInstance()->getConsigneeList($params, $user);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 资产申请-提交
     * @param string $locale 当前语种
     * @param array $params 搜索条件
     * @return array
     */
    public function assetApply($locale, $params)
    {
        try {
            BaseService::setLanguage($locale['locale']);
            $params = trim_array($params);
            $validate = AssetApplyService::$validate_add;
            //by申请单上使用方向
            $validate['use'] = 'Required|IntIn:'.MaterialEnums::USE_VALIDATE;
            //by上明细行最多25条
            $validate['products'] = 'Required|Arr|ArrLenGeLe:1,25';
            //由于by上的使用方向作用在申请单上，所以走申请单的严重，明细行验证取消； 另外明细行的使用方向与申请单使用方向保持一致
            unset($validate['products[*].use']);
            if (!empty($params['products'])) {
                foreach ($params['products'] as &$item) {
                    $item['use'] = $params['use'] ?? 0;
                }
            }
            //申请来源于by端
            $params['source_type'] = MaterialAssetApplyEnums::SOURCE_TYPE_BY;
            Validation::validate($params, $validate);
            return AssetApplyService::getInstance()->add($params, ['id' => $params['user_id']]);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 资产申请-撤回
     * @param string $locale 当前语种
     * @param array $params 搜索条件
     * @return array
     */
    public function assetApplyCancel($locale, $params)
    {
        try {
            BaseService::setLanguage($locale['locale']);
            $params = trim_array($params);
            Validation::validate($params, array_merge(AssetApplyService::$validate_update, AssetApplyService::$validate_cancel));
            return AssetApplyService::getInstance()->cancel($params, ['id' => $params['user_id']]);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 资产申请-详情
     * @param string $locale 当前语种
     * @param array $params 搜索条件
     * @return array
     */
    public function assetApplyDetail($locale, $params)
    {
        try {
            BaseService::setLanguage($locale['locale']);
            $params = trim_array($params);
            $validate = AssetApplyService::$validate_workflow_no;
            $validate['type'] = 'Required|IntIn:1,2';
            Validation::validate($params, $validate);
            return AssetApplyService::getInstance()->detailForBy($params, $params['type'], ['id' => $params['user_id']]);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 资产申请-审批通过
     * @param string $locale 当前语种
     * @param array $params 搜索条件
     * @return array
     */
    public function assetApplyPass($locale, $params)
    {
        try {
            BaseService::setLanguage($locale['locale']);
            $params = trim_array($params);
            Validation::validate($params, array_merge(AssetApplyService::$validate_update, AssetApplyService::$validate_products));
            return AssetApplyService::getInstance()->pass($params, ['id' => $params['user_id']]);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 资产申请-审批拒绝
     * @param string $locale 当前语种
     * @param array $params 搜索条件
     * @return array
     */
    public function assetApplyReject($locale, $params)
    {
        try {
            BaseService::setLanguage($locale['locale']);
            $params = trim_array($params);
            Validation::validate($params, array_merge(AssetApplyService::$validate_update, AssetApplyService::$validate_reject, AssetApplyService::$validate_products));
            return AssetApplyService::getInstance()->reject($params, ['id' => $params['user_id']]);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 资产申请-查看出库信息
     * @param string $locale 当前语种
     * @param array $params 搜索条件
     * @return array
     */
    public function getAssetOutStorageList($locale, $params)
    {
        try {
            BaseService::setLanguage($locale['locale']);
            $params = trim_array($params);
            Validation::validate($params, AssetApplyService::$validate_workflow_no);
            return AssetApplyService::getInstance()->getAssetOutStorageList($params, ['id' => $params['user_id']]);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 资产申请-查看路由
     * @param string $locale 当前语种
     * @param array $params 搜索条件
     * @return array
     */
    public function getOutboundTrackingInfo($locale, $params)
    {
        try {
            BaseService::setLanguage($locale['locale']);
            $params = trim_array($params);
            return AssetApplyService::getInstance()->getOutboundTrackingInfo($locale['locale'], $params);
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }
}