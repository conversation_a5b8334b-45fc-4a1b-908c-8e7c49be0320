<?php

namespace app\modules\Rpc\callback;

use App\Library\BaseService;
use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Modules\PaperDocument\Services\ConfirmationService;
use App\Modules\PaperDocument\Services\SysService;

class PaperDocument extends \App\Library\BaseService
{
    /**
     * 获取基本信息
     * @param $locale
     * @param $params
     * @return array
     */
    public function getPaperDocBasicInfo($locale, $params): array
    {
        BaseService::setLanguage($locale['locale']);
        try {
            $info = ConfirmationService::getInstance()->getPaperDocBasicInfo($params);
            return ['data' => $info];
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 获取基本信息
     * @param $locale
     * @param $params
     * @return array
     * @throws ValidationException
     */
    public function getPaperDocDetailInfo($locale, $params): array
    {
        BaseService::setLanguage($locale['locale']);
        $instance = ConfirmationService::getInstance();
        $details = $instance->getDetail($params, ['id' => $params['staff_id']]);
        return ['data' => $instance->formatDetails($details)];
    }

    /**
     * 批量确认
     * @param $locale
     * @param $params
     * @return array
     * @throws \Exception
     */
    public function batchConfirm($locale, $params): array
    {
        BaseService::setLanguage($locale['locale']);
        try {
            $info = ConfirmationService::getInstance()->batchConfirmPaperDocument($params, ['id' => $params['staff_id']]);
            return ['data' => $info];
        } catch (\Exception $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 批量确认
     * @param $locale
     * @param $params
     * @return array
     * @throws \Exception
     */
    public function singleConfirm($locale, $params): array
    {
        BaseService::setLanguage($locale['locale']);
        try {
            $result = ConfirmationService::getInstance()->confirmPaperDocument($params, ['id' => $params['staff_id']]);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        }
        return ['data' => $result];
    }

    /**
     * 批量确认
     * @param $locale
     * @param $params
     * @return array
     */
    public function singleSubmit($locale, $params): array
    {
        BaseService::setLanguage($locale['locale']);
        try {
            $result = ConfirmationService::getInstance()->submitPaperDocument($params, ['id' => $params['staff_id']]);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        }
        return ['data' => $result];
    }

    public function getConfirmReasonTypeList($locale, $params): array
    {
        BaseService::setLanguage($locale['locale']);
        $result = SysService::getInstance()->getConfirmReason();
        return ['data' => $result];
    }
}