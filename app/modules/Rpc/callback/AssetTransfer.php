<?php

namespace app\modules\Rpc\callback;

use App\Library\BaseController;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\MaterialAssetApplyEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Material\Services\AssetPdfService;
use App\Modules\Material\Services\AssetTransferExportService;
use App\Modules\Material\Services\AssetTransferListService;
use App\Modules\Material\Services\AssetTransferService;
use App\Modules\Material\Services\BaseService;
use App\Modules\User\Services\StaffService;
use App\Modules\User\Services\UserService;

class AssetTransfer extends \App\Library\BaseService
{
    /**
     * 资产转移-我的资产-列表
     * @param string $locale 当前语种
     * @param array $params 搜索条件
     * @return array
     */
    public function getMyAssetList($locale, $params)
    {
        try {
            //设置service语言
            BaseService::setLanguage($locale['locale']);
            //获取用户信息
            $us = new UserService();
            $user = $us->getUserById($params['user_id']);
            $base_controller = new BaseController();
            $user_info = $base_controller->format_user($user);
            unset($params['user_id']);
            //参数trim
            $params = trim_array($params);
            return AssetTransferListService::getInstance()->getList($params, $locale['locale'], $user_info, false, 0, true);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 资产转移-提交转移时-我的资产列表
     * @param $locale
     * @param $params
     * @return array
     * @date 2022/11/20
     */
    public function getMyAssetById($locale, $params)
    {
        try {
            //设置service语言
            BaseService::setLanguage($locale['locale']);
            //获取用户信息
            $us = new UserService();
            $user = $us->getUserById($params['user_id']);
            $base_controller = new BaseController();
            $user_info = $base_controller->format_user($user);
            unset($params['user_id']);
            //参数trim
            $params = trim_array($params);
            return AssetTransferListService::getInstance()->getMyAssetByIds($params, $locale['locale'], $user_info);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 资产转移-我的资产-详情
     * @param string $locale 当前语种
     * @param array $params 搜索条件
     * @return array
     */
    public function getDetail($locale, $params)
    {
        try {
            //设置service语言
            BaseService::setLanguage($locale['locale']);
            //获取用户信息
            $us = new UserService();
            $user = $us->getUserById($params['user_id']);
            $base_controller = new BaseController();
            $user_info = $base_controller->format_user($user);
            unset($params['user_id']);
            //参数trim
            $params = trim_array($params);
            return AssetTransferListService::getInstance()->getDetailBy($params, $user_info, $locale['locale']);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 资产转移-获取枚举
     * @param string $locale 当前语种
     * @param $params
     * @return array
     */
    public function getOptionDefault($locale, $params)
    {
        try {
            //设置service语言
            BaseService::setLanguage($locale['locale']);
            //获取用户信息
            $us = new UserService();
            $user = $us->getUserById($params['user_id']);
            $base_controller = new BaseController();
            $user_info = $base_controller->format_user($user);
            return AssetTransferListService::getInstance()->getOptionsDefault($user_info, true);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 资产转移-我的资产-批量转移
     * @param string $locale 当前语种
     * @param array $params 搜索条件
     * @return array
     */
    public function batchTransfer($locale, $params)
    {
        try {
            //设置service语言
            BaseService::setLanguage($locale['locale']);
            //获取用户信息
            $us = new UserService();
            $user = $us->getUserById($params['user_id']);
            $base_controller = new BaseController();
            $user_info = $base_controller->format_user($user);
            unset($params['user_id']);
            //参数trim
            $params = trim_array($params);
            return AssetTransferService::getInstance()->conversionByTransfer($params, $user_info);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 资产转移-我的资产-批量撤销
     * @param string $locale 当前语种
     * @param array $params 搜索条件
     * @return array
     */
    public function batchCancel($locale, $params)
    {
        try {
            //设置service语言
            BaseService::setLanguage($locale['locale']);
            //获取用户信息
            $us = new UserService();
            $user = $us->getUserById($params['user_id']);
            $base_controller = new BaseController();
            $user_info = $base_controller->format_user($user);
            unset($params['user_id']);
            //参数trim
            $params = trim_array($params);
            return AssetTransferService::getInstance()->conversionByCancel($params, $user_info);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 资产转移-待接收数量(小红点)
     * @param string $locale 当前语种
     * @param array $params 搜索条件
     * @return array
     */
    public function getToBeReceiver($locale, $params)
    {
        try {
            //设置service语言
            BaseService::setLanguage($locale['locale']);
            //获取用户信息
            $us = new UserService();
            $user = $us->getUserById($params['user_id']);
            $base_controller = new BaseController();
            $user_info = $base_controller->format_user($user);
            $count = AssetTransferListService::getInstance()->getReceiverListCount('', [], $user_info);
            return ['code' => ErrCode::$SUCCESS, 'message' => '', 'data' => ['count' => $count]];
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 资产转移-待接收-列表
     * @param string $locale 当前语种
     * @param array $params 搜索条件
     * @return array
     */
    public function getReceiverList($locale, $params)
    {
        try {
            //设置service语言
            BaseService::setLanguage($locale['locale']);
            //获取用户信息
            $us = new UserService();
            $user = $us->getUserById($params['user_id']);
            $base_controller = new BaseController();
            $user_info = $base_controller->format_user($user);
            return AssetTransferListService::getInstance()->receiverList($params, $locale['locale'], $user_info, true);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 资产转移-待接收-列表(勾选拒绝/勾选接收时通过ids获取的列表)
     * @param string $locale 当前语种
     * @param array $params 搜索条件
     * @return array
     */
    public function getReceiverListById($locale, $params)
    {
        try {
            //设置service语言
            BaseService::setLanguage($locale['locale']);
            //获取用户信息
            $us = new UserService();
            $user = $us->getUserById($params['user_id']);
            $base_controller = new BaseController();
            $user_info = $base_controller->format_user($user);
            return AssetTransferListService::getInstance()->receiverListByIds($params, $locale['locale'], $user_info);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 资产转移-生成资产同意书/申请书-rpc-for[BY/HCM]
     * @param string $locale 当前语种
     * @param array $params 搜索条件
     * @return array
     */
    public function getAssetsPdf($locale, $params)
    {
        try {
            $params['platform'] = $params['platform'] ?? MaterialAssetApplyEnums::SOURCE_TYPE_BY;//请求平台-默认by
            // [BY]需要传递资产转移ID组，[HCM]不需要
            if ($params['platform'] == MaterialAssetApplyEnums::SOURCE_TYPE_BY) {
                // 参数验证
                Validation::validate($params, AssetTransferExportService::$export_validate);
            }
            //设置service语言
            BaseService::setLanguage($locale['locale']);
            //获取用户信息
            $us = new UserService();
            $user = $us->getUserById($params['user_id']);
            $base_controller = new BaseController();
            $user_info = $base_controller->format_user($user);
            $this->logger->info('asset_transfer_get_asset_pdf-getAssetsPdf 请求参数:' . json_encode($params, JSON_UNESCAPED_UNICODE));
            $country_code = get_country_code();
            //马来 - 资产管理同意书
            if ($country_code == GlobalEnums::MY_COUNTRY_CODE) {
                return AssetPdfService::getInstance()->exportAgreePdfForMy($params, $user_info);
            } elseif ($country_code == GlobalEnums::TH_COUNTRY_CODE && $user_info['hire_type'] == StaffInfoEnums::HIRE_TYPE_PERSONAL_AGENCY) {
                //泰国 - 个人代理 - 资产管理同意书
                return AssetPdfService::getInstance()->exportAgreePdfForThPersonalAgent($params, $user_info);
            }
            return AssetTransferExportService::getInstance()->exportPdf($params, $user_info);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 资产转移-待接收-批量拒绝
     * @param string $locale 当前语种
     * @param array $params 搜索条件
     * @return array
     */
    public function batchReject($locale, $params)
    {
        try {
            //设置service语言
            BaseService::setLanguage($locale['locale']);
            //获取用户信息
            $us = new UserService();
            $user = $us->getUserById($params['user_id']);
            $base_controller = new BaseController();
            $user_info = $base_controller->format_user($user);
            unset($params['user_id']);
            //参数trim
            $params = trim_array($params);
            return AssetTransferService::getInstance()->assetReject($params, $user_info);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 资产转移-待接收-批量接收
     * @param string $locale 当前语种
     * @param array $params 搜索条件
     * @return array
     */
    public function batchReception($locale, $params)
    {
        try {
            //设置service语言
            BaseService::setLanguage($locale['locale']);
            //获取用户信息
            $us = new UserService();
            $user = $us->getUserById($params['user_id']);
            $base_controller = new BaseController();
            $user_info = $base_controller->format_user($user);
            unset($params['user_id']);
            //参数trim
            $params = trim_array($params);
            return AssetTransferService::getInstance()->assetReception($params, $user_info, true);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 资产转移-待接收-详情
     * @param string $locale 当前语种
     * @param array $params 搜索条件
     * @return array
     */
    public function getReceiverDetail($locale, $params)
    {
        try {
            //设置service语言
            BaseService::setLanguage($locale['locale']);
            //参数trim
            $params = trim_array($params);
            return AssetTransferListService::getInstance()->getReceiverDetail($params, $locale['locale']);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * by站内信-通过资产id获取资产列表
     * @param string $locale 当前语种
     * @param array $params 搜索条件
     * @return array
     */
    public function getAssetsByIds($locale, $params)
    {
        try {
            //设置service语言
            BaseService::setLanguage($locale['locale']);
            //参数trim
            $params = trim_array($params);
            return AssetTransferListService::getInstance()->getAssetsByIds($params, $locale['locale']);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 获取资产转移-站内信-资产批量变更提醒[导入转移消息详情]
     * @param array $locale 语言参数组
     * @param array $params 请求参数组
     * @return array
     */
    public function getAssetTransferMsgInfo($locale, $params)
    {
        try {
            //设置service语言
            BaseService::setLanguage($locale['locale']);
            //参数trim
            $params = trim_array($params);
            // 参数验证
            Validation::validate($params,['batch_id' => 'Required|IntGt:0', 'category' => 'Required|IntGt:0']);
            return AssetTransferListService::getInstance()->getAssetTransferMsgInfo($params, $locale['locale']);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 资产管理-转移-接收人工号
     * @param array $locale 语言参数组
     * @param array $params 请求参数组
     * @return array
     */
    public function searchStaff($locale, $params)
    {
        try {
            //设置service语言
            BaseService::setLanguage($locale['locale']);
            //参数trim
            $params = trim_array($params);
            // 参数验证
            Validation::validate($params,['staff_id' => 'Required|IntGt:0']);
            return StaffService::getInstance()->searchStaff($params);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }
}