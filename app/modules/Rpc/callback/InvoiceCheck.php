<?php

namespace app\modules\Rpc\callback;

use App\Library\BaseService;
use App\Modules\Invoice\Services\InvoiceDataService;

class InvoiceCheck extends BaseService
{
    /**
     * 获取 e-invoice的 UUID
     * @param $locale
     * @param $params
     * @return array
     */
    public function getInvoiceUuidByInternalNo($locale, $params): array
    {
        try {
            //参数trim
            $internalNo = $params['internal_no'];
            $this->logger->info('getInvoiceUuidByInternalNo-params : ' . json_encode($params, JSON_UNESCAPED_UNICODE));
            $return = (new InvoiceDataService())->getInvoiceUuidByInternalNo($internalNo);
            $this->logger->info('getInvoiceUuidByInternalNo-return : ' . json_encode($return, JSON_UNESCAPED_UNICODE));
            return $return;
        } catch (\Exception $e) {
            $this->logger->error('getInvoiceUuidByInternalNo-error !! : ' . json_encode($params, JSON_UNESCAPED_UNICODE));
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }
}