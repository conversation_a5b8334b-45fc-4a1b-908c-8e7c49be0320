<?php

namespace app\modules\Rpc\callback;

use App\Library\Enums;
use App\Library\Enums\ReimbursementEnums;
use App\Library\ErrCode;
use App\Library\RedisClient;
use App\Models\oa\ReimbursementDetailTravelRoommateRelModel;
use App\Modules\Reimbursement\Models\Reimbursement;
use App\Modules\Reimbursement\Services\BaseService;
use App\Library\Validation\ValidationException;
use App\Modules\Reimbursement\Services\AddService;
use App\Modules\Reimbursement\Services\DetailService;
use App\Modules\Reimbursement\Services\ListService;
use App\Modules\Reimbursement\Services\ReimbursementFlowService;
use App\Modules\User\Services\UserService;
use App\Repository\backyard\HrJobDepartmentRelationRepository;
use App\Util\RedisKey;

class BusinessTripCheck extends BaseService
{
    /**
     * 验证出差是否已经报销
     * @param array $locale 当前语种
     * @param array $params
     * @return bool
     * @throws ValidationException
     */
    public function checkBusinessTripIsReimbursed($locale, $params): bool
    {
        BaseService::setLanguage($locale['locale']);
        if (empty($params['serial_no'])) {
            throw new ValidationException('need serial_no');
        }
        return AddService::getInstance()->checkBusinessTripIsReimbursed($params['serial_no']);
    }

    /**
     * 获取员工报销申请待办数
     */
    public function getReimbursementApplyPendingNum($locale, $params)
    {
        $code    = ErrCode::$SUCCESS;
        $message = static::$t->_('success');
        $data    = [
            'total_count' => 0,
        ];

        $staff_id = $params['staff_id'];
        $src      = $params['src'];
        $log      = "staff_id={$staff_id}, src={$src}";

        try {
            if (!in_array($src, [UserService::OA_REDDOT_GET_SRC_APP, UserService::OA_REDDOT_GET_SRC_PUSH])) {
                throw new ValidationException('src 渠道标识错误[app/push], 红点返回 0', ErrCode::$VALIDATE_ERROR);
            }

            // 判断是否是预热缓存中的待办人[待办人缓存定期重置 + 发消息实时追加缓存]
            if (!RedisClient::getInstance()->getClient()->sismember(RedisKey::OA_REIMBURSEMENT_APPLY_PREHOT_CACHE_KEY, $staff_id)) {
                throw new ValidationException('非报销申请预热缓存中的待办人, 红点返回 0', ErrCode::$VALIDATE_ERROR);
            }

            // 符合获取OA红点条件
            $data['total_count'] = ListService::getInstance()->getUserPendingCount($staff_id, UserService::OA_REDDOT_CHANNEL_BY_MENU);

            $log .= ", total_count={$data['total_count']}";
            $this->logger->info('用户获取报销申请红点数成功, ' . $log);
        } catch (ValidationException $e) {
            $this->logger->info($e->getMessage() . ', ' . $log);
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->notice("用户获取报销申请红点数异常, {$log}, 原因可能是: " . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 获取申请列表筛选项枚举
     */
    public function getReimbursementEnums($locale, $params)
    {
        BaseService::setLanguage($locale['locale']);

        $code    = ErrCode::$SUCCESS;
        $message = static::$t->_('success');
        $data    = [];

        try {
            $data['apply_status'] = (new BaseService())->getStatusItem(BaseService::LIST_TYPE_APPLY);

            foreach (Enums::$loan_pay_status as $k => $v) {
                $data['pay_status'][] = [
                    'code'  => (string)$k,
                    'label' => static::$t->_($v),
                ];
            }
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->notice("获取报销申请列表-筛选项枚举异常, 原因可能是: " . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 获取报销申请列表
     */
    public function getReimbursementList($locale, $params)
    {
        BaseService::setLanguage($locale['locale']);

        $code    = ErrCode::$SUCCESS;
        $message = static::$t->_('success');
        $data    = [];

        try {
            if (!isset($params['data_type']) || !in_array($params['data_type'], [ReimbursementEnums::LIST_TAB_TYPE_PENDING, ReimbursementEnums::LIST_TAB_TYPE_ALL])) {
                throw new ValidationException(static::$t->_('params_error', ['param' => 'data_type']), ErrCode::$VALIDATE_ERROR);
            }

            $data = ListService::getInstance()->getApplyListFromMobile($params);
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->notice("获取报销申请列表-异常, 原因可能是: " . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 签字提醒(待签字)
     */
    public function signatureReminder($locale, $params)
    {
        BaseService::setLanguage($locale['locale']);

        $code    = ErrCode::$SUCCESS;
        $message = static::$t->_('success');
        $data    = [
            'send_res' => false,
        ];

        try {
            if (empty($params['order_no'])) {
                throw new ValidationException(static::$t->_('params_error', ['param' => 'order_no']), ErrCode::$VALIDATE_ERROR);
            }

            $data['send_res'] = AddService::getInstance()->sendSignatureReminder($params);
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->notice("报销申请列表-发送签字提醒异常, 原因可能是: " . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 确认详情(待确认的)
     */
    public function getConfirmedDetail($locale, $params)
    {
        BaseService::setLanguage($locale['locale']);

        $code    = ErrCode::$SUCCESS;
        $message = static::$t->_('success');
        $data    = [];

        try {
            if (empty($params['order_no'])) {
                throw new ValidationException(static::$t->_('params_error', ['param' => 'order_no']), ErrCode::$VALIDATE_ERROR);
            }

            $data = DetailService::getInstance()->getConfirmedDetail($params);
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->notice("报销申请列表-获取确认详情异常, 原因可能是: " . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 提交
     */
    public function submitReimbursement($locale, $params)
    {
        BaseService::setLanguage($locale['locale']);

        try {
            if (empty($params['order_no'])) {
                throw new ValidationException(static::$t->_('params_error', ['param' => 'order_no']), ErrCode::$VALIDATE_ERROR);
            }

            $user = UserService::getInstance()->getLoginUser($params['user_id']);
            if (empty($user)) {
                throw new ValidationException(static::$t->_('re_staff_info_id_not_exist'), ErrCode::$VALIDATE_ERROR);
            }

            $res     = AddService::getInstance()->manualSubmit(['no' => $params['order_no']], $user);
            $code    = $res['code'];
            $message = $res['message'];
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->notice("报销申请-报销单提交异常, 原因可能是: " . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
        ];
    }

    /**
     * 撤回
     */
    public function cancelReimbursement($locale, $params)
    {
        BaseService::setLanguage($locale['locale']);

        try {
            if (empty($params['order_no'])) {
                throw new ValidationException(static::$t->_('params_error', ['param' => 'order_no']), ErrCode::$VALIDATE_ERROR);
            }

            $user = UserService::getInstance()->getLoginUser($params['user_id']);
            if (empty($user)) {
                throw new ValidationException(static::$t->_('re_staff_info_id_not_exist'), ErrCode::$VALIDATE_ERROR);
            }

            $order_info = $this->getOrderInfo($params['order_no']);
            if ($order_info['created_id'] != $user['id']) {
                throw new ValidationException(static::$t->_('reimbursement_save_error_029', ['user_id' => $user['id']]), ErrCode::$VALIDATE_ERROR);
            }

            $cancel_reason = $params['cancel_reason'] ?? '';
            $res           = (new ReimbursementFlowService())->cancel($order_info['id'], $cancel_reason, $user);

            $code    = $res['code'];
            $message = $res['message'];
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->notice("报销申请列表-报销单撤回异常, 原因可能是: " . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
        ];
    }

    /**
     * 下载
     */
    public function downloadReimbursement($locale, $params)
    {
        BaseService::setLanguage($locale['locale']);

        $data = [];

        try {
            if (empty($params['order_no'])) {
                throw new ValidationException(static::$t->_('params_error', ['param' => 'order_no']), ErrCode::$VALIDATE_ERROR);
            }

            $order_info = $this->getOrderInfo($params['order_no']);
            if ($order_info['created_id'] != $params['user_id']) {
                throw new ValidationException(static::$t->_('reimbursement_save_error_029', ['user_id' => $params['user_id']]),
                    ErrCode::$VALIDATE_ERROR);
            }

            if ($order_info['status'] != ReimbursementEnums::STATUS_PASS) {
                throw new ValidationException(static::$t->_('reimbursement_save_error_029', ['user_id' => $params['user_id']]),
                    ErrCode::$VALIDATE_ERROR);
            }

            $res     = DetailService::getInstance()->download($order_info['id'], $params['user_id']);
            $code    = $res['code'];
            $message = $res['message'];
            $data    = $res['data'];
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->notice("报销申请列表-下载异常, 原因可能是: " . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 获取报销单信息
     */
    protected function getOrderInfo($order_no)
    {
        $model = Reimbursement::findFirst([
            'conditions' => 'no = :no:',
            'bind'       => ['no' => $order_no],
            'columns'    => ['id', 'created_id', 'apply_id', 'status'],
        ]);
        if (empty($model)) {
            throw new ValidationException(static::$t->_('biz_order_data_is_null', ['biz_id' => $order_no]), ErrCode::$VALIDATE_ERROR);
        }

        return $model->toArray();
    }

    /**
     * 查看/查看详情
     */
    public function viewDetail($locale, $params)
    {
        BaseService::setLanguage($locale['locale']);

        $code    = ErrCode::$SUCCESS;
        $message = static::$t->_('success');
        $data    = [];

        try {
            if (empty($params['order_no'])) {
                throw new ValidationException(static::$t->_('params_error', ['param' => 'order_no']), ErrCode::$VALIDATE_ERROR);
            }

            $order_info = $this->getOrderInfo($params['order_no']);

            $can_view_user_list = [
                $order_info['created_id'],
                $order_info['apply_id'],
            ];
            if (!in_array($params['user_id'], $can_view_user_list)) {
                throw new ValidationException(static::$t->_('reimbursement_save_error_029', ['user_id' => $params['user_id']]),
                    ErrCode::$VALIDATE_ERROR);
            }

            $user = UserService::getInstance()->getLoginUser($params['user_id']);
            if (empty($user)) {
                throw new ValidationException(static::$t->_('re_staff_info_id_not_exist'), ErrCode::$VALIDATE_ERROR);
            }

            $view_type = ReimbursementEnums::MOBILE_DETAIL_PAGE_TYPE_VIEW;
            $data = DetailService::getInstance()->getDetailFromMobile($params['order_no'], $user, $view_type);
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->notice("报销申请列表-获取查看/查看详情异常, 原因可能是: " . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 去确认(待确认的:共同住宿人去确认操作)
     */
    public function goConfirmInfo($locale, $params)
    {
        BaseService::setLanguage($locale['locale']);

        $code    = ErrCode::$SUCCESS;
        $message = static::$t->_('success');
        $data    = [];

        try {
            if (empty($params['order_no'])) {
                throw new ValidationException(static::$t->_('params_error', ['param' => 'order_no']), ErrCode::$VALIDATE_ERROR);
            }

            $user = UserService::getInstance()->getLoginUser($params['user_id']);
            if (empty($user)) {
                throw new ValidationException(static::$t->_('re_staff_info_id_not_exist'), ErrCode::$VALIDATE_ERROR);
            }

            $data = DetailService::getInstance()->goConfirmInfo($params);
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->notice("报销申请列表-获取[去确认]信息异常, 原因可能是: " . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 未共同住宿(共同住宿人拒绝确认)
     */
    public function rejectConfirm($locale, $params)
    {
        BaseService::setLanguage($locale['locale']);

        $code    = ErrCode::$SUCCESS;
        $message = static::$t->_('success');

        try {
            if (empty($params['order_no'])) {
                throw new ValidationException(static::$t->_('params_error', ['param' => 'order_no']), ErrCode::$VALIDATE_ERROR);
            }

            $user = UserService::getInstance()->getLoginUser($params['user_id']);
            if (empty($user)) {
                throw new ValidationException(static::$t->_('re_staff_info_id_not_exist'), ErrCode::$VALIDATE_ERROR);
            }

            AddService::getInstance()->rejectConfirm($params, $user);
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->notice("报销申请列表-去确认-拒绝确认操作异常, 原因可能是: " . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
        ];
    }

    /**
     * 确认无误(共同住宿人同意)
     */
    public function agreeConfirm($locale, $params)
    {
        BaseService::setLanguage($locale['locale']);

        $code    = ErrCode::$SUCCESS;
        $message = static::$t->_('success');

        try {
            if (empty($params['order_no'])) {
                throw new ValidationException(static::$t->_('params_error', ['param' => 'order_no']), ErrCode::$VALIDATE_ERROR);
            }

            $user = UserService::getInstance()->getLoginUser($params['user_id']);
            if (empty($user)) {
                throw new ValidationException(static::$t->_('re_staff_info_id_not_exist'), ErrCode::$VALIDATE_ERROR);
            }

            AddService::getInstance()->agreeConfirm($params, $user);
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->notice("报销申请列表-去确认-确认无误操作异常, 原因可能是: " . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
        ];
    }

    /**
     * 作废
     */
    public function invalidReimbursement($locale, $params)
    {
        BaseService::setLanguage($locale['locale']);

        $code    = ErrCode::$SUCCESS;
        $message = static::$t->_('success');

        try {
            if (empty($params['order_no'])) {
                throw new ValidationException(static::$t->_('params_error', ['param' => 'order_no']), ErrCode::$VALIDATE_ERROR);
            }

            $user = UserService::getInstance()->getLoginUser($params['user_id']);
            if (empty($user)) {
                throw new ValidationException(static::$t->_('re_staff_info_id_not_exist'), ErrCode::$VALIDATE_ERROR);
            }

            $invalid_params = [
                'no'              => $params['order_no'],
                'operate_channel' => $params['platform'] ?? 0,
            ];

            AddService::getInstance()->invalidOrder($invalid_params, $user);
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->notice("报销申请列表-作废操作异常[发起人], 原因可能是: " . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
        ];
    }

    /**
     * 去签字信息(申请人)
     */
    public function goSignInfo($locale, $params)
    {
        BaseService::setLanguage($locale['locale']);

        $code    = ErrCode::$SUCCESS;
        $message = static::$t->_('success');
        $data    = [];

        try {
            if (empty($params['order_no'])) {
                throw new ValidationException(static::$t->_('params_error', ['param' => 'order_no']), ErrCode::$VALIDATE_ERROR);
            }

            $order_info = $this->getOrderInfo($params['order_no']);
            if ($order_info['apply_id'] != $params['user_id'] || $order_info['status'] != ReimbursementEnums::STATUS_WAITING_SIGNED) {
                throw new ValidationException(static::$t->_('reimbursement_save_error_029', ['user_id' => $params['user_id']]),
                    ErrCode::$VALIDATE_ERROR);
            }

            $user = UserService::getInstance()->getLoginUser($params['user_id']);
            if (empty($user)) {
                throw new ValidationException(static::$t->_('re_staff_info_id_not_exist'), ErrCode::$VALIDATE_ERROR);
            }

            // 申请人职位性质
            $user['job_position_type'] = HrJobDepartmentRelationRepository::getInstance()->getDepartmentJobInfo($user['node_department_id'],
                $user['job_title_id'])['position_type'] ?? '';

            $view_type = ReimbursementEnums::MOBILE_DETAIL_PAGE_TYPE_GO_SIGN;
            $data = DetailService::getInstance()->getDetailFromMobile($params['order_no'], $user, $view_type);
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->notice("报销申请列表-获取去签字信息异常[申请人], 原因可能是: " . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 拒绝签字(申请人)
     */
    public function rejectSign($locale, $params)
    {
        BaseService::setLanguage($locale['locale']);

        $code    = ErrCode::$SUCCESS;
        $message = static::$t->_('success');

        try {
            if (empty($params['order_no'])) {
                throw new ValidationException(static::$t->_('params_error', ['param' => 'order_no']), ErrCode::$VALIDATE_ERROR);
            }

            $reject_reason_len = mb_strlen($params['reject_reason'] ?? '');
            if ($reject_reason_len < 1 || $reject_reason_len > 500) {
                throw new ValidationException(static::$t->_('reimbursement_save_error_038'), ErrCode::$VALIDATE_ERROR);
            }

            $user = UserService::getInstance()->getLoginUser($params['user_id']);
            if (empty($user)) {
                throw new ValidationException(static::$t->_('re_staff_info_id_not_exist'), ErrCode::$VALIDATE_ERROR);
            }

            AddService::getInstance()->rejectSign($params, $user);
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->notice("报销申请列表-去签字-拒绝签字操作异常, 原因可能是: " . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
        ];
    }

    /**
     * 获取申请人身份信息
     */
    public function getApplyIDInfo($locale, $params)
    {
        BaseService::setLanguage($locale['locale']);

        $code    = ErrCode::$SUCCESS;
        $message = static::$t->_('success');
        $data    = [];

        try {
            if (empty($params['order_no'])) {
                throw new ValidationException(static::$t->_('params_error', ['param' => 'order_no']), ErrCode::$VALIDATE_ERROR);
            }

            $order_info = $this->getOrderInfo($params['order_no']);
            if ($order_info['apply_id'] != $params['user_id'] || $order_info['status'] != ReimbursementEnums::STATUS_WAITING_SIGNED) {
                throw new ValidationException(static::$t->_('reimbursement_save_error_029', ['user_id' => $params['user_id']]),
                    ErrCode::$VALIDATE_ERROR);
            }

            $data['identity_file'] = AddService::getInstance()->getApplyIDInfo($order_info['apply_id']);
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->notice("报销申请列表-去签字-获取申请人身份证信息异常, 原因可能是: " . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 签字提交(申请人)
     */
    public function submitSignInfo($locale, $params)
    {
        BaseService::setLanguage($locale['locale']);

        try {
            if (empty($params['order_no'])) {
                throw new ValidationException(static::$t->_('params_error', ['param' => 'order_no']), ErrCode::$VALIDATE_ERROR);
            }

            $identity_file = $params['identity_file'] ?? '';
            if (filter_var($identity_file, FILTER_VALIDATE_URL) == false) {
                throw new ValidationException(static::$t->_('params_error', ['param' => 'identity_file']), ErrCode::$VALIDATE_ERROR);
            }

            $signature_file = $params['signature_file'] ?? '';
            if (filter_var($signature_file, FILTER_VALIDATE_URL) == false) {
                throw new ValidationException(static::$t->_('params_error', ['param' => 'signature_file']), ErrCode::$VALIDATE_ERROR);
            }

            $user = UserService::getInstance()->getLoginUser($params['user_id']);
            if (empty($user)) {
                throw new ValidationException(static::$t->_('re_staff_info_id_not_exist'), ErrCode::$VALIDATE_ERROR);
            }

            $submit_params = [
                'no'                => $params['order_no'],
                'identity_file'     => $identity_file,
                'signature_file'    => $signature_file,
                'signature_channel' => $params['platform'] ?? 0,
            ];

            $res           = AddService::getInstance()->submitApplySignatureInfo($submit_params, $user);
            $code          = $res['code'];
            $message       = $res['message'];
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->notice("报销申请列表-去签字-签字提交操作异常, 原因可能是: " . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
        ];
    }

    /**
     * 扫码签字/待签字消息 -> 去签字鉴权
     */
    public function checkSignAuth($locale, $params)
    {
        BaseService::setLanguage($locale['locale']);

        $code    = ErrCode::$SUCCESS;
        $message = static::$t->_('success');
        $data    = null;

        try {
            if (empty($params['order_no'])) {
                throw new ValidationException(static::$t->_('params_error', ['param' => 'order_no']), ErrCode::$VALIDATE_ERROR);
            }

            $user = UserService::getInstance()->getLoginUser($params['user_id']);
            if (empty($user)) {
                throw new ValidationException(static::$t->_('re_staff_info_id_not_exist'), ErrCode::$VALIDATE_ERROR);
            }

            $order_info = $this->getOrderInfo($params['order_no']);
            if ($order_info['apply_id'] != $user['id']) {
                $data['status'] = ReimbursementEnums::AUTH_STATUS_APPLY_ERROR;
            }

            if (!isset($data['status']) && $order_info['status'] != ReimbursementEnums::STATUS_WAITING_SIGNED) {
                $data['status'] = ReimbursementEnums::AUTH_STATUS_ORDER_ERROR;
            }

            $data['status'] = $data['status'] ?? ReimbursementEnums::AUTH_STATUS_SUCCESS;
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->notice('去签字-单据状态或操作用户验证异常, 原因可能是: ' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 待确认消息 -> 去确认鉴权
     */
    public function checkConfirmAuth($locale, $params)
    {
        BaseService::setLanguage($locale['locale']);

        $code    = ErrCode::$SUCCESS;
        $message = static::$t->_('success');
        $data    = null;

        try {
            if (empty($params['order_no'])) {
                throw new ValidationException(static::$t->_('params_error', ['param' => 'order_no']), ErrCode::$VALIDATE_ERROR);
            }

            $user = UserService::getInstance()->getLoginUser($params['user_id']);
            if (empty($user)) {
                throw new ValidationException(static::$t->_('re_staff_info_id_not_exist'), ErrCode::$VALIDATE_ERROR);
            }

            $order_info = $this->getOrderInfo($params['order_no']);
            if ($order_info['status'] != ReimbursementEnums::STATUS_WAITING_CONFIRMED) {
                $data['status'] = ReimbursementEnums::AUTH_STATUS_ORDER_ERROR;
            }

            // 获取该用户待确认的共同住宿单
            if (!isset($data['status']) && empty($this->getStaffWaitingConfirmRoommateId($order_info['id'], $user['id']))) {
                $data['status'] = ReimbursementEnums::AUTH_STATUS_ORDER_ERROR;
            }

            $data['status'] = $data['status'] ?? ReimbursementEnums::AUTH_STATUS_SUCCESS;
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->notice('去确认-单据状态或操作用户验证异常, 原因可能是: ' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 获取当前用户待确认的共同住宿单
     */
    protected function getStaffWaitingConfirmRoommateId($main_id, $apply_staff_id)
    {
        return ReimbursementDetailTravelRoommateRelModel::findFirst([
                'conditions' => 're_id = :re_id: AND apply_staff_id = :apply_staff_id: AND confirm_status = :confirm_status:',
                'bind'       => [
                    're_id'          => $main_id,
                    'apply_staff_id' => $apply_staff_id,
                    'confirm_status' => ReimbursementEnums::TRAVEL_ROOMMATE_CONFIRM_STATUS_1,
                ],
                'columns'    => ['id'],
            ])->id ?? '';
    }

}
