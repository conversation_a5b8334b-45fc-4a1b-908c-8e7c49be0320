<?php

namespace app\modules\Rpc\callback;

use App\Library\Enums\MaterialWmsEnums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Services\StoreService;
use App\Modules\Material\Services\WmsAllotService;
use App\Modules\Material\Services\WmsApplyService;
use App\Modules\Material\Services\BaseService;
use App\Modules\Material\Services\WmsOutStorageService;
use App\Modules\Purchase\Services\BaseService as PurchaseService;
use GuzzleHttp\Exception\GuzzleException;

class MaterialWms extends \App\Library\BaseService
{
    /**
     * 耗材申请-可申请耗材列表
     * @param array $locale 当前语种
     * @param array $params 搜索条件
     * @return array
     */
    public function getWmsSauList(array $locale, array $params)
    {
        try {
            BaseService::setLanguage($locale['locale']);
            $params = trim_array($params);
            $params = BaseService::handleParams($params, WmsApplyService::$not_must_params);
            Validation::validate($params, WmsApplyService::getInstance()->getValidateSearchBarcode());
            $params['source_type'] = MaterialWmsEnums::SOURCE_TYPE_BY;
            return WmsApplyService::getInstance()->searchBarcode($params);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 耗材申请-输入基本信息回显
     * @param array $locale 当前语种
     * @param array $params 搜索条件
     * @return array
     */
    public function getWmsAddDefault(array $locale, array $params)
    {
        try {
            BaseService::setLanguage($locale['locale']);
            $params = trim_array($params);
            Validation::validate($params, ['id' => 'Required|IntGt:0', 'name' => 'Required|StrLenGeLe:1,50']);
            $result = WmsApplyService::getInstance()->getAddDefault($params);
            //所属公司列表
            $result['data']['cost_company'] = (new PurchaseService())->getCooCostCompany();
            return $result;
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 耗材申请-选择耗材使用地点
     * @param array $locale 当前语种
     * @param array $params 搜索条件
     * @return array
     */
    public function getWmsAddressList(array $locale, array $params)
    {
        try {
            BaseService::setLanguage($locale['locale']);
            $params = trim_array($params);
            $list   = (new StoreService())->getAddressList($params);
            return ['code' => ErrCode::$SUCCESS, 'message' => '', 'data' => $list];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 耗材申请-提交
     * @param array $locale 当前语种
     * @param array $params 提交数据
     * @return array
     */
    public function wmsApplyAdd(array $locale, array $params)
    {
        try {
            BaseService::setLanguage($locale['locale']);
            $params                = trim_array($params);
            $validate              = WmsApplyService::$validate_add;
            $validate['products']  = 'Required|Arr|ArrLenGeLe:1,50';
            $params['source_type'] = MaterialWmsEnums::SOURCE_TYPE_BY;
            Validation::validate($params, $validate);
            return WmsApplyService::getInstance()->add($params, ['id' => $params['user_id']]);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 耗材申请-撤回
     * @param array $locale 当前语种
     * @param array $params 搜索条件
     * @return array
     */
    public function wmsApplyCancel(array $locale, array $params)
    {
        try {
            BaseService::setLanguage($locale['locale']);
            $params = trim_array($params);
            Validation::validate($params, array_merge(WmsApplyService:: $validate_share, WmsApplyService::$validate_cancel));
            return WmsApplyService::getInstance()->cancel($params, ['id' => $params['user_id']]);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 耗材申请-详情
     * @param array $locale 当前语种
     * @param array $params 搜索条件
     * @return array
     */
    public function getWmsApplyDetail(array $locale, array $params)
    {
        try {
            BaseService::setLanguage($locale['locale']);
            $params           = trim_array($params);
            $validate         = WmsApplyService::$validate_workflow_no;
            $validate['type'] = 'Required|IntIn:1,2';
            Validation::validate($params, $validate);
            return WmsApplyService::getInstance()->detailForBy($params, $params['type'], ['id' => $params['user_id']]);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 耗材申请-审批通过
     * @param array $locale 当前语种
     * @param array $params 搜索条件
     * @return array
     */
    public function wmsApplyPass(array $locale, array $params)
    {
        try {
            BaseService::setLanguage($locale['locale']);
            $params = trim_array($params);
            Validation::validate($params, array_merge(WmsApplyService::$validate_share, WmsApplyService::$validate_pass));
            return WmsApplyService::getInstance()->pass($params, ['id' => $params['user_id']]);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 耗材申请- 驳回
     * @param array $locale 当前语种
     * @param array $params 搜索条件
     * @return array
     */
    public function wmsApplyReject(array $locale, array $params)
    {
        try {
            BaseService::setLanguage($locale['locale']);
            $params = trim_array($params);
            Validation::validate($params, array_merge(WmsApplyService::$validate_share, WmsApplyService::$validate_reject, WmsApplyService::$validate_pass));
            return WmsApplyService::getInstance()->reject($params, ['id' => $params['user_id']]);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 耗材申请-查看出库信息
     * @param array $locale 当前语种
     * @param array $params 搜索条件
     * @return array
     */
    public function getWmsOutStorageList(array $locale, array $params)
    {
        try {
            BaseService::setLanguage($locale['locale']);
            $params = trim_array($params);
            Validation::validate($params, WmsApplyService::$validate_workflow_no);
            return WmsApplyService::getInstance()->getWmsOutStorageList($params);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }


    /**
     * 耗材申请-查询收货人
     * @param array $locale 当前语种
     * @param array $params 搜索条件
     * @return array
     */
    public function getWmsApplyConsigneeList(array $locale, array $params)
    {
        try {
            BaseService::setLanguage($locale['locale']);
            $params = trim_array($params);
            Validation::validate($params, WmsApplyService::$validate_consignee);

            $user['id'] = $params['user_id'];
            $user['name'] = $params['user_name'];
            $user['sys_store_id'] = $params['sys_store_id'];
            return WmsApplyService::getInstance()->consigneeList($params, $user);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 耗材申请-获取抄送收货人站内信消息详情
     * @param array $locale 当前语种
     * @param array $params 搜索条件
     * @return array
     */
    public function getConsigneeCCMsg(array $locale, array $params)
    {
        try {
            BaseService::setLanguage($locale['locale']);
            $params = trim_array($params);
            Validation::validate($params, WmsApplyService::$validate_share);
            return WmsApplyService::getInstance()->getWmsApplyInfo($params['id']);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 耗材申请-根据快递单号查看物料详情
     * @param array $locale 当前语种
     * @param array $params 参数组
     * @return array
     */
    public function getOutboundExpressProduct(array $locale, array $params)
    {
        try {
            BaseService::setLanguage($locale['locale']);
            $params = trim_array($params);
            Validation::validate($params, WmsOutStorageService::$validate_express_sn);
            return WmsOutStorageService::getInstance()->getOutboundExpressProduct($params['express_sn']);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 耗材调拨单-站内信详情
     * @param array $locale 当前语种
     * @param array $params 参数组
     * @return array
     */
    public function getPackageAllotMsgInfo(array $locale, array $params)
    {
        try {
            BaseService::setLanguage($locale['locale']);
            $params = trim_array($params);
            Validation::validate($params, WmsAllotService::$validate_id);
            return WmsAllotService::getInstance()->getPackageAllotMsgInfo($params['id']);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 耗材调拨单-耗材调拨待处理数
     * @param array $locale 当前语种
     * @param array $params 参数组
     * @return array
     */
    public function getPackageAllotWaitHandleNum(array $locale, array $params)
    {
        try {
            BaseService::setLanguage($locale['locale']);
            $params = trim_array($params);
            Validation::validate($params, WmsAllotService::$validate_by_wait_handle_hot);
            return WmsAllotService::getInstance()->getPackageAllotWaitHandleNumForBy($params);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 耗材调拨单-获取枚举
     * @param array $locale 当前语种
     * @param array $params 参数组
     * @return array
     */
    public function getPackageAllotOptionsDefault(array $locale, array $params)
    {
        try {
            BaseService::setLanguage($locale['locale']);
            $params = trim_array($params);
            return WmsAllotService::getInstance()->getPackageAllotOptionsDefaultForBy();
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 耗材调拨单-待处理/已处理-列表
     * @param array $locale 当前语种
     * @param array $params 参数组
     * @return array
     */
    public function getPackageAllotList(array $locale, array $params)
    {
        try {
            BaseService::setLanguage($locale['locale']);
            $params = trim_array($params);
            $params = BaseService::handleParams($params, WmsAllotService::$not_must_params);
            Validation::validate($params, WmsAllotService::$validate_by_list);
            return WmsAllotService::getInstance()->getPackageAllotListForBy($params);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 耗材调拨单-查看
     * @param array $locale 当前语种
     * @param array $params 参数组
     * @return array
     */
    public function getPackageAllotInfo(array $locale, array $params)
    {
        try {
            BaseService::setLanguage($locale['locale']);
            $params = trim_array($params);
            Validation::validate($params, WmsAllotService::$validate_id);
            return WmsAllotService::getInstance()->detail($params);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 耗材调拨单-查看-查看物流
     * @param array $locale 当前语种
     * @param array $params 参数组
     * @return array
     */
    public function getPackageAllotExpressList(array $locale, array $params)
    {
        try {
            BaseService::setLanguage($locale['locale']);
            $params = trim_array($params);
            Validation::validate($params, WmsAllotService::$validate_id);
            return WmsAllotService::getInstance()->getExpressList($params);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 耗材调拨单-查看-查看路由
     * @param array $locale 当前语种
     * @param array $params 参数组
     * @return array
     * @throws GuzzleException
     */
    public function getPackageAllotExpressRoute(array $locale, array $params)
    {
        try {
            BaseService::setLanguage($locale['locale']);
            $params = trim_array($params);
            Validation::validate($params, WmsAllotService::$validate_express_sn);
            return WmsAllotService::getInstance()->getExpressRoute($params);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 耗材调拨单-无法调出
     * @param array $locale 当前语种
     * @param array $params 参数组
     * @return array
     */
    public function cancelPackageAllot(array $locale, array $params)
    {
        try {
            BaseService::setLanguage($locale['locale']);
            $params = trim_array($params);
            Validation::validate($params, array_merge(WmsAllotService::$validate_cancel, WmsAllotService::$validate_user));
            $params['cancel_source'] = MaterialWmsEnums::MATERIAL_PACKAGE_ALLOT_CANCEL_REASON_BY;
            return WmsAllotService::getInstance()->cancel($params, ['id' => $params['user_id'], 'name' => $params['user_name']]);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 耗材调拨单-确认调出
     * @param array $locale 当前语种
     * @param array $params 参数组
     * @return array
     */
    public function confirmOutPackageAllot(array $locale, array $params)
    {
        try {
            BaseService::setLanguage($locale['locale']);
            $params = trim_array($params);
            Validation::validate($params, array_merge(WmsAllotService::$validate_id, WmsAllotService::$validate_user));
            return WmsAllotService::getInstance()->confirmOut($params, ['id' => $params['user_id'], 'name' => $params['user_name']]);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 耗材调拨单-确认调入
     * @param array $locale 当前语种
     * @param array $params 参数组
     * @return array
     */
    public function confirmInPackageAllot(array $locale, array $params)
    {
        try {
            BaseService::setLanguage($locale['locale']);
            $params = trim_array($params);
            Validation::validate($params, array_merge(WmsAllotService::$validate_id, WmsAllotService::$validate_user));
            return WmsAllotService::getInstance()->confirmIn($params, ['id' => $params['user_id'], 'name' => $params['user_name']]);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }
}
