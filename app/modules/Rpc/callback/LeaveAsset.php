<?php

namespace app\modules\Rpc\callback;

use App\Library\BaseController;
use App\Library\Enums\MaterialClassifyEnums;
use App\Library\Enums\MaterialEnums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Material\Services\BaseService;
use App\Modules\Material\Services\LeaveAssetsManagerService;
use App\Modules\Material\Services\LeaveAssetsMessageService;
use App\Modules\Material\Services\LeaveAssetsService;
use App\Modules\Material\Services\StandardService;
use App\Modules\User\Services\UserService;
use App\Util\RedisKey;

class LeaveAsset extends \App\Library\BaseService
{
    /**
     * 离职资产-生成离职人员资产数据
     * @param array $locale 当前语种
     * @param array $params 搜索条件
     * @return array
     */
    public function createLeaveAsset($locale, $params)
    {
        try {
            //设置service语言
            BaseService::setLanguage($locale['locale']);
            //参数trim
            $params = trim_array($params);
            $this->logger->info('leave-assets-create-params : ' . json_encode($params, JSON_UNESCAPED_UNICODE));
            $return = LeaveAssetsService::getInstance()->createLeaveAsset($params);
            $this->logger->info('leave-assets-create-return : ' . json_encode($return, JSON_UNESCAPED_UNICODE));
            return $return;
        } catch (\Exception $e) {
            $this->logger->error('leave-assets-create-error !! : ' . json_encode($params, JSON_UNESCAPED_UNICODE));
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 离职资产-取消离职
     * @param array $locale 当前语种
     * @param array $params 搜索条件
     * @return array
     */
    public function cancelLeaveAsset($locale, $params)
    {
        try {
            //设置service语言
            BaseService::setLanguage($locale['locale']);
            //参数trim
            $params = trim_array($params);
            $this->logger->info('leave-assets-cancel-params : ' . json_encode($params, JSON_UNESCAPED_UNICODE));
            $return = LeaveAssetsService::getInstance()->cancelLeaveAsset($params);
            $this->logger->info('leave-assets-cancel-return : ' . json_encode($return, JSON_UNESCAPED_UNICODE));
            return $return;
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 离职资产-上级变更
     * @param array $locale 当前语种
     * @param array $params 搜索条件
     * @return array
     */
    public function leaveAssetManagerChange($locale, $params)
    {
        try {
            //设置service语言
            BaseService::setLanguage($locale['locale']);
            //参数trim
            $params = trim_array($params);
            $this->logger->info('leave-assets-manager-change-params : ' . json_encode($params, JSON_UNESCAPED_UNICODE));
            $return = LeaveAssetsService::getInstance()->leaveAssetManagerChange($params);
            $this->logger->info('leave-assets-cancel-return : ' . json_encode($return, JSON_UNESCAPED_UNICODE));
            return $return;
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 离职资产-by打卡限制-主管名下是否有待处理的员工离职资产任务,且员工最后工作日在在今天之前(包含今天)
     * @param array $locale 当前语种
     * @param array $params 搜索条件
     * @return array
     */
    public function hasLeaveAssets($locale, $params)
    {
        try {
            //设置service语言
            BaseService::setLanguage($locale['locale']);
            //参数trim
            $params = trim_array($params);
            $this->logger->info('leave-assets-has-leave-assets-params : ' . json_encode($params, JSON_UNESCAPED_UNICODE));
            $return = LeaveAssetsManagerService::getInstance()->hasLeaveAssets($params);
            $this->logger->info('leave-assets-has-leave-assets-return : ' . json_encode($return, JSON_UNESCAPED_UNICODE));
            return $return;
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 离职资产-离职须知barcode列表
     * @param $locale
     * @param $params
     * @return array
     * @date 2022/11/20
     */
    public function getAssetsByStaffId($locale, $params)
    {
        try {
            //设置service语言
            BaseService::setLanguage($locale['locale']);
            //获取用户信息
            $us = new UserService();
            $user = $us->getUserById($params['user_id']);
            $base_controller = new BaseController();
            $user_info = $base_controller->format_user($user);
            unset($params['user_id']);
            //参数trim
            $params = trim_array($params);
            return LeaveAssetsService::getInstance()->getAssetsByStaffId($params, $locale['locale'], $user_info);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 离职资产-离职须知barcode列表
     * @param $locale
     * @param $params
     * @return array
     * @date 2022/11/20
     */
    public function getAssetsDetailByStaffId($locale, $params)
    {
        try {
            //设置service语言
            BaseService::setLanguage($locale['locale']);
            //获取用户信息
            $us = new UserService();
            $user = $us->getUserById($params['user_id']);
            $base_controller = new BaseController();
            $user_info = $base_controller->format_user($user);
            unset($params['user_id']);
            //参数trim
            $params = trim_array($params);
            return LeaveAssetsService::getInstance()->getAssetsDetailByStaffId($params, $locale['locale'], $user_info);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 离职资产-获取离职资产站内信内容
     * @param $locale
     * @param $params
     * @return array
     * @date 2022/11/20
     */
    public function getLeaveMessageContent($locale, $params)
    {
        try {
            //设置service语言
            BaseService::setLanguage($locale['locale']);
            //获取用户信息
            $us = new UserService();
            $user = $us->getUserById($params['user_id']);
            $base_controller = new BaseController();
            $user_info = $base_controller->format_user($user);
            unset($params['user_id']);
            //参数trim
            $params = trim_array($params);
            return LeaveAssetsMessageService::getInstance()->getLeaveMessageContent($params, $locale['locale']);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 离职资产-获取员工离职资产详情(hcm-员工离职管理)
     * @param $locale
     * @param $params
     * @return array
     * @date 2022/11/20
     */
    public function getLeaveAssetsDetail($locale, $params)
    {
        try {
            //设置service语言
            BaseService::setLanguage($locale['locale']);
            //参数trim
            $params = trim_array($params);
            return LeaveAssetsService::getInstance()->getAssetsDetailHcm($params, $locale['locale']);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 离职资产-获取多个员工资产详情(hcm-导出)
     * @param $locale
     * @param $params
     * @return array
     * @date 2022/11/20
     */
    public function getNotReturnAssetsList($locale, $params)
    {
        try {
            //设置service语言
            BaseService::setLanguage($locale['locale']);
            //参数trim
            $params = trim_array($params);
            return LeaveAssetsService::getInstance()->getNotReturnAssetsList($params, $locale['locale']);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 离职资产-by端上级确认-列表
     * @param $locale
     * @param $params
     * @return array
     * @date 2023/3/18
     */
    public function getAssetsManagerList($locale, $params)
    {
        try {
            //设置service语言
            BaseService::setLanguage($locale['locale']);
            //获取用户信息
            $us = new UserService();
            $user = $us->getUserById($params['user_id']);
            $base_controller = new BaseController();
            $user_info = $base_controller->format_user($user);
            unset($params['user_id']);
            //参数trim
            $params = trim_array($params);
            return LeaveAssetsManagerService::getInstance()->getAssetsManagerList($params, $user_info);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 离职资产-by端上级确认-枚举
     * @param $locale
     * @param $params
     * @return array
     * @date 2023/3/18
     */
    public function getAssetsManagerDefault($locale, $params)
    {
        try {
            //设置service语言
            BaseService::setLanguage($locale['locale']);
            return LeaveAssetsManagerService::getInstance()->getDefault();
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 离职资产-by端上级确认-详情
     * @param $locale
     * @param $params
     * @return array
     * @date 2023/3/18
     */
    public function getAssetsManagerDetail($locale, $params)
    {
        try {
            //设置service语言
            BaseService::setLanguage($locale['locale']);
            //获取用户信息
            $us = new UserService();
            $user = $us->getUserById($params['user_id']);
            $base_controller = new BaseController();
            $user_info = $base_controller->format_user($user);
            unset($params['user_id']);
            //参数trim
            $params = trim_array($params);
            return LeaveAssetsManagerService::getInstance()->getAssetsManagerDetail($params, $locale['locale'], $user_info);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 离职资产-by端上级确认-搜索barcode
     * @param $locale
     * @param $params
     * @return array
     * @date 2023/3/18
     */
    public function searchAssetsManagerBarcode($locale, $params)
    {
        try {
            //设置service语言
            BaseService::setLanguage($locale['locale']);
            //获取用户信息
            $us = new UserService();
            $user = $us->getUserById($params['user_id']);
            $base_controller = new BaseController();
            $user_info = $base_controller->format_user($user);
            unset($params['user_id']);
            $params['status'] = MaterialClassifyEnums::MATERIAL_START_USING;
            return StandardService::getInstance()->searchBarcode($locale['locale'], $params);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 离职资产-by端上级确认-添加单个
     * @param $locale
     * @param $params
     * @return array
     * @date 2023/3/18
     */
    public function addAssetsManagerInfo($locale, $params)
    {
        try {
            //设置service语言
            BaseService::setLanguage($locale['locale']);
            //获取用户信息
            $us = new UserService();
            $user = $us->getUserById($params['user_id']);
            $base_controller = new BaseController();
            $user_info = $base_controller->format_user($user);
            unset($params['user_id']);
            //参数trim
            $params = trim_array($params);
            //参数验证
            $params = BaseService::handleParams($params, LeaveAssetsManagerService::$not_must_edit_add_by);
            Validation::validate($params, LeaveAssetsManagerService::$validate_edit_add_by);
            $lock_key = md5(RedisKey::LEAVE_ASSET_EDIT_ADD_LOCK . '_' . $params['id']);
            $res = $this->atomicLock(function () use ($params, $user_info) {
                return LeaveAssetsManagerService::getInstance()->editAddBy($params, $user_info);
            }, $lock_key, 30);
            return ['code' => $res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, 'message' => $res['message'] ?? $this->t->_('sys_processing'), 'data' => $res['data'] ?? []];
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 离职资产-by端上级确认-保存
     * @param $locale
     * @param $params
     * @return array
     * @date 2023/3/18
     */
    public function editSaveBy($locale, $params)
    {
        try {
            //设置service语言
            BaseService::setLanguage($locale['locale']);
            //获取用户信息
            $us = new UserService();
            $user = $us->getUserById($params['user_id']);
            $base_controller = new BaseController();
            $user_info = $base_controller->format_user($user);
            unset($params['user_id']);
            //参数trim
            $params = trim_array($params);
            //基本验证,必须要有的字段
            Validation::validate($params, LeaveAssetsManagerService::$validate_edit_save_basic_by);
            $lock_key = md5(RedisKey::LEAVE_ASSET_EDIT_SAVE_LOCK . '_' . $params['id']);
            $res = $this->atomicLock(function () use ($params, $user_info) {
                return LeaveAssetsManagerService::getInstance()->editSaveBy($params, $user_info);
            }, $lock_key, 30);
            return ['code' => $res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, 'message' => $res['message'] ?? $this->t->_('sys_processing'), 'data' => $res['data'] ?? []];
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 离职资产-by端上级确认-数量(红点)
     * @param $locale
     * @param $params
     * @return array
     * @date 2023/3/18
     */
    public function getLeaveAssetsManagerCount($locale, $params)
    {
        try {
            //设置service语言
            BaseService::setLanguage($locale['locale']);
            //获取用户信息
            $us = new UserService();
            $user = $us->getUserById($params['user_id']);
            $base_controller = new BaseController();
            $user_info = $base_controller->format_user($user);
            unset($params['user_id']);
            return LeaveAssetsManagerService::getInstance()->getLeaveAssetsManagerCount($user_info);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 离职资产-by端上级确认-删除
     * @param $locale
     * @param $params
     * @return array
     * @date 2023/3/18
     */
    public function deleteLeaveAssetsManager($locale, $params)
    {
        try {
            //设置service语言
            BaseService::setLanguage($locale['locale']);
            //获取用户信息
            $us = new UserService();
            $user = $us->getUserById($params['user_id']);
            $base_controller = new BaseController();
            $user_info = $base_controller->format_user($user);
            unset($params['user_id']);
            //参数trim
            $params = trim_array($params);
            //TODO 下次前端加上参数id, 然后加redis锁
            //$lock_key = md5(RedisKey::LEAVE_ASSET_EDIT_DELETE_LOCK . '_' . $params['id']);
            return LeaveAssetsManagerService::getInstance()->editDeleteBatch($params, $user_info);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 离职资产-by端离职申请-详情页资产列表
     * @param $locale
     * @param $params
     * @return array
     * @date 2023/3/18
     */
    public function getAuditLeaveAssets($locale, $params)
    {
        try {
            //设置service语言
            BaseService::setLanguage($locale['locale']);
            //参数trim
            $params = trim_array($params);
            return LeaveAssetsService::getInstance()->getAuditLeaveAssets($params, $locale['locale']);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 离职资产-by端离职申请-详情页资产列表
     * @param $locale
     * @param $params
     * @return array
     * @date 2023/3/18
     */
    public function getLeaveAssetsStaffCount($locale, $params)
    {
        try {
            //设置service语言
            BaseService::setLanguage($locale['locale']);
            //参数trim
            $params = trim_array($params);
            return LeaveAssetsService::getInstance()->getLeaveAssetsStaffCount($params, $locale['locale']);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 正式工转个人代理，对应的个人代理工号到岗确认，新工号变成在职的时候正式工离职资产处理逻辑
     * @param array $locale 语种
     * @param array $params 参数组
     * @return array
     */
    public function transferLeaveAssetsToPersonalAgent($locale, $params)
    {
        try {
            //设置service语言
            BaseService::setLanguage($locale['locale']);
            //参数trim
            $params = trim_array($params);
            return LeaveAssetsService::getInstance()->transferLeaveAssetsToPersonalAgent($params, $locale['locale']);
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }
}