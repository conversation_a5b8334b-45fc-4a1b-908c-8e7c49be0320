<?php

namespace app\modules\Rpc\callback;

/**
 * 开放kpi接口
 */

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Material\Services\BaseService;
use App\Modules\Kpi\Services\StaffService;

class Kpi extends \App\Library\BaseService
{

    public function changeLeader($locale, $params): array
    {
        try {
            BaseService::setLanguage($locale['locale']);
            $params          = trim_array($params);
            $validate_params = [
                // 直线上级变更后的ids 格式：array
                'change_leader' => 'Required|Arr|>>>:param error[change_leader]',
            ];
            Validation::validate($params, $validate_params);
            return StaffService::getInstance()->changeLeader($params);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }


}