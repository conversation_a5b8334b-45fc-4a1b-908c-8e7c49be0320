<?php
namespace app\modules\Rpc\callback;

use App\Library\BaseController;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Material\Services\AssetReturnService;
use App\Modules\Material\Services\BaseService;
use App\Modules\Material\Services\MaterialSettingService;
use App\Modules\User\Services\UserService;

class AssetReturn extends \App\Library\BaseService
{
    /**
     * 获取用户信息
     * @param integer $user_id 用户ID
     * @return array
     */
    public function getUserInfo($user_id)
    {
        $user = (new UserService())->getUserById($user_id);
        return (new BaseController())->format_user($user);
    }

    /**
     * 资产管理-退回类型
     * @param string $locale 当前语种
     * @param array $params 搜索条件
     * @return array
     */
    public function searchReturnType($locale, $params)
    {
        try {
            //设置service语言
            BaseService::setLanguage($locale['locale']);
            $params['type'] = MaterialSettingService::RETURN_REPAIR_SEARCH_TYPE_TYPE;
            return MaterialSettingService::getInstance()->returnRepairSetSearch($params);
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 资产管理-我的资产-退回（批量）
     * @param string $locale 当前语种
     * @param array $params 搜索条件
     * @return array
     */
    public function batchReturn($locale, $params)
    {
        try {
            //设置service语言
            BaseService::setLanguage($locale['locale']);
            //参数验证
            Validation::validate($params, AssetReturnService::$return_validate);

            //获取用户信息
            $user_info = $this->getUserInfo($params['user_id']);
            unset($params['user_id']);
            //参数trim
            $params = trim_array($params);
            return AssetReturnService::getInstance()->assetReturn($params, $user_info);
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 资产管理-我的资产-退回撤销（批量）
     * @param string $locale 当前语种
     * @param array $params 搜索条件
     * @return array
     */
    public function batchCancel($locale, $params)
    {
        try {
            //设置service语言
            BaseService::setLanguage($locale['locale']);
            // 参数验证
            Validation::validate($params, AssetReturnService::$cancel_by_validate);

            //获取用户信息
            $user_info = $this->getUserInfo($params['user_id']);
            unset($params['user_id']);
            //参数trim
            $params = trim_array($params);
            return AssetReturnService::getInstance()->conversionByCancel($params, $user_info);
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 资产管理-资产退回申请-列表
     * @param string $locale 当前语种
     * @param array $params 搜索条件
     * @return array
     */
    public function returnList($locale, $params)
    {
        try {
            //设置service语言
            BaseService::setLanguage($locale['locale']);
            // 参数验证
            $params = AssetReturnService::handleParams($params, AssetReturnService::$return_not_must_params);
            Validation::validate($params, AssetReturnService::$by_return_list_validate);

            //获取用户信息
            $user_info = $this->getUserInfo($params['user_id']);
            return AssetReturnService::getInstance()->returnList($params, $user_info);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 资产管理-资产退回申请-查看
     * @param string $locale 当前语种
     * @param array $params 搜索条件
     * @return array
     */
    public function returnDetail($locale, $params)
    {
        try {
            //设置service语言
            BaseService::setLanguage($locale['locale']);
            // 参数验证
            Validation::validate($params, AssetReturnService::$id_validate);
            //获取用户信息
            $user_info = $this->getUserInfo($params['user_id']);
            return AssetReturnService::getInstance()->returnDetailForBy($params, $user_info);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 资产管理-资产退回申请-撤销
     * @param string $locale 当前语种
     * @param array $params 搜索条件
     * @return array
     */
    public function returnCancel($locale, $params)
    {
        try {
            //设置service语言
            BaseService::setLanguage($locale['locale']);
            // 参数验证
            Validation::validate($params, AssetReturnService::$cancel_validate);

            //获取用户信息
            $user_info = $this->getUserInfo($params['user_id']);
            return AssetReturnService::getInstance()->assetCancel($params, $user_info);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }

    /**
     * 资产管理-资产退回申请-撤销
     * @param string $locale 当前语种
     * @param array $params 搜索条件
     * @return array
     */
    public function returnAddAssetList($locale, $params)
    {
        try {
            //设置service语言
            BaseService::setLanguage($locale['locale']);
            // 参数验证
            $params = AssetReturnService::handleParams($params, AssetReturnService::$return_not_must_params);
            Validation::validate($params, AssetReturnService::$by_return_list_validate);

            //获取用户信息
            $user_info = $this->getUserInfo($params['user_id']);
            return AssetReturnService::getInstance()->returnAddAssetList($params, $user_info);
        } catch (ValidationException $e) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'message' => $e->getMessage(), 'data' => []];
        } catch (\Exception $e) {
            return ['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => []];
        }
    }
}