<?php

namespace App\Modules\ReserveFund\Services;

use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\PayEnums;
use App\Library\Enums\ReserveFundReturnEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\OssHelper;
use App\Library\Validation\ValidationException;
use App\Models\oa\MiniHrStaffInfoModel;
use App\Models\oa\ReserveFundApplyReturnRelModel;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Common\Services\StoreService;
use App\Modules\Common\Services\WaterMarkerService;
use App\Modules\Hc\Models\HrStaffInfoModel;
use App\Modules\Pay\Services\PayService;
use App\Modules\Reimbursement\Models\Reimbursement;
use App\Modules\Reimbursement\Services\ListService;
use App\Modules\ReserveFund\Models\ReserveFundApply;
use App\Modules\ReserveFund\Models\ReserveFundReimburse;
use App\Modules\ReserveFund\Models\ReserveFundReturn;
use App\Modules\Setting\Services\CommonDataPermissionService;
use App\Modules\User\Models\AttachModel;
use App\Modules\User\Models\DepartmentModel;
use App\Modules\User\Models\StoreModel;
use App\Modules\User\Services\UserService;
use App\Modules\Workflow\Services\FYRService;
use App\Modules\Workflow\Services\WorkflowServiceV2;
use Exception;
use GuzzleHttp\Exception\GuzzleException;
use Mpdf\Mpdf;

class ApplyService extends BaseService
{
    const NOTREQUIRED_LONG_TEXT_LEN = 'StrLenGeLe:0,100';
    const REQUIRED_LONG_TEXT_LEN = 'Required|StrLenGeLe:1,100';
    const CREATE_STORE_NAME_LEN = 40;

    public static $validate_detail = [
        'id' => 'Required|IntGe:1',
    ];

    public static $validate_audit = [
        'id'          => 'Required|IntGe:1',
        'pass_or_not' => 'Required|IntIn:1,2',
        'note'        => 'IfIntEq:pass_or_not,2|Required|StrLenGeLe:1,1000',
    ];

    public static $validate_cancel = [
        'id'   => 'Required|IntGe:1',
        'note' => 'Required|StrLenGeLe:1,1000',
    ];
    // url校验
    public static $validate_url_param = [
        'name' => 'Required|StrLenGeLe:1,256|>>>:param error',
        'url'  => 'Required|StrLenGeLe:64,1024|>>>:param error',
    ];

    public static $validate_export_reimburse = [
        'rfano' => 'Required|StrLenGeLe:1,20',
    ];


    public static $validate_param     = [
        'rfano'                     => 'Required|StrLenGeLe:10,20',
        'apply_date'                => 'Required|Date',
        'create_id'                 => 'Required',
        'create_name'               => 'Required',
        'create_department_id'      => 'Required',
        'create_department_name'    => self::REQUIRED_LONG_TEXT_LEN,
        'create_company_id'         => 'Required',
        'create_company_name'       => self::REQUIRED_LONG_TEXT_LEN,
        'create_store_id'           => 'Required',
//        'create_store_name' => self::REQUIRED_LONG_TEXT_LEN,
        'payee_username'            => 'Required',
        'payee_bank'                => 'Required|IntIn:0,1,2,21',
        'payee_account'             => self::REQUIRED_LONG_TEXT_LEN,
        'amount'                    => 'Required|FloatGe:0',
        'apply_reason'              => 'Required|StrLenGeLe:1,5000',
        'currency'                  => 'Required|IntIn:' . GlobalEnums::VALIDATE_CURRENCY_PARAMS,
        'pc_code'                   => 'StrLenGeLe:0,10',
        'attachment'                => 'Required|Arr|ArrLenGe:1',
        'attachment[*].bucket_name' => 'Required',
        'attachment[*].object_key'  => 'Required',
        'attachment[*].file_name'   => 'Required',
    ];
    public static $validate_pay_param = [
        'id'          => 'Required|IntGe:1',
        'pass_or_not' => 'Required|IntIn:1,2',
        'real_pay_at' => 'IfIntEq:pass_or_not,1|Required|Date',
        'pay_bank'    => 'IfIntEq:pass_or_not,1|Required|StrIn:Thai Military Bank,Siam Commercial Bank,Union Bank 1213,Union Bank 0429,CIMB,Public Bank|>>>:payment bank error',
        'pay_account' => 'IfIntEq:pass_or_not,1|Required|StrIn:**********,**********,************,************,**********,**********|>>>:payment bank account error',
        'note'        => 'IfIntEq:pass_or_not,2|Required|StrLenGeLe:1,1000',
    ];

    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return ApplyService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 备用金申请单获得默认值
     *
     * @param $user
     * @return array
     */
    public function defaultData($user)
    {
        $code         = ErrCode::$SUCCESS;
        $message      = 'success';
        $real_message = '';

        $data = [];
        try {
            if (empty($user)) {
                throw new ValidationException("The employee information is null [{$user['id']}]",
                    ErrCode::$VALIDATE_ERROR);
            }

            $arr = $this->getUserMetaFromBi($user['id']);
            if (empty($arr)) {
                throw new ValidationException(static::$t->_("re_staff_info_id_not_exist"), ErrCode::$VALIDATE_ERROR);
            }

            $now_date                = date("Ymd");
            $data['rfano']           = 'WDBYJ' . static::getNo($now_date);
            $data['apply_date']      = date("Y-m-d", strtotime($now_date));
            $data['create_id']       = $user['id'];
            $data                    = array_merge($data, $arr);
            $data['create_name']     = $this->getNameAndNickName($user['name'] ?? "", $user['nick_name'] ?? "");
            $data['currency']        = $this->getApplyCurrency();
            $data['cost_company_id'] = (new EnumsService())->getSettingEnvValueIds('purchase_sap_company_ids');
            /**
             * 记录网点的pc code
             * 12362【ALL|OA|备用金】备用金功能优化 二期
             * https://l8bx01gcjr.feishu.cn/docs/doccncFto251qTBdqAup89T7JId#
             */
            $data['pc_code'] = (new StoreService())->getPcCodeByStoreId($data['create_store_id']);
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $data         = [];
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('reserve_fund-get-default-data-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * @param $id
     * @param $uid
     * @param bool $if_download
     * @param null $flag
     * @return array
     */
    public function getDetail($id, $uid = null, $if_download = false, $flag = null)
    {
        try {
            $apply = ReserveFundApply::findFirst([
                'id = :id:',
                'bind' => ['id' => $id],
            ]);

            if (empty($apply)) {
                return [];
            }
            $req = (new PayFlowService(Enums::WF_RESERVE_FUND_APPLY))->getRequest($id);
            if (empty($req->id)) {
                throw new BusinessException('获取工作流批次失败');
            }

            $attachment = $apply->getFile(["columns" => "bucket_name,object_key,file_name"]);
            $attachment = $attachment ? $attachment->toArray() : [];

            $data = $apply->toArray();


            $data['attachment'] = $attachment;
            $ask                = (new FYRService())->getRequestToByReplyAsk($req, $uid);
            $data['ask_id']     = $ask ? $ask->id : '';

            $data['auth_logs'] = $this->getAuditLogs($req, $apply, $if_download);

            $data = $this->handleDetailData($data);
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (Exception $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('ReserveFund-Apply-get-audit-detail-failed:' . $real_message);
        }
        return [
            'code'    => $code ?? ErrCode::$SUCCESS,
            'message' => $message ?? 'success',
            'data'    => $data ?? [],
        ];
    }

    /**
     * @param $data
     * @param $is_filter
     * @return array
     */
    private function handleDetailData($data)
    {
        if (empty($data) || !is_array($data)) {
            return [];
        }
        $status                  = Enums::$loan_status[$data['status']] ?? '';
        $data['amount']          = bcdiv($data['amount'], 1000, 2);
        $data['status_text']     = static::$t->_($status);
        $payment_currency        = GlobalEnums::$currency_item[$data['currency']] ?? '';
        $data['currency_text']   = static::$t->_($payment_currency);
        $data['pay_status_text'] = static::$t->_(Enums::$loan_pay_status[$data['pay_status']]);
        $data['payee_bank_text'] = Enums::$bank_type[$data['payee_bank']];
        // 付款银行和账号枚举
        $data['payee_bank_list'] = json_decode(EnvModel::getEnvByCode('reimbursement_bank'), true);

        return $data;
    }


    private function getAuditLogs($req, $item, $if_download = false)
    {
        $auth_logs = (new WorkflowServiceV2())->getAuditLogs($req);

        //下载的时候不要申请
        if ($if_download) {
            $temp = [];
            foreach ($auth_logs as $k => $v) {
                //如果申请的就跳过
                if ($v['action'] == 0) {
                    continue;
                }
                $temp[] = $v;
            }
            $auth_logs = $temp;
        }
        $pay_staff_id = $this->getReserveFundPaymentPayStaffIds();
        //查询支付模块的审批流
        if ($item->is_pay_module == PayEnums::BIZ_DATA_IS_PAY_MODULE_YES) {
            $payment_data = PayService::getInstance()->getPaymentByBusinessNo(Enums\BankFlowEnums::BANK_FLOW_OA_TYPE_RESERVE_FUND,
                $item->rfano);
            if (!empty($payment_data)) {
                $pay_flow_service   = new \App\Modules\Pay\Services\PayFlowService();
                $payment_audit_logs = $pay_flow_service->getAuditLogs($payment_data, true);
                //上下文必须保证两个数组是索引数组,且$payment_audit_logs排在$auth_logs之前
                $auth_logs = array_merge($payment_audit_logs, $auth_logs);
                //查到支付模块数据直接返回, 没查到的继续走下边的拼接支付人逻辑(兼容开启支付模块后历史数据审批通过未支付完成的)
                return $auth_logs;
            }
        }
        $us = new UserService();
        //审核通过，待支付，下载不要待支付的
        if ($item->status == Enums::CONTRACT_STATUS_APPROVAL && $item->pay_status == Enums::LOAN_PAY_STATUS_PENDING && !$if_download) {
            $payPendingLogs = [
                'action_name'       => self::$t->_(Enums::$loan_pay_status[$item->pay_status]),
                'audit_at'          => $item->approve_at,
                'audit_at_datetime' => $item->approve_at,
                'action'            => 5,
                "info"              => $item->remark ?? '',
            ];
            foreach ($pay_staff_id as $staff_id) {
                $current = $us->getUserById($staff_id);
                if (!empty($current)) {
                    //待支付
                    $payPendingLogs['list'][] = [
                        'staff_id'         => $staff_id,
                        'staff_name'       => $this->getNameAndNickName($current->name, $current->nick_name ?? ''),
                        'staff_department' => $current->getDepartment()->name ?? '',
                        'job_title'        => $current->getJobTitle()->name ?? '',
                    ];
                }
            }
            array_unshift($auth_logs, $payPendingLogs);
        }
        //审核通过，已支付或者未支付
        if ($item->status == Enums::CONTRACT_STATUS_APPROVAL && ($item->pay_status == Enums::LOAN_PAY_STATUS_PAY || $item->pay_status == Enums::LOAN_PAY_STATUS_NOTPAY)) {
            if (!empty($item->pay_id)) {
                $current = $us->getUserById($item->pay_id);
                //放入付款
                if ($current && !is_string($current)) {
                    $payLogs = [
                        'staff_id'          => $item->pay_id,
                        'staff_name'        => $this->getNameAndNickName($current->name, $current->nick_name ?? ''),
                        'staff_department'  => $current->getDepartment()->name ?? '',
                        'job_title'         => $current->getJobTitle()->name ?? '',
                        'action_name'       => self::$t->_(Enums::$loan_pay_status[$item->pay_status]),
                        'audit_at'          => $item->pay_at,
                        'audit_at_datetime' => $item->pay_at,
                        'action'            => 5,
                        "info"              => $item->operation_remark,
                    ];
                    array_unshift($auth_logs, $payLogs);
                }
            }
        }
        //下载要正序
        if ($if_download) {
            $auth_logs = array_reverse($auth_logs);
        }
        return $auth_logs;
    }

    /**
     * @param $data
     * @param $user
     * @return array
     */
    public function saveOne($data, $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db      = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            // 校验该工号在HRIS系统内的职位和网点信息
            $job_store_valid = $this->isValidJobStore($user);
            if (!$job_store_valid) {
                throw new ValidationException(static::$t->_('header_office_staff_not_allowed'),
                    ErrCode::$VALIDATE_ERROR);
            }

            // 仅网点可申请备用金
            $check_store = EnvModel::getEnvByCode('reserve_fund_apply_store_workflow', 0);
            if ($check_store) {
                if (empty($data['create_store_id']) || $data['create_store_id'] == Enums::HEAD_OFFICE_STORE_FLAG) {
                    throw new ValidationException(static::$t->_('reserve_fund_apply_check_store'),
                        ErrCode::$VALIDATE_ERROR);
                }
            }

            //非在职状态 不能申请
            if($user['state'] != StaffInfoEnums::STAFF_STATE_IN || $user['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES){
                throw new ValidationException(static::$t->_('fund_state_notice'), ErrCode::$VALIDATE_ERROR);
            }

            // 当申请人公司在配置purchase_sap_company_ids中时，费用所属中心字段：必填
            $cost_company_id = (new EnumsService())->getSettingEnvValueIds('purchase_sap_company_ids');
            if (in_array($data['create_company_id'], $cost_company_id) && empty($data['pc_code'])) {
                throw new ValidationException(static::$t->_('cost_center_required_error'), ErrCode::$VALIDATE_ERROR);
            }

            $uid             = (int)$user['id'];
            $staff_info      = HrStaffInfoModel::getUserInfo($uid);
            $create_store_id = $staff_info['sys_store_id'] ?? 0;

            /**
             * 根据优化方案做如下逻辑判断调整
             * 12362【ALL|OA|备用金】备用金功能优化 二期
             * https://l8bx01gcjr.feishu.cn/docs/doccncFto251qTBdqAup89T7JId#WTuhsb
             */
            if ($create_store_id === 0) {
                throw new ValidationException(static::$t->_('reserve_fund_apply_check_store'),
                    ErrCode::$VALIDATE_ERROR);
            }

            // 第一步判断申请人所属网点下是否有申请中或未归还的申请单记录
            $this->hasApplyForStore($create_store_id);

            // 第二步判断申请人名下是否有申请中或未归还的申请单记录
            $this->hasApplyForUser($uid);

            // 第三步组装申请单信息
            $data = $this->handleData($data, $user);
            if (is_string($data)) {
                throw new ValidationException($data, ErrCode::$VALIDATE_ERROR);
            }

            // TODO 13283取消马来和菲律宾的金额限制,后期不需要限制金额可以删除specialAmountTip方法
            // 金额特殊限制
            //$is_valid = $this->specialAmountTip($user,$data);
            //if (!$is_valid) {
            //    throw new ValidationException(static::$t->_('over_max_amount_invalid'), ErrCode::$VALIDATE_ERROR);
            //}

            $model = new ReserveFundApply();
            $bool  = $model->i_create($data);
            if ($bool === false) {
                $messages = $model->getMessages();
                throw new BusinessException('备用金申请创建失败=' . implode(",", $messages), ErrCode::$CONTRACT_CREATE_ERROR);
            }
            if (!empty($data['attachment'])) {
                $attachments = [];
                foreach ($data['attachment'] as $attachment) {
                    $tmp                    = [];
                    $tmp['oss_bucket_type'] = Enums::OSS_BUCKET_TYPE_RESERVE_FUND_APPLY;
                    $tmp['oss_bucket_key']  = $model->id;
                    $tmp['bucket_name']     = $attachment['bucket_name'];
                    $tmp['object_key']      = $attachment['object_key'];
                    $tmp['file_name']       = $attachment['file_name'];
                    $attachments[]          = $tmp;
                }
                $attach_bool = (new AttachModel())->batchInsert($attachments);
                if ($attach_bool === false) {
                    throw new BusinessException('备用金申请-附件创建失败', ErrCode::$CONTRACT_CREATE_ERROR);
                }
            }
            $flow_bool = (new PayFlowService(Enums::WF_RESERVE_FUND_APPLY))->createRequest($model->id, $user);
            if ($flow_bool === false) {
                throw new BusinessException(static::$t->_('loan_create_work_flow_failed'),
                    ErrCode::$LOAN_CREATE_WORK_FLOW_ERROR);
            }

            $db->commit();
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            if (in_array($e->getCode(), [ErrCode::$BUDGET_OVERAMOUNT_QUARTERLY, ErrCode::$BUDGET_OVERAMOUNT_MONTH])) {
                $code   = ErrCode::$SUCCESS;
                $result = [
                    'message'   => $e->getMessage(),
                    'can_apply' => $e->getCode() == ErrCode::$BUDGET_OVERAMOUNT_QUARTERLY ? 0 : 1,
                ];
            } else {
                $code    = $e->getCode();
                $message = $e->getMessage();
            }
        } catch (BusinessException $e) {
            //业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (Exception $e) {
            //系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->warning('ReserveFundApply-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $result ?? [],
        ];
    }

    /**
     * 判断某网点下是否有待审核或审核通过已支付申请中或未归还的申请单记录
     *
     * @param string $create_store_id 网点ID
     * @throws ValidationException
     */
    private function hasApplyForStore($create_store_id)
    {
        $count = ReserveFundApply::count([
            'conditions' => 'create_store_id = :store_id: and (status=:status1: or (status=:status2: and pay_status!=:pay_status:)) and return_status in({return_status:array})',
            'bind'       => [
                'store_id'      => $create_store_id,
                'status1'       => ReserveFundReturnEnums::STATUS_WAIT,
                'status2'       => ReserveFundReturnEnums::STATUS_PASS,
                'pay_status'    => ReserveFundReturnEnums::PAY_STATUS_NOTPAY,
                'return_status' => [
                    ReserveFundReturnEnums::BACK_STATUS_INIT,
                    ReserveFundReturnEnums::BACK_STATUS_NOT,
                    ReserveFundReturnEnums::BACK_STATUS_ING,
                    ReserveFundReturnEnums::BACK_STATUS_CONFIRM,
                ],
            ],
        ]);
        if ($count > 0) {
            throw new ValidationException(static::$t->_('only_one_store_reserve_fund_apply'), ErrCode::$VALIDATE_ERROR);
        }
    }

    /**
     * 判断某申请人名下是否有待审核或审核通过已支付申请中或未归还的申请单记录
     *
     * @param integer $uid 申请人工号
     * @throws ValidationException
     */
    private function hasApplyForUser($uid)
    {
        $info = ReserveFundApply::findFirst([
            'conditions' => 'create_id = :create_id: and (status=:status1: or (status=:status2: and pay_status!=:pay_status:)) and return_status in({return_status:array})',
            'bind'       => [
                'create_id'     => $uid,
                'status1'       => ReserveFundReturnEnums::STATUS_WAIT,
                'status2'       => ReserveFundReturnEnums::STATUS_PASS,
                'pay_status'    => ReserveFundReturnEnums::PAY_STATUS_NOTPAY,
                'return_status' => [
                    ReserveFundReturnEnums::BACK_STATUS_INIT,
                    ReserveFundReturnEnums::BACK_STATUS_NOT,
                    ReserveFundReturnEnums::BACK_STATUS_ING,
                    ReserveFundReturnEnums::BACK_STATUS_CONFIRM,
                ],
            ],
            'order'      => 'id desc',
        ]);
        if (isset($info->id) && !empty($info->id)) {
            throw new ValidationException(str_replace('{store_name}', $info->create_store_name,
                static::$t->_('reback_exist_reserve_fund')), ErrCode::$VALIDATE_ERROR);
        }
    }

    /**
     * 格式化申请单信息
     *
     * @param $data
     * @param $user
     * @return array|string
     */
    private function handleData($data, $user)
    {
        if (empty($data) || !is_array($data)) {
            return 'data empty';
        }

        $staffInfo                    = (new UserService())->getUserByIdInRbi($user['id']);
        $data['amount']               = bcmul(floatval($data['amount']), 1000);
        $data['status']               = Enums::CONTRACT_STATUS_PENDING;
        $data['created_at']           = date('Y-m-d H:i:s');
        $data['updated_at']           = $data['created_at'];
        $data['create_id']            = $user['id'] ?? 0;
        $data['create_name']          = $this->getNameAndNickName($user['name'] ?? '', $user['nick_name'] ?? '');
        $data['create_store_id']      = $staffInfo->sys_store_id;
        $data['create_department_id'] = empty($staffInfo->node_department_id) ? $staffInfo->sys_department_id : $staffInfo->node_department_id;
        $data['payee_username']       = $staffInfo->name;
        $data['payee_bank']           = $staffInfo->bank_type;
        $data['payee_account']        = $staffInfo->bank_no ?? '';
        $data['pay_status']           = Enums::LOAN_PAY_STATUS_PENDING;

        // 获取币种与系统默认币种的汇率
        $exchange_rate         = EnumsService::getInstance()->getCurrencyExchangeRate($data['currency']);
        $data['exchange_rate'] = $exchange_rate ? $exchange_rate : 1;

        $data['voucher_description'] = ($user['id'] ?? 0) . '-' . $data['create_store_name'];
        if (strlen(($user['id'] ?? 0) . $data['create_store_name']) > self::CREATE_STORE_NAME_LEN) {
            $data['voucher_description'] = ($user['id'] ?? 0) . '-' . $data['create_store_id'];
        }
        return $data;
    }

    /**
     * 申请列表
     *
     * @param $condition
     * @param array $user
     * @param int $type
     * @return array
     */
    public function getList($condition, $user = [], $type = 0)
    {
        $condition['uid'] = $user['id'];
        $page_size        = empty($condition['pageSize']) ? 20 : $condition['pageSize'];
        $page_num         = empty($condition['pageNum']) ? 1 : $condition['pageNum'];
        $offset           = $page_size * ($page_num - 1);

        $code    = ErrCode::$SUCCESS;
        $message = '';
        $data    = [];

        try {
            if ($type == self::LIST_TYPE_PAYMENT) {
                $reserve_fund_payment_pay_staff_id = $this->getReserveFundPaymentPayStaffIds();
                if (!in_array($user['id'], $reserve_fund_payment_pay_staff_id)) {
                    throw new ValidationException(static::$t->_('no_pay_auth_access_msg'),
                        ErrCode::$COMMON_STAFF_PAY_AUTH_FAIL_ERROR);
                }
            }

            $builder = $this->modelsManager->createBuilder();
            $builder->from(['r' => ReserveFundApply::class]);
            $builder = $this->getCondition($builder, $condition, $user, $type);

            //先查询总数
            $count = (int)$builder->columns('COUNT(DISTINCT r.id) AS total')->getQuery()->getSingleResult()->total;

            $items = [];
            if ($count) {
                $column_str = 'r.id,r.rfano,r.apply_date,r.amount,r.status,r.is_cite,r.currency,r.pay_status, r.return_status, staff.state, staff.wait_leave_state';
                if (in_array($type, [self::LIST_TYPE_QUERY, self::LIST_TYPE_APPLY])) {
                    $column_str .= ",rfrei.rei_id rfrei_id, rf.rrno, rf.status as verify_status, apply_return_rel.return_id";
                }

                if (in_array($type,
                    [0, self::LIST_TYPE_AUDIT, self::LIST_TYPE_FYR, self::LIST_TYPE_PAYMENT, self::LIST_TYPE_QUERY])) {
                    $column_str .= ",r.create_id,r.create_name,r.create_company_name,r.create_department_name,r.create_store_id, r.create_store_name";
                }

                // 审核模块的已处理列表, 展示处理时间
                if ($type == self::LIST_TYPE_AUDIT && isset($condition['flag']) && $condition['flag'] == GlobalEnums::AUDIT_TAB_PROCESSED) {
                    $column_str .= ',log.audit_at';
                }

                $builder->columns($column_str);

                if ($type == self::LIST_TYPE_PAYMENT) {
                    if ($condition['flag'] == GlobalEnums::PAYMENT_TAB_PENDING) {
                        $builder->orderBy('r.approve_at asc');
                    } else {
                        $builder->orderBy('r.pay_at desc');
                    }
                } else {
                    if (!in_array($type, [self::LIST_TYPE_AUDIT, self::LIST_TYPE_FYR])) {
                        $builder->orderBy('r.id desc');
                    }
                }

                $builder->groupBy('r.id');
                $builder->limit($page_size, $offset);

                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleItems($items, $condition, $type);
            }

            $data = [
                'items'      => $items,
                'pagination' => [
                    'current_page' => $page_num,
                    'per_page'     => $page_size,
                    'total_count'  => $count,
                ],
            ];
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('reserve-fund-list-failed:' . $e->getMessage());
        }

        // 无权限的情况,返回正常结构的空数据,按成功处理
        if ($code == ErrCode::$COMMON_STAFF_PAY_AUTH_FAIL_ERROR) {
            $code    = ErrCode::$SUCCESS;
            $message = 'success';
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * @param $builder
     * @param $condition
     * @param $user
     * @param int $type
     * @return mixed
     * @throws BusinessException
     */
    private function getCondition($builder, $condition, $user, $type = 0)
    {
        $rfano            = $condition['rfano'] ?? '';
        $status           = $condition['status'] ?? 0;
        $pay_status       = $condition['pay_status'] ?? 0;
        $apply_date_start = $condition['created_at_start'] ?? '';
        $apply_date_end   = $condition['created_at_end'] ?? '';
        $create_id        = $condition['create_name'] ?? '';
        $store_id         = $condition['create_store_id'] ?? '';

        // 征询回复进度
        $is_reply = $condition['is_reply'] ?? GlobalEnums::CONSULTED_REPLY_STATE_PENDING;
        $is_reply = in_array($is_reply,
            GlobalEnums::CONSULTED_REPLY_STATE_ITEM) ? $is_reply : GlobalEnums::CONSULTED_REPLY_STATE_PENDING;

        // 审批处理进度
        $flag = $condition['flag'] ?? GlobalEnums::AUDIT_TAB_PENDING;
        $flag = in_array($flag, GlobalEnums::AUDIT_TAB_STATE_ITEM) ? $flag : GlobalEnums::AUDIT_TAB_PENDING;

        // 关联报销单
        $reimno = $condition['reimno'] ?? '';
        // 备用金归还单号
        $rrno = $condition['rrno'] ?? '';
        // 归还状态
        $return_status = $condition['return_status'] ?? 0;
        // 归还日期
        $return_date_start = $condition['return_date_start'] ?? '';
        $return_date_end   = $condition['return_date_end'] ?? '';
        // 申请人所属公司
        $create_company_id = $condition['create_company_id'] ?? [];

        // 创建单据的开始时间
        $create_start_time = $condition['create_start_time'] ?? '';

        // 创建单据的结束时间
        $create_end_time = $condition['create_end_time'] ?? '';
        $builder->leftjoin(MiniHrStaffInfoModel::class, 'r.create_id = staff.staff_info_id', 'staff');
        if ($type == self::LIST_TYPE_QUERY || ApplyService::LIST_TYPE_APPLY == $type) {
            $builder->leftjoin(ReserveFundReimburse::class, 'rfrei.rfano=r.rfano', 'rfrei');
            $builder->leftjoin(Reimbursement::class, 'rei.id=rfrei.rei_id', 'rei');
            //16053需求修改为通过申请单归还单关联关系表查询归还数据
            $builder->leftjoin(ReserveFundApplyReturnRelModel::class,
                'apply_return_rel.apply_id = r.id and apply_return_rel.is_deleted = ' . GlobalEnums::IS_NO_DELETED,
                'apply_return_rel');
            $builder->leftjoin(ReserveFundReturn::class,
                'rf.id = apply_return_rel.return_id and apply_return_rel.is_deleted = ' . GlobalEnums::IS_NO_DELETED,
                'rf');
        }

        //在职状态查询
        if(!empty($condition['staff_status'])){
            if (in_array(StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE, $condition['staff_status'])) {
                $builder->andWhere("staff.state IN ({states:array}) OR (staff.state=:state: AND staff.wait_leave_state=:wait_leave_state:)",
                    [
                        'states'           => $condition['staff_status'],
                        'state'            => StaffInfoEnums::STAFF_STATE_IN,
                        'wait_leave_state' => StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES,
                    ]);
            } elseif (!in_array(StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE, $condition['staff_status']) && in_array(StaffInfoEnums::STAFF_STATE_IN, $condition['staff_status'])) {
                $builder->andWhere("staff.state IN ({states:array}) AND staff.wait_leave_state != :wait_leave_state:",
                    ['states' => $condition['staff_status'], 'wait_leave_state' => StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES]);
            } else {
                $builder->andWhere("staff.state IN ({states:array})", ['states' => $condition['staff_status']]);
            }
        }

        // 审核列表
        if ($type == self::LIST_TYPE_AUDIT) {
            $builder = (new WorkflowServiceV2())->getBizWorkflowOrderListByProcessState($builder, $flag,
                [Enums::WF_RESERVE_FUND_APPLY], $condition['uid'], 'r');
        } else {
            if ($type == self::LIST_TYPE_APPLY) {
                $builder->andWhere('r.create_id = :uid:', ['uid' => $condition['uid']]);
                // 申请列表: 申请人搜索选项是多余的, 因此置空, 无需参与模糊查询
                $create_id = '';
            } else {
                if ($type == self::LIST_TYPE_FYR) {
                    // 意见征询回复列表 v18276: 待回复的单据无需取终审通过 且 待支付的
                    $biz_table_info = ['table_alias' => 'r', 'pay_status_field_name' => ''];
                    $builder        = (new FYRService())->getBizOrderConsultationReplyListByProcessState($builder,
                        $is_reply, [Enums::WF_RESERVE_FUND_APPLY], $condition['uid'], $biz_table_info);
                } else {
                    if ($type == self::LIST_TYPE_PAYMENT) {
                        // 支付列表: 当前用户在支付人列表的, 且付款申请已审核通过的
                        $builder->andWhere('r.status = :approval_status:',
                            ['approval_status' => Enums::PAYMENT_APPLY_STATUS_APPROVAL]);
                        if ($flag == 1) {
                            // 待处理
                            $pay_status_item = [
                                Enums::PAYMENT_PAY_STATUS_PENDING,
                            ];
                            $builder->andWhere('r.is_pay_module = :is_pay_module:',
                                ['is_pay_module' => PayEnums::BIZ_DATA_IS_PAY_MODULE_NO]);
                            $builder->inWhere('r.pay_status', $pay_status_item);
                        } else {
                            if ($flag == 2) {
                                // 已处理
                                $pay_status_item = [
                                    Enums::PAYMENT_PAY_STATUS_PAY,
                                    Enums::PAYMENT_PAY_STATUS_NOTPAY,
                                ];
                                $builder->inWhere('r.pay_status', $pay_status_item);
                            }
                        }
                    } else {
                        if ($type == self::LIST_TYPE_QUERY) {
                            // 数据查询列表

                            // 付款日期: 银行流水日期
                            if (!empty($condition['pay_date_start'])) {
                                $builder->andWhere('r.real_pay_at >= :pay_date_start:',
                                    ['pay_date_start' => $condition['pay_date_start'] . ' 0:0:0']);
                            }

                            if (!empty($condition['pay_date_end'])) {
                                $builder->andWhere('r.real_pay_at <= :pay_date_end:',
                                    ['pay_date_end' => $condition['pay_date_end'] . ' 23:59:59']);
                            }

                            // 终审通过日期
                            if (!empty($condition['approved_start_date'])) {
                                $builder->andWhere('r.approve_at >= :approved_start_date:',
                                    ['approved_start_date' => $condition['approved_start_date'] . ' 0:0:0']);
                            }

                            if (!empty($condition['approved_end_date'])) {
                                $builder->andWhere('r.approve_at <= :approved_end_date:',
                                    ['approved_end_date' => $condition['approved_end_date'] . ' 23:59:59']);
                            }

                            // 对接通用数据权限
                            // 业务表参数
                            $table_params = [
                                'table_alias_name'                => 'r',
                                'create_id_field'                 => 'create_id',
                                'create_node_department_id_filed' => 'create_department_id',
                            ];
                            $builder      = CommonDataPermissionService::getInstance()->getCommonDataPermission($user,
                                $builder, Enums\SysConfigEnums::SYS_MODULE_RESERVE, $table_params);
                        }
                    }
                }
            }
        }

        if (!empty($rfano)) {
            $builder->andWhere('r.rfano = :rfano:', ['rfano' => $rfano]);
        }

        // 报销单单号搜索
        if (!empty($reimno)) {
            $builder->andWhere('rei.no = :reino:', ['reino' => $reimno]);
        }

        // 归还单号搜索
        if (!empty($rrno)) {
            $builder->andWhere('rf.rrno = :rrno:', ['rrno' => $rrno]);
        }

        // 归还状态搜索
        if (!empty($return_status)) {
            $builder->andWhere('r.return_status = :return_status:', ['return_status' => $return_status]);
        }

        // 归还日期搜索
        if (!empty($return_date_start) && !empty($return_date_end)) {
            $builder->andWhere("rf.apply_date between '{$return_date_start}' and '{$return_date_end}'");
        }

        //审核，或者是下载，或者数据
        //工号或者姓名
        if (!empty($create_id)) {
            $builder->andWhere('r.create_id = :create_id: or r.create_name=:create_id:', ['create_id' => $create_id]);
        }

        if (!empty($status)) {
            $builder->andWhere('r.status = :status:', ['status' => $status]);
        }

        if (!empty($pay_status)) {
            $builder->andWhere('r.pay_status = :pay_status:', ['pay_status' => $pay_status]);
        }

        if (!empty($apply_date_start)) {
            $builder->andWhere('r.apply_date >= :apply_date_start:', ['apply_date_start' => $apply_date_start]);
        }

        if (!empty($apply_date_end)) {
            $builder->andWhere('r.apply_date <= :apply_date_end:', ['apply_date_end' => $apply_date_end]);
        }

        if (!empty($create_start_time)) {
            $builder->andWhere('r.created_at >= :create_start_time:', ['create_start_time' => $create_start_time]);
        }

        if (!empty($create_end_time)) {
            $builder->andWhere('r.created_at <= :create_end_time:', ['create_end_time' => $create_end_time]);
        }

        if (!empty($store_id)) {
            $builder->andWhere('r.create_store_id = :store_id:', ['store_id' => $store_id]);
        }

        // 申请人所属公司
        if (!empty($create_company_id)) {
            if (is_array($create_company_id)) {
                $builder->andWhere('r.create_company_id IN ({create_company_id:array})', ['create_company_id' => array_values($create_company_id)]);
            } else {
                $builder->andWhere('r.create_company_id = :create_company_id:', ['create_company_id' => $create_company_id]);
            }
        }

        return $builder;
    }

    /**
     * @param $items
     * @param $condition
     * @param $type
     *
     * @return array
     */
    private function handleItems($items, $condition = [], $type = 0)
    {
        if (empty($items) || !is_array($items)) {
            return [];
        }
        // 钮权限仅授权给部分配置工号
        if (ApplyService::LIST_TYPE_QUERY == $type) {
            $audit_auth = $this->checkCanOperateBtn($condition['uid'] ?? 0, 'reserve_fund_access_staff_id');
        } else {
            $audit_auth = $this->checkCanOperateBtn($condition['uid'] ?? 0);
        }
        foreach ($items as $k => &$item) {
            $status                 = (isset($item['status']) && isset(Enums::$loan_status[$item['status']])) ? Enums::$loan_status[$item['status']] : '';
            $item['amount']         = bcdiv($item['amount'] ?? 0, 1000, 2);
            $item['return_amount']  = isset($item['return_amount']) ? bcdiv($item['return_amount'], 1000, 2) : 0;
            $item['return_amounts'] = isset($item['return_amounts']) ? bcdiv($item['return_amounts'], 1000, 2) : 0;

            $item['status_text']     = static::$t->_($status) ?? '';
            $payment_currency        = isset($item['currency']) ? (GlobalEnums::$currency_item[$item['currency']] ?? '') : '';
            $item['currency_text']   = static::$t->_($payment_currency);
            $item['pay_status_text'] = isset($item['pay_status']) ? static::$t->_(Enums::$loan_pay_status[$item['pay_status']]) : '';
            // 是否已付款
            $item['is_pay_text']     = (isset($item['pay_status']) && $item['pay_status'] == Enums::LOAN_PAY_STATUS_PAY) ? static::$t->_('payment_contract_status_1') : static::$t->_('payment_contract_status_0');
            $item['payee_bank_text'] = isset($item['payee_bank']) ? Enums::$bank_type[$item['payee_bank']] : '';
            $item['is_cite']         = isset($item['rfrei_id']) && !empty($item['rfrei_id']);

            // 归还单状态
            $item['return_status_text'] = '';
            if (!empty($item['return_status'])) {
                $item['return_status_text'] = static::$t->_(ReserveFundReturnEnums::$back_status[$item['return_status']]);
            }

            $item['return_currency_text'] = isset($item['return_currency']) ? static::$t->_(GlobalEnums::$currency_item[$item['return_currency']] ?? '') : '';

            // 流水附件
            $item['attachment_str'] = (isset($item['rf_id']) && isset($items['attachment'][$item['rf_id']])) ? implode(';',
                $items['attachment'][$item['rf_id']]) : '';

            // 归还单ID
            $item['return_id'] = $item['return_id'] ?? 0;
            // 归还按钮展示状态【仅有store_access_staff_id（setting env配置）下的工号有展示且已支付归还状态为未归还或未追回】才可展示
            if ($audit_auth && $item['pay_status'] == ReserveFundReturnEnums::PAY_STATUS_PAY && ($item['return_status'] == ReserveFundReturnEnums::BACK_STATUS_NOT || $item['return_status'] == ReserveFundReturnEnums::BACK_STATUS_NOT_RECOVER)) {
                $item['show_return_btn'] = true;
            } else {
                $item['show_return_btn'] = false;
            }
            // 坏账按钮展示状态【仅有store_access_staff_id（setting env配置）下的工号有展示且已支付归还状态为未归还】才可展示
            if ($audit_auth && $item['pay_status'] == ReserveFundReturnEnums::PAY_STATUS_PAY && $item['return_status'] == ReserveFundReturnEnums::BACK_STATUS_NOT) {
                $item['show_bad_return_btn'] = true;
            } else {
                $item['show_bad_return_btn'] = false;
            }

            if($item['state'] == StaffInfoEnums::STAFF_STATE_IN && $item['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES){
                $item['state'] = StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE;
            }
            $item['state_text'] = static::$t->_(StaffInfoEnums::$staff_state[$item['state']]);

        }
        if (isset($items['attachment'])) {
            unset($items['attachment']);
        }

        return $items;
    }


    /**
     * 导出列表
     *
     * @param $condition
     * @param $user
     * @param int $data_type
     * @return array
     * @throws BusinessException
     * @throws GuzzleException
     */
    public function export($condition, $user, $data_type = 0)
    {
        if ($data_type == self::LIST_TYPE_EXPORT) {
            ini_set('memory_limit', '1024M');
        }

        $data = $this->exportList($condition, $user, $data_type);
        return $this->returnExport($data ?? [], $condition['export_file_name'] ?? '');
    }

    /**
     * 返回导出文件链接
     *
     * @param array $data
     * @param string $export_file_name
     * @return array
     * @throws BusinessException
     * @throws GuzzleException
     */
    public function returnExport($data = [], $export_file_name = '')
    {
        $new_data = [];

        foreach ($data as $key => $val) {
            $new_data[$key]   = [];
            $new_data[$key][] = $val['rfano'];
            $new_data[$key][] = $val['create_name'];
            $new_data[$key][] = $val['create_id'];
            $new_data[$key][] = $val['state_text'];
            $new_data[$key][] = $val['create_company_name'];
            $new_data[$key][] = $val['create_department_name'];
            $new_data[$key][] = $val['create_store_name'];
            $new_data[$key][] = $val['apply_date'];
            $new_data[$key][] = $val['currency_text'];
            $new_data[$key][] = $val['payee_username'];
            $new_data[$key][] = $val['payee_account'];
            $new_data[$key][] = $val['payee_bank_text'];
            $new_data[$key][] = $val['apply_reason'];
            $new_data[$key][] = $val['amount'];
            $new_data[$key][] = $val['is_pay_text'];          // 是否已付款
            $new_data[$key][] = $val['pay_bank'];             // 付款银行
            $new_data[$key][] = $val['pay_account'];          // 付款账号
            $new_data[$key][] = $val['payee_username'];       // 签收人
            $new_data[$key][] = $val['pay_at'];               // 付款日期
            $new_data[$key][] = $val['operation_remark'];     // 备注
            $new_data[$key][] = $val['rrno'];                 // 备用金归还单号
            $new_data[$key][] = $val['return_amount'];        // 归还金额
            $new_data[$key][] = $val['return_status_text'];   // 归还单状态
            $new_data[$key][] = $val['approve_at'];           // 归还单最终审批时间
            $new_data[$key][] = $val['return_amounts'];       // 归还金额
            $new_data[$key][] = $val['return_currency_text']; // 归还金额币种
            $new_data[$key][] = $val['bank_flow_date'];       // 归还单银行流水日期
            $new_data[$key][] = $val['attachment_str'];       // 归还流水附件
            $new_data[$key][] = $val['no'];                   // 关联报销单号
        }

        $file_name = !empty($export_file_name) ? $export_file_name : 'reserve_fund_apply_' . date('YmdHis') . '.xlsx';
        $header    = [
            static::$t->_('reserve_fund_apply_no'),             //编号
            static::$t->_('global.applicant.name'),             //申请人
            static::$t->_('global.applicant.id'),               //申请人工号
            static::$t->_('view_work_status'),                  //申请人在职状态
            static::$t->_('global.company.name'),               //公司名称
            static::$t->_('global.department.name'),            //部门名称
            static::$t->_('global.branch'),                     //网点
            static::$t->_('global.apply.date'),                 //申请日期
            static::$t->_('purchase_order_field_currency'),     //币种
            static::$t->_('re_field_bank_name'),                //收款人-户名
            static::$t->_('re_field_bank_account'),             //申请-账号
            static::$t->_('re_field_bank_type'),                //开户行
            static::$t->_('purchase_apply_field_apply_reason'), //说明
            static::$t->_('global.amount'),                     //金额

            static::$t->_('payment_store_renting_is_pay'),                // 是否已付款
            static::$t->_('payment_store_renting_payment_bank'),          // 付款银行
            static::$t->_('payment_store_renting_payment_bank_account'),  // 付款账号
            static::$t->_('re_field_sign_name'),                          // 签收人
            static::$t->_('pay_date'),                                    // 付款日期
            static::$t->_('payment_store_renting_remark'),                // 备注
            static::$t->_('reserve_fund_return_no'),                      // 备用金归还单号
            static::$t->_('reserve_fund_return_amount'),                  // 归还单金额
            static::$t->_('reserve_fund_return_status'),                  // 归还单状态
            static::$t->_('reserve_fund_return_approved_at'),             // 归还单最终审批时间
            static::$t->_('loan_back_amount'),                            // 归还金额
            static::$t->_('reserve_fund_return_currency'),                // 归还金额币种
            static::$t->_('reserve_fund_return_ban_flow_date'),           // 归还单银行流水日期
            static::$t->_('reserve_fund_return_attachment'),              // 归还银行流水附件
            static::$t->_('loan_reimbursement_no'),                       // 关联报销单号

        ];

        return $this->exportExcel($header, $new_data, $file_name);
    }

    /**
     * 导出网点备用金关联的报销单
     *
     * @param $rfano
     * @return array
     * @throws GuzzleException
     */
    public function exportReimburse($rfano)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $data    = '';

        try {
            $list = ReserveFundReimburse::find([
                'conditions' => 'rfano = :rfano:',
                'bind'       => ['rfano' => $rfano],
                'columns'    => 'rei_id',
            ])->toArray();
            if ($list) {
                $ids     = array_column($list, 'rei_id');
                $result  = ListService::getInstance()->export(['ids' => $ids]);
                $code    = $result['code'] ?? $code;
                $message = $result['message'] ?? $message;
                $data    = $result['data'] ?? $data;
            }
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('导出网点备用关联的报销单异常, 原因可能是:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }


    public function getUserMetaFromBi($userId)
    {
        $model = (new UserService())->getUserByIdInRbi($userId);
        if (empty($model)) {
            return [];
        }

        $data                = [];
        $data['create_id']   = $model->staff_info_id ?? "";
        $data['create_name'] = $this->getNameAndNickName($model->name ?? "", $model->nick_name ?? "");

        $department_id                = $model->sys_department_id;
        $node_department_id           = $model->node_department_id;
        $data['create_store_id']      = $model->sys_store_id;
        $data['create_department_id'] = empty($node_department_id) ? $department_id : $node_department_id;
        $t                            = DepartmentModel::findFirst([
            "conditions" => "id = :id:",
            "bind"       => [
                "id" => $data['create_department_id'],
            ],
        ]);
        $s                            = StoreModel::findFirst([
            "conditions" => "id = :id:",
            "bind"       => [
                "id" => $data['create_store_id'],
            ],
        ]);

        $create_store_name = -1 == $data['create_store_id'] ? 'Header Office' : $s->name;

        $data['create_department_name'] = !empty($t) ? $t->name : "";
        $data['create_company_id']      = $t->company_id;
        $data['create_company_name']    = $t->company_name;
        $data['create_store_name']      = $create_store_name;
        $data['payee_username']         = $model->name;
        $data['payee_bank']             = $model->bank_type;
        $data['payee_bank_text']        = Enums::$bank_type[$model->bank_type];
        $data['payee_account']          = $model->bank_no ?? '';
        return $data;
    }


    public function pay($id, $data, $user, $is_from = 1)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db      = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            if ($data['pass_or_not'] == 2 && empty($data['note'])) {
                throw new ValidationException("原因必填！", ErrCode::$CONTRACT_GET_INFO_ERROR);
            }

            $reserve_fund_payment_pay_staff_id = $this->getReserveFundPaymentPayStaffIds();
            if (!in_array($user['id'], $reserve_fund_payment_pay_staff_id) && $is_from == 1) {
                throw new ValidationException(static::$t->_('no_pay_auth_access_msg'), ErrCode::$VALIDATE_ERROR);
            }

            $payment = ReserveFundApply::findFirst([
                'id = :id: and status = :status: ',
                'bind'        => ['id' => $id, 'status' => Enums::CONTRACT_STATUS_APPROVAL],
                'for_updated' => true,
            ]);


            if (empty($payment)) {
                throw new BusinessException("网点备用金-支付失败-未找可支付的付款单", ErrCode::$VALIDATE_ERROR);
            }
            if ($is_from == 1 && $payment->is_pay_module == 1) {
                throw new ValidationException(static::$t->_('payment_has_entered_the_payment_module'),
                    ErrCode::$VALIDATE_ERROR);
            }
            switch ($data['pass_or_not']) {
                case 1:
                    $updateData = [
                        "return_status"    => ReserveFundReturnEnums::BACK_STATUS_NOT,//支付时将归还状态变更为未归还
                        "pay_status"       => Enums::LOAN_PAY_STATUS_PAY,
                        "pay_id"           => $user['id'],
                        "operation_remark" => $data['note'] ?? null,
                        "real_pay_at"      => $data['real_pay_at'] ?? null,
                        "pay_at"           => date("Y-m-d H:i:s"),
                        'pay_account'      => $data['pay_account'],
                        'pay_bank'         => $data['pay_bank'],
                    ];
                    break;
                case 2:
                    $updateData = [
                        "pay_status"       => Enums::LOAN_PAY_STATUS_NOTPAY,
                        "pay_id"           => $user['id'],
                        "pay_at"           => date("Y-m-d H:i:s"),
                        "operation_remark" => $data['note'] ?? null,
                    ];
                    break;
                default:
                    throw new BusinessException("网点备用金-支付失败-支付状态异常" . $data['pass_or_not'], ErrCode::$VALIDATE_ERROR);
                    break;
            }

            $bool = $payment->i_update($updateData);
            if ($bool === false) {
                throw new BusinessException("备用金申请单-支付失败-更新付款申请单失败", ErrCode::$VALIDATE_ERROR);
            }
            $db->commit();
            $this->delUnReadNumsKeyByStaffIds($reserve_fund_payment_pay_staff_id);
        } catch (ValidationException $e) {               //校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
            $db->rollback();
        } catch (BusinessException $e) {               //校验错误，可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (Exception $e) {                       //系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('reserve_fund-payment-update-failed:' . $real_message);
            $db->rollback();
        }
        return [
            'code'    => $code,
            'message' => $message,
            'data'    => [],
        ];
    }

    /**
     * 获取用户已收到的总金额
     * 12362【ALL|OA|备用金】备用金功能优化 二期
     * https://l8bx01gcjr.feishu.cn/docs/doccncFto251qTBdqAup89T7JId#
     * 之前个人已申请备用金总金额【已支付】、  已支付的金额 + 申请人名下的待审核备用金申请单 + 申请人名下已通过且待支付的申请单、归还备用金状态为待审核/已通过、个人未支付金额是以人为纬度统计汇总
     * 修改为以人和申请网点纬度统计汇总
     *
     * @param $user
     * @param $create_store_id
     * @return mixed
     */
    public function getPaidTotalMoney($user, $create_store_id)
    {
        $create_id = (int)$user['id'];
        // 网点维度申请备用金金额 由于之前需求漏洞只统计了已通过已支付数据，这期增加待审核、已通过待支付+已支付逻辑
        $total_sum = ReserveFundApply::sum([
            'conditions' => 'create_store_id = :create_store_id: and status in ({status:array}) and pay_status in ({pay_status:array})',
            'bind'       => [
                'create_store_id' => $create_store_id,
                'status'          => [Enums::WF_STATE_PENDING, Enums::WF_STATE_APPROVED],
                'pay_status'      => [Enums::PAYMENT_PAY_STATUS_PENDING, Enums::PAYMENT_PAY_STATUS_PAY],
            ],
            'column'     => 'amount',
        ]);
        // 网点维度归还备用金状态为待审核/已通过
        $total_sum_return = ReserveFundReturn::sum([
            'conditions' => 'create_store_id = :create_store_id: and status in ({status:array}) and type in ({type:array})',
            'bind'       => [
                'create_store_id' => $create_store_id,
                'status'          => [Enums::WF_STATE_PENDING, Enums::WF_STATE_APPROVED],
                'type'            => [
                    ReserveFundReturnEnums::RETURN_TYPE,
                    ReserveFundReturnEnums::RETURN_SALARY_DEDUCT_TYPE,
                ],
            ],
            'column'     => 'amount',
        ]);
        $total_nopay      = $total_sum - $total_sum_return;


        // 个人已申请备用金总金额【已支付】
        $sum_pay = ReserveFundApply::sum([
            'conditions' => 'create_store_id = :create_store_id: and create_id = :create_id: and status = :status: and pay_status = :pay_status:',
            'bind'       => [
                'create_store_id' => $create_store_id,
                'create_id'       => $create_id,
                'status'          => Enums::WF_STATE_APPROVED,
                'pay_status'      => Enums::PAYMENT_PAY_STATUS_PAY,
            ],
            'column'     => 'amount',
        ]);
        // 已支付的金额 + 申请人名下的待审核备用金申请单 + 申请人名下已通过且待支付的申请单
        $sum = ReserveFundApply::sum([
            'conditions' => 'create_store_id = :create_store_id: and create_id = :create_id: and status in ({status:array}) and pay_status in ({pay_status:array})',
            'bind'       => [
                'create_store_id' => $create_store_id,
                'create_id'       => $create_id,
                'status'          => [Enums::WF_STATE_PENDING, Enums::WF_STATE_APPROVED],
                'pay_status'      => [Enums::PAYMENT_PAY_STATUS_PENDING, Enums::PAYMENT_PAY_STATUS_PAY],
            ],
            'column'     => 'amount',
        ]);
        // 归还备用金状态为待审核/已通过
        $sum_return = ReserveFundReturn::sum([
            'conditions' => 'create_store_id = :create_store_id: and create_id = :create_id: and status in ({status:array}) and type in ({type:array})',
            'bind'       => [
                'create_store_id' => $create_store_id,
                'create_id'       => $create_id,
                'status'          => [Enums::WF_STATE_PENDING, Enums::WF_STATE_APPROVED],
                'type'            => [
                    ReserveFundReturnEnums::RETURN_TYPE,
                    ReserveFundReturnEnums::RETURN_SALARY_DEDUCT_TYPE,
                ],
            ],
            'column'     => 'amount',
        ]);
        // 个人未支付金额
        $sum_nopay = $sum - $sum_return;
        return [
            'total_amount' => $total_nopay,
            'apply_amount' => $sum_pay,
            'nopay_amount' => $sum_nopay,
        ];
    }

    /**
     * 关联报销，可用的备用金单号
     *
     * @param $user
     * @return array
     */
    public function getMyApplyMap($user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $list    = ReserveFundApply::find([
            'conditions' => 'create_id = ?1 and status = ?2 and pay_status = ?3',
            'bind'       => [
                '1' => (int)$user['id'],
                '2' => Enums::CONTRACT_STATUS_APPROVAL,
                '3' => Enums::LOAN_PAY_STATUS_PAY,
            ],
            'columns'    => 'rfano',
        ])->toArray();
        $list    = array_column($list, 'rfano');
        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $list,
        ];
    }

    /**
     * 返回可用币种
     * 之前币种都是表里获取的审批通过已支付的数据币种，产品说币种无需从库里取，直接用每个国家默认币种即可
     * https://l8bx01gcjr.feishu.cn/docs/doccncFto251qTBdqAup89T7JId#
     *
     * @return mixed
     */
    public function getApplyCurrency()
    {
        $default_currency = (new EnumsService())->getSysCurrencyInfo();
        return $default_currency['code'];
    }

    public function download(int $id, int $uid, $is_audit = false)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $lang = $this->getLang();

        $return_data = [];
        try {
            $data = $this->getDetail($id, $uid, true);
            if ($data['code'] != 1) {
                throw new BusinessException('获取备用金申请信息失败', ErrCode::$STORE_RENTING_PAYMENT_GET_INFO_ERROR);
            }

            $data = $data['data'];
            /*
            // 审核未通过: 不可下载
            if ($data['status'] != Enums::CONTRACT_STATUS_APPROVAL) {
                throw new BusinessException('网点备用金，审批未通过，不能下载', ErrCode::$STORE_RENTING_PAYMENT_DOWNLOAD_APPROVAL_STATUS_ERROR);
            }
            */
            foreach ($data['auth_logs'] as $kk => $item) {
                foreach ($item as $k => $v) {
                    if ($this->isEmpty($v)) {
                        $data['auth_logs'][$kk][$k] = "-";
                    } else {
                        if (is_string($data['auth_logs'][$kk][$k]) && mb_strlen($data['auth_logs'][$kk][$k]) > 100) {
                            $data['auth_logs'][$kk][$k] = substr($data['auth_logs'][$kk][$k], 0, 100) . '...';
                        }
                    }
                }
            }

            $fieldArr = $this->getLangKeyArr();
            foreach ($fieldArr as $val) {
                $data['field'][$val] = static::$t->_("re_field_" . $val);
            }
            $data['field']['apply_date']              = static::$t->_('global.apply.date');
            $data['field']['reserve_fund_detail']     = static::$t->_('reserve_fund_detail');
            $data['field']['apply_money']             = static::$t->_('global.apply.money');
            $data['field']['reserve_apply_reason']    = static::$t->_('reserve_apply_reason');
            $data['field']['reserve_fund_apply_name'] = static::$t->_('filed_reserve_fund_apply_name');


            // 文件临时目录
            $sys_tmp_dir = sys_get_temp_dir();
            $file_dir    = $file_dir = $sys_tmp_dir . '/';
            $file_name   = 'ReserveFundApply' . md5($id) . "_{$lang}.pdf";
            $file_path   = $file_dir . $file_name;

            $view = new \Phalcon\Mvc\View();
            $view->setViewsDir(APP_PATH . '/views');
            $view->setVars($data);

            $view->start();
            $view->disableLevel(
                [
                    \Phalcon\Mvc\View::LEVEL_LAYOUT      => false,
                    \Phalcon\Mvc\View::LEVEL_MAIN_LAYOUT => false,
                ]
            );

            // 代码里审批日志用的倒序
            $view->render("reserve", "apply");
            $view->finish();
            $content = $view->getContent();
            $mpdf    = new Mpdf([
                'format' => 'A4',
                'mode'   => 'zh-CN',
            ]);

            $mpdf->useAdobeCJK = true;
            $mpdf->SetDisplayMode('fullpage');
            $mpdf->SetHTMLHeader("");
            $mpdf->SetHTMLFooter("");
            $mpdf->WriteHTML($content);
            $mpdf->Output($file_path, "f");

            // 加水印
            WaterMarkerService::getInstance()->addWaterMarkerToPdfFile($file_path, $file_path);

            // 生成成功, 上传OSS
            $upload_res = OssHelper::uploadFile($file_path);
            if (!empty($upload_res['object_url'])) {
                $return_data['file_name'] = self::$t['reserve_fund_pdf_file_name'] . '.pdf';
                $return_data['file_url']  = $upload_res['object_url'];
            } else {
                throw new BusinessException('网点备用金下载失败，请重试', ErrCode::$STORE_RENTING_PAYMENT_DOWNLOAD_FAIL_ERROR);
            }
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Mpdf\MpdfException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('store_renting_payment-detail-download-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $return_data,
        ];
    }

    /**
     * 文件流输出
     *
     * @param string $file_path
     * @param string $file_name
     * @return mixed
     */
    public function outputPdfFile(string $file_path, string $file_name = '')
    {
        if (empty($file_path)) {
            return [
                'code'    => ErrCode::$VALIDATE_ERROR,
                'message' => 'access error',
                'data'    => [],
            ];
        }

        $file_name = $file_name ? $file_name : 'File_' . date('YmdHis') . '.pdf';

        header('Content-Description: File Transfer');
        header('Content-Transfer-Encoding: binary');
        header('Cache-Control: public, must-revalidate, max-age=0');
        header('Pragma: public');
        header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');
        header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');
        header('Content-Type: application/pdf');

        if (!isset($_SERVER['HTTP_ACCEPT_ENCODING']) || empty($_SERVER['HTTP_ACCEPT_ENCODING'])) {
            // don't use length if server using compression
            header('Content-Length: ' . get_headers($file_path, true)['Content-Length']);
        }

        header('Content-Disposition: attachment; filename="' . $file_name . '"');

        exit(file_get_contents($file_path));
    }

    private function getLang()
    {
        $lang = self::$language;
        if (empty($lang) || !in_array($lang, ["th", "en", "zh-CN"], 1)) {
            $lang = "th";
        }
        return $lang;
    }


    /**
     * 待支付数量
     *
     * @param $user_id
     * @return int
     */
    public function getWaitingPayCount($user_id)
    {
        if (empty($user_id)) {
            return 0;
        }

        $pay_staff_id = $this->getReserveFundPaymentPayStaffIds();
        if (empty($pay_staff_id) || !in_array($user_id, $pay_staff_id)) {
            return 0;
        }

        return ReserveFundApply::count([
            'conditions' => 'is_pay_module = :is_pay_module: AND status = :status: AND pay_status = :pay_status:',
            'bind'       => [
                'is_pay_module' => PayEnums::BIZ_DATA_IS_PAY_MODULE_NO,
                'status'        => Enums::PAYMENT_APPLY_STATUS_APPROVAL,
                'pay_status'    => Enums::PAYMENT_PAY_STATUS_PENDING,
            ],
        ]);
    }

    private function isEmpty($val)
    {
        if (!isset($val)) {
            return true;
        }

        if (is_string($val) && strlen($val) == 0) {
            return true;
        }
        return false;
    }

    public function getLangKeyArr()
    {
        return [
            "title",
            "id",
            "base_info",
            "created_name",
            "created_id",
            "created_company_name",
            "created_department_name",

            "apply_name",
            "apply_id",
            "apply_company_name",
            "apply_department_name",
            "apply_store_name",
            "date",
            "apply_center_code",
            "bank_info",

            "bank_name",
            "currency_text",
            "bank_account",
            "bank_type",
            "detail",
            "detail_id",
            "category_a",
            "category_b",
            "start_at",

            "travel_start",
            "travel_end",
            "fuel_start",
            "fuel_end",
            "fuel_mileage",
            "rate",
            "tax",
            "tax_not",
            "invoices_ids",
            "stat",

            "detail_amount",
            "travel",
            "local",
            "amount",
            "loan_amount",
            "other_amount",
            "real_amount",
            "auth_logs",
            "step",
            "finished_at",

            "deal_id",
            "deal_name",
            "deal_res",
            "deal_mark",
            "approve",
            "info",
            "cost_store_name",
            "apply_mobile",
            "pay_info",
            "is_pay",
            "pay_bank_account",

            "pay_bank_name",
            "sign_name",
            "pay_at",
            "remark",
        ];
    }

    /**
     * 针对马来做职位和网点的特殊配置
     *
     * @param array $user 用户信息组
     * @return bool
     */
    private function isValidJobStore($user)
    {
        $is_valid     = false;
        $country_code = get_country_code();
        $jobTitle     = $user['job_title_id'];

        $model = (new UserService())->getUserByIdInRbi($user['id']);
        if (empty($model)) {
            return $is_valid;
        }
        // 非总部
        if (-1 != $model->sys_store_id) {
            // 马来需要根据职位和网点来判断
            if ('MY' == $country_code) {
                // 需要校的职位
                $reserve_fund_job_title = EnvModel::getEnvByCode('reserve_fund_job_title', '');
                $reserve_fund_job_title = explode(',', $reserve_fund_job_title);
                // 所属网点=具体网点 且 Branch Supervisor
                if (in_array($jobTitle, $reserve_fund_job_title)) {
                    $is_valid = true;
                }
            } else {
                $is_valid = true;
            }
        }

        return $is_valid;
    }

    private function specialAmountTip($user, $data)
    {
        $is_valid     = true;
        $country_code = get_country_code();
        $amount       = $data['exchange_rate'] * $data['amount'] / 1000;
        // 申请人信息
        $userInfo = HrStaffInfoModel::getUserInfo($user['id']);
        if ('MY' == $country_code) {
            // 申请金额大于500MYR且申请人所属一级部门=Malaysia Network Management
            if ($amount > GlobalEnums::RESERVE_FUND_MY_MAX_AMOUNT) {
                if (GlobalEnums::RESERVE_FUND_NETWORK_MANAGEMENT == $userInfo['sys_department_id'] &&
                    in_array($user['job_title_id'], [
                        GlobalEnums::RESERVE_FUND_AREA_MANAGER_JOB_TITLE,
                        GlobalEnums::RESERVE_FUND_DISTRICT_MANAGER_JOB_TITLE,
                    ])
                ) {
                    $is_valid = true;
                } else {
                    $is_valid = false;
                }
            }
        }
        if ('PH' == $country_code) {
            if ($amount > GlobalEnums::RESERVE_FUND_PH_MAX_AMOUNT_1 && GlobalEnums::RESERVE_FUND_PHILIPPINES_HUB != $userInfo['sys_department_id']) {
                $is_valid = false;
            }
            if ($amount > GlobalEnums::RESERVE_FUND_PH_MAX_AMOUNT_2 && GlobalEnums::RESERVE_FUND_PHILIPPINES_HUB == $userInfo['sys_department_id']) {
                $is_valid = false;
            }
        }

        return $is_valid;
    }

    /**
     * 获取某个备用金申请单名下的归还单坏账单列表
     *
     * @param integer $id 申请单ID
     * @return array
     */
    public function getReturnList($id)
    {
        try {
            //获取申请单关联的归还单信息
            $apply_return_rel = ReserveFundApplyReturnRelModel::find([
                'conditions' => 'apply_id = :apply_id: and is_deleted = :is_deleted:',
                'bind'       => ['apply_id' => $id, 'is_deleted' => GlobalEnums::IS_NO_DELETED],
                'columns'    => 'return_id',
            ])->toArray();
            $return_ids       = array_column($apply_return_rel, 'return_id');
            if (!empty($return_ids)) {
                $data = ReserveFundReturn::find([
                    'columns'    => [
                        'id',
                        'rrno',
                        'create_id',
                        'create_name',
                        'create_company_name',
                        'create_department_name',
                        'create_store_name',
                        'amount',
                        'currency',
                        'status',
                        'type',
                    ],
                    'conditions' => ' id in ({ids:array}) ',
                    'bind'       => ['ids' => $return_ids],
                ])->toArray();
                if ($data) {
                    foreach ($data as &$item) {
                        $item['amount']        = bcdiv($item['amount'], 1000, 2);
                        $item['status_text']   = static::$t->_(Enums::$loan_status[$item['status']]);
                        $item['currency_text'] = static::$t->_(GlobalEnums::$currency_item[$item['currency'] ?? '']);
                    }
                }
            }
        } catch (Exception $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('ReserveFund-Apply-get-return-list-failed:' . $real_message);
        }
        return [
            'code'    => $code ?? ErrCode::$SUCCESS,
            'message' => $message ?? 'success',
            'data'    => $data ?? [],
        ];
    }

    /**
     * 获取某员工备用金未归还金额
     * 16052需求，用于向BY端【我的申请-离职申请、我的审批-离职申请】提供备用金未归还金额
     * 备用金未归还金额 = 员工申请的备用金总额(1. 审批通过 且 支付状态=已支付状态；2. 申请状态=待审核；3. 审批通过且支付状态=待支付)申请总金额） - 已归还金额总额（备用金归还单状态=审批同意 && 归还类型=归还）
     *
     * @param int $staff_id 员工id
     * @return string|null
     */
    public function getStaffReserveFundOutstandingAmountReal(int $staff_id)
    {
        // 已支付的金额 + 申请人名下的待审核备用金申请单 + 申请人名下已通过且待支付的申请单的申请备用金总额
        $sum = ReserveFundApply::sum([
            'conditions' => 'create_id = :create_id: and status in ({status:array}) and pay_status in ({pay_status:array})',
            'bind'       => [
                'create_id'  => $staff_id,
                'status'     => [Enums::WF_STATE_PENDING, Enums::WF_STATE_APPROVED],
                'pay_status' => [Enums::PAYMENT_PAY_STATUS_PENDING, Enums::PAYMENT_PAY_STATUS_PAY],
            ],
            'column'     => 'amount',
        ]);

        // 归还备用金归还状态为已通过&&归还类型=归还的归还金额总计
        $sum_return = ReserveFundReturn::sum([
            'conditions' => 'create_id = :create_id: and status = :status: and type in ({type:array})',
            'bind'       => [
                'create_id' => $staff_id,
                'status'    => Enums::WF_STATE_APPROVED,
                'type'      => [
                    ReserveFundReturnEnums::RETURN_TYPE,
                    ReserveFundReturnEnums::RETURN_SALARY_DEDUCT_TYPE,
                ],
            ],
            'column'     => 'amount',
        ]);

        //员工备用金未归还金额
        return bcdiv(bcsub($sum, $sum_return), 1000, 2);
    }

    /**
     * 批量获取各员工的备用金未归还金额
     * 16052需求，用于向HCM端【Flash员工离职管理】提供备用金未归还金额
     * 备用金未归还金额 = 员工申请的备用金总额(1. 审批通过 且 支付状态=已支付状态；2. 申请状态=待审核；3. 审批通过且支付状态=待支付)申请总金额） - 已归还金额总额（备用金归还单状态=审批同意 && 归还类型=归还）
     *
     * @param array $staff_ids 员工id组
     * @return array
     */
    public function batchGetStaffReserveFundOutstandingAmountReal(array $staff_ids)
    {
        $outstanding_amount = [];

        $staff_ids = array_values(array_unique(array_filter($staff_ids)));
        // 已支付的金额 + 申请人名下的待审核备用金申请单 + 申请人名下已通过且待支付的申请单的申请备用金总额
        $sum_list = ReserveFundApply::sum([
            'conditions' => 'create_id in ({create_ids:array}) and status in ({status:array}) and pay_status in ({pay_status:array})',
            'bind'       => [
                'create_ids' => $staff_ids,
                'status'     => [Enums::WF_STATE_PENDING, Enums::WF_STATE_APPROVED],
                'pay_status' => [Enums::PAYMENT_PAY_STATUS_PENDING, Enums::PAYMENT_PAY_STATUS_PAY],
            ],
            'column'     => 'amount',
            'group'      => 'create_id',
        ])->toArray();
        // 各员工名下已经申请的备用金总金额
        $sum_user_list = array_column($sum_list, 'sumatory', 'create_id');

        // 归还备用金归还状态为已通过&&归还类型=归还的归还金额总计
        $sum_return_list = ReserveFundReturn::sum([
            'conditions' => 'create_id in ({create_ids:array}) and status = :status: and type in ({type:array})',
            'bind'       => [
                'create_ids' => $staff_ids,
                'status'     => Enums::WF_STATE_APPROVED,
                'type'       => [
                    ReserveFundReturnEnums::RETURN_TYPE,
                    ReserveFundReturnEnums::RETURN_SALARY_DEDUCT_TYPE,
                ],
            ],
            'column'     => 'amount',
            'group'      => 'create_id',
        ])->toArray();

        // 各员工名下已经归还的备用金总金额
        $sum_user_return_list = array_column($sum_return_list, 'sumatory', 'create_id');

        foreach ($staff_ids as $staff_id) {
            $user_amount                   = $sum_user_list[$staff_id] ?? 0;       //借的备用金金额
            $user_return_amount            = $sum_user_return_list[$staff_id] ?? 0;//还的备用金金额
            $outstanding_amount[$staff_id] = bcdiv(bcsub($user_amount, $user_return_amount), 1000, 2);
        }
        return $outstanding_amount;
    }

    /**
     * 数据查询导出
     * 备用金表和报销抵扣关联数据
     * 备用金表和归还关联数据
     * 剔除重复的无报销抵扣无归还的数据行
     * 报表将上面数据合并
     *
     * @param $condition
     * @param $user
     * @param $data_type
     * @return array
     */
    public function exportList($condition, $user = [], $data_type = 0)
    {
        $reserve_data  = [];
        $only_rei_data = [];
        $data          = [];
        // 关联报销单
        $reimno = $condition['reimno'] ?? '';
        // 备用金归还单号
        $rrno = $condition['rrno'] ?? '';

        // 归还日期
        $return_date_start = $condition['return_date_start'] ?? '';
        $return_date_end   = $condition['return_date_end'] ?? '';
        try {
            $fields = 'r.id,r.rfano,r.create_name,r.create_id,staff.state,staff.wait_leave_state,r.create_company_id,r.create_company_name,r.create_department_name,r.create_store_name,r.apply_date,r.payee_username,r.payee_bank,
                       r.payee_account,r.apply_reason,r.amount,r.currency,r.status,r.pay_status,r.pay_bank, r.pay_account,r.payee_username,r.pay_at,r.operation_remark,r.return_status';

            $return_fields = $fields . ',rf.type,rf.rrno,rf.amount as return_amount,rf.currency as return_currency,rf.approve_at,rf.bank_flow_date,apply_return_rel.return_id,rf.id as rf_id';

            $rei_fields = $fields . ',rei.no';
            if (empty($rrno) && empty($return_date_start) && empty($return_date_end)) {
                $rei_builder = $this->modelsManager->createBuilder();
                $rei_builder->columns($rei_fields);
                $rei_builder->from(['r' => ReserveFundApply::class]);
                $rei_builder->leftjoin(MiniHrStaffInfoModel::class, 'r.create_id = staff.staff_info_id', 'staff');
                $rei_builder->leftjoin(ReserveFundReimburse::class, 'rfrei.rfano=r.rfano', 'rfrei');
                $rei_builder->leftjoin(Reimbursement::class, 'rei.id=rfrei.rei_id', 'rei');
                $rei_builder = $this->getExportCondition($rei_builder, $condition, $user, $data_type);
                // 报销单单号搜索
                if (!empty($reimno)) {
                    $rei_builder->andWhere('rei.no = :reino:', ['reino' => $reimno]);
                }

                $rei_builder->orderBy('r.id DESC');
                $rei_list = $rei_builder->getQuery()->execute()->toArray();

                $rfano_amount_arr = [];
                foreach ($rei_list as &$item) {
                    $item['rrno']            = '';
                    $item['return_amount']   = '';
                    $item['return_currency'] = '';
                    $item['approve_at']      = '';
                    $item['bank_flow_date']  = '';

                    if (empty($item['no'])) {
                        $reserve_data[] = $item;
                    } else {
                        if (!empty($reimno)) {
                            $rei_data[] = $item;
                        }
                        $only_rei_data[] = $item;
                    }
                }
            }

            $return_builder = $this->modelsManager->createBuilder();
            $return_builder->columns($return_fields);
            $return_builder->from(['r' => ReserveFundApply::class]);
            $return_builder->leftjoin(ReserveFundApplyReturnRelModel::class,
                'apply_return_rel.apply_id = r.id and apply_return_rel.is_deleted = ' . GlobalEnums::IS_NO_DELETED,
                'apply_return_rel');
            $return_builder->leftjoin(ReserveFundReturn::class,
                'rf.id = apply_return_rel.return_id and apply_return_rel.is_deleted = ' . GlobalEnums::IS_NO_DELETED,
                'rf');
            $return_builder->leftjoin(MiniHrStaffInfoModel::class, 'staff.staff_info_id = r.create_id', 'staff');

            $return_builder = $this->getExportCondition($return_builder, $condition, $user, $data_type);
            if (!empty($rei_data) && !empty($reimno)) {
                $rfano_arr = array_values(array_unique(array_column($rei_data, 'rfano')));

                if (is_array($rfano_arr)) {
                    $return_builder->inWhere('r.rfano', $rfano_arr);
                }
            }
            // 归还单号搜索
            if (!empty($rrno)) {
                $return_builder->andWhere('rf.rrno = :rrno:', ['rrno' => $rrno]);
            }

            // 归还日期搜索
            if (!empty($return_date_start) && !empty($return_date_end)) {
                $return_builder->betweenWhere('rf.apply_date', $return_date_start, $return_date_end);
            }

            $return_builder->orderBy('r.id DESC');
            $return_list = $return_builder->getQuery()->execute()->toArray();

            foreach ($return_list as &$item) {
                if ($item['type'] != ReserveFundReturnEnums::RETURN_BAD_TYPE) {
                    $rfano_amount_arr[$item['rfano']] = bcadd($rfano_amount_arr[$item['rfano']] ?? 0,
                        $item['return_amount']);
                }

                $item['no'] = '';
                if (empty($item['return_id'])) {
                    $reserve_data[] = $item;
                } else {
                    $only_return_data[] = $item;
                }
            }

            $data = array_merge($only_return_data ?? [], $only_rei_data);

            $rfano_total_arr = array_column($data, 'rfano');

            if (!empty($reserve_data)) {
                $reserve_data = array_column($reserve_data, null, 'rfano');

                foreach ($reserve_data as &$item) {
                    if (!in_array($item['rfano'], $rfano_total_arr)) {
                        $data[] = $item;
                    }
                }
            }

            //计算 已支付抵扣金额合计
            foreach ($data as &$item) {
                $item['return_amounts'] = $rfano_amount_arr[$item['rfano']] ?? 0;
            }

            // 归还银行流水附件
            $oss_bucket_ids = array_values(array_filter(array_column($data, 'rf_id')));
            $attachments    = [];
            if (!empty($oss_bucket_ids)) {
                $attachments = AttachModel::find([
                    'conditions' => "oss_bucket_key in({ids:array}) and oss_bucket_type = :oss_bucket_type: and deleted = :deleted:",
                    'bind'       => [
                        'ids'             => $oss_bucket_ids,
                        'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_RESERVE_FUND_RETURN_BANK_FLOW,
                        'deleted'         => GlobalEnums::IS_NO_DELETED,
                    ],
                    'columns'    => 'oss_bucket_key,bucket_name,object_key',
                ])->toArray();
            }

            $attachment_arr = [];
            foreach ($attachments as $file) {
                $attachment_arr[$file['oss_bucket_key']][] = gen_file_url($file);
            }

            $data['attachment'] = $attachment_arr;
        } catch (Exception $e) {
            $real_message = $e->getMessage();
            $this->logger->warning('reserve-export-failed:' . $real_message);
        }

        return $this->handleItems($data);
    }

    /**
     * @param $builder
     * @param $condition
     * @param $user
     * @param int $data_type
     * @return mixed
     * @throws BusinessException
     */
    private function getExportCondition($builder, $condition, $user, $data_type = 0)
    {
        $rfano            = $condition['rfano'] ?? '';
        $status           = $condition['status'] ?? 0;
        $pay_status       = $condition['pay_status'] ?? 0;
        $apply_date_start = $condition['created_at_start'] ?? '';
        $apply_date_end   = $condition['created_at_end'] ?? '';
        $create_id        = $condition['create_name'] ?? '';
        $store_id         = $condition['create_store_id'] ?? '';


        // 归还状态
        $return_status = $condition['return_status'] ?? 0;

        // 申请人所属公司
        $create_company_id = $condition['create_company_id'] ?? [];

        // 创建单据的开始时间
        $create_start_time = $condition['create_start_time'] ?? '';

        // 创建单据的结束时间
        $create_end_time = $condition['create_end_time'] ?? '';

        // 付款日期: 银行流水日期
        if (!empty($condition['pay_date_start'])) {
            $builder->andWhere('r.real_pay_at >= :pay_date_start:',
                ['pay_date_start' => $condition['pay_date_start'] . ' 00:00:00']);
        }

        if (!empty($condition['pay_date_end'])) {
            $builder->andWhere('r.real_pay_at <= :pay_date_end:',
                ['pay_date_end' => $condition['pay_date_end'] . ' 23:59:59']);
        }
        //在职状态查询
        if(!empty($condition['staff_status'])){
            if (in_array(StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE, $condition['staff_status'])) {
                $builder->andWhere("staff.state IN ({states:array}) OR (staff.state=:state: AND staff.wait_leave_state=:wait_leave_state:)",
                    [
                        'states'           => $condition['staff_status'],
                        'state'            => StaffInfoEnums::STAFF_STATE_IN,
                        'wait_leave_state' => StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES,
                    ]);
            } elseif (!in_array(StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE, $condition['staff_status']) && in_array(StaffInfoEnums::STAFF_STATE_IN, $condition['staff_status'])) {
                $builder->andWhere("staff.state IN ({states:array}) AND staff.wait_leave_state != :wait_leave_state:",
                    ['states' => $condition['staff_status'], 'wait_leave_state' => StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES]);
            } else {
                $builder->andWhere("staff.state IN ({states:array})", ['states' => $condition['staff_status']]);
            }
        }


        // 终审通过日期
        if (!empty($condition['approved_start_date'])) {
            $builder->andWhere('r.approve_at >= :approved_start_date:',
                ['approved_start_date' => $condition['approved_start_date'] . ' 00:00:00']);
        }

        if (!empty($condition['approved_end_date'])) {
            $builder->andWhere('r.approve_at <= :approved_end_date:',
                ['approved_end_date' => $condition['approved_end_date'] . ' 23:59:59']);
        }


        if (!empty($rfano)) {
            $builder->andWhere('r.rfano = :rfano:', ['rfano' => $rfano]);
        }


        // 归还状态搜索
        if (!empty($return_status)) {
            $builder->andWhere('r.return_status = :return_status:', ['return_status' => $return_status]);
        }


        //审核，或者是下载，或者数据
        //工号或者姓名
        if (!empty($create_id)) {
            $builder->andWhere('r.create_id = :create_id: or r.create_name=:create_id:', ['create_id' => $create_id]);
        }

        if (!empty($status)) {
            $builder->andWhere('r.status = :status:', ['status' => $status]);
        }

        if (!empty($pay_status)) {
            $builder->andWhere('r.pay_status = :pay_status:', ['pay_status' => $pay_status]);
        }

        if (!empty($apply_date_start)) {
            $builder->andWhere('r.apply_date >= :apply_date_start:', ['apply_date_start' => $apply_date_start]);
        }

        if (!empty($apply_date_end)) {
            $builder->andWhere('r.apply_date <= :apply_date_end:', ['apply_date_end' => $apply_date_end]);
        }

        if (!empty($create_start_time)) {
            $builder->andWhere('r.created_at >= :create_start_time:', ['create_start_time' => $create_start_time]);
        }

        if (!empty($create_end_time)) {
            $builder->andWhere('r.created_at <= :create_end_time:', ['create_end_time' => $create_end_time]);
        }

        if (!empty($store_id)) {
            $builder->andWhere('r.create_store_id = :store_id:', ['store_id' => $store_id]);
        }

        // 申请人所属公司
        if (!empty($create_company_id)) {
            if (is_array($create_company_id)) {
                $builder->andWhere('r.create_company_id IN ({create_company_id:array})', ['create_company_id' => array_values($create_company_id)]);
            } else {
                $builder->andWhere('r.create_company_id = :create_company_id:', ['create_company_id' => $create_company_id]);
            }
        }

        // 对接通用数据权限
        if ($data_type == self::LIST_TYPE_EXPORT) {
            // 业务表参数
            $table_params = [
                'table_alias_name'                => 'r',
                'create_id_field'                 => 'create_id',
                'create_node_department_id_filed' => 'create_department_id',
            ];
            $builder      = CommonDataPermissionService::getInstance()->getCommonDataPermission($user, $builder,
                Enums\SysConfigEnums::SYS_MODULE_RESERVE, $table_params);
        }

        return $builder;
    }
}
