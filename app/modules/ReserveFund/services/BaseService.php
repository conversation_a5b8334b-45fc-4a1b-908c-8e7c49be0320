<?php

namespace App\Modules\ReserveFund\Services;

use App\Library\Enums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\RedisClient;
use App\Models\backyard\HrStaffInfoModel;
use App\Modules\Setting\Services\BaseService as SettingService;
use App\Modules\Common\Models\EnvModel;
use App\Modules\ReserveFund\Models\PurchaseUpdateTotalLog;
use App\Modules\User\Models\DepartmentModel;
use App\Modules\User\Services\UserService;
use App\Util\RedisExpire;
use App\Util\RedisKey;

class BaseService extends \App\Library\BaseService
{
    const LIST_TYPE_APPLY = 1;
    const LIST_TYPE_AUDIT = 2;
    const LIST_TYPE_FYR = 3;
    const LIST_TYPE_PAYMENT = 4;
    const LIST_TYPE_QUERY = 5;
    const LIST_TYPE_EXPORT = 6;


    const NOTREQUIRED_LONG_TEXT_LEN = 'StrLenGeLe:0,100';
    const REQUIRED_LONG_TEXT_LEN = 'Required|StrLenGeLe:1,100';


    /**
     * 去bi里面取相关数据
     *
     * @param $userId
     * @return array
     */
    public function getUserMetaFromBi($userId)
    {
        $model = (new UserService)->getUserByIdInRbi($userId);
        if (empty($model)) {
            return [];
        }
        $department_id      = $model->sys_department_id;
        $node_department_id = $model->node_department_id;

        $t  = DepartmentModel::findFirst([
            "conditions" => "id = :id:",
            "bind"       => [
                "id" => empty($node_department_id) ? $department_id : $node_department_id,
            ],
        ]);
        $jt = $model->getJobTitle();

        return [
            'create_id'             => $model->staff_info_id ?? "",
            'create_name'           => $this->getNameAndNickName($model->name ?? "", $model->nick_name ?? ""),
            'create_department'     => !empty($t) ? $t->name : "",
            'create_department_id'  => empty($node_department_id) ? $department_id : $node_department_id,
            'department_id'         => $department_id,
            'node_department_id'    => $node_department_id, //前端用来判断是否去掉广告
            'create_job_title_name' => $jt ? $jt->job_name : '',
        ];
    }

    /**
     * 获取编号
     *
     * @return string
     */
    public static function getNo($date, $key = null)
    {
        $key = self::getCounterKey($date, $key);
        if (self::getCounter($key)) {           //有计数器
            $lno = self::incrCounter($key);
        } else {
            $lno = self::setCounter($key);
        }
        return $date . sprintf('%04s', $lno);
    }

    /**
     * 判断计数器是否存在
     *
     * @param string $key
     * @return bool|int
     */
    private static function getCounter($key)
    {
        return RedisClient::getInstance()->getClient()->exists($key);
    }

    /**
     * 计数器不存在的情况下
     *
     * @param string $key
     * @return bool|int
     */
    private static function setCounter($key)
    {
        $lno = 1;
        RedisClient::getInstance()->getClient()->setex($key, RedisExpire::ONE_DAY, $lno);
        return $lno;
    }

    /**
     * 计数器存在的情况下
     *
     * @param string $key
     * @return int
     */
    private static function incrCounter($key)
    {
        return RedisClient::getInstance()->getClient()->incrBy($key, 1);
    }


    private static function getCounterKey($date, $key = null)
    {
        $key = $key ?? RedisKey::PURCHASE_ORDER_COUNTER;
        return $key . "_" . $date;
    }


    /**
     * 获得付款申请单，待支付的人员
     *
     * @return array|false|string[]
     */

    public function getReserveFundPaymentPayStaffIds()
    {
        $pay_staff_id = EnvModel::getEnvByCode("reserve_fund_payment_pay_staff_id");
        $pay_staff_id = explode(',', $pay_staff_id);
        return $pay_staff_id ?? [];
    }

    public function isCanDownload($item, $uid)
    {
        if (empty($item)) {
            return '0';
        }

        if (empty($uid)) {
            return '1';
        }

        if ($item['status'] == Enums::CONTRACT_STATUS_CANCEL || $item['pay_status'] == Enums::LOAN_PAY_STATUS_NOTPAY) {
            return '0';
        }

        return '1';
    }

    /**
     * 检测当前登陆账号是否拥有替别人操作归还、坏账的权限
     *
     * @param integer $uid 当前登陆人
     * @param string $code setting_env中的code
     * @return bool
     */
    public function checkCanOperateBtn($uid, $code = '')
    {
        $store_access_staff_ids = SettingService::getSettingAuthStaffId($code);
        return in_array($uid, $store_access_staff_ids) ? true : false;
    }

    //员工状态和翻译 待离职 返回999
    public static function formatStaffState($userId)
    {
        if(empty($userId)){
            return [];
        }
        $user = HrStaffInfoModel::findFirst([
            'columns' => 'staff_info_id, state, wait_leave_state',
            'conditions' => 'staff_info_id = :staff_info_id:',
            'bind' => ['staff_info_id' => $userId]
        ]);
        if(empty($user)){
            return [];
        }
        $user = $user->toArray();

        //新增 员工状态
        $state = $user['state'];
        if($state == StaffInfoEnums::STAFF_STATE_IN && $user['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES){
            $state = StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE;
        }
        $stateText = static::$t->_(StaffInfoEnums::$staff_state[$state]);
        return [$state, $stateText];

    }

}
