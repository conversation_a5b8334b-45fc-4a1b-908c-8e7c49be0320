<?php

namespace App\Modules\ReserveFund\Services;

use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Models\oa\ReserveFundApplyReturnRelModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\ReserveFund\Models\ReserveFundApply;
use App\Modules\ReserveFund\Models\ReserveFundReturn;
use App\Modules\User\Models\AttachModel;
use App\Modules\User\Models\DepartmentModel;
use App\Modules\User\Models\StoreModel;
use App\Modules\User\Services\UserService;
use App\Modules\Workflow\Services\FYRService;
use App\Modules\Workflow\Services\WorkflowServiceV2;

class ReturnService extends BaseService
{
    const NOTREQUIRED_LONG_TEXT_LEN = 'StrLenGeLe:0,100';
    const REQUIRED_LONG_TEXT_LEN = 'Required|StrLenGeLe:1,100';

    public static $validate_detail = [
        'id' => 'Required|IntGe:1',
    ];

    public static $validate_audit = [
        'id'          => 'Required|IntGe:1',
        'pass_or_not' => 'Required|IntIn:1,2',
        'note'        => 'IfIntEq:pass_or_not,2|Required|StrLenGeLe:1,1000',
    ];

    public static $validate_cancel = [
        'id'   => 'Required|IntGe:1',
        'note' => 'Required|StrLenGeLe:1,1000',
    ];

    /**
     * 添加归还｜坏账
     *
     * @var array
     */
    public static $validate_param = [
        'rrno'                           => 'Required|StrLenGeLe:10,20',
        'apply_date'                     => 'Required|Date',
        'create_id'                      => 'Required',
        'create_name'                    => 'Required',
        'create_department_id'           => 'Required',
        'create_department_name'         => self::REQUIRED_LONG_TEXT_LEN,
        'create_company_id'              => 'Required',
        'create_company_name'            => self::REQUIRED_LONG_TEXT_LEN,
        'create_store_id'                => 'Required',
        'create_store_name'              => self::REQUIRED_LONG_TEXT_LEN,
        'create_total_amount'            => 'FloatGe:0',
        'amount'                         => 'Required|FloatGt:0',
        'return_reason'                  => 'Required|StrLenGeLe:1,5000',
        'type'                           => 'Required|IntIn:' . Enums\ReserveFundReturnEnums::RETURN_TYPE . "," . Enums\ReserveFundReturnEnums::RETURN_BAD_TYPE,
        //归还类型1归还，2坏账
        //银行流水-附件
        'attachment_flow'                => 'IfIntEq:type,' . Enums\ReserveFundReturnEnums::RETURN_TYPE . '|Required|Arr|ArrLenGeLe:0,10',
        'attachment_flow[*].bucket_name' => 'Required',
        'attachment_flow[*].object_key'  => 'Required',
        'attachment_flow[*].file_name'   => 'Required',
        //附件
        'attachment[*].bucket_name'      => 'Required',
        'attachment[*].object_key'       => 'Required',
        'attachment[*].file_name'        => 'Required',
    ];


    private static $instance;


    private function __construct()
    {
    }

    /**
     * @return ReturnService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 获取申请人网点纬度归还列表
     *
     * @param array $user ['id'=>'归还申请人用户id']
     * @param string $create_store_id 网点ID
     * @return array
     */
    public function defaultData($user, $create_store_id)
    {
        $code         = ErrCode::$SUCCESS;
        $message      = 'success';
        $real_message = '';

        $data = [];
        try {
            if (empty($user)) {
                throw new ValidationException("The employee information is null [{$user['id']}]",
                    ErrCode::$VALIDATE_ERROR);
            }

            $arr = $this->getUserMetaFromBi($user['id']);
            if (empty($arr)) {
                throw new ValidationException(static::$t->_("re_staff_info_id_not_exist"), ErrCode::$VALIDATE_ERROR);
            }

            /**
             * 12362【ALL|OA|备用金】备用金功能优化 二期
             * https://l8bx01gcjr.feishu.cn/docs/doccncFto251qTBdqAup89T7JId#MaUywJ
             * 由20220106 邮件需求: 当归还申请人在HRIS内，所属网点=Head office时，取该工号在备用金申请单中创建日期最近的备用金申请单的网点名称
             * 修改为以申请人每个网点纬度的数据展示
             */
            $fund_apply_list = $this->getStaffFundApplyForStore($user['id'], $create_store_id);

            if ($fund_apply_list) {
                $now_date = date("Ymd");
                //之前币种都是表里获取的审批通过已支付的数据币种，产品说币种无需从库里取，直接用每个国家默认币种即可
                $default_currency = (new EnumsService())->getSysCurrencyInfo();
                foreach ($fund_apply_list as $item) {
                    $refund_data              = [];
                    $arr['create_store_id']   = $item['create_store_id'] ?? '';
                    $arr['create_store_name'] = $item['create_store_name'] ?? '';

                    // 2022-07-29 邮件需求: 归还单的公司和部门从申请单中带出来
                    $arr['create_company_id']      = $item['create_company_id'] ?? '';
                    $arr['create_company_name']    = $item['create_company_name'] ?? '';
                    $arr['create_department_id']   = $item['create_department_id'] ?? '';
                    $arr['create_department_name'] = $item['create_department_name'] ?? '';

                    $refund_data['rrno']       = 'WDBGH' . static::getNo($now_date);
                    $refund_data['apply_date'] = date("Y-m-d", strtotime($now_date));
                    $refund_data               = array_merge($refund_data, $arr);
                    // 计算归还总金额
                    $totalInfo                          = ApplyService::getInstance()->getPaidTotalMoney($user,
                        $arr['create_store_id']);
                    $refund_data['create_total_amount'] = bcdiv($totalInfo['total_amount'], 1000, 2);
                    // 个人申请总金额
                    $refund_data['create_apply_amount'] = bcdiv($totalInfo['apply_amount'], 1000, 2);
                    // 个人未归还总金额
                    $refund_data['create_nopay_amount'] = bcdiv($totalInfo['nopay_amount'], 1000, 2);
                    //币种
                    $refund_data['currency'] = $default_currency['code'];
                    //币种文本
                    $refund_data['currency_text'] = $default_currency['symbol'];
                    $data[]                       = $refund_data;
                }
            }
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $data         = [];
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('reserve_fund-get-default-data-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 获取归还单详情
     *
     * @param int $id 归还单id
     * @param int $uid 用户id
     * @param bool $if_download
     * @param bool $is_audit
     * @return array
     */
    public function getDetail(int $id, int $uid = 0, $if_download = false, $is_audit = false)
    {
        $code         = ErrCode::$SUCCESS;
        $real_message = '';

        try {
            $return = ReserveFundReturn::findFirst([
                'id = :id:',
                'bind' => ['id' => $id],
            ]);

            if (empty($return)) {
                throw new ValidationException(static::$t->_('data_not_exist_by_id', ['biz_id' => $id]),
                    ErrCode::$VALIDATE_ERROR);
            }

            $data = $return->toArray();

            $data['rfano']    = ''; // 申请单号, 薪资抵扣类型时, 会用到
            $data['can_edit'] = false;
            $data['ask_id']   = '';

            $attachment = []; // 附件
            $flow       = []; // 银行流水附件
            $auth_logs  = []; // 审批流日志

            // 非薪资抵扣: 原归还/坏账流程
            if ($return->type != Enums\ReserveFundReturnEnums::RETURN_SALARY_DEDUCT_TYPE) {
                //归还审批
                $req = (new PayFlowService(Enums::WF_RESERVE_FUND_RETURN))->getRequest($id);
                if (empty($req->id)) {
                    throw new BusinessException('获取工作流批次失败, id=' . $id, ErrCode::$BUSINESS_ERROR);
                }

                //审批过程中是否可编辑字段进行审批
                if ($is_audit && $return->type == Enums\ReserveFundReturnEnums::RETURN_TYPE) {
                    //只有归还单类型的审批才需要判断特定节点审批
                    $data['can_edit'] = (new PayFlowService(Enums::WF_RESERVE_FUND_RETURN))->getCanEditFieldByReq($req,
                        $uid) === false ? false : true;
                }
                $attachment = $return->getFile(['columns' => 'bucket_name,object_key,file_name']);
                $attachment = $attachment ? $attachment->toArray() : [];

                //银行流水
                $flow      = AttachModel::find([
                    'conditions' => 'oss_bucket_key = :oss_bucket_key: AND oss_bucket_type = :oss_bucket_type:',
                    'bind'       => [
                        'oss_bucket_key'  => $id,
                        'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_RESERVE_FUND_RETURN_BANK_FLOW,
                    ],
                    'columns'    => ['bucket_name', 'object_key', 'file_name'],
                ])->toArray();
                $auth_logs = $this->getAuditLogs($req, $if_download);

                $ask            = (new FYRService())->getRequestToByReplyAsk($req, $uid);
                $data['ask_id'] = $ask ? $ask->id : '';
            } else {
                //薪资抵扣的归还类型需要获取关联的申请单单号
                $return_rel = ReserveFundApplyReturnRelModel::findFirst([
                    'columns'    => 'apply_id',
                    'conditions' => 'return_id = :return_id: and is_deleted = :is_deleted:',
                    'bind'       => ['return_id' => $id, 'is_deleted' => GlobalEnums::IS_NO_DELETED],
                ]);
                if (!empty($return_rel)) {
                    $apply_info    = ReserveFundApply::findFirst([
                        'columns'    => 'rfano',
                        'conditions' => 'id = :apply_id:',
                        'bind'       => ['apply_id' => $return_rel->apply_id],
                    ]);
                    $data['rfano'] = !empty($apply_info) ? $apply_info->rfano : '';
                }
            }

            $data['attachment']      = $attachment;
            $data['attachment_flow'] = $flow;
            $data['auth_logs']       = $auth_logs;
            $data                    = $this->handleDetailData($data);
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning('ReserveFund-return-get-detail-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message ?? 'success',
            'data'    => $data ?? [],
        ];
    }

    /**
     * @param $data
     * @return array
     */
    private function handleDetailData($data)
    {
        $status                      = Enums::$loan_status[$data['status']] ?? '';
        $data['status_text']         = !empty($status) ? static::$t->_($status) : '';
        $payment_currency            = GlobalEnums::$currency_item[$data['currency']] ?? '';
        $data['currency_text']       = !empty($payment_currency) ? static::$t->_($payment_currency) : '';
        $data['amount']              = bcdiv($data['amount'], 1000, 2);
        $data['create_total_amount'] = bcdiv($data['create_total_amount'], 1000, 2);
        return $data;
    }


    private function getAuditLogs($req, $if_download = false)
    {
        $auth_logs = (new WorkflowServiceV2())->getAuditLogs($req);

        //下载的时候不要申请
        if ($if_download) {
            $temp = [];
            foreach ($auth_logs as $k => $v) {
                //如果申请的就跳过
                if ($v['action'] == 0) {
                    continue;
                }
                $temp[] = $v;
            }
            $auth_logs = $temp;
        }

        return $auth_logs;
    }

    /**
     * 添加归还/坏账申请单
     *
     * @param array $data 提交的表单信息组
     * @param array $user 当前登陆者信息
     * @return array
     */
    public function saveOne($data, $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db      = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $return_type            = $data['type'];//归还类型：1:归还，2:坏账
            $data['bank_flow_date'] = isset($data['bank_flow_date']) && $data['bank_flow_date'] ? $data['bank_flow_date'] : null;
            //判断银行流水日期(日期传递了并且是归还类型的归还单才判断)
            if ($data['bank_flow_date'] && $return_type == Enums\ReserveFundReturnEnums::RETURN_TYPE) {
                Validation::validate(['bank_flow_date' => $data['bank_flow_date']],
                    ['bank_flow_date' => 'Date|DateTo:' . date('Y-m-d')]);
            }
            // 判断当前登陆人是否拥有权限给其他申请人操作归还或坏账的权限
            if (isset($data['return_type']) && $data['return_type'] == Enums\ReserveFundReturnEnums::APPLY_TYPE_QUERY) {
                $audit = $this->checkCanOperateBtn($user['id'], 'reserve_fund_access_staff_id');
            } else {
                $audit = $this->checkCanOperateBtn($user['id']);
            }
            if ($audit && $user['id'] != $data['create_id']) {
                //拥有权限并且登陆人跟被操作的记录的申请不是同一人，需要获取被操作的申请人的信息
                $us         = new UserService();
                $apply_user = $us->getUserById($data['create_id']);
                $user       = [
                    'id'                => $apply_user->id,
                    'name'              => $apply_user->name,
                    'organization_type' => $apply_user->organization_type,
                    'organization_id'   => $apply_user->organization_id,
                    'department'        => $apply_user->getDepartment()->name ?? '',
                    'department_id'     => $apply_user->department_id,
                    'job_title'         => $apply_user->getJobTitle()->name ?? '',
                    'job_title_id'      => $apply_user->job_title,
                    'nick_name'         => $apply_user->nick_name ?? '',
                ];
            }
            // 存在待审核的备用金申请单
            $item_exist = ReserveFundApply::findFirst([
                "conditions" => 'create_id = :create_id: and (status = :status_pending: or (status = :status_pass: and pay_status = :pay:))',
                'bind'       => [
                    'create_id'      => (int)$user['id'],
                    'status_pending' => Enums::WF_STATE_PENDING,
                    'status_pass'    => Enums::WF_STATE_APPROVED,
                    'pay'            => Enums::PAYMENT_PAY_STATUS_PENDING,
                ],
            ]);
            if (isset($item_exist->id)) {
                throw new ValidationException(static::$t->_('reserve_fund_apply_have_been_exist'),
                    ErrCode::$VALIDATE_ERROR);
            }

            // 已经存在审核中的归还单
            $item_arr = ReserveFundReturn::find([
                'conditions' => 'create_id = :create_id: and status = :status:',
                'bind'       => [
                    'create_id' => (int)$user['id'],
                    'status'    => Enums::WF_STATE_PENDING,
                ],
            ])->toArray();
            if (!empty($item_arr)) {
                throw new ValidationException(static::$t->_('reserve_fund_return_not_empty'), ErrCode::$VALIDATE_ERROR);
            }

            // 获取某个用户某个网点审核通过、已支付的申请单且未添加过归还/坏账归还单的申请单列表有申请单数据才可做归还/坏账申请单的添加
            $return_type_col = "";                  //按照那个归还字段去筛选
            $return_status   = Enums\ReserveFundReturnEnums::BACK_STATUS_NOT;
            if ($return_type == Enums\ReserveFundReturnEnums::RETURN_TYPE) {
                //归还(未归还、未追回)
                $return_type_col = 'return_status in ({return_status:array})';
                $return_status   = [
                    Enums\ReserveFundReturnEnums::BACK_STATUS_NOT,
                    Enums\ReserveFundReturnEnums::BACK_STATUS_NOT_RECOVER,
                ];
            } else {
                if ($return_type == Enums\ReserveFundReturnEnums::RETURN_BAD_TYPE) {
                    //坏账（未归还）
                    $return_type_col = 'return_status = :return_status:';
                }
            }
            $apply_rel = [];//关联申请单数据列表
            if ($return_type_col) {
                $apply_rel_model = ReserveFundApply::find([
                    "conditions" => 'create_store_id = :create_store_id: and create_id = :create_id: and status = :status: 
                and pay_status = :pay_status: and ' . $return_type_col,
                    'bind'       => [
                        'create_store_id' => $data['create_store_id'],
                        'create_id'       => (int)$user['id'],
                        'status'          => Enums::WF_STATE_APPROVED,
                        'pay_status'      => Enums::PAYMENT_PAY_STATUS_PAY,
                        'return_status'   => $return_status,
                    ],
                    'order'      => 'id DESC',
                ]);
                $apply_rel       = $apply_rel_model->toArray();
            }
            if (empty($apply_rel)) {
                throw new ValidationException(static::$t->_('reserve_fund_apply_rel_not_exist'),
                    ErrCode::$VALIDATE_ERROR);
            }
            //保存归还申请单信息
            //之前币种都是表里获取的审批通过已支付的数据币种，产品说币种无需从库里取，直接用每个国家默认币种即可
            $default_currency            = (new EnumsService())->getSysCurrencyInfo();
            $data['currency']            = $default_currency['code'];
            $data['amount']              = bcmul($data['amount'], 1000, 2);             //前端提交过来的数据是没有✖️1000的
            $data['create_total_amount'] = bcmul($data['create_total_amount'], 1000, 2);//前端提交过来的数据是没有✖️1000的
            $data['status']              = Enums::CONTRACT_STATUS_PENDING;
            $data['created_at']          = date('Y-m-d H:i:s');
            $data['updated_at']          = date('Y-m-d H:i:s');
            $model                       = new ReserveFundReturn();
            $bool                        = $model->i_create($data);
            if ($bool === false) {
                throw new BusinessException('备用金归还申请单[添加失败]，待处理数据: ' . json_encode($data,
                        JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($model),
                    ErrCode::$CONTRACT_CREATE_ERROR);
            }
            //保存银行流水附件信息
            if (!empty($data['attachment_flow'])) {
                $attachments = [];
                foreach ($data['attachment_flow'] as $attachment) {
                    $tmp                    = [];
                    $tmp['oss_bucket_type'] = Enums::OSS_BUCKET_TYPE_RESERVE_FUND_RETURN_BANK_FLOW;
                    $tmp['oss_bucket_key']  = $model->id;
                    $tmp['bucket_name']     = $attachment['bucket_name'];
                    $tmp['object_key']      = $attachment['object_key'];
                    $tmp['file_name']       = $attachment['file_name'];
                    $attachments[]          = $tmp;
                }
                $attach      = new AttachModel();
                $attach_bool = $attach->batchInsert($attachments);
                if ($attach_bool === false) {
                    throw new BusinessException('备用金归还申请-银行流水附件[添加失败]，待处理数据: ' . json_encode($attachments,
                            JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($attach),
                        ErrCode::$CONTRACT_CREATE_ERROR);
                }
            }

            //保存附件信息
            if (!empty($data['attachment'])) {
                $attachments = [];
                foreach ($data['attachment'] as $attachment) {
                    $tmp                    = [];
                    $tmp['oss_bucket_type'] = Enums::OSS_BUCKET_TYPE_RESERVE_FUND_RETURN;
                    $tmp['oss_bucket_key']  = $model->id;
                    $tmp['bucket_name']     = $attachment['bucket_name'];
                    $tmp['object_key']      = $attachment['object_key'];
                    $tmp['file_name']       = $attachment['file_name'];
                    $attachments[]          = $tmp;
                }
                $attach      = new AttachModel();
                $attach_bool = $attach->batchInsert($attachments);
                if ($attach_bool === false) {
                    throw new BusinessException('备用金归还申请-附件[添加失败]，待处理数据: ' . json_encode($attachments,
                            JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($attach),
                        ErrCode::$CONTRACT_CREATE_ERROR);
                }
            }

            //申请单与归还单数据组
            $apply_return_rel = [];
            // 更新对应申请单的关联归还单ID
            foreach ($apply_rel_model as $rel) {
                $rel_update = [];
                if ($return_type == Enums\ReserveFundReturnEnums::RETURN_TYPE) {
                    //归还单类型
                    $rel_update = ['return_status' => Enums\ReserveFundReturnEnums::BACK_STATUS_ING];
                } else {
                    if ($return_type == Enums\ReserveFundReturnEnums::RETURN_BAD_TYPE) {
                        //坏账归还单类型
                        $rel_update = ['return_status' => Enums\ReserveFundReturnEnums::BACK_STATUS_CONFIRM];
                    }
                }
                $bool = $rel->i_update($rel_update);
                if ($bool === false) {
                    throw new BusinessException('备用金归还申请-更新申请单[归还状态失败], 待处理数据: ' . json_encode($rel_update,
                            JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($rel),
                        ErrCode::$CONTRACT_CREATE_ERROR);
                }
                $apply_return_rel[] = [
                    'apply_id'   => $rel->id,
                    'return_id'  => $model->id,
                    'created_at' => date('Y-m-d H:i:s'),
                ];
            }
            //16053需求改造后，申请单与归还单关系变更为存储关联关系表
            if (!empty($apply_return_rel)) {
                $apply_return_rel_model = new ReserveFundApplyReturnRelModel();
                $bool                   = $apply_return_rel_model->batch_insert($apply_return_rel);
                if ($bool === false) {
                    throw new BusinessException('备用金归还申请-申请单与归还单[绑定关联关系失败], 待处理数据: ' . json_encode($apply_return_rel,
                            JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($apply_return_rel_model),
                        ErrCode::$CONTRACT_CREATE_ERROR);
                }
            }

            $flow_bool = (new PayFlowService(Enums::WF_RESERVE_FUND_RETURN))->createRequest($model->id, $user);
            if ($flow_bool === false) {
                throw new BusinessException(static::$t->_('loan_create_work_flow_failed'),
                    ErrCode::$LOAN_CREATE_WORK_FLOW_ERROR);
            }

            $db->commit();
        } catch (ValidationException $e) {      //校验错误，可对外抛出
            if (in_array($e->getCode(), [ErrCode::$BUDGET_OVERAMOUNT_QUARTERLY, ErrCode::$BUDGET_OVERAMOUNT_MONTH])) {
                $code   = ErrCode::$SUCCESS;
                $result = [
                    'message'   => $e->getMessage(),
                    'can_apply' => $e->getCode() == ErrCode::$BUDGET_OVERAMOUNT_QUARTERLY ? 0 : 1,
                ];
            } else {
                $code    = $e->getCode();
                $message = $e->getMessage();
            }
        } catch (BusinessException $e) {        //业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {               //系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->warning('ReserveFundApply-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $result ?? [],
        ];
    }

    /**
     * @param $condition
     * @param $uid
     * @param int $type
     * @return array
     */
    public function getList($condition, $uid = null, $type = 0)
    {
        $condition['uid'] = $uid;
        $page_size        = empty($condition['pageSize']) ? 20 : $condition['pageSize'];
        $page_num         = empty($condition['pageNum']) ? 1 : $condition['pageNum'];
        $offset           = $page_size * ($page_num - 1);

        $code    = ErrCode::$SUCCESS;
        $message = '';
        $data    = [];
        try {
            $column_str = 'r.id,r.rrno,r.create_name,r.create_id,r.create_company_name,r.create_department_name,r.create_store_name,r.apply_date,r.amount,r.status,r.currency';

            // 审核模块的已处理列表, 展示处理时间
            if ($type == self::LIST_TYPE_AUDIT && isset($condition['flag']) && $condition['flag'] == GlobalEnums::AUDIT_TAB_PROCESSED) {
                $column_str .= ',log.audit_at';
            }

            $builder = $this->modelsManager->createBuilder();
            $builder->from(['r' => ReserveFundReturn::class]);
            $builder = $this->getCondition($builder, $condition, $type);

            $count = (int)$builder->columns('COUNT(DISTINCT r.id) AS total')->getQuery()->getSingleResult()->total;

            $items = [];
            if ($count) {
                $builder->columns($column_str);
                $builder->groupBy('r.id');

                if (!in_array($type, [self::LIST_TYPE_AUDIT, self::LIST_TYPE_FYR])) {
                    $builder->orderBy('r.id desc');
                }

                $builder->limit($page_size, $offset);
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleItems($items, $uid);
            }

            $data = [
                'items'      => $items,
                'pagination' => [
                    'current_page' => $page_num,
                    'per_page'     => $page_size,
                    'total_count'  => $count,
                ],
            ];
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('reserve-fund-list-failed:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * @param $builder
     * @param $condition
     * @param int $type
     * @return mixed
     */
    private function getCondition($builder, $condition, $type = 0)
    {
        $rrno             = $condition['rrno'] ?? '';
        $status           = $condition['status'] ?? 0;
        $apply_date_start = $condition['apply_date_start'] ?? '';
        $apply_date_end   = $condition['apply_date_end'] ?? '';
        $create_id        = $condition['create_name'] ?? '';
        $store_id         = $condition['create_store_id'] ?? '';

        // 征询回复进度
        $is_reply = $condition['is_reply'] ?? GlobalEnums::CONSULTED_REPLY_STATE_PENDING;
        $is_reply = in_array($is_reply,
            GlobalEnums::CONSULTED_REPLY_STATE_ITEM) ? $is_reply : GlobalEnums::CONSULTED_REPLY_STATE_PENDING;

        // 审批处理进度
        $flag = $condition['flag'] ?? GlobalEnums::AUDIT_TAB_PENDING;
        $flag = in_array($flag, GlobalEnums::AUDIT_TAB_STATE_ITEM) ? $flag : GlobalEnums::AUDIT_TAB_PENDING;

        //审核列表
        if ($type == self::LIST_TYPE_AUDIT) {
            $builder = (new WorkflowServiceV2())->getBizWorkflowOrderListByProcessState($builder, $flag,
                [Enums::WF_RESERVE_FUND_RETURN], $condition['uid'], 'r');
        } elseif ($type == self::LIST_TYPE_APPLY) {
            $builder->andWhere('r.create_id = :uid:', ['uid' => $condition['uid']]);
            // 申请列表: 申请人搜索选项是多余的, 因此置空, 无需参与模糊查询
            $create_id = null;
        } elseif ($type == self::LIST_TYPE_FYR) {
            // 意见征询回复列表
            $biz_table_info = ['table_alias' => 'r'];
            $builder        = (new FYRService())->getBizOrderConsultationReplyListByProcessState($builder, $is_reply,
                [Enums::WF_RESERVE_FUND_RETURN], $condition['uid'], $biz_table_info);
        }

        if (!empty($rrno)) {
            $builder->andWhere('r.rrno = :rrno:', ['rrno' => $rrno]);
        }

        //审核，或者是下载，或者数据
        //工号或者姓名
        if (!empty($create_id)) {
            $builder->andWhere('r.create_id = :create_id: or r.create_name=:create_id:', ['create_id' => $create_id]);
        }

        if (!empty($status)) {
            $builder->andWhere('r.status = :status:', ['status' => $status]);
        }

        if (!empty($pay_status)) {
            $builder->andWhere('r.pay_status = :pay_status:', ['pay_status' => $pay_status]);
        }

        if (!empty($apply_date_start)) {
            $builder->andWhere('r.apply_date >= :apply_date_start:', ['apply_date_start' => $apply_date_start]);
        }

        if (!empty($apply_date_end)) {
            $builder->andWhere('r.apply_date <= :apply_date_end:', ['apply_date_end' => $apply_date_end]);
        }

        if (!empty($store_id)) {
            $builder->andWhere('r.create_store_id = :store_id:', ['store_id' => $store_id]);
        }

        return $builder;
    }

    /**
     * @param $items
     * @return array
     */
    private function handleItems($items, $uid = null)
    {
        if (empty($items) || !is_array($items)) {
            return [];
        }
        foreach ($items as &$item) {
            $status                = Enums::$loan_status[$item['status']] ?? '';
            $item['amount']        = bcdiv($item['amount'], 1000, 2);
            $item['status_text']   = static::$t->_($status);
            $payment_currency      = GlobalEnums::$currency_item[$item['currency']] ?? '';
            $item['currency_text'] = static::$t->_($payment_currency);
        }
        return $items;
    }


    public function getUserMetaFromBi($userId)
    {
        $model = (new UserService())->getUserByIdInRbi($userId);
        if (empty($model)) {
            return [];
        }

        $data                = [];
        $data['create_id']   = $model->staff_info_id ?? "";
        $data['create_name'] = $this->getNameAndNickName($model->name ?? "", $model->nick_name ?? "");

        $department_id                = $model->sys_department_id;
        $node_department_id           = $model->node_department_id;
        $data['create_store_id']      = $model->sys_store_id;
        $data['create_department_id'] = empty($node_department_id) ? $department_id : $node_department_id;
        $t                            = DepartmentModel::findFirst([
            "conditions" => "id = :id:",
            "bind"       => [
                "id" => $data['create_department_id'],
            ],
        ]);
        $s                            = StoreModel::findFirst([
            "conditions" => "id = :id:",
            "bind"       => [
                "id" => $data['create_store_id'],
            ],
        ]);

        $data['create_department_name'] = !empty($t) ? $t->name : "";
        $data['create_company_id']      = $t->company_id ?? 0;
        $data['create_company_name']    = $t->company_name ?? '';
        $data['create_store_name']      = $s->name ?? '';
        return $data;
    }

    /**
     * 清空归还单和申请单关联关系
     *
     * @param integer $id 归还表ID
     * @param integer $type 1归还，2坏账
     * @return bool
     */
    public function clearApplyReturnRelation($id, $type)
    {
        //获取归还单关联的申请单的关联关系
        $apply_rel     = ReserveFundApplyReturnRelModel::find([
            'conditions' => 'return_id = :return_id:',
            'bind'       => ['return_id' => $id],
        ]);
        $apply_rel_arr = $apply_rel->toArray();
        if (!empty($apply_rel_arr)) {
            //先删除关联关系
            $del_ret = $apply_rel->delete();
            if ($del_ret === false) {
                return false;
            }
            //找到关联的申请单信息
            $apply_ids      = array_column($apply_rel_arr, 'apply_id');
            $apply_list     = ReserveFundApply::find([
                'conditions' => 'id in ({ids:array})',
                'bind'       => ['ids' => $apply_ids],
            ]);
            $apply_rel_data = $apply_list->toArray();
            if (!empty($apply_rel_data)) {
                //变更申请单上的归还状态
                foreach ($apply_list as $rel) {
                    //针对归还单驳回时需要判断是否有审核已通过的坏账单记录(return_bad_id!=0)，如果有则申请表中的归还状态要标记为未追回
                    if ($type == Enums\ReserveFundReturnEnums::RETURN_TYPE && $this->checkBadReturn($rel->id) > 0) {
                        $return_status = Enums\ReserveFundReturnEnums::BACK_STATUS_NOT_RECOVER;
                    } else {
                        //其他标记为未归还
                        $return_status = Enums\ReserveFundReturnEnums::BACK_STATUS_NOT;
                    }
                    $bool = $rel->i_update(['return_status' => $return_status, 'updated_at' => date('Y-m-d H:i:s')]);
                    if ($bool === false) {
                        return false;
                    }
                }
            }
        }
        return true;
    }

    /**
     * 判断某笔申请单下是否有审核已通过的坏账单记录
     *
     * @param integer $apply_id 申请单id
     * @return int
     */
    private function checkBadReturn($apply_id)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['rl' => ReserveFundApplyReturnRelModel::class]);
        $builder->leftJoin(ReserveFundReturn::class, 'r.id = rl.return_id', 'r');
        $builder->columns('count(rl.id) as total');
        $builder->where('rl.apply_id = :apply_id: and rl.is_deleted = :is_deleted: and r.type = :type: and r.status = :status:',
            [
                'apply_id'   => $apply_id,
                'is_deleted' => GlobalEnums::IS_NO_DELETED,
                'type'       => Enums\ReserveFundReturnEnums::RETURN_BAD_TYPE,
                'status'     => Enums::WF_STATE_APPROVED,
            ]);
        return intval($builder->getQuery()->getSingleResult()->total);
    }

    /**
     * 获取申请人网点纬度的记录信息
     *
     * @param integer $staff_id 申请人工号
     * @param string $create_store_id 网点ID
     * @return array
     */
    public function getStaffFundApplyForStore($staff_id, $create_store_id)
    {
        if (empty($staff_id)) {
            return [];
        }
        $builder = $this->modelsManager->createBuilder();
        $builder->from([ReserveFundApply::class]);
        $builder->columns([
            'create_store_id',
            'create_store_name',
            'create_company_id',
            'create_company_name',
            'create_department_id',
            'create_department_name',
        ]);
        $builder->where('create_id = :create_id: and status IN ({status:array}) and pay_status IN ({pay_status:array})',
            [
                'create_id'  => $staff_id,
                'status'     => [Enums::CONTRACT_STATUS_PENDING, Enums::CONTRACT_STATUS_APPROVAL],
                'pay_status' => [Enums::PAYMENT_PAY_STATUS_PAY, Enums::PAYMENT_PAY_STATUS_PENDING],
            ]);
        if ($create_store_id) {
            $builder->andWhere('create_store_id = :create_store_id:', ['create_store_id' => $create_store_id]);
        }
        $builder->groupBy('create_store_id');
        return $builder->getQuery()->execute()->toArray();
    }
}
