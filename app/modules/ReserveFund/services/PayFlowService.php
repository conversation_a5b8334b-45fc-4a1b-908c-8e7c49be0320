<?php

namespace App\Modules\ReserveFund\Services;

use App\Library\Enums;
use App\Library\Enums\SysConfigEnums;
use App\Library\ErrCode;
use App\Library\Enums\GlobalEnums;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Models\oa\ReserveFundApplyReturnRelModel;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Pay\Services\PayService;
use App\Modules\ReserveFund\Services\ReturnService;
use App\Modules\ReserveFund\Models\PurchaseApply;
use App\Modules\Organization\Models\SysDepartmentModel;
use App\Modules\ReserveFund\Models\PurchaseOrder;
use App\Modules\ReserveFund\Models\PurchaseOrderProduct;
use App\Modules\ReserveFund\Models\PurchasePayment;
use App\Modules\ReserveFund\Models\PurchasePaymentReceipt;
use App\Modules\ReserveFund\Models\ReserveFundApply;
use App\Modules\ReserveFund\Models\ReserveFundReturn;
use App\Modules\User\Services\UserService;
use App\Modules\Workflow\Models\WorkflowAuditLogModel;
use App\Modules\Workflow\Models\WorkflowNodeModel;
use App\Modules\Workflow\Models\WorkflowUpdateLogModel;
use App\Modules\Workflow\Services\AbstractFlowService;
use App\Modules\Workflow\Services\WorkflowServiceV2;
use Google\Protobuf\Enum;
use Phalcon\Mvc\Model;

class PayFlowService extends AbstractFlowService
{
    private $model    = null;
    private $blz_type = null;

    //no字段
    public $field_no = 'lno';

    //关联表id
    public $link_field_no = 'paid';

    //note字段
    public $field_note = 'operation_remark';

    //department_id字段
    public $field_department_id = 'create_department';

    public $update_data = [];

    //各国家网点类型对应的审批流  TODO以后需求各国家统一了, 这里要合并
    public $store_category = [
        GlobalEnums::TH_COUNTRY_CODE => [
            'network'       => [1, 2, 10, 13, 14],
            'network_bulky' => [1, 2, 10, 13, 14],
            'shop'          => [4, 5, 7],
            'hub'           => [8, 9, 12],
        ],
        GlobalEnums::LA_COUNTRY_CODE => [
            'network' => [1, 2, 10, 13, 14],
            'hub'     => [8, 9, 12],
        ],
        GlobalEnums::PH_COUNTRY_CODE => [
            'network' => [1, 2, 10, 13, 14],
            'hub'     => [8, 9, 12],
        ],
        'COMMON'                     => [
            'network' => [1, 2, 10, 13, 14],
            'shop'    => [4, 5, 7],
            'hub'     => [8, 9, 12],
        ],
    ];

    public function __construct($blz_type, $update_data = [])
    {
        $this->blz_type = $blz_type;

        switch ($this->blz_type) {
            case Enums::WF_RESERVE_FUND_APPLY:
                $this->model    = new ReserveFundApply();
                $this->field_no = 'rfano';
                break;
            case Enums::WF_RESERVE_FUND_RETURN:
                $this->model    = new ReserveFundReturn();
                $this->field_no = 'rrno';
                break;
        }

        $this->update_data = $update_data;
    }


    /**
     * @param $id
     * @param $note
     * @param $user
     * @return array
     */
    public function approve($id, $note, $user)
    {
        $code     = ErrCode::$SUCCESS;
        $message  = $real_message = '';
        $work_req = $this->getRequest($id);
        $db       = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            if (empty($work_req->id)) {
                throw new BusinessException('获取工作流批次失败', ErrCode::$CONTRACT_GET_WORK_REQUEST_ERROR);
            }
            $item = $this->model::findFirst([
                'id = :id:',
                'bind' => ['id' => $id],
            ]);
            if ($item->status == Enums::CONTRACT_STATUS_CANCEL) {
                throw new ValidationException(static::$t->_('contract_has_been_withdrawal'), ErrCode::$VALIDATE_ERROR);
            }

            //如果更新数据不为空
            if (!empty($this->update_data)) {
                $can_edit_field = $this->getCanEditFieldByReq($work_req, $user['id']);
                if (!empty($can_edit_field)) {
                    $this->dealEditField($item, $can_edit_field, $this->update_data, $work_req, $user);
                }
            }

            $ws     = new WorkflowServiceV2();
            $result = $ws->doApprove($work_req, $user, $this->getWorkflowParams($item, $user), $note);
            if (!empty($result->approved_at)) {
                $update_data = [
                    'status'     => Enums::WF_STATE_APPROVED,
                    'updated_at' => date('Y-m-d H:i:s'),
                    'approve_at' => date("Y-m-d H:i:s"),
                ];
                $bool        = $item->i_update($update_data);
                if ($bool === false) {
                    throw new BusinessException('工作流拒绝更新宿主失败,宿主id=' . $item->id . "==宿主type==" . $this->blz_type . '; 可能存在问题是：' . get_data_object_error_msg($item),
                        ErrCode::$CONTRACT_UPDATE_ERROR);
                } else {
                    if ($this->blz_type == Enums::WF_RESERVE_FUND_APPLY) {
                        $pay_staff_id = (new BaseService())->getReserveFundPaymentPayStaffIds();
                        $this->sendEmailToAuditors($work_req, $pay_staff_id, 1);
                        $this->delUnReadNumsKeyByStaffIds($pay_staff_id);

                        if (EnumsService::getInstance()->getPayModuleStatus(SysConfigEnums::SYS_MODULE_RESERVE,
                            $item->create_company_id)) {
                            PayService::getInstance()->saveOne($item);
                        }
                    } else {
                        if ($this->blz_type == Enums::WF_RESERVE_FUND_RETURN) {
                            //归还审批流最后一个审批节点通过时，获取该归还单关联的所有申请单列表
                            $apply_return_rel = ReserveFundApplyReturnRelModel::find([
                                'conditions' => 'return_id = :return_id:',
                                'bind'       => ['return_id' => $item->id],
                                'columns'    => 'apply_id',
                            ])->toArray();
                            $apply_ids        = implode(',', array_column($apply_return_rel, 'apply_id'));
                            if ($item->type == Enums\ReserveFundReturnEnums::RETURN_TYPE) {
                                //需将关联了归还单的申请单的归还状态置为已归还3
                                $db->updateAsDict('reserve_fund_apply', [
                                    'return_status' => Enums\ReserveFundReturnEnums::BACK_STATUS_BACK,
                                    'updated_at'    => date('Y-m-d H:i:s'),
                                ], ['conditions' => "id in ({$apply_ids})"]);
                            } else {
                                if ($item->type == Enums\ReserveFundReturnEnums::RETURN_BAD_TYPE) {
                                    //需将关联了坏账归还单的申请单的归还状态由待确认4置为未追回5
                                    $db->updateAsDict('reserve_fund_apply', [
                                        'return_status' => Enums\ReserveFundReturnEnums::BACK_STATUS_NOT_RECOVER,
                                        'updated_at'    => date('Y-m-d H:i:s'),
                                    ],
                                        ['conditions' => "id in ({$apply_ids}) and return_status = " . Enums\ReserveFundReturnEnums::BACK_STATUS_CONFIRM]);
                                }
                            }
                        }
                    }
                }
            }
            $db->commit();
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = $e->getCode();
            $message      = $e->getMessage();
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->warning('ReserveFund - PayFlowService===approve-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
        ];
    }

    /**
     * @param $id
     * @param $note
     * @param $user
     * @return array
     */
    public function reject($id, $note, $user)
    {
        $code     = ErrCode::$SUCCESS;
        $message  = $real_message = '';
        $work_req = $this->getRequest($id);
        $db       = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            if (empty($work_req->id)) {
                throw new BusinessException('获取工作流批次失败', ErrCode::$CONTRACT_GET_WORK_REQUEST_ERROR);
            }
            $item = $this->model::findFirst([
                'id = :id:',
                'bind' => ['id' => $id],
            ]);
            if ($item->status == Enums::CONTRACT_STATUS_CANCEL) {
                throw new ValidationException(static::$t->_('contract_has_been_withdrawal'), ErrCode::$VALIDATE_ERROR);
            }
            $result = (new WorkflowServiceV2())->doReject($work_req, $user, $this->getWorkflowParams($item, $user),
                $note);
            if ($result === false) {
                throw new BusinessException('审批流拒绝失败', ErrCode::$CONTRACT_WORK_FLOW_REJECT_ERROR);
            }
            $bool = $item->i_update([
                'status'          => Enums::CONTRACT_STATUS_REJECTED,
                'updated_at'      => date('Y-m-d H:i:s'),
                $this->field_note => $note,
            ]);
            if ($bool === false) {
                throw new BusinessException('工作流拒绝更新宿主失败,宿主id=' . $item->id . "==宿主type==" . $this->blz_type,
                    ErrCode::$CONTRACT_UPDATE_ERROR);
            }
            // 归还单驳回，需要清空关联单号
            if ($this->blz_type == Enums::WF_RESERVE_FUND_RETURN) {
                $bool = ReturnService::getInstance()->clearApplyReturnRelation($item->id, $item->type);
                if ($bool === false) {
                    throw new BusinessException('归还单关联关系清空失败=' . $item->id . "==宿主type==" . $this->blz_type,
                        ErrCode::$CONTRACT_UPDATE_ERROR);
                }
            }

            $db->commit();
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('PayFlowService===reject-failed:' . $real_message);
        }


        return [
            'code'    => $code,
            'message' => $message,
        ];
    }

    /**
     * @param $id
     * @param $note
     * @param $user
     * @return array
     */
    public function cancel($id, $note, $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $work_req = $this->getRequest($id);
        $db       = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            //只能撤回自己的
            $item = $this->model::findFirst([
                'id = :id: and create_id =:uid:',
                'bind' => ['id' => $id, "uid" => $user['id']],
            ]);
            if (empty($item)) {
                throw new BusinessException(static::$t->_('contract_get_info_failed_when_update'),
                    ErrCode::$CONTRACT_GET_INFO_ERROR);
            }
            $result = (new WorkflowServiceV2())->doCancel($work_req, $user, $this->getWorkflowParams($item, $user),
                $note);
            if ($result === false) {
                throw new BusinessException('审批流拒绝失败', ErrCode::$CONTRACT_WORK_FLOW_REJECT_ERROR);
            }
            $bool = $item->i_update([
                'status'          => Enums::CONTRACT_STATUS_CANCEL,
                'updated_at'      => date('Y-m-d H:i:s'),
                $this->field_note => $note,
            ]);
            if ($bool === false) {
                throw new BusinessException(static::$t->_('contract_withdrawal_failed'),
                    ErrCode::$CONTRACT_CANCEL_ERROR);
            }
            // 归还单撤回，需要清空关联单号
            if ($this->blz_type == Enums::WF_RESERVE_FUND_RETURN) {
                $bool = ReturnService::getInstance()->clearApplyReturnRelation($item->id, $item->type);
                if ($bool === false) {
                    throw new BusinessException('归还单关联关系清空失败=' . $item->id . "==宿主type==" . $this->blz_type,
                        ErrCode::$CONTRACT_UPDATE_ERROR);
                }
            }

            $db->commit();
        } catch (ValidationException $e) {               //校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {                 //业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {                       //系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('PayFlowService===cancel-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
        ];
    }

    /**
     * @param int $id 业务数据ID
     * @param
     * @return \Phalcon\Mvc\Model|void
     */
    public function getRequest($id)
    {
        return $this->getRequestByBiz($id, $this->blz_type);
    }

    /**
     * @param $id
     * @param $user
     * @return \Phalcon\Mvc\Model\Resultset|\Phalcon\Mvc\Phalcon\Mvc\Model
     * @throws BusinessException
     * @throws ValidationException
     */
    public function createRequest($id, $user)
    {
        $item = $this->model::findFirst([
            'id = :id:',
            'bind' => ['id' => $id],
        ]);

        $data['id']   = $item->id;
        $field_no     = $this->field_no;
        $data['name'] = $item->$field_no . '审批申请';

        $data['biz_type'] = $this->blz_type;
        $info             = $this->getWorkflowParams($item, $user);
        $data['flow_id']  = $this->getFlowId($item);
        return (new WorkflowServiceV2())->createRequest($data, $user, $info);
    }

    /**
     * 获取备用金模块的审批流id
     *
     * @param null $item
     * @return int
     * @date 2022/6/8
     * @throws BusinessException
     * @throws ValidationException
     */
    public function getFlowId($item = null)
    {
        if (!$item) {
            throw new BusinessException('not found the flow id, item is empty', ErrCode::$BUSINESS_ERROR);
        }

        $country_code = get_country_code();
        $company_ids  = EnumsService::getInstance()->getSysDepartmentCompanyIds();
        $store_id     = 0;
        $store        = [];
        if (isset($item->create_store_id)) {
            $store_id = $item->create_store_id;
        }

        $work_flow_service = new WorkflowServiceV2();

        // 备用金申请审批流
        $data = '';
        if ($this->blz_type == Enums::WF_RESERVE_FUND_APPLY) {
            $store = $work_flow_service->getStoreById($store_id);

            if ($country_code == GlobalEnums::TH_COUNTRY_CODE) {
                //快递公司区分 网点
                if ($item->create_company_id == $company_ids['FlashExpress']) {
                    $country_config = $this->store_category[$country_code] ?? $this->store_category['COMMON'];
                    if ((isset($country_config['network']) && in_array($store['category'],
                                $country_config['network'])) || (isset($country_config['hub']) && in_array($store['category'],
                                $country_config['hub']))) {//hub和network
                        $data = Enums::WF_RESERVE_FUND_APPLY_TYPE337;
                    } else {
                        if (isset($country_config['shop']) && in_array($store['category'], $country_config['shop'])) {
                            $data = Enums::WF_RESERVE_FUND_APPLY_TYPE338;
                        }
                    }
                } else {//非快递公司
                    $data = Enums::WF_RESERVE_FUND_APPLY_TYPE339;
                }
            } else {
                // 申请公司是指定子公司的
                if (isset($item->create_company_id) && in_array((string)$item->create_company_id, [
                        $company_ids['FlashFullfillment'],
                        $company_ids['FlashPay'],
                        $company_ids['FlashMoney'],
                        $company_ids['FCommerce'],
                    ])) {
                    $data = Enums::WF_RESERVE_FUND_APPLY_FFM_WF_ID;
                } else {
                    // 审批流是否按部门对应的网点类型来
                    $store_workflow = EnvModel::getEnvByCode('reserve_fund_apply_store_workflow', 0);

                    if ($store_workflow == 1) {
                        $country_config = $this->store_category[$country_code] ?? $this->store_category['COMMON'];

                        if (isset($country_config['network']) && in_array($store['category'],
                                $country_config['network'])) {
                            $data = ($country_code == GlobalEnums::LA_COUNTRY_CODE || ($item->create_company_id == $company_ids['FlashExpress'] && in_array($country_code,
                                        [
                                            GlobalEnums::PH_COUNTRY_CODE,
                                            GlobalEnums::MY_COUNTRY_CODE,
                                        ]))) ? Enums::WF_RESERVE_FUND_APPLY_TYPE139 : Enums::WF_RESERVE_FUND_APPLY_TYPE75;
                        } else {
                            if (isset($country_config['shop']) && in_array($store['category'],
                                    $country_config['shop'])) {
                                $data = Enums::WF_RESERVE_FUND_APPLY_TYPE74;
                            } else {
                                if (isset($country_config['hub']) && in_array($store['category'],
                                        $country_config['hub'])) {
                                    $data = ($country_code == GlobalEnums::LA_COUNTRY_CODE || ($item->create_company_id == $company_ids['FlashExpress'] && in_array($country_code,
                                                [
                                                    GlobalEnums::PH_COUNTRY_CODE,
                                                    GlobalEnums::MY_COUNTRY_CODE,
                                                ]))) ? Enums::WF_RESERVE_FUND_APPLY_TYPE141 : Enums::WF_RESERVE_FUND_APPLY_TYPE73;
                                } else {
                                    if (isset($country_config['network_bulky']) && in_array($store['category'],
                                            $country_config['network_bulky'])) {
                                        $data = Enums::WF_RESERVE_FUND_APPLY_TYPE142;
                                    }
                                }
                            }
                        }
                    } else {
                        //备用金申请
                        $data = Enums::WF_RESERVE_FUND_APPLY_TYPE46;
                    }
                }
            }
            // 备用金归还审批流
        } else {
            if ($this->blz_type == Enums::WF_RESERVE_FUND_RETURN) {
                // 申请公司是指定子公司的
                if (isset($item->create_company_id) && in_array((string)$item->create_company_id, [
                        $company_ids['FlashFullfillment'],
                        $company_ids['FlashPay'],
                        $company_ids['FlashMoney'],
                        $company_ids['FCommerce'],
                    ])) {
                    $data = Enums::WF_RESERVE_FUND_RETURN_FFM_WF_ID;
                } else {
                    $country_config = $this->store_category[$country_code] ?? $this->store_category['COMMON'];
                    $store          = $work_flow_service->getStoreById($store_id);
                    if (isset($country_config['network']) && in_array($store['category'], $country_config['network'])) {
                        $data = (in_array($country_code, [
                                GlobalEnums::TH_COUNTRY_CODE,
                                GlobalEnums::LA_COUNTRY_CODE,
                            ]) || ($item->create_company_id == $company_ids['FlashExpress'] && $country_code == GlobalEnums::PH_COUNTRY_CODE)) ? Enums::WF_RESERVE_FUND_APPLY_TYPE143 : Enums::WF_RESERVE_FUND_RETURN_TYPE47;
                    } else {
                        if (isset($country_config['shop']) && in_array($store['category'], $country_config['shop'])) {
                            $data = $country_code == GlobalEnums::TH_COUNTRY_CODE ? Enums::WF_RESERVE_FUND_APPLY_TYPE144 : Enums::WF_RESERVE_FUND_RETURN_TYPE49;
                        } else {
                            if (isset($country_config['hub']) && in_array($store['category'], $country_config['hub'])) {
                                $data = (in_array($country_code, [
                                        GlobalEnums::TH_COUNTRY_CODE,
                                        GlobalEnums::LA_COUNTRY_CODE,
                                    ]) || ($item->create_company_id == $company_ids['FlashExpress'] && $country_code == GlobalEnums::PH_COUNTRY_CODE)) ? Enums::WF_RESERVE_FUND_APPLY_TYPE145 : Enums::WF_RESERVE_FUND_RETURN_TYPE48;
                            } else {
                                if (isset($country_config['network_bulky']) && in_array($store['category'],
                                        $country_config['network_bulky'])) {
                                    $data = Enums::WF_RESERVE_FUND_APPLY_TYPE146;
                                }
                            }
                        }
                    }
                }
            }
        }

        // 未配置 或 未找到对应审批流, 给出提示语
        if (empty($data)) {
            $this->logger->warning('biz_data: ' . json_encode($item->toArray(),
                    JSON_UNESCAPED_UNICODE) . 'store_category_setting: ' . json_encode($this->store_category,
                    JSON_UNESCAPED_UNICODE) . 'store_data: ' . json_encode($store, JSON_UNESCAPED_UNICODE));

            throw new ValidationException(static::$t->_('not_found_spc_flow_id'), ErrCode::$VALIDATE_ERROR);
        }

        return $data;
    }

    /**
     * 审批流需要数据
     *
     * @param $item
     * @param $user
     * @return array
     */
    public function getWorkflowParams($item, $user)
    {
        //if ('PH' == strtoupper(env('country_code'))) {
        //    $exchange_rate = empty($item->exchange_rate) ? (new EnumsService())->getCurrencyExchangeRate($item->currency) : $item->exchange_rate;
        //    $amount = (new EnumsService())->amountExchangeRateCalculation($item->amount/1000, $exchange_rate, 2);
        //} else {
        //    $amount = $item->amount;
        //}
        $exchange_rate = empty($item->exchange_rate) ? EnumsService::getInstance()->getCurrencyExchangeRate($item->currency) : $item->exchange_rate;
        $exchange_rate = $exchange_rate ? $exchange_rate : 1;

        $amount = EnumsService::getInstance()->amountExchangeRateCalculation($item->amount / 1000, $exchange_rate, 2);
        return [
            'amount'             => $amount,
            'currency'           => $item->currency,
            'submitter_id'       => $item->create_id,
            'node_department_id' => $item->create_department_id,
            'store_id'           => $item->create_store_id,
            'department_id'      => $item->create_department_id,
            'company_id'         => $item->create_company_id,
            'create_company_id'  => $item->create_company_id //2023年6月30应产品要求,部门字段改为公司字段
        ];
    }

    /**
     * 处理更新数据
     *
     * @param object $item 要更新可审批编辑数据对象信息
     * @param array $edit_field 在workflow_node表中can_edit_field 配置参数组
     * @param array $update_data 要在审批过程中要变更的数据
     * @param object $work_req 当前审批节点数据对象信息
     * @param array $user 当前登陆操作人信息
     */
    public function dealEditField($item, $edit_field, $update_data, $work_req, $user)
    {
        $mainData        = [];
        $logData         = [];
        $logData['main'] = [];
        $update_flag     = false;
        if (!empty($edit_field['main'])) {
            foreach ($edit_field['main'] as $key) {
                //如果没有定义该参数，则不修改,isset针对如果像日期字段设置为null需要存储时，就会标记为true，导致日期重置不会被修改
                if (!isset($update_data[$key]) && $key != 'bank_flow_date') {
                    continue;
                }
                $tmp = $update_data[$key];
                //不相等，才记录
                if ($tmp != $item->$key) {
                    $log['before']     = $item->$key;
                    $log['after']      = $tmp;
                    $log['field_name'] = $key;
                    $logData['main'][] = $log;

                    $update_flag    = true;
                    $mainData[$key] = $tmp;
                }
            }
        }
        //有变更数据则更新主表信息
        if (!empty($mainData)) {
            $item->update($mainData);
        }
        //记录审批操作过程中更新日志
        if ($update_flag) {
            $log = new WorkflowUpdateLogModel();
            $log->save(
                [
                    'request_id'       => $work_req->id,
                    'flow_id'          => $work_req->flow_id,
                    'flow_node_id'     => $work_req->current_flow_node_id,
                    'staff_id'         => $user['id'],
                    'staff_name'       => $this->getNameAndNickName($user['name'], $user['nick_name']),
                    'staff_department' => $user['department'],
                    'staff_job_title'  => $user['job_title'],
                    'content'          => json_encode($logData, JSON_UNESCAPED_UNICODE),
                    'created_at'       => date('Y-m-d H:i:s'),
                ]
            );
        }
    }

    /**
     * 获得修改的日志
     *
     * @param $req
     * @return array
     */
    public function getEditLog($req)
    {
        $list = WorkflowUpdateLogModel::find(
            [
                'conditions' => 'request_id = :id:',
                'bind'       => ['id' => $req->id],
                'order'      => 'id DESC',
            ]
        )->toArray();


        foreach ($list as $k => $item) {
            $logArr  = [];
            $content = json_decode($item['content'], 1);
            foreach ($content['main'] as $kk => $vv) {
                if ($vv['is_display'] == 0) {
                    continue;
                }


                $log               = [];
                $log['field_name'] = static::$t->_($vv['field_name']);
                if ($vv['need_translate']) {
                    $log['before'] = static::$t->_($vv['before']);
                    $log['after']  = static::$t->_($vv['after']);
                } else {
                    $log['before'] = $vv['before'];
                    $log['after']  = $vv['after'];
                }
                $logArr[] = $log;
            }

            foreach ($content['meta'] as $kk => $vv) {
                if ($vv['is_display'] == 0) {
                    continue;
                }
                $log               = [];
                $log['field_name'] = $vv['no'] . "-" . static::$t->_($vv['field_name']);
                if ($vv['need_translate']) {
                    $log['before'] = static::$t->_($vv['before']);
                    $log['after']  = static::$t->_($vv['after']);
                } else {
                    //税率
                    if ($vv['field_name'] == 'purchase_product_field_wht_ratio') {
                        $log['before'] = "%" . $vv['before'];
                        $log['after']  = "%" . $vv['after'];
                    } else {
                        $log['before'] = $vv['before'];
                        $log['after']  = $vv['after'];
                    }
                }
                $logArr[] = $log;
            }

            $list[$k]['log'] = $logArr;
            unset($list[$k]['content']);
        }
        return $list;
    }
}
