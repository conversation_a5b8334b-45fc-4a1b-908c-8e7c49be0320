<?php

namespace App\Modules\ReserveFund\Controllers;

use App\Library\Enums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\ReserveFund\Services\PayFlowService;
use App\Modules\ReserveFund\Services\ReturnService;
use App\Modules\User\Services\UserService;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class ReturnController extends BaseController
{
    /**
     * 备用金归还添加
     * @Permission(action='reserve.return.apply')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/24270
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function addAction()
    {
        $data = $this->request->get();

        $data['type'] = Enums\ReserveFundReturnEnums::RETURN_TYPE;
        Validation::validate($data, ReturnService::$validate_param);

        /**
         * 在备用金归还页面归还操作人是当前申请人归还自己的
         * 在数据查询页面操作归还是设置用于归还权限的工号可以帮其他申请人归还包括自己
         */
        $res = ReturnService::getInstance()->saveOne($data, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 备用金申请单获得默认值
     * @Permission(action='reserve.return.apply')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/24266
     * @return Response|ResponseInterface
     */
    public function getDefaultAction()
    {
        $apply_id        = $this->request->get('uid');
        $create_store_id = $this->request->get('create_store_id');
        $create_store_id = $create_store_id ?? '';
        $user            = empty($apply_id) ? $this->user : ['id' => $apply_id];
        $res             = ReturnService::getInstance()->defaultData($user, $create_store_id);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 归还申请单列表
     * @Permission(action='reserve.return.search')
     *
     * @return Response|ResponseInterface
     * @api https://yapi.flashexpress.pub/project/133/interface/api/24274
     */
    public function listAction()
    {
        $params = $this->request->get();
        $res    = ReturnService::getInstance()->getList($params, $this->user['id'], ReturnService::LIST_TYPE_APPLY);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 归还申请单详情
     * @Permission(action='reserve.return.view')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/24278
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function detailAction()
    {
        $id = $this->request->get('id');
        Validation::validate(['id' => $id], ReturnService::$validate_detail);

        $res = ReturnService::getInstance()->getDetail($id, $this->user['id']);
        if ($res['code'] == ErrCode::$SUCCESS) {
            [$res['data']['state'],$res['data']['state_text']] = ReturnService::getInstance()->formatStaffState($res['data']['create_id']);
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }


    /**
     * 归还撤销 - 暂时没有该功能
     * @Permission(action='reserve.return.cancel')
     *
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function cancelAction()
    {
        $data = $this->request->get();
        $id   = $this->request->get('id', 'int');
        $note = $this->request->get('note', 'trim');

        Validation::validate($data, ReturnService::$validate_cancel);

        $res = (new PayFlowService(Enums::WF_RESERVE_FUND_RETURN))->cancel($id, $note, $this->user);
        // todo 撤销 释放占用

        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 归还申请单申请单-审核列表
     * @Permission(action='reserve.audit.return.search')
     * @return Response|ResponseInterface
     */
    public function auditListAction()
    {
        $params = $this->request->get();
        $res    = ReturnService::getInstance()->getList($params, $this->user['id'], ReturnService::LIST_TYPE_AUDIT);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }


    /**
     * 归还申请单-审核详情
     * @Permission(action='reserve.audit.return.view')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function auditDetailAction()
    {
        $id = $this->request->get('id');
        Validation::validate(['id' => $id], ReturnService::$validate_detail);

        $res = ReturnService::getInstance()->getDetail($id, $this->user['id'], false, true);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }


    /**
     * 归还申请单-审核
     * @Permission(action='reserve.audit.return.audit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/24290
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function auditAction()
    {
        $data           = $this->request->get();
        $id             = $this->request->get('id', 'int');
        $flag           = $this->request->get("pass_or_not", 'int');
        $note           = $this->request->get('note', 'trim');
        $bank_flow_date = $this->request->get('bank_flow_date', 'trim');

        //审核通过操作&&传递了银行流水日期
        if ($bank_flow_date && $flag == 1) {
            Validation::validate(['bank_flow_date' => $data['bank_flow_date']],
                ['bank_flow_date' => 'Date|DateTo:' . date('Y-m-d')]);
        }

        Validation::validate($data, ReturnService::$validate_audit);

        //传递了银行流水日期则需要在审批过程中对改字段进行变更操作
        $bank_flow_date = $bank_flow_date ? $bank_flow_date : null;
        //只有审核通过的才需要审批过程中修改了银行流水信息要做保存操作
        $audit_update = ($flag == 1) ? ['bank_flow_date' => $bank_flow_date] : [];
        $service      = new PayFlowService(Enums::WF_RESERVE_FUND_RETURN, $audit_update);
        if ($flag == 1) {
            //通过
            $res = $service->approve($id, $note, $this->user);
        } else {
            //拒绝
            $res = $service->reject($id, $note, $this->user);
        }

        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 待回复征询列表
     *
     * @Token
     */
    public function publicListAction()
    {
        $params = $this->request->get();
        $list   = ReturnService::getInstance()->getList($params, $this->user['id'], ReturnService::LIST_TYPE_FYR);
        return $this->returnJson(ErrCode::$SUCCESS, '', $list['data']);
    }

    /**
     * 获取坏账/归还详情(公共的)
     *
     * @Token
     */
    public function publicDetailAction()
    {
        $id = $this->request->get('id');
        Validation::validate(['id' => $id], ReturnService::$validate_detail);

        $res = ReturnService::getInstance()->getDetail($id, $this->user['id']);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 添加坏账归还单
     *
     * @return Response|ResponseInterface
     * @throws ValidationException
     * @Permission(action='reserve.return.bad')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/49171
     */
    public function badAction()
    {
        $data = $this->request->get();

        $data['type'] = Enums\ReserveFundReturnEnums::RETURN_BAD_TYPE;
        Validation::validate($data, ReturnService::$validate_param);

        /**
         * 在备用金归还页面归还操作人是当前申请人归还自己的
         * 在数据查询页面操作归还是设置用于归还权限的工号可以帮其他申请人归还包括自己
         */
        $res = ReturnService::getInstance()->saveOne($data, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 备用金数据查询-归还添加
     * @Permission(action='reserve.data.back')
     * @return Response|ResponseInterface
     * @throws ValidationException
     * @api https://yapi.flashexpress.pub/project/133/interface/api/55573
     */
    public function queryAddAction()
    {
        $data = $this->request->get();

        $data['type']        = Enums\ReserveFundReturnEnums::RETURN_TYPE;
        $data['return_type'] = Enums\ReserveFundReturnEnums::APPLY_TYPE_QUERY;
        Validation::validate($data, ReturnService::$validate_param);

        /**
         * 在备用金归还页面归还操作人是当前申请人归还自己的
         * 在数据查询页面操作归还是设置用于归还权限的工号可以帮其他申请人归还包括自己
         */
        $res = ReturnService::getInstance()->saveOne($data, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 数据查询-添加坏账
     * @return Response|ResponseInterface
     * @throws ValidationException
     * @Permission(action='reserve.data.bad')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/55579
     */
    public function queryBadAction()
    {
        $data = $this->request->get();

        $data['type']        = Enums\ReserveFundReturnEnums::RETURN_BAD_TYPE;
        $data['return_type'] = Enums\ReserveFundReturnEnums::APPLY_TYPE_QUERY;
        Validation::validate($data, ReturnService::$validate_param);

        /**
         * 在备用金归还页面归还操作人是当前申请人归还自己的
         * 在数据查询页面操作归还是设置用于归还权限的工号可以帮其他申请人归还包括自己
         */
        $res = ReturnService::getInstance()->saveOne($data, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

}
