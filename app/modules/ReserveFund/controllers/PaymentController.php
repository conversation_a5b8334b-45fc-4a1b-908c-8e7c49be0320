<?php

namespace App\Modules\ReserveFund\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Services\EnumsService;
use App\Modules\ReserveFund\Services\ApplyService;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class PaymentController extends BaseController
{

    /**
     * 备用金支付
     * @Permission(action='reserve.pay.pay')
     *
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function payAction()
    {
        $data = $this->request->get();
        $id   = $data['id'] ?? 0;

        $validate_rule = ApplyService::$validate_pay_param;

        //获取付款配置
        $bank_info = EnumsService::getInstance()->getPaymentBankString();

        $validate_rule['pay_bank']    = 'IfIntEq:pass_or_not,1|Required|StrIn:' . $bank_info['bank_name'] . '|>>>:payment bank error';
        $validate_rule['pay_account'] = 'IfIntEq:pass_or_not,1|Required|StrIn:' . $bank_info['bank_account'] . '|>>>:payment bank account error';
        Validation::validate($data, $validate_rule);

        $res = ApplyService::getInstance()->pay($id, $data, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 备用金支付list
     * @Permission(action='reserve.pay.search')
     *
     * @return Response|ResponseInterface
     */
    public function listAction()
    {
        $params = $this->request->get();
        $res    = ApplyService::getInstance()->getList($params, $this->user, ApplyService::LIST_TYPE_PAYMENT);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 备用金支付详情
     * @Permission(action='reserve.pay.view')
     *
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function detailAction()
    {
        $data = $this->request->get();
        $id   = $this->request->get('id');

        Validation::validate($data, ApplyService::$validate_detail);

        $res = ApplyService::getInstance()->getDetail($id, $this->user['id']);
        if ($res['code'] == ErrCode::$SUCCESS) {
            [$res['data']['state'],$res['data']['state_text']] = ApplyService::getInstance()->formatStaffState($res['data']['create_id']);
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }


}

