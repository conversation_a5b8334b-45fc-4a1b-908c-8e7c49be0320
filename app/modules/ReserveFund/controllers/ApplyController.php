<?php

namespace App\Modules\ReserveFund\Controllers;

use App\Library\Enums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\ReserveFund\Services\ApplyService;
use App\Modules\ReserveFund\Services\PayFlowService;
use Exception;
use GuzzleHttp\Exception\GuzzleException;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class ApplyController extends BaseController
{
    /**
     * 备用金添加
     * @Permission(action='reserve.apply.apply')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/24106
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function addAction()
    {
        $data = $this->request->get();

        $validate_rule               = ApplyService::$validate_param;
        $validate_rule['payee_bank'] = 'Required|IntIn:' . implode(',', array_keys(Enums::$bank_type));
        Validation::validate($data, $validate_rule);

        $res = ApplyService::getInstance()->saveOne($data, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 备用金申请单获得默认值
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/24102
     * @Permission(action='reserve.apply.apply')
     *
     * @return Response|ResponseInterface
     */
    public function getDefaultAction()
    {
        $res = ApplyService::getInstance()->defaultData($this->user);
        if ($res['code'] == ErrCode::$SUCCESS) {
            [$res['data']['state'],$res['data']['state_text']] = ApplyService::getInstance()->formatStaffState($res['data']['create_id']);
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 网点备用金申请单列表
     * @Permission(action='reserve.apply.search')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/24110
     * @return Response|ResponseInterface
     */
    public function listAction()
    {
        $params = $this->request->get();
        $res    = ApplyService::getInstance()->getList($params, $this->user, ApplyService::LIST_TYPE_APPLY);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 网点备用金申请详情
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/24142
     * @Permission(action='reserve.apply.view')
     *
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function detailAction()
    {
        $data = $this->request->get();
        $id   = $this->request->get('id');

        Validation::validate($data, ApplyService::$validate_detail);

        $res = ApplyService::getInstance()->getDetail($id, $this->user['id']);
        if ($res['code'] == ErrCode::$SUCCESS) {
            [$res['data']['state'],$res['data']['state_text']] = ApplyService::getInstance()->formatStaffState($res['data']['create_id']);
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }


    /**
     * 备用金申请撤销
     * @Permission(action='reserve.apply.cancel')
     *
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function cancelAction()
    {
        $data = $this->request->get();
        $id   = $this->request->get('id', 'int');
        $note = $this->request->get('note', 'trim');

        Validation::validate($data, ApplyService::$validate_cancel);

        $res = (new PayFlowService(Enums::WF_RESERVE_FUND_APPLY))->cancel($id, $note, $this->user);
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 备用金申请单-审核列表
     * @Permission(action='reserve.audit.apply.search')
     * @return Response|ResponseInterface
     */
    public function auditListAction()
    {
        $params = $this->request->get();
        $res    = ApplyService::getInstance()->getList($params, $this->user, ApplyService::LIST_TYPE_AUDIT);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }


    /**
     * 备用金申请单-审核详情
     * @Permission(action='reserve.audit.apply.view')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function auditDetailAction()
    {
        $data = $this->request->get();
        $id   = $this->request->get('id');

        Validation::validate($data, ApplyService::$validate_detail);

        $res = ApplyService::getInstance()->getDetail($id, $this->user['id']);
        if ($res['code'] == ErrCode::$SUCCESS) {
            [$res['data']['state'],$res['data']['state_text']] = ApplyService::getInstance()->formatStaffState($res['data']['create_id']);
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }


    /**
     * 备用金申请单-审核
     * @Permission(action='reserve.audit.apply.audit')
     */
    public function auditAction()
    {
        $data = $this->request->get();
        $id   = $this->request->get('id', 'int');
        $flag = $this->request->get("pass_or_not", 'int');
        $note = $this->request->get('note', 'trim');

        Validation::validate($data, ApplyService::$validate_audit);

        $service = new PayFlowService(Enums::WF_RESERVE_FUND_APPLY);

        if ($flag == 1) {
            //通过
            $res = $service->approve($id, $note, $this->user);
        } else {
            //拒绝
            $res = $service->reject($id, $note, $this->user);
        }

        return $this->returnJson($res['code'], $res['message']);
    }


    /**
     * 数据查询 -导出
     * @Permission(action='reserve.data.export')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/24326
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function exportAction()
    {
        $params = trim_array($this->request->get());

        // 增加参数校验
        $validate_params = [];
        if (!empty($params['pay_date_start'])) {
            $validate_params['pay_date_start'] = 'Date|>>>:params error[pay_date_start YYYY-MM-DD]';
        }

        if (!empty($params['pay_date_end'])) {
            $validate_params['pay_date_end'] = 'Date|>>>:params error[pay_date_end YYYY-MM-DD]';
        }

        if (!empty($params['approved_start_date'])) {
            $validate_params['approved_start_date'] = 'Date|>>>:params error[approved_start_date YYYY-MM-DD]';
        }

        if (!empty($params['approved_end_date'])) {
            $validate_params['approved_end_date'] = 'Date|>>>:params error[approved_end_date YYYY-MM-DD]';
        }

        Validation::validate($params, $validate_params);

        $lock_key = md5('reserve_fund_apply_data_export_' . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return ApplyService::getInstance()->export($params, $this->user, ApplyService::LIST_TYPE_EXPORT);
        }, $lock_key, 30);

        if (isset($res['code'])) {
            $code    = $res['code'];
            $message = $res['message'];
            $data    = $res['data'];
        } else {
            $code    = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
            $data    = [];
        }

        return $this->returnJson($code, $message, $data);
    }

    /**
     * 数据查询-数据list
     * @Permission(action='reserve.data.search')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/24322
     * @throws ValidationException
     */
    public function dataSearchAction()
    {
        $params = trim_array($this->request->get());

        // 增加参数校验
        $validate_params = [];
        if (!empty($params['pay_date_start'])) {
            $validate_params['pay_date_start'] = 'Date|>>>:params error[pay_date_start YYYY-MM-DD]';
        }

        if (!empty($params['pay_date_end'])) {
            $validate_params['pay_date_end'] = 'Date|>>>:params error[pay_date_end YYYY-MM-DD]';
        }

        if (!empty($params['approved_start_date'])) {
            $validate_params['approved_start_date'] = 'Date|>>>:params error[approved_start_date YYYY-MM-DD]';
        }

        if (!empty($params['approved_end_date'])) {
            $validate_params['approved_end_date'] = 'Date|>>>:params error[approved_end_date YYYY-MM-DD]';
        }

        Validation::validate($params, $validate_params);

        $res = ApplyService::getInstance()->getList($params, $this->user, ApplyService::LIST_TYPE_QUERY);

        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 数据查询-数据详情
     * @Permission(action='reserve.data.view')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function dataDetailAction()
    {
        $data = $this->request->get();
        $id   = $this->request->get('id');

        Validation::validate($data, ApplyService::$validate_detail);

        $res = ApplyService::getInstance()->getDetail($id, $this->user['id']);
        if ($res['code'] == ErrCode::$SUCCESS) {
            [$res['data']['state'],$res['data']['state_text']] = ApplyService::getInstance()->formatStaffState($res['data']['create_id']);
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 我的申请map-
     * @Permission(action='reserve.apply.search')
     *
     * @return Response|ResponseInterface
     */
    public function getMyApplyMapAction()
    {
        // 申请人工号
        $apply_id = $this->request->get('apply_id');
        $user     = empty($apply_id) ? $this->user : ['id' => $apply_id];

        $res = ApplyService::getInstance()->getMyApplyMap($user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 关联报销-数据下载
     * @Permission(action='reserve.apply.search')
     * @return Response|ResponseInterface
     * @throws ValidationException
     * @throws GuzzleException
     */
    public function exportReimburseAction()
    {
        $data  = $this->request->get();
        $rfano = $this->request->get('rfano', 'trim');
        Validation::validate($data, ApplyService::$validate_export_reimburse);

        $res = ApplyService::getInstance()->exportReimburse($rfano);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }


    /**
     * 待回复征询列表
     *
     * @Token
     */
    public function publicListAction()
    {
        $params = $this->request->get();
        $list   = ApplyService::getInstance()->getList($params, $this->user, ApplyService::LIST_TYPE_FYR);
        return $this->returnJson(ErrCode::$SUCCESS, '', $list['data']);
    }

    /**
     * 征询详情
     *
     * @Token
     */
    public function publicDetailAction()
    {
        $id  = $this->request->get('id');
        $res = ApplyService::getInstance()->getDetail($id, $this->user['id']);
        if ($res['code'] == ErrCode::$SUCCESS) {
            [$res['data']['state'],$res['data']['state_text']] = ApplyService::getInstance()->formatStaffState($res['data']['create_id']);
        }

        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 备用金 - 数据查询 - 下载
     * @Permission(action='reserve.data.download')
     *
     * @return mixed
     * @throws Exception
     */
    public function downloadAction()
    {
        $data = $this->request->get();
        $id   = $this->request->get('id', 'int');

        Validation::validate($data, ApplyService::$validate_detail);

        // 加锁处理
        $lock_key = md5('reserve_fund_apply_download_' . $id . '_' . $this->user['id']);
        $res      = $this->atomicLock(function () use ($id) {
            return ApplyService::getInstance()->download($id, $this->user['id']);
        }, $lock_key, 30);

        if (isset($res['code'])) {
            $code    = $res['code'];
            $message = $res['message'];
            $data    = $res['data'];
        } else {
            $code    = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
            $data    = [];
        }

        return $this->returnJson($code, $message, $data);
    }

    /**
     * pdf文件流输出
     *
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function outputAction()
    {
        $data = $this->request->get();
        $url  = $this->request->get('url', 'trim');
        $name = $this->request->get('name', 'trim');

        Validation::validate($data, ApplyService::$validate_url_param);

        $res = ApplyService::getInstance()->outputPdfFile($url, $name);
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 获取某个备用金申请单名下的归还单坏账单列表
     * @Permission(action='reserve.data.search')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/49585
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function getReturnListAction()
    {
        $data = $this->request->get();
        $id   = $this->request->get('id');

        Validation::validate($data, ApplyService::$validate_detail);

        $res = ApplyService::getInstance()->getReturnList($id);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 备用金 - 申请 - 下载
     * @Permission(action='reserve.apply.download')
     *
     * @return mixed
     * @throws Exception
     */
    public function applyDownloadAction()
    {
        $data = $this->request->get();
        $id   = $this->request->get('id', 'int');

        Validation::validate($data, ApplyService::$validate_detail);

        // 加锁处理
        $lock_key = md5('reserve_fund_apply_apply_download_' . $id . '_' . $this->user['id']);
        $res      = $this->atomicLock(function () use ($id) {
            return ApplyService::getInstance()->download($id, $this->user['id']);
        }, $lock_key, 30);

        if (isset($res['code'])) {
            $code    = $res['code'];
            $message = $res['message'];
            $data    = $res['data'];
        } else {
            $code    = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
            $data    = [];
        }

        return $this->returnJson($code, $message, $data);
    }
}
