<?php
/**
 * Created by PhpStorm.
 * Date: 2022/1/15
 * Time: 18:07
 */

namespace App\Modules\SapManage\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Budget\Services\LedgerAccountService;
use App\Modules\OrdinaryPayment\Services\BaseService;
use App\Modules\SapManage\Services\OrdinaryPaymentSapService;

class OrdinarypaymentsapController extends BaseController
{
    /**
     * @Permission(action='sapmanage.ordinarypayment.list')
     * 普通付款列表
     */
    public function listAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, []);

        Validation::validate($params, OrdinaryPaymentSapService::$validate_list_search);

        $list = OrdinaryPaymentSapService::getInstance()->getList($params);

        return $this->returnJson($list['code'], $list['message'], $list['data']);
    }

    /**
     * @Permission(action='sapmanage.ordinarypayment.detail')
     * 查看
     *
     */
    public function detailAction()
    {
        $param = $this->request->get();
        $id    = $this->request->get('id');

        Validation::validate($param, BaseService::$validate_detail_param);

        $res = OrdinaryPaymentSapService::getInstance()->getOrdinaryPaymentDetail($id, $this->user['id']);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * @Permission(action='sapmanage.ordinarypayment.update')
     * 编辑
     */
    public function updatePaymentAction()
    {
        $data = $this->request->get();

        Validation::validate($data, OrdinaryPaymentSapService::$validate_update);

        $res = OrdinaryPaymentSapService::getInstance()->updatePayment($data, $this->user['id']);

        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * @Permission(action='sapmanage.ordinarypayment.send')
     * 重新发送
     */
    public function retrySendAction()
    {
        $ids = $this->request->get();

        Validation::validate($ids, [
            'ids' => 'Required|StrLenGeLe:1,5000|>>>:ids error',
        ]);

        $res = OrdinaryPaymentSapService::getInstance()->sendSap($ids);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * @Token
     * 枚举
     * */
    public function getEnumsAction()
    {
        $res = OrdinaryPaymentSapService::getInstance()->getStatus();
        return $this->returnJson(ErrCode::$SUCCESS, '', $res);
    }

    /**
     * @Token
     * 模糊搜索核算科目
     *
     */
    public function getLedgerListByNameAction()
    {
        $validate = [
            'ledger_account_name' => 'Required|StrLenGeLe:1,100',
        ];

        $param = $this->request->get();
        $param = array_only($param, array_keys($validate));

        Validation::validate($param, $validate);

        $res = LedgerAccountService::getInstance()->getLedgerListByName($param['ledger_account_name']);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }


    /**
     * @Token
     * 导出
     */
    public function exportListAction()
    {
        $params   = $this->request->get();
        $lock_key = md5('sap_manage_ordinary_payment_sap_export_' . $this->user['id']);

        $res = $this->atomicLock(function () use ($params) {
            return OrdinaryPaymentSapService::getInstance()->exportList($params);
        }, $lock_key, 30);

        if (isset($res['code'])) {
            $code    = $res['code'];
            $message = $res['message'];
            $data    = $res['data'];
        } else {
            $code    = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
            $data    = [];
        }

        return $this->returnJson($code, $message, $data);
    }

}
