<?php
/**
 * Created by PhpStorm.
 * Date: 2022/09/05
 * Time: 18:07
 */

namespace App\Modules\SapManage\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\OrdinaryPayment\Services\BaseService;
use App\Modules\SapManage\Services\LoanSapService;

class LoansapController extends BaseController
{
    /**
     * @Permission(action='sapmanage.loan.list')
     * 借款列表
     */
    public function listAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, []);

        Validation::validate($params, LoanSapService::$validate_list_search);

        $list = LoanSapService::getInstance()->getList($params);
        return $this->returnJson($list['code'], $list['message'], $list['data']);
    }

    /**
     *
     *
     * @Permission(action='sapmanage.loan.detail')
     * 查看
     */
    public function detailAction()
    {
        $param = $this->request->get();
        $id    = $this->request->get('id');
        Validation::validate($param, BaseService::$validate_detail_param);

        $res = LoanSapService::getInstance()->getDetail($id);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * @Permission(action='sapmanage.loan.update')
     * 编辑
     */
    public function updateAction()
    {
        $data = $this->request->get();

        Validation::validate($data, LoanSapService::$validate_update);

        $res = LoanSapService::getInstance()->update($data, $this->user['id']);

        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * @Permission(action='sapmanage.loan.send')
     * 重新发送
     */
    public function retrySendAction()
    {
        $ids = $this->request->get();

        Validation::validate($ids, [
            'ids' => 'Required|StrLenGeLe:1,5000|>>>:ids error',
        ]);

        $res = LoanSapService::getInstance()->sendSap($ids);
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * @Token
     * 导出
     */
    public function exportListAction()
    {
        $params   = $this->request->get();
        $lock_key = md5('sap_manage_loan_sap_export_' . $this->user['id']);

        $res = $this->atomicLock(function () use ($params) {
            return LoanSapService::getInstance()->exportList($params);
        }, $lock_key, 30);

        if (isset($res['code'])) {
            $code    = $res['code'];
            $message = $res['message'];
            $data    = $res['data'];
        } else {
            $code    = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
            $data    = [];
        }

        return $this->returnJson($code, $message, $data);
    }

}
