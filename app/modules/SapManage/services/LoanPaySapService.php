<?php
/**
 * Created by PhpStorm.
 * Date: 2022/9/05
 * Time: 10:57
 */

namespace App\Modules\SapManage\Services;

use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Modules\Budget\Models\LedgerAccount;

use App\Modules\Loan\Models\LoanPay;
use App\Modules\Loan\Models\Loan;

use mysql_xdevapi\Exception;

class LoanPaySapService extends BaseService
{
    const  LEDGER_ACCOUNT_ID = '********';
    public static $validate_list_search = [
        'apply_no'            => 'StrLenGeLe:12,20|>>>:apply no error',
        'apply_start_date'    => 'date|>>>:apply start date error',
        'apply_end_date'      => 'date|>>>:apply end date error',
        'approved_start_date' => 'date|>>>:approved start date error',
        'approved_end_date'   => 'date|>>>:approved end date error',
        'pay_start_date'      => 'date|>>>:pay start date error',
        'pay_end_date'        => 'date|>>>:pay end date error',
        'status'              => 'IntIn:1,2,3|>>>:sync status error',
        'cost_company_id'     => 'IntGt:0|>>>:cost company id error',

    ];

    public static  $validate_update = [
        'id'                   => 'Required|IntGt:0',
        'cost_center_name'     => 'StrLenGeLe:0,35',//pc code
        'pay_transaction_date' => 'date|>>>: transaction date error',

    ];
    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return LoanPaySapService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 列表
     * */
    public function getList($condition, $export = false)
    {
        $page_size = empty($condition['page_size']) ? 20 : $condition['page_size'];
        $page_num  = empty($condition['page']) ? 1 : $condition['page'];
        $offset    = $page_size * ($page_num - 1);


        $code         = ErrCode::$SUCCESS;
        $message      = 'success';
        $real_message = '';

        $data = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];

        try {
            if (isset($condition['apply_start_date']) && isset($condition['apply_end_date']) && $condition['apply_start_date'] > $condition['apply_end_date']) {
                throw new ValidationException('start date or end date error', ErrCode::$VALIDATE_ERROR);
            }

            if (isset($condition['approved_start_date']) && isset($condition['approved_end_date']) && $condition['approved_start_date'] > $condition['approved_end_date']) {
                throw new ValidationException(' approved start date or end date error', ErrCode::$VALIDATE_ERROR);
            }
            if (isset($condition['pay_start_date']) && isset($condition['pay_end_date']) && $condition['pay_start_date'] > $condition['pay_end_date']) {
                throw new ValidationException('pay start date or end date error', ErrCode::$VALIDATE_ERROR);
            }

            $builder = $this->modelsManager->createBuilder();
            $builder->from(['main' => Loan::class]);
            $builder->leftjoin(LoanPay::class, 'main.id = d.loan_id', 'd');

            $builder = $this->getCondition($builder, $condition);
            // 取总条数
            $count_info = $builder->columns('COUNT(d.id) as t_count')->getQuery()->getSingleResult();
            $count      = $count_info->t_count ?? 0;
            $items      = [];

            if ($count) {
                $builder->columns([
                    'main.id',
                    'main.lno',
                    'main.create_company_id',
                    'main.create_company_name',
                    'main.create_date',
                    'main.back_approved_at as approved_at',
                    'main.back_date',
                    'main.back_transaction_date',
                    'main.back_sync_sap',
                    'main.back_sap_note',
                    'd.pay_date',
                ]);
                $builder->orderBy('main.id asc');
                if ($export) {
                    $builder->limit(0, self::DOWN_LIMIT_NUM);
                } else {
                    $builder->limit($page_size, $offset);
                }
                $items                             = $builder->getQuery()->execute()->toArray();
                $items                             = $this->handleItems($items);
                $data['items']                     = $items;
                $data['pagination']['total_count'] = (int)$count;
            }


            if ($export) {
                return $items;
            }
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }

        if (!empty($real_message)) {
            $this->logger->warning('sap借款归还传输列表:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 列表数据格式处理
     *
     * @param array $items
     * @return array
     */
    private function handleItems(array $items)
    {
        if (empty($items)) {
            return [];
        }

        $sap_status = (self::getStatus())['status'];
        $sap_status = array_column($sap_status, 'value', 'key');
        foreach ($items as &$item) {
            $item['back_sync_sap_text'] = $sap_status[$item['back_sync_sap']];
            $item['back_date']          = empty($item['back_transaction_date']) ? $item['pay_date'] : $item['back_transaction_date'];
        }

        return $items;
    }

    /**
     * 查询条件
     * */
    private function getCondition($builder, $condition)
    {
        //申请编号
        if (isset($condition['apply_no']) && !empty($condition['apply_no'])) {
            $builder->andWhere('main.lno = :apply_no:', ['apply_no' => $condition['apply_no']]);
        }

        //申请时间-起始日期
        if (isset($condition['apply_start_date']) && !empty($condition['apply_start_date'])) {
            $condition['apply_start_date'] .= ' 00:00:00';

            $builder->andWhere('main.created_at >= :apply_start_date:',
                ['apply_start_date' => $condition['apply_start_date']]);
        }

        //申请时间-截止日期
        if (isset($condition['apply_end_date']) && !empty($condition['apply_end_date'])) {
            $condition['apply_end_date'] .= ' 23:59:59';

            $builder->andWhere('main.created_at <= :apply_end_date:',
                ['apply_end_date' => $condition['apply_end_date']]);
        }

        //审批通过时间-起始日期
        if (isset($condition['approved_start_date']) && !empty($condition['approved_start_date'])) {
            $condition['approved_start_date'] .= ' 00:00:00';

            $builder->andWhere('main.back_approved_at >= :approved_start_date:',
                ['approved_start_date' => $condition['approved_start_date']]);
        }

        //审批时间-截止日期
        if (isset($condition['approved_end_date']) && !empty($condition['approved_end_date'])) {
            $condition['approved_end_date'] .= ' 23:59:59';

            $builder->andWhere('main.back_approved_at <= :approved_end_date:',
                ['approved_end_date' => $condition['approved_end_date']]);
        }

        //过账时间-起始日期
        if (isset($condition['pay_start_date']) && !empty($condition['pay_start_date'])) {
            $builder->andWhere('main.back_date >= :pay_start_date:',
                ['pay_start_date' => $condition['pay_start_date']]);
        }

        //过账时间-截止日期
        if (isset($condition['pay_end_date']) && !empty($condition['pay_end_date'])) {
            $builder->andWhere('main.back_date <= :pay_end_date:', ['pay_end_date' => $condition['pay_end_date']]);
        }

        //费用所属公司
        if (isset($condition['cost_company_id']) && !empty($condition['cost_company_id'])) {
            $builder->andWhere('main.create_company_id = :cost_company_id:',
                ['cost_company_id' => $condition['cost_company_id']]);
        }

        //发送状态
        if (isset($condition['status']) && !empty($condition['status'])) {
            $builder->andWhere('main.back_sync_sap = :status:', ['status' => $condition['status']]);
        } else {
            $builder->inWhere('main.back_sync_sap', [self::SYNC_SAP_SUCCESS, self::SYNC_SAP_FAIL, self::SYNC_SAP_DEAL]);
        }

        return $builder;
    }

    /**
     * 修改
     * */
    public function update($data, $uid)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $record_log = [];
            // 该申请编号是否存在
            $loan = Loan::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $data['id']],
            ]);

            if (empty($loan)) {
                throw new ValidationException('The number not exists' . $data['id'], ErrCode::$VALIDATE_ERROR);
            }

            $update_data = [];
            //更新详情表
            if (isset($data['cost_center_name'])) {
                $update_data['cost_center_name'] = $data['cost_center_name'];
            }
            if (isset($data['cost_center_id'])) {
                $update_data['cost_center_id'] = $data['cost_center_id'];
            }
            if (isset($data['transaction_date'])) {
                $update_data['back_transaction_date'] = $data['transaction_date'];
            }

            if (!empty($update_data)) {
                //记录log
                $main_bool = $loan->save($update_data);
                if ($main_bool === false) {
                    throw new BusinessException('sap借款归还-更新信息失败 = ' . json_encode([
                            'update_data' => $update_data,
                            'message'     => get_data_object_error_msg($loan),
                        ], JSON_UNESCAPED_UNICODE), ErrCode::$VALIDATE_ERROR);
                }
            }

            $db->commit();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->warning('sap—loan-pay-update-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
        ];
    }

    /**
     * 查看
     * */
    public function getDetail($id)
    {
        $message = $real_message = '';
        try {
            $detail = Loan::getFirst([
                'id = :id:',
                'bind'    => ['id' => $id],
                'columns' => [
                    'id',
                    'lno',
                    'create_company_name',
                    'create_id',
                    'create_name',
                    'create_node_department_name',
                    'create_department_name',
                    'back_approved_at as approved_at',
                    'currency',
                    'amount',
                    'cost_center_name',
                    'back_transaction_date',
                    'back_date',
                ],

            ]);
            if (empty($detail)) {
                return [];
            }
            //查询主表信息
            $data = $detail->toArray();
            $data = $this->handleDetailData($data);
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning('loan-pay-sap-failed:' . $real_message);
        }
        return [
            'code'    => $code ?? ErrCode::$SUCCESS,
            'message' => $message ?? 'success',
            'data'    => $data ?? [],
        ];
    }

    /**
     * @param $data
     * @param $is_filter
     * @return array
     */
    private function handleDetailData($data)
    {
        $data['ledger_account_id'] = SELF::LEDGER_ACCOUNT_ID;
        //核算科目名字
        $ledger_name = [];
        $ledger_name = LedgerAccount::findFirst([
            'conditions' => 'account = ?1',
            'bind'       => [1 => $data['ledger_account_id']],
        ]);
        if (!empty($ledger_name)) {
            $ledger_name = $ledger_name->toArray();
        }
        $payment_currency               = GlobalEnums::$currency_item[$data['currency']] ?? '';
        $data['currency_text']          = static::$t->_($payment_currency);
        $data['ledger_account_name']    = $ledger_name['name_en'] ?? '';
        $data['back_transaction_date']  = empty($data['back_transaction_date']) ? $data['back_date'] : $data['back_transaction_date'];
        $data['create_department_name'] = !empty($data['create_node_department_name']) ? $data['create_node_department_name'] : $data['create_department_name'];

        $data['amount'] = bcdiv($data['amount'], 1000, 2);

        return $data;
    }

    /**
     * 重发
     * */
    public function sendSap($ids)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db      = $this->getDI()->get('db_oa');
        try {
            $ids    = $ids['ids'];
            $id_arr = explode(',', $ids);
            foreach ($id_arr as $k => $v) {
                if (!is_numeric($v)) {
                    throw new BusinessException(static::$t->_('param_is_err'), ErrCode::$SYSTEM_ERROR);
                }
            }
            $db->updateAsDict(
                (new Loan())->getSource(),
                [
                    'back_sync_sap' => self::SYNC_SAP_DEAL,
                    'updated_at'    => date('Y-m-d H:i:s'),
                ],
                [
                    'conditions' => " id IN ($ids) and back_sync_sap =" . self::SYNC_SAP_FAIL,
                ]
            );
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning('sap—loan-pay-send-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
        ];
    }

    /**
     * 导出
     * */
    public function exportList($condition)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            $data      = [];
            $file_name = 'loan_sap_' . date("YmdHis");
            $header    = [
                static::$t->_('field_sap_export_no'),
                static::$t->_('field_sap_sync_text'),
                static::$t->_('field_sap_note'),
            ];
            $item      = $this->getList($condition, true);
            foreach ($item as $key => $value) {
                $data[] = [
                    $value['lno'],
                    $value['back_sync_sap_text'],
                    $value['back_sap_note'],
                ];
            }

            $res = $this->exportExcel($header, $data, $file_name);
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning('sap—loan-pay-export-failed:' . $real_message);
        }

        return [
            'code'    => $code ?? ErrCode::$SUCCESS,
            'message' => $message ?? 'success',
            'data'    => $res['data'] ?? '',
        ];
    }


}