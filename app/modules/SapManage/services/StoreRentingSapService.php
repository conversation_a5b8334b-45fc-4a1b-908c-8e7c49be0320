<?php
/**
 * Created by PhpStorm.
 * Date: 2022/5/13
 * Time: 10:57
 */

namespace App\Modules\SapManage\Services;

use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Modules\Budget\Services\LedgerAccountService;
use App\Modules\Common\Models\EnvModel;

use App\Modules\Common\Services\EnumsService;
use App\Modules\Organization\Models\SysDepartmentModel;
use App\Modules\Payment\Models\PaymentStoreRenting;
use App\Modules\Payment\Models\PaymentStoreRentingDetail;
use App\Modules\Purchase\Services\BaseService as PurchaseService;
use App\Modules\ReserveFund\Models\ReserveFundApply;

use App\Modules\User\Models\DepartmentModel;
use mysql_xdevapi\Exception;

class StoreRentingSapService extends BaseService
{
    public static $validate_list_search = [
        'apply_no'            => 'StrLenGeLe:12,20|>>>:apply no error',
        'apply_start_date'    => 'date|>>>:apply start date error',
        'apply_end_date'      => 'date|>>>:apply end date error',
        'approved_start_date' => 'date|>>>:approved start date error',
        'approved_end_date'   => 'date|>>>:approved end date error',
        'pay_start_date'      => 'date|>>>:pay start date error',
        'pay_end_date'        => 'date|>>>:pay end date error',
        'status'              => 'IntIn:1,2,3|>>>:sync status error',
        'cost_company_id'     => 'IntGt:0|>>>:cost company id error',
        'create_store_id'     => 'Str|>>>:create_store  id error',
        'sap_supplier_no'     => 'StrLenGeLe:0,100',
    ];

    public static  $validate_update = [
        'id'               => 'Required|IntGt:0',
        'cost_center_code' => 'StrLenGeLe:0,35',//pc code
        'certificate_desc' => 'StrLenGeLe:0,40',//凭证摘要
        'transaction_date' => 'date|>>>: transaction date error',
        'sap_supplier_no'  => 'StrLenGeLe:0,100',
    ];
    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return StoreRentingSapService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 列表
     * */
    public function getList($condition, $export = false)
    {
        $page_size = empty($condition['page_size']) ? 20 : $condition['page_size'];
        $page_num  = empty($condition['page']) ? 1 : $condition['page'];
        $offset    = $page_size * ($page_num - 1);


        $code         = ErrCode::$SUCCESS;
        $message      = 'success';
        $real_message = '';

        $data = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];

        try {
            if (isset($condition['apply_start_date']) && isset($condition['apply_end_date']) && $condition['apply_start_date'] > $condition['apply_end_date']) {
                throw new ValidationException('start date or end date error', ErrCode::$VALIDATE_ERROR);
            }

            if (isset($condition['approved_start_date']) && isset($condition['approved_end_date']) && $condition['approved_start_date'] > $condition['approved_end_date']) {
                throw new ValidationException(' approved start date or end date error', ErrCode::$VALIDATE_ERROR);
            }
            if (isset($condition['pay_start_date']) && isset($condition['pay_end_date']) && $condition['pay_start_date'] > $condition['pay_end_date']) {
                throw new ValidationException('pay start date or end date error', ErrCode::$VALIDATE_ERROR);
            }

            $builder = $this->modelsManager->createBuilder();
            $builder->from(['d' => PaymentStoreRentingDetail::class]);
            $builder->leftjoin(PaymentStoreRenting::class, 'main.id = d.store_renting_id', 'main');

            $builder = $this->getCondition($builder, $condition);
            // 取总条数
            $count_info = $builder->columns('COUNT(d.id) as t_count')->getQuery()->getSingleResult();
            $count      = $count_info->t_count ?? 0;
            $items      = [];

            if ($count) {
                $builder->columns([
                    'main.apply_no',
                    'main.cost_company_id',
                    'main.cost_center_department_name',
                    'main.created_at',
                    'main.approved_at',
                    'd.index_no',
                    'd.contract_no',
                    'd.transaction_date',
                    'd.sync_sap',
                    'd.sap_note',
                    'd.id',
                    'd.sap_supplier_no',

                ]);
                $builder->orderBy('d.id asc');
                if ($export) {
                    $builder->limit(0, self::DOWN_LIMIT_NUM);
                } else {
                    $builder->limit($page_size, $offset);
                }
                $items                             = $builder->getQuery()->execute()->toArray();
                $items                             = $this->handleItems($items);
                $data['items']                     = $items;
                $data['pagination']['total_count'] = (int)$count;
            }


            if ($export) {
                return $items;
            }
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('sap租房付款传输列表:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 列表数据格式处理
     *
     * @param array $items
     * @return array
     */
    private function handleItems(array $items)
    {
        if (empty($items)) {
            return [];
        }

        $sap_status       = (self::getStatus())['status'];
        $sap_status       = array_column($sap_status, 'value', 'key');
        $coo_company_list = (new PurchaseService())->getCooCostCompany();
        $coo_company_list = array_column($coo_company_list, 'cost_company_name', 'cost_company_id');
        foreach ($items as &$item) {
            $item['sync_sap_text']       = $sap_status[$item['sync_sap']];
            $item['create_company_name'] = $coo_company_list[$item['cost_company_id']];
        }

        return $items;
    }

    /**
     * 查询条件
     * */
    private function getCondition($builder, $condition)
    {
        //申请编号
        if (isset($condition['apply_no']) && !empty($condition['apply_no'])) {
            $builder->andWhere('main.apply_no = :apply_no:', ['apply_no' => $condition['apply_no']]);
        }

        //申请时间-起始日期
        if (isset($condition['apply_start_date']) && !empty($condition['apply_start_date'])) {
            $condition['apply_start_date'] .= ' 00:00:00';

            $builder->andWhere('main.created_at >= :apply_start_date:',
                ['apply_start_date' => $condition['apply_start_date']]);
        }

        //申请时间-截止日期
        if (isset($condition['apply_end_date']) && !empty($condition['apply_end_date'])) {
            $condition['apply_end_date'] .= ' 23:59:59';

            $builder->andWhere('main.created_at <= :apply_end_date:',
                ['apply_end_date' => $condition['apply_end_date']]);
        }

        //审批通过时间-起始日期
        if (isset($condition['approved_start_date']) && !empty($condition['approved_start_date'])) {
            $condition['approved_start_date'] .= ' 00:00:00';

            $builder->andWhere('main.approved_at >= :approved_start_date:',
                ['approved_start_date' => $condition['approved_start_date']]);
        }

        //审批时间-截止日期
        if (isset($condition['approved_end_date']) && !empty($condition['approved_end_date'])) {
            $condition['approved_end_date'] .= ' 23:59:59';

            $builder->andWhere('main.approved_at <= :approved_end_date:',
                ['approved_end_date' => $condition['approved_end_date']]);
        }

        //过账时间-起始日期
        if (isset($condition['pay_start_date']) && !empty($condition['pay_start_date'])) {
            $builder->andWhere('d.transaction_date >= :pay_start_date:',
                ['pay_start_date' => $condition['pay_start_date']]);
        }

        //过账时间-截止日期
        if (isset($condition['pay_end_date']) && !empty($condition['pay_end_date'])) {
            $builder->andWhere('d.transaction_date <= :pay_end_date:', ['pay_end_date' => $condition['pay_end_date']]);
        }
        //费用所属公司
        if (isset($condition['cost_company_id']) && !empty($condition['cost_company_id'])) {
            $builder->andWhere('main.cost_company_id = :cost_company_id:',
                ['cost_company_id' => $condition['cost_company_id']]);
        }


        //合同
        if (isset($condition['contract_no']) && !empty($condition['contract_no'])) {
            $builder->andWhere('d.contract_no = :contract_no:', ['contract_no' => $condition['contract_no']]);
        }
        //sap供应商
        if (isset($condition['sap_supplier_no']) && !empty($condition['sap_supplier_no'])) {
            $builder->andWhere('d.sap_supplier_no = :sap_supplier_no:',
                ['sap_supplier_no' => $condition['sap_supplier_no']]);
        }


        //发送状态
        if (isset($condition['status']) && !empty($condition['status'])) {
            $builder->andWhere('d.sync_sap = :status:', ['status' => $condition['status']]);
        } else {
            $builder->andWhere('d.sync_sap in({status:array})', ['status' => [1, 2, 3]]);
        }

        return $builder;
    }

    /**
     * 修改
     * */
    public function update($data, $uid)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $record_log = [];
            // 该申请编号是否存在
            $payment_detail = PaymentStoreRentingDetail::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $data['id']],
            ]);

            if (empty($payment_detail)) {
                throw new ValidationException('The number not exists' . $data['id'], ErrCode::$VALIDATE_ERROR);
            }

            $update_data = [];
            //更新详情表
            if (isset($data['certificate_desc'])) {
                $update_data['certificate_desc'] = $data['certificate_desc'];
            }

            if (isset($data['cost_center_code'])) {
                $update_data['cost_center_code'] = $data['cost_center_code'];
            }

            if (isset($data['transaction_date'])) {
                $update_data['transaction_date'] = $data['transaction_date'];
            }

            if (isset($data['sap_supplier_no'])) {
                $update_data['sap_supplier_no'] = $data['sap_supplier_no'];
            }
            if (!empty($update_data)) {
                //记录log

                $main_bool = $payment_detail->save($update_data);
                if ($main_bool === false) {
                    $msg_arr = $payment_detail->getMessages();

                    throw new BusinessException('sap租房付款-更新信息失败 = ' . json_encode([
                            'update_data' => $update_data,
                            'message'     => $msg_arr,
                        ], JSON_UNESCAPED_UNICODE), ErrCode::$VALIDATE_ERROR);
                }
            }

            $db->commit();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('sap—payment-sore-update-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
        ];
    }

    /**
     * 查看
     * */
    public function getDetail($id)
    {
        try {
            $detail = PaymentStoreRentingDetail::getFirst([
                'id = :id:',
                'bind'    => ['id' => $id],
                'columns' => [
                    'id',
                    'store_renting_id',
                    'cost_center_code',
                    'certificate_desc',
                    'sap_supplier_no',
                    'index_no',
                    'ledger_account_id',
                    'store_name',
                    'amount',
                    'contract_no',
                    'transaction_date',
                    'cost_type',
                    'vat_rate',
                    'vat_amount',
                    'amount_has_tax',
                    'wht_category',
                    'wht_tax_rate',
                    'wht_amount',
                    'actually_amount',
                ],

            ]);
            if (empty($detail)) {
                return [];
            }
            //查询主表信息
            $data = $detail->toArray();

            $main_model = PaymentStoreRenting::getFirst([
                'id = :id:',
                'bind'    => ['id' => $data['store_renting_id']],
                'columns' => [
                    'apply_no',
                    'cost_company_id',
                    'currency',
                    'cost_department_id',
                    'approved_at',
                    'created_at',
                ],

            ]);

            $data = array_merge($data, $main_model->toArray());
            $data = $this->handleDetailData($data);
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning('storeRentingSap-failed:' . $real_message);
        }
        return [
            'code'    => $code ?? ErrCode::$SUCCESS,
            'message' => $message ?? 'success',
            'data'    => $data ?? [],
        ];
    }

    /**
     * @param $data
     * @param $is_filter
     * @return array
     */
    private function handleDetailData(&$data)
    {
        if (empty($data) || !is_array($data)) {
            return [];
        }
        //核算科目名字
        $ledger_account_ids = array_values(array_filter(array_column($data['amount_detail_item'],
            'ledger_account_id')));
        $ledgerIdToName     = [];
        if (!empty($ledger_account_ids)) {
            $res = LedgerAccountService::getInstance()->getList($ledger_account_ids);
            if ($res['code'] == ErrCode::$SUCCESS) {
                $ledgerIdToName = array_column($res['data'], null, 'id');
            }
        }

        // 网点租房付款费用类型枚举
        $store_rent_payment_cost_enums = EnumsService::getInstance()->getStoreRentPaymentCostTypeItem();

        $data['cost_type_text'] = self::$t[$store_rent_payment_cost_enums[$data['cost_type']]];

        $data['wht_tax_rate'] .= '%';
        $data['vat_rate']     .= '%';

        // 费用所属部门
        if (!empty($data['cost_department_id'])) {
            $cost_department              = DepartmentModel::getFirst([
                'conditions' => 'id = :id:',
                'columns'    => 'name',
                'bind'       => ['id' => $data['cost_department_id']],
            ]);
            $data['cost_department_name'] = $cost_department->name ?? '';
        } else {
            $data['cost_department_name'] = $data['cost_center_department_name'] ?? '';
        }
        $data['cost_company_name'] = '';
        if (!empty($data['cost_company_id'])) {
            $data['cost_company_name'] = SysDepartmentModel::getCompanyNameByCompanyId($data['cost_company_id']);
        }

        $status                       = Enums::$loan_status[$data['status']] ?? '';
        $payment_currency             = GlobalEnums::$currency_item[$data['currency']] ?? '';
        $data['currency_text']        = static::$t->_($payment_currency);
        $value['ledger_account_name'] = $ledgerIdToName[$data['ledger_account_id']] ? $ledgerIdToName[$data['ledger_account_id']]['name'] : '';


        return $data;
    }

    /**
     * 重发
     * */
    public function sendSap($ids)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db      = $this->getDI()->get('db_oa');
        try {
            $ids = $ids['ids'];
            $db->updateAsDict(
                (new PaymentStoreRentingDetail())->getSource(),
                [
                    'sync_sap'   => 3,
                    'updated_at' => gmdate('Y-m-d H:i:s'),
                ],
                [
                    'conditions' => " id IN ($ids) and sync_sap =2",
                ]
            );
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('sap—payment-store-send-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
        ];
    }

    /**
     * 导出
     * */
    public function exportList($condition)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            $data      = [];
            $file_name = 'payment_store_renting_sap_' . date("YmdHis");
            $header    = [
                static::$t->_('field_sap_export_no'),
                static::$t->_('field_sap_sync_text'),
                static::$t->_('field_sap_note'),
            ];
            $item      = $this->getList($condition, true);
            foreach ($item as $key => $value) {
                $data[] = [
                    $value['apply_no'] . '_' . $value['index_no'],
                    $value['sync_sap_text'],
                    $value['sap_note'],
                ];
            }

            $res = $this->exportExcel($header, $data, $file_name);
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('sap—payment-store-export-failed:' . $real_message);
        }

        return [
            'code'    => $code ?? ErrCode::$SUCCESS,
            'message' => $message ?? 'success',
            'data'    => $res['data'] ?? '',
        ];
    }


}