<?php
/**
 * Created by PhpStorm.
 * Date: 2022/1/17
 * Time: 11:30
 */

namespace App\Modules\SapManage\Services;

use App\Library\Enums\GlobalEnums;
use App\Modules\Common\Services\EnumsService;

class BaseService extends \App\Library\BaseService
{
    const DOWN_LIMIT_NUM = 20000;

    const SYNC_SAP_SUCCESS = 1;//成功
    const SYNC_SAP_FAIL = 2;   //失败
    const SYNC_SAP_DEAL = 3;   //处理中
    // 普通付款申请验证规则
    public static $validate_update = [
        'id'                                          => 'Required|IntGt:0',
        'extra_message'                               => 'StrLenGeLe:0,35',                        //额外参考消息
        'voucher_abstract'                            => 'StrLenGeLe:0,40',                        //凭证摘要
        'sap_supplier_no'                             => 'StrLenGeLe:0,100',                       //sap供应商编号
        'amount_detail_item[*].id'                    => 'Required|IntGt:0',                       //id
        'amount_detail_item[*].voucher_description'   => 'StrLenGeLe:0,40',                        //凭证描述
        'amount_detail_item[*].deductible_vat_tax'    => 'Str',                                    //可抵扣VAT税率
        'amount_detail_item[*].deductible_tax_amount' => 'Str',                                    //可抵扣VAT金额
        'amount_detail_item[*].ledger_account_id'     => 'IntGe:0|>>>:ledger_account_id error',    //核算科目id
        'amount_detail_item[*].cost_center_name'      => 'StrLenGeLe:0,100|>>>:cost center error', //费用所属中心（pccode)

    ];

    public function getStatus()
    {
        return [
            'status' => [
                [
                    'key'   => self::SYNC_SAP_SUCCESS,
                    'value' => self::$t->_('send_sap_success'),
                ],
                [
                    'key'   => self::SYNC_SAP_FAIL,
                    'value' => self::$t->_('send_sap_fail'),
                ],
                [
                    'key'   => self::SYNC_SAP_DEAL,
                    'value' => self::$t->_('send_sap_retry'),
                ],
            ],
        ];
    }

}
