<?php
/**
 * Created by PhpStorm.
 * Date: 2022/1/17
 * Time: 11:29
 */

namespace App\Modules\SapManage\Services;


use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Modules\Budget\Services\BudgetService;
use App\Modules\Budget\Services\LedgerAccountService;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Material\Models\MaterialFinanceCategoryModel;
use App\Modules\OrdinaryPayment\Models\OrdinaryPayment;
use App\Modules\OrdinaryPayment\Models\OrdinaryPaymentDetail;
use App\Modules\OrdinaryPayment\Models\OrdinaryPaymentExtend;
use App\Modules\Purchase\Services\BaseService as PurchaseService;
use App\Modules\SapManage\Models\OrdinaryPaymentOpreateLogModel;
use mysql_xdevapi\Exception;

class OrdinaryPaymentSapService extends BaseService
{

    public static $validate_update = [
        'id'                                          => 'Required|IntGt:0',
        'extra_message'                               => 'StrLenGeLe:0,35',                        //额外参考消息
        'voucher_abstract'                            => 'StrLenGeLe:0,40',                        //凭证摘要
        'sap_supplier_no'                             => 'StrLenGeLe:0,100',                       //sap供应商编号
        'amount_detail_item[*].id'                    => 'Required|IntGt:0',                       //id
        'amount_detail_item[*].voucher_description'   => 'StrLenGeLe:0,40',                        //凭证描述
        'amount_detail_item[*].deductible_vat_tax'    => 'Str',                                    //可抵扣VAT税率
        'amount_detail_item[*].deductible_tax_amount' => 'Str',                                    //可抵扣VAT金额
        'amount_detail_item[*].cost_center_name'      => 'StrLenGeLe:0,100|>>>:cost center error', //费用所属中心（pccode)

    ];

    public static $validate_list_search = [
        'apply_no'            => 'StrLenGeLe:12,20|>>>:apply no error',
        'supplier_name'       => 'StrLenGeLe:1,128|>>>:supplier_name length error',
        'sap_supplier_no'     => 'StrLenGeLe:1,128|>>>:sap_supplier_no length error',
        'apply_start_date'    => 'date|>>>:apply start date error',
        'apply_end_date'      => 'date|>>>:apply end date error',
        'approved_start_date' => 'date|>>>:approved start date error',
        'approved_end_date'   => 'date|>>>:approved end date error',
        'pay_start_date'      => 'date|>>>:pay start date error',
        'pay_end_date'        => 'date|>>>:pay end date error',
        'status'              => 'IntIn:1,2,3|>>>:sync status error',
        'cost_company_id'     => 'IntGt:0|>>>:cost company id error',
    ];


    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return OrdinaryPaymentSapService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 列表
     * */
    public function getList($condition, $export = false)
    {
        $page_size = empty($condition['page_size']) ? 20 : $condition['page_size'];
        $page_num  = empty($condition['page']) ? 1 : $condition['page'];
        $offset    = $page_size * ($page_num - 1);


        $code         = ErrCode::$SUCCESS;
        $message      = 'success';
        $real_message = '';

        $data = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];

        try {
            if (isset($condition['apply_start_date']) && isset($condition['apply_end_date']) && $condition['apply_start_date'] > $condition['apply_end_date']) {
                throw new ValidationException('start date or end date error', ErrCode::$VALIDATE_ERROR);
            }

            if (isset($condition['approved_start_date']) && isset($condition['approved_end_date']) && $condition['approved_start_date'] > $condition['approved_end_date']) {
                throw new ValidationException(' approved start date or end date error', ErrCode::$VALIDATE_ERROR);
            }
            if (isset($condition['pay_start_date']) && isset($condition['pay_end_date']) && $condition['pay_start_date'] > $condition['pay_end_date']) {
                throw new ValidationException('pay start date or end date error', ErrCode::$VALIDATE_ERROR);
            }

            $builder = $this->modelsManager->createBuilder();

            $builder->from(['main' => OrdinaryPayment::class]);
            $builder->leftjoin(OrdinaryPaymentExtend::class, 'main.id=ope.ordinary_payment_id', 'ope');
            $builder->andWhere('main.updated_at >= :updated_at:', ['updated_at' => '2022-03-01 00:00:00']);
            $builder = $this->getCondition($builder, $condition);
            // 取总条数
            $count_info = $builder->columns('COUNT(main.id) as t_count')->getQuery()->getSingleResult();
            $count      = $count_info->t_count ?? 0;
            $items      = [];
            if ($count) {
                $builder->columns([
                    'main.id',
                    'main.apply_no',
                    'main.cost_company_id',
                    'main.created_at',
                    'main.approved_at',
                    'main.sync_sap',
                    'main.sap_note',
                    'ope.supplier_name',
                    'ope.sap_supplier_no',
                    'ope.pay_at',

                ]);
                $builder->orderBy('main.id asc');
                if ($export) {
                    $builder->limit(0, self::DOWN_LIMIT_NUM);
                } else {
                    $builder->limit($page_size, $offset);
                }
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleItems($items);
                if ($export) {
                    return $items;
                }
                $data['items']                     = $items;
                $data['pagination']['total_count'] = $count;
            }
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('sap普通付款传输列表:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 查询条件
     * */
    private function getCondition($builder, $condition)
    {
        //申请编号
        if (isset($condition['apply_no']) && !empty($condition['apply_no'])) {
            $builder->andWhere('main.apply_no = :apply_no:', ['apply_no' => $condition['apply_no']]);
        }

        if (isset($condition['supplier_name']) && !empty($condition['supplier_name'])) {
            $builder->andWhere('ope.supplier_name  LIKE :supplier_name:',
                ['supplier_name' => "%{$condition['supplier_name']}%"]);
        }

        if (isset($condition['sap_supplier_no']) && !empty($condition['sap_supplier_no'])) {
            $builder->andWhere('ope.sap_supplier_no = :sap_supplier_no:',
                ['sap_supplier_no' => $condition['sap_supplier_no']]);
        }


        //申请时间-起始日期
        if (isset($condition['apply_start_date']) && !empty($condition['apply_start_date'])) {
            $condition['apply_start_date'] .= ' 00:00:00';

            $builder->andWhere('main.created_at >= :apply_start_date:',
                ['apply_start_date' => $condition['apply_start_date']]);
        }

        //申请时间-截止日期
        if (isset($condition['apply_end_date']) && !empty($condition['apply_end_date'])) {
            $condition['apply_end_date'] .= ' 23:59:59';

            $builder->andWhere('main.created_at <= :apply_end_date:',
                ['apply_end_date' => $condition['apply_end_date']]);
        }

        //审批通过时间-起始日期
        if (isset($condition['approved_start_date']) && !empty($condition['approved_start_date'])) {
            $condition['approved_start_date'] .= ' 00:00:00';
            $builder->andWhere('main.approved_at >= :approved_start_date:',
                ['approved_start_date' => $condition['approved_start_date']]);
        }

        //审批时间-截止日期
        if (isset($condition['approved_end_date']) && !empty($condition['approved_end_date'])) {
            $condition['approved_end_date'] .= ' 23:59:59';

            $builder->andWhere('main.approved_at <= :approved_end_date:',
                ['approved_end_date' => $condition['approved_end_date']]);
        }

        //支付时间-起始日期
        if (isset($condition['pay_start_date']) && !empty($condition['pay_start_date'])) {
            $condition['pay_start_date'] .= ' 00:00:00';

            $builder->andWhere('ope.pay_at >= :pay_start_date:', ['pay_start_date' => $condition['pay_start_date']]);
        }

        //支付时间-截止日期
        if (isset($condition['pay_end_date']) && !empty($condition['pay_end_date'])) {
            $condition['pay_end_date'] .= ' 23:59:59';

            $builder->andWhere('ope.pay_at <= :pay_end_date:', ['pay_end_date' => $condition['pay_end_date']]);
        }
        //费用所属公司
        if (isset($condition['cost_company_id']) && !empty($condition['cost_company_id'])) {
            $builder->andWhere('main.cost_company_id = :cost_company_id:',
                ['cost_company_id' => $condition['cost_company_id']]);
        }

        //发送状态
        if (isset($condition['status']) && !empty($condition['status'])) {
            $builder->andWhere('main.sync_sap = :status:', ['status' => $condition['status']]);
        } else {
            $builder->andWhere('main.sync_sap in({status:array})', ['status' => [1, 2, 3]]);
        }

        return $builder;
    }

    /**
     * 列表数据格式处理
     *
     * @param array $items
     * @return array
     */
    private function handleItems(array $items)
    {
        if (empty($items)) {
            return [];
        }
        //COO/CEO下的BU级公司列表
        $company = (new PurchaseService())->getCooCostCompany();
        //根据费用所属部门查询对应的COO/CEO下的BU级部门
        $cost_company_kv = array_column($company, 'cost_company_name', 'cost_company_id');

        $sap_status = (self::getStatus())['status'];
        $sap_status = array_column($sap_status, 'value', 'key');
        foreach ($items as &$item) {
            $item['cost_company_name'] = $cost_company_kv[$item['cost_company_id']] ?? '';
            $item['sync_sap_text']     = $sap_status[$item['sync_sap']];
        }

        return $items;
    }


    /**
     * 获取普通付款详情
     *
     * @param int $id
     * @param int $uid
     * @param boolean $if_download
     * @param bool $is_audit
     * @return array|mixed
     * @throws ValidationException
     * @throws BusinessException
     */
    public function getDetail(int $id)
    {
        //获取付款申请主表信息
        $main_model = OrdinaryPayment::findFirst([
            'id = :id:',
            'bind' => ['id' => $id],
        ]);
        if (empty($main_model)) {
            return [];
        }

        $attachments         = $main_model->getFile()->toArray();
        $data                = $main_model->toArray();
        $data['attachments'] = $attachments;

        // 获取付款申请拓展表信息
        $extend_conditions = [
            'conditions' => 'ordinary_payment_id = :ordinary_payment_id:',
            'bind'       => ['ordinary_payment_id' => $id],
        ];
        $extend_data       = OrdinaryPaymentExtend::getFirst($extend_conditions);

        $extend_data = $extend_data ? $extend_data->toArray() : [];

        $data['supplier_info'] = $extend_data;

        //金额详情
        $amount_detail = OrdinaryPaymentDetail::find([
            'conditions' => 'ordinary_payment_id = :ordinary_payment_id:',
            'bind'       => ['ordinary_payment_id' => $id],
        ]);

        $data['amount_detail_item'] = $amount_detail ? $amount_detail->toArray() : [];

        foreach ($data['amount_detail_item'] as &$item) {
            $item['deductible_tax_amount'] = bcdiv($item['deductible_tax_amount'], 100, 2);
            $item['vat7_rate']             = $item['vat_rate'] . '%';
        }


        return $this->handleData($data);
    }

    /**
     * 处理详情数据格式
     *
     * @param $data
     * @return array
     */
    protected function handleData($data)
    {
        if (empty($data)) {
            return [];
        }

        if (!empty($data['amount_detail_item'])) {
            //财务分类
            $finance_category = MaterialFinanceCategoryModel::find([
                'columns' => 'id,name',
            ])->toArray();
            $finance_category = array_column($finance_category, 'name', 'id');
            $budgetIds        = array_column($data['amount_detail_item'], 'budget_id'); //科目IDs（付款分类）
            $productIds       = array_column($data['amount_detail_item'], 'product_id');//产品IDs(费用类型）
            $budgetService    = new BudgetService();
            $budgets          = $budgetService->budgetObjectList($budgetIds);
            $products         = $budgetService->budgetObjectProductList($productIds);

            //核算科目名字
            $ledger_account_ids = array_values(array_filter(array_column($data['amount_detail_item'],
                'ledger_account_id')));
            $ledgerIdToName     = [];
            if (!empty($ledger_account_ids)) {
                $res = LedgerAccountService::getInstance()->getList($ledger_account_ids);
                if ($res['code'] == ErrCode::$SUCCESS) {
                    $ledgerIdToName = array_column($res['data'], null, 'id');
                }
            }

            //COO/CEO下的BU级公司列表
            $company = (new PurchaseService())->getCooCostCompany();
            //根据费用所属部门查询对应的COO/CEO下的BU级部门
            $cost_company_kv = array_column($company, 'cost_company_name', 'cost_company_id');

            $lang = strtolower(substr(self::$language, -2));
            if (!in_array($lang, ['th', 'en', 'cn'])) {
                $lang = 'en';
            }

            $wht_cat_map = EnumsService::getInstance()->getWhtRateCategoryMap(0);
            foreach ($data['amount_detail_item'] as $key => &$value) {
                $value['budget_name']       = isset($budgets[$value['budget_id']]) ? $budgets[$value['budget_id']]['name_' . $lang] : '';
                $value['product_name']      = isset($products[$value['product_id']]) ? $products[$value['product_id']]['name_' . $lang] : '';
                $value['wht_category_name'] = $wht_cat_map[$value['wht_category']] ?? 0;
                $value['wht_rate_name']     = $value['wht_rate'] . '%';
                //核算科目
                $value['ledger_account_name'] = $ledgerIdToName[$value['ledger_account_id']] ? $ledgerIdToName[$value['ledger_account_id']]['name'] : '';
                $value['ledger_account']      = $ledgerIdToName[$value['ledger_account_id']] ? $ledgerIdToName[$value['ledger_account_id']]['account'] : '';
                //财务分类
                $value['finance_category_text'] = $finance_category[$value['finance_category_id']] ?? '';
            }
        }

        return [
            'base_info'          => [
                'id'                   => $data['id'],
                'apply_no'             => $data['apply_no'],
                'create_id'            => $data['create_id'],
                'cost_department_name' => $data['cost_department_name'],
                'currency'             => (int)$data['currency'],
                'currency_text'        => self::$t->_(GlobalEnums::$currency_item[$data['currency']]),
                'approved_at'          => $data['approved_at'],
                'extra_message'        => $data['extra_message'],
                'voucher_abstract'     => $data['voucher_abstract'],
                'cost_company_id'      => $data['cost_company_id'],
                'cost_company_name'    => $cost_company_kv[$data['cost_company_id']] ?? '',
                'ticket_number'        => $data['ticket_number'],
                'created_at'           => $data['created_at'],
                'ticket_date'          => $data['ticket_date'] ? date('Y-m-d', strtotime($data['ticket_date'])) : '',
                'pay_at'               => $data['supplier_info']['pay_at'],
                'supplier_name'        => $data['supplier_info']['supplier_name'],
                'sap_supplier_no'      => $data['supplier_info']['sap_supplier_no'],

            ],
            'amount_detail_item' => $data['amount_detail_item'], // 金额详情
        ];
    }

    /**
     * 普通付款申请详情数据获取
     *
     * @param $id
     * @param $uid
     * @param $is_audit
     * @return array
     */
    public function getOrdinaryPaymentDetail($id)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];

        try {
            //获取申请详情数据
            $data = $this->getDetail($id);
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('sap-ordinary-payment-detail-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 编辑
     * */
    public function updatePayment($data, $uid)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            // 该申请编号是否存在
            $ordinary_payment = OrdinaryPayment::getFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $data['id']],
            ]);

            if (empty($ordinary_payment)) {
                throw new ValidationException('The number not exists' . $data['apply_no'] . ']',
                    ErrCode::$VALIDATE_ERROR);
            }
            $update_data = [];
            //更新主表
            if (isset($data['extra_message'])) {
                $update_data['extra_message'] = $data['extra_message'];
            }

            if (isset($data['voucher_abstract'])) {
                $update_data['voucher_abstract'] = $data['voucher_abstract'];
            }


            if (!empty($update_data)) {
                //记录log
                $record_log['update_before'] = [
                    'extra_message'    => $ordinary_payment->extra_message,
                    'voucher_abstract' => $ordinary_payment->voucher_abstract,
                ];


                $record_log['update_after'] = $update_data;


                $main_bool = $ordinary_payment->save($update_data);
                if ($main_bool === false) {
                    $msg_arr = $ordinary_payment->getMessages();

                    throw new BusinessException('sap普通付款-更新信息失败 = ' . json_encode([
                            'update_data' => $update_data,
                            'message'     => $msg_arr,
                        ], JSON_UNESCAPED_UNICODE), ErrCode::$VALIDATE_ERROR);
                }
            }

            //更新供应商表
            $update_extend_data = [];
            if (isset($data['sap_supplier_no'])) {
                $update_extend_data['sap_supplier_no']         = $data['sap_supplier_no'];
                $record_log['update_after']['sap_supplier_no'] = $data['sap_supplier_no'];
            }
            if (!empty($update_extend_data)) {
                // 获取付款申请拓展表信息
                $extend_conditions = [
                    'conditions' => 'ordinary_payment_id = :ordinary_payment_id:',
                    'bind'       => ['ordinary_payment_id' => $data['id']],
                ];
                $extend_data       = OrdinaryPaymentExtend::getFirst($extend_conditions);

                $extend_data                                    = $extend_data ? $extend_data->toArray() : [];
                $record_log['update_before']['sap_supplier_no'] = $extend_data['sap_supplier_no'] ?? '';

                $extend_bool = $db->updateAsDict(
                    (new OrdinaryPaymentExtend)->getSource(),
                    $update_extend_data,
                    ['conditions' => 'ordinary_payment_id=?', 'bind' => $data['id']]
                );

                if ($extend_bool === false) {
                    throw new BusinessException('sap普通付款-供应商表更新信息失败 = ' . json_encode(['update_data' => $update_data],
                            JSON_UNESCAPED_UNICODE), ErrCode::$VALIDATE_ERROR);
                }
            }


            //更新附表字段
            if (isset($data['amount_detail_item']) && !empty($data['amount_detail_item'])) {
                $update_before_detail_data = [];

                foreach ($data['amount_detail_item'] as $k => $item) {
                    $update_detail_data      = [];
                    $detail_bool             = false;
                    $ordinary_payment_detail = OrdinaryPaymentDetail::getFirst([
                        'conditions' => 'id = :id:',
                        'bind'       => ['id' => $item['id']],
                    ]);


                    if (isset($item['ledger_account_id'])) {
                        $update_detail_data['ledger_account_id'] = $item['ledger_account_id'];
                    }

                    if (isset($item['cost_center_name'])) {
                        $update_detail_data['cost_center_name'] = $item['cost_center_name'];
                    }
                    if (isset($item['voucher_description'])) {
                        $update_detail_data['voucher_description'] = $item['voucher_description'];
                    }

                    if (isset($item['deductible_vat_tax'])) {
                        $update_detail_data['deductible_vat_tax'] = $item['deductible_vat_tax'];
                    }

                    if (isset($item['deductible_tax_amount'])) {
                        $update_detail_data['deductible_tax_amount'] = !empty($item['deductible_tax_amount']) ? $item['deductible_tax_amount'] * 100 : '0';
                    }

                    if (isset($item['finance_category_id'])) {
                        $update_detail_data['finance_category_id'] = $item['finance_category_id'];
                    }

                    $update_before_detail_data[$k] = [
                        'ledger_account_id'     => $ordinary_payment_detail->ledger_account_id ?? '',
                        'cost_center_code'      => $ordinary_payment_detail->cost_center_name ?? '',
                        'voucher_description'   => $ordinary_payment_detail->voucher_description ?? '',
                        'deductible_vat_tax'    => $ordinary_payment_detail->deductible_vat_tax ?? '',
                        'deductible_tax_amount' => $ordinary_payment_detail->deductible_tax_amount ?? '',
                        'finance_category_id'   => $ordinary_payment_detail->finance_category_id ?? '',
                    ];

                    $record_log['update_after']['amount_detail_item'][] = $update_detail_data;

                    $detail_bool = $ordinary_payment_detail->save($update_detail_data);
                    if ($detail_bool === false) {
                        throw new BusinessException('sap普通付款-更新信息失败 = ' . json_encode(['update_data' => $update_detail_data,],
                                JSON_UNESCAPED_UNICODE), ErrCode::$VALIDATE_ERROR);
                    }
                }
            }
            $record_log['update_before']['amount_detail_item'] = $update_before_detail_data ?? '';

            $model = new OrdinaryPaymentOpreateLogModel();
            $model->i_create([
                'or_id'      => $data['id'],
                'create_id'  => $uid,
                'created_at' => date('Y-m-d H:i:s'),
                'log'        => json_encode($record_log),

            ]);
            $db->commit();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('sap—ordinaryPayment-update-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => [],
        ];
    }

    /**
     * 发送sap
     *
     * */
    public function sendSap($ids)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db      = $this->getDI()->get('db_oa');
        try {
            $ids    = $ids['ids'];
            $id_arr = explode(',', $ids);
            foreach ($id_arr as $k => $v) {
                if (!is_numeric($v)) {
                    throw new BusinessException('输入参数错误');
                }
            }

            $db->updateAsDict(
                (new OrdinaryPayment())->getSource(),
                [
                    'sync_sap'   => 3,
                    'updated_at' => gmdate('Y-m-d H:i:s'),
                ],
                [
                    'conditions' => " id IN ($ids) and sync_sap =2",
                ]
            );
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('sap—ordinaryPayment-send-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => [],
        ];
    }


    public function exportList($condition)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            $data      = [];
            $file_name = 'ordinary_payment_apply_sap_' . date("YmdHis");
            $header    = [
                static::$t->_('field_sap_export_no'),
                static::$t->_('field_sap_sync_text'),
                static::$t->_('field_sap_note'),
            ];
            $item      = $this->getList($condition, true);
            foreach ($item as $key => $value) {
                $data[] = [
                    $value['apply_no'],
                    $value['sync_sap_text'],
                    $value['sap_note'],
                ];
            }

            $res = $this->exportExcel($header, $data, $file_name);
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('sap—ordinary-export-failed:' . $real_message);
        }

        return [
            'code'    => $code ?? ErrCode::$SUCCESS,
            'message' => $message ?? 'success',
            'data'    => $res['data'] ?? '',
        ];
    }


}