<?php
/**
 * Created by PhpStorm.
 * Date: 2022/4/26
 * Time: 14:57
 */

namespace App\Modules\SapManage\Services;

use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Models\EnvModel;
use App\Modules\ReserveFund\Models\ReserveFundApply;
use mysql_xdevapi\Exception;

class ReserveSapService extends BaseService
{
    public static $validate_list_search = [
        'rfano'               => 'StrLenGeLe:12,20|>>>:apply no error',
        'apply_start_date'    => 'date|>>>:apply start date error',
        'apply_end_date'      => 'date|>>>:apply end date error',
        'approved_start_date' => 'date|>>>:approved start date error',
        'approved_end_date'   => 'date|>>>:approved end date error',
        'pay_start_date'      => 'date|>>>:pay start date error',
        'pay_end_date'        => 'date|>>>:pay end date error',
        'status'              => 'IntIn:1,2,3|>>>:sync status error',
        'cost_company_id'     => 'IntGt:0|>>>:cost company id error',
        'create_store_id'     => 'Str|>>>:create_store  id error',
    ];

    public static  $validate_update = [
        'id'                          => 'Required|IntGt:0',
        'pc_code'                     => 'StrLenGeLe:0,35',//pc code
        'voucher_voucher_description' => 'StrLenGeLe:0,40',//凭证摘要
    ];
    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return ReserveSapService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 列表
     * */
    public function getList($condition, $export = false)
    {
        $page_size = empty($condition['page_size']) ? 20 : $condition['page_size'];
        $page_num  = empty($condition['page']) ? 1 : $condition['page'];
        $offset    = $page_size * ($page_num - 1);


        $code         = ErrCode::$SUCCESS;
        $message      = 'success';
        $real_message = '';

        $data = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];

        try {
            if (isset($condition['apply_start_date']) && isset($condition['apply_end_date']) && $condition['apply_start_date'] > $condition['apply_end_date']) {
                throw new ValidationException('start date or end date error', ErrCode::$VALIDATE_ERROR);
            }

            if (isset($condition['approved_start_date']) && isset($condition['approved_end_date']) && $condition['approved_start_date'] > $condition['approved_end_date']) {
                throw new ValidationException(' approved start date or end date error', ErrCode::$VALIDATE_ERROR);
            }
            if (isset($condition['pay_start_date']) && isset($condition['pay_end_date']) && $condition['pay_start_date'] > $condition['pay_end_date']) {
                throw new ValidationException('pay start date or end date error', ErrCode::$VALIDATE_ERROR);
            }

            $builder = $this->modelsManager->createBuilder();
            $builder->from(['main' => ReserveFundApply::class]);

            $builder = $this->getCondition($builder, $condition);
            // 取总条数
            $count_info = $builder->columns('COUNT(main.id) as t_count')->getQuery()->getSingleResult();
            $count      = $count_info->t_count ?? 0;
            $items      = [];

            if ($count) {
                $builder->columns([
                    'main.id',
                    'main.rfano',
                    'main.create_company_id',
                    'main.create_company_name',
                    'main.create_store_name',
                    'main.apply_date',
                    'main.approve_at',
                    'main.sync_sap',
                    'main.sap_note',
                    'main.real_pay_at',

                ]);
                $builder->orderBy('main.id asc');
                if ($export) {
                    $builder->limit(0, self::DOWN_LIMIT_NUM);
                } else {
                    $builder->limit($page_size, $offset);
                }
                $items                             = $builder->getQuery()->execute()->toArray();
                $items                             = $this->handleItems($items);
                $data['items']                     = $items;
                $data['pagination']['total_count'] = (int)$count;
            }


            if ($export) {
                return $items;
            }
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('sap备用金传输列表:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 列表数据格式处理
     *
     * @param array $items
     * @return array
     */
    private function handleItems(array $items)
    {
        if (empty($items)) {
            return [];
        }

        $sap_status = (self::getStatus())['status'];
        $sap_status = array_column($sap_status, 'value', 'key');
        foreach ($items as &$item) {
            $item['sync_sap_text'] = $sap_status[$item['sync_sap']];
        }

        return $items;
    }

    /**
     * 查询条件
     * */
    private function getCondition($builder, $condition)
    {
        //申请编号
        if (isset($condition['rfano']) && !empty($condition['rfano'])) {
            $builder->andWhere('main.rfano = :rfano:', ['rfano' => $condition['rfano']]);
        }

        //申请时间-起始日期
        if (isset($condition['apply_start_date']) && !empty($condition['apply_start_date'])) {
            $builder->andWhere('main.apply_date >= :apply_start_date:',
                ['apply_start_date' => $condition['apply_start_date']]);
        }

        //申请时间-截止日期
        if (isset($condition['apply_end_date']) && !empty($condition['apply_end_date'])) {
            $builder->andWhere('main.apply_date <= :apply_end_date:',
                ['apply_end_date' => $condition['apply_end_date']]);
        }

        //审批通过时间-起始日期
        if (isset($condition['approved_start_date']) && !empty($condition['approved_start_date'])) {
            $condition['approved_start_date'] .= ' 00:00:00';

            $builder->andWhere('main.approve_at >= :approved_start_date:',
                ['approved_start_date' => $condition['approved_start_date']]);
        }

        //审批时间-截止日期
        if (isset($condition['approved_end_date']) && !empty($condition['approved_end_date'])) {
            $condition['approved_end_date'] .= ' 23:59:59';

            $builder->andWhere('main.approve_at <= :approved_end_date:',
                ['approved_end_date' => $condition['approved_end_date']]);
        }

        //支付时间-起始日期
        if (isset($condition['pay_start_date']) && !empty($condition['pay_start_date'])) {
            $condition['pay_start_date'] .= ' 00:00:00';

            $builder->andWhere('main.real_pay_at >= :pay_start_date:',
                ['pay_start_date' => $condition['pay_start_date']]);
        }

        //支付时间-截止日期
        if (isset($condition['pay_end_date']) && !empty($condition['pay_end_date'])) {
            $condition['pay_end_date'] .= ' 23:59:59';
            $builder->andWhere('main.real_pay_at <= :pay_at:', ['pay_at' => $condition['pay_end_date']]);
        }
        //费用所属公司
        if (isset($condition['cost_company_id']) && !empty($condition['cost_company_id'])) {
            $builder->andWhere('main.create_company_id = :cost_company_id:',
                ['cost_company_id' => $condition['cost_company_id']]);
        }

        //费用所属网点
        if (isset($condition['create_store_id']) && !empty($condition['create_store_id'])) {
            $builder->andWhere('main.create_store_id = :create_store_id:',
                ['create_store_id' => $condition['create_store_id']]);
        }


        //发送状态
        if (isset($condition['status']) && !empty($condition['status'])) {
            $builder->andWhere('main.sync_sap = :status:', ['status' => $condition['status']]);
        } else {
            $builder->andWhere('main.sync_sap in({status:array})', ['status' => [1, 2, 3]]);
        }

        return $builder;
    }

    /**
     * 修改
     * */
    public function update($data, $uid)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $record_log = [];
            // 该申请编号是否存在
            $reserve = ReserveFundApply::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $data['id']],
            ]);

            if (empty($reserve)) {
                throw new ValidationException('The number not exists' . $data['id'], ErrCode::$VALIDATE_ERROR);
            }

            $update_data = [];
            //更新主表
            if (isset($data['voucher_description'])) {
                $update_data['voucher_description'] = $data['voucher_description'];
            }

            if (isset($data['pc_code'])) {
                $update_data['pc_code'] = $data['pc_code'];
            }

            if (!empty($update_data)) {
                //记录log
                $record_log['update_before'] = [
                    'extra_message'    => $reserve->extra_message,
                    'voucher_abstract' => $reserve->voucher_abstract,

                ];
                $record_log['update_after']  = $update_data;
                $main_bool                   = $reserve->save($update_data);
                if ($main_bool === false) {
                    $msg_arr = $reserve->getMessages();

                    throw new BusinessException('sap备用金-更新信息失败 = ' . json_encode([
                            'update_data' => $update_data,
                            'message'     => $msg_arr,
                        ], JSON_UNESCAPED_UNICODE), ErrCode::$VALIDATE_ERROR);
                }
            }

            $db->commit();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('sap—reserve-update-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
        ];
    }

    /**
     * 查看
     * */
    public function getDetail($id)
    {
        try {
            $apply = ReserveFundApply::findFirst([
                'id = :id:',
                'bind' => ['id' => $id],
            ]);

            if (empty($apply)) {
                return [];
            }

            $data = $apply->toArray();


            $data = $this->handleDetailData($data);
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('ReserveSap-Apply-get-audit-detail-failed:' . $real_message);
        }
        return [
            'code'    => $code ?? ErrCode::$SUCCESS,
            'message' => $message ?? 'success',
            'data'    => $data ?? [],
        ];
    }

    /**
     * @param $data
     * @param $is_filter
     * @return array
     */
    private function handleDetailData($data)
    {
        if (empty($data) || !is_array($data)) {
            return [];
        }
        $ledger_account = EnvModel::getEnvByCode('reserve_sap_ledger_account');
        $ledger_account = json_decode($ledger_account, true);

        $status                      = Enums::$loan_status[$data['status']] ?? '';
        $data['amount']              = bcdiv($data['amount'], 1000, 2);
        $data['status_text']         = static::$t->_($status);
        $payment_currency            = GlobalEnums::$currency_item[$data['currency']] ?? '';
        $data['currency_text']       = static::$t->_($payment_currency);
        $data['pay_status_text']     = static::$t->_(Enums::$loan_pay_status[$data['pay_status']]);
        $data['ledger_account_name'] = $ledger_account['ledger_account_name'] ?? '';
        $data['ledger_account_id']   = $ledger_account['ledger_account_id'] ?? '';


        return $data;
    }

    /**
     * 重发
     * */
    public function sendSap($ids)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db      = $this->getDI()->get('db_oa');
        try {
            $ids    = $ids['ids'];
            $id_arr = explode(',', $ids);
            foreach ($id_arr as $k => $v) {
                if (!is_numeric($v)) {
                    throw new BusinessException('输入参数错误');
                }
            }
            $db->updateAsDict(
                (new ReserveFundApply())->getSource(),
                [
                    'sync_sap'   => 3,
                    'updated_at' => gmdate('Y-m-d H:i:s'),
                ],
                [
                    'conditions' => " id IN ($ids) and sync_sap =2",
                ]
            );
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('sap—reserve-send-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
        ];
    }

    public function exportList($condition)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            $data      = [];
            $file_name = 'reserve_apply_sap_' . date("YmdHis");
            $header    = [
                static::$t->_('field_sap_export_no'),
                static::$t->_('field_sap_sync_text'),
                static::$t->_('field_sap_note'),
            ];
            $item      = $this->getList($condition, true);
            foreach ($item as $key => $value) {
                $data[] = [
                    $value['rfano'],
                    $value['sync_sap_text'],
                    $value['sap_note'],
                ];
            }

            $res = $this->exportExcel($header, $data, $file_name);
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('sap—reserve-export-failed:' . $real_message);
        }

        return [
            'code'    => $code ?? ErrCode::$SUCCESS,
            'message' => $message ?? 'success',
            'data'    => $res['data'] ?? '',
        ];
    }
}