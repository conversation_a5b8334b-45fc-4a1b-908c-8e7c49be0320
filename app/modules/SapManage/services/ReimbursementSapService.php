<?php
/**
 * Created by PhpStorm.
 * Date: 2022/1/18
 * Time: 10:40
 */

namespace App\Modules\SapManage\Services;


use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\ReimbursementEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Modules\Budget\Models\BudgetObjectProduct;
use App\Modules\Budget\Services\BudgetService;
use App\Modules\Budget\Services\LedgerAccountService;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Purchase\Services\BaseService as PurchaseService;
use App\Modules\Reimbursement\Models\Detail;
use App\Modules\Reimbursement\Models\Reimbursement;
use App\Modules\Reimbursement\Services\CategoryService;
use App\Modules\ReserveFund\Models\ReserveFundReimburse;
use App\Modules\SapManage\Models\ReimbursementOpreateLogModel;
use mysql_xdevapi\Exception;

class ReimbursementSapService extends BaseService
{
    public static $validate_list_search = [
        'apply_no'            => 'StrLenGeLe:12,20|>>>:apply no error',
        'apply_start_date'    => 'date|>>>:apply start date error',
        'apply_end_date'      => 'date|>>>:apply end date error',
        'approved_start_date' => 'date|>>>:approved start date error',
        'approved_end_date'   => 'date|>>>:approved end date error',
        'pay_start_date'      => 'date|>>>:pay start date error',
        'pay_end_date'        => 'date|>>>:pay end date error',
        'status'              => 'IntIn:1,2,3|>>>:sync status error',
        'cost_company_id'     => 'IntGt:0|>>>:cost company id error',
    ];

    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return ReimbursementSapService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 列表
     * */
    public function getList($condition, $export = false)
    {
        $page_size = empty($condition['page_size']) ? 20 : $condition['page_size'];
        $page_num  = empty($condition['page']) ? 1 : $condition['page'];
        $offset    = $page_size * ($page_num - 1);


        $code         = ErrCode::$SUCCESS;
        $message      = 'success';
        $real_message = '';

        $data = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];

        try {
            if (isset($condition['apply_start_date']) && isset($condition['apply_end_date']) && $condition['apply_start_date'] > $condition['apply_end_date']) {
                throw new ValidationException('start date or end date error', ErrCode::$VALIDATE_ERROR);
            }

            if (isset($condition['approved_start_date']) && isset($condition['approved_end_date']) && $condition['approved_start_date'] > $condition['approved_end_date']) {
                throw new ValidationException(' approved start date or end date error', ErrCode::$VALIDATE_ERROR);
            }
            if (isset($condition['pay_start_date']) && isset($condition['pay_end_date']) && $condition['pay_start_date'] > $condition['pay_end_date']) {
                throw new ValidationException('pay start date or end date error', ErrCode::$VALIDATE_ERROR);
            }

            $builder = $this->modelsManager->createBuilder();

            $builder->from(['main' => Reimbursement::class]);
            $builder->andWhere('main.updated_at >= :updated_at:', ['updated_at' => '2022-03-01 00:00:00']);

            $builder = $this->getCondition($builder, $condition);
            // 取总条数
            $count_info = $builder->columns('COUNT(main.id) as t_count')->getQuery()->getSingleResult();
            $count      = $count_info->t_count ?? 0;
            $items      = [];
            if ($count) {
                $builder->columns([
                    'main.id',
                    'main.no',
                    'main.cost_company_id',
                    'main.created_at',
                    'main.updated_at',
                    'main.approved_at',
                    'main.sync_sap',
                    'main.sap_note',
                    'main.pay_at',

                ]);
                $builder->orderBy('main.id asc');
                if ($export) {
                    $builder->limit(0, self::DOWN_LIMIT_NUM);
                } else {
                    $builder->limit($page_size, $offset);
                }
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleItems($items);
                if ($export) {
                    return $items;
                }
                $data['items']                     = $items;
                $data['pagination']['total_count'] = $count;
            }
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('sap普通付款传输列表:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 查询条件
     * */
    private function getCondition($builder, $condition)
    {
        //申请编号
        if (isset($condition['apply_no']) && !empty($condition['apply_no'])) {
            $builder->andWhere('main.no = :apply_no:', ['apply_no' => $condition['apply_no']]);
        }

        //申请时间-起始日期
        if (isset($condition['apply_start_date']) && !empty($condition['apply_start_date'])) {
            $condition['apply_start_date'] .= ' 00:00:00';

            $builder->andWhere('main.created_at >= :apply_start_date:',
                ['apply_start_date' => $condition['apply_start_date']]);
        }

        //申请时间-截止日期
        if (isset($condition['apply_end_date']) && !empty($condition['apply_end_date'])) {
            $condition['apply_end_date'] .= ' 23:59:59';

            $builder->andWhere('main.created_at <= :apply_end_date:',
                ['apply_end_date' => $condition['apply_end_date']]);
        }

        //审批通过时间-起始日期
        if (isset($condition['approved_start_date']) && !empty($condition['approved_start_date'])) {
            $condition['approved_start_date'] .= ' 00:00:00';

            $builder->andWhere('main.approved_at >= :approved_start_date:',
                ['approved_start_date' => $condition['approved_start_date']]);
        }

        //审批时间-截止日期
        if (isset($condition['approved_end_date']) && !empty($condition['approved_end_date'])) {
            $condition['approved_end_date'] .= ' 23:59:59';

            $builder->andWhere('main.approved_at <= :approved_end_date:',
                ['approved_end_date' => $condition['approved_end_date']]);
        }

        //支付时间-起始日期
        if (isset($condition['pay_start_date']) && !empty($condition['pay_start_date'])) {
            $condition['pay_start_date'] .= ' 00:00:00';
            $builder->andWhere('main.pay_at >= :pay_at:', ['pay_at' => $condition['pay_start_date']]);
        }

        //支付时间-截止日期
        if (isset($condition['pay_end_date']) && !empty($condition['pay_end_date'])) {
            $condition['pay_end_date'] .= ' 23:59:59';

            $builder->andWhere('main.pay_at <= :pay_at:', ['pay_at' => $condition['pay_end_date']]);
        }
        //费用所属公司
        if (isset($condition['cost_company_id']) && !empty($condition['cost_company_id'])) {
            $builder->andWhere('main.cost_company_id = :cost_company_id:',
                ['cost_company_id' => $condition['cost_company_id']]);
        }

        //发送状态
        if (isset($condition['status']) && !empty($condition['status'])) {
            $builder->andWhere('main.sync_sap = :status:', ['status' => $condition['status']]);
        } else {
            $builder->andWhere('main.sync_sap in({status:array})', ['status' => [1, 2, 3]]);
        }

        return $builder;
    }

    /**
     * 列表数据格式处理
     *
     * @param array $items
     * @return array
     */
    private function handleItems(array $items)
    {
        if (empty($items)) {
            return [];
        }
        //COO/CEO下的BU级公司列表
        $company = (new PurchaseService())->getCooCostCompany();
        //根据费用所属部门查询对应的COO/CEO下的BU级部门
        $cost_company_kv = array_column($company, 'cost_company_name', 'cost_company_id');

        $sap_status = (self::getStatus())['status'];
        $sap_status = array_column($sap_status, 'value', 'key');
        foreach ($items as &$item) {
            $item['cost_company_name'] = $cost_company_kv[$item['cost_company_id']] ?? '';
            $item['sync_sap_text']     = $sap_status[$item['sync_sap']];
        }

        return $items;
    }


    /**
     * @param $id
     * @param null $uid
     * @return array|mixed
     * @throws ValidationException
     * @throws BusinessException
     */
    public function getDetail($id)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        try {
            $item = Reimbursement::getFirst([
                'id = :id:',
                'bind' => ['id' => $id],
            ]);

            if (empty($item)) {
                return [];
            }

            $details = $item->getDetails();
            $data    = $item->toArray();

            $data['expense'] = $details->toArray();

            $data = $this->handleData($data);
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('Reimbursement-get-audit-detail-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * @param $data
     * @return array
     */
    private function handleData($data)
    {
        if (empty($data) || !is_array($data)) {
            return [];
        }

        $expenseIdArr = array_values(array_unique(array_column($data['expense'], "category_b")));

        $expenseMap = CategoryService::getInstance()->getListByIds($expenseIdArr);

        $budgetIds = array_values(array_filter(array_column($data['expense'], 'budget_id')));
        if ($budgetIds) {
            $budgetService = new BudgetService();
            $budgets       = $budgetService->budgetObjectList($budgetIds);
        }

        $productIds = array_values(array_filter(array_column($data['expense'], 'product_id')));
        if ($productIds) {
            $products = BudgetObjectProduct::find([
                'conditions' => ' id in ({ids:array})',
                'bind'       => ['ids' => $productIds],
            ])->toArray();
            $products = array_column($products, null, 'id');
        }


        //核算科目名字
        $ledger_account_ids = array_values(array_filter(array_column($data['expense'], 'ledger_account_id')));
        $ledgerIdToName     = [];
        if (!empty($ledger_account_ids)) {
            $res = LedgerAccountService::getInstance()->getList($ledger_account_ids);
            if ($res['code'] == ErrCode::$SUCCESS) {
                $ledgerIdToName = array_column($res['data'], null, 'id');
            }
        }
        //备用金信息
        $data['rfano']      = '';
        $data['petty_used'] = ReimbursementEnums::PETTY_USED_NO;
        $rfrei              = ReserveFundReimburse::findFirst('rei_id=' . $data['id']);
        if ($rfrei) {
            $data['rfano']      = $rfrei->rfano;
            $data['petty_used'] = ReimbursementEnums::PETTY_USED_YES;
        }

        $wht_type_arr = EnumsService::getInstance()->getWhtRateCategoryMap(0);

        $company = (new PurchaseService())->getCooCostCompany();
        //根据费用所属部门查询对应的COO/CEO下的BU级部门
        $cost_company_kv = array_column($company, 'cost_company_name', 'cost_company_id');

        foreach ($data['expense'] as &$item) {
            $item['tax']     = bcdiv($item['tax'], 1000, 2);
            $item['tax_not'] = bcdiv($item['tax_not'], 1000, 2);
            $item['amount']  = bcdiv($item['amount'], 1000, 2);
            //先除1000,转为0.06, 兼容以前的逻辑(除1000可以直接用于计算),再乘100转为前端的枚举
            $item['rate'] = (string)bcdiv($item['rate'], 1000, 4);
            $item['rate'] = (string)bcmul($item['rate'], 100, 2);
            $item['rate'] = rtrim(rtrim($item['rate'], '0'), '.');

            //匹配不到枚举的返回空
            if (!is_numeric($item['rate'])) {
                $item['rate'] = '';
            }

            $item['rate'] .= '%';

            $item['wht_tax'] = (string)$item['wht_tax'] ?? '';

            $item['wht_tax'] .= '%';

            $item['wht_tax_amount']        = bcdiv($item['wht_tax_amount'], 1000, 2);
            $item['deductible_vat_tax']    = (string)$item['deductible_vat_tax'] ?? '';
            $item['deductible_tax_amount'] = bcdiv($item['deductible_tax_amount'], 1000, 2);
            $item['payable_amount']        = bcdiv($item['payable_amount'], 1000, 2);
            if (!empty($item['fuel_mileage'])) {
                $item['fuel_mileage'] = bcdiv($item['fuel_mileage'], 1000, 2);
            }

            $item['category_a_text'] = $item['category_a'] ? static::$t->_(Enums::$reimbursement_expense_type[$item['category_a']]) : "";

            $item['category_b_text'] = $item['category_b'] ? static::$t->_($expenseMap[$item['category_b']]) : "";

            if (isset($budgets) && isset($budgets[$item['budget_id']])) {
                $item['budget_text'] = $budgets[$item['budget_id']]['name_' . strtolower(substr(static::$language,
                    -2))];
            } else {
                $item['budget_text'] = '';
            }
            $item['template_type'] = isset($products) && isset($products[$item['product_id']]) ? $products[$item['product_id']]['template_type'] : 0;

            if ($item['product_id']) {
                $item['product_name'] = isset($products) && isset($products[$item['product_id']]) ? $products[$item['product_id']]['name_' . strtolower(substr(static::$language,
                    -2))] : $item['product_name'];
            }

            $item['start_at']          = empty($item['start_at']) ? '' : $item['start_at'];
            $item['end_at']            = empty($item['end_at']) ? '' : $item['end_at'];
            $item['cost_store_n_id']   = empty($item['cost_store_n_id']) ? '' : $item['cost_store_n_id'];
            $item['cost_store_n_name'] = empty($item['cost_store_n_name']) ? '' : $item['cost_store_n_name'];
            $item['cost_center_code']  = empty($item['cost_center_code']) ? '' : $item['cost_center_code'];
            $item['wht_type']          = empty($item['wht_type']) ? '' : $item['wht_type'];

            $item['wht_type_name'] = $wht_type_arr[$item['wht_type']] ?? '/';

            //核算科目
            $ledger_account_info = $ledgerIdToName[$item['ledger_account_id']] ?? [];
            if (!empty($ledger_account_info)) {
                $item['ledger_account_name'] = $ledger_account_info['name'];
                $item['ledger_account']      = $ledger_account_info['account'];
            } else {
                $item['ledger_account_name'] = '';
                $item['ledger_account']      = '';
            }
        }

        $data['pay_at'] = $data['pay_at'] ?? "";
        $data['remark'] = $data['remark'] ?? "";

        $data['cost_company_id'] = $data['cost_company_id'] ?? "";
        //查询公司名称
        $data['cost_company_name'] = '';
        if ($data['cost_company_id']) {
            $data['cost_company_name'] = $cost_company_kv[$data['cost_company_id']] ?? '';
        }
        $data['extra_message']    = $data['extra_message'] ?? "";
        $data['voucher_abstract'] = $data['voucher_abstract'] ?? "";

        $data['currency_text']   = static::$t->_(GlobalEnums::$currency_item[$data['currency']]);
        $data['pay_status_text'] = static::$t->_(Enums::$loan_pay_status[$data['pay_status']]);


        return [
            'base_info'          => [
                'no'                   => $data['no'],
                'cost_company_name'    => $data['cost_company_name'],
                'cost_department_name' => $data['cost_department_name'],
                'created_at'           => $data['created_at'],
                'updated_at'           => $data['updated_at'],
                'approved_at'          => $data['approved_at'],
                'pay_at'               => $data['pay_at'],
                'extra_message'        => $data['extra_message'],
                'voucher_abstract'     => $data['voucher_abstract'],
                'currency_text'        => static::$t->_(GlobalEnums::$currency_item[$data['currency']]),
                'cost_store_type'      => $data['cost_store_type'],


            ],
            'amount_detail_item' => $data['expense'],
        ];
    }

    /**
     * 编辑
     * */
    public function updateReimbursement($data, $uid)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $record_log = [];
            // 该申请编号是否存在
            $reimbursement = Reimbursement::getFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $data['id']],
            ]);

            if (empty($reimbursement)) {
                throw new ValidationException('The number not exists' . $data['id'], ErrCode::$VALIDATE_ERROR);
            }

            $update_data = [];
            //更新主表
            if (isset($data['extra_message'])) {
                $update_data['extra_message'] = $data['extra_message'];
            }

            if (isset($data['voucher_abstract'])) {
                $update_data['voucher_abstract'] = $data['voucher_abstract'];
            }

            if (!empty($update_data)) {
                //记录log
                $record_log['update_before'] = [
                    'extra_message'    => $reimbursement->extra_message,
                    'voucher_abstract' => $reimbursement->voucher_abstract,

                ];
                $record_log['update_after']  = $update_data;
                $main_bool                   = $reimbursement->save($update_data);
                if ($main_bool === false) {
                    $msg_arr = $reimbursement->getMessages();

                    throw new BusinessException('sap普通付款-更新信息失败 = ' . json_encode([
                            'update_data' => $update_data,
                            'message'     => $msg_arr,
                        ], JSON_UNESCAPED_UNICODE), ErrCode::$VALIDATE_ERROR);
                }
            }


            //更新附表字段
            if (isset($data['amount_detail_item']) && !empty($data['amount_detail_item'])) {
                $update_before_detail_data = [];

                foreach ($data['amount_detail_item'] as $k => $item) {
                    $update_detail_data   = [];
                    $detail_bool          = false;
                    $reimbursement_detail = Detail::getFirst([
                        'conditions' => 'id = :id:',
                        'bind'       => ['id' => $item['id']],
                    ]);

                    if (empty($reimbursement_detail)) {
                        throw new ValidationException('The item_id not exists' . $item['id'], ErrCode::$VALIDATE_ERROR);
                    }


                    if (isset($item['ledger_account_id'])) {
                        $update_detail_data['ledger_account_id'] = $item['ledger_account_id'];
                    }

                    if (isset($item['cost_center_code'])) {
                        $update_detail_data['cost_center_code'] = $item['cost_center_code'];
                    }
                    if (isset($item['voucher_description'])) {
                        $update_detail_data['voucher_description'] = $item['voucher_description'];
                    }

                    if (isset($item['deductible_vat_tax'])) {
                        $update_detail_data['deductible_vat_tax'] = $item['deductible_vat_tax'];
                    }

                    if (isset($item['deductible_tax_amount'])) {
                        $update_detail_data['deductible_tax_amount'] = bcmul(round($item['deductible_tax_amount'] ?? 0,
                            2), 1000);
                    }
                    $record_log['update_after']['amount_detail_item'][] = $update_detail_data;
                    $update_before_detail_data[$k]                      = [
                        'ledger_account_id'     => $reimbursement_detail->ledger_account_id ?? '',
                        'cost_center_code'      => $reimbursement_detail->cost_center_code ?? '',
                        'voucher_description'   => $reimbursement_detail->voucher_description ?? '',
                        'deductible_vat_tax'    => $reimbursement_detail->deductible_vat_tax ?? '',
                        'deductible_tax_amount' => $reimbursement_detail->deductible_tax_amount ?? '',
                    ];
                    $detail_bool                                        = $reimbursement_detail->save($update_detail_data);
                    if ($detail_bool === false) {
                        throw new BusinessException('sap普通付款-更新信息失败 = ' . json_encode(['update_data' => $update_detail_data,],
                                JSON_UNESCAPED_UNICODE), ErrCode::$VALIDATE_ERROR);
                    }
                }
            }
            $record_log['update_before']['amount_detail_item'] = $update_before_detail_data ?? '';


            $model = new ReimbursementOpreateLogModel();
            $model->i_create([
                're_id'      => $data['id'],
                'create_id'  => $uid,
                'created_at' => date('Y-m-d H:i:s'),
                'log'        => json_encode($record_log),

            ]);

            $db->commit();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('sap—reimbursement-update-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
        ];
    }

    /**
     * 发送sap
     *
     * */
    public function sendSap($ids)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db      = $this->getDI()->get('db_oa');
        try {
            $ids    = $ids['ids'];
            $id_arr = explode(',', $ids);
            foreach ($id_arr as $k => $v) {
                if (!is_numeric($v)) {
                    throw new BusinessException('输入参数错误');
                }
            }


            $db->updateAsDict(
                (new Reimbursement())->getSource(),
                [
                    'sync_sap'   => 3,
                    'updated_at' => gmdate('Y-m-d H:i:s'),
                ],
                [
                    'conditions' => " id IN ($ids) and sync_sap =2",
                ]
            );
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('sap—reimbursement-send-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
        ];
    }


    public function exportList($condition)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            ini_set('memory_limit', '1024M');

            $data      = [];
            $file_name = 'reimbursement_apply_sap_' . date("YmdHis");
            $header    = [
                static::$t->_('field_sap_export_no'),
                static::$t->_('field_sap_sync_text'),
                static::$t->_('field_sap_note'),
            ];
            $item      = $this->getList($condition, true);
            foreach ($item as $key => $value) {
                $data[] = [
                    $value['no'],
                    $value['sync_sap_text'],
                    $value['sap_note'],
                ];
            }

            $res = $this->exportExcel($header, $data, $file_name);
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('sap—reimbursement-export-failed:' . $real_message);
        }

        return [
            'code'    => $code ?? ErrCode::$SUCCESS,
            'message' => $message ?? 'success',
            'data'    => $res['data'] ?? '',
        ];
    }


}