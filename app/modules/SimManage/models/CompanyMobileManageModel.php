<?php

namespace App\Modules\SimManage\Models;

use App\Models\Base;

class CompanyMobileManageModel extends Base
{
    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_backyard');
        $this->setSource('company_mobile_manage');
    }

    //手机号状态
    const STATUS_WAIT_ACTIVE = 1 ;//待激活
    const STATUS_ACTIVE = 2; //激活
    const STATUS_NOT_ENABLED = 3;//未启用
    consT STATUS_TERMINATE_CONTRACT = 4;//终止合约
    CONST STATUS_FEEDBACK_HANDLING = 5;//异常处理中

    const STATUS_LIST = [
        self::STATUS_WAIT_ACTIVE,
        self::STATUS_ACTIVE,
        self::STATUS_NOT_ENABLED,
        self::STATUS_TERMINATE_CONTRACT,
        self::STATUS_FEEDBACK_HANDLING,
    ];


    //手机号码更新SIM序列号状态
    CONST UPDATE_SIM_STATUS_WAITING = 1;
    CONST UPDATE_SIM_STATUS_UPDATED = 2;
    CONST UPDATE_SIM_STATUS_EMPTY = 3;
    const UPDATE_SIM_STATUS_LIST = [
        self::UPDATE_SIM_STATUS_EMPTY,
        self::UPDATE_SIM_STATUS_WAITING,
        self::UPDATE_SIM_STATUS_UPDATED,
    ];

}
