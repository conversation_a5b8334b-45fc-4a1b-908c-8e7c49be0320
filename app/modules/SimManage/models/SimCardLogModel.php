<?php

namespace App\Modules\SimManage\Models;

use App\Models\Base;

class SimCardLogModel extends Base
{
    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_backyard');
        $this->setSource('sim_card_log');
    }

    const TYPE_ADD = 1;
    const TYPE_BATCH_ADD = 2;
    const TYPE_EDIT = 3;
    const TYPE_BATCH_EDIT = 4;
    const TYPE_ABANDON = 5;//废弃
    const TYPE_USE = 6;//使用

}
