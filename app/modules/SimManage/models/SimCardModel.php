<?php

namespace App\Modules\SimManage\Models;

use App\Models\Base;

class SimCardModel extends Base
{
    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_backyard');
        $this->setSource('sim_card');
    }

    /**
     * 运营商
     */
    const SIM_COMPANY_AIS = 1;
    const SIM_COMPANY_TRUE = 2;

    const SIM_COMPANY_LIST = [
        self::SIM_COMPANY_AIS  => 'AIS',
        self::SIM_COMPANY_TRUE => 'TRUE',
    ];

    const STATUS_AVAILABLE = 1;//可用
    const STATUS_USED = 2;//已使用
    const STATUS_ABANDON = 3;//废弃


    const STATUS_LIST=[
        self::STATUS_AVAILABLE,
        self::STATUS_USED,
        self::STATUS_ABANDON,
    ];

}
