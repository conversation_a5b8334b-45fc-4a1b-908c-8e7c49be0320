<?php

namespace App\Modules\SimManage\Models;

use App\Models\Base;

class CompanyMobileFeedbackModel extends Base
{
    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_backyard');
        $this->setSource('company_mobile_feedback');
    }

    CONST OPERATOR_TYPE_STOP = 1;//停用并清空BY号码
    CONST OPERATOR_TYPE_NORMAL = 2;//标记号码正常

    CONST STATE_PENDING = 1;//待出来
    CONST STATE_PROCESSED = 2;//已处理
}
