<?php

namespace App\Modules\SimManage\Models;

use App\Models\Base;

class CompanyMobileManageLogModel extends Base
{
    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_backyard');
        $this->setSource('company_mobile_manage_log');
    }

    const TYPE_ADD = 1;             //新增
    const TYPE_IMPORT_ADD = 2;      //导入新增
    const TYPE_EDIT = 3;            //编辑
    const TYPE_IMPORT_EDIT = 4;     //导入编辑
    const TYPE_ACTIVE = 5;          //激活
    const TYPE_STOP = 6;            //停用
    const TYPE_STAFF_NEW_NUMBER = 7;//员工发起申请新号码
    const TYPE_STAFF_UPDATE_SIM = 8;//员工发起更换SIM卡
    const TYPE_STAFF_FEEDBACK = 9;//企业号码异常反馈
    const TYPE_STAFF_STOP_BY = 10;//停用并清空BY号码
    const TYPE_STAFF_NORMAL = 11;//标记号码正常
    const TYPE_STAFF_CHANGE = 12;//变更使用人

    const TYPE_LIST = [
        self::TYPE_ADD,
        self::TYPE_IMPORT_ADD,
        self::TYPE_EDIT,
        self::TYPE_IMPORT_EDIT,
        self::TYPE_ACTIVE,
        self::TYPE_STOP,
        self::TYPE_STAFF_NEW_NUMBER,
        self::TYPE_STAFF_UPDATE_SIM,
        self::TYPE_STAFF_FEEDBACK,
        self::TYPE_STAFF_STOP_BY,
        self::TYPE_STAFF_NORMAL,
        self::TYPE_STAFF_CHANGE
    ];






}
