<?php

namespace App\Modules\SimManage\Controllers;

use App\Library\ErrCode;
use App\Library\Enums\ImportCenterEnums;
use App\Library\Exception\BusinessException;
use app\modules\SimManage\models\CompanyMobileFeedbackModel;
use App\Modules\Common\Services\ImportCenterService;
use App\Modules\SimManage\Services\CompanyMobileService;
use App\Modules\SimManage\Services\FeedbackService;
use Exception;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use GuzzleHttp\Exception\GuzzleException;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;


class FeedbackController extends BaseController
{


    /**
     * @Permission(action=sim_card_feedback_query)
     * 列表
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function listAction()
    {
        $params = (array)$this->request->get();
        Validation::validate($params, FeedbackService::$validate_list);
        $res = FeedbackService::getInstance()->getDataList($params);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }


    /**
     * @Permission(action=sim_card_feedback_export)
     * 导出
     * @return Response|ResponseInterface
     * @throws ValidationException|Exception
     */
    public function exportAction()
    {
        $params = (array)$this->request->get();
        // 加锁
        $lock_key = md5('sim_manage_feedback_export_' . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return FeedbackService::getInstance()->export($params, $this->user);
        }, $lock_key, 5);

        if ($res === false) {
            return $this->returnJson(ErrCode::$FREQUENT_VISIT_ERROR, $this->t['sys_processing'], []);
        }
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }


    /**
     * @Permission(action=sim_card_feedback_import)
     * 批量编辑
     * @return Response|ResponseInterface
     * @throws ValidationException
     * @throws BusinessException
     * @throws GuzzleException
     */
    public function batchEditAction()
    {
        $res = CompanyMobileService::getInstance()->uploadFileToImportCenter($this->user,ImportCenterEnums::TYPE_SIM_COMPANY_MOBILE_FEEDBACK_EDIT);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }

    /**
     * 批量修改结果
     * @Permission(action=sim_card_feedback_import)
     * @return Response|ResponseInterface
     */
    public function batchEditResultAction()
    {
        $res = ImportCenterService::getInstance()->getBarcodeImportResult(ImportCenterEnums::TYPE_SIM_COMPANY_MOBILE_FEEDBACK_EDIT,
            $this->user['id']);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 标记号码正常
     * @Permission(action=sim_card_feedback_normal_phone)
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function normalPhoneNumberAction()
    {
        $params                        = (array)$this->request->get();
        $params['operator_type'] = CompanyMobileFeedbackModel::OPERATOR_TYPE_NORMAL;
        Validation::validate($params, FeedbackService::$validate_operator_type);
        $res = FeedbackService::getInstance()->handlePhoneNumber($params, $this->user);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }

    /**
     * 停用并清空BY号码
     * @Permission(action=sim_card_feedback_stop_phone)
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function stopPhoneNumberAction()
    {
        $params                        = (array)$this->request->get();
        $params['operator_type'] = CompanyMobileFeedbackModel::OPERATOR_TYPE_STOP;
        Validation::validate($params, FeedbackService::$validate_operator_type);
        $res = FeedbackService::getInstance()->handlePhoneNumber($params, $this->user);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }




}
