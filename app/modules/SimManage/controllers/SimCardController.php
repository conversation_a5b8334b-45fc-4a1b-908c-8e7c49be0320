<?php

namespace App\Modules\SimManage\Controllers;


use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Enums\ImportCenterEnums;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Services\ImportCenterService;
use App\Modules\Setting\Controllers\BaseController;
use App\Modules\SimManage\Services\CompanyMobileService;
use App\Modules\SimManage\Services\SimCardService;
use Exception;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;


class SimCardController extends BaseController
{


    /**
     * @Token
     * 枚举信息
     * @return Response|ResponseInterface
     */
    public function getOptionsDefaultAction()
    {
        $res = SimCardService::getInstance()->getOptionsDefault();
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }

    /**
     * @Permission(action=sim_card_list)
     * 列表
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function listAction()
    {
        $params = (array)$this->request->get();
        Validation::validate($params, SimCardService::$validate_list);
        $res = SimCardService::getInstance()->getDataList($params);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }


    /**
     * @Permission(action=sim_card_export)
     * 导出
     * @return Response|ResponseInterface
     * @throws ValidationException|Exception
     */
    public function exportAction()
    {
        $params = (array)$this->request->get();
        // 加锁
        $lock_key = md5('SIM_SN_number_' . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return SimCardService::getInstance()->export($params, $this->user);
        }, $lock_key, 5);
        if ($res === false) {
            return $this->returnJson(ErrCode::$FREQUENT_VISIT_ERROR, $this->t['sys_processing'], []);
        }
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }

    /**
     * 批量新增
     * @Permission(action=sim_card_batch_add)
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function batchAddAction()
    {
        $params = (array)$this->request->get();
        Validation::validate($params, SimCardService::$validate_batch);
        $res = CompanyMobileService::getInstance()->uploadFileToImportCenter($this->user,ImportCenterEnums::TYPE_SIM_CARD_ADD);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }


    /**
     * 批量编辑
     * @Permission(action=sim_card_batch_edit)
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function batchEditAction()
    {
        $params = (array)$this->request->get();
        Validation::validate($params, SimCardService::$validate_batch);
        $res = CompanyMobileService::getInstance()->uploadFileToImportCenter($this->user,ImportCenterEnums::TYPE_SIM_CARD_EDIT);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }

    /**
     * @Permission(action=sim_card_batch_add)
     * 批量新增结果
     * @return Response|ResponseInterface
     */
    public function batchAddResultAction()
    {
        $res = ImportCenterService::getInstance()->getBarcodeImportResult(ImportCenterEnums::TYPE_SIM_CARD_ADD,
            $this->user['id']);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * @Permission(action=sim_card_batch_edit)
     * 批量修改结果
     * @return Response|ResponseInterface
     */
    public function batchEditResultAction()
    {
        $res = ImportCenterService::getInstance()->getBarcodeImportResult(ImportCenterEnums::TYPE_SIM_CARD_EDIT,
            $this->user['id']);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }


}
