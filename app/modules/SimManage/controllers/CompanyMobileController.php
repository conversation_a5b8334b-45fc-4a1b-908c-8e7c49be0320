<?php

namespace App\Modules\SimManage\Controllers;

use App\Library\ErrCode;
use App\Library\Enums\StaffInfoEnums;
use App\Library\Enums\ImportCenterEnums;
use App\Modules\User\Services\StaffService;
use App\Modules\SimManage\Services\BaseService;
use App\Modules\Common\Services\ImportCenterService;
use App\Modules\SimManage\Services\CompanyMobileService;
use App\Modules\SimManage\Models\CompanyMobileManageModel;
use Exception;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;


class CompanyMobileController extends BaseController
{

    /**
     * 查询员工信息
     * @Token
     * @return Response|ResponseInterface
     */
    public function searchStaffAction()
    {
        $params                   = $this->request->get();
        $params['state']          = 1;
        $params['hire_type_list'] = [
            StaffInfoEnums::HIRE_TYPE_PERMANENT_EMPLOYEE,
            StaffInfoEnums::HIRE_TYPE_MONTHLY,
            StaffInfoEnums::HIRE_TYPE_DAILY,
            StaffInfoEnums::HIRE_TYPE_HOURLY,
            StaffInfoEnums::HIRE_TYPE_INTERN,
        ];
        $result                   = StaffService::getInstance()->searchStaff($params);
        if ($result['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $result['message'], $result['data']);
        }
        return $this->returnJson($result['code'], $result['message']);
    }

    /**
     * @Token
     * 枚举信息
     * @return Response|ResponseInterface
     */
    public function getOptionsDefaultAction()
    {
        $res = CompanyMobileService::getInstance()->getOptionsDefault();
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }

    /**
     * @Permission(action=company_mobile_list)
     * 列表
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function listAction()
    {
        $params = (array)$this->request->get();
        Validation::validate($params, CompanyMobileService::$validate_list);
        $res = CompanyMobileService::getInstance()->getDataList($params);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }


    /**
     * 待激活/待更新序列号数量
     * @Permission(action=company_mobile_list)
     * @return Response|ResponseInterface
     */
    public function todoDataAction()
    {
        $res = CompanyMobileService::getInstance()->todoData(false);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);

    }


    /**
     * @Permission(action=company_mobile_export)
     * 导出
     * @return Response|ResponseInterface
     * @throws ValidationException|Exception
     */
    public function exportAction()
    {
        $params = (array)$this->request->get();
        // 加锁
        $lock_key = md5('sim_call_number_export_' . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return CompanyMobileService::getInstance()->export($params, $this->user);
        }, $lock_key, 5);

        if ($res === false) {
            return $this->returnJson(ErrCode::$FREQUENT_VISIT_ERROR, $this->t['sys_processing'], []);
        }
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }


    /**
     * 新增
     * @Permission(action=company_mobile_add)
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function addAction()
    {
        $params = (array)$this->request->get();
        $params = BaseService::handleParams($params, CompanyMobileService::$validate_add_not_must);
        Validation::validate($params, CompanyMobileService::$validate_add);
        $params = array_only($params,
            array_merge(CompanyMobileService::$validate_add_not_must, array_keys(CompanyMobileService::$validate_add)));
        $res    = CompanyMobileService::getInstance()->add($params, $this->user);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }

    /**
     * 编辑
     * @Permission(action=company_mobile_edit)
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function editAction()
    {
        $params = (array)$this->request->get();
        $params = BaseService::handleParams($params, CompanyMobileService::$validate_edit_not_must);
        Validation::validate($params, CompanyMobileService::$validate_edit);
        $params = array_only($params,
            array_merge(CompanyMobileService::$validate_edit_not_must, array_keys(CompanyMobileService::$validate_edit)));
        $res = CompanyMobileService::getInstance()->edit($params, $this->user);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }


    /**
     * 批量新增
     * @Permission(action=company_mobile_batch_add)
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function batchAddAction()
    {
        $params = (array)$this->request->get();
        Validation::validate($params, CompanyMobileService::$validate_batch);
        $res = CompanyMobileService::getInstance()->uploadFileToImportCenter($this->user,ImportCenterEnums::TYPE_SIM_COMPANY_MOBILE_ADD);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }

    /**
     * 批量编辑
     * @Permission(action=company_mobile_batch_edit)
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function batchEditAction()
    {
        $params = (array)$this->request->get();
        Validation::validate($params, CompanyMobileService::$validate_batch);
        $res = CompanyMobileService::getInstance()->uploadFileToImportCenter($this->user,ImportCenterEnums::TYPE_SIM_COMPANY_MOBILE_EDIT);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }

    /**
     * 批量新增结果
     * @Permission(action=company_mobile_batch_add)
     * @return Response|ResponseInterface
     */
    public function batchAddResultAction()
    {
        $res = ImportCenterService::getInstance()->getBarcodeImportResult(ImportCenterEnums::TYPE_SIM_COMPANY_MOBILE_ADD,
            $this->user['id']);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 批量修改结果
     * @Permission(action=company_mobile_batch_edit)
     * @return Response|ResponseInterface
     */
    public function batchEditResultAction()
    {
        $res = ImportCenterService::getInstance()->getBarcodeImportResult(ImportCenterEnums::TYPE_SIM_COMPANY_MOBILE_EDIT,
            $this->user['id']);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 激活
     * @Permission(action=company_mobile_active)
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function activeAction()
    {
        $params                        = (array)$this->request->get();
        $params['phone_number_status'] = CompanyMobileManageModel::STATUS_ACTIVE;
        Validation::validate($params, CompanyMobileService::$validate_update_status);
        $res = CompanyMobileService::getInstance()->updateState($params, $this->user);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }

    /**
     * 停用
     * @Permission(action=company_mobile_stop)
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function stopAction()
    {
        $params                        = (array)$this->request->get();
        $params['phone_number_status'] = CompanyMobileManageModel::STATUS_NOT_ENABLED;
        Validation::validate($params, CompanyMobileService::$validate_update_status);
        $res = CompanyMobileService::getInstance()->updateState($params, $this->user);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }

    /**
     * 详情数据
     * @Permission(action=company_mobile_view)
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function detailAction()
    {
        $params = (array)$this->request->get();
        Validation::validate($params, CompanyMobileService::$validate_detail);
        $res = CompanyMobileService::getInstance()->detail($params);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }

    /**
     * 操作记录列表
     * @Permission(action=company_mobile_view_log)
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function operationListAction()
    {
        $params = (array)$this->request->get();
        Validation::validate($params, CompanyMobileService::$validate_operation_list);
        $res = CompanyMobileService::getInstance()->getOperationDataList($params);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }

    /**
     * 变更使用人
     * @Permission(action=company_mobile_change_staff)
     * @api https://yapi.flashexpress.pub/project/133/interface/api/88274
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function changeStaffAction()
    {
        $params = (array)$this->request->get();
        Validation::validate($params, CompanyMobileService::$validate_change_staff);
        $res = CompanyMobileService::getInstance()->changeStaff($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }
}
