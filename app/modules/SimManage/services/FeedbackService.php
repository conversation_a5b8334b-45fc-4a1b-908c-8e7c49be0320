<?php

namespace App\Modules\SimManage\Services;


use App\Library\ApiClient;
use App\Library\ErrCode;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Models\backyard\HrStaffInfoModel;
use App\Library\Enums\DownloadCenterEnum;
use App\Modules\Common\Services\StaffLangService;
use app\modules\SimManage\models\CompanyMobileFeedbackModel;
use App\Modules\Setting\Services\BaseService;
use App\Modules\Common\Services\EnumsService;
use App\Modules\SimManage\Models\SimCardModel;
use App\Modules\Common\Services\DownloadCenterService;
use App\Modules\Organization\Services\DepartmentService;
use App\Modules\SimManage\Models\CompanyMobileManageModel;
use App\Modules\SimManage\Models\CompanyMobileManageLogModel;
use GuzzleHttp\Exception\GuzzleException;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use Exception;

class FeedbackService extends BaseService
{



    /**
     * 操作类型
     * @var string[]
     */
    public static $validate_operator_type = [
        'id'              => 'Required|IntGt:0',
        'operator_type'   => 'Required|IntIn:1,2',
        'operator_remark' => 'Required|StrLenGeLe:1,500',
    ];

    //列表校验
    public static $validate_list = [
        'pageSize'  => 'Required|IntGt:0',  //每页条数
        'pageNum'   => 'Required|IntGt:0',  //页码
        'state'     => 'Required|IntIn:1,2', //状态
        'store_id'  => 'ArrLenGeLe:0,50',
    ];

    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    /**
     * @return FeedbackService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 操作类型
     * @return array
     */
    protected function getFeedbackCategory(): array
    {
        $data[] = ['value' => '1', 'label' => self::$t->_('feedback_category_1')];
        $data[] = ['value' => '2', 'label' => self::$t->_('feedback_category_2')];
        $data[] = ['value' => '99', 'label' => self::$t->_('feedback_category_99')];
        return $data ?? [];
    }


    /**
     * 筛选静态枚举
     * @return array[]
     */
    public function getOptionsDefault(): array
    {
        return [
            'feedback_category' => $this->getFeedbackCategory(),
        ];
    }

    /**
     * 列表查询
     * @param array $params 查询条件
     * @return array
     */
    public function getDataList(array $params): array
    {
        $page_size = $params['pageSize'];
        $page_num  = $params['pageNum'];
        $code      = ErrCode::$SUCCESS;
        $message   = '';
        $data      = [
            'items'      => [],
            'pagination' => [
                'current_page' => intval($page_num),
                'per_page'     => intval($page_size),
                'total_count'  => 0,
            ],
        ];
        $count     = $this->getCount($params);
        if ($count > 0) {
            $items = $this->getList($params);
        }
        $data['items']                     = $items ?? [];
        $data['pagination']['total_count'] = $count;
        return $data;
    }


    /**
     * @return int
     */
    public function todoData(): int
    {
        return (int)CompanyMobileFeedbackModel::count([
            'conditions' => 'state = :state: ',
            'bind'       => [
                'state'            => CompanyMobileFeedbackModel::STATE_PENDING,
            ],
        ]);
    }


    /**
     * @param $params
     * @return array
     * @throws BusinessException
     * @throws GuzzleException
     * @throws ValidationException
     */
    public function export($params, $user): array
    {
        $result = ['export_method' => 2, 'file_url' => ''];

        $count = $this->getCount($params);
        $max_limit = DownloadCenterEnum::DEFAUL_PER_PAGE_SIZE;
        if (get_runtime_env() == 'dev') {
            $max_limit = 10;
        }
        if ($count <= $max_limit) {
            $result['file_url']      = $this->downloadExcelData($params);
            $result['export_method'] = 1;
        } else {
            $res = DownloadCenterService::getInstance()->addDownloadCenter($user['id'],
                DownloadCenterEnum::COMPANY_MOBILE_FEEDBACK_EXPORT, $params);
            if ($res['code'] == ErrCode::$SUCCESS) {
                return $result;
            }
            throw new ValidationException($res['message']);
        }
        return $result;
    }


    /**
     * @param $params
     * @return mixed
     * @throws ValidationException
     * @throws BusinessException
     * @throws GuzzleException
     */
    public function downloadExcelData($params)
    {
        $params['pageNum']  = 1;
        $params['pageSize'] = 1000;
        $excel_data         = [];
        while (true) {
            $list = $this->getList($params);
            if (empty($list)) {
                break;
            }

            foreach ($list as $item) {
                $excel_data[] = [
                    $item['phone_number'],
                    $item['staff_info_id'],
                    $item['staff_name'],
                    $item['staff_state_text'],
                    $item['store_name'],
                    $item['department_name'],
                    $item['job_title_name'],
                    $item['category_text'],
                    $item['reason'],
                    $item['phone_number_status_text'],
                    $item['sim_card_number'],
                    $item['created_at'],
                    $item['last_operator'],
                    $item['last_operator_name'],
                    $item['operator_type_text'],
                    $item['operator_remark'],
                    $item['operator_at'],
                ];
            }
            $params['pageNum']++;
        }
        $excel_header = [
            static::$t->_('phone_number'),                                               //手机号码
            static::$t->_('employee_id'),                                                //工号
            static::$t->_('employee_name'),                                              //姓名
            static::$t->_('working_state'),                                              //在职状态
            static::$t->_('branch_name'),                                                //网点
            static::$t->_('department'),                                                 //部门
            static::$t->_('position'),                                                   //职位
            static::$t->_('classification_of_causes'),                                   //原因分类
            static::$t->_('reasons_for_abnormality'),                                    //异常原因
            static::$t->_('phone_number_status'),                                        //手机号状态
            static::$t->_('sim_card_number'),                                            //SIM卡序列号
            static::$t->_('feedback_time'),                                              //反馈日期
            static::$t->_('latest_operator_id'),                                         //最后操作人工号
            static::$t->_('latest_operator_name'),                                       //最后操作人姓名
            static::$t->_('operation_type'),                                             //操作类型
            static::$t->_('operation_remark'),                                           //操作备注
            static::$t->_('operation_time'),                                             //操作时间
        ];
        $file_name    = 'SIM_feedback_number_' . date('YmdHis') . '.xlsx';
        $file_url     = $this->exportExcel($excel_header, $excel_data, $file_name)['data'];
        if (empty($file_url)) {
            throw new ValidationException(static::$t->_('download_fail_error'), ErrCode::$VALIDATE_ERROR);
        }
        return $file_url;
    }

    /**
     * 处理筛选条件
     * @param $builder
     * @param $params
     * @return void
     */
    protected function makeBuilder(&$builder, $params)
    {
        $builder->from(['feedback' => CompanyMobileFeedbackModel::class]);
        $builder->leftjoin(HrStaffInfoModel::class, 'feedback.staff_info_id = staff.staff_info_id', 'staff');
        $builder->leftjoin(CompanyMobileManageModel::class, 'feedback.phone_number = mobile.phone_number', 'mobile');
        $builder->leftjoin(SimCardModel::class, 'mobile.sim_card_id = sim.id', 'sim');
        //处理状态
        if (!empty($params['state'])) {
            $builder->andWhere('feedback.state = :state:', ['state' => $params['state']]);
        }
        //手机号码
        if (!empty($params['phone_number'])) {
            $builder->andWhere('feedback.phone_number = :phone_number:', ['phone_number' => $params['phone_number']]);
        }
        //工号
        if (!empty($params['staff_info_id'])) {
            $builder->andWhere('feedback.staff_info_id = :staff_info_id:', ['staff_info_id' => $params['staff_info_id']]);
        }

        if (!empty($params['staff_name'])) {
            $builder->andWhere('staff.name like :staff_name:', ['staff_name' => '%' . $params['staff_name'] . '%']);
        }
        if (!empty($params['department_id'])) {
            //获取子部门
            $department_service = new DepartmentService();
            $department_ids     = $department_service->getChildrenListByDepartmentIdV2($params['department_id'], true);
            $department_ids[]   = $params['department_id'];
            $builder->inWhere('staff.node_department_id', $department_ids);
        }
        if (!empty($params['store_id']) && is_array($params['store_id'])) {
            $builder->inWhere('staff.sys_store_id', $params['store_id']);
        }

        if (!empty($params['job_title'])) {
            $builder->andWhere('staff.job_title = :job_title:', ['job_title' => $params['job_title']]);
        }

        //反馈日期-开始
        if (!empty($params['feedback_date_start'])) {
            $builder->andWhere('feedback.created_at >= :feedback_date_start:', ['feedback_date_start' => zero_time_zone($params['feedback_date_start'])]);
        }
        //反馈日期-结束
        if (!empty($params['feedback_date_end'])) {
            $builder->andWhere('feedback.created_at <= :feedback_date_end:', ['feedback_date_end' =>zero_time_zone(date('Y-m-d', strtotime($params['feedback_date_end'] .' + 1 day')))]);
        }
        //原因分类
        if (!empty($params['category'])) {
            $builder->andWhere('feedback.category = :category:', ['category' => $params['category']]);
        }

    }

    /**
     * 总数
     * @param $params
     * @return int
     */
    public function getCount($params): int
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(1) as total');
        $this->makeBuilder($builder, $params);
        return intval($builder->getQuery()->getSingleResult()->total);
    }

    /**
     * 获取列表数据
     * @param array $params
     * @return array
     */
    public function getList(array $params): array
    {
        $page_num  = $params['pageNum'];
        $page_size = $params['pageSize'];
        $offset    = $page_size * ($page_num - GlobalEnums::DEFAULT_PAGE_NUM);
        $builder   = $this->modelsManager->createBuilder();
        $columns = [
            'feedback.id',
            'feedback.phone_number',
            'feedback.staff_info_id',
            'staff.name as staff_name',
            'staff.state as staff_state',
            'staff.wait_leave_state',
            'staff.sys_store_id',
            'staff.node_department_id',
            'staff.job_title',
            'feedback.category',
            'feedback.reason',
            'mobile.status as phone_number_status',
            'sim.number as sim_card_number',
            'feedback.created_at',
            'feedback.last_operator',
            'feedback.last_operator_name',
            'feedback.operator_type',
            'feedback.operator_remark',
            'feedback.operator_at',
        ];
        $builder->columns($columns);
        $this->makeBuilder($builder, $params);
        $builder->limit($page_size, $offset);
        if ($params['state'] == CompanyMobileFeedbackModel::STATE_PROCESSED) {
            $builder->orderby('feedback.updated_at desc');
        } else {
            $builder->orderby('feedback.id asc');
        }

        $list = $builder->getQuery()->execute()->toArray();
        if (empty($list)) {
            return [];
        }

        foreach ($list as &$item) {
            $item['department_name']  = '';
            $item['job_title_name']   = '';
            $item['store_name']       = '';
            $item['staff_state_text'] = '';
            if ($item['staff_info_id']) {
                $item['department_name'] = $this->showDepartmentName($item['node_department_id']);
                $item['job_title_name']  = $this->showJobTitleName($item['job_title']);
                $item['store_name']      = $this->showStoreName($item['sys_store_id']);
                if ($item['staff_state'] == StaffInfoEnums::STAFF_STATE_IN && $item['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES) {
                    $item['staff_state'] = StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE;
                }
                $item['staff_state_text'] = static::$t->_(StaffInfoEnums::$staff_state[$item['staff_state']]);
            } else {
                $item['staff_info_id'] = '';
            }
            $item['phone_number_status_text'] = $item['phone_number_status'] ? self::$t->_('phone_number_status_' . $item['phone_number_status']) : '';                //手机号状态
            $item['operator_type_text']       = $item['operator_type'] ? self::$t->_('feedback_operation_type_' . $item['operator_type']) : '';                         //操作类型
            $item['category_text']            = self::$t->_('feedback_category_' . $item['category']);                                                                 //原因分类
            $item['operator_at']              = show_time_zone($item['operator_at']);
            $item['created_at']               = show_time_zone($item['created_at']);
        }
        return $list;
    }


    /**
     * @param $file_url
     * @param $t_t
     * @return array
     * @throws ValidationException
     */
    protected function getBatchExcelData($file_url): array
    {
        $excel_data = get_oss_file_excel_data($file_url, [\Vtiful\Kernel\Excel::TYPE_STRING,\Vtiful\Kernel\Excel::TYPE_STRING]);
        if (empty($excel_data)) {
            throw new ValidationException(self::$t->_('20211_error_message_004'));
        }
        $title = [$excel_data[0], $excel_data[1]];
        unset($excel_data[0], $excel_data[1]);
        return [$title, $excel_data];
    }


    /**
     * @param $file_url
     * @param $user
     * @return string[]
     * @throws BusinessException
     * @throws GuzzleException
     * @throws ValidationException
     */
    public function batchEdit($file_url, $user): array
    {
        $result = [
            'result_url'  => '',
            'success_num' => 0,
            'fail_num'    => 0,
        ];
        [$title, $excel_data] = $this->getBatchExcelData($file_url);
        $this->logger->info(['batchEdit' => $excel_data]);

        foreach ($excel_data as $key => $excel_item) {
            if (empty($excel_item[0])) {
                continue;
            }

            $data_item = [];
            try {
                //手机号码只能10个数字
                if (!is_numeric($excel_item[0]) || strlen($excel_item[0]) != 10) {
                    throw new ValidationException(self::$t->_('20211_error_message_006'));
                }
                $companyMobileCheck = CompanyMobileFeedbackModel::findFirst([
                    'conditions' => 'phone_number = :phone_number: and state = :state:',
                    'bind'       => ['phone_number' => $excel_item[0],'state'=>CompanyMobileFeedbackModel::STATE_PENDING],
                ]);
                if (empty($companyMobileCheck)) {
                    //失败，该手机号不是待处理的数据，请检查
                    throw new ValidationException(self::$t->_('20951_error_message_002'));
                }

                if (empty($excel_item[1]) || empty($excel_item[2])) {
                    //失败，手机号，操作类型，操作备注必填，请检查
                    throw new ValidationException(self::$t->_('20951_error_message_003'));
                }

                $data_item['id'] = $companyMobileCheck->id;
                //操作类型
                if (!in_array($excel_item[1][0], [
                    CompanyMobileFeedbackModel::OPERATOR_TYPE_STOP,
                    CompanyMobileFeedbackModel::OPERATOR_TYPE_NORMAL,
                ])) {
                    //失败，操作类型只能填写停用并清空BY号码或者标记号码正常
                    throw new ValidationException(self::$t->_('20951_error_message_004'));
                }
                //操作类型
                $data_item['operator_type'] = $excel_item[1][0];
                //操作备注
                $data_item['operator_remark'] = mb_substr($excel_item[2],0,500);

                $excel_data[$key][3] = self::$t->_('excel_result_success');
                $this->handlePhoneNumber($data_item, $user);
                $result['success_num']++;
            } catch (Exception $e) {
                $result['fail_num']++;
                $excel_data[$key][3] = $e->getMessage();
            }
        }
        $header               = array_shift($title);
        $excel_data           = array_merge(array_values($title), $excel_data);
        $file_name            = 'batch_feedback_edit_result_' . date('YmdHis') . '.xlsx';
        $result['result_url'] = $this->exportExcel($header, $excel_data, $file_name)['data'];
        return $result;
    }


    /**
     * 处理数据
     * @param $params
     * @param $user
     * @return true
     * @throws ValidationException
     */
    public function handlePhoneNumber($params, $user): bool
    {
        $db = $this->getDI()->get('db_backyard');
        $db->begin();
        $update_staff_data             = [];
        $update_staff_data['operator'] = $user['id'];

        try {
            $feedbackModel = CompanyMobileFeedbackModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $params['id']],
                'for_update' => true,
            ]);

            if (empty($feedbackModel)) {
                throw new ValidationException(self::$t->_('empty_data'));
            }

            if ($feedbackModel->state != CompanyMobileFeedbackModel::STATE_PENDING) {
                throw new ValidationException(self::$t->_('refresh_and_retry'));
            }

            $companyMobileModel = CompanyMobileManageModel::findFirst([
                'conditions' => 'phone_number = :phone_number:',
                'bind'       => ['phone_number' => $feedbackModel->phone_number],
            ]);

            if (!empty($companyMobileModel)) {
                $before_status = $companyMobileModel->status;
            } else {
                if ($params['operator_type'] != CompanyMobileFeedbackModel::OPERATOR_TYPE_STOP) {
                    throw new ValidationException(self::$t->_('20951_error_message_005'));
                }
            }

            $staff_language = StaffLangService::getInstance()->getLatestMobileLang($feedbackModel->staff_info_id);
            $s_t                          = BaseService::getTranslation($staff_language);

            $staffInfoModel = HrStaffInfoModel::findFirst([
                'conditions' => 'staff_info_id = :staff_info_id:',
                'bind'       => ['staff_info_id' =>$feedbackModel->staff_info_id],
            ]);
            if (empty($staffInfoModel)) {
                throw new ValidationException(self::$t->_('empty_data'));
            }
            $log_type = 0;
            //停用并清空BY号码
            $before_staff_info_id = $after_staff_info_id = 0;
            if ($params['operator_type'] == CompanyMobileFeedbackModel::OPERATOR_TYPE_STOP) {
                if (!empty($companyMobileModel)) {
                    if ($companyMobileModel->sim_card_id) {
                        $simCardModel = SimCardModel::findFirst($companyMobileModel->sim_card_id);
                        if ($simCardModel) {
                            $simCardModel->status = SimCardModel::STATUS_ABANDON;
                            $simCardModel->save();
                        }
                    }
                    $before_staff_info_id              = $companyMobileModel->staff_info_id;
                    $companyMobileModel->status        = CompanyMobileManageModel::STATUS_NOT_ENABLED;
                    $companyMobileModel->staff_info_id = null;
                    $companyMobileModel->sim_card_id   = 0;
                }
                $log_type = CompanyMobileManageLogModel::TYPE_STAFF_STOP_BY;
                //hris清空企业号码
                $update_staff_data['mobile_company'] = '';

                //发送消息
                $kit_param                    = [];
                $kit_param['staff_users']     = [['id' => $feedbackModel->staff_info_id]];
                $kit_param['message_title']   = $s_t->_('20951_feedback_notice_title');
                $kit_param['message_content'] = $s_t->_('20951_feedback_notice_content_stop',
                    [
                        'phone_number' => $feedbackModel->phone_number,
                        'staff_id'     => $feedbackModel->staff_info_id,
                        'staff_name'   => $staffInfoModel->name,
                    ]);
                $kit_param['category'] = '-1';
                $bi_rpc                       = (new ApiClient('hcm_rpc', '', 'add_kit_message'));
                $bi_rpc->setParams([$kit_param]);
                $res2 = $bi_rpc->execute();

            }

            //标记号码正常
            if ($params['operator_type'] == CompanyMobileFeedbackModel::OPERATOR_TYPE_NORMAL) {
                !empty($companyMobileModel) && $companyMobileModel->status = CompanyMobileManageModel::STATUS_ACTIVE;
                $log_type = CompanyMobileManageLogModel::TYPE_STAFF_NORMAL;
                $kit_param                    = [];
                $kit_param['staff_users']     = [['id' => $feedbackModel->staff_info_id]];
                $kit_param['message_title']   = $s_t->_('20951_feedback_notice_title');
                $kit_param['message_content'] = $s_t->_('20951_feedback_notice_content_normal',
                    [
                        'phone_number' => $feedbackModel->phone_number,
                        'staff_id'     => $feedbackModel->staff_info_id,
                        'staff_name'   => $staffInfoModel->name,
                    ]);
                $kit_param['category'] = '-1';
                $bi_rpc                       = (new ApiClient('hcm_rpc', '', 'add_kit_message'));
                $bi_rpc->setParams([$kit_param]);
                $res = $bi_rpc->execute();
            }

            if (!empty($companyMobileModel)) {
                $companyMobileModel->save();
                CompanyMobileService::getInstance()->addLog($companyMobileModel->id,
                    $log_type, $user, ['phone_number_status' => $before_status,'staff_info_id'=>$before_staff_info_id],
                    ['phone_number_status' => $companyMobileModel->status,'staff_info_id'=>$after_staff_info_id]);
            }
            $feedbackModel->state              = CompanyMobileFeedbackModel::STATE_PROCESSED;
            $feedbackModel->operator_type      = $params['operator_type'];
            $feedbackModel->operator_remark    = $params['operator_remark'];
            $feedbackModel->last_operator      = $user['id'];
            $feedbackModel->last_operator_name = $user['name'];
            $feedbackModel->operator_at        = gmdate("Y-m-d H:i:s");
            $feedbackModel->updated_at         = gmdate("Y-m-d H:i:s");
            $feedbackModel->save();

            $db->commit();
            //异步更新员工信息
            CompanyMobileService::getInstance()->syncStaffInfo($feedbackModel->staff_info_id,$feedbackModel->staff_info_id,$update_staff_data);
        }catch (Exception $e){
            $db->rollback();
            throw $e;
        }

        return true;
    }




}
