<?php

namespace App\Modules\SimManage\Services;


use App\Library\Enums\ImportCenterEnums;
use App\Library\ErrCode;
use App\Library\ApiClient;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\OssHelper;
use App\Library\RocketMQ;
use App\Models\backyard\HrStaffInfoModel;
use App\Library\Enums\DownloadCenterEnum;
use App\Modules\Common\Services\ImportCenterService;
use App\Modules\SimManage\Models\SimCardLogModel;
use App\Modules\User\Services\UserService;
use App\Modules\Setting\Services\BaseService;
use App\Modules\Common\Services\EnumsService;
use App\Modules\SimManage\Models\SimCardModel;
use App\Modules\Common\Services\StaffLangService;
use App\Modules\Common\Services\DownloadCenterService;
use App\Modules\Organization\Services\DepartmentService;
use App\Modules\SimManage\Models\CompanyMobileManageModel;
use App\Modules\SimManage\Models\CompanyMobileManageLogModel;
use App\Repository\HrStaffRepository;
use Exception;
use GuzzleHttp\Exception\GuzzleException;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;

class CompanyMobileService extends BaseService
{

    public static $validate_detail = [
        'id' => 'Required|IntGt:0',
    ];

    //非必填
    public static $not_must_params = [
        'staff_info_id',
        'pageSize',
        'pageNum',
    ];

    //激活停用
    public static $validate_update_status = [
        'id'                  => 'Required|IntGt:0',
        'phone_number_status' => 'Required|IntIn:2,3',
    ];

    //列表校验
    public static $validate_list = [
        'pageSize'            => 'Required|IntGt:0', //每页条数
        'pageNum'             => 'Required|IntGt:0', //页码
        'store_id'            => 'ArrLenGeLe:0,50',
        'job_title'           => 'ArrLenGeLe:0,50',
        'staff_state'         => 'ArrLenGeLe:0,4',
        'phone_number_status' => 'ArrLenGeLe:0,4',
    ];


    //添加
    public static $validate_add = [
        'phone_number'        => 'Required|StrLen:10',
        'sim_card_number'     => 'StrLenGeLe:13,18',
        'phone_number_status' => 'Required|Int',
        'staff_info_id'       => 'Int',
        'package'             => 'Required|Int',
        'sim_company'         => 'Required|IntIn:' . SimCardModel::SIM_COMPANY_AIS . ',' . SimCardModel::SIM_COMPANY_TRUE,
    ];
    //添加-非必填
    public static $validate_add_not_must = [
        'sim_card_number',
        'staff_info_id',
        'remark',
    ];

    //编辑
    public static $validate_edit = [
        'id'      => 'Required|IntGt:0|>>>:param error[id]',
        'package' => 'Required|Int',
        'remark'  => 'Required|Str',
        'sim_company' => 'Required|IntIn:' . SimCardModel::SIM_COMPANY_AIS . ',' . SimCardModel::SIM_COMPANY_TRUE,
    ];
    //编辑-非必填
    public static $validate_edit_not_must = [
        'sim_card_number',
        'update_sim_status',
    ];

    //批量操作
    public static $validate_batch = [
    ];

    //列表校验
    public static  $validate_operation_list = [
        'pageSize' => 'IntGt:0', //每页条数
        'pageNum'  => 'IntGt:0', //页码
        'id'       => 'IntGt:0', //公司号码表主键
    ];

    //变更使用人
    public static $validate_change_staff = [
        'id' => 'Required|IntGt:0', //公司号码表主键
        'staff_info_id' => 'Required|Int',
        'remark' => 'Required|StrLenGeLe:1,500',
    ];
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    /**
     * @return CompanyMobileService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 在职状态
     * @return array
     */
    protected function getStaffStateEnum(): array
    {
        foreach (StaffInfoEnums::$staff_state as $key => $value) {
            $data[] = ['value' => (string)$key, 'label' => self::$t->_($value)];
        }
        return $data ?? [];
    }

    /**
     * 手机套餐
     * @return array
     */
    protected function getMobilePackageEnum(): array
    {
        $mobile_company_package = EnumsService::getInstance()->getSettingEnvValueIds('phone_number_package');
        foreach ($mobile_company_package as $package) {
            $data[] = ['value' => (string)$package, 'label' => self::$t->_('phone_number_package_' . $package)];
        }
        return $data ?? [];
    }

    /**
     * 手机号状态
     * @param bool $is_add
     * @return array
     */
    protected function phoneNumberStatusEnum(bool $is_add = false): array
    {
        $status_list = CompanyMobileManageModel::STATUS_LIST;
        foreach ($status_list as $status) {
            $is_can_select = !in_array($status, [
                CompanyMobileManageModel::STATUS_TERMINATE_CONTRACT,
                CompanyMobileManageModel::STATUS_FEEDBACK_HANDLING,
            ]);

            $data[] = [
                'value'         => (string)$status,
                'label'         => self::$t->_('phone_number_status_' . $status),
                'is_can_select' => $is_can_select,
            ];
        }
        return $data ?? [];
    }

    /**
     * sim卡运营商
     * @return array
     */
    protected function simCompanyEnum(): array
    {
        $status_list = SimCardModel::SIM_COMPANY_LIST;
        foreach ($status_list as $key => $company) {
            $data[] = ['value' => (string)$key, 'label' => $company];
        }
        return $data ?? [];
    }

    /**
     * 手机号码更新SIM序列号状态
     * @return array
     */
    protected function updateSimStatusEnum(): array
    {
        $status_list = CompanyMobileManageModel::UPDATE_SIM_STATUS_LIST;
        foreach ($status_list as $status) {
            $item = [
                'value' => (string)$status,
                'label' => self::$t->_('update_sim_status_' . $status),
            ];
            $item['is_can_select'] = $status != CompanyMobileManageModel::UPDATE_SIM_STATUS_EMPTY;
            $data[] = $item;
        }
        return $data ?? [];
    }

    /**
     * 操作类型
     * @return array
     */
    protected function operationTypeEnumEnum(): array
    {
        $type_list = CompanyMobileManageLogModel::TYPE_LIST;
        foreach ($type_list as $type) {
            $data[] = ['value' => (string)$type, 'label' => self::$t->_('sim_operation_type_' . $type)];
        }
        return $data ?? [];
    }
    /**
     * 反馈原因枚举
     * @return array
     */
    protected function getFeedbackCategory(): array
    {
        $data[] = ['value' => '1', 'label' => self::$t->_('feedback_category_1')];
        $data[] = ['value' => '2', 'label' => self::$t->_('feedback_category_2')];
        $data[] = ['value' => '99', 'label' => self::$t->_('feedback_category_99')];
        return $data ?? [];
    }

    /**
     * 筛选静态枚举
     * @return array[]
     */
    public function getOptionsDefault(): array
    {
        return [
            'staff_state_enum'                  => $this->getStaffStateEnum(),
            'package_enum'                      => $this->getMobilePackageEnum(),
            'phone_number_status_enum'          => $this->phoneNumberStatusEnum(),
            'phone_number_status_when_add_enum' => $this->phoneNumberStatusEnum(true),
            'sim_company_enum'                  => $this->simCompanyEnum(),
            'update_sim_status_enum'            => $this->updateSimStatusEnum(),
            'operation_type_enum'               => $this->operationTypeEnumEnum(),
            'feedback_category'                 => $this->getFeedbackCategory(),
        ];
    }


    /**
     * 列表查询
     * @param array $params 查询条件
     * @return array
     */
    public function getDataList(array $params): array
    {
        $page_size = $params['pageSize'];
        $page_num  = $params['pageNum'];
        $code      = ErrCode::$SUCCESS;
        $message   = '';
        $data      = [
            'items'      => [],
            'pagination' => [
                'current_page' => intval($page_num),
                'per_page'     => intval($page_size),
                'total_count'  => 0,
            ],
        ];
        $count     = $this->getCount($params);
        if ($count > 0) {
            $items = $this->getList($params);
        }
        $data['items']                     = $items ?? [];
        $data['pagination']['total_count'] = $count;
        return $data;
    }

    /**
     * 待激活/待更新序列号
     * @param bool $is_red_dot
     * @return array|int
     */
    public function todoData(bool $is_red_dot = false)
    {
        if ($is_red_dot) {
            return (int)CompanyMobileManageModel::count([
                'conditions' => 'update_sim_status = :update_sim_status: or status = :status: ',
                'bind'       => [
                    'update_sim_status' => CompanyMobileManageModel::UPDATE_SIM_STATUS_WAITING,
                    'status'            => CompanyMobileManageModel::STATUS_WAIT_ACTIVE,
                ],
            ]);
        }

        $result['to_be_activated'] = (int)CompanyMobileManageModel::count([
            'conditions' => 'status = :status:',
            'bind'       => [
                'status' => CompanyMobileManageModel::STATUS_WAIT_ACTIVE,
            ],
        ]);
        $result['to_be_updated']   = (int)CompanyMobileManageModel::count([
            'conditions' => 'update_sim_status = :update_sim_status: ',
            'bind'       => [
                'update_sim_status' => CompanyMobileManageModel::UPDATE_SIM_STATUS_WAITING,
            ],
        ]);
        return $result;
    }




    /**
     * @param $params
     * @return array
     * @throws BusinessException
     * @throws GuzzleException
     * @throws ValidationException
     */
    public function export($params, $user): array
    {
        $result = ['export_method' => 2, 'file_url' => ''];

        $count = $this->getCount($params);
        $max_limit = DownloadCenterEnum::DEFAUL_PER_PAGE_SIZE;
        if (get_runtime_env() == 'dev') {
            $max_limit = 20;
        }
        if ($count <= $max_limit) {
            $result['file_url']      = $this->downloadExcelData($params);
            $result['export_method'] = 1;
        } else {
            $res = DownloadCenterService::getInstance()->addDownloadCenter($user['id'],
                DownloadCenterEnum::COMPANY_MOBILE_EXPORT, $params);
            if ($res['code'] == ErrCode::$SUCCESS) {
                return $result;
            }
            throw new ValidationException($res['message']);
        }
        return $result;
    }


    /**
     * @param $params
     * @return mixed
     * @throws ValidationException
     * @throws BusinessException
     * @throws GuzzleException
     */
    public function downloadExcelData($params)
    {
        $params['pageNum']  = 1;
        $params['pageSize'] = 1000;
        $excel_data         = [];
        while (true) {
            $list = $this->getList($params);
            if (empty($list)) {
                break;
            }

            foreach ($list as $item) {
                $excel_data[] = [
                    $item['phone_number'],
                    $item['sim_card_number'],
                    $item['phone_number_status_text'],
                    $item['package_text'],
                    SimCardModel::SIM_COMPANY_LIST[$item['sim_company']] ?? '',
                    $item['staff_info_id'],
                    $item['staff_name'],
                    $item['job_title_name'],
                    $item['staff_state_text'],
                    $item['leave_date'],
                    $item['store_name'],
                    $item['department_name'],
                    $item['operator_text'],
                    $item['updated_at'],
                    $item['remark'],

                ];
            }
            $params['pageNum']++;
        }

        $excel_header = [
            static::$t->_('phone_number'),                               // 手机号码
            static::$t->_('sim_card_number'),                            // SIM卡序列号
            static::$t->_('phone_number_status'),                        // 手机号状态
            static::$t->_('phone_number_package'),                       //手机号套餐
            static::$t->_('sim_company'),                                //所属运营商
            static::$t->_('employee_id'),                                //工号
            static::$t->_('employee_name'),                              //姓名
            static::$t->_('position'),                                   //职位
            static::$t->_('working_state'),                              //在职状态
            static::$t->_('date_of_resignation_pending_resignation'),    // 离职/待离职日期
            static::$t->_('branch_name'),                                // 网点
            static::$t->_('department'),                                 //部门
            static::$t->_('latest_operator'),                            //最新操作人
            static::$t->_('latest_operation_time'),                      //最新操作时间
            static::$t->_('remark'),                                     //备注

        ];
        $file_name    = 'SIM_call_number_' . date('YmdHis') . '.xlsx';
        $file_url     = $this->exportExcel($excel_header, $excel_data, $file_name)['data'];
        if (empty($file_url)) {
            throw new ValidationException(static::$t->_('download_fail_error'), ErrCode::$VALIDATE_ERROR);
        }
        return $file_url;
    }

    /**
     * 处理筛选条件
     * @param $builder
     * @param $params
     * @return void
     */
    protected function makeBuilder(&$builder, $params)
    {
        $builder->from(['mobile' => CompanyMobileManageModel::class]);
        $builder->leftjoin(HrStaffInfoModel::class, 'mobile.staff_info_id = staff.staff_info_id', 'staff');
        $builder->leftjoin(SimCardModel::class, 'mobile.sim_card_id = sim.id', 'sim');
        //SIM卡序列号
        if (!empty($params['sim_card_number'])) {
            $builder->andWhere('sim.number = :sim_card_number:', ['sim_card_number' => $params['sim_card_number']]);
        }
        //手机号码
        if (!empty($params['phone_number'])) {
            $builder->andWhere('mobile.phone_number = :phone_number:', ['phone_number' => $params['phone_number']]);
        }
        if (!empty($params['staff_info_id'])) {
            $builder->andWhere('mobile.staff_info_id = :staff_info_id:', ['staff_info_id' => $params['staff_info_id']]);
        }
        if (!empty($params['staff_name'])) {
            $builder->andWhere('staff.name like :staff_name:', ['staff_name' => '%' . $params['staff_name'] . '%']);
        }
        if (!empty($params['department_id'])) {
            //获取子部门
            $department_service = new DepartmentService();
            $department_ids     = $department_service->getChildrenListByDepartmentIdV2($params['department_id'], true);
            $department_ids[]   = $params['department_id'];
            $builder->inWhere('staff.node_department_id', $department_ids);
        }
        if (!empty($params['store_id']) && is_array($params['store_id'])) {
            $builder->inWhere('staff.sys_store_id', $params['store_id']);
        }
        if (!empty($params['job_title']) && is_array($params['job_title'])) {
            $builder->inWhere('staff.job_title', $params['job_title']);
        }
        //在职状态
        if (!empty($params['staff_state']) && is_array($params['staff_state'])) {
            if (in_array(StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE, $params['staff_state'])) {
                $builder->andWhere(
                    'staff.state in ({staff_state:array}) OR staff.wait_leave_state = 1',
                    ['staff_state' => $params['staff_state']]
                );
            } else {
                $builder->andwhere('staff.state in ({staff_state:array}) AND staff.wait_leave_state = 0',
                    ['staff_state' => $params['staff_state']]);
            }
        }
        //待离职/离职日期-开始
        if (!empty($params['leave_date_start'])) {
            $builder->andWhere('staff.leave_date >= :leave_date_start:',
                ['leave_date_start' => $params['leave_date_start']]);
        }
        //待离职/离职日期-结束
        if (!empty($params['leave_date_end'])) {
            $builder->andWhere('staff.leave_date <= :leave_date_end:', ['leave_date_end' => $params['leave_date_end']]);
        }

        //手机号套餐
        if (!empty($params['package'])) {
            $builder->andWhere('mobile.package = :package:', ['package' => $params['package']]);
        }
        //手机号状态
        if (!empty($params['phone_number_status']) && is_array($params['phone_number_status'])) {
            $builder->inWhere('mobile.status', $params['phone_number_status']);
        }
        //所属运营商 - V21255筛选SIM手机号对应的运营商
        if (!empty($params['sim_company'])) {
            $builder->andWhere('mobile.sim_company = :sim_company:', ['sim_company' => $params['sim_company']]);
        }
        //手机号码更新SIM序列号
        if (!empty($params['update_sim_status'])) {
            $builder->andWhere('mobile.update_sim_status = :update_sim_status:',
                ['update_sim_status' => $params['update_sim_status']]);
        }
    }

    /**
     * 总数
     * @param $params
     * @return int
     */
    public function getCount($params): int
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(1) as total');
        $this->makeBuilder($builder, $params);
        return intval($builder->getQuery()->getSingleResult()->total);
    }

    /**
     * 获取列表数据
     * @param array $params
     * @return array
     */
    public function getList(array $params): array
    {
        $page_num  = $params['pageNum'];
        $page_size = $params['pageSize'];
        $offset    = $page_size * ($page_num - GlobalEnums::DEFAULT_PAGE_NUM);
        $builder   = $this->modelsManager->createBuilder();
        $columns   = [
            'mobile.id',
            'mobile.phone_number',
            'mobile.sim_card_id',
            'mobile.staff_info_id',
            'mobile.package',
            'mobile.status as phone_number_status',
            'mobile.update_sim_status',
            'mobile.remark',
            'mobile.last_operator',
            'mobile.last_operator_name',
            'mobile.created_at',
            'mobile.updated_at',
            'sim.number as sim_card_number',
            'mobile.sim_company',
            'staff.name as staff_name',
            'staff.node_department_id',
            'staff.job_title',
            'staff.sys_store_id',
            'staff.state as staff_state',
            'staff.wait_leave_state',
            'staff.leave_date',
        ];
        $builder->columns($columns);
        $this->makeBuilder($builder, $params);
        $builder->limit($page_size, $offset);
        $builder->orderby('mobile.updated_at DESC');
        $list = $builder->getQuery()->execute()->toArray();
        if (empty($list)) {
            return [];
        }

        foreach ($list as &$item) {
            $item['department_name']  = '';
            $item['job_title_name']   = '';
            $item['store_name']       = '';
            $item['staff_state_text'] = '';
            if ($item['staff_info_id']) {
                $item['department_name'] = $this->showDepartmentName($item['node_department_id']);
                $item['job_title_name']  = $this->showJobTitleName($item['job_title']);
                $item['store_name']      = $this->showStoreName($item['sys_store_id']);
                if ($item['staff_state'] == StaffInfoEnums::STAFF_STATE_IN && $item['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES) {
                    $item['staff_state'] = StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE;
                }
                $item['staff_state_text'] = static::$t->_(StaffInfoEnums::$staff_state[$item['staff_state']]);
                $item['leave_date']       = substr($item['leave_date'], 0, 10);
            } else {
                $item['staff_info_id'] = '';
                $item['leave_date']    = '';
            }
            $item['phone_number_status_text'] = self::$t->_('phone_number_status_' . $item['phone_number_status']);                //手机号状态
            $item['package_text']             = self::$t->_('phone_number_package_' . $item['package']);              //手机号套餐 必填
            $item['update_sim_status_text']   = self::$t->_('update_sim_status_' . $item['update_sim_status']);       //手机号码更新SIM序列号
            $item['sim_company_text']         = SimCardModel::SIM_COMPANY_LIST[$item['sim_company']] ?? '';           //运营商 必填
            $item['operator_text']            = $item['last_operator_name'] . "(" . $item['last_operator'] . ")";
            $item['updated_at']               = show_time_zone($item['updated_at']);
        }
        return $list;
    }


    public function detail($params)
    {
        $companyMobileModel = CompanyMobileManageModel::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => ['id' => $params['id']],
        ]);
        if (empty($companyMobileModel)) {
            throw new ValidationException(self::$t->_('empty_data'));
        }
        $result = $companyMobileModel->toArray();

        $result['department_name'] = '';
        $result['job_title_name']  = '';
        $result['store_name']      = '';
        $result['sim_card_number'] = '';
        $result['staff_name']      = '';
        if ($result['staff_info_id']) {
            $staffInfoModel = HrStaffInfoModel::findFirst([
                'conditions' => 'staff_info_id = :staff_info_id:',
                'bind'       => ['staff_info_id' => $result['staff_info_id']],
            ]);
            if ($staffInfoModel) {
                $result['staff_name']      = $staffInfoModel->name;
                $result['department_name'] = $this->showDepartmentName($staffInfoModel->node_department_id);
                $result['job_title_name']  = $this->showJobTitleName($staffInfoModel->job_title);
                $result['store_name']      = $this->showStoreName($staffInfoModel->sys_store_id);
                if ($staffInfoModel->state == StaffInfoEnums::STAFF_STATE_IN && $staffInfoModel->wait_leave_state == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES) {
                    $staffInfoModel->state = StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE;
                }
                $result['staff_state_text'] = static::$t->_(StaffInfoEnums::$staff_state[$staffInfoModel->state]);
            }
        }
        if ($result['sim_card_id']) {
            $simCardModel              = SimCardModel::findFirst([
                'conditions' => 'id = :sim_card_id:',
                'bind'       => ['sim_card_id' => $result['sim_card_id']],
            ]);
            $result['sim_card_number'] = $simCardModel ? $simCardModel->number : '';
        }

        $result['phone_number_status']      = $result['status'];
        $result['phone_number_status_text'] = self::$t->_('phone_number_status_' . $result['status']);
        $result['package_text']             = self::$t->_('phone_number_package_' . $result['package']);
        $result['update_sim_status_text']   = self::$t->_('update_sim_status_' . $result['update_sim_status']);
        $result['sim_company_text']         = SimCardModel::SIM_COMPANY_LIST[$result['sim_company']] ?? '';
        return $result;
    }


    /**
     * 新增
     * @param $params
     * @param $user
     * @param int $type
     * @return true
     * @throws ValidationException
     */
    public function add($params, $user, int $type = CompanyMobileManageLogModel::TYPE_ADD): bool
    {
        $this->logger->info(['add'=>$params,'type'=>$type]);

        $db = $this->getDI()->get('db_backyard');
        $db->begin();
        try {
            $companyMobileCheck = CompanyMobileManageModel::findFirst([
                'conditions' => 'phone_number = :phone_number:',
                'bind'       => ['phone_number' => $params['phone_number']],
            ]);
            //手机号码重复，不可导入
            if (!empty($companyMobileCheck)) {
                throw new ValidationException(self::$t->_('20211_error_message_001'));
            }
            //SIM卡序列号不存在
            if (!empty($params['sim_card_number'])) {
                $simCardModel = SimCardModel::findFirst([
                    'conditions' => 'number = :number: and status = :status:',
                    'bind'       => [
                        'number' => $params['sim_card_number'],
                        'status' => SimCardModel::STATUS_AVAILABLE,
                    ],
                ]);
                if (empty($simCardModel)) {
                    throw new ValidationException(self::$t->_('20211_error_message_002'));
                }

                //V21255 校验SIM手机号的运营商和SIM序列号的运营商是否一致，如果不一致，则提示：手机号和序列号的运营商不一致，请检查！
                if ($simCardModel->sim_company != $params['sim_company']) {
                    throw new ValidationException(self::$t->_('21255_error_message_001'), ErrCode::$VALIDATE_ERROR);
                }
            }
            //使用人只能绑定已激活，待激活的手机号码
            if (!empty($params['staff_info_id'])) {
                if (!in_array($params['phone_number_status'],
                    [CompanyMobileManageModel::STATUS_ACTIVE, CompanyMobileManageModel::STATUS_WAIT_ACTIVE])) {
                    throw new ValidationException(self::$t->_('20211_error_message_003'));
                }

                $staffInfoModel = HrStaffInfoModel::findFirst([
                    'conditions' => 'staff_info_id = :staff_info_id: and state = :state: and is_sub_staff = 0 and formal in ({formal:array}) and hire_type in ({hire_type:array})',
                    'bind'       => [
                        'staff_info_id' => $params['staff_info_id'],
                        'state'         => StaffInfoEnums::STAFF_STATE_IN,
                        'formal'        => [StaffInfoEnums::FORMAL_TRAINEE, StaffInfoEnums::FORMAL_IN],
                        'hire_type'     => [
                            StaffInfoEnums::HIRE_TYPE_PERMANENT_EMPLOYEE,
                            StaffInfoEnums::HIRE_TYPE_MONTHLY,
                            StaffInfoEnums::HIRE_TYPE_DAILY,
                            StaffInfoEnums::HIRE_TYPE_HOURLY,
                            StaffInfoEnums::HIRE_TYPE_INTERN,
                        ],
                    ],
                ]);
                if (empty($staffInfoModel)) {
                    throw new ValidationException(self::$t->_('20211_error_message_016'));
                }
                $params['staff_name'] = $staffInfoModel->name;
            }

            if (!empty($simCardModel)) {
                //记操作记录
                SimCardService::getInstance()->addLog($simCardModel->id, SimCardLogModel::TYPE_USE, $user,
                    ['status' => $simCardModel->status], ['status' => SimCardModel::STATUS_USED]);
                $simCardModel->status = SimCardModel::STATUS_USED;
                $simCardModel->save();
            }

            $companyMobileModel                = new CompanyMobileManageModel();
            $companyMobileModel->phone_number  = $params['phone_number'];
            $companyMobileModel->sim_company   = $params['sim_company'];
            $companyMobileModel->sim_card_id   = !empty($simCardModel) ? $simCardModel->id : 0;
            $companyMobileModel->staff_info_id = !empty($params['staff_info_id']) ? $params['staff_info_id'] : null;
            $companyMobileModel->package       = $params['package'];
            $companyMobileModel->status        = $params['phone_number_status'];
            if (!empty($params['remark'])) {
                $params['remark'] = mb_substr($params['remark'], 0, 500);
                $companyMobileModel->remark = $params['remark'];
            }
            $companyMobileModel->last_operator      = $user['id'];
            $companyMobileModel->last_operator_name = $user['name'];
            $companyMobileModel->save();

            //加操作日志
            $this->addLog($companyMobileModel->id, $type, $user, [], $params);
            $db->commit();
            //同步员工数据
            $this->syncStaffInfo($companyMobileModel->staff_info_id, $companyMobileModel->staff_info_id);
        } catch (ValidationException $e) {
            $db->rollBack();
            throw $e;
        } catch (Exception $exception) {
            $db->rollBack();
            $e_c = [
                'message' => $exception->getMessage(),
                'file'    => $exception->getFile(),
                'line'    => $exception->getLine(),
                'trace'   => $exception->getTraceAsString(),
            ];
            $this->logger->error($e_c);
            throw new ValidationException(self::$t->_('retry_later'));
        }
        return true;
    }

    /**
     * 编辑
     * @param $params
     * @param $user
     * @param int $type
     * @return bool
     * @throws ValidationException
     */
    public function edit($params, $user, int $type = CompanyMobileManageLogModel::TYPE_EDIT): bool
    {

        $this->logger->info(['edit'=>$params,'type'=>$type]);

        $db = $this->getDI()->get('db_backyard');
        $db->begin();
        try {
            if (empty($params['id'])) {
                throw new ValidationException(self::$t->_('empty_data'));
            }

            $companyMobileModel = CompanyMobileManageModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $params['id']],
                'for_update' => true,
            ]);
            if (empty($companyMobileModel)) {
                throw new ValidationException(self::$t->_('empty_data'));
            }

            $before['package'] = $companyMobileModel->package;
            if (!empty($params['update_sim_status'])) {
                $before['update_sim_status'] = $companyMobileModel->update_sim_status;
            }

            if (!empty($params['phone_number_status'])) {
                $before['phone_number_status'] = $companyMobileModel->status;
            }

            $before['remark'] = $companyMobileModel->remark;
            $before['sim_company'] = $companyMobileModel->sim_company;

            //当前绑定的sim卡
            $before['sim_card_number'] = '';
            $used_sim_card_sim_company = 0;
            if (!empty($companyMobileModel->sim_card_id)) {
                $usedSimCardModel = SimCardModel::findFirst([
                    'conditions' => 'id = :id: ',
                    'bind'       => ['id' => $companyMobileModel->sim_card_id],
                ]);
                $usedSimCardModel && $before['sim_card_number'] = $usedSimCardModel->number;
                $used_sim_card_sim_company = $usedSimCardModel ? $usedSimCardModel->sim_company : $used_sim_card_sim_company;
            }

            //SIM卡序列号不存在
            if (!empty($params['sim_card_number'])) {
                if (empty($usedSimCardModel) || $usedSimCardModel->number != $params['sim_card_number']) {
                    $simCardModel = SimCardModel::findFirst([
                        'conditions' => 'number = :number: and status = :status:',
                        'bind'       => [
                            'number' => $params['sim_card_number'],
                            'status' => SimCardModel::STATUS_AVAILABLE,
                        ],
                    ]);
                    if (empty($simCardModel)) {
                        throw new ValidationException(self::$t->_('20211_error_message_002'));
                    }

                    //记操作记录
                    SimCardService::getInstance()->addLog($simCardModel->id, SimCardLogModel::TYPE_USE, $user,
                        ['status' => $simCardModel->status], ['status' => SimCardModel::STATUS_USED]);
                    $simCardModel->status = SimCardModel::STATUS_USED;
                    $simCardModel->save();
                    if (!empty($usedSimCardModel)) {
                        //记操作记录
                        SimCardService::getInstance()->addLog($usedSimCardModel->id, SimCardLogModel::TYPE_ABANDON,
                            $user, ['status' => $usedSimCardModel->status], ['status' => SimCardModel::STATUS_ABANDON]);
                        //换号 把之前的作废
                        $usedSimCardModel->status = SimCardModel::STATUS_ABANDON;
                        $usedSimCardModel->save();
                    }
                    //更新关联关系
                    $companyMobileModel->sim_card_id = $simCardModel->id;
                    $used_sim_card_sim_company = $simCardModel->sim_company;
                }
            }
            if (!empty($params['update_sim_status'])) {
                $companyMobileModel->update_sim_status = $params['update_sim_status'];
            }
            $updateState = false;
            $after =  array_only($params, array_keys($before));
            if (!empty($params['phone_number_status'])) {
                if (in_array($params['phone_number_status'],
                    [CompanyMobileManageModel::STATUS_ACTIVE, CompanyMobileManageModel::STATUS_NOT_ENABLED])) {
                    $updateState = true;
                    unset($after['phone_number_status']);
                    unset($before['phone_number_status']);
                } else {
                    $companyMobileModel->status = $params['phone_number_status'];
                }
            }

            if (!empty($params['remark'])) {
                $params['remark']           = mb_substr($params['remark'], 0, 500);
                $companyMobileModel->remark = $params['remark'];
            }
            if (!empty($params['package'])) {
                $companyMobileModel->package = $params['package'];
            }

            //V21255 校验SIM手机号的运营商和SIM序列号的运营商是否一致，如果不一致，则提示：手机号和序列号的运营商不一致，请检查！
            if (!empty($params['sim_company']) && $used_sim_card_sim_company && $used_sim_card_sim_company != $params['sim_company']) {
                throw new ValidationException(self::$t->_('21255_error_message_001'), ErrCode::$VALIDATE_ERROR);
            }
            $companyMobileModel->sim_company = $params['sim_company'] ?? 0;
            $companyMobileModel->last_operator      = $user['id'];
            $companyMobileModel->last_operator_name = $user['name'];
            $companyMobileModel->save();
            $after =  array_only($params, array_keys($before));
            //加操作日志
            $this->addLog($companyMobileModel->id, $type, $user, $before,$after);

            ///处理停用或者激活的逻辑
            if (!empty($updateState)) {
                $this->updateState($params, $user, $companyMobileModel);
            } else {
                $this->syncStaffInfo($companyMobileModel->staff_info_id, $companyMobileModel->staff_info_id, ['operator' => $user['id']]);
            }

            $db->commit();

        } catch (ValidationException $e) {
            $db->rollBack();
            throw $e;
        } catch (Exception $exception) {
            $db->rollBack();
            $e_c = [
                'message' => $exception->getMessage(),
                'file'    => $exception->getFile(),
                'line'    => $exception->getLine(),
                'trace'   => $exception->getTraceAsString(),
            ];
            $this->logger->error($e_c);
            throw new ValidationException(self::$t->_('retry_later'));
        }
        return true;
    }


    /**
     * @param $file_url
     * @param $t_t
     * @return array
     * @throws ValidationException
     */
    protected function getBatchExcelData($file_url): array
    {
        $excel_data = get_oss_file_excel_data($file_url, [\Vtiful\Kernel\Excel::TYPE_STRING,\Vtiful\Kernel\Excel::TYPE_STRING]);
        if (empty($excel_data)) {
            throw new ValidationException(self::$t->_('20211_error_message_004'));
        }
        $title = [$excel_data[0], $excel_data[1], $excel_data[2]];
        unset($excel_data[0], $excel_data[1], $excel_data[2]);
        return [$title, $excel_data];
    }


    /**
     * @param $file_url
     * @param $user
     * @return array
     * @throws BusinessException
     * @throws GuzzleException
     * @throws ValidationException
     */
    public function batchAdd($file_url, $user): array
    {
        $result = [
            'result_url'  => '',
            'success_num' => 0,
            'fail_num'    => 0,
        ];
        [$title, $excel_data] = $this->getBatchExcelData($file_url);

        $mobile_company_package = EnumsService::getInstance()->getSettingEnvValueIds('phone_number_package');
        $this->logger->info(['batchAddData' => $excel_data,'mobile_company_package'=>$mobile_company_package]);

        foreach ($excel_data as $key => $excel_item) {
            if (empty($excel_item[0])) {
                continue;
            }
            try {
                $package = explode('-', $excel_item[3])[0];
                $sim_company = isset($excel_item[4]) ? (explode('-', $excel_item[4])[0] ?? 0) : 0;//所属运营商
                $data_item = [
                    'phone_number'        => $excel_item[0],
                    'phone_number_status' => $excel_item[2][0],
                    'package'             => $package,
                    'sim_company'         => $sim_company
                ];
                //手机号码只能10个数字
                if (!is_numeric($excel_item[0]) || strlen($excel_item[0]) != 10) {
                    throw new ValidationException(self::$t->_('20211_error_message_006'));
                }
                //SIM卡序列号只能18或者13个数字，且是可用的序列号
                if (!empty($excel_item[1])) {
                    if (!is_numeric($excel_item[1]) || !in_array(strlen($excel_item[1]),
                            [SimCardService::SIM_CARD_NUMBER_LEN_TRUE, SimCardService::SIM_CARD_NUMBER_LEN_AIS])) {
                        throw new ValidationException(self::$t->_('20211_error_message_005'));
                    }
                    $excel_data[$key][1]                = strval($excel_item[1]);
                    $data_item['sim_card_number'] = $excel_item[1];
                }
                //手机号状态只能是文档里的值 字符串取第一位
                if (!in_array($excel_item[2][0], [
                    CompanyMobileManageModel::STATUS_WAIT_ACTIVE,
                    CompanyMobileManageModel::STATUS_ACTIVE,
                    CompanyMobileManageModel::STATUS_NOT_ENABLED,
                ])) {
                    throw new ValidationException(self::$t->_('20211_error_message_007'));
                }
                //手机号套餐只能是文档里的值 字符串取第一位
                if (!in_array($package, $mobile_company_package)) {
                    throw new ValidationException(self::$t->_('20211_error_message_009'));
                }
                //所属运营商
                if (!in_array($sim_company, [SimCardModel::SIM_COMPANY_AIS, SimCardModel::SIM_COMPANY_TRUE])) {
                    throw new ValidationException(self::$t->_('21255_error_message_002'), ErrCode::$VALIDATE_ERROR);
                }

                if (!empty($excel_item[5])) {
                    $data_item['staff_info_id'] = $excel_item[5];
                }
                if (!empty($excel_item[6])) {
                    $data_item['staff_name'] = $excel_item[6];
                }
                if (!empty($excel_item[7])) {
                    $data_item['remark'] = $excel_item[7];
                }

                $excel_data[$key][8] = self::$t->_('excel_result_success');
                $this->add($data_item, $user, CompanyMobileManageLogModel::TYPE_IMPORT_ADD);
                $result['success_num']++;
            } catch (Exception $e) {
                $result['fail_num']++;
                $excel_data[$key][8] = $e->getMessage();
            }
        }
        $header               = array_shift($title);
        $excel_data           = array_merge(array_values($title), $excel_data);
        $file_name            = 'batch_add_call_number_result_' . date('YmdHis') . '.xlsx';
        $result['result_url'] = $this->exportExcel($header, $excel_data, $file_name)['data'];
        return $result;
    }


    /**
     * @param $file_url
     * @param $user
     * @return string[]
     * @throws BusinessException
     * @throws GuzzleException
     * @throws ValidationException
     */
    public function batchEdit($file_url, $user): array
    {
        $result = [
            'result_url'  => '',
            'success_num' => 0,
            'fail_num'    => 0,
        ];
        [$title, $excel_data] = $this->getBatchExcelData($file_url);
        $mobile_company_package = EnumsService::getInstance()->getSettingEnvValueIds('phone_number_package');
        $this->logger->info(['batchEdit' => $excel_data]);

        foreach ($excel_data as $key => $excel_item) {
            if (empty($excel_item[0])) {
                continue;
            }

            $data_item = [];
            try {
                //手机号码只能10个数字
                if (!is_numeric($excel_item[0]) || strlen($excel_item[0]) != 10) {
                    throw new ValidationException(self::$t->_('20211_error_message_006'));
                }
                $companyMobileCheck = CompanyMobileManageModel::findFirst([
                    'conditions' => 'phone_number = :phone_number:',
                    'bind'       => ['phone_number' => $excel_item[0]],
                ]);
                if (empty($companyMobileCheck)) {
                    throw new ValidationException(self::$t->_('empty_data'));
                }

                if ($companyMobileCheck->status == CompanyMobileManageModel::STATUS_FEEDBACK_HANDLING) {
                    //失败，该手机已被反馈异常，请去异常号码工单菜单进行处理！
                    throw new ValidationException(self::$t->_('20951_error_message_006'));
                }

                $data_item['id'] = $companyMobileCheck->id;

                //SIM卡序列号只能18或者13个数字，且是可用的序列号
                if (!empty($excel_item[1])) {
                    if (!is_numeric($excel_item[1]) || !in_array(strlen($excel_item[1]),
                            [SimCardService::SIM_CARD_NUMBER_LEN_TRUE, SimCardService::SIM_CARD_NUMBER_LEN_AIS])) {
                        throw new ValidationException(self::$t->_('20211_error_message_005'));
                    }
                    $excel_data[$key][1]                = strval($excel_item[1]);
                    $data_item['sim_card_number'] = $excel_item[1];
                }

                //手机号状态只能是文档里的值
                if (!empty($excel_item[2])) {
                    if (!in_array($excel_item[2][0], [
                        CompanyMobileManageModel::STATUS_WAIT_ACTIVE,
                        CompanyMobileManageModel::STATUS_ACTIVE,
                        CompanyMobileManageModel::STATUS_NOT_ENABLED,
                        CompanyMobileManageModel::STATUS_TERMINATE_CONTRACT,
                    ])) {
                        throw new ValidationException(self::$t->_('20211_error_message_007'));
                    }
                    $data_item['phone_number_status'] = $excel_item[2][0];
                }

                //手机号码更新SIM序列号只能是文档里的值，且只能更新待更新的数据
                if (!empty($excel_item[3])) {
                    if ($excel_item[3][0] != CompanyMobileManageModel::UPDATE_SIM_STATUS_UPDATED || $companyMobileCheck->update_sim_status != CompanyMobileManageModel::UPDATE_SIM_STATUS_WAITING) {
                        throw new ValidationException(self::$t->_('20211_error_message_008'));
                    }
                    $data_item['update_sim_status'] = $excel_item[3][0];
                }

                //手机号套餐只能是文档里的值
                if (!empty($excel_item[4])) {
                    $package = explode('-', $excel_item[4])[0];
                    if (!in_array($package, $mobile_company_package)) {
                        throw new ValidationException(self::$t->_('20211_error_message_009'));
                    }
                    $data_item['package'] = $package;
                }
                //所属运营商
                if (!empty($excel_item[5])) {
                    $sim_company = explode('-', $excel_item[5])[0] ?? 0;
                    if (!in_array($sim_company, [SimCardModel::SIM_COMPANY_AIS, SimCardModel::SIM_COMPANY_TRUE])) {
                        throw new ValidationException(self::$t->_('21255_error_message_003'), ErrCode::$VALIDATE_ERROR);
                    }
                    $data_item['sim_company'] = $sim_company;
                }

                //备注
                if (!empty($excel_item[6])) {
                    $data_item['remark'] = $excel_item[6];
                }
                //改成未启用的原因
                if ($data_item['phone_number_status'] == CompanyMobileManageModel::STATUS_NOT_ENABLED) {
                    if (empty($excel_item[7])) {
                        //改成未启用的原因必填
                        throw new ValidationException(self::$t->_('20951_error_message_007'));
                    }
                    $data_item['not_enabled_reason'] = $excel_item[7];
                }


                $excel_data[$key][8] = self::$t->_('excel_result_success');
                $this->edit($data_item, $user, CompanyMobileManageLogModel::TYPE_IMPORT_EDIT);
                $result['success_num']++;
            } catch (Exception $e) {
                $result['fail_num']++;
                $excel_data[$key][8] = $e->getMessage();
            }
        }
        $header               = array_shift($title);
        $excel_data           = array_merge(array_values($title), $excel_data);
        $file_name            = 'batch_edit_call_number_result_' . date('YmdHis') . '.xlsx';
        $result['result_url'] = $this->exportExcel($header, $excel_data, $file_name)['data'];
        return $result;
    }


    /**
     * 激活、停用
     * @param $params
     * @param $user
     * @param null $companyMobileModel
     * @return bool
     * @throws ValidationException
     */
    public function updateState($params, $user,$companyMobileModel = null): bool
    {
        $db = $this->getDI()->get('db_backyard');
        $db->begin();
        try {
            $companyMobileModel = $companyMobileModel ? : CompanyMobileManageModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $params['id']],
                'for_update' => true,
            ]);
            if (empty($companyMobileModel)) {
                throw new ValidationException(self::$t->_('empty_data'));
            }
            $staff_info_id     = $companyMobileModel->staff_info_id ?: 0;
            $staff_update_data = ['operator' => $user['id']];
            $before = [
                'phone_number_status' => $companyMobileModel->status,
                'remark'              => $companyMobileModel->remark,
                'not_enabled_reason'  => $companyMobileModel->not_enabled_reason,
            ];
            $after = [];
            if ($params['phone_number_status'] == CompanyMobileManageModel::STATUS_ACTIVE) {
                if ($companyMobileModel->status == CompanyMobileManageModel::STATUS_ACTIVE) {
                    throw new ValidationException(self::$t->_('submit_audit_error.4'));
                }
                $type = CompanyMobileManageLogModel::TYPE_ACTIVE;//激活
            } elseif ($params['phone_number_status'] == CompanyMobileManageModel::STATUS_NOT_ENABLED) {
                $type = CompanyMobileManageLogModel::TYPE_STOP;  //停用
                if ($companyMobileModel->status == CompanyMobileManageModel::STATUS_NOT_ENABLED) {
                    throw new ValidationException(self::$t->_('submit_audit_error.4'));
                }
                //记录原工号
                if (!empty($companyMobileModel->staff_info_id)) {
                    $before['staff_info_id'] = $companyMobileModel->staff_info_id;
                    if ($staff_info = (new UserService())->getUserByIdInRbi($companyMobileModel->staff_info_id)) {
                        $before['staff_name'] = $staff_info->name;
                        $staff_update_data['mobile_company'] = '';
                    }
                    $after['staff_info_id'] = '';
                    $after['staff_name']    = '';
                    $companyMobileModel->staff_info_id = null;
                }
                //记录原sim卡序号
                if (!empty($companyMobileModel->sim_card_id)) {
                    $simCardModel = SimCardModel::findFirst($companyMobileModel->sim_card_id);
                    if (!empty($simCardModel)) {
                        $before['sim_card_number'] = $simCardModel->number;
                        $simCardModel->status      = SimCardModel::STATUS_ABANDON;
                        $simCardModel->save();
                    }
                    $companyMobileModel->sim_card_id = 0;
                }
            }

            if (empty($type)) {
                throw new ValidationException('params phone_number_status error');
            }
            
            if (!empty($params['remark'])) {
                $params['remark'] = mb_substr($params['remark'], 0, 500);
                $companyMobileModel->remark = $params['remark'];
            }

            if (!empty($params['not_enabled_reason'])) {
                $params['not_enabled_reason'] = mb_substr($params['not_enabled_reason'], 0, 500);
                $companyMobileModel->not_enabled_reason = $params['not_enabled_reason'];
            }

            $companyMobileModel->status             = $params['phone_number_status'];
            $companyMobileModel->last_operator      = $user['id'];
            $companyMobileModel->last_operator_name = $user['name'];
            $companyMobileModel->save();

            $after = array_merge($after,
                [
                    'phone_number_status' => $companyMobileModel->status,
                    'remark'              => $params['remark'] ?? '',
                    'not_enabled_reason'  => $params['not_enabled_reason'] ?? '',
                ]);

            //加操作日志
            $this->addLog($companyMobileModel->id, $type, $user, $before, $after);

            //激活 发送消息
            if ($params['phone_number_status'] == CompanyMobileManageModel::STATUS_ACTIVE && !empty($companyMobileModel->staff_info_id)) {
                $staff_language               = StaffLangService::getInstance()->getLatestMobileLang($companyMobileModel->staff_info_id);
                $s_t                          = BaseService::getTranslation($staff_language);
                $kit_param                    = [];
                $kit_param['staff_users']     = [['id' => $companyMobileModel->staff_info_id]];
                $kit_param['message_title']   = $s_t->_('20211_active_message_title');
                $kit_param['message_content'] = $s_t->_('20211_active_message_content',
                    ['phone_number' => $companyMobileModel->phone_number]);
                $kit_param['category']        = '-1';
                $client                       = new ApiClient('hcm_rpc', '', 'add_kit_message');
                $client->setParams([$kit_param]);
                $res = $client->execute();
            }
            $db->commit();
            if ($staff_info_id) {
                $this->syncStaffInfo($staff_info_id, $staff_info_id, $staff_update_data);
            }
        } catch (ValidationException|Exception $e) {
            $db->rollBack();
            throw $e;
        }
        return true;
    }


    /**
     * 同步员工数据
     * @param $staff_info_id
     * @param $sharding_key
     * @param array $params
     * @return false|mixed|string
     */
    public function syncStaffInfo($staff_info_id, $sharding_key, array $params = [])
    {
        if (empty($staff_info_id)) {
            return false;
        }
        $staff_info = (new UserService())->getUserByIdInRbi($staff_info_id);
        if (empty($staff_info)) {
            return false;
        }
        $params['name']            = $staff_info->name;
        $params['staff_info_id']   = $staff_info_id;
        $rmq                       = new RocketMQ('update-staff-info');
        $sendData['jsonCondition'] = json_encode($params, JSON_UNESCAPED_UNICODE);
        $sendData['handleType']    = RocketMQ::TAG_HR_STAFF_UPDATE;
        $rmq->setShardingKey($sharding_key);
        return $rmq->sendOrderlyMsg($sendData, 3);
    }


    /**
     * 增加操作记录
     * @param $cmm_id
     * @param $user
     * @param $type
     * @param $before
     * @param $after
     * @return mixed
     */
    public function addLog($cmm_id, $type, $user, $before, $after)
    {
        $model                            = new CompanyMobileManageLogModel();
        $data['mobile_company_manage_id'] = $cmm_id;
        $data['operator']                 = $user['id'];
        $data['operator_name']            = $user['name'];
        $data['operator_dept_id']         = $user['node_department_id'];
        $data['type']                     = $type;
        $data['before']                   = json_encode($before, JSON_UNESCAPED_UNICODE);
        $data['after']                    = json_encode($after, JSON_UNESCAPED_UNICODE);
        return $model->i_create($data);
    }


    /**
     * 列表查询
     * @param array $params 查询条件
     * @return array
     */
    public function getOperationDataList(array $params): array
    {
        $page_size = $params['pageSize'];
        $page_num  = $params['pageNum'];
        $code      = ErrCode::$SUCCESS;
        $message   = '';
        $data      = [
            'items'      => [],
            'pagination' => [
                'current_page' => intval($page_num),
                'per_page'     => intval($page_size),
                'total_count'  => 0,
            ],
        ];
        $count     = $this->getOperationCount($params);
        if ($count > 0) {
            $items = $this->getOperationList($params);
        }
        $data['items']                     = $items ?? [];
        $data['pagination']['total_count'] = $count;
        return $data;
    }

    public function getOperationCount($params): int
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(1) as total');
        $this->makeOperationBuilder($builder, $params);
        return intval($builder->getQuery()->getSingleResult()->total);
    }

    public function getOperationList(array $params): array
    {
        $page_size = $params['pageSize'];
        $page_num  = $params['pageNum'];
        $offset    = $page_size * ($page_num - GlobalEnums::DEFAULT_PAGE_NUM);
        $builder   = $this->modelsManager->createBuilder();
        $builder->columns('*');
        $this->makeOperationBuilder($builder, $params);
        $builder->limit($page_size, $offset);
        $builder->orderby('id DESC');
        $list = $builder->getQuery()->execute()->toArray();
        if (empty($list)) {
            return [];
        }
        foreach ($list as &$item) {
            $item['sim_operation_type_text']  = self::$t->_('sim_operation_type_'.$item['type']);
            $item['created_at']               = show_time_zone($item['created_at']);
            $item['operator_department_name'] = $this->showDepartmentName($item['operator_dept_id']);
            $item['operator_content']         = [];
            $before                           = json_decode($item['before'], JSON_UNESCAPED_UNICODE);
            $after                            = json_decode($item['after'], JSON_UNESCAPED_UNICODE);
            $changes                          = [];
            array_filter($after, function ($after_value, $after_key) use (
                $before,
                &$changes
            ) {
                [$after_key_lang, $after_value_show] = $this->getOperationTransferTitleContent($after_key,
                    $after_value);

                //之前没有，之后有了  新增
                if (!isset($before[$after_key])) {
                    $changes[] = '[' . self::$t->_('add') . ']: ' . $after_key_lang . ':  => ' . $after_value_show;
                } else {
                    if ($after_value != $before[$after_key]) {
                        [$k, $before_value_show] = $this->getOperationTransferTitleContent($after_key,
                            $before[$after_key]);
                        $changes[] = '[' . self::$t->_('update') . ']: ' . $after_key_lang . ': ' . $before_value_show . ' => ' . $after_value_show;
                    }
                }
            }, ARRAY_FILTER_USE_BOTH);

            $item['operator_content'] = $changes;
        }
        return $list;
    }

    /**
     * 处理操作日志的翻译
     * @param $key
     * @param $value
     * @return array
     */
    protected function getOperationTransferTitleContent($key, $value): array
    {
        $key_show   = self::$t->_($key);
        $value_show = $value;
        if ($key == 'package') {
            $key_show   = self::$t->_('phone_number_package');
            $value_show = self::$t->_('phone_number_package_' . $value);
        }
        if (in_array($key, ['phone_number_status', 'update_sim_status',])) {
            $value_show = self::$t->_($key . '_' . $value);
        }
        return [$key_show, $value_show];
    }


    /**
     * 处理筛选条件
     * @param $builder
     * @param $params
     * @return void
     */
    protected function makeOperationBuilder(&$builder, $params)
    {
        $builder->from(CompanyMobileManageLogModel::class);

        //操作类型
        if (!empty($params['type']) && is_array($params['type'])) {
            $builder->inWhere('type', $params['type']);
        }
        //手机号码
        if (!empty($params['operator'])) {
            $builder->andWhere('operator = :operator:', ['operator' => $params['operator']]);
        }

        if (!empty($params['id'])) {
            $builder->andWhere('mobile_company_manage_id = :mobile_company_manage_id:',
                ['mobile_company_manage_id' => $params['id']]);
        }
    }


    /**
     * @param $user
     * @param $type
     * @return bool
     * @throws BusinessException
     * @throws GuzzleException
     * @throws ValidationException
     */
    public function uploadFileToImportCenter($user, $type): bool
    {
        $check_excel_header = '';
        switch ($type) {
            case ImportCenterEnums::TYPE_SIM_COMPANY_MOBILE_ADD:
                $check_excel_header = BaseService::getTranslation('zh')->_('20211_company_mobile_batch_add_excel_title');
                break;
            case ImportCenterEnums::TYPE_SIM_COMPANY_MOBILE_EDIT:
                $check_excel_header = BaseService::getTranslation('zh')->_('20951_company_mobile_batch_edit_excel_title');
                break;
            case ImportCenterEnums::TYPE_SIM_CARD_ADD:
                $check_excel_header = BaseService::getTranslation('zh')->_('20951_sim_card_batch_add_excel_title');
                break;
            case ImportCenterEnums::TYPE_SIM_CARD_EDIT:
                $check_excel_header = BaseService::getTranslation('zh')->_('20951_sim_card_batch_edit_excel_title');
                break;
            case ImportCenterEnums::TYPE_SIM_COMPANY_MOBILE_FEEDBACK_EDIT:
                $check_excel_header = BaseService::getTranslation('zh')->_('20951_sim_card_feedback_excel_title');
                break;
        }

        $object_url = $this->getExcelUrl($check_excel_header,$type);
        return ImportCenterService::getInstance()->addImportCenter($user, $object_url,
            $type, $user);
    }

    /**
     * @param $check_header
     * @param $type
     * @return mixed
     * @throws BusinessException
     * @throws GuzzleException
     * @throws ValidationException
     */
    public function getExcelUrl($check_header,$type)
    {
        // 文件格式校验
        if (!$this->request->hasFiles()) {
            throw new ValidationException(self::$t->_('pay_upload_cheque_file_error_02'));
        }
        $file      = $this->request->getUploadedFiles()[0];
        $extension = $file->getExtension();
        if ($extension != 'xlsx') {
            throw new ValidationException(self::$t->_('pay_upload_cheque_file_error_02'));
        }
        $config = ['path' => ''];
        $excel  = new \Vtiful\Kernel\Excel($config);

        // 读取上传文件数据
        $excel_data = $excel->openFile($file->getTempName())
            ->openSheet()
            ->getSheetData();
        $this->logger->info(['getExcelUrl_excel_data'=>$excel_data]);
        // 读取每行单元格数据时，可指定每个单元格数据类型进行读取
        if (empty($excel_data)) {
            throw new ValidationException(self::$t->_('data_empty_or_read_data_failed'));
        }
        $excel_header = implode('', array_filter($excel_data[0]));
        if ($check_header != $excel_header) {
            throw new ValidationException(self::$t->_('template_error'));
        }

        if (in_array($type, [
            ImportCenterEnums::TYPE_SIM_COMPANY_MOBILE_ADD,
            ImportCenterEnums::TYPE_SIM_COMPANY_MOBILE_EDIT,
            ImportCenterEnums::TYPE_SIM_CARD_ADD,
            ImportCenterEnums::TYPE_SIM_CARD_EDIT,
        ])) {
            if (empty($excel_data[3]) || empty($excel_data[3][0])) {
                throw new ValidationException(self::$t->_('20211_error_message_017'));
            }
        }

        if ($type == ImportCenterEnums::TYPE_SIM_COMPANY_MOBILE_FEEDBACK_EDIT) {
            if (empty($excel_data[2]) || empty($excel_data[2][0])) {
                throw new ValidationException(self::$t->_('20211_error_message_017'));
            }

            $excel_data_tmp = array_values(array_filter(array_column($excel_data, 0)));
            if (count($excel_data_tmp) != count(array_unique($excel_data_tmp))) {
                //表格里手机号重复，不可导入
                throw new ValidationException(self::$t->_('20951_error_message_001'));
            }
        }

        // 文件生成OSS链接
        $file_path = sys_get_temp_dir() . '/' . $file->getName();
        $file->moveTo($file_path);
        $oss_result = OssHelper::uploadFile($file_path);
        $this->logger->info($oss_result);
        if (empty($oss_result['object_url'])) {
            throw new ValidationException(self::$t->_('file_upload_error'));
        }
        return $oss_result['object_url'];
    }

    /**
     * 变更使用人
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function changeStaff($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_backyard');
        $db->begin();
        try {
            $companyMobileModel = CompanyMobileManageModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $params['id']],
                'for_update' => true,
            ]);
            if (empty($companyMobileModel)) {
                throw new ValidationException(self::$t->_('empty_data'), ErrCode::$VALIDATE_ERROR);
            }
            //只能对“激活”或者“未启用”或者“待激活"的手机号变更使用人
            if (!in_array($companyMobileModel->status, [CompanyMobileManageModel::STATUS_WAIT_ACTIVE, CompanyMobileManageModel::STATUS_ACTIVE, CompanyMobileManageModel::STATUS_NOT_ENABLED])) {
                throw new ValidationException(self::$t->_('21255_error_message_004'), ErrCode::$VALIDATE_ERROR);
            }

            //新使用人不可等于原使用人，如果等于，提示：前后使用人不可一致！
            if ($companyMobileModel->staff_info_id == $params['staff_info_id']) {
                throw new ValidationException(self::$t->_('21255_error_message_005'), ErrCode::$VALIDATE_ERROR);
            }

            $old_staff_id = $companyMobileModel->staff_info_id;
            $before = ['staff_info_id' => $old_staff_id, 'remark' => $companyMobileModel->remark];

            //SIM卡管理的使用人信息发生变化，手机号的状态不变化，SIM卡管理-SIM卡管理增加操作日志
            $companyMobileModel->staff_info_id = $params['staff_info_id'];
            $companyMobileModel->remark = $params['remark'];
            $companyMobileModel->last_operator = $user['id'];
            $companyMobileModel->last_operator_name = $user['name'];
            $bool = $companyMobileModel->save();
            if ($bool === false) {
                throw new BusinessException('SIM卡管理-变更使用人失败 = ' . json_encode($params, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($companyMobileModel), ErrCode::$BUSINESS_ERROR);
            }

            //加操作日志
            $after = ['staff_info_id' => $params['staff_info_id'], 'remark' => $params['remark'],];
            $bool = $this->addLog($companyMobileModel->id, CompanyMobileManageLogModel::TYPE_STAFF_CHANGE, $user, $before, $after);
            if ($bool === false) {
                throw new BusinessException('SIM卡管理-变更使用人-SIM卡管理增加操作日志失败', ErrCode::$BUSINESS_ERROR);
            }

            //事务提交
            $db->commit();

            //获取原使用人、新使用人信息
            $staff_info_ids[] = $params['staff_info_id'];
            if ($old_staff_id) {
                $staff_info_ids[] = $old_staff_id;
            }
            $staff_list = (new HrStaffRepository())->getStaffListByStaffIds($staff_info_ids);
            //SIM卡管理-修改员工信息-MQ-有序消费key
            $sharding_key = 'sim_company_mobile_change_staff';
            /**
             * 若原使用人非离职，且该工号在HCM-flash员工信息管理上的企业号码为本次变更的手机号
             *  清空HCM-flash员工管理，该工号的企业号码字段
             *  给HCM-flash员工操作记录里，增加一条记录，操作人：点击更新的员工，原使用人企业号码变更的前后值
             *  给其发BY消息
             */
            $old_staff_info = $staff_list[$old_staff_id] ?? [];
            if ($old_staff_info && $old_staff_info['state'] != StaffInfoEnums::STAFF_STATE_LEAVE && $old_staff_info['mobile_company'] == $companyMobileModel->phone_number) {
                $staff_language = StaffLangService::getInstance()->getLatestMobileLang($companyMobileModel->staff_info_id);
                $s_t = BaseService::getTranslation($staff_language);
                $kit_param = [
                    'staff_users' => [['id' => $old_staff_id]],
                    'message_title' => $s_t->_('21255_change_staff_old_title'),
                    'message_content' => $s_t->_('21255_change_staff_old_message_content', ['staff_id' => $old_staff_id, 'staff_name' => $old_staff_info['name'], 'phone_number' => $companyMobileModel->phone_number]),
                    'category' => '-1'
                ];
                $client = new ApiClient('hcm_rpc', '', 'add_kit_message');
                $client->setParams([$kit_param]);
                $client->execute();
                $this->syncStaffInfo($old_staff_id, $sharding_key, ['operator' => $user['id'], 'mobile_company' => '']);
            }

            /**
             * 给新使用人，且该工号在HCM-flash员工信息管理上的企业号码非本次变更的手机号
             *  HCM-flash员工管理，该工号的企业号码字段赋予本次操作的手机号
             *  给HCM-flash员工操作记录里，增加一条记录，操作人：点击更新的员工，原使用人企业号码变更的前后值
             *  给其发BY消息
             */
            $new_staff_info = $staff_list[$params['staff_info_id']] ?? [];
            if ($new_staff_info && $new_staff_info['mobile_company'] != $companyMobileModel->phone_number) {
                $staff_language = StaffLangService::getInstance()->getLatestMobileLang($params['staff_info_id']);
                $s_t = BaseService::getTranslation($staff_language);
                $kit_param = [
                    'staff_users' => [['id' => $params['staff_info_id']]],
                    'message_title' => $s_t->_('21255_change_staff_new_title'),
                    'message_content' => $s_t->_('21255_change_staff_new_message_content', ['staff_id' => $params['staff_info_id'], 'staff_name' => $new_staff_info['name'], 'phone_number' => $companyMobileModel->phone_number]),
                    'category' => '-1'
                ];
                $client = new ApiClient('hcm_rpc', '', 'add_kit_message');
                $client->setParams([$kit_param]);
                $client->execute();
                $this->syncStaffInfo($params['staff_info_id'], $sharding_key, ['operator' => $user['id'], 'mobile_company' => $companyMobileModel->phone_number]);
            }
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            //事务回滚
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->warning('SIM卡管理-变更使用人-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $message,
            'data' => [],
        ];
    }
}
