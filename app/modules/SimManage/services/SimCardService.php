<?php

namespace App\Modules\SimManage\Services;

use App\Library\ErrCode;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\DownloadCenterEnum;
use App\Library\Exception\BusinessException;
use App\Modules\Setting\Services\BaseService;
use App\Modules\SimManage\Models\CompanyMobileManageModel;
use App\Modules\SimManage\Models\SimCardModel;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Services\DownloadCenterService;
use App\Modules\SimManage\Models\SimCardLogModel;
use GuzzleHttp\Exception\GuzzleException;
use Exception;

class SimCardService extends BaseService
{

    const SIM_CARD_NUMBER_LEN_TRUE = 18;
    const SIM_CARD_NUMBER_LEN_AIS = 13;


    public static $validate_detail = [
        'id' => 'Required|IntGt:0',
    ];

    //非必填
    public static $not_must_params = [
        'staff_info_id',
        'pageSize',
        'pageNum',
    ];


    //列表校验
    public static $validate_list = [
        'pageSize' => 'Required|IntGt:0', //每页条数
        'pageNum'  => 'Required|IntGt:0', //页码
    ];


    //添加
    public static $validate_add = [
        'phone_number'        => 'Required|StrLen:10',
        'sim_card_number'     => 'StrLenGeLe:13,18',
        'phone_number_status' => 'Required|Int',
        'staff_info_id'       => 'Int',
        'package'             => 'Required|Int',
    ];

    //编辑
    public static $validate_edit = [
        'id'      => 'Required|IntGt:0|>>>:param error[id]',
        'package' => 'Required|Int',
        'remark'  => 'Required|Str',
    ];


    //批量操作
    public static $validate_batch = [
    ];

    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    /**
     * @return
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * SIM卡序列号状态
     * @return array
     */
    protected function getStatusEnum(): array
    {
        foreach (SimCardModel::STATUS_LIST as $value) {
            $data[] = ['value' => (string)$value, 'label' => self::$t->_('sim_card_status_' . $value)];
        }
        return $data ?? [];
    }


    /**
     * sim卡运营商
     * @return array
     */
    protected function simCompanyEnum(): array
    {
        $status_list = SimCardModel::SIM_COMPANY_LIST;
        foreach ($status_list as $key => $company) {
            $data[] = ['value' => (string)$key, 'label' => $company];
        }
        return $data ?? [];
    }


    /**
     * 筛选静态枚举
     * @return array[]
     */
    public function getOptionsDefault(): array
    {
        return [
            'status_enum'      => $this->getStatusEnum(),
            'sim_company_enum' => $this->simCompanyEnum(),

        ];
    }


    /**
     * 列表查询
     * @param array $params 查询条件
     * @return array
     */
    public function getDataList(array $params): array
    {
        $page_size = $params['pageSize'];
        $page_num  = $params['pageNum'];
        $code      = ErrCode::$SUCCESS;
        $message   = '';
        $data      = [
            'items'      => [],
            'pagination' => [
                'current_page' => intval($page_num),
                'per_page'     => intval($page_size),
                'total_count'  => 0,
            ],
        ];
        $count     = $this->getCount($params);
        if ($count > 0) {
            $items = $this->getList($params);
        }
        $data['items']                     = $items ?? [];
        $data['pagination']['total_count'] = $count;
        return $data;
    }


    /**
     * @param $params
     * @return array
     * @throws BusinessException
     * @throws GuzzleException
     * @throws ValidationException
     */
    public function export($params, $user): array
    {
        $result = ['export_method' => 2, 'file_url' => ''];

        $count = $this->getCount($params);
        $max_limit = DownloadCenterEnum::DEFAUL_PER_PAGE_SIZE;
        if (get_runtime_env() == 'dev') {
            $max_limit = 20;
        }
        if ($count <= $max_limit) {
            $result['file_url']      = $this->downloadExcelData($params);
            $result['export_method'] = 1;
        } else {
            $res = DownloadCenterService::getInstance()->addDownloadCenter($user['id'],
                DownloadCenterEnum::COMPANY_MOBILE_SIM_EXPORT, $params);
            if ($res['code'] == ErrCode::$SUCCESS) {
                return $result;
            }
            throw new ValidationException($res['message']);
        }
        return $result;
    }


    /**
     * @param $params
     * @return mixed
     * @throws ValidationException
     * @throws BusinessException
     * @throws GuzzleException
     */
    public function downloadExcelData($params)
    {
        $params['pageNum']  = 1;
        $params['pageSize'] = 1000;
        $excel_data         = [];
        while (true) {
            $list = $this->getList($params);
            if (empty($list)) {
                break;
            }

            foreach ($list as $item) {
                $excel_data[] = [
                    $item['sim_card_number'],
                    $item['status_text'],
                    $item['sim_company_text'],
                    $item['remark'],

                ];
            }
            $params['pageNum']++;
        }

        $excel_header = [
            static::$t->_('sim_number'),                                 //SIM卡序列号
            static::$t->_('sim_card_status'),                            //SIM卡序列号状态
            static::$t->_('sim_company'),                                //SIM所属运营商
            static::$t->_('remark'),                                     //备注

        ];
        $file_name    = 'SIM_SN_number_' . date('YmdHis') . '.xlsx';
        $file_url     = $this->exportExcel($excel_header, $excel_data, $file_name)['data'];
        if (empty($file_url)) {
            throw new ValidationException(static::$t->_('download_fail_error'), ErrCode::$VALIDATE_ERROR);
        }
        return $file_url;
    }

    /**
     * 处理筛选条件
     * @param $builder
     * @param $params
     * @return void
     */
    protected function makeBuilder(&$builder, $params)
    {
        $builder->from(['sim' => SimCardModel::class]);
        //SIM卡序列号
        if (!empty($params['sim_card_number'])) {
            $builder->andWhere('sim.number = :sim_card_number:', ['sim_card_number' => $params['sim_card_number']]);
        }

        //所属运营商
        if (!empty($params['sim_company'])) {
            $builder->andWhere('sim.sim_company = :sim_company:', ['sim_company' => $params['sim_company']]);
        }
        //SIM卡序列号状态
        if (!empty($params['status']) && is_array($params['status'])) {
            $builder->inWhere('sim.status', $params['status']);
        }
    }

    /**
     * 总数
     * @param $params
     * @return int
     */
    public function getCount($params): int
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(1) as total');
        $this->makeBuilder($builder, $params);
        return intval($builder->getQuery()->getSingleResult()->total);
    }

    /**
     * 获取列表数据
     * @param array $params
     * @return array
     */
    public function getList(array $params): array
    {
        $page_size = $params['pageSize'];
        $page_num  = $params['pageNum'];
        $offset    = $page_size * ($page_num - GlobalEnums::DEFAULT_PAGE_NUM);
        $builder   = $this->modelsManager->createBuilder();
        $builder->columns(['number as sim_card_number', 'status', 'sim_company', 'type_text', 'remark']);
        $this->makeBuilder($builder, $params);
        $builder->limit($page_size, $offset);
        $builder->orderby('id DESC');
        $list = $builder->getQuery()->execute()->toArray();
        if (empty($list)) {
            return [];
        }
        foreach ($list as &$item) {
            $item['status_text']      = self::$t->_('sim_card_status_' . $item['status']);                    //SIM卡序列号状态
            $item['sim_company_text'] = SimCardModel::SIM_COMPANY_LIST[$item['sim_company']] ?? '';           //运营商
        }
        return $list;
    }


    /**
     * 新增
     * @param $params
     * @param $user
     * @param int $type
     * @return true
     * @throws ValidationException
     */
    public function add($params, $user, int $type = SimCardLogModel::TYPE_ADD): bool
    {
        $db = $this->getDI()->get('db_backyard');
        $db->begin();
        try {
            $simCardCheck = SimCardModel::findFirst([
                'conditions' => 'number = :number:',
                'bind'       => ['number' => $params['number']],
            ]);
            //SIM卡序列号重复
            if (!empty($simCardCheck)) {
                throw new ValidationException(self::$t->_('20211_error_message_011'));
            }

            $simCardModel                     = new SimCardModel();
            $simCardModel->number             = $params['number'];
            $simCardModel->sim_company        = $params['sim_company'];
            $simCardModel->status             = $params['status'] ?? SimCardModel::STATUS_AVAILABLE;
            $simCardModel->type_text          = mb_substr($params['type_text'], 0, 50);
            $simCardModel->remark             = mb_substr($params['remark'], 0, 500);
            $simCardModel->last_operator      = $user['id'];
            $simCardModel->last_operator_name = $user['name'];
            $simCardModel->save();

            //加操作日志
            $this->addLog($simCardModel->id, $type, $user, [], $params);
            $db->commit();
        } catch (ValidationException $e) {
            $db->rollBack();
            throw $e;
        } catch (Exception $exception) {
            $db->rollBack();
            $e_c = [
                'message' => $exception->getMessage(),
                'file'    => $exception->getFile(),
                'line'    => $exception->getLine(),
                'trace'   => $exception->getTraceAsString(),
            ];
            $this->logger->error($e_c);
            throw new ValidationException(self::$t->_('retry_later'));
        }
        return true;
    }

    /**
     * 编辑
     * @param $params
     * @param $user
     * @param int $type
     * @return bool
     * @throws ValidationException
     */
    public function edit($params, $user, int $type = SimCardLogModel::TYPE_EDIT): bool
    {
        $db = $this->getDI()->get('db_backyard');
        $db->begin();
        try {
            if (empty($params['id'])) {
                throw new ValidationException(self::$t->_('empty_data'));
            }

            $simCardModel = SimCardModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $params['id']],
                'for_update' => true,
            ]);
            if (empty($simCardModel)) {
                throw new ValidationException(self::$t->_('empty_data'));
            }

            $before['type_text'] = $simCardModel->type_text;
            $before['remark']    = $simCardModel->remark;
            if (!is_null($params['type_text'])) {
                $simCardModel->type_text = mb_substr($params['type_text'], 0, 50);
            }

            if (!empty($params['status'])) {
                $before['status']     = $simCardModel->status;
                $simCardModel->status = $params['status'];
                if ($params['status'] == SimCardModel::STATUS_AVAILABLE) {
                    $mobileCompanyCheck = CompanyMobileManageModel::findFirst([
                        'conditions' => 'sim_card_id = :sim_card_id:',
                        'bind'       => ['sim_card_id' => $simCardModel->id],
                    ]);
                    if (!empty($mobileCompanyCheck)) {
                        //该SIM序列号被手机号xxxx绑定，不可改成可用！
                        throw new ValidationException(self::$t->_('20951_error_message_008',['phone_number' => $mobileCompanyCheck->phone_number]));
                    }
                }
            }

            if (!is_null($params['remark'])) {
                $simCardModel->remark = mb_substr($params['remark'], 0, 500);
            }


            $simCardModel->last_operator      = $user['id'];
            $simCardModel->last_operator_name = $user['name'];
            $simCardModel->save();

            //加操作日志
            $this->addLog($simCardModel->id, $type, $user, $before, $params);
            $db->commit();
        } catch (ValidationException $e) {
            $db->rollBack();
            throw $e;
        } catch (Exception $exception) {
            $db->rollBack();
            $e_c = [
                'message' => $exception->getMessage(),
                'file'    => $exception->getFile(),
                'line'    => $exception->getLine(),
                'trace'   => $exception->getTraceAsString(),
            ];
            $this->logger->error($e_c);
            throw new ValidationException(self::$t->_('retry_later'));
        }
        return true;
    }


    /**
     * @param $file_url
     * @return array
     * @throws ValidationException
     */
    protected function getBatchExcelData($file_url): array
    {
        $excel_data = get_oss_file_excel_data($file_url, [\Vtiful\Kernel\Excel::TYPE_STRING]);
        if (empty($excel_data)) {
            throw new ValidationException(self::$t->_('20211_error_message_004'));
        }
        $title = [$excel_data[0], $excel_data[1], $excel_data[2]];
        unset($excel_data[0], $excel_data[1], $excel_data[2]);
        return [$title, $excel_data];
    }


    /**
     *
     * @param $file_url
     * @param $user
     * @return string[]
     * @throws BusinessException
     * @throws GuzzleException
     * @throws ValidationException
     */
    public function batchAdd($file_url, $user): array
    {
        $result = [
            'result_url'  => '',
            'success_num' => 0,
            'fail_num'    => 0,
        ];
        [$title, $excel_data] = $this->getBatchExcelData($file_url);

        $sim_company_list = array_flip(SimCardModel::SIM_COMPANY_LIST);
        foreach ($excel_data as $key => $excel_item) {
            if (empty($excel_item[0])) {
                continue;
            }
            try {
                //SIM运营商不是文档里所允许的值，请检查
                if (empty($excel_item[1]) || !in_array($excel_item[1], array_values(SimCardModel::SIM_COMPANY_LIST))) {
                    throw new ValidationException(self::$t->_('20211_error_message_012'));
                }
                //SIM序列号不是18或者13位数字，请检查
                if ($excel_item[1] == SimCardModel::SIM_COMPANY_LIST[SimCardModel::SIM_COMPANY_AIS] && strlen($excel_item[0]) != self::SIM_CARD_NUMBER_LEN_AIS) {
                    throw new ValidationException(self::$t->_('20211_error_message_014'));
                }
                if ($excel_item[1] == SimCardModel::SIM_COMPANY_LIST[SimCardModel::SIM_COMPANY_TRUE] && strlen($excel_item[0]) != self::SIM_CARD_NUMBER_LEN_TRUE) {
                    throw new ValidationException(self::$t->_('20211_error_message_014'));
                }
                $excel_data[$key][0] = strval($excel_item[0]);

                $data_item           = [
                    'number'      => $excel_item[0],
                    'sim_company' => $sim_company_list[$excel_item[1]],
                    'type_text'   => $excel_item[2],
                    'remark'      => $excel_item[3],
                ];
                $excel_data[$key][4] = self::$t->_('excel_result_success');
                $this->add($data_item, $user, SimCardLogModel::TYPE_BATCH_ADD);
                $result['success_num']++;
            } catch (Exception $e) {
                $result['fail_num']++;
                $excel_data[$key][4] = $e->getMessage();
            }
        }
        $header     = array_shift($title);
        $excel_data = array_merge(array_values($title), $excel_data);

        $file_name            = 'batch_add_call_number_result_' . date('YmdHis') . '.xlsx';
        $result['result_url'] = $this->exportExcel($header, $excel_data, $file_name)['data'];
        return $result;
    }


    /**
     * @param $file_url
     * @param $user
     * @return string[]
     * @throws BusinessException
     * @throws GuzzleException
     * @throws ValidationException
     */
    public function batchEdit($file_url, $user): array
    {
        $result = [
            'result_url'  => '',
            'success_num' => 0,
            'fail_num'    => 0,
        ];
        [$title, $excel_data] = $this->getBatchExcelData($file_url);

        foreach ($excel_data as $key => $excel_item) {
            if (empty($excel_item[0])) {
                continue;
            }
            $data_item = [];
            try {
                //SIM序列号只能填写18或者13位数字
                if (!is_numeric($excel_item[0]) || !in_array(strlen($excel_item[0]),
                        [self::SIM_CARD_NUMBER_LEN_TRUE, self::SIM_CARD_NUMBER_LEN_AIS])) {
                    throw new ValidationException(self::$t->_('20211_error_message_005'));
                }
                $excel_data[$key][0] = strval($excel_item[0]);

                //SIM序列号状态不是文档里所允许的值，请检
                if (!empty($excel_item[1])) {
                    if (!in_array($excel_item[1][0], [SimCardModel::STATUS_AVAILABLE, SimCardModel::STATUS_ABANDON])) {
                        throw new ValidationException(self::$t->_('20211_error_message_013'));
                    }
                    $data_item['status'] = $excel_item[1][0];
                }

                $simCardCheck = SimCardModel::findFirst([
                    'conditions' => 'number = :number:',
                    'bind'       => ['number' => $excel_item[0]],
                ]);
                if (empty($simCardCheck)) {
                    throw new ValidationException(self::$t->_('20211_error_message_015'));
                }

                $data_item['id']        = $simCardCheck->id;
                $data_item['type_text'] = $excel_item[2] ?? '';
                $data_item['remark']    = $excel_item[3] ?? '';
                $excel_data[$key][4]    = self::$t->_('excel_result_success');
                $this->edit($data_item, $user, SimCardLogModel::TYPE_BATCH_EDIT);
                $result['success_num']++;
            } catch (Exception $e) {
                $result['fail_num']++;
                $excel_data[$key][4] = $e->getMessage();
            }
        }
        $header               = array_shift($title);
        $excel_data           = array_merge(array_values($title), $excel_data);
        $file_name            = 'batch_edit_call_number_result_' . date('YmdHis') . '.xlsx';
        $result['result_url'] = $this->exportExcel($header, $excel_data, $file_name)['data'];
        return $result;
    }


    /**
     * 增加操作记录
     * @param $sim_card_id
     * @param $type
     * @param $user
     * @param $before
     * @param $after
     * @return mixed
     */
    public function addLog($sim_card_id, $type, $user, $before, $after)
    {
        $model                    = new SimCardLogModel();
        $data['sim_card_id']      = $sim_card_id;
        $data['operator']         = $user['id'];
        $data['operator_name']    = $user['name'];
        $data['operator_dept_id'] = $user['node_department_id'];
        $data['type']             = $type;
        $data['before']           = json_encode($before, JSON_UNESCAPED_UNICODE);
        $data['after']            = json_encode($after, JSON_UNESCAPED_UNICODE);
        return $model->i_create($data);
    }


}
