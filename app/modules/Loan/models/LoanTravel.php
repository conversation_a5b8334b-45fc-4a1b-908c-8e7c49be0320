<?php

namespace App\Modules\Loan\Models;

use App\Models\Base;

class LoanTravel extends Base
{
    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        $this->setSource('loan_travel');

        $this->hasMany(
            'travel_id',
            LoanTravelLog::class,
            'original_id', [
                'params' => [
                    'conditions' => "type = 10"
                ],
                "alias" => "Log",
            ]
        );

    }

    /**
     * @param $condition
     * @return mixed
     */
    public static function getFirst($condition)
    {
        return parent::findFirst($condition);
    }

    /**
     * @param null $parameters
     * @return mixed
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }
}
