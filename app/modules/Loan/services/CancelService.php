<?php

namespace App\Modules\Loan\Services;

use App\Library\Enums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Modules\Loan\Models\Contract;
use App\Modules\Loan\Models\Loan;
use App\Modules\Workflow\Models\WorkflowRequestModel;
use App\Modules\Workflow\Services\WorkflowServiceV2;

class CancelService extends BaseService
{
    public static $validate_cancel = [
        'id' => 'Required|IntGe:1',                                  //借款ID
        'note' => 'Required|StrLenGeLe:'.parent::REQUIRED_LONG_TEXT_LEN
    ];

    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return CancelService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self();
        }
        return self::$instance;
    }
}
