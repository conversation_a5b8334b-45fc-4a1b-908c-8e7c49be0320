<?php

namespace App\Modules\Loan\Services;

use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\PayEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\OssHelper;
use App\Library\Validation\ValidationException;
use App\Models\oa\LoanReturnModel;
use App\Models\oa\SysAttachmentModel;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Common\Services\WaterMarkerService;
use App\Modules\Loan\Models\Loan;
use App\Modules\Pay\Services\PayFlowService;
use App\Modules\Pay\Services\PayService;
use App\Modules\Reimbursement\Services\ReimbursementFlowService;
use App\Modules\User\Services\UserService;
use App\Modules\Workflow\Services\FYRService;
use App\Modules\Workflow\Services\WorkflowServiceV2;
use App\Repository\oa\LoanRepository;
use Mpdf\Mpdf;

class DetailService extends BaseService
{
    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return DetailService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 借款详情
     * @param $params
     * @param $uid
     * @param boolean $if_download
     * @return array|mixed
     * @throws BusinessException
     */
    public function getDetail($params, $uid = 0, $if_download = false)
    {
        $id = $params['id'];
        $fyr_id = $params['fyr_id'] ?? 0;

        $loan = Loan::findFirst([
            'id = :id:',
            'bind' => ['id' => $id]
        ]);

        if (empty($loan)) {
            return [];
        }

        $req = (new LoanFlowService())->getRequest($id);
        if (empty($req->id)) {
            throw new BusinessException('获取工作流批次失败', ErrCode::$CONTRACT_GET_WORK_REQUEST_ERROR);
        }

        $travel = $loan->getTravel();//借款差旅信息
        $pay_bank = $loan->getBank(['columns' => 'name, account, bank_type']);//借款银行信息
        $pay = $loan->getPay(['columns' => 'create_id, is_sign, not_sign_reason, pay_type, sign_name, pay_date, sign_date, mark, pay_bank_name, pay_bank_account, created_at']);//借款支付信息
        $file = $loan->getFile(['columns' => 'bucket_name, object_key, file_name']);//借款单附件
        $consent = $loan->getFileConsent(['columns' => 'bucket_name, object_key, file_name']);//借款同意书

        $data = $loan->toArray();
        $data['travel'] = $travel ? $travel->toArray() : [];//借款差旅信息
        if (!empty($data['travel']) && $if_download) {
            $log = $travel->getLog();
            if ($log) {
                $temp = $log->toArray();
                $tempLog = [];
                foreach ($temp as $k => $v) {
                    //去掉待审核的
                    if ($v['to_status_type'] == 1) {
                        continue;
                    }

                    $tempLog[] = $v;
                }

                $data['travel']['log'] =$tempLog;
            }
        }

        $data['bank'] = $pay_bank ? $pay_bank->toArray() : [];//借款银行信息
        $data['event_file'] = $file ? $file->toArray() : [];//借款单附件
        $data['consent_file'] = $consent ? $consent->toArray() : [];//借款同意书
        $data['pay'] = $pay ? $pay->toArray() : [];//借款支付信息

        // 被征询ID与征询内容
        $data['ask_id'] = '0';
        $data['fyr_info'] = (object)[];
        // 新逻辑
        if ($fyr_id) {
            $fyr_info = FYRService::getInstance()->getFyrInfoById($req->id, $fyr_id, $uid);
            if (!empty($fyr_info)) {
                $data['fyr_info'] = $fyr_info;
                $data['ask_id'] = $fyr_info['ask_id'];
            }

        } else {
            // 历史逻辑
            $ask = FYRService::getInstance()->getRequestToByReplyAsk($req, $uid);
            $data['ask_id'] = $ask ? $ask->id : '0';
        }

        $data['auth_logs'] = $this->getAuditLogs($req, $data, $if_download);

        //获取可编辑字段
        $data['can_edit'] = false;
        $data['can_edit_fields']  = (object)[];
        //借款单最新一笔归还详情
        $data['back_return_last_info'] = [];
        //借款单历史归还记录
        $data['back_return_history'] = [];
        //转账归还小计
        $data['back_amount'] = 0;
        //薪资抵扣小计
        $data['salary_back_amount'] = 0;

        //15873需求逻辑判断是否有归还行为，依据借款单状态!=无需归还
        if ($data['loan_status'] != Enums\LoanEnums::LOAN_STATUS_NO_NEED_RETURN) {
            //历史归还记录
            $back_return_history_list = LoanReturnModel::find([
                'conditions' => 'loan_id = :loan_id:',
                'bind' => ['loan_id' => $data['id']],
                'columns' => ['id', 'loan_id', 'back_apply_date', 'back_type', 'back_amount', 'back_actual_amount', 'back_date', 'back_mark', 'back_progress', 'created_at', 'is_new'],
                'order' => 'id DESC'
            ])->toArray();
            if (!empty($back_return_history_list)) {
                $back_loan_id = 0;
                $back_return_ids = [];
                foreach ($back_return_history_list as $history) {
                    if ($history['is_new'] == Enums\LoanEnums::IS_NEW_NO) {
                        //单笔归还
                        $back_loan_id = $data['id'];
                    } else if ($history['back_type'] == Enums\LoanEnums::LOAN_BACK_TYPE_TRANSFER) {
                        //多笔归还&&是转账归还才需要查询附件
                        $back_return_ids[] = $history['id'];
                    }
                    if ($history['back_type'] == Enums\LoanEnums::LOAN_BACK_TYPE_TRANSFER && $history['back_actual_amount'] > 0) {
                        $data['back_amount'] = bcadd($data['back_amount'], $history['back_actual_amount']);
                    } else if ($history['back_type'] == Enums\LoanEnums::LOAN_BACK_TYPE_SALARY) {
                        $data['salary_back_amount'] = bcadd($data['salary_back_amount'], $history['back_actual_amount']);
                    }
                }
                //单笔归还的附件是绑定在借款单身上的所以需要拿借款单去查
                $loan_bank_attachments = [];
                if (!empty($back_loan_id)) {
                    $loan_bank_attachments = $loan->getBacks(['columns' => 'bucket_name, object_key, file_name']);
                }

                //多笔归还的附件是绑定在归还单身上的所以需要拿归还单去查
                $return_bank_attachments = [];
                if (!empty($back_return_ids)) {
                    $bank_attachments = SysAttachmentModel::find([
                        'conditions' => 'oss_bucket_type = :oss_bucket_type: and sub_type = 1 and deleted = :is_deleted: and oss_bucket_key in ({oss_bucket_key:array})',
                        'bind' => ['oss_bucket_type' => Enums::OSS_BUCKET_TYPE_LOAN_BACK, 'is_deleted' => GlobalEnums::IS_NO_DELETED, 'oss_bucket_key' => $back_return_ids],
                        'columns' => 'id, oss_bucket_key, bucket_name, object_key, file_name'
                    ])->toArray();
                    if (!empty($bank_attachments)) {
                        foreach ($bank_attachments as $attachment) {
                            if (isset($return_bank_attachments[$attachment['oss_bucket_key']])) {
                                array_push($return_bank_attachments[$attachment['oss_bucket_key']], $attachment);
                            } else {
                                $return_bank_attachments[$attachment['oss_bucket_key']][] = $attachment;
                            }
                        }
                    }
                }
                foreach ($back_return_history_list as &$return) {
                    $return['created_at'] = substr($return['created_at'], 0 , 16);//创建时间
                    $return['back_amount'] = empty($return['back_amount']) ? 0 : bcdiv($return['back_amount'], 1000, 2);//归还金额
                    $return['back_actual_amount'] = empty($return['back_actual_amount']) ? '0.00' : bcdiv($return['back_actual_amount'], 1000, 2);//实际归还金额
                    $return['back_type_text'] = static::$t->_(Enums\LoanEnums::$loan_return_back_type[$return['back_type']]);//归还方式文本
                    $return['back_progress_text'] = static::$t->_(Enums\LoanEnums::$loan_return_back_progress[$return['back_progress']]);//归还进度文本
                    $return['bank_attachments'] = ($return['is_new'] == Enums\LoanEnums::IS_NEW_NO) ? $loan_bank_attachments ? $loan_bank_attachments->toArray() : [] : (!empty($return_bank_attachments) && isset($return_bank_attachments[$return['id']]) ? $return_bank_attachments[$return['id']] : []);//银行流水附件

                    if ($return['back_type'] == Enums\LoanEnums::LOAN_BACK_TYPE_TRANSFER) {
                        //借款单最新的一笔归还记录[16053需求，借款详情页面的归还申请不展示薪资抵扣的数据]
                        $data['back_return_last_info'] = $data['back_return_last_info'] ? $data['back_return_last_info'] : $return ;
                    }
                }
                $data['back_return_history'] = $back_return_history_list;
                //获取借款归还审核-审批请求
                if (!empty($data['back_return_last_info']['id'])) {
                    $back_req = (new LoanBackFlowService())->getRequest($data['back_return_last_info']['id']);
                    $can_edit_data = (new LoanBackFlowService())->getCanEditFieldByReq($back_req, $uid);
                    if (!empty($can_edit_data)) {
                        $data['can_edit'] = true;
                        $data['can_edit_fields'] = $can_edit_data;
                    }
                }
            }
        }
        return $this->handleData($data);
    }

    /**
     * 借款详情
     * @param $params
     * @param $uid
     * @param $type
     * @return array
     */
    public function getCommonDetail($params, $uid, $type = 0)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';

        $loan = [];
        try {
            // 支付详情: 当前用户 须是 支付人
            if ($type == self::LIST_TYPE_PAY) {
                $pay_staff_id = $this->getLoanPayStaffIds();
                if (!in_array($uid, $pay_staff_id)) {
                    throw new ValidationException(static::$t->_('user_no_pay_permission_error'), ErrCode::$VALIDATE_ERROR);
                }
            }

            $loan = $this->getDetail($params, $uid);
            if (empty($loan['id'])) {
                throw new ValidationException(static::$t->_('biz_order_data_is_null', ['biz_id' => $params['id']]), ErrCode::$VALIDATE_ERROR);
            }

        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning('loan-get-detail-failed:' . $e->getMessage());
        } catch (\Exception $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->error('loan-get-detail-failed:' . $e->getMessage());
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $loan
        ];
    }

    /**
     * @param $id
     * @param $uid
     * @return array
     */
    public function download($id, $uid)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $lang = $this->getLang();
        if ('th' == $lang && in_array(get_country_code(),GlobalEnums::$default_en_country_list)) {
            $lang = 'en';
        }

        $url = [];

        try {
            $data = $this->getDetail(['id' => $id], $uid, true);
            if (empty($data['id'])) {
                throw new BusinessException('获取借款信息失败', ErrCode::$CONTRACT_GET_INFO_ERROR);
            }
            // 合同币种
            $data['currency_text'] = static::$t->_(Enums\GlobalEnums::$currency_item[$data['currency']] ?? '');
            // 传固定模版
            $data['loan_download_template_content'] = EnvModel::getEnvByCode('loan_download_template_content','');

            $file_path = sys_get_temp_dir() . '/';
            $file_name = "loan_".md5($id)."_".$lang.".pdf";
            $travel_file_name = "loan_travel_".md5($id)."_".$lang.".pdf";

            $view = new \Phalcon\Mvc\View();
            $path = APP_PATH . '/views';
            $view->setViewsDir($path);
            $view->setVars($data);

            $view->start();
            $view->disableLevel(
                [
                    \Phalcon\Mvc\View::LEVEL_LAYOUT => false,
                    \Phalcon\Mvc\View::LEVEL_MAIN_LAYOUT => false,
                ]
            );

            //代码里审批日志用的倒序
            $view->render("loan", "apply_".$lang);
            $view->finish();
            $content = $view->getContent();

            $mpdf = new Mpdf([
                'format' => 'A4',
                'mode'=>'zh-CN'
            ]);

            $mpdf->useAdobeCJK = true;
            $mpdf->SetDisplayMode('fullpage');
            $mpdf->SetHTMLHeader("");
            $mpdf->SetHTMLFooter("");
            $mpdf->WriteHTML($content);
            $mpdf->Output($file_path.$file_name,"f");

            // pdf 加水印
            WaterMarkerService::getInstance()->addWaterMarkerToPdfFile($file_path.$file_name, $file_path.$file_name);

            // 生成成功, 上传OSS
            $upload_res = OssHelper::uploadFile($file_path.$file_name);
            $url['loan.pdf'] = !empty($upload_res['object_url']) ? $upload_res['object_url'] : '';

            if(!empty($data['travel'])){
                $data['travel']['create_name'] = $data['create_name'];
                $data['travel']['create_id'] = $data['create_id'];

                $url['loan_travel.pdf'] = $this->getTravelPdf($travel_file_name,$data['travel'],$lang);
            }

        }catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        }catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }catch (\Mpdf\MpdfException $e){
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('loan-detail-download-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => ["url"=>$url]
        ];

    }

    /**
     * 格式化借款详情
     * @param $data
     * @return array
     */
    private function handleData($data)
    {
        if (empty($data) || !is_array($data)) {
            return [];
        }

        $data['left_amount'] = bcdiv(bcsub(bcsub(bcsub($data['amount'],  $data['re_amount']), $data['back_amount']), $data['salary_back_amount']), 1000, 2);//预计需归还金额（未还款金额）
        $data['back_amount'] = empty($data['back_amount']) ? 0 : bcdiv($data['back_amount'], 1000, 2);//转账归还小计
        $data['salary_back_amount'] = empty($data['salary_back_amount']) ? 0 : bcdiv($data['salary_back_amount'], 1000, 2);//薪资抵扣小计
        $data['re_amount'] = empty($data['re_amount']) ? 0 : bcdiv($data['re_amount'], 1000, 2);//报销抵扣小计
        $data['amount'] = bcdiv($data['amount'], 1000, 2);
        if (!empty($data['travel'])) {
            if (!empty($data['travel']['pic'])) {
                $data['travel']['pic'] = explode(',', $data['travel']['pic']);
            } else {
                $data['travel']['pic'] = [];
            }

            $data['travel']['is_single_text'] = static::$t->_(Enums::$loan_traffic_single[$data['travel']['is_single']]);
            $data['travel']['status_text'] = static::$t->_(Enums::$loan_traffic_status[$data['travel']['status']]);
            if ($data['travel']['transport']!= Enums::TRAFFIC_TOOLS_OTHER ) {
                $data['travel']['transport_text'] = !empty(Enums::$loan_traffic_tools[$data['travel']['transport']]) ? static::$t->_(Enums::$loan_traffic_tools[$data['travel']['transport']]) ?? '' : '';
            }
        }

        $data['lname'] = static::$t->_('loan_name', ['year' => date('Y', strtotime($data['created_at'])), 'month' => date('m', strtotime($data['created_at']))]);
        $data['create_display_department_name'] = !empty($data['create_node_department_name']) ? $data['create_node_department_name'] : $data['create_department_name'];
        return $data;
    }

    private function getTravelPdf($file_name,$travel,$lang){

        $view = new \Phalcon\Mvc\View();
        $path = APP_PATH . '/views';
        $view->setViewsDir($path);
        $view->setVars($travel);

        $view->start();
        $view->disableLevel(
            [
                \Phalcon\Mvc\View::LEVEL_LAYOUT => false,
                \Phalcon\Mvc\View::LEVEL_MAIN_LAYOUT => false,
            ]
        );

        $view->render("loan", "travel_".$lang);
        $view->finish();
        $content = $view->getContent();

        $path = sys_get_temp_dir() . '/';
        $mpdf = new Mpdf([
            'format' => 'A4',
            'mode'=>'zh-CN'
        ]);

        $mpdf->useAdobeCJK = true;
        $mpdf->SetDisplayMode('fullpage');
        $mpdf->SetHTMLHeader("");
        $mpdf->SetHTMLFooter("");
        $mpdf->WriteHTML($content);
        $mpdf->Output($path.$file_name ,"f");

        // pdf 加水印
        WaterMarkerService::getInstance()->addWaterMarkerToPdfFile($path.$file_name, $path.$file_name);

        // 生成成功, 上传OSS
        $upload_res = OssHelper::uploadFile($path.$file_name);

        return !empty($upload_res['object_url']) ? $upload_res['object_url'] : '';
    }

    /**
     * @param $req
     * @param $loan
     * @param bool $if_download
     * @return array
     * @throws BusinessException
     */
    private function getAuditLogs($req,$loan,$if_download=false)
    {
        $auth_logs = (new WorkflowServiceV2())->getAuditLogs($req);

        //下载的时候不要申请
        if($if_download){
            $temp = [];
            foreach ($auth_logs as $k=>$v){
                //如果申请的就跳过
                if($v['action']==0){
                    continue;
                }
                $temp[] = $v;
            }
            $auth_logs = $temp;
        }


        $loan_pay_staff_id = $this->getLoanPayStaffIds();
        //查询支付模块的审批流
        if ($loan['is_pay_module'] == PayEnums::BIZ_DATA_IS_PAY_MODULE_YES) {
            $payment_data = PayService::getInstance()->getPaymentByBusinessNo(Enums\BankFlowEnums::BANK_FLOW_OA_TYPE_LOAN, $loan['lno']);
            if (!empty($payment_data)) {
                $pay_flow_service = new PayFlowService();
                $payment_audit_logs = $pay_flow_service->getAuditLogs($payment_data, true);
                //上下文必须保证两个数组是索引数组,且$payment_audit_logs排在$auth_logs之前
                $auth_logs = array_merge($payment_audit_logs, $auth_logs);
                //查到支付模块数据直接返回, 没查到的继续走下边的拼接支付人逻辑(兼容开启支付模块后历史数据审批通过未支付完成的)
                return $auth_logs;
            }
        }
        $us = new UserService();
        //审核通过
        if($loan['status']==Enums::CONTRACT_STATUS_APPROVAL && $loan['pay_status'] == Enums::LOAN_PAY_STATUS_PENDING) {
            $payPendingLogs = [
                'staff_id' => '',
                'staff_name' => '',
                'action_name' => self::$t->_(Enums::$loan_pay_status[$loan['pay_status']]),
                'audit_at' => $loan['approved_at'],
                'audit_at_datetime' => $loan['approved_at'],
                'action' => 5,
                "info" => ''
            ];
            foreach ($loan_pay_staff_id as $staff_id) {
                $current = $us->getUserById($staff_id);
                if (!empty($current) && !is_string($current)) {
                    //待支付
                    $payPendingLogs['list'][] = [
                        'staff_id' => $staff_id,
                        'staff_name' => $this->getNameAndNickName($current->name,$current->nick_name??''),
                        'staff_department' => $current->getDepartment()->name ?? '',
                        'job_title' => $current->getJobTitle()->name ??'',
                    ];

                }
            }
            array_unshift($auth_logs,$payPendingLogs);
        }
        //只有通过的时候要
        if($loan['status'] == Enums::CONTRACT_STATUS_APPROVAL && ($loan['pay_status'] == Enums::LOAN_PAY_STATUS_PAY || $loan['pay_status'] == Enums::LOAN_PAY_STATUS_NOTPAY)){
            //支付操作后，不要待支付
            $current = $us->getUserById($loan['pay']['create_id']);
            //放入付款
            if($current){
                $payLogs = [
                    'staff_id' => $loan['pay']['create_id'],
                    'staff_name' => $this->getNameAndNickName($current->name,$current->nick_name??''),
                    'staff_department' => $current->getDepartment()->name ?? '',
                    'job_title' => $current->getJobTitle()->name ??'',
                    'action_name' => self::$t->_(Enums::$loan_pay_status[$loan['pay_status']]),
                    'audit_at' => $loan['pay']['created_at'],
                    'audit_at_datetime'=>$loan['pay']['created_at'],
                    'action' => 5,
                    "info"=>""
                ];
                array_unshift($auth_logs,$payLogs);
            }
        }

        return $auth_logs;
    }

    private function getLang(){
        $lang = self::$language;
        if(empty($lang) || !in_array($lang,["th","en","zh-CN"],1)){
            $lang ="th";
        }
        return $lang;
    }

    /**
     * 获取某条归还单审批流日志
     * @param integer $loan_return_id 归还单id
     * @return array
     */
    public function getLoanReturnAuthLog($loan_return_id)
    {
        $back_req = (new LoanBackFlowService())->getRequest($loan_return_id);
        return (new WorkflowServiceV2())->getAuditLogs($back_req);
    }
}
