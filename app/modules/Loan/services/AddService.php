<?php

namespace App\Modules\Loan\Services;

use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\LoanEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Models\oa\LoanReturnModel;
use App\Models\oa\LoanStaffGradeAmountModel;
use App\Models\oa\SysAttachmentModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Loan\Models\Loan;
use App\Modules\Loan\Models\LoanPayBank;
use App\Modules\Loan\Models\LoanStaffAmount;
use App\Modules\Loan\Models\LoanTravel;
use App\Modules\Reimbursement\Models\Reimbursement;
use App\Modules\Reimbursement\Models\ReimbursementRelLoan;
use App\Modules\User\Models\AttachModel;
use App\Modules\User\Models\TripModel;
use App\Repository\HrStaffRepository;
use App\Util\RedisKey;

use App\Library\Enums\StaffInfoEnums;

class AddService extends BaseService
{
    public static $not_must_params = [

    ];

    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return AddService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * @param $data
     * @param $user
     * @return array
     */
    public function one($data, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $exists = Loan::getFirst([
                'conditions' => 'lno = ?1',
                'columns' => 'id',
                'bind' => [1 => $data['lno']]
            ]);

            if (!empty($exists)) {
                throw new ValidationException(static::$t->_('loan_add_error_001', ['loan_no' => $data['lno']]), ErrCode::$VALIDATE_ERROR);
            }
            //非在职状态 不能申请
            if($user['state'] != StaffInfoEnums::STAFF_STATE_IN || $user['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES){
                throw new ValidationException(static::$t->_('loan_state_notice'), ErrCode::$VALIDATE_ERROR);
            }


            $sap_company_ids = EnumsService::getInstance()->getSettingEnvValueIds('purchase_sap_company_ids');
            if (isset($data['create_company_id']) && in_array($data['create_company_id'], $sap_company_ids) && empty($data['cost_center_name'])) {
                throw new ValidationException(static::$t->_('cost_center_required_error'), ErrCode::$VALIDATE_ERROR);
            }

            $amount = $data['amount'];//前端提交过来的金额
            $data = $this->handleData($data, $user);

            // v19041 借款申请 新流程
            // 员工是否 可 多次借款
            $loan_staff_amount = LoanStaffAmount::findFirst([
                'conditions' => ' staff_info_id = :staff_info_id:',
                'bind' => [
                    'staff_info_id' => $user['id']
                ]
            ]);

            //当前币种申请借款金额 = 申请借款金额*换算汇率，保留两位小数
            $amount = EnumsService::getInstance()->amountExchangeRateCalculation($amount, $data['exchange_rate']);

            // 可多次借款
            if (!empty($loan_staff_amount)) {
                //满足一，新增逻辑判断二：判断未归还金额+此次申请金额是否超过最大借款设置（loan_max_amount）
                $loan_max_amount = $loan_staff_amount->amount ?? 0;
                //当前币种剩余未归还金额，保留的是2位小数
                $staff_not_back_amount = $this->getStaffLoanOutstandingAmountReal($data['create_id']);


                //累加剩余未归还金额+申请借款金额，保留两位小数
                $total_amount = bcadd($amount, $staff_not_back_amount, 2);

                //最大借款值配置值与员工总借款值比较
                $loan_max_amount = (string)$loan_max_amount;
                if (bccomp($loan_max_amount, $total_amount, 2) === -1) {
                    //超过设置的最大借款金额
                    throw new ValidationException(static::$t->_('pass_loan_max_amount'), ErrCode::$VALIDATE_ERROR);
                }
            } else {
                // 不可多次借款
                // 判断该员工名下“已审核”，“已支付”的借款单，所关联的报销单为（1）“待审核”，或者（2）“已审核”&“待支付”的报销单
                $check_res = $this->checkStaffIsExistUnWrittenReimbursementOrder($user['id']);
                if (!empty($check_res)) {
                    throw new ValidationException(static::$t->_('loan_add_error_002'), ErrCode::$VALIDATE_ERROR);
                }

                // 是否存在未归还的借款单
                $is_havenot_back_loan = $this->isHaveNotBackLoan($user['id']);
                if (!empty($is_havenot_back_loan)) {
                    throw new ValidationException(static::$t->_('loan_can_not_apply'), ErrCode::$VALIDATE_ERROR);
                }

                // 职级借款标准
                $loan_staff_grade_status = EnumsService::getInstance()->getSettingEnvValue('loan_staff_grade_status', 0);
                if ($loan_staff_grade_status) {
                    //开启了，需要判断借款金额是否超过员工当前职级对应的最大金额
                    $apply_user_info = (new HrStaffRepository())->getStaffById($user['id']);
                    $staff_grade_amount_limit = 0;
                    //员工信息存在，且职级非空
                    if ($apply_user_info && !is_null($apply_user_info['job_title_grade_v2'])) {
                        $staff_grade_amount_info = LoanStaffGradeAmountModel::findFirst([
                            'conditions' => ' job_title_grade = :job_title_grade:',
                            'bind' => [
                                'job_title_grade' => $apply_user_info['job_title_grade_v2']
                            ]
                        ]);
                        $staff_grade_amount_limit = $staff_grade_amount_info ? $staff_grade_amount_info->amount : $staff_grade_amount_limit;
                    }
                    if (bccomp($staff_grade_amount_limit, $amount, 2) === -1) {
                        throw new ValidationException(static::$t->_('loan_staff_grade_amount_pass'), ErrCode::$VALIDATE_ERROR);
                    }
                }
            }

            //差旅
            if ($data['type'] == Enums::LOAN_TYPE_TRAVEL) {
                if ($data['travel']['status'] != Enums::TRAFFIC_STATUS_APPROVAL) {
                    throw new ValidationException(static::$t->_('loan_travel_status_error'), ErrCode::$VALIDATE_ERROR);
                }
                if ($this->isSubmmited($data['travel']['travel_id'])) {
                    throw new ValidationException(static::$t->_('loan_travel_has_benn_exist'), ErrCode::$VALIDATE_ERROR);
                }
            }

            //采购
            if ($data['type'] == Enums::LOAN_TYPE_PURCHASE) {
                if ($this->isPurchaseAmountGt($data['real_amount'], $data['currency'])) {
                    if (GlobalEnums::LA_COUNTRY_CODE == get_country_code()) {
                        $validations = static::$t->_('purchase_require_amount_2');
                    } else {
                        $validations = static::$t->_('purchase_require_amount');
                    }
                    throw new ValidationException($validations, ErrCode::$VALIDATE_ERROR);
                }
                if ($this->isPurchaseUpToThree($user['id'])) {
                    throw new ValidationException(static::$t->_('purchase_require_times'), ErrCode::$VALIDATE_ERROR);
                }
            }

            //保存借款信息
            $model = new Loan();
            $bool = $model->i_create($data);
            if ($bool === false) {
                throw new BusinessException('借款创建失败=' . json_encode($data, JSON_UNESCAPED_UNICODE), ErrCode::$CONTRACT_CREATE_ERROR);
            }

            //银行转账
            if ($model->pay_type == Enums::LOAN_PAY_TYPE_BANK) {
                $pay_bank = new LoanPayBank();
                $data['bank']['loan_id'] = $model->id;
                $pay_bank_bool = $pay_bank->i_create($data['bank']);

                if ($pay_bank_bool === false) {
                    throw new BusinessException('借款银行信息创建失败=' . json_encode($data['bank'], JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                }
            }

            //差旅信息
            if ($model->type == Enums::LOAN_TYPE_TRAVEL) {
                $travel = new LoanTravel();
                $data['travel']['loan_id'] = $model->id;
                $data['travel']['pic'] = implode(",", $data['travel']['pic']);
                $travel_bool = $travel->i_create($data['travel']);
                if ($travel_bool === false) {
                    throw new BusinessException('借款差旅信息创建失败=' . json_encode($data['travel'], JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                }
            }
            //同意书附件
            $attachArr = [];
            foreach ($data['consent_file'] as $consent_k => $consent_v) {
                $tmp = [];
                $tmp['oss_bucket_type'] = Enums::OSS_BUCKET_TYPE_LOAN_CONSENT;
                $tmp['oss_bucket_key'] = $model->id;
                $tmp['sub_type'] = 0;
                $tmp['bucket_name'] = $consent_v['bucket_name'];
                $tmp['object_key'] = $consent_v['object_key'];
                $tmp['file_name'] = $consent_v['file_name'];
                $attachArr[] = $tmp;
            }

            //附件信息
            if (!empty($data['event_file'])) {
                foreach ($data['event_file'] as $k => $file) {
                    $tmp = [];
                    $tmp['oss_bucket_type'] = Enums::OSS_BUCKET_TYPE_LOAN;
                    $tmp['oss_bucket_key'] = $model->id;
                    $tmp['sub_type'] = 0;
                    $tmp['bucket_name'] = $file['bucket_name'];
                    $tmp['object_key'] = $file['object_key'];
                    $tmp['file_name'] = $file['file_name'];
                    $attachArr[] = $tmp;
                }
            }
            $attach = new AttachModel();
            $attach_bool = $attach->batchInsert($attachArr);
            if ($attach_bool === false) {
                throw new BusinessException('借款附件创建失败,data=' . json_encode($attachArr, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }

            //创建付款申请审批流
            $flow_bool = (new LoanFlowService())->createRequest($model->id, $user);
            if ($flow_bool === false) {
                throw new BusinessException('借款申请审批流创建失败, data=' . json_encode($model->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }

            $db->commit();
        } catch (ValidationException $e) {      //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {        //业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {               //系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->warning('loan-create-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message
        ];
    }

    /**
     * 判断该员工名下是否存在 已审核 && 已支付 的借款单，所关联的报销单为（1）待审核 或者（2）已审核 && 待支付 的报销单
     *
     * @param int $staff_id
     * @return mixed
     */
    public function checkStaffIsExistUnWrittenReimbursementOrder(int $staff_id)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['loan' => Loan::class]);
        $builder->leftjoin(ReimbursementRelLoan::class, 'loan.id = rel.loan_id', 'rel');
        $builder->leftjoin(Reimbursement::class, 'reimbursement.id = rel.re_id', 'reimbursement');
        $builder->where('loan.create_id = :create_id: AND loan.status = :loan_status: AND loan.pay_status = :loan_pay: AND rel.is_deleted = :is_deleted:', [
            'create_id' => $staff_id,
            'loan_status' => Enums::WF_STATE_APPROVED,
            'loan_pay' => Enums::PAYMENT_PAY_STATUS_PAY,
            'is_deleted' => GlobalEnums::IS_NO_DELETED
        ]);

        $builder->andWhere('reimbursement.status IN ({reimbursement_status:array}) AND reimbursement.pay_status = :reimbursement_pay:', [
            'reimbursement_status' => [Enums::WF_STATE_PENDING, Enums::WF_STATE_APPROVED],
            'reimbursement_pay' => Enums::PAYMENT_PAY_STATUS_PENDING
        ]);

        $builder->columns('loan.id');
        return $builder->getQuery()->getSingleResult();
    }

    /**
     * @param $data
     * @param $user
     * @return array
     */
    private function handleData($data, $user)
    {
        $data['real_amount'] = $data['amount'];
        $data['amount'] = bcmul($data['amount'], 1000);
        $data['status'] = Enums::CONTRACT_STATUS_PENDING;
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['updated_at'] = $data['created_at'];
        $data['create_id'] = $user['id'] ?? 0;
        $data['create_name'] = $this->getNameAndNickName($user['name'] ?? '',$user['nick_name']);
        $data['back_date'] = (isset($data['back_date']) && !empty($data['back_date'])) ? $data['back_date'] : null;

        // 获取币种与系统默认币种的汇率
        $exchange_rate = EnumsService::getInstance()->getCurrencyExchangeRate($data['currency']);
        $data['exchange_rate'] = $exchange_rate ? $exchange_rate : 1;
        $data['is_new'] = LoanEnums::IS_NEW_YES;

        return $data;
    }
    /**
     * @param $user
     * @return array
     */

    public function defaultData($user){

        $code = ErrCode::$SUCCESS;
        $message = 'success';
        $real_message = '';

        $data = [];

        try{
            if(empty($user)){
                throw new ValidationException("The employee information is null [{$user['id']}]", ErrCode::$VALIDATE_ERROR);
            }

            $arr = $this->getUserMetaFromBi($user['id']);
            if(empty($arr)){
                throw new ValidationException(static::$t->_("re_staff_info_id_not_exist"), ErrCode::$VALIDATE_ERROR);
            }

            $now_date = date("Ymd");
            $year = date("Y",strtotime($now_date));
            $month = date("m",strtotime($now_date));

            $data['lname'] = static::$t->_('loan_name',["year"=>$year,"month"=>$month]);
            $data['lno'] = static::genSerialNo('',RedisKey::LOAN_CREATE_COUNTER);
            $data['create_id'] = $user['id'];

            $data = array_merge($data,$arr);
            $data['create_name'] = $this->getNameAndNickName($user['name']??"",$user['nick_name']);

            $data['create_date'] = date("Y-m-d");

            $data['cost_center_id'] = '0';

            //$data['bank'] = $this->getBank($model);
            $data['travel'] = $this->getTravel($user['id']);

            //空
            if(empty($data['travel'])) {
                $data['travel_flag']=Enums::LOAN_TRAVEL_FLAG_NOTSUBMIT;
            }else {
                if($data['travel']['status'] == Enums::TRAFFIC_STATUS_APPROVAL){
                    $data['travel_flag']=Enums::LOAN_TRAVEL_FLAG_SUBMIT;
                }else{
                    $data['travel_flag']=Enums::LOAN_TRAVEL_FLAG_NOTSUBMIT;
                }
                if($this->isSubmmited($data['travel']['travel_id'])){
                    $data['travel_flag']=Enums::LOAN_TRAVEL_FLAG_SUBMITED;
                }
            }
            $data['travel_flag'].="";
            $data['pay_purchase_flag']= !$this->isPurchaseUpToThree($user['id']);

        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();

        } catch (\Exception $e){
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $data = [];
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('loan-get-default-data-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' =>$data
        ];
    }

    private function getTravel($userId)
    {
        $start = date('Y-m-d H:i:s', strtotime('-30 days'));
        $model = TripModel::findFirst([
            'conditions' => 'apply_user = :apply_user: AND created_at > :created_at: AND status != :status:',
            'bind' => [
                'apply_user' => $userId,
                'created_at' => $start,
                'status' => Enums::WF_STATE_CANCEL
            ],
            'order'=>'id desc'
        ]);

        $data = [];
        if (empty($model)) {
            return $data;
        }

        $data['travel_id'] = $model->id;
        $data['serial_no'] = $model->serial_no;
        $data['reason'] = $model->reason_application;
        $data['transport'] = $model->traffic_tools;     //1飞机 2火车 3汽车 4其他,other_traffic_name

        if ($data['transport'] == Enums::TRAFFIC_TOOLS_OTHER) {
            $data['transport_text'] = $model->other_traffic_name;
        } else {
            $data['transport_text'] = !empty(Enums::$loan_traffic_tools[$data['transport']]) ? static::$t->_(Enums::$loan_traffic_tools[$data['transport']]) : '';
        }

        $data['is_single'] = $model->oneway_or_roundtrip;
        $data['is_single_text'] = !empty(Enums::$loan_traffic_single[$data['is_single']]) ? static::$t->_(Enums::$loan_traffic_single[$data['is_single']]) : '';

        $data['start_city'] = $model->departure_city;
        $data['end_city'] = $model->destination_city;
        $data['start_date'] = $model->start_time;
        $data['end_date'] = $model->end_time;
        $data['days'] = $model->days_num;
        $data['pic'] = [];
        $data['created_at'] = $model->created_at;

        $pics = $model->getPic();
        if (!empty($pics)) {
            $picArr = array_column($pics->toArray(), 'img_path');
            $data['pic'] = $picArr;
        }

        $data['mark'] = $model->remark;
        $data['status'] = $model->status;
        $data['status_text'] =  static::$t->_(Enums::$loan_traffic_status[$data['status']]);
        return $data;
    }

    public function isSubmmited($travel_id){
        $temp = $this->modelsManager->createBuilder()
        ->from(["lt"=>LoanTravel::class])
        ->join(Loan::class,"l.id=lt.loan_id","l")
        ->andWhere('lt.travel_id=:travel_id:', ['travel_id' => $travel_id])
        ->andWhere("l.status not in (".Enums::CONTRACT_STATUS_REJECTED.",".Enums::CONTRACT_STATUS_CANCEL.")")
        ->getQuery()
        ->execute()->toArray();
        if(!empty($temp)){
            return true;
        }else{
            return false;
        }
    }

    /**
     * 判断借款-采购 当月审核提交的非撤回的是否超过3次
     * @param $uid
     * @return bool
     */
    private function isPurchaseUpToThree($uid){
        $temp = Loan::count([
            'conditions' => 'create_id = ?1 and type=2 and status!=4 and created_at>=?2',
            'columns' => 'id',
            'bind' => [1 => $uid,2=>date("Y-m-01 00:00:00")]
        ]);

        if($temp<3){
            return false;
        }else{
            return true;
        }
    }

    /**
     * 判断采购 金额是否超过2000
     * @param $amount
     * @param $currency
     * @return bool
     */
    private function isPurchaseAmountGt($amount,$currency){
        // 老挝最大值为4000000
        if ('LA' == get_country_code()) {
            $max_amount =4000000;
        } else {
            $max_amount =2000;
        }
        //美元
        if($currency == GlobalEnums::CURRENCY_USD){
            $max_amount = 100;
        }
        if($amount>$max_amount){
            return true;
        }else{
            return false;
        }
    }


    /**
     * 借款审核添加==
     * @param array $param 归还单参数组
     * @param array $user 归还借款的用户信息组
     * @return array
     */
    public function backAdd($param, $user)
    {

        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $loan = Loan::findFirst([
                'conditions' => 'id = :id: and status = :status: and pay_status = :pay_status: and create_id = :user_id:',
                'bind'=> ['id' => $param['id'], 'status' => Enums::WF_STATE_APPROVED, 'pay_status' => Enums::LOAN_PAY_STATUS_PAY, 'user_id' => $user['id']]
            ]);

            //查询借款单信息是否存在
            if (empty($loan)) {
                throw new ValidationException(static::$t->_('loan_notfound_or_status_error'), ErrCode::$VALIDATE_ERROR);
            }

            //15873需求变更为按照借款单状态=未开始归还或部分归还才可归还
            if (!in_array($loan->loan_status, [LoanEnums::LOAN_STATUS_NOT_START_RETURN, LoanEnums::LOAN_STATUS_PARTIAL_RETURN])) {
                throw new ValidationException(static::$t->_('access_data_audit_rejected_1'), ErrCode::$VALIDATE_ERROR);
            }

            //15873需求借款单存在转账归还中的归还单不可提交
            $loan_returning_count = LoanReturnModel::count([
                'conditions' => 'loan_id = :loan_id: and back_status = :back_status: and back_type = :back_type:',
                'bind' => ['loan_id' => $loan->id, 'back_status' => LoanEnums::LOAN_BACK_STATUS_ING, 'back_type' => LoanEnums::LOAN_BACK_TYPE_TRANSFER]
            ]);
            if ($loan_returning_count > 0) {
                throw new ValidationException(static::$t->_('loan_return_has_in_approval'), ErrCode::$VALIDATE_ERROR);
            }

            //本次归还金额
            $back_amount = bcmul($param['back_amount'], 1000);
            //借款金额>0&&<=剩余未归还金额(本次归还金额 > 剩余未还金额)
            if (bccomp($back_amount, bcsub($loan->amount, bcadd($loan->re_amount, $loan->back_amount))) === 1 ) {
                throw new ValidationException(static::$t->_('back_amount_lncorrect'), ErrCode::$VALIDATE_ERROR);
            }

            //验证都通过了，开始录入归还记录表
            $now = date('Y-m-d H:i:s');
            $loan_return = new LoanReturnModel();
            $loan_return_data = [
                'back_no' => static::genSerialNo(LoanEnums::LOAN_RETURN_BACK_APPLY_NO_PREFIX, 'loan_return_back_apply_counter', 5),//归还单申请编号
                'back_apply_date' => date('Y-m-d'),//归还申请日期
                'loan_id' => $loan->id,//借款单id
                'back_amount' => $back_amount,//归还金额
                'back_status' => LoanEnums::LOAN_BACK_STATUS_ING,//还款状态
                'back_mark' => $param['back_mark'],//备注
                'back_date' => !empty($param['back_date']) ? $param['back_date'] : null,//归还时银行流水日期
                'is_new' => LoanEnums::IS_NEW_YES,
                'created_at' => $now,
                'updated_at' => $now
            ];
            $return_bool = $loan_return->save($loan_return_data);
            if ($return_bool === false) {
                //记录归还明细失败
                throw new BusinessException('借款归还申请-创建失败, 原因可能是: ' . get_data_object_error_msg($loan_return) . '; 数据: ' . json_encode($loan_return_data, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }
            //若是旧的单笔归还，则需要把关联借款单的附件给删掉
            if ($loan->is_new == LoanEnums::IS_NEW_NO) {
                //如果有老的附件清空
                $back_files = $loan->getBacks();
                foreach ($back_files as $back_file) {
                    $back_file->deleted = GlobalEnums::IS_DELETED;
                    $back_file->save();
                }
            }

            //新的借款归还附件信息要绑定归还单明细表id
            if (!empty($param['bank_attachments'])) {
                $attachments = [];
                foreach ($param['bank_attachments'] as $attachment) {
                    $tmp = [];
                    $tmp['oss_bucket_type'] =  Enums::OSS_BUCKET_TYPE_LOAN_BACK;
                    $tmp['sub_type'] =  1;//0是单笔归还附件，1多笔归还附件
                    $tmp['oss_bucket_key'] = $loan_return->id;
                    $tmp['file_name'] = $attachment['file_name'];
                    $tmp['bucket_name'] = $attachment['bucket_name'];
                    $tmp['object_key'] = $attachment['object_key'];
                    $attachments[] = $tmp;
                }
                $sys_attachment = new SysAttachmentModel();
                $attach_bool = $sys_attachment->batch_insert($attachments);
                if ($attach_bool === false) {
                    throw new BusinessException('借款-归还附件创建失败, 原因可能是: ' . get_data_object_error_msg($sys_attachment) . '; 数据: ' . json_encode($attachments, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                }
            }

            //创建审批流绑定到归还单身上
            $flow_bool = (new LoanBackFlowService())->createRequest($loan_return->id, $user);
            if ($flow_bool === false) {
                throw new BusinessException(static::$t->_('loan_create_work_flow_failed')."==back", ErrCode::$LOAN_CREATE_WORK_FLOW_ERROR);
            }

            $db->commit();
        } catch (ValidationException $e) {      //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {        //业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {               //系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->warning('loan-backAdd-create-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => []
        ];
    }

    /**
     * 是否还有未归还的借款
     * @param int $user_id 申请人工号
     * @return mixed
     */
    public function isHaveNotBackLoan(int $user_id)
    {
        //付款状态，不算未支付;15873需求按照借款单状态判断是否有为归还借款（申请人 && 借款审批状态在待审批或审批通过 &&  待支付或已支付 && 无需归还或归还状态在未开始归还或部分归还）
        return Loan::findFirst([
            'conditions' => ' create_id = :user_id: and status in ({status:array}) and pay_status in ({pay_status:array}) and loan_status in ({loan_status:array})',
            'bind' => [
                'user_id' => $user_id,
                'status' => [Enums::WF_STATE_PENDING, Enums::WF_STATE_APPROVED],
                'pay_status' => [Enums::LOAN_PAY_STATUS_PENDING, Enums::LOAN_PAY_STATUS_PAY],
                'loan_status' => [LoanEnums::LOAN_STATUS_NO_NEED_RETURN, LoanEnums::LOAN_STATUS_NOT_START_RETURN, LoanEnums::LOAN_STATUS_PARTIAL_RETURN]
            ]
        ]);
    }

    /**
     * 用于报销时验证是否存在未冲减,为归还的借款
     * 获取某员工借款剩余未归还总金额
     * @param int $staff_id 员工工号
     * @param int $use_module 用途模块1：报销，2：借款，默认为报销
     * @return mixed
     */
    public function getStaffLoanOutstandingAmount(int $staff_id)
    {
        $default_currency = (new EnumsService())->getSysCurrencyInfo();
        $outstanding_amount = 0;
        try {
            //报销模块获取员工借款总额(已审批通过 且 已支付状态)（带币种）
            $conditions =  'create_id = :create_id: AND status = :status: AND pay_status = :pay_status:';
            $bind = [
                'create_id' => $staff_id,
                'status' => Enums::CONTRACT_STATUS_APPROVAL,
                'pay_status' => Enums::PAYMENT_PAY_STATUS_PAY
            ];
            $loan_total_amount = Loan::findFirst([
                'conditions' => $conditions,
                'bind' => $bind,
                'columns' => 'SUM(if(currency='.$default_currency['code'].', amount, amount*exchange_rate)) AS amount',
            ])->amount ?? 0;
            /**
             * 由获取员工报销单中已核销的金额(报销单状态: 待审核 OR (已通过 && 待支付) OR 已支付)逻辑修改为
             * 根据当前申请人工号，去查reimbursement表里面 loan amount！=0的数据(`status`=1 or (`status`=3 and pay_status in(1,2)))，
             * 查到后，根据reimbursement_rel_loan的关系，查到报销单关联的loan no，取对应的create id=当前申请人工号的，
             * 就是有效的冲减报销的金额。
             */
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['l' => ReimbursementRelLoan::class]);
            $builder->columns('SUM(if(r.currency='.$default_currency['code'].', r.loan_amount, r.loan_amount*r.exchange_rate)) as loan_amount');
            $builder->leftjoin(Loan::class, 'lo.id=l.loan_id', 'lo');
            $builder->leftjoin(Reimbursement::class, 'r.id=l.re_id', 'r');
            $builder->where("lo.create_id = :create_id: and r.loan_amount != :loan_amount: and l.is_deleted = :is_deleted:", ['create_id' => $staff_id, 'loan_amount' => 0, 'is_deleted' => GlobalEnums::IS_NO_DELETED]);
            $builder->andWhere("r.status = :status_pending: OR (r.status = :status_approval: AND r.pay_status IN ({pay_status_item:array}))",[
                'status_pending' => Enums::CONTRACT_STATUS_PENDING,
                'status_approval' => Enums::CONTRACT_STATUS_APPROVAL,
                'pay_status_item' => [
                    Enums::PAYMENT_PAY_STATUS_PENDING,
                    Enums::PAYMENT_PAY_STATUS_PAY
                ]]);
            $amount_info = $builder->getQuery()->getSingleResult();
            $loan_write_off_amount = $amount_info->loan_amount ?? 0;

            // 获取员工借款15873需求变更为借款状态非无需归还的归还金额（不扣减转账归还中的归还金额，跟产品确定了）
            $loan_back_amount = Loan::findFirst([
                    'conditions' => 'create_id = :create_id: AND loan_status != :loan_status: ',
                    'bind' => [
                        'create_id' => $staff_id,
                        'loan_status' => LoanEnums::LOAN_STATUS_NO_NEED_RETURN
                    ],
                    'columns' => 'SUM(if(currency='.$default_currency['code'].', back_amount, back_amount*exchange_rate)) AS back_amount',
                ])->back_amount ?? 0;


            $outstanding_amount = bcdiv(bcsub(bcsub($loan_total_amount, $loan_write_off_amount), $loan_back_amount), 1000, 2);


        } catch (\Exception $e) {
            $this->logger->warning('获取申请人剩余未还总金额异常: staff_id = ' . $staff_id);
        }

        $this->logger->info("获取申请人剩余未还总金额: staff_id = $staff_id, amount = $outstanding_amount");

        return $outstanding_amount;

    }

    /**
     * 查询员工在费用公司下是否存在未归还完的借款单
     * @param $staff_id
     * @param $company_id
     * @return bool
     * @date 2023/4/7
     */
    public function getStaffUnReturnByCompany($staff_id, $company_id)
    {
        $exist = false;
        $company_loan = Loan::findFirst([
            'columns' => 'id',
            'conditions' => 'create_id = :create_id: AND create_company_id = :create_company_id: AND loan_status in ({loan_status:array}) and amount > re_amount + back_amount',
            'bind' => [
                'create_id' => $staff_id,
                'create_company_id' => $company_id,
                'loan_status' => [LoanEnums::LOAN_STATUS_NOT_START_RETURN, LoanEnums::LOAN_STATUS_PARTIAL_RETURN],
            ],
        ]);
        if ($company_loan) {
            $exist = true;
        }
        return $exist;
    }

    /**
     * 验证借款单是否可以用来抵扣报销单
     * @param $loan_id
     * @param $apply_id
     * @param $company_id
     * @return void
     * @throws ValidationException
     */
    public function checkLoanReimbursement($loan_id, $apply_id, $company_id)
    {
        $loan_info = Loan::findFirst([
            'columns' => 'id, create_id, create_company_id, loan_status',
            'conditions' => 'id = :id:',
            'bind' => [
                'id' => $loan_id,
            ]
        ]);
        if (empty($loan_info)) {
            throw new ValidationException(static::$t->_('reimbursement_check_loan_id_not_exist'), ErrCode::$VALIDATE_ERROR);
        }
        if ($loan_info->create_id != $apply_id) {
            throw new ValidationException(static::$t->_('reimbursement_check_create_id_error'), ErrCode::$VALIDATE_ERROR);
        }
        if ($loan_info->create_company_id != $company_id) {
            throw new ValidationException(static::$t->_('reimbursement_check_company_id_error'), ErrCode::$VALIDATE_ERROR);
        }
        if (!in_array($loan_info->loan_status, [LoanEnums::LOAN_STATUS_NOT_START_RETURN, LoanEnums::LOAN_STATUS_PARTIAL_RETURN])) {
            throw new ValidationException(static::$t->_('reimbursement_check_loan_status_error'), ErrCode::$VALIDATE_ERROR);
        }
    }

    /**
     * 用于提交借款单时验证剩余可借款额度
     * 获取某员工借款剩余未归还总金额  借款金额(待审核,申通通过待支付,审核通过已支付) - 已支付的报销单冲减金额 - 已归还的归还金额
     * @param int $staff_id
     * @return int|string|null
     * @date 2022/6/23
     */
    public function getStaffLoanOutstandingAmountReal(int $staff_id)
    {
        $default_currency = EnumsService::getInstance()->getSysCurrencyInfo();
        $outstanding_amount = 0;
        try {
            //借款模块获取员工借款总额(1. 审批通过 且 支付状态=已支付状态；2. 申请状态=待审核；3. 审批通过且支付状态=待支付)申请总金额（带币种））
            $conditions =  'create_id = :create_id: AND (status = :status_pending: OR (status = :status_approval: AND pay_status IN ({pay_status:array})))';
            $bind = [
                'create_id' => $staff_id,
                'status_pending' => Enums::CONTRACT_STATUS_PENDING,
                'status_approval' => Enums::CONTRACT_STATUS_APPROVAL,
                'pay_status' => [
                    Enums::PAYMENT_PAY_STATUS_PENDING,
                    Enums::PAYMENT_PAY_STATUS_PAY
                ],
            ];
            $loan_total_amount = Loan::findFirst([
                'conditions' => $conditions,
                'bind' => $bind,
                'columns' => 'SUM(if(currency='.$default_currency['code'].', amount, amount*exchange_rate)) AS amount',
            ])->amount ?? 0;

            //查询员工报销冲减总金额(已支付)
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['l' => ReimbursementRelLoan::class]);
            $builder->columns('SUM(if(r.currency='.$default_currency['code'].', r.loan_amount, r.loan_amount*r.exchange_rate)) as loan_amount');
            $builder->leftjoin(Loan::class, 'lo.id=l.loan_id', 'lo');
            $builder->leftjoin(Reimbursement::class, 'r.id=l.re_id', 'r');
            $builder->where("lo.create_id = :create_id: and r.loan_amount != :loan_amount: and l.is_deleted = :is_deleted:", ['create_id' => $staff_id, 'loan_amount' => 0, 'is_deleted' => GlobalEnums::IS_NO_DELETED]);
            $builder->andWhere("r.status = :status_approval: AND r.pay_status = :pay_status_item:",[
                'status_approval' => Enums::CONTRACT_STATUS_APPROVAL,
                'pay_status_item' => Enums::PAYMENT_PAY_STATUS_PAY
            ]);
            $amount_info = $builder->getQuery()->getSingleResult();
            $loan_write_off_amount = $amount_info->loan_amount ?? 0;

            // 获取员工已归还金额
            $loan_back_amount = Loan::findFirst([
                'conditions' => 'create_id = :create_id: AND (loan_status > :loan_status: AND pay_status = :pay_status:)',
                'bind' => [
                    'create_id' => $staff_id,
                    'loan_status' => LoanEnums::LOAN_STATUS_NOT_START_RETURN,
                    'pay_status' => Enums::PAYMENT_PAY_STATUS_PAY
                ],
                'columns' => 'SUM(if(currency='.$default_currency['code'].', back_amount, back_amount*exchange_rate)) AS back_amount',
            ])->back_amount ?? 0;

            $outstanding_amount = bcdiv(bcsub(bcsub($loan_total_amount, $loan_write_off_amount), $loan_back_amount), 1000, 2);

        } catch (\Exception $e) {
            $this->logger->warning('获取申请人剩余未还总金额异常: staff_id = ' . $staff_id);
        }

        $this->logger->info("获取申请人剩余未还总金额: staff_id = $staff_id, amount = $outstanding_amount");

        return $outstanding_amount;
    }

    /**
     * 批量获取各员工的借款未归还金额
     * 16052需求，用于向HCM端【Flash员工离职管理】提供借款未归还金额
     * 借款未归还金额 =  借款金额(待审核,申通通过待支付,审核通过已支付) - 已支付的报销单冲减金额 - 已归还的归还金额
     * @param array $staff_ids 员工id组
     * @return array
     */
    public function batchGetStaffLoanOutstandingAmountReal(array $staff_ids)
    {
        $outstanding_amount = [];

        //当前币种
        $default_currency = (new EnumsService())->getSysCurrencyInfo();

        //借款模块获取员工借款总额(1. 审批通过 且 支付状态=已支付状态；2. 申请状态=待审核；3. 审批通过且支付状态=待支付)申请总金额（带币种））
        $builder = $this->modelsManager->createBuilder();
        $builder->from(Loan::class);
        $builder->columns('create_id, SUM(if(currency='.$default_currency['code'].', amount, amount*exchange_rate)) AS amount');
        $builder->where('create_id in ({create_ids:array}) and status in ({status:array}) and pay_status in ({pay_status:array})', ['create_ids' => $staff_ids, 'status' => [Enums::WF_STATE_PENDING, Enums::WF_STATE_APPROVED], 'pay_status' => [Enums::PAYMENT_PAY_STATUS_PENDING, Enums::PAYMENT_PAY_STATUS_PAY]]);
        $builder->groupBy('create_id');
        $sum_list = $builder->getQuery()->execute()->toArray();
        $sum_user_loan_amount = array_column($sum_list, 'amount' , 'create_id');

        //查询员工报销冲减总金额(已支付)
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['l' => ReimbursementRelLoan::class]);
        $builder->leftjoin(Loan::class, 'lo.id=l.loan_id', 'lo');
        $builder->leftjoin(Reimbursement::class, 'r.id=l.re_id', 'r');
        $builder->columns('lo.create_id, SUM(if(r.currency='.$default_currency['code'].', r.loan_amount, r.loan_amount*r.exchange_rate)) as loan_amount');
        $builder->where("lo.create_id in ({create_ids:array}) and r.loan_amount != :loan_amount: and l.is_deleted = :is_deleted:", ['create_ids' => $staff_ids, 'loan_amount' => 0, 'is_deleted' => GlobalEnums::IS_NO_DELETED]);
        $builder->andWhere("r.status = :status_approval: AND r.pay_status = :pay_status_item:",[
            'status_approval' => Enums::CONTRACT_STATUS_APPROVAL,
            'pay_status_item' => Enums::PAYMENT_PAY_STATUS_PAY
        ]);
        $builder->groupBy('lo.create_id');
        $sum_reimbursement_list = $builder->getQuery()->execute()->toArray();
        $sum_user_reimbursement_amount = array_column($sum_reimbursement_list, 'loan_amount' , 'create_id');

        // 获取员工已归还金额
        $builder = $this->modelsManager->createBuilder();
        $builder->from(Loan::class);
        $builder->columns('create_id, SUM(if(currency='.$default_currency['code'].', back_amount, back_amount*exchange_rate)) AS back_amount');
        $builder->where('create_id in ({create_ids:array}) AND (loan_status > :loan_status: AND pay_status = :pay_status:)', ['create_ids' => $staff_ids, 'loan_status' => LoanEnums::LOAN_STATUS_NOT_START_RETURN, 'pay_status' => Enums::PAYMENT_PAY_STATUS_PAY]);
        $builder->groupBy('create_id');
        $sum_loan_back_list = $builder->getQuery()->execute()->toArray();
        $sum_user_loan_back_amount = array_column($sum_loan_back_list, 'back_amount' , 'create_id');

        foreach ($staff_ids as $staff_id) {
            $user_loan_amount = $sum_user_loan_amount[$staff_id] ?? 0;//员工借款金额
            $user_reimbursement_amount = $sum_user_reimbursement_amount[$staff_id] ?? 0;//员工报销冲减总金额(已支付)
            $user_loan_back_amount = $sum_user_loan_back_amount[$staff_id] ?? 0;//员工员工已归还金额
            $outstanding_amount[$staff_id] = bcdiv(bcsub(bcsub($user_loan_amount, $user_reimbursement_amount), $user_loan_back_amount), 1000, 2);
        }
        return $outstanding_amount;
    }
}
