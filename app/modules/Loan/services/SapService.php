<?php
/**
 * Created by PhpStorm.
 * Date: 2022/8/5
 * Time: 15:08
 */

namespace App\Modules\Loan\Services;


use App\Library\Enums\GlobalEnums;
use App\Modules\Reimbursement\Models\RequestSapLog;
use App\Modules\Reimbursement\Services\SapService as reSapService;
use App\Modules\Purchase\Services\SapsService;


class SapService extends BaseService
{
    private static $instance;
    private static $sap_loan_route = '/sap/yyd8h6xfdy_managecust_receival';


    private function __construct()
    {

    }

    /**
     * @return \App\Modules\Loan\Services\SapService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    public function loanSap($data)
    {

        try {
            if (empty($data)) {
                return [];
            }

            $data['loan_note'] = reSapService::getInstance()->loanNote($data['create_id'],$data['lno']);



            $post_xml='<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:glob="http://sap.com/xi/SAPGlobal20/Global">
   <soapenv:Header/>
   <soapenv:Body>
      <glob:Cust_ReceivalesPayableCreateRequest_sync>
         <BasicMessageHeader>
         </BasicMessageHeader>
         
     <Cust_ReceivalesPayable>                
                                                             
             <!--供应商:-->                                                                        
            <BusinessPartnerInternalID>S00214</BusinessPartnerInternalID>                                                                        
            <!--公司:-->                                                                        
            <CompanyID>' . $data['cost_company_id'] . '</CompanyID>                                                                        
            <!--供应商贷记4:-->                                                                        
            <ReceivablesPayablesEntryTypeCode>4</ReceivablesPayablesEntryTypeCode>                                                                        
            <!--外部参考号:-->                                                                        
            <PartnerBaseBusinessTransactionDocumentReference>'.$data['lno'].'</PartnerBaseBusinessTransactionDocumentReference>                                                                        
            <!--单据描述:-->                                                                        
            <Description>'.$data['create_id'].'</Description>                                                                        
            <!--货币:-->                                                                        
            <TransactionCurrencyCode>' . $data['currency'] . '</TransactionCurrencyCode>                                                                        
            <!--过账日期:-->                                                                        
            <PostingDate>'.$data['pay_transaction_date'].'</PostingDate>                                                                        
            <!--单据日期:-->                                                                        
            <BusinessTransactionDocumentDate>'.$data['create_date'].'</BusinessTransactionDocumentDate>                                                                        
            <!--Optional:-->                                                                        
            <CountryCode>'.get_country_code().'</CountryCode>                                                                        
            <!--Zero or more repetitions:-->        
                                                                            
            <item>                                                                        
               <!--默认值:-->                                                                        
               <PropertyMovementDirectionCode></PropertyMovementDirectionCode>                                                                        
               <!--总账科目:-->                                                                        
               <GeneralLedgerAccountAliasCode>********</GeneralLedgerAccountAliasCode>                                                                        
               <TransactionCurrencyNetAmount currencyCode="' . $data['currency'] . '">'.$data['amount'].'</TransactionCurrencyNetAmount>                                                                        
                                                                            
                <!--成本中心:-->                                                                        
               <CostCentreID></CostCentreID>                                                                        
               <!--利润中心-->                                                                        
               <ProfitCentreID></ProfitCentreID> 
               <!--税务代码:-->
               <TaxCode></TaxCode>
               <!--行项目描述:-->
               <ItemDescription>'. $data['loan_note'].'</ItemDescription>                                                                                                
            </item>
                                                                
         </Cust_ReceivalesPayable>        

      </glob:Cust_ReceivalesPayableCreateRequest_sync>
   </soapenv:Body>
</soapenv:Envelope>';

            $method = self::$sap_loan_route;
            if (get_country_code() == GlobalEnums::PH_COUNTRY_CODE && in_array(env('runtime'), ['dev', 'test', 'tra', 'training'])) {
                $method = env('sap_loan_route');
            }

            $this->logger->info('loan_sap_post_data ' . $post_xml);
            $xml = \App\Modules\Purchase\Services\SapsService::getInstance()->httpRequestXml($method , $post_xml);
            $this->logger->info('loan_sap_return_data ' . $xml);

            preg_match_all("/\<Cust_ReceivalesPayable\>(.*?)\<\/Cust_ReceivalesPayable\>/s", $xml, $re_data);
            $return_data = [];
            if (isset($re_data[0][0]) && !empty($re_data[0][0])) {
                $return_data = \App\Modules\Purchase\Services\SapsService::getInstance()->xmlToArray($re_data[0][0]);
            }

            preg_match_all("/\<Log\>(.*?)\<\/Log\>/s", $xml, $log);
            $return_data['log'] = '';

            if (isset($log[0][0]) && !empty($log[0][0])) {
                $return_data['log'] = \App\Modules\Purchase\Services\SapsService::getInstance()->xmlToArray($log[0][0]);
            }

            $logModel = new RequestSapLog();

            $logModel->save(['uuid' => $return_data['SAP_UUID'] ?? '','order_code'=>$data['lno'], 'type' => 6, 'request_data' => $post_xml, 'response_data' => $xml ?? '', 'create_at' => date('Y-m-d H:i:s')]);

            return $return_data;

        } catch (Exception $e) {
            $this->logger->warning('loan_sap_service_exception: ' . $e->getMessage());
            return [];
        }

    }

    /**
     * 借款归还发送sap
     * @param array $data 参数组
     * @param string $country_code 国家码
     * @return array|mixed
     */
    public function loanBackSap($data, $country_code)
    {

        try {
            if (empty($data)) {
                return [];
            }

            $data['loan_note'] = reSapService::getInstance()->loanNote($data['create_id'],$data['lno']);
            $post_xml='<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:glob="http://sap.com/xi/SAPGlobal20/Global">
   <soapenv:Header/>
   <soapenv:Body>
      <glob:Cust_ReceivalesPayableCreateRequest_sync>
         <BasicMessageHeader>
         </BasicMessageHeader>
         
     <Cust_ReceivalesPayable>                
                                                             
             <!--供应商:-->                                                                        
            <BusinessPartnerInternalID>S00214</BusinessPartnerInternalID>                                                                        
            <!--公司:-->                                                                        
            <CompanyID>' . $data['cost_company_id'] . '</CompanyID>                                                                        
            <!--供应商贷记4:-->                                                                        
            <ReceivablesPayablesEntryTypeCode>3</ReceivablesPayablesEntryTypeCode>                                                                        
            <!--外部参考号:-->                                                                        
            <PartnerBaseBusinessTransactionDocumentReference>'.$data['lno'].'</PartnerBaseBusinessTransactionDocumentReference>                                                                        
            <!--单据描述:-->                                                                        
            <Description>'.$data['create_id'].'</Description>                                                                        
            <!--货币:-->                                                                        
            <TransactionCurrencyCode>' . $data['currency'] . '</TransactionCurrencyCode>                                                                        
            <!--过账日期:-->                                                                        
            <PostingDate>'.$data['back_transaction_date'].'</PostingDate>                                                                        
            <!--单据日期:-->                                                                        
            <BusinessTransactionDocumentDate>'.$data['create_date'].'</BusinessTransactionDocumentDate>                                                                        
            <!--Optional:-->                                                                        
            <CountryCode>'.$country_code.'</CountryCode>                                                                        
            <!--Zero or more repetitions:-->        
                                                                            
            <item>                                                                        
               <!--默认值:-->                                                                        
               <PropertyMovementDirectionCode></PropertyMovementDirectionCode>                                                                        
               <!--总账科目:-->                                                                        
               <GeneralLedgerAccountAliasCode>********</GeneralLedgerAccountAliasCode>                                                                        
               <TransactionCurrencyNetAmount currencyCode="' . $data['currency'] . '">'.$data['back_amount'].'</TransactionCurrencyNetAmount>                                                                        
                                                                            
                <!--成本中心:-->                                                                        
               <CostCentreID></CostCentreID>                                                                        
               <!--利润中心-->                                                                        
               <ProfitCentreID></ProfitCentreID> 
               <!--税务代码:-->
               <TaxCode></TaxCode>
               <!--行项目描述:-->
               <ItemDescription>'. $data['loan_note'].'</ItemDescription>                                                                                                
            </item>
                                                                
         </Cust_ReceivalesPayable>        

      </glob:Cust_ReceivalesPayableCreateRequest_sync>
   </soapenv:Body>
</soapenv:Envelope>';
            $method = self::$sap_loan_route;

            if ($country_code == GlobalEnums::PH_COUNTRY_CODE && in_array(env('runtime'), ['dev', 'test', 'tra', 'training'])) {
                $method = env('sap_loan_route');
            }
            $xml = SapsService::getInstance()->httpRequestXml($method, $post_xml);
            preg_match_all("/\<Cust_ReceivalesPayable\>(.*?)\<\/Cust_ReceivalesPayable\>/s", $xml, $re_data);
            $return_data = [];
            if (isset($re_data[0][0]) && !empty($re_data[0][0])) {
                $return_data = SapsService::getInstance()->xmlToArray($re_data[0][0]);
            }

            preg_match_all("/\<Log\>(.*?)\<\/Log\>/s", $xml, $log);
            $return_data['log'] = '';

            if (isset($log[0][0]) && !empty($log[0][0])) {
                $return_data['log'] = SapsService::getInstance()->xmlToArray($log[0][0]);
            }
            $logModel = new RequestSapLog();
            $logModel->save(['uuid' => $return_data['SAP_UUID'] ?? '','order_code'=>$data['back_no'], 'type' => 7, 'request_data' => $post_xml, 'response_data' => $xml ?? '', 'create_at' => date('Y-m-d H:i:s')]);

            return $return_data;

        } catch (Exception $e) {
            $this->logger->warning('loan_back_sap_service_exception: ' . $e->getMessage());
            return [];
        }

    }

}