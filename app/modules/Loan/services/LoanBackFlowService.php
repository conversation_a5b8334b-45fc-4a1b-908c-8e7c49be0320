<?php


namespace App\Modules\Loan\Services;


use App\Library\Enums;
use App\Library\Enums\ContractEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Models\oa\LoanReturnModel;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Loan\Models\Loan;
use App\Modules\Reimbursement\Services\ReimbursementFlowService;
use App\Modules\Workflow\Models\WorkflowRequestModel;
use App\Modules\Workflow\Services\AbstractFlowService;
use App\Modules\Workflow\Services\WorkflowServiceV2;
use App\Repository\HrStaffRepository;

class LoanBackFlowService extends AbstractFlowService
{
    /**
     * 归还单审批通过
     * @param int $loan_return_id 归还单ID
     * @param string $note 驳回原因
     * @param array $user 审核人信息组
     * @param array $update_data 审核过程中可编辑的信息，如银行流水日期
     * @return array
     */
    public function approve($loan_return_id, $note, $user, $update_data = [])
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_oa');
        try {

            $db->begin();
            $loan_return = LoanReturnModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $loan_return_id]
            ]);
            //未找到归还单信息
            if (empty($loan_return)) {
                throw new ValidationException(static::$t->_('loan_return_notfound'), ErrCode::$VALIDATE_ERROR);
            }
            //归还单已撤回审批，不可在操作
            if ($loan_return->back_audit_status == Enums::WF_STATE_CANCEL) {
                throw new ValidationException(static::$t->_('workflow_has_been_withdrawal'), ErrCode::$VALIDATE_ERROR);
            }
            //获取归还单审批流
            $work_req = $this->getRequest($loan_return_id);
            if (empty($work_req->id)) {
                throw new BusinessException('获取工作流批次失败', ErrCode::$CONTRACT_GET_WORK_REQUEST_ERROR);
            }

            $now = date('Y-m-d H:i:s');
            // 财务节点可修改银行流水日期
            $can_edit_data = (new LoanBackFlowService())->getCanEditFieldByReq($work_req, $user['id']);
            if (!empty($can_edit_data)) {
                // 是否更新银行流水日期
                $update_back_data = [
                    'back_date' =>  $update_data['update_data']['back_date'],
                    'back_transaction_date' => $update_data['update_data']['back_date'],
                    'updated_at' => $now
                ];
                $bool = $loan_return->i_update($update_back_data);
                if ($bool === false) {
                    throw new BusinessException('借款归还-审批传银行流水日期-更新失败, 原因可能是: ' . get_data_object_error_msg($loan_return) . json_encode($update_back_data, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                }
            }

            $result = (new WorkflowServiceV2())->doApprove($work_req, $user, $this->getLoanWorkflowParams($loan_return, $user), $note);
            if (!empty($result->approved_at)) {
                //终审通过需要更新归还单信息
                $loan_return_data = [
                    'back_status' => Enums\LoanEnums::LOAN_BACK_STATUS_BACK,//归还状态=已归还
                    'back_actual_amount' => $loan_return->back_amount,//实际归还金额
                    'back_audit_status' =>  Enums::WF_STATE_APPROVED,//审核状态通过
                    'back_approved_at' => $result->approved_at,//审批通过时间
                    'back_progress' => Enums\LoanEnums::LOAN_RETURN_BACK_PROGRESS_DONE,//处理进度已处理
                    'updated_at' => $now,//更新时间
                ];
                $bool = $loan_return->i_update($loan_return_data);
                if ($bool === false) {
                    throw new BusinessException('借款归还-审批通过，更新归还单信息失败, 原因可能是: ' . get_data_object_error_msg($loan_return) . json_encode($loan_return_data, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                }
                //归还单终审通过需要更新借款单借款状态
                $loan = Loan::findFirst([
                    'conditions' => 'id = :id:',
                    'bind' => ['id' => $loan_return->loan_id]
                ]);
                $loan->back_amount += $loan_return->back_amount;//将本次归还金额累计到借款单归还金额中
                //已归还金额
                $paid_return_amount = $loan->re_amount + $loan->back_amount;
                if ($paid_return_amount < $loan->amount) {
                    //部分归还：已归还金额<借款金额
                    $loan->loan_status = Enums\LoanEnums::LOAN_STATUS_PARTIAL_RETURN;
                } else if ($paid_return_amount == $loan->amount) {
                    //已还清：已归还总金额=借款金额
                    $loan->loan_status = Enums\LoanEnums::LOAN_STATUS_PAID_OFF;
                } else if ($paid_return_amount > $loan->amount) {
                    //超额归还：已归还总金额＞借款金额
                    $loan->loan_status = Enums\LoanEnums::LOAN_STATUS_OVER_RETURN;
                }
                $loan->updated_at = $now;
                $bool = $loan->save();
                if ($bool === false) {
                    throw new BusinessException('借款归还-审批通过，更新借款单信息失败, 原因可能是: ' . get_data_object_error_msg($loan) . json_encode(['loan_status' => $loan->loan_status, 'back_amount' => $loan->back_amount], JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                }
            }
            $db->commit();
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->warning('loan-back-approve-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
        ];
    }

    /**
     * 归还单驳回
     * @param int $loan_return_id 归还单ID
     * @param string $note 驳回原因
     * @param array $user 审核人信息组
     * @return array
     */
    public function reject($loan_return_id, $note, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_oa');
        try {

            $db->begin();
            $loan_return = LoanReturnModel::findFirst([
               'conditions' => 'id = :id:',
                'bind' => ['id' => $loan_return_id]
            ]);
            //未找到归还单信息
            if (empty($loan_return)) {
                throw new ValidationException(static::$t->_('loan_return_notfound'), ErrCode::$VALIDATE_ERROR);
            }
            //归还单已撤回审批，不可在操作
            if ($loan_return->back_audit_status == Enums::WF_STATE_CANCEL) {
                throw new ValidationException(static::$t->_('workflow_has_been_withdrawal'), ErrCode::$VALIDATE_ERROR);
            }
            //获取归还单审批流
            $work_req = $this->getRequest($loan_return_id);
            if (empty($work_req->id)) {
                throw new BusinessException('获取工作流批次失败', ErrCode::$CONTRACT_GET_WORK_REQUEST_ERROR);
            }
            $result = (new WorkflowServiceV2())->doReject($work_req, $user, $this->getLoanWorkflowParams($loan_return, $user), $note);
            if ($result === false) {
                throw new BusinessException('审批流拒绝失败', ErrCode::$CONTRACT_WORK_FLOW_REJECT_ERROR);
            }
            $bool = $loan_return->i_update([
                'back_audit_status' => Enums::WF_STATE_REJECTED,
                'back_rejected_at' => $result->rejected_at,
                'back_refuse_reason' => $note,
                'back_status' => Enums\LoanEnums::LOAN_BACK_STATUS_NOT,
                'back_progress' => Enums\LoanEnums::LOAN_RETURN_BACK_PROGRESS_DONE,
                'updated_at' => date('Y-m-d H:i:s'),
            ]);
            if ($bool === false) {
                throw new BusinessException('同步合同信息失败', ErrCode::$CONTRACT_UPDATE_ERROR);
            }
            $db->commit();
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->warning('loan-return-reject-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
        ];
    }

    /**
     * 暂时没有用到
     * @param $id
     * @param $note
     * @param $user
     * @return array
     */
    public function cancel($id, $note, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $work_req = $this->getRequest($id);
        $db = $this->getDI()->get('db_oa');
        try {

            $db->begin();

            $loan = Loan::findFirst([
                'id = :id:',
                'bind' => ['id' => $id]
            ]);
            if (empty($loan)) {
                throw new BusinessException(static::$t->_('contract_get_info_failed_when_update'), ErrCode::$CONTRACT_GET_INFO_ERROR);
            }
            $result = (new WorkflowServiceV2())->doCancel($work_req, $user, $this->getLoanWorkflowParams($loan, $user), $note);
            if ($result === false) {
                throw new BusinessException('审批流拒绝失败', ErrCode::$CONTRACT_WORK_FLOW_REJECT_ERROR);
            }
            $bool = $loan->i_update([
                'back_audit_status' => Enums::CONTRACT_STATUS_CANCEL,
                'updated_at' => date('Y-m-d H:i:s'),
                'back_cancel_reason' => $note,
                'back_amount'=>0,
                'back_status' => Enums::LOAN_BACK_STATUS_NOT
            ]);
            if ($bool === false) {
                throw new BusinessException(static::$t->_('contract_withdrawal_failed'), ErrCode::$CONTRACT_CANCEL_ERROR);
            }
            $db->commit();
        } catch (ValidationException $e) {               //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {                 //业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {                       //系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('loan-update-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
        ];
    }

    /**
     * 获取归还单审批流
     * @param int $loan_return_id 归还单id
     * @return \Phalcon\Mvc\Model
     */
    public function getRequest($loan_return_id)
    {
        //找最新的
        return WorkflowRequestModel::findFirst([
            'conditions' => 'biz_type = :type: and biz_value= :biz_value:',
            'bind' => ['type'=> Enums::WF_LOAN_BACK_TYPE , 'biz_value' => $loan_return_id],
            'order'=>'id desc'
        ]);
    }

    /**
     * 创建借款归还审批
     * @param integer $loan_return_id 借款归还ID
     * @param array $user 登陆者信息组
     * @return \Phalcon\Mvc\Model\Resultset|\Phalcon\Mvc\Phalcon\Mvc\Model
     * @throws BusinessException
     * @throws ValidationException
     */
    public function createRequest($loan_return_id, $user)
    {
        $loan_return = LoanReturnModel::findFirst([
            'id = :id:',
            'bind' => ['id' => $loan_return_id]
        ]);

        $data['id'] = $loan_return->id;
        $data['name'] = $loan_return->back_no . '归还审批申请';
        $data['biz_type'] = Enums::WF_LOAN_BACK_TYPE;

        //如果有老的归还审批，改成遗弃
        $loan = Loan::findFirst([
            'conditions' => 'id = :id:',
            'bind' => ['id' => $loan_return->loan_id]
        ]);
        if (empty($loan)) {
            throw new ValidationException(static::$t->_('loan_notfound_or_status_error'), ErrCode::$VALIDATE_ERROR);
        }
        if ($loan->is_new == Enums\LoanEnums::IS_NEW_NO) {
            $req = $this->getRequest($loan->id);
            if (!empty($req)) {
                $req->is_abandon = Enums\GlobalEnums::WORKFLOW_ABANDON_STATE_YES;
                $req->save();
            }
        }

        return (new WorkflowServiceV2())->createRequest($data, $user, $this->getLoanWorkflowParams($loan_return, $user));
    }

    /**
     * 获取借款归还审批流需要数据
     * @param object $loan_return 归还单对小组
     * @param array $user 当前操作信息组
     * @return array
     * @throws ValidationException
     */
    public function getLoanWorkflowParams($loan_return, $user)
    {
        $loan = Loan::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => ['id' => $loan_return->loan_id],
        ]);
        if (empty($loan)) {
            throw new ValidationException(static::$t->_('loan_notfound_or_status_error'), ErrCode::$VALIDATE_ERROR);
        }
        // 金额根据币种汇率转换为系统默认币种的额度
        $default_currency_amount = (new EnumsService())->amountExchangeRateCalculation($loan->amount,
            $loan->exchange_rate, 0);

        //V21791 申请人所属一级部门等于PMD-Thailand && 当申请人直线上级等于配置的CMO工号时该节点需要审批
        $is_cmo = 0;
        if (get_country_code() == Enums\GlobalEnums::TH_COUNTRY_CODE) {
            $department_config = EnumsService::getInstance()->getSettingEnvValueMap('appoint_store_by_department_id');
            if (isset($department_config['pmd']) && $loan->create_department_id == $department_config['pmd']) {
                $is_cmo = (new HrStaffRepository())->isCMO($loan->create_id);
            }
        }
        return [
            'amount'            => $default_currency_amount,
            'currency'          => $loan->currency,
            'submitter_id'      => $loan->create_id,
            //申请人用，loan的创建人
            'create_company_id' => $loan->create_company_id,
            'is_cmo'            =>  $is_cmo,
        ];
    }

    /**
     * @inheritDoc
     */
    public function getFlowId($model=null)
    {
        //借款-归还 v8382
        return Enums::WF_LOAN_BACK_WF_ID;
    }
}
