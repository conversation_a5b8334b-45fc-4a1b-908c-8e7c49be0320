<?php

namespace App\Modules\Loan\Services;

use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;

class LoanExportService extends BaseService
{
    private static $instance;

    private function __construct()
    {
    }


    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * @param string $filename
     * @param array $exportData
     * @return bool | array
     */
    public function exportLoanData($filename,$exportData = [])
    {
        $headerMap = [
            static::$t->_('loan_field_no'),
            static::$t->_('loan_back_status'),
            static::$t->_('loan_field_amount'),
            static::$t->_('loan_left_amount'),
            static::$t->_('re_field_currency_text'),
            static::$t->_('loan_export_field_back_at'),
            static::$t->_('re_field_apply_id'),
            static::$t->_('re_field_apply_name'),
            static::$t->_('csr_field_dept_name'),
            static::$t->_('re_field_apply_mobile'),
            static::$t->_('import_field_payment_create_email')
        ];

        $filterData = [];
        $header = array_values($headerMap);
        foreach ($exportData as $key => $item) {
            $filterData[$key][] = $item['lno'] ?: '';
            $filterData[$key][] = (isset($item['back_status']) && isset(Enums::$loan_back_status[$item['back_status']])) ? static::$t->_(Enums::$loan_back_status[$item['back_status']]) : '';
            $filterData[$key][] = bcdiv($item['amount'] ?: 0, 1000, 2);
            $filterData[$key][] = bcdiv(bcsub(bcsub($item['amount'], $item['re_amount']), $item['back_amount']), 1000, 2);
            $filterData[$key][] = static::$t->_(GlobalEnums::$currency_item[$item['currency']]); // 币种
            $filterData[$key][] = $item['back_at'] ?: ''; // 预计还款时间
            $filterData[$key][] = $item['create_id'] ?: '';
            $filterData[$key][] = $item['create_name'] ?: '';
            $filterData[$key][] = $item['create_node_department_name'] ?: '';
            $filterData[$key][] = $item['create_phone'] ?: '';
            $filterData[$key][] = $item['create_email'] ?: '';
        }
        $result = $this->exportExcel($header,$filterData,$filename);
        if ($result['code'] != ErrCode::$SUCCESS) {
            return false;
        }

        return $result;
    }


}
