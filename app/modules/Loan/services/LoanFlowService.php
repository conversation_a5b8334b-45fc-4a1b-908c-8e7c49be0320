<?php


namespace App\Modules\Loan\Services;


use App\Library\Enums;
use App\Library\Enums\SysConfigEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Loan\Models\Loan;
use App\Modules\Pay\Services\PayService;
use App\Modules\User\Services\UserService;
use App\Modules\Workflow\Services\AbstractFlowService;
use App\Modules\Workflow\Services\WorkflowServiceV2;
use App\Repository\HrStaffRepository;
use Phalcon\Mvc\Model;
use Phalcon\Mvc\Model\Resultset;

class LoanFlowService extends AbstractFlowService
{

    /**
     * @param $loan_id
     * @param $note
     * @param $user
     * @return array
     */
    public function approve($loan_id,$note, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $work_req = $this->getRequest($loan_id);
        $db = $this->getDI()->get('db_oa');
        try {

            $db->begin();
            if (empty($work_req->id)) {
                throw new BusinessException('获取工作流批次失败', ErrCode::$CONTRACT_GET_WORK_REQUEST_ERROR);
            }
            $loan = Loan::getFirst([
                'id = :id:',
                'bind' => ['id' => $loan_id]
            ]);
            if ($loan->status == Enums::CONTRACT_STATUS_CANCEL) {
                throw new ValidationException(static::$t->_('contract_has_been_withdrawal'), ErrCode::$VALIDATE_ERROR);
            }

            $result = (new WorkflowServiceV2())->doApprove($work_req, $user, $this->getLoanWorkflowParams($loan, $user),$note);
            if (!empty($result->approved_at)) {
                $bool = $loan->i_update([
                    'status' => Enums::CONTRACT_STATUS_APPROVAL,
                    'updated_at' => date('Y-m-d H:i:s'),
                    'approved_at' => $result->approved_at,
                ]);
                if ($bool === false) {
                    throw new BusinessException('同步合同信息失败', ErrCode::$CONTRACT_UPDATE_ERROR);
                }else{
                    //给支付人员发送待支付信息邮件
                    $pay_staff_id = (new BaseService())->getLoanPayStaffIds();
                    $this->sendEmailToAuditors($work_req,$pay_staff_id,1);
                    $this->delUnReadNumsKeyByStaffIds($pay_staff_id);

                    //同步数据到支付模块
                    if (EnumsService::getInstance()->getPayModuleStatus(SysConfigEnums::SYS_MODULE_LOAN, $loan->create_company_id)) {
                        PayService::getInstance()->saveOne($loan);
                    }
                }
            }
            $db->commit();
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('loan-approve-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
        ];
    }

    /**
     * @param $loan_id
     * @param $note
     * @param $user
     * @return array
     */
    public function reject($loan_id, $note, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $work_req = $this->getRequest($loan_id);
        $db = $this->getDI()->get('db_oa');
        try {

            $db->begin();
            if (empty($work_req->id)) {
                throw new BusinessException('获取工作流批次失败', ErrCode::$CONTRACT_GET_WORK_REQUEST_ERROR);
            }
            $loan = Loan::getFirst([
                'id = :id:',
                'bind' => ['id' => $loan_id]
            ]);
            if ($loan->status == Enums::CONTRACT_STATUS_CANCEL) {
                throw new ValidationException(static::$t->_('contract_has_been_withdrawal'), ErrCode::$VALIDATE_ERROR);
            }
            $result = (new WorkflowServiceV2())->doReject($work_req, $user, $this->getLoanWorkflowParams($loan, $user), $note);
            if ($result === false) {
                throw new BusinessException('审批流拒绝失败', ErrCode::$CONTRACT_WORK_FLOW_REJECT_ERROR);
            }
            $bool = $loan->i_update([
                'status' => Enums::CONTRACT_STATUS_REJECTED,
                'updated_at' => date('Y-m-d H:i:s'),
                'rejected_at' => $result->rejected_at,
                'refuse_reason' => $note,
                'pay_status'=> Enums::LOAN_PAY_STATUS_NOTPAY
            ]);
            if ($bool === false) {
                throw new BusinessException('同步合同信息失败', ErrCode::$CONTRACT_UPDATE_ERROR);
            }
            $db->commit();
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('contract-reject-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
        ];
    }

    /**
     * @param $id
     * @param $note
     * @param $user
     * @return array
     */
    public function cancel($id, $note, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $work_req = $this->getRequest($id);
        $db = $this->getDI()->get('db_oa');
        try {

            $db->begin();

            $loan = Loan::findFirst([
                'id = :id:',
                'bind' => ['id' => $id]
            ]);
            if (empty($loan)) {
                throw new BusinessException(static::$t->_('contract_get_info_failed_when_update'), ErrCode::$CONTRACT_GET_INFO_ERROR);
            }
            $result = (new WorkflowServiceV2())->doCancel($work_req, $user, $this->getLoanWorkflowParams($loan, $user), $note);
            if ($result === false) {
                throw new BusinessException('审批流拒绝失败', ErrCode::$CONTRACT_WORK_FLOW_REJECT_ERROR);
            }
            $bool = $loan->i_update([
                'status' => Enums::CONTRACT_STATUS_CANCEL,
                'updated_at' => date('Y-m-d H:i:s'),
                'cancel_reason' => $note,
                'pay_status' => Enums::LOAN_PAY_STATUS_NOTPAY
            ]);
            if ($bool === false) {
                throw new BusinessException(static::$t->_('contract_withdrawal_failed'), ErrCode::$CONTRACT_CANCEL_ERROR);
            }
            $db->commit();
        } catch (ValidationException $e) {               //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {                 //业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {                       //系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('loan-update-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
        ];
    }

    /**
     * @param $loanId
     * @return Model
     */
    public function getRequest($loanId)
    {
        return $this->getRequestByBiz($loanId, Enums::WF_LOAN_TYPE);
    }

    /**
     * @param $loanId
     * @param $user
     * @return Resultset|Model
     * @throws BusinessException
     * @throws ValidationException
     */
    public function createRequest($loanId, $user)
    {
        $loan = Loan::findFirst([
            'id = :id:',
            'bind' => ['id' => $loanId]
        ]);

        $data['id'] = $loan->id;
        $data['name'] = $loan->lno . '审批申请';
        $data['biz_type'] = Enums::WF_LOAN_TYPE;

        return (new WorkflowServiceV2())->createRequest($data, $user, $this->getLoanWorkflowParams($loan, $user));
    }

    /**
     * 获取借款审批流需要数据
     * @param $loan
     * @param $user
     * @return array
     */
    public function getLoanWorkflowParams($loan, $user)
    {
        $staffInfo = (new UserService())->getUserByIdInRbi($loan->create_id);
        $store_id = 0;
        if (!empty($staffInfo)){
            $store_id = $staffInfo->sys_store_id;
        }
        // 金额根据币种汇率转换为系统默认币种的额度
        $default_currency_amount = (new EnumsService())->amountExchangeRateCalculation($loan->amount, $loan->exchange_rate, 0);

        return [
            'amount'             => $default_currency_amount,
            'currency'           => $loan->currency,
            'staff_id'           => env('default_auditor', 20254),
            'submitter_id'       => $loan->create_id,         //申请人用，loan的创建人
            'job_title_level'    => $loan->create_job_title_level,
            'department_id'      => $loan->create_department_id,
            'node_department_id' => $loan->create_node_department_id,
            'store_id'           => $store_id,
            'create_company_id'  => $loan->create_company_id,
            'type'               => $loan->type,
            'is_cmo'             => get_country_code() == GlobalEnums::TH_COUNTRY_CODE ? (new HrStaffRepository())->isCMO($loan->create_id) : 0,//V21791-当申请人直线上级等于配置的CMO工号时该节点需要审批

        ];
    }

    /**
     * @inheritDoc
     */
    public function getFlowId($model=null)
    {
        //由2改成12改成14改成23改成31 v7038
        return Enums::WF_LOAN_APPLY_WF_ID;
    }

    /**
     * 获取抄送的摘要
     * @param $biz_value
     * @return array
     */
    public function getSummary($biz_value)
    {
        $summary = [];
        //查询详情
        $loan = Loan::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => ['id' => $biz_value]
        ]);
        if (empty($loan)) {
            return $summary;
        }

        //借款事由
        $summary['cc_summary_loan_event_name'] = $loan->event_name;
        //借款详细说明
        $summary['cc_summary_loan_event_info'] = $loan->event_info;
        //借款金额
        $summary['cc_summary_loan_amount'] = bcdiv($loan->amount, 1000, 2) . ' ' . static::$t->_(GlobalEnums::$currency_item[$loan->currency] ?? '');
        //预计还款时间
        $summary['cc_summary_loan_back_at'] = $loan->back_at;
        return $summary;
    }
}