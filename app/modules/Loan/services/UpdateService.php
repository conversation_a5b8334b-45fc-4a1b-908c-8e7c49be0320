<?php

namespace App\Modules\Loan\Services;

use App\Library\Enums;
use App\Library\Enums\PayEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Modules\Loan\Models\Loan;
use App\Modules\Loan\Models\LoanPay;
use Phalcon\Mvc\Model\Transaction\Failed as TxFailed;

class UpdateService extends BaseService
{
    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return UpdateService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * @param $id
     * @param $data
     * @param $user
     * @param int $is_from 1本模块，2是付款模块
     * @return array
     */

    public function pay($id, $data, $user, $is_from = 1)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';
        $real_message = '';

        $db = $this->getDI()->get('db_oa');
        $db->begin();

        try {
            $loan_pay_staff_id = $this->getLoanPayStaffIds();
            if (!in_array($user['id'], $loan_pay_staff_id) && $is_from == PayEnums::IS_FROM_SELF) {
                throw new ValidationException(static::$t->_('user_no_pay_permission_error'), ErrCode::$VALIDATE_ERROR);
            }

            $data = $this->handleData($data, $user);

            $loan = Loan::getFirst([
                'conditions' => 'id = :id: AND status = :status:',
                'bind' => ['id' => $id, 'status' => Enums::WF_STATE_APPROVED]
            ]);

            if (empty($loan)) {
                throw new ValidationException(static::$t->_('biz_order_data_is_null', ['biz_id' => $id]), ErrCode::$VALIDATE_ERROR);
            }

            // 非待支付状态, 不可重复支付
            if ($loan->pay_status != Enums::PAYMENT_PAY_STATUS_PENDING) {
                throw new ValidationException(static::$t->_('repeated_payment_error_hint', ['serial_no' => $loan->lno]), ErrCode::$VALIDATE_ERROR);
            }

            if ($is_from == PayEnums::IS_FROM_SELF && $loan->is_pay_module == PayEnums::BIZ_DATA_IS_PAY_MODULE_YES) {
                throw new ValidationException(static::$t->_('payment_has_entered_the_payment_module'), ErrCode::$VALIDATE_ERROR);
            }

            if ($data['is_sign'] == Enums::LOAN_PAY_SIGN) {
                $data['sign_name'] = $loan->create_name;
            }

            $loan_pay = new LoanPay();
            $bool = $loan_pay->i_create($data);
            if ($bool === false) {
                throw new BusinessException('借款申请付款-创建失败, 原因可能是: ' . get_data_object_error_msg($loan_pay) . '; 数据: ' . json_encode($data, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }

            $update_data = [
                'pay_status' => $data['is_sign'] == Enums::LOAN_PAY_SIGN ? Enums::LOAN_PAY_STATUS_PAY : Enums::LOAN_PAY_STATUS_NOTPAY,
                'loan_status' => $data['is_sign'] == Enums::LOAN_PAY_SIGN ? Enums\LoanEnums::LOAN_STATUS_NOT_START_RETURN : Enums\LoanEnums::LOAN_STATUS_NO_NEED_RETURN,//借款单审批通过&&已支付，则更新借款单状态=2未开始归还，否则=1无需归还
                'updated_at' => date('Y-m-d H:i:s')
            ];
            $bool = $loan->i_update($update_data);
            if ($bool === false) {
                throw new BusinessException('借款申请付款-更新借款申请失败, 原因可能是: ' . get_data_object_error_msg($loan) . "; 数据: [loan_id:{$loan->id}], " . json_encode($update_data, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }

            $db->commit();

            $this->delUnReadNumsKeyByStaffIds($loan_pay_staff_id);

        } catch (ValidationException $e) {
            $db->rollback();

            //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $db->rollback();

            //业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (TxFailed $e) {
            $db->rollback();

            //数据库错误，不可对外抛出
            $code = ErrCode::$MYSQL_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $db->rollback();

            //系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning('loan-pay-update-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => []
        ];
    }

    /**
     * @param $data
     * @param $user
     * @return array
     * @throws ValidationException
     */
    private function handleData($data, $user)
    {
        if (empty($data) || !is_array($data)) {
            return [];
        }

        $data['is_sign'] = intval($data['is_sign']);
        $data['loan_id'] = $data['id'];
        $data['create_id'] = $user['id'] ?? 0;
        $data['create_name'] = $this->getNameAndNickName($user['name'] ?? '', $user['nick_name'] ?? '');
        $data['create_department_name'] = '';
        $data['create_job_title_name'] = '';
        $data['created_at'] = date('Y-m-d H:i:s');
        unset($data['id']);

        if (!empty($data['create_id'])) {
            $temp = $this->getUserMetaFromBi($data['create_id']);
            if (!empty($temp)) {
                $data['create_department_name'] = $temp['create_display_department_name'];
                $data['create_job_title_name'] = $temp['create_job_title_name'];
            }
        }

        if ($data['is_sign'] == Enums::LOAN_PAY_NOTSIGN) {
            $data['pay_bank_name'] = '';
            $data['pay_bank_account'] = '';
            $data['sign_name'] = '';
            $data['pay_type'] = 1;
            $data['pay_date'] = null;
            $data['sign_date'] = null;
            $data['mark'] = null;
        } else {
            $data['not_sign_reason'] = '';

            /**
             * 13784【MY|OA|银行流水】 银行流水及支付模块迁移
             * https://flashexpress.feishu.cn/docx/doxcn8oZcw7n1PUJRdbcOuHdeTd?useNewLarklet=1
             * 之前历史模块迁移的时候，有的模块有现金，在迁移国家时，隐藏了现金的支付方式，增加了一个银行账号必填。
             * 此次需求开启后，马来不再强制校验银行账号必填
             */
            if (in_array(get_country_code(), [GlobalEnums::ID_COUNTRY_CODE, GlobalEnums::VN_COUNTRY_CODE])) {
                if (empty($data['pay_bank_name']) || empty($data['pay_bank_account'])) {
                    throw new ValidationException(static::$t->_('access_data_request_param_error') . '[pay_bank_name/pay_bank_account]', ErrCode::$VALIDATE_ERROR);
                }

                $data['pay_type'] = 0;// 付款方式: 无
            }
        }

        return $data;
    }
}
