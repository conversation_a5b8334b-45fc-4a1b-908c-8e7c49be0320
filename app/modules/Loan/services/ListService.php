<?php

namespace App\Modules\Loan\Services;

use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\PayEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\Enums\ReimbursementEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Models\oa\LoanReturnModel;
use App\Modules\Loan\Models\Loan;
use App\Modules\Loan\Models\LoanPay;
use App\Modules\Loan\Models\LoanPayBank;
use App\Modules\Reimbursement\Models\Reimbursement;
use App\Modules\Reimbursement\Models\ReimbursementRelLoan;
use App\Modules\Setting\Services\CommonDataPermissionService;
use App\Modules\User\Models\AttachModel;
use App\Modules\Workflow\Services\FYRService;
use App\Modules\Workflow\Services\WorkflowServiceV2;
use App\Repository\HrStaffRepository;
use GuzzleHttp\Exception\GuzzleException;

class ListService extends BaseService
{
    public static $not_must_params = [
        'lno',
        'status',
        'pay_status',
        'pageSize',
        'pageNum'
    ];

    public static $validate_list_search = [

    ];

    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return ListService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * @param $condition
     * @param array $user
     * @param int $type
     * @param bool $if_download
     * @return array
     */
    public function getList($condition, $user = [], $type = 0, $if_download = false)
    {
        $condition['uid'] = $user['id'];
        $page_size = empty($condition['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : intval($condition['pageSize']);
        $page_num = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : intval($condition['pageNum']);
        $offset = $page_size * ($page_num - 1);

        $code = ErrCode::$SUCCESS;
        $message = 'success';
        $real_message = '';

        $data = [
            'items' => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page' => $page_size,
                'total_count' => 0,
            ]
        ];

        try {
            // 借款支付列表: 当前用户 须是 支付人
            if ($type == self::LIST_TYPE_PAY) {
                $pay_staff_id = $this->getLoanPayStaffIds();
                if (!in_array($user['id'], $pay_staff_id)) {
                    throw new ValidationException(static::$t->_('user_no_pay_permission_error'), ErrCode::$VALIDATE_ERROR);
                }
            }

            $builder = $this->modelsManager->createBuilder();
            $builder->from(['c' => Loan::class]);
            $builder->leftJoin(LoanPay::class, 'c.id = lp.loan_id', 'lp');

            // 数据查询/借款归还列表非来自报销模块查询需要关联报销单
            if ($type == self::LIST_TYPE_DATA || ($type == self::LIST_TYPE_APPLY && !isset($condition['biz_channel']))) {
                $builder->leftJoin(ReimbursementRelLoan::class, 'c.id = rl.loan_id and rl.is_deleted = '.GlobalEnums::IS_NO_DELETED, 'rl');
                $builder->leftJoin(Reimbursement::class, 'rl.re_id = r.id', 'r');
            }

            $builder = $this->getCondition($builder, $condition, $type, $user);

            //13578需求，借款审核-借款归还-列表不需要按照借款单分组
            // 16194需求, 借款回复列表, 亦无需按照借款单分组(以征询次数为维度展示)
            if (in_array($type, [self::LIST_TYPE_BACK_AUDIT, self::LIST_TYPE_FYR])) {
                $count_sql = 'COUNT(c.id) AS total';
            } else {
                $count_sql = 'COUNT(DISTINCT(c.id)) AS total';
            }
            $count = (int) $builder->columns($count_sql)->getQuery()->getSingleResult()->total;

            $items = [];
            if ($count) {
                $fields  = 'c.id,c.lno,c.lname,c.status,c.currency,c.amount,c.re_amount,c.back_amount,c.refuse_reason,
            c.cancel_reason,c.create_id,c.create_name,c.create_company_id,c.create_company_name,c.create_job_title_name,c.cost_center_name,
            c.create_email,c.pay_type,c.finished_at,c.back_at,c.create_department_name,c.created_at,c.pay_status,c.loan_status,
            c.create_phone,c.type,c.event_info,c.create_date,c.approved_at,lp.pay_date,lp.created_at AS pay_created_at, c.is_pay_module';

                // 数据查询/借款归还列表非来自报销模块查询需要关联报销单
                if ($type == self::LIST_TYPE_DATA || ($type == self::LIST_TYPE_APPLY && !isset($condition['biz_channel']))) {
                    $fields .= ',SUM(if( r.pay_status = 2 , r.loan_amount, 0 )) loan_amounts, GROUP_CONCAT(r.no) nos';
                } else if ($type == self::LIST_TYPE_FYR) {
                    // 借款回复列表:返回征询ID，回复详情接口用到
                    $fields .= ',reply.fyr_id';
                } else if (in_array($type, [self::LIST_TYPE_AUDIT, self::LIST_TYPE_BACK_AUDIT]) && isset($condition['flag']) && $condition['flag'] == GlobalEnums::AUDIT_TAB_PROCESSED) {
                    // 审核模块的已处理列表, 展示处理时间
                    $fields .= ',log.audit_at';
                }

                //13578需求，借款审核-归还列表，需要返回归还单的信息
                if ($type == self::LIST_TYPE_BACK_AUDIT) {
                    $fields .= ',lr.id loan_return_id, lr.back_amount return_back_amount, lr.back_apply_date, lr.back_audit_status, lr.created_at as return_created_at';
                }

                $builder->columns($fields);

                if (!empty($condition['order'])) {
                    //个人归还列表
                    $builder->orderBy($condition['order']);
                } else if ($type == self::LIST_TYPE_PAY) {
                    if (isset($condition['flag']) && $condition['flag'] == GlobalEnums::AUDIT_TAB_PENDING) {
                        $builder->orderBy('c.approved_at ASC');
                    } else {
                        $builder->orderBy('lp.created_at DESC');
                    }
                } else if (!in_array($type, [self::LIST_TYPE_AUDIT, self::LIST_TYPE_BACK_AUDIT, self::LIST_TYPE_FYR])) {
                    $builder->orderBy('c.id DESC');
                }

                //13873需求后导出也需要按照借款单维度展示报销单组而不是按照报销单维度展示多行展示同笔借款单；只有借款审核-借款归还的不需要按照借款单分组
                // 16194需求, 借款回复列表, 亦无需按照借款单分组(以征询次数为维度展示)
                if (!in_array($type, [self::LIST_TYPE_BACK_AUDIT, self::LIST_TYPE_FYR])) {
                    $builder->groupBy('c.id');
                }

                if (!$if_download) {
                    $builder->limit($page_size, $offset);
                }
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleItems($items, $condition, $type);
            }

            $data = [
                'items' => $items,
                'pagination' => [
                    'current_page' => $page_num,
                    'per_page' => $page_size,
                    'total_count' => $count,
                ]
            ];
        } catch (ValidationException $e) {
            $code         = $e->getCode();
            $message      = $e->getMessage();

        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning('loan-list-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * @param $builder
     * @param $condition
     * @param $type
     * @param $user
     * @return mixed
     * @throws BusinessException
     */
    private function getCondition($builder, $condition, $type, $user)
    {
        $lno = $condition['lno'] ?? '';
        $status = $condition['status'] ?? 0;
        $pay_status = $condition['pay_status'] ?? 0;
        $create_id = $condition['create_id'] ?? 0;

        // 征询回复进度
        $is_reply = $condition['is_reply'] ?? GlobalEnums::CONSULTED_REPLY_STATE_PENDING;
        $is_reply = in_array($is_reply, GlobalEnums::CONSULTED_REPLY_STATE_ITEM) ? $is_reply : GlobalEnums::CONSULTED_REPLY_STATE_PENDING;

        // 审批处理进度
        $flag = $condition['flag'] ?? GlobalEnums::AUDIT_TAB_PENDING;
        $flag = in_array($flag, GlobalEnums::AUDIT_TAB_STATE_ITEM) ? $flag : GlobalEnums::AUDIT_TAB_PENDING;

        $loan_status = $condition['loan_status'] ?? 0;//借款单状态
        $back_at = $condition['back_at'] ?? '';
        $currency = $condition['currency'] ?? 0;

        $is_used_by_re = $condition['is_used_by_re'] ?? null;
        $amount_cmp = $condition['amount_cmp'] ?? null;

        // 报销关联单号
        $reim_no = $condition['rno'] ?? '';

        // 申请人公司
        $company_id = $condition['company_id'] ?? [];
        // 审批通过开始时间
        $approved_at_start = $condition['approved_at_start'] ?? null;
        // 审批通过结束时间
        $approved_at_end = $condition['approved_at_end'] ?? null;
        // 申请人部门
        $department_id = $condition['department_id'] ?? 0;
        // 支付时银行流水开始日期
        $pay_date_start = $condition['pay_date_start'] ?? null;
        // 支付时银行流水开始日期
        $pay_date_end = $condition['pay_date_end'] ?? null;

        // 创建单据的开始时间
        $create_start_time = $condition['create_start_time'] ?? '';

        // 创建单据的结束时间
        $create_end_time = $condition['create_end_time'] ?? '';

        //归还审核状态
        $back_audit_status = $condition['back_audit_status'] ?? 0;

        // 申请人在职状态
        $created_id_states = isset($condition['staff_status']) && is_array($condition['staff_status']) ? $condition['staff_status'] : [];

        // 借款申请审核列表
        if ($type == self::LIST_TYPE_AUDIT) {
            $builder = (new WorkflowServiceV2())->getBizWorkflowOrderListByProcessState($builder, $flag, [Enums::WF_LOAN_TYPE], $condition['uid'], 'c');

        } else if ($type == self::LIST_TYPE_BACK_AUDIT) {
            // 借款归还审核;15873需求后需要关联借款归还单获取归还金额等信息
            $builder->leftjoin(LoanReturnModel::class, 'lr.loan_id = c.id', 'lr');
            $builder = (new WorkflowServiceV2())->getBizWorkflowOrderListByProcessState($builder, $flag, [Enums::WF_LOAN_BACK_TYPE], $condition['uid'], 'lr');

        } else if ($type == self::LIST_TYPE_APPLY) {
            // 来自报销模块: 只能检索申请人的借款单
            if (isset($condition['biz_channel']) && $condition['biz_channel'] == 'reimbursement') {
                $uid_arr[] = $condition['applyId'];
            } else {
                $uid_arr = [$condition['uid']];
                if (!empty($condition['applyId'])) {
                    $uid_arr[] = $condition['applyId'];
                }
            }
            $builder->andWhere('c.create_id IN ({uid:array})', ['uid' => $uid_arr]);

        } else if ($type == self::LIST_TYPE_PAY) {
            // 借款支付列表
            $builder->andWhere('c.status = :main_status: AND c.is_pay_module = :is_pay_module:', [
                'main_status' => Enums::WF_STATE_APPROVED,
                'is_pay_module' => PayEnums::BIZ_DATA_IS_PAY_MODULE_NO
            ]);

            // 待处理: 待支付
            if ($flag == GlobalEnums::AUDIT_TAB_PENDING) {
                $builder->andWhere('c.pay_status = :pay_status_pending:', ['pay_status_pending' => Enums::PAYMENT_PAY_STATUS_PENDING]);
            } else if ($flag == GlobalEnums::AUDIT_TAB_PROCESSED) {
                // 已处理: 已支付/未支付
                $builder->inWhere('c.pay_status', [Enums::PAYMENT_PAY_STATUS_PAY, Enums::PAYMENT_PAY_STATUS_NOTPAY]);
            }

        } else if ($type == self::LIST_TYPE_FYR) {
            // 意见征询回复列表 v18276: 待回复的单据无需取终审通过 且 待支付的
            $biz_table_info = ['table_alias' => 'c', 'pay_status_field_name' => ''];
            $builder = (new FYRService())->getBizOrderConsultationReplyListByProcessState($builder, $is_reply, [Enums::WF_LOAN_TYPE], $condition['uid'], $biz_table_info);

        } else if ($type == ListService::LIST_TYPE_DATA) {
            // 是否根据报销单号查询
            if (!empty($reim_no)) {
                $builder->andWhere('r.no = :no:', ['no' => $reim_no]);
            }

            // 根据员工在职状态过滤申请人: 返回空则下述inWhere取数结果为空
            if (!empty($created_id_states)) {
                $loan_created_ids = $this->getMatchStaffStateLoanCreatedIds($created_id_states);
                $builder->inWhere('c.create_id', $loan_created_ids);
            }

            // 对接通用数据权限
            // 业务表参数
            $table_params = [
                'table_alias_name' => 'c',
                'create_id_field' => 'create_id',
                'create_node_department_id_filed' => 'create_node_department_id',
            ];
            $builder = CommonDataPermissionService::getInstance()->getCommonDataPermission($user, $builder, Enums\SysConfigEnums::SYS_MODULE_LOAN, $table_params);
        }

        if (!empty($lno)) {
            $builder->andWhere('c.lno = :lno:', ['lno' => $lno]);
        }

        if (!empty($create_id)) {
            $builder->andWhere('c.create_id = :create_id:', ['create_id' => $create_id]);
        }

        if (!empty($status)) {
            $builder->andWhere('c.status = :status:', ['status' => $status]);
        }

        if (!empty($pay_status)) {
            $builder->andWhere('c.pay_status = :pay_status:', ['pay_status' => $pay_status]);
        }

        //15873需求后按照借款单状态筛选
        if (!empty($loan_status)) {
            if (is_array($loan_status)) {
                $builder->andWhere('c.loan_status IN ({loan_status:array})', ['loan_status' => $loan_status]);
            } else {
                $builder->andWhere('c.loan_status = :loan_status:', ['loan_status' => $loan_status]);
            }
        }

        //预计还款时间
        if (is_array($back_at) && !empty($back_at[0]) && !empty($back_at[1])) {
            $builder->andWhere('c.back_at >= :start_back_at:', ['start_back_at' => $back_at[0]]);
            $builder->andWhere('c.back_at <= :end_back_at:', ['end_back_at' => $back_at[1]]);
        }

        //货币类型
        if (!empty($currency)) {
            $builder->andWhere('c.currency = :currency:', ['currency' => $currency]);
        }

        //是否核销过
        if (isset($is_used_by_re)) {
            $builder->andWhere('c.is_used_by_re = :is_used_by_re:', ['is_used_by_re' => $is_used_by_re]);
        }

        if (isset($amount_cmp)) {
            $builder->andWhere('amount > re_amount + back_amount');
        }

        if (!empty($company_id)) {
            if (is_array($company_id)) {
                $builder->andWhere('c.create_company_id IN ({create_company_id:array})', ['create_company_id' => array_values($company_id)]);
            } else {
                $builder->andWhere('c.create_company_id = :create_company_id:', ['create_company_id' => $company_id]);
            }
        }

        if (!empty($approved_at_start) && !empty($approved_at_end)) {
            $builder->andWhere('c.approved_at >= :approved_at_start: and c.approved_at <= :approved_at_end:', [
                'approved_at_start' => $approved_at_start,
                'approved_at_end' => $approved_at_end
            ]);
        }

        if (!empty($department_id)) {
            $builder->andWhere('c.create_node_department_id = :department_id:', ['department_id' => $department_id]);
        }

        if (!empty($pay_date_start) && !empty($pay_date_end)) {
            $builder->andWhere('lp.pay_date >= :pay_date_start: and lp.pay_date <= :pay_date_end:', [
                'pay_date_start' => $pay_date_start,
                'pay_date_end' => $pay_date_end
            ]);
        }

        if (!empty($create_start_time)) {
            $builder->andWhere('c.created_at >= :create_start_time:', ['create_start_time' => $create_start_time]);
        }

        if (!empty($create_end_time)) {
            $builder->andWhere('c.created_at <= :create_end_time:', ['create_end_time' => $create_end_time]);
        }

        if (!empty($back_audit_status)) {
            //15873需求借款审核-已处理-借款归还-新增按照归还审核状态筛选
            $builder->andWhere('lr.back_audit_status = :back_audit_status:', ['back_audit_status' => $back_audit_status]);
        }

        return $builder;
    }

    /**
     * 支付待办统计
     * @param int $user_id
     *
     * @return mixed
     */
    public function getPayPendingCount(int $user_id)
    {
        $pay_pending_count = 0;

        if (empty($user_id)) {
            return $pay_pending_count;
        }

        // 是付款人: 终审通过且待支付的
        $pay_staff_ids = $this->getLoanPayStaffIds();
        if (in_array($user_id, $pay_staff_ids)) {
            $pay_pending_count = Loan::count([
                'conditions' => 'status = :audit_status: AND pay_status = :pay_status: AND is_pay_module = :is_pay_module:',
                'bind' => [
                    'audit_status' => Enums::WF_STATE_APPROVED,
                    'pay_status' => Enums::PAYMENT_PAY_STATUS_PENDING,
                    'is_pay_module' => PayEnums::BIZ_DATA_IS_PAY_MODULE_NO
                ],
            ]);
        }

        return $pay_pending_count;
    }

    /**
     * @param $items
     * @param $condition
     * @param $type
     * @return array
     */
    private function handleItems($items, $condition, $type)
    {
        if (empty($items) || !is_array($items)) {
            return [];
        }
        // 借款归还按钮权限
        $condition['uid'] = $condition['uid'] ?? 0;
        $authIds = BaseService::getSettingAuthStaffId();
        $isAuth = in_array($condition['uid'], $authIds) ? true : false;

        //对于支付员工
        $pay_staff_id = $this->getLoanPayStaffIds();
        $is_payer = !empty($condition['uid']) && in_array($condition['uid'], $pay_staff_id);

        // 借款申请人在职状态
        $created_list = $this->getStaffStateByIds(array_values(array_unique(array_column($items, 'create_id'))));
        
        // 借款单的收款银行信息
        $loan_ids = array_values(array_filter(array_unique(array_column($items, 'id'))));
        $loan_pay_bank_item = $this->getLoanPayBankItem($loan_ids);
        $loan_pay_bank_item = array_column($loan_pay_bank_item, null, 'loan_id');

        // 借款单关联的归还单数据
        $loan_return_list = [];
        //借款归还列表非来自报销模块的或数据查询列表
        if (($type == self::LIST_TYPE_APPLY && !isset($condition['biz_channel'])) || $type == self::LIST_TYPE_DATA) {
            $loan_return_list = $this->getLoanReturnItem($loan_ids);
        }
        $payment_currency = GlobalEnums::$currency_item;
        $status           = Enums::$loan_status;
        $loan_pay_status  = Enums::$loan_pay_status;
        foreach ($items as &$item) {
            $item['return_back_amount'] = isset($item['return_back_amount']) ? bcdiv($item['return_back_amount'], 1000, 2) : 0;    //本次归还的金额
            //18214需求，剩余未还款金额 当借款申请单的状态为，已驳回，已撤回，已通过且未支付三种中的一种时，显示为0
            if (in_array($item['status'], [Enums::WF_STATE_REJECTED, Enums::WF_STATE_CANCEL]) || ($item['status'] == Enums::WF_STATE_APPROVED && $item['pay_status'] == Enums::PAYMENT_PAY_STATUS_NOTPAY)) {
                $item['left_amount'] = 0.00;
            } else {
                $item['left_amount'] = bcdiv(bcsub(bcsub($item['amount'], $item['re_amount']), $item['back_amount']), 1000, 2);
            }

            //实际未还款金额 = 借款金额amount- 15873需求后改为每笔借款单下实际归还总金额（ps也可以用借款单back_amount存储的也是实际归还金额总计）
            $real_un_pay_amount = bcsub($item['amount'], $item['back_amount']);
            //借款单名下报销单是已支付的数据存在时，实际未还款金额-某借款单关联的已支付报销单的冲减抵扣金额总计
            $real_un_pay_amount = bcsub($real_un_pay_amount, $item['loan_amounts'] ?? 0);
            $item['real_un_pay_amount'] = bcdiv($real_un_pay_amount, 1000, 2);//实际未还款金额

            $item['back_amount'] = bcdiv($item['back_amount'], 1000, 2);//转账归还小计
            $item['re_amount'] = bcdiv($item['re_amount'], 1000, 2);//报销冲减金额
            $item['amount'] = bcdiv($item['amount'], 1000, 2);//借款金额
            $item['status_text'] = static::$t->_($status[$item['status']] ?? '');
            $item['currency_text'] = static::$t->_($payment_currency[$item['currency']] ?? '');
            $item['pay_status_text'] = static::$t->_($loan_pay_status[$item['pay_status']]);
            $item['loan_status_text'] = static::$t->_(Enums\LoanEnums::$loan_status[$item['loan_status']]);  //借款单状态
            $item['lname'] = static::$t->_('loan_name',["year"=>date("Y",strtotime($item['created_at'])),"month"=>date("m",strtotime($item['created_at']))]);
            $item['nos'] = isset($item['nos']) ? $item['nos']: '';//关联的所有报销单号，逗号分割
            $item['show_reimbursement_status'] = empty($item['nos']) ? GlobalEnums::LOAN_REL_REIMBURSEMENT_NOT_SHOW : GlobalEnums::LOAN_REL_REIMBURSEMENT_SHOW;
            if (self::LIST_TYPE_DATA == $type) {
                // 借款事由
                $item['type_txt'] = static::$t->_(GlobalEnums::$loan_apply_type[$item['type']]);

                $loan_pay_bank_info = $loan_pay_bank_item[$item['id']] ?? [];
                if (!empty($loan_pay_bank_info)) {
                    // 收款人户名
                    $item['rec_name'] = $loan_pay_bank_info['name'];
                    // 收款人账号
                    $item['rec_account'] = $loan_pay_bank_info['account'];
                    // 收款人开户银行
                    $item['rec_bank_type'] = $loan_pay_bank_info['bank_type'];
                } else {
                    $item['rec_name'] = '';
                    $item['rec_account'] = '';
                    $item['rec_bank_type'] = '';
                }
            } else if (self::LIST_TYPE_BACK_AUDIT == $type) {
                //15873需求，借款审核-借款归还-返回归还审批通过文本
                $item['back_audit_status_text'] = static::$t->_(Enums::$loan_status[$item['back_audit_status']] ?? '');
            }

            // 归还按钮展示权限
            $item['back_btn_auth'] = false;
            //归还列表而非报销关联那里过来的
            if ($type == self::LIST_TYPE_APPLY && !isset($condition['biz_channel'])) {
                //借款归还列表-归还按钮显示逻辑 = 借款单状态是未开始归还或部分归还 && 无归还中 &&归还方式=银行转账的归还单
                $item['back_btn_auth'] = in_array($item['loan_status'], [Enums\LoanEnums::LOAN_STATUS_NOT_START_RETURN, Enums\LoanEnums::LOAN_STATUS_PARTIAL_RETURN]) && (!isset($loan_return_list[$item['id']]) || isset($loan_return_list[$item['id']]) && $loan_return_list[$item['id']]['back_status_ing'] === false) ? true : false;
            } else if ($type == self::LIST_TYPE_DATA) {
                //数据查询列表-归还按钮显示逻辑 = 满足配置的权限组工号内 && 借款单状态是未开始归还或部分归还  && 无归还中 && 归还方式=银行转账的归还单
                $item['back_btn_auth'] = $isAuth && in_array($item['loan_status'], [Enums\LoanEnums::LOAN_STATUS_NOT_START_RETURN, Enums\LoanEnums::LOAN_STATUS_PARTIAL_RETURN]) && (!isset($loan_return_list[$item['id']]) || isset($loan_return_list[$item['id']]) && $loan_return_list[$item['id']]['back_status_ing'] === false) ? true : false;
            }

            // 审批已处理列表: 已终审通过 且 已支付/未支付, 且 未走支付模块的 且 是支付人 则已处理时间 取支付时间
            if ($is_payer && isset($item['audit_at']) && $item['status'] == Enums::WF_STATE_APPROVED && !$item['is_pay_module'] && in_array($item['pay_status'], [Enums::LOAN_PAY_STATUS_PAY, Enums::LOAN_PAY_STATUS_NOTPAY])) {
                $item['audit_at'] = $item['pay_created_at'];
            }

            // 申请人在职状态
            $created_info = $created_list[$item['create_id']] ?? [];
            $item['create_state_code'] = $created_info['code'] ?? '0';
            $item['create_state_label'] = $created_info['label'] ?? '';

            if (self::LIST_TYPE_EXPORT == $type) {
                $item['type_txt'] = static::$t->_(GlobalEnums::$loan_apply_type[$item['type']]);
                $item['loan_amount']     = empty($item['loan_amount']) ? '' : bcdiv($item['loan_amount'], 1000, 2);
                $item['r_currency_text'] = static::$t->_($payment_currency[$item['r_currency']] ?? '');  // 报销单号冲减的币种
                $item['r_status_text']   = static::$t->_($status[$item['r_status']] ?? ''); // 报销单号的审批状态
                $item['r_pay_status_text']      = static::$t->_($loan_pay_status[$item['r_pay_status']] ?? ''); // 报销单号的支付状态
                if (isset($item['back_type']) && Enums\LoanEnums::LOAN_BACK_TYPE_SALARY == $item['back_type']) {
                    $item['r_back_amount'] = bcdiv($item['back_actual_amount'], 1000, 2); //申请的转账归还的金额
                } else if (isset($item['back_type']) && Enums\LoanEnums::LOAN_BACK_TYPE_TRANSFER == $item['back_type']) {
                    $item['r_back_amount'] = bcdiv($item['r_back_amount'], 1000, 2); //申请的转账归还的金额
                }
                $item['back_audit_status_text'] = static::$t->_($status[$item['back_audit_status']] ?? ''); //转账归还的审批状态
            }

        }

        return $items;
    }

    /**
     * 借款导出
     *
     * @param $condition
     * @param array $user
     * @param $data_type
     * @return array
     * @throws BusinessException
     * @throws GuzzleException
     */
    public function export($condition, $user = [], $data_type)
    {
        ini_set('memory_limit', '1024M');

        $data = $this->exportList($condition, $user, $data_type);


        // 取收款人银行信息
        $loan_ids = !empty($data) ? array_column($data, 'id' ) : [];
        $loan_pay_bank_item = [];
        if (!empty($loan_ids)) {
            $loan_pay_bank_item = LoanPayBank::find([
                'conditions' => 'loan_id IN ({ids:array})',
                'bind' => ['ids' => $loan_ids],
            ])->toArray();

            $loan_pay_bank_item = !empty($loan_pay_bank_item) ? array_column($loan_pay_bank_item, null, 'loan_id') : [];
        }

        $new_data = [];
        $i = 0;

        foreach ($data as $key => $val) {
            ++$i;

            $loan_pay_bank = $loan_pay_bank_item[$val['id']] ?? [];

            $new_data[$key][] = $i;//编号
            $new_data[$key][] = $val['lname'];//标题
            $new_data[$key][] = $val['lno'];//借款单号
            $new_data[$key][] = $val['amount']; //借款金额
            $new_data[$key][] = $val['create_name'];//申请人
            $new_data[$key][] = $val['create_id'];//申请人工号
            $new_data[$key][] = $val['create_state_label'];// 申请人在职状态
            $new_data[$key][] = $val['create_company_name']; // 申请人公司
            $new_data[$key][] = $val['create_department_name'] ?? "";//申请人所属部门
            $new_data[$key][] = $val['create_job_title_name'] ?? ''; // 申请人职位
            $new_data[$key][] = $val['cost_center_name']; // 费用所属中心
            $new_data[$key][] = $val['create_date'];//申请日期
            $new_data[$key][] = $val['create_phone'];//申请人电话
            $new_data[$key][] = $val['create_email']; // 申请人邮箱
            $new_data[$key][] = $val['type_txt'];//借款事由
            $new_data[$key][] = $val['event_info'];//事项使用说明及估算过程
            $new_data[$key][] = static::$t->_(GlobalEnums::$payment_method_item[$val['pay_type']] ?? ''); // 付款方式
            $new_data[$key][] = $val['finished_at']; // 预计完成工作时间
            $new_data[$key][] = $val['back_at']; // 预计还款时间
            $new_data[$key][] = $val['status_text'];//借款单审批状态
            $new_data[$key][] = $val['pay_status_text'];//借款单支付状态
            $new_data[$key][] = $val['loan_status_text'];//借款单状态
            $new_data[$key][] = $val['left_amount'];//剩余未还款金额
            $new_data[$key][] = $val['real_un_pay_amount'];//实际未还款金额
            $new_data[$key][] = $val['currency_text'];//币种

            $new_data[$key][] = $val['back_amount'];//转账归还小计

            $new_data[$key][] = $val['re_amount']; // 报销冲减借款小计

            $new_data[$key][] = $val['no']; // 关联报销单号

            $new_data[$key][] = $val['loan_amount']; // 报销单号冲减的金额
            $new_data[$key][] = $val['r_currency_text']; // 报销单号冲减的币种
            $new_data[$key][] = $val['r_status_text']; // 报销单号的审批状态
            $new_data[$key][] = $val['r_pay_status_text']; // 报销单号的支付状态
            $new_data[$key][] = $val['r_back_amount']; // 申请的转账归还的金额
            $new_data[$key][] = $val['back_date'] ?? ''; //申请的转账归还填写的银行日期
            $new_data[$key][] = $val['back_audit_status_text']; //转账归还的审批状态

            $new_data[$key][] = $loan_pay_bank['name'] ?? '';//收款人户名

            $new_data[$key][] = $loan_pay_bank['account'] ?? '';//收款人账号
            $new_data[$key][] = $loan_pay_bank['bank_type'] ?? '';//收款人开户银行
            $new_data[$key][] = $val['pay_date'];// 借款申请银行流水日期
        }

        $file_name = $condition['export_file_name'] ?? 'loan_' . date('YmdHis') . '.xlsx';
        $header = [
            static::$t->_('global.number'),             //编号
            static::$t->_('loan_field_title'),          //标题
            static::$t->_('loan_field_no'),             //借款单号
            static::$t->_('loan_field_amount'),         //借款金额
            static::$t->_('global.applicant.name'),     //申请人
            static::$t->_('global.applicant.id'),       //申请人工号
            static::$t->_('view_work_status'),       //申请人在职状态
            static::$t->_('global.company.name'),       //申请人公司
            static::$t->_('re_field_apply_department_name'),  //申请人所属部门
            static::$t->_('applicant_position'),        //申请人职位
            static::$t->_('csr_field_cost_center'),     //费用所属中心
            static::$t->_('global.apply.date'),//申请日期
            static::$t->_('re_field_apply_mobile'),//申请人电话
            static::$t->_('import_field_payment_create_email'),     //申请人邮箱
            static::$t->_('loan_type_reason'),//借款事由
            static::$t->_('loan_event_info'),//事项使用说明及估算过程
            static::$t->_('loan_pay_type_text'),     //付款方式（申请）
            static::$t->_('loan_export_field_finished_at'),     //预计工作完成时间
            static::$t->_('loan_export_field_back_at'),     // 预计还款时间
            static::$t->_('global.apply.status.text'), //申请状态
            static::$t->_('global_pay_status'),  //支付状态
            static::$t->_('loan_loan_status_text'),       //借款单状态
            static::$t->_('loan_left_amount'),        //剩余未还款金额
            static::$t->_('loan_real_un_pay_amount'),//实际未还款金额
            static::$t->_('re_field_currency_text'),        //币种
            static::$t->_('loan_back_amount'),        //转账归还小计

            static::$t->_('re_field_loan_amount'),        //报销冲减借款小计
            static::$t->_('loan_reimbursement_no'),        //关联报销单号


            static::$t->_('re_field_r_loan_amount'),        //报销单号冲减的金额
            static::$t->_('re_field_r_currency_text'),        //报销单号冲减的币种
            static::$t->_('re_field_r_status'),        //报销单号的审批状态
            static::$t->_('re_field_pay_status'),        //报销单号的支付状态
            static::$t->_('re_field_back_amount'),        // 申请的转账归还的金额
            static::$t->_('re_field_back_apply_date'),        // 申请的转账归还填写的银行日期
            static::$t->_('re_field_back_audit_status'),        //转账归还的审批状态

            static::$t->_('re_field_bank_name'),        //收款人户名
            static::$t->_('re_field_bank_account'),        //收款人账号
            static::$t->_('re_field_bank_type'),        //收款人开户银行
            static::$t->_('loan_export_field_apply_pay_at'),//借款申请银行流水日期

        ];

        return $this->exportExcel($header, $new_data, $file_name);
    }


    /**
     * 获得借款总额，通过转入的货币
     *
     * @param $amount (integer 没有乘以1000的数)
     * @param $to_currency
     * @param $loan_id
     * @param $user_id
     * @param bool $is_submit 是否是提交，提交的话会加锁，并且修改back_status
     * @param int $apply_id 申请人ID
     * @param int $apply_cost_company_id 报销申请单-费用所属公司
     * @return array
     */
    public function getAmountFromCurrency($amount, $to_currency, $loan_id, $user_id, $is_submit = false, $apply_id = 0, $apply_cost_company_id = 0)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = '0';

        $arr = [];

        try {
            $this->logger->info('getAmountFromCurrency 参数列表: ' . json_encode(func_get_args(), JSON_UNESCAPED_UNICODE));
            if (empty($loan_id)) {
                throw new ValidationException('loan_id is null');
            }

            //如果是提交的时候，锁住
            // 通过发起人ID获取借款单;15873需求按照借款单状态（未开始归还或部分归还）查询借款单信息
            $loan = Loan::findFirst([
                'conditions' => 'id =:id: and create_id = :create_id: and loan_status IN ({loan_status:array}) and pay_status = :pay_status: and status = :status: and currency=:currency:',
                'bind' => [
                    'id' => $loan_id,
                    'create_id' => $user_id,
                    'loan_status' => [Enums\LoanEnums::LOAN_STATUS_NOT_START_RETURN, Enums\LoanEnums::LOAN_STATUS_PARTIAL_RETURN],
                    'pay_status' => Enums::LOAN_PAY_STATUS_PAY,
                    'status' => Enums::WF_STATE_APPROVED,
                    'currency' => $to_currency
                ],
                'for_update' => $is_submit
            ]);

            $this->logger->info('getAmountFromCurrency 借款数据(create_id=user_id): ' . json_encode(!empty($loan) ? $loan->toArray() : [], JSON_UNESCAPED_UNICODE));

            if (empty($loan) && !empty($apply_id)) {
                // 通过申请人ID获取借款单;15873需求按照借款单状态（未开始归还或部分归还）查询借款单信息
                $loan = Loan::findFirst([
                    'conditions' => 'id =:id: and create_id = :create_id: and loan_status IN ({loan_status:array}) and pay_status = :pay_status: and status = :status: and currency=:currency:',
                    'bind' => [
                        'id' => $loan_id,
                        'create_id' => $apply_id,
                        'loan_status' => [Enums\LoanEnums::LOAN_STATUS_NOT_START_RETURN, Enums\LoanEnums::LOAN_STATUS_PARTIAL_RETURN],
                        'pay_status' => Enums::LOAN_PAY_STATUS_PAY,
                        'status' => Enums::WF_STATE_APPROVED,
                        'currency'=>$to_currency
                    ],
                    'for_update' => $is_submit
                ]);

                $this->logger->info('getAmountFromCurrency 借款数据(create_id=apply_id): ' . json_encode(!empty($loan) ? $loan->toArray() : [], JSON_UNESCAPED_UNICODE));
            }

            if (empty($loan)) {
                throw new ValidationException(static::$t->_('loan_notfound_or_status_error'), ErrCode::$VALIDATE_ERROR);
            }

            $amount = bcmul($amount,1000);
            $data = '0';

            // 剩余可抵扣金额
            $left_amount = bcsub($loan->amount, $loan->re_amount);
            $left_amount = bcsub($left_amount, $loan->back_amount); //这时候还款金额，暂时肯定是0

            //借款大
            if (bccomp($left_amount, $amount) >= 0) {
                $data = $amount;
                /*if ($is_submit) {
                    $loan->re_amount = bcadd($loan->re_amount,$loan->amount);
                }*/
            } else {
                //借款小,全部还完，已还款
                $data = $left_amount;
                /*if ($is_submit) {
                    $loan->re_amount = $loan->amount;
                }*/
            }

            $this->logger->info("getAmountFromCurrency 借款: 剩余可抵扣金额 left_amount={$left_amount}, 本次可抵扣金额={$data}");

            if ($is_submit) {
                //15872【OA】报销核销借款限制公司需求申请费用所属公司需要跟关联的借款单中的申请人所属公司一致
                if ($apply_cost_company_id != $loan->create_company_id) {
                    throw new ValidationException(static::$t->_('reimbursement_submit_apply_cost_company_error'), ErrCode::$VALIDATE_ERROR);
                }

                $loan->re_amount = bcadd($loan->re_amount, $data);//更新报销金额->借款单的报销抵扣金额中累计
                $loan->is_used_by_re = 1;   //被报销单关联过
                $loan->updated_at = date('Y-m-d H:i:s');

                $this->logger->info('getAmountFromCurrency 借款待更新的数据(被关联):' . json_encode($loan->toArray(), JSON_UNESCAPED_UNICODE));

                if ($loan->save() === false) {
                    throw new \Exception('报销关联借款金额: loan状态更新失败, 原因可能是: ' . get_data_object_error_msg($loan), ErrCode::$SYSTEM_ERROR);
                }
            }

            /*
                $enumsService = new EnumsService();
            foreach ($loans as $loan) {
                $data = bcadd($data, $loan->amount);
                *if ($loan->currency == $to_currency) {
                    $data = bcadd($data, $loan->amount);
                } else {
                    //借款货币->转默认货币额汇率
                    $rate = $enumsService->getCurrencyExchangeRate($loan->currency);
                    //借款货币->默认货币
                    $temp = $enumsService->amountExchangeRateCalculation($loan->amount, $rate);

                    $to_rate = $enumsService->getCurrencyExchangeRate($to_currency);
                    $data = bcadd($data, $enumsService->amountExchangeRateCalculation($temp, 1 / $to_rate));
                }
                if ($is_submit) {
                    $loan->back_status = 2;
                    $loan->save();
                }
            }*/
            $arr = $loan->toArray();

        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
            $this->logger->info('loan=getAmountFromCurrency===' . $e->getMessage());
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('loan=getAmountFromCurrency===' . $e->getMessage());
        }

        $res = [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];

        //有可能有不是当前操作人的
        if ($is_submit) {
            $res['loan'] = $arr;
        } else {
            $res['data'] = bcdiv($data, 1000, 2);
        }

        return $res;
    }

    /**
     * @param $id
     * @return array
     */
    public function getLoanRelDetail($id = 0)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $builder = $this->modelsManager->createBuilder();
        $builder->columns('distinct r.id,r.no,r.loan_amount,r.currency,r.status,r.pay_status');
        $builder->from(['rl' => ReimbursementRelLoan::class]);
        $builder->leftjoin(Loan::class, 'l.id=rl.loan_id', 'l');
        $builder->leftjoin(Reimbursement::class, 'r.id=rl.re_id', 'r');
        $builder->andWhere('rl.loan_id = :loan_id:', ['loan_id' => $id]);
        $builder->andWhere('rl.is_deleted = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);

        $data = $builder->getQuery()->execute()->toArray();

        foreach ($data as &$item) {
            $item['loan_amount'] = $item['loan_amount'] ? bcdiv($item['loan_amount'], 1000, 2) : 0;
            $item['currency_text'] = static::$t->_(GlobalEnums::$currency_item[$item['currency']]) ?? '';
            $item['status_text'] = static::$t->_(Enums::$loan_status[$item['status']]);
            $item['pay_status_text'] = static::$t->_(Enums::$payment_pay_status[$item['pay_status']]);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' =>$data
        ];
    }

    /**
     * @param array $loan_ids
     * @return array
     */
    public function getLoanPayBankItem($loan_ids = [])
    {
        if (empty($loan_ids)) {
            return [];
        }

        // 收款账号和银行
        return LoanPayBank::find([
            'conditions' => 'loan_id IN ({loan_ids:array})',
            'bind' => ['loan_ids' => $loan_ids],
        ])->toArray();
    }

    /**
     * 获取每笔借款单是否有归还中归还单
     * @param array $loan_ids
     * @return array
     */
    public function getLoanReturnItem($loan_ids = [])
    {
        if (empty($loan_ids)) {
            return [];
        }

        // 借款归还单记录
        $loan_return_list = LoanReturnModel::find([
            'conditions' => 'loan_id IN ({loan_ids:array})',
            'bind' => ['loan_ids' => $loan_ids],
        ])->toArray();

        //返回每笔借款单实际归还总金额、借款单是否有归还中归还单
        $loan_return_arr = [];
        if (!empty($loan_return_list)) {
            foreach ($loan_return_list as $return) {
                $loan_id = $return['loan_id'];
                //归还方式=转账归还&&归还状态=归还中，才是归还中
                if (isset($loan_return_arr[$loan_id])) {
                    $loan_return_arr[$loan_id]['back_status_ing'] = $loan_return_arr[$loan_id]['back_status_ing'] ? true : ($return['back_status'] == Enums\LoanEnums::LOAN_BACK_STATUS_ING && $return['back_type'] == Enums\LoanEnums::LOAN_BACK_TYPE_TRANSFER ? true : false);
                } else {
                    $loan_return_arr[$loan_id]['back_status_ing'] = ($return['back_status'] == Enums\LoanEnums::LOAN_BACK_STATUS_ING && $return['back_type'] == Enums\LoanEnums::LOAN_BACK_TYPE_TRANSFER) ? true : false;
                }

            }
        }
        return $loan_return_arr;
    }

    /**
     * 根据在职状态过滤符合条件的借款单申请人工号
     *
     * @param array $staff_states
     * @return array
     */
    public function getMatchStaffStateLoanCreatedIds(array $staff_states = [])
    {
        if (empty($staff_states)) {
            return [];
        }

        // 所有借款申请人(目前泰国pro300+,根据数据增长趋势, 约10年内根据全量数据过滤没啥性能问题)
        $all_loan_created_ids = Loan::find([
            'columns' => 'create_id',
            'group' => 'create_id'
        ])->toArray();
        $all_loan_created_ids = array_values(array_filter(array_column($all_loan_created_ids, 'create_id')));
        if (empty($all_loan_created_ids)) {
            return [];
        }

        // 根据在职状态过滤上述工号
        $all_loan_created_ids = (new HrStaffRepository())->getStaffItemByStaffIdStates($all_loan_created_ids, $staff_states);
        return array_column($all_loan_created_ids, 'staff_info_id');
    }

    /**
     * 获取员工的在职状态
     *
     * @param array $staff_ids
     * @return array
     */
    public function getStaffStateByIds(array $staff_ids = [])
    {
        $result = [];
        if (empty($staff_ids)) {
            return $result;
        }

        $staff_ids = (new HrStaffRepository())->getStaffItemByStaffIdStates($staff_ids);

        $translate = static::$t;
        foreach ($staff_ids as $info) {
            $_state = 0;
            switch ($info['state']) {
                case StaffInfoEnums::STAFF_STATE_IN:
                    if ($info['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_NO) {
                        $_state = StaffInfoEnums::STAFF_STATE_IN;
                    } else if ($info['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES) {
                        $_state = StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE;
                    }

                    break;
                case StaffInfoEnums::STAFF_STATE_LEAVE:
                case StaffInfoEnums::STAFF_STATE_STOP:
                    $_state = $info['state'];
                    break;
                default:
                    break;
            }

            $result[$info['staff_info_id']] = [
                'code' => (string) $_state,
                'label' => !empty($_state) ? $translate[StaffInfoEnums::$staff_state[$_state]] : ''
            ];
        }

        unset($staff_ids);
        return $result;
    }

    /**
     * 导出关联了报销的借款单(全部)
     */
    public function exportAllLoanDataByRelReimbursement()
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            'loan.lno',
            'reimbursement.no',
            'reimbursement.loan_amount',
            'reimbursement.currency',
            'reimbursement.status',
            'reimbursement.pay_status',
        ]);
        $builder->from(['loan' => Loan::class]);
        $builder->leftjoin(ReimbursementRelLoan::class, 'loan.id = rel.loan_id', 'rel');
        $builder->leftjoin(Reimbursement::class, 'rel.re_id = reimbursement.id', 'reimbursement');
        $builder->andWhere('rel.is_deleted = :is_deleted: AND rel.id IS NOT NULL', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);
        $builder->orderBy('loan.id DESC');
        $list = $builder->getQuery()->execute()->toArray();

        $excel_data = [];
        foreach ($list as $item) {
            $excel_data[] = [
                $item['lno'],
                $item['no'],
                $item['loan_amount'] ? bcdiv($item['loan_amount'], 1000, 2) : 0,
                static::$t->_(GlobalEnums::$currency_item[$item['currency']]) ?? '',
                static::$t->_(Enums::$loan_status[$item['status']]),
                static::$t->_(Enums::$payment_pay_status[$item['pay_status']])
            ];
        }

        // Excel表头
        $excel_header = [
            static::$t->_('loan_export_filed_no'), // 借款单号
            static::$t->_('loan_export_filed_rel_rno'),// 关联的报销单号
            static::$t->_('loan_export_filed_write_down_loans'),// 冲减借款金额
            static::$t->_('loan_export_filed_currency'),// 报销单币种
            static::$t->_('loan_export_filed_approval_status'),// 报销单审批状态
            static::$t->_('loan_export_filed_pay_status'),// 报销单支付状态
        ];

        // 生成Excel
        $file_name = '借款单-报销冲减明细 ' . date('Ymd') . '.xlsx';
        return $this->exportExcel($excel_header, $excel_data, $file_name);
    }

    /**
     * 导出发起归还的借款单(全部)
     */
    public function exportLoanDataByRelReturn()
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            'loan.lno',
            'return.back_apply_date',
            'return.back_amount',
            'return.back_audit_status',
        ]);
        $builder->from(['loan' => Loan::class]);
        $builder->leftjoin(LoanReturnModel::class, 'loan.id = return.loan_id', 'return');
        $builder->andWhere('return.id IS NOT NULL');
        $builder->orderBy('loan.id DESC');
        $list = $builder->getQuery()->execute()->toArray();

        $excel_data = [];
        foreach ($list as $item) {
            $excel_data[] = [
                $item['lno'],
                $item['back_apply_date'],
                $item['back_amount'] ? bcdiv($item['back_amount'], 1000, 2) : 0,
                static::$t->_(Enums::$loan_status[$item['back_audit_status']]),
            ];
        }

        // Excel表头
        $excel_header = [
            static::$t->_('loan_export_filed_no'), // 借款单号
            static::$t->_('loan_return_export_field_apply_date'),// 转账归还申请日期
            static::$t->_('loan_return_export_filed_back_amount'),// 转账归还金额
            static::$t->_('loan_return_export_filed_approval_status'),// 归还审批状态
        ];

        // 生成Excel
        $file_name = '借款单-转账归还明细 ' . date('Ymd') . '.xlsx';
        return $this->exportExcel($excel_header, $excel_data, $file_name);

    }

    /**
     * 数据查询导出
     * 借款表和报销抵扣关联数据
     * 借款表和借款归还关联数据
     * 剔除重复的无抵扣无归还的数据行
     * 报表将上面数据合并
     *
     * @param $condition
     * @param $user
     * @param int $data_type
     * @return array
     */
    public function exportList($condition, $user, $data_type = 0)
    {

        $loan_data = [];
        $data      = [];

        // 报销关联单号
        $rei_no = $condition['rno'] ?? '';
        try {
            $fields = 'c.id,c.lno,c.lname,c.status,c.currency,c.amount,c.re_amount,c.back_amount,c.refuse_reason,
            c.cancel_reason,c.create_id,c.create_name,c.create_company_id,c.create_company_name,c.create_job_title_name,c.cost_center_name,
            c.create_email,c.pay_type,c.finished_at,c.back_at,c.create_department_name,c.created_at,c.pay_status,c.loan_status,
            c.create_phone,c.type,c.event_info,c.create_date,c.approved_at,lp.pay_date,lp.created_at AS pay_created_at, c.is_pay_module';

            $return_fields = $fields . ',lr.back_date,lr.back_amount as r_back_amount,lr.back_actual_amount,lr.back_type,lr.back_audit_status,lr.loan_id';

            $rei_fields  = $fields . ',r.loan_amount,r.no,r.currency as r_currency,r.status as r_status,r.pay_status as r_pay_status';
            $rei_builder = $this->modelsManager->createBuilder();
            $rei_builder->columns($rei_fields);

            $rei_builder->from(['c' => Loan::class]);
            $rei_builder->leftJoin(LoanPay::class, 'c.id = lp.loan_id', 'lp');
            $rei_builder->leftjoin(ReimbursementRelLoan::class, 'c.id = rel.loan_id', 'rel');
            $rei_builder->leftjoin(Reimbursement::class, 'rel.re_id = r.id', 'r');
            $rei_builder = $this->getExportCondition($rei_builder, $condition, $user, $data_type);
            // 是否根据报销单号查询
            if (!empty($rei_no)) {
                $rei_builder->andWhere('r.no = :no:', ['no' => $rei_no]);
            }

            if (!empty($pay_status)) {
                $rei_builder->andWhere('r.pay_status = :pay_status:', ['pay_status' => $pay_status]);
            }

            $rei_builder->andWhere('rel.is_deleted = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);
            $rei_builder->orderBy('c.id DESC');
            $rei_list = $rei_builder->getQuery()->execute()->toArray();


            $loan_amount_arr = [];
            foreach ($rei_list as &$item) {
                if ($item['r_pay_status'] == Enums::LOAN_PAY_STATUS_PAY) {
                    $loan_amount_arr[$item['lno']] = bcadd($loan_amount_arr[$item['lno']] ?? 0, $item['loan_amount']);
                }

                $item['back_apply_date']   = '';
                $item['r_back_amount']     = '';
                $item['back_audit_status'] = '';

                if (empty($item['no'])) {
                    $loan_data[] = $item;
                } else {
                    if (!empty($rei_no)) {
                        $rei_data[] = $item;
                    }

                    $data[] = $item;
                }

            }


            $return_builder = $this->modelsManager->createBuilder();

            $return_builder->columns($return_fields);

            $return_builder->from(['c' => Loan::class]);
            $return_builder->leftJoin(LoanPay::class, 'c.id = lp.loan_id', 'lp');
            $return_builder->leftjoin(LoanReturnModel::class, 'c.id = lr.loan_id', 'lr');


            $return_builder = $this->getExportCondition($return_builder, $condition, $user, $data_type);
            if (!empty($rei_data) && !empty($rei_no)) {
                $lno_arr = array_values(array_unique(array_column($rei_data, 'lno')));

                if (is_array($lno_arr)) {
                    $return_builder->inWhere('c.lno', $lno_arr);

                }

            }

            $return_builder->orderBy('c.id DESC');
            $return_list = $return_builder->getQuery()->execute()->toArray();


            foreach ($return_list as &$item) {

                $item['loan_amount']  = '';
                $item['no']           = '';
                $item['r_currency']   = '';
                $item['r_status']     = '';
                $item['r_pay_status'] = '';
                if (empty($item['loan_id'])) {
                    $loan_data[] = $item;
                } else {
                    $data[] = $item;
                }

            }

            $lno_total_arr = array_column($data, 'lno');

            if (!empty($loan_data)) {
                $loan_data = array_column($loan_data, null, 'lno');

                foreach ($loan_data as &$item) {
                    if (!in_array($item['lno'], $lno_total_arr)) {
                        $data[] = $item;
                    }
                }
            }

            //计算 已支付抵扣金额合计
            foreach ($data as &$item) {
                $item['loan_amounts'] = $loan_amount_arr[$item['lno']] ?? 0;
            }

        } catch (\Exception $e) {

            $real_message = $e->getMessage();
            $this->logger->warning('loan-export-failed:' . $real_message);

        }

        return $this->handleItems($data, [], $data_type);


    }

    /**
     * 借款导出相关条件组合
     *
     * @param $builder
     * @param $condition
     * @param array $user
     * @param $data_type
     * @return object|null
     * @throws BusinessException
     */
    public function getExportCondition($builder, $condition, $user = [], $data_type = 0)
    {
        $lno        = $condition['lno'] ?? '';
        $status     = $condition['status'] ?? 0;
        $pay_status = $condition['pay_status'] ?? 0;
        $create_id  = $condition['create_id'] ?? 0;


        $loan_status = $condition['loan_status'] ?? 0;//借款单状态


        // 申请人公司
        $company_id = $condition['company_id'] ?? [];
        // 审批通过开始时间
        $approved_at_start = $condition['approved_at_start'] ?? null;
        // 审批通过结束时间
        $approved_at_end = $condition['approved_at_end'] ?? null;
        // 申请人部门
        $department_id = $condition['department_id'] ?? 0;
        // 支付时银行流水开始日期
        $pay_date_start = $condition['pay_date_start'] ?? null;
        // 支付时银行流水开始日期
        $pay_date_end = $condition['pay_date_end'] ?? null;

        // 申请人在职状态
        $created_id_states = isset($condition['staff_status']) && is_array($condition['staff_status']) ? $condition['staff_status'] : [];


        if (!empty($lno)) {
            $builder->andWhere('c.lno = :lno:', ['lno' => $lno]);
        }

        if (!empty($create_id)) {
            $builder->andWhere('c.create_id = :create_id:', ['create_id' => $create_id]);
        }

        if (!empty($status)) {
            $builder->andWhere('c.status = :status:', ['status' => $status]);
        }

        if (!empty($pay_status)) {
            $builder->andWhere('c.pay_status = :pay_status:', ['pay_status' => $pay_status]);
        }

        //15873需求后按照借款单状态筛选
        if (!empty($loan_status)) {
            if (is_array($loan_status)) {
                $builder->andWhere('c.loan_status IN ({loan_status:array})', ['loan_status' => $loan_status]);
            } else {
                $builder->andWhere('c.loan_status = :loan_status:', ['loan_status' => $loan_status]);
            }
        }

        if (!empty($company_id)) {
            if (is_array($company_id)) {
                $builder->andWhere('c.create_company_id IN ({create_company_id:array})', ['create_company_id' => $company_id]);
            } else {
                $builder->andWhere('c.create_company_id = :create_company_id:', ['create_company_id' => $company_id]);
            }
        }

        if (!empty($approved_at_start) && !empty($approved_at_end)) {
            $builder->andWhere('c.approved_at >= :approved_at_start: and c.approved_at <= :approved_at_end:', [
                'approved_at_start' => $approved_at_start,
                'approved_at_end'   => $approved_at_end
            ]);
        }

        if (!empty($department_id)) {
            $builder->andWhere('c.create_node_department_id = :department_id:', ['department_id' => $department_id]);
        }

        if (!empty($pay_date_start) && !empty($pay_date_end)) {

            $builder->andWhere('lp.pay_date >= :pay_date_start: and lp.pay_date <= :pay_date_end:', [
                'pay_date_start' => $pay_date_start,
                'pay_date_end'   => $pay_date_end
            ]);
        }

        // 根据员工在职状态过滤申请人: 返回空则下述inWhere取数结果为空
        if (!empty($created_id_states)) {
            $loan_created_ids = $this->getMatchStaffStateLoanCreatedIds($created_id_states);
            $builder->inWhere('c.create_id', $loan_created_ids);
        }

        // 数据查询-导出 对接通用数据权限
        if ($data_type == self::LIST_TYPE_EXPORT && !empty($user)) {
            // 业务表参数
            $table_params = [
                'table_alias_name' => 'c',
                'create_id_field' => 'create_id',
                'create_node_department_id_filed' => 'create_node_department_id',
            ];
            $builder = CommonDataPermissionService::getInstance()->getCommonDataPermission($user, $builder, Enums\SysConfigEnums::SYS_MODULE_LOAN, $table_params);
        }

        return $builder;
    }




}
