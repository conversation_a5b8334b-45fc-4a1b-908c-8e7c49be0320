<?php

namespace App\Modules\Loan\Services;

use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\RedisClient;
use App\Models\backyard\HrStaffInfoModel;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Common\Services\StoreService;
use App\Modules\Loan\Models\Contract;
use App\Models\backyard\SysStoreModel;
use App\Modules\Purchase\Services\BaseService as PurchaseService;
use App\Modules\OrdinaryPayment\Services\BaseService as OrdinaryPaymentServices;
use App\Modules\Reimbursement\Models\BankListModel;
use App\Modules\User\Models\DepartmentModel;
use App\Modules\Organization\Models\SysDepartmentModel;
use App\Modules\User\Services\UserService;
use App\Repository\HrStaffRepository;
use App\Util\RedisExpire;
use App\Util\RedisKey;

class BaseService extends \App\Library\BaseService
{
    const LIST_TYPE_APPLY = 1;//借款归还-列表
    const LIST_TYPE_AUDIT = 2;//借款审核-借款申请-列表
    const LIST_TYPE_FYR = 3;//借款回复-列表
    const LIST_TYPE_DATA = 4;//数据查询-列表
    const LIST_TYPE_BACK_AUDIT = 5;//借款审核-借款归还-列表
    const LIST_TYPE_PAY = 6;//借款支付
    const LIST_TYPE_EXPORT = 7;//数据查询-导出



    const SHORT_TEXT_LEN ='0,20';
    const LONG_TEXT_LEN = '0,50';
    const REQUIRED_SHORT_TEXT_LEN = '5,20';
    const REQUIRED_LONG_TEXT_LEN = '10,50';

    // 详情通用校验
    public static $validate_detail = [
        'id' => 'Required|IntGe:1'
    ];

    // 借款回复详情校验
    public static $validate_reply_detail = [
        'id' => 'Required|IntGe:1',// 业务单ID
        'fyr_id' => 'Required|IntGe:0',// 征询ID
    ];

    private static $validate_param = [
        'lname' => 'Required|StrLenGeLe:1,50',                      //合同名称
        'lno' => 'Required|StrLenGeLe:10,20',                       //合同编号
        'create_phone'=>'Required|Numbers|StrLenLe:15',   //申请人电话
        'create_email'=>'Required|Email',   //申请人邮箱
        'type'=>'Required|IntIn:1,2,3,4,5,6',     //申请事由
        'type_other'=>'IfIntEq:type,5|Required|StrLenGeLe:5,30',//申请事由其他
        'pay_type'=>'Required|IntIn:'.GlobalEnums::VALIDATE_PAYMENT_METHOD_PARAMS,       //付款方式
        'currency' => 'Required|IntIn:'.GlobalEnums::VALIDATE_CURRENCY_PARAMS,     //币种
        'event_name'=>'Required|StrLenGeLe:'.self::REQUIRED_SHORT_TEXT_LEN,     //事项名称
        'amount' => 'Required|FloatGt:0',         //金额
        'finished_at'=>'Required|Date',  //预计完成时间
        'back_at'=>'Required|Date',       //预计还款时间
        'event_info'=>'Required|StrLenGeLe:10,1000',      //事项说明
        'travel.travel_id' => 'IfIntEq:type,1|Required',        //差旅信息
        'bank.account'=>    'IfIntEq:pay_type,2|Required|StrLenGeLe:1,100|>>>:account error',   //银行账号
        'bank.bank_type' => 'IfIntEq:pay_type,2|Required|StrLenGeLe:1,50|>>>:bank_type error', // 开户行
        'bank.name'              => 'IfIntEq:pay_type,2|Required|StrLenGeLe:1,100|>>>:name error', // 收款人户名
        'create_company_id'=>'Required|IntGt:0',//申请人公司ID
        'consent_file' => 'Required|Arr|ArrLenGeLe:1,10',//同意书附件
        'consent_file[*].bucket_name' => 'Required',
        'consent_file[*].object_key' => 'Required',
        'consent_file[*].file_name' => 'Required',
    ];

    public static $validate_pay_param = [
        'id' => 'Required|IntGe:1',
        'is_sign' => 'Required|IntIn:1,2',                      //合同名称
        'not_sign_reason'=>'IfIntEq:is_sign,2|Required|StrLenGeLe:'.self::REQUIRED_LONG_TEXT_LEN,
        'pay_type'=>'IfIntEq:is_sign,1|Required|IntIn:0,1,2,3',
        'pay_date'=>'IfIntEq:is_sign,1|Required|Date',
        'sign_date'=>'IfIntEq:is_sign,1|Required|Date',
        'pay_bank_account' => 'IfIntEq:is_sign,1|StrLenGeLe:1,200',
        'pay_bank_name' => 'IfIntEq:is_sign,1|StrLenGeLe:1,200',
    ];

    private static $validate_update = [
        'id' => 'Required|IntGe:1'
    ];

    public static function handleParams($params, $not_must)
    {
        if (empty($not_must) || !is_array($not_must)) {
            return $params;
        }
        foreach ($not_must as $value) {
            if (isset($params[$value]) && empty($params[$value])) {
                unset($params[$value]);
            }
        }
        return $params;
    }

    /**
     * @param bool $is_update
     * @return array
     */
    public static function getValidateParams($is_update = false)
    {
        $rules = [];
        if ($is_update === true) {
            $rules = array_merge($rules, self::$validate_update);
        }

        return array_merge(self::$validate_param, $rules);
    }

    /**
     * 获取合同编号
     * @return string
     */
    public static function getLoanNo($date)
    {
        $key = self::getLoanCounterKey($date);

        if (self::getLoanCounter($key)) {           //有计数器
            $lno = self::incrContractCounter($key);
        } else {                                    //没有计数器（一直都没有，有但是存在宕机）
            $lno = self::setLoanCounter($key);
        }
        return $date . sprintf('%02s', $lno);
    }

    /**
     * 判断计数器是否存在
     * @param string $key
     * @return bool|int
     */
    private static function getLoanCounter($key)
    {
        return RedisClient::getInstance()->getClient()->exists($key);
    }

    /**
     * 计数器不存在的情况下
     * @param string $key
     * @return bool|int
     */
    private static function setLoanCounter($key)
    {
        $lno = 1;
        RedisClient::getInstance()->getClient()->setex($key, RedisExpire::ONE_DAY, $lno);
        return $lno;
    }

    /**
     * 计数器存在的情况下
     * @param string $key
     * @return int
     */
    private static function incrContractCounter($key)
    {
        return RedisClient::getInstance()->getClient()->incrBy($key, 1);
    }

    private static function getLoanCounterKey($date){
        return RedisKey::CONTRACT_CREATE_COUNTER."_".$date;
    }


    /**
     * 去bi里面取相关数据
     * @param $userId
     * @return array
     */
    public function getUserMetaFromBi($userId){

        $model = (new UserService())->getUserByIdInRbi($userId);
        if(empty($model)){
            return [];
        }

        $data = [];
        $data['create_id']  = $model->staff_info_id??"";
        $data['create_name'] = $this->getNameAndNickName($model->name??"",$model->nick_name??"");
        $data['create_nick_name'] = $model->nick_name ?? '';
        $data['create_email'] = $model->email??"";
        $data['create_phone'] = $model->mobile??"";

        $data['create_department_id'] = $model->sys_department_id;
        $data['create_department_name'] = "";

        $data['create_node_department_id'] = $model->node_department_id;
        $data['create_node_department_name'] = "";

        $t = DepartmentModel::find([
            "conditions" =>"id in (?0,?1)",
            "bind"=>[
                0=>$data['create_department_id'],
                1=>$data['create_node_department_id']
            ]
        ]);
        $data['create_company_id'] = '0';
        $data['create_company_name'] =  '';
        $dept_info                 = SysDepartmentModel::findFirst($model->sys_department_id);
        $parent_dept_ids          = DepartmentModel::getParentIdsByDepartmentId((string)$dept_info->ancestry_v3);
        if (!empty($parent_dept_ids)) {
            // 所属公司
            foreach ($parent_dept_ids as $k1 => $v1) {
                if (1 == $v1['type'] && 0 == $v1['level']) {
                    $data['create_company_id'] = $v1['id'] ?? 0;
                    $data['create_company_name'] = $v1['name'] ?? '';
                }
            }
        }
        if(!empty($t)){
            // 一级部门/直属部门
            $tArr = array_column($t->toArray(),"name","id");
            $data['create_department_name'] = isset($tArr[$data['create_department_id']])?$tArr[$data['create_department_id']]:"";
            $data['create_node_department_name'] = isset($tArr[$data['create_node_department_id']])?$tArr[$data['create_node_department_id']]:"";
        }

        $data['create_display_department_name'] = !empty($data['create_node_department_name'])?$data['create_node_department_name']:$data['create_department_name'];
        $data['create_job_title_id'] = $model->job_title;
        $data['create_job_title_name'] = "";
        $t = $model->getJobTitle();
        if(!empty($t)){
            $data['create_job_title_name'] = $t->job_name;
        }

        $data['bank']['name'] = $model->name;
        if (in_array(get_country_code(), [GlobalEnums::ID_COUNTRY_CODE, GlobalEnums::VN_COUNTRY_CODE])) {
            $staff_items_arr      = (new HrStaffRepository())->getStaffItems([$userId], [StaffInfoEnums::HR_STAFF_ITEMS_BANK_NO_NAME]);
            $data['bank']['name'] = $staff_items_arr[0]['value'] ?? $model->name;
        }

        $data['bank']['account'] = $model->bank_no;
        // 取bank_list表名称
        $bank_type_name = '';
        if (!empty($model->bank_type)) {
            $bank_info = BankListModel::findFirst($model->bank_type);
            $bank_type_name = isset($bank_info->bank_name) ? $bank_info->bank_name : '';
        }
        $data['bank']['bank_type'] = $bank_type_name;

        $data['create_job_title_level'] = $model->job_title_level;

        /****** 10708【OA】子公司借款审批流开始 ******/
        //修复之前的cost_center_name字段的bug，成本所属中心名称原来就么有存储，跟产品沟通呢，取申请人二级部门对应的pc_code
        $data['cost_center_name'] = (new OrdinaryPaymentServices())->getPcCodeByDepartmentId($data['create_node_department_id']);
        //如果所属网点不是head office(-1),取MS网点的pc code
        if ($model->sys_store_id != '-1'){
            $data['cost_center_name'] = (new StoreService())->getPcCodeByStoreId($model->sys_store_id);
        }
        //COO/CEO下的BU级公司列表
        $data['cost_company_list'] = (new PurchaseService())->getCooCostCompany();

        //依据【若带出的BU属于COO/CEO下，则不允许修改；若部门无BU，或BU不属于COO/CEO下，则为空；】需求，现需要判断当前申请人公司是否属于COO/CEO下
        $cost_company_kv = array_column($data['cost_company_list'],'cost_company_name','cost_company_id');
        if(!empty($data['create_company_id']) && key_exists($data['create_company_id'],$cost_company_kv)){
            $data['create_company_id'] = $data['create_company_id'];
            $data['create_company_name'] = $cost_company_kv[$data['create_company_id']];
        } else {
            //不属于则返回空
            $data['create_company_id'] = '0';
            $data['create_company_name'] =  '';
        }
        /****** 10708【OA】子公司借款审批流结束 ******/
        $data['create_store_id'] = $model->sys_store_id;
        if ($model->sys_store_id == Enums::HEAD_OFFICE_STORE_FLAG) {
            $data['create_store_name'] = Enums::PAYMENT_HEADER_STORE_NAME;
        } else {
            $tmp                      = SysStoreModel::findFirst(['conditions' => 'id = :id:', 'bind' => ['id' => $model->sys_store_id]]);
            $data['create_store_name'] = $tmp ? $tmp->name : '';
        }
        return $data;
    }

    /**
     * 获得借款待支付人
     * @return false|string[]
     */

    public function getLoanPayStaffIds(){
        $pay_staff_id = EnvModel::getEnvByCode('loan_pay_staff_id');
        $pay_staff_id = explode(',', $pay_staff_id);
        return $pay_staff_id ?? [];
    }

    public static function getSettingAuthStaffId(){
        $authIds = EnvModel::getEnvByCode('store_access_staff_id','');

        return !empty($authIds) ? explode(',',$authIds) : [];
    }

    //员工状态和翻译 待离职 返回999
    public static function formatStaffState($userId)
    {
        if(empty($userId)){
            return [];
        }
        $user = HrStaffInfoModel::findFirst([
            'columns' => 'staff_info_id, state, wait_leave_state',
            'conditions' => 'staff_info_id = :staff_info_id:',
            'bind' => ['staff_info_id' => $userId]
        ]);
        if(empty($user)){
            return [];
        }
        $user = $user->toArray();
        //新增 员工状态
        $state = $user['state'];
        if($state == StaffInfoEnums::STAFF_STATE_IN && $user['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES){
            $state = StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE;
        }
        $stateText = static::$t->_(StaffInfoEnums::$staff_state[$state]);
        return [$state, $stateText];

    }

}
