<?php

namespace App\Modules\Loan\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Loan\Services\BaseService;
use App\Modules\Loan\Services\DetailService;
use App\Modules\Loan\Services\ListService;
use App\Modules\Loan\Services\LoanFlowService;
use App\Modules\User\Services\UserService;
use App\Modules\Workflow\Services\FYRService;

class ConsultationController extends BaseController
{
    /**
     * 待回复征询列表
     * @Token
     */
    public function listAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, ListService::$not_must_params);
        try {
            Validation::validate($params, ListService::$validate_list_search);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $list = ListService::getInstance()->getList($params, $this->user, ListService::LIST_TYPE_FYR);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $list['data']);
    }

    /**
     * 借款回复详情
     * @Token
     */
    public function detailAction()
    {
        $data = $this->request->get();
        try {
            Validation::validate($data, BaseService::$validate_reply_detail);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }
        $res = DetailService::getInstance()->getCommonDetail($data, $this->user['id'], ListService::LIST_TYPE_FYR);
        if ($res['code'] == ErrCode::$SUCCESS) {
            [$res['data']['state'],$res['data']['state_text']] = DetailService::getInstance()->formatStaffState($res['data']['create_id']);
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     *
     * @Token
     */
    public function askAction()
    {
        $biz_id = $this->request->get('id', 'int');
        $note = $this->request->get('note','trim');
        $to_staffs = $this->request->get('to_staff');
        $attachments = $this->request->get('attachments');

        // 执行参数校验
        $check_data = [
            'id' => $biz_id,
            'note' => $note,
            'to_staff' => $to_staffs,
            'attachments' => $attachments
        ];
        FYRService::getInstance()->askValidation($check_data);

        $request = (new LoanFlowService())->getRequest($biz_id);
        $to_staffs = (new UserService())->getUserInfos($to_staffs);
        $result = FYRService::getInstance()->createAsk($request, $note, $to_staffs, $this->user, $attachments);

        return $this->returnJson($result['code'], $result['message']);
    }

    /**
     * 回复征询
     *
     * @Token
     */
    public function replyAction()
    {
        $ask_id = $this->request->get('ask_id','int');
        $note = $this->request->get('note','trim');
        $attachments = $this->request->get('attachments');

        // 执行参数校验
        $check_data = [
            'id' => $ask_id,
            'note' => $note,
            'attachments' => $attachments
        ];
        FYRService::getInstance()->replyValidation($check_data);

        $result = FYRService::getInstance()->createReply($ask_id, $note, $this->user, $attachments);
        return $this->returnJson($result['code'], $result['message']);
    }
}
