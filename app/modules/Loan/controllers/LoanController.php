<?php

namespace App\Modules\Loan\Controllers;

use App\Library\Enums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Loan\Services\AddService;
use App\Modules\Loan\Services\BaseService;
use App\Modules\Loan\Services\CancelService;
use App\Modules\Loan\Services\DetailService;
use App\Modules\Loan\Services\ListService;
use App\Modules\Loan\Services\LoanBackFlowService;
use App\Modules\Loan\Services\UpdateService;
use App\Modules\Loan\Services\LoanFlowService;
use App\Modules\User\Services\UserService;
use App\Util\RedisKey;
use Exception;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class LoanController extends BaseController
{
    /**
     * 借款添加
     * @Permission(action='loan.apply.apply')
     *
     * @return mixed
     * @throws ValidationException
     */
    public function addAction()
    {
        $data = $this->request->get();
        $data = BaseService::handleParams($data, AddService::$not_must_params);
        Validation::validate($data, BaseService::getValidateParams(false));

        $res = AddService::getInstance()->one($data, $this->user);
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 借款列表
     * @Permission(action='loan.apply.search')
     * @return mixed
     */
    public function listAction()
    {
        $params = $this->request->get();
        try {
            $params = BaseService::handleParams($params, ListService::$not_must_params);
            Validation::validate($params, ListService::$validate_list_search);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = ListService::getInstance()->getList($params, $this->user, ListService::LIST_TYPE_APPLY);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '',$res['data']);
        }
        return $this->returnJson($res['code'],$res['message']);
    }

    /**
     * 借款审核列表
     * @Permission(action='loan.audit.search')
     * @return mixed
     */
    public function auditListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, ListService::$not_must_params);
        try {
            Validation::validate($params, ListService::$validate_list_search);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = ListService::getInstance()->getList($params, $this->user, ListService::LIST_TYPE_AUDIT);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '',$res['data']);
        }
        return $this->returnJson($res['code'],$res['message']);
    }

    /**
     * 借款详情
     * @Permission(action='loan.apply.view')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/4717
     * @return Response|ResponseInterface
     */
    public function detailAction()
    {
        $data = $this->request->get();
        try {
            Validation::validate($data, BaseService::$validate_detail);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }
        $res = DetailService::getInstance()->getCommonDetail($data, $this->user['id']);
        //增加 在职状态 https://flashexpress.feishu.cn/wiki/MdYnwd0l2iWUYxkG6wWcL5WVnkf
        if ($res['code'] == ErrCode::$SUCCESS) {
            [$res['data']['state'],$res['data']['state_text']] = DetailService::getInstance()->formatStaffState($res['data']['create_id']);
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 借款审核详情
     * @Permission(action='loan.audit.view')
     *
     * @return Response|ResponseInterface
     */
    public function auditDetailAction()
    {
        $data = $this->request->get();
        try {
            Validation::validate($data, BaseService::$validate_detail);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }
        $res = DetailService::getInstance()->getCommonDetail($data, $this->user['id']);
        if ($res['code'] == ErrCode::$SUCCESS) {
            [$res['data']['state'],$res['data']['state_text']] = DetailService::getInstance()->formatStaffState($res['data']['create_id']);
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 借款下载
     * @Permission(action='loan.apply.download')
     * @return mixed
     */
    public function downloadAction()
    {
        $data = $this->request->get();
        $id = $data['id'] ?? 0;
        try {
            Validation::validate($data, BaseService::$validate_detail);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = DetailService::getInstance()->download($id, $this->user['id']);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'],$res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 借款审核通过
     * @Permission(action='loan.audit.audit')
     */
    public function approveAction()
    {
        $loan_id = $this->request->get('id','int',0);
        $note = $this->request->get('note','trim','');
        try {
            Validation::validate(['id'=>$loan_id,'note'=>$note], LoanFlowService::$validate_approve);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = (new LoanFlowService())->approve($loan_id,$note, $this->user);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', []);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 借款审核拒绝
     * @Permission(action='loan.audit.audit')
     */
    public function rejectAction()
    {
        $loan_id = $this->request->get('id','int',0);
        $note = $this->request->get('note','trim','');
        try {
            Validation::validate(['id'=>$loan_id,'note'=>$note], LoanFlowService::$validate_reject);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = (new LoanFlowService())->reject($loan_id, $note, $this->user);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', []);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 借款撤销
     * @Permission(action='loan.apply.cancel')
     *
     * @return Response|ResponseInterface
     */
    public function cancelAction()
    {
        $data = $this->request->get();
        $loan_id = $this->request->get('id','int');
        $note = $this->request->get('note', 'trim');

        try {
            Validation::validate($data, CancelService::$validate_cancel);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = (new LoanFlowService())->cancel($loan_id, $note, $this->user);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', []);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 借款获得默认值
     * @Permission(action='loan.apply.apply')
     * @return mixed
     */
    public function getDefaultAction()
    {
        // 申请人ID
        $apply_id = $this->request->get('apply_id');

        $us = new UserService();
        if (!empty($apply_id)) {
            $apply_user = $us->getUserById($apply_id);
            $user = $this->format_user($apply_user);
        } else {
            $user = $this->user;
        }

        $res = AddService::getInstance()->defaultData($user);
        if($res['code'] == ErrCode::$SUCCESS){
            [$res['data']['state'],$res['data']['state_text']] = AddService::getInstance()->formatStaffState($res['data']['create_id']);
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 借款支付
     * @Permission(action='loan.pay.pay')
     *
     * @return mixed
     */
    public function payAction()
    {
        $data = $this->request->get();

        $this->logger->info('借款支付 - 请求参数: ' . json_encode($data, JSON_UNESCAPED_UNICODE));
        $this->logger->info('借款支付 - 登录用户: ' . json_encode($this->user, JSON_UNESCAPED_UNICODE));

        try {
            $data['pay_type'] = empty($data['pay_type']) ? 0 : $data['pay_type'];
            Validation::validate($data, BaseService::$validate_pay_param);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }

        $res = UpdateService::getInstance()->pay($data['id'], $data, $this->user);

        $this->logger->info('借款支付 - 返回参数: ' . json_encode($res, JSON_UNESCAPED_UNICODE));
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 数据列表
     * @Permission(action='loan.data.search')
     * @return mixed
     */
    public function dataListAction()
    {
        $params = $this->request->get();
        $res = ListService::getInstance()->getList($params, $this->user, ListService::LIST_TYPE_DATA);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }


    /**
    * 数据详情
    * @Token
    * @return mixed
    */
    public function dataDetailAction()
    {
        $data = $this->request->get();
        try {
            Validation::validate($data, BaseService::$validate_detail);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }

        //uid=0，不用判断权限，能看所有
        $res = DetailService::getInstance()->getCommonDetail($data, 0);
        if($res['code'] == ErrCode::$SUCCESS){
            [$res['data']['state'],$res['data']['state_text']] = AddService::getInstance()->formatStaffState($res['data']['create_id']);
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 数据导出
     * @Permission(action='loan.data.export')
     *
     * @return mixed
     * @throws Exception
     */
    public function exportAction()
    {
        $params = $this->request->get();

        // 加锁处理
        $lock_key = md5('loan_data_export_' . $this->user['id']);
        $res = $this->atomicLock(function() use ($params){
            return ListService::getInstance()->export($params, $this->user, ListService::LIST_TYPE_EXPORT);
        }, $lock_key, 30);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 借款下载
     * @Permission(action='loan.data.download')
     * @return mixed
     */
    public function dataDownloadAction()
    {
        $data = $this->request->get();
        $id = $data['id'] ?? 0;
        try {
            Validation::validate($data, BaseService::$validate_detail);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = DetailService::getInstance()->download($id, 0);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'],$res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 借款-个人归还列表
     * @Permission(action='loan.return.search')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/25978
     * @return Response|ResponseInterface
     */
    public function backListAction()
    {
        $params = $this->request->get();

        $keys = [
            'pageSize',
            'pageNum',
            'lno',
            'back_at',
            'loan_status',
        ];

        $data = array_only($params, $keys);

        $data['pay_status'] = Enums::LOAN_PAY_STATUS_PAY;   //已支付
        $data['order'] = 'back_at asc';

        $res = ListService::getInstance()->getList($data, $this->user, ListService::LIST_TYPE_APPLY);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 借款归还-添加
     * @Permission(action='loan.return.add')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/26022
     * @return Response|ResponseInterface
     * @throws Exception
     */
    public function backAddAction()
    {
        $validate = [
            'id' => 'Required|IntGe:1',
            'back_amount'=>'Required|FloatGt:0',
            'bank_attachments' => 'Required|Arr|ArrLenGe:1',
            'bank_attachments[*].bucket_name'=>'Required|StrLenGeLe:1,63',
            'bank_attachments[*].object_key'=>'Required|StrLenGeLe:1,100',
            'bank_attachments[*].file_name'=>'Required|StrLenGeLe:1,200',
            'back_mark'=>'StrLenGeLe:0,1000'
        ];
        $params = $this->request->get();
        //15873需求，银行流水日期，菲律宾非必填，其他国家必填
        $country_code = get_country_code();
        if ($country_code != Enums\GlobalEnums::PH_COUNTRY_CODE) {
            $validate['back_date'] = 'Required|Date';
        } else if (!empty($params['back_date'])) {
            //菲律宾国家，填写了银行流水日期，则必须是日期格式
            $validate['back_date'] = 'Date';
        }
        $data = array_only($params,array_keys($validate));
        try {
            Validation::validate($data, $validate);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $us = new UserService();
        // 申请人ID
        $apply_id = $this->request->get('apply_id');
        if (!empty($apply_id)) {
            $apply_user = $us->getUserById($apply_id);
            $user = $this->format_user($apply_user);
        } else {
            $user = $this->user;
        }
        // 加锁处理
        $lock_key = md5(RedisKey::LOAN_BACK_ADD_LOCK_PREFIX . $data['id']);
        $res = $this->atomicLock(function() use ($data, $user) {
            return AddService::getInstance()->backAdd($data, $user);
        }, $lock_key, 5);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 借款数据查询-归还-添加
     * @Permission(action='loan.return.add')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/26022
     * @return Response|ResponseInterface
     * @throws Exception
     */
    public function backAddByOtherAction()
    {
        $validate = [
            'id' => 'Required|IntGe:1',
            'apply_id' => 'Required|IntGe:1',
            'back_amount'=>'Required|FloatGt:0',
            'bank_attachments' => 'Required|Arr|ArrLenGe:1',
            'bank_attachments[*].bucket_name'=>'Required|StrLenGeLe:1,63',
            'bank_attachments[*].object_key'=>'Required|StrLenGeLe:1,100',
            'bank_attachments[*].file_name'=>'Required|StrLenGeLe:1,200',
            'back_mark'=>'StrLenGeLe:0,1000'
        ];

        $params = $this->request->get();
        //15873需求，银行流水日期，菲律宾非必填，其他国家必填
        $country_code = get_country_code();
        if ($country_code != Enums\GlobalEnums::PH_COUNTRY_CODE) {
            $validate['back_date'] = 'Required|Date';
        } else if (!empty($params['back_date'])) {
            //菲律宾国家，填写了银行流水日期，则必须是日期格式
            $validate['back_date'] = 'Date';
        }
        $data = array_only($params, array_keys($validate));

        try {
            Validation::validate($data, $validate);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        // 权限校验
        if (!$this->getAuthorize()) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $this->t->_('staff_permission_is_forbidden'));
        }

        $us = new UserService();
        // 申请人ID
        $apply_id = $this->request->get('apply_id');
        $apply_user = $us->getUserById($apply_id);
        $user = $this->format_user($apply_user);
        // 加锁处理
        $lock_key = md5(RedisKey::LOAN_BACK_ADD_LOCK_PREFIX . $data['id']);
        $res = $this->atomicLock(function() use ($data, $user) {
            return AddService::getInstance()->backAdd($data, $user);
        }, $lock_key, 5);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 借款归还-审核列表
     * @Permission(action='loan.audit.search')
     * @return mixed
     */
    public function backAuditListAction(){
        $params = $this->request->get();

        $keys = [
            'lno',
            'create_id',
            'flag',
            'back_audit_status',
            'pageSize',
            'pageNum'
        ];

        // 不要过滤前端参数
//        $data = array_only($params, $keys);
        $params['pay_status'] = Enums::LOAN_PAY_STATUS_PAY;   //已支付
        $res = ListService::getInstance()->getList($params, $this->user, ListService::LIST_TYPE_BACK_AUDIT);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 借款归还-审核通过
     * @Permission(action='loan.audit.audit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/26026
     * @return Response|ResponseInterface
     */
    public function backApproveAction()
    {
        $loan_id = $this->request->get('id','int',0);
        $note = $this->request->get('note','trim','');
        $update_data = $this->request->get();

        try {
            Validation::validate(['id'=>$loan_id, 'note'=>$note], LoanFlowService::$validate_approve);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = (new LoanBackFlowService())->approve($loan_id, $note, $this->user, $update_data);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', []);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 借款归还-审核拒绝
     * @Permission(action='loan.audit.audit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/26030
     * @return Response|ResponseInterface
     */
    public function backRejectAction()
    {
        $loan_id = $this->request->get('id','int',0);
        $note = $this->request->get('note','trim','');
        try {
            $validate_reject = [
                'id' => 'Required|IntGe:1',
                'note' => 'StrLenGeLe:0,1000'   //产品没写，可以不必填。
            ];
            Validation::validate(['id'=>$loan_id,'note'=>$note], $validate_reject);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = (new LoanBackFlowService())->reject($loan_id, $note, $this->user);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', []);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 借款归还-撤销-暂时没有用到
     * @Permission(action='loan.apply.cancel')
     * @return Response|ResponseInterface
     */
    public function backCancelAction()
    {
        $data = $this->request->get();
        $loan_id = $this->request->get('id','int');
        $note = $this->request->get('note', 'trim');

        try {
            Validation::validate($data, CancelService::$validate_cancel);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = (new LoanBackFlowService())->cancel($loan_id, $note, $this->user);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', []);
        }
        return $this->returnJson($res['code'], $res['message']);
    }


    /**
     * 借款列表-关联报销单号详情
     * @return Response|ResponseInterface
     */
    public function loanRelDetailAction()
    {
        $loan_id = $this->request->get('id','int');

        $res = ListService::getInstance()->getLoanRelDetail($loan_id);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 借款支付列表
     * @Permission(action='loan.pay.search')
     * @return mixed
     */
    public function payListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, ListService::$not_must_params);
        try {
            Validation::validate($params, ListService::$validate_list_search);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = ListService::getInstance()->getList($params, $this->user, ListService::LIST_TYPE_PAY);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 借款审核详情
     * @Permission(action='loan.pay.view')
     *
     * @return Response|ResponseInterface
     */
    public function payDetailAction()
    {
        $data = $this->request->get();
        try {
            Validation::validate($data, BaseService::$validate_detail);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }

        $res = DetailService::getInstance()->getCommonDetail($data, $this->user['id'], BaseService::LIST_TYPE_PAY);
        if ($res['code'] == ErrCode::$SUCCESS) {
            [$res['data']['state'],$res['data']['state_text']] = DetailService::getInstance()->formatStaffState($res['data']['create_id']);
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 获取某条归还单审批流日志
     * @Permission(action='loan.apply.view')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/67672
     * @return Response|ResponseInterface
     */
    public function getLoanReturnAuthLogAction()
    {
        $data = $this->request->get();
        try {
            Validation::validate($data, BaseService::$validate_detail);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }
        $res = DetailService::getInstance()->getLoanReturnAuthLog($data['id']);
        return $this->returnJson(ErrCode::$SUCCESS, '', $res);
    }
}
