<?php
namespace App\Modules\Kpi\Controllers;

use App\Library\Enums\KpiActivityStaffEnums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Kpi\Services\ActivityStaffService;
use App\Modules\Kpi\Services\PermissionService;
use App\Modules\Kpi\Services\TemplateService;
use App\Modules\Kpi\Services\BaseService;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

/**
 * 下级目标管理
 * Class LeaderController
 * @package App\Modules\Kpi\Controllers
 */
class LeaderController extends BaseController
{
    public function initialize()
    {
        parent::initialize();
        PermissionService::getInstance()->isLeader($this->user);
    }

    /**
     * 列表
     * @Permission(action='kpi.activity.staff.byLeader')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/40578
     * @return Response|ResponseInterface
     */
    public function listAction()
    {
        try {
            $params = trim_array($this->request->get());
            $params = BaseService::handleParams($params, ActivityStaffService::$not_must_kpi_leader_staff_list);
            Validation::validate($params, ActivityStaffService::$validate_kpi_leader_staff_list);
            $res = ActivityStaffService::getInstance()->getActivityStaffListByLeader($params, $this->user['id']);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 员工个人KPI详情页
     * @Permission(action='kpi.activity.leader.staff.info')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/40522
     * @return Response|ResponseInterface
     */
    public function infoAction()
    {
        try {
            $params = trim_array($this->request->get());
            Validation::validate($params, ActivityStaffService::$validate_kpi_staff_detail);
            $res = ActivityStaffService::getInstance()->getActivityStaffInfo($params, $this->user, KpiActivityStaffEnums::PERMISSION_LEADER);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 导出员工KPI目标
     * @Permission(action='kpi.activity.leader.staff.export')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/40816
     * @return Response|ResponseInterface
     */
    public function exportAction()
    {
        try {
            $params = trim_array($this->request->get());
            Validation::validate($params, ActivityStaffService::$validate_kpi_staff_detail);
            // 加锁处理
            $lock_key = md5('kpi_staff_indicators_export_' . $params['activity_id'].'-'.$params['staff_id']);
            $res = $this->atomicLock(function () use ($params) {
                return ActivityStaffService::getInstance()->exportStaffKpi($params, $this->user, KpiActivityStaffEnums::PERMISSION_LEADER);
            }, $lock_key, 10);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson($res['code'] ?? ErrCode::$SYSTEM_ERROR, $res['message'] ?? $this->t->_('retry_later'), $res['data'] ?? []);
    }

    /**
     * 关键业绩KPI指标
     * @Permission(action='kpi.activity.leader.staff.info')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/40809
     * @return Response|ResponseInterface
     */
    public function indicatorsListAction()
    {
        try {
            $params = trim_array($this->request->get());
            Validation::validate($params, ActivityStaffService::$validate_kpi_staff_detail);
            $res = ActivityStaffService::getInstance()->getStaffIndicatorsList($params, $this->user, KpiActivityStaffEnums::PERMISSION_LEADER);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 给员工新建KPI指标
     * @Permission(action='kpi.activity.staff.add')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/40991
     * @return Response|ResponseInterface
     */
    public function addAction()
    {
        try {
            $params = $this->request->get();
            //增加参数过滤
            $params = filter_param($params);
            Validation::validate($params, ActivityStaffService::$kpi_staff_indicators_create);
            $res = ActivityStaffService::getInstance()->addIndicators($params, $this->user, KpiActivityStaffEnums::PERMISSION_LEADER);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 给员工编辑KPI指标
     * @Permission(action='kpi.activity.staff.edit')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/41012
     * @return Response|ResponseInterface
     */
    public function editAction()
    {
        try {
            $params = $this->request->get();
            //增加参数过滤
            $params = filter_param($params);
            Validation::validate($params, array_merge(ActivityStaffService::$kpi_staff_indicators_edit, ActivityStaffService::$kpi_staff_indicators_create));
            $res = ActivityStaffService::getInstance()->editStaffIndicators($params, $this->user, KpiActivityStaffEnums::PERMISSION_LEADER);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 删除指标
     * @Permission(action='kpi.activity.staff.del')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/41019
     * @return Response|ResponseInterface
     */
    public function delAction()
    {
        try {
            $params = $this->request->get();
            Validation::validate($params, array_merge(ActivityStaffService::$kpi_staff_indicators_edit, ActivityStaffService::$validate_kpi_staff_detail));
            $res = ActivityStaffService::getInstance()->delStaffIndicators($params, $this->user, KpiActivityStaffEnums::PERMISSION_LEADER);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 导入指标
     * @Permission(action='kpi.activity.staff.import')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/41033
     * @return Response|ResponseInterface
     */
    public function importAction()
    {
        try {
            $params = $this->request->get();
            $excel_file = $this->request->getUploadedFiles();
            Validation::validate($params, ActivityStaffService::$validate_kpi_staff_detail);
            // 加锁处理
            $lock_key = md5('kpi_activity_indicators_import_' . $this->user['id']);
            $res = $this->atomicLock(function () use ($params, $excel_file) {
                return ActivityStaffService::getInstance()->importIndicators($params, $excel_file, $this->user, KpiActivityStaffEnums::PERMISSION_LEADER);
            }, $lock_key, 10);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson($res['code'] ?? ErrCode::$SYSTEM_ERROR, $res['message'] ?? $this->t->_('retry_later'), $res['data'] ?? []);
    }

    /**
     * 存为模版
     * @Permission(action='kpi.activity.staff.save')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/41068
     * @return Response|ResponseInterface
     */
    public function saveAction()
    {
        try {
            $params = $this->request->get();
            Validation::validate($params,array_merge(ActivityStaffService::$validate_kpi_staff_detail, TemplateService::$kpi_template_create));
            $res = ActivityStaffService::getInstance()->saveTemplate($params, $this->user, KpiActivityStaffEnums::PERMISSION_LEADER);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 提交
     * @Permission(action='kpi.activity.staff.submit')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/40592
     * @return Response|ResponseInterface
     */
    public function submitAction()
    {
        try {
            $params = $this->request->get();
            Validation::validate($params, ActivityStaffService::$validate_kpi_staff_detail);
            $res = ActivityStaffService::getInstance()->submitStaffKpi($params, $this->user, KpiActivityStaffEnums::PERMISSION_LEADER);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 批量提交
     * @Permission(action='kpi.activity.staff.submit')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/40907
     * @return Response|ResponseInterface
     */
    public function batchSubmitAction()
    {
        try {
            $params = $this->request->get();
            Validation::validate($params, ActivityStaffService::$kpi_staff_batch_submit);
            // 加锁处理
            $lock_key = md5('kpi_activity_leader_batch_submit_' . $this->user['id']);
            $res = $this->atomicLock(function () use ($params) {
                return ActivityStaffService::getInstance()->batchSubmit($params, $this->user);
            }, $lock_key, 20);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson($res['code'] ?? ErrCode::$SYSTEM_ERROR, $res['message'] ?? $this->t->_('retry_later'), $res['data'] ?? []);
    }

    /**
     * 修改指标
     * @Permission(action='kpi.activity.staff.indicators')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/64977
     * @return Response|ResponseInterface
     */
    public function updateAction()
    {
        try {
            $params = $this->request->get();
            Validation::validate($params, ActivityStaffService::$validate_kpi_staff_detail);
            $res = ActivityStaffService::getInstance()->updateStaffKpi($params, $this->user, KpiActivityStaffEnums::PERMISSION_LEADER);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 引用模板
     * @Permission(action='kpi.activity.staff.quoteTemplate')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/40662
     * @return Response|ResponseInterface
     */
    public function quoteTemplateAction()
    {
        try {
            $params = $this->request->get();
            Validation::validate($params, ActivityStaffService::$kpi_staff_quote_template);
            $res = ActivityStaffService::getInstance()->quoteTemplate($params, $this->user);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }
}