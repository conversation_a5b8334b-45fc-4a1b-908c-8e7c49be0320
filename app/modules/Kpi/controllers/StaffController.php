<?php

namespace App\Modules\Kpi\Controllers;

use App\Modules\Kpi\Services\ActivityStaffService;
use App\Modules\Kpi\Services\BaseService;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Kpi\Services\PermissionService;
use App\Modules\Kpi\Services\StaffService;
use App\Library\ErrCode;
use app\modules\v1\business\Staff;

/**
 * 参与员工页面中的所有接口
 * 替换原有的ActivityStaffController.php文件中的部分接口
 */
class StaffController extends BaseController
{
    public function initialize()
    {
        parent::initialize();
        PermissionService::getInstance()->isPPM($this->user);
    }

    /**
     * KPI活动管理 - 获取参与活动的员工列表
     * @api https://yapi.flashexpress.pub/project/133/interface/api/65837
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Validation\ValidationException
     * @Permission(action='kpi.activity.staffList')
     * <AUTHOR>
     */
    public function getStaffListAction()
    {
        $params = trim_array($this->request->get());
        $params = BaseService::handleParams($params, StaffService::$not_must_params);
        try {
            Validation::validate($params, StaffService::$get_staff_list_validate);
            // 表示请求来自 KPI目标活动中 参与员工列表
            $params["source"] = "activity";
            $data             = ActivityStaffService::getInstance()->getActivityStaffList($params);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson($data['code'], $data['message'], $data['data']);
    }

    /**
     * KPI活动管理 - 获取 准备 添加参与kpi活动的用户列表
     * @api https://yapi.flashexpress.pub/project/133/interface/api/65842
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Validation\ValidationException
     * @Permission(action='kpi.activity.staffAdd')
     * <AUTHOR>
     */
    public function searchStaffListAction()
    {
        $params = trim_array($this->request->get());
        $params = BaseService::handleParams($params, StaffService::$not_must_params);
        try {
            Validation::validate($params, StaffService::$search_staff_list_validate);
            $data = StaffService::getInstance()->getStaffList($params);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson($data['code'], $data['message'], $data['data']);
    }

    /**
     * KPI活动管理 - 添加员工 - 多选
     * @api https://yapi.flashexpress.pub/project/133/interface/api/65847
     * @throws \App\Library\Validation\ValidationException
     * @Permission(action='kpi.activity.staffAdd')
     * <AUTHOR>
     */
    public function addActivityStaffAction()
    {
        $params = $this->request->get();

        $validate_params = [
            'activity_id' => 'Required|IntGt:0|>>>:param error[activity_id]',
            'staff_ids'   => 'Required|Arr|>>>:param error[staff_ids]',
        ];
        Validation::validate($params, $validate_params);

        $result = ActivityStaffService::getInstance()->addActivityStaff($params, $this->user['id'],
            $this->user['name']);

        return $this->returnJson($result['code'], $result['message'], $result['data']);
    }

    /**
     * KPI活动管理 - 添加员工 - 全选
     * @api https://yapi.flashexpress.pub/project/133/interface/api/65942
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Validation\ValidationException
     * @Permission(action='kpi.activity.staffAdd')
     */
    function addActivityAllStaffAction()
    {
        try {
            $params = $this->request->get();
            $params = BaseService::handleParams($params, StaffService::$not_must_params);
            unset(StaffService::$search_staff_list_validate['pageNum'], StaffService::$search_staff_list_validate['pageSize']);
            Validation::validate($params, StaffService::$search_staff_list_validate);
            $result = StaffService::getInstance()->addActivityAllStaff($params, $this->user);
        } catch (ValidationException $e) {
            $result['code']    = ErrCode::$VALIDATE_ERROR;
            $result['message'] = $e->getMessage();
            $result['data']    = [];
        } catch (\Exception $e) {
            $result['code']    = ErrCode::$SYSTEM_ERROR;
            $result['message'] = $e->getMessage();
            $result['data']    = [];
        }
        return $this->returnJson($result['code'], $result['message'], $result['data']);
    }

    /**
     * KPI活动管理 - 导入员工
     * @api https://yapi.flashexpress.pub/project/133/interface/api/65852
     * @throws \App\Library\Validation\ValidationException
     * @Permission(action='kpi.activity.staffImport')
     * <AUTHOR>
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @throws \App\Library\Exception\BusinessException
     */
    public function importActivityStaffAction()
    {
        $params          = $this->request->get();
        $validate_params = [
            'activity_id' => 'Required|IntGt:0|>>>:param error[activity_id]',
        ];
        Validation::validate($params, $validate_params);

        //获取传入xlsx
        $excel_file = $this->request->getUploadedFiles();
        $config     = ['path' => ''];
        $excel      = new \Vtiful\Kernel\Excel($config);

        // 读取文件
        $excel_data = $excel->openFile($excel_file[0]->getTempName())
            ->openSheet()
            ->setSkipRows(1)
            ->getSheetData();
        $lock_key   = md5('import_activity_staff'.$this->user['id']);
        $result     = $this->atomicLock(function () use ($params, $excel_data) {
            return ActivityStaffService::getInstance()->importActivityStaff($params, $excel_data, $this->user['id'],
                $this->user['name']);
        }, $lock_key, 20);
        return $this->returnJson($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 活动参数人员个数
     * @api https://yapi.flashexpress.pub/project/133/interface/api/65857
     * @throws \App\Library\Validation\ValidationException
     * @Permission(action='kpi.activity.staffList')
     * <AUTHOR>
     */
    public function getJoinActivityStaffNumsAction()
    {
        $params          = $this->request->get();
        $validate_params = [
            'activity_id' => 'Required|IntGt:0|>>>:param error[activity_id]',
        ];
        Validation::validate($params, $validate_params);

        $data = StaffService::getInstance()->getJoinActivityStaffNums($params);
        return $this->returnJson($data['code'], $data['msg'], $data['data']);
    }

    /**
     * 参与kpi员工 - 向上级发送通知、push等
     * @api https://yapi.flashexpress.pub/project/133/interface/api/65862
     * @Permission(action='kpi.activity.sendToLeader')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function batchSendMsgAction()
    {
        try {
            $params          = $this->request->get();
            $validate_params = [
                //活动ID
                'activity_id' => 'Required|IntGe:1|>>>:activity_id error',
            ];
            Validation::validate($params, $validate_params);
            $lock_key = md5('batch_send_msg'.$this->user['id']);
            $res      = $this->atomicLock(function () use ($params) {
                return StaffService::getInstance()->batchSendMsg($params, $this->user);
            }, $lock_key, 20);
        } catch (ValidationException $e) {
            $res['code']    = ErrCode::$VALIDATE_ERROR;
            $res['message'] = $e->getMessage();
            $res['data']    = [];
        } catch (\Exception $e) {
            $res['code']    = ErrCode::$SYSTEM_ERROR;
            $res['message'] = $e->getMessage();
            $res['data']    = [];
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 员工个人KPI详情页
     * @Permission(action='kpi.activity.staff.info')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/65062
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function infoAction()
    {
        try {
            $params = trim_array($this->request->get());
            Validation::validate($params, ActivityStaffService::$validate_kpi_staff_detail);
            $res = ActivityStaffService::getInstance()->getActivityStaffInfo($params, $this->user);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 参与kpi员工 - 变更制定人、批量变更制定人
     * @api https://yapi.flashexpress.pub/project/133/interface/api/65867
     * @Permission(action='kpi.activity.sendToLeader')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function batchModifyPlannerAction()
    {
        try {
            $params          = $this->request->get();
            $validate_params = [
                //活动ID
                'activity_id'  => 'Required|IntGe:1|>>>:activity_id error',
                //员工id
                'staff_ids'    => 'Required|Arr|>>>:param error[staff_ids]',
                'staff_ids[*]' => 'Required|IntGe:1|>>>:staff_id error',
                // 变更后的kpi制定人
                'leader_id'    => 'Required|IntGe:1|>>>:leader_id error',
            ];
            Validation::validate($params, $validate_params);
            $lock_key = md5('batch_modify_planner_'.$this->user['id']);
            $res      = $this->atomicLock(function () use ($params) {
                return StaffService::getInstance()->batchModifyPlanner($params, $this->user);
            }, $lock_key, 20);
        } catch (ValidationException $e) {
            $res['code']    = ErrCode::$VALIDATE_ERROR;
            $res['message'] = $e->getMessage();
            $res['data']    = [];
        } catch (\Exception $e) {
            $res['code']    = ErrCode::$SYSTEM_ERROR;
            $res['message'] = $e->getMessage();
            $res['data']    = [];
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 本期先不做全选变更kpi制定人
     * 参与kpi员工 - 变更制定人 - 选择全部时调用该接口
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @api
     * @Permission(action='kpi.activity.sendToLeader')
     */
    public function allModifyPlannerAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, StaffService::$not_must_params);
        try {
            // 选择全部时，前端只需要传递所有的query参数，翻页参数不需要。
            unset(StaffService::$get_staff_list_validate['pageNum'], StaffService::$get_staff_list_validate['pageSize']);
            StaffService::$get_staff_list_validate['leader_id'] = 'Required|IntGe:1|>>>:leader_id error';
            Validation::validate($params, StaffService::$get_staff_list_validate);
            // 表示请求来自 KPI目标活动中 参与员工列表
            $params["source"] = "activity";
            $res              = StaffService::getInstance()->allModifyPlanner($params, $this->user);
        } catch (ValidationException $e) {
            $res['code']    = ErrCode::$VALIDATE_ERROR;
            $res['message'] = $e->getMessage();
            $res['data']    = [];
        } catch (\Exception $e) {
            $res['code']    = ErrCode::$SYSTEM_ERROR;
            $res['message'] = $e->getMessage();
            $res['data']    = [];
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 导出员工KPI目标
     * @Permission(action='kpi.activity.staff.export')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/65067
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function exportAction()
    {
        try {
            $params = trim_array($this->request->get());
            Validation::validate($params, ActivityStaffService::$validate_kpi_staff_detail);
            // 加锁处理
            $lock_key = md5('kpi_staff_indicators_export_'.$params['activity_id'].'-'.$params['staff_id']);
            $res      = $this->atomicLock(function () use ($params) {
                return ActivityStaffService::getInstance()->exportStaffKpi($params, $this->user);
            }, $lock_key, 10);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson($res['code'] ?? ErrCode::$SYSTEM_ERROR, $res['message'] ?? $this->t->_('retry_later'),
            $res['data'] ?? []);
    }

    /**
     * 关键业绩KPI指标
     * @Permission(action='kpi.activity.staff.info')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/65072
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function indicatorsListAction()
    {
        try {
            $params = trim_array($this->request->get());
            Validation::validate($params, ActivityStaffService::$validate_kpi_staff_detail);
            $res = ActivityStaffService::getInstance()->getStaffIndicatorsList($params, $this->user);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 删除/批量删除参与员工
     * @Permission(action='kpi.activity.staffDel')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/40032
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function delActivityStaffAction()
    {
        try {
            $params = trim_array($this->request->get());
            Validation::validate($params, ActivityStaffService::$kpi_staff_batch_submit);

            // 加锁处理
            $lock_key = md5('kpi_activity_del_activity_staff_'.$this->user['id']);
            $res      = $this->atomicLock(function () use ($params) {
                return ActivityStaffService::getInstance()->delActivityStaff($params);
            }, $lock_key, 20);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson($res['code'] ?? ErrCode::$SYSTEM_ERROR, $res['message'] ?? $this->t->_('retry_later'),
            $res['data'] ?? []);
    }

    /**
     * 变更记录
     * @Permission(action='kpi.activity.changeLog')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/65087
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function changeLogAction()
    {
        try {
            $params = trim_array($this->request->get());
            Validation::validate($params, ActivityStaffService::$kpi_change_log);
            $res = ActivityStaffService::getInstance()->changeLog($params);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 员工管理-批量提醒员工确认
     * @Permission(action='kpi.activity.staff.batchConfirm')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/41173
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function batchConfirmSendMsgAction()
    {
        try {
            $params     = trim_array($this->request->get());
            $params     = BaseService::handleParams($params, ActivityStaffService::$not_must_kpi_activity_staff_list);
            $validation = ActivityStaffService::$kpi_staff_list_params;
            unset($validation['stage'], $validation['pageNum'], $validation['pageSize']);
            Validation::validate($params, $validation);

            // 加锁处理
            $lock_key = md5('kpi_activity_staff_batch_confirm_'.$this->user['id']);
            $res      = $this->atomicLock(function () use ($params) {
                return ActivityStaffService::getInstance()->batchSendConfirmMsg($params);
            }, $lock_key, 30);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson($res['code'] ?? ErrCode::$SYSTEM_ERROR, $res['message'] ?? $this->t->_('retry_later'),
            $res['data'] ?? []);
    }

    /**
     * 员工KPI目标 【全部】、【待制定目标】、【待确认目标】、【确认完毕，目标执行】各个阶段汇总数据接口
     * @api https://yapi.flashexpress.pub/project/133/interface/api/65902
     * @Permission(action='kpi.activity.staff.byManage')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function getStaffStageNumsAction()
    {
        $params = trim_array($this->request->get());
        $params = BaseService::handleParams($params, StaffService::$not_must_params);
        try {
            unset(ActivityStaffService::$kpi_staff_list_params['pageNum'], ActivityStaffService::$kpi_staff_list_params['pageSize']);
            Validation::validate($params, ActivityStaffService::$kpi_staff_list_params);
            $res = StaffService::getInstance()->getStaffStageNums($params);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 员工KPI目标-参与员工-所有参与KPI活动员工列表、分页、筛选
     * @api  https://yapi.flashexpress.pub/project/133/interface/api/65907
     * @Permission(action='kpi.activity.staff.byManage')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function getStaffKpiListAction()
    {
        $params = trim_array($this->request->get());
        $params = BaseService::handleParams($params, StaffService::$not_must_params);
        try {
            Validation::validate($params, ActivityStaffService::$kpi_staff_list_params);
            // 表示请求来自 员工kpi目标 参与员工列表
            $params["source"] = "staff";
            $data             = ActivityStaffService::getInstance()->getActivityStaffList($params);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson($data['code'], $data['message'], $data['data']);
    }

    /**
     * 员工KPI目标-参与员工-待制定目标环节 一键催办
     * @api https://yapi.flashexpress.pub/project/133/interface/api/65937
     * @Permission(action='kpi.activity.staff.batchSubmit')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function batchStageLeaderSendMsgAction()
    {
        try {
            $params = $this->request->get();
            $params = BaseService::handleParams($params, StaffService::$not_must_params);
            unset(ActivityStaffService::$kpi_staff_list_params['pageNum'], ActivityStaffService::$kpi_staff_list_params['pageSize']);
            Validation::validate($params, ActivityStaffService::$kpi_staff_list_params);
            $lock_key = md5('batch_stage_leader_send_msg'.$this->user['id']);
            $res      = $this->atomicLock(function () use ($params) {
                return ActivityStaffService::getInstance()->batchStageLeaderSendMsg($params);
            }, $lock_key, 20);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }
}