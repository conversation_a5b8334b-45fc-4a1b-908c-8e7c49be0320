<?php
namespace App\Modules\Kpi\Controllers;

use App\Library\ErrCode;
use App\Modules\Kpi\Services\ActivityService;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Kpi\Services\BaseService;
use App\Modules\Kpi\Services\PermissionService;

/**
 * KPI活动管理控制器
 * Class ActivityController
 * @package App\Modules\Kpi\Controllers
 */
class ActivityController extends BaseController
{
    public function initialize()
    {
        parent::initialize();
        PermissionService::getInstance()->isPPM($this->user);
    }

    /**
     * KPI活动管理-新建活动
     * @Permission(action='kpi.activity.add')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/39633
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function addAction()
    {
        try {
            $params = trim_array($this->request->get());
            $excel_file = $this->request->getUploadedFiles();
            Validation::validate($params, ActivityService::$kpi_activity_create);
            $res = ActivityService::getInstance()->addActivity($params, $excel_file, $this->user);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * KPI活动管理-列表
     * @Permission(action='kpi.activity.list')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/39703
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function listAction()
    {
        $params = trim_array($this->request->get());
        $params = BaseService::handleParams($params, ActivityService::$not_must_params);
        try {
            Validation::validate($params, ActivityService::$validate_list_search);
            $res = ActivityService::getInstance()->getList($params);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
    }

    /**
     * KPI活动管理-删除
     * @Permission(action='kpi.activity.del')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/39731
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function delAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, ActivityService::$validate_kpi_activity_id);
            $res = ActivityService::getInstance()->delActivity($params['id']);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * KPI活动管理-启用或完成
     * @Permission(action='kpi.activity.status')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/39745
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function statusAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, ActivityService::$validate_kpi_activity_status);
            $res = ActivityService::getInstance()->updateActivityStatus($params['id'], $params['status']);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * KPI活动管理-导入价值观
     * @Permission(action='kpi.activity.values')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/39801
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function valuesAction()
    {
        try {
            $params = $this->request->get();
            $excel_file = $this->request->getUploadedFiles();
            Validation::validate($params, ActivityService::$validate_kpi_activity_id);
            $res = ActivityService::getInstance()->importActivityValues($params['id'], $excel_file);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * KPI活动管理-编辑活动基本信息
     * @Permission(action='kpi.activity.edit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/39871
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function editAction()
    {
        try {
            $params = trim_array($this->request->get());
            Validation::validate($params, ActivityService::$validate_kpi_edit);
            $res = ActivityService::getInstance()->editActivity($params);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * KPI活动管理-时间节点设置
     * @Permission(action='kpi.activity.set')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/64557
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function setTimeNodeAction()
    {
        $params = trim_array($this->request->get());
        try {
            Validation::validate($params, ActivityService::$validate_kpi_activity_set_time_node);
            $res = ActivityService::getInstance()->setTimeNode($params);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * KPI活动管理-自动提醒设置
     * @Permission(action='kpi.activity.notice')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/64562
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function setAutoRemindAction()
    {
        $params = trim_array($this->request->get());
        try {
            Validation::validate($params, ActivityService::$validate_kpi_activity_set_auto_remind);
            $res = ActivityService::getInstance()->setAutoRemind($params);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * KPI活动管理-OKR名单-列表
     * @Permission(action='kpi.activity.okrList')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/64587
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function okrListAction()
    {
        $params = trim_array($this->request->get());
        $params = BaseService::handleParams($params, ActivityService::$not_must_params);
        try {
            Validation::validate($params, ActivityService::$validate_okr_list_search);
            $res = ActivityService::getInstance()->okrList($params);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
    }

    /**
     * KPI活动管理-OKR名单-批量导入
     * @Permission(action='kpi.activity.okrImport')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/64617
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function okrImportAction()
    {
        try {
            $params = $this->request->get();
            $excel_file = $this->request->getUploadedFiles();
            Validation::validate($params, ActivityService::$validate_kpi_activity_id);

            // 加锁处理
            $lock_key = md5('kpi_activity_okr_import_' . $this->user['id']);
            $res = $this->atomicLock(function () use ($params, $excel_file) {
                return ActivityService::getInstance()->okrImport($params['id'], $excel_file, $this->user);
            }, $lock_key, 10);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson($res['code'] ?? ErrCode::$SYSTEM_ERROR, $res['message'] ?? $this->t->_('retry_later'), $res['data'] ?? []);
    }

    /**
     * KPI活动管理-OKR名单-批量删除
     * @Permission(action='kpi.activity.okrDel')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/64642
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function okrBatchDelAction()
    {
        $params = trim_array($this->request->get());
        try {
            Validation::validate($params, ActivityService::$validate_okr_batch_del);
            // 加锁处理
            $lock_key = md5('kpi_activity_okr_batch_del_' . $this->user['id']);
            $res = $this->atomicLock(function () use ($params) {
                return ActivityService::getInstance()->okrBatchDel($params);
            }, $lock_key, 10);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson($res['code'] ?? ErrCode::$SYSTEM_ERROR, $res['message'] ?? $this->t->_('retry_later'), $res['data'] ?? []);
    }

    /**
     * KPI活动管理-OKR名单-删除
     * @Permission(action='kpi.activity.okrDel')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/64647
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function okrDelAction()
    {
        $params = trim_array($this->request->get());
        try {
            Validation::validate($params, ActivityService::$validate_okr_del);
            $res = ActivityService::getInstance()->okrDel($params);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * KPI活动管理-OKR名单-导出
     * @Permission(action='kpi.activity.okrExport')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/64637
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function okrExportAction()
    {
        try {
            $params = trim_array($this->request->get());
            $params = BaseService::handleParams($params, ActivityService::$not_must_params);
            $validation = ActivityService::$validate_okr_list_search;
            unset($validation['pageNum'], $validation['pageSize']);
            Validation::validate($params, $validation);
            // 加锁处理
            $lock_key = md5('kpi_activity_okr_export_' . $this->user['id']);
            $res = $this->atomicLock(function () use ($params) {
                return ActivityService::getInstance()->okrExport($params);
            }, $lock_key, 10);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson($res['code'] ?? ErrCode::$SYSTEM_ERROR, $res['message'] ?? $this->t->_('retry_later'), $res['data'] ?? []);
    }
}