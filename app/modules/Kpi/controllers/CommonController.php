<?php
namespace App\Modules\Kpi\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Kpi\Services\ActivityService;
use App\Modules\Kpi\Services\ActivityStaffService;
use App\Modules\Kpi\Services\PermissionService;
use App\Modules\Kpi\Services\TemplateService;
use App\Modules\Kpi\Services\ValuesService;

/**
 * KPI公用控制器
 * Class CommonController
 * @package App\Modules\Kpi\Controllers
 */
class CommonController extends BaseController
{
    /**
     * KPI活动管理-初始化枚举配置
     * @api https://yapi.flashexpress.pub/project/133/interface/api/39451
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function getDefaultAction()
    {
        $res = ActivityService::getInstance()->getDefaultData();
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * KPI活动管理-活动基本信息
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/39836
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function infoAction()
    {
        try {
            $params = $this->request->get();
            Validation::validate($params, ActivityService::$validate_kpi_activity_id);
            $res =  ActivityService::getInstance()->getActivityInfo($params['id']);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 获取所有进行中和已结束的活动
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/40396
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function getListAllAction()
    {
        $data = ActivityService::getInstance()->getListAll();
        return $this->returnJson($data['code'], $data['message'], $data['data']);
    }

    /**
     * KPI活动管理-价值观列表
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/39829
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function valuesListAction()
    {
        try {
            $params = $this->request->get();
            Validation::validate($params, ActivityService::$validate_kpi_activity_id);
            $values_service = new ValuesService();
            $res = $values_service->getActivityValuesList($params['id']);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * KPI活动管理-员工枚举接口
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/40081
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function getEnumAction()
    {
        $data = ActivityStaffService::getInstance()->getEnum();
        return $this->returnJson(ErrCode::$SUCCESS, '', $data);
    }

    /**
     * KPI模版管理-初始化枚举配置
     * @api https://yapi.flashexpress.pub/project/133/interface/api/40109
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function getTemplateDefaultAction()
    {
        $res = TemplateService::getInstance()->getDefaultData();
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 获取用户团队KPI目标权限
     * @api https://yapi.flashexpress.pub/project/133/interface/api/65657
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function getTeamPermissionAction()
    {
        $res = PermissionService::getInstance()->getTeamPermission($this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }
}