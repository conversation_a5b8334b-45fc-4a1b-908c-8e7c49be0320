<?php
namespace App\Modules\Kpi\Controllers;
use App\Library\ErrCode;
use App\Modules\Kpi\Services\IndicatorsService;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Kpi\Services\PermissionService;

/**
 * KPI模版指标信息管理控制器
 * Class IndicatorsController
 * @package App\Modules\Kpi\Controllers
 */
class IndicatorsController extends BaseController
{
    public function initialize()
    {
        parent::initialize();
        PermissionService::getInstance()->isLeader($this->user);
    }

    /**
     * KPI指标管理-新建指标
     * @Permission(action='kpi.indicators.add')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/40193
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function addAction()
    {
        try {
            $params = trim_array($this->request->get());
            Validation::validate($params, IndicatorsService::$kpi_indicators_create);
            $res = IndicatorsService::getInstance()->addIndicators($params, $this->user);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * KPI指标管理-列表
     * @Permission(action='kpi.indicators.list')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/40207
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function listAction()
    {
        $params = trim_array($this->request->get());
        try {
            Validation::validate($params, IndicatorsService::$validate_kpi_template_id);
            $res = IndicatorsService::getInstance()->getList($params['template_id'], $this->user);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * KPI指标管理-编辑指标
     * @Permission(action='kpi.indicators.edit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/40228
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function editAction()
    {
        try {
            $params = trim_array($this->request->get());
            Validation::validate($params, array_merge(IndicatorsService::$validate_kpi_indicators_id, IndicatorsService::$kpi_indicators_create));
            $res = IndicatorsService::getInstance()->editIndicators($params, $this->user);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * KPI指标管理-删除指标
     * @Permission(action='kpi.indicators.del')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/40235
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function delAction()
    {
        try {
            $params = trim_array($this->request->get());
            Validation::validate($params, IndicatorsService::$validate_kpi_indicators_id);
            $res = IndicatorsService::getInstance()->delIndicators($params['id'], $this->user);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * KPI指标管理-导入指标
     * @Permission(action='kpi.indicators.import')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/40333
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function importAction()
    {
        try {
            $params = trim_array($this->request->get());
            $excel_file = $this->request->getUploadedFiles();
            Validation::validate($params, IndicatorsService::$validate_kpi_template_id);

            // 加锁处理
            $lock_key = md5('kpi_activity_indicators_import_' . $this->user['id']);
            $res = $this->atomicLock(function () use ($params, $excel_file) {
                return IndicatorsService::getInstance()->importIndicators($params['template_id'], $excel_file, $this->user);
            }, $lock_key, 10);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson($res['code'] ?? ErrCode::$SYSTEM_ERROR, $res['message'] ?? $this->t->_('retry_later'), $res['data'] ?? []);
    }
    /**
     * KPI指标管理-导出指标
     * @Permission(action='kpi.indicators.export')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/40368
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function exportAction()
    {
        try {
            $params = trim_array($this->request->get());
            Validation::validate($params, IndicatorsService::$validate_kpi_template_id);
            // 加锁处理
            $lock_key = md5('kpi_template_indicators_export_' . $this->user['id']);
            $res = $this->atomicLock(function () use ($params) {
                return IndicatorsService::getInstance()->exportIndicators($params['template_id'], $this->user);
            }, $lock_key, 10);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson($res['code'] ?? ErrCode::$SYSTEM_ERROR, $res['message'] ?? $this->t->_('retry_later'), $res['data'] ?? []);
    }
}