<?php

namespace App\Modules\Kpi\Controllers;

use App\Library\Enums\KpiActivityStaffEnums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Kpi\Services\ActivityStaffService;
use App\Modules\Kpi\Services\BaseService;
use App\Modules\Kpi\Services\PermissionService;
use App\Modules\Kpi\Services\StaffService;
use App\Modules\Kpi\Services\TeamService;

/**
 * 团队KPI-我的管辖
 * Class TeamManageController
 * @package App\Modules\Kpi\Controllers
 */
class TeamManageController extends BaseController
{
    public function initialize()
    {
        parent::initialize();
        PermissionService::getInstance()->isHrbp($this->user);
    }

    /**
     * 员工个人KPI详情页
     * @Permission(action='kpi.team.manage.staff.info')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/65687
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function infoAction()
    {
        try {
            $params = trim_array($this->request->get());
            Validation::validate($params, ActivityStaffService::$validate_kpi_staff_detail);
            $res = ActivityStaffService::getInstance()->getActivityStaffInfo($params, $this->user,
                KpiActivityStaffEnums::PERMISSION_HRBP);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 导出员工KPI目标
     * @Permission(action='kpi.team.manage.staff.export')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/65697
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function exportAction()
    {
        try {
            $params = trim_array($this->request->get());
            Validation::validate($params, ActivityStaffService::$validate_kpi_staff_detail);
            // 加锁处理
            $lock_key = md5('kpi_staff_indicators_export_'.$params['activity_id'].'-'.$params['staff_id']);
            $res      = $this->atomicLock(function () use ($params) {
                return ActivityStaffService::getInstance()->exportStaffKpi($params, $this->user,
                    KpiActivityStaffEnums::PERMISSION_HRBP);
            }, $lock_key, 10);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson($res['code'] ?? ErrCode::$SYSTEM_ERROR, $res['message'] ?? $this->t->_('retry_later'),
            $res['data'] ?? []);
    }

    /**
     * 关键业绩KPI指标
     * @Permission(action='kpi.team.manage.staff.info')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/65692
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function indicatorsListAction()
    {
        try {
            $params = trim_array($this->request->get());
            Validation::validate($params, ActivityStaffService::$validate_kpi_staff_detail);
            $res = ActivityStaffService::getInstance()->getStaffIndicatorsList($params, $this->user,
                KpiActivityStaffEnums::PERMISSION_HRBP);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 团队KPI-我的管辖-汇总接口
     * @Permission(action='kpi.team.manage.staff.list')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/66847
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function getManageStageNumsAction()
    {
        $params = trim_array($this->request->get());
        try {
            Validation::validate($params, TeamService::$get_department_stage_nums);
            $res = TeamService::getInstance()->getStageNums($params, $this->user,
                KpiActivityStaffEnums::PERMISSION_HRBP);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 团队KPI-我的管辖-列表页面
     * @Permission(action='kpi.team.manage.staff.list')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/66857
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function listAction()
    {
        $params = trim_array($this->request->get());
        $params = BaseService::handleParams($params, StaffService::$not_must_params);
        try {
            Validation::validate($params, TeamService::$get_team_list_validate);
            $res = TeamService::getInstance()->getTeamList($params, $this->user,
                KpiActivityStaffEnums::PERMISSION_HRBP);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }
}