<?php
namespace App\Modules\Kpi\Controllers;
use App\Library\ErrCode;
use App\Modules\Kpi\Services\BaseService;
use App\Modules\Kpi\Services\PermissionService;
use App\Modules\Kpi\Services\TemplateService;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;

/**
 * KPI模版管理控制器
 * Class TemplateController
 * @package App\Modules\Kpi\Controllers
 */
class TemplateController extends BaseController
{
    public function initialize()
    {
        parent::initialize();
        PermissionService::getInstance()->isLeader($this->user);
    }

    /**
     * KPI模版管理-新建模版
     * @Permission(action='kpi.template.add')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/40116
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function addAction()
    {
        try {
            $params = trim_array($this->request->get());
            //增加参数过滤
            $params = filter_param($params);
            Validation::validate($params, TemplateService::$kpi_template_create);
            $res = TemplateService::getInstance()->addTemplate($params, $this->user);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * KPI模版管理-列表
     * @Permission(action='kpi.template.list')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/39143
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function listAction()
    {
        $params = trim_array($this->request->get());
        $params = BaseService::handleParams($params, TemplateService::$not_must_params);
        try {
            Validation::validate($params, TemplateService::$validate_list_search);
            $res = TemplateService::getInstance()->getList($params, $this->user['id']);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
    }

    /**
     * KPI模版管理-复制模版
     * @Permission(action='kpi.template.copy')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/40144
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function copyAction()
    {
        try {
            $params = trim_array($this->request->get());
            Validation::validate($params, TemplateService::$validate_kpi_template_id);
            $res = TemplateService::getInstance()->copyTemplate($params['id'], $this->user);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * KPI模版管理-编辑模版
     * @Permission(action='kpi.template.edit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/40123
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function editAction()
    {
        try {
            $params = trim_array($this->request->get());
            Validation::validate($params, TemplateService::$kpi_template_edit);
            $res = TemplateService::getInstance()->editTemplate($params, $this->user);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * KPI模版管理-删除模版
     * @Permission(action='kpi.template.del')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/40130
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function delAction()
    {
        try {
            $params = trim_array($this->request->get());
            Validation::validate($params, TemplateService::$validate_kpi_template_id);
            $res = TemplateService::getInstance()->delTemplate($params['id'], $this->user);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * KPI模版管理-模版信息
     * @Permission(action='kpi.template.info')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/40165
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function infoAction()
    {
        try {
            $params = trim_array($this->request->get());
            Validation::validate($params, TemplateService::$validate_kpi_template_id);
            $res = TemplateService::getInstance()->infoTemplate($params['id'], $this->user);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * KPI模版管理-启用模版
     * @Permission(action='kpi.template.status')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/40291
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function statusAction()
    {
        $params = trim_array($this->request->get());
        try {
            Validation::validate($params, TemplateService::$validate_kpi_template_id);
            $res = TemplateService::getInstance()->updateTemplateStatus($params['id'], $this->user);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 获取自己创建且启用的模版列表
     * @Permission(action='kpi.template.list')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/40606
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function getEnableListAction()
    {
        $res = TemplateService::getInstance()->getEnableList($this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }
}