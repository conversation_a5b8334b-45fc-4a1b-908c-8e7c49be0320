<?php
namespace App\Modules\Kpi\Services;
/**
 * KPI管理基础服务类
 * Class BaseService
 * @package App\Modules\Kpi\Services
 */
class BaseService extends \App\Library\BaseService
{
    public static function handleParams($params, $not_must)
    {
        if (empty($not_must) || !is_array($not_must)) {
            return $params;
        }
        foreach ($not_must as $value) {
            if (isset($params[$value]) && empty($params[$value])) {
                unset($params[$value]);
            }
        }
        return $params;
    }

    /**
     * @param $type
     * @param bool $is_update
     * @return array
     */
    public static function getValidateParams($type, $is_update = false)
    {
        $rules = [];
        $type = $type ? : 0;
        if (isset(self::$validate_other[$type])) {
            $rules = self::$validate_other[$type];
        }
        if ($is_update === true) {
            $rules = array_merge($rules, self::$validate_update);
        }

        return array_merge(self::$validate_currency, $rules);
    }

    /**
     * 如果网点名字为空，则赋值Header Office
     * @param $storeName
     * @return string
     */
    public function getStoreName($storeName)
    {
        if(empty($storeName)) {
            return "Header Office";
        }
        return $storeName;
    }

    /**
     * 生成自定义的Excel文件
     * @param array $head Excel 列名
     * @param array $data Excel 业务数据
     * @param array $extra_data 额外数据,
     * 示例:
     * file_desc: Excel 首行说明文案内容
     * end_column_char: 最后列字母
     * column_width: 列宽
     * header_column_font_color: 表头字体颜色
     * header_columns: 表头包含列
     * header_column_row: 表头所在行
     *
     * @return string
     */
    protected function customizeExcelToFile(array $head, array $data = [], array $extra_data = [])
    {
        $config = [
            'path' => sys_get_temp_dir() . '/'
        ];
        $excel = new \Vtiful\Kernel\Excel($config);

        // Excel；临时文件名
        $fileName = $extra_data['file_name'] ?? time() . '_excel.xlsx';

        // 此处会自动创建一个工作表
        $fileObject = $excel->fileName($fileName);
        $fileHandle = $fileObject->getHandle();

        // 设置Excel首行文件说明文案
        $end_column_char = $extra_data['end_column_char'] ?? 'Z';
        if (!empty($extra_data['file_desc'])) {
            $format    = new \Vtiful\Kernel\Format($fileHandle);
            $wrapStyle = $format->wrap()->align(\Vtiful\Kernel\Format::FORMAT_ALIGN_VERTICAL_CENTER)->toResource();
            $fileObject->mergeCells("A1:{$end_column_char}1", $extra_data['file_desc'])->setRow("A1:{$end_column_char}1", 200, $wrapStyle);
        }

        // 设置列宽
        if (!empty($extra_data['column_width'])) {
            $fileObject->header($head)->data($data)->setColumn("A:{$end_column_char}", $extra_data['column_width']);
        }

        // 设置表头字体颜色
        if (!empty($extra_data['header_column_font_color'])) {
            $format    = new \Vtiful\Kernel\Format($fileHandle);
            $colorStyle = $format->fontColor($extra_data['header_column_font_color'])->toResource();
            foreach ($extra_data['header_columns'] as $column) {
                $fileObject->setRow("{$column}{$extra_data['header_column_row']}", 15, $colorStyle);
            }
        }

        // 最后的最后，输出文件
        $filePath = $fileObject->output();

        //判断文件是否生成成功了
        if (!is_file($filePath)) {
            return '';
        }

        return $filePath;
    }

    /**
     * 针对员工kpi目标特别定制生成自定义的Excel文件,
     * @param array $head Excel 列名
     * @param array $data Excel 业务数据
     * @param array $extra_data 额外数据,
     * 示例:
     * row_height：行宽
     * end_column_char: 最后列字母
     * column_width: 列宽
     * header_column_font_color: 表头字体颜色
     * header_columns: 表头包含列
     * header_column_row: 表头所在行
     *
     * @return string
     */
    protected function kpiExcelToFile(array $head, array $data = [], array $extra_data = [])
    {
        $config = [
            'path' => sys_get_temp_dir() . '/'
        ];
        $excel = new \Vtiful\Kernel\Excel($config);

        // Excel；临时文件名
        $fileName = $extra_data['file_name'] ?? time() . '_excel.xlsx';

        // 此处会自动创建一个工作表
        $fileObject = $excel->fileName($fileName);
        $fileHandle = $fileObject->getHandle();
        $info_num = 1;//基本信息行号
        $indicators_num = 4;//关键业绩KPI行号

        //获取xls的扩展版本
        $xls_version = phpversion('xlswriter');
        if (version_compare($xls_version, '1.3.6') >= 0) {
            //开发、测试、k8s的版本都是 1.3.4.1，tra和pro的高于这个版本会有兼容性问题,故而做这个判断
            $values_num = $indicators_num+count($data['indicators']);//价值观行号
            $confirm_num = $values_num+count($data['values']);//员工确认行号
        } else {
            $values_num = $indicators_num+count($data['indicators'])+1;//价值观行号
            $confirm_num = $values_num+count($data['values'])+1;//员工确认行号
        }
        // 合并单元格结束位置
        $end_column_char = $extra_data['end_column_char'] ?? 'Z';
        foreach ($head as $key=>$h) {
            // 设置列宽
            if (!empty($extra_data['column_width'])) {
                $fileObject->header([$h])->data($data[$key])->setColumn("A:{$end_column_char}", $extra_data['column_width']);
            }
        }
        $row_height = $extra_data['row_height'] ?? 200;
        //给指定的行号设置合并单元格样式
        $format    = new \Vtiful\Kernel\Format($fileHandle);
        $wrapStyle = $format->wrap()->align(\Vtiful\Kernel\Format::FORMAT_ALIGN_VERTICAL_CENTER)->background(0xDFEAFF)->font('Tahoma')->fontSize(12)->bold()->fontColor($extra_data['header_column_font_color'])->toResource();
        $merge_cells_rows = ['info'=>$info_num, 'indicators'=>$indicators_num, 'values'=>$values_num, 'confirm'=>$confirm_num];
        foreach ($merge_cells_rows as $key=>$num) {
            $fileObject->mergeCells("A{$num}:{$end_column_char}{$num}", $head[$key])->setRow("A{$num}", $row_height, $wrapStyle);
        }
        //给基本信息的指定行、关键业绩KPI的表头、价值观表头赋予行样式
        $title_rows = [2, 3, $indicators_num+1, $values_num+1];
        $format    = new \Vtiful\Kernel\Format($fileHandle);
        $fontStyle = $format->font('方正书宋_GBK')->fontSize(10)->bold()->fontColor($extra_data['header_column_font_color'])->toResource();
        foreach ($title_rows as $key=>$row) {
            $fileObject->setRow("A{$row}", 20, $fontStyle);
        }
        // 最后的最后，输出文件
        $filePath = $fileObject->output();

        //判断文件是否生成成功了
        if (!is_file($filePath)) {
            return '';
        }
        return $filePath;
    }

    /**
     * 暂时废弃，目前扩展不支持此方法
     * 给关键业绩KPI的表头、价值观表头的文本内容设置多样式文本格式
     * $fileObject = $this->setRichText($fileObject, $fileHandle, $data[$key][0], $row);
     * @param $fileObject
     * @param $fileHandle
     * @param $data
     * @param $row
     * @return mixed
     */
    private function setRichText($fileObject, $fileHandle, $data, $row)
    {
        // 创建黑色字符样式
        $format1  = new \Vtiful\Kernel\Format($fileHandle);
        $colorBlack = $format1->fontColor(\Vtiful\Kernel\Format::COLOR_BLACK)->toResource();

        // 创建橙色字符样式
        $format2     = new \Vtiful\Kernel\Format($fileHandle);
        $colorOrange = $format2->fontColor(0xEB7E33)->toResource();

        foreach ($data as $key=>$value) {
            $start = mb_strpos($value,'(');
            $end = mb_strpos($value,')')+1;
            $part1 = mb_substr($value, 0, $start);
            $part2 = mb_substr($value, $start,(mb_strlen($value) -$start-mb_strlen(mb_substr($value, $end))));
            $part3 = mb_substr($value, $end);

            // 创建黑色文字
            $richStringOne = new \Vtiful\Kernel\RichString($part1, $colorBlack);
            // 创建橙色文字
            $richStringTwo = new \Vtiful\Kernel\RichString($part2, $colorOrange);
            // 创建黑色文字
            $richStringThree = new \Vtiful\Kernel\RichString($part3, $colorBlack);
            // 将 红色文字 与 橙色文字 写入单元格
            $fileObject->insertRichText($row, $key, [
                $richStringOne,
                $richStringTwo,
                $richStringThree
            ]);
        }

//        exit();
        return $fileObject;
    }
}
