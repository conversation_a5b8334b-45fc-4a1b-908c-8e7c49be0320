<?php

namespace App\Modules\Kpi\Services;

use App\Models\backyard\KpiLeaderMsgSendLogModel;

class KpiLeaderMsgSendLogService extends BaseService
{
    private static $instance;

    /**
     * 构造函数
     * KpiLeaderMsgSendLogService constructor.
     */
    public function __construct()
    {
    }

    /**
     * 类实例
     * @return KpiLeaderMsgSendLogService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * @param array $params
     * @param array $colum
     * @return array
     * <AUTHOR>
     */
    public function getKpiLeaderMsgSendLogServiceByParams(array $params, array $colum): array
    {
        if (empty($params)) {
            return [];
        }
        $fields  = empty($colum) ? "*" : implode(',', $colum);
        $builder = $this->modelsManager->createBuilder();
        $builder->columns($fields);
        $builder->from(['klm' => KpiLeaderMsgSendLogModel::class]);
        foreach ($params as $field => $val) {
            if (!is_array($val)) {
                $builder->andWhere('klm.'.$field." = :{$field}:", [$field => $val]);
            }
            if (is_array($val)) {
                $builder->andWhere('klm.'.$field." IN ({{$field}:array})", [$field => $val]);
            }
        }
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * @param $activity_id
     * @param $leader_ids
     * @param $staff_ids
     * @return array
     */
    public function getKpiLeaderMsgSendLogByLeaderIds (int $activity_id,array $leader_ids,array $staff_ids = []): array
    {
        $list = [];
        if (0 >= $activity_id || empty($leader_ids)) {
            return $list;
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->columns(
            'klm.id, klm.staff_id, klm.leader_id, klm.leader_name, klm.created_at'
        );
        $builder->from(['klm' => KpiLeaderMsgSendLogModel::class]);
        $builder->andWhere('klm.activity_id = :activity_id:',['activity_id' => $activity_id]);
        $builder->andWhere('klm.leader_id IN ({leader_ids:array})',['leader_ids' => $leader_ids]);

        // 拼接staff_ids的原因：oa变更过kpi制定人是 由于离职 恢复为默认的上级时 此时log记录表中的数据为删除，会出现取错日志的情况
        if (!empty($staff_ids)) {
            $builder->andWhere('klm.staff_id IN ({staff_ids:array})',['staff_ids' => $staff_ids]);
        }

        $builder->orderBy('klm.created_at ASC');
        $result = $builder->getQuery()->execute()->toArray();
        if (empty($result)) {
            return  $list;
        }

        foreach ($result AS $key => $val) {
            if (isset($list[$val['leader_id']]) && !empty($list[$val['leader_id']])) {
                continue;
            }
            $list[$val['leader_id']] =  $val;
        }
        return $list;
    }
}