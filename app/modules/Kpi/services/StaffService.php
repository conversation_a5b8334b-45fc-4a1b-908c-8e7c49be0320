<?php

namespace App\Modules\Kpi\Services;

use App\Library\Enums\KpiActivityStaffEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Models\backyard\KpiLeaderMsgSendLogModel;
use App\Models\backyard\KpiStaffChangeLeaderLogModel;
use App\Models\backyard\SysDepartmentModel as DepartmentModel;
use App\Models\backyard\HrStaffItemsModel;
use App\Models\backyard\SysStoreModel;
use App\Modules\Organization\Services\DepartmentService;
use App\Models\backyard\HrProbationModel;
use App\Models\backyard\KpiActivityOkrModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\HrJobTitleModel;
use App\Models\backyard\KpiActivityStaffModel;
use App\Library\Enums\KpiActivityEnums;
use App\Library\Exception\BusinessException;
use App\Modules\User\Models\StaffPermissionModel;


/**
 * KPI活动管理服务类
 * Class StaffService
 * @package App\Modules\Kpi\Services
 */
class StaffService extends BaseService
{
    private static $instance;

    /**
     * 构造函数
     * StaffService constructor.
     */
    private function __construct()
    {
    }

    /**
     * 类实例
     * @return StaffService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    public static $not_must_params = [
        'name',
        'node_department_id',
        'job_id',
        'sys_store_id',
        'working_country',
        'leader_id',
        'staff_state',
        'hire_date',
        'is_change_leader',
        'job_title_grade',
    ];

    /**
     * KPI活动管理 - 获取 准备 添加参与kpi活动的用户列表
     */
    public static $search_staff_list_validate = [
        // 工号/姓名 支持模糊匹配
        'name'               => 'StrLenGeLe:0,50|>>>:param error[name]',
        // 部门筛选
        'node_department_id' => 'IntGeLe:0,1000000000|>>>:param error[node_department_id]',
        // 职位搜索
        'job_id'             => 'IntGeLe:0,1000000000|>>>:param error[job_id]',
        // 所属网点
        'sys_store_id'       => 'StrLenGeLe:0,10|>>>:param error[sys_store_id]',
        // 工作所在国家，支持多选
        'working_country'    => 'Arr|>>>:param error[working_country]',
        // 直线上级 支持工号/姓名 模糊匹配
        'leader'             => 'StrLenGeLe:0,50|>>>:param error[leader]',
        // 在职状态，支持选择多个
        'staff_state'        => 'Arr|>>>:param error[staff_state]',
        // 员工入职日期
        'hire_date'          => 'ArrLen:2|>>>:param error[hire_date]',
        // 雇佣类型 1 -> 正式员工 4 -> 实习生
        'hire_type'          => 'Arr|ArrLenLe:6|>>>:param error[formal]',
        // 试用期状态 1试用期，2已通过，3未通过，4已转正
        'status'             => 'Arr|ArrLenLe:4|>>>:param error[status]',
        // 翻页参数
        'pageNum'            => 'Required|IntGe:1|>>>:param error[pageNum]',
        // 翻页参数
        'pageSize'           => 'Required|IntGt:0|>>>:param error[pageSize]',
        // 活动id
        'activity_id'        => 'Required|IntGt:0|>>>:param error[activity_id]',
    ];

    /**
     * KPI活动管理 - 获取参与活动的员工列表
     */
    public static $get_staff_list_validate = [
        // 支持按照 姓名/工号/昵称 模糊匹配
        'name'               => 'StrLenGeLe:0,50|>>>:param error[name]',
        // 部门id搜索
        'node_department_id' => 'IntGeLe:0,1000000000|>>>:param error[node_department_id]',
        // 职位搜索
        'job_id'             => 'IntGeLe:0,1000000000|>>>:param error[job_id]',
        // 所属网点搜索
        'sys_store_id'       => 'StrLenGeLe:0,10|>>>:param error[sys_store_id]',
        // 工作所在国家搜素
        'working_country'    => 'Arr|>>>:param error[working_country]',
        // 直线上级 姓名/工号、 模糊搜搜
        'leader'             => 'StrLenGeLe:0,50|>>>:param error[leader]',
        // 在职状态 支持多选
        'staff_state'        => 'Arr|>>>:param error[staff_state]',
        // 入职日期
        'hire_date'          => 'ArrLen:2|>>>:param error[hire_date]',
        // 是否变更过KPI指定人:1否，2是
        'is_change_leader'   => 'IntIn:1,2|>>>:param error[is_change_leader]',
        // 翻页参数
        'pageNum'            => 'Required|IntGe:1|>>>:param error[pageNum]',
        // 翻页参数
        'pageSize'           => 'Required|IntGt:0|>>>:param error[pageSize]',
        // 活动id
        'activity_id'        => 'Required|IntGt:0|>>>:param error[activity_id]',
    ];

    /**
     * 获取 准备 添加参与kpi活动的用户列表 条件拼接
     * @param object $builder
     * @param array $params
     * @return object
     */
    public function getStaffListCondition(object $builder, array $params): object
    {
        if (!empty($params['working_country'])) {
            $builder->leftJoin(HrStaffItemsModel::class,
                "hsi.staff_info_id = hsit.staff_info_id AND item = 'WORKING_COUNTRY'", 'hsit');
        }
        $builder->andWhere("hsi.is_sub_staff = :is_sub_staff: AND hsi.formal IN ({formal:array})", [
            'is_sub_staff' => StaffInfoEnums::IS_SUB_STAFF_NO,
            'formal'       => [StaffInfoEnums::FORMAL_IN, StaffInfoEnums::FORMAL_TRAINEE],
        ]);
        //$builder->andWhere("kao.activity_id = :activity_id:", ['activity_id' => $params['activity_id']]);
        // 过滤 ork名单的数据
        $builder->andWhere("kao.staff_id is null");

        // 工号/姓名 支持模糊匹配
        if (isset($params['name']) && !empty($params['name'])) {
            $builder->andWhere('hsi.staff_info_id LIKE :staff_info_id: OR hsi.name LIKE :name:',
                ['staff_info_id' => "%{$params['name']}%", 'name' => "%{$params['name']}%"]);
        }

        // 部门id查询
        if (isset($params['node_department_id']) && !empty($params['node_department_id'])) {
            $department_ids = (new DepartmentService())->getChildrenListByDepartmentIdV2($params['node_department_id'],
                true);
            array_push($department_ids, $params['node_department_id']);
            $builder->andWhere('hsi.node_department_id IN ({department_ids:array})',
                ['department_ids' => $department_ids]);
        }

        // 职位id 查询
        if (isset($params['job_id']) && !empty($params['job_id'])) {
            $builder->andWhere('hsi.job_title = :job_id:', ['job_id' => $params['job_id']]);
        }

        // 员工所在网点
        if (isset($params['sys_store_id']) && !empty($params['sys_store_id'])) {
            $builder->andWhere('hsi.sys_store_id = :sys_store_id:', ['sys_store_id' => $params['sys_store_id']]);
        }

        // 工作所在国家
        if (isset($params['working_country']) && !empty($params['working_country'][0])) {
            $builder->andWhere("hsit.value IN ({working_country:array})",
                ['working_country' => $params['working_country']]);
        }

        // 直线上级 支持工号 模糊匹配
        if (isset($params['leader']) && !empty($params['leader'])) {
            $builder->andWhere("hsi.manger LIKE :leader_id:", ['leader_id' => "%{$params['leader']}%"]);
        }

        // todo 除了在职状态之外的其他查询条件 都必须过滤离职的数据
        $builder->andWhere("hsi.state != :state:", ['state' => StaffInfoEnums::STAFF_STATE_LEAVE]);

        // 在职状态，支持选择多个 如果前端传递了离职状态，则需求提出掉
        if (isset($params['staff_state'][0]) && !empty($params['staff_state'][0])) {
            $key = array_search(StaffInfoEnums::STAFF_STATE_LEAVE, $params['staff_state']);
            if (false !== $key) {
                unset($params['staff_state'][$key]);
                $params['staff_state'] = array_values($params['staff_state']);
            }

            // 防止只是传递了一个离职，unset后为空的情况
            if (!empty($params['staff_state'])) {
                if (in_array(StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE, $params['staff_state'])) {
                    $builder->andWhere("hsi.state IN ({states:array}) OR (hsi.state = :wait_state: AND hsi.wait_leave_state = :wait_leave_state:)",
                        [
                            'states'           => $params['staff_state'],
                            'wait_state'       => StaffInfoEnums::STAFF_STATE_IN,
                            'wait_leave_state' => StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES,
                        ]);
                } else {
                    $builder->andWhere("hsi.state IN ({states:array}) AND hsi.wait_leave_state = :wait_leave_state:",
                        [
                            'states'           => $params['staff_state'],
                            'wait_leave_state' => StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_NO,
                        ]);
                }
            }
        }

        // 员工入职日期
        if (!empty($params['hire_date'][0]) && !empty($params['hire_date'][1])) {
            $params['hire_date'][0] = $params['hire_date'][0].' 00:00:00';
            $params['hire_date'][1] = $params['hire_date'][1].' 23:59:59';
            $builder->andWhere("hsi.hire_date >= :hire_date_st: AND hsi.hire_date <= :hire_date_end:",
                ['hire_date_st' => $params['hire_date'][0], 'hire_date_end' => $params['hire_date'][1]]);
        }

        // 雇佣类型 正式员工”“实习生”“月薪制合同工”“日薪制合同工”“时薪制合同工
        if (isset($params['hire_type'][0]) && !empty($params['hire_type'][0])) {
            $builder->andWhere("hsi.hire_type IN ({hire_type:array})", ['hire_type' => $params['hire_type']]);
        }

        // 试用期状态 1试用期，2已通过，3未通过，4已转正
        if (isset($params['status'][0]) && !empty($params['status'][0])) {
            $builder->leftJoin(HrProbationModel::class, "hp.staff_info_id = hsi.staff_info_id", 'hp');
            $builder->andWhere("hp.status IN ({status:array})", ['status' => $params['status']]);
        }
        return $builder;
    }

    /**
     * 获取 准备 添加参与kpi活动的用户列表记录数据
     * @param $params
     * @return array
     * <AUTHOR>
     */
    public function getStaffListCount(array $params)
    {
        $builder = $this->modelsManager->createBuilder();
        $columns = 'COUNT(DISTINCT hsi.staff_info_id) AS total';
        $builder->columns($columns);
        $builder->from(['hsi' => HrStaffInfoModel::class]);
        // 过滤掉 okr 的员工
        $builder->leftJoin(KpiActivityOkrModel::class,
            "hsi.staff_info_id = kao.staff_id AND kao.activity_id = {$params['activity_id']}", 'kao');
        $builder = $this->getStaffListCondition($builder, $params);
        return intval($builder->getQuery()->getSingleResult()->total);
    }

    /**
     * 获取 准备 添加参与kpi活动的用户列表
     * @param $params
     * @return array
     * <AUTHOR>
     */
    public function getStaffList($params): array
    {
        $page   = intval($params['pageNum']);
        $size   = intval($params['pageSize']);
        $offset = $size * ($page - 1);

        $data    = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page,
                'per_page'     => $size,
                'total_count'  => 0,
            ],
        ];
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        try {
            //验证活动数据 如果不存 或者 活动已经结束，则直接抛出异常
            ActivityStaffService::getInstance()->validationActivity($params);

            //获取total count
            $data['pagination']['total_count'] = $this->getStaffListCount($params);
            if (0 == $data['pagination']['total_count']) {
                return [
                    'code'    => $code,
                    'message' => $message,
                    'data'    => $data,
                ];
            }

            //获取列表数据
            $builder = $this->modelsManager->createBuilder();
            $columns = 'DISTINCT hsi.staff_info_id, 
                hsi.name, 
                hsi.node_department_id,
                hsi.job_title AS job_id,
                hsi.sys_store_id,
                hsi.state, 
                hsi.wait_leave_state, 
                hsi.hire_date';
            $builder->from(['hsi' => HrStaffInfoModel::class]);
            // 过滤掉 okr 的员工
            $builder->leftJoin(KpiActivityOkrModel::class,
                "hsi.staff_info_id = kao.staff_id AND kao.activity_id = {$params['activity_id']}", 'kao');
            // 如果拼接了工作所在国家 需要获取工作所在国家的字段
            if (!empty($params['working_country'])) {
                $columns .= ',hsit.value';
            }

            // 组织查询条件
            $builder = $this->getStaffListCondition($builder, $params);
            $builder->columns($columns);
            $builder->limit($size, $offset);
            $builder->orderBy('hsi.staff_info_id ASC');
            $data['items'] = $builder->getQuery()->execute()->toArray();
            $staff_ids     = array_column($data['items'], 'staff_info_id');

            // 网点id
            $storeIds  = array_values(array_unique(array_column($data['items'], 'sys_store_id')));
            $storeList = CommonService::getInstance()->getSysStoreByParams(['id' => $storeIds], ['id', 'name']);
            if (!empty($storeList)) {
                $storeList = array_column($storeList, null, 'id');
            }

            // 部门id
            $departmentIds  = array_values(array_unique(array_column($data['items'], 'node_department_id')));
            $departmentList = CommonService::getInstance()->getDepartmentByParams([
                "id" => $departmentIds,
            ], ['id', 'name', 'deleted']);
            if (!empty($departmentList)) {
                $departmentList = array_column($departmentList, null, "id");
            }

            // 职位id
            $jobIds       = array_values(array_unique(array_column($data['items'], "job_id")));
            $jobTitleList = CommonService::getInstance()->getJobTitleByParams([
                "id"     => $jobIds,
                "status" => 1,
            ], ['id', 'job_name']);
            if (!empty($jobTitleList)) {
                $jobTitleList = array_column($jobTitleList, null, 'id');
            }

            foreach ($data['items'] as $key => &$val) {
                $val['node_department_name'] = (CommonService::getInstance())->getDepartmentNameView($departmentList[$val['node_department_id']] ?? []);
                $val['job_name']             = !empty($jobTitleList[$val['job_id']]) ? $jobTitleList[$val['job_id']]['job_name'] : "";
                if ($val['sys_store_id'] != KpiActivityStaffEnums::STORE_HEADER_OFFICE_ID) {
                    $val['sys_store_name'] = !empty($storeList[$val['sys_store_id']]) ? $storeList[$val['sys_store_id']]['name'] : "";
                }
                $val['hire_date'] = !empty($val['hire_date']) ? date('Y-m-d', strtotime($val['hire_date'])) : "";
            }

            //处理、组装数据
            $data['items'] = $this->createStaffListData($data['items'], $params['activity_id'], $staff_ids);
        } catch (ValidationException $e) {
            $code    = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error(__CLASS__.' function: getStafflist-failed:'.$e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }


    /**
     * KPI活动管理 - 添加员工 - 全选
     * @param $params
     * @param $user_info
     * @return array
     */
    public function addActivityAllStaff($params, $user_info)
    {
        $code        = ErrCode::$SUCCESS;
        $message     = $real_message = '';
        $result_data = [
            'success_count' => 0,
            'error_count'   => 0,
        ];
        // 防止选择 group CEO的情况
        set_time_limit(0);
        $db = $this->getDI()->get('db_backyard');
        try {
            //验证活动数据 如果不存 或者 活动已经结束，则直接抛出异常
            ActivityStaffService::getInstance()->validationActivity($params);

            //获取total count
            $count = $this->getStaffListCount($params);
            if (0 == $count) {
                return [
                    'code'    => $code,
                    'message' => $message,
                    'data'    => $result_data,
                ];
            }

            // 最大支持10000
            if ($count > ActivityStaffService::$max_add_staff_count) {
                throw new ValidationException(static::$t->_('kpi_max_mum',
                    ['nums' => ActivityStaffService::$max_add_staff_count]), ErrCode::$VALIDATE_ERROR);
            }

            // 是否终止循环，默认不终止
            $is_while = true;
            $page     = 1;                                 // 默认是从第一页开始处理
            $size     = ActivityStaffService::$chunk_num;  // 每页处理2000条数据
            $db->begin();
            while ($is_while) {
                $offset = $size * ($page - 1);

                //获取列表数据
                $builder = $this->modelsManager->createBuilder();
                $columns = 'DISTINCT hsi.staff_info_id, 
                hsi.name, 
                hsi.node_department_id,
                hsi.job_title AS job_id,
                hsi.sys_store_id,
                hsi.state, 
                hsi.wait_leave_state, 
                hsi.hire_date';
                $builder->from(['hsi' => HrStaffInfoModel::class]);
                // 过滤掉 okr 的员工
                $builder->leftJoin(KpiActivityOkrModel::class,
                    "hsi.staff_info_id = kao.staff_id AND kao.activity_id = {$params['activity_id']}", 'kao');
                // 如果拼接了工作所在国家 需要获取工作所在国家的字段
                if (!empty($params['working_country'])) {
                    $columns .= ',hsit.value';
                }

                // 组织查询条件
                $builder = $this->getStaffListCondition($builder, $params);
                $builder->columns($columns);
                $builder->limit($size + 1, $offset);
                $builder->orderBy('hsi.staff_info_id DESC');
                $data = $builder->getQuery()->execute()->toArray();
                if (count($data) > $size) {
                    // 去掉末尾多余的一条数据
                    array_pop($data);
                    $page++;
                } else {
                    // 没有下一页了需要退出循环
                    $is_while = false;
                }

                // 拿到所有的用户id数据
                $staff_ids  = array_column($data, 'staff_info_id');
                $staff_data = $this->createStaffListData($data, $params['activity_id'], $staff_ids);
                // 生成批量插入的数据
                $insert_data = ActivityStaffService::getInstance()->createInsertData($staff_data,
                    $params['activity_id'], $user_info['id'], $user_info['name']);

                //批量插入active staff数据
                $activity_staff_model = new KpiActivityStaffModel();
                if (!empty($insert_data['insert_data'])) {
                    $batch_insert_ret = $activity_staff_model->batch_insert($insert_data['insert_data'], 'db_backyard');
                    if (false === $batch_insert_ret) {
                        throw new BusinessException('addActivityAllStaff 向 kpi_activity_staff 批量插入数据失败：'.json_encode($activity_staff_model->toArray(),
                                JSON_UNESCAPED_UNICODE).get_data_object_error_msg($activity_staff_model),
                            ErrCode::$BUSINESS_ERROR);
                    }
                }
                $result_data['success_count'] += $insert_data['success_count'];
                $result_data['error_count']   += $insert_data['error_count'];
                $result_data['page']          = $page;
            }
            // 要么全部成功 要么全部失败
            $db->commit();
        } catch (ValidationException $e) {
            $code    = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            //事务回滚
            $db->rollback();
            $this->logger->warning(__CLASS__.' function: addActivityAllStaff-failed:'.$real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $result_data,
        ];
    }

    /**
     * 通过 用户id数组 获取用户列表
     * @param $activity_id
     * @param $staff_ids
     * @return array
     * <AUTHOR>
     */
    function getStaffListByStaffIds($activity_id, $staff_ids, $state_arr = []): array
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns(
            'hsi.staff_info_id, 
            hsi.name, 
            hsi.state, 
            hsi.wait_leave_state, 
            hsi.node_department_id, 
            d.name node_department_name, 
            hjt.id job_id, 
            hjt.job_name, 
            hsi.sys_store_id, 
            ss.name sys_store_name, 
            hsi.hire_date'
        );
        $builder->from(['hsi' => HrStaffInfoModel::class]);
        $builder->leftJoin(DepartmentModel::class, 'hsi.node_department_id = d.id', 'd');
        $builder->leftJoin(HrJobTitleModel::class, 'hsi.job_title = hjt.id', 'hjt');
        $builder->leftJoin(SysStoreModel::class, 'hsi.sys_store_id = ss.id', 'ss');
        $builder->Where("hsi.staff_info_id IN ({staff_ids:array})", ['staff_ids' => $staff_ids]);
        $builder->andWhere('hsi.formal IN ({formal:array}) AND hsi.is_sub_staff = :is_sub_staff:',
            [
                'formal'       => [StaffInfoEnums::FORMAL_IN, StaffInfoEnums::FORMAL_TRAINEE],
                'is_sub_staff' => StaffInfoEnums::IS_SUB_STAFF_NO,
            ]
        );

        if (is_array($state_arr) && !empty($state_arr)) {
            $builder->andWhere("hsi.state IN ({state:array})", ['state' => $state_arr]);
        }
        $data = $builder->getQuery()->execute()->toArray();
        if (empty($data)) {
            return $data;
        }

        //处理、组装数据
        return $this->createStaffListData($data, $activity_id, $staff_ids);
    }

    /**
     * 组装staff list 数据
     * @param $data
     * @param $staff_ids
     * @return array
     * <AUTHOR>
     */
    function createStaffListData($data, $activity_id, $staff_ids)
    {
        //获取所在工作国家
        $working_country_list = HrStaffItemsModel::find([
            'conditions' => "item = 'WORKING_COUNTRY' AND staff_info_id IN ({staff_ids:array})",
            'bind'       => ['staff_ids' => $staff_ids],
            'columns'    => ['staff_info_id, value'],
        ])->toArray();
        $working_country_list = array_column($working_country_list, null, 'staff_info_id');
        //获取直接上级
        $Leader_list = $this->getLeaderListByStaffId($staff_ids);
        $Leader_list = array_column($Leader_list, null, 'staff_info_id');
        //获取已经参与活动的员工列表
        $activity_staff_list = ActivityStaffService::getInstance()->getStafflistByActivityStaff($activity_id,
            $staff_ids);
        $activity_staff_list = array_column($activity_staff_list, null, 'staff_id');

        //处理数据
        foreach ($data as $key => $val) {
            if (empty($working_country_list[$val['staff_info_id']]['value'])) {
                //print_r($val);die;
            }

            if ($val['state'] == StaffInfoEnums::STAFF_STATE_IN && $val['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES) {
                $data[$key]['state_name'] = static::$t->_(StaffInfoEnums::$staff_state[StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE]);
            } else {
                $data[$key]['state_name'] = static::$t->_(StaffInfoEnums::$staff_state[$val['state']]);
            }
            $data[$key]['working_country']      = !empty($working_country_list[$val['staff_info_id']]['value']) ? $working_country_list[$val['staff_info_id']]['value'] : 0;
            $data[$key]['working_country_name'] = !empty($working_country_list[$val['staff_info_id']]['value']) ? static::$t->_(StaffInfoEnums::$working_country[$working_country_list[$val['staff_info_id']]['value']]) : "";
            $data[$key]['leader_id']            = !empty($Leader_list[$val['staff_info_id']]['leader_id']) ? $Leader_list[$val['staff_info_id']]['leader_id'] : '';
            $data[$key]['leader_name']          = !empty($Leader_list[$val['staff_info_id']]['leader_name']) ? $Leader_list[$val['staff_info_id']]['leader_name'] : '';
            $data[$key]['leader_state']         = !empty($Leader_list[$val['staff_info_id']]['leader_state']) ? $Leader_list[$val['staff_info_id']]['leader_state'] : 0;
            $data[$key]['selected']             = isset($activity_staff_list[$val['staff_info_id']]) ? 1 : 0;
            if ($val['sys_store_id'] == KpiActivityStaffEnums::STORE_HEADER_OFFICE_ID) {
                $data[$key]['sys_store_name'] = KpiActivityStaffEnums::STORE_HEADER_OFFICE_NAME;
            }
            $data[$key]['leader_info'] = ActivityStaffService::getInstance()->getStaffInfoIdAndName((int)$data[$key]['leader_id'],
                (string)$data[$key]['leader_name']);
        }

        return $data;
    }

    /**
     * 批量获取leader list
     * @param $staff_ids
     * @return mixed
     * <AUTHOR>
     */
    function getLeaderListByStaffId($staff_ids)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('hsit.staff_info_id, hsi.staff_info_id leader_id, hsi.name leader_name, hsi.state leader_state');
        $builder->from(['hsit' => HrStaffItemsModel::class]);
        $builder->leftJoin(HrStaffInfoModel::class, 'hsit.value = hsi.staff_info_id', 'hsi');
        $builder->where('hsi.formal IN ({formal:array}) AND is_sub_staff = :is_sub_staff:', [
            'formal'       => [StaffInfoEnums::FORMAL_IN, StaffInfoEnums::FORMAL_TRAINEE],
            'is_sub_staff' => StaffInfoEnums::IS_SUB_STAFF_NO,
        ]);
        $builder->andWhere("hsit.item = 'MANGER' AND hsit.staff_info_id IN ({staff_ids:array})",
            ['staff_ids' => $staff_ids]);
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 根据员工工号获取员工信息
     * @param int $staff_id 员工工号
     * @return mixed
     */
    public function getStaffInfoByStaffId($staff_id)
    {
        return HrStaffInfoModel::findFirst([
            'conditions' => "staff_info_id = :staff_info_id:",
            'bind'       => ['staff_info_id' => $staff_id],
        ]);
    }

    /**
     * 根据员工工号获取员工信息
     * @param int $staff_id 员工工号
     * @return mixed
     */
    public function getStaffInfo($staff_id)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            'main.staff_info_id',
            'main.manger',
            'main.name',
            'main.nick_name',
            'main.node_department_id',
            'main.job_title_grade_v2',
            'department.name as node_department_name',
            'department.deleted as node_department_deleted',
            'main.state',
            'main.wait_leave_state',
            'main.job_title',
            'job.job_name',
        ]);
        $builder->from(['main' => HrStaffInfoModel::class]);
        $builder->leftjoin(DepartmentModel::class, 'CAST(main.node_department_id as CHAR) = department.id',
            'department');
        $builder->leftjoin(HrJobTitleModel::class, 'CAST(main.job_title AS UNSIGNED) = job.id', 'job');
        $builder->where('main.staff_info_id = :staff_id:', ['staff_id' => $staff_id]);
        return $builder->getQuery()->getSingleResult()->toArray();
    }

    /**
     * 活动参数人员个数
     * @param $params
     * @return array
     * <AUTHOR>
     */
    public function getJoinActivityStaffNums($params): array
    {
        $data = [
            'code' => ErrCode::$SUCCESS,
            'msg'  => 'success',
            'data' => [
                'nums'        => 0,
                'activity_id' => $params['activity_id'],
            ],
        ];
        try {
            //验证活动数据
            ActivityStaffService::getInstance()->validationActivity($params);

            // 获取未推送通知上级的数据
            $result = $this->getKpiActivityStaffByParams([
                'activity_id'    => $params['activity_id'],
                'is_send_leader' => KpiActivityStaffEnums::IS_SEND_LEADER_NO,
            ], ['id', 'activity_id', 'staff_id', 'leader_id', 'leader_name', 'is_send_leader']);
            if (empty($result)) {
                return $data;
            }

            $data['data'] = [
                'nums'        => count($result),
                'activity_id' => $params['activity_id'],
            ];
        } catch (ValidationException $e) {
            $log_info = [
                'message' => $e->getLine(),
                'code'    => $e->getCode(),
                'file'    => $e->getFile(),
                'line'    => $e->getLine(),
            ];
            $this->logger->info(__CLASS__." function getJoinActivityStaffNums: ValidationExceptionREt".json_encode($log_info,
                    JSON_UNESCAPED_UNICODE));
        }
        return $data;
    }

    /**
     * @param array $params
     * @param array $colum
     * @return array
     * <AUTHOR>
     */
    public function getKpiActivityStaffByParams(array $params, array $colum, array $notParams = []): array
    {
        if (empty($params)) {
            return [];
        }
        $fields  = empty($colum) ? "*" : implode(',', $colum);
        $builder = $this->modelsManager->createBuilder();
        $builder->columns($fields);
        $builder->from(['kao' => KpiActivityStaffModel::class]);
        foreach ($params as $field => $val) {
            if (!is_array($val)) {
                $builder->andWhere('kao.'.$field." = :{$field}:", [$field => $val]);
            }
            if (is_array($val)) {
                $builder->andWhere('kao.'.$field." IN ({{$field}:array})", [$field => $val]);
            }
        }
        if (!empty($notParams)) {
            foreach ($notParams as $field => $val) {
                if (!is_array($val)) {
                    $builder->andWhere('kao.'.$field." != :{$field}:", [$field => $val]);
                }
                if (is_array($val)) {
                    $builder->andWhere('kao.'.$field." IN ({{$field}:array})", [$field => $val]);
                }
            }
        }
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 批量提醒上级填写
     * @param array $data ['leader_list'=>'批量发送消息员工组', 'activity_id'=>'活动ID', 'title'=>'消息标题翻译key', 'content'=>'消息内容翻译key'] 参数组
     * @param array $user 当前登陆者用户信息
     * @return array
     * <AUTHOR>
     */
    public function batchSendMsg(array $params, array $user): array
    {
        $real_message = '';
        $data         = [
            'code'    => ErrCode::$SUCCESS,
            'message' => 'success',
            'data'    => true,
        ];

        set_time_limit(0);
        $db = $this->getDI()->get('db_backyard');
        try {
            $activity_id = $params['activity_id'];
            //获取活动信息 并验证是否开启
            $activity_info = ActivityStaffService::getInstance()->validationActivity($params);

            // 参与kpi活动员工的kpi制定人默认为直线上级 获取直线上级
            $columns = [
                "kas.id AS id",
                "kas.is_change_leader AS is_change_leader",
                "kas.is_send_leader AS is_send_leader",
                "staff.staff_info_id AS staff_id",
                "staff.name AS staff_name",
                "IF(kas.leader_id = 0,manger.staff_info_id,kas.leader_id ) AS leader_id",
                "IF(kas.leader_id = 0,manger.name,kas.leader_name) AS leader_name",
                "manger.state AS leader_state",
            ];
            $list    = $this->getKpiLeader(
                $columns,
                [],
                [$activity_id],
                [KpiActivityStaffEnums::IS_SEND_LEADER_NO]
            );
            if (empty($list)) {
                // 如果没有找到 直接返回
                return $data;
            }

            // 存储用户发送push 和 消息的数据
            $leader_list = [];
            // 存储日志数据
            $insert_log_data = [];
            $date            = date('Y-m-d H:i:s');
            foreach ($list as $key => $value) {
                // 如果存在上级 且 是非离职 记录到发送消息的列表中
                if (!empty($value['leader_id']) && in_array($value['leader_state'],
                        [StaffInfoEnums::STAFF_STATE_IN, StaffInfoEnums::STAFF_STATE_STOP])) {
                    // 以leader_id为key，可以防止重复数据
                    $leader_list[$value['leader_id']] = $value;
                    //组装发送记录
                    $insert_log_data[] = [
                        'activity_id'     => $activity_id,                                                //活动ID
                        'staff_id'        => $value['staff_id'],                                          //员工id
                        'leader_id'       => $value['leader_id'],                                         //leader1ID
                        'leader_name'     => $value['leader_name'],                                       //leader1姓名
                        'send_staff_id'   => $user['id'],                                                 //发送者ID
                        'send_staff_name' => $user['name'],                                               //发送者name
                        'created_at'      => $date,                                                       //创建时间
                        'updated_at'      => $date                                                        //更新时间
                    ];
                }
            }

            //先根据leader_id 查询发送记录表是否已经发送过, 否则后续插入数据后，会影响判断逻辑
            $leader_send_list = [];
            if (!empty($leader_list)) {
                $leader_send_log = KpiLeaderMsgSendLogService::getInstance()->getKpiLeaderMsgSendLogServiceByParams([
                    "activity_id" => $activity_id,
                    'leader_id'   => array_values(array_keys($leader_list)),
                ], ['id', 'activity_id', 'staff_id', 'leader_id', 'leader_name']);
                if (!empty($leader_send_log)) {
                    $leader_send_list = array_column($leader_send_log, null, 'leader_id');
                }
            }

            // 第二步 db 操作，如果先插入会影响到查询后的逻辑验证
            $db->begin();
            // 用于更新kpi_activity_staff表的is_send_leader字段使用
            $ids = implode(',', array_column($list, 'id'));
            // 更新kpi_activity_staff表的is_send_leader状态为已经发送
            $update_ret = $db->updateAsDict(
                (new KpiActivityStaffModel())->getSource(),
                [
                    'is_send_leader' => KpiActivityStaffEnums::IS_SEND_LEADER_YES,
                    'updated_at'     => $date,
                    'stage'          => KpiActivityStaffEnums::STAGE_LEADER,
                ],
                ["conditions" => "activity_id = ".$params['activity_id']." AND id IN ($ids)"]
            );
            if (false === $update_ret) {
                throw new BusinessException('batchSendMsg 更新 kpi_activity_staff 表失败-params：'.json_encode([
                        'activity_id' => $params['activity_id'],
                        "id"          => $ids,
                    ], JSON_UNESCAPED_UNICODE).get_data_object_error_msg($db), ErrCode::$BUSINESS_ERROR);
            }

            // 插入发送日志数据
            if (!empty($insert_log_data)) {
                $kpi_send_log_model = new KpiLeaderMsgSendLogModel();
                $batch_insert_ret   = $kpi_send_log_model->batch_insert($insert_log_data, 'db_backyard');
                if (false === $batch_insert_ret) {
                    throw new BusinessException('batchSendMsg 向 kpi_leader_msg_send_log 批量插入数据失败：'.json_encode($kpi_send_log_model->toArray(),
                            JSON_UNESCAPED_UNICODE).get_data_object_error_msg($kpi_send_log_model),
                        ErrCode::$BUSINESS_ERROR);
                }
            }
            $db->commit();

            // 如果leader_list为空，直接返回，不走发送push和消息的逻辑，
            if (empty($leader_list)) {
                return $data;
            }

            // 活动配置信息
            $activity_setting     = json_decode($activity_info['config'], true);
            $leader_setting_power = $activity_setting['time_node']['leader']['on-off'];
            $title_key            = KpiActivityStaffEnums::REMIND_LEADER_TITLE;
            $content_key          = KpiActivityStaffEnums::REMIND_LEADER_CONTENT;
            if ($leader_setting_power) {
                // 是否限制下班打卡，站内信的内容不同
                $content_key = KpiActivityStaffEnums::REMIND_LEADER_CONTENT_1;
            }

            //保存发送站内信的用户数据
            $staff_users  = [];
            $push_service = new PushService();
            //$message_scheme = $this->getLeaderMessageScheme();//leader的push消息跳转地址
            foreach ($leader_list as $leader) {
                // 上级只是接受一次通知，如果日志表中存在，则不再发送，只是更新db的数据
                if (isset($leader_send_list[$leader['leader_id']]) && !empty($leader_send_list[$leader['leader_id']])) {
                    continue;
                }

                $staff_users[] = ['id' => $leader['leader_id']];
                //todo 产品要求 暂时不发送push 发送push
                //$push_service->sendPush($leader['leader_id'], $activity_info, $title_key, $content_key, $message_scheme);
            }

            //发送站内信
            $info        = [
                'years'  => $activity_info['years'],
                'period' => $activity_info['period'],
                // 多有leader的截止时间都是利用当前时间 + 上活动配置的截止时间，因为存在发送记录的leader 不在接受消息和push
                'date'   => date('Y-m-d', strtotime("+{$activity_setting['time_node']['leader']['day']} day")),
            ];
            $msg_title   = $push_service->getFormatMsg($title_key, $info);
            $msg_content = $push_service->getFormatMsg($content_key, $info, '<br>');
            $msg_content = KpiActivityStaffEnums::MSG_STYLE_BEGIN.$msg_content.KpiActivityStaffEnums::MSG_STYLE_END;
            $push_service->sendMessage($staff_users, $msg_title, $msg_content,
                KpiActivityStaffEnums::MESSAGE_CATEGORY_KPI_LEADER);
            return $data;
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $data['code']    = $e->getCode();
            $data['message'] = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $data['code']    = $e->getCode();
            $data['message'] = static::$t->_('retry_later');
            $real_message    = $e->getMessage();
        } catch (\Exception $e) {
            $data['code']    = ErrCode::$SYSTEM_ERROR;
            $data['message'] = static::$t->_('retry_later');
            $real_message    = $e->getMessage().$e->getTraceAsString();
        }

        if (!empty($real_message)) {
            //事务回滚
            $db->rollback();
            $this->logger->warning(__CLASS__.'  function:batchSendMsg-failed:'.$real_message);
        }

        $data['data'] = empty($real_message);
        return $data;
    }

    /**
     * 获取leader push消息跳转页面链接地址
     * @return string
     * <AUTHOR>
     */
    public function getLeaderMessageScheme()
    {
        return env('kpi_leader_detail_url', 'http://192.168.0.222:90/#/');
    }

    /**
     * 根据条件获取员工信息
     * @param array $params
     * @param array $colum
     * @return mixed
     * <AUTHOR>
     */
    public function getStaffInfoByParams(array $params, array $colum): array
    {
        if (empty($params)) {
            return [];
        }
        $fields  = empty($colum) ? "*" : implode(',', $colum);
        $builder = $this->modelsManager->createBuilder();
        $builder->columns($fields);
        $builder->from(['hsi' => HrStaffInfoModel::class]);
        foreach ($params as $field => $val) {
            if (!is_array($val)) {
                $builder->andWhere('hsi.'.$field." = :{$field}:", [$field => $val]);
            }
            if (is_array($val)) {
                $builder->andWhere('hsi.'.$field." IN ({{$field}:array})", [$field => $val]);
            }
        }
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 获取kpi制定人
     * @params array $columns
     * @param array $staff_ids
     * @param array $activity_ids
     * @param array $is_send_leaders
     * @param bool $filter_manger_state
     * @return array
     */
    public function getKpiLeader(
        array $columns,
        array $staff_ids,
        array $activity_ids,
        array $is_send_leaders = [],
        array $manger_state = []
    ): array {
        $column_str = "*";
        if (!empty($columns)) {
            $column_str = implode(',', $columns);
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->columns($column_str);
        $builder->from(['kas' => KpiActivityStaffModel::class]);
        $builder->leftjoin(HrStaffInfoModel::class, 'kas.staff_id = staff.staff_info_id', 'staff');
        $builder->leftjoin(HrStaffInfoModel::class,
            '(kas.leader_id = 0 and manger.staff_info_id = staff.manger) OR (kas.leader_id !=0 and manger.staff_info_id = kas.leader_id)',
            'manger');
        $builder->andWhere('staff.formal IN ({formal:array}) AND staff.is_sub_staff = :is_sub_staff:',
            [
                'formal'       => [StaffInfoEnums::FORMAL_IN, StaffInfoEnums::FORMAL_TRAINEE],
                'is_sub_staff' => StaffInfoEnums::IS_SUB_STAFF_NO,
            ]
        );
        if ($manger_state) {
            $builder->inWhere('manger.state', $manger_state);
        }

        if (!empty($is_send_leaders)) {
            $builder->inWhere('kas.is_send_leader', $is_send_leaders);
        }

        if (!empty($staff_ids)) {
            $builder->inWhere('kas.staff_id', $staff_ids);
        }

        if (!empty($activity_ids)) {
            $builder->inWhere('kas.activity_id', $activity_ids);
        }

        return $builder->getQuery()->execute()->toArray();
    }


    /**
     * 批量变更制定人
     * @param array $params
     * @param array $userInfo
     * @return array
     * <AUTHOR>
     */
    public function allModifyPlanner(array $params, array $userInfo): array
    {
        $code        = ErrCode::$SUCCESS;
        $message     = '';
        $result_data = [];
        try {
            // 验证活动是否为进行中的活动
            $activity_info = ActivityService::getInstance()->checkActivityInfo($params['activity_id']);
            //活动已结束
            if ($activity_info->status == KpiActivityEnums::STATUS_DONE) {
                throw new ValidationException(static::$t->_('kpi_activity_status_send_done'), ErrCode::$VALIDATE_ERROR);
            }
            $activity_info = $activity_info->toArray();

            // 验证变更后的kpi制定人是否合法。
            // [1] 工号是否存在
            $leader_list = $this->getStaffInfoByParams([
                "staff_info_id" => $params['leader_id'],
                "is_sub_staff"  => StaffInfoEnums::IS_SUB_STAFF_NO,
                "formal"        => [StaffInfoEnums::FORMAL_IN, StaffInfoEnums::FORMAL_TRAINEE],
                //"state" => StaffInfoEnums::STAFF_STATE_IN
            ], ['id', 'staff_info_id', 'name', 'state']);
            if (empty($leader_list)) {
                // 提示 该工号不存在，请重新输入
                throw new ValidationException(static::$t->_('kpi_activity_staff_not_exist'), ErrCode::$VALIDATE_ERROR);
            }

            $leader_info = $leader_list[0];
            if ($leader_info['state'] == StaffInfoEnums::STAFF_STATE_LEAVE) {
                // 提示 该员工非在职状态,请重新输入
                throw new ValidationException(static::$t->_('kpi_activity_staff_inactive'), ErrCode::$VALIDATE_ERROR);
            }

            // 获取符合条件的个数
            $count = ActivityStaffService::getInstance()->getActivityStaffListCount($params);
            // 如果没有符合条件的数据，则直接返回
            if (0 == $count) {
                return [
                    'code'    => $code,
                    'message' => $message,
                    'data'    => $result_data,
                ];
            }

            // 是否终止循环，默认不终止
            $is_while        = true;
            $page            = 1;                                 // 默认是从第一页开始处理
            $size            = ActivityStaffService::$chunk_num;  // 每页处理2000条数据
            $cur_time        = date("Y-m-d H:i:s", time());
            $is_send_message = false;
            $staff_list      = [];
            while ($is_while) {
                $offset = $size * ($page - 1);
                // 根据条件获取数据
                $builder = $this->modelsManager->createBuilder();
                $builder->columns('kas.id, 
                    kas.activity_id,  
                    kas.staff_id,
                    kas.is_send_leader,
                    IF(kas.leader_id = 0,manger.staff_info_id,kas.leader_id ) AS leader_id,
                    IF(kas.leader_id = 0,manger.name,kas.leader_name) AS leader_name,
                    kas.stage, 
                    hsi.name AS staff_name, 
                    hsi.state,
                    hsi.hire_date'
                );
                $builder->from(['kas' => KpiActivityStaffModel::class]);
                $builder->leftJoin(HrStaffInfoModel::class,
                    "hsi.staff_info_id = kas.staff_id", 'hsi');
                $builder->leftjoin(HrStaffInfoModel::class, 'manger.staff_info_id = hsi.manger', 'manger');
                $builder->leftJoin(HrStaffItemsModel::class,
                    "hsit.staff_info_id = kas.staff_id AND item = 'WORKING_COUNTRY'", 'hsit');
                $builder->andWhere('manger.state = :state:', ['state' => StaffInfoEnums::STAFF_STATE_IN]);

                // 组织查询条件
                $builder = ActivityStaffService::getInstance()->getActivityStaffCondition($builder, $params);
                $builder->limit($size + 1, $offset);
                $builder->orderBy('hsi.hire_date DESC');
                $data = $builder->getQuery()->execute()->toArray();
                if (count($data) > $size) {
                    // 去掉末尾多余的一条数据
                    array_pop($data);
                    if (1 == $page) {
                        // 该数据用作向新的制定人发送通知时 ，组织消息内容时使用
                        $staff_list = $data;
                    }
                    $page++;
                } else {
                    // 没有下一页了需要退出循环
                    $is_while = false;
                }

                $staff_ids    = array_column($data, 'staff_id');
                $modifyParams = [
                    'activity_id' => $params['activity_id'],
                    'staff_ids'   => $staff_ids,
                    'leader_id'   => $params['leader_id'],
                ];

                // db 操作，插入变更日志
                $this->modifyPlanner($data, $leader_info, $modifyParams, $userInfo, $cur_time, $is_send_message);
            }

            // 向新的leader赋权限、发送通知只是需要发送一次即可
            $this->grantPermissionsToNewLeader($leader_info['staff_info_id'], $cur_time);

            // 只有想上级发送过活动开启的通知后，才向新的leader发送消息，否则不发送变更通知。
            if ($is_send_message) {
                $this->changeNoticeToLeader($staff_list, $leader_info, $activity_info);
            }
        } catch (ValidationException $e) {
            $code    = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error(__CLASS__.' function: allModifyPlanner-failed:'.$e->getMessage());
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $result_data,
        ];
    }

    /**
     * 批量变更制定人人
     * @return array
     * @throws BusinessException
     * @throws ValidationException
     * <AUTHOR>
     */
    public function batchModifyPlanner(array $params, array $userInfo)
    {
        // 未勾选员工时
        if (empty($params['staff_ids'])) {
            // 请勾选员工数据
            throw new ValidationException(static::$t->_('kpi_activity_staff_empty'), ErrCode::$VALIDATE_ERROR);
        }

        // 最大支持2000
        if (count($params['staff_ids']) > ActivityStaffService::$max_add_staff_count) {
            throw new ValidationException(static::$t->_('kpi_max_mum',
                ['nums' => ActivityStaffService::$max_add_staff_count]), ErrCode::$VALIDATE_ERROR);
        }

        // 去重处理
        $staff_ids   = array_values(array_unique($params['staff_ids']));
        $leader_id   = $params['leader_id'];
        $activity_id = $params['activity_id'];

        // 验证活动是否为进行中的活动
        $activity_info = ActivityStaffService::getInstance()->validationActivity($params);

        // 验证变更后的kpi制定人是否合法。
        // [1] 工号是否存在
        $leader_list = $this->getStaffInfoByParams([
            "staff_info_id" => $leader_id,
            "is_sub_staff"  => StaffInfoEnums::IS_SUB_STAFF_NO,
            "formal"        => [StaffInfoEnums::FORMAL_IN, StaffInfoEnums::FORMAL_TRAINEE],
        ], ['id', 'staff_info_id', 'name', 'state']);
        if (empty($leader_list)) {
            // 提示 该工号不存在，请重新输入
            throw new ValidationException(static::$t->_('kpi_activity_staff_not_exist'), ErrCode::$VALIDATE_ERROR);
        }

        if (in_array($leader_id, $staff_ids)) {
            // 提示 直线上级工号不能与员工工号一样
            throw new ValidationException(static::$t->_('kpi_activity_staff_and_leader_equal'),
                ErrCode::$VALIDATE_ERROR);
        }

        $leader_info = $leader_list[0];
        if ($leader_info['state'] == StaffInfoEnums::STAFF_STATE_LEAVE) {
            // 提示 该员工非在职状态,请重新输入
            throw new ValidationException(static::$t->_('kpi_activity_staff_inactive'), ErrCode::$VALIDATE_ERROR);
        }

        // 查询员工现在的上级信息
        $columns = [
            "kas.id AS id",
            "kas.is_change_leader AS is_change_leader",
            "kas.is_send_leader AS is_send_leader",
            "staff.staff_info_id AS staff_id",
            "staff.name AS staff_name",
            "IF(kas.leader_id = 0,manger.staff_info_id,kas.leader_id ) AS leader_id",
            "IF(kas.leader_id = 0,manger.name,kas.leader_name) AS leader_name",
            "manger.state AS leader_state",
        ];
        // 此处查询时 无需过滤老上级的在职状态
        $activity_staff_list = $this->getKpiLeader($columns, $staff_ids, [$activity_id]);
        if (empty($activity_staff_list)) {
            // 操作操作失败，请重试
            throw new ValidationException(static::$t->_('kpi_activity_staff_operate_error'), ErrCode::$VALIDATE_ERROR);
        }

        // 拿到所有老的上级id
        $all_old_leader = array_values(array_unique(array_column($activity_staff_list, 'leader_id')));
        if (in_array($leader_id, $all_old_leader)) {
            // 变更后的制定人不能与变更前的制定人一样
            throw new ValidationException(static::$t->_('kpi_same_line_superior'), ErrCode::$VALIDATE_ERROR);
        }

        // 删除kpi_leader_msg_send_log表的数据
        $this->delKpiLeaderMsgSendLog([$activity_id], $staff_ids, $all_old_leader);

        // db 操作，向kpi_staff_change_leader_log表插入数据 ，更新kpi_activity_staff表的is_change_leader
        $cur_time        = date("Y-m-d H:i:s", time());
        $is_send_message = false;
        $this->modifyPlanner($activity_staff_list, $leader_info, $params, $userInfo, $cur_time, $is_send_message);

        // 新leader赋予权限
        $this->grantPermissionsToNewLeader($leader_info['staff_info_id'], $cur_time);

        // 向新的leader发送消息
        if ($is_send_message) {
            $this->changeNoticeToLeader($activity_staff_list, $leader_info, $activity_info);
        }

        return [
            'code'    => ErrCode::$SUCCESS,
            'message' => 'success',
            'data'    => '',
        ];
    }

    /**
     * 根据活动id、staff_id、leader_id 删除数据
     * @param array $activity_ids
     * @param array $staff_ids
     * @param array $leader_ids
     * @param int $from
     * @return bool
     * @throws BusinessException
     */
    public function delKpiLeaderMsgSendLog(
        array $activity_ids,
        array $staff_ids,
        array $leader_ids,
        int $from = 0
    ): bool {
        if (empty($activity_ids) || empty($staff_ids) || empty($leader_ids)) {
            return false;
        }

        // hcm变更直线上级时，无法拿到老的上级，删除数据时只能用排除新上级id的方式来处理
        $leader_symbol  = (1 === $from) ? "NOT IN" : "IN";
        $conditions_str = "activity_id IN ({activity_ids:array}) AND staff_id IN ({staff_ids:array}) AND leader_id {$leader_symbol} ({leader_ids:array})";
        $bind_values    = [
            'activity_ids' => $activity_ids,
            'staff_ids'    => $staff_ids,
            'leader_ids'   => $leader_ids,
        ];

        // 执行查询
        $leader_msg_send_log_obj  = KpiLeaderMsgSendLogModel::find([
            "conditions" => $conditions_str,
            'bind'       => $bind_values,
        ]);
        $leader_msg_send_log_list = $leader_msg_send_log_obj->toArray();
        $this->logger->info("delKpiLeaderMsgSendLog:".json_encode([
                "conditions_str" => $conditions_str,
                "bind_values"    => $bind_values,
                "find_result"    => $leader_msg_send_log_list,
            ],JSON_UNESCAPED_UNICODE)
        );

        // 如果不存在 则不走删除逻辑
        if (empty($leader_msg_send_log_list)) {
            return true;
        }

        // 执行删除操作
        $del_ret = $leader_msg_send_log_obj->delete();
        if (false === $del_ret) {
            throw new BusinessException(
                'KPI员工删除失败= '.json_encode([$activity_ids, $staff_ids, $leader_ids, $from],
                    JSON_UNESCAPED_UNICODE).';可能的原因是：'.get_data_object_error_msg($leader_msg_send_log_obj),
                ErrCode::$BUSINESS_ERROR
            );
        }
        return true;
    }

    /**
     * 变更制定人db操作，权限变更等
     * @param array $activity_staff_list
     * @param array $leader_info 变更后leader的信息
     * @param array $params 传递的基本查询参数
     * @param array $userInfo 当前登入的用户信息
     * @param string $cur_time 当前时间
     * @param bool $is_send_message 是否需要向新的上级发送变更通知
     * @return true
     * @throws ValidationException
     */
    public function modifyPlanner(
        array $activity_staff_list,
        array $leader_info,
        array $params,
        array $userInfo,
        string $cur_time,
        bool &$is_send_message
    ): bool {
        // 组织插入的数据
        $log_data = $update_ids = [];
        foreach ($activity_staff_list as $key => $val) {
            $log_data[] = [
                "activity_id"      => $params['activity_id'],
                "staff_id"         => $val['staff_id'],
                "from_leader_id"   => !empty($val['leader_id']) ? $val['leader_id'] : 0,
                "from_leader_name" => !empty($val['leader_name']) ? $val['leader_name'] : "",
                "to_leader_id"     => $leader_info['staff_info_id'],
                "to_leader_name"   => $leader_info['name'],
                "create_id"        => $userInfo['id'],
                "create_name"      => $userInfo['name'],
                "created_at"       => $cur_time,
                "updated_at"       => $cur_time,
            ];

            // 如果向该员工的上级发送过活动开启的通知，需要向新的上级发送变更通知。
            if ($val['is_send_leader'] == KpiActivityStaffEnums::IS_SEND_LEADER_YES) {
                $is_send_message = true;
            }
            $update_ids[] = $val["id"];
        }
        // db 操作
        // [1] 向kpi_staff_change_leader_log表插入变更记录
        $db = $this->getDI()->get('db_backyard');
        $db->begin();
        $kpi_staff_change_leader_model = new KpiStaffChangeLeaderLogModel();
        $add_log_result                = $kpi_staff_change_leader_model->batch_insert($log_data, 'db_backyard');
        if ($add_log_result === false) {
            $db->rollBack();
            $this->logger->info("插入 by.kpi_staff_change_leader_log 表失败, parmas:".json_encode($log_data,JSON_UNESCAPED_UNICODE));
            throw new ValidationException("DB operation failed", ErrCode::$VALIDATE_ERROR);
        }

        // [2] 更新kpi_activity_staff 表的 is_change_leader 状态为2、更新上级的信息
        $ids        = implode(',', $update_ids);
        $update_ret = $db->updateAsDict(
            (new KpiActivityStaffModel())->getSource(),
            [
                "is_change_leader" => KpiActivityStaffEnums::IS_CHANGE_LEADER_YES,
                "leader_id"        => $leader_info['staff_info_id'],
                "leader_name"      => $leader_info['name'],
                "updated_at"       => $cur_time,
            ],
            ["conditions" => "activity_id = ".$params['activity_id']." AND id IN ($ids)"]
        );
        if ($update_ret === false) {
            $db->rollBack();
            $this->logger->info("更新 by.kpi_activity_staff 表失败, parmas:".json_encode([
                    'activity_id' => $params['activity_id'],
                    'id'          => $ids,
                ],JSON_UNESCAPED_UNICODE));
            throw new ValidationException("DB operation failed", ErrCode::$VALIDATE_ERROR);
        }
        $db->commit();
        return true;
    }

    /**
     * 变更kpi制定人时 为新的leader赋权限
     * @param $leader_id
     * @param $cur_time
     * @return bool
     * @throws ValidationException
     */
    public function grantPermissionsToNewLeader($leader_id, $cur_time): bool
    {
        // [3] 回收 老kpi制定人的菜单权限 老权限可先不回收 脚本处理
        // 回收时需要判断名下还是有参与kpi的员工

        // [4] 为变更后的kpi制定人赋予新的权限
        // [4.1] 先判断变更后的kpi制定人是否有相关权限
        $staff_permission_obj = \App\Modules\User\Models\StaffPermissionModel::findFirst([
            "conditions" => "staff_id = :staff_info_id:",
            "bind"       => [
                'staff_info_id' => $leader_id, //$leader_info['staff_info_id'],
            ],
        ]);
        $staff_permission     = [];
        if (is_object($staff_permission_obj) && !empty($staff_permission_obj->toArray())) {
            $staff_permission = $staff_permission_obj->toArray();
        }

        if (!empty($staff_permission)) {
            $permission_ids = explode(',', $staff_permission['permission_ids']);
            // 新的目标制定人不存在 【下级KPI制定】 菜单权限时，则需要追加权限
            if (!in_array(PermissionService::SUBORDINATE_KPI_FORMULATE_MENU, $permission_ids)) {
                $new_permission_ids = $staff_permission['permission_ids'].','.implode(',',
                        PermissionService::SUBORDINATE_KPI_FORMULATE);
                // 更新
                $staff_permission_obj->permission_ids  = $new_permission_ids;
                $staff_permission_obj->last_updated_at = $cur_time;
                $result                                = $staff_permission_obj->save();
                if (false === $result) {
                    $this->logger->info("batchModifyPlanner 更新 oa.staff_permission 表失败, parmas:".json_encode([
                            'permission_ids' => $new_permission_ids,
                        ],JSON_UNESCAPED_UNICODE));
                    throw new ValidationException("DB operation failed", ErrCode::$VALIDATE_ERROR);
                }
            }
        } else {
            // 如果新的kpi制定人 没有任何权限时则进行数据插入
            $staff_permission_obj                  = new \App\Modules\User\Models\StaffPermissionModel();
            $staff_permission_obj->staff_id        = $leader_id; //$leader_info['staff_info_id'];
            $staff_permission_obj->permission_ids  = implode(',', PermissionService::SUBORDINATE_KPI_FORMULATE);
            $staff_permission_obj->is_granted      = 1;
            $staff_permission_obj->last_updated_at = $cur_time;
            $save_res                              = $staff_permission_obj->save();
            if (false === $save_res) {
                $this->logger->info("batchModifyPlanner 插入 oa.staff_permission 表失败, parmas:".json_encode([
                        'permission_ids' => implode(',', PermissionService::SUBORDINATE_KPI_FORMULATE),
                    ],JSON_UNESCAPED_UNICODE));
                throw new ValidationException("DB operation failed", ErrCode::$VALIDATE_ERROR);
            }
        }
        return true;
    }

    /**
     * 变更kpi制定人时 向新的上级发送变更通知
     * @param array $activity_staff_list
     * @param array $leader_info
     * @param array $activity_info
     * @return bool
     */
    public function changeNoticeToLeader(array $activity_staff_list, array $leader_info, array $activity_info): bool
    {
        // [5] 向新的上级发送变更通知
        // [5.1] 判断活动是否已经发起，如果已经发起了则向新的上级发送变更通知 否则不发送
        // 通知消息内容中最多拼接前20个
        $max_count = 20;
        $is_append = false;
        if (count($activity_staff_list) > $max_count) {
            $is_append           = true;
            $activity_staff_list = array_slice($activity_staff_list, 0, $max_count, true);
        }
        $staff_list = [];
        foreach ($activity_staff_list as $key => $val) {
            $staff_list[] = "{".$val['staff_id']."（".$val['staff_name']."）"."}";
        }

        $staff_str = implode('、', $staff_list);
        if ($is_append) {
            $staff_str .= '...';
        }

        //存在要发送的直接上级组
        $push_service = new PushService();
        $msg_title    = static::$t->_(KpiActivityStaffEnums::REMIND_LEADER_CHANGE_TITLE, [
            'years'  => $activity_info['years'],
            "period" => static::$t->_(KpiActivityEnums::$kpi_activity_period[$activity_info['period']]),
        ]);
        $msg_content  = static::$t->_(KpiActivityStaffEnums::REMIND_LEADER_CHANGE_CONTENT, [
            "staff_info" => $staff_str,
        ]);
        $msg_content  = KpiActivityStaffEnums::MSG_STYLE_BEGIN.$msg_content.KpiActivityStaffEnums::MSG_STYLE_END;
        $push_service->sendMessage([['id' => $leader_info['staff_info_id']]], $msg_title, $msg_content,
            KpiActivityStaffEnums::MESSAGE_CATEGORY_KPI_LEADER);
        return true;
    }

    /**
     * hris 回调：hris 有直线上级变更时需要通知oa
     * @param array $params
     * @return array
     * @throws BusinessException
     * <AUTHOR>
     */
    public function changeLeader(array $params)
    {
        // 记录hris传入的参数
        $this->logger->info('HRIS 调用上级变更接口 changeLeader params:'.json_encode($params, JSON_UNESCAPED_UNICODE));
        // 返回的格式
        $data = [
            'code'    => ErrCode::$SUCCESS,
            'message' => 'success',
            'data'    => '',
        ];

        // 解析传递过来的参数格式
        $staff_ids = $leader_ids = [];
        foreach ($params["change_leader"] as $leader_id => $staff_arrays) {
            // 所有的leader_id
            $leader_ids[] = $leader_id;
            // 所有变更过直线上级的staff_info_id
            $staff_ids = array_merge($staff_ids, $staff_arrays);
        }

        if (count($staff_ids) > KpiActivityStaffEnums::MAX_CHANGE_MANGER_NUMS) {
            // 此处向前返回 只是为了给调用方记录一个日志，不返回用户，所以此处没有定义翻译key
            $data['code']    = ErrCode::$VALIDATE_ERROR;
            $data['message'] = "Maximum support ".KpiActivityStaffEnums::MAX_CHANGE_MANGER_NUMS;
            return $data;
        }

        // 理论上不存在重复的leader_id 或者 staff_id
        //$leader_ids = array_values(array_unique($leader_ids));
        $staff_ids = array_values(array_unique($staff_ids));

        // [1] 获取正在进行中的活动
        $activity_list = ActivityService::getInstance()->getIngActivityList();
        // 如果不存在进行中的活动 则直接返回
        if (empty($activity_list)) {
            return $data;
        }
        // 拿到所有正在进行中的活动id
        $activity_list = array_column($activity_list, null, 'id');
        $activity_ids  = array_column($activity_list, 'id');

        //[2] 员工id 和 变更后后的直线上级id 和 活动id 链表查询 正在参与kpi活动的员工
        $builder = $this->modelsManager->createBuilder();
        $builder->columns(
            "kas.id,kas.activity_id,
            kas.leader_id,
            kas.leader_name,
            kas.is_send_leader,
            kas.is_change_leader, 
            hsi.staff_info_id,
            hsi.name,
            manger.staff_info_id AS new_leader_id,
            manger.name AS new_leader_name"
        );
        $builder->from(["kas" => KpiActivityStaffModel::class]);
        $builder->leftjoin(HrStaffInfoModel::class, 'kas.staff_id = hsi.staff_info_id', 'hsi');
        $builder->leftjoin(HrStaffInfoModel::class, 'manger.staff_info_id = hsi.manger', 'manger');
        $builder->where("hsi.formal IN ({formal:array}) AND hsi.is_sub_staff = :is_sub_staff:", [
            "formal"       => [StaffInfoEnums::FORMAL_IN, StaffInfoEnums::FORMAL_TRAINEE],
            "is_sub_staff" => StaffInfoEnums::IS_SUB_STAFF_NO,
        ]);
        $builder->andWhere("kas.stage = :kas_stage:", ['kas_stage' => KpiActivityStaffEnums::STAGE_LEADER]);
        $builder->andWhere("kas.staff_id IN ({staff_id:array})", ["staff_id" => $staff_ids]);
        $builder->andwhere("hsi.state IN ({hsi_state:array})",
            ["hsi_state" => [StaffInfoEnums::STAFF_STATE_IN, StaffInfoEnums::STAFF_STATE_STOP]]);
        $builder->andWhere('(kas.leader_id IN ({leader_ids:array})) OR (hsi.manger IN ({leader_ids:array}) and kas.leader_id = :leader_id_default:)',
            ['leader_ids' => $leader_ids, 'leader_id_default' => 0]);
        $builder->andWhere("kas.activity_id IN ({activity_id:array})", ["activity_id" => $activity_ids]);
        $builder->andwhere("manger.state = :manger_state:", ["manger_state" => StaffInfoEnums::STAFF_STATE_IN]);
        $result = $builder->getQuery()->execute()->toArray();

        // 如果为空 则 直接返回
        if (empty($result)) {
            return $data;
        }

        // 组织数据格式
        $no_change_staff_list           = [];   // 未变更过制定人的数据
        $no_change_staff_new_leader_ids = [];   // 需要赋予权限的leader
        $change_leader_list             = [];   // 变更过制定人的数据
        $change_leader_ids              = [];
        foreach ($result as $key => $info) {
            if (0 == $info['leader_id'] && "" == $info['leader_name']) {
                $no_change_staff_list[$info['activity_id']][$info['new_leader_id']][] = $info;
                $no_change_staff_new_leader_ids[]                                     = $info['new_leader_id'];
            } else {
                $change_leader_list[] = $info;
                $change_leader_ids[]  = $info['leader_id'];
            }
        }
        // 去重操作
        $no_change_staff_new_leader_ids = array_values(array_unique($no_change_staff_new_leader_ids));

        // 删除发送日志数据
        $del_activity_ids = array_values(array_unique(array_column($result, 'activity_id')));
        $this->delKpiLeaderMsgSendLog($del_activity_ids, $staff_ids, $leader_ids, 1);

        $cur_time = date('Y-m-d H:i:s');
        /**[3]
         * 处理未变更过制定人的数据，发送变更通知，多个活动时 需要发送多条
         * 不需要向kpi_staff_change_leader_log表插入数据
         */
        if (!empty($no_change_staff_list)) {
            $this->processNoChangeLeader($no_change_staff_list, $activity_list);
        }

        /** [4]
         * 在oa变更过kpi制定人数据的处理，判断leader_id 是否还在职，
         * 如果不在职 则需要回退为新的直线上级，
         * 吧leader_id值为0 leader_name值为空，is_change_leader保持不变
         * 同时向kpi_staff_change_leader_log表插入一条数据，create_id 为 10000 create_namne 为SuperAdmin
         */
        if (!empty($change_leader_ids)) {
            $this->processChangeLeader($change_leader_list, $change_leader_ids, $cur_time);
        }

        // [5] 为新的上级赋值对应的权限
        $staff_permission = StaffPermissionModel::find([
            "conditions" => "staff_id IN ({staff_ids:array})",
            "bind"       => [
                'staff_ids' => $no_change_staff_new_leader_ids,
            ],
        ])->toArray();
        $staff_permission = !empty($staff_permission) ? array_column($staff_permission, null, 'staff_id') : [];

        $db = $this->getDI()->get('db_oa');
        foreach ($no_change_staff_new_leader_ids as $key => $leader) {
            if (!empty($staff_permission[$leader])) {
                $permission_ids = explode(',', $staff_permission[$leader]['permission_ids']);
                // 新的目标制定人不存在 【下级KPI制定】 菜单权限时，则需要追加权限
                if (!in_array(PermissionService::SUBORDINATE_KPI_FORMULATE_MENU, $permission_ids)) {
                    $new_permission_ids = $staff_permission[$leader]['permission_ids'].','.implode(',',
                            PermissionService::SUBORDINATE_KPI_FORMULATE);
                    // 更新
                    $update_ret = $db->updateAsDict(
                        (new StaffPermissionModel())->getSource(),
                        [
                            "permission_ids"  => $new_permission_ids,
                            "last_updated_at" => $cur_time,
                        ],
                        ["conditions" => "id = ".$staff_permission[$leader]["id"]]
                    );
                    if (false === $update_ret) {
                        // 如果插入失败 记录个info日志，即便是失败，也有脚本可以处理
                        $this->logger->info("更新 changeLeader oa.staff_permission 表失败, parmas:".json_encode([
                                'permission_ids' => $new_permission_ids,
                            ],JSON_UNESCAPED_UNICODE));
                    }
                }
            } else {
                // 如果新的kpi制定人 没有任何权限时则进行数据插入
                $insert_staff_permission = [
                    'staff_id'        => $leader,
                    'permission_ids'  => implode(',', PermissionService::SUBORDINATE_KPI_FORMULATE),
                    'is_granted'      => 1,
                    'last_updated_at' => $cur_time,
                ];
                $db->insertAsDict((new StaffPermissionModel())->getSource(), $insert_staff_permission);
                $task_id = $db->lastInsertId();
                if (empty($task_id)) {
                    // 如果插入失败 记录个info日志，即便是失败，也有脚本可以处理
                    $this->logger->info("插入 changeLeader oa.staff_permission 表失败, parmas:".json_encode([
                            'permission_ids' => implode(',', PermissionService::SUBORDINATE_KPI_FORMULATE),
                        ],JSON_UNESCAPED_UNICODE));
                }
            }
        }
        return $data;
    }

    /**
     * oa变更过kpi制定人时 hcm直线上级变更 需要再恢复为直线上级 且记录日志 不发by消息
     * @param $change_leader_list
     * @param $change_leader_ids
     * @return bool
     * <AUTHOR>
     */
    public function processChangeLeader($change_leader_list, $change_leader_ids, $cur_time)
    {
        // 查询出离职的数据
        $builder = $this->modelsManager->createBuilder();
        $builder->columns("hsi.id, hsi.staff_info_id, hsi.name, hsi.state");
        $builder->from(["hsi" => HrStaffInfoModel::class]);
        $builder->where("hsi.formal IN ({formal:array}) ",
            ['formal' => [StaffInfoEnums::FORMAL_IN, StaffInfoEnums::FORMAL_TRAINEE]]);
        $builder->andWhere("hsi.is_sub_staff = :is_sub_staff: ", ['is_sub_staff' => StaffInfoEnums::IS_SUB_STAFF_NO]);
        $builder->andWhere("hsi.staff_info_id IN ({staff_info_id:array}) ",
            ["staff_info_id" => array_values(array_unique($change_leader_ids))]);
        $builder->andWhere("hsi.state = :state:", ["state" => StaffInfoEnums::STAFF_STATE_LEAVE]);
        $change_leader_info_list = $builder->getQuery()->execute()->toArray();
        $change_leader_info_list = array_column($change_leader_info_list, null, 'staff_info_id');

        // 不存在离职数据时则直接返回
        if (empty($change_leader_info_list)) {
            return true;
        }

        // oa变更过kpi制定人后，存在离职数据时
        foreach ($change_leader_list as $key => $change_info) {
            // 如果变更过后的KPI制定人没有离职 则跳过处理
            if (empty($change_leader_info_list[$change_info['leader_id']])) {
                continue;
            }

            $db = $this->getDI()->get('db_backyard');
            $db->begin();
            // 更新kpi_activity_staff表的leader_id 和 leader_name
            $update_ret = $db->updateAsDict(
                (new KpiActivityStaffModel())->getSource(),
                [
                    "leader_id"   => 0,
                    "leader_name" => "",
                    "updated_at"  => $cur_time,
                ],
                ["conditions" => "id = ".$change_info['id']]
            );
            if (false === $update_ret) {
                $db->rollBack();
                continue;
            }

            //向kpi_staff_change_leader_log表插入一条数据，create_id 为 10000 create_namne 为SuperAdmin
            $change_log = [
                'activity_id'      => $change_info['activity_id'],
                'staff_id'         => $change_info['staff_info_id'],
                'from_leader_id'   => $change_info['leader_id'],
                'from_leader_name' => $change_info['leader_name'],
                'to_leader_id'     => $change_info['new_leader_id'],
                'to_leader_name'   => $change_info['new_leader_name'],
                'create_id'        => 10000,
                'create_name'      => "SuperAdmin",
                'created_at'       => $cur_time,
                'updated_at'       => $cur_time,
            ];
            $this->logger->info('OA KPI制定人变更后已离职的数据：'.json_encode($change_log, JSON_UNESCAPED_UNICODE));
            $db->insertAsDict((new KpiStaffChangeLeaderLogModel())->getSource(), $change_log);
            $task_id = $db->lastInsertId();
            if (empty($task_id)) {
                $db->rollBack();
                continue;
            }
            $db->commit();
        }

        return true;
    }

    /**
     * oa未变更过kpi制定人时 hcm直线上级变更 后需要发送by消息
     * @param $no_change_staff_list
     * @param $activity_list
     * @return bool
     * <AUTHOR>
     */
    public function processNoChangeLeader($no_change_staff_list, $activity_list): bool
    {
        //存在要发送的直接上级组
        $push_service = new PushService();
        // [1]活动维度处理数据
        foreach ($no_change_staff_list as $activity_id => $staffs) {
            // 活动信息
            $activity_info = $activity_list[$activity_id] ?? [];
            if (empty($activity_info)) {
                continue;
            }

            // [2] leader 维度处理 一个leader下存在多个员工
            foreach ($staffs as $new_leader_id => $staff) {
                // [3] 组织多个员工的数据
                // 组织发送的消息通知内容
                $send_staff_list = [];
                foreach ($staff as $key => $val) {
                    $send_staff_list[] = "{".$val['staff_info_id']."（".$val['name']."）"."}";
                }

                // 根据语言生成 消息标题 和 消息文本
                $msg_title   = static::$t->_(KpiActivityStaffEnums::REMIND_LEADER_CHANGE_TITLE, [
                    'years'  => $activity_info["years"],
                    "period" => static::$t->_(KpiActivityEnums::$kpi_activity_period[$activity_info['period']]),
                ]);
                $msg_content = static::$t->_(KpiActivityStaffEnums::REMIND_LEADER_CHANGE_CONTENT, [
                    "staff_info" => implode('、', $send_staff_list),
                ]);

                // 消息发送
                $msg_content = KpiActivityStaffEnums::MSG_STYLE_BEGIN.$msg_content.KpiActivityStaffEnums::MSG_STYLE_END;
                $push_service->sendMessage([['id' => $new_leader_id]], $msg_title, $msg_content,
                    KpiActivityStaffEnums::MESSAGE_CATEGORY_KPI_LEADER);
                $this->logger->info('HCM直线上级变更后 发送消息通知：'.json_encode([
                        "msg_title"     => $msg_title,
                        "msg_content"   => $msg_content,
                        "new_leader_id" => $new_leader_id,
                    ], JSON_UNESCAPED_UNICODE));
            }
        }
        return true;
    }

    /**
     * 员工kpi目标 下 参与员工中 头部目标阶段统计
     * @param array $params
     * @return array
     */
    public function getStaffStageNums(array $params): array
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $result  = [
            "all_nums"     => 0,        // 全部
            "stage_leader" => 0,        // 待制定目标
            "stage_staff"  => 0,        // 待确认目标
            "stage_done"   => 0,        // 确认完毕，目标执行
        ];
        try {
            $field   = [
                'kas.stage',
                'count (DISTINCT kas.id) AS nums',
            ];
            $builder = $this->createActivityStaffBuilder($field);
            // 组织查询条件
            // todo 根据产品意思是 查询计数时 永远不跟上stage的变化而变化
            $params['stage'] = KpiActivityStaffEnums::STAGE_DEFAULT;
            $builder         = ActivityStaffService::getInstance()->getActivityStaffCondition($builder, $params);
            $builder->groupBy('kas.stage');

            $list = $builder->getQuery()->execute()->toArray();
            $list = array_column($list, null, 'stage');

            $result['stage_leader'] = (int)$list[KpiActivityStaffEnums::STAGE_LEADER]['nums'];
            $result['stage_staff']  = (int)$list[KpiActivityStaffEnums::STAGE_STAFF]['nums'];
            $result['stage_done']   = (int)$list[KpiActivityStaffEnums::STAGE_DONE]['nums'];
            $result['all_nums']     = $result['stage_leader'] + $result['stage_staff'] + $result['stage_done'];
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage().$e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning(__CLASS__.'function: getStaffStageNums failed:'.$real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $result ?? false,
        ];
    }

    /**
     * 组织查询
     * @param array $field
     * @return mixed
     */
    public function createActivityStaffBuilder(array $field)
    {
        $builder_sql = !empty($field) ? implode(', ', $field) : '*';
        $builder     = $this->modelsManager->createBuilder();
        $builder->columns($builder_sql);
        $builder->from(['kas' => KpiActivityStaffModel::class]);
        $builder->leftJoin(HrStaffInfoModel::class,
            "hsi.staff_info_id = kas.staff_id", 'hsi');
        $builder->leftjoin(HrStaffInfoModel::class, 'manger.staff_info_id = hsi.manger', 'manger');
        $builder->leftJoin(HrStaffItemsModel::class,
            "hsit.staff_info_id = kas.staff_id AND item = 'WORKING_COUNTRY'", 'hsit');
        return $builder;
    }
}