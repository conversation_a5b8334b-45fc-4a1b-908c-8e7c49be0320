<?php

namespace App\Modules\Kpi\Services;

use App\Models\backyard\SysStoreModel;
use App\Models\backyard\SysDepartmentModel;
use App\Models\backyard\HrJobTitleModel;
use App\Library\Enums;

/**
 *
 * 可以封装些kpi业务下共用的的查询方法、或者共用的业务逻辑等。
 *
 */
class CommonService extends BaseService
{
    private static $instance;

    /**
     * 构造函数
     * CommonService constructor.
     */
    public function __construct()
    {
    }

    /**
     * 类实例
     * @return CommonService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * @param array $params 查询条件
     * @param array $colum 需要展示的字段
     * @return array  返回的格式
     * <AUTHOR>
     */
    public function getSysStoreByParams(array $params, array $colum): array
    {
        if (empty($params)) {
            return [];
        }
        $fields = empty($colum) ? "*" : implode(',', $colum);

        $builder = $this->modelsManager->createBuilder();
        $builder->columns($fields);
        $builder->from(['ssm' => SysStoreModel::class]);
        foreach ($params as $field => $val) {
            if (!is_array($val)) {
                $builder->andWhere('ssm.'.$field." = :{$field}:", [$field => $val]);
            }
            if (is_array($val)) {
                $builder->andWhere('ssm.'.$field." IN ({{$field}:array})", [$field => $val]);
            }
        }
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * @param array $params 查询条件
     * @param array $colum 需要展示的额字段
     * @return array        返回格式
     * <AUTHOR>
     */
    public function getDepartmentByParams(array $params, array $colum): array
    {
        if (empty($params)) {
            return [];
        }
        $fields = empty($colum) ? "*" : implode(',', $colum);

        $builder = $this->modelsManager->createBuilder();
        $builder->columns($fields);
        $builder->from(['sdm' => SysDepartmentModel::class]);
        foreach ($params as $field => $val) {
            if (!is_array($val)) {
                $builder->andWhere('sdm.'.$field." = :{$field}:", [$field => $val]);
            }
            if (is_array($val)) {
                $builder->andWhere('sdm.'.$field." IN ({{$field}:array})", [$field => $val]);
            }
        }
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * @param array $params 查询条件
     * @param array $colum  需要展示的字段
     * @return array        返回格式
     * <AUTHOR>
     */
    public function getJobTitleByParams (array $params, array $colum): array
    {
        if (empty($params)) {
            return [];
        }
        $fields = empty($colum) ? "*" : implode(',', $colum);

        $builder = $this->modelsManager->createBuilder();
        $builder->columns($fields);
        $builder->from(['hjt' => HrJobTitleModel::class]);
        foreach ($params as $field => $val) {
            if (!is_array($val)) {
                $builder->andWhere('hjt.'.$field." = :{$field}:", [$field => $val]);
            }
            if (is_array($val)) {
                $builder->andWhere('hjt.'.$field." IN ({{$field}:array})", [$field => $val]);
            }
        }
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 格式化部门名称
     * @param array $departmentInfo
     * @return string
     */
    public function getDepartmentNameView (array $departmentInfo): string
    {
        $departmentName = '';
        if (empty($departmentInfo)) {
            return $departmentName;
        }

        $departmentName = !empty($departmentInfo['name']) ? trim($departmentInfo['name']) : "";
        if (!empty($departmentName) && $departmentInfo['deleted'] == Enums\GlobalEnums::IS_DELETED) {
            $departmentName .= static::$t->_('deleted');
        }
        return $departmentName;
    }
}