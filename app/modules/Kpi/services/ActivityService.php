<?php
namespace App\Modules\Kpi\Services;

use App\Library\Enums\GlobalEnums;
use App\Library\Enums\KpiActivityEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Library\Exception\BusinessException;
use App\Models\backyard\HrJobTitleModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\KpiActivityModel;
use App\Models\backyard\KpiActivityOkrModel;
use App\Models\backyard\KpiValuesModel;
use App\Models\backyard\KpiActivityStaffModel;
use App\Models\backyard\SysDepartmentModel;
use App\Modules\Organization\Services\DepartmentService;

/**
 * KPI活动管理服务类
 * Class ActivityService
 * @package App\Modules\Kpi\Services
 */
class ActivityService extends BaseService
{
    private static $instance;

    /**
     * 构造函数
     * ActivityService constructor.
     */
    public function __construct()
    {
    }

    /**
     * 类实例
     * @return ActivityService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * kpi活动管理-创建
     * @var array
     */
    public static $kpi_activity_create = [
        'name'  => 'Required|StrLenGeLe:1,500|>>>:name error',//活动名称
        'years' => 'Required|IntGeLe:' . KpiActivityEnums::YEAR_VALIDATE_RULE . '|>>>:years error',//年度
        'period' => 'Required|IntIn:' . KpiActivityEnums::PERIOD_FIRST_HALF_YEAR . ',' . KpiActivityEnums::PERIOD_SECOND_HALF_YEAR . ',' . KpiActivityEnums::PERIOD_ALL_YEAR . '|>>>:period error',//周期
        'period_start'  => 'Required|Date|>>>:period start error',//周期开始时间
        'period_end'  => 'Required|Date|>>>:period end error',//周期结束时间
    ];

    /**
     * kpi活动管理-列表-非必要的筛选条件
     * @var array
     */
    public static $not_must_params = [
        'name',
        'years',
        'status',
        'period',
        'created_at_start',
        'created_at_end',
        'job_id',
        'department_id',
        'state',
        'pageNum',
        'pageSize'
    ];

    /**
     * kpi活动管理-列表-搜索条件
     * @var array
     */
    public static $validate_list_search = [
        'name' => 'StrLenLe:500|>>>:name error', //活动名称
        'years' => 'IntGe:2022|>>>:years error',//年度
        'period' => 'IntIn:' . KpiActivityEnums::PERIOD_FIRST_HALF_YEAR . ',' . KpiActivityEnums::PERIOD_SECOND_HALF_YEAR . ',' . KpiActivityEnums::PERIOD_ALL_YEAR . '|>>>:period error',//季度
        'status' => 'IntIn:' . KpiActivityEnums::STATUS_ING . ',' . KpiActivityEnums::STATUS_DONE . '|>>>:status error', //状态(1进行中，2已完成)
        'created_at_start' => 'Date',
        'created_at_end' => 'Date',
        'pageNum' => 'IntGe:1|>>>:page error', //当前页码
        'pageSize' => 'IntGe:1|>>>:page_size error', //每页条数
    ];

    /**
     * kpi活动管理-查看、删除、导入价值观、价值观列表、OKR名单
     * @var array
     */
    public static $validate_kpi_activity_id = [
        'id' => 'Required|IntGe:1|>>>:id error', //活动ID
    ];

    /**
     * kpi活动管理-启用、停用
     * @var array
     */
    public static $validate_kpi_activity_status = [
        'id' => 'Required|IntGe:1|>>>:id error', //活动ID
        'status' => 'Required|IntIn:' . KpiActivityEnums::STATUS_ING . ',' . KpiActivityEnums::STATUS_DONE . '|>>>:status error', //状态(1进行中，2已完成)
    ];

    /**
     * kpi活动管理-编辑
     * @var array
     */
    public static $validate_kpi_edit = [
        'id' => 'Required|IntGe:1|>>>:id error', //活动ID
        'name' => 'Required|StrLenGeLe:1,500|>>>:name error', //活动名称
    ];

    /**
     * KPI活动管理-时间节点设置
     * @var array
     */
    public static $validate_kpi_activity_set_time_node = [
        'id' => 'Required|IntGe:1|>>>:id error', //活动ID
        'leader_on_off' => 'Required|IntIn:0,1|>>>:leader_on_off error',//上级KPI目标制定阶段开关
        'leader_day' => 'Required|IntGeLe:1,99|>>>:leader_day error',//上级KPI目标制定阶段天数
        'staff_on_off' => 'Required|IntIn:0,1|>>>:staff_on_off error',//员工KPI目标确认阶段开关
        'staff_day' => 'Required|IntGeLe:1,99|>>>:staff_day error',//员工KPI目标确认阶段天数
    ];

    /**
     * KPI活动管理-自动提醒设置
     * @var array
     */
    public static $validate_kpi_activity_set_auto_remind = [
        'id' => 'Required|IntGe:1|>>>:id error', //活动ID
        'leader_on_off' => 'Required|IntIn:0,1|>>>:leader_on_off error',//上级KPI目标制定阶段开关
        'leader_time' => 'Required|IntGeLe:0,23|>>>:leader_time error',//上级KPI目标制定阶段时刻
        'staff_on_off' => 'Required|IntIn:0,1|>>>:staff_on_off error',//员工KPI目标确认阶段开关
        'staff_time' => 'Required|IntGeLe:0,23|>>>:staff_time error',//员工KPI目标确认阶段时刻
    ];

    /**
     * KPI活动管理-OKR名单-列表、导出
     * @var array
     */
    public static $validate_okr_list_search = [
        'id' => 'Required|IntGe:1|>>>:id error', //活动ID
        'name' => 'StrLenGe:0|>>>:name error',//工号/姓名/昵称
        'department_id' => 'IntGe:1|>>>:department_id error',//部门ID
        'job_id' => 'IntGe:1|>>>:job_id error', //职位ID
        'state' => 'IntGe:1|>>>:state error',//在职状态
        'pageNum' => 'IntGe:1|>>>:page error', //当前页码
        'pageSize' => 'IntGe:1|>>>:page_size error', //每页条数
    ];

    /**
     * KPI活动管理-OKR名单-批量删除
     * @var array
     */
    public static $validate_okr_batch_del = [
        'id' => 'Required|IntGe:1|>>>:id error', //活动ID
        'staff_ids' => 'Required|Arr|ArrLenGeLe:1,2000|>>>:staff_ids error',//员工工号组
        'staff_ids[*]' => 'Required|IntGe:1|>>>:staff_id error',//员工工号
    ];

    /**
     * KPI活动管理-OKR名单-删除
     * @var array
     */
    public static $validate_okr_del = [
        'id' => 'Required|IntGe:1|>>>:id error', //活动ID
        'staff_id' => 'Required|IntGe:1|>>>:staff_id error',//员工工号
    ];

    /**
     * 活动管理-初始化枚举配置
     * @return array
     */
    public function getDefaultData()
    {
        $code = ErrCode::$SUCCESS;
        $message = '';
        $data = [];
        try {
            $data['year_items'] = $this->getYear();
            $data['period_items'] = $this->getPeriod();
            $data['status_items'] = $this->getStatus();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $data = [];
        }
        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 获取活动年度配置
     * @return array
     */
    public function getYear()
    {
        $item = KpiActivityEnums::$kpi_year;
        $tmp_item = [];
        foreach ($item as $index => $t_key) {
            $tmp_item[] = [
                'id' => intval($t_key),
                'label' => $t_key,
            ];
        }
        return $tmp_item;
    }

    /**
     * 获取活动周期配置
     * @return array
     */
    public function getPeriod()
    {
        $item = KpiActivityEnums::$kpi_activity_period;
        $tmp_item = [];
        foreach ($item as $index => $t_key) {
            $tmp_item[] = [
                'id' => $index,
                'label' => self::$t[$t_key],
            ];
        }
        return $tmp_item;
    }

    /**
     * 获取活动状态配置
     * @return array
     */
    public function getStatus()
    {
        $item = KpiActivityEnums::$kpi_activity_status;
        $tmp_item = [];
        foreach ($item as $index => $t_key) {
            $tmp_item[] = [
                'id' => $index,
                'label' => self::$t[$t_key],
            ];
        }
        return $tmp_item;
    }

    /**
     * 新建kpi活动
     * @param array $data 活动基本信息参数组
     * @param array $excel_file 价值观模版文件
     * @param array $user 当前登陆着用户信息
     * @return array
     */
    public function addActivity(array $data, array $excel_file, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_backyard');
        $db->begin();
        try {
            //检测时间
            $start_at = strtotime($data['period_start']);
            if ($start_at > strtotime($data['period_end'])) {
                throw new ValidationException(static::$t->_('kpi_activity_quarter_end_error'), ErrCode::$VALIDATE_ERROR);
            }
            //是否上传了文件
            if (empty($excel_file)) {
                throw new ValidationException(static::$t->_('kpi_activity_values_file_error'), ErrCode::$VALIDATE_ERROR);
            }
            //检测本年度本周期活动是否已存在
            $exists = KpiActivityModel::findFirst([
                'conditions' => 'years = :years: and period = :period:',
                'columns'    => 'id',
                'bind'       => ['years' => $data['years'], 'period'=>$data['period']],
            ]);
            if (!empty($exists)) {
                throw new ValidationException(static::$t->_('kpi_activity_repeat', ['years'=>$data['years'], 'period'=>static::$t->_(KpiActivityEnums::$kpi_activity_period[$data['period']])]), ErrCode::$VALIDATE_ERROR);
            }
            //若是全年活动创建则判断是否存在本年度上/下半年活动
            if ($data['period'] == KpiActivityEnums::PERIOD_ALL_YEAR) {
                $exists = KpiActivityModel::find([
                    'conditions' => 'years = :years: and period != :period:',
                    'columns'    => 'id, period',
                    'bind'       => ['years' => $data['years'], 'period' => KpiActivityEnums::PERIOD_ALL_YEAR],
                ])->toArray();
                if (!empty($exists)) {
                    $period_arr = array_column($exists, 'period');
                    $period = in_array(KpiActivityEnums::PERIOD_FIRST_HALF_YEAR, $period_arr) ? static::$t->_(KpiActivityEnums::$kpi_activity_period[KpiActivityEnums::PERIOD_FIRST_HALF_YEAR]) : '';
                    $period .= in_array(KpiActivityEnums::PERIOD_SECOND_HALF_YEAR, $period_arr) ? static::$t->_(KpiActivityEnums::$kpi_activity_period[KpiActivityEnums::PERIOD_SECOND_HALF_YEAR]) : '';
                    throw new ValidationException(static::$t->_('kpi_activity_repeat', ['years' => $data['years'], 'period' => $period]), ErrCode::$VALIDATE_ERROR);
                }
            } else {
                //若非全年活动则判断是否存在本年度全年活动
                $exists = KpiActivityModel::findFirst([
                    'conditions' => 'years = :years: and period = :period:',
                    'columns'    => 'id',
                    'bind'       => ['years' => $data['years'], 'period' => KpiActivityEnums::PERIOD_ALL_YEAR],
                ]);
                if (!empty($exists)) {
                    throw new ValidationException(static::$t->_('kpi_activity_repeat', ['years'=>$data['years'], 'period'=>static::$t->_(KpiActivityEnums::$kpi_activity_period[KpiActivityEnums::PERIOD_ALL_YEAR])]), ErrCode::$VALIDATE_ERROR);
                }
            }

            //检测活动价值观合法性
            $values_service = new ValuesService();
            [$is_error, $excel_data, $excel_header_column] = $values_service->checkKpiValues($excel_file);
            if ($is_error === false) {
                //不存在异常的价值观信息,则可以创建活动
                //kpi活动基本信息
                $kpi_activity_data = [
                    'name' => $data['name'],//活动名称
                    'years' => $data['years'],//年度
                    'period' => $data['period'],//周期
                    'period_start' => $data['period_start'],//周期开始时间
                    'period_end' => $data['period_end'],//周期结束时间
                    'config' => json_encode(['time_node' => ['leader' => ['on-off' => 0, 'day' => 10], 'staff' => ['on-off' => 0, 'day' => 10]], 'auto_remind' => ['leader' => ['on-off' => 1, 'time' => 17], 'staff' => ['on-off' => 1, 'time' => 17]]], JSON_UNESCAPED_UNICODE),//
                    'create_id' => $user['id'],//创建人工号
                    'create_name' => $user['name'],//创建人姓名
                    'status' => KpiActivityEnums::STATUS_ING,//活动状态：1进行中
                    'created_at' => date('Y-m-d H:i:s'),//创建时间
                    'updated_at' => date('Y-m-d H:i:s')//更新时间
                ];
                $kpi_activity_model = new KpiActivityModel();
                $add_activity_result = $kpi_activity_model->i_create($kpi_activity_data);
                if ($add_activity_result === false) {
                    throw new BusinessException('kpi活动管理-活动创建失败 = ' . json_encode($kpi_activity_data, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($kpi_activity_model), ErrCode::$BUSINESS_ERROR);
                }

                //活动创建成功,则插入价值观
                $kpi_activity_id = $kpi_activity_model->id; //kpi活动ID
                $kpi_values = [];
                foreach ($excel_data as $item) {
                    $kpi_values[] = [
                        'activity_id' => $kpi_activity_id,//kpi活动ID
                        'sort' => $item[0],//序号
                        'concept' => $item[1],//理念
                        'criterion' => $item[2],//行为准则
                        'score' => $item[3], //得分
                        'created_at' => date('Y-m-d H:i:s'),//创建时间
                        'updated_at' => date('Y-m-d H:i:s')//更新时间
                    ];
                }
                $kpi_values_model = new KpiValuesModel();
                $kpi_values_result = $kpi_values_model->batch_insert($kpi_values, 'db_backyard');
                if ($kpi_values_result === false) {
                    throw new BusinessException('kpi活动管理-活动创建失败 = ' . json_encode($kpi_values, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($kpi_values_model), ErrCode::$BUSINESS_ERROR);
                }
                //事务提交
                $db->commit();
                //活动创建成功页面自动进入活动详情页面
                $result = $kpi_activity_id;
            } else {
                //异常的价值观信息，将异常信息返回
                $new_excel_data = [];
                foreach ($excel_data as $item) {
                    $new_excel_data[] = array_values($item);
                }
                $result = $this->exportExcel($excel_header_column, $new_excel_data, "价值观导入失败原因-" . date("Ymd"));
                $code = ErrCode::$BUSINESS_ERROR;
                $message = static::$t->_('values_upload_error');
                $result = $result['data'];
            }
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            //事务回滚
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->warning('kpi_activity-create-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $result ?? ""
        ];
    }

    /**
     * 获取活动列表
     * @param array $condition 查询条件组
     * @return array
     */
    public function getList(array $condition)
    {
        $page_num  = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['pageNum'];
        $page_size = empty($condition['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $condition['pageSize'];
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];
        try {
            $count = $this->getListCount($condition);
            if ($count > 0) {
                $builder = $this->modelsManager->createBuilder();
                $builder->columns([
                    'main.id',
                    'main.name',
                    'main.years',
                    'main.period',
                    'main.period_start',
                    'main.period_end',
                    'main.create_id',
                    'main.create_name',
                    'main.created_at',
                    'main.status',
                    'count(staff.id) as staff_count',
                ]);
                $builder->from(['main' => KpiActivityModel::class]);
                $builder->leftjoin(KpiActivityStaffModel::class, 'staff.activity_id = main.id', 'staff');
                $builder = $this->getCondition($builder, $condition);
                $builder->orderBy('main.created_at DESC');
                $builder->groupBy('main.id');
                $offset    = $page_size * ($page_num - 1);
                $builder->limit($page_size, $offset);
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->formatList($items);
            }
            $data['items']                     = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-kpi_activity-list-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * kpi活动列表-记录数
     * @param array $condition 查询条件组
     * @return mixed
     */
    public function getListCount(array $condition)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(main.id) as total');
        $builder->from(['main' => KpiActivityModel::class]);
        $builder = $this->getCondition($builder, $condition);
        return intval($builder->getQuery()->getSingleResult()->total);
    }

    /**
     * 组装列表搜索条件
     * @param object $builder 查询器对象
     * @param array $condition['name'=>'活动名称','years'=>'年度','period'=>'周期','status'=>'状态(1进行中，2已完成)','created_at_start'=>'创建时间起始','created_at_end'=>'创建时间截止']查询条件组
     * @return mixed
     */
    private function getCondition(object $builder, array $condition)
    {
        $name = $condition['name'] ?? '';//活动名称
        $years = $condition['years'] ?? 0;//年度
        $period = $condition['period'] ?? 0;//周期
        $status = $condition['status'] ?? 0;//状态(1进行中，2已完成)
        $create_start = $condition['created_at_start'] ?? '';//创建时间起始
        $create_end = $condition['created_at_end'] ?? '';//创建时间结束
        //活动名称
        if (!empty($name)) {
            $builder->andWhere('main.name LIKE :name:', ['name' => "%{$name}%"]);
        }
        //年度
        if (!empty($years)) {
            $builder->andWhere('main.years = :years:', ['years' => $years]);
        }
        //季度
        if (!empty($period)) {
            $builder->andWhere('main.period = :period:', ['period' => $period]);
        }
        //状态(1进行中，2已完成)
        if ($status) {
            $builder->andWhere('main.status = :status:', ['status' => $status]);
        }
        //活动创建起始与结束校验
        if (!empty($create_start) && !empty($create_end)) {
            $builder->betweenWhere('main.created_at', $create_start . ' 00:00:00', $create_end . ' 23:59:59');
        }
        return $builder;
    }

    /**
     * 格式化活动列表信息
     * @param array $list 活动列表
     * @return array
     */
    private function formatList(array $list)
    {
        if (!$list) {
            return [];
        }
        foreach ($list as &$values) {
            $values['status_text'] = static::$t->_(KpiActivityEnums::$kpi_activity_status[$values['status']]);
            $values['period_text'] = static::$t->_(KpiActivityEnums::$kpi_activity_period[$values['period']]);
            $values['period_date'] = date('Y/m/d', strtotime($values['period_start'])).'-'.date('Y/m/d', strtotime($values['period_end']));
        }
        return $list;
    }

    /**
     * 根据活动ID获取活动信息
     * @param int $id 活动ID
     * @return mixed
     */
    public function getActivityInfoById(int $id)
    {
        return KpiActivityModel::findFirst([
            'conditions' => 'id = :id:',
            'bind' => ['id' => $id]
        ]);
    }

    /**
     * 检测活动合法性
     * @param int $activity_id 活动ID
     * @return mixed
     * @throws ValidationException
     */
    public function checkActivityInfo(int $activity_id)
    {
        $kpi_activity_info = $this->getActivityInfoById($activity_id);
        if (empty($kpi_activity_info)) {
            throw new ValidationException(static::$t->_('kpi_activity_not_found'), ErrCode::$VALIDATE_ERROR);
        }
        return $kpi_activity_info;
    }

    /**
     * KPI活动管理-删除
     * @param int $activity_id 活动ID
     * @return array
     */
    public function delActivity(int $activity_id)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        //活动删除状态
        $kpi_activity_del_result = false;
        //开启事务
        $db = $this->getDI()->get('db_backyard');
        $db->begin();
        try {
            //获取活动信息
            $kpi_activity_info = $this->checkActivityInfo($activity_id);
            //该KPI活动没有参与KPI员工，才可以删除该KPI活动
            $staff_service = new ActivityStaffService();
            $staff_count = $staff_service->checkActivityHasStaff($activity_id);
            if ($staff_count > 0) {
                throw new ValidationException(static::$t->_('kpi_activity_has_staff'), ErrCode::$VALIDATE_ERROR);
            }
            //删除活动
            $kpi_activity_del_result = $kpi_activity_info->delete();
            if ($kpi_activity_del_result === false) {
                throw new BusinessException('KPI活动管理-删除失败 = ' . json_encode($kpi_activity_info->toArray(), JSON_UNESCAPED_UNICODE) . '; 可能存在的原因：'.get_data_object_error_msg($kpi_activity_info), ErrCode::$BUSINESS_ERROR);
            }
            $kpi_values_del_result = $kpi_activity_info->getValues()->delete();
            if ($kpi_values_del_result === false) {
                throw new BusinessException('KPI活动管理-删除失败 = ' . json_encode($kpi_activity_info->toArray(), JSON_UNESCAPED_UNICODE) . '; 可能存在的原因：'.get_data_object_error_msg($kpi_activity_info), ErrCode::$BUSINESS_ERROR);
            }
            //事物提交
            $db->commit();
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $message;
        }  catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            //事务回滚
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->warning('kpi_activity-del-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $kpi_activity_del_result ? true : false,
        ];
    }

    /**
     * KPI活动管理-启用或完成
     * @param int $activity_id 活动ID
     * @param int $status 1重启，2完成
     * @return array
     */
    public function updateActivityStatus(int $activity_id, int $status)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        //活动重启或完成状态
        $kpi_activity_change_status_result = false;
        try {
            //获取活动信息
            $kpi_activity_info = $this->checkActivityInfo($activity_id);
            //活动已是进行中状态，无需重启
            if ($status == KpiActivityEnums::STATUS_ING && $kpi_activity_info->status == KpiActivityEnums::STATUS_ING) {
                throw new ValidationException(static::$t->_('kpi_activity_status_ing'), ErrCode::$VALIDATE_ERROR);
            }
            //活动已是已结束状态，无需完成
            if ($status == KpiActivityEnums::STATUS_DONE && $kpi_activity_info->status == KpiActivityEnums::STATUS_DONE) {
                throw new ValidationException(static::$t->_('kpi_activity_status_done'), ErrCode::$VALIDATE_ERROR);
            }
            //重启或完成活动
            $kpi_activity_change_status_result = $kpi_activity_info->i_update([
                'status' => $status, //进行中或已结束
                'updated_at' => date('Y-m-d H:i:s')//更新时间
            ]);
            if ($kpi_activity_change_status_result === false) {
                throw new BusinessException('KPI活动管理-重启或完成失败 =' . json_encode($kpi_activity_info->toArray(), JSON_UNESCAPED_UNICODE) . '; 可能存在的原因：'.get_data_object_error_msg($kpi_activity_info), ErrCode::$BUSINESS_ERROR);
            }
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $message;
        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('kpi_activity-change-status-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $kpi_activity_change_status_result ? true : false,
        ];
    }

    /**
     * 导入价值观
     * @param int $activity_id 活动
     * @param array $excel_file 价值观模版文件
     * @return array
     */
    public function importActivityValues(int $activity_id, array $excel_file)
    {
        $result = [];
        try {
            //是否上传了文件
            if (empty($excel_file)) {
                throw new ValidationException(static::$t->_('kpi_activity_values_file_error'), ErrCode::$VALIDATE_ERROR);
            }
            //获取活动信息
            $kpi_activity_info = $this->checkActivityInfo($activity_id);
            //本次KPI活动已结束，不支持导入价值观文件
            if ($kpi_activity_info->status == KpiActivityEnums::STATUS_DONE) {
                throw new ValidationException(static::$t->_('kpi_activity_had_done'), ErrCode::$VALIDATE_ERROR);
            }
            //导入价值观
            $values_service = new ValuesService();
            $kpi_activity_import_values_result = $values_service->importValues($kpi_activity_info, $excel_file);
            $code = $kpi_activity_import_values_result['code'];
            $message = $kpi_activity_import_values_result['message'];
            $result = $kpi_activity_import_values_result['data'];
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $message;
        }  catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('kpi_activity-import-values-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $result,
        ];
    }

    /**
     * 活动基本信息
     * @param int $activity_id 活动ID
     * @return array
     */
    public function getActivityInfo(int $activity_id)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            //获取活动信息
            $kpi_activity_info = $this->checkActivityInfo($activity_id);
            $data = $kpi_activity_info->toArray();
            $data['config'] = json_decode($data['config'], true);
            $data['config']['time_node']['leader']['on-off'] = (int)$data['config']['time_node']['leader']['on-off'];
            $data['config']['time_node']['staff']['on-off'] = (int)$data['config']['time_node']['staff']['on-off'];
            $data['config']['auto_remind']['leader']['on-off'] = (int)$data['config']['auto_remind']['leader']['on-off'];
            $data['config']['auto_remind']['staff']['on-off'] = (int)$data['config']['auto_remind']['staff']['on-off'];
            $data['year_text'] = static::$t->_('kpi_years');
            $data['status_text'] = static::$t->_(KpiActivityEnums::$kpi_activity_status[$data['status']]);
            $data['period_text'] = static::$t->_(KpiActivityEnums::$kpi_activity_period[$data['period']]);
            //删除不需要返回的数据
            unset($data['create_id'], $data['create_name'], $data['created_at'], $data['updated_at']);
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
            $real_message = $message;
        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-kpi_activity-info-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data??[],
        ];
    }

    /**
     * 编辑活动基本信息
     * @param array $data 活动更新的数据
     * @return array
     */
    public function editActivity(array $data)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        //编辑信息的操作结果
        $kpi_activity_edit_result = false;
        try {
            //获取活动信息
            $kpi_activity_info = $this->checkActivityInfo($data['id']);
            //已结束的活动不可编辑
            if ($kpi_activity_info->status == KpiActivityEnums::STATUS_DONE) {
                throw new ValidationException(static::$t->_('kpi_activity_status_edit_done'), ErrCode::$VALIDATE_ERROR);
            }
            //都满足了开始更新
            $kpi_activity_edit_result = $kpi_activity_info->i_update([
                'name' => $data['name'],//活动名称
                'updated_at' => date('Y-m-d H:i:s')//更新时间
            ]);
            if ($kpi_activity_edit_result === false) {
                throw new BusinessException('KPI活动管理-编辑基本信息失败 = ' . json_encode($kpi_activity_info->toArray(), JSON_UNESCAPED_UNICODE) . '; 可能存在的原因：' . get_data_object_error_msg($kpi_activity_info), ErrCode::$BUSINESS_ERROR);
            }
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $message;
        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('edit-kpi_activity-info-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $kpi_activity_edit_result,
        ];
    }
    /**
     * 获取所有进行中和已结束的活动
     * @return array
     */
    public function getListAll()
    {
        $code    = ErrCode::$SUCCESS;
        $message = '';
        $list = [];
        try {
            $list = KpiActivityModel::find([
                'columns' => 'id, name, period, years, status',
                'order' => 'id DESC'
            ])->toArray();
            if(!empty($list)) {
                foreach($list as $key => $val) {
                    $list[$key]['years_text'] = $val['years'] . static::$t->_('kpi_years');
                    $list[$key]['period_text'] = static::$t->_(KpiActivityEnums::$kpi_activity_period[$val['period']]);
                    $list[$key]['status_text'] = static ::$t->_(KpiActivityEnums::$kpi_activity_status[$val['status']]);
                }
            }
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $this->logger->warning('get-kpi_activity-all-list-failed:' . $e->getMessage());
        }
        return [
            'code' => $code,
            'message' => $message,
            'data' => $list
        ];
    }

    /**
     * 获取所有正在进行中的KPI活动列表
     * @return mixed
     */
    public function getIngActivityList()
    {
        return KpiActivityModel::find([
            'conditions' => 'status = :status:',
            'bind' => ['status' => KpiActivityEnums::STATUS_ING],
            'columns' => ['id','years','period','config']
        ])->toArray();
    }

    /**
     * kpi活动管理-时间节点设置
     * @param array $data 活动更新的数据
     * @return array
     */
    public function setTimeNode(array $data)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        //编辑信息的操作结果
        $kpi_activity_edit_result = false;
        try {
            //获取活动信息
            $kpi_activity_info = $this->checkActivityInfo($data['id']);
            //已结束的活动不可更改设置
            if ($kpi_activity_info->status == KpiActivityEnums::STATUS_DONE) {
                throw new ValidationException(static::$t->_('kpi_activity_status_edit_done'), ErrCode::$VALIDATE_ERROR);
            }
            //都满足了开始更新
            $config = json_decode($kpi_activity_info->config, true);
            $config['time_node']['leader']['on-off'] = $data['leader_on_off'];
            $config['time_node']['leader']['day'] = $data['leader_day'];
            $config['time_node']['staff']['on-off'] = $data['staff_on_off'];
            $config['time_node']['staff']['day'] = $data['staff_day'];
            $kpi_activity_edit_result = $kpi_activity_info->i_update([
                'config' => json_encode($config, JSON_UNESCAPED_UNICODE),//活动配置
                'updated_at' => date('Y-m-d H:i:s')//更新时间
            ]);
            if ($kpi_activity_edit_result === false) {
                throw new BusinessException('KPI活动管理-时间节点设置失败 = ' . json_encode($kpi_activity_info->toArray() , JSON_UNESCAPED_UNICODE) . '; 可能存在的原因：' . get_data_object_error_msg($kpi_activity_info), ErrCode::$BUSINESS_ERROR);
            }
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $message;
        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('kpi_activity-set-time-node-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $kpi_activity_edit_result,
        ];
    }

    /**
     * kpi活动管理-自动提醒设置
     * @param array $data 活动更新的数据
     * @return array
     */
    public function setAutoRemind(array $data)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        //编辑信息的操作结果
        $kpi_activity_edit_result = false;
        try {
            //获取活动信息
            $kpi_activity_info = $this->checkActivityInfo($data['id']);
            //已结束的活动不可编辑
            if ($kpi_activity_info->status == KpiActivityEnums::STATUS_DONE) {
                throw new ValidationException(static::$t->_('kpi_activity_status_edit_done'), ErrCode::$VALIDATE_ERROR);
            }
            //都满足了开始更新
            $config = json_decode($kpi_activity_info->config, true);
            $config['auto_remind']['leader']['on-off'] = $data['leader_on_off'];
            $config['auto_remind']['leader']['time'] = $data['leader_time'];
            $config['auto_remind']['staff']['on-off'] = $data['staff_on_off'];
            $config['auto_remind']['staff']['time'] = $data['staff_time'];
            $kpi_activity_edit_result = $kpi_activity_info->i_update([
                'config' => json_encode($config, JSON_UNESCAPED_UNICODE),//活动配置
                'updated_at' => date('Y-m-d H:i:s')//更新时间
            ]);
            if ($kpi_activity_edit_result === false) {
                throw new BusinessException('KPI活动管理-自动提醒设置失败 = ' . json_encode($kpi_activity_info->toArray(), JSON_UNESCAPED_UNICODE) . '; 可能存在的原因：' . get_data_object_error_msg($kpi_activity_info), ErrCode::$BUSINESS_ERROR);
            }
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $message;
        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('kpi_activity-set-auto-remind-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $kpi_activity_edit_result,
        ];
    }

    /**
     * KPI活动管理-OKR名单-列表
     * @param array $condition 请求参数组
     * @return array
     */
    public function okrList(array $condition)
    {
        $page_num  = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['pageNum'];
        $page_size = empty($condition['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $condition['pageSize'];
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];
        try {
            $count = $this->getOkrListCount($condition);
            if ($count > 0) {
                $builder = $this->modelsManager->createBuilder();
                $builder->columns([
                    'main.staff_id',
                    'staff.name',
                    'staff.nick_name',
                    'staff.node_department_id',
                    'department.name as node_department_name',
                    'department.deleted as node_department_deleted',
                    'staff.state',
                    'staff.wait_leave_state',
                    'staff.job_title',
                    'job.job_name'
                ]);
                $builder->from(['main' => KpiActivityOkrModel::class]);
                $builder->leftjoin(HrStaffInfoModel::class, 'staff.staff_info_id = main.staff_id', 'staff');
                $builder->leftjoin(SysDepartmentModel::class, 'CAST(staff.node_department_id as CHAR) = department.id', 'department');
                $builder->leftjoin(HrJobTitleModel::class, 'cast(staff.job_title AS UNSIGNED) = job.id', 'job');
                $builder = $this->getOkrCondition($builder, $condition);
                $builder->orderBy('main.created_at DESC');
                $offset    = $page_size * ($page_num - 1);
                $builder->limit($page_size, $offset);
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->formatOkrList($items);
            }
            $data['items']                     = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-kpi_activity-okr-list-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * kpi活动列表-记录数
     * @param array $condition 查询条件组
     * @return mixed
     */
    public function getOkrListCount(array $condition)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(main.id) as total');
        $builder->from(['main' => KpiActivityOkrModel::class]);
        $builder->leftjoin(HrStaffInfoModel::class, 'staff.staff_info_id = main.staff_id', 'staff');
        $builder->leftjoin(SysDepartmentModel::class, 'CAST(staff.node_department_id as CHAR) = department.id', 'department');
        $builder->leftjoin(HrJobTitleModel::class, 'cast(staff.job_title AS UNSIGNED) = job.id', 'job');
        $builder = $this->getOkrCondition($builder, $condition);
        return intval($builder->getQuery()->getSingleResult()->total);
    }

    /**
     * 组装列表搜索条件
     * @param object $builder 查询器对象
     * @param array $condition['name'=>'工号/名称/昵称','department_id'=>'部门','job_id'=>'职位','state'=>'在职状态']查询条件组
     * @return mixed
     */
    private function getOkrCondition(object $builder, array $condition)
    {
        $name = $condition['name'] ?? '';//工号/名称/昵称
        $node_department_id = $condition['department_id'] ?? 0;//部门
        $job_id = $condition['job_id'] ?? 0;//职位
        $state = $condition['state'] ?? 0;//在职状态
        $builder->where('main.activity_id = :activity_id:', ['activity_id' => $condition['id']]);
        //工号
        if (!empty($name)) {
            $builder->andWhere('main.staff_id  LIKE :name: OR staff.name LIKE :name: OR staff.nick_name  LIKE :name:', ['name' => '%' . $name . '%']);
        }
        //部门
        if (!empty($node_department_id)) {
            //按照部门查找,需要找该部门以及下属子部门的所有信息
            $department_service = new DepartmentService();
            $department_ids = $department_service->getChildrenListByDepartmentIdV2($node_department_id, true);
            array_push($department_ids, $node_department_id);
            $builder->inWhere('department.id', $department_ids);
        }
        //职位
        if (!empty($job_id)) {
            $builder->andWhere('job.id = :job_id:', ['job_id' => $job_id]);
        }
        //在职状态
        if (!empty($state)) {
            //在职状态搜索筛选待
            if ($state == StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE) {
                //待离职
                $builder->andWhere('staff.wait_leave_state = :wait_leave_state:', ['wait_leave_state' => StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES]);
            } else if ($state == StaffInfoEnums::STAFF_STATE_IN) {
                //在职
                $builder->andWhere('staff.state = :state: and staff.wait_leave_state = :wait_leave_state:', ['state' => $state, 'wait_leave_state' => StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_NO]);
            } else {
                //离职、停职
                $builder->andWhere('staff.state = :state:', ['state' => $state]);
            }
        }
        return $builder;
    }

    /**
     * 格式化OKR名单-列表
     * @param array $list 活动列表
     * @return array
     */
    private function formatOkrList(array $list)
    {
        if (!$list) {
            return [];
        }
        foreach ($list as &$values) {
            $state = ($values['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES) ? StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE : $values['state'];
            $values['state_text'] = isset(StaffInfoEnums::$staff_state[$state]) ? static::$t[StaffInfoEnums::$staff_state[$state]] : '';
            $values['node_department_name'] = (CommonService::getInstance())->getDepartmentNameView([
                'name' => $values['node_department_name'],
                'deleted' => $values['node_department_deleted']
            ]);
        }
        return $list;
    }

    /**
     * OKR名单-批量导入
     * @param int $activity_id 活动ID
     * @param array $excel_file OKR名单模版文件
     * @param array $user 当前登陆者信息组
     * @return array
     * @throws BusinessException
     */
    public function okrImport(int $activity_id, array $excel_file, array $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = '';
        $data = [];
        $excel_data = [];
        $excel_header_column = [];
        $insert_staff = [];
        try {
            //是否上传了文件
            if (empty($excel_file)) {
                throw new ValidationException(static::$t->_('kpi_activity_okr_file_error'), ErrCode::$VALIDATE_ERROR);
            }
            //获取活动信息
            $kpi_activity_info = $this->checkActivityInfo($activity_id);
            //本次KPI活动已结束，不支持导入OKR名单
            if ($kpi_activity_info->status == KpiActivityEnums::STATUS_DONE) {
                throw new ValidationException(static::$t->_('kpi_activity_done'), ErrCode::$VALIDATE_ERROR);
            }
            //导入OKR名单
            [$excel_data, $excel_header_column, $insert_staff] = $this->checkKpiOkr($activity_id, $excel_file, $user);
            if (!empty($insert_staff)) {
                $kpi_activity_okr = new KpiActivityOkrModel();
                $okr_insert_ret = $kpi_activity_okr->batch_insert($insert_staff, 'db_backyard');
                if ($okr_insert_ret === false)  {
                   throw new BusinessException('活动管理-OKR名单-批量导入-失败 = ' . json_encode($insert_staff, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($kpi_activity_okr), ErrCode::$BUSINESS_ERROR);
                }
            }
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $message;
        }  catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('kpi_activity-import-okr-failed:' . $real_message);
        }
        if ($code == ErrCode::$SUCCESS) {
            $new_excel_data = [];
            //非验证类或系统错误的提示，需要生成下载模版
            foreach ($excel_data as $item) {
                $new_excel_data[] = array_values($item);
            }
            $result = $this->exportExcel($excel_header_column, $new_excel_data, static::$t->_('kpi_okr_import_title') . '-' . date("Ymd"));
            $all_num = count($excel_data);
            //成功数目
            $success_num = count($insert_staff);
            $data = [
                'url' => $result['data'],
                'all_num' => $all_num,
                'success_num' => $success_num,
                'failed_num' => intval(bcsub($all_num, $success_num))
            ];
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 检测OKR名单数据合理性
     * @param int $activity_id 活动ID
     * @param array $excel_file excel文件内容
     * @param array $user 当前登陆者信息组
     * @return array
     * @throws ValidationException
     */
    private function checkKpiOkr(int $activity_id, array $excel_file, array $user)
    {
        //符合插入条件的员工组
        $insert_staff = [];
        $time = date('Y-m-d H:i:s');
        try {
            $config = ['path' => ''];
            $excel = new \Vtiful\Kernel\Excel($config);
            $excel_data = $excel->openFile($excel_file[0]->getTempName())
                ->openSheet()
                ->setType([
                    \Vtiful\Kernel\Excel::TYPE_INT,
                    \Vtiful\Kernel\Excel::TYPE_STRING,
                    \Vtiful\Kernel\Excel::TYPE_STRING,
                    \Vtiful\Kernel\Excel::TYPE_INT,
                    \Vtiful\Kernel\Excel::TYPE_STRING,
                ])
                ->getSheetData();
        } catch (\Exception $e) {
            throw new ValidationException(static::$t->_('kpi_activity_okr_file_error'), ErrCode::$VALIDATE_ERROR);
        }
        //弹出excel标题一行信息
        $excel_header_column = array_shift($excel_data);
        $excel_header_column[] = static::$t->_('kpi_import_result'); // 追加导入结果标题
        if (empty($excel_data)) {
            throw new ValidationException(static::$t->_('kpi_activity_okr_error'), ErrCode::$VALIDATE_ERROR);
        }

        //获取用户信息
        $staff_ids = array_values(array_unique(array_filter(array_column($excel_data, 0))));
        $staff_list = HrStaffInfoModel::find([
           'columns' => 'staff_info_id, name, is_sub_staff, formal',
            'conditions' => 'staff_info_id in ({staff_ids:array})',
            'bind' => ['staff_ids' => $staff_ids]
        ])->toArray();
        $staff_list = array_column($staff_list, null, 'staff_info_id');

        //获取活动的员工
        $activity_staff = KpiActivityStaffModel::find([
            'columns' => 'staff_id',
            'conditions' => 'activity_id  = :activity_id:',
            'bind' => ['activity_id' => $activity_id]
        ])->toArray();
        $activity_staff = array_column($activity_staff,'staff_id');
        //获取活动中已配置的OKR名单
        $activity_okr_staff = KpiActivityOkrModel::find([
            'columns' => 'staff_id',
            'conditions' => 'activity_id  = :activity_id:',
            'bind' => ['activity_id' => $activity_id]
        ])->toArray();
        $activity_okr_staff = array_column($activity_okr_staff,'staff_id');

        //验证每项元素是否符合标准
        foreach ($excel_data as $key => &$item) {
            $error_msg = [];
            //工号
            $staff_id = trim($item[0]);
            //姓名
            $staff_name = trim($item[1]);
            //工号必填
            if (empty($staff_id)) {
                $error_msg[] = static::$t->_('kpi_activity_import_staff_error.1');
            } else if (empty($staff_name)) {
                //姓名必填
                $error_msg[] = static::$t->_('kpi_activity_import_staff_error.2');
            } else if (!isset($staff_list[$staff_id]) || empty($staff_list[$staff_id])) {
                //员工不存在
                $error_msg[] = static::$t->_('kpi_activity_import_staff_error.3');
            } else if ($staff_name != trim($staff_list[$staff_id]['name'])) {
                //工号与姓名不一致
                $error_msg[] = static::$t->_('kpi_activity_import_staff_error.4');
            } else if ($staff_list[$staff_id]['is_sub_staff'] != StaffInfoEnums::IS_SUB_STAFF_NO) {
                //是子账号
                $error_msg[] = static::$t->_('kpi_activity_import_staff_error.8');
            } else if (!in_array($staff_list[$staff_id]['formal'], [StaffInfoEnums::FORMAL_IN, StaffInfoEnums::FORMAL_TRAINEE])) {
                //非编制或者实习生
                $error_msg[] = static::$t->_('kpi_activity_import_staff_error.9');
            } else if (in_array($staff_id, $activity_staff)) {
                //已经参与KPI
                $error_msg[] = static::$t->_('kpi_activity_import_staff_error.10');
            } else if (in_array($staff_id, $activity_okr_staff)) {
                //已经在OKR名单中
                $error_msg[] = static::$t->_('kpi_activity_import_staff_error.11');
            } else {
                array_push($activity_okr_staff, $staff_id);
            }

            //存在错误信息，则要赋予备注信息
            if ($error_msg) {
                //存在错误信息，则要赋予备注信息
                $item[2] = implode(";", $error_msg);
            } else {
                $insert_staff[] = [
                    'activity_id' => $activity_id,
                    'staff_id' => $staff_id,
                    'create_id' => $user['id'],
                    'create_name' => $user['name'],//创建人姓名
                    'created_at' => $time,
                    'updated_at' => $time
                ];
            }
        }
        return [$excel_data, $excel_header_column, $insert_staff];
    }

    /**
     * KPI活动管理-OKR名单-批量删除
     * @param array $data 请求参数组
     * @return array
     */
    public function okrBatchDel(array $data)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $kpi_activity_okr_del_ret = false;
        try {
            //获取活动信息
            $kpi_activity_info = $this->checkActivityInfo($data['id']);
            //已结束的活动不可操作
            if ($kpi_activity_info->status == KpiActivityEnums::STATUS_DONE) {
                throw new ValidationException(static::$t->_('kpi_activity_done'), ErrCode::$VALIDATE_ERROR);
            }

            //获取活动中已配置的OKR名单
            $kpi_activity_okr = KpiActivityOkrModel::find([
                'conditions' => 'activity_id  = :activity_id: and staff_id in ({staff_ids:array})',
                'bind' => ['activity_id' => $data['id'], 'staff_ids' => array_values($data['staff_ids'])]
            ]);
            $has_okr_staff = array_column($kpi_activity_okr->toArray(), 'staff_id');
            $diff_staff_ids = array_diff($data['staff_ids'], $has_okr_staff);
            if ($diff_staff_ids) {
                throw new ValidationException(static::$t->_('kpi_okr_not_exist', ['staff_id' => implode(',', $diff_staff_ids)]), ErrCode::$VALIDATE_ERROR);
            }
            $kpi_activity_okr_del_ret = $kpi_activity_okr->delete();
            if ($kpi_activity_okr_del_ret === false) {
                throw new BusinessException('KPI活动管理-OKR名单-批量删除失败 = ' . json_encode($data, JSON_UNESCAPED_UNICODE) . '; 可能存在的原因：' . get_data_object_error_msg($kpi_activity_okr), ErrCode::$BUSINESS_ERROR);
            }
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $message;
        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('kpi_activity-okr-batch-del-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $kpi_activity_okr_del_ret,
        ];
    }

    /**
     * KPI活动管理-OKR名单-删除
     * @param array $data 请求参数组
     * @return array
     */
    public function okrDel(array $data)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $kpi_activity_okr_del_ret = false;
        try {
            //获取活动信息
            $kpi_activity_info = $this->checkActivityInfo($data['id']);
            //已结束的活动不可操作
            if ($kpi_activity_info->status == KpiActivityEnums::STATUS_DONE) {
                throw new ValidationException(static::$t->_('kpi_activity_done'), ErrCode::$VALIDATE_ERROR);
            }

            //获取活动中已配置的OKR名单
            $kpi_activity_okr = KpiActivityOkrModel::findFirst([
                'conditions' => 'activity_id  = :activity_id: and staff_id = :staff_id:',
                'bind' => ['activity_id' => $data['id'], 'staff_id' => $data['staff_id']]
            ]);
            if (empty($kpi_activity_okr)) {
                throw new ValidationException(static::$t->_('kpi_okr_not_exist', ['staff_id' => $data['staff_id']]), ErrCode::$VALIDATE_ERROR);
            }
            $kpi_activity_okr_del_ret = $kpi_activity_okr->delete();
            if ($kpi_activity_okr_del_ret === false) {
                throw new BusinessException('KPI活动管理-OKR名单-删除失败 = ' . json_encode($data , JSON_UNESCAPED_UNICODE) . '; 可能存在的原因：' . get_data_object_error_msg($kpi_activity_okr), ErrCode::$BUSINESS_ERROR);
            }
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $message;
        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('kpi_activity-okr-del-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $kpi_activity_okr_del_ret,
        ];
    }

    /**
     * KPI活动管理-OKR名单-导出
     * @param array $condition 请求参数组
     * @return array
     */
    public function okrExport(array $condition)
    {
        try {
            set_time_limit(0);
            $builder = $this->modelsManager->createBuilder();
            $builder->columns([
                'main.staff_id',
                'staff.name',
                'staff.nick_name',
                'staff.node_department_id',
                'department.name as node_department_name',
                'department.deleted AS department_deleted',
                'staff.state',
                'staff.wait_leave_state',
                'staff.job_title',
                'job.job_name'
            ]);
            $builder->from(['main' => KpiActivityOkrModel::class]);
            $builder->leftjoin(HrStaffInfoModel::class, 'staff.staff_info_id = main.staff_id', 'staff');
            $builder->leftjoin(SysDepartmentModel::class, 'CAST(staff.node_department_id as CHAR) = department.id', 'department');
            $builder->leftjoin(HrJobTitleModel::class, 'cast(staff.job_title AS UNSIGNED) = job.id', 'job');
            $builder = $this->getOkrCondition($builder, $condition);
            $builder->orderBy('main.created_at DESC');
            $items = $builder->getQuery()->execute()->toArray();
            $rows = [];
            if (!empty($items)) {
                foreach ($items as $item) {
                    $state = ($item['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES) ? StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE : $item['state'];
                    $rows[] = [
                        $item['staff_id'],
                        $item['name'],
                        $item['nick_name'],
                        (CommonService::getInstance())->getDepartmentNameView([
                            'name' => $item['node_department_name'],
                            'deleted' => $item['department_deleted']
                        ]),
                        $item['job_name'],
                        isset(StaffInfoEnums::$staff_state[$state]) ? static::$t[StaffInfoEnums::$staff_state[$state]] : ''
                    ];
                }
            }
            $header = [
                static::$t->_('kpi_staff_id'), //工号
                static::$t->_('kpi_staff_name'), //姓名
                static::$t->_('kpi_staff_nick_name'),//昵称
                static::$t->_('kpi_staff_department'), //部门
                static::$t->_('kpi_staff_job'), //职位
                static::$t->_('kpi_staff_state'), //在职状态
            ];
            $file_name         = static::$t->_('kpi_okr_export_title') . '-' . date("Ymd");
            $result            = $this->exportExcel($header, $rows, $file_name);
            $result['message'] = $result['code'] == ErrCode::$SUCCESS ? 'success' : $result['data'];
            return $result;
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = $e->getMessage() . $e->getTraceAsString();
            $this->logger->warning('download-kpi_activity-okr-failed:' . $message . $e->getTraceAsString());
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => [],
        ];
    }
}