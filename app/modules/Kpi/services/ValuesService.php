<?php
namespace App\Modules\Kpi\Services;

use App\Library\Enums\KpiActivityEnums;
use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Library\Exception\BusinessException;
use App\Models\backyard\KpiValuesModel;

/**
 * KPI活动管理价值观服务类
 * Class ValuesService
 * @package App\Modules\Kpi\Services
 */
class ValuesService extends BaseService
{
    private static $instance;

    /**
     * 构造函数
     * ValuesService constructor.
     */
    public function __construct()
    {
    }

    /**
     * 类实例
     * @return ValuesService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 检测价值观数据合理性
     * @param array $excel_file excel文件内容
     * @return array
     * @throws ValidationException
     */
    public function checkKpiValues(array $excel_file)
    {
        //是否有异常元素存在，false不存在错误
        $is_error = false;

        try {
            $config = ['path' => ''];
            $excel = new \Vtiful\Kernel\Excel($config);
            $excel_data = $excel->openFile($excel_file[0]->getTempName())
                ->openSheet()
                ->setType([
                    \Vtiful\Kernel\Excel::TYPE_STRING,
                    \Vtiful\Kernel\Excel::TYPE_STRING,
                    \Vtiful\Kernel\Excel::TYPE_STRING,
                ])
                ->getSheetData();
        } catch (\Exception $e) {
            throw new ValidationException(static::$t->_('kpi_activity_values_file_error'), ErrCode::$VALIDATE_ERROR);
        }
        //弹出excel标题一行信息
        $excel_header_column = array_shift($excel_data);
        array_splice($excel_header_column,-1,1,[static::$t->_('kpi_import_result')]);
      //  $excel_header_column[] = static::$t->_('kpi_import_result');
        if (empty($excel_data)) {
            throw new ValidationException(static::$t->_('kpi_activity_values_error'), ErrCode::$VALIDATE_ERROR);
        }
        if (count($excel_data) > KpiActivityEnums::ACTIVITY_VALUES_MAX) {
            throw new ValidationException(static::$t->_('kpi_activity_values_pass_max', ['max' => KpiActivityEnums::ACTIVITY_VALUES_MAX]), ErrCode::$VALIDATE_ERROR);
        }
        //验证每项元素是否符合标准
        foreach ($excel_data as $key => &$item) {
            $error_msg = "";
            if (empty($item[0])) {
                //序号必填不能为空
                $error_msg .= self::$t['values_upload_error_000'].PHP_EOL;
            } else if (!preg_match(KpiActivityEnums::POSITIVE_INTEGER_RULE, $item[0])) {
                // 序号只能填写正整数
                $error_msg .= self::$t['values_upload_error_001'].PHP_EOL;
                $excel_data[$key] = $item;
            }
            if (empty($item[1])) {
                //理念必填不能为
                $error_msg .= self::$t['values_upload_error_002'].PHP_EOL;
            } else if (mb_strlen($item[1]) > 1000) {
                //理念最大长度限制1000字符
                $error_msg .= self::$t['values_upload_error_003'].PHP_EOL;
            }
            if (empty($item[2])) {
                //行为准则必填不能为空
                $error_msg .= self::$t['values_upload_error_004'].PHP_EOL;
            } else if (mb_strlen($item[2]) > 5000) {
                //行为准则最大长度限制5000字符
                $error_msg .= self::$t['values_upload_error_005'].PHP_EOL;
            }
            if (empty($item[3])) {
                //得分必填不能为空
                $error_msg .= self::$t['values_upload_error_006'].PHP_EOL;
            } else if (!preg_match(KpiActivityEnums::POSITIVE_INTEGER_RULE, $item[3]) || intval($item[3]) > 100) {
                // 得分只能填写正整数
                $error_msg .= self::$t['values_upload_error_007'];
            }
            //存在错误信息，则要赋予备注信息
            if ($error_msg) {
                $is_error = true;
                $item[4] = $error_msg;
            }
        }
        return [$is_error, $excel_data, $excel_header_column];
    }

    /**
     * 导入价值观
     * @param object $kpi_activity_info 活动信息对下
     * @param array $excel_file 价值观模版文件
     * @return array
     * @throws BusinessException
     * @throws ValidationException
     */
    public function importValues(object $kpi_activity_info, array $excel_file)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        list($is_error, $excel_data, $excel_header_column) = $this->checkKpiValues($excel_file);
        if ($is_error === false) {
            //不存在异常，则要将kpi活动原来的价值观删掉，导入新的价值观数据
            $db = $this->getDI()->get('db_backyard');
            $db->begin();
            $kpi_values_del_result = $kpi_activity_info->getValues()->delete();
            //原来的删除失败
            if ($kpi_values_del_result === false) {
                //失败，则事务回滚
                $db->rollback();
                throw new BusinessException('kpi活动管理-导入价值观-删除原来的价值观失败 = ' . json_encode(['kpi_values_data' => $kpi_activity_info->getValues()->toArray()], JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($kpi_activity_info), ErrCode::$BUSINESS_ERROR);
            }
            //原来的删除成功
            $kpi_values = [];
            foreach ($excel_data as $item) {
                $kpi_values[] = [
                    'activity_id' => $kpi_activity_info->id,//kpi活动ID
                    'sort' => $item[0],//序号
                    'concept' => $item[1],//理念
                    'criterion' => $item[2],//行为准则
                    'score' => $item[3], //得分
                    'created_at' => date('Y-m-d H:i:s'),//创建时间
                    'updated_at' => date('Y-m-d H:i:s')//更新时间
                ];
            }
            $kpi_values_model = new KpiValuesModel();
            $kpi_values_add_result = $kpi_values_model->batch_insert($kpi_values, 'db_backyard');
            if ($kpi_values_add_result === false) {
                //删除旧的价值观失败或导入新的价值观失败，事务回滚
                $db->rollback();
                throw new BusinessException('kpi活动管理-导入价值观-插入新的的价值观失败 = ' . json_encode(['kpi_values_data' => $kpi_values], JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($kpi_values_model), ErrCode::$BUSINESS_ERROR);
            }
            //成功，则事物提交
            $db->commit();
        } else {
            //异常的价值观信息，将异常信息返回
            $new_excel_data = [];
            foreach ($excel_data as $item) {
                $new_excel_data[] = array_values($item);
            }
            $result = $this->exportExcel($excel_header_column, $new_excel_data, "价值观 Core Value Behavior ค่านิยมองค์กร failed-" . date("Ymd"));
            $code = ErrCode::$BUSINESS_ERROR;
            $message = static::$t->_('values_upload_error');
            $result = $result['data'];
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $result ?? []
        ];
    }

    /**
     * 获取活动的价值观列表
     * @param int $activity_id 活动ID
     * @return mixed
     */
    public function getValuesList(int $activity_id)
    {
        return KpiValuesModel::find([
            'conditions' => "activity_id = :activity_id:",
            'bind' => [ 'activity_id' => $activity_id],
            'columns' => [ 'concept', 'criterion', 'score', 'sort' ],
            'order' => 'sort, score ASC'
        ])->toArray();
    }

    /**
     * 获取活动的价值观列表格式化后的
     * @param int $activity_id 活动ID
     * @return array
     */
    public function getActivityValuesList(int $activity_id)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            $list = $this->getValuesList($activity_id);
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-kpi_activity-values-list-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $list ?? [],
        ];
    }
}