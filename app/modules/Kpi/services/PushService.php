<?php
namespace App\Modules\Kpi\Services;

use App\Library\Enums\KpiActivityEnums;
use App\Modules\Common\Services\StaffLangService;
use App\Library\ApiClient;
use App\Modules\Training\Services\TaskService;

/**
 * KPI活动管理push消息类
 * Class PushService
 * @package App\Modules\Kpi\Services
 */
class PushService extends BaseService
{
    private static $instance;

    /**
     * 构造函数
     * PushService constructor.
     */
    public function __construct()
    {
    }

    /**
     * 类实例
     * @return PushService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 返回指定语言包格式的翻译信息
     * @param string $key 翻译key
     * @param array $info 翻译key里的需要替换的内容组
     * @param string $type 特殊标识符号，需要换行传递<br>
     * @param array $lang 展示的需要种类，默认泰、中、英
     * @return string
     * <AUTHOR>
     */
    public function getFormatMsg($key, $info, $type = "", $lang = ['th', 'zh', 'en'])
    {
        $new_info = $info;
        foreach ($lang as $item) {
            $l = self::getTranslation($item);
            if (array_key_exists('period', $info)) {
                $new_info['period'] = $l->_(KpiActivityEnums::$kpi_activity_period[$info['period']]);
            }
            $tran_lang[] = $l->_($key, $new_info);
        }
        return <<<EOF
$tran_lang[0]
$type$tran_lang[1]
$type$tran_lang[2]
EOF;
    }

    /**
     * 发送站内信
     * @param array $staff_users 发送站内信员工工号组
     * @param string $title 标题
     * @param string $content 内容
     * @param int $category 消息类型0普通消息
     * <AUTHOR>
     */
    public function sendMessage(array $staff_users, string $title, $content, int $category = 0)
    {
        try {
            $kit_param['staff_users']        = $staff_users;
            $kit_param['message_title']      = $title;
            $kit_param['message_content']    = $content;
            $kit_param['top']                = 0;
            $kit_param['category']           = $category;
            $staff_info_ids_str              = implode(",", array_column($staff_users, "id"));
            $kit_param['staff_info_ids_str'] = $staff_info_ids_str;

            $bi_rpc = (new ApiClient('hcm_rpc', '', 'add_kit_message'));
            $bi_rpc->setParams([$kit_param]);
            $res       = $bi_rpc->execute();
            $bi_return = $res && isset($res['result']) && $res['result']['code'] == 1;
            $res_msg   = $bi_return ? '成功' : '失败';
            $res_log   = '给员工工号组['.$staff_info_ids_str.']';
            $res_log   .= " 发送KPI活动title：".$title;
            if (is_numeric($content)) {
                $res_log .= ' 发送KPI活动ID：['.$content.']';
            } else {
                $res_log .= ' 发送KPI活动文本内容：['.$content.']';
            }
            $res_log .= ' 站内信消息，发送时间['.date('Y-m-d H:i:s').']，发送结果['.$res_msg.']';
            $this->logger->info($res_log);
        } catch (\Exception $e) {
            $this->logger->warning("给KPI绩效管理直线上级发送站内信消息，失败[异常]".$e->getMessage().PHP_EOL);
        }
    }

    /**
     * 推送PUSH
     * @param int $staff_user 发送push的员工工号
     * @param array $activity_info 活动信息
     * @param string $title_key 标题翻译key
     * @param string $content_key 内容翻译key
     * @param string $cut_off_time 最后限制打卡的时间
     * @param string $message_scheme 跳转地址
     * <AUTHOR>
     */
    public function sendPush(
        int $staff_user,
        array $activity_info,
        string $title_key,
        string $content_key,
        string $message_scheme = "",
        string $cut_off_time = ""
    ) {
        try {
            $activity_setting = json_decode($activity_info['config'],true);
            $info    = [
                'years'     => $activity_info['years'],
                'date'      => !empty($cut_off_time) ? trim($cut_off_time) : date('Y-m-d',strtotime("+{$activity_setting['time_node']['leader']['day']} day")),
            ];

            $t = StaffLangService::getInstance()->getMsgTemplateByUserId($staff_user, "",['kpi']);
            if (php_sapi_name() == "cli") {
                // todo 暂时先保证上线，这儿二期需要优化下，和title的翻译放在一起,优化后注释一起删掉
                //$t = TaskService::getTranslation(get_country_code());
                //$info['period'] = $t->_(KpiActivityEnums::$kpi_activity_period[$activity_info['period']]);
                $info['period'] = $t->_(KpiActivityEnums::$kpi_activity_period[$activity_info['period']]);
            } else {
                $info['period'] = static::$t->_(KpiActivityEnums::$kpi_activity_period[$activity_info['period']]);
            }
            //拼接push接口数据
            $data = [
                "staff_info_id"    => $staff_user,                                                      //员工工号
                "message_title"    => $t->_($title_key, $info),                                         //标题
                "message_content"  => $t->_($content_key, $info),                                       //内容
                "message_scheme"   => $message_scheme,                                                  //地址
                "message_priority" => 1,                                                                // push优先级: 0-普通; 1-优先
            ];
            $data['src'] = 'backyard';
            $ret         = new ApiClient('bi_svc', '', 'push_to_staff');
            $ret->setParams([$data]);
            $res       = $ret->execute();
            $bi_return = $res && isset($res['result']) && $res['result'] == true;
            $res_msg   = $bi_return ? '成功' : '失败';
            $res_log   = '给平台为[backyard]员工工号['.$staff_user.']发送KPI活动ID['.$activity_info['id'].']任务push消息，发送时间['.date('Y-m-d H:i:s').']，发送结果['.$res_msg.']';
            $this->logger->info($res_log);
        } catch (\Exception $e) {
            $this->logger->warning("给KPI绩效管理直线上级发送PUSH消息，失败[异常]，失败[异常]".$e->getMessage().PHP_EOL);
        }
    }
}