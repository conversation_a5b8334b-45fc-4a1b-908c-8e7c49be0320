<?php

namespace App\Modules\Kpi\Services;

use App\Models\backyard\KpiActivityOkrModel;

class KpiActivityOkrService extends BaseService
{
    private static $instance;

    /**
     * 构造函数
     * KpiActivityOkrService constructor.
     */
    public function __construct()
    {
    }

    /**
     * 类实例
     * @return KpiActivityOkrService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * @param array $params
     * @param array $colum
     * @return array
     * <AUTHOR>
     */
    public function getKpiActivityOKRByParams(array $params, array $colum): array
    {
        if (empty($params)) {
            return [];
        }
        $fields  = empty($colum) ? "*" : implode(',', $colum);
        $builder = $this->modelsManager->createBuilder();
        $builder->columns($fields);
        $builder->from(['kao' => KpiActivityOkrModel::class]);
        foreach ($params as $field => $val) {
            if (!is_array($val)) {
                $builder->andWhere('kao.'.$field." = :{$field}:", [$field => $val]);
            }
            if (is_array($val)) {
                $builder->andWhere('kao.'.$field." IN ({{$field}:array})", [$field => $val]);
            }
        }
        return $builder->getQuery()->execute()->toArray();
    }
}