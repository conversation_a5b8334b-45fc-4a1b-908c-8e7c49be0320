<?php
namespace App\Modules\Kpi\Services;

use App\Library\Enums\KpiActivityStaffEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\ErrCode;
use App\Library\JobTransferEnums;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\HrStaffInfoPositionModel;
use App\Models\backyard\HrStaffManageStoreModel;
use App\Models\backyard\KpiActivityStaffModel;
use App\Modules\Common\Models\EnvModel;
use App\Library\Validation\ValidationException;
use App\Repository\DepartmentRepository;
use App\Repository\StaffDepartmentAreasStoreRepository;

/**
 * 鉴权、赋予权限服务层
 * Class PermissionService
 * @package App\Modules\Kpi\Services
 */
class PermissionService extends BaseService
{
    private static $instance;

    // 下级kpi目标
    const SUBORDINATE_KPI_FORMULATE_MENU = 565; // 菜单
    const SUBORDINATE_KPI_FORMULATE = [561,565,564,599,600,602,604,605,606,607,608,609,610,611];

    /**
     * 构造函数
     * PermissionService constructor.
     */
    public function __construct()
    {
    }

    /**
     * 类实例
     * @return PermissionService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 是否是PPM
     * @param array $user 当前登陆者信息组
     * @return bool
     * @throws ValidationException
     */
    public function isPPM($user)
    {
        $ppm_ids = EnvModel::getEnvByCode(KpiActivityStaffEnums::PPM_ENV_CODE);
        $ppm_ids = explode(',', $ppm_ids);
        if(!in_array($user['id'], $ppm_ids)) {
            //您没有PPM权限
            throw new ValidationException(static::$t->_('kpi_audit_no_ppm'), ErrCode::$VALIDATE_ERROR);
        }
        return true;
    }

    /**
     * 是否是目标制定人
     * @param array $user 当前登陆者信息组
     * @return bool
     * @throws ValidationException
     */
    public function isLeader($user)
    {
        //检测是否是KPI目标活动制定人
        $leader_count = KpiActivityStaffModel::count([
            'conditions' => 'leader_id = :leader_id: and stage != :stage_default:',
            'bind' => ['leader_id' => $user['id'], 'stage_default' => KpiActivityStaffEnums::STAGE_DEFAULT]
        ]);
        if ($leader_count > 0) {
            return true;
        } else {
            //非目标制定人，检测是否是上级
            $manager_count = HrStaffInfoModel::count([
                'conditions' => 'manger = :manger: and is_sub_staff = :is_sub_staff: and formal in ({formal:array})',
                'bind' => ['manger' => $user['id'], 'is_sub_staff' => StaffInfoEnums::IS_SUB_STAFF_NO, 'formal' => [StaffInfoEnums::FORMAL_IN, StaffInfoEnums::FORMAL_TRAINEE]]
            ]);
            if ($manager_count > 0) {
                return true;
            } else {
                throw new ValidationException(static::$t->_('kpi_audit_no_leader'), ErrCode::$VALIDATE_ERROR);
            }
        }
    }

    /**
     * 是否是部门负责人
     * @param array $user 当前登陆者信息组
     * @return bool
     * @throws ValidationException
     */
    public function isDepartmentManager($user)
    {
        $is_manger = (new DepartmentRepository())->departmentManagerExists($user['id']);
        if ($is_manger === false) {
            throw new ValidationException(static::$t->_('kpi_audit_no_department'), ErrCode::$VALIDATE_ERROR);
        }
        return $is_manger;
    }

    /**
     * 是否是HRBP
     * @param array $user 当前登陆者信息组
     * @return bool
     * @throws ValidationException
     */
    public function isHrbp($user)
    {
        $is_hrbp = HrStaffInfoPositionModel::findFirst([
            'staff_info_id = :staff_info_id: and position_category = :position_category:',
            'bind' => [
                'staff_info_id' => $user['id'],
                'position_category' => JobTransferEnums::ROLES_HRBP_ID
            ],
        ]);
        if (empty($is_hrbp)) {
            throw new ValidationException(static::$t->_('kpi_audit_no_hrbp'), ErrCode::$VALIDATE_ERROR);
        }
        return true;
    }

    /**
     * 获取用户团队KPI目标权限
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function getTeamPermission($user)
    {
        $code = ErrCode::$SUCCESS;
        $message = '';
        $data = [
            'is_manger' => false,
            'is_hrbp' => false
        ];
        try {
            $data['is_manger'] = $this->isDepartmentManager($user);
            $data['is_hrbp'] = $this->isHrbp($user);
        } catch (\Exception $e) {

        }
        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 直线上级查看则需要鉴权改登陆者是否是操作员工的直线上级
     * @param int $staff_id 员工ID
     * @param int $change_leader_id 员工当前目标制定人
     * @param int $login_user_id 当前登陆者ID
     * @throws ValidationException
     * @return bool
     */
    public function judgeLeaderRange($staff_id, $change_leader_id, $login_user_id)
    {
        if ($change_leader_id != 0 && $change_leader_id != $login_user_id) {
            //变更过目标制定人，非变更过的目标制定人
            throw new ValidationException(static::$t->_('kpi_activity_staff_has_not_permission'), ErrCode::$VALIDATE_ERROR);
        } else if ($change_leader_id == 0) {
            //未变更过目标制定人，检查直线上级是否为当前登陆者
            $hr_staff_info = StaffService::getInstance()->getStaffInfoByStaffId($staff_id);
            if (empty($hr_staff_info) || $hr_staff_info->manger != $login_user_id) {
                throw new ValidationException(static::$t->_('kpi_activity_staff_has_not_permission'), ErrCode::$VALIDATE_ERROR);
            }
        }
        return true;
    }

    /**
     * 团队负责人查看则需要鉴权改登陆者是否在该负责人管辖范围内
     * @param int $staff_id 员工ID
     * @param int $department_manger_id 部门管理者ID
     * @throws ValidationException
     * @return bool
     */
    public function judgeDepartmentRange($staff_id, $department_manger_id)
    {
        $hr_staff_info = StaffService::getInstance()->getStaffInfoByStaffId($staff_id);
        if (empty($hr_staff_info)) {
            throw new ValidationException(static::$t->_('kpi_activity_staff_has_not_permission'), ErrCode::$VALIDATE_ERROR);
        }
        //获取部门负责人管辖的所有部门
        $department_list = (new DepartmentRepository())->getDepartmentAllListByManagerId($department_manger_id);
        $department_ids = array_column($department_list, 'id');
        //被查看员工所属部门是否在团队负责人管辖的部门下
        if (!in_array($hr_staff_info->node_department_id, $department_ids)) {
            throw new ValidationException(static::$t->_('kpi_activity_staff_has_not_permission'), ErrCode::$VALIDATE_ERROR);
        }
        return true;
    }

    /**
     * HRBP查看则需要鉴权改登陆者是否在该HRBP管辖范围内
     * @param int $staff_id 员工ID
     * @param int $hrbp_user_id HRBP用户ID
     * @throws ValidationException
     * @return bool
     */
    public function judgeHrbpAreasRange($staff_id, $hrbp_user_id)
    {
        $hr_staff_info = StaffService::getInstance()->getStaffInfoByStaffId($staff_id);
        if (empty($hr_staff_info)) {
            throw new ValidationException(static::$t->_('kpi_activity_staff_has_not_permission'), ErrCode::$VALIDATE_ERROR);
        }
        //获取HRBP管辖范围
        $areas_range = (new StaffDepartmentAreasStoreRepository())->getStaffManageDepartmentsAreasStores($hrbp_user_id);
        //未设置任何管辖范围
        if (empty($areas_range) || (empty($areas_range['department_ids']) && empty($areas_range['stores_ids']))) {
            throw new ValidationException(static::$t->_('kpi_activity_staff_has_not_permission'), ErrCode::$VALIDATE_ERROR);
        }
        //存在管辖的部门
        if (!empty($areas_range['department_ids'])) {
            if (!in_array($hr_staff_info->node_department_id, $areas_range['department_ids'])) {
                //员工所属部门不在HRBP管辖的部门下，存在网点管辖范围则需要判断网点
                if (!empty($areas_range['stores_ids'])) {
                    //不存在管辖部门，则需要判断是否存在网点管辖范围
                    if (in_array(HrStaffManageStoreModel::$all_id, $areas_range['stores_ids']) && $hr_staff_info->sys_store_id == KpiActivityStaffEnums::STORE_HEADER_OFFICE_ID) {
                        //如果是-2表示所有网点,但非总部才可以
                        throw new ValidationException(static::$t->_('kpi_activity_staff_has_not_permission'), ErrCode::$VALIDATE_ERROR);
                    }

                    if (!in_array(HrStaffManageStoreModel::$all_id, $areas_range['stores_ids']) && !in_array($hr_staff_info->sys_store_id, $areas_range['stores_ids'])) {
                        //非-2所有网点，则判断员工所属网点在不在管辖范围内网点名单中
                        throw new ValidationException(static::$t->_('kpi_activity_staff_has_not_permission'), ErrCode::$VALIDATE_ERROR);
                    }
                } else {
                    //没有设置网点权限
                    throw new ValidationException(static::$t->_('kpi_activity_staff_has_not_permission'), ErrCode::$VALIDATE_ERROR);
                }
            }
        } else if (!empty($areas_range['stores_ids'])) {
            //不存在管辖部门，则需要判断是否存在网点管辖范围
            if (in_array(HrStaffManageStoreModel::$all_id, $areas_range['stores_ids']) && $hr_staff_info->sys_store_id == KpiActivityStaffEnums::STORE_HEADER_OFFICE_ID) {
                //如果是-2表示所有网点,但非总部才可以
                throw new ValidationException(static::$t->_('kpi_activity_staff_has_not_permission'), ErrCode::$VALIDATE_ERROR);
            }

            if (!in_array(HrStaffManageStoreModel::$all_id, $areas_range['stores_ids']) && !in_array($hr_staff_info->sys_store_id, $areas_range['stores_ids'])) {
                //非-2所有网点，则判断员工所属网点在不在管辖范围内网点名单中
                throw new ValidationException(static::$t->_('kpi_activity_staff_has_not_permission'), ErrCode::$VALIDATE_ERROR);
            }
        }
        return true;
    }
}