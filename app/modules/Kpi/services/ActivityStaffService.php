<?php
namespace App\Modules\Kpi\Services;

use App\Library\Enums;
use App\Library\Enums\KpiActivityEnums;
use App\Library\Enums\KpiActivityStaffEnums;
use App\Library\Enums\KpiTemplateEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Models\backyard\HrJobTitleModel;
use App\Models\backyard\KpiLeaderMsgSendLogModel;
use App\Models\backyard\KpiStaffChangeLeaderLogModel;
use App\Models\backyard\SysAttachmentModel;
use App\Models\backyard\SysDepartmentModel;
use App\Models\backyard\SysStoreModel;
use App\Models\backyard\KpiActivityModel;
use App\Models\backyard\KpiActivityStaffModel;
use App\Models\backyard\KpiStaffIndicatorsRelModel;
use App\Models\backyard\KpiTemplateIndicatorsRelModel;
use App\Models\backyard\KpiTemplateModel;
use App\Modules\Organization\Services\DepartmentService;
use App\Library\Exception\BusinessException;
use App\Library\OssHelper;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\HrStaffItemsModel;
use App\Repository\HrStaffRepository;

/**
 * KPI活动管理服务类
 * Class ActivityStaffService
 * @package App\Modules\Kpi\Services
 */
class ActivityStaffService extends BaseService
{
    private static $instance;

    /**
     * 构造函数
     * ActivityStaffService constructor.
     */
    public function __construct()
    {
    }

    /**
     * 类实例
     * @return ActivityStaffService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    public static $max_import_count = 10000; //上传最大条数
    public static $max_add_staff_count = 10000;//2000;  //添加员工最大上限
    public static $min_import_count = 1;    //上传最小条数
    public static $chunk_num = 2000;        //数组分割标准

    /**
     * 下级目标管理-列表-非必要的筛选条件
     * @var array
     */
    public static $not_must_kpi_leader_staff_list = [
        'department_id',
        'job_id',
        'job_title_grade',
        'state',
        'hire_date_start',
        'hire_date_end',
        'pageNum',
        'pageSize'
    ];

    /**
     * 下级目标管理-列表
     * @var array
     */
    public static $validate_kpi_leader_staff_list = [
        'activity_id' => 'Required|IntGe:1|>>>:id error', //活动ID
        'name' => 'StrLenGe:0|>>>:name error',//工号/姓名/昵称
        'department_id' => 'IntGe:1|>>>:department_id error',//部门ID
        'job_id' => 'IntGe:1|>>>:job_id error', //职位ID
        'sys_store_id' => 'StrLenGeLe:0,10|>>>:sys_store_id error',//网点
        'job_title_grade' => 'Arr',//职级
        'job_title_grade[*]' => 'StrLenGe:1',
        'state' => 'Arr',//在职状态
        'state[*]' => 'IntGe:1|>>>:state error',//在职状态
        'hire_date_start' => 'Date',//入职日期起始
        'hire_date_end' => 'Date',//入职日期结束
        'pageNum' => 'IntGe:1|>>>:page error', //当前页码
        'pageSize' => 'IntGe:1|>>>:page_size error', //每页条数
    ];

    /**
     * 员工个人KPI详情页
     * @var array
     */
    public static $validate_kpi_staff_detail = [
        'activity_id' => 'Required|IntGe:1|>>>:activity id error', //活动ID
        'staff_id' => 'Required|IntGe:1|>>>:staff id error', //员工ID
    ];

    /**
     * 员工新建KPI指标
     * @var array
     */
    public static $kpi_staff_indicators_create = [
        'activity_id' => 'Required|IntGe:1|>>>:activity id error', //活动ID
        'staff_id' => 'Required|IntGe:1|>>>:staff id error', //员工ID
        'name'  => 'Required|StrLenGeLe:1,10000|>>>:name error',//KPI目标最多支持10000字
        'importance' => 'Required|IntGeLe:1,100|>>>:importance error',//重要度仅支持输入1~100间的整数
        'method'  => 'Required|StrLenGeLe:1,10000|>>>:method error',//测量方法最多支持10000字
        'target'  => 'Required|StrLenGeLe:1,10000|>>>:target error',//目标最多支持10000字
        'standard'  => 'Required|StrLenGeLe:1,10000|>>>:standard error',//测量标准最多支持10000字
    ];

    /**
     * 员工编辑、删除KPI指标
     * @var array
     */
    public static $kpi_staff_indicators_edit = [
        'id' => 'Required|IntGe:1|>>>:id error', //指标ID
    ];

    /**
     * 引用模版
     * @var array
     */
    public static $kpi_staff_quote_template = [
        'staff_ids' => 'Required|Arr|ArrLenGeLe:1,2000|>>>:staff_ids error',
        'staff_ids[*]' => 'Required|IntGe:1|>>>:staff_id error',
        'activity_id' => 'Required|IntGt:0|>>>:activity_id error',
        'template_id' => 'Required|IntGt:0|>>>:template_id error',
    ];

    /**
     * 批量提交\删除、批量删除
     * @var array
     */
    public static $kpi_staff_batch_submit = [
        'staff_ids' => 'Required|Arr|ArrLenGeLe:1,2000|>>>:staff_ids error',
        'staff_ids[*]' => 'Required|IntGe:1|>>>:staff_id error',
        'activity_id' => 'Required|IntGt:0|>>>:activity_id error',
    ];

    /**
     * 变更记录
     * @var array
     */
    public static $kpi_change_log = [
        'activity_id' => 'Required|IntGe:1|>>>:activity id error', //活动ID
        'staff_id' => 'Required|IntGe:1|>>>:staff id error', //员工ID
        'pageNum' => 'IntGe:1|>>>:page error', //当前页码
        'pageSize' => 'IntGe:1|>>>:page_size error', //每页条数
    ];

    /**
     * 活动管理-员工列表-非必要的筛选条件
     * @var array
     */
    public static $not_must_kpi_activity_staff_list = [
        'stage',
        'name',
        'node_department_id',
        'job_id',
        'sys_store_id',
        'staff_state',
        'leader',
        'pageNum',
        'pageSize'
    ];

    /**
     * 员工KPI目标-参与员工-所有参与KPI活动员工列表
     */
    public static $kpi_staff_list_params = [
        // 活动id
        'activity_id'        => 'Required|IntGt:0|>>>:param error[activity_id]',
        // 阶段 必传 参与员工的目标阶段 1待制定 2待确认目标 3 确认完毕 目标执行
        'stage'              => 'StrIn:0,1,2,3|>>>:stage id error',
        // 支持按照 姓名/工号/昵称 模糊匹配
        'name'               => 'StrLenGeLe:0,50|>>>:param error[name]',
        // 部门id搜索
        'node_department_id' => 'IntGeLe:0,1000000000|>>>:param error[node_department_id]',
        // 职位搜索
        'job_id'             => 'IntGeLe:0,1000000000|>>>:param error[job_id]',
        // 所属网点搜索
        'sys_store_id'       => 'StrLenGeLe:0,10|>>>:param error[sys_store_id]',
        // 在职状态 支持多选
        'staff_state'        => 'Arr|>>>:param error[staff_state]',
        'staff_state[*]'     => 'StrLenGe:1|>>>:staff_state error',//在职状态
        // 直线上级 姓名/工号、 模糊搜搜
        'leader'             => 'StrLenGeLe:0,50|>>>:param error[leader]',
        //职级
        'job_title_grade'    => 'Arr',
        'job_title_grade[*]' => 'StrLenGe:1',
        // 翻页参数
        'pageNum'            => 'Required|IntGe:1|>>>:param error[pageNum]',
        // 翻页参数
        'pageSize'           => 'Required|IntGt:0|>>>:param error[pageSize]',
    ];

    public static $kpi_staff_stage_nums = [
        // 活动id
        'activity_id' => 'Required|IntGt:0|>>>:param error[activity_id]',
    ];

    /**
     * 员工枚举接口
     * @return mixed
     */
    public function getEnum()
    {
        $data['working_country'] = $this->getWorkerCountryEnum();//所在国家
        $data['staff_state'] = $this->getStaffStateEnum();//在职状态
        $data['hire_type'] = $this->getStaffHireTypeEnum();//员工雇佣类型
        $data['probation_status'] = $this->getStaffProbationStatusEnum();//试用期状态
        $data['kpi_staff_title_grade'] = StaffInfoEnums::$config_job_title_grade_v2;//职级
        $data['kpi_staff_stage'] = $this->getActivityStaffStageEnum();//阶段
        $data['kpi_staff_change_leader'] = $this->getActivityStaffChangeLeaderEnum();//是否变更过KPI制定人
        return $data;
    }

    /**
     * 工作国家
     * @return array
     */
    public function getWorkerCountryEnum()
    {
        $working_country = StaffInfoEnums::$working_country;
        foreach($working_country as $key=>$val){
            $working_country[$key] = static::$t->_($val);
        }
        return $working_country;
    }

    /**
     * 在职状态
     * @return array
     */
    public function getStaffStateEnum()
    {
        $state = StaffInfoEnums::$staff_state;
        foreach($state as $key=>$val){
            $state[$key] = static::$t->_($val);
        }
        return $state;
    }

    /**
     * 员工雇佣类型
     * @return array
     */
    public function getStaffHireTypeEnum()
    {
        $hire_type = StaffInfoEnums::$hire_type;
        foreach($hire_type as $key => $val) {
            $hire_type[$key] = static::$t->_($val);
        }
        return $hire_type;
    }

    /**
     * 试用期状态
     * @return array
     */
    public function getStaffProbationStatusEnum()
    {
        $probation_status = StaffInfoEnums::$probation_status;
        foreach($probation_status as $key=>$val){
            $probation_status[$key] = static::$t->_($val);
        }
        return $probation_status;
    }

    /**
     * 当前阶段
     * @return array
     */
    public function getActivityStaffStageEnum()
    {
        $stage = KpiActivityStaffEnums::$kpi_activity_staff_stage;
        foreach($stage as $key => $val) {
            $stage[$key] = static::$t->_($val);
        }
        return $stage;
    }



    /**
     * 是否变更过KPI指定人
     * @return array
     */
    public function getActivityStaffChangeLeaderEnum()
    {
        $change = KpiActivityStaffEnums::$kpi_activity_staff_is_change_leader;
        foreach($change as $key => $val) {
            $change[$key] = static::$t->_($val);
        }
        return $change;
    }

    /**
     * 获取已经参与活动的员工信息
     * @param $activity_id
     * @param $staff_ids
     * @return mixed
     * <AUTHOR>
     */
    function getStafflistByActivityStaff($activity_id, $staff_ids)
    {
        return KpiActivityStaffModel::find([
            'conditions' => "activity_id = :activity_id: AND staff_id IN ({staff_ids:array})",
            'bind'       => ['activity_id' => $activity_id, 'staff_ids' => $staff_ids],
            'columns'    => ['id, staff_id'],
        ])->toArray();
    }

    /**
     * 添加参与活动的用户 - 勾选
     * @param $params
     * @param $create_id
     * @param $create_name
     * <AUTHOR>
     * @return array
     */
    public function addActivityStaff($params, $create_id, $create_name)
    {
        $code        = ErrCode::$SUCCESS;
        $message     = $real_message = '';
        $result_data = [
            'success_count' => 0,
            'error_count'   => 0,
        ];

        $db = $this->getDI()->get('db_backyard');
        try {
            // 最大支持10000
            if (count($params['staff_ids']) > self::$max_add_staff_count) {
                throw new ValidationException(static::$t->_('kpi_max_mum', ['nums' => self::$max_add_staff_count]),
                    ErrCode::$VALIDATE_ERROR);
            }

            // 活动数据验证，如果未找到 或者 活动已经结束都抛出异常
            $this->validationActivity($params);
            // 根据前端传递的工号id 去 检索数据 过滤离职数据，需要在职，待离职、停职数据
            $list = StaffService::getInstance()->getStaffListByStaffIds($params['activity_id'], $params['staff_ids'], [StaffInfoEnums::STAFF_STATE_IN, StaffInfoEnums::STAFF_STATE_STOP]);
            if (empty($list)) {
                return [
                    'code'    => $code,
                    'message' => 'success',
                    'data'    => $result_data,
                ];
            }

            // 生成批量插入的数据
            $data = $this->createInsertData($list, $params['activity_id'], $create_id, $create_name);

            $db->begin();
            //批量插入active staff数据
            $activity_staff_model = new KpiActivityStaffModel();
            if (!empty($data['insert_data'])) {
                $batch_insert_ret = $activity_staff_model->batch_insert($data['insert_data'], 'db_backyard');
                if (false === $batch_insert_ret) {
                    throw new BusinessException('addActivityStaff 向 kpi_activity_staff 批量插入数据失败：' . json_encode($activity_staff_model->toArray(), JSON_UNESCAPED_UNICODE) . get_data_object_error_msg($activity_staff_model), ErrCode::$BUSINESS_ERROR);
                }
            }

            $db->commit();
            $result_data['success_count'] = $data['success_count'];
            $result_data['error_count']   = $data['error_count'];

        } catch (ValidationException $e) {
            $code    = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            //事务回滚
            $db->rollback();
            $this->logger->warning(__CLASS__ . ' function: addActivityStaff-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $result_data,
        ];
    }

    /**
     * 组装insert数据
     * @param $list
     * @param $activity_id
     * @param $create_id
     * @param $create_name
     * @return array
     * <AUTHOR>
     */
    public function createInsertData($list, $activity_id, $create_id, $create_name)
    {
        $data['insert_data']   = $data['update_staff_ids'] = [];
        $data['success_count'] = $data['error_count'] = 0;
        $logger                = $this->getDI()->get('logger');
        if (empty($list)) {
            return $data;
        }

        $staff_info_ids = array_values(array_unique(array_column($list, 'staff_info_id')));
        // 过滤 OKR 名单中的数据
        $okr_staff_ids = [];
        $okr_list      = KpiActivityOkrService::getInstance()->getKpiActivityOKRByParams([
            "staff_id"    => $staff_info_ids,
            "activity_id" => $activity_id,
        ], ['id', 'activity_id', 'staff_id']);
        if (!empty($okr_list)) {
            $okr_staff_ids = array_column($okr_list, 'staff_id');
        }

        $cur_time = gmdate("Y-m-d H:i:s", time() + get_sys_time_offset() * 3600);
        // 如果有值则进行处理
        foreach ($list as $val) {
            // 过滤okr名单中的数据
            if (in_array($val['staff_info_id'], $okr_staff_ids)) {
                $data['error_count']++;
                $logger->info('message: exist OKR, kpi-add-staff-fail:'.json_encode(
                        [
                            'staff_id'     => $val['staff_info_id'],
                            'leader_id'    => $val['leader_id'],
                            'leader_state' => $val['leader_state'],
                        ])
                );
                continue;
            }

            // 过滤离职数据，停职的员工可以参加kpi活动
            if ($val['state'] == StaffInfoEnums::STAFF_STATE_LEAVE) {
                $data['error_count']++;
                $logger->info('message: employee has left 员工已经离职, kpi-add-staff-fail:'.json_encode(
                        [
                            'staff_id'     => $val['staff_info_id'],
                            'leader_id'    => $val['leader_id'],
                            'leader_state' => $val['leader_state'],
                        ])
                );
                continue;
            }

            if ($val['selected'] == 0) {
                $data['insert_data'][] = [
                    'activity_id' => $activity_id,
                    'staff_id'    => $val['staff_info_id'],
                    'leader_id'   => 0,
                    'leader_name' => '',
                    'create_id'   => $create_id,
                    'create_name' => $create_name,
                    'stage'       => KpiActivityStaffEnums::STAGE_DEFAULT,
                    'created_at'  => $cur_time,
                    'updated_at'  => $cur_time
                ];
            } else {
                $data['update_staff_ids'][] = $val['staff_info_id'];
            }
            $data['success_count']++;
        }
        return $data;
    }

    /**
     * 组装导入insert数据
     * @param $list
     * @param $activity_id
     * @param $create_id
     * @param $create_name
     * @return array
     * <AUTHOR>
     */
    public function createImportData($list, $activity_id, $create_id, $create_name)
    {
        $data['insert_data']   = $data['update_staff_ids'] = [];
        $data['success_count'] = $data['error_count'] = 0;
        if (empty($list)) {
            return $data;
        }

        $cur_time = gmdate("Y-m-d H:i:s", time() + get_sys_time_offset() * 3600);
        foreach ($list as $val) {
            $data['insert_data'][] = [
                'activity_id'          => $activity_id,
                'staff_id'             => $val['staff_info_id'],
                'leader_id'            => 0,
                'leader_name'          => '',
                'create_id'            => $create_id,
                'create_name'          => $create_name,
                'stage'                => KpiActivityStaffEnums::STAGE_DEFAULT,
                'created_at'           => $cur_time,
                'updated_at'           => $cur_time,
            ];
            $data['success_count']++;
        }

        return $data;
    }

    /**
     * 检测某个活动员工参与数
     * @param integer $activity_id 活动ID
     * @return int
     */
    public function checkActivityHasStaff($activity_id)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns(['count(main.id) as total']);
        $builder->from(['main' => KpiActivityStaffModel::class]);
        $builder->andWhere('main.activity_id = :activity_id:', ['activity_id' => $activity_id]);
        return intval($builder->getQuery()->getSingleResult()->total);
    }

    /**
     * 导入员工
     * @param $params
     * @param $excel_data
     * @param $create_id
     * @param $create_name
     * @return array
     * @throws \App\Library\Exception\BusinessException
     * <AUTHOR>
     */
    public function importActivityStaff($params, $excel_data, $create_id, $create_name): array
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $real_message = '';
        $insert_data = $error_data = [];
        $result_data = [
            'success_data_count'      => 0, //校验成功条数
            'error_data_count'        => 0, //校验失败条数
            'data_count'              => 0, //总条数
            'result_file'             => '',//结果详情文件路径
            'cache_excel_success_key' => '',
        ];

        $db = $this->getDI()->get('db_backyard');
        try {
            foreach ($excel_data as $key => $val) {
                if (empty($val[0]) && empty($val[1])) {
                    // 滤掉空白行
                    unset($excel_data[$key]);
                }
                if(!empty($val[0])) {
                    $val[0] = trim($val[0]);
                }
                if (!empty($val[1])) {
                    $val[1] = trim($val[1]);
                }
            }

            // 条数限制 最小1 最大 5000
            if (count($excel_data) < self::$min_import_count || count($excel_data) > self::$max_import_count) {
                throw new ValidationException(static::$t->_('kpi_max_mum',['nums' => self::$max_import_count]), ErrCode::$VALIDATE_ERROR);
            }

            //验证活动数据 如果
            $this->validationActivity($params);

            //获取用户信息
            $staff_ids  = array_values(array_unique(array_column($excel_data, 0)));
            $staff_list = StaffService::getInstance()->getStaffListByStaffIds($params['activity_id'], $staff_ids);
            $staff_list = array_column($staff_list, null, 'staff_info_id');

            // 获取 okr 名单数据
            $okr_staff_ids = [];
            $okr_list      = KpiActivityOkrService::getInstance()->getKpiActivityOKRByParams([
                "staff_id"    => $staff_ids,
                "activity_id" => $params['activity_id'],
            ], ['id', 'activity_id', 'staff_id']);
            if (!empty($okr_list)) {
                $okr_staff_ids = array_column($okr_list, 'staff_id');
            }

            $activity_staff_ids = [];
            // 获取正在参与活动的员工数据
            $activity_staff_list = $this->getStafflistByActivityStaff($params['activity_id'], $staff_ids);
            if (!empty($activity_staff_list)) {
                $activity_staff_ids = array_column($activity_staff_list, 'staff_id');
            }

            //组装要插入的数据和要更新的数据
            $data                = $this->createImportData($staff_list, $params['activity_id'], $create_id,
                $create_name);
            $data['insert_data'] = array_column($data['insert_data'], null, 'staff_id');

            //验证数据
            $insert_staff_ids = [];
            foreach ($excel_data as $key => &$val) {
                $val[2] = "";
                // 工号为空的提示
                if (empty($val[0])) {
                    $error_data[] = [
                        $val[0],
                        $val[1],
                        static::$t->_('kpi_activity_import_staff_error.1')
                    ];
                    $val[2] = static::$t->_('kpi_activity_import_staff_error.1');     //工号不能为空;
                    continue;
                }
                // 姓名为空的提示
                if (empty($val[1])) {
                    $error_data[] = [
                        $val[0],
                        $val[1],
                        static::$t->_('kpi_activity_import_staff_error.2')
                    ];
                    $val[2] = static::$t->_('kpi_activity_import_staff_error.2');     //姓名不可为空
                    continue;
                }

                // 员工不存在
                if (empty($staff_list[$val[0]])) {
                    $error_data[] = [
                        $val[0],
                        $val[1],
                        static::$t->_('kpi_activity_import_staff_error.3')
                    ];
                    $val[2] = static::$t->_('kpi_activity_import_staff_error.3');
                    continue;
                }
                // 工号与姓名不一致
                if (trim($val[1]) != trim($staff_list[$val[0]]['name'])) {
                    $error_data[] = [
                        $val[0],
                        $val[1],
                        static::$t->_('kpi_activity_import_staff_error.4')
                    ];
                    $val[2] = static::$t->_('kpi_activity_import_staff_error.4');
                    continue;
                }

                // 工号已经离职
                if ($staff_list[$val[0]]['state'] == StaffInfoEnums::STAFF_STATE_LEAVE) {
                    $error_data[] = [
                        $val[0],
                        $val[1],
                        static::$t->_('kpi_activity_import_staff_error.12')
                    ];
                    $val[2] = static::$t->_('kpi_activity_import_staff_error.12');
                    continue;
                }

                // 同一个表中重复，
                if (in_array($val[0], $insert_staff_ids)) {
                    $error_data[] = [
                        $val[0],
                        $val[1],
                        static::$t->_('kpi_add_repeatedly')
                        //static::$t->_('kpi_duplicate_job_number')
                    ];
                    $val[2] = static::$t->_('kpi_add_repeatedly');//static::$t->_('kpi_duplicate_job_number');
                    continue;
                }

                // OKR名单过滤
                if (in_array($val[0], $okr_staff_ids)) {
                    $error_data[] = [
                        $val[0],
                        $val[1],
                        static::$t->_('kpi_activity_import_staff_error.13')
                    ];
                    $val[2] = static::$t->_('kpi_activity_import_staff_error.13');
                    continue;
                }

                // 已经在活动中的人员， excel表中存在重复的数据，且该重复数据已经在kpi活动中了，则不再向$insert_staff_ids插入数据判重
                if (in_array($val[0], $activity_staff_ids)) {
                    $error_data[] = [
                        $val[0],
                        $val[1],
                        static::$t->_('kpi_add_repeatedly')
                    ];
                    $val[2] = static::$t->_('kpi_add_repeatedly');  //工号已经已经，请勿重复添加
                    continue;
                }

                // 需要插入的数据
                $insert_data[]      = $data['insert_data'][$val[0]];
                // 验证工号重复
                $insert_staff_ids[] = $val[0];
            }

            $db->begin();

            //批量插入active staff数据
            $activity_staff_model = new KpiActivityStaffModel();
            if (!empty($insert_data)) {
                $batch_insert_ret = $activity_staff_model->batch_insert($insert_data, 'db_backyard');
                if (false === $batch_insert_ret) {
                    throw new BusinessException('importActivityStaff 向 kpi_activity_staff 批量插入数据失败：' . json_encode($activity_staff_model->toArray(), JSON_UNESCAPED_UNICODE) . get_data_object_error_msg($activity_staff_model), ErrCode::$BUSINESS_ERROR);
                }
            }
            $db->commit();
        } catch (ValidationException $e) {
            $code    = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            //事务回滚
            $db->rollback();
            $this->logger->warning(__CLASS__ . ' function: importActivityStaff-failed:' . $real_message);
        }

        $result_data = [];
        //生成创建失败文件xlsx并上传
        if ($code == ErrCode::$SUCCESS) {
            $file_result = [];
            $upload_key  = "";
            if (!empty($excel_data)) {
                $new_excel_data = [];
                foreach ($excel_data as $item) {
                    $new_excel_data[] = array_values($item);
                }
                $upload_key   = static::$t->_('kpi_activity_import_staff_error_file').'-'.date('Y-m-d');
                $excel_header = [
                    static::$t->_('kpi_staff_id'),
                    static::$t->_('kpi_staff_name'),
                    static::$t->_('kpi_import_result'),
                ];
                $file_result  = self::exportExcel($excel_header, $new_excel_data, $upload_key.'.xlsx');
            }
            $result_data['success_data_count']      = count($insert_data); //校验成功条数
            $result_data['error_data_count']        = count($error_data);  //校验失败条数
            $result_data['data_count']              = count($excel_data);  //总条数
            $result_data['result_file']             = $file_result['data'];//结果详情文件路径
            $result_data['cache_excel_success_key'] = $upload_key;       //数据缓存key
        }

        return [
            'code' => $code,
            'msg'  => $message,
            'data' => $result_data,
        ];
    }

    /**
     * 获取参与活动的员工列表
     * @param $params
     * @return array
     * <AUTHOR>
     */
    public function getActivityStaffList($params)
    {
        $page   = intval($params['pageNum']);
        $size   = intval($params['pageSize']);
        $offset = $size * ($page - 1);

        $data    = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page,
                'per_page'     => $size,
                'total_count'  => 0,
            ],
        ];
        $code    = ErrCode::$SUCCESS;
        $message = '';

        try {
            //判断活动是否存在 如果不用存在则抛出异常
            ActivityService::getInstance()->checkActivityInfo($params['activity_id']);

            // 获取符合条件的个数
            $data['pagination']['total_count'] = $this->getActivityStaffListCount($params);
            if (0 == $data['pagination']['total_count']) {
                return [
                    'code'    => $code,
                    'message' => $message,
                    'data'    => $data,
                ];
            }
            $field   = [
                'DISTINCT kas.id',
                'kas.activity_id',
                'kas.template_id',
                'kas.staff_id',
                'IF(kas.leader_id = 0,manger.staff_info_id,kas.leader_id ) AS kpi_leader_id',
                'IF(kas.leader_id = 0,manger.name,kas.leader_name) AS kpi_leader_name',
                'manger.staff_info_id AS leader_id',
                'manger.name AS leader_name',
                'kas.create_id',
                'kas.create_name',
                'kas.stage',
                'kas.created_at',
                'kas.updated_at',
                'hsi.name AS staff_name',
                'hsi.node_department_id',
                'hsi.job_title AS job_id',
                'hsi.sys_store_id',
                'hsi.staff_info_id',
                'hsi.nick_name',
                'hsi.state',
                'hsi.wait_leave_state',
                'hsi.hire_date',
                'hsi.job_title_grade_v2 AS job_title_grade',
                'hsit.value AS working_country',
            ];
            $builder = StaffService::getInstance()->createActivityStaffBuilder($field);
            // 组织查询条件
            $builder = $this->getActivityStaffCondition($builder, $params);
            $builder->limit($size, $offset);
            $builder->orderBy('kas.staff_id ASC');
            $data['items'] = $builder->getQuery()->execute()->toArray();

            // 网点id
            $store_ids  = array_values(array_unique(array_column($data['items'], 'sys_store_id')));
            $store_list = CommonService::getInstance()->getSysStoreByParams(['id' => $store_ids], ['id', 'name']);
            if (!empty($store_list)) {
                $store_list = array_column($store_list, null, 'id');
            }

            // 部门id
            $department_ids  = array_values(array_unique(array_column($data['items'], 'node_department_id')));
            $department_list = CommonService::getInstance()->getDepartmentByParams([
                "id" => $department_ids,
            ], ['id', 'name','deleted']);
            if (!empty($department_list)) {
                $department_list = array_column($department_list, null, "id");
            }

            // 职位id
            $job_ids        = array_values(array_unique(array_column($data['items'], "job_id")));
            $job_title_list = CommonService::getInstance()->getJobTitleByParams([
                "id"     => $job_ids,
                "status" => 1,
            ], ['id', 'job_name']);
            if (!empty($job_title_list)) {
                $job_title_list = array_column($job_title_list, null, 'id');
            }

            foreach ($data['items'] as $key => $val) {
                $data['items'][$key]['leader_id'] = is_null($val['leader_id']) ? '' : $val['leader_id'];
                $data['items'][$key]['leader_name'] = is_null($val['leader_name']) ? '' : trim($val['leader_name']);
                $data['items'][$key]['working_country_name'] = !empty(StaffInfoEnums::$working_country[$val['working_country']]) ? static::$t->_(StaffInfoEnums::$working_country[$val['working_country']]) : "";
                $data['items'][$key]['stage_name']           = !empty(KpiActivityStaffEnums::$kpi_activity_staff_stage[$val['stage']]) ? static::$t->_(KpiActivityStaffEnums::$kpi_activity_staff_stage[$val['stage']]) : '';
                if (StaffInfoEnums::STAFF_STATE_IN == $val['state'] && StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES == $val['wait_leave_state']) {
                    $val['state'] = StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE;
                }
                $data['items'][$key]['state_name'] = static::$t->_(StaffInfoEnums::$staff_state[$val['state']]);
                if ($val['sys_store_id'] == KpiActivityStaffEnums::STORE_HEADER_OFFICE_ID) {
                    $data['items'][$key]['sys_store_name'] = KpiActivityStaffEnums::STORE_HEADER_OFFICE_NAME;
                } else {
                    $data['items'][$key]['sys_store_name'] = !empty($store_list[$val['sys_store_id']]) ? $store_list[$val['sys_store_id']]['name'] : "";
                }
                $data['items'][$key]['hire_date']            = !empty($val['hire_date']) ? date('Y/m/d',
                    strtotime($val['hire_date'])) : '';
                $data['items'][$key]['node_department_name'] = (CommonService::getInstance())->getDepartmentNameView($department_list[$val['node_department_id']] ?? []);
                $data['items'][$key]['job_name']             = !empty($job_title_list[$val['job_id']]) ? $job_title_list[$val['job_id']]['job_name'] : "";
                $data['items'][$key]['leader']               = $this->getStaffInfoIdAndName((int)$val['leader_id'],
                    (string)$val['leader_name']);
                $data['items'][$key]['job_title_grade']      = 0; //职级
                $data['items'][$key]['job_title_grade_text'] = "";//职级文案
                if (isset($val['job_title_grade'])) {
                    $job_title_grade                             = $val['job_title_grade'];
                    $data['items'][$key]['job_title_grade']      = $job_title_grade;
                    $data['items'][$key]['job_title_grade_text'] = static::$t->_(StaffInfoEnums::$config_job_title_grade_v2[$job_title_grade]);
                }
            }
        } catch (ValidationException $e) {
            $code    = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error(__CLASS__.' function: getActivityStaffList-failed:'.$e->getMessage());
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * @param int $staff_info_id
     * @param string $name
     * @return string
     */
    public function getStaffInfoIdAndName(int $staff_info_id = 0, string $name = ''): string
    {
        $view_info = '';
        if (0 == $staff_info_id && $name == '') {
            return $view_info;
        }
        return $staff_info_id.'（'.trim($name).'）';
    }

    /**
     * 获取参与活动的员工列表-获取个数
     * @param $params
     * @return int
     */
    public function getActivityStaffListCount($params)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('COUNT(DISTINCT kas.id) AS total');
        $builder->from(['kas' => KpiActivityStaffModel::class]);
        $builder->leftJoin(HrStaffInfoModel::class, "hsi.staff_info_id = kas.staff_id", 'hsi');
        $builder->leftjoin(HrStaffInfoModel::class, 'manger.staff_info_id = hsi.manger', 'manger');
        $builder->leftJoin(HrStaffItemsModel::class, "hsit.staff_info_id = kas.staff_id AND item = 'WORKING_COUNTRY'", 'hsit');
        //$builder->andWhere('manger.state = :state:', ['state' => StaffInfoEnums::STAFF_STATE_IN]);
        $builder = $this->getActivityStaffCondition($builder, $params);
        return intval($builder->getQuery()->getSingleResult()->total);
    }

    /**
     * 获取参与活动的员工列表-组织查询条件
     * @param $builder
     * @param $params
     * @param string $source
     * @return mixed
     * <AUTHOR>
     */
    public function getActivityStaffCondition($builder, $params)
    {
        $builder->andWhere('hsi.formal IN ({formal:array}) AND hsi.is_sub_staff = :is_sub_staff:',
            [
                'formal'       =>
                    [StaffInfoEnums::FORMAL_IN, StaffInfoEnums::FORMAL_TRAINEE],
                'is_sub_staff' => StaffInfoEnums::IS_SUB_STAFF_NO,
            ]);

        $builder->andWhere('kas.activity_id = :activity_id: ',
            ['activity_id' => $params['activity_id']]);

        // source表示来源 activity表示来自【目标管理下】-【参与员工】列表查询，其他查询军来自员工管理
        if ((isset($params['source']) && $params['source'] != 'activity') || empty($params['source'])) {
            // 默认查询所有
            $all_stage = [
                KpiActivityStaffEnums::STAGE_LEADER,
                KpiActivityStaffEnums::STAGE_STAFF,
                KpiActivityStaffEnums::STAGE_DONE,
            ];

            $all_stage = (isset($params['stage']) && $params['stage'] > 0) ? [(int) $params['stage']] : $all_stage;
            // 只有员工kpi目录才 追加这个字段
            $builder->inWhere('kas.stage', $all_stage);
        }

        // 支持 按照名称 、昵称 、和工号进行模糊搜索
        if (isset($params['name']) && !empty(trim($params['name']))) {
            $builder->andWhere("hsi.name LIKE :name: OR hsi.staff_info_id LIKE :name: OR hsi.nick_name LIKE :name: ",
                ['name' => '%'.trim($params['name']).'%']);
        }

        // 选择上级某个部门，下级部门的人员也会被搜索出来
        if (isset($params['node_department_id']) && !empty($params['node_department_id'])) {
            $department_ids = (new DepartmentService())->getChildrenListByDepartmentIdV2($params['node_department_id'],
                true);
            array_push($department_ids, $params['node_department_id']);
            $builder->andWhere('hsi.node_department_id IN ({department_ids:array})',
                ['department_ids' => $department_ids]);
        }

        // 职位搜索
        if (isset($params['job_id']) && !empty($params['job_id'])) {
            $builder->andWhere('hsi.job_title = :job_title:',
                ['job_title' => $params['job_id']]);
        }

        // 所属网点
        if (isset($params['sys_store_id']) && !empty($params['sys_store_id'])) {
            $builder->andWhere('hsi.sys_store_id = :sys_store_id:',
                ['sys_store_id' => $params['sys_store_id']]);
        }

        // 工作所在国家
        if (isset($params['working_country'][0]) && !empty($params['working_country'][0])) {
            $builder->andWhere("hsit.value IN ({working_country:array})",
                ['working_country' => $params['working_country']]);
        }

        // 直接上级
        if (isset($params['leader']) && !empty($params['leader'])) {
            // todo 产品要求只是查询hcm中的直线上级，oa变更过的上级 不做查询和展示
            $builder->andWhere("manger.staff_info_id LIKE :leader:", ['leader' => '%'.trim($params['leader']).'%']);
        }

        // 在职状态检索 支持多选
        if (isset($params['staff_state'][0]) && !empty($params['staff_state'][0])) {
            if (in_array(StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE, $params['staff_state'])) {    // 待离职的处理
                $builder->andWhere("hsi.state IN ({states:array}) OR (hsi.state= :state: AND hsi.wait_leave_state = :wait_leave_state:)",
                    ['states'           => $params['staff_state'],
                     'state'            => StaffInfoEnums::STAFF_STATE_IN,
                     'wait_leave_state' => StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES,
                    ]);
            } else {
                $builder->andWhere("hsi.state IN ({states:array}) AND hsi.wait_leave_state = :wait_leave_state:",
                    ['states'           => $params['staff_state'],
                     'wait_leave_state' => StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_NO,
                    ]);
            }
        }

        // 入职日期检索
        if (isset($params['hire_date'][0]) && !empty($params['hire_date'][1])) {
            $params['hire_date'][0] = $params['hire_date'][0].' 00:00:00';
            $params['hire_date'][1] = $params['hire_date'][1].' 23:59:59';
            $builder->andWhere("hsi.hire_date >= :hire_date_st: AND hsi.hire_date <= :hire_date_end:",
                ['hire_date_st' => $params['hire_date'][0], 'hire_date_end' => $params['hire_date'][1]]);
        }

        // 职级检索
        if (isset($params['job_title_grade'][0])) {
            // 职级搜索 会有传递0的情况
            $builder->andWhere('hsi.job_title_grade_v2 IN ({job_title_grade_v2:array})',
                ['job_title_grade_v2' => $params['job_title_grade']]);
        }

        // 是否变更过KPI制定人 1 未变更 2 变更
        if (!empty($params['is_change_leader'])) {
            $builder->andWhere('kas.is_change_leader = :is_change_leader:',
                ['is_change_leader' => $params['is_change_leader']]);
        }

        return $builder;
    }

    /**
     * 员工KPI目标-参与员工-待制定目标环节 一键催办
     */
    public function batchStageLeaderSendMsg(array $params)
    {
        $code    = ErrCode::$SUCCESS;
        $message = '';
        try {
            set_time_limit(0);
            //判断活动是否存在 如果不用存在则抛出异常
            //获取活动信息
            $activity_info    = $this->validationActivity($params);
            $activity_configs = json_decode($activity_info['config'], true);
            $leader_config    = $activity_configs['time_node']['leader'];

            // 是否终止循环，默认不终止
            $is_while = true;
            $page     = 1;
            $size     = self::$chunk_num;  // 每页处理2000条数据
            while ($is_while) {
                $offset = $size * ($page - 1);

                // 根据条件获取所偶待制定目标的上级信息，包括变更后的kpi制定人
                $builder = $this->modelsManager->createBuilder();
                $builder->columns(
                    'kas.id, 
                    kas.activity_id,  
                    kas.staff_id,  
                    IF(kas.leader_id = 0,manger.staff_info_id,kas.leader_id ) AS leader_id,
                    IF(kas.leader_id = 0,manger.name,kas.leader_name) AS leader_name'
                );
                $builder->from(['kas' => KpiActivityStaffModel::class]);
                $builder->leftJoin(HrStaffInfoModel::class, "hsi.staff_info_id = kas.staff_id", 'hsi');
                $builder->leftjoin(HrStaffInfoModel::class, 'manger.staff_info_id = hsi.manger', 'manger');

                // 组织查询条件
                $params['source'] = 'staff';
                $params['stage']  = KpiActivityStaffEnums::STAGE_LEADER;
                $builder          = $this->getActivityStaffCondition($builder, $params);

                $builder->limit($size + 1, $offset);
                $builder->orderBy('kas.id DESC');
                $data = $builder->getQuery()->execute()->toArray();

                if (1 == $page && empty($data)) {
                    throw new ValidationException(static::$t->_('kpi_activity_leader_not_confirm'), ErrCode::$VALIDATE_ERROR);
                }

                if (count($data) > $size) {
                    // 去掉末尾多余的一条数据
                    array_pop($data);
                } else {
                    // 没有下一页了需要退出循环
                    $is_while = false;
                }
                $page++;

                // 处理发送通知的逻辑
                $leader_ids = array_values(array_filter(array_unique(array_column($data, 'leader_id'))));
                $staff_ids = array_values(array_filter(array_unique(array_column($data,'staff_id'))));

                // 获取leader的消息发送记录,拿到发送时间最小的一条数据
                //$first_send_leader_log_date = '';//date('Y-m-d');
                $leader_send_log            = KpiLeaderMsgSendLogService::getInstance()->getKpiLeaderMsgSendLogByLeaderIds($params['activity_id'],
                    $leader_ids,$staff_ids);

                //$staff_users    = [];
                $push_service   = new PushService();
                $message_scheme = StaffService::getInstance()->getLeaderMessageScheme();//leader的push消息跳转地址
                foreach ($leader_ids as $leader_id) {
                    $first_send_leader_log_date = '';
                    $cut_off_time = '';
                    if (!empty($leader_send_log[$leader_id])) {
                        $first_send_leader_log_date = date("Y-m-d", strtotime($leader_send_log[$leader_id]['created_at']));
                        // 计算最后限制打卡的时间
                        $cut_off_time = date("Y-m-d",
                            strtotime("+".(int)$leader_config['day']." days", strtotime($first_send_leader_log_date)));
                    }

                    $title_key   = KpiActivityStaffEnums::STAFF_KPI_REMIND_LEADER_TITLE;
                    $content_key = KpiActivityStaffEnums::STAFF_KPI_REMIND_LEADER_CONTENT;
                    if ($leader_config['on-off']) {
                        $content_key = KpiActivityStaffEnums::STAFF_KPI_REMIND_LEADER_CONTENT_1;
                    }

                    if (empty($first_send_leader_log_date)) {
                        $content_key = KpiActivityStaffEnums::STAFF_KPI_REMIND_LEADER_CONTENT_2;
                    }

                    // todo  产品要求 暂时去掉push
                    //发送push
                    //$staff_users[] = ['id' => $leader_id];
//                    $push_service->sendPush($leader_id, $activity_info, $title_key, $content_key, $message_scheme,
//                        $cut_off_time);

                    //发送站内信
                    $info        = [
                        'years'  => $activity_info['years'],
                        'period' => $activity_info['period'],
                        'date'   => $cut_off_time,
                    ];
                    $msg_title   = $push_service->getFormatMsg($title_key, $info);
                    $msg_content = $push_service->getFormatMsg($content_key, $info, '<br>');
                    $msg_content = KpiActivityStaffEnums::MSG_STYLE_BEGIN.$msg_content.KpiActivityStaffEnums::MSG_STYLE_END;
                    $push_service->sendMessage([['id' => $leader_id]], $msg_title, $msg_content,
                        KpiActivityStaffEnums::MESSAGE_CATEGORY_KPI_LEADER);

                }
            }
        } catch (ValidationException $e) {
            $code    = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error(__CLASS__.' function: batchStageLeaderSendMsg-failed:'.$e->getMessage());
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => [],
        ];
    }

    /**
     * 删除/批量删除参与员工
     * @param array $params 请求参数组
     * @return array
     */
    public function delActivityStaff(array $params)
    {
        $code = ErrCode::$SUCCESS;
        $message = '';
        $db = $this->getDI()->get('db_backyard');
        $db->begin();
        try {
            //活动ID
            $activity_id = $params['activity_id'];
            //检测活动信息
            $this->validationActivity($params);

            //检测员工合法性
            $activity_staff_list = KpiActivityStaffModel::find([
                "conditions" => "activity_id = :activity_id: AND staff_id IN ({staff_ids:array})",
                'bind' => ['activity_id' => $activity_id, 'staff_ids' => $params['staff_ids'] ]
            ]);
            $activity_staff_list_arr = $activity_staff_list->toArray();
            if(empty($activity_staff_list_arr)) {
                throw new ValidationException(static::$t->_('kpi_activity_staff_not_found'), ErrCode::$VALIDATE_ERROR);
            }

            //删除员工信息
            $del_staff = $activity_staff_list->delete();
            if ($del_staff === false) {
                throw new BusinessException('KPI员工删除失败= ' . json_encode($activity_staff_list_arr, JSON_UNESCAPED_UNICODE) . ';可能的原因是：' . get_data_object_error_msg($activity_staff_list), ErrCode::$BUSINESS_ERROR);
            }

            //删除的员工工号组
            $del_activity_staff_ids = array_values(array_column($activity_staff_list_arr, 'staff_id'));

            //删除员工的KPI推送通知到上级-用于限制目标制定人打卡记录
            $staff_leader_msg_log = KpiLeaderMsgSendLogModel::find([
                'conditions' => 'activity_id = :activity_id: and staff_id in ({staff_ids:array})',
                'bind' => ['activity_id' => $activity_id, 'staff_ids' => $del_activity_staff_ids]
            ]);
            $del_staff_leader_msg_log = $staff_leader_msg_log->delete();
            if ($del_staff_leader_msg_log === false) {
                throw new BusinessException('KPI员工-删除员工操作-删除员工关联的KPI推送通知到上级-用于限制目标制定人打卡记录=' . json_encode($staff_leader_msg_log->toArray(), JSON_UNESCAPED_UNICODE) . get_data_object_error_msg($staff_leader_msg_log), ErrCode::$BUSINESS_ERROR);
            }

            //删除员工的KPI员工目标制定人变更记录表
            $staff_change_leader_log = KpiStaffChangeLeaderLogModel::find([
                'conditions' => 'activity_id = :activity_id: and staff_id in ({staff_ids:array})',
                'bind' => ['activity_id' => $activity_id, 'staff_ids' => $del_activity_staff_ids]
            ]);

            $del_staff_change_leader_log = $staff_change_leader_log->delete();
            if ($del_staff_change_leader_log === false) {
                throw new BusinessException('KPI员工-删除员工操作-删除员工关联的KPI员工目标制定人变更记录=' . json_encode($staff_change_leader_log->toArray(), JSON_UNESCAPED_UNICODE) . get_data_object_error_msg($staff_change_leader_log), ErrCode::$BUSINESS_ERROR);
            }

            //删除员工关联的指标信息
            $old_staff_indicator = KpiStaffIndicatorsRelModel::find([
                'conditions' => 'activity_id = :activity_id: and staff_id in ({staff_ids:array})',
                'bind' => ['activity_id' => $activity_id, 'staff_ids' => $del_activity_staff_ids]
            ]);
            $del_staff_indicator = $old_staff_indicator->delete();
            if ($del_staff_indicator === false) {
                throw new BusinessException('KPI员工-删除员工操作-删除员工关联的指标信息=' . json_encode($old_staff_indicator->toArray(), JSON_UNESCAPED_UNICODE) . get_data_object_error_msg($old_staff_indicator), ErrCode::$BUSINESS_ERROR);
            }
            $data['staff_count'] = count($del_activity_staff_ids);
            $db->commit();
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $message;
        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            //事务回滚
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->warning('kpi_delete-staff-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data ?? []
        ];
    }

    /**
     * 变更记录
     * @param array $params 请求参数组
     * @return array
     */
    public function changeLog(array $params)
    {
        $page_num  = empty($params['pageNum']) ? Enums\GlobalEnums::DEFAULT_PAGE_NUM : $params['pageNum'];
        $page_size = empty($params['pageSize']) ? Enums\GlobalEnums::DEFAULT_PAGE_SIZE : $params['pageSize'];
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];
        try {
            $activity_id = $params['activity_id'];//活动ID
            $staff_id = $params['staff_id'];//员工工号
            $this->checkActivityInfo($activity_id);

            //变更记录
            $builder = $this->modelsManager->createBuilder();
            $builder->from(KpiStaffChangeLeaderLogModel::class);
            $builder->where('activity_id = :activity_id: and staff_id = :staff_id:', ['activity_id' => $activity_id, 'staff_id' => $staff_id]);
            //获取列表总数
            $builder->columns('count(id) as total');
            $count = intval($builder->getQuery()->getSingleResult()->total);
            //获取列表数据
            if ($count > 0) {
                $builder->columns('create_id, create_name, created_at, from_leader_id, from_leader_name, to_leader_id, to_leader_name');
                $offset = $page_size * ($page_num - 1);
                $builder->limit($page_size, $offset);
                $builder->orderBy('id DESC');
                $items = $builder->getQuery()->execute()->toArray();
            }
            $data['items'] = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (ValidationException $e) {
            $code    = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = $e->getMessage() . $e->getTraceAsString();
            $this->logger->warning('get-kpi_staff_change-list-failed:' . $message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 下级目标管理-列表
     * @param array $params 请求参数组
     * @param int $leader_id 上级ID
     * @return mixed
     */
    public function getActivityStaffListByLeader(array $params, int $leader_id)
    {
        $page_num  = empty($params['pageNum']) ? Enums\GlobalEnums::DEFAULT_PAGE_NUM : $params['pageNum'];
        $page_size = empty($params['pageSize']) ? Enums\GlobalEnums::DEFAULT_PAGE_SIZE : $params['pageSize'];
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];
        try {
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['kas' => KpiActivityStaffModel::class]);
            $builder->leftjoin(HrStaffInfoModel::class, 'staff.staff_info_id = kas.staff_id', 'staff');
            $builder->leftjoin(SysDepartmentModel::class, 'CAST(staff.node_department_id as CHAR) = department.id', 'department');
            $builder->leftjoin(HrJobTitleModel::class, 'cast(staff.job_title AS UNSIGNED) = job.id', 'job');
            $builder->leftjoin(SysStoreModel::class, 'store.id = staff.sys_store_id', 'store');
            $builder->where('(kas.leader_id = :leader_id:) OR (staff.manger = :leader_id: and kas.leader_id = :leader_id_default:)', ['leader_id' => $leader_id, 'leader_id_default' => 0]);
            $builder = $this->createStaffListByLeaderWhere($builder, $params);

            //获取列表总数
            $builder->columns('count(kas.id) as total');
            $count = intval($builder->getQuery()->getSingleResult()->total);
            //获取列表数据
            if ($count > 0) {
                $builder->columns('kas.activity_id, kas.staff_id, kas.stage, staff.name, department.name as node_department_name,department.deleted AS node_department_deleted, job.job_name, staff.job_title_grade_v2, staff.sys_store_id, store.name as sys_store_name, staff.wait_leave_state, staff.state, staff.hire_date, staff.manger');
                $offset = $page_size * ($page_num - 1);
                $builder->limit($page_size, $offset);
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->formatLeaderList($items);
            }
            $data['items'] = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('get-kpi-staff-list-by-leader-failed:' . $e->getMessage());
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 格式化下级目标管理-列表
     * @param array $list 活动列表
     * @return array
     */
    private function formatLeaderList(array $list)
    {
        if (!$list) {
            return [];
        }
        //直线上级工号组
        $manger_staff_ids = array_values(array_unique(array_filter(array_column($list, 'manger'))));
        //直线上级组
        $manger_staff_arr = [];
        if (!empty($manger_staff_ids)) {
            $manger_staff_list = HrStaffInfoModel::find([
                'columns' => 'staff_info_id, name',
                'conditions' => 'staff_info_id in ({staff_ids:array})',
                'bind' => ['staff_ids' => $manger_staff_ids]
            ])->toArray();
            $manger_staff_arr = array_column($manger_staff_list, null, 'staff_info_id');
        }
        foreach ($list as &$values) {
            //直线上级名称
            $values['manger_name'] = !empty($manger_staff_arr[$values['manger']]) ? $manger_staff_arr[$values['manger']]['name'] : '';
            //网点名称
            $values['sys_store_name'] = ($values['sys_store_id'] == KpiActivityStaffEnums::STORE_HEADER_OFFICE_ID) ? KpiActivityStaffEnums::STORE_HEADER_OFFICE_NAME : $values['sys_store_name'];
            //在职状态
            $state = ($values['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES) ? StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE : $values['state'];
            $values['state_text'] = isset(StaffInfoEnums::$staff_state[$state]) ? static::$t[StaffInfoEnums::$staff_state[$state]] : '';
            //职级
            $values['job_title_grade_text'] = StaffInfoEnums::$config_job_title_grade_v2[$values['job_title_grade_v2']] ?? '';
            //入职日期
            $values['hire_date'] = $values['hire_date'] ? date('Y-m-d', strtotime($values['hire_date'])) : '';
            $values['node_department_name'] = (CommonService::getInstance())->getDepartmentNameView([
                'name' => $values['node_department_name'],
                'deleted' => $values['node_department_deleted']
            ]);
        }
        return $list;
    }

    /**
     * 下级目标管理-列表-查询条件
     * @param object $builder 查询对象
     * @param array $condition 请求参数组
     * @return mixed
     */
    public function createStaffListByLeaderWhere($builder, $condition)
    {
        $activity_id = $condition['activity_id'] ?? 0;//活动ID
        $name = $condition['name'] ?? '';//工号/名称/昵称
        $node_department_id = $condition['department_id'] ?? 0;//部门
        $job_id = $condition['job_id'] ?? 0;//职位
        $job_title_grade = $condition['job_title_grade'] ?? [];//职级
        $sys_store_id = $condition['sys_store_id'] ?? '';//网点
        $state = $condition['state'] ?? [];//在职状态
        $hire_date_start = $condition['hire_date_start'] ?? '';//入职日期起始
        $hire_date_end = $condition['hire_date_end'] ?? '';//入职日期结束

        $builder->andWhere('kas.activity_id = :activity_id: and kas.stage != :stage_default:', ['activity_id' => $activity_id, 'stage_default' => KpiActivityStaffEnums::STAGE_DEFAULT]);

        //工号/名称/昵称
        if (!empty($name)) {
            $builder->andWhere('kas.staff_id LIKE :name: OR staff.name LIKE :name: OR staff.nick_name  LIKE :name:', ['name' => '%' . $name . '%']);
        }

        //部门
        if (!empty($node_department_id)) {
            //按照部门查找,需要找该部门以及下属子部门的所有信息
            $department_service = new DepartmentService();
            $department_ids = $department_service->getChildrenListByDepartmentIdV2($node_department_id, true);
            array_push($department_ids, $node_department_id);
            $builder->inWhere('department.id', $department_ids);
        }

        //职位
        if (!empty($job_id)) {
            $builder->andWhere('job.id = :job_id:', ['job_id' => $job_id]);
        }

        //职级
        if (!empty($job_title_grade)) {
            $builder->inWhere('staff.job_title_grade_v2', $job_title_grade);
        }

        //网点
        if (!empty($sys_store_id)) {
            $builder->andWhere('staff.sys_store_id = :sys_store_id:', ['sys_store_id' => $sys_store_id]);
        }

        //在职状态
        if (!empty($state)) {
            //在职状态搜索筛选待离职
            $state_where = '';
            $state_condition = [];
            if (in_array(StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE, $state)) {
                //存在待离职检索
                $state_where = 'staff.wait_leave_state = :wait_leave_state: and staff.state = :state:';
                $state_condition['wait_leave_state'] = StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES;
                $state_condition['state'] = StaffInfoEnums::STAFF_STATE_IN;
                //从在职状态组中移除
                $state = array_diff($state, [StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE]);
            }
            if (!empty($state)) {
                //在职、离职、停职
                $state_where .= $state_where ? ' OR (staff.state in ({states:array}) and staff.wait_leave_state = :leave_state:)' : 'staff.state in ({states:array}) and staff.wait_leave_state = :leave_state:';
                $state_condition['states'] = array_values($state);
                $state_condition['leave_state'] = StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_NO;
            }
            $builder->andWhere($state_where, $state_condition);
        }

        //入职日期起始与结束
        if (!empty($hire_date_start) && !empty($hire_date_end)) {
            $builder->betweenWhere('staff.hire_date', $hire_date_start . ' 00:00:00', $hire_date_end . ' 23:59:59');
        }
        return $builder;
    }

    /**
     * 检测活动合理性
     * @param int $activity_id 活动ID
     * @return mixed
     * @throws ValidationException
     */
    public function checkActivityInfo(int $activity_id)
    {
        //获取活动信息
        $kpi_activity_service = new ActivityService();
        return $kpi_activity_service->checkActivityInfo($activity_id);
    }

    /**
     * 检测员工是否参与KPI活动以及各身份鉴权
     * @param int $activity_id 活动ID
     * @param int $staff_id 员工工号
     * @param int $login_user_id 当前登陆者员工工号
     * @param int $type 鉴权类型0=>PPM，1=>直线上级，2=>部门负责人，3=>HRBP
     * @return mixed
     * @throws ValidationException
     */
    public function checkActivityStaff($activity_id, $staff_id, $login_user_id = 0, $type = KpiActivityStaffEnums::PERMISSION_PPM)
    {
        $staff_info = KpiActivityStaffModel::findFirst([
            'conditions' => 'activity_id = :activity_id: AND staff_id = :staff_id: and stage != :stage_default:',
            'bind' => ['activity_id' => $activity_id, 'staff_id' => $staff_id, 'stage_default' => KpiActivityStaffEnums::STAGE_DEFAULT],
        ]);
        if (empty($staff_info)) {
            //用户没有参与此次KPI活动
            throw new ValidationException(static::$t->_('kpi_activity_staff_not_found'), ErrCode::$VALIDATE_ERROR);
        }
        //直线上级、部门负责人、HRBP鉴权
        if ($type == KpiActivityStaffEnums::PERMISSION_LEADER) {
            //直线上级查看则需要鉴权改登陆者是否是操作员工的直线上级
            PermissionService::getInstance()->judgeLeaderRange($staff_id, $staff_info->leader_id, $login_user_id);
        } else if ($type == KpiActivityStaffEnums::PERMISSION_DEPARTMENT) {
            //团队负责人查看则需要鉴权改登陆者是否在该负责人管辖范围内
            PermissionService::getInstance()->judgeDepartmentRange($staff_id, $login_user_id);
        } else if ($type == KpiActivityStaffEnums::PERMISSION_HRBP) {
            //HRBP查看则需要鉴权改登陆者是否在该HRBP管辖范围内
            PermissionService::getInstance()->judgeHrbpAreasRange($staff_id, $login_user_id);
        }
        return $staff_info;
    }

    /**
     * 目标制定人鉴权-针对于批量操作员工情况【例如引用模板、批量提交】
     * @param int $activity_id 活动ID
     * @param array $staff_ids 员工工号组
     * @param int $leader_id 目标制定人
     * @return mixed
     * @throws ValidationException
     */
    public function checkLeaderPermission(int $activity_id, array $staff_ids, int $leader_id)
    {
        //给多个员工引用模板,需要鉴权是否隶属于当前目标制定人
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['kas' => KpiActivityStaffModel::class]);
        $builder->leftJoin(HrStaffInfoModel::class, 'staff.staff_info_id = kas.staff_id', 'staff');
        $builder->where('kas.activity_id = :activity_id: and stage != :stage_default:', ['activity_id' => $activity_id, 'stage_default' => KpiActivityStaffEnums::STAGE_DEFAULT]);
        $builder->inWhere('kas.staff_id', $staff_ids);
        $builder->andWhere('(kas.leader_id = :leader_id:) OR (staff.manger = :leader_id: and kas.leader_id = :leader_id_default:)', ['leader_id' => $leader_id, 'leader_id_default' => 0]);
        $builder->columns('kas.staff_id, kas.stage');
        $permission_staff_list = $builder->getQuery()->execute()->toArray();

        if (empty(array_column($permission_staff_list, 'staff_id'))) {
            //如果不存在有权限的工号可引用模板给予提示
            throw new ValidationException(static::$t->_('kpi_activity_staff_has_not_permission'), ErrCode::$VALIDATE_ERROR);
        }
        return $permission_staff_list;
    }

    /**
     * 检测员工kpi指标数量是否已达到50
     * @param int $activity_id  活动ID
     * @param int $staff_id 员工工号
     * @param int $add_count 添加数量
     * @return bool
     * @throws ValidationException
     */
    public function checkStaffIndicatorsCount($activity_id, $staff_id, $add_count = 1)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns(['count(id) as total']);
        $builder->from(KpiStaffIndicatorsRelModel::class);
        $builder->andWhere('activity_id = :activity_id: and staff_id = :staff_id:', ['activity_id' => $activity_id, 'staff_id' => $staff_id]);
        $count = intval($builder->getQuery()->getSingleResult()->total);
        if (($count+$add_count) > KpiTemplateEnums::MAX_INDICATORS_COUNT) {
            throw new ValidationException(static::$t->_('kpi_indicator_max'), ErrCode::$VALIDATE_ERROR);
        }
        return true;
    }

    /**
     * 获取员工个人KPI详情页信息
     * @param array $params['activity_id'=>'活动ID','staff_id'=>'员工工号']
     * @param array $user 当前登陆者信息组
     * @param int $type 鉴权类型0=>PPM，1=>直线上级，2=>部门负责人，3=>HRBP
     * @return array
     */
    public function getActivityStaffInfo(array $params, array $user, int $type = 0)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            $activity_id = $params['activity_id'];//活动ID
            $this->checkActivityInfo($activity_id);
            //获取员工基本信息
            $staff_id = $params['staff_id'];//员工工号
            $staff_info = $this->checkActivityStaff($activity_id, $staff_id, $user['id'], $type);
            $data = $this->formatStaffInfo($staff_info);
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-leader-kpi_staff-info-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data??[],
        ];
    }

    /**
     * 格式化员工信息
     * @param object $activity_staff_info KPI活动员工信息
     * @return array
     */
    private function formatStaffInfo($activity_staff_info)
    {
        //获取员工基本信息
        $base_staff_info = StaffService::getInstance()->getStaffInfo($activity_staff_info->staff_id);
        $job_title_grade = 0;//职级
        $job_title_grade_text = "";//职级文案
        if ($base_staff_info) {
            $job_title_grade = $base_staff_info['job_title_grade_v2'];
            $job_title_grade_text = static::$t->_(StaffInfoEnums::$config_job_title_grade_v2[$job_title_grade]);
        }
        //获取电子签附件
        $confirm_at = "";
        $confirm_sign_addr = "";
        //确认完毕，目标执行
        if($activity_staff_info->stage == KpiActivityStaffEnums::STAGE_DONE && $activity_staff_info->sys_attachment_id) {
            //存在数据且确认过签字
            $attachment_info = SysAttachmentModel::findFirst([
                'conditions' => 'id=:id: AND deleted = :deleted:',
                'bind' => ['id' => $activity_staff_info->sys_attachment_id, 'deleted' => Enums\GlobalEnums::IS_NO_DELETED],
                'columns' => ['bucket_name', 'object_key']
            ]);

            //获取附件域名地址
            $img_prefix = $this->getDI()->get('config')->application->img_prefix;
            $confirm_sign_addr = !empty($attachment_info) ? $img_prefix . $attachment_info->object_key : '';
            $confirm_at = !is_null($activity_staff_info->confirm_at) && $activity_staff_info->confirm_at != '0000-00-00 00:00:00' ? date('Y/m/d', strtotime($activity_staff_info->confirm_at)) : "";
        }
        if ($base_staff_info['state'] == StaffInfoEnums::STAFF_STATE_IN && $base_staff_info['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES) {
            //待离职
            $base_staff_info['state'] = StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE;
        }
        if ($activity_staff_info->leader_id == 0) {
            //未变更过目标制定人，则需要查询直线上级
            $leader_info = StaffService::getInstance()->getStaffInfoByStaffId($base_staff_info['manger']);
            if (!empty($leader_info)) {
                $activity_staff_info->leader_id = $leader_info->staff_info_id;
                $activity_staff_info->leader_name = $leader_info->name;
            }
        }
        //获取头像
        $staff_items = (new HrStaffRepository())->getStaffItems([$activity_staff_info->staff_id]);
        $head_portrait = $staff_items && isset($staff_items[0]) && isset($staff_items[0]['value']) ? $staff_items[0]['value'] : '';
        $head_portrait_url = !empty($head_portrait) ? gen_file_url(['object_key' => $head_portrait]) : '';
        return [
            'activity_id' => $activity_staff_info->activity_id,
            'staff_id' => $activity_staff_info->staff_id,
            'head_portrait' => $head_portrait_url,
            'staff_name' => $base_staff_info['name'],
            'node_department_name' => (CommonService::getInstance())->getDepartmentNameView([
                'name'      => $base_staff_info['node_department_name'],
                'deleted'   => $base_staff_info['node_department_deleted']
            ]),
            'job_name' => $base_staff_info['job_name'],
            'leader_id' => $activity_staff_info->leader_id,
            'leader_name' => $activity_staff_info->leader_name,
            'stage' =>$activity_staff_info->stage,
            'stage_text' => static::$t->_(KpiActivityStaffEnums::$kpi_activity_staff_stage[$activity_staff_info->stage]),
            'job_title_grade' => $job_title_grade,
            'job_title_grade_text'=>$job_title_grade_text,
            'state' => $base_staff_info['state'],
            'state_text'=>static::$t->_(StaffInfoEnums::$staff_state[$base_staff_info['state']]),
            'confirm_at' => $confirm_at,
            'confirm_sign_addr' => $confirm_sign_addr
        ];
    }

    /**
     * 检测各项KPI指标的重要度之和是否为100%
     * @param int $activity_id 活动ID
     * @param int $staff_id 员工工号
     * @return bool
     * @throws ValidationException
     */
    public function checkSumImportance(int $activity_id, int $staff_id)
    {
        $rel_info = KpiStaffIndicatorsRelModel::findFirst([
            'conditions' => 'activity_id = :activity_id: and staff_id = :staff_id:',
            'bind' => ['activity_id' => $activity_id, 'staff_id' => $staff_id],
            'columns' => 'SUM(importance) AS importance_sum'
        ]);
        if (empty($rel_info) || $rel_info->importance_sum != KpiTemplateEnums::IMPORTANCE_SUM) {
            throw new ValidationException(static::$t->_('kpi_activity_staff_importance_error'), ErrCode::$VALIDATE_ERROR);
        }
        return true;
    }

    /**
     * 下级目标管理～提交
     * @param array $params['activity_id'=>'活动ID','staff_id'=>'员工工号']
     * @param array $user 当前登陆者用户信息
     * @param int $type 鉴权类型0=>PPM，1=>直线上级，2=>部门负责人，3=>HRBP
     * @return array
     */
    public function submitStaffKpi(array $params, array $user, int $type)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        //提交审核状态
        $kpi_staff_status_result = false;
        try {
            //获取活动信息
            $activity_id = $params['activity_id'];//活动ID
            $staff_id = $params['staff_id'];//员工工号
            //检测活动信息
            $this->validationActivity(['activity_id' => $activity_id]);
            //检测员工是否参与KPI活动
            $staff_info = $this->checkActivityStaff($activity_id, $staff_id, $user['id'], $type);
            //当前阶段非“待制定目标”,则提示 “已提交，请勿重复操作”
            if ($staff_info->stage != KpiActivityStaffEnums::STAGE_LEADER) {
                throw new ValidationException(static::$t->_('kpi_activity_staff_had_audit'), ErrCode::$VALIDATE_ERROR);
            }
            //检测各项KPI指标的重要度之和是否为100%
            $this->checkSumImportance($activity_id, $staff_id);
            //提交
            $kpi_staff_status_result = $staff_info->i_update([
                'stage' => KpiActivityStaffEnums::STAGE_STAFF, //待确认目标
                'submit_at' => date('Y-m-d H:i:s'),//提交时间
                'updated_at' => date('Y-m-d H:i:s')//更新时间
            ]);
            if ($kpi_staff_status_result === false) {
                throw new BusinessException('KPI员工提交失败= ' . json_encode($staff_info, JSON_UNESCAPED_UNICODE) . ';可能的原因是：' . get_data_object_error_msg($staff_info), ErrCode::$BUSINESS_ERROR);
            }
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $message;
        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('kpi_leader-staff-submit-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $kpi_staff_status_result ? true : false,
        ];
    }

    /**
     * 下级目标管理～修改指标
     * @param array $params['activity_id'=>'活动ID','staff_id'=>'员工工号']
     * @param array $user 当前登陆者用户信息
     * @param int $type 鉴权类型0=>PPM，1=>直线上级，2=>部门负责人，3=>HRBP
     * @return array
     */
    public function updateStaffKpi(array $params, array $user, int $type)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        //提交审核状态
        $kpi_staff_status_result = false;
        try {
            $activity_id = $params['activity_id'];//活动ID
            $staff_id = $params['staff_id'];//员工工号
            //检测活动信息
            $this->validationActivity(['activity_id' => $activity_id]);
            //检测员工是否参与KPI活动
            $staff_info = $this->checkActivityStaff($activity_id, $staff_id, $user['id'], $type);
            //当前阶段为“待制定目标”,则提示 “待制定目标，无需修改指标”
            if ($staff_info->stage == KpiActivityStaffEnums::STAGE_LEADER) {
                throw new ValidationException(static::$t->_('kpi_activity_staff_not_submit'), ErrCode::$VALIDATE_ERROR);
            }
            //修改指标
            $kpi_staff_status_result = $staff_info->i_update([
                'stage' => KpiActivityStaffEnums::STAGE_LEADER, //待制定目标
                'submit_at' => null,//提交时间
                'confirm_at' => null,//确认时间
                'updated_at' => date('Y-m-d H:i:s')//更新时间
            ]);
            if ($kpi_staff_status_result === false) {
                throw new BusinessException('KPI员工修改指标失败= ' . json_encode($staff_info, JSON_UNESCAPED_UNICODE) . ';可能的原因是：' . get_data_object_error_msg($staff_info), ErrCode::$BUSINESS_ERROR);
            }
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $message;
        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('kpi_leader-staff-update-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $kpi_staff_status_result ? true : false,
        ];
    }

    /**
     * 导出员工KPI目标
     * @param array $params['activity_id'=>'活动ID','staff_id'=>'员工工号']
     * @param array $user 当前登陆者用户信息
     * @param int $type 鉴权类型0=>PPM，1=>直线上级，2=>部门负责人，3=>HRBP
     * @return array
     */
    public function exportStaffKpi(array $params, array $user, int $type = 0)
    {
        $code = ErrCode::$SUCCESS;
        $message = "";
        try {
            set_time_limit(0);
            $activity_id = $params['activity_id'];//活动ID
            $staff_id = $params['staff_id'];//员工工号
            $kpi_activity_info = $this->checkActivityInfo($activity_id);
            $staff_info = $this->checkActivityStaff($activity_id, $staff_id, $user['id'], $type);
            $base_staff_info = $this->formatStaffInfo($staff_info);
            $file_name = static::$t->_('kpi_activity_staff_export_title', ['years' => $kpi_activity_info->years, 'period'=>static::$t->_(KpiActivityEnums::$kpi_activity_period[$kpi_activity_info->period]), 'staff_name' => $base_staff_info['staff_name']]);
            $excel_header_notice = [
                'info'=>static::$t->_('kpi_activity_staff_base_info'),
                'indicators'=>static::$t->_('kpi_indicators'),
                'values'=>static::$t->_('kpi_values'),
                'confirm'=>static::$t->_('kpi_staff_confirm'),
            ];
            $excel_extra_config = [
                'file_name' =>  $file_name.'.xlsx',
                'column_width' => 50,
                'row_height' => 30,
                'header_column_font_color' => 0x020101,
                'header_columns' => ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'],
                'header_column_row' => 7,
                'end_column_char' => 'H',
                'batch_head_notice' => $excel_header_notice
            ];
            //基本信息
            $info_rows = [[
                static::$t->_('kpi_staff_id'), //工号
                $base_staff_info['staff_id'],
                '',
                static::$t->_('kpi_staff_name'), //姓名
                $base_staff_info['staff_name'],
                '',
                static::$t->_('kpi_staff_grade'), //职级
                $base_staff_info['job_title_grade_text']
            ],[
                static::$t->_('kpi_staff_department'),//部门
                $base_staff_info['node_department_name'],
                '',
                static::$t->_('kpi_staff_job'), //职位
                $base_staff_info['job_name'],
                '',
                static::$t->_('kpi_staff_leader'), //直接上级
                $base_staff_info['leader_name'],
            ]];
            //关键业绩KPI
            $indicators_header_column = [
                static::$t->_('kpi_indicators_serial_number'),//序号
                static::$t->_('kpi_indicators_name'),//KPI目标
                static::$t->_('kpi_indicators_method'),//测量方法
                static::$t->_('kpi_indicators_target'),//目标
                static::$t->_('kpi_indicators_standard'),//测量标准
                static::$t->_('kpi_indicators_importance'),//重要度（百分比）
            ];
            $indicators_rows = [];
            $indicators_list = $this->getStaffIndicatorsListByActivityStaffId($activity_id, $staff_id);
            if ($indicators_list) {
                foreach ($indicators_list as $key=>$indicator) {
                    $indicators_rows[] = [
                        (string)($key+1),
                        $indicator['name'],
                        $indicator['method'],
                        $indicator['target'],
                        $indicator['standard'],
                        $indicator['importance'],
                    ];
                }
            }
            array_unshift($indicators_rows, $indicators_header_column);
            //价值观
            $values_header_column = [
                static::$t->_('kpi_values_concept'), //理念
                static::$t->_('kpi_values_criterion'), //行为准则
                static::$t->_('kpi_values_score'), //得分
            ];
            $values_rows = [];
            $values_list = ValuesService::getInstance()->getValuesList($activity_id);
            foreach ($values_list as $key => $item) {
                $values_rows[] = [
                    $item['concept'],
                    $item['criterion'],
                    $item['score'],
                ];
            }
            array_unshift($values_rows, $values_header_column);
            //员工确认
            $staff_confirm_rows = [[
                static::$t->_('kpi_staff_confirm_at'),//确认时间
                $base_staff_info['confirm_at'], //签字时间
                static::$t->_('kpi_staff_sign'),//员工签字
                $base_staff_info['confirm_sign_addr']//签名地址
            ]];
            //获取xls的扩展版本
            $xls_version = phpversion('xlswriter');
            if (version_compare($xls_version, '1.3.6') >= 0) {
                //开发、测试、k8s的版本都是 1.3.4.1，tra和pro的高于这个版本会有兼容性问题,故而做这个判断
                array_unshift($indicators_rows, []);
                array_unshift($values_rows, []);
                array_unshift($staff_confirm_rows, []);
            }
            $row_values = [
                'info' => $info_rows,
                'indicators' => $indicators_rows,
                'values' => $values_rows,
                'confirm' => $staff_confirm_rows
            ];
            $path = self::kpiExcelToFile($excel_header_notice, $row_values,  $excel_extra_config);
            $oss_result = OssHelper::uploadFile($path);
            $result = $oss_result['object_url'];
        } catch (ValidationException $e) {
            $code    = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = $e->getMessage() . $e->getTraceAsString();
            $this->logger->warning('download-kpi_staff_indicators-failed:' . $message . $e->getTraceAsString());
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $result??"",
        ];
    }

    /**
     * 获取某个员工某个活动下的业绩KPI指标
     * @param int $activity_id 活动ID
     * @param int $staff_id 员工工号
     * @return mixed
     */
    public function getStaffIndicatorsListByActivityStaffId(int $activity_id, int $staff_id)
    {
        return KpiStaffIndicatorsRelModel::find([
            'conditions' => "activity_id = :activity_id: and staff_id = :staff_id:",
            'bind' => [ 'activity_id' => $activity_id, 'staff_id' => $staff_id],
            'columns' => ['id', 'name', 'importance', 'method', 'target', 'standard'],
        ])->toArray();
    }

    /**
     * 关键业绩KPI指标
     * @param array $params['activity_id'=>'活动ID','staff_id'=>'员工工号']
     * @param array $user 当前登陆者用户信息
     * @param int $type 鉴权类型0=>PPM，1=>直线上级，2=>部门负责人，3=>HRBP
     * @return array
     */
    public function getStaffIndicatorsList(array $params, array $user, int $type = 0)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            $activity_id = $params['activity_id'];//活动ID
            $staff_id = $params['staff_id'];//员工工号
            $this->checkActivityInfo($activity_id);
            $this->checkActivityStaff($activity_id, $staff_id, $user['id'], $type);
            $data = $this->getStaffIndicatorsListByActivityStaffId($activity_id, $staff_id);
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = $e->getMessage() . $e->getTraceAsString();
            $this->logger->warning('download-kpi_staff_indicators-failed:' . $message . $e->getTraceAsString());
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data ?? [],
        ];
    }

    /**
     * leader视角非待提交阶段不可对员工指标做操作
     * @param object $staff_info 参与考核员工信息
     * @throws ValidationException
     */
    private function checkLeaderStaffStage($staff_info)
    {
        if ($staff_info->stage != KpiActivityStaffEnums::STAGE_LEADER) {
            //员工阶段非待提交阶段
            throw new ValidationException(static::$t->_('kpi_activity_staff_had_submit'), ErrCode::$VALIDATE_ERROR);
        }
    }

    /**
     * 下级目标管理～新建KPI指标
     * @param array $params 新建指标参数
     * @param array $user 当前登陆者用户信息
     * @param int $type 鉴权类型0=>PPM，1=>直线上级，2=>部门负责人，3=>HRBP
     * @return array
     */
    public function addIndicators(array $params, array $user, int $type)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        //开启事务
        $db = $this->getDI()->get('db_backyard');
        $db->begin();
        try {
            $activity_id = $params['activity_id'];//活动ID
            $staff_id = $params['staff_id'];//员工工号
            //检测活动信息
            $this->validationActivity(['activity_id' => $activity_id]);
            //检测员工是否参与此次活动
            $staff_info = $this->checkActivityStaff($activity_id, $staff_id, $user['id'], $type);
            //检测leader视角员工阶段
            $this->checkLeaderStaffStage($staff_info);
            //检测员工kpi指标数量是否已达到100
            $this->checkStaffIndicatorsCount($activity_id, $staff_id);
            //员工kpi指标基本信息
            $kpi_staff_indicators_data = [
                'activity_id' => $activity_id,//活动ID
                'staff_id' => $staff_id,//员工工号
                'name' => $params['name'],//KPI目标
                'importance' => $params['importance'],//重要度
                'method' => $params['method'],//测量方法
                'target' => $params['target'],//目标
                'standard' => $params['standard'],//测量标准
                'created_at' => date('Y-m-d H:i:s'),//创建时间
                'updated_at' => date('Y-m-d H:i:s')//更新时间
            ];
            $kpi_staff_indicators_model = new KpiStaffIndicatorsRelModel();
            $add_indicators_result = $kpi_staff_indicators_model->i_create($kpi_staff_indicators_data);
            if ($add_indicators_result === false) {
                throw new BusinessException('给员工新建KPI指标失败 = ' . json_encode($kpi_staff_indicators_data, JSON_UNESCAPED_UNICODE) . ';可能的原因是：' . get_data_object_error_msg($kpi_staff_indicators_model), ErrCode::$BUSINESS_ERROR);
            }
            //变更指标需要更新阶段以及状态
            $this->updateIndicatorStage($staff_info, $db);
            $data = $kpi_staff_indicators_model->toArray();
            //删除掉不需要返回的数据
            unset($data['channel'], $data['created_at'], $data['updated_at']);
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            //事务回滚
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->warning('kpi_staff_indicators-create-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data ?? []
        ];
    }

    /**
     * 变更指标需要更新阶段以及状态
     * @param object $staff_info 参与kpi考核员工信息
     * @param object $db db类
     * @throws BusinessException
     */
    private function updateIndicatorStage($staff_info, $db)
    {
        //如果当前员工阶段不处于待制定目标，添加了指标，需要目标制定人重新提交
        if ($staff_info->stage != KpiActivityStaffEnums::STAGE_LEADER) {
            $kpi_staff_indicators_edit_result = $staff_info->i_update([
                'stage' => KpiActivityStaffEnums::STAGE_LEADER, //当前阶段修改为：上级填写
                'updated_at' => date('Y-m-d H:i:s')//更新时间
            ]);
            if ($kpi_staff_indicators_edit_result === false) {
                throw new BusinessException('修改员工指标-阶段变更为待制定目标失败 = ' . json_encode($staff_info, JSON_UNESCAPED_UNICODE) .';可能原因是：' . get_data_object_error_msg($staff_info), ErrCode::$BUSINESS_ERROR);
            }
        }
        //更新成功，事物提交
        $db->commit();
    }

    /**
     * 检测KPI指标信息
     * @param int $id kpi指标ID
     * @param int $activity_id 活动ID
     * @param int $staff_id 员工工号
     * @return mixed
     * @throws ValidationException
     */
    public function checkStaffIndicatorsInfo(int $id, int $activity_id, int $staff_id)
    {
        $kpi_indicators_info = KpiStaffIndicatorsRelModel::findFirst([
            'conditions' => 'id = :id: and activity_id = :activity_id: and staff_id = :staff_id:',
            'bind' => ['id' => $id, 'activity_id' => $activity_id, 'staff_id' => $staff_id]
        ]);
        if (empty($kpi_indicators_info)) {
            throw new ValidationException(static::$t->_('kpi_indicators_not_found'), ErrCode::$VALIDATE_ERROR);
        }
        return $kpi_indicators_info;
    }

    /**
     * 下级目标管理～编辑KPI指标
     * @param array $data 指标更新的数据
     * @param array $user 当前登陆者用户信息
     * @param int $type 鉴权类型0=>PPM，1=>直线上级，2=>部门负责人，3=>HRBP
     * @return array
     */
    public function editStaffIndicators(array $data, array $user, int $type)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        //编辑信息的操作结果
        $kpi_indicators_edit_result = false;
        //开启事务
        $db = $this->getDI()->get('db_backyard');
        $db->begin();
        try {
            $activity_id = $data['activity_id'];//活动ID
            $staff_id = $data['staff_id'];//员工工号
            //检测活动信息
            $this->validationActivity(['activity_id' => $activity_id]);
            //检测员工是否参与此次活动
            $staff_info = $this->checkActivityStaff($activity_id, $staff_id, $user['id'], $type);
            //检测leader视角员工阶段
            $this->checkLeaderStaffStage($staff_info);
            //获取指标信息
            $kpi_indicators_info = $this->checkStaffIndicatorsInfo($data['id'], $activity_id, $staff_id);
            $kpi_indicators_edit_result = $kpi_indicators_info->i_update([
                'name' => $data['name'],//KPI目标
                'importance' => $data['importance'],//重要度
                'method' => $data['method'],//测量方法
                'target' => $data['target'],//目标
                'standard' => $data['standard'],//测量标准
                'updated_at' => date('Y-m-d H:i:s')//更新时间
            ]);
            if ($kpi_indicators_edit_result === false) {
                throw new BusinessException('员工编辑KPI指标失败 = ' . json_encode($data, JSON_UNESCAPED_UNICODE) . ';可能的原因是：' . get_data_object_error_msg($kpi_indicators_info), ErrCode::$BUSINESS_ERROR);
            }
            $this->updateIndicatorStage($staff_info, $db);
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $message;
        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            //事务回滚
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->warning('edit-kpi-staff_indicators-info-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $kpi_indicators_edit_result? true : false,
        ];
    }

    /**
     * 下级目标管理～删除KPI指标
     * @param array $data['id'=>'指标ID','activity_id'=>'活动ID','staff_id'=>'员工工号']
     * @param array $user 当前登陆者用户信息
     * @param int $type 鉴权类型0=>PPM，1=>直线上级，2=>部门负责人，3=>HRBP
     * @return array
     */
    public function delStaffIndicators(array $data, array $user, int $type)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        //指标删除状态
        $kpi_indicators_del_result = false;
        //开启事务
        $db = $this->getDI()->get('db_backyard');
        $db->begin();
        try {
            $activity_id = $data['activity_id'];//活动ID
            $staff_id = $data['staff_id'];//员工工号
            //检测活动信息
            $this->validationActivity(['activity_id' => $activity_id]);
            //检测员工是否参与此次活动
            $staff_info = $this->checkActivityStaff($activity_id, $staff_id, $user['id'], $type);
            //检测leader视角员工阶段
            $this->checkLeaderStaffStage($staff_info);
            //获取指标信息
            $kpi_indicators_info = $this->checkStaffIndicatorsInfo($data['id'], $activity_id, $staff_id);
            //删除指标
            $kpi_indicators_del_result = $kpi_indicators_info->delete();
            if ($kpi_indicators_del_result === false) {
                throw new BusinessException('员工删除KPI指标失败 = ' . json_encode($data, JSON_UNESCAPED_UNICODE) . ';可能的原因是：' . get_data_object_error_msg($kpi_indicators_info), ErrCode::$BUSINESS_ERROR);
            }
            //变更指标需要更新阶段以及状态
            $this->updateIndicatorStage($staff_info, $db);
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $message;
        }  catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            //事务回滚
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->warning('kpi_staff_indicators-del-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $kpi_indicators_del_result ? true : false,
        ];
    }

    /**
     * 下级目标管理～导入指标
     * @param array $params['activity_id'=>'活动ID','staff_id'=>'员工工号']
     * @param array $excel_file 指标文件
     * @param array $user 当前登陆者用户信息
     * @param int $type 鉴权类型0=>PPM，1=>直线上级，2=>部门负责人，3=>HRBP
     * @return array
     */
    public function importIndicators(array $params, array $excel_file, array $user, int $type)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $result = false;
        //开启事务
        $db = $this->getDI()->get('db_backyard');
        $db->begin();
        try {
            //是否上传了文件
            if (empty($excel_file)) {
                throw new ValidationException(static::$t->_('kpi_indicators_file_error'), ErrCode::$VALIDATE_ERROR);
            }
            $activity_id = $params['activity_id'];//活动ID
            $staff_id = $params['staff_id'];//员工工号
            //检测活动信息
            $this->validationActivity(['activity_id' => $activity_id]);
            //检测员工是否参与此次活动
            $staff_info = $this->checkActivityStaff($activity_id, $staff_id, $user['id'], $type);
            //检测leader视角员工阶段
            $this->checkLeaderStaffStage($staff_info);
            //检测KPI数据合理性
            [$is_error, $excel_data, $excel_header_column] = IndicatorsService::getInstance()->checkKpiIndicators($excel_file);
            if ($is_error === false) {
                //检测员工kpi指标数量是否已达到50
                $this->checkStaffIndicatorsCount($activity_id, $staff_id, count($excel_data));
                //开始插入kpi指标
                $kpi_indicators = [];
                foreach ($excel_data as $item) {
                    $kpi_indicators[] = [
                        'activity_id' => $activity_id,//活动ID
                        'staff_id' => $staff_id,//员工工号
                        'name' => $item[0],//KPI目标
                        'method' => $item[1],//测量方法
                        'target' => $item[2],//目标
                        'standard' => $item[3],//测量标准
                        'importance' => $item[4],//重要度
                        'created_at' => date('Y-m-d H:i:s'),//创建时间
                        'updated_at' => date('Y-m-d H:i:s')//更新时间
                    ];
                }
                $kpi_indicators_model = new KpiStaffIndicatorsRelModel();
                $kpi_indicators_add_result = $kpi_indicators_model->batch_insert($kpi_indicators, 'db_backyard');
                if ($kpi_indicators_add_result === false) {
                    throw new BusinessException('员工导入KPI指标失败 = ' . json_encode($kpi_indicators, JSON_UNESCAPED_UNICODE) . ';可能的原因是：' . get_data_object_error_msg($kpi_indicators_model), ErrCode::$BUSINESS_ERROR);
                }
                //变更指标需要更新阶段以及状态
                $this->updateIndicatorStage($staff_info, $db);
                $result = true;
            } else {
                //存在不合理的数据
                $new_excel_data = [];
                foreach ($excel_data as $item) {
                    $new_excel_data[] = array_values($item);
                }
                $excel_extra_config = [
                    'file_name' =>  'KPI导入模板 KPI Template แบบฟอร์มอัปโหลดKPI failed'.date('YmdHis').'.xlsx',
                    'column_width' => 15,
                ];
                $path = self::customizeExcelToFile($excel_header_column, $new_excel_data,  $excel_extra_config);
                $oss_result = OssHelper::uploadFile($path);
                if (!empty($oss_result['object_url'])) {
                    $code = 5;
                    $message = "上传文件失败，浏览器自动下载导入失败文件";
                    // 下载导入结果
                    $result = $oss_result['object_url'];
                }
            }
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $message;
        }  catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            //事务回滚
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->warning('kpi_staff-import-indicators-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $result,
        ];
    }

    /**
     * 下级目标管理～存为模版
     * @param array $params['activity_id'=>'活动ID','staff_id'=>'员工工号', 'name'=>'模版名称', 'content'=>'模版描述']
     * @param array $user 当前登陆者用户信息
     * @param int $type 鉴权类型0=>PPM，1=>直线上级，2=>部门负责人，3=>HRBP
     * @return array
     */
    public function saveTemplate(array $params, array $user, int $type)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        //开启事务
        $db = $this->getDI()->get('db_backyard');
        $db->begin();
        try {
            $activity_id = $params['activity_id'];//活动ID
            $staff_id = $params['staff_id'];//员工工号
            //检测活动信息
            $this->validationActivity(['activity_id' => $activity_id]);
            //检测员工是否参与此次活动
            $staff_info = $this->checkActivityStaff($activity_id, $staff_id, $user['id'], $type);
            //检测各项KPI指标的重要度之和是否为100%
            $this->checkSumImportance($activity_id, $staff_id);
            $name = $params['name'];
            $md5_name = md5($name);
            //检测模版名称是否存在
            TemplateService::getInstance()->checkTemplateName($md5_name, $user['id']);
            $kpi_template_data = [
                'name' => $name,//模版名称
                'md5_name' => $md5_name,//加密后的模版名称
                'content' => $params['content'],//模版描述
                'create_id' => $user['id'],//创建人工号
                'create_name' => $user['name'],//创建人姓名
                'status' => KpiTemplateEnums::STATUS_ENABLE,//模版状态：1草稿
                'created_at' => date('Y-m-d H:i:s'),//创建时间
                'updated_at' => date('Y-m-d H:i:s')//更新时间
            ];
            $kpi_template_model = new KpiTemplateModel();
            $add_template_result = $kpi_template_model->i_create($kpi_template_data);
            if ($add_template_result === false) {
                throw new BusinessException('员工存为模版失败 = ' . json_encode($kpi_template_data, JSON_UNESCAPED_UNICODE) . ';可能的原因是：' . get_data_object_error_msg($kpi_template_model), ErrCode::$BUSINESS_ERROR);
            }

            //获取该员工的所有kpi指标
            $add_indicators_list = $this->getStaffIndicatorsListByActivityStaffId($activity_id, $staff_id);
            //添加成功的模版ID
            $add_template_id = $kpi_template_model->id;
            //存在KPI指标则要复制指标
            foreach ($add_indicators_list as &$item) {
                unset($item['id']);
                $item['template_id'] = $add_template_id;
            }
            //批量插入
            $kpi_indicators_model = new KpiTemplateIndicatorsRelModel();
            $kpi_indicators_result = $kpi_indicators_model->batch_insert($add_indicators_list, 'db_backyard');
            if ($kpi_indicators_result === false) {
                throw new BusinessException('员工存为模版失败 = ' . json_encode($add_indicators_list, JSON_UNESCAPED_UNICODE). ';可能的原因是：' . get_data_object_error_msg($kpi_indicators_model), ErrCode::$BUSINESS_ERROR);
            }
            //批量添加指标成功，事物提交
            $db->commit();
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $message;
        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            //事务回滚
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->warning('kpi_leader-save-template-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $add_template_result ?? false,
        ];
    }

    /**
     * 员工引用模版
     * @param array $params 请求参数组
     * @param array $user 当前登陆员工信息组
     * @return array
     */
    public function quoteTemplate(array $params, array $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = '';
        //开启事务
        $db = $this->getDI()->get('db_backyard');
        $db->begin();
        try {
            $template_id = $params['template_id'];//模版ID
            $activity_id = $params['activity_id'];//活动ID
            $staff_ids = $params['staff_ids'];//员工工号
            //检测活动信息
            $this->validationActivity($params);

            //检测员工是否参与KPI活动
            if (count($staff_ids) == 1) {
                //给单个员工引用模版
                $staff_info = $this->checkActivityStaff($activity_id, $staff_ids[0], $user['id'], KpiActivityStaffEnums::PERMISSION_LEADER);
                //检测leader视角员工阶段
                $this->checkLeaderStaffStage($staff_info);
            } else {
                //给多个员工引用模板,需要鉴权是否隶属于当前目标制定人
                $staff_list = $this->checkLeaderPermission($activity_id, $staff_ids, $user['id']);
                $wait_submit_staff = [];
                foreach ($staff_list as $staff) {
                    if ($staff['stage'] == KpiActivityStaffEnums::STAGE_LEADER) {
                        $wait_submit_staff[] = $staff['staff_id'];
                    }
                }
                //只有待确认目标阶段的员工才能引用模板
                if (empty($wait_submit_staff)) {
                    throw new ValidationException(static::$t->_('kpi_activity_staff_had_submit'), ErrCode::$VALIDATE_ERROR);
                }
                $staff_ids = $wait_submit_staff;
            }

            //验证模版数据
            $this->validationTemplate($template_id, $user['id']);

            //验证模版指标数据
            $template_indicators = IndicatorsService::getInstance()->getTemplateIndicatorsList($template_id);
            $this->validationTemplateIndicators($template_indicators);

            //给符合条件的员工组绑定指标
            $insert_data = $this->createStaffIndicators($activity_id, $staff_ids, $template_indicators);
            //删除员工原来的指标
            $old_staff_indicator = KpiStaffIndicatorsRelModel::find([
                'conditions' => 'activity_id = :activity_id: and staff_id in ({staff_ids:array})',
                'bind' => ['activity_id' => $activity_id, 'staff_ids' => array_values($staff_ids)]
            ]);
            $old_staff_indicator_del = $old_staff_indicator->delete();
            if ($old_staff_indicator_del === false) {
                throw new BusinessException('引用模版-删除员工旧指标失败=' . json_encode($old_staff_indicator->toArray(), JSON_UNESCAPED_UNICODE) . get_data_object_error_msg($old_staff_indicator), ErrCode::$BUSINESS_ERROR);
            }

            //给员工赋予新的指标
            $kpi_staff_indicators_rel = new KpiStaffIndicatorsRelModel();
            $new_staff_indicator_ret = $kpi_staff_indicators_rel->batch_insert($insert_data, 'db_backyard');
            if ($new_staff_indicator_ret === false) {
                throw new BusinessException('引用模版-给员工赋予新指标失败=' . json_encode($insert_data, JSON_UNESCAPED_UNICODE) . get_data_object_error_msg($kpi_staff_indicators_rel), ErrCode::$BUSINESS_ERROR);
            }

            //给员工绑定模版ID
            $bind_template_to_staff = $db->updateAsDict(
                (new KpiActivityStaffModel())->getSource(),
                ['template_id' => $template_id, 'stage' => KpiActivityStaffEnums::STAGE_LEADER, 'submit_at' => null, 'confirm_at' => null, 'updated_at' => date('Y-m-d H:i:s')],
                ["conditions" => "activity_id = $activity_id and staff_id IN (".implode(',', $staff_ids).")"]
            );
            if ($bind_template_to_staff === false) {
                throw new BusinessException('引用模版-给员工绑定模版ID失败=' . json_encode($params, JSON_UNESCAPED_UNICODE) . get_data_object_error_msg($db), ErrCode::$BUSINESS_ERROR);
            }

            //事务提交
            $db->commit();
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $message;
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $message;
        }

        if (!empty($message)) {
            //事务回滚
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->warning('quote-Template-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $bind_template_to_staff ?? false,
        ];
    }

    /**
     * 组装员工kpi目标（引入模版）
     * @param int $activity_id 活动ID
     * @param array $staff_ids 员工工号组
     * @param array $template_indicators 模版指标列表
     * @return array
     */
    private function createStaffIndicators(int $activity_id, array $staff_ids, array $template_indicators)
    {
        $insert_data = [];
        foreach($staff_ids as $staff_id) {
            foreach($template_indicators as $indicator) {
                $insert_data[] = [
                    'activity_id' => $activity_id,
                    'staff_id' => $staff_id,
                    'channel' => KpiTemplateEnums::CHANNEL_TEMPLATE,
                    'name' => $indicator['name'],
                    'importance' => $indicator['importance'],
                    'method' => $indicator['method'],
                    'target' => $indicator['target'],
                    'standard' => $indicator['standard'],
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s'),
                ];
            }
        }
        return $insert_data;
    }

    /**
     * 验证活动是合理性以及是否结束
     * @param array $params 请求参数组
     * @return mixed
     * @throws ValidationException
     * <AUTHOR>
     */
    public function validationActivity($params)
    {
        //检测活动合理性
        $activity = $this->checkActivityInfo($params['activity_id']);
        //该活动已结束，不支持该操作
        if ($activity->status == KpiActivityEnums::STATUS_DONE) {
            throw new ValidationException(static::$t->_('kpi_activity_done'), ErrCode::$VALIDATE_ERROR);
        }
        return $activity->toArray();
    }

    /**
     * 验证模版数据
     * @param int $template_id 模版ID
     * @param int $create_id 创建者ID
     * @throws ValidationException
     */
    public function validationTemplate($template_id, $create_id)
    {
        $template = IndicatorsService::getInstance()->checkTemplate($template_id, $create_id);
        if ($template->status == KpiTemplateEnums::STATUS_DRAFT) {
            throw new ValidationException(static::$t->_('kpi_template_is_draft'), ErrCode::$VALIDATE_ERROR);
        }
    }

    /**
     * 验证模版指标数据合法性
     * @param array $template_indicators 指标组
     * @throws ValidationException
     */
    public function validationTemplateIndicators($template_indicators)
    {
        if (empty($template_indicators)) {
            throw new ValidationException(static::$t->_('kpi_template_indicators_empty'), ErrCode::$VALIDATE_ERROR);
        }
    }

    /**
     * 批量提交
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function batchSubmit(array $params, array $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = '';
        try{
            $activity_id = $params['activity_id'];//活动ID
            $staff_ids = $params['staff_ids'];//员工工号
            //检测活动信息
            $this->validationActivity($params);

            //给多个员工批量提交,需要鉴权是否隶属于当前目标制定人
            $staff_list = $this->checkLeaderPermission($activity_id, $staff_ids, $user['id']);
            //检测员工的提交状态,若全部提交,则提示请勿重复提交
            $stage_status = array_unique(array_column($staff_list, 'stage'));
            if (count($stage_status) == 1 && $stage_status[0] != KpiActivityStaffEnums::STAGE_LEADER) {
                throw new ValidationException(static::$t->_('kpi_activity_staff_had_audit'), ErrCode::$VALIDATE_ERROR);
            }

            //存在需要提交的员工
            $staff_ids = array_column($staff_list, 'staff_id');
            //获取员工指标重要度之和
            $importance_list = $this->getImportanceListByActivityStaffIds($activity_id, $staff_ids);
            //员工均未找到指标信息,则提示，检查重要度合计
            if (empty($importance_list)) {
                throw new ValidationException( static::$t->_('submit_audit_error.1'), ErrCode::$VALIDATE_ERROR);
            }

            //获取已提交、可以提交的员工组
            [$has_submit_staff_ids, $submit_staff_ids] = $this->createSubmitData($staff_list, array_column($importance_list, null, 'staff_id'));
            if (empty($submit_staff_ids)) {
                //若不存在满足提交条件的员工组,则提示，检查重要度合计
                throw new ValidationException( static::$t->_('submit_audit_error.1'), ErrCode::$VALIDATE_ERROR);
            }

            //批量提交
            $db = $this->getDI()->get('db_backyard');
            $bool = $db->updateAsDict(
                (new KpiActivityStaffModel())->getSource(),
                ['stage' => KpiActivityStaffEnums::STAGE_STAFF, 'submit_at' => date('Y-m-d H:i:s'), 'updated_at' => date('Y-m-d H:i:s')],
                ["conditions" => "activity_id = $activity_id and staff_id IN (" . implode(',', $submit_staff_ids) . ")"]
            );
            if ($bool === false) {
                throw new BusinessException('KPI员工批量提交失败= ' . json_encode($submit_staff_ids, JSON_UNESCAPED_UNICODE) . ';可能的原因是：' . get_data_object_error_msg($db), ErrCode::$BUSINESS_ERROR);
            }

            //提交过来的员工过滤掉已提交的
            if (count(array_diff($staff_ids, $has_submit_staff_ids)) != count($submit_staff_ids)) {
                //非全部提交，成功的message要提示部分失败
                throw new ValidationException( static::$t->_('submit_audit_error.3'), ErrCode::$VALIDATE_ERROR);
            }
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $message;
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $message;
        }
        if (!empty($real_message)) {
            $this->logger->warning('batch-submit-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $bool ?? false,
        ];
    }

    /**
     * 获取各员工指标重要度之和
     * @param int $activity_id 活动ID
     * @param array $staff_ids 员工工号组
     * @return array
     */
    public function getImportanceListByActivityStaffIds(int $activity_id, array $staff_ids)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(KpiStaffIndicatorsRelModel::class);
        $builder->columns('SUM(importance) as importance, staff_id');
        $builder->where('activity_id = :activity_id:', ['activity_id' => $activity_id]);
        $builder->inWhere('staff_id', $staff_ids);
        $builder->groupBy('staff_id');
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 构建提交数据
     * @param array $staff_list 提交员工列表
     * @param array $importance_list 员工指标列表
     * @return array
     */
    public function createSubmitData($staff_list, $importance_list)
    {
        //已提交员工组
        $has_submit_staff_ids = [];
        //需要提交员工组
        $submit_staff_ids = [];
        foreach ($staff_list as $staff) {
            //已提交的跳过
            if ($staff['stage'] != KpiActivityStaffEnums::STAGE_LEADER) {
                $has_submit_staff_ids[] = $staff['staff_id'];
                continue;
            }
            //未提交但员工没有设置指标或指标重要度之和非100%,跳过
            if (!isset($importance_list[$staff['staff_id']]) || $importance_list[$staff['staff_id']]['importance'] != KpiTemplateEnums::IMPORTANCE_SUM) {
                continue;
            }
            //需要提交的员工工号组
            $submit_staff_ids[] = $staff['staff_id'];
        }
        return [$has_submit_staff_ids, $submit_staff_ids];
    }

    /**
     * 获取某活动直接上级的待提交数量
     * @param $leader_id
     * @return int
     */
    public function getWaitingSubmitKPICount($leader_id)
    {
        $count = 0;
        try {
            $builder = $this->modelsManager->createBuilder();
            $builder->columns('COUNT(kas.id) AS total_count');
            $builder->from(['kas' => KpiActivityStaffModel::class]);
            $builder->leftJoin(KpiActivityModel::class, 'ka.id = kas.activity_id', 'ka');
            $builder->leftjoin(HrStaffInfoModel::class, 'staff.staff_info_id = kas.staff_id', 'staff');
            $builder->where('kas.stage = :stage: and ka.status = :status:', ['stage' => KpiActivityStaffEnums::STAGE_LEADER, 'status' => KpiActivityEnums::STATUS_ING]);
            $builder->andWhere('(kas.leader_id = :leader_id:) OR (staff.manger = :leader_id: and kas.leader_id = :leader_id_default:)', ['leader_id' => $leader_id, 'leader_id_default' => 0]);
            $count = $builder->getQuery()->getSingleResult()->total_count;
        } catch (\Exception $e) {
            $this->logger->warning('get-waiting-submit-kpi-count-failed:' . $e->getMessage());
        }
        return intval($count);
    }

    /**
     * 获取员工 push消息跳转页面链接地址
     * @param int $activity_id 活动ID
     * @return string
     */
    public function getStaffMessageScheme($activity_id)
    {
        $kpi_staff_detail_url = env('kpi_staff_detail_url','http://192.168.0.222:90/#/');
        return "flashbackyard://fe/html?url=" . urlencode($kpi_staff_detail_url.$activity_id);
    }

    /**
     * 批量提醒员工确认
     * @param array $data['staff_list'=>'批量发送消息员工组', 'activity_id'=>'活动ID', 'title'=>'消息标题翻译key', 'content'=>'消息内容翻译key'] 参数组
     * @return array
     */
    public function batchSendConfirmMsg(array $data)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            set_time_limit(0);
            //活动ID
            $activity_id = $data['activity_id'];
            //获取活动信息
            $activity_info = $this->validationActivity($data);

            //push推送相关
            $message_scheme = $this->getStaffMessageScheme($activity_id);//消息跳转地址
            $title_key = KpiActivityStaffEnums::REMIND_STAFF_TITLE;
            $content_key = KpiActivityStaffEnums::REMIND_STAFF_CONTENT;
            $push_service = new PushService();
            $info = ['years' => $activity_info['years'], 'period' => $activity_info['period']];
            $msg_title = $push_service->getFormatMsg($title_key, $info);

            $page_num = Enums\GlobalEnums::DEFAULT_PAGE_NUM;
            $page_size = self::$chunk_num;
            $stage_staff_list = $this->getWaitConfirmStaffList($data, $page_num, $page_size);
            //未找到待确认目标的员工数据
            if (empty($stage_staff_list)) {
                throw new ValidationException(static::$t->_('kpi_activity_staff_not_confirm'), ErrCode::$VALIDATE_ERROR);
            }
            while (!empty($stage_staff_list)) {
                //找到所有处于员工确认待确认的员工数据
                $diff_staff_list = array_column($stage_staff_list, 'staff_id');
                $staff_users = []; //站内信用户组
                foreach ($diff_staff_list as $staff) {
                    //todo 产品要求去掉push 发送push
                    //$push_service->sendPush($staff, $activity_info, $title_key, $content_key, $message_scheme);
                    $staff_users[] = ['id' => $staff];
                }
                //发送站内信
                $push_service->sendMessage($staff_users, $msg_title, $activity_id, KpiActivityStaffEnums::MESSAGE_CATEGORY_KPI_STAFF_CONFIRM);
                $stage_staff_list = $this->getWaitConfirmStaffList($data, ++$page_num, $page_size);
            }
            $result = true;
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-kpi_staff-batch-send-msg-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $result ?? false,
        ];
    }

    /**
     * 获取待确认目标员工列表
     * @param array $params 筛选条件
     * @param integer $page_num 页码
     * @param integer $page_size 每页条数
     * @return mixed
     */
    private function getWaitConfirmStaffList($params, $page_num, $page_size)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('kas.staff_id');
        $builder->from(['kas' => KpiActivityStaffModel::class]);
        $builder->leftJoin(HrStaffInfoModel::class, 'hsi.staff_info_id = kas.staff_id', 'hsi');
        $builder->leftjoin(HrStaffInfoModel::class, 'manger.staff_info_id = hsi.manger', 'manger');
        $params['stage'] = KpiActivityStaffEnums::STAGE_STAFF;
        //组织查询条件
        $builder = $this->getActivityStaffCondition($builder, $params);
        $builder->limit($page_size, $page_size * ($page_num - 1));
        return $builder->getQuery()->execute()->toArray();
    }
}