<?php
namespace App\Modules\Kpi\Services;

use App\Library\Enums\GlobalEnums;
use App\Library\Enums\KpiTemplateEnums;
use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Library\Exception\BusinessException;
use App\Models\backyard\KpiTemplateIndicatorsRelModel;
use App\Models\backyard\KpiTemplateModel;

/**
 * KPI模版管理服务类
 * Class TemplateService
 * @package App\Modules\Kpi\Services
 */
class TemplateService extends BaseService
{
    private static $instance;

    /**
     * 构造函数
     * TemplateService constructor.
     */
    public function __construct()
    {
    }

    /**
     * 类实例
     * @return TemplateService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    // kpi模版管理-创建
    public static $kpi_template_create = [
        'name'  => 'Required|StrLenGeLe:1,200|>>>:name error',//模版名称
        'content' => 'Required|StrLenLe:1000|>>>:content error',//模版描述
    ];

    // kpi模版管理-列表-非必要的筛选条件
    public static $not_must_params = [
        'name',
        'status',
    ];

    // kpi模版管理-列表-搜索条件
    public static $validate_list_search = [
        'name' => 'StrLenLe:500|>>>:name error', //活动名称
        'status' => 'IntIn:'.KpiTemplateEnums::STATUS_DRAFT.','.KpiTemplateEnums::STATUS_ENABLE.'|>>>:status error', //状态(1草稿，2启用)
        'pageNum' => 'IntGe:1|>>>:page error', //当前页码
        'pageSize' => 'IntGe:1|>>>:page_size error', //每页条数
    ];

    // kpi模版管理-编辑
    public static $kpi_template_edit = [
        'id' => 'Required|IntGe:1|>>>:id error', //模版ID
        'name'  => 'Required|StrLenGeLe:1,500|>>>:name error',//活动名称
        'content' => 'Required|StrLenLe:1000|>>>:content error',//模版名称
    ];

    // kpi模版管理-复制、删除
    public static $validate_kpi_template_id = [
        'id' => 'Required|IntGe:1|>>>:id error', //模版ID
    ];

    /**
     * 模版管理-初始化枚举配置
     * @return array
     */
    public function getDefaultData()
    {
        $code = ErrCode::$SUCCESS;
        $message = '';
        $data = [];
        try {
            $data['status_items'] = $this->getStatus();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $data = [];
        }
        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 获取模版状态配置
     * @return array
     */
    public function getStatus()
    {
        $item = KpiTemplateEnums::$kpi_template_status;
        $tmp_item = [];
        foreach ($item as $index => $t_key) {
            $tmp_item[] = [
                'id' => $index,
                'label' => self::$t[$t_key],
            ];
        }
        return $tmp_item;
    }

    /**
     * 检测模版名称是否存在
     * @param string $md5_name md5后的名称
     * @param integer $create_id 创建者ID
     * @return bool
     * @throws ValidationException
     */
    public function checkTemplateName($md5_name, $create_id)
    {
        $exists = KpiTemplateModel::findFirst([
            'conditions' => 'create_id = :create_id: and md5_name = :md5_name:',
            'columns'    => 'id',
            'bind'       => ['create_id'=>$create_id, 'md5_name' => $md5_name],
        ]);
        if (!empty($exists)) {
            throw new ValidationException(static::$t->_('kpi_template_repeat'), ErrCode::$VALIDATE_ERROR);
        }
        return true;
    }

    /**
     * 新建kpi模版
     * @param array $data 模版基本信息参数组
     * @param array $user 当前登陆着用户信息
     * @return array
     */
    public function addTemplate(array $data, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            $name = $data['name'];//模版名称
            $md5_name = md5($name);//加密后的模版名称
            //检测模版名称是否已存在
            $this->checkTemplateName($md5_name, $user['id']);
            //kpi模版基本信息
            $kpi_template_data = [
                'name' => $name,//模版名称
                'md5_name' => $md5_name,//加密后的模版名称
                'content' => $data['content'],//模版描述
                'create_id' => $user['id'],//创建人工号
                'create_name' => $user['name'],//创建人姓名
                'status' => KpiTemplateEnums::STATUS_DRAFT,//模版状态：1草稿
                'created_at' => date('Y-m-d H:i:s'),//创建时间
                'updated_at' => date('Y-m-d H:i:s')//更新时间
            ];
            $kpi_template_model = new KpiTemplateModel();
            $add_template_result = $kpi_template_model->i_create($kpi_template_data);
            if ($add_template_result === false) {
                //创建模版失败
                throw new BusinessException('kpi模版管理-模版创建失败 = ' . json_encode($kpi_template_data, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($kpi_template_model), ErrCode::$BUSINESS_ERROR);
            }
            $template_id = $kpi_template_model->id;
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('kpi_template-create-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $template_id ?? 0
        ];
    }

    /**
     * 获取模版列表
     * @param array $condition 查询条件组
     * @param int $uid 当前登陆着用户ID
     * @return array
     */
    public function getList(array $condition, int $uid = 0)
    {
        $page_num  = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['pageNum'];
        $page_size = empty($condition['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $condition['pageSize'];
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];
        try {
            $condition['create_id'] = $uid;
            $count = $this->getListCount($condition);
            if ($count > 0) {
                $builder = $this->modelsManager->createBuilder();
                $builder->columns([
                    'main.id',
                    'main.name',
                    'main.created_at',
                    'main.status',
                ]);
                $builder->from(['main' => KpiTemplateModel::class]);
                $builder = $this->getCondition($builder, $condition);
                $builder->orderBy('main.created_at DESC');
                $offset = $page_size * ($page_num - 1);
                $builder->limit($page_size, $offset);
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->formatList($items);
            }
            $data['items']                     = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-kpi_template-list-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * kpi模版列表-记录数
     * @param array $condition 查询条件组
     * @return mixed
     */
    public function getListCount(array $condition)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(main.id) as total');
        $builder->from(['main' => KpiTemplateModel::class]);
        $builder = $this->getCondition($builder, $condition);
        $total_info = $builder->getQuery()->getSingleResult();
        return intval($total_info->total);
    }

    /**
     * 组装列表搜索条件
     * @param object $builder 查询器对象
     * @param array $condition['name'=>'模版名称','status'=>'状态(1草稿，2启用)','create_id'=>'创建者ID']查询条件组
     * @return mixed
     */
    private function getCondition(object $builder, array $condition)
    {
        $name = $condition['name'] ?? '';//模版名称
        $status = $condition['status'] ?? 0;//状态(1草稿，2启用)
        $create_id = $condition['create_id'] ?? 0;
        $builder->andWhere('main.create_id = :create_id:', ['create_id' => $create_id]);
        //模版名称
        if (!empty($name)) {
            $builder->andWhere('main.name LIKE :name:', ['name' => "%{$name}%"]);
        }
        //状态(1草稿，2启用)
        if ($status) {
            $builder->andWhere('main.status = :status:', ['status' => $status]);
        }
        return $builder;
    }

    /**
     * 格式化模版列表信息
     * @param array $list 活动列表
     * @return array
     */
    private function formatList(array $list)
    {
        if (!$list) {
            return [];
        }
        foreach ($list as &$values) {
            $values['status_text'] = static::$t->_(KpiTemplateEnums::$kpi_template_status[$values['status']]);
        }
        return $list;
    }

    /**
     * 编辑模版基本信息
     * @param array $data 模版更新的数据
     * @param array $user 挡墙登陆员工信息
     * @return array
     */
    public function editTemplate(array $data, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        //编辑信息的操作结果
        $kpi_template_edit_result = false;
        try {
            $name = $data['name'];//模版名称
            $md5_name = md5($name);//加密后的模版名称
            //获取活动信息
            $kpi_template_info = $this->getTemplateInfoById($data['id'], $user['id']);
            if (empty($kpi_template_info)) {
                throw new ValidationException(static::$t->_('kpi_template_not_found'), ErrCode::$VALIDATE_ERROR);
            }
            //检测模版名称是否已存在
            $exists = KpiTemplateModel::findFirst([
                'conditions' => 'create_id = :create_id: and md5_name = :md5_name: and id != :id:',
                'columns'    => 'id',
                'bind'       => ['create_id' => $user['id'], 'md5_name' => $md5_name, 'id' => $data['id']],
            ]);
            if (!empty($exists)) {
                throw new ValidationException(static::$t->_('kpi_template_repeat'), ErrCode::$VALIDATE_ERROR);
            }
            //都满足了开始更新
            $kpi_template_edit_result = $kpi_template_info->i_update([
                'name' => $name,//活动名称
                'md5_name' => $md5_name,//加密后的模版名称
                'content' => $data['content'],//模版描述
                'create_id' => $user['id'],//创建人工号
                'create_name' => $user['name'],//创建人姓名
                'updated_at' => date('Y-m-d H:i:s')//更新时间
            ]);
            if ($kpi_template_edit_result === false) {
                throw new BusinessException('KPI模版管理-编辑基本信息失败 = ' . json_encode($kpi_template_info->toArray(), JSON_UNESCAPED_UNICODE) . ';可能存在的原因：'.get_data_object_error_msg($kpi_template_info), ErrCode::$BUSINESS_ERROR);
            }
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $message;
        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('edit-kpi_template-info-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $kpi_template_edit_result? true : false,
        ];
    }

    /**
     * 根据模版ID获取模版信息
     * @param int $id 模版ID
     * @param int $create_id 创建者ID
     * @return mixed
     */
    public function getTemplateInfoById(int $id, int $create_id)
    {
        return KpiTemplateModel::findFirst([
            'conditions' => 'id = :id: and create_id= :create_id:',
            'bind' => ['id' => $id, 'create_id'=>$create_id]
        ]);
    }

    /**
     * KPI模版管理-复制
     * @param int $template_id 模版ID
     * @param array $user 当前登陆着用户信息
     * @return array
     */
    public function copyTemplate(int $template_id, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        //模版复制状态
        $copy_template_result = false;
        //开启事务
        $db = $this->getDI()->get('db_backyard');
        $db->begin();
        try {
            //获取活动信息
            $kpi_template_info = $this->getTemplateInfoById($template_id, $user['id']);
            if (empty($kpi_template_info)) {
                throw new ValidationException(static::$t->_('kpi_template_not_found'), ErrCode::$VALIDATE_ERROR);
            }
            $copy_count = $kpi_template_info->copy_count+1;
            //复制模版信息
            $name = $kpi_template_info->name.static::$t->_('kpi_template_copy').'('.$copy_count.')';
            if (mb_strlen($name) > 200) {
                //模版名称最多可填写200个字符
                throw new ValidationException(static::$t->_('kpi_template_name_error'), ErrCode::$VALIDATE_ERROR);
            }
            $update_info_result = $kpi_template_info->i_update([
                'copy_count' => $copy_count
            ]);
            if ($update_info_result === false) {
                //复制失败
                throw new BusinessException('KPI模版管理-复制失败 = ' . json_encode($kpi_template_info->toArray(), JSON_UNESCAPED_UNICODE) . ';可能存在的原因：' . get_data_object_error_msg($kpi_template_info), ErrCode::$BUSINESS_ERROR);
            }
            //复制模版信息
            $kpi_template_data = [
                'name' => $name,//模版名称
                'md5_name' => md5($name),//加密后的模版名称
                'content' => $kpi_template_info->content,//模版描述
                'create_id' => $user['id'],//创建人工号
                'create_name' => $user['name'],//创建人姓名
                'status' => KpiTemplateEnums::STATUS_DRAFT,//模版状态：1草稿
                'created_at' => date('Y-m-d H:i:s'),//创建时间
                'updated_at' => date('Y-m-d H:i:s')//更新时间
            ];
            $kpi_template_model = new KpiTemplateModel();
            $copy_template_result = $kpi_template_model->i_create($kpi_template_data);
            if ($copy_template_result === false) {
                throw new BusinessException('KPI模版管理-复制失败 = ' . json_encode($kpi_template_data, JSON_UNESCAPED_UNICODE) . ';可能存在的原因：' . get_data_object_error_msg($kpi_template_model), ErrCode::$BUSINESS_ERROR);
            }

            //查询关联的KPI目标
            $indicators_service = new IndicatorsService();
            $copy_indicators_list = $indicators_service->getTemplateIndicatorsList($template_id);
            if ($copy_indicators_list) {
                //复制的模版ID
                $copy_template_id = $kpi_template_model->id;
                //存在KPI指标则要复制指标
                foreach ($copy_indicators_list as &$item) {
                    $item['template_id'] = $copy_template_id;
                }
                //批量插入
                $kpi_indicators_model = new KpiTemplateIndicatorsRelModel();
                $kpi_indicators_result = $kpi_indicators_model->batch_insert($copy_indicators_list, 'db_backyard');
                if ($kpi_indicators_result === false) {
                    throw new BusinessException('KPI模版管理-复制失败 = ' . json_encode($copy_indicators_list, JSON_UNESCAPED_UNICODE) . ';可能存在的原因：' . get_data_object_error_msg($kpi_indicators_model), ErrCode::$BUSINESS_ERROR);
                }
            }

            //不存在KPI指标则直接复制模版即可,复制成功，事物提交
            $db->commit();
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $message;
        }  catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            //事务回滚
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->warning('kpi_template-copy-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $copy_template_result ? true : false,
        ];
    }

    /**
     * KPI模版管理-删除
     * @param int $template_id 模版ID
     * @param array $user 当前登陆着用户信息
     * @return array
     */
    public function delTemplate(int $template_id, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        //模版删除状态
        $kpi_template_del_result = false;
        //开启事务
        $db = $this->getDI()->get('db_backyard');
        $db->begin();
        try {
            //获取活动信息
            $kpi_template_info = $this->getTemplateInfoById($template_id, $user['id']);
            if (empty($kpi_template_info)) {
                throw new ValidationException(static::$t->_('kpi_template_not_found'), ErrCode::$VALIDATE_ERROR);
            }
            //删除活动
            $kpi_template_del_result = $kpi_template_info->delete();
            if ($kpi_template_del_result === false) {
                throw new BusinessException('KPI模版管理-删除失败 = ' . json_encode($kpi_template_info->toArray(), JSON_UNESCAPED_UNICODE) . ';可能存在的原因：' . get_data_object_error_msg($kpi_template_info), ErrCode::$BUSINESS_ERROR);
            }

            //模版删除成功，则需要删除模版关联的KPI指标
            $kpi_template_indicators = $kpi_template_info->getIndicators();
            $kpi_template_indicators_data = $kpi_template_indicators->toArray();
            if (!empty($kpi_template_indicators_data)) {
                $kpi_template_indicators_del_result = $kpi_template_info->getIndicators()->delete();
                if ($kpi_template_indicators_del_result === false) {
                    throw new BusinessException('KPI模版管理-删除模版指标失败 = ' . json_encode($kpi_template_indicators_data, JSON_UNESCAPED_UNICODE) . ';可能存在的原因：' . get_data_object_error_msg($kpi_template_indicators), ErrCode::$BUSINESS_ERROR);
                }
            }
            //删除成功，事物提交
            $db->commit();
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $message;
        }  catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            //事务回滚
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->warning('kpi_template-del-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $kpi_template_del_result ? true : false,
        ];
    }

    /**
     * 获取模版信息
     * @param int $template_id 模版ID
     * @param array $user 当前登陆着用户信息
     * @return array
     */
    public function infoTemplate(int $template_id, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            //获取模版信息
            $kpi_template_info = $this->getTemplateInfoById($template_id, $user['id']);
            if (empty($kpi_template_info)) {
                throw new ValidationException(static::$t->_('kpi_template_not_found'), ErrCode::$VALIDATE_ERROR);
            }
            $data = $kpi_template_info->toArray();
            $data['status_text'] = static::$t->_(KpiTemplateEnums::$kpi_template_status[$data['status']]);
            //删除不需要返回的数据
            unset($data['md5_name'], $data['create_id'], $data['create_name'], $data['created_at'], $data['updated_at']);
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-kpi_template-info-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data??[],
        ];
    }

    /**
     * KPI模版管理-启用模版
     * @param int $template_id 模版ID
     * @param array $user 当前登陆着用户信息
     * @return array
     */
    public function updateTemplateStatus(int $template_id, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        //模版启用状态
        $kpi_template_change_status_result = false;
        try {
            //获取模版信息
            $kpi_template_info = $this->getTemplateInfoById($template_id, $user['id']);
            if (empty($kpi_template_info)) {
                throw new ValidationException(static::$t->_('kpi_template_not_found'), ErrCode::$VALIDATE_ERROR);
            }
            //模版已启用的无需再启用
            if ($kpi_template_info->status == KpiTemplateEnums::STATUS_ENABLE) {
                throw new ValidationException(static::$t->_('kpi_template_status_enable'), ErrCode::$VALIDATE_ERROR);
            }
            //判断模版下的KPI重要度是否是100%
            $Indicators_service = new IndicatorsService();
            $importance_sum = $Indicators_service->getSumImportance($template_id);
            if ($importance_sum != KpiTemplateEnums::IMPORTANCE_SUM) {
                throw new ValidationException(static::$t->_('kpi_template_importance_error'), ErrCode::$VALIDATE_ERROR);
            }
            //开始启用模版
            $kpi_template_change_status_result = $kpi_template_info->i_update([
                'status' => KpiTemplateEnums::STATUS_ENABLE, //启用
                'updated_at' => date('Y-m-d H:i:s')//更新时间
            ]);
            if ($kpi_template_change_status_result === false) {
                //启用失败
                throw new BusinessException('KPI模版管理-启用模版失败 = ' . json_encode($kpi_template_info->toArray(), JSON_UNESCAPED_UNICODE) . ';可能存在的原因：' . get_data_object_error_msg($kpi_template_info), ErrCode::$BUSINESS_ERROR);
            }
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $message;
        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('kpi_template-change-status-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $kpi_template_change_status_result ? true : false,
        ];
    }

    /**
     * 获取自己创建且启用的模版列表
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function getEnableList(array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = '';
        $data = [];
        try {
            $data = KpiTemplateModel::find([
                'columns' => 'id, name',
                'conditions' => 'status= :status: AND create_id=:create_id:',
                'bind' => ['create_id' => $user['id'], 'status' => KpiTemplateEnums::STATUS_ENABLE]
            ])->toArray();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('get-enable-kpi_template-list-failed:' . $e->getMessage());
        }
        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data
        ];
    }
}