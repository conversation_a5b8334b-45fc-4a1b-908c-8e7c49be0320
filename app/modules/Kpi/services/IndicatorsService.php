<?php
namespace App\Modules\Kpi\Services;

use App\Library\Enums\KpiTemplateEnums;
use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Library\Exception\BusinessException;
use App\Models\backyard\KpiTemplateIndicatorsRelModel;
use App\Library\OssHelper;

/**
 * KPI模版指标信息服务类
 * Class IndicatorsService
 * @package App\Modules\Kpi\Services
 */
class IndicatorsService extends BaseService
{
    private static $instance;

    /**
     * 构造函数
     * IndicatorsService constructor.
     */
    public function __construct()
    {
    }

    // kpi模版管理-创建指标
    public static $kpi_indicators_create = [
        'template_id' => 'Required|IntGe:1|>>>:template_id error', //模版ID
        'name'  => 'Required|StrLenGeLe:1,10000|>>>:name error',//KPI目标最多支持10000字
        'importance'=>'Required|IntGeLe:1,100|>>>:importance error',//重要度仅支持输入1~100间的整数
        'method'  => 'Required|StrLenGeLe:1,10000|>>>:method error',//测量方法最多支持10000字
        'target'  => 'Required|StrLenGeLe:1,10000|>>>:target error',//目标最多支持10000字
        'standard'  => 'Required|StrLenGeLe:1,10000|>>>:standard error',//测量标准最多支持10000字
    ];

    // kpi模版管理-模版ID
    public static $validate_kpi_template_id = [
        'template_id' => 'Required|IntGe:1|>>>:template_id error', //模版ID
    ];

    //kpi指标管理-指标编辑、删除
    public static $validate_kpi_indicators_id = [
        'id' => 'Required|IntGe:1|>>>:id error', //指标ID
    ];

    /**
     * 类实例
     * @return IndicatorsService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 获取模版关联的KPI目标
     * @param int $template_id 模版ID
     * @return mixed
     */
    public function getTemplateIndicatorsList(int $template_id)
    {
        return KpiTemplateIndicatorsRelModel::find([
            'conditions' => 'template_id = :template_id:',
            'bind' => ['template_id' => $template_id],
            'columns' => ['name', 'importance', 'method', 'target', 'standard'],
            'order' => 'id ASC'
        ])->toArray();
    }

    /**
     * 获取模版关联的KPI目标总数量
     * @param int $template_id 模版ID
     * @return int
     */
    public function getTemplateIndicatorsCount(int $template_id)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns(['count(main.id) as total']);
        $builder->from(['main' => KpiTemplateIndicatorsRelModel::class]);
        $builder->andWhere('main.template_id = :template_id:', ['template_id' => $template_id]);
        return intval($builder->getQuery()->getSingleResult()->total);
    }

    /**
     * 检测模版信息
     * @param int $template_id 模版ID
     * @param int $create_id 创建者ID
     * @return mixed
     * @throws ValidationException
     */
    public function checkTemplate(int $template_id, int $create_id)
    {
        $template_service = new TemplateService();
        $template_info = $template_service->getTemplateInfoById($template_id, $create_id);
        if (empty($template_info)) {
            throw new ValidationException(static::$t->_('kpi_template_not_found'), ErrCode::$VALIDATE_ERROR);
        }
        return $template_info;
    }

    /**
     * 检测KPI指标数量
     * @param int $template_id 模版ID
     * @param int $create_id 创建者ID
     * @param int $add_count 添加指标数量
     * @return mixed
     * @throws ValidationException
     */
    private function checkIndicators(int $template_id, $create_id, $add_count = 1)
    {
        $template_info = $this->checkTemplate($template_id, $create_id);
        $count = $this->getTemplateIndicatorsCount($template_id);
        if (($count+$add_count) > KpiTemplateEnums::MAX_INDICATORS_COUNT) {
            throw new ValidationException(static::$t->_('kpi_indicator_max'), ErrCode::$VALIDATE_ERROR);
        }
        return $template_info;
    }

    /**
     * 检测KPI指标信息
     * @param int $id 指标ID
     * @return mixed
     * @throws ValidationException
     */
    private function checkIndicatorsInfo($id)
    {
        $kpi_indicators_info = $this->getIndicatorsInfoById($id);
        if (empty($kpi_indicators_info)) {
            throw new ValidationException(static::$t->_('kpi_indicators_not_found'), ErrCode::$VALIDATE_ERROR);
        }
        return $kpi_indicators_info;
    }

    /**
     * 新建kpi指标
     * @param array $data 指标基本信息参数组
     * @param array $user 当前登陆着用户信息
     * @return array
     */
    public function addIndicators(array $data, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        //都满足了开始添加，开启事务
        $db = $this->getDI()->get('db_backyard');
        $db->begin();
        try {
            $importance = $data['importance'];
            if (!preg_match(KpiTemplateEnums::IMPORTANCE_RULE, $importance)) {
                //重要度请输入1-100间的数字
                throw new ValidationException(static::$t->_('kpi_indicators_importance_error'), ErrCode::$VALIDATE_ERROR);
            }
            $template_id = $data['template_id'];
            //检测KPI指标
            $template_info = $this->checkIndicators($template_id, $user['id']);
            //kpi指标基本信息
            $kpi_indicators_data = [
                'template_id' => $data['template_id'],//模版ID
                'name' => $data['name'],//KPI目标
                'importance' => $importance,//重要度
                'method' => $data['method'],//测量方法
                'target' => $data['target'],//目标
                'standard' => $data['standard'],//测量标准
                'created_at' => date('Y-m-d H:i:s'),//创建时间
                'updated_at' => date('Y-m-d H:i:s')//更新时间
            ];
            $kpi_indicators_model = new KpiTemplateIndicatorsRelModel();
            $add_indicators_result = $kpi_indicators_model->i_create($kpi_indicators_data);
            if ($add_indicators_result === false) {
                throw new BusinessException('kpi模版管理-指标创建失败 = ' . json_encode($kpi_indicators_data, JSON_UNESCAPED_UNICODE) . ';可能的原因是：' . get_data_object_error_msg($kpi_indicators_model), ErrCode::$BUSINESS_ERROR);
            }
            //如果模版是启用的状态
            if ($template_info->status == KpiTemplateEnums::STATUS_ENABLE) {
                $kpi_template_edit_result = $template_info->i_update([
                    'status' => KpiTemplateEnums::STATUS_DRAFT, //修改为草稿状态
                    'updated_at' => date('Y-m-d H:i:s')//更新时间
                ]);
                if ($kpi_template_edit_result === false) {
                    throw new BusinessException('KPI模版管理-添加指标-重制模版状态为草稿失败 = ' . json_encode($template_info->toArray(), JSON_UNESCAPED_UNICODE) . ';可能的原因是：' . get_data_object_error_msg($template_info), ErrCode::$BUSINESS_ERROR);
                }
            }

            //更新成功，事物提交
            $db->commit();
            $data_result = $kpi_indicators_model->toArray();
            //删除掉不需要返回的数据
            unset($data_result['created_at'], $data_result['updated_at']);
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            //事务回滚
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->warning('kpi_indicators-create-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data_result ?? []
        ];
    }

    /**
     * 获取KPI指标列表
     * @param int $template_id 模版ID
     * @param array $user 当前登陆者信息组
     * @return mixed
     */
    public function getList(int $template_id, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            $this->checkTemplate($template_id, $user['id']);
            $data = KpiTemplateIndicatorsRelModel::find([
                'conditions' => 'template_id = :template_id:',
                'bind' => ['template_id' => $template_id],
                'columns' => ['id','template_id', 'name', 'importance', 'method', 'target', 'standard'],
                'order' => 'id ASC'
            ])->toArray();
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        }  catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-kpi_template-indicators-list-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data ?? [],
        ];
    }

    /**
     * 根据指标ID获取指标信息
     * @param int $id 指标ID
     * @return mixed
     */
    public function getIndicatorsInfoById($id)
    {
        return KpiTemplateIndicatorsRelModel::findFirst([
            'conditions' => 'id = :id:',
            'bind' => ['id' => $id]
        ]);
    }

    /**
     * 编辑指标基本信息
     * @param array $data 指标更新的数据
     * @param array $user 当前登陆着用户信息
     * @return array
     */
    public function editIndicators(array $data, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        //编辑信息的操作结果
        $kpi_indicators_edit_result = false;
        //开启事务
        $db = $this->getDI()->get('db_backyard');
        $db->begin();
        try {
            //检测模版信息
            $template_id = $data['template_id'];
            $template_info = $this->checkTemplate($template_id, $user['id']);
            //获取指标信息
            $kpi_indicators_info = $this->checkIndicatorsInfo($data['id']);
            $kpi_indicators_edit_result = $kpi_indicators_info->i_update([
                'name' => $data['name'],//KPI目标
                'importance' => $data['importance'],//重要度
                'method' => $data['method'],//测量方法
                'target' => $data['target'],//目标
                'standard' => $data['standard'],//测量标准
                'updated_at' => date('Y-m-d H:i:s')//更新时间
            ]);
            if ($kpi_indicators_edit_result === false) {
                throw new BusinessException('KPI模版管理-编辑指标基本信息失败 = ' . json_encode($data, JSON_UNESCAPED_UNICODE) . ';可能的原因是：' . get_data_object_error_msg($kpi_indicators_info), ErrCode::$BUSINESS_ERROR);
            }

            //如果模版是启用的状态
            if ($template_info->status == KpiTemplateEnums::STATUS_ENABLE) {
                $kpi_template_edit_result = $template_info->i_update([
                    'status' => KpiTemplateEnums::STATUS_DRAFT, //修改为草稿状态
                    'updated_at' => date('Y-m-d H:i:s')//更新时间
                ]);
                if ($kpi_template_edit_result === false) {
                    throw new BusinessException('KPI模版管理-编辑指标基本信息失败 = ' . json_encode($template_info->toArray(), JSON_UNESCAPED_UNICODE) . ';可能的原因是：' . get_data_object_error_msg($template_info), ErrCode::$BUSINESS_ERROR);
                }
            }
            //更新成功，事物提交
            $db->commit();
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $message;
        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            //事务回滚
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->warning('edit-kpi_template-indicators-info-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $kpi_indicators_edit_result? true : false,
        ];
    }

    /**
     * KPI模版管理-删除指标
     * @param int $id 模版ID
     * @param array $user 当前登陆着用户信息
     * @return array
     */
    public function delIndicators(int $id, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        //指标删除状态
        $kpi_indicators_del_result = false;
        //开启事务
        $db = $this->getDI()->get('db_backyard');
        $db->begin();
        try {
            //获取指标信息
            $kpi_indicators_info = $this->checkIndicatorsInfo($id);
            //检测模版信息
            $template_id = $kpi_indicators_info->template_id;
            $template_info = $this->checkTemplate($template_id, $user['id']);
            //删除指标
            $kpi_indicators_del_result = $kpi_indicators_info->delete();
            if ($kpi_indicators_del_result === false) {
                throw new BusinessException('KPI活动管理-删除指标基本信息失败 = ' . json_encode($kpi_indicators_info, JSON_UNESCAPED_UNICODE) . ';可能的原因是：' . get_data_object_error_msg($kpi_indicators_info), ErrCode::$BUSINESS_ERROR);
            }

            //指标删除，如果模版是启用的状态
            if ($template_info->status == KpiTemplateEnums::STATUS_ENABLE) {
                $kpi_template_edit_result = $template_info->i_update([
                    'status' => KpiTemplateEnums::STATUS_DRAFT, //修改为草稿状态
                    'updated_at' => date('Y-m-d H:i:s')//更新时间
                ]);
                if ($kpi_template_edit_result === false) {
                    throw new BusinessException('KPI模版管理-删除指标基本信息失败 = ' . json_encode($template_info->toArray(), JSON_UNESCAPED_UNICODE) . ';可能的原因是：' . get_data_object_error_msg($template_info), ErrCode::$BUSINESS_ERROR);
                }
            }
            //更新成功，事物提交
            $db->commit();
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $message;
        }  catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            //事务回滚
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->warning('kpi_indicators-del-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $kpi_indicators_del_result ? true : false,
        ];
    }

    /**
     * 获取模版下的指标重要度之和
     * @param int $template_id 模版ID
     * @return int
     */
    public function getSumImportance(int $template_id)
    {
        $info = KpiTemplateIndicatorsRelModel::findFirst([
            'conditions' => 'template_id = :template_id:',
            'bind' => ['template_id' => $template_id],
            'columns' => 'SUM(importance) AS importance_sum'
        ]);
        return !empty($info) ? $info->importance_sum : 0;
    }

    /**
     * 导入指标
     * @param int $template_id 模版ID
     * @param array $excel_file 指标文件
     * @param array $user 当前登陆着用户信息
     * @return array
     */
    public function importIndicators(int $template_id, array $excel_file, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_backyard');
        $db->begin();
        try {
            //是否上传了文件
            if (empty($excel_file)) {
                throw new ValidationException(static::$t->_('kpi_indicators_file_error'), ErrCode::$VALIDATE_ERROR);
            }
            //检测KPI数据合理性
            list($is_error, $excel_data, $excel_header_column) = $this->checkKpiIndicators($excel_file);
            if ($is_error === false) {
                //模版文件内容符合要求，则获取模版信息
                $template_info = $this->checkIndicators($template_id, $user['id'], count($excel_data));
                //开始插入kpi指标
                $kpi_indicators = [];
                foreach ($excel_data as $item) {
                    $kpi_indicators[] = [
                        'template_id' => $template_id,//模版ID
                        'name' => $item[0],//KPI目标
                        'method' => $item[1],//测量方法
                        'target' => $item[2],//目标
                        'standard' => $item[3],//测量标准
                        'importance' => $item[4],//重要度
                        'created_at' => date('Y-m-d H:i:s'),//创建时间
                        'updated_at' => date('Y-m-d H:i:s')//更新时间
                    ];
                }
                $kpi_indicators_model = new KpiTemplateIndicatorsRelModel();
                $kpi_indicators_add_result = $kpi_indicators_model->batch_insert($kpi_indicators, 'db_backyard');
                if ($kpi_indicators_add_result === false) {
                    throw new BusinessException('KPI模版管理-导入指标失败 = ' . json_encode($kpi_indicators, JSON_UNESCAPED_UNICODE) . ';可能的原因是：' . get_data_object_error_msg($kpi_indicators_model), ErrCode::$BUSINESS_ERROR);
                }
                //指标添加成功
                if ($template_info->status == KpiTemplateEnums::STATUS_ENABLE) {
                    $kpi_template_edit_result = $template_info->i_update([
                        'status' => KpiTemplateEnums::STATUS_DRAFT, //修改为草稿状态
                        'updated_at' => date('Y-m-d H:i:s')//更新时间
                    ]);
                    if ($kpi_template_edit_result === false) {
                        throw new BusinessException('KPI模版管理-导入指标失败 = ' . json_encode($template_info->toArray(), JSON_UNESCAPED_UNICODE) . ';可能的原因是：' . get_data_object_error_msg($template_info), ErrCode::$BUSINESS_ERROR);
                    }
                }
                //更新成功，事物提交
                $db->commit();
            } else {
                //存在不合理的数据
                $new_excel_data = [];
                foreach ($excel_data as $item) {
                    $new_excel_data[] = array_values($item);
                }
                $excel_extra_config = [
                    'file_name' =>  'KPI导入模板 KPI Template แบบฟอร์มอัปโหลดKPI failed'.date('YmdHis').'.xlsx',
                    'column_width' => 15,
                ];
                $path = self::customizeExcelToFile($excel_header_column, $new_excel_data,  $excel_extra_config);
                $oss_result = OssHelper::uploadFile($path);
                if (!empty($oss_result['object_url'])) {
                    $code = 5;
                    $message = "上传文件失败，浏览器自动下载导入失败文件";
                    // 下载导入结果
                    $result = $oss_result['object_url'];
                }
            }
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $message;
        }  catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            //事务回滚
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->warning('kpi_template-import-indicators-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $result ?? '',
        ];
    }

    /**
     * 检测KPI数据合理性
     * @param array $excel_file excel文件内容
     * @return array
     * @throws ValidationException
     */
    public function checkKpiIndicators(array $excel_file)
    {
        //是否有异常元素存在，false不存在错误
        $is_error = false;
        try {
            $config = ['path' => ''];
            $excel = new \Vtiful\Kernel\Excel($config);
            $sheetList = $excel->openFile($excel_file[0]->getTempName())
                ->sheetList();
            if (empty($sheetList)) {
                throw new ValidationException(static::$t->_('kpi_indicators_error'), ErrCode::$VALIDATE_ERROR);
            }
            $excel_data = $excel->openFile($excel_file[0]->getTempName())
                ->openSheet($sheetList[0])
                ->setType([
                    \Vtiful\Kernel\Excel::TYPE_STRING,
                    \Vtiful\Kernel\Excel::TYPE_STRING,
                    \Vtiful\Kernel\Excel::TYPE_STRING,
                    \Vtiful\Kernel\Excel::TYPE_STRING,
                ])
                ->getSheetData();
        } catch (\Exception $e) {
            throw new ValidationException(static::$t->_('kpi_indicators_file_error'), ErrCode::$VALIDATE_ERROR);
        }
        //弹出excel标题一行信息
        $excel_header_column = array_shift($excel_data);
        if (empty($excel_data)) {
            throw new ValidationException(static::$t->_('kpi_indicators_error'), ErrCode::$VALIDATE_ERROR);
        }
        //验证每项元素是否符合标准
        foreach ($excel_data as $key => &$item) {
            $error_msg = "";
            if (empty($item[0])) {
                //KPI目标不能为空
                $error_msg .= self::$t['indicators_upload_error_000'].PHP_EOL;
            } else if (mb_strlen($item[0]) > 10000) {
                // KPI目标最大长度限制10000字符
                $error_msg .= self::$t['indicators_upload_error_001'].PHP_EOL;
            }
            if (empty($item[1])) {
                //测量方法必填不能为
                $error_msg .= self::$t['indicators_upload_error_002'].PHP_EOL;
            } else if (mb_strlen($item[1]) > 10000) {
                //测量方法最大长度限制10000字符
                $error_msg .= self::$t['indicators_upload_error_003'].PHP_EOL;
            }
            if (empty($item[2])) {
                //目标必填不能为空
                $error_msg .= self::$t['indicators_upload_error_004'].PHP_EOL;
            } else if (mb_strlen($item[2]) > 10000) {
                //目标最大长度限制10000字符
                $error_msg .= self::$t['indicators_upload_error_005'].PHP_EOL;
            }
            if (empty($item[3])) {
                //测量标准必填不能为空
                $error_msg .= self::$t['indicators_upload_error_006'].PHP_EOL;
            } else if (mb_strlen($item[3]) > 10000) {
                //测量标准最大长度限制10000字符
                $error_msg .= self::$t['indicators_upload_error_007'].PHP_EOL;
            }
            if (!preg_match(KpiTemplateEnums::IMPORTANCE_RULE, $item[4])) {
                //重要度请输入1-100间的数字
                $error_msg .= self::$t['indicators_upload_error_010'].PHP_EOL;
            }
            //存在错误信息，则要赋予备注信息
            if ($error_msg) {
                $item[5] = $error_msg;
                $is_error = true;
            }
        }
        return [$is_error, $excel_data, $excel_header_column];
    }

    /**
     * 导出指标
     * @param int $template_id 模版ID
     * @param array $user 当前登陆着用户信息
     * @return array
     */
    public function exportIndicators(int $template_id, array $user)
    {
        try {
            set_time_limit(0);
            $template_info = $this->checkTemplate($template_id, $user['id']);
            $list = $this->getTemplateIndicatorsList($template_id);
            $row_values = [];
            if ($list) {
                foreach ($list as $item) {
                    $row_values[] = [
                        $item['name'],
                        $item['method'],
                        $item['target'],
                        $item['standard'],
                        $item['importance'],
                    ];
                }
            }
            $header = [
                static::$t->_('kpi_indicators_name'), //KPI目标
                static::$t->_('kpi_indicators_method'), //测量方法
                static::$t->_('kpi_indicators_target'), //目标
                static::$t->_('kpi_indicators_standard'), //测量标准
                static::$t->_('kpi_indicators_importance'), //重要度
            ];
            $file_name = $template_info->name.'-' . date("Ymd");
            $result = $this->exportExcel($header, $row_values, $file_name);
            $result['message'] = $result['code'] == ErrCode::$SUCCESS ? 'success' : $result['data'];
            return $result;
        } catch (ValidationException $e) {
            $code    = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = $e->getMessage() . $e->getTraceAsString();
            $this->logger->warning('download-kpi_template_indicators-failed:' . $message . $e->getTraceAsString());
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => [],
        ];
    }
}