<?php
namespace App\Modules\Kpi\Services;

use App\Library\Enums\KpiActivityStaffEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Models\backyard\KpiActivityStaffModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Modules\Organization\Services\DepartmentService;
use App\Repository\DepartmentRepository;
use App\Repository\StaffDepartmentAreasStoreRepository;
use App\Library\Validation\ValidationException;
use App\Library\ErrCode;
use App\Models\backyard\HrStaffManageStoreModel;
use App\Library\Enums;

/**
 * 团队service层
 */
class TeamService extends BaseService
{
    private static $instance;

    /**
     * 构造函数
     * StaffService constructor.
     */
    private function __construct()
    {
    }

    /**
     * 类实例
     * @return StaffService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * KPI活动管理 - 获取参与活动的员工列表
     */
    public static $get_team_list_validate = [
        // 支持按照 姓名/工号/昵称 模糊匹配
        'name'               => 'StrLenGeLe:0,50|>>>:param error[name]',
        // 部门id搜索
        'node_department_id' => 'IntGeLe:0,1000000000|>>>:param error[node_department_id]',
        // 职位搜索
        'job_id'             => 'IntGeLe:0,1000000000|>>>:param error[job_id]',
        // 所属网点搜索
        'sys_store_id'       => 'StrLenGeLe:0,10|>>>:param error[sys_store_id]',
        // 直线上级 姓名/工号、 模糊搜搜
        'leader'             => 'StrLenGeLe:0,50|>>>:param error[leader]',
        // 在职状态 支持多选
        'staff_state'        => 'Arr',
        'staff_state[*]'     => 'StrLenGe:1',
        //职级
        'job_title_grade'    => 'Arr',
        'job_title_grade[*]' => 'StrLenGe:1',
        // 翻页参数
        'pageNum'            => 'Required|IntGe:1|>>>:param error[pageNum]',
        // 翻页参数
        'pageSize'           => 'Required|IntGt:0|>>>:param error[pageSize]',
        // 活动id
        'activity_id'        => 'Required|IntGt:0|>>>:param error[activity_id]',
    ];

    public static $get_department_stage_nums = [
        // 活动id
        'activity_id' => 'Required|IntGt:0|>>>:param error[activity_id]',
    ];

    /**
     * 【全部】、【待制定目标】、【待确认目标】、【确认完毕，目标执行】数据汇总接口
     * @param array $params
     * @param array $user_info
     * @param int $type
     * @return array|int[]
     */
    public function getStageNums(array $params, array $user_info, int $type = 0): array
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $result  = [
            "all_nums"     => 0,        // 全部
            "stage_leader" => 0,        // 待制定目标
            "stage_staff"  => 0,        // 待确认目标
            "stage_done"   => 0,        // 确认完毕，目标执行
        ];
        try {
            $res = $this->getDepAndStoreIdByType($params, $user_info, $type);
            if (KpiActivityStaffEnums::PERMISSION_DEPARTMENT == $type && empty($res['node_department_ids'])) {
                return $result;
            }
            if (KpiActivityStaffEnums::PERMISSION_HRBP == $type && empty($res['node_department_ids']) && empty($res['store_ids'])) {
                return $result;
            }

            $builder = $this->modelsManager->createBuilder();
            $builder->columns('kas.stage, count (kas.id) AS nums');
            $builder->from(['kas' => KpiActivityStaffModel::class]);
            $builder->leftJoin(HrStaffInfoModel::class, "hsi.staff_info_id = kas.staff_id", 'hsi');
            $builder = $this->getTeamCondition($builder, $params, $type);

            $builder->groupBy('kas.stage');
            $list = $builder->getQuery()->execute()->toArray();
            $list = array_column($list, null, 'stage');

            $result['stage_leader'] = (int)$list[KpiActivityStaffEnums::STAGE_LEADER]['nums'];
            $result['stage_staff']  = (int)$list[KpiActivityStaffEnums::STAGE_STAFF]['nums'];
            $result['stage_done']   = (int)$list[KpiActivityStaffEnums::STAGE_DONE]['nums'];
            $result['all_nums']     = $result['stage_leader'] + $result['stage_staff'] + $result['stage_done'];
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage().$e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning(__CLASS__.'function: getStageNums failed:'.$real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $result ?? false,
        ];
    }

    public function getDepAndStoreIdByType(array &$params, array $user_info, int $type = 0)
    {
        $result         = [
            'node_department_ids' => [],
            'store_ids'           => [],
        ];
        $department_ids = [];
        if (isset($params['node_department_id']) && !empty($params['node_department_id'])) {
            // 如果用户选择了按照部门进行筛选，需要根据用户筛选的部门id查询出所有的子部门
            $department_ids = (new DepartmentService())->getChildrenListByDepartmentIdV2($params['node_department_id'],
                true);
        }

        // 判断是部门负责人登入
        if (KpiActivityStaffEnums::PERMISSION_DEPARTMENT == $type) {
            // 查询出当前登入者的所有部门及其子部门
            $department_list = (new DepartmentRepository())->getDepartmentAllListByManagerId($user_info['id']);
            // 部门负责人身份登入时 如果未查询到子部门时 则直接返回
            if (empty($department_list)) {
                return $result;
            }
            $manage_department_ids = array_column($department_list, 'id');
            // 求交集
            $intersect_arr                 = !empty($department_ids) ? array_intersect($manage_department_ids,
                $department_ids) : $manage_department_ids;
            $result['node_department_ids'] = $intersect_arr;
            $params['node_department_id']  = $intersect_arr;
            return $result;
        }

        // 判断是HRBP登入
        if (KpiActivityStaffEnums::PERMISSION_HRBP == $type) {
            //获取HRBP管辖范围
            $range = (new StaffDepartmentAreasStoreRepository())->getStaffManageDepartmentsAreasStores($user_info['id']);
            // hrbp身份登入时 如果都未返回数据 说明未设置管辖范围
            if (empty($range['stores_ids']) && empty($range['department_ids'])) {
                return $result;
            }
            $result['store_ids']           = $range['stores_ids'];
            $result['node_department_ids'] = !empty($department_ids) ? array_intersect($range['department_ids'],
                $department_ids) : $range['department_ids'];
            $params['node_department_id']  = $result['node_department_ids'];
            $params['sys_store_id']        = $result['store_ids'];
            // 如果当前HRBP管辖范围没有-2 且 用户又筛选了网点 则需要获取交集
            if (!empty($range['stores_ids']) && !in_array(HrStaffManageStoreModel::$all_id,
                    $range['stores_ids']) && !empty($params['sys_store_id'])) {
                $result['store_ids']    = array_intersect($range['stores_ids'], [$params['sys_store_id']]);
                $params['sys_store_id'] = $result['store_ids'];
            }
        }
        return $result;
    }

    /**
     * 团队kpi-我的部门-我的管辖列表-支持翻页
     * @param array $params
     * @param array $user_info
     * @param int $type
     * @return array
     */
    public function getTeamList(array $params, array $user_info, int $type = 0): array
    {
        $page    = intval($params['pageNum']);
        $size    = intval($params['pageSize']);
        $offset  = $size * ($page - 1);
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page,
                'per_page'     => $size,
                'total_count'  => 0,
            ],
        ];
        $return  = [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
        try {
            //判断活动是否存在 如果不用存在则抛出异常
            ActivityService::getInstance()->checkActivityInfo($params['activity_id']);

            $res = $this->getDepAndStoreIdByType($params, $user_info, $type);
            if (KpiActivityStaffEnums::PERMISSION_DEPARTMENT == $type && empty($res['node_department_ids'])) {
                return $return;
            }
            if (KpiActivityStaffEnums::PERMISSION_HRBP == $type && empty($res['node_department_ids']) && empty($res['store_ids'])) {
                return $return;
            }

            // 获取符合条件的个数
            $data['pagination']['total_count'] = $this->getTeamCount($params, $type);
            if (0 == $data['pagination']['total_count']) {
                return $return;
            }

            $builder = $this->modelsManager->createBuilder();
            $builder->columns(
                'DISTINCT kas.id,
                kas.activity_id, 
                kas.staff_id,  
                IF(kas.leader_id = 0,manger.staff_info_id,kas.leader_id ) AS leader_id,
                IF(kas.leader_id = 0,manger.name,kas.leader_name) AS leader_name,
                kas.stage,
                hsi.name AS staff_name, 
                hsi.node_department_id, 
                hsi.job_title AS job_id,
                hsi.sys_store_id,
                hsi.nick_name,
                hsi.state,
                hsi.wait_leave_state,
                hsi.hire_date,
                hsi.job_title_grade'
            );
            $builder->from(['kas' => KpiActivityStaffModel::class]);
            $builder->leftJoin(HrStaffInfoModel::class, "hsi.staff_info_id = kas.staff_id", 'hsi');
            $builder->leftjoin(HrStaffInfoModel::class, 'manger.staff_info_id = hsi.manger', 'manger');

            // 组织查询条件
            $builder = $this->getTeamCondition($builder, $params, $type);
            $builder->limit($size, $offset);
            $builder->orderBy('kas.staff_id ASC');
            $data['items'] = $builder->getQuery()->execute()->toArray();

            // 网点id
            $store_ids  = array_values(array_unique(array_column($data['items'], 'sys_store_id')));
            $store_list = CommonService::getInstance()->getSysStoreByParams(['id' => $store_ids], ['id', 'name']);
            if (!empty($store_list)) {
                $store_list = array_column($store_list, null, 'id');
            }

            // 部门id
            $department_ids  = array_values(array_unique(array_column($data['items'], 'node_department_id')));
            $department_list = CommonService::getInstance()->getDepartmentByParams([
                "id"      => $department_ids,
                "deleted" => Enums\GlobalEnums::IS_NO_DELETED,
            ], ['id', 'name']);
            if (!empty($department_list)) {
                $department_list = array_column($department_list, null, "id");
            }

            // 职位id
            $job_ids        = array_values(array_unique(array_column($data['items'], "job_id")));
            $job_title_list = CommonService::getInstance()->getJobTitleByParams([
                "id"     => $job_ids,
                "status" => 1,
            ], ['id', 'job_name']);
            if (!empty($job_title_list)) {
                $job_title_list = array_column($job_title_list, null, 'id');
            }

            foreach ($data['items'] as $key => $val) {
                $data['items'][$key]['stage_name']           = !empty(KpiActivityStaffEnums::$kpi_activity_staff_stage[$val['stage']]) ? static::$t->_(KpiActivityStaffEnums::$kpi_activity_staff_stage[$val['stage']]) : '';
                if (StaffInfoEnums::STAFF_STATE_IN == $val['state'] && StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES == $val['wait_leave_state']) {
                    $val['state'] = StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE;
                }
                $data['items'][$key]['state_name'] = static::$t->_(StaffInfoEnums::$staff_state[$val['state']]);
                if ($val['sys_store_id'] == KpiActivityStaffEnums::STORE_HEADER_OFFICE_ID) {
                    $data['items'][$key]['sys_store_name'] = KpiActivityStaffEnums::STORE_HEADER_OFFICE_NAME;
                } else {
                    $data['items'][$key]['sys_store_name'] = !empty($store_list[$val['sys_store_id']]) ? $store_list[$val['sys_store_id']]['name'] : "";
                }
                $data['items'][$key]['hire_date']            = !empty($val['hire_date']) ? date('Y/m/d',
                    strtotime($val['hire_date'])) : '';
                $data['items'][$key]['node_department_name'] = !empty($department_list[$val['node_department_id']]) ? $department_list[$val['node_department_id']]['name'] : "";
                $data['items'][$key]['job_name']             = !empty($job_title_list[$val['job_id']]) ? $job_title_list[$val['job_id']]['job_name'] : "";
                $data['items'][$key]['leader']               = ActivityStaffService::getInstance()->getStaffInfoIdAndName((int)$val['leader_id'],
                    (string)$val['leader_name']);
                $data['items'][$key]['job_title_grade']      = 0; //职级
                $data['items'][$key]['job_title_grade_text'] = "";//职级文案
                if (isset($val['job_title_grade'])) {
                    $job_title_grade                             = $val['job_title_grade'];
                    $data['items'][$key]['job_title_grade']      = $job_title_grade;
                    $data['items'][$key]['job_title_grade_text'] = static::$t->_(StaffInfoEnums::$config_job_title_grade_v2[$job_title_grade]);
                }
            }
        } catch (ValidationException $e) {
            $code    = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error(__CLASS__.' function: getTeamList-failed:'.$e->getMessage());
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    public function getTeamCount(array $params, int $type): int
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('COUNT(DISTINCT kas.id) AS total');
        $builder->from(['kas' => KpiActivityStaffModel::class]);
        $builder->leftJoin(HrStaffInfoModel::class, "hsi.staff_info_id = kas.staff_id", 'hsi');
        $builder->leftjoin(HrStaffInfoModel::class, 'manger.staff_info_id = hsi.manger', 'manger');
        $builder = $this->getTeamCondition($builder, $params, $type);
        return intval($builder->getQuery()->getSingleResult()->total);
    }

    public function getTeamCondition(object $builder, array $params, int $type)
    {
        // 过滤员工的基本条件
        $builder->andWhere('hsi.formal IN ({formal:array}) AND hsi.is_sub_staff = :is_sub_staff:',
            [
                'formal'       => [
                    StaffInfoEnums::FORMAL_IN,
                    StaffInfoEnums::FORMAL_TRAINEE,
                ],
                'is_sub_staff' => StaffInfoEnums::IS_SUB_STAFF_NO,
            ]
        );

        // 活动id的过滤
        $builder->andWhere('kas.activity_id = :activity_id: ',
            [
                'activity_id' => $params['activity_id'],
            ]
        );

        // state 字段的过滤
        $all_stage = [
            KpiActivityStaffEnums::STAGE_LEADER,
            KpiActivityStaffEnums::STAGE_STAFF,
            KpiActivityStaffEnums::STAGE_DONE,
        ];
        $builder->inWhere('kas.stage', $all_stage);

        // 支持 按照名称 、昵称 、和工号进行模糊搜索
        if (isset($params['name']) && !empty(trim($params['name']))) {
            $builder->andWhere("hsi.name LIKE :name: OR hsi.staff_info_id LIKE :name: OR hsi.nick_name LIKE :name: ",
                [
                    'name' => '%'.trim($params['name']).'%',
                ]
            );
        }

        // 部门负责人的查询条件
        if (KpiActivityStaffEnums::PERMISSION_DEPARTMENT == $type) {
            // 选择上级某个部门，下级部门的人员也会被搜索出来
            if (isset($params['node_department_id']) && !empty($params['node_department_id'])) {
                $builder->andWhere('hsi.node_department_id IN ({department_ids:array})',
                    [
                        'department_ids' => array_values($params['node_department_id']),
                    ]
                );
            }

            // 所属网点
            if (isset($params['sys_store_id']) && !empty($params['sys_store_id'])) {
                $builder->andWhere('hsi.sys_store_id = :sys_store_id:',
                    [
                        'sys_store_id' => $params['sys_store_id'],
                    ]
                );
            }
        }

        // HRBP 查询条件
        if (KpiActivityStaffEnums::PERMISSION_HRBP == $type) {
            $builder_sql = [];
            if (isset($params['node_department_id']) && !empty($params['node_department_id'])) {
                $builder_sql['builder_sql'][]                                    = "(hsi.node_department_id IN ({jurisdiction_node_department_ids:array}) AND hsi.sys_store_id = ".KpiActivityStaffEnums::STORE_HEADER_OFFICE_ID." )";
                $builder_sql['builder_bind']['jurisdiction_node_department_ids'] = array_values($params['node_department_id']);
            }
            if (isset($params['sys_store_id']) && !empty($params['sys_store_id']) && in_array(HrStaffManageStoreModel::$all_id,
                    $params['sys_store_id'])) {
                $builder_sql['builder_sql'][]                   = " hsi.sys_store_id != :no_sys_store_id:";
                $builder_sql['builder_bind']['no_sys_store_id'] = KpiActivityStaffEnums::STORE_HEADER_OFFICE_ID;
            }
            if (isset($params['sys_store_id']) && !empty($params['sys_store_id']) && !in_array(HrStaffManageStoreModel::$all_id,
                    $params['sys_store_id'])) {
                $builder_sql['builder_sql'][]                              = " hsi.sys_store_id IN ({jurisdiction_sys_store_ids:array})";
                $builder_sql['builder_bind']['jurisdiction_sys_store_ids'] = array_values($params['sys_store_id']);
            }

            $builder_sql_str = '('.implode(' OR', $builder_sql['builder_sql']).')';
            $builder->andWhere($builder_sql_str, $builder_sql['builder_bind']);
        }

        // 职位搜索
        if (isset($params['job_id']) && !empty($params['job_id'])) {
            $builder->andWhere('hsi.job_title = :job_title:',
                [
                    'job_title' => $params['job_id'],
                ]
            );
        }

        // 直接上级
        if (isset($params['leader']) && !empty($params['leader'])) {
            $builder->andWhere("(kas.leader_id LIKE :leader:) OR (manger.staff_info_id LIKE :leader: AND kas.leader_id = 0 AND kas.leader_name = '')",
                [
                    'leader' => '%'.trim($params['leader']).'%',
                ]
            );
        }

        // 在职状态检索 支持多选
        if (isset($params['staff_state'][0]) && !empty($params['staff_state'][0])) {
            if (in_array(StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE, $params['staff_state'])) {    // 待离职的处理
                $builder->andWhere("hsi.state IN ({states:array}) OR (hsi.state= :state: AND hsi.wait_leave_state = :wait_leave_state:)",
                    [
                        'states'           => $params['staff_state'],
                        'state'            => StaffInfoEnums::STAFF_STATE_IN,
                        'wait_leave_state' => StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES,
                    ]);
            } else {
                $builder->andWhere("hsi.state IN ({states:array}) AND hsi.wait_leave_state = :wait_leave_state:",
                    [
                        'states'           => $params['staff_state'],
                        'wait_leave_state' => StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_NO,
                    ]);
            }
        }

        // 职级检索
        if (!empty($params['job_title_grade'][0])) {
            $builder->andWhere('hsi.job_title_grade IN ({job_title_grade:array})',
                [
                    'job_title_grade' => $params['job_title_grade'],
                ]
            );
        }

        return $builder;
    }

}