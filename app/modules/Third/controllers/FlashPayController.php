<?php
namespace App\Modules\Third\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Third\Services\FlashPayService;
use App\Modules\Third\Services\FlashPaySupportService;

/**
 * FlashPay在线支付控制器层
 * Class FlashPayController
 * @package App\Modules\Pay\Controllers
 */
class FlashPayController extends BaseController
{
    /**
     * 在线支付-FlashPay-异步通知回调接口
     * @api https://yapi.flashexpress.pub/project/133/interface/api/70172
     * @throws \Exception
     */
    public function payTradeSyncAction()
    {
        $params = trim_array($this->request->get());
        $res = FlashPayService::getInstance()->payTradeNoSync($params);
        $this->response->setJsonContent($res);
        return $this->response;
    }
}
