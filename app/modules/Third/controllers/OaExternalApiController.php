<?php
namespace App\Modules\Third\Controllers;


use App\Modules\AgencyPayment\Services\AgencyPaymentApiService;
use App\Modules\Third\Services\OaExternalApiService;
use App\Library\ErrCode;

/**
 * oa 对外提供业务接口
 * Class OaExternalApiController
 * @package App\Modules\Pay\Controllers
 */
class OaExternalApiController extends BaseController
{
    /**
     * 增加到对应的业务块数据 eg  根据传入的模块类型  添加到对应的数据
     * @api https://yapi.flashexpress.pub/project/723/interface/api/78897
     */
    public function addAction()
    {
        $params = $this->request->get();
        $lock_key = md5($params['crowdsourcing_no'] . '_' . $params['batch_id'] . '_' . $params['payee_no'] . '_' . $params['amount']);
        $res      = $this->atomicLock(function () use ($params) {
            return OaExternalApiService::getInstance()->add($params);
        }, $lock_key, 300);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? (object)[]);
    }

    /**
     * 对外接口 众包/个人快递查询业务块数据
     * @api https://yapi.flashexpress.pub/project/723/interface/api/78902
     */
    public function searchAction()
    {
        $params = $this->request->get();
        $res    = OaExternalApiService::getInstance()->search($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 数据集成到代理支付模块
     * @api https://yapi.flashexpress.pub/project/723/interface/api/85025
     */
    public function agencyPaymentAddAction()
    {
        $params = trim_array($this->request->get());
        $lock_key = md5($params['task_batch_no'] . '_' . $params['payment_batch_no']);
        $res = $this->atomicLock(function () use ($params) {
            return AgencyPaymentApiService::getInstance()->add($params);
        }, $lock_key, 300);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? (object)[]);
    }

    /**
     * 数据集成到代理支付模块 - 查询代付结果
     * @api https://yapi.flashexpress.pub/project/723/interface/api/85043
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function getPaymentResultAction()
    {
        $params = trim_array($this->request->get());
        $res = AgencyPaymentApiService::getInstance()->getPaymentResult($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

}
