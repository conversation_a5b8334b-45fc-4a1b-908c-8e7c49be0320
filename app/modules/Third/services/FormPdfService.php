<?php
namespace App\Modules\Third\Services;

use App\Library\ErrCode;

/**
 * JAVA pdf 服务层
 * Class FormPdfService
 * @package App\Modules\Third\Services
 */
class FormPdfService extends BaseService
{
    private static $instance;
    private function __construct()
    {
    }
    private function __clone()
    {
    }

    /**
     * @return FormPdfService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * pdf 设置
     * @var string[]
     */
    private $optionsColumns = [
        'displayHeaderFooter', // true/false
        'footerTemplate', //html代码
        'format',
        'headerTemplate', //html代码
        'height',
        'landscape',
        'margin',
        'pageRanges',
        'path',
        'preferCSSPageSize',
        'printBackground', //true false
        'scale',
        'width',
    ];

    /**
     * 生成pdf服务
     * 新版 根据html页面和 form表单数据 生成pdf，
     * 参数说明，请详细阅读后使用：
     * @see https://dev20-ph-pdf-svc.fex.pub/swagger-ui/index.html#/pdf-convert-controller/createPdfByJvppeteerUsingPOST
     * @param string $oss_html_path 上传阿里云后的html页面的url地址
     * @param array $form_data 文本形式的表单数据，key=>value格式，key 对应 "。ftl" html模板中的文本变量名称
     * @param array $img_data 签名图片的数据，key=>value，key对应模板中的变量名称
     * @param array $pdfOptions 其他扩展设置
     * @param string $fileName 指定文件名
     * @param string $contentDisposition
     * @return array|mixed $pdf_path 返回一个上传阿里云后的一个资源地址，完整路径，可以直接访问
     */
    public function generatePdf(string $oss_html_path, array $form_data, array $img_data = [], array $pdfOptions = [], string $fileName = '', string $contentDisposition = 'inline')
    {
        $url  = env('pdf_rpc_endpoint', '') . '/api/pdf/createPdfByJvppeteer';

        //组装接口提交参数
        $post_data = [
            'pdfName'      => $fileName ? $fileName : time(),         //返回的pdf名称，目前没用到
            'templateUrl'  => $oss_html_path, //pdf 模板oss路径（html页面）
            'data'         => $form_data,     //表单变量数据
            'downLoadData' => $img_data ?: [], //图片变量写到这里，这个是模板中使用的，生成模板这一步就使用的资源图片
            'contentDisposition' => $contentDisposition,//inline 或 attchment (直接下载)

        ];
        //pdf样式设置,该参数控制的是在pdf生成时候的pdf的样式设置
        if ($pdfOptions) {
            foreach ($pdfOptions as $optionName => $optionValue) {
                if (in_array($optionName, $this->optionsColumns)) {
                    $post_data['pdfOptions'][$optionName] = $optionValue;
                }
            }
        }
        $post_data_json = json_encode($post_data, JSON_UNESCAPED_UNICODE);

        //发送请求
        $header[] = 'content-type:application/json;charset=UTF-8';
        $result_json = curl_request($url, $post_data_json, 'POST', $header);
        $result = json_decode($result_json, true);
        $pdf_path = [];
        if (isset($result['code']) && $result['code'] == ErrCode::$SUCCESS) {
            $pdf_path = $result['result'];
            $this->logger->info('generatePdf success:' . json_encode([$url, $post_data_json, $result]));
        } else {
            $this->logger->warning('generatePdf error:' . $result_json);
        }
        return $pdf_path;
    }
}
