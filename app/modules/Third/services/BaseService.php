<?php
namespace App\Modules\Third\Services;

/**
 * 三方基础服务类
 * Class BaseService
 * @package App\Modules\Third\Services
 */
class BaseService extends \App\Library\BaseService
{
    public static function handleParams($params, $not_must)
    {
        if (empty($not_must) || !is_array($not_must)) {
            return $params;
        }
        foreach ($not_must as $value) {
            if (isset($params[$value]) && empty($params[$value])) {
                unset($params[$value]);
            }
        }
        return $params;
    }

}
