<?php

namespace App\Modules\Third\Services;

use App\Library\Enums;
use App\Library\Enums\BankFlowEnums;
use App\Library\Enums\WagesEnums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Library\Exception\BusinessException;
use App\Library\Enums\SysConfigEnums;
use App\Models\oa\PaymentPushRecordModel;
use App\Modules\Wages\Models\WagesModel;
use App\Modules\Wages\Services\AddService;
use Exception;

/**
 * 对外接口处理
 * Class OaExternalApiService
 * @package App\Modules\Third\Services
 */
class OaExternalApiService extends BaseService
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    /**
     * @return OaExternalApiService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 增加到对应的业务块数据
     *
     * @param array $params
     * @return  array
     **/
    public function add($params)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = (object)[];
        $this->logger->info('oa_external_api_add ' . json_encode($params, JSON_UNESCAPED_UNICODE));
        try {
            $module_name = $params['module_type'];
            switch ($module_name) {
                case SysConfigEnums::SYS_MODULE_SALARY_DEDUCT:
                    //众包/快递个人代理数据对接薪资扣款
                    if (!isset($params['is_personal_agent'])) {
                        //众包没有此参数的传递，验证器要依赖，故而兼容一下
                        $params['is_personal_agent'] = 0;
                    }
                    Validation::validate($params, AddService::$add_external_validate);
                    $data = (new AddService())->addExternal($params);
                    break;
                default:

            }
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->error('oa_external_api_add failed:' . $real_message . json_encode($params, JSON_UNESCAPED_UNICODE));
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data
        ];
    }


    /**
     * 查询对应的业务块数据
     *
     * @param array $params
     * @return  array
     */
    public function search($params)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = (object)[];
        $this->logger->info('oa_external_api_search' . json_encode($params, JSON_UNESCAPED_UNICODE));
        try {
            $module_name = $params['module_type'];
            if (empty($module_name)) {
                throw new ValidationException(self::$t['module_name_not_null'], ErrCode::$VALIDATE_ERROR);
            }
            switch ($module_name) {
                case SysConfigEnums::SYS_MODULE_SALARY_DEDUCT:
                    //众包数据查询薪资抵扣
                    Validation::validate($params, AddService::$search_external_validate);
                    $data = (new AddService())->searchExternal($params);
                    break;
                default:

            }
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->error('oa_external_api_search failed:' . $real_message . json_encode($params, JSON_UNESCAPED_UNICODE));
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data
        ];
    }


    /**
     * oa支付模块推送三方记录表
     * @param object $params
     * @return  bool
     */
    public function addPaymentPushRecord($params)
    {
        $this->logger->info('add_payment_push_record' . json_encode($params, JSON_UNESCAPED_UNICODE));
        try {
            if ($params->oa_type == BankFlowEnums::BANK_FLOW_OA_TYPE_WAGE) {

                $payment_push_record = PaymentPushRecordModel::findFirst(
                    [
                        'conditions' => 'no = :no:',
                        'bind'       => [
                            'no'         => $params->no
                        ]
                    ]
                );
                $wages = WagesModel::findFirst(
                    [
                        'conditions' => 'no = :no: and  apply_type in ({apply_type:array})',
                        'bind'       => [
                            'no'         => $params->no,
                            'apply_type' => [WagesEnums::APPLY_TYPE_CROWD_SOURCING, WagesEnums::APPLY_TYPE_PERSONAL_AGENT]
                        ]
                    ]
                );
                //必须是众包的 ，必须在表里面不存在才插入
                if (!empty($wages) && empty($payment_push_record)) {
                    $record_data         = [
                        'payment_id'  => $params->id,
                        'oa_type'     => $params->oa_type,
                        'no'          => $params->no,
                        'status'      => Enums::PAYMENT_PUSH_RECORD_STATUS_PENDING,//默认是0
                        'push_target' => ($wages->apply_type == WagesEnums::APPLY_TYPE_CROWD_SOURCING) ? Enums::PUSH_TARGET_FMS : Enums::PUSH_TARGET_FBI,
                        'push_result' => '',
                        'created_at'  => date('Y-m-d H:i:s', time()),
                    ];
                    $payment_push_record = new PaymentPushRecordModel();
                    if ($payment_push_record->i_create($record_data) === false) {
                        throw new BusinessException('oa支付模块推送三方记录表, 原因可能是: ' . get_data_object_error_msg($payment_push_record) . '; 数据: ' . json_encode($record_data, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                    }
                }
            }
        } catch (BusinessException $e) {
            $real_message = $e->getMessage();
        } catch (Exception $e) {
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->error('add_payment_push_record failed:' . $real_message . json_encode($params, JSON_UNESCAPED_UNICODE));
        }
        return true;
    }






}
