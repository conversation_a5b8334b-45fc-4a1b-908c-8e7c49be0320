<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 2020/5/20
 * Time: 下午5:46
 */



namespace App\Modules\Share\Models;

use App\Models\Base;

/**
 * @property int id    主键
 * @property string dir_level_code    关联 share_center_dir code
 * @property string name    文件名称更改后的 默认保存源文件名 不带扩展名从全路径里面取
 * @property string file_url    文件oss全路径
 * @property int view_type    可查看该文件的权限类型 1-按部门 -1 所有部门 2-按职位 -2所有职位 3-角色 -3 所有角色 4-职等职级 -4 所有职等职级 5 工号 对应数据id 关联表share_center_permission
 * @property int operate_id    创建人工号
 * @property int add_department_id    创建人部门 可供同部门其他人查看
 * @property int update_id    最后修改人
 * @property string created_at
 * @property string updated_at
 * @property int is_delete    删除
 */
class ShareCenterFile extends Base
{
    // 已删除
    const DELETE = 1;
    // 未删除
    const NOT_DELETE = 0;

    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        // setSource()方法指定数据库表
        $this->setSource('share_center_file');
    }

}
