<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 2020/5/20
 * Time: 下午5:46
 */



namespace App\Modules\Share\Models;

use App\Models\Base;

/**
 * @property int id    主键
 * @property int level    目录层级 1为顶层
 * @property string level_code    层级code 每层三位数 例子：001023 第二层23号目录
 * @property string icon    文件夹对应显示图标文件路径 by显示用
 * @property string name_th
 * @property string name_en    英文文件夹名称
 * @property string name_cn    中文文件夹名称
 * @property int sort    排序
 * @property int is_default    是否是默认文件夹 0否 1是
 * @property int operate_id    配置人工号
 * @property int update_id    最后修改人
 * @property string created_at
 * @property string updated_at
 * @property int is_delete    删除
 */
class ShareCenterDir extends Base
{
    // 已删除
    const DELETE = 1;
    // 未删除
    const NOT_DELETE = 0;
    // 是默认文件夹
    const DEFAULT = 1;
    // 不是默认文件夹
    const NOT_DEFAULT = 0;

    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        // setSource()方法指定数据库表
        $this->setSource('share_center_dir');
    }


    /**
     * @return array
     */
    public function get_dir_list(){
        $builder = $this->modelsManager->createBuilder();
//        $builder->columns('id,name');
        $builder->from(SysStore::class);
        $builder->where('state = 1');
        $builder->limit(0, 100);
        $items = $builder->getQuery()->execute()->toArray();
        return [
            'items' => $items,
        ];
    }

    /**
     * Notes: 是否是默认文件夹
     * @return bool
     */
    public function isDefault(): bool
    {
        return $this->is_default == self::DELETE;
    }
}
