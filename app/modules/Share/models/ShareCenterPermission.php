<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 2020/5/20
 * Time: 下午5:46
 */



namespace App\Modules\Share\Models;

use App\Models\Base;

/**
 * @property int file_id	file表 主键
 * @property int permission_value	对应类型下 对应id  例子：file表 view type 1 则保存 部门id
 * @property int is_include_sub	是否包含下级 0-不包含 1-包含
 * @property string created_at
 * @property string updated_at
 * @property int is_delete	删除
 */
class ShareCenterPermission extends Base
{

    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        // setSource()方法指定数据库表
        $this->setSource('share_center_permission');
    }

}
