<?php

namespace App\Modules\Share\Services;

use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Models\backyard\SysDepartmentModel;
use App\Modules\Share\Models\ShareCenterPermission;
use App\Repository\DepartmentRepository;
use App\Repository\ShareCenterDirRepository;
use App\Modules\User\Models\DepartmentModel;
use App\Modules\User\Models\StaffInfoModel;

class ListService extends BaseService
{

    private static $instance;

    public static $forbidden = array(4,25,13);

    private function __construct()
    {
    }

    /**
     * @return ListService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 根据 语言环境 获取对应字段名 涉及表 share_center_dir  share_center_file 字段名(name_th,name_en,name_cn)
     * @param $lang
     */
    protected function get_lang_column($lang = 'th'){
        $lang_arr = array(
            'en' => 'name_en',
            'th' => 'name_th',
            'zh-CN' => 'name_cn',
        );
        return empty($lang_arr[$lang]) ? '' : $lang_arr[$lang];
    }
    /**
     * 目录列表 和文件列表  初始页面 只有目录 子目录 会包括文件
     * @param $param
     * @return mixed
     */
    public function getDirList($param)
    {
        $level = intval($param['level']) + 1;//默认层级为顶层 1
        $code  = empty($param['level_code']) ? '' : $param['level_code'];

        //参数 包括 code  file name  如果 只有name  没有code  搜文件 不查目录 如果2 都有 两个都差  如果 只有code 没有name 也两个都差
        // name ->file  code -> all  name,code -> all
        //dir -> code || code name
        //file -> name
        $return['list']  = $return['file_list'] = [];
        $return['count'] = 0;
        if (!empty($code) ||
            (!empty($param['file_name']) && !empty($code)) ||
            (empty($code) && empty($param['file_name'])) ||
            (!empty($param['file_name']))
        ) {//如果 有搜索文件名 不去找目录列表
            // 本级以及下级的所有文件夹，下级的文件夹用来计算文件数量
            $shareCenterDirRepo = new ShareCenterDirRepository();
            $fileList           = $shareCenterDirRepo->getShareCenterDirList($code, $param['file_name'],
                self::$language)->toArray();
            // 只返回本级的文件夹
            foreach ($fileList as $item) {
                if ($item['level'] == $level) {
                    $return['list'][] = $item;
                }
            }
            if (!empty($return['list'])) {
                //获取语言环境 返回对应语言的 名称
                $column      = $this->get_lang_column(static::$language);
                $operate_ids = array_column($return['list'], 'operate_id');
                $update_ids  = array_column($return['list'], 'update_id');
                $dir_code    = array_column($fileList, 'level_code');
                $staff_model = new StaffInfoModel();
                $op_staff    = $staff_model->users_info($operate_ids);
                $up_staff    = $staff_model->users_info($update_ids);
                //查询目录下是否有文件
                $code_str = "'".implode("','", $dir_code)."'";
                $sql      = "select dir_level_code , count(1) num from share_center_file where dir_level_code in ({$code_str}) 
                    and is_delete = 0 group by dir_level_code";
//            echo $sql;exit;
                $dir_files = $this->getDI()->get('db_oa')->fetchAll($sql);
                $dir_files = array_column($dir_files, 'num', 'dir_level_code');

                $op_staff = array_column($op_staff, 'name', 'id');
                $up_staff = array_column($up_staff, 'name', 'id');
                foreach ($return['list'] as &$v) {
                    $v['file_num'] = empty($dir_files[$v['level_code']]) ? 0 : $dir_files[$v['level_code']];
                    // 累加下级文件夹中的文件数量
                    foreach ($dir_files as $kk => $vv) {
                        if ($kk != $v['level_code'] && strpos($kk, $v['level_code']) === 0) {
                            $v['file_num'] += $vv;
                        }
                    }
                    $v['file_num']   = (int)$v['file_num'];
                    $v['operate_id'] = $op_staff[$v['operate_id']]."({$v['operate_id']})";
                    $v['update_id']  = $up_staff[$v['update_id']]."({$v['update_id']})";
                    $v['name']       = $v[$column];
                }
            }
        }
        if (!empty($param['file_name']) || !empty($code)) {
            //获取文件列表
            $file_param['code']       = $param['level_code'];
            $file_param['page']       = $param['page'];//点击文件夹 默认固定为第一页
            $file_param['size']       = $param['size'];
            $file_param['staff_info'] = $param['staff_info'];
            if (!empty($param['file_name'])) {
                $file_param['file_name'] = $param['file_name'];
            }
            if (!empty($param['department_id'])) {
                $file_param['department_id'] = $param['department_id'];
            }

            $file_data = $this->getFileList($file_param);

            $return['count']     = $file_data['count'];
            $return['file_list'] = $file_data['list'];
        }

        return $return;
    }

    /**
     * 文件列表
     * @param $param
     *
     * explain select f.*
     * -- ,group_concat(p.permission_value) as permission_value
     * from share_center_file f
     * left join share_center_permission p on f.id = p.file_id
     * where f.is_delete = 0
     * and (f.view_type = -1 or (p.permission_value = 0 and p.is_delete = 0) or f.add_department_id = 0)
     * and f.dir_level_code = '004' and f.dir_level_code like '004%' and `name` like '%111%' group by f.id limit 0 , 100
     *
     * @return mixed
     */
    public function getFileList($param){
        $size = empty($param['size']) ? 100 : intval($param['size']);
        $start = empty($param['page']) ? 0 : ($param['page'] -1) * $size ;

        //可以不传code  搜索全目录  二期优化 改的
//        if(empty($param['code']))
//            return array();
        $code = $param['code'];//对应文件夹code
        $search_name = empty($param['file_name']) ? '' : $param['file_name'];
        $department_id = empty($param['department_id']) ? '' : implode(',',$param['department_id']);

        //获取用户信息
        $staff_model = new StaffInfoModel();
        $staff_info = $staff_model->user_info($param['staff_info']['id']);
        $user_department_id = intval($staff_info['department_id']);

        // 上传文件时 setting_env表中保存的id，直接存入到了该表中，无需在获取父级id
        $can_view_file_department_ids = $this->getCanViewFilePrivilegeDepartmentIds();

        $all_dep = [];
        if (!in_array($user_department_id,$can_view_file_department_ids)) {
            //所有部门
            $all_dep = $this->get_department();
            $all_dep = array_column($all_dep,null,'id');
            if (!empty($all_dep[$user_department_id]) && $all_dep[$user_department_id]['ancestry'] != 1) {
                $user_department_id = empty($all_dep[$all_dep[$user_department_id]['ancestry']]['id']) ? $user_department_id : $all_dep[$all_dep[$user_department_id]['ancestry']]['id'];
            }
        }

        $sql = "select f.* 
                -- ,group_concat(p.permission_value) as permission_value 
                from share_center_file f
                left join share_center_permission p on f.id = p.file_id
                ";

        $count_sql = "select f.id
                from share_center_file f
                left join share_center_permission p on f.id = p.file_id
                ";

        $where = " where f.is_delete = 0 
                    and (f.view_type = -1 or (p.permission_value = {$user_department_id} and p.is_delete = 0) or f.add_department_id = {$user_department_id})
                    ";

        if (!empty($param['code'])) {
            $where .= " and f.dir_level_code = '{$code}' ";
        }

        //搜索条件 如果搜索文件 需要搜索出来子文件夹里面的文件
        if(!empty($search_name)){
            $where .= " and f.dir_level_code like '{$code}%' ";
            $where .= " and `name` like '%{$search_name}%' ";
        }

        if(!empty($department_id))
            $where .= " and (p.permission_value in ({$department_id}) and p.is_delete = 0 or f.view_type = -1) ";

        $group = " group by f.id  ";
        $limit = " limit {$start} , {$size} ";

//        echo $sql . $where . $group . $limit;exit;
        $list = $this->getDI()->get('db_oa')->fetchAll($sql . $where . $group . $limit);
        $count = $this->getDI()->get('db_oa')->fetchColumn("select count(1) from ({$count_sql}{$where}{$group}) a");
        //如果 view_type 为负数 则取对应所有数据 塞里面 防止后期 选中所有部门 新增部门 无法查看该文件
        if(!empty($list)){
            //获取 文件对应的 查看权限部门
            $file_ids   = array_column($list, 'id');
            $permission = $this->getFilePermission($file_ids);

            //获取 用户名字
            $operate_ids = array_column($list,'operate_id');
            $update_ids = array_column($list,'update_id');

            $op_staff = $staff_model->users_info($operate_ids);
//            $op_department = array_column($op_staff,'department_id','id');
            $up_staff = $staff_model->users_info($update_ids);

            $op_staff = array_column($op_staff,'name','id');
            $up_staff = array_column($up_staff,'name','id');

            //Human Resources、product部门的人可以选择公司任意一个部门，或所有部门
            //操作（只能编辑、删除本部门发送的文件，非本部门发送的文件，即使有文件编辑、删除的权限，也不可编辑、删除 注：product id 20 部门可以编辑所有部门发送的文件）
            $can_edit_file_department_ids = $this->getCanEditFilePrivilegeDepartmentIds();
            $can_view_file_department_ids = $this->getCanViewFilePrivilegeDepartmentIds();

            //前端 回先编辑 部门问题 需要 处理排除掉 6 和20 这两个 特殊部门
            $forbidden                = self::$forbidden;
            $forbiddenPermissionValue = [
                $this->buildPermissionValue($user_department_id, 0, $all_dep[$user_department_id]['name'] ?? '',
                    $all_dep[$all_dep[$user_department_id]['ancestry']]['name'] ?? '',
                    $all_dep[$all_dep[$user_department_id]['ancestry']]['id'] ?? 0),
            ];

            // 获取最顶级部门
            $depRepo = new DepartmentRepository();
            $topDep = $depRepo->getTopDepartment();
            if ($topDep) {
                $topDep = $topDep->toArray();
            } else {
                $topDep['id']   = GlobalEnums::TOP_DEPARTMENT_ID;
                $topDep['name'] = GlobalEnums::TOP_DEPARTMENT_NAME;
            }

            foreach($list as &$li){
                // 时间由数据库中的零时区改成当地的时间
                $li['created_at'] = show_time_zone($li['created_at']);
                $li['updated_at'] = show_time_zone($li['updated_at']);
                $li['show'] = [];//可查看权限 机构
                if ($li['view_type'] < 0) {
                    //所有部门部门
                    if ($li['view_type'] == -1) {//所有部门 拼到permission_value 字段里
                        $li['permission_value'] = [
                            $this->buildPermissionValue($topDep['id'], 1, $topDep['name']),
                        ];
//                        $li['view_text'] = static::$t->_('all_department');//所有部门 翻译
                        $li['show'] = [$topDep['id']];
                    }
                } else {
                    $filePermissions = empty($permission[$li['id']]) ? [] : $permission[$li['id']];
                    foreach ($filePermissions as $k => $filePermission) {
                        if (in_array($filePermission['value'], $can_view_file_department_ids)) {
                            // 配置的默认有全部查看权限的部门，不用展示在设置中
                            unset($filePermissions[$k]);
                        }
                    }
                    $li['permission_value'] = array_values($filePermissions);
                    if ($li['view_type'] == 1 && !empty($li['permission_value'])) {
//                        $li['view_text'] = implode(',', array_column($li['permission_value'], 'name'));//对应部门
                        $li['show'] = array_column($li['permission_value'], 'value');
                    }
                }

                if (in_array($user_department_id, $forbidden)) {
                    $li['show'] = [$user_department_id];
                    $li['permission_value'] = $forbiddenPermissionValue;
                }

                //拼接 名字
                $li['operate_id'] = $op_staff[$li['operate_id']]."({$li['operate_id']})";
                $li['update_id']  = $up_staff[$li['update_id']]."({$li['update_id']})";

                //是否显示编辑按钮
                $li['is_edit'] = 0;
                if ($user_department_id == $li['add_department_id'] || in_array($user_department_id,
                        $can_edit_file_department_ids)) {
                    $li['is_edit'] = 1;
                }
            }
        }


        $return['list'] = $list;
        $return['count'] = $count;
        return $return;

    }

    /**
     * Notes: 构建返回值
     * @param $departmentId
     * @param $isIncludeSub
     * @param $name
     * @param $ancestryName
     * @param $ancestry
     * @return array
     */
    private function buildPermissionValue($departmentId, $isIncludeSub = 0, $name = '', $ancestryName = '', $ancestry = 0): array {
        return [
            'value'          => $departmentId,
            'is_include_sub' => (string) $isIncludeSub,
            // GroupCEO
            'name'           => $name,
            'ancestry_name'  => $ancestryName,
            'ancestry'       => $ancestry,
        ];
    }

    /**
     * Notes: 获取文件的权限设置
     * @param array $fileIds
     * @return array
     */
    public function getFilePermission(array $fileIds): array
    {
        $permission  = [];
        $tPermission = ShareCenterPermission::find([
            'conditions' => 'file_id IN ({file_id:array}) AND is_delete = '. GlobalEnums::IS_NO_DELETED,
            'bind'       => [
                'file_id' => $fileIds,
            ],
            'columns'    => 'file_id,permission_value,is_include_sub',
        ])->toArray();
        if ($tPermission) {
            $depRepo = new DepartmentRepository();
            // 获取部门ID和其上级的ID
            $departmentIds = array_values(array_unique(array_column($tPermission, 'permission_value')));
            $deptIds       = $depRepo->getDepByIds($departmentIds, 'id,ancestry')->toArray();
            $ancestry      = array_column($deptIds, 'ancestry', 'id');
            $ancestryIds   = array_column($deptIds, 'ancestry');
            $total         = array_values(array_unique(array_merge($departmentIds, $ancestryIds)));
            // 获取部门信息
            $totalInfo = $depRepo->getDepByIds($total, 'id,name')->toArray();
            $totalInfo = array_column($totalInfo, 'name', 'id');
            // 组装部门选择组件需要的信息
            foreach ($tPermission as $item) {
                $item['value']         = $item['permission_value'];
                $item['name']          = $totalInfo[$item['permission_value']] ?? '';
                $item['ancestry']      = $ancestry[$item['permission_value']] ?? '';
                $item['ancestry_name'] = $totalInfo[$item['ancestry']] ?? '';
                unset($item['permission_value']);
                $permission[$item['file_id']][] = $item;
            }
        }
        return $permission;
    }

    //查询 该目录下 是否存在文件或自目录下 存在文件

    /**
     *
     * 存在 true
     * @param $dir_code
     * @return array
     */
    public function check_exist_file($dir_code){

        $sql = " select id from share_center_file 
                where (dir_level_code = '{$dir_code}' or dir_level_code like '{$dir_code}%' )
                and is_delete = 0
                limit 1";

        $exist = $this->getDI()->get('db_oa')->fetchOne($sql);

        $code = empty($exist) ?  ErrCode::$SUCCESS : -1;
        $message = empty($exist) ?  '' : static::$t->_('exist_file');

        return [
            'code' => $code,
            'message' => $message
        ];
    }

    public function get_department(){

        $model = new DepartmentModel();
        $return = $model->get_department();

        $all_name = static::$t->_('all_department');
        $row['id'] = -1;
        $row['name'] = $all_name;
        $row['ancestry'] = 1;
        array_unshift($return,$row);
        return $return;
    }

}
