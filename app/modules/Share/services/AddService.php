<?php

namespace App\Modules\Share\Services;
use App\Library\Enums;
use App\Library\Exception\AuthorizationException;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Models\backyard\SysDepartmentModel;
use App\Modules\Share\Models\ShareCenterDir;
use App\Library\ErrCode;
use App\Modules\Share\Models\ShareCenterFile;
use App\Modules\Share\Models\ShareCenterPermission;
use App\Repository\DepartmentRepository;
use App\Repository\ShareCenterDirRepository;
use App\Modules\User\Models\StaffInfoModel;

class AddService extends BaseService
{

    private static $instance;
    protected $ext = array(
        'pptx','ppt','pdf','doc','docx','jpg','png','xls','xlsx'
        ,'mp4'
    );

    private function __construct()
    {
    }

    /**
     * @return AddService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * @param $data
     * @return array
     */
    public function createDir($data)
    {
        $insert = [];

        if (!empty($data['icon'])) {
            $insert['icon'] = $data['icon'];
        }

        $level = intval($data['p_lever']) + 1;
        //上级目录
        $p_code = $data['p_code'];
        if ($level > 2) {
            return [
                'code'    => 3,
                'message' => static::$t->_('can not create here,please go back'),
            ];
        }

        //重名判断
        $repo = new ShareCenterDirRepository();
        if (Enums\GlobalEnums::VN_COUNTRY_CODE == get_country_code()) {
            $act = $repo->getInfoByNameVN($data['name_en'], $data['name_vn'], $data['name_cn'], $level);
        } else {
            $act = $repo->getInfoByName($data['name_en'], $data['name_th'], $data['name_cn'], $level);
        }
        if (!empty($act)) {
            return [
                'code'    => 3,
                'message' => static::$t->_('dir_exist'),
            ];
        }

        $insert['level']      = intval($level);
        $insert['level_code'] = $this->get_level_code($insert['level'], $p_code);
        $insert['name_en']    = $data['name_en'];
        $insert['name_th']    = $data['name_th'];
        $insert['name_cn']    = $data['name_cn'];
        $insert['name_vn']    = $data['name_vn'];
        $insert['operate_id'] = $insert['update_id'] = $data['user_info']['id'];

        $model = new ShareCenterDir();
        $id    = $model->save($insert);
        return [
            'code'    => 1,
            'message' => 'success',
            'data'    => $id,
        ];
    }

    public function updateDir($data){
        $logger = $this->getDI()->get('logger');
        try{
            if(empty($data['level_code']) && empty($data['id']))
                return false;

            $model = new ShareCenterDir();
            if(!empty($data['level_code']))
                $act = $model->findFirst("level_code='{$data['level_code']}'");
            else
                $act = $model->findFirst("id={$data['id']}");
            if(empty($act))
                return [
                    'code' => 3,
                    'message' => static::$t->_('no_dir')
                ];

            $id = $act->id;


            //重名判断
            $model = new ShareCenterDir();
            $name_act = $model->findFirst("name_en='{$data['name_en']}' and name_th='{$data['name_th']}' and name_en='{$data['name_en']}'  and is_delete = 0 and id != {$id}");
            if(!empty($name_act)){
                return [
                    'code' => 3,
                    'message' => static::$t->_('dir_exist')
                ];
            }

            if(!empty($data['icon']))
                $act->icon = $data['icon'];
            if(!empty($data['name_en']))
                $act->name_en = $data['name_en'];
            if(!empty($data['name_th']))
                $act->name_th = $data['name_th'];
            if(!empty($data['name_cn']))
                $act->name_cn = $data['name_cn'];

            //更新时间
            $act->updated_at = date('Y-m-d H:i:s',time());

            $del = empty($data['is_delete']) ? 0 : 1;
            $act->update_id = $data['user_info']['id'];
            $act->is_delete = $del;

            return $act->update();
        }catch (\Exception $e){
            $logger->warning('shareDir-updateDir-fail:' . $e->getMessage());
            return false;
        }

    }

    /**
     * 根据层级 获取当前层级 添加code
     * @param $level
     * @param $p_code 父目录
     */
    protected function get_level_code($level,$p_code = ''){
        $num = 3;//code 固定位数 每层级3位

        $sql = "select max(level_code) from share_center_dir where `level` = {$level} ";
        //父目录  不为空
        if(!empty($p_code))
            $sql .= " and level_code like '{$p_code}%' ";
        $code = $this->getDI()->get('db_oa')->fetchColumn($sql);

        if(empty($code) && !empty($p_code))
            $code = $p_code . '001';
        else
            $code = intval($code) + 1;

        return str_pad($code,$level * $num,"0",STR_PAD_LEFT);
    }

    public function addFile($param){
        $logger = $this->getDI()->get('logger');
        $code = ErrCode::$SUCCESS;
        $message = '';

        $url_list = $param['file_url'];
        if(empty($url_list))
            return [
                'code' => 3,
                'message' => static::$t->_('empty_string')
            ];

        //验证文件数量
        if(count($url_list) > 10){
            return [
                'code' => 3,
                'message' => static::$t->_('size_notice')
            ];
        }
        //验证扩展名
        foreach ($url_list as $e){
            $extend = explode('.', $e);
            $extend = end($extend);
            if(!in_array($extend,$this->ext)){
                $code = ErrCode::$VALIDATE_ERROR;
                $message = static::$t->_('ext_notice');
                return [
                    'code' => $code,
                    'message' => $message
                ];
            }
        }

        //重名判断 批量的
        $model = new ShareCenterFile();
        $act = $model->findFirst([
            'conditions' => "name IN ({names:array}) AND dir_level_code = :dir_level_code: AND is_delete = 0",
            'bind' => ['names' => array_values($param['file_name']), 'dir_level_code' => $param['dir_level_code']],
        ]);

        if(!empty($act)){
            return [
                'code' => 3,
                'message' => static::$t->_('file_exist')
            ];
        }
        if(count($param['file_name']) != count(array_unique($param['file_name']))){
            return [
                'code' => 3,
                'message' => static::$t->_('file_exist')
            ];
        }

        //获取上传用户信息 部门
        $model = new StaffInfoModel();
        $staff_info = $model->user_info($param['user_info']['id']);
        if(empty($staff_info)){
            $code = ErrCode::$VALIDATE_ERROR;
            return [
                'code' => $code,
                'message' => 'no staff data'
            ];
        }
        $user_department_id = intval($staff_info['department_id']);
        $all_dep = ListService::getInstance()->get_department();
        $all_dep = array_column($all_dep,null,'id');
        if (!empty($all_dep[$user_department_id]) && $all_dep[$user_department_id]['ancestry'] != 1) {
            $user_department_id = $all_dep[$all_dep[$user_department_id]['ancestry']]['id'] ?? $user_department_id;
        }

        //保存 文件表
        try{
            $db = $this->getDI()->get('db_oa');
            $db->begin();

            //入库 文件表
            $model = new ShareCenterFile();
            //可查看部门权限  需额外增加product 20、IT&Product部门 6，不论有没有选择
            $dep_list = $this->getAllCanVieFileDepartments($param['department_data']);
            $isAllDep = $this->checkHaveCeoGroup($dep_list);

            foreach ($url_list as $k => $e) {
                $insert['file_url']       = $e;
                $insert['dir_level_code'] = $param['dir_level_code'];
                $names                    = explode('/', $e);
                $insert['name']           = end($names);
                if (!empty($param['file_name'][$k])) {
                    $insert['name'] = $param['file_name'][$k];
                }
                $insert['view_type'] = 1;//一期 只有部门
                if ($isAllDep) {
                    // 所有人都能看
                    $insert['view_type'] = -1;
                }
                $insert['operate_id']        = $insert['update_id'] = $param['user_info']['id'];
                $insert['add_department_id'] = $user_department_id;

                $clone_m = clone $model;//咋这么坑
                $clone_m->create($insert);
                $file_id = $clone_m->id;
                if (!$isAllDep) {
                    //如果不是所有部门  需要保存对应部门查看权限表
                    $this->savePermissionValue($dep_list, $file_id, []);
                }
            }

            $db->commit();
            return [
                'code' => $code,
                'message' => $message
            ];
        }catch (\Exception $e){
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('server error');
            $logger->warning('shareFile-addFile-fail:' . $e->getMessage());
            return [
                'code' => $code,
                'message' => $message
            ];
        }
    }

    /**
     * Notes: 保存权限设置
     * @param $departmentData
     * @param $fileId
     * @param $existsPermissions
     * @throws BusinessException
     */
    public function savePermissionValue($departmentData, $fileId, $existsPermissions)
    {
        foreach ($departmentData as $dep) {
            if ($dep['is_include_sub'] == 1) {
                //获取部门链
                $deptInfo = SysDepartmentModel::findFirst([
                    'conditions' => "id = :id: and deleted = 0",
                    'bind'       => [
                        'id' => $dep['id'],
                    ],
                ]);
                if (empty($deptInfo)) {
                    continue;
                }
                $chain     = SysDepartmentModel::find([
                    'conditions' => "ancestry_v3 like :chain: and deleted = 0",
                    'bind'       => [
                        'chain' => $deptInfo->ancestry_v3.'/%',
                    ],
                    'columns'    => 'id',
                ])->toArray();
                $chainList = array_column($chain, 'id');
                if (!empty($chainList)) {
                    foreach ($chainList as $item) {
                        $key = $this->getExistsPermissionsKey($fileId, $item);
                        if (isset($existsPermissions[$key])) {
                            // 已经存在的进行更新
                            $permissionModel = $existsPermissions[$key];
                            /**
                             * @var $permissionModel ShareCenterPermission
                             */
                            $permissionModel->is_delete      = 0;
                            $permissionModel->is_include_sub = 0;
                            $flag                            = $permissionModel->save();
                            if (!$flag) {
                                throw new BusinessException('保存权限部门失败1,error:'.$permissionModel->getErrorMessagesJson(), ErrCode::$BUSINESS_ERROR);
                            }
                            continue;
                        }
                        // 不存在的新增
                        $model                   = new ShareCenterPermission();
                        $model->file_id          = $fileId;
                        $model->permission_value = $item;
                        $model->is_include_sub   = 0;
                        $flag                    = $model->save();
                        if (!$flag) {
                            throw new BusinessException('保存权限部门失败2,error:'.$model->getErrorMessagesJson(), ErrCode::$BUSINESS_ERROR);
                        }
                        $existsPermissions[$key] = $model;
                    }
                }
            }

            $key1 = $this->getExistsPermissionsKey($fileId, $dep['id']);
            if (isset($existsPermissions[$key1])) {
                // 已经存在的进行更新
                $permissionModel = $existsPermissions[$key1];
                /**
                 * @var $permissionModel ShareCenterPermission
                 */
                $permissionModel->is_delete      = 0;
                $permissionModel->is_include_sub = $dep['is_include_sub'];
                $flag                            = $permissionModel->save();
                if (!$flag) {
                    throw new BusinessException('保存权限部门失败3,error:'.$permissionModel->getErrorMessagesJson(), ErrCode::$BUSINESS_ERROR);
                }
                continue;
            }

            $row['file_id']          = $fileId;
            $row['permission_value'] = $dep['id'];
            $row['is_include_sub']   = $dep['is_include_sub'];

            $model = new ShareCenterPermission;
            $flag  = $model->create($row);

            if (!$flag) {
                throw new BusinessException('保存权限部门失败4,error:'.$model->getErrorMessagesJson(), ErrCode::$BUSINESS_ERROR);
            }
            $existsPermissions[$key1] = $model;
        }
    }

    /**
     * Notes:
     * @param $fileId
     * @param $depId
     * @return string
     */
    public function getExistsPermissionsKey($fileId, $depId): string
    {
        return $fileId.'_'.$depId;
    }


    //修改文件信息
    public function editFile($param){
        $logger = $this->getDI()->get('logger');
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try{
            $code = ErrCode::$SUCCESS;
            $message = '';

            //验证扩展名
            if(!empty($param['file_url'])){
                $extend = explode('.', $param['file_url']);
                $extend = end($extend);
                if(!in_array($extend,$this->ext)){
                    $code = ErrCode::$VALIDATE_ERROR;
                    $message = static::$t->_('ext_notice');
                    return [
                        'code' => $code,
                        'message' => $message
                    ];
                }
            }
            $file_model = new ShareCenterFile();


            //验证文件信息
            $file_info = $file_model->findFirst("id={$param['file_id']} and is_delete = 0 ");
            if (empty($file_info)) {
                $code    = ErrCode::$VALIDATE_ERROR;
                $message = static::$t->_('no_file');
                return [
                    'code'    => $code,
                    'message' => $message,
                ];
            }
            /**
             * @var $file_info ShareCenterFile
             */
            $info = $file_info->toArray();


            //重名判断
            if(!empty($param['file_name'])){
                $file_model = new ShareCenterFile();
                $act = $file_model->findFirst("name='{$param['file_name']}' and dir_level_code = '{$param['dir_level_code']}' and is_delete = 0 and id != {$info['id']}");
                if(!empty($act)){
                    return [
                        'code' => 3,
                        'message' => static::$t->_('file_exist')
                    ];
                }
            }

            //验证目录信息
            $dir_model = new ShareCenterDir();
            $dir_info = $dir_model->findFirst("level_code='{$param['dir_level_code']}' and is_delete = 0");
            if(empty($dir_info)){
                $code = ErrCode::$VALIDATE_ERROR;
                $message = static::$t->_('no_directory');
                return [
                    'code' => $code,
                    'message' => $message
                ];
            }

            $newPermission = $this->getAllCanVieFileDepartments($param['department_data']);
            $isAllDep = $this->checkHaveCeoGroup($newPermission);

            //格式化数据
            $file_info->dir_level_code = $param['dir_level_code'];
            if (!empty($param['file_name'])) {
                $file_info->name = $param['file_name'];
            }
            if (!empty($param['file_url'])) {
                $file_info->file_url = $param['file_url'];
            }
            $file_info->update_id = $param['user_info']['id'];
            //如果 选择 全部 更新view_type -1
            $file_info->view_type = 1;
            if ($isAllDep) {
                $file_info->view_type = -1;
            }

            //更新时间
            $file_info->updated_at = date('Y-m-d H:i:s',time());

            $flag = $file_info->update();
            if(!$flag){
                $code = ErrCode::$VALIDATE_ERROR;
                $message = static::$t->_('server error');
                $db->rollback();
                return [
                    'code' => $code,
                    'message' => $message
                ];
            }

            // 不是全部人都能看则保存权限设置
            if (!$isAllDep) {
                // 把原来的权限设置删除（share_center_permission表主键是PRIMARY KEY (`file_id`,`permission_value`)，不能全部删除之后在全部重新添加）
                $existsPermissions = ShareCenterPermission::find([
                    'conditions' => 'file_id = :file_id:',
                    'bind'       => [
                        'file_id' => $param['file_id'],
                    ],
                ]);

                foreach ($existsPermissions as $permission) {
                    /**
                     * @var $permission ShareCenterPermission
                     */
                    if ($permission->is_delete == Enums\GlobalEnums::IS_DELETED) {
                        // 已经是被删除状态，跳过
                        continue;
                    }
                    $permission->is_delete = Enums\GlobalEnums::IS_DELETED;
                    $res                   = $permission->save();
                    if (!$res) {
                        throw new BusinessException('删除老的权限失败，error：'.$permission->getErrorMessagesJson(), ErrCode::$BUSINESS_ERROR);
                    }
                }

                // 添加新的权限
                $existsPermissionT = [];
                foreach ($existsPermissions as $v) {
                    $existsPermissionT[$this->getExistsPermissionsKey($v->file_id, $v->permission_value)] = $v;
                }
                $this->savePermissionValue($newPermission, $param['file_id'], $existsPermissionT);
            }

            $db->commit();
            //记录日志 查问题
            $logger->info('shareFile-editFile-:' . json_encode($param, JSON_UNESCAPED_UNICODE));
            return [
                'code' => $code,
                'message' => $message
            ];

        }catch (\Exception $e){
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('server error');
            $logger->warning('shareFile-editFile-fail:'.$e->getMessage().',trace:'.$e->getTraceAsString());
            $db->rollback();
            return [
                'code' => $code,
                'message' => $message
            ];
        }
    }

    public function del_file($file_id){

        $db = $this->getDI()->get('db_oa');
        $file_model = new ShareCenterFile();
        $file_info = $file_model->findFirst("id={$file_id} and is_delete = 0");
        if(empty($file_info)){
            return ['code' => ErrCode::$VALIDATE_ERROR,'message' => static::$t->_('no_file')];
        }
        $file_info->is_delete = 1;
        $file_info->save();
        $del_sql = "update share_center_permission set is_delete = 1 where file_id = {$file_id}";
        $flag = $db->execute($del_sql);

        if($flag)
            return ['code' => ErrCode::$SUCCESS,'message' =>'','data' => $flag];
        else
            return ['code' => ErrCode::$VALIDATE_ERROR,'message' =>'server error','data' => $flag];


    }

    /**
     * Notes: 删除文件夹-文件夹下的文件一并删除
     * @param $param
     * @return bool
     * @throws BusinessException|AuthorizationException
     * @throws ValidationException
     */
    public function delDir($param): bool
    {
        // 检查删除权限
        $this->checkDelDirPermission($param);
        $dirCode = $param['level_code'];
        // 获取文件夹和子文件夹
        $repo = new ShareCenterDirRepository();
        $info = $repo->getInfoByLevelCode($dirCode);
        if (!$info) {
            // 文件夹不存在
            return true;
        }

        if ($info->isDefault()) {
            // 默认文件夹不能删除
            throw new ValidationException(self::$t->_('The default folder cannot be deleted'));
        }

        // 上面判断了文件夹是否存在，这里至少会返回一个
        $dirs     = $repo->getDirListByLevelCode($dirCode);
        $dirCodes = array_column($dirs->toArray(), 'level_code');
        // 获取文件夹下的文件
        $files = ShareCenterFile::find([
            'conditions' => 'dir_level_code IN ({dir_level_code:array}) AND is_delete = :is_delete:',
            'bind'       => [
                'dir_level_code' => $dirCodes,
                'is_delete'      => ShareCenterFile::NOT_DELETE,
            ],
        ]);

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        // 删除文件夹
        foreach ($dirs as $dir) {
            /**
             * @var $dir ShareCenterDir
             */
            $dir->update_id = $param['user_info']['id'];
            $dir->is_delete = ShareCenterDir::DELETE;
            $res            = $dir->update();
            if (!$res) {
                $db->rollback();
                $this->logger->warning('删除文件共享中心文件夹失败,error:'.$dir->getErrorMessagesJson());
                throw new BusinessException(self::$t->_('server error'), ErrCode::$BUSINESS_ERROR);
            }
        }

        // 删除文件
        foreach ($files as $file) {
            /**
             * @var $file ShareCenterFile
             */
            $file->update_id = $param['user_info']['id'];
            $file->is_delete = ShareCenterFile::DELETE;
            $res             = $file->save();
            if (!$res) {
                $db->rollback();
                $this->logger->warning('删除文件共享中心文件失败,error:'.$file->getErrorMessagesJson());
                throw new BusinessException(self::$t->_('server error'), ErrCode::$BUSINESS_ERROR);
            }

            // 删除文件权限
            $permissions = ShareCenterPermission::find([
                'conditions' => 'file_id = :file_id: AND is_delete = 0',
                'bind'       => ["file_id" => $file->id],
            ]);

            // -1 表明选择了 Group CEO ，权限表是不插入数据的。
//            if ($file->view_type != '-1' && (!is_object($permissions) || empty($permissions->toArray()))) {
//                $this->logger->warning('未找到权限数据，error：'.$file->getErrorMessagesJson());
//                throw new BusinessException(self::$t->_('server error'), ErrCode::$BUSINESS_ERROR);
//            }

            foreach ($permissions as $permission) {
                /**
                 * @var $permission ShareCenterPermission
                 */
                $permission->is_delete = Enums\GlobalEnums::IS_DELETED;
                $res                   = $permission->save();
                if (!$res) {
                    $db->rollback();
                    $this->logger->warning('删除老的权限失败，error：'.$permission->getErrorMessagesJson());
                    throw new BusinessException(self::$t->_('server error'), ErrCode::$BUSINESS_ERROR);
                }
            }
        }
        $db->commit();
        return true;
    }

    /**
     * Notes: 检查删除文件夹的权限
     * @param $param
     * @throws AuthorizationException
     */
    public function checkDelDirPermission($param)
    {
        $can_edit_file_department_ids = $this->getCanEditFilePrivilegeDepartmentIds();
        if (!in_array($param['user_info']['department_id'], $can_edit_file_department_ids)) {
            // 无权限
            throw new AuthorizationException('forbidden');
        }
    }

    /**
     * Notes: 获取所有可查看文件的部门（包活特殊配置部门）
     * @param array $inputDeps
     * @return array
     */
    private function getAllCanVieFileDepartments(array $inputDeps): array
    {
        $can_view_file_department_ids = $this->getCanViewFilePrivilegeDepartmentIds();
        $repeatIds                    = [];
        foreach ($inputDeps as $item) {
            if (in_array($item['id'], $can_view_file_department_ids)) {
                // 与特殊权限部门重合
                $repeatIds[] = $item['id'];
            }
        }
        $addDepartments = [];
        foreach ($can_view_file_department_ids as $can_view_file_department_id) {
            if (in_array($can_view_file_department_id, $repeatIds)) {
                // 以用户的提交为准
                continue;
            }
            $temp                   = [];
            $temp['id']             = $can_view_file_department_id;
            $temp['is_include_sub'] = 0;
            $addDepartments[]       = $temp;
        }
        return array_merge($inputDeps, $addDepartments);
    }

    /**
     * Notes: 检查用户是否选择了最顶级的部门
     * @param $departmentData
     * @return bool
     */
    private function checkHaveCeoGroup($departmentData): bool
    {
        $departmentRepo = new DepartmentRepository();
        $topDep         = $departmentRepo->getTopDepartment();
        $topDepId       = Enums\GlobalEnums::TOP_DEPARTMENT_ID;
        if ($topDep) {
            $topDepId = $topDep->id;
        }
        $result = false;
        foreach ($departmentData as $datum) {
            if ($datum['id'] == $topDepId && $datum['is_include_sub'] == 1) {
                $result = true;
            }
        }
        return $result;
    }
}