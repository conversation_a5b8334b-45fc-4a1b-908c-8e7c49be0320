<?php

namespace App\Modules\Share\Services;

use App\Library\Enums;
use App\Library\RedisClient;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Contract\Models\Contract;
use App\Util\RedisExpire;
use App\Util\RedisKey;

class BaseService extends \App\Library\BaseService
{

    public static $validate_detail = [
        'id' => 'Required|IntGe:1'                                  //合同ID
    ];

    private static $validate_update = [
        'id' => 'Required|IntGe:1'                                  //合同ID
    ];

    public static function handleParams($params, $not_must)
    {
        if (empty($not_must) || !is_array($not_must)) {
            return $params;
        }
        foreach ($not_must as $value) {
            if (isset($params[$value]) && empty($params[$value])) {
                unset($params[$value]);
            }
        }
        return $params;
    }

    /**
     * @param $oss_file_arr
     * @return string
     */
    public function handle_oss_file($oss_file_arr)
    {
        if (empty($oss_file_arr) || !is_array($oss_file_arr)) {
            return '';
        }
        $oss_key_arr = [];
        foreach ($oss_file_arr as $item) {
            $oss_key_arr[] = implode('@', $item);
        }
        return implode(',', $oss_key_arr);
    }

    /**
     * @param $path
     * @return array|string
     */
    public function getRemotePath($path)
    {
        if (empty($path)) {
            return [];
        }
        $path_arr = [];
        if (is_array($path)) {
            foreach ($path as $val) {
                $file_arr = $this->getOssFileName($val);
                $path_arr[] = [
                    'file_name' => $file_arr['file_name'],
                    'bucket_name' => $file_arr['bucket_name'],
                    'object_key' => $file_arr['object_key'],
                ];
            }
        } else {
            $file_arr = $this->getOssFileName($path);
            $path_arr[] = [
                'file_name' => $file_arr['file_name'],
                'bucket_name' => $file_arr['bucket_name'],
                'object_key' => $file_arr['object_key'],
            ];
        }

        return $path_arr;
    }

    /**
     * @param $oss_file
     * @return array|mixed
     */
    private function getOssFileName($oss_file)
    {
        if (empty($oss_file) || strpos($oss_file, '@') == false) {
            return [
                'file_name' => '',
                'bucket_name' => '',
                'object_key' => '',
            ];
        }
        list($file_name, $bucket, $object_key) = explode('@', $oss_file);

        return [
            'file_name' => $file_name,
            'bucket_name' => $bucket,
            'object_key' => $object_key,
        ];
    }

    /**
     * @param $type
     * @param bool $is_update
     * @return array
     */
    public static function getValidateParams($type, $is_update = false)
    {
        $rules = [];
        $type = $type ? : 0;
        if (isset(self::$validate_other[$type])) {
            $rules = self::$validate_other[$type];
        }
        if ($is_update === true) {
            $rules = array_merge($rules, self::$validate_update);
        }

        return array_merge(self::$validate_currency, $rules);
    }

    /**
     * 获取有文件查看特权的部门id清单
     */
    public function getCanViewFilePrivilegeDepartmentIds ()
    {
        $ids = EnvModel::getEnvByCode('info_share_center_can_view_file_department_ids', '');
        return array_unique(array_filter(explode(',' , $ids)));
    }

    /**
     * 获取有文件编辑特权的部门id清单
     */
    public function getCanEditFilePrivilegeDepartmentIds ()
    {
        $ids = EnvModel::getEnvByCode('info_share_center_can_edit_file_department_ids', '');
        return array_unique(array_filter(explode(',' , $ids)));
    }

}
