<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 2020/5/20
 * Time: 下午2:19
 */


namespace App\Modules\Share\Controllers;

use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Share\Services\ListService;
use App\Library\ErrCode;
use App\Modules\Share\Services\AddService;
use App\Modules\User\Services\UserService;
use App\Library\Enums;

class ShareController extends BaseController{




    //外层 目录列表页接口 获取一级目录
    /**
     * @Token
     */
    public function dir_listAction(){
//        echo $this->user['id'];exit;
        $param['page'] = $this->request->get('page','int');
        $param['size'] = $this->request->get('size','int');
        $param['level'] = $this->request->get('level','int');
        $param['level_code'] = $this->request->get('level_code','string');
        $param['staff_info'] = $this->user;
        $param['file_name'] = $this->request->get('file_name','string');
        $param['department_id'] = $this->request->get('department_id');
        $list = ListService::getInstance()->getDirList($param);
        return $this->returnJson(ErrCode::$SUCCESS, '', $list);
    }


    //内存 文件列表页接口
    /**
     * @Permission(action='share.downFile')
     */
    public function file_listAction(){
        try{
            $param['page'] = $this->request->get('page','int');
            $param['size'] = $this->request->get('size','int');
            $param['code'] = $this->request->get('level_code','string');
            $param['file_name'] = $this->request->get('file_name','string');
            $param['department_id'] = $this->request->get('department_id');
            $param['staff_info'] = $this->user;
            $param = filter_param($param);


            $list = ListService::getInstance()->getFileList($param);
            return $this->returnJson(ErrCode::$SUCCESS, '', $list);
        }catch (\Exception $e){
            echo $e->getMessage();
        }

    }


    /**
     * Notes: 创建文件夹
     * @Permission(action='share.addDir')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function add_dirAction()
    {
        $param['p_lever']   = $this->request->get('level', 'string');
        $param['p_code']    = $this->request->get('level_code', 'string');
        $param['id']        = $this->request->get('id', 'int');        //有则update  没有 insert
        $param['name_en']   = $this->request->get('name_en', 'string');//操作目录名称
        $param['name_th']   = $this->request->get('name_th', 'string');//操作目录名称
        $param['name_cn']   = $this->request->get('name_cn', 'string');//操作目录名称
        $param['name_vn']   = $this->request->get('name_vn', 'string');//操作目录名称
        $param['user_info'] = $this->user;

        $param = filter_param($param);


        try {
            $res = AddService::getInstance()->createDir($param);
            return $this->returnJson($res['code'], $res['message'], $res['data']);
        } catch (\Exception $e) {
            //$logger = $this->getDI()->get('logger');
            //$logger->warning('share-add_dir:'.$e->getMessage());
            $this->logger->warning('share-add_dir:'.$e->getMessage());
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, 'server error', []);
        }
    }

    /**
     * Notes: 编辑文件夹
     * @Permission(action='share.editDir')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function update_dirAction()
    {
        $param['id']        = $this->request->get('id', 'int');
        $param['name_en']   = $this->request->get('name_en', 'string');//操作目录名称
        $param['name_th']   = $this->request->get('name_th', 'string');//操作目录名称
        $param['name_cn']   = $this->request->get('name_cn', 'string');//操作目录名称
        $param['name_vn']   = $this->request->get('name_vn', 'string');//操作目录名称
        $param['user_info'] = $this->user;

        $param = filter_param($param);
        $this->checkDirName($param);

        try {
            $flag = AddService::getInstance()->updateDir($param);
            $code = $flag ? ErrCode::$SUCCESS : ErrCode::$SYSTEM_ERROR;
            return $this->returnJson($code, '', []);
        } catch (\Exception $e) {
            //$logger = $this->getDI()->get('logger');
            //$logger->warning('share-add_dir:'.$e->getMessage());
            $this->logger->warning('share-add_dir:'.$e->getMessage());
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, 'server error', []);
        }
    }

    /**
     * Notes: 添加、编辑文件夹名称检查
     * @param $param
     * @throws ValidationException
     */
    private function checkDirName($param)
    {
        switch (get_country_code()) {
            case Enums\GlobalEnums::TH_COUNTRY_CODE:
                $validate = [
                    'name_en' => 'Required|StrLenGeLe:1,51',
                    'name_cn' => 'StrLenGeLe:0,51',
                    'name_th' => 'Required|StrLenGeLe:1,51',
                ];
                break;
            case Enums\GlobalEnums::VN_COUNTRY_CODE:
                $validate = [
                    'name_en' => 'Required|StrLenGeLe:1,51',
                    'name_cn' => 'StrLenGeLe:0,51',
                    'name_vn' => 'Required|StrLenGeLe:1,51',
                ];
                break;
            default:
                $validate = [
                    'name_en' => 'Required|StrLenGeLe:1,51',
                    'name_cn' => 'StrLenGeLe:0,51',
                    'name_th' => 'StrLenGeLe:0,51',
                ];
                break;
        }
        Validation::validate($param, $validate);
    }

    /**
     * Notes: 删除目录接口
     * @Permission(action='share.delDir')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Exception\BusinessException|\App\Library\Exception\AuthorizationException
     */
    public function del_dirAction()
    {
        $dir_code = $this->request->get('level_code', 'string');
        $param['level_code'] = $dir_code;
        $param['user_info'] = $this->user;
        $flag = AddService::getInstance()->delDir($param);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $flag);
    }



    //上传文件 修改文件 重命名文件
    /**
     * @Permission(action='share.addFile')
     */
    public function upload_fileAction(){
        //.pptx .ppt .pdf .doc .docx .jpg .png .xls .xlsx
        //默认所有可查看部门均有product 、IT&Product部门，不论有没有选择

        try{
            $param['department_data'] = $this->request->get('department_data');
            $param['dir_level_code'] = $this->request->get('dir_level_code','string');
            $param['file_url'] = $this->request->get('file_url');
            $param['file_name'] = $this->request->get('file_name');
            $param['user_info'] = $this->user;

            // 参数基础过滤
            $param = filter_param($param);

            // 参数再次校验
            $validate = [
                'department_data' => 'Required|Arr|ArrLenGe:1|>>>:param error[department_data]',
                'dir_level_code' => 'Required|Numbers|>>>:param error[level_code]',
                'file_url' => 'Required|Arr|ArrLenGe:1|>>>:param error[file_url]',
                'file_url[*]' => 'Required|Url|>>>:param error[file_url]',
                'file_name' => 'Required|Arr|ArrLenGe:1|>>>:param error[file_name]',
                'file_name[*]' => 'Required|StrLenGeLe:1,500|>>>:param error[file_name]',
            ];

            Validation::validate($param, $validate);

            $res = AddService::getInstance()->addFile($param);
            return $this->returnJson($res['code'], $res['message'], []);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage(), []);

        } catch (\Exception $e){
            $this->logger->error( '信息共享中心 - 文件上传 异常: ' . $e->getMessage());
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, 'exception', []);
        }
    }

    //编辑文件相关信息接口
    /**
     * @Permission(action='share.editFile')
     */
    public function edit_fileAction(){
        try{
            $param['file_id'] = $this->request->get('file_id','int');//文件主键
            $param['department_data'] = $this->request->get('department_data');//需更新的 查看权限部门id 需匹配源id 多退少补
            $param['dir_level_code'] = $this->request->get('level_code','string');//文件所在文件夹位置 变更
            $param['file_url'] = $this->request->get('file_url','string');//可更改别的文件
            $param['file_name'] = $this->request->get('file_name','string');//可命名
            $param['user_info'] = $this->user;

            // 参数基础过滤
            $param = filter_param($param);

            // 参数再次校验
            $validate = [
                'file_id' => 'Required|IntGe:1|>>>:param error[file_id]',
                'department_data' => 'Required|Arr|ArrLenGe:1|>>>:param error[department_data]',
                'dir_level_code' => 'Required|Numbers|>>>:param error[level_code]',
                'file_url' => 'Required|Url|>>>:param error[file_url]',
                'file_name' => 'Required|StrLenGeLe:1,500|>>>:param error[file_name]',
            ];

            Validation::validate($param, $validate);

            $res = AddService::getInstance()->editFile($param);
            return $this->returnJson($res['code'], $res['message'], []);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage(), []);

        } catch (\Exception $e){
            $this->logger->error( '信息共享中心 - 文件编辑 异常: ' . $e->getMessage());
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, 'exception', []);
        }
    }
    /**
     * @Token
     */
    public function department_listAction(){
        //Human Resources部门的人可以选择公司任意一个部门，或所有部门
        //Network Management、Hub、Shop Porject部门的人默认选择自己部门，且只有一个选项
        //Network Management：部门ID=4
        //hub：部门ID=25
        //Shop Porject：部门ID=13
        $forbidden = ListService::$forbidden;

        $list = ListService::getInstance()->get_department();
        $list = array_column($list, null, 'id');

        //获取用户信息
        $model = new UserService();
        $user_info = $model->getUserById($this->user['id'])->toArray();

//        $model = new UserService();
//        $user_info = $model->get_user_info($this->user['id']);
        //如果该员工 部门是子部门 找父部门
        $p_id = $list[$user_info['department_id']]['ancestry'];
        $parent_department = $list[$user_info['department_id']];
        $return = array();
        if($p_id != 1)
            $parent_department = $list[$p_id];

        if(in_array($parent_department['id'], $forbidden))
            $return = array($list[$parent_department['id']]);
        else{//返回所有 一级部门 排除子部门
            foreach ($list as $li){
                if($li['ancestry'] == 1)
                    $return[] = $li;
            }

        }
        return $this->returnJson( ErrCode::$SUCCESS , '', $return);
    }


    //删除文件接口 同时对应 permission 更新删除字段
    /**
     * @Permission(action='share.delFile')
     */
    public function del_fileAction(){
        $file_id = $this->request->get('file_id','int');//文件主键
        $res = AddService::getInstance()->del_file($file_id);
        return $this->returnJson($res['code'], $res['message'], []);
    }

    //图片下载 转流
    /**
     * @Permission(action='user.permission.grant')
     */
    public function down_imgAction(){
        $filename = $this->request->get('file_name','string');
        ob_start();
        readfile($filename);
        $img = ob_get_contents();
        ob_end_clean();
        header('Content-Disposition: attachment;filename=' . basename($filename));
        if(!empty($img)){
            return $this->returnStream($img);
        }
        return $this->returnStream('no file');
    }

    /**
     * @Token
     */
    public function all_departmentAction(){
        $list = ListService::getInstance()->get_department();
        $list = array_column($list, null, 'id');
        $return = array();
        foreach ($list as $li){
            if($li['ancestry'] == 1)
                $return[] = $li;
        }
        return $this->returnJson( ErrCode::$SUCCESS , '', $return);
    }

}