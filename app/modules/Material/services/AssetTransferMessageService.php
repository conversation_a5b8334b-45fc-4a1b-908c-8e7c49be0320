<?php

namespace App\Modules\Material\Services;

use App\Library\ApiClient;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\MaterialEnums;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Models\backyard\HrStaffInfoModel;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Common\Services\StaffLangService;

class AssetTransferMessageService extends BaseService
{
    private static $instance;
    //push消息翻译key
    //提醒
    public static $push_remind_title = 'transfer_asset_message_remind_title';
    //拒绝
    public static $push_reject_title = 'transfer_asset_message_reject_title';
    //自动接收
    public static $push_auto_receive_title = 'transfer_asset_message_auto_receive_title';
    //待接收资产超过资产转移间隔时间提醒
    public static $push_pending_receipt_reminder_title = 'transfer_asset_pending_receipt_reminder_title';

    //push类型
    //提醒
    public static $push_type_remind = 1;
    //拒绝
    public static $push_type_reject = 2;
    //自动接收
    public static $push_type_receive = 3;
    //待接收资产超过资产转移间隔时间提醒
    public static $push_type_pending_receipt_reminder = 4;

    //站内信title翻译key
    //提醒
    public static $by_message_remind_title = 'message_title_transfer_asset_batch_remind';
    //拒绝
    public static $by_message_reject_title = 'message_title_transfer_asset_batch_reject';
    //接收
    public static $by_message_receive_title = 'message_title_transfer_asset_auto_reception';
    //批量提醒邮件翻译key
    public static $email_content_key = 'asset_transfer_email_content_remind';
    public static $email_oa_url_text_key = 'oa_url_text';
    public static $email_title_key = 'asset_transfer_email_title_remind';
    /**
     * 单例
     * @return AssetTransferMessageService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 我转出-批量提醒-发送邮件
     * @param string $send_user 发件人 工号(姓名)
     * @param array $send_to_user_number key=数量 value=[员工id]
     * @return bool
     * @date 2022/10/28
     */
    public function sendEmail(string $send_user, array $send_to_user_number)
    {
        try {
            $is_success = false;
            //您好, %user% 给你转移了 %number% 个资产，请及时去“OA系统-物料/资产管理-资产转移-待接收”查看并操作！或者登录BY，去“我的资产-待接收资产”查看并操作！
            //获取翻译
            $lang = $this->getNoticeEmailTemplateLang();
            $first_lang = $lang['first_lang'];
            $second_lang = $lang['second_lang'];
            //oa系统链接
            $first_url_text = $first_lang->_(self::$email_oa_url_text_key);
            $second_url_text = $second_lang->_(self::$email_oa_url_text_key);
            // 各环境各国家dashboard页地址
            $url = EnvModel::getEnvByCode(GlobalEnums::OA_DASHBOARD_PAGE_URL_CODE);
            $url = !empty($url) ? $url : GlobalEnums::OA_DASHBOARD_PAGE_URL_DEFAULT;
            $title = $second_lang->_(self::$email_title_key);
            //查询所有收件人邮箱
            $staff_ids = [];
            foreach ($send_to_user_number as $to_user_arr) {
                foreach ($to_user_arr as $user_id) {
                    $staff_ids[] = $user_id;
                }
            }
            $staff_ids = array_values(array_unique($staff_ids));
            $emails = HrStaffInfoModel::find([
                'columns' => 'staff_info_id, email, personal_email',
                'conditions' => 'staff_info_id in ({ids:array})',
                'bind' => [
                    'ids' => $staff_ids,
                ]
            ])->toArray();
            $emails = array_column($emails, null, 'staff_info_id');
            //分批发送不同提醒内容的收件人
            foreach ($send_to_user_number as $number => $to_user) {
                //获取翻译后的内容
                $first_content = $first_lang->_(self::$email_content_key, ['user' => $send_user, 'number' => $number]);
                $second_content = $second_lang->_(self::$email_content_key, ['user' => $send_user, 'number' => $number]);
                $content = <<<EOF
    <span style="margin-left: 16px"></span>{$first_content}<br/>
    <span style="margin-left: 16px"></span>{$first_url_text}<a href="{$url}" target="_blank">{$url}</a><br/>
    <span style="margin-left: 16px"></span>{$second_content}<br/>
    <span style="margin-left: 16px"></span>{$second_url_text}<a href="{$url}" target="_blank">{$url}</a>
EOF;
                //获取邮箱
                $to_emails = [];
                foreach ($to_user as $one_user) {
                    $one_user_email = !empty($emails[$one_user]['email']) ? $emails[$one_user]['email'] : $emails[$one_user]['personal_email'];
                    if (empty($one_user_email)) {
                        $this->logger->info('assetTransferSendEmail not_email_address！[' . $title . ' ], to user=' . json_encode($one_user, JSON_UNESCAPED_UNICODE));
                        continue;
                    }
                    $to_emails[] = $one_user_email;
                }
                //没有邮箱不发
                if (empty($to_emails)) {
                    continue;
                }
                //有邮箱,发送邮件
                $send_res = $this->mailer->sendAsync($to_emails, $title, $content);
                if ($send_res) {
                    //有一个成功就返回成功, 不影响后续逻辑
                    $is_success = true;
                    $this->logger->info("assetTransferSendEmail 发送成功！[" . $title . "] " . json_encode($content, JSON_UNESCAPED_UNICODE) . ' to user=' . json_encode($to_emails));
                } else {
                    $this->logger->warning("assetTransferSendEmail 发送失败！[" . $title . "] " . json_encode($content, JSON_UNESCAPED_UNICODE) . ' to user=' . json_encode($to_emails));
                }
            }
        } catch (ValidationException $e) {
            $this->logger->info('assetTransferSendEmail 验证错误！[ 发件人=' . $send_user . ';收件人=' . json_encode($send_to_user_number, JSON_UNESCAPED_UNICODE) . '] 原因可能是：' . $e->getMessage());
        } catch (BusinessException $e) {
            $this->logger->warning('assetTransferSendEmail 业务异常！[ 发件人=' . $send_user . ';收件人=' . json_encode($send_to_user_number, JSON_UNESCAPED_UNICODE) . '] 原因可能是：' . $e->getMessage());
        } catch (\Exception $e) {
            $this->logger->error('assetTransferSendEmail 发送异常！[ 发件人=' . $send_user . ';收件人=' . json_encode($send_to_user_number, JSON_UNESCAPED_UNICODE) . '] 原因可能是：' . $e->getMessage());
        }
        return $is_success;
    }

    /**
     * 推送PUSH
     * @param array $send_to_user 接收人数组
     * @param int $type 消息类型
     * @param string $message_scheme 跳转地址
     * @param string $platform 平台
     * @return bool
     */
    public function sendPush(array $send_to_user, $type, $message_scheme = '', $platform = 'backyard')
    {
        try {
            //是否成功
            $is_success = false;
            if ($type == self::$push_type_remind) {
                $title_key = self::$push_remind_title;
            } elseif ($type == self::$push_type_reject) {
                $title_key = self::$push_reject_title;
            } elseif ($type == self::$push_type_receive) {
                $title_key = self::$push_auto_receive_title;
            } elseif ($type == self::$push_type_pending_receipt_reminder) {
                $title_key = self::$push_pending_receipt_reminder_title;
            } else {
                return $is_success;
            }
            $send_to_user = array_unique($send_to_user);
            //逐条推送push
            foreach ($send_to_user as $value) {
                //兼容空号空不发送push
                if (!$value) {
                    continue;
                }
                $title_and_content = StaffLangService::getInstance()->getMsgTemplateByUserId($value, $title_key);
                $data = [
                    "staff_info_id" => $value,  //员工工号
                    "message_title" => $title_and_content,  //标题
                    "message_content" => $title_and_content,//内容
                    "message_scheme" => $message_scheme, //地址
                    "message_priority" => 0,// push优先级: 0-普通; 1-优先
                ];
                $data['src'] = $platform;
                $ret = new ApiClient('bi_svc', '', 'push_to_staff');
                $ret->setParams([$data]);
                $res = $ret->execute();
                $bi_return = $res && isset($res['result']) && $res['result'] == true;
                $res_msg = $bi_return ? '成功' : '失败';
                $res_log = '给平台为[backyard]员工工号[' . $value . ']发送资产转移push消息，发送时间[' . date('Y-m-d H:i:s') . ';send_to_user=' . json_encode($send_to_user) . ';type=' . $type . ']，发送结果[' . $res_msg . ']';
                $this->logger->info($res_log);
                if ($bi_return) {
                    $is_success = true;
                }
            }
        } catch (\Exception $e) {
            $this->logger->warning("发送资产转移push消息失败, message=" . $e->getMessage() . ';send_to_user=' . json_encode($send_to_user) . ';type=' . $type . PHP_EOL);
        }
        return $is_success;
    }

    /**
     * 发送by站内信
     * @param $message_info
     * @return bool|mixed|null
     * @date 2022/10/28
     */
    public function sendMessage($message_info)
    {
        $this->logger->info("send_message_input:" . json_encode(['message' => $message_info, 'date' => date('Y-m-d H:i:s')]));
        try {
            $kit_param = [];
            //扩展方法支持单个或批量发站内信
            if (isset($message_info['staff_users'])) {
                $kit_param['staff_users'] = $message_info['staff_users'];
            } else {
                $kit_param['staff_users'] = [['id' => $message_info['staff_info_id']]];
            }
            $kit_param['message_title'] = $message_info['message_title'];
            $kit_param['message_content'] = $message_info['message_content'];
            $kit_param['category'] = $message_info['category'];

            $bi_rpc = (new ApiClient('bi_svc', '', 'add_kit_message'));
            $bi_rpc->setParams([$kit_param]);
            $res = $bi_rpc->execute();
            if (isset($res['result']['code']) && ($res['result']['code'] == 1)) {
                return $res;
            }
            $this->logger->warning("send_message_api_return_info" . json_encode($res));
        } catch (\Exception $e) {
            $this->logger->warning("send_message_api_error" . $e->getMessage() . PHP_EOL);
        }
        return false;
    }

    /**
     * 资产转移-我转出-批量提醒/资产业务数据查询-批量提醒
     * @param $user_number ['用户id'=>数量]
     * @param int $category 批量提醒类型 我转出/业务数据查询
     * @param array $user 员工信息 我转出类型需要
     * @return bool
     * @date 2022/11/26
     */
    public function sendRemindMessage($user_number, $category, $user)
    {
        $message_info = [];
        //中英泰翻译
        $translation_zh = $this->getTranslation('zh');
        $translation_en = $this->getTranslation('en');
        $translation_th = $this->getTranslation('th');
        //2.标题
        $title_key = self::$by_message_remind_title;
        $title_zh = $translation_zh->_($title_key);
        $title_en = $translation_en->_($title_key);
        if (get_country_code() == GlobalEnums::TH_COUNTRY_CODE) {
            $title_th = $translation_th->_($title_key);
            $message_info['message_title'] = $title_th . ' ' . $title_en . ' ' . $title_zh;
        } else {
            $message_info['message_title'] = $title_en . ' ' . $title_zh;
        }

        //3.分类
        $message_info['category'] = $category;
        //4.内容
        //接收人
        $all_staff_id = array_values(array_keys($user_number));
        //查询员工信息
        $staff_data = HrStaffInfoModel::find([
            'columns' => 'staff_info_id, name, node_department_id',
            'conditions' => 'staff_info_id in ({ids:array})',
            'bind' => [
                'ids' => $all_staff_id,
            ]
        ])->toArray();
        $staff_data = array_column($staff_data, null, 'staff_info_id');
        foreach ($user_number as $staff_id => $number) {
            $message_info['staff_info_id'] = $staff_id;
            if ($category == MaterialEnums::MATERIAL_TRANSFER_MESSAGE_CATEGORY_REMIND1) {
                //转移人
                $staff_view_name = AssetTransferListService::getInstance()->getViewName($user['id'], false, $user['name']);
            } else {
                //接收人
                $staff_view_name = AssetTransferListService::getInstance()->getViewName($staff_id, false, $staff_data[$staff_id]['name'] ?? '');
            }
            $content = [
                'staff_name' => $staff_view_name,
                'number' => $number,
            ];
            $message_info['message_content'] = json_encode($content);
            $this->sendMessage($message_info);
        }
        return true;
    }

    /**
     * 待接收-批量拒绝-发送by站内信
     * @param array $reject_staff_asset 拒绝详情 ['用户id'=>[资产id集合]]
     * @param string $remark 拒绝原因
     * @param array $user 拒绝人信息
     * @return bool|mixed|null
     * @date 2022/10/28
     */
    public function sendRejectMessage($reject_staff_asset, $remark, $user)
    {
        $message_info = [];
        //中英泰翻译
        $translation_zh = $this->getTranslation('zh');
        $translation_en = $this->getTranslation('en');
        $translation_th = $this->getTranslation('th');
        //2.标题
        $title_key = self::$by_message_reject_title;
        $title_zh = $translation_zh->_($title_key);
        $title_en = $translation_en->_($title_key);
        if (get_country_code() == GlobalEnums::TH_COUNTRY_CODE) {
            $title_th = $translation_th->_($title_key);
            $message_info['message_title'] = $title_th . ' ' . $title_en . ' ' . $title_zh;
        } else {
            $message_info['message_title'] = $title_en . ' ' . $title_zh;
        }
        //3.分类
        $message_info['category'] = MaterialEnums::MATERIAL_TRANSFER_MESSAGE_CATEGORY_REJECT;
        //4.内容
        //转移人
        $transfer_user = AssetTransferListService::getInstance()->getViewName($user['id'], false, $user['name']);
        foreach ($reject_staff_asset as $staff_id => $asset_ids) {
            $message_info['staff_info_id'] = $staff_id;
            $content = [
                'staff_name' => $transfer_user,
                'reject_remark' => $remark,
                'asset_ids' => array_values(array_unique($asset_ids))
            ];
            $message_info['message_content'] = json_encode($content);
            $this->sendMessage($message_info);
        }
        return true;
    }

    /**
     * 自动接收脚本-发送by站内信
     * @param array $reception_staff_asset 接收详情 ['用户id'=>[资产id集合]]
     * @return bool
     * @date 2022/10/28
     */
    public function sendAutoReceptionMessage($reception_staff_asset)
    {
        $message_info = [];
        //中英泰翻译
        $translation_zh = $this->getTranslation('zh');
        $translation_en = $this->getTranslation('en');
        $translation_th = $this->getTranslation('th');
        //2.标题
        $title_key = self::$by_message_receive_title;
        $title_zh = $translation_zh->_($title_key);
        $title_en = $translation_en->_($title_key);
        if (get_country_code() == GlobalEnums::TH_COUNTRY_CODE) {
            $title_th = $translation_th->_($title_key);
            $message_info['message_title'] = $title_th . ' ' . $title_en . ' ' . $title_zh;
        } else {
            $message_info['message_title'] = $title_en . ' ' . $title_zh;
        }
        //3.分类
        $message_info['category'] = MaterialEnums::MATERIAL_TRANSFER_MESSAGE_CATEGORY_AUTO_RECEPTION;
        //4.内容
        //转移人
        foreach ($reception_staff_asset as $staff_id => $asset_ids) {
            $message_info['staff_info_id'] = $staff_id;
            $content = [
                'asset_ids' => array_values(array_unique($asset_ids))
            ];
            $message_info['message_content'] = json_encode($content);
            $this->sendMessage($message_info);
        }
        return true;
    }

    /**
     * 资产接收提醒
     * @param array $message_info 消息参数组
     */
    public function sendPendingReceiptReminder($message_info)
    {
        $message_info['message_title'] = (get_country_code() == GlobalEnums::TH_COUNTRY_CODE) ? 'การแจ้งเตือนการรับสินทรัพย์ 资产接收提醒' : 'Asset Receive Reminder 资产接收提醒';
        $this->sendMessage($message_info);
    }
}
