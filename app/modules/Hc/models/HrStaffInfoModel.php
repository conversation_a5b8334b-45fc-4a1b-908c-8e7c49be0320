<?php

namespace App\Modules\Hc\Models;

use App\Models\Base;

class HrStaffInfoModel extends Base
{
    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_rbackyard');
        // setSource()方法指定数据库表
        $this->setSource('hr_staff_info');
    }

    /**
     * @param $staffInfoId
     * @param string $columns
     * @return array
     */
    public static function getUserInfo($staffInfoId, $columns = '*')
    {
        if (empty($staffInfoId)) {
            return [];
        }

        $userInfo = self::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id:',
            'bind'       => ['staff_info_id' => $staffInfoId],
            'columns'    => $columns
        ]);
        return  $userInfo ? $userInfo->toArray() : [];
    }
}