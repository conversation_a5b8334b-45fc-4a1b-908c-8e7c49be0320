<?php


namespace App\Modules\Hc\Models;


use App\Models\Base;

class HcApprovalDetailModel extends Base
{
    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_backyard');
        // setSource()方法指定数据库表
        $this->setSource('hc_approval_detail');
    }

    /**
     * @param array $data
     * @return bool
     */
    public function batch_insert_execute(array $data){
        if (empty($data)) {
            return false;
        }
        $keys = array_keys(reset($data));
        $keys = array_map(function ($key) {
            return "`{$key}`";
        }, $keys);
        $keys = implode(',', $keys);
        $sql = "INSERT INTO " . $this->getSource() . " ({$keys}) VALUES ";
        foreach ($data as $v) {
            $v = array_map(function ($value) {
                if ($value === null) {
                    return 'NULL';
                } else {
                    $value = addslashes($value); //处理特殊符号，如单引号
                    return "'{$value}'";
                }
            }, $v);
            $values = implode(',', array_values($v));
            $sql .= " ({$values}), ";
        }
        $sql = rtrim(trim($sql), ',');
        //DI中注册的数据库服务名称为"db"
        $result = $this->getDI()->get('db_backyard')->execute($sql);
        if (!$result) {
            return false;
        }
        return $result;
    }

}