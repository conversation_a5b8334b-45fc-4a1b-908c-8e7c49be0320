<?php

namespace App\Modules\Hc\Services;


use App\Library\ApiClient;
use App\Library\Enums;
use App\Library\Enums\ByWorkflowEnums;
use App\Library\Enums\GlobalEnums;
use app\library\Enums\HrEntryEnums;
use app\library\Enums\HrHcEnums;
use app\library\Enums\JobTransferEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\OssHelper;
use App\Library\Validation\ValidationException;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\HrStaffingModel;
use App\Models\backyard\JobTransferModel;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Hc\Models\HcBudgetPerMonthModel;
use App\Modules\Hc\Models\HcBudgetUploadDetail;
use App\Modules\Hc\Models\HrEntryModel;
use App\Modules\Hc\Models\HrInterviewModel;
use app\modules\Hc\models\HrJobDepartmentRelationModel;
use App\Modules\Hc\Models\SysDepartmentModel;
use App\Modules\Organization\Models\HrJobLogModel;
use App\Modules\Organization\Models\HrJobTitleModel;
use App\Modules\Organization\Services\JobLogService;
use App\Modules\Transfer\Models\HrHcModel;
use App\Modules\Transfer\Models\SettingEnvModel;
use App\Modules\User\Models\StaffInfoModel;
use App\Modules\Organization\Models\HrJobDepartmentRelationModel as OrgHrJobDepartmentRelationModel;
use App\Util\RedisKey;

class BudgetService extends BaseService
{
    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return BudgetService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    public static $max_budget_count = 100000;//最大预算申请人数
    public static $min_bugget_count = 1;//最小预算申请人数

    public static $batch_upload_detail_params = [
        'upload_key',
        'store_id',
        'job_title_id',
        'page_size',
        'page_num'
    ];

    public static $validate_batch_upload_detail_params = [
        'upload_key' => 'Required|StrLenGeLe:1,100',
        'store_id' => 'StrLenGeLe:1,50',
        'job_title_id' => 'IntGt:0',
        'page_size' => 'IntGt:0',
        'page_num' => 'IntGt:0'
    ];

    public static $search_params = [
        'month',
        'status',
        'dept_id',
        'tab',
        'page_size',
        'page_num',
    ];

    public static $validate_search_param = [
        'month' => 'StrLenGeLe:1,50',
        'status' => 'IntIn:1,2,3,4',
        'dept_id' => 'IntGt:0',
        'tab' => 'IntIn:1,2,3',
        'page_size' => 'IntGt:0',
        'page_num' => 'IntGt:0',
    ];

    public static $search_hc_dep_params = [
        'job_title_id',
        'department_id',
        'page_size',
        'page_num',
    ];

    public static $validate_search_hc_dep_param = [
        'job_title_id' => 'IntGt:0',
        'department_id' => 'IntGt:0',
        'page_size' => 'IntGt:0',
        'page_num' => 'IntGt:0',
    ];

    public static $search_hc_store_params = [
        'job_title_id',
        'department_id',
        'region_id',
        'piece_id',
        'store_id',
        'page_size',
        'page_num',
    ];

    public static $validate_search_hc_store_param = [
        'job_title_id' => 'IntGt:0',
        'department_id' => 'IntGt:0',
        'region_id' => 'IntGe:0',
        'piece_id' => 'IntGe:0',
        'store_id' => 'StrLenGeLe:1,50',
        'page_size' => 'IntGt:0',
        'page_num' => 'IntGt:0',
    ];

    public static $search_upload_params = [
        'budget_month',
        'department_id',
    ];

    public static $max_excel_count = 1000;
    public static $min_excel_count = 1;

    /*
     * 同步申请数据
     */
    public function budgetCreate($data)
    {
        try {
            $this->logger->info('Budget_create----参数：' . json_encode($data));
            $ac = new ApiClient('by', '', 'addHcBudget', static::$language);
            $ac->setParams([$data]);

            $res = $ac->execute();
            $this->logger->info('Budget_create----结果' . json_encode($res));
            return $res;
        }catch (\Exception $e){
            throw $e;
        }

    }

    /*
     * 预算列表
     */
    public function budgetList($params, $staff_id)
    {
        $page_num = empty($params['page_num']) ? 1 : $params['page_num'];
        $page_size = empty($params['page_size']) ? 20 : $params['page_size'];
        $page_size = ($page_size > 1000) ? 1000 : $page_size;

        $ac = new ApiClient('by', '', 'getHcBudgetList', static::$language);
        $ac->setParams([
            [
                'budget_month' => $params['month'] ?? '',
                'status' => $params['status'] ?? 0,
                'dept_id' => $params['dept_id'] ?? '',
                'type' => $params['type'] ?? 1,
                'tab' => $params['tab'] ?? 0,
                'is_all' => $params['is_all'] ?? 0,
                'page_size' => $page_size,
                'page_num' => $page_num,
                'staff_id' => $staff_id
            ]
        ]);
        $res = $ac->execute();

        $items = empty($res['result']['data']['items']) ? [] : $res['result']['data']['items'];

        if(!empty($params['type']) && $params['type'] == 2){
            //待审批的时候调用删除
            $this->delUnReadNumsKeyByStaffIds([$staff_id]);
        }


        return [
            'items' => $items,
            'current_page' => $page_num,
            'per_page' => $page_size,
            'total_count' => intval($res['result']['data']['count'] ?? 0),
        ];
    }

    /*
     * 预算申请详情
     */
    public function budgetDetail($budget_id, $staff_info_id)
    {
        try {
            $ac = new ApiClient('by', '', 'getHcBudgetDetail', static::$language);
            $ac->setParams([
                [
                    'hcb_id' => $budget_id,
                    'staff_id' => $staff_info_id
                ]
            ]);
            $this->getDI()->get('logger')->info('budgetDetail:staff_info_id----' . $staff_info_id . '-----budget_id:' . $budget_id);
            $res = $ac->execute();
            $this->getDI()->get('logger')->info('budgetDetail:res----' . json_encode($res));
            $annex = empty($res['result']['data']['detail']['annex']) ? '' : json_decode($res['result']['data']['detail']['annex']);
            $res['result']['data']['detail']['annex'] = $annex;

            if ($res['result']['data']['detail']['is_batch'] == 1) {
                $item_file = empty($res['result']['data']['detail']['item_file']) ? '' : json_decode($res['result']['data']['detail']['item_file']);
                $res['result']['data']['detail']['item_file'] = $item_file;
            }

            $detail = $res['result']['data'] ?? [];
            return $detail;
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->error('budgetDetail:error----'.$e->getMessage().'-----'.$e->getFile());
            return [];
        }
    }

    /*
     * 保存上传详情数据
     */
    public function addBudgetUploadDetail($data)
    {
        $detail_model = new HcBudgetUploadDetail();
        return $detail_model->batch_insert($data);
    }

    /*
     * 上传文件详情 分页
     */
    public function getBudgetUploadDetail($param, $staff_info_id)
    {
        $upload_key = $param['upload_key'];

        $job_title_id = $param['job_title_id'] ?? '';
        $store_id = $param['store_id'] ?? '';

        $page_size = empty($param['page_size']) ? 20 : $param['page_size'];
        $page_num = empty($param['page_num']) ? 1 : $param['page_num'];
        $offset = $page_size * ($page_num - 1);

        $builder = $this->modelsManager->createBuilder()
            ->columns('dept_id,dept_name,job_title_id,job_title_name,store_id,store_name,budget_count,on_job_count,to_be_hired_count,hiring_count,adopt_count,cur_month_budget,cur_month_planed')
            ->from(HcBudgetUploadDetail::class)
            ->where('budget_upload_key = :budget_upload_key:', ['budget_upload_key' => $upload_key])
            ->andWhere('staff_info_id = :staff_info_id:', ['staff_info_id' => $staff_info_id]);

        if (!empty($job_title_id)) {
            $builder->andWhere('job_title_id = :job_title_id:', ['job_title_id' => $job_title_id]);
        }

        if (!empty($store_id)) {
            $builder->andWhere('store_id = :store_id:', ['store_id' => $store_id]);
        }

        $count = $builder->getQuery()->execute()->count();

        $builder->limit($page_size, $offset);
        $list = $builder->getQuery()->execute()->toArray();
        return [
            'items' => $list,
            'pagination' => [
                'current_page' => $page_num,
                'per_page' => $page_size,
                'total_count' => $count,
            ]
        ];
    }

    /*
     * 上传文件详情分组
     */
    public function getBudgetUploadDetailGroup($staff_info_id, $upload_key)
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns(['dept_id', 'dept_name', 'job_title_id', 'job_title_name', 'sum(budget_count) as budget_count', 'sum(on_job_count) as on_job_count', 'sum(to_be_hired_count) as to_be_hired_count', 'sum(hiring_count) as hiring_count, sum(adopt_count) as adopt_count, sum(cur_month_budget) as cur_month_budget, sum(cur_month_planed) as cur_month_planed'])
            ->from(HcBudgetUploadDetail::class)
            ->where('budget_upload_key = :budget_upload_key:', ['budget_upload_key' => $upload_key])
            ->andWhere('staff_info_id = :staff_info_id:', ['staff_info_id' => $staff_info_id])
            ->groupBy(['dept_id', 'dept_name', 'job_title_id', 'job_title_name'])
            ->getQuery()->execute()->toArray();

        $group_staffing = $this->getBudgetUploadStaffingGroup($staff_info_id, $upload_key);
        foreach ($builder as $Key => $value) {
            $builder[$Key]['on_job_count'] = $group_staffing[$value['dept_id'] . '_' . $value['job_title_id']]['on_job_count'] ?? 0;
            $builder[$Key]['to_be_hired_count'] = $group_staffing[$value['dept_id'] . '_' . $value['job_title_id']]['to_be_hired_count'] ?? 0;
            $builder[$Key]['hiring_count'] = $group_staffing[$value['dept_id'] . '_' . $value['job_title_id']]['hiring_count'] ?? 0;
            $builder[$Key]['adopt_count'] = $group_staffing[$value['dept_id'] . '_' . $value['job_title_id']]['adopt_count'] ?? 0;
            $builder[$Key]['cur_month_budget'] = $group_staffing[$value['dept_id'] . '_' . $value['job_title_id']]['cur_month_budget'] ?? 0;
            $builder[$Key]['cur_month_planed'] = $group_staffing[$value['dept_id'] . '_' . $value['job_title_id']]['cur_month_planed'] ?? 0;
        }

        return $builder;
    }

    /*
     * 人员配备分组详情
     */
    public function getBudgetUploadStaffingGroup($staff_info_id, $upload_key)
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns(['dept_id', 'job_title_id', 'store_id', 'max(on_job_count) as on_job_count', 'max(to_be_hired_count) as to_be_hired_count', 'max(hiring_count) as hiring_count, max(adopt_count) as adopt_count, max(cur_month_budget) as cur_month_budget, max(cur_month_planed) as cur_month_planed'])
            ->from(HcBudgetUploadDetail::class)
            ->where('budget_upload_key = :budget_upload_key:', ['budget_upload_key' => $upload_key])
            ->andWhere('staff_info_id = :staff_info_id:', ['staff_info_id' => $staff_info_id])
            ->groupBy(['dept_id', 'job_title_id', 'store_id'])
            ->getQuery()->execute()->toArray();

        $list = [];
        foreach ($builder as $key => $value) {
            $list[$value['dept_id'] . '_' . $value['job_title_id']]['on_job_count'] += $value['on_job_count'] ?? 0;
            $list[$value['dept_id'] . '_' . $value['job_title_id']]['to_be_hired_count'] += $value['to_be_hired_count'] ?? 0;
            $list[$value['dept_id'] . '_' . $value['job_title_id']]['hiring_count'] += $value['hiring_count'] ?? 0;
            $list[$value['dept_id'] . '_' . $value['job_title_id']]['adopt_count'] += $value['adopt_count'] ?? 0;
            $list[$value['dept_id'] . '_' . $value['job_title_id']]['cur_month_budget'] += $value['cur_month_budget'] ?? 0;
            $list[$value['dept_id'] . '_' . $value['job_title_id']]['cur_month_planed'] += $value['cur_month_planed'] ?? 0;
        }
        return $list;
    }

    /*
     * 获取上传文件数据详情
     */
    public function getAllBudgetUploadDetail($staff_info_id, $upload_key)
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('dept_id,dept_name,job_title_id,job_title_name,store_id,store_name,budget_count,on_job_count,to_be_hired_count,hiring_count,adopt_count,cur_month_budget,cur_month_planed')
            ->from(HcBudgetUploadDetail::class)
            ->where('budget_upload_key = :budget_upload_key:', ['budget_upload_key' => $upload_key])
            ->andWhere('staff_info_id = :staff_info_id:', ['staff_info_id' => $staff_info_id]);
        return $builder->getQuery()->execute()->toArray();
    }

    /*
     * 审批 1通过 2驳回 3撤销
     */
    public function auditHcBudget($params)
    {
        $this->getDI()->get('logger')->info('hc_budget_audit, rpc params: ' . json_encode($params, JSON_UNESCAPED_UNICODE));

        //导出锁
        if($this->checkLock(RedisKey::HC_BUDGET_APPROVAL_LOCK . $params['hcb_id'])) {
            return ['code' => ErrCode::$SYSTEM_ERROR, 'message' => 'approving now Please wait!'];
        } else {
            $this->setLock(RedisKey::HC_BUDGET_APPROVAL_LOCK. $params['hcb_id'],1,10);
        }

        $ac = new ApiClient('by', '', 'auditHcBudget', static::$language);
        $ac->setParams([$params]);

        if ($params['action'] == 1 || $params['action'] == 2) {
            $this->delUnReadNumsKeyByStaffIds([$params['staff_id']]);
        }

        $res = $ac->execute();

        $this->getDI()->get('logger')->info('hc_budget_audit, rpc result: ' . json_encode($res, JSON_UNESCAPED_UNICODE));

        return $res['result'];
    }

    /*
     * 上传文件数据处理
     */
    public function batchUpload($excel_data, $staff_info_id, $my_dept_id, $budget_month)
    {
        if (count($excel_data) < self::$min_excel_count) {
            return ['code' => 2, 'msg' => 'Excel error', 'data' => []];
        }

        if (count($excel_data) > self::$max_excel_count) {
            return ['code' => 2, 'msg' => static::$t->_('hc_error_5'), 'data' => []];
        }

        $upload_key = 'batch_upload_' . time();

        $all_job_title = SysService::getInstance()->getAllJobTitleList();
        $my_dept_list = SysService::getInstance()->getMyDeptListV2($staff_info_id, $my_dept_id, false);
        $store_list = SysService::getInstance()->getStoreListBySearchName();

        $all_job_title_name = array_column($all_job_title, 'job_title_name', 'job_title_id');
        $my_dept_list_name = array_column($my_dept_list, 'name', 'id');
        $store_list_name = array_column($store_list, 'name', 'store_id');
        //unset($store_list_name[-1]);批量上传支持总部网点

        $dept_ids = array_keys(array_column($my_dept_list, null, 'id'));
        $dept_job_title = SysService::getInstance()->getDeptJobTitleConfig($dept_ids);

        //部门名字列表
        $departmentNameList     = array_column($excel_data, 0);
        $departmentNameListInfo = SysDepartmentModel::find([
            "name in ({nameList:array})",
            "bind"    => [
                "nameList" => $departmentNameList,
            ],
            "columns" => "id",
        ])->toArray();
        $departmentNameListIds  = array_column($departmentNameListInfo, 'id');

        //职位名字列表
        $jobTitleNameList     = array_column($excel_data, 1);
        $jobTitleNameListInfo = HrJobTitleModel::find([
            "job_name in ({nameList:array})",
            "bind"    => [
                "nameList" => $jobTitleNameList,
            ],
            "columns" => "id",
        ])->toArray();
        $jobTitleListIds      = array_column($jobTitleNameListInfo, 'id');

        if (!empty($departmentNameListIds) && !empty($jobTitleListIds)) {
            //获取指定部门、职位的计划HC人数
            $hrJobDepartmentRelateInfo = OrgHrJobDepartmentRelationModel::find([
                'conditions' => 'department_id IN ({department_ids:array}) and job_id IN({job_ids:array})',
                'bind'       => [
                    'department_ids' => $departmentNameListIds,
                    'job_ids'        => $jobTitleListIds,
                ],
                'columns'    => "concat(department_id,'-',job_id) unique_key,department_id,job_id,plan_hc_nums,is_plan",
            ])->toArray();
            $hrJobDepartmentRelateInfo = array_column($hrJobDepartmentRelateInfo, null, 'unique_key');
        }

        //没有指定部门职位关联关系
        if (empty($hrJobDepartmentRelateInfo)) {
            return ['code' => ErrCode::$VALIDATE_ERROR, 'msg' => static::$t->_('error_message_not_exist_relate'), 'data' => []];
        }

        $result_success = 0;
        $result_error = 0;
        $success_data = [];
        $insert_data = [];
        $staffing_search = [];
        foreach ($excel_data as $key => $value) {

            $msg = [];
            $dept_id = array_search($value[0], $my_dept_list_name);
            if ($dept_id === false) {
                $msg[] = static::$t->_('hc_error_1');
            }

            $job_title_id = array_search($value[1], $all_job_title_name);
            if ($job_title_id === false) {
                $msg[] = static::$t->_('hc_error_2');
            } else {
                if (!isset($dept_job_title[$dept_id . '_' . $job_title_id])) {
                    $msg[] = static::$t->_('hc_error_2');
                }
            }
            $store_name = $value[2];
            if(strtolower($store_name) == 'head office') {
                $store_name = strtolower($store_name);
            }
            $store_id = array_search($store_name, $store_list_name);
            if ($store_id === false) {
                $msg[] = static::$t->_('hc_error_4');
            }

            if (!is_numeric($value[3]) || ($value[3] > self::$max_budget_count || $value[3] < self::$min_bugget_count)) {
                $msg[] = static::$t->_('hc_error_3');
            }

            $uniqueKey = sprintf("%d-%d", $dept_id, $job_title_id);
            if (!in_array($uniqueKey, array_keys($hrJobDepartmentRelateInfo))) {
                $msg[] = static::$t->_('error_message_not_exist_relate');
            } else {

                //如果制定了HC计划人数，并且HC预算必须小于等于HC计划人数
                if (isset($hrJobDepartmentRelateInfo[$uniqueKey]) &&
                    $hrJobDepartmentRelateInfo[$uniqueKey]['is_plan'] == OrgHrJobDepartmentRelationModel::PLAN_STATE_FORMULATE &&
                    $hrJobDepartmentRelateInfo[$uniqueKey]['plan_hc_nums'] < $value[3]
                ) {
                    $msg[] = static::$t->_('hc_error_7');
                }
            }

            $param['budget_month'] = $budget_month;
            $param['dept_id'] = $dept_id;
            $param['job_title_id'] = $job_title_id;
            $param['store_id'] = $store_id;
            if($dept_id === false || $job_title_id === false || $store_id === false) {
                $msg[] = 'Dept or Job Title or Store Error';
            } else {
                $repeat_list = $this->getHcBudgetDetailRepeat($param);
                if(!empty($repeat_list['result']['data']) && $repeat_list['result']['data'] > 0) {
                    $msg[] = static::$t->_('hc_error_6',['dept_id' => $value[0],'job_title' => $value[1],'store' => $value[2]]);
                }
            }

            if (empty($msg)) {
                $result_success++;
                $success_data[] = $value;
                $insert_data[] = [
                    'staff_info_id' => $staff_info_id,
                    'dept_id' => $dept_id,
                    'dept_name' => $value[0],
                    'job_title_id' => $job_title_id,
                    'job_title_name' => $value[1],
                    'store_id' => $store_id,
                    'store_name' => $value[2],
                    'budget_count' => $value[3],
                    'budget_upload_key' => $upload_key
                ];
                $staffing_search[$dept_id.'_'.$job_title_id.'_'.$store_id] = ['dept_id' => $dept_id, 'job_title_id' => $job_title_id, 'store_id' => $store_id, 'month' => $budget_month];
            } else {
                $result_error++;
            }
            $excel_result[] = [
                $value[0],
                $value[1],
                $value[2],
                $value[3],
                (empty($msg) ? static::$t->_('hc_success') : static::$t->_('hc_failure')),
                (empty($msg) ? '' : implode(',', $msg))
            ];
        }
        $excel_header = [
            static::$t->_('hc_directly_dept'),
            static::$t->_('hc_job_title'),
            static::$t->_('hc_branch_name'),
            static::$t->_('hc_budget_count'),
            static::$t->_('hc_upload_result'),
            "  "
        ];
        $path = self::excelToFile($excel_header, $excel_result, $upload_key . '.xlsx');
        $file = OssHelper::uploadFile($path);

        $staffing_list = SysService::getInstance()->Staffing(array_values($staffing_search));

        foreach ($insert_data as $key => $value) {
            $on_job_count = $staffing_list[$value['dept_id'] . '_' . $value['job_title_id'] . '_' . $value['store_id']]['on_the_job'] ?? 0;
            $to_be_hired_count = $staffing_list[$value['dept_id'] . '_' . $value['job_title_id'] . '_' . $value['store_id']]['to_be_employed'] ?? 0;
            $hiring_count = $staffing_list[$value['dept_id'] . '_' . $value['job_title_id'] . '_' . $value['store_id']]['hiring'] ?? 0;
            $adopt_count = $staffing_list[$value['dept_id'] . '_' . $value['job_title_id'] . '_' . $value['store_id']]['adopt_count'] ?? 0;
            $cur_month_budget = $staffing_list[$value['dept_id'] . '_' . $value['job_title_id'] . '_' . $value['store_id']]['cur_month_budget'] ?? 0;
            $cur_month_planed = $staffing_list[$value['dept_id'] . '_' . $value['job_title_id'] . '_' . $value['store_id']]['cur_month_planed'] ?? 0;

            $insert_data[$key]['on_job_count'] = $on_job_count;
            $insert_data[$key]['to_be_hired_count'] = $to_be_hired_count;
            $insert_data[$key]['hiring_count'] = $hiring_count;
            $insert_data[$key]['adopt_count'] = $adopt_count;
            $insert_data[$key]['cur_month_budget'] = $cur_month_budget;
            $insert_data[$key]['cur_month_planed'] = $cur_month_planed;
        }
        $this->addBudgetUploadDetail($insert_data);

        $result_data = [
            'success_data_count' => $result_success, //校验成功条数
            'error_data_count' => $result_error, //校验失败条数
            'data_count' => count($excel_data), //总条数
            'result_file' => $file, //结果详情文件路径
            'cache_excel_success_key' => $upload_key //数据缓存key
        ];
        return [
            'code' => 1,
            'msg' => '',
            'data' => $result_data
        ];
    }

    /**
     * 创建
     * @noinspection PhpUnhandledExceptionInspection
     *
     */
    public function create($param, $user_id, $user_dept_id) {
        $budget_month = $param['budget_month']; //预算月份
        $budget_dept_id = $param['dept_id'];      //用人部门
        $annex = $param['annex'];        //附件
        $remarks = $param['remarks'];      //申请备注
        $items = $param['items'];        //详情数据

        $my_dept_id = $user_dept_id;//登录人所在部门
        $applicant_id = $user_id; //登陆人id
        $all_job_title = SysService::getInstance()->getAllJobTitleList();
        $my_dept_list = SysService::getInstance()->getMyDeptListV2($applicant_id, $my_dept_id, false);
        $store_list = SysService::getInstance()->getStoreListBySearchName();

        $all_job_title_name = array_column($all_job_title, 'job_title_name', 'job_title_id');
        $my_dept_list_name = array_column($my_dept_list, 'name', 'id');
        $store_list_name = array_column($store_list, 'name', 'store_id');

        $current_month = date('Y-m');
        $last_month = date("Y-m",strtotime("$current_month +1 month"));
        if(!in_array($budget_month,[$current_month,$last_month])) {
            throw new ValidationException("Only apply for the current month and the next month", ErrCode::$VALIDATE_ERROR);
        }

        if (count($items) < 1) {
            throw new ValidationException("Details cannot be empty", ErrCode::$VALIDATE_ERROR);
        }

        $dept_ids = array_keys(array_column($my_dept_list, null, 'id'));
        $dept_job_title = SysService::getInstance()->getDeptJobTitleConfig($dept_ids);

        foreach ($items as $key => $value) {
            $dept_id = $value['dept_id'];
            //$dept_name = $value['dept_name'];
            $job_title_id = $value['job_title_id'];
            //$job_title_name = $value['job_title_name'];
            $store_id = $value['store_id'] ?? '';
            //$store_name = $value['store_name'];
            //预算人数
            $budget_count = isset($value['budget_count']) && !empty($value['budget_count']) ? $value['budget_count'] : 0;

            //在职人数
            $on_job_count = isset($value['on_job_count']) && !empty($value['on_job_count']) ? $value['on_job_count'] : 0;

            //待入职人数
            $to_be_hired_count = isset($value['to_be_hired_count']) && !empty($value['to_be_hired_count']) ? $value['to_be_hired_count'] : 0;

            //招聘中人数
            $hiring_count = isset($value['hiring_count']) && !empty($value['hiring_count']) ? $value['hiring_count'] : 0;

            //已提交的hc人数（待审批）
            $adopt_count = isset($value['adopt_count']) && !empty($value['adopt_count']) ? $value['adopt_count'] : 0;

            //可提交HC数=       预算人数        - 在职人数        -待入职人数            -招聘中人数-已提交HC总人数
            $can_apply_count = $budget_count - $on_job_count - $to_be_hired_count - $hiring_count - $adopt_count;

            //这个在plugins/ExceptionPlugin.php会被捕获
            if($can_apply_count <= 0){
                throw new ValidationException(self::$t->_("hc_budget_apply_count_error"),ErrCode::$VALIDATE_ERROR);
            }

            if (!isset($my_dept_list_name[$dept_id])) {
                throw new BusinessException("Department Error", ErrCode::$BUSINESS_ERROR);
            }

            if (!isset($all_job_title_name[$job_title_id])) {
                throw new BusinessException("Job Error", ErrCode::$BUSINESS_ERROR);
            }

            if (!isset($dept_job_title[$dept_id . '_' . $job_title_id])) {
                throw new BusinessException("Job Title Config Error", ErrCode::$BUSINESS_ERROR);
            }

            if (!empty($store_id) && !isset($store_list_name[$store_id])) {
                throw new BusinessException("Store Error", ErrCode::$BUSINESS_ERROR);
            }

            $param['budget_month'] = $budget_month;
            $param['dept_id'] = $value['dept_id'];
            $param['job_title_id'] = $value['job_title_id'];
            $param['store_id'] = $store_id;
            $repeat_list = BudgetService::getInstance()->getHcBudgetDetailRepeat($param);
            if(!empty($repeat_list['result']['data']) && $repeat_list['result']['data'] > 0) {
                throw new ValidationException(static::$t->_('hc_error_6',['dept_id' => $my_dept_list_name[$dept_id],'job_title' => $all_job_title_name[$job_title_id],'store' => $store_list_name[$store_id]??'']), ErrCode::$VALIDATE_ERROR);
            }

            if (!is_numeric($budget_count) || ($budget_count > BudgetService::$max_budget_count || $budget_count < BudgetService::$min_bugget_count)) {
                throw new BusinessException("Budget Count Error", ErrCode::$BUSINESS_ERROR);
            }
        }

        //调用接口 保存数据
        $data = [
            'budget_month' => $budget_month, //预算月份
            'dept_id' => $budget_dept_id,   //部门id
            'applicant_id' => $applicant_id, //申请人id
            'remarks' => $remarks, //备注
            'annex' => empty($annex) ? '' : json_encode($annex),  //附件
            'summary' => '',    //批量上传 统计数据
            'is_batch' => 0,    //是否批量 0否 1是
            'item_file' => '',
            'items' => $items
        ];
        return BudgetService::getInstance()->budgetCreate($data);
    }

    /*
     * 批量创建
     */
    public function batchCreate($param, $staff_info_id, $my_dept_id)
    {
        $budget_month = $param['budget_month'];//预算月份
        $budget_dept_id = $param['dept_id'];//用人部门
        $annex = $param['annex'];//附件
        $remarks = $param['remarks'];//申请备注
        $upload_key = $param['upload_key'];//详情数据上传文件保存批次
        //调用接口 保存数据

        $items = $this->getAllBudgetUploadDetail($staff_info_id, $upload_key);
        $dept_id_arr = array_column($items, 'dept_id');

        $envModel = new SettingEnvModel();
        //如果在共享部门里 不允许批量创建
	    $department_position = SysService::getInstance()->getShareDepartmentPosition();
	    $share_department = $department_position['department_ids'] ?? [];
   
        
        //判断是否有交集
	    if(!empty(array_intersect($dept_id_arr, $share_department))){
            return [
                'code'         => ErrCode::$VALIDATE_ERROR,
                'message'  => static::$t->_('batch_create_hint'),
                'data'      => []
            ];
        }
        $file = '';
        if (count($items) > 0) {
            $excel_header = [
                static::$t->_('hc_directly_dept'),
                static::$t->_('hc_job_title'),
                static::$t->_('hc_branch_name'),
                static::$t->_('hc_budget_count'),
                static::$t->_('hc_on_job'),
                static::$t->_('hc_to_be_hired_count'),
                static::$t->_('hc_hiring_count'),
                static::$t->_('hc_adopt_count'),
                static::$t->_('hc_cur_month_budget'),
                static::$t->_('hc_cur_month_planed'),
            ];
            $excel_result = [];
            foreach ($items as $key => $value) {
                $excel_result[] = [$value['dept_name'], $value['job_title_name'], $value['store_name'], $value['budget_count'], $value['on_job_count'], $value['to_be_hired_count'], $value['hiring_count'], $value['adopt_count'], $value['cur_month_budget'], $value['cur_month_planed']];
            }
            $path = self::excelToFile($excel_header, $excel_result, $upload_key . '.xlsx');
            $file = OssHelper::uploadFile($path);
        }

        //分组详情数据
        $summary = $this->getBudgetUploadDetailGroup($staff_info_id, $upload_key);
        $data = [
            'budget_month' => $budget_month,    //预算月份
            'dept_id' => $budget_dept_id,       //部门id
            'applicant_id' => $staff_info_id,   //申请人id
            'remarks' => $remarks,              //备注
            'annex' => json_encode($annex),     //附件
            'summary' => json_encode($summary), //批量上传 统计数据
            'is_batch' => 1,                    //是否批量 0否 1是
            'item_file' => json_encode($file),  //预算明细文件
            'items' => $items                   //预算明细
        ];

        $result = $this->budgetCreate($data);

        $sql = "DELETE FROM `hc_budget_upload_detail` WHERE `budget_upload_key` = ?";
        $this->getDI()->get('db_oa')->execute($sql, [$upload_key]);
        return $result;
    }

    /*
     * 同部门 同职位 同网点是否有重复申请
     */
    public function getHcBudgetDetailRepeat($param) {

        $ac = new ApiClient('by', '', 'getHcBudgetDetailRepeat', static::$language);
        $ac->setParams([$param]);

        //$this->getDI()->get('logger')->warning('getHcBudgetDetailRepeat----参数'.json_encode($param).'----结果' . json_encode($res));
        return $ac->execute();
    }

    /**
     * HC预算汇总列表
     * @param $params
     * @param $staff_id
     * @return array
     */
    public function hcListByDep($params)
    {
        $page_num  = empty($params['page_num']) ? 1 : $params['page_num'];
        $page_size = empty($params['page_size']) ? 20 : $params['page_size'];
        $page_size = ($page_size > 1000) ? 1000 : $page_size;
        $is_export = empty($params['export']) ? 0 : $params['export'];
        $staff_id  = empty($params['staff_id']) ? 0 : $params['staff_id'];
        $month = $params['budget_month'] ?? '';

        //部门权限
        $deptList = $this->getDeptViewPermission($staff_id);
        if (empty($deptList)) {
            return [];
        }
        //存在部门筛选
        if (isset($params['department_id']) && in_array($params['department_id'], $deptList)) {
            $department_ids = [$params['department_id']];
        } else {
            $department_ids = $deptList;
        }
        $ac = new ApiClient('by', '', 'getHcBudgetListByDep', static::$language);
        $ac->setParams([
            [
                'budget_month'  => $month,
                'department_id' => $department_ids ?? [],
                'job_title'     => $params['job_title_id'] ?? '',
                'page_size'     => $page_size,
                'page_num'      => $page_num,
                'is_export'     => $is_export,
            ]
        ]);
        $res = $ac->execute();
        $items = empty($res['result']['data']['items']) ? [] : $res['result']['data']['items'];

        //获取职位
        $all_job_title = SysService::getInstance()->getAllJobTitleList();
        $all_dept_list = SysService::getInstance()->getAllDepartmentList();
        $all_job_title_name = array_column($all_job_title, 'job_title_name', 'job_title_id');
        $all_dept_list_name = array_column($all_dept_list, 'name', 'id');

        foreach ($items as $key => $value) {
            $items[$key]['job_title_name'] = $all_job_title_name[$value['job_title_id']] ?? '';
            $items[$key]['department_name'] = $all_dept_list_name[$value['department_id']] ?? '';
        }

        return [
            'items'         => $items,
            'current_page'  => $page_num,
            'per_page'      => $page_size,
            'total_count'   => intval($res['result']['data']['count'] ?? 0),
        ];
    }

    /**
     * HC预算汇总列表
     * @param $params
     * @param $staff_id
     * @return array
     */
    public function hcListByStore($params)
    {
        $page_num  = empty($params['page_num']) ? 1 : $params['page_num'];
        $page_size = empty($params['page_size']) ? 20 : $params['page_size'];
        $page_size = ($page_size > 1000) ? 1000 : $page_size;
        $is_export = empty($params['export']) ? 0 : $params['export'];
        $manage_region = $params['area'][0] ?? '';
        $manage_piece  = $params['area'][1] ?? '';
        $month = $params['budget_month'] ?? '';

        $ac = new ApiClient('by', '', 'getHcBudgetListByStore', static::$language);
        $ac->setParams([
            [
                'budget_month'  => $month,
                'manage_region' => $manage_region,
                'manage_piece'  => $manage_piece,
                'store_id'      => $params['store_id'] ?? '',
                'department_id' => $params['department_id'] ?? '',
                'job_title'     => $params['job_title_id'] ?? '',
                'page_size'     => $page_size,
                'page_num'      => $page_num,
                'is_export'     => $is_export,
            ]
        ]);
        $res = $ac->execute();

        $items = empty($res['result']['data']['items']) ? [] : $res['result']['data']['items'];

        //获取职位
        $all_job_title = SysService::getInstance()->getAllJobTitleList();
        $all_dept_list = SysService::getInstance()->getAllDepartmentList();
        $store_list = SysService::getInstance()->getStoreListBySearchName();
        $region_list = SysService::getInstance()->getAllRegions();
        $piece_list = SysService::getInstance()->getAllPieces();

        $all_job_title_name = array_column($all_job_title, 'job_title_name', 'job_title_id');
        $all_dept_list_name = array_column($all_dept_list, 'name', 'id');
        $store_list_name = array_column($store_list, 'name', 'store_id');
        $region_list = array_column($region_list, 'name', 'id');
        $piece_list = array_column($piece_list, 'name', 'id');

        foreach ($items as $key => $value) {
            if (isset($value['manage_piece']) && $value['manage_piece']) {
                $regionName = ($region_list[$value['manage_region']] ?? '') . '-' . ($piece_list[$value['manage_piece']] ?? '');
            } else {
                $regionName = $region_list[$value['manage_region']] ?? '-';
            }
            $items[$key]['region_name'] = $regionName ?? '';
            $items[$key]['manage_region'] = $value['manage_region'] ?? 0;
            $items[$key]['manage_piece'] = $value['manage_piece'] ?? 0;
            $items[$key]['job_title_name'] = $all_job_title_name[$value['job_title_id']] ?? '';
            $items[$key]['department_name'] = $all_dept_list_name[$value['department_id']] ?? '';
            $items[$key]['store_name'] = $store_list_name[$value['store_id']] ?? 'Header Office';
        }

        return [
            'items'         => $items,
            'current_page'  => $page_num,
            'per_page'      => $page_size,
            'total_count'   => intval($res['result']['data']['count'] ?? 0),
        ];
    }


    /**
     * 导出列表
     *
     * @param $condition
     * @param $staffId
     * @return array
     */
    public function exportHcListByDep($condition, $staffId)
    {
        //导出锁
        if($this->checkLock(RedisKey::HC_BUDGET_LIST_EXPORT_LOCK . $staffId)) {
            return ['code' => ErrCode::$SYSTEM_ERROR, 'message' => 'exporting now Please wait!'];
        } else {
            $this->setLock(RedisKey::HC_BUDGET_LIST_EXPORT_LOCK,1,10);
        }

        $condition['staff_id'] = $staffId;
        $condition['export'] = 1;
        $data = $this->hcListByDep($condition);
        if(empty($data)){
            return $data;
        }

        $data = $data['items'];

        $new_data = [];

        foreach ($data as $key => $val) {
            $new_data[$key][] = $val['budget_month'];
            $new_data[$key][] = $val['job_title_name'];
            $new_data[$key][] = $val['department_name'];
            $new_data[$key][] = $val['budget'];
            $new_data[$key][] = $val['employee_in_service'];
            $new_data[$key][] = $val['hc_pending'];
            $new_data[$key][] = $val['hc_demand'];
            $new_data[$key][] = $val['hc_request'];
            $new_data[$key][] = $val['cur_month_budget'];
            $new_data[$key][] = $val['cur_month_planed'];
        }
        $file_name = "Export_".date("YmdHis");
        $header = [
            static::$t->_('hc_budget_month'),               //预算所属月份
            static::$t->_('department.manager_job_title'),  //职位
            static::$t->_('hc_directly_dept'),              //直属部门
            static::$t->_('new_budget'),                    //新增招聘人数
            static::$t->_('hc_on_job'),                     //在职人数
            static::$t->_('hc_to_be_hired_count'),          //待入职人数
            static::$t->_('hc_hiring_count'),               //招聘中人数
            static::$t->_('hc_submit_hc_count'),            //当前可提交HC人数
            static::$t->_('hc_budget_count'),               //预算人数
            static::$t->_('hc_planned'),                    //当月计划
        ];
        $data = $this->exportExcel($header, $new_data, $file_name);
        $data['data']['url'] = $data['data'];
        $this->unLock(RedisKey::HC_BUDGET_LIST_EXPORT_LOCK . $staffId);
        return $data;
    }


    /**
     * 导出列表
     * @param array $condition
     * @param $staffId
     * @return array
     */
    public function exportHcListByStore(array $condition, $staffId)
    {
        //导出锁
        if($this->checkLock(RedisKey::HC_BUDGET_LIST_EXPORT_2_LOCK . $staffId)){
            return ['code' => ErrCode::$SYSTEM_ERROR,'message'=>'exporting now Please wait!'];
        }else{
            $this->setLock(RedisKey::HC_BUDGET_LIST_EXPORT_2_LOCK,1,10);
        }

        $condition['staff_id'] = $staffId;
        $condition['export'] = 1;
        $data = $this->hcListByStore($condition);
        if(empty($data)){
            return $data;
        }

        $data = $data['items'];

        $new_data = [];

        foreach ($data as $key => $val) {
            $new_data[$key][] = $val['budget_month'];
            $new_data[$key][] = $val['region_name'] ;
            $new_data[$key][] = $val['store_name'];
            $new_data[$key][] = $val['job_title_name'];
            $new_data[$key][] = $val['department_name'];
            $new_data[$key][] = $val['budget'];
            $new_data[$key][] = $val['cur_month_budget'];
            $new_data[$key][] = $val['cur_month_planed'];
            $new_data[$key][] = $val['employee_in_service'];
            $new_data[$key][] = $val['hc_pending'];
            $new_data[$key][] = $val['hc_demand'];
            $new_data[$key][] = $val['hc_request'];
            $new_data[$key][] = $val['hc_p1'];
            $new_data[$key][] = $val['hc_p2'];
            $new_data[$key][] = $val['hc_p3'];
            $new_data[$key][] = $val['hc_p4'];
        }
        $file_name = "Export_".date("YmdHis");
        $header = [
            static::$t->_('hc_budget_month'),               //预算所属月份
            static::$t->_('global.area'),                   //区域
            static::$t->_('global.branch'),                 //网点
            static::$t->_('department.manager_job_title'),  //职位
            static::$t->_('hc_directly_dept'),              //直属部门
            static::$t->_('new_budget'),                    //新增招聘人数
            static::$t->_('hc_budget_count'),               //预算人数
            static::$t->_('hc_planned'),                    //当月计划
            static::$t->_('hc_on_job'),                     //在职人数
            static::$t->_('hc_to_be_hired_count'),          //待入职人数
            static::$t->_('hc_hiring_count'),               //招聘中人数
            static::$t->_('hc_submit_hc_count'),            //当前可提交HC人数
            'HC P1',
            'HC P2',
            'HC P3',
            'HC P4',
        ];
        $data = $this->exportExcel($header, $new_data, $file_name);
        $data['data']['url'] = $data['data'];
        $this->unLock(RedisKey::HC_BUDGET_LIST_EXPORT_2_LOCK . $staffId);
        return $data;
    }

    /**
     * 上传文件数据处理
     * @param $excel_data
     * @param $staff_info_id
     * @param $budget_month
     * @param $department_id
     * @return array
     * @throws \App\Library\Exception\BusinessException
     */
    public function uploadPerMonthBudget($excel_data, $staff_info_id, $budget_month, $department_id): array
    {
        //输入行数限制
        if (count($excel_data) < self::$min_excel_count) {
            return ['code' => 2, 'msg' => 'Excel error', 'data' => []];
        }

        if (count($excel_data) > self::$max_excel_count) {
            return ['code' => 2, 'msg' => static::$t->_('hc_error_5'), 'data' => []];
        }
        $permission = EnvModel::getEnvByCode('upload_hc_budget_staff_ids');
        $permissionIDS = explode(',', $permission);
        if (!in_array($staff_info_id, $permissionIDS)) {
            return ['code' => 2, 'msg' => 'no permission', 'data' => []];
        }

        $upload_key = 'upload_hc_budget' . time();

        $all_job_title = SysService::getInstance()->getAllJobTitleList();
        $my_dept_list = SysService::getInstance()->getMyDeptListV2($staff_info_id, $department_id, false);
        $store_list = SysService::getInstance()->getStoreListBySearchName();

        $all_job_title_name = array_column($all_job_title, 'job_title_name', 'job_title_id');
        $my_dept_list_name = array_column($my_dept_list, 'name', 'id');
        $store_list_name = array_column($store_list, 'name', 'store_id');
        unset($store_list_name[-1]);

        $dept_ids = array_keys(array_column($my_dept_list, null, 'id'));
        $dept_job_title = SysService::getInstance()->getDeptJobTitleConfig($dept_ids);

        $result_success = 0;
        $result_error = 0;
        $insert_data = [];
        foreach ($excel_data as $key => $value) {
            $msg = [];
            $dept_id = array_search($value[0], $my_dept_list_name);
            if ($dept_id === false || $dept_id != $department_id) {
                $msg[] = static::$t->_('hc_error_1');
            }

            $job_title_id = array_search($value[1], $all_job_title_name);
            if ($job_title_id === false) {
                $msg[] = static::$t->_('hc_error_2');
            } else {
                if (!isset($dept_job_title[$dept_id . '_' . $job_title_id])) {
                    $msg[] = static::$t->_('hc_error_2');
                }
            }

            $store_id = array_search($value[2], $store_list_name);
            if ($store_id === false) {
                $msg[] = static::$t->_('hc_error_4');
            }
            if (!is_numeric($value[3]) || ($value[3] > self::$max_budget_count || $value[3] < self::$min_bugget_count)) {
                $msg[] = static::$t->_('hc_error_3');
            }

            if (empty($msg)) {
                $result_success++;
                $insert_data[] = [
                    'operator' => $staff_info_id,
                    'budget_month' => $budget_month,
                    'department_id' => $dept_id,
                    'job_title_id' => $job_title_id,
                    'store_id' => $store_id,
                    'budget_count' => $value[3],
                ];
                $staffing_search[] = ['dept_id' => $dept_id, 'job_title_id' => $job_title_id, 'store_id' => $store_id];
            } else {
                $result_error++;
            }
            $excel_result[] = [
                $value[0],
                $value[1],
                $value[2],
                $value[3],
                (empty($msg) ? static::$t->_('hc_success') : static::$t->_('hc_failure')),
                (empty($msg) ? '' : implode(',', $msg))
            ];
        }

        //导出XLSX
        $excel_header = [
            static::$t->_('hc_directly_dept'),
            static::$t->_('hc_job_title'),
            static::$t->_('hc_branch_name'),
            static::$t->_('hc_budget_count'),
            static::$t->_('hc_upload_result'),
            "  "
        ];
        $path = self::excelToFile($excel_header, $excel_result, $upload_key . '.xlsx');
        $file = OssHelper::uploadFile($path);

        //没有错误，则保存
        if ($result_error === 0) {

            //删除指定部门月份的预算人数
            $budget_list = HcBudgetPerMonthModel::find([
                'conditions' => 'budget_month = :budget_month: and department_id = :department_id:',
                'bind'       => [
                    'budget_month'  => $budget_month,
                    'department_id' => $department_id
                ],
            ]);
            $budget_list->delete();

            //批量插入
            $detail_model = new HcBudgetPerMonthModel();
            $detail_model->batch_insert($insert_data);
        }

        //返回结果
        $result_data = [
            'success_data_count' => $result_success, //校验成功条数
            'error_data_count' => $result_error, //校验失败条数
            'data_count' => count($excel_data), //总条数
            'result_file' => $file, //结果详情文件路径
            'cache_excel_success_key' => $upload_key //数据缓存key
        ];
        return [
            'code' => 1,
            'msg' => '',
            'data' => $result_data
        ];
    }

    /**
     * 上传文件详情 分页
     * @param $param
     * @param $staff_info_id
     * @return array
     */
    public function getUploadPerMonthBudgetDetail($param, $staff_info_id): array
    {
        $upload_key = $param['upload_key'];

        $job_title_id = $param['job_title_id'] ?? '';
        $store_id = $param['store_id'] ?? '';

        $page_size = empty($param['pageSize']) ? 20 : $param['pageSize'];
        $page_num = empty($param['pageNum']) ? 1 : $param['pageNum'];
        $offset = $page_size * ($page_num - 1);

        $builder = $this->modelsManager->createBuilder()
            ->columns('dept_id,dept_name,job_title_id,job_title_name,store_id,store_name,budget_count,on_job_count,to_be_hired_count,hiring_count')
            ->from(HcBudgetUploadDetail::class)
            ->where('budget_upload_key = :budget_upload_key:', ['budget_upload_key' => $upload_key])
            ->andWhere('staff_info_id = :staff_info_id:', ['staff_info_id' => $staff_info_id]);

        if (!empty($job_title_id)) {
            $builder->andWhere('job_title_id = :job_title_id:', ['job_title_id' => $job_title_id]);
        }

        if (!empty($store_id)) {
            $builder->andWhere('store_id = :store_id:', ['store_id' => $store_id]);
        }

        $count = $builder->getQuery()->execute()->count();

        $builder->limit($page_size, $offset);
        $list = $builder->getQuery()->execute()->toArray();
        return [
            'items' => $list,
            'pagination' => [
                'current_page' => $page_num,
                'per_page' => $page_size,
                'total_count' => $count,
            ]
        ];
    }

    /**
     * 获取预算汇总-部门维度权限
     * @param $staff_id
     * @return array
     */
    public function getDeptViewPermission($staff_id): array
    {
        if (empty($staff_id)) {
            return [];
        }

        $staffInfo = StaffInfoModel::findFirst([
            'conditions' => 'id = :staff_info_id:',
            'bind' => [
                'staff_info_id' => $staff_id
            ]
        ]);
        if (empty($staffInfo)) {
            return [];
        }
        $staffInfoArr = $staffInfo->toArray();

        //查询是否为CPO的助理
        $CAssList = SysDepartmentModel::findFirst([
            'conditions' => 'assistant_id = :assistant_id: and id = 444',
            'bind' => [
                'assistant_id' => $staff_id
            ]
        ]);

        //获取CLevel负责部门
        $CLevelDeptList = SysDepartmentModel::find([
            'conditions' => 'type = 4 and deleted = 0',
            'columns' => "id,name"
        ])->toArray();

        //白名单
        $permission = EnvModel::getEnvByCode('upload_hc_budget_staff_ids');
        $permissionIDS = explode(',', $permission);

        //全部权限: CPO职位、CPO的助理、TA、HRBP部门、白名单(56207)
        //部分权限: GM/C-Level(除CPO外)、assistant、部门负责人
        if (Enums::JOB_TITLE_CPO == $staffInfoArr['job_title'] ||
            in_array($staffInfoArr['node_department_id'], [Enums::DEPARTMENT_ID_HRBP, Enums::DEPARTMENT_ID_TA, Enums::DEPARTMENT_ID_BP]) ||
            !empty($CAssList) ||
            in_array($staff_id, $permissionIDS)
        ) {
            $deptList = SysDepartmentModel::find([
                'conditions' => 'type != 1 and deleted = 0',
            ])->toArray();
            $returnIds = array_column($deptList, 'id');
        } else { //部分权限

            //C-level(除CPO外)
            $CList = SysDepartmentModel::find([
                'conditions' => '(manager_id = :manager_id: or assistant_id = :assistant_id:) and id != 444 and type in (4,5) and deleted = 0',
                'bind' => [
                    'manager_id'    => $staff_id,
                    'assistant_id'  => $staff_id
                ],
                'columns' => "id,name"
            ])->toArray();

            $CList          = array_column($CList, 'id');
            $CLevelDeptList = array_column($CLevelDeptList, 'id');
            if (!empty($CList) || in_array($staffInfoArr['department_id'], $CLevelDeptList)) { //获取group_boss_id
                if (!empty($CList) && in_array($staffInfoArr['department_id'], $CLevelDeptList)) {
                    $deptArr = array_merge($CList, [$staffInfoArr['department_id']]);
                } else if (!empty($CList)) {
                    $deptArr = $CList;
                } else {
                    $deptArr = [$staffInfoArr['department_id']];
                }

                $CDeptListArr = SysDepartmentModel::find([
                    'conditions' => 'group_boss_id in({group_boss_id:array}) and deleted = 0',
                    'bind' => [
                        'group_boss_id'    => $deptArr,
                    ]
                ])->toArray();
                if (!empty($CDeptListArr)) {
                    $CDeptListIds = array_column($CDeptListArr, 'id');
                } else {
                    $CDeptListIds = [];
                }
            } else {
                $CDeptListIds = [];
            }

            //GM
            $GMList = SysDepartmentModel::find([
                'conditions' => '(manager_id = :manager_id: or assistant_id = :assistant_id:) and type = 1 and deleted = 0',
                'bind' => [
                    'manager_id'    => $staff_id,
                    'assistant_id'  => $staff_id
                ],
                'columns' => "id,name"
            ])->toArray();
            $CGMDeptList = SysDepartmentModel::find([
                'conditions' => 'type = 1 and deleted = 0',
                'columns' => "id,name"
            ])->toArray();
            $GMList      = array_column($GMList, 'id');
            $CGMDeptList = array_column($CGMDeptList, 'id');

            if (!empty($GMList) || in_array($staffInfoArr['department_id'], $CGMDeptList)) {
                if (!empty($GMList) && in_array($staffInfoArr['department_id'], $CGMDeptList)) {
                    $deptArr = array_merge($GMList, [$staffInfoArr['department_id']]);
                } else if (!empty($GMList)) {
                    $deptArr = $GMList;
                } else {
                    $deptArr = [$staffInfoArr['department_id']];
                }
                //获取所有子部门
                $depIds  = SysDepartmentModel::find([
                    'conditions' => 'company_id in({company_id:array}) and deleted = 0',
                    'bind' => [
                        'company_id' => $deptArr,
                    ],
                    'columns' => "id,name"
                ])->toArray();
                $GMListIds = array_column($depIds, 'id');
            } else {
                $GMListIds = [];
            }

            //部门负责人
            $deptListArr = SysDepartmentModel::find([
                'conditions' => '(manager_id = :manager_id: or assistant_id = :assistant_id:) and type in (2,3) and deleted = 0',
                'bind' => [
                    'manager_id'    => $staff_id,
                    'assistant_id'  => $staff_id
                ]
            ])->toArray();

            if (!empty($deptListArr)) {
                $deptListIds = array_column($deptListArr, 'id');
            } else {
                $deptListIds = [];
            }

            //取并集
            $returnIds = array_merge($deptListIds, $GMListIds, $CDeptListIds);
        }
        return $returnIds;
    }

    /**
     * @desc  检查上传的文件是否存在共享职位
     */
    public function checkSharePosition($job_title_id_arr)
    {
	    $department_position = SysService::getInstance()->getShareDepartmentPosition();
	    $share_position_arr = $department_position['position_ids'] ?? [];
        foreach($job_title_id_arr as $value) {
            if(in_array($value, $share_position_arr)){
                return false;
                break;
            }
        }
        return true;
    }

    /**
     * 校验HC计划人数
     * @param $params
     * @return array
     */
    public function checkBudget($params): array
    {
        $departmentId = $params['department_id'];
        $jobTitleIds  = $params['job_id'];

        //HC预算剩余数量:预算人数 - 在职人数 - 待入职人数 - 招聘中人数 - 已提交HC总人数(待审批) - 待转岗
        //1.获取在职人数
        $onJobCnt = $this->getOnJobStaffCount($departmentId, $jobTitleIds);
        //2.获取待入职人数
        $pendingEntryCnt = $this->getPendingEntryCount($departmentId, $jobTitleIds);
        //3.获取招聘中人数
        $surplusCnt = $this->getSurplusCount($departmentId, $jobTitleIds);
        //4.获取已提交的HC总人数（待审批）
        $adoptCon = $this->getBudgetAdoptCount($departmentId, $jobTitleIds);
        //5.待转岗转出人数
        $transferCnt = $this->getJobTransferCount($departmentId, $jobTitleIds);
        //6.停职人数
        $suspend = $this->getSuspend($departmentId, $jobTitleIds);
        //7.待离职人数
        //$waitLeave = $this->getWaitLeave($departmentId, $jobTitleId);
        //8.待转入人数
        //$pendingTransfer = $this->getJobTransferEntryCount($departmentId, $jobTitleId);
        $pendingTransferForBudget = $this->getJobTransferEntryCountForBudget($departmentId, $jobTitleIds);

        //全部占用
        $leftCount = $onJobCnt + $pendingEntryCnt + $surplusCnt + $adoptCon - $transferCnt + $suspend + $pendingTransferForBudget;

        //待入职+招聘中+已提交的HC
        $count = $pendingEntryCnt + $surplusCnt + $adoptCon + $pendingTransferForBudget;

        // 记录详细信息，方便调试
        $this->logger->info("HC预算校验 - 部门: $departmentId, 职位: " . json_encode($jobTitleIds) . json_encode([
            '在职人数'   => $onJobCnt,
            '待入职人数' => $pendingEntryCnt,
            '招聘中人数' => $surplusCnt,
            '待审批人数' => $adoptCon,
            '待转岗人数' => $transferCnt,
            '停职人数'   => $suspend,
            '待转入人数' => $pendingTransferForBudget,
            '剩余HC数量' => $leftCount,
        ]));
        return [$leftCount, $count];
    }

    /**
     * 获取剩余人数
     * @param $departmentId
     * @param $jobTitleIds
     * @return array
     */
    public function getLeftHcReuqestCnt($departmentId, $jobTitleIds)
    {
        //1.获取在职人数
        $onJobCnt = $this->getOnJobStaffCount($departmentId, $jobTitleIds);
        //2.获取待入职人数
        $pendingEntryCnt = $this->getPendingEntryCount($departmentId, $jobTitleIds);
        //3.获取招聘中人数
        $surplusCnt = $this->getSurplusCount($departmentId, $jobTitleIds);
        //4.获取已提交的HC总人数（待审批）
        $adoptCon = $this->getBudgetAdoptCount($departmentId, $jobTitleIds);
        //5.待转入人数
        $pendingTransferForBudget = $this->getJobTransferEntryCountForBudget($departmentId, $jobTitleIds);

        return [
            $onJobCnt,
            $surplusCnt,
            $pendingEntryCnt,
            $adoptCon,
            $pendingTransferForBudget,
        ];
    }

    /**
     * @description 获取计划Hc人数
     * @return int
     */
    public function getPlanHcNums($dept_id, $job_title_id)
    {
        $info = HrJobDepartmentRelationModel::findFirst([
            "department_id = :dept_id: and job_id = :job_title:",
            "bind" => [
                'dept_id' => $dept_id,
                'job_title' => $job_title_id
            ],
            'columns' => 'is_plan,plan_hc_nums'
        ]);
        return empty($info) ? []: $info->toArray();
    }

    /**
     * @description 获取预算信息
     * @param $department_id
     * @param $job_id
     * @return array
     */
    public function getBudget($department_id, $job_id): array
    {
        $count = HrStaffingModel::find([
            'conditions' => "dept_id = :dept_id: and job_title_id IN ({job_title:array})",
            "bind"       => [
                'dept_id'   => $department_id,
                'job_title' => [$job_id],
            ],
        ])->toArray();
        if (count($count) > 1) { //部门、职位对应的预算在多个工作地点，返回0
            return [
                'budget_count'      => 0,
                'share_state'       => [],
                'budget_multi_rows' => true,
            ];
        }
        //共享状态
        $shareState = $this->checkShareJobState($department_id, $job_id);

        //获取共享职位
        $hrStaffingModel = $this->getBudgetModel($department_id, $shareState['share_job_title'] ?? [$job_id]);

        return [
            'budget_count'      => $hrStaffingModel ? intval($hrStaffingModel->count) : 0,
            'share_state'       => $shareState,
            'budget_multi_rows' => false,
        ];
    }

    /**
     * @description 获取预算信息
     * 该版本在hr_staffing表中，原数据纬度为：部门、职位、网点，现在数据纬度为：部门、职位
     *
     * @param $department_id
     * @param $job_id
     * @return array
     */
    public function getBudgetV2($department_id, $job_id): array
    {
        $budgetInfo = HrStaffingModel::findFirst([
            'conditions' => "dept_id = :dept_id: and job_title_id = :job_title:",
            "bind"       => [
                'dept_id'   => $department_id,
                'job_title' => $job_id,
            ],
        ]);
        //共享状态
        $shareState = $this->checkShareJobStateV2($department_id, $job_id);
        if (empty($budgetInfo)) {
            return [
                'is_budget_exist'  => false,                                     //是否存在预算
                'budget_count'     => 0,                                         //预算数量
                'share_state'      => [],                                        //预算共享情况
                'is_hc_share'      => $shareState['is_hc_share'],                //HC预算是否共享
                'share_department' => $shareState['share_department'],           //HC预算共享部门
                'share_job_title'  => $shareState['share_job_title'],            //HC预算共享职位
            ];
        }

        //获取共享职位
        $count = $this->getBudgetCount($department_id, $job_id);
        return [
            'is_budget_exist'  => true,   //是否存在预算
            'budget_count'     => intval($count),
            'share_state'      => $shareState,
            'is_hc_share'      => $shareState['is_hc_share'],                //HC预算是否共享
            'share_department' => $shareState['share_department'],           //HC预算共享部门
            'share_job_title'  => $shareState['share_job_title'],            //HC预算共享职位
        ];
    }

    /**
     * @description 获取共享情况
     * @param $department_id
     * @param $job_id
     * @return array
     */
    public function checkShareJobState($department_id, $job_id): array
    {
        $share_department_position = SysService::getInstance()->getShareDepartmentPosition();
        $department_position       = $share_department_position['department_position'];

        foreach ($department_position as $share_department_id => $job_title_list) {
            if ($department_id != $share_department_id) {
                continue;
            }
            if (empty($job_title_list)) {
                $share_position = [$job_id];
            }
            if (in_array($job_id, $job_title_list)) {
                $share_position = $job_title_list;
            }
            $share_state = true;
        }

        return [
            'is_hc_share'      => $share_state ?? false,                //HC预算是否共享
            'share_department' => $department_id,                       //HC预算共享部门
            'share_job_title'  => $share_position ?? [$job_id],         //HC预算共享职位
        ];
    }

    /**
     * @description 获取共享情况
     * @param $department_id
     * @param $job_id
     * @return array
     */
    public function checkShareJobStateV2($department_id, $job_id): array
    {
        //获取 HC 共享预算服务
        $shareService = HcShareBudgetService::getInstance()->init();

        return [
            //HC预算是否共享
            'is_hc_share'      => $shareService->isShare($department_id, $job_id),
            //HC预算共享部门
            'share_department' => $shareService->getShareGroupDepartmentIds($department_id, $job_id),
            //HC预算共享职位
            'share_job_title'  => $shareService->getShareGroupJobTitleIds($department_id, $job_id),
        ];
    }

    /**
     * @description 获取预算信息
     * @param $department_id
     * @param $job_id
     * @return mixed
     */
    public function getBudgetModel($department_id, $job_id)
    {
        if (is_array($job_id)) {
            $conditions = "dept_id = :dept_id: and job_title_id IN ({job_title:array})";
        } else {
            $conditions = "dept_id = :dept_id: and job_title_id = :job_title:";
        }
        $bind = [
            "dept_id"   => $department_id,
            "job_title" => $job_id
        ];
        return HrStaffingModel::findFirst([
            'conditions' => $conditions,
            "bind"       => $bind,
            "order"      => 'count DESC',
        ]);
    }

    /**
     * @description 获取预算信息
     * @param $department_id
     * @param $job_id
     * @return mixed
     */
    public function getBudgetCount($department_id, $job_id)
    {
        $conditions = "dept_id = :dept_id: and job_title_id = :job_title:";
        $bind = [
            "dept_id"   => $department_id,
            "job_title" => $job_id
        ];
        $count = HrStaffingModel::findFirst([
            'conditions' => $conditions,
            "bind"       => $bind,
        ]);
        return $count ? $count->count : 0;
    }

    /**
     * @description 创建预算
     * @param $department_id
     * @param $job_id
     * @return void
     */
    public function createBudget($department_id, $job_id, $store_id, $count)
    {
        $model = new HrStaffingModel();
        $model->dept_id = $department_id;
        $model->job_title_id = $job_id;
        $model->count = $count;
        if (!empty($store_id)) {
            $model->store_id = $store_id;
        } else {
            $model->store_id = "";
        }
        $model->save();
    }

    /**
     * @description 创建预算
     * @param $department_id
     * @param $job_id
     * @param $count
     * @return void
     */
    public function createBudgetV2($department_id, $job_id, $count)
    {
        $model = new HrStaffingModel();
        $model->dept_id = $department_id;
        $model->job_title_id = $job_id;
        $model->count = $count;
        return $model->save();
    }

    /**
     * @description 同步HC预算人数
     * @param $params
     * @return void
     */
    public function syncHcBudgetCountV2($params)
    {
        $department_id = $params['department_id'];
        $job_id        = $params['job_id'];
        $count         = $params['plan_hc_nums'];

        //获取共享详情
        $budgetInfo = $this->getBudgetV2($department_id, $job_id);
        if ($budgetInfo['is_hc_share']) { //共享
            foreach ($budgetInfo['share_job_title'] as $job_title_id) {
                $this->syncBudgetV2($department_id, $job_title_id, $count);
            }
        } else { //非共享

            //同步预算
            $this->syncBudgetV2($department_id, $job_id, $count);
        }
    }

    /**
     * @description 同步HC预算人数
     * @param $department_id
     * @param $job_id
     * @param $count
     * @return bool
     */
    public function syncBudget($department_id, $job_id, $count): bool
    {
        //这里同步预算要跟HC申请时，对HC预算的校验吻合
        //否则，就会出现存在预算但是无法申请的情况
        //
        //1. 如果非共享，则对HC预算的校验是部门、网点、职位3个维度的
        //2. 如果共享，则对HC预算的校验是共享部门下、的共享职位2个维度的
        $model = BudgetService::getInstance()->getBudgetModel($department_id, $job_id);
        if ($model) {
            //只更新预算的人数，不会涉及维度问题
            $model->count = $count;
            if (!$model->update()) {
                $this->logger->warning('同步预算失败，,sql错误信息： ' . json_encode($model->getMessages()));
                return false;
            }
        } else {
            //插入数据涉及维度问题
            foreach ($job_id as $v) {
                $bidgetService = BudgetService::getInstance();
                $shareInfo = $bidgetService->checkShareJobState($department_id, $v);
                if (empty($shareInfo['is_hc_share'])) { //非共享
                    $bidgetService->createBudget($department_id, $v, "-1", $count);
                } else { //共享
                    $bidgetService->createBudget($department_id, $v, "", $count);
                }
            }
        }

        return true;
    }

    /**
     * @description 同步HC预算人数
     * @param $department_id
     * @param $job_id
     * @param $count
     * @return bool
     */
    public function syncBudgetV2($department_id, $job_id, $count): bool
    {
        $model = $this->getBudgetModel($department_id, [$job_id]);
        if (!empty($model)) {
            $model->count = $count;
            if (!$model->update()) {
                $this->logger->warning('同步预算失败，,sql错误信息： ' . json_encode($model->getMessages()));
                return false;
            }
        } else {
            $this->createBudgetV2($department_id, $job_id, $count);
        }
        return true;
    }

    /**
     * @param $department_id
     * @param $job_id
     * @param $count
     * @return void
     */
    public function syncBudgetV3($department_id, $job_id, $count)
    {
        $shareService = HcShareBudgetService::getInstance()->init();
        if ($shareService->isShare($department_id, $job_id)) {
            $shareGroup = $shareService->getShareGroup($department_id, $job_id);
            foreach ($shareGroup as $shareDepartmentId => $shareJobTitleIdArr) {
                $this->saveMultiBudget($shareDepartmentId, $shareJobTitleIdArr, $count);
            }
        } else {
            $this->saveSingleBudget($department_id, $job_id, $count);
        }
    }

    /**
     * @param $department_id
     * @param $job_id
     * @param $count
     * @return bool
     */
    public function saveSingleBudget($department_id, $job_id, $count): bool
    {
        $model = $this->getBudgetModel($department_id, $job_id);
        if (empty($model)) {
            $this->createBudgetV2($department_id, $job_id, $count);
            return true;
        }
        $model->count = $count;
        if (!$model->update()) {
            return false;
        }
        return true;
    }

    /**
     * @description 获取在职人数
     * 指定部门、职位、工作所在国家, 在职、在编、不含子账号、不含实习生、不含待离职
     * @param $departmentId
     * @param $jobTitle
     * @return int
     */
    public function getOnJobStaffCount($departmentId, $jobTitle): int
    {
        if (empty($departmentId) || empty($jobTitle)) {
            return 0;
        }

        $countryCode = (new HrStaffInfoModel)->getWorkingCountryCode();
        $conditions = 'node_department_id = :node_department_id: and job_title in ({job_title_id:array}) and
                       formal in({formal:array}) and state = :state: and is_sub_staff = :is_sub_staff: and wait_leave_state = :wait_leave_state: and 
                       working_country = :working_country:';
        $bind       = [
            'formal'             => [StaffInfoEnums::FORMAL_IN, StaffInfoEnums::FORMAL_TRAINEE],
            'state'              => StaffInfoEnums::STAFF_STATE_IN,
            'is_sub_staff'       => StaffInfoEnums::IS_SUB_STAFF_NO,
            'wait_leave_state'   => StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_NO,
            'node_department_id' => $departmentId,
            'job_title_id'       => $jobTitle,
            'working_country'    => $countryCode,
        ];

        //获取指定部门、网点、职位的，在职、在编、非子账号的人数
        return HrStaffInfoModel::count([
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);
    }

    /**
     * @description 获取待入职人数 ,
     * 新逻辑：进入到入职表数据
     *  旧逻辑存在漏洞，存在部分总部的人不上传附件还发了offer，却没有入到入职表
     * 旧逻辑：已发offer人数
     * @param $departmentId
     * @param $jobTitle
     * @return int
     */
    public function getPendingEntryCount($departmentId, $jobTitle = null): int
    {
        if (empty($departmentId)) {
            return 0;
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->columns("count(1) as cou");
        $builder->from(['h' => HrHcModel::class]);
        $builder->leftJoin(HrEntryModel::class,'e.hc_id = h.hc_id','e');
        $builder->andWhere('e.deleted = :deleted:', ['deleted' => Enums\GlobalEnums::IS_NO_DELETED]);
        $builder->andWhere('e.status = :status:', ['status' => HrEntryEnums::ENTRY_STATUS_PENDING]);
        $builder->andWhere('h.department_id = :dept_id:', ['dept_id' => $departmentId]);
        if (!empty($jobTitle)) {
            $builder->inWhere('h.job_title', $jobTitle);
        }
        $count = $builder->getQuery()->execute()->getFirst();

        return intval($count->cou);
    }

    /**
     * 获取招聘中人数
     * @param $departmentId
     * @param $jobTitle
     * @return int
     */
    public function getSurplusCount($departmentId, $jobTitle = null): int
    {
        if (empty($departmentId)) {
            return 0;
        }

        if (!empty($jobTitle)) {
            $conditions = 'state_code = :state_code: and department_id = :department_id: and
                            job_title in ({job_title_id:array})';
            $bind       = [
                'state_code'    => HrHcEnums::STATE_RECRUITING, // 招聘中
                'department_id' => $departmentId,
                'job_title_id'  => $jobTitle,
            ];
        } else {
            $conditions = 'state_code = :state_code: and department_id = :department_id:';
            $bind       = [
                'state_code'    => HrHcEnums::STATE_RECRUITING, // 招聘中
                'department_id' => $departmentId,
            ];
        }

        $count      = HrHcModel::findFirst([
            'conditions' => $conditions,
            'bind'       => $bind,
            'columns'    => "sum(surplusnumber) as cou",
        ]);
        if (empty($count)) {
            return 0;
        }
        return $count->cou ?? 0;
    }

    /**
     * @desc 获取已提交的HC总人数（待审批）
     * @param $departmentId
     * @param $jobTitle
     * @return int
     */
    public function getBudgetAdoptCount($departmentId, $jobTitle = null): int
    {
        if (empty($departmentId)) {
            return 0;
        }

        if (!empty($jobTitle)) {
            $conditions = 'state_code = :state_code: and approval_state_code = :approval_state_code:
                and department_id = :department_id: and job_title IN({job_title_id:array}) and deleted = 1';
            $bind = [
                'state_code'          => HrHcEnums::STATE_NOT_EFFECTIVE,
                'approval_state_code' => HrHcEnums::APPROVAL_STATE_PENDING,
                'department_id'       => $departmentId,
                'job_title_id'        => $jobTitle,
            ];
        } else {
            $conditions = 'state_code = :state_code: and approval_state_code = :approval_state_code: 
                and department_id = :department_id: and deleted = 1';
            $bind = [
                'state_code'          => HrHcEnums::STATE_NOT_EFFECTIVE,
                'approval_state_code' => HrHcEnums::APPROVAL_STATE_PENDING,
                'department_id'       => $departmentId,
            ];
        }
        $count = HrHcModel::sum([
            'conditions' => $conditions,
            'bind'       => $bind,
            'column'     => 'demandnumber',
        ]);
        return $count ?? 0;
    }

    /**
     * 获得待转岗(转出)人数
     * @param $department_id
     * @param $jobTitleIds
     * @return int
     */
    public function getJobTransferCount($department_id, $jobTitleIds)
    {
        //审核通过，待转岗
        $conditions = 'approval_state = :approval_state: and state = :state: and current_department_id = :department_id: 
            and current_position_id in ({job_title_id:array}) ';
        $bind = [];

        $bind['approval_state'] = ByWorkflowEnums::BY_OPERATE_PASS;
        $bind['state'] = JobTransferEnums::JOB_TRANSFER_STATE_PENDING;
        $bind['department_id'] = $department_id;
        $bind['job_title_id'] = $jobTitleIds;

        return JobTransferModel::count([
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);
    }

    /**
     * 获得待转岗(转入)人数
     * 此处会限制部门删除、接触关联等场景，需要考虑待审批的情况
     *
     * @param $department_id
     * @param $jobTitleIds
     * @return int
     */
    public function getJobTransferEntryCount($department_id, $jobTitleIds = null): int
    {
        //审核通过，待转岗
        $bind = [];
        if (!empty($jobTitleIds)) {
            $conditions = 'approval_state in({approval_state:array}) and state = :state: and after_department_id = :department_id: and after_position_id in ({job_title_id:array}) ';
            $bind['approval_state'] = [ByWorkflowEnums::BY_OPERATE_PASS, ByWorkflowEnums::BY_OPERATE_WAIT_AUDIT];
            $bind['state'] = JobTransferEnums::JOB_TRANSFER_STATE_PENDING;
            $bind['department_id'] = $department_id;
            $bind['job_title_id'] = $jobTitleIds;
        } else {
            $conditions = 'approval_state in({approval_state:array}) and state = :state: and after_department_id = :department_id:';
            $bind['approval_state'] = [ByWorkflowEnums::BY_OPERATE_PASS, ByWorkflowEnums::BY_OPERATE_WAIT_AUDIT];
            $bind['state'] = JobTransferEnums::JOB_TRANSFER_STATE_PENDING;
            $bind['department_id'] = $department_id;
        }

        return JobTransferModel::count([
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);
    }

    /**
     * 泰国版本存在待转入人数，PH、MY版本不存在
     * @param $department_id
     * @param $jobTitleIds
     * @return int
     */
    public function getJobTransferEntryCountForBudget($department_id, $jobTitleIds = null): int
    {
        if (!in_array(get_country_code(), [GlobalEnums::TH_COUNTRY_CODE, GlobalEnums::MY_COUNTRY_CODE, GlobalEnums::PH_COUNTRY_CODE])) {
            return 0;
        } else {
            return $this->getJobTransferEntryCount($department_id, $jobTitleIds);
        }
    }

    /**
     * @description 停职人数
     * @param $departmentId
     * @param $jobTitle
     * @return mixed
     */
    public function getSuspend($departmentId, $jobTitle)
    {
        $countryCode = (new HrStaffInfoModel)->getWorkingCountryCode();
        return HrStaffInfoModel::count([
            'conditions' => 'node_department_id = :node_department_id: and job_title in ({job_title_id:array}) and
                state = :state: and formal in({formal:array}) and is_sub_staff = :is_sub_staff: and wait_leave_state = :wait_leave_state: 
                and working_country = :working_country:',
            'bind' => [
                'formal'             => [StaffInfoEnums::FORMAL_IN, StaffInfoEnums::FORMAL_TRAINEE],
                'state'              => StaffInfoEnums::STAFF_STATE_STOP,
                'is_sub_staff'       => StaffInfoEnums::IS_SUB_STAFF_NO,
                'wait_leave_state'   => StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_NO,
                'node_department_id' => $departmentId,
                'job_title_id'       => $jobTitle,
                'working_country'    => $countryCode,
            ],
        ]);
    }

    /**
     * @description 待离职人数
     * @param $departmentId
     * @param $jobTitle
     * @return mixed
     */
    public function getWaitLeave($departmentId, $jobTitle)
    {
        $countryCode = (new HrStaffInfoModel)->getWorkingCountryCode();
        return HrStaffInfoModel::count([
            'conditions' => 'node_department_id = :node_department_id: and job_title in ({job_title_id:array})
                and state = :state: and formal in({formal:array}) and is_sub_staff = :is_sub_staff: and wait_leave_state = :wait_leave_state: 
                and working_country = :working_country:',
            'bind' => [
                'formal'             => [StaffInfoEnums::FORMAL_IN, StaffInfoEnums::FORMAL_TRAINEE],
                'state'              => StaffInfoEnums::STAFF_STATE_IN,
                'is_sub_staff'       => StaffInfoEnums::IS_SUB_STAFF_NO,
                'wait_leave_state'   => StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES,
                'node_department_id' => $departmentId,
                'job_title_id'       => $jobTitle,
                'working_country'    => $countryCode,
            ],
        ]);
    }

    /**
     * 是否当前国家被配置为使用新版预算
     * 如返回 true 则使用新版预算
     *  否则，使用老版本预算
     * @return bool
     */
    public function isConfiguredBudget(): bool
    {
        return !empty($this->getBudgetConfiguredCountries());
    }

    /**
     * 获取配置新版预算的国家
     * @return string
     */
    public function getBudgetConfiguredCountries()
    {
        return (new SettingEnvModel())->getSetVal('hc_budget_configured');
    }
}