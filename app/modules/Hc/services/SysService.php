<?php

namespace App\Modules\Hc\Services;

use App\Library\ApiClient;
use app\modules\Hc\models\HcmStaffManageListModel;
use App\Modules\Hc\Models\HcmStaffManageRegionsModel;
use App\Modules\Hc\Models\HrHcSummaryDataTblModel;
use app\modules\Hc\models\HrJobDepartmentRelationModel;
use App\Modules\Hc\Models\HrJobTitleModel;
use App\Modules\Hc\Models\SysDepartmentModel;
use App\Modules\Hc\Models\SysManagePieceModel;
use App\Modules\Hc\Models\SysManageRegionModel;
use App\Modules\Hc\Models\SysStoreModel;
use App\Modules\Transfer\Models\SettingEnvModel;
use App\Modules\User\Models\HrStaffInfoModel;

class SysService extends BaseService
{
    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return SysService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 所有职位
     * @return array
     */
    public function getAllJobTitleList()
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('id as job_title_id,job_name as job_title_name');
        $builder->from(HrJobTitleModel::class);
        $builder->where('status = 1');
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 部门关联职位list
     * @param $dept_id
     * @return array
     */
    public function getDeptJobTitleList($dept_id)
    {
        if (empty($dept_id)) {
            return [];
        }

        $builder = $this->modelsManager->createBuilder()
            ->columns('hr_job_department_relation.job_id as job_title_id, hr_job_title.job_name as job_title_name')
            ->addFrom(HrJobDepartmentRelationModel::class, 'hr_job_department_relation')
            ->leftJoin(HrJobTitleModel::class, 'hr_job_department_relation.job_id = hr_job_title.id', 'hr_job_title')
            ->where('hr_job_title.status = 1');
        if (is_array($dept_id)) {
            $builder->inWhere('hr_job_department_relation.department_id', $dept_id);
        } else {
            $builder->andWhere('hr_job_department_relation.department_id = :sys_depeartment_id:', ['sys_depeartment_id' => $dept_id]);
        }

        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 部门职位关联配置
     * @param $dept_ids
     * @return array
     */
    public function getDeptJobTitleConfig($dept_ids)
    {

        $builder = $this->modelsManager->createBuilder()
            ->from(HrJobDepartmentRelationModel::class)
            ->columns('department_id as sys_depeartment_id,job_id as job_title_id,plan_hc_nums,is_plan')
            ->inWhere('department_id', $dept_ids);

        $list = $builder->getQuery()->execute()->toArray();

        $dept_job_title_config = [];
        foreach ($list as $key => $value) {
            $dept_job_title_config[$value['sys_depeartment_id'] . '_' . $value['job_title_id']] = $value;
        }

        return $dept_job_title_config;
    }

    /**
     * 网点名称模糊搜索网点
     * @param $search_name
     * @return array
     */
    public function getStoreListBySearchName($search_name = '')
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('id as store_id,name')
            ->from(SysStoreModel::class)
            ->where('state = :state:', ['state' => 1]);
        if (!empty($search_name)) {
            $builder->andWhere('name like :search_name:', ['search_name' => '%' . $search_name . '%']);
        }
        $list = $builder->getQuery()->execute()->toArray();
        $head_office = ['store_id' => -1, 'name' => 'head office'];

        array_unshift($list, $head_office);
        return $list;
    }

    public function getNodeDeptList($dept_id, $is_tree = true)
    {
        $dept = SysDepartmentModel::findFirst([
            'conditions' => 'id = ?1',
            'bind' => [
                1 => $dept_id
            ]
        ]);
        if(empty($dept)){
        	return [];
        }
	    $dept = $dept->toArray();

        if ($dept_id == 999) {
            $ancestry_v3 = '999/';
        } else {
            $ancestry_v3 = $dept['ancestry_v3'] . '/';
        }

        $builder = $this->modelsManager->createBuilder()
            ->columns('id, ancestry, ancestry_v3, name')
            ->from(SysDepartmentModel::class)->where('deleted = 0')
            ->andWhere("ancestry_v3 like :ancestry_v3:", ['ancestry_v3' => $ancestry_v3 . '%'])
            ->orWhere("id = :dept_id:", ['dept_id' => $dept['id']])
            ->getQuery()->execute()->toArray();

        if ($is_tree === true) {
            return self::_treeNode($builder, 'ancestry', $dept['ancestry']);
        }
        return $builder;
    }

    /*
     * 根据负责人(助理)id 获取管辖组织下面的部门
     */
    public function getGroupDeptByManagerId($staff_info_id)
    {
        $group_list = SysDepartmentModel::find([
            'conditions' => '(manager_id = ?1 or assistant_id = ?1) and type in (4,5) and deleted = 0',
            'bind' => [
                1 => $staff_info_id
            ],
            'columns' => 'id,type'
        ])->toArray();

        $staffInfo = HrStaffInfoModel::findFirst([
            "conditions" => "staff_info_id = :staff_info_id:",
            "columns" => "staff_info_id as staff_id, name, node_department_id, sys_store_id, job_title",
            "bind" => [ "staff_info_id" => $staff_info_id]
        ]);

        if (!empty($staffInfo->node_department_id)) {

            $sysInfo = SysDepartmentModel::findFirst([
                'conditions' => 'id = ?1 and deleted = 0',
                'bind' => [
                    1 => $staffInfo->node_department_id
                ],
                'columns' => 'id,type'
            ]);
            if (isset($sysInfo->type) && in_array($sysInfo->type, [4, 5])) {
                $group_list = array_merge($group_list, [[
                    'id'    => $sysInfo->id,
                    'type'  => $sysInfo->type,
                ]]);
            }
        }

        $dept_ids = [];
        if(!empty($group_list)) {
            $is_all = false;
            $group_ids = [];
            foreach ($group_list as $key => $value) {
                //GroupCEO 、CPO可申请全公司的HC预算
                if($value['type'] == 5 || $value['type'] == 4 && $value['id'] == 444) {
                    $is_all = true;
                    break;
                } else {
                    $group_ids[] = $value['id'];
                }
            }

            $builder = $this->modelsManager->createBuilder()
                ->columns('id, ancestry, ancestry_v3, name, manager_id, assistant_id, company_id, type')
                ->from(SysDepartmentModel::class)
                ->where('deleted = 0');

            if($is_all == false) {
                $builder->andWhere('group_boss_id IN ({group_boss_id:array}) or id IN ({group_boss_id:array})', [
                    'group_boss_id' => $group_ids
                ]);
            }
            $dept_list = $builder->getQuery()->execute()->toArray();
            $dept_ids = array_column($dept_list,'id');
            $dept_ids = array_map('intval', $dept_ids);
        }
        return $dept_ids;
    }

    /*
     * 根据负责人(助理)id 获取管辖公司下面的部门
     */
    public function getCompanyDeptByManagerId($staff_info_id)
    {
         $company_list = SysDepartmentModel::find([
             'conditions' => 'deleted = 0 and type = 1 and (manager_id = ?1 or assistant_id = ?1)',
             'bind' => [
                 1 => $staff_info_id
             ]
         ])->toArray();

        $staffInfo = HrStaffInfoModel::findFirst([
            "conditions" => "staff_info_id = :staff_info_id:",
            "columns" => "staff_info_id as staff_id, name, node_department_id, sys_store_id, job_title",
            "bind" => [ "staff_info_id" => $staff_info_id]
        ]);

        if (!empty($staffInfo->node_department_id)) {

            $sysInfo = SysDepartmentModel::findFirst([
                'conditions' => 'id = ?1 and deleted = 0',
                'bind' => [
                    1 => $staffInfo->node_department_id
                ],
                'columns' => 'id,type'
            ]);
            if (isset($sysInfo->type) && $sysInfo->type == 1) {
                $company_list = array_merge($company_list, [[
                    'id'    => $sysInfo->id,
                    'type'  => $sysInfo->type,
                ]]);
            }
        }

        $dept_ids = [];
        if(!empty($company_list)) {
            $company_ids = array_column($company_list,'id');
            $dept_list = SysDepartmentModel::find([
                'conditions' => 'deleted = 0 and company_id in({company_ids:array})',
                'bind' => [
                    'company_ids' => $company_ids
                ]
            ])->toArray();

            $dept_ids = array_column($dept_list,'id');
            $dept_ids = array_map('intval', $dept_ids);
        }
        return $dept_ids;
    }

    /*
     * 根据负责人(助理)id 获取管辖的部门
     */
    public function getDeptByManagerId($staff_info_id)
    {
        $my_dept_list = SysDepartmentModel::find([
            'conditions' => 'deleted = 0 and type in (2, 3) and (manager_id = ?1 or assistant_id = ?1)',
            'bind' => [
                1 => $staff_info_id
            ]
        ])->toArray();

        $dept_ids = [];
        if(!empty($my_dept_list)) {

            //获取CLevel、BU
            $c_dept_list = SysDepartmentModel::find([
                'conditions' => 'deleted = 0 and type in (1,4,5)',
                'columns' => 'id'
            ])->toArray();
            $c_dept_list = array_column($c_dept_list, 'id');

            foreach ($my_dept_list as $key => $value) {
                $ancestry_chain = explode('/', $value['ancestry_v3']);
                if(isset($value['type']) && $value['type'] == 2) { //公司下部门

                    //找出部门链中的一级部门
                    $levelOneDepartment = SysDepartmentModel::findFirst([
                        'conditions' => "id in ({departmentIds:array}) and level = 1",
                        'bind'       => [
                            'departmentIds'   => $ancestry_chain,
                        ],
                        'columns' => "id,name,ancestry_v3"
                    ]);

                    //获取一级部门的部门链
                    $where  = $levelOneDepartment->ancestry_v3 ?? '';
                    $levelOneDepartmentId = $levelOneDepartment->id ?? 0;
                } else { //直属部门
                    $depAncestryChain  = array_diff($ancestry_chain, $c_dept_list);
                    $tmpDept = array_shift($depAncestryChain);

                    //找出部门链中的一级部门
                    $levelOneDepartment = SysDepartmentModel::findFirst([
                        'conditions' => "id = :department_id:",
                        'bind'       => [
                            'department_id'   => $tmpDept,
                        ],
                        'columns' => "id,name,ancestry_v3"
                    ]);

                    //获取一级部门的部门链
                    $where  = $levelOneDepartment->ancestry_v3 ?? '';
                    $levelOneDepartmentId = $levelOneDepartment->id ?? 0;
                }

                $builder = $this->modelsManager->createBuilder()
                    ->columns('id, ancestry, ancestry_v3, name, type')
                    ->from(SysDepartmentModel::class)->where('deleted = 0')
                    ->andWhere("ancestry_v3 like :ancestry_chain:", ['ancestry_chain' => $where . '/%'])
                    ->orWhere("id = :dept_id:", ['dept_id' => $levelOneDepartmentId])
                    ->getQuery()->execute()->toArray();

                $builder_ids = array_column($builder, 'id');
                $dept_ids = array_merge($dept_ids,$builder_ids);
            }
            $dept_ids = array_map('intval', $dept_ids);
        }
        return $dept_ids;
    }

    public function getDeptByMyDeptId($my_dept_id) {
        $my_dept = SysDepartmentModel::findFirst([
            'conditions' => 'id = ?1',
            'bind' => [
                1 => $my_dept_id
            ]
        ]);
        //获取CLevel、BU
        $c_dept_list = SysDepartmentModel::find([
            'conditions' => 'deleted = 0 and type in (1,4,5)',
            'columns' => 'id'
        ])->toArray();
        $c_dept_list = array_column($c_dept_list, 'id');

        $ancestry_chain = explode('/', $my_dept->ancestry_v3);
        if(isset($my_dept->type) && $my_dept->type == 2) { //公司下部门

            //找出部门链中的一级部门
            $levelOneDepartment = SysDepartmentModel::findFirst([
                'conditions' => "id in ({departmentIds:array}) and level = 1",
                'bind'       => [
                    'departmentIds'   => $ancestry_chain,
                ],
                'columns' => "id,name,ancestry_v3"
            ]);

            //获取一级部门的部门链
            $where  = $levelOneDepartment->ancestry_v3 ?? '';
            $or_dept_id = $levelOneDepartment->id ?? 0;
        } else if ($my_dept->type == 3) { //直属部门
            $depAncestryChain  = array_diff($ancestry_chain, $c_dept_list);
            $tmpDept = array_shift($depAncestryChain);

            //找出部门链中的一级部门
            $levelOneDepartment = SysDepartmentModel::findFirst([
                'conditions' => "id = :department_id:",
                'bind'       => [
                    'department_id'   => $tmpDept,
                ],
                'columns' => "id,name,ancestry_v3"
            ]);

            //获取一级部门的部门链
            $where  = $levelOneDepartment->ancestry_v3 ?? '';
            $or_dept_id = $levelOneDepartment->id ?? 0;
        } else {
            return [];
        }

        $builder = $this->modelsManager->createBuilder()
            ->columns('id, ancestry, ancestry_v3, name')
            ->from(SysDepartmentModel::class)->where('deleted = 0')
            ->andWhere("ancestry_v3 like :ancestry_chain:", ['ancestry_chain' => $where . '/%'])
            ->orWhere("id = :dept_id:", ['dept_id' => $or_dept_id])
            ->getQuery()->execute()->toArray();

        $builder_ids = array_column($builder, 'id');
        return array_map('intval', $builder_ids);
    }

    /*
     * 获取总管辖部门
     */
    public function getMyDeptListV2($staff_info_id, $my_dept_id, $is_tree = true)
    {

        $g_ids = $this->getGroupDeptByManagerId($staff_info_id);
        $c_ids = $this->getCompanyDeptByManagerId($staff_info_id);
        $p_ids = $this->getBpHeadDeptByManagerId($staff_info_id);
        $d_ids = $this->getDeptByManagerId($staff_info_id);
        $m_ids = $this->getDeptByMyDeptId($my_dept_id);

        $ids = array_values(array_unique(array_merge($g_ids, $c_ids, $d_ids, $m_ids, $p_ids)));
        //log
        $this->getDI()->get('logger')->info([
            'my_dept_id' => $my_dept_id,
            'g_ids'      => $g_ids,
            'c_ids'      => $c_ids,
            'p_ids'      => $p_ids,
            'd_ids'      => $d_ids,
            'm_ids'      => $m_ids,
            'ids'        => $ids,
        ]);

        if(empty($ids)) {
            return [];
        }

        $list = SysDepartmentModel::find([
            'conditions' => 'deleted = 0 and id in({ids:array})',
            'bind' => [
                'ids' => $ids
            ],
            'columns' => 'id, name, ancestry'
        ])->toArray();

        if(!$is_tree) {
            return $list;
        }

        $items = [];
        foreach ($list as $value) {
            $items[$value['id']] = [
                'value' => $value['id'],
                'label' => $value['name'],
                'ancestry' => $value['ancestry'],
            ];
        }

        $tree = [];
        foreach ($items as $key => $value) {
            if (isset($items[$value['ancestry']])) {
                $items[$value['ancestry']]['children'][] = &$items[$key];
            } else {
                $tree[] = &$items[$key];
            }
        }
        return $tree;
    }

    /**
     * @description 获取HC预算相关人数
     * @param $param
     * @return bool|mixed|null
     */
    public function Staffing($param)
    {
        $ac = new ApiClient('by', '', 'staffing', static::$language);
        $ac->setParams([$param]);
        $res = $ac->execute();

        $result = [];
        if (isset($res['result']) && !empty($res['result'])) {
            foreach ($res['result'] as $key => $value) {
                $result[$value['dept_id'].'_'.$value['job_title_id'].'_'.$value['store_id']] = $value;
            }
        }

        return $result;
    }

    /**
     * 全部部门列表
     */
    public function getAllDepartmentList()
    {
        return SysDepartmentModel::find([
            'conditions' => 'deleted = ?1',
            'bind' => [
                1 => 0
            ]
        ])->toArray();
    }

    /**
     * 获取区域树状结构
     */
    public function getRegionsAndPieces()
    {
        $regionList = SysManageRegionModel::find([
            'conditions' => 'deleted = ?1',
            'bind' => [
                1 => 0
            ],
            'columns' => "id, name",
            'order' => 'id asc'
        ])->toArray();

        $pieceList = SysManagePieceModel::find([
            'conditions' => 'deleted = ?1',
            'bind' => [
                1 => 0
            ],
            'columns' => "id, name, manage_region_id",
            'order' => 'id asc'
        ])->toArray();

        $data = [];
        foreach ($regionList as $region){
            $temp = [];
            $temp['value'] = $region['id'];
            $temp['label'] = $region['name'];
            $temp['children'] = [];
            $data[$region['id']] = $temp;
        }
        foreach ($pieceList as $piece){
            $temp = [];
            $temp['value'] = $piece['id'];
            $temp['label'] = $piece['name'];
            $data[$piece['manage_region_id']]['children'][] = $temp;
        }
        return array_values($data);
    }

    /**
     * 获取区域
     */
    public function getAllRegions()
    {
        return SysManageRegionModel::find([
            'conditions' => 'deleted = ?1',
            'bind' => [
                1 => 0
            ],
            'columns' => "id, name",
        ])->toArray();
    }

    /**
     * 获取区域
     */
    public function getAllPieces()
    {
        return SysManagePieceModel::find([
            'conditions' => 'deleted = ?1',
            'bind' => [
                1 => 0
            ],
            'columns' => "id, name",
        ])->toArray();
    }

    /**
     * 获取bp head 管辖的部门
     * @param $staff_info_id
     * @return array
     */
    public function getBpHeadDeptByManagerId($staff_info_id): array
    {
        $info = HcmStaffManageRegionsModel::find([
            'conditions' => 'staff_info_id = ?1 and category = 1 and deleted = 0',
            'bind' => [
                1 => $staff_info_id
            ],
            'columns' => "value",
        ])->toArray();
        if (empty($info)) {
            return [];
        }

        return array_column($info, 'value');
    }

    /**
     * OA审批流HRBP节点寻人逻辑,通过员工id查找所属管辖HRBP
     * @param $staff_info_id
     * @return int|mixed
     * @date 2022/2/14
     */
    public function getBpHeadByStaff($staff_info_id){
        $staff_info = HrStaffInfoModel::findFirst(
            [
                'conditions' => 'staff_info_id = :staff_id:',
                'bind' => [
                    'staff_id' => $staff_info_id
                ]
            ]
        );
        $staff_data = empty($staff_info)?[]:$staff_info->toArray();
        if (empty($staff_data) || empty($staff_data['node_department_id'])){
            return 0;
        }
        //查询部门管辖的hrbp
        $hrbp_info = HcmStaffManageRegionsModel::findFirst([
            'conditions' => 'value = :value: and category = 1 and deleted = 0 and staff_info_id>0',
            'bind' => [
                'value' => $staff_data['node_department_id']
            ],
            'columns' => "staff_info_id",
        ]);
        $hrbp_data = empty($hrbp_info)?[]:$hrbp_info->toArray();
        if (empty($hrbp_data) || empty($hrbp_data['staff_info_id'])){
            return 0;
        }
        //查询员工是否在职
        $staff = HrStaffInfoModel::findFirst([
            'conditions' => 'staff_info_id = ?1',
            'bind' => [
                1 => $hrbp_data['staff_info_id']
            ],
        ]);

        /**
         * 如果存在，且在职
         */
        if (!empty($staff) && $staff->state == 1) {
            return $hrbp_data['staff_info_id'];
        }else{
            return 0;
        }
    }


	/**
	 * @description: 获取共享部门和职位
	 *
	 * @param null
	 * department_ids 所有的共享部门 id   position_ids 所有的共享职位 id    department_position  部门=>[共享职位,共享职位]
	 * @return     :['department_ids'=>[1,2],'position_ids'=>[3,4,5,6],'department_position'=>[1=>[3,4],2=>[5,6]]];
	 * <AUTHOR> L.J
	 * @time       : 2022/1/12 17:31
	 */
	public  function getShareDepartmentPosition(): array
	{
		$result = ['department_ids'=>[],'position_ids'=>[],'department_position'=>[]];
	
		try {
			$envModel = new SettingEnvModel();
			//格式是  部门|职位,职位&部门|职位,职位
			$share = $envModel->getSetVal('hc_share_department_position');

			if (!empty($share)) {
				$share = explode('#', $share);
				foreach ($share as $k => $v) {
					if(empty($v)){
						continue;
					}
					$department_position                           = explode('|', $v);
					$department_id                                 = $department_position[0] ?? 0;//部门 id
					$result['department_ids'][]                    = $department_id;              //部门 id
					$result['department_position'][$department_id] = [];
					if (isset($department_position[1]) && !empty($department_position[1])) {
						$position_ids                                  = explode(',', $department_position[1]); //职位 id
						$result['position_ids']                        = array_merge($result['position_ids'], $position_ids);
						$result['department_position'][$department_id] = $position_ids;

					}
				}
			}
		}catch (\Exception $e) {
			$this->logger->error(
				'file ' . $e->getFile() .
				' line ' . $e->getLine() .
				' message ' . $e->getMessage() .
				' trace ' . $e->getTraceAsString()
			);
		}
		return $result;
	}


    /**
     * @description:  获取计划 hc 数量  如果是总部 则返回可以校验
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/5/12 19:01
     */

    public function  getPlanHcNum($department_id = 0,$job_id = 0): array
    {
        $data['plan_hc_nums'] = 0;  //计划 hc
        $data['work_place_type'] = 0;  //类型
        $data['is_check']  = 0;
        $info = HrJobDepartmentRelationModel::findFirst([
                                                    'conditions' => 'department_id = :department_id: and job_id = :job_id:',
                                                    'bind' => [
                                                        'department_id' => $department_id,
                                                        'job_id' => $job_id,
                                                    ],
                                                    'columns' => "plan_hc_nums,work_place_type",
                                                ]);
        if($info){
            $data['plan_hc_nums'] = $info->plan_hc_nums;  //计划 hc
            $data['work_place_type'] = $info->work_place_type;  //类型
            $data['is_check'] = in_array($info->work_place_type,[HrJobDepartmentRelationModel::$work_place_type_2,HrJobDepartmentRelationModel::$work_place_type_3]) ? 1 : 0; // 1 需要校验 0 不需要
        }

        return $data;
    }

    /**
     * @description HC汇总状态
     * @return array
     */
    public function getState()
    {
        return [
            [
                'code' => HrHcSummaryDataTblModel::HC_BUDGET_NOT_OVERRUN,
                'title' => static::$t->_('budget_state_not_overrun'), //1未超预算
            ],[
                'code' => HrHcSummaryDataTblModel::HC_BUDGET_OVERRUN,
                'title' => static::$t->_('budget_state_overrun'), //2超过预算
            ]
        ];
    }
}