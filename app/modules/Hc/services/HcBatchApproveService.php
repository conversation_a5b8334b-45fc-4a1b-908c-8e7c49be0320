<?php

namespace App\Modules\Hc\Services;


use App\Library\ApiClient;
use App\Library\ErrCode;
use App\Library\OssHelper;
use App\Library\Validation\ValidationException;
use App\Modules\Hc\Models\HrJobTitleModel;
use App\Modules\Hc\Models\VehicleInfoModel;
use App\Modules\Hc\Models\HCApprovalTaskModel;
use App\Modules\Hc\Models\HcApprovalDetailModel;
use App\Modules\Hc\Models\HrStaffInfoByModel;
use App\Modules\Hc\Models\SysDepartmentModel;
use App\Modules\Hc\Models\SysManageRegionModel;
use App\Modules\Hc\Models\SysManagePieceModel;
use App\Modules\Hc\Models\SysStoreModel;
use App\Modules\Hc\Models\HrStaffInfoModel;
use App\Modules\Transfer\Models\HrHcModel;
use App\Modules\Transfer\Models\SettingEnvModel;
use Exception;


class HcBatchApproveService extends BaseService
{

    private static $instance;
	public static $validate_search_param = [
		'pageSize' => 'IntGt:0',
		'pageNum' => 'IntGt:0',
	];
    public static $data_max_long  = 100;//最多上传 100 条
    public static $source = 2;//1 是 by  2 是 oa
    public static $not_store_id = '-1';//不需要的 hc
    public static $van_courier_job_id = '110';// van 职位
    public static $car_courier_job_id = '1199';// car_courier 职位
    public static $bike_courier_job_id = '13';// bike_courier 职位
    public static $dc_officer_job_id = '37';// $dc_officer 职位
    public static $branch_supervisor_job_id = '16';//
    public static $assistant_branch_supervisor_job_id = '451';//assistant_branch_supervisor

    /**
	 * @return BudgetService
	 */
	public static function getInstance()
	{
		if (!isset(self::$instance) || !(self::$instance instanceof self)) {
			self::$instance = new self;
		}
		return self::$instance;
	}

	
	/**
	 * @description: 获取 列表数据
	 *
	 * @param null
	 *
	 * @return     :
	 * <AUTHOR> L.J
	 * @time       : 2022/03/31 13:38
	 */
	public function getList($param, $staff_info_id)
	{

		$pageSize              = empty($param['pageSize']) ? 20 : $param['pageSize'];       //条数
		$pageNum               = empty($param['pageNum']) ? 1 : $param['pageNum'];          //页数
		$start                 = ($pageNum - 1) * $pageSize;

        $conditions = ' staff_id = :staff_id:  and  source = 2';
        $bind['staff_id'] = $staff_info_id;

		$list = HCApprovalTaskModel::find([
			                                      'conditions' => $conditions,
			                                      'bind'       => $bind,
			                                      'offset'     => $start,
			                                      'limit'      => $pageSize,
                                                  'order' => 'id desc',
		                                      ])->toArray();
		//格式化内容
		$list = $this->buildDataList($list);
		//查询数据总量
		$count = HCApprovalTaskModel::count([
			                                        'conditions' => $conditions,
			                                        'bind'       => $bind,
		                                        ]);
        return [
            'items'        => $list,
            'pagination'   => [
                'current_page' => $pageNum,
                'per_page'     => $pageSize,
                'total_count'  => $count,
            ],
        ];
		
	}

    public function buildDataList($list){
        foreach($list as $k=>&$v){
            $v['create_at'] = date('Y-m-d H:i:s', strtotime($v['create_at']) + get_sys_time_offset() * 3600);
            $v['error_num'] = !empty($v['is_deal']) ? ($v['total_num'] - $v['success_num']) : 0; // 失败数量
        }
        return $list;
    }
	
	/**
	 * @description:下载数据
	 *
	 * @param int $staff_info_id 用户 id
	 *
	 * @return     :
	 * <AUTHOR> L.J
	 * @time       : 2021/11/9 18:56
	 */
	public function Download($id,$staff_info_id): array
    {
        $code    = ErrCode::$SUCCESS;
        $message = '';
        $data    = [];
        try {
            //获取数据
            $data_task = HCApprovalTaskModel::findFirst([
                                                       'conditions' => 'id=:id: and staff_id = :staff_id:',
                                                       'bind' => ['id' => $id,'staff_id'=>$staff_info_id]
                                                   ]);
            if(empty($data_task)){
                throw new ValidationException(static::$t->_('hc_batch_download_null_data')); //没有该权限的数据
            }
            if(empty($data_task->export_path)) {
                $list         = HcApprovalDetailModel::find([
                                                                'conditions' => ' hc_task_id = :hc_task_id:',
                                                                'bind'       => ['hc_task_id' => $id],
                                                            ])->toArray();
                $excel_header = $this->getDetailTplAndField()['header'];
                $excel_result = [];
                foreach ($list as $k => $v) {
                    $excel_result[] = [
                        $v['hc_id'],//hc_id
                        $v['approval_result'],//是否审批通过
                        $v['reason'],//备注
                        static::$t->_('hc_approval_batch_approval_state_' . $v['state']),//结果
                        $v['error_message'],//失败原因
                    ];

                }
                //下载excle，方法返回上传阿里云后地址数组
                $file_arr = \App\Modules\Organization\Services\BaseService::excelToFile($excel_header, $excel_result, 'oa-hc_approval_batch-' . time() . '.xlsx');

                $data['file_url'] = $file_arr ? $file_arr['object_url'] : '';
                //给表赋值
                $data_task->export_path = $data['file_url'];
                $data_task->save();
                //修改export_time
                $this->getDI()->get('db_backyard')->updateAsDict('hc_approval_detail', ['export_time' => gmdate('Y-m-d H:i:s')],
                                                                 ['conditions' => 'hc_task_id = ?', 'bind' => [$id]]);

            }else{
                $data['file_url'] = $data_task->export_path;
            }

        } catch (ValidationException $e) {
            $code         = ErrCode::$VALIDATE_ERROR;
            $message      = $e->getMessage();
        } catch (Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->getDI()->get('logger')->error('hc 批量审批-Download-异常信息: ' . $real_message.'---------' . $e->getLine());
        }

        $res_data =  [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
        $this->getDi()->get('logger')->info('hc 批量审批-Download parameter -- '.$id.','.$staff_info_id.'  -- '.json_encode($res_data));
        return $res_data;
	}


    /**
     * 获取导入后的数据
     * @param $data
     * @param $user
     * @return array
     */
    public function import($file, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';
        $res = false;
        if(empty($file)){
            throw new ValidationException(static::$t->_('hc_batch_approve_not_file'));
        }
        $file_name = $file->getName();
        $excel = new \Vtiful\Kernel\Excel(['path' => '']);

        // 读取文件
        $excelData = $excel->openFile($file->getTempName())
                           ->openSheet()
                           ->setSkipRows(1)
                           ->getSheetData();

        if (empty($excelData) || empty($excelData[0])) {
            throw new ValidationException(static::$t->_('hc_batch_approve_data_empty'));
        }

        if (count($excelData) > static::$data_max_long) {
            throw new ValidationException(static::$t->_('hc_batch_approve_data_gt').static::$data_max_long);
        }
        // 上传OSS
        $upload_res   = OssHelper::uploadFile($file->getTempName());
        $download_url = !empty($upload_res['object_url']) ? $upload_res['object_url'] : '';

        //开始
        $db = $this->getDI()->get('db_backyard');
        $db->begin();
        try {
            //插入 task 表
            $task_data = [
                'staff_id'=>$user,
                'total_num'=>count($excelData),
                'file_name'=>$file_name,
                'file_path'=>$download_url,
                'source'=>static::$source,
                'create_at'=>gmdate('Y-m-d H:i:s'),
            ];
            $db->insertAsDict('hc_approval_task', $task_data);
            $task_id = $db->lastInsertId();
            if (empty($task_id)){
                throw new Exception("insert  task error");
            }
            $insert_data = [];
            foreach ($excelData as $k => $v) {
                $insert_data[]=[
                    'hc_id'=>(int)$v[0],
                    'approval_result'=>$v[1],
                    'reason'=>$v[2],
                    'hc_task_id'=>$task_id,
                ];
            }
            $detail_model = new HcApprovalDetailModel();
            $res = $detail_model->batch_insert_execute($insert_data);
            $db->commit();
        } catch (Exception $e) {
            $db->rollback();
            $code = ErrCode::$SYSTEM_ERROR;
            $this->logger->notice('HcBatchApproveService import error=' . $e->getMessage(). '---------' . $e->getLine());
            $message = static::$t->_('retry_later');
        }
        return ['code' => $code, 'message' => $message, 'data' => $res];
    }


    /**
     * @description:获取待审批的数据
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/3/31 23:50
     */
    public function getExportPendingList($staff_info_id): array
    {
        $data = ['code'    => ErrCode::$VALIDATE_ERROR,
                 'message' =>  static::$t->_('hc_batch_approve_no_pending_approval'),
                 'data'    => []
        ];

        $page_num  = 1;   //页数
        $page_size = 500; //最多 500 条
        $type      = 6;   // hc 申请 类型
        $ApiClient = new ApiClient('by', '', 'get_audit_list', static::$language);
        $ApiClient->setParams(
            [
                [
                    'type'      => $type,
                    'page_num'  => $page_num,
                    'page_size' => $page_size,
                    'staff_id'  => $staff_info_id,
                ]
            ]
        );
        $res   = $ApiClient->execute();
        $items = empty($res['result']['dataList']) ? [] : $res['result']['dataList'];
        if (!empty($items)) {
            //查询 hc 列表  不要 总部的数据
            $hc_ids = array_column($items, 'biz_value');

            $list = HrHcModel::find([
                                        'conditions' => ' hc_id in ({hc_ids:array}) and worknode_id != :worknode_id:',
                                        'bind'       => ['hc_ids' => $hc_ids, 'worknode_id' => static::$not_store_id],
                                        'columns'    => 'hc_id,worknode_id,job_title,demandnumber,reason_type,reason',
                                        'offset'     => '0',
                                        'limit'      => '100',
                                    ])->toArray();

            //获取相关信息
            if ($list) {
                if (get_country_code() == 'MY') {

                    $data = $this->getStoreDetailsMy($list);
                } else {

                    $data = $this->getStoreDetails($list);
                }

            }
        }
        $this->getDi()->get('logger')->info('hc 批量审批-导出待审批 parameter -- '.$staff_info_id.'  -- '.json_encode($data));
        return $data;
    }

    /**
     * @description:拼装网点信息  my
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/4/2 14:20
     */
    public function getStoreDetailsMy($list): array
    {
        $code    = ErrCode::$SUCCESS;
        $message = '';
        $data    = [];
        try {
            if(empty($list)){
                throw new \Exception("list is  null!");
            }
        //获取职位名称
        $job_title_ids = array_values(array_unique(array_column($list, 'job_title')));
        $job_list        = HrJobTitleModel::find([
                                                     'columns'    => ['job_name', 'id'],
                                                     'conditions' => 'id in ({job_title_ids:array})',
                                                     'bind'       => ['job_title_ids' => $job_title_ids],
                                                 ])->toArray();
        $job_list =  array_column($job_list, 'job_name','id');
        //查询提取网点
        $store_ids = array_values(array_unique(array_column($list, 'worknode_id')));
        //查询网点信息
        $store_arr = SysStoreModel::find([
                                                           'conditions' => 'id in ({ids:array}) ',
                                                           'bind'       => ['ids' => $store_ids],
                                                           'columns'    => 'id,manage_piece,manage_region,name'
                                                       ])->toArray();
        $store_arr = array_column($store_arr, null, 'id');

        //获取大区
        $manage_region_arr = array_values(array_unique(array_column($store_arr, 'manage_region')));
        $region_arr= [];
        if(!empty($manage_region_arr)) {
            $region_arr = SysManageRegionModel::find([
                                                         'conditions' => ' id in ({ids:array})',
                                                         'bind'       => [
                                                             'ids' => $manage_region_arr,
                                                         ],
                                                         'columns'    => 'id,name'
                                                     ])->toArray();
            $region_arr = array_column($region_arr, 'name', 'id');
        }

        //获取片区
            $piece_arr = [];
            $manage_piece_arr = array_values(array_unique(array_column($store_arr, 'manage_piece')));
            if(!empty($manage_piece_arr)) {
                $piece_arr        = SysManagePieceModel::find([
                                                                  'conditions' => ' id in ({ids:array})',
                                                                  'bind'       => [
                                                                      'ids' => $manage_piece_arr,
                                                                  ],
                                                                  'columns'    => 'id,name'
                                                              ])->toArray();
                $piece_arr        = array_column($piece_arr, 'name', 'id');
            }
        //获取
        //Network Operations(Malaysia) 部门下
        $envModel = new SettingEnvModel();
        $networkOperationsId = $envModel->getSetVal('dept_network_operations_id');
        $sysDepList = [];
        //获取部门
        $networkOperation = SysDepartmentModel::findFirst([
                                                              'conditions' => 'id = :department_id:',
                                                              'bind' => [
                                                                  'department_id' => $networkOperationsId
                                                              ],
                                                              'columns' => 'id,name,ancestry_v3',
                                                              'order' => 'name asc'
                                                          ]);

        if($networkOperation){
            //获取子部门
            $sysDepList = SysDepartmentModel::find([
                                                       'conditions' => 'ancestry_v3 like :chain: or id = :department_id:',
                                                       'bind' => [
                                                           'chain'         => $networkOperation->ancestry_v3 . "/%",
                                                           'department_id' => $networkOperationsId
                                                       ],
                                                       'columns' => 'id,name',
                                                       'order' => 'name asc'
                                                   ])->toArray();
            $sysDepList = array_column($sysDepList,'id');
        }

        //获取网点快递员人数
        $sys_store_list =  $this->getStoreNumber($store_ids,[static::$van_courier_job_id,static::$bike_courier_job_id,static::$car_courier_job_id],$sysDepList);

        //获取van 人数
        $sys_store_van_list =  $this->getStoreNumber($store_ids,[static::$van_courier_job_id],$sysDepList);

        //获取car 人数
        $sys_store_car_list =  $this->getStoreNumber($store_ids,[static::$car_courier_job_id],$sysDepList);

        //获取bike 人数
        $sys_store_bike_list =  $this->getStoreNumber($store_ids,[static::$bike_courier_job_id],$sysDepList);
        //获取dc offer  人数
        $sys_store_dc_list =  $this->getStoreNumber($store_ids,[static::$dc_officer_job_id],$sysDepList);

        //获取=branch supervisor，assistant branch Supervisoi  人数
        $sys_store_branch_assistant_list =  $this->getStoreNumber($store_ids,[static::$branch_supervisor_job_id,static::$assistant_branch_supervisor_job_id],$sysDepList);

        //获取 van project(人）
        $sys_store_van_project_list =  $this->getVanProjectNum($store_ids);
        //获取人效
        $bi_data_list  = $this->getBiStoreAvgCount($store_ids);
        //header
        $excel_header=[
            static::$t->_('hc_batch_approve_export_hcid'),//hcid
            static::$t->_('hc_batch_approve_export_agree_or_reject').' (Agree or Reject)',//是否审批通过
            static::$t->_('hc_batch_approve_export_reject_reason'),//驳回原因
            static::$t->_('hc_batch_approve_export_region'),//大区
            static::$t->_('hc_batch_approve_export_area'),//片区
            static::$t->_('hc_batch_approve_export_store'),//网点
            static::$t->_('hc_batch_approve_export_last_week_count'),//上周-日均派件量
            static::$t->_('hc_batch_approve_export_avg_delivery_count'),//本周-日均派
            static::$t->_('hc_batch_approve_export_avg_delivery_human_effect'),//本周人均人效
            static::$t->_('hc_batch_approve_export_store_num'),//网点快递员人数
            static::$t->_('hc_batch_approve_export_van_courier_num'),//Van Courier(人）
            static::$t->_('hc_batch_approve_export_van_project_num'),//van project(人）
            static::$t->_('hc_batch_approve_export_car_courier_num'),//car project(人）
            static::$t->_('hc_batch_approve_export_bike_courier_num'),//bike project(人）
            static::$t->_('hc_batch_approve_export_dco_officer_num'),//dco_officer(人）
            static::$t->_('hc_batch_approve_export_branch_supervisor_num'),//主管（正副人）(人）
            static::$t->_('hc_batch_approve_export_job_title'),//申请的职位
            static::$t->_('hc_batch_approve_export_hc_num'),//申请人数
            static::$t->_('hc_batch_approve_export_hc_reason'),//备注
        ];
        $excel_result = [];
        foreach($list as $k=>$v){
            $excel_result[] = [
                $v['hc_id'],
                '',//是否通过
                '',//驳回原因
                $region_arr[$store_arr[$v['worknode_id']]['manage_region']] ?? '-',//大区
                $piece_arr[$store_arr[$v['worknode_id']]['manage_piece']] ?? '-',//片区
                $store_arr[$v['worknode_id']]['name'] ?? '-',//网点
                $bi_data_list[$v['worknode_id']]['last_week_avg_delivery_count'] ?? '-',//上周-日均派件量
                $bi_data_list[$v['worknode_id']]['avg_delivery_count'] ?? '-',//本周-日均派件量
                $bi_data_list[$v['worknode_id']]['avg_delivery_human_effect'] ?? '-',//本周-人效
                $sys_store_list[$v['worknode_id']] ?? '-',//网点快递员人数
                $sys_store_van_list[$v['worknode_id']] ?? '-',//Van Courier(人）
                $sys_store_van_project_list[$v['worknode_id']] ?? '-',//van project(人）
                $sys_store_car_list[$v['worknode_id']] ?? '-',//car project(人）
                $sys_store_bike_list[$v['worknode_id']] ?? '-',//bike project(人）
                $sys_store_dc_list[$v['worknode_id']] ?? '-',//dco_officer(人）
                $sys_store_branch_assistant_list[$v['worknode_id']] ?? '-',//主管（正副人）(人）
                $job_list[$v['job_title']] ?? '-',//申请的职位
                $v['demandnumber'] ?? '-',//申请人数
                static::$t->_('hc_reason_type_'.$v['reason_type']).' '.$v['reason'],//备注
            ];
        }
        //下载excle，方法返回上传阿里云后地址数组
        $file_arr         = \App\Modules\Organization\Services\BaseService::excelToFile($excel_header, $excel_result, 'oa-hc_approval_pending-' . time() . '.xlsx');
        $data['file_url'] = $file_arr['object_url'] ?? '';

        } catch (Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->getDI()->get('logger')->error('hc 批量审批-导出待审批数据-异常信息: ' . $real_message.'---------' . $e->getLine());
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];


    }


    /**
     * @description:拼装网点信息
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/4/2 14:20
     */
    public function getStoreDetails($list): array
    {
        $code    = ErrCode::$SUCCESS;
        $message = '';
        $data    = [];
        try {
            if(empty($list)){
                throw new \Exception("list is  null!");
            }
            //获取职位名称
            $job_title_ids = array_values(array_unique(array_column($list, 'job_title')));
            $job_list        = HrJobTitleModel::find([
                                                         'columns'    => ['job_name', 'id'],
                                                         'conditions' => 'id in ({job_title_ids:array})',
                                                         'bind'       => ['job_title_ids' => $job_title_ids],
                                                     ])->toArray();
            $job_list =  array_column($job_list, 'job_name','id');
            //查询提取网点
            $store_ids = array_values(array_unique(array_column($list, 'worknode_id')));
            //查询网点信息
            $store_arr = SysStoreModel::find([
                                                 'conditions' => 'id in ({ids:array}) ',
                                                 'bind'       => ['ids' => $store_ids],
                                                 'columns'    => 'id,manage_piece,manage_region,name'
                                             ])->toArray();
            $store_arr = array_column($store_arr, 'id');

            //header
            $excel_header=[
                static::$t->_('hc_batch_approve_export_hcid'),//hcid
                static::$t->_('hc_batch_approve_export_agree_or_reject').' (Agree or Reject)',//是否审批通过
                static::$t->_('hc_batch_approve_export_reject_reason'),//驳回原因
                static::$t->_('hc_batch_approve_export_store'),//网点
                static::$t->_('hc_batch_approve_export_job_title'),//申请的职位
                static::$t->_('hc_batch_approve_export_hc_num'),//申请人数
                static::$t->_('hc_batch_approve_export_hc_reason'),//备注
            ];
            $excel_result = [];
            foreach($list as $k=>$v){
                $excel_result[] = [
                    $v['hc_id'],
                    '',//是否通过
                    '',//驳回原因
                    $store_arr[$v['worknode_id']]['name'] ?? '-',//网点
                    $job_list[$v['job_title']] ?? '-',//申请的职位
                    $v['demandnumber'] ?? '-',//申请人数
                    static::$t->_('hc_reason_type_'.$v['reason_type']).' '.$v['reason'],//备注
                ];
            }
            //下载excle，方法返回上传阿里云后地址数组
            $file_arr         = \App\Modules\Organization\Services\BaseService::excelToFile($excel_header, $excel_result, 'oa-hc_approval_pending-' . time() . '.xlsx');
            $data['file_url'] = $file_arr['object_url'] ?? '';

        } catch (Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->getDI()->get('logger')->error('hc 批量审批-导出待审批数据-异常信息: ' . $real_message.'---------' . $e->getLine());
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];


    }

    /**
     * @description: 获取Network Operations(Malaysia) 部门下 相关网点人数 相关职位的人
     * @param array $store_ids  网点数组
     * @param array $job_title_ids  职位数据
     * @param array $dep_ids  部门数据
     * @return     :['sys_store_id'=>count]
     * <AUTHOR> L.J
     * @time       : 2022/4/2 16:57
     */
    public function getStoreNumber($store_ids,$job_title_ids,$dep_ids): array
    {
        if(empty($store_ids) || empty($job_title_ids) || empty($dep_ids)){
            return [];
        }


        //查找相关的人数
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('sys_store_id,count(1) as count');
        $builder->from(HrStaffInfoModel::class);
        $builder->where('formal =1 and state = 1 and is_sub_staff = 0');
        $builder->inWhere('sys_store_id',$store_ids);
        $builder->inWhere('node_department_id',$dep_ids);
        $builder->inWhere('job_title',$job_title_ids);
        $builder->groupby('sys_store_id');
        $sys_store_list = $builder->getQuery()->execute()->toArray();
        $sys_store_list = array_column($sys_store_list,'count','sys_store_id');

        return $sys_store_list;
    }
    /**
     * @description:获取van project(人）
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/4/6 12:01
     */

    public function  getVanProjectNum($store_ids){
        if(empty($store_ids)){
            return [];
        }
        //查找相关的人数
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('staff.sys_store_id,count(1) as count');
        $builder->from(['v' => VehicleInfoModel::class]);
        $builder->leftjoin(HrStaffInfoByModel::class, 'staff.staff_info_id = v.uid','staff');
        $builder->inWhere('staff.sys_store_id',$store_ids);
        //vehicle_type 1 //van     vehicle_source  = 2   //租用公司车辆
        $builder->where('staff.formal =1 and staff.state = 1 and staff.is_sub_staff = 0 and v.vehicle_type = 1 and v.vehicle_source = 2 and v.deleted = 0');
        $builder->groupby('staff.sys_store_id');
        $sys_store_list = $builder->getQuery()->execute()->toArray();
        $sys_store_list = array_column($sys_store_list,'count','sys_store_id');
        return $sys_store_list;

    }
    /**
     * @description:调用 bi
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/4/2 14:23
     */
    public function getBiStoreAvgCount($store_ids){
        //因为该接口最多 10 个网点
        $limit = 10;
        $res_data = [];
        //拆分数组
        $store_id_arr = array_chunk($store_ids,$limit);
        $ApiClient = new ApiClient('bi', '', 'dc.store_avg_count', static::$language);
        foreach($store_id_arr as $v){
            $ApiClient->setParams([$v]);
            $res = $ApiClient->execute();
            $res_data  = array_merge($res_data,$res['result']['data']?? []);
        }
        return $res_data;
    }


    /**
     * 获得结果导出模板的头，及字段
     * @return array
     */
    public function getDetailTplAndField()
    {
        $field = [
            'hc_id',//hc_id
            'approval_result',//是否审批通过
            'reason',//备注
            'state',//结果
            'error_message',//失败原因
        ];

        $excel_header = [
            static::$t->_('hc_batch_approve_export_hcid'), //hc_id
            static::$t->_('hc_batch_approve_export_agree_or_reject'), //是否审批通过
            static::$t->_('hc_batch_approve_export_reject_reason'), //备注
            static::$t->_('hc_approval_batch_approval_result'), // 审批结果
            static::$t->_('hc_approval_batch_error_message'), //审批失败原因
        ];
        return ['header' => $excel_header, 'field' => $field];
    }



	
}