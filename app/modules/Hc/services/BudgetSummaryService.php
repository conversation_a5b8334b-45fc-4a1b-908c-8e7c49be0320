<?php

namespace App\Modules\Hc\Services;


use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\OssHelper;
use App\Models\backyard\HrHcSummaryShareGroupTblModel;
use App\Modules\Hc\Models\HrHcSummaryVersionTblModel;
use App\Modules\Hc\Models\HrHcSummaryDataTblModel;
use App\Modules\Hc\Models\HrJobTitleModel;
use App\Modules\Hc\Models\SysDepartmentModel;
use App\Modules\Hc\Models\SysStoreModel;
use App\Modules\Hc\Models\HrStaffInfoModel;
use App\Modules\Transfer\Models\SettingEnvModel;
use App\Modules\User\Models\BiStaffInfoPositionModel;
use App\Repository\StaffDepartmentAreasStoreRepository;

class BudgetSummaryService extends BaseService
{
	private static $instance;
	public         $version_state_0 = 0;                               // 0 非完整数据
	public         $version_state_1 = 1;                               // 1 成熟数据
	public         $version_state_2 = 2;                               // 2  丢弃数据
	public         $task_state_0    = 0;                               //待执行的数据
	public         $task_state_1    = 1;                               //执行完的数据
	public         $data_type_1     = 1;                               // 1 普通数据 2 总数 3 共享数据',
	public         $data_type_2     = 2;                               // 1 普通数据 2 总数 3 共享数据',
	public         $data_type_3     = 3;                               // 1 普通数据 2 总数 3 共享数据',
	public         $redis_key       = 'budget_summary_add_staff_info_new';//关联 id 的缓存,用于判断脚本是否在执行中  没有关联 id  用于防止点击缓存
	public        $redis_cache_list_time = 60*60;//设置列表权限缓存时间; 1 小时


    public static $validate_search_param = [
        'pageSize'              => 'IntGt:0',
        'pageNum'               => 'IntGt:0',
        'subsidiary_department' => 'Int',
    ];
	
	/**
	 * @return BudgetService
	 */
	public static function getInstance()
	{
		if (!isset(self::$instance) || !(self::$instance instanceof self)) {
			self::$instance = new self;
		}
		return self::$instance;
	}
	
	/**
	 * @description: 获取最新的版本号
	 *
	 * @param null
	 *
	 * @return     :
	 * <AUTHOR> L.J
	 * @time       : 2021/11/8 20:26
	 */
	public function getNewestVersion()
	{
		$HrHcSummaryVersionTblModel = HrHcSummaryVersionTblModel::findFirst([
			                                                                    'conditions' => 'state = ?1',
			                                                                    'bind'       => [
				                                                                    '1' => $this->version_state_1,
			                                                                    ],
			                                                                    'order'      => 'id desc',
		                                                                    ]);
		if (empty($HrHcSummaryVersionTblModel)) {
			return ['id' => 0, 'version_date' => ''];
		}
		$result                 = $HrHcSummaryVersionTblModel->toArray();
		$result['version_date'] = date('Y-m-d H:i:s', strtotime($result['updated_at']) + get_sys_time_offset() * 3600);//版本时间
		return $result;
	}
	
	/**
	 * @description: 查询部门下 hc 预算数量
	 *
	 * @param null
	 *
	 * @return     :
	 * <AUTHOR> L.J
	 * @time       : 2021/11/8 20:25
	 */
	
	public function getDepartmentSummary()
	{
		//获取版本号
		$version_id = $this->getNewestVersion();
		//查询根据部门为维度的 hc 预算数量
		$list = HrHcSummaryDataTblModel::find([
			                                      'conditions' => 'version_id = :version_id:',
			                                      'bind'       => ['version_id' => $version_id['id']],
			                                      'columns'    => ['sum(budget_real) AS budget_real_sum', 'department_id'],
			                                      'group'      => ['department_id'],
		                                      ])->toArray();
		$list = array_column($list, null, 'department_id');
		return $list;
	}
	
	
	/**
	 * @description: 获取 列表数据
	 *
	 * @param null
	 *
	 * @return     :
	 * <AUTHOR> L.J
	 * @time       : 2021/11/9 13:38
	 */
	public function getList($param, $staff_info_id)
	{
        $department_id         = $param['department_id'] ?? 0;                               //部门
        $job_title_id          = $param['job_title_id'] ?? 0;                                //职位
        $store_id              = $param['store_id'] ?? '';                                   //网点
        $subsidiary_department = $param['subsidiary_department'] ?? 0;                       //是否包含子部门 1 是
        $pageSize              = empty($param['pageSize']) ? 20 : $param['pageSize'];        //条数
        $pageNum               = empty($param['pageNum']) ? 1 : $param['pageNum'];           //页数
        $hcPlanState           = $param['hc_budget_state'] ?? 0;                             //HC状态 0全部 1未超预算 2超过预算
        $start                 = ($pageNum - 1) * $pageSize;

        $department_ids = [];
        if ($subsidiary_department == 1 && !empty($department_id)) {
            //获取子部门
            $department_id_list = SysService::getInstance()->getNodeDeptList($department_id, false);
            $department_ids     = array_column($department_id_list, 'id');
        }

        //获取列表
        //第一步获取当前最新的版本号
        $version    = $this->getNewestVersion();
        $version_id = $version['id'];


        //获取列表数据
        $bind       = ['type' => [$this->data_type_1, $this->data_type_3], 'version_id' => $version_id];
        $conditions = ' version_id = :version_id: and  type in ({type:array})  ';
        if (!empty($department_id)) {
            $department_ids[]      = $department_id;
            $bind['department_id'] = $department_ids;
            $conditions            .= ' and department_id in ({department_id:array}) ';
        }
        if (!empty($job_title_id)) {
            $bind['job_title_id'] = explode(',', $job_title_id);
            $conditions           .= ' and job_title_id in ({job_title_id:array}) ';
        }
        if (!empty($store_id)) {
            $bind['store_id'] = explode(',', $store_id);
            $conditions       .= "  and store_id in ({store_id:array}) ";
        }

        if (!empty($hcPlanState)) {
            //数据库中状态 HC预算状态 1=未超预算 2=超过预算
            $bind['hc_budget_state'] = $hcPlanState == HrHcSummaryDataTblModel::HC_BUDGET_NOT_OVERRUN ? HrHcSummaryDataTblModel::HC_BUDGET_NOT_OVERRUN : HrHcSummaryDataTblModel::HC_BUDGET_OVERRUN;
            $conditions              .= "  and hc_budget_state = :hc_budget_state: ";
        }

        //获取数据权限
        $list_rang  = $this->getListJurisdiction($staff_info_id);
        $conditions .= $list_rang['conditions'];

        $bind = array_merge($bind, $list_rang['bind']);


        $list = HrHcSummaryDataTblModel::find([
            'conditions' => $conditions,
            'bind'       => $bind,
            'offset'     => $start,
            'limit'      => $pageSize,
        ])->toArray();
        //格式化内容
        $list = $this->buildDataList($list);
        //查询数据总量
        $count = HrHcSummaryDataTblModel::count([
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);
        //获取汇总数据
        $getSummaryData = $this->getSummaryData($conditions, $bind);


        return [
            'items'        => $list,
            'summary_data' => $getSummaryData,
            'version_date' => $version['version_date'],
            'pagination'   => [
                'current_page' => $pageNum,
                'per_page'     => $pageSize,
                'total_count'  => $count,
            ],
        ];
		
	}
	
	
	/**
	 * @description:添加更新数据  这里有 30 秒缓存
	 *
	 * @param int $staff_info_id 用户 id
	 *
	 * @return     :
	 * <AUTHOR> L.J
	 * @time       : 2021/11/9 18:56
	 */
	public function addSummary($staff_info_id)
	{
		
		$result = ['code' => ErrCode::$SUCCESS, 'message' => static::$t->_('hr_hc_summary_succeeded'), 'data' => ['version_id' => 0]];//1 有待执行的数据
		//这里先获取缓存
		$cache = $this->getCache($this->redis_key);
		if (!empty($cache)) {
            $result['data']['version_id'] = $cache;
			return $result;
		}
		//获取列表是否有没有执行完毕的数据 如果有直接返回版本号
		$HrHcSummaryVersionTblModel = HrHcSummaryVersionTblModel::findFirst([
			                                                                    'conditions' => 'task_state = ?1',
			                                                                    'bind'       => [
				                                                                    '1' => $this->task_state_0,
			                                                                    ],
			                                                                    'columns'    => ['id'],
		                                                                    ]);
		if ($HrHcSummaryVersionTblModel) {
			$result['data']['version_id'] = $HrHcSummaryVersionTblModel->id;
			//这里设置缓存防止重复点击
			$this->setLock($this->redis_key, $HrHcSummaryVersionTblModel->id, 30);
			return $result;
		}
		//可以插入一条
		$HrHcSummaryVersionTblModel                = new HrHcSummaryVersionTblModel();
		$HrHcSummaryVersionTblModel->version       = gmdate("YmdHis", time());
		$HrHcSummaryVersionTblModel->state         = $this->version_state_0;
		$HrHcSummaryVersionTblModel->staff_info_id = $staff_info_id;
		$HrHcSummaryVersionTblModel->task_state    = $this->task_state_0;
		$HrHcSummaryVersionTblModel->created_at    = gmdate("Y-m-d H:i:s", time());
		$data                                      = $HrHcSummaryVersionTblModel->save();
		if ($data) {
			$result = ['code' => ErrCode::$SUCCESS, 'message' => static::$t->_('hr_hc_summary_succeeded'), 'data' => ['version_id' => $HrHcSummaryVersionTblModel->id]];//插入成功
			$this->isStaffInfoHeartbeat($HrHcSummaryVersionTblModel->id, 2);                                                                                            //2 跟新缓存   1 执行中
		}
		//这里设置缓存防止重复点击
		$this->setLock($this->redis_key, $HrHcSummaryVersionTblModel->id, 30);
		return $result;
	}
	
	/**
	 * @description:缓存
	 *
	 * @param $version_id  版本 id
	 * @param $type        1 查询缓存  2 更新缓存 3 删除缓存
	 * @param $val        1 执行完成  2 执行中
	 *
	 * @return     :  1 点击了更新按钮  0 没有点击更新
	 * <AUTHOR> L.J
	 * @time       : 2021/11/9 19:35
	 */
	public function isStaffInfoHeartbeat($version_id = '', $type = 1, $val = 2, $time = 30)
	{
		$redis_key = $this->redis_key . $version_id; //把查询结果缓存 30 秒
		if ($type == 1) {
			return $this->getCache($redis_key);
		}
		if ($type == 2) {
			$this->setLock($redis_key, $val, $time); //缓存
		}
		if ($type == 3) {
			$this->unLock($redis_key);//释放
		}
		return 1;
	}
	
	
	/**
	 * @description: 检测用户点击更新数据按钮 数据是否更新完毕
	 *
	 * @param $version_id  版本 id
	 *
	 * @return     : 1 完成   2 执行中
	 * <AUTHOR> L.J
	 * @time       : 2021/11/9 19:37
	 */
	
	public function getHeartbeatSummary($version_id)
	{
		$cache = $this->isStaffInfoHeartbeat($version_id); //先获取缓存结果
		
		if (!empty($cache)) {  //如果缓存存在 获取缓存数据  缓存 ==1 是执行完成   缓存 == 2 是执行中
			return ['code' => $cache];
		}
		//查询真实的更新数据
		$HrHcSummaryVersionTblModel = HrHcSummaryVersionTblModel::findFirst([
			                                                                    'conditions' => 'id= ?2',
			                                                                    'bind'       => [
				                                                                    '2' => $version_id,
			                                                                    ],
			                                                                    'columns'    => ['id', 'task_state'],
		                                                                    ]);
		
		if (!empty($HrHcSummaryVersionTblModel) && $HrHcSummaryVersionTblModel->task_state == 0) {
			$result = ['code' => '2'];                   //还在执行当中
			$this->isStaffInfoHeartbeat($version_id, 2,2); //缓存 2 是执行中
		} else {
			$result = ['code' => '1'];                      //执行完成
			$this->isStaffInfoHeartbeat($version_id, 2, 1); //缓存 1 是执行完成
		}
		return $result;
		
	}
	
	
	/**
	 * @description:封装列表数据
	 *
	 * @param array $list
	 *
	 * @return     :
	 * <AUTHOR> L.J
	 * @time       : 2021/11/9 16:19
	 */
	public function buildDataList($list = [])
	{
		if (empty($list)) {
			return $list;
		}
        //获取列表内的网点
        $store_id_map = [];
        $store_ids    = array_column($list, 'store_id');
        $store_list   = SysStoreModel::find([
            'columns'    => ['name', 'id'],
            'conditions' => 'id in ({store_ids:array})',
            'bind'       => ['store_ids' => $store_ids],
        ])->toArray();
        if (!empty($store_list)) {
            $store_id_map = array_column($store_list, 'name', 'id');
        }
        $store_id_map['-1'] = 'Head Office';
		
		//获取列表内职位
		$job_id_name_map = [];
		$job_title_ids   = array_column($list, 'job_title_id');

        //获取 HC预算配置 service
        $shareService     = HcShareBudgetService::getInstance()->init();
        $allShareJobTitle = $shareService->getAllShareJobTitle();
        $job_title_ids    = array_merge($job_title_ids, $allShareJobTitle);
        $job_list = HrJobTitleModel::find([
            'columns'    => ['job_name', 'id'],
            'conditions' => 'id in ({job_title_ids:array})',
            'bind'       => ['job_title_ids' => $job_title_ids],
        ])->toArray();
		if (!empty($job_list)) {
			$job_id_name_map = array_column($job_list, 'job_name', 'id');
		}
		//获取列表内部门
        $allShareDepartment = $shareService->getAllShareDepartment();

		$department_id_name_map = [];
		$department_ids         = array_column($list, 'department_id');
        $department_ids         = array_merge($department_ids, $allShareDepartment);
		$department_list        = SysDepartmentModel::find(
			[
				'columns'    => ['id', 'name'],
				'conditions' => 'id in ({department_ids:array})',
				'bind'       => ['department_ids' => $department_ids],
			]
		)->toArray();
		if (!empty($department_list)) {
			$department_id_name_map = array_column($department_list, 'name', 'id');
		}

        //获取计划 hc
        $dept_job_title = SysService::getInstance()->getDeptJobTitleConfig($department_ids);

        $stateList = SysService::getInstance()->getState();
        $stateList = array_column($stateList, 'title', 'code');

        if (BudgetService::getInstance()->isConfiguredBudget()) {
            $flag = true;
        } else {
            $flag = false;
        }
		
		foreach ($list as $k => $v) {
            $list[$k]['department_name']     = $department_id_name_map[$v['department_id']] ?? ''; //部门名称
            $list[$k]['job_title_name']      = $job_id_name_map[$v['job_title_id']] ?? ''; //职位名称
            $list[$k]['store_name']          = $v['type'] == $this->data_type_3
                ? static::$t->_('share_store_text')
                : ($store_id_map[$v['store_id']] ?? '');                    //网点名称

            if ($flag) {
                $list[$k]['share_position_list'] = $v['type'] == $this->data_type_3 && !empty($v['share_job_title_ids']) && !empty($v['share_department_ids'])
                    ? $this->resolveShareDepartmentAndJobTitle($v['share_job_title_ids'], $job_id_name_map, $v['share_department_ids'], $department_id_name_map)
                    : ''; //共享职位名称
            } else {
                $list[$k]['share_position_list'] = $v['type'] == $this->data_type_3 && !empty($v['share_job_title_ids'])
                    ? $this->resolveShareJobTitle($v['share_job_title_ids'], $job_id_name_map)
                    : [];  //共享职位名称
            }

            $list[$k]['hc_budget_state_name'] = $stateList[$v['hc_budget_state']] ?? '';

            $uniqueKey = $v['department_id'] . '_' . $v['job_title_id'];
            if (isset($dept_job_title[$uniqueKey])) {
                $isPlan = $dept_job_title[$uniqueKey]['is_plan'];
            } else {
                $isPlan = false;
            }

            if ($isPlan) {
                $list[$k]['plan_hc_nums'] = $v['hc_plan_num'] ?? 0;
            } else {
                $list[$k]['plan_hc_nums'] = '';
            }
            unset($list[$k]['hc_plan_num']);
		}
		
		return $list;
	}

    /**
     * 解析共享
     * @param $share_job_title_ids
     * @param $share_job_title
     * @param $share_department_ids
     * @param $share_department
     * @return array
     */
    private function resolveShareDepartmentAndJobTitle($share_job_title_ids, $share_job_title, $share_department_ids, $share_department): array
    {
        $result             = [];
        $shareDepartmentIds = explode(',', $share_department_ids);
        $shareJobTitleIds   = explode(',', $share_job_title_ids);
        foreach ($shareDepartmentIds as $shareDepartmentId) {
            $result[] = [
                'department_name' => $share_department[$shareDepartmentId] ?? '',
                'position_name'   => array_map(function ($position) use ($share_job_title) {
                    return $share_job_title[$position] ?: '';
                }, $shareJobTitleIds),
            ];
        }
        return $result;
    }

    /**
     * 解析共享职位
     * @param $share_job_title_ids
     * @param array $share_job_title
     * @return array|string[]
     */
    private function resolveShareJobTitle($share_job_title_ids, array $share_job_title): array
    {
        $shareJobTitleIds = explode(',', $share_job_title_ids);
        return array_map(function ($position) use ($share_job_title) {
            return $share_job_title[$position] ?: '';
        }, $shareJobTitleIds);
    }


    public function getSummaryData($conditions, $bind)
    {
        if (BudgetService::getInstance()->isConfiguredBudget()) {
            return $this->getSummaryDataV2($conditions, $bind);
        } else {
            return $this->getSummaryDataV1($conditions, $bind);
        }
	}

    /**
     * @description: 获取汇总数据
     * @param $conditions
     * @param $bind
     */
    public function getSummaryDataV1($conditions, $bind)
    {
        //获取汇总的一条数据
        $data = HrHcSummaryDataTblModel::findFirst([
            'conditions' => $conditions,
            'bind'       => $bind,
            'columns'    => [
                'sum(budget_real) AS budget',
                'sum(employee_in_service) AS employee_in_service',
                'sum(hc_pending) AS hc_pending',
                'sum(hc_demand) AS hc_demand',
                'sum(hc_demand_wait) as hc_demand_wait',
                'sum(hc_request_real) AS hc_request',
                'sum(hc_plan_num_for_sum) AS hc_plan_num_for_sum',
            ],
        ]);
        if ($data) {
            $data = $data->toArray();
        } else {
            $getSummaryData['department_name']      = '';
            $getSummaryData['job_title_name']       = '';
            $getSummaryData['store_name']           = '';
            $getSummaryData['budget']               = 0;
            $getSummaryData['employee_in_service']  = 0;
            $getSummaryData['hc_pending']           = 0;
            $getSummaryData['hc_demand']            = 0;
            $getSummaryData['hc_demand_wait']       = 0;
            $getSummaryData['hc_request']           = 0;
            $getSummaryData['type']                 = 0;
            $getSummaryData['plan_hc_nums']         = 0;
            $getSummaryData['hc_budget_state_name'] = '';
            return $getSummaryData;
        }
        $getSummaryData['department_name']     = '';
        $getSummaryData['job_title_name']      = '';
        $getSummaryData['store_name']          = '';
        $getSummaryData['budget']              = $data['budget'] ?? 0;
        $getSummaryData['employee_in_service'] = $data['employee_in_service'] ?? 0;
        $getSummaryData['hc_pending']          = $data['hc_pending'] ?? 0;
        $getSummaryData['hc_demand']           = $data['hc_demand'] ?? 0;
        $getSummaryData['hc_demand_wait']      = $data['hc_demand_wait'] ?? 0;
        $getSummaryData['hc_request']          = $data['hc_request'] ?? 0;
        $getSummaryData['type']                = $this->data_type_2;
        $getSummaryData['plan_hc_nums']        = $data['hc_plan_num_for_sum'] ?? 0;
        $getSummaryData['hc_budget_state_name']= '';//计划 hc
        return $getSummaryData;
	}

	/**
	 * @description:获取汇总数据
	 *
	 * @param int $version_id 版本号
	 *
	 * @return     :
	 * <AUTHOR> L.J
	 * @time       : 2021/11/9 15:24
	 */
	public function getSummaryDataV2($conditions, $bind)
	{
        //获取共享数据统计
        $shareGroupDetail = $this->shareGroupDetail($conditions, $bind);

        //获取汇总的一条数据
        $data = HrHcSummaryDataTblModel::findFirst([
            'conditions' => $conditions,
            'bind'       => $bind,
            'columns'    => [
                'sum(budget_real) AS budget',
                'sum(employee_in_service) AS employee_in_service',
                'sum(hc_pending) AS hc_pending',
                'sum(hc_demand) AS hc_demand',
                'sum(hc_demand_wait) as hc_demand_wait',
                'sum(hc_request_real) AS hc_request',
                'sum(hc_plan_num_for_sum) AS hc_plan_num_for_sum',
                'sum(employee_suspend) AS employee_suspend',
                'sum(employee_wait_leave) AS employee_wait_leave',
                'sum(job_trans_entry) AS job_trans_entry',
            ],
        ]);
        if ($data) {
            $data = $data->toArray();
            if (!empty($shareGroupDetail)) {
                $data['budget']     += array_sum(array_column($shareGroupDetail, 'budget_real'));
                $data['hc_request'] += array_sum(array_column($shareGroupDetail, 'hc_request_real'));
            }
        } else {
            $getSummaryData['department_name']      = '';
            $getSummaryData['job_title_name']       = '';
            $getSummaryData['store_name']           = '';
            $getSummaryData['budget']               = 0;
            $getSummaryData['employee_in_service']  = 0;
            $getSummaryData['hc_pending']           = 0;
            $getSummaryData['hc_demand']            = 0;
            $getSummaryData['hc_demand_wait']       = 0;
            $getSummaryData['hc_request']           = 0;
            $getSummaryData['type']                 = 0;
            $getSummaryData['plan_hc_nums']         = 0;
            $getSummaryData['hc_budget_state_name'] = '';
            $getSummaryData['employee_suspend']     = 0;
            $getSummaryData['employee_wait_leave']  = 0;
            $getSummaryData['job_trans_entry']      = 0;
            return $getSummaryData;
        }
        $getSummaryData['department_name']     = '';
        $getSummaryData['job_title_name']      = '';
        $getSummaryData['store_name']          = '';
        $getSummaryData['budget']              = $data['budget'] ?? 0;
        $getSummaryData['employee_in_service'] = $data['employee_in_service'] ?? 0;
        $getSummaryData['hc_pending']          = $data['hc_pending'] ?? 0;
        $getSummaryData['hc_demand']           = $data['hc_demand'] ?? 0;
        $getSummaryData['hc_demand_wait']      = $data['hc_demand_wait'] ?? 0;
        $getSummaryData['hc_request']          = $data['hc_request'] ?? 0;
        $getSummaryData['type']                = $this->data_type_2;
        $getSummaryData['plan_hc_nums']        = $data['hc_plan_num_for_sum'] ?? 0;
        $getSummaryData['hc_budget_state_name']= '';//计划 hc
        $getSummaryData['employee_suspend']    = $data['employee_suspend'] ?? 0;
        $getSummaryData['employee_wait_leave'] = $data['employee_wait_leave'] ?? 0;
        $getSummaryData['job_trans_entry']     = $data['job_trans_entry'] ?? 0;
        return $getSummaryData;
	}
	
	
	/**
	 * @description:获取权限
	 *
	 * @param null
	 *
	 * @return     :
	 * <AUTHOR> L.J
	 * @time       : 2021/11/9 20:56
	 */
	public function getListJurisdiction($staff_info_id)
	{
		$redis               = $this->getDI()->get("redis");
		$redis_key = $this->redis_key.'list_range_'.$staff_info_id;

		try {
			
			$result     = ['conditions' => '', 'bind' => []];
			$conditions = '';
			$envModel   = new SettingEnvModel();
			//判断是否能查看全部权限
			//获取配置的部门
			$env_departments = explode(',', $envModel->getSetVal('hc_budget_summary_deps'));
			//获取配置的工号
			$env_staffs = explode(',', $envModel->getSetVal('hc_budget_summary_staffs'));
			//获取配置的角色
			$env_roles = explode(',', $envModel->getSetVal('hc_budget_summary_roles'));
			//获取用户角色
			$role_ids = BiStaffInfoPositionModel::getRoleIds($staff_info_id);
			//获取用户本身部门
			$staff_info_node_department    = HrStaffInfoModel::getUserInfo($staff_info_id, 'sys_department_id,node_department_id');
			$staff_info_node_department_id = empty($staff_info_node_department['node_department_id']) ? 0 : $staff_info_node_department['node_department_id'];
		
			//如果本身角色在角色配置里面  或者  工号在配置里面 或者 部门在配置里面  可以看到全部数据
			if (!empty(array_intersect($env_roles, $role_ids)) || in_array($staff_info_id, $env_staffs) || (in_array($staff_info_node_department_id, $env_departments) && !empty($staff_info_node_department_id))) {
				//这里可以看全部数据
                $this->logger->info('hc 预算汇总 获取列表权限 - 在配置文件里 可以获得全部数据 返回参数: ' . json_encode($result, JSON_UNESCAPED_UNICODE));
				return $result;
			}


			//这里先读缓存
			$cache                = $redis->get($redis_key);
			if($cache){
                $this->logger->info('hc 预算汇总 获取列表权限 - 读的缓存数据 返回参数: key'.$redis_key.' 内容' . $cache);
				return json_decode($cache,true);
			}

			//获取配置管辖范围的角色   获取
			$env_range_roles = explode(',', $envModel->getSetVal('hc_budget_summary_range_roles'));
			if (!empty(array_intersect($env_range_roles, $role_ids))) {
				//查询管辖范围内的网点和部门
                $areas_range = (new StaffDepartmentAreasStoreRepository())->getStaffManageDepartmentsAreasStores($staff_info_id);

                //这里是管辖部门权限
				$bind['range_department_ids']      = empty($areas_range['department_ids']) ? ['0'] : array_values($areas_range['department_ids']);
				$bind['range_department_store_id'] = ['', '-1'];
				$conditions                        .= ' ( department_id in ({range_department_ids:array}) and store_id in ({range_department_store_id:array}) )';
				// -2 代表全部网点  所以能看到全部数据(不包含 -1)
				if (!in_array('-2', $areas_range['stores_ids'])) {
					if(!empty($areas_range['stores_ids'])){
						$bind['range_store_id'] = array_values($areas_range['stores_ids']);
						$conditions             .= ' or  store_id in ({range_store_id:array}) ';
					}
				} else {
					$conditions .= " or  store_id  != '-1' ";
				}
			}

            $env_common_staffs_str = $envModel->getSetVal('hc_budget_common_staffs');
            $env_common_staffs     = $env_common_staffs_str ? explode(',', $env_common_staffs_str) : [];

			if (in_array($staff_info_id, $env_common_staffs)) {
                //这里获取 自己的一级部门 和自己管辖部门的数据范围
                $department_ids[] = $staff_info_department_ids[] = empty($staff_info_node_department['sys_department_id']) ? 0 : $staff_info_node_department['sys_department_id'];


                //查询所属一级部门的  如果 999 在这里 则获取所有数据
                if (in_array(GlobalEnums::TOP_DEPARTMENT_ID, $staff_info_department_ids)) {
                    //可以查询所有的部门
                    $this->logger->info('hc 预算汇总 获取列表权限 - 部门 999 可以获得全部数据 返回参数: ' . json_encode($result, JSON_UNESCAPED_UNICODE));
                    return $result;
                }
            } else {
                $department_ids[] = $staff_info_department_ids[] = 0;
            }

			//判断当前人是不是部门负责人或者助理
			$deptListArr = SysDepartmentModel::find([
				                                        'conditions' => '(manager_id = :manager_id: or assistant_id = :assistant_id:) and deleted = 0',
				                                        'columns'    => ['ancestry_v3', 'id'],
				                                        'bind'       => [
					                                        'manager_id'   => $staff_info_id,
					                                        'assistant_id' => $staff_info_id,
				                                        ],
			                                        ])->toArray();

			if (!empty($deptListArr)) {
				$ancestry_v3=[];
				//获取负责的一级部门
				foreach ($deptListArr as $k => $v) {
					//查找所有的父链 id
					$ancestry_v3_data                 = $v['ancestry_v3'] ? explode('/', $v['ancestry_v3']) : [];
					$ancestry_v3 = array_merge($ancestry_v3,$ancestry_v3_data);
					$staff_info_department_ids[] = $v['id'];//把本部门塞进去 能看到本部门的数据
				}
				//查询出一级部门
				$ancestry_v3 = array_values(array_unique($ancestry_v3));
				$deptListArrList = SysDepartmentModel::find([
					                                        'conditions' => ' id in ({ids:array}) and level = 1 ',
					                                        'columns'    => ['ancestry_v3', 'id'],
					                                        'bind'       => [
						                                        'ids'   => $ancestry_v3,
					                                        ],
				                                        ])->toArray();
				$deptListArrList = array_column($deptListArrList, 'id');
				$staff_info_department_ids= array_merge($staff_info_department_ids,$deptListArrList);
				
			}

			$staff_info_department_ids = array_unique($staff_info_department_ids);
			
			//获取所能看到一级部门的所有子部门
			foreach ($staff_info_department_ids as $k => $v) {
				if (empty($v)) {
					continue;
				}
				$department_id_list = SysService::getInstance()->getNodeDeptList($v, false);
				$department_ids     = array_merge($department_ids, array_column($department_id_list, 'id'));
			}
			$department_ids                    = array_unique($department_ids);
			$conditions                        = !empty($conditions) ? $conditions . ' or ' : '';
			$conditions                        .= ' department_id in ({staff_info_department_ids:array}) ';
			$bind['staff_info_department_ids'] = array_values($department_ids);
			
			
			$conditions = ' and ( ' . $conditions . ')';

            $result = ['conditions' => $conditions ?? '', 'bind' => $bind ?? []];

            $this->logger->info('hc 预算汇总 获取列表权限 - 返回参数: ' . json_encode($result, JSON_UNESCAPED_UNICODE));

            if (get_runtime_env() != 'pro') {
                $this->redis_cache_list_time = 3;
            }

            //这里设置缓存
            $redis->setex($redis_key, $this->redis_cache_list_time, json_encode($result,JSON_UNESCAPED_UNICODE));

		} catch (\Exception $e) {
            $result = ['conditions' => 'and 1=2 ', 'bind' =>  []];  //设置一个不能完成的条件
            $this->logger->error('hc 预算汇总 获取列表权限 getListJurisdiction----结果[Exception]' . $e->getMessage() . '---------' . $e->getLine() . ' staff_info_id ==> ' . $staff_info_id);
		}

		
		return $result;
	}

    public function exportExcelList($params, $staff_info_id)
    {
        if (BudgetService::getInstance()->isConfiguredBudget()) {
            return $this->exportExcelListV2($params, $staff_info_id);
        } else {
            return $this->exportExcelListV1($params, $staff_info_id);
        }
    }

    /**
     * @description:导出
     *
     * @param null
     *
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2021/11/10 19:26
     */

    public function exportExcelListV1($params, $staff_info_id)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];

        try {
            //获取列表
            $params['pageNum']  = 1;
            $params['pageSize'] = 20000;
            $list               = $this->getList($params, $staff_info_id);

            //获取部门列表map
            $department_list = SysDepartmentModel::find([
                'columns' => "id,name,ancestry,ancestry_v3,type,level,company_id,company_name",
            ])->toArray();
            if (!empty($department_list)) {
                $department_list = array_column($department_list, null, 'id');
            }

            $excel_result[] = [
                '',//列汇总
                static::$t->_('hc_summary_department'), //所属部门
                static::$t->_('department.group_ceo'),
                static::$t->_('department.c_level'),
                static::$t->_('department.bu'),
                static::$t->_('department.level_1'),
                static::$t->_('department.level_2'),
                static::$t->_('department.level_3'),
                static::$t->_('department.level_4'),
                static::$t->_('job_name'), //职位名称
                static::$t->_('hc_summary_store_text'), // 工作地点
                static::$t->_('hc_summary_budget'), //预算汇总
                static::$t->_('hc_summary_employee_in_service'), // 在职人数
                static::$t->_('hc_summary_hc_pending'), // 待入职人数
                static::$t->_('hc_summary_hc_demand'), // 招聘中人数
                static::$t->_('hc_summary_hc_demand_wait'), // 已提交 hc 数量
                static::$t->_('hc_summary_hc_request'), // 剩余可提交 hc 数量
            ];
            $excel_result[]     = [
                static::$t->_('hc_summary_column'), //列汇总
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                $list['summary_data']['department_name']??'',//所属部门
                $list['summary_data']['job_title_name'] ?? '',//职位名称
                $list['summary_data']['store_name'] ?? '',//工作地点
                $list['summary_data']['budget'] ?? '',//预算汇总
                $list['summary_data']['employee_in_service'] ?? '',//在职人数
                $list['summary_data']['hc_pending'] ?? '',//待入职人数
                $list['summary_data']['hc_demand'] ?? '',//招聘中人数
                $list['summary_data']['hc_demand_wait'] ?? '',//已提交 hc 数量
                $list['summary_data']['hc_request'] ?? '',//剩余可提交 hc 数量
            ];
            foreach ($list['items'] as $k => $v) {

                $department_level = (new \App\Modules\Organization\Services\BaseService())->getDepartmentLevel($v['department_id'], $department_list);
                $excel_result[] = [
                    '',//列汇总
                    $v['department_name'],//部门
                    $department_level[0] ?? '-',
                    $department_level[1] ?? '-',
                    $department_level[2] ?? '-',
                    $department_level[3] ?? '-',
                    $department_level[4] ?? '-',
                    $department_level[5] ?? '-',
                    $department_level[6] ?? '-',
                    $v['job_title_name'],//职位
                    $v['store_name'],//网点
                    $v['budget'],//预算总人数
                    $v['employee_in_service'],//在职人数
                    $v['hc_pending'],//待入职
                    $v['hc_demand'],//招聘中
                    $v['hc_demand_wait'],//已提交 hc
                    $v['hc_request'],//剩余hc
                ];

            }
            //todo excel导出处理
            $excel_header = [
                static::$t->_('hc_summary_hc_update_time'), // 更新时间
                $list['version_date']??'',//更新时间,
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                '',
            ];
            //下载excle，方法返回上传阿里云后地址数组
            $file = BaseService::excelToFile($excel_header, $excel_result, 'oa-hc_summary-' . time() . '.xlsx');
            $file_arr         = OssHelper::uploadFile($file);
            $data['file_url'] = $file_arr ? $file_arr['object_url'] : '';
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->getDI()->get('logger')->error('hc 预算汇总-exportExcelList-异常信息: ' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

	/**
	 * @description:导出
	 *
	 * @param null
	 *
	 * @return     :
	 * <AUTHOR> L.J
	 * @time       : 2021/11/10 19:26
	 */
	
	public function exportExcelListV2($params, $staff_info_id)
	{
		$code    = ErrCode::$SUCCESS;
		$message = $real_message = '';
		$data    = [];
		
		try {
			if (in_array(get_country_code() , [GlobalEnums::TH_COUNTRY_CODE, GlobalEnums::MY_COUNTRY_CODE, GlobalEnums::PH_COUNTRY_CODE])) {
                [$excel_header, $excel_result] = $this->generateExcelTh($params, $staff_info_id);
            } else {
                [$excel_header, $excel_result] = $this->generateExcelOther($params, $staff_info_id);
            }
			//下载excle，方法返回上传阿里云后地址数组
			$file             = BaseService::excelToFile($excel_header, $excel_result, 'oa-hc_summary-' . time() . '.xlsx');
            $file_arr         = OssHelper::uploadFile($file);
			$data['file_url'] = $file_arr['object_url'] ?? '';
		} catch (\Exception $e) {
			$code         = ErrCode::$SYSTEM_ERROR;
			$message      = static::$t->_('retry_later');
			$real_message = $e->getMessage();
			$this->getDI()->get('logger')->error('hc 预算汇总-exportExcelList-异常信息: ' . $real_message);
		}
		return [
			'code'    => $code,
			'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
			'data'    => $data,
		];
	}

    public function generateExcelTh($params, $staff_info_id)
    {
        //获取列表
        $params['pageNum']  = 1;
        $params['pageSize'] = 20000;
        $list               = $this->getList($params, $staff_info_id);
        $excel_result[] = [
            '',                                                 //列汇总
            static::$t->_('hc_summary_department'),             //所属部门
            static::$t->_('department.group_ceo'),              //Group CEO
            static::$t->_('department.c_level'),                //C Level
            static::$t->_('department.bu'),                     //BU
            static::$t->_('department.level_1'),                //一级部门
            static::$t->_('department.level_2'),                //二级部门
            static::$t->_('department.level_3'),                //三级部门
            static::$t->_('department.level_4'),                //四级部门
            static::$t->_('job_name'),                          //职位名称
            static::$t->_('hc_summary_budget.budget_state'),    //HC预算状态
            static::$t->_('hc_summary_budget.budget_num'),      //HC总数
            static::$t->_('hc_summary_employee_in_service'),    // 在职人数
            static::$t->_('hc_summary_hc_request'),             // 剩余可提交 hc 数量
            static::$t->_('hc_summary_hc_demand_wait'),         // 已提交 hc 数量
            static::$t->_('hc_summary_hc_demand'),              // 招聘中人数
            static::$t->_('hc_summary_hc_pending'),             // 待入职人数
            static::$t->_('hc_summary_budget.suspend_num'),     // 停职人数
            static::$t->_('hc_summary_budget.wait_leave_num'),  // 待离职职人数
            static::$t->_('hc_summary_budget.pending_entry'),   // 待转入人数
        ];
        $excel_result[]     = [
            static::$t->_('hc_summary_column'), //列汇总
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            $list['summary_data']['department_name'] ?? '',    //所属部门
            $list['summary_data']['job_title_name'] ?? '',     //职位名称
            '',                                                //HC预算状态
            $list['summary_data']['budget'] ?? '',             //HC总数
            $list['summary_data']['employee_in_service'] ?? '',//在职人数
            $list['summary_data']['hc_request'] ?? '',         //剩余可提交HC数量
            $list['summary_data']['hc_demand_wait'] ?? '',     //已提交HC数量
            $list['summary_data']['hc_demand'] ?? '',          //招聘中人数
            $list['summary_data']['hc_pending'] ?? '',         //待入职人数
            $list['summary_data']['employee_suspend'] ?? '',   //停职人数
            $list['summary_data']['employee_wait_leave'] ?? '',//待离职职人数
            $list['summary_data']['job_trans_entry'] ?? '',    //待转入人数
        ];
        foreach ($list['items'] as $k => $v) {

            $excel_result[] = [
                '',                       //列汇总
                $v['department_name'],    //所属部门
                $v['department_name_0'],
                $v['department_name_1'],
                $v['department_name_2'],
                $v['department_name_3'],
                $v['department_name_4'],
                $v['department_name_5'],
                $v['department_name_6'],
                $v['job_title_name'],      //职位名称
                $v['hc_budget_state_name'],//HC预算状态
                $v['budget'],              //HC总数
                $v['employee_in_service'], //在职人数
                $v['hc_request'],          //剩余可提交HC数量
                $v['hc_demand_wait'],      //已提交HC数量
                $v['hc_demand'],           //招聘中人数
                $v['hc_pending'],          //待入职人数
                $v['employee_suspend'],    //停职人数
                $v['employee_wait_leave'], //待离职职人数
                $v['job_trans_entry'],     //待转入人数
            ];

        }
        //todo excel导出处理
        $excel_header = [
            static::$t->_('hc_summary_hc_update_time'), // 更新时间
            $list['version_date']??'',//更新时间,
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
        ];
        return [$excel_header, $excel_result];
    }

    public function generateExcelOther($params, $staff_info_id)
    {
        //获取列表
        $params['pageNum']  = 1;
        $params['pageSize'] = 20000;
        $list               = $this->getList($params, $staff_info_id);
        $excel_result[] = [
            '',                                                 //列汇总
            static::$t->_('hc_summary_department'),             //所属部门
            static::$t->_('department.group_ceo'),              //Group CEO
            static::$t->_('department.c_level'),                //C Level
            static::$t->_('department.bu'),                     //BU
            static::$t->_('department.level_1'),                //一级部门
            static::$t->_('department.level_2'),                //二级部门
            static::$t->_('department.level_3'),                //三级部门
            static::$t->_('department.level_4'),                //四级部门
            static::$t->_('job_name'),                          //职位名称
            static::$t->_('hc_summary_budget.budget_state'),    //HC预算状态
            static::$t->_('hc_summary_budget.budget_num'),      //HC总数
            static::$t->_('hc_summary_employee_in_service'),    // 在职人数
            static::$t->_('hc_summary_hc_request'),             // 剩余可提交 hc 数量
            static::$t->_('hc_summary_hc_demand_wait'),         // 已提交 hc 数量
            static::$t->_('hc_summary_hc_demand'),              // 招聘中人数
            static::$t->_('hc_summary_hc_pending'),             // 待入职人数
            static::$t->_('hc_summary_budget.suspend_num'),     // 停职人数
            static::$t->_('hc_summary_budget.wait_leave_num'),  // 待离职职人数
        ];
        $excel_result[]     = [
            static::$t->_('hc_summary_column'), //列汇总
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            $list['summary_data']['department_name'] ?? '',    //所属部门
            $list['summary_data']['job_title_name'] ?? '',     //职位名称
            '',                                                //HC预算状态
            $list['summary_data']['budget'] ?? '',             //HC总数
            $list['summary_data']['employee_in_service'] ?? '',//在职人数
            $list['summary_data']['hc_request'] ?? '',         //剩余可提交HC数量
            $list['summary_data']['hc_demand_wait'] ?? '',     //已提交HC数量
            $list['summary_data']['hc_demand'] ?? '',          //招聘中人数
            $list['summary_data']['hc_pending'] ?? '',         //待入职人数
            $list['summary_data']['employee_suspend'] ?? '',   //停职人数
            $list['summary_data']['employee_wait_leave'] ?? '',//待离职职人数
        ];
        foreach ($list['items'] as $k => $v) {

            $excel_result[] = [
                '',                       //列汇总
                $v['department_name'],    //所属部门
                $v['department_name_0'],
                $v['department_name_1'],
                $v['department_name_2'],
                $v['department_name_3'],
                $v['department_name_4'],
                $v['department_name_5'],
                $v['department_name_6'],
                $v['job_title_name'],      //职位名称
                $v['hc_budget_state_name'],//HC预算状态
                $v['budget'],              //HC总数
                $v['employee_in_service'], //在职人数
                $v['hc_request'],          //剩余可提交HC数量
                $v['hc_demand_wait'],      //已提交HC数量
                $v['hc_demand'],           //招聘中人数
                $v['hc_pending'],          //待入职人数
                $v['employee_suspend'],    //停职人数
                $v['employee_wait_leave'], //待离职职人数
            ];

        }
        //todo excel导出处理
        $excel_header = [
            static::$t->_('hc_summary_hc_update_time'), // 更新时间
            $list['version_date']??'',//更新时间,
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
        ];
        return [$excel_header, $excel_result];
    }

    /**
     * @param $conditions
     * @param $bind
     * @return array
     */
    private function shareGroupDetail($conditions, $bind)
    {
        $shareGroupDetail = [];
        $shareGroup       = HrHcSummaryDataTblModel::find([
            'conditions' => $conditions,
            'bind'       => $bind,
            'columns'    => [
                'distinct(share_group_id) as share_group_id',
            ],
        ])->toArray();
        $shareGroupIds    = array_column($shareGroup, 'share_group_id');
        $shareGroupIds    = array_values(array_filter($shareGroupIds, 'is_numeric'));
        if (empty($shareGroupIds)) {
            return $shareGroupDetail;
        }
        $version    = $this->getNewestVersion();
        $version_id = $version['id'];

        return HrHcSummaryShareGroupTblModel::find([
            'conditions' => 'share_group_id IN ({share_group_ids:array}) and version_id = :version_id:',
            'bind'       => [
                'version_id'      => $version_id,
                'share_group_ids' => $shareGroupIds,
            ],
            'columns'    => 'budget_real,hc_request_real',
        ])->toArray();
    }
}