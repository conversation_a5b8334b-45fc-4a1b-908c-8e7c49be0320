<?php

namespace App\Modules\Hc\Services;


class BaseService extends \App\Library\BaseService
{
    public static function _treeNode($data, $pk = 'ancestry', $parentId = 0)
    {
        $node = [];
        foreach ($data as $key => $value) {
            if ($parentId == $value[$pk]) {
                $node[] = [
                    'value' => $value['id'],
                    'label' => $value['name'],
                    'children' => self::_treeNode($data, $pk, $value['id'])
                ];
            }
        }
        return $node;
    }

    public static function handleParams($params, $not_must)
    {
        if (empty($not_must) || !is_array($not_must)) {
            return $params;
        }
        foreach ($not_must as $value) {
            if (isset($params[$value]) && empty($params[$value])) {
                unset($params[$value]);
            }
        }
        return $params;
    }

    public static function excelToFile($head, $data = [], $fileName = 'excel.xlsx')
    {
        $config = [
            'path' => sys_get_temp_dir() . '/'
        ];
        $excel = new \Vtiful\Kernel\Excel($config);
        // 此处会自动创建一个工作表
        $fileObject = $excel->fileName($fileName);
        $fileObject->header($head)->data($data);
        // 最后的最后，输出文件
        $filePath = $fileObject->output();
        //判断文件是否生成成功了
        if (!is_file($filePath)) {
            return "";
        }
        return $filePath;
    }
}