<?php

namespace App\Modules\Hc\Services;


use App\Library\Enums;
use App\Library\ErrCode;
use App\Modules\Hc\Models\HrEntryModel;
use App\Modules\Hc\Models\HrInterviewModel;
use App\Modules\Hc\Services\BudgetSummaryService;
use App\Modules\Hc\Models\HrInterviewOfferModel;
use App\Modules\Hc\Models\HrResumeModel;
use App\Modules\Transfer\Models\HrHcModel;
use App\Modules\Hc\Models\SysDepartmentModel;
use App\Modules\Hc\Models\SysStoreModel;
use App\Modules\Hc\Models\HrJobTitleModel;
use App\Library\Enums\GlobalEnums;

class HrEntryService extends BaseService
{
    private static $instance;
    public         $status = 2;//待入职


    public static $validate_search_param = [
        'pageSize' => 'IntGt:0',
        'pageNum'  => 'IntGt:0',
    ];

    /**
     * @return BudgetService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }


    /**
     * @description: 获取 列表数据
     *
     * @param null
     *
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2021/11/9 13:38
     */
    public function getList($param, $staff_info_id): array
    {
        $department_id         = $param['department_id'] ?? 0;                               //部门
        $job_title_id          = $param['job_title_id'] ?? 0;                                //职位
        $store_id              = $param['store_id'] ?? '';                                   //网点
        $subsidiary_department = $param['subsidiary_department'] ?? 0;                       //是否包含子部门 1 是
        $hc_id                 = $param['hc_id'] ?? 0;                                       //hc_id
        $start_work_time      = $param['start_work_time'] ?? '';                           //预计入职开始时间
        $end_work_time        = $param['end_work_time'] ?? '';                             //预计入职结束时间
        $name                  = $param['name'] ?? '';                                       //预计入职结束时间
        $pageSize              = empty($param['pageSize']) ? 20 : $param['pageSize'];        //条数
        $pageNum               = empty($param['pageNum']) ? 1 : $param['pageNum'];           //页数
        $start                 = ($pageNum - 1) * $pageSize;
        $department_ids        = [];
        if ($subsidiary_department == 1 && !empty($department_id)) {
            //获取子部门
            $department_id_list = SysService::getInstance()->getNodeDeptList($department_id, false);
            $department_ids     = array_column($department_id_list, 'id');
        }


        //因为 offer 限制问题只能这么查询
        $conditions = 'hr_entry.status = :state:  ';
        $bind       = ['state' => $this->status];
        //查询部门
        if (!empty($department_id)) {
            $department_ids[]      = $department_id;
            $conditions            .= ' and hr_hc.department_id in ({department_id:array}) ';
            $bind['department_id'] = $department_ids;
        }
        if (!empty($job_title_id)) {
            $bind['job_title_id'] = explode(',', $job_title_id);
            $conditions           .= ' and hr_hc.job_title in ({job_title_id:array}) ';
        }
        if (!empty($store_id)) {
            $bind['store_id'] = explode(',', $store_id);
            $conditions       .= "  and hr_hc.worknode_id in ({store_id:array}) ";
        }
        if (!empty($hc_id)) {
            $bind['hc_id'] = $hc_id;
            $conditions    .= "  and hr_interview.hc_id = :hc_id: ";
        }
        if (!empty($start_work_time)) {
            $bind['start_work_time'] = date('Y-m-d 00:00:00',strtotime($start_work_time));
            $conditions               .= "  and hr_interview_offer.work_time >= :start_work_time: ";
        }
        if (!empty($end_work_time)) {
            $bind['end_work_time'] = date('Y-m-d 23:59:59',strtotime($end_work_time));
            $conditions             .= "  and hr_interview_offer.work_time  <= :end_work_time: ";
        }

        //简历表存储name空格不统一
        if (!empty($name)) {
            $bind['name'] = str_replace(' ', '', $name)."%";
            $conditions   .= "  and replace(hr_resume.name, ' ','')  like :name: ";
        }

        //获取数据权限
        $list_rang = (new BudgetSummaryService())->getListJurisdiction($staff_info_id);
        if ($list_rang['conditions']) {
            $list_rang['conditions'] = str_replace(' department_id', ' hr_hc.department_id', $list_rang['conditions']);
            $list_rang['conditions'] = str_replace(' store_id', ' hr_hc.worknode_id', $list_rang['conditions']);
        }
        $conditions .= $list_rang['conditions'];
        $bind       = array_merge($bind, $list_rang['bind']);


        //获取列表数据
        $builder = $this->modelsManager->createBuilder()
            ->columns('count(1) as total')
            ->addFrom(HrInterviewModel::class, 'hr_interview')
            ->leftJoin(HrHcModel::class, 'hr_interview.hc_id = hr_hc.hc_id', 'hr_hc')
            ->leftJoin(HrInterviewOfferModel::class,
                'hr_interview_offer.interview_id = hr_interview.interview_id and hr_interview_offer.id = (SELECT max( a.id ) AS id FROM App\Modules\Hc\Models\HrInterviewOfferModel as a WHERE a.interview_id = hr_interview.interview_id)',
                'hr_interview_offer')
            ->leftJoin(HrResumeModel::class, 'hr_interview.resume_id = hr_resume.id', 'hr_resume')
            ->join(HrEntryModel::class, 'hr_entry.interview_id = hr_interview.interview_id', 'hr_entry');

        $builder->where($conditions, $bind);
        $totalInfo  = $builder->getQuery()->getSingleResult();
        $count = (int)($totalInfo->total);

        //获取列表数据
        $builder->columns('
            hr_interview.interview_id,
            hr_hc.department_id,
            hr_hc.worknode_id,
            hr_hc.job_title,
            hr_hc.hc_id,
            hr_resume.first_name, 
            hr_resume.last_name,
            hr_resume.middle_name,
            hr_resume.suffix_name,
            hr_resume.first_name_en,
            hr_resume.last_name_en,
            hr_interview_offer.work_time
        ');

        $builder->limit($pageSize, $start);
        $builder->orderBy('hr_interview_offer.work_time ASC');
        $list = $builder->getQuery()->execute()->toArray();

        //格式化内容
        $list = $this->buildDataList($list);
        return [
            'items'      => $list,
            'pagination' => [
                'current_page' => $pageNum,
                'per_page'     => $pageSize,
                'total_count'  => $count,
            ],
        ];

    }


    public function buildDataList($list): array
    {
        if(empty($list)){
            return $list;
        }
        //获取所属部门
        $dep_ids     = array_values(array_unique(array_column($list, 'department_id')));
        $dep_nod     = SysDepartmentModel::find(
            ['conditions' => " id in ({ids:array}) ",
             'bind'       => ['ids' => $dep_ids],
             'columns'    => ['id', 'name', 'ancestry_v3']
            ])->toArray();
        $dep_nod_arr = array_column($dep_nod, null, 'id');
        //获取工作地点
        $store_ids                                = array_values(array_unique(array_column($list, 'worknode_id')));
        $store                                    = SysStoreModel::find(['conditions' => " id in ({ids:array}) ",
                                                                         'bind'       => ['ids' => $store_ids],
                                                                         'columns'    => ['id', 'name']
                                                                        ])->toArray();
        $store_arr                                = array_column($store, 'name', 'id');
        $store_arr[Enums::HEAD_OFFICE_STORE_FLAG] = Enums::PAYMENT_HEADER_STORE_NAME;
        //获取职位
        $job_title_ids = array_values(array_unique(array_column($list, 'job_title')));
        $job_title     = HrJobTitleModel::find(['conditions' => " id in ({ids:array}) ",
                                                'bind'       => ['ids' => $job_title_ids],
                                                'columns'    => ['id', 'job_name']
                                               ])->toArray();

        $job_title_arr = array_column($job_title, 'job_name', 'id');


        $country_code = get_country_code();
        foreach ($list as $k => &$v) {
            $v['first_level_department_name'] = '';
            //获取部门
            $v['nod_department_name'] = $dep_nod_arr[$v['department_id']]['name'] ?? '';
            //网点
            $v['store_name'] = $store_arr[$v['worknode_id']] ?? '';
            //职位
            $v['job_name']  = $job_title_arr[$v['job_title']] ?? '';
            $v['work_time'] = !empty($v['work_time']) ? date('Y-m-d', strtotime($v['work_time'])) : '';

            if ($country_code == GlobalEnums::PH_COUNTRY_CODE) {
                $v['name'] = $v['first_name'] . " " . $v['middle_name'] . " " . $v['last_name'] . " " . $v['suffix_name'];
            } else {
                $v['name'] = $v['first_name'] . " " . $v['last_name'];
            }

            $v['name_en'] = $v['first_name_en'] . " " . $v['last_name_en'];
        }
        return $list;
    }

    /**
     * @description:导出
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/4/7 21:14
     */
    public function exportExcelList($params, $staff_info_id): array
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];

        try {
            //获取列表
            $params['pageNum']  = 1;
            $params['pageSize'] = 10000;
            $list               = $this->getList($params, $staff_info_id);
            $excel_result=[];
            foreach ($list['items'] as $k => $v) {
                $excel_result[] = [
                    $v['nod_department_name'],//部门
                    $v['job_name'],//职位
                    $v['store_name'],//网点
                    $v['hc_id'],//hcid
                    $v['name'],//姓名
                    $v['name_en'],//英文名
                    $v['work_time'],//时间
                ];

            }
            //todo excel导出处理
            $excel_header = [
                static::$t->_('view_department'),//所属部门
                static::$t->_('department.manager_job_title'),//职位
                static::$t->_('hc_summary_store_text'),//工作地点
                static::$t->_('hc_batch_approve_export_hcid'),//HCID
                static::$t->_('hc_hr_resume_name'),//姓名
                static::$t->_('hc_hr_resume_name_en'),//英文名
                static::$t->_('hc_work_time'),//预计入职时间
            ];
            //下载excle，方法返回上传阿里云后地址数组
            $file_arr         = \App\Modules\Organization\Services\BaseService::excelToFile($excel_header, $excel_result, 'oa-hc_pending-' . time() . '.xlsx');
            $data['file_url'] = $file_arr ? $file_arr['object_url'] : '';
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->getDI()->get('logger')->error('hc 入职管理-exportExcelList-异常信息: ' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }



}