<?php

namespace App\Modules\Hc\Controllers;

use App\Library\Enums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\OssHelper;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Hc\Services\BaseService;
use App\Modules\Hc\Services\BudgetService;
use App\Modules\Hc\Services\SysService;

class BudgetController extends BaseController
{
    /**
     * @Token
     * 预算申请列表
     */
    public function listAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, BudgetService::$search_params);
        try {
            Validation::validate($params, BudgetService::$validate_search_param);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $params['type'] = 1;
        $params['tab'] = 0;
        $staff_info_id = $this->user['id'];
        $params['is_all'] = 0;
        //职位=632 或者 部门是49，50的可以看全部申请数据
        if($this->user['job_title_id'] == Enums::JOB_TITLE_CPO || in_array($this->user['department_id'],[Enums::DEPARTMENT_ID_HRBP, Enums::DEPARTMENT_ID_TA, Enums::DEPARTMENT_ID_BP]) || in_array($staff_info_id, Enums::HC_BUDGET_LIST_ALL_STAFF_INFO_ID)) {
            $params['is_all'] = 1;
        }
        $list = BudgetService::getInstance()->budgetList($params, $staff_info_id);

        return $this->returnJson(ErrCode::$SUCCESS, '', $list);
    }

    /**
     * @Token
     * 审核列表
     */
    public function auditListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, BudgetService::$search_params);
        try {
            Validation::validate($params, BudgetService::$validate_search_param);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $params['type'] = 2;
        $params['status'] = 0;
        $staff_info_id = $this->user['id'];
        $list = BudgetService::getInstance()->budgetList($params, $staff_info_id);

        return $this->returnJson(ErrCode::$SUCCESS, '', $list);
    }

    /**
     * @Token
     * 预算详情
     */
    public function detailAction()
    {
        $budget_id = $this->request->get('budget_id');//预算id

        $params = ['budget_id' => $budget_id];
        $validate_param = ["budget_id" => "Required|IntGe:0"];

        try {
            Validation::validate($params, $validate_param);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $staff_id = $this->user['id'];
        $detail = BudgetService::getInstance()->budgetDetail($budget_id, $staff_id);
        return $this->returnJson(ErrCode::$SUCCESS, '', $detail);
    }

    /**
     * @Token
     * 创建预算申请
     */
    public function createAction()
    {
        $param = $this->request->get();
        $my_dept_id = $this->user['department_id'];//登录人所在部门
        $staff_info_id = $this->user['id']; //登陆人id
        $this->getDI()->get('logger')->info('hc_budget 请求参数: ' . json_encode($param, JSON_UNESCAPED_UNICODE)."\n".json_encode($this->user, JSON_UNESCAPED_UNICODE));

        try {
            $result = BudgetService::getInstance()->create($param, $staff_info_id, $my_dept_id);

            $err_code = isset($result['result']['code']) && $result['result']['code'] == 1 ? ErrCode::$SUCCESS : ErrCode::$BUSINESS_ERROR;
            $err_msg = !empty($result['result']['msg']) ? $result['result']['msg'] : '';
            $data = !empty($result['result']['data']) ? $result['result']['data'] : [];

            $this->getDI()->get('logger')->info('hc_budget 响应结果: ' . json_encode($result, JSON_UNESCAPED_UNICODE));

            return $this->returnJson($err_code, $err_msg, $data);
        } catch (ValidationException $e) {
            $this->logger->info('createAction----结果[ValidationException]' . $e->getMessage().'---------'.$e->getLine());
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage(), []);

        } catch (BusinessException $e) {
            $this->logger->notice('createAction----结果[BusinessException]' . $e->getMessage().'---------'.$e->getLine());
            return $this->returnJson(ErrCode::$BUSINESS_ERROR, $e->getMessage(), []);
        } catch (\Exception $e){
            $this->logger->warning('createAction----结果[Exception]' . $e->getMessage().'---------'.$e->getLine());
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage(), []);
        }
    }

    /**
     * @Token
     * 批量创建
     */
    public function batchCreateAction()
    {
        set_time_limit(0);
        $param = $this->request->get();

        $my_dept_id = $this->user['department_id'];//登录人所在部门
        $staff_info_id = $this->user['id']; //登陆人id
        
        $result = BudgetService::getInstance()->batchCreate($param, $staff_info_id, $my_dept_id);
        $code = ErrCode::$SUCCESS;
        $message = '';
        if(array_key_exists('code', $result)) {
            $code = $result['code'];
            $message = $result['message'];
        }
        return $this->returnJson($code, $message, $result['result']['data'] ?? []);
    }

    /**
     * @Token
     * 批量上传
     */
    public function batchUploadAction()
    {
        try {
            $excel_file = $this->request->getUploadedFiles();
            $param = $this->request->get();
            $budget_month = $param['budget_month'];
            $current_month = date('Y-m');
            $last_month = date("Y-m",strtotime("$current_month +1 month"));
            if(!in_array($budget_month,[$current_month,$last_month])) {
                throw new BusinessException("只能申请当月和下月", ErrCode::$BUSINESS_ERROR);
            }

            $config = ['path' => ''];
            $excel = new \Vtiful\Kernel\Excel($config);
            // 读取文件
            $excel_data = $excel->openFile($excel_file[0]->getTempName())
                ->openSheet()
                ->setSkipRows(1)
                ->getSheetData();

            $staff_info_id = $this->user['id'];
            $my_dept_id = $this->user['department_id'];

            $result = BudgetService::getInstance()->batchUpload($excel_data, $staff_info_id, $my_dept_id, $budget_month);
            return $this->returnJson($result['code'], $result['msg'], $result['data']);
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->warning('batchUploadAction----结果' . $e->getMessage().'---------'.$e->getLine());
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage(), []);
        }
    }

    /**
     * @Token
     * 返回批量上传详情
     */
    public function batchUploadDetailAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, BudgetService::$batch_upload_detail_params);
        $params['store_id'] = (string) ($params['store_id'] ?? ''); // 这里需要字符串
        $params['upload_key']  =  (string) ($params['upload_key'] ?? ''); // 这里需要字符串
        try {
            Validation::validate($params, BudgetService::$validate_batch_upload_detail_params);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $staff_info_id = $this->user['id'];
        $data = BudgetService::getInstance()->getBudgetUploadDetail($params, $staff_info_id);
        return $this->returnJson(ErrCode::$SUCCESS, '', $data);
    }

    /**
     * @Token
     * 批量上传详情分组
     */
    public function batchUploadDetailGroupAction()
    {
        $upload_key = $this->request->get('upload_key');

        $params = ['upload_key' => $upload_key];
        $validate_param = ["upload_key" => "Required|StrLenGeLe:1,100"];

        try {
            Validation::validate($params, $validate_param);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $staff_info_id = $this->user['id'];
        $data = BudgetService::getInstance()->getBudgetUploadDetailGroup($staff_info_id, $upload_key);
        return $this->returnJson(ErrCode::$SUCCESS, '', $data);
    }

    /**
     * @Token
     * 申请撤销
     */
    public function cancelAction()
    {
        $budget_id = $this->request->get('budget_id');

        $params = ['budget_id' => $budget_id];
        $validate_param = ["budget_id" => "Required|IntGe:0"];

        try {
            Validation::validate($params, $validate_param);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $staff_id = $this->user['id'];
        $param = [
            'hcb_id' => $budget_id,
            'staff_id' => $staff_id,
            'action' => 3,
            'note' => '',
        ];

        $result = BudgetService::getInstance()->auditHcBudget($param);
        return $this->returnJson(ErrCode::$SUCCESS, '', $result);
    }

    /**
     * @Token
     * 审核通过
     */
    public function approveAction()
    {
        $budget_id = $this->request->get('budget_id');
        $reject_reason = $this->request->get('reject_reason');

        $params = ['budget_id' => $budget_id,'reject_reason'=>$reject_reason];
        $validate_param = [
            "budget_id" => "Required|IntGe:0",
        ];

        try {
            Validation::validate($params, $validate_param);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $staff_id = $this->user['id'];
        $param = [
            'hcb_id' => $budget_id,
            'staff_id' => $staff_id,
            'action' => 1,
            'note' => addslashes($reject_reason),
        ];

        $result = BudgetService::getInstance()->auditHcBudget($param);
        return $this->returnJson($result['code'] == 1 ? ErrCode::$SUCCESS : ErrCode::$BUSINESS_ERROR, $result['msg'], $result['data']);
    }

    /**
     * @Token
     * 审核拒绝
     */
    public function rejectAction()
    {
        $budget_id = $this->request->get('budget_id');
        $reject_reason = $this->request->get('reject_reason');

        $params = ['budget_id' => $budget_id,'reject_reason'=>$reject_reason];
        $validate_param = [
            "budget_id" => "Required|IntGe:0",
            "reject_reason" => "Required|StrLenGeLe:1,1000",
            ];

        try {
            Validation::validate($params, $validate_param);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $staff_id = $this->user['id'];
        $param = [
            'hcb_id' => $budget_id,
            'staff_id' => $staff_id,
            'action' => 2,
            'note' => addslashes($reject_reason),
        ];

        $result = BudgetService::getInstance()->auditHcBudget($param);
        return $this->returnJson(ErrCode::$SUCCESS, '', $result);
    }

    /**
     * @Token
     * 部门维度的HC预算汇总列表
     */
    public function getHcListByDepAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, BudgetService::$search_hc_dep_params);

        try {
            Validation::validate($params, BudgetService::$validate_search_hc_dep_param);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        //获取当前工号
        $params['staff_id'] = $this->user['id'];

        $list = BudgetService::getInstance()->hcListByDep($params);

        return $this->returnJson(ErrCode::$SUCCESS, '', $list);
    }

    /**
     * @Token
     * 部门维度的HC预算汇总列表
     */
    public function getHcListByStoreAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, BudgetService::$search_hc_store_params);
        try {
            Validation::validate($params, BudgetService::$validate_search_hc_store_param);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        //获取当前工号
        $params['staff_id'] = $this->user['id'];

        $list = BudgetService::getInstance()->hcListByStore($params);

        return $this->returnJson(ErrCode::$SUCCESS, '', $list);
    }

    /**
     * @Token
     * 部门维度的HC预算汇总列表
     */
    public function exportHcListByDepAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, BudgetService::$search_hc_dep_params);
        try {
            Validation::validate($params, BudgetService::$validate_search_hc_dep_param);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $staffId = $this->user['id'];
        $list = BudgetService::getInstance()->exportHcListByDep($params, $staffId);

        return $this->returnJson(ErrCode::$SUCCESS, '', $list);
    }

    /**
     * @Token
     * 部门维度的HC预算汇总列表
     */
    public function exportHcListByStoreAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, BudgetService::$search_hc_store_params);
        try {
            Validation::validate($params, BudgetService::$validate_search_hc_store_param);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $staffId = $this->user['id'];
        $list = BudgetService::getInstance()->exportHcListByStore($params, $staffId);

        return $this->returnJson(ErrCode::$SUCCESS, '', $list);
    }

    /**
     * @Token
     * 批量上传
     * @throws \App\Library\Exception\BusinessException
     */
    public function uploadPerMonthBudgetAction()
    {
        //校验传入参数
        $params = $this->request->get();
        $params = BaseService::handleParams($params, BudgetService::$search_upload_params);

        try {
            Validation::validate($params, [
                'budget_month' => 'Required|Regexp:/^[0-9]{4}-[0-9]{2}$/|>>>:' . BaseService::getTranslation($this->locale)->t('hc_budget_invalid_month'),
                'department_id' => 'Required|IntGt:0|>>>:' . 'miss args',
            ]);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        //获取传入xlsx
        $excel_file = $this->request->getUploadedFiles();
        $config = ['path' => ''];
        $excel = new \Vtiful\Kernel\Excel($config);

        // 读取文件
        $excel_data = $excel->openFile($excel_file[0]->getTempName())
            ->openSheet()
            ->setSkipRows(1)
            ->getSheetData();

        //获取当前工号
        $staff_info_id = $this->user['id'];

        //上传预算
        $result = BudgetService::getInstance()->uploadPerMonthBudget($excel_data, $staff_info_id, $params['budget_month'], $params['department_id']);
        return $this->returnJson($result['code'], $result['msg'], $result['data']);
    }

    /**
     * @Token
     * 返回批量上传详情
     */
    public function getUploadPerMonthBudgetDetailAction()
    {
        $params = $this->request->get();
        if (empty($params['upload_key'])) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, 'Empty Upload Key', []);
        }
        $params = BaseService::handleParams($params, BudgetService::$not_must_params);
        $staff_info_id = $this->user['id'];
        $data = BudgetService::getInstance()->getUploadPerMonthBudgetDetail($params, $staff_info_id);
        return $this->returnJson(ErrCode::$SUCCESS, '', $data);
    }

    /**
     * @Token
     * @description 校验提交的预算人数是否超过计划HC数量 (预算功能已废弃)
     */
    public function checkBudgetAction()
    {
        $budget_id = $this->request->get();

        $params = ['budget_id' => $budget_id];

        try {
            Validation::validate($params, [
                'dept_id'      => 'Required|IntGt:0|>>>:'.'miss args',
                'job_title_id' => 'Required|IntGt:0|>>>:'.'miss args',
                'store_id'     => 'Required|StrLenGe:0|>>>:'.'miss args',
            ]);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $staff_id = $this->user['id'];
        //$detail   = BudgetService::getInstance()->checkBudget($budget_id, $staff_id);
        return $this->returnJson(ErrCode::$SUCCESS, '', $detail ?? []);
    }
}