<?php


namespace App\Modules\Hc\Controllers;

use App\Library\ErrCode;
use App\Library\OssHelper;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Hc\Services\HcBatchApproveService;
use App\Modules\Purchase\Services\ProductService;

class HcBatchApproveController extends BaseController
{
    //hc 批量审批

	/**
	 * @description:  hc 批量审批列表
	 *
	 * @param null
	 *
	 * @return     :
	 * <AUTHOR> L.J
	 * @time       : 2021/11/9 13:36
	 * @Token
	 * @Permission(action='hc.hc_batch_approve.get_list')
	 * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
	 */
	
	public function getListAction()
	{
		try {

			$param['pageSize'] = $this->request->get('per_page','int',20);
			$param['pageNum'] = $this->request->get('current_page','int',1);
			$staff_info_id = $this->user['id'];           //登陆人id
			
			Validation::validate($param, HcBatchApproveService::$validate_search_param);

			$result = HcBatchApproveService::getInstance()->getList($param, $staff_info_id);
			return $this->returnJson(ErrCode::$SUCCESS, 'success', $result);
		} catch (ValidationException $e) {
			return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
		} catch (\Exception $e) {
			$this->getDI()->get('logger')->error('HcBatchApproveController getList----结果[Exception]' . $e->getMessage() . '---------' . $e->getLine());
			return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage(), []);
		}
	}
	
	
	/**
	 * @description:下载结果
	 *
	 * @param null
	 *
	 * @return     :
	 * <AUTHOR> L.J
	 * @time       : 2021/11/9 19:19
	 * @Token
     * @Permission(action='hc.hc_batch_approve.get_list')
	 * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
	 */
	
	public function downloadAction()
	{
		try {
            $param['id'] = $this->request->get('id','int',1);
			$staff_info_id = $this->user['id'];           //登陆人id
            Validation::validate($param, [	'id' => 'IntGt:0']);
			$result = HcBatchApproveService::getInstance()->Download($param['id'],$staff_info_id);
			return $this->returnJson($result['code'], $result['message'], $result['data']);
		}  catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
			$this->getDI()->get('logger')->error('HcBatchApproveController downloadAction----结果[Exception]' . $e->getMessage() . '---------' . $e->getLine());
			return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage(), []);
		}
		
	}
	
	/**
	 * @description:上传附件
	 *
	 * @param null
	 *
	 * @return     :
	 * <AUTHOR> L.J
	 * @time       : 2021/11/9 20:19
	 * @Token
     * @Permission(action='hc.hc_batch_approve.get_list')
	 */

    public function UploadAttachmentAction()
    {
        try {
            if (!$this->request->hasFiles()) {
                throw new ValidationException($this->t->_("hc_batch_approve_not_file"));
            }
            $file      = $this->request->getUploadedFiles()[0];
            $extension = $file->getExtension();
            if (!in_array($extension, ['xlsx'])) {
                throw new ValidationException($this->t->_("hc_batch_approve_not_format_file").'xlsx');
            }
            $lock_key = md5('hc_batch_approve_upload_oa_' . $this->user['id']);
            $res      = $this->atomicLock(function () use ($file) {
                return HcBatchApproveService::getInstance()->import($file, $this->user['id']);
            }, $lock_key,30);
            if ($res['code'] == ErrCode::$SUCCESS) {
                return $this->returnJson($res['code'], $res['message'], $res['data']);
            }
            return $this->returnJson($res['code'], $res['message']);

        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->error('HcBatchApproveController UploadAttachmentAction----结果[Exception]' . $e->getMessage() . '---------' . $e->getLine());
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage(), []);
        }
    }


	
	
	/**
	 * @description:导出待审批
	 *
	 * @param null
	 *
	 * @return     :
	 * <AUTHOR> L.J
	 * @time       : 2021/11/10 19:57
     * @Token
     * @Permission(action='hc.hc_batch_approve.get_list')
	 * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
	 */
	
	public function exportExcelListAction(){
		try {
            $staff_info_id = $this->user['id'];
            $lock_key = md5('hc_batch_approve_export_oa_' . $this->user['id']);
            $res      = $this->atomicLock(function () use ($staff_info_id) {
                return HcBatchApproveService::getInstance()->getExportPendingList( $staff_info_id);
            }, $lock_key,30);
            if ($res['code'] == ErrCode::$SUCCESS) {
                return $this->returnJson($res['code'], $res['message'], $res['data']);
            }
            return $this->returnJson($res['code'], $res['message']);
		} catch (\Exception $e) {
			$this->getDI()->get('logger')->error('HcBatchApproveController exportExcelListAction----结果[Exception]' . $e->getMessage() . '---------' . $e->getLine());
			return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage(), []);
		}
	}
}