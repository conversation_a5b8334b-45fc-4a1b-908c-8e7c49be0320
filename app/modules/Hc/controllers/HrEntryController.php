<?php


namespace App\Modules\Hc\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Hc\Services\HrEntryService;

class HrEntryController extends BaseController
{
    /**
     * @description:  待入职员工详情 有预算列表的权限可以查看
     *
     * @param null
     *
     * @return     :
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * <AUTHOR> L.J
     * @time       : 2021/11/9 13:36
     * @Token
     * @Permission(action='hc.budget_summary.get_list')
     */

    public function getListAction()
    {
        try {

            $param['department_id']         = $this->request->get('department_id', 'trim', 0);        //部门 id
            $param['hc_id']                 = $this->request->get('hc_id', 'trim', 0);                // hc_id
            $param['store_id']              = $this->request->get('store_id', 'trim', '');            //工作地点
            $param['subsidiary_department'] = $this->request->get('subsidiary_department', 'int', 0); // 是否包含子部门 1 是
            $param['job_title_id']          = $this->request->get('job_title_id', 'trim', 0);         // 职位 id
            $param['name']                  = $this->request->get('name', 'trim', '');                // 名称
            $param['start_work_time']      = $this->request->get('start_work_time', 'trim', '');    // 预计入职开始时间
            $param['end_work_time']        = $this->request->get('end_work_time', 'trim', '');      // 预计入职结束时间
            $param['pageSize']              = $this->request->get('per_page', 'int', 20);
            $param['pageNum']               = $this->request->get('current_page', 'int', 1);

            $staff_info_id = $this->user['id'];           //登陆人id

            Validation::validate($param, HrEntryService::$validate_search_param);

            $result = HrEntryService::getInstance()->getList($param, $staff_info_id);
            return $this->returnJson(ErrCode::$SUCCESS, 'success', $result);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->error('HrEntryController getList----结果[Exception]' . $e->getMessage() . '---------' . $e->getLine());
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage(), []);
        }
    }


    /**
     * @description:导出按钮  有预算列表的权限可以导出
     *
     * @param null
     *
     * @return     :
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @Token
     * <AUTHOR> L.J
     * @time       : 2021/11/10 19:57
     * @Permission(action='hc.budget_summary.get_list')
     */
    public function exportExcelListAction()
    {
        try {
            $param         = $this->request->get();
            $staff_info_id = $this->user['id'];           //登陆人id

            $lock_key = md5('hr_entry.loan.export' . $this->user['id']);
            $res      = $this->atomicLock(function () use ($param, $staff_info_id) {
                return HrEntryService::getInstance()->exportExcelList($param, $staff_info_id);
            }, $lock_key, 30);
            if ($res['code'] == ErrCode::$SUCCESS) {
                return $this->returnJson($res['code'], $res['message'], $res['data']);
            }
            return $this->returnJson($res['code'], $res['message']);
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->error('HrEntryController exportExcelListAction----结果[Exception]' . $e->getMessage() . '---------' . $e->getLine());
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage(), []);
        }
    }
}