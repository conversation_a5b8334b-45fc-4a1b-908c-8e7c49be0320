<?php


namespace App\Modules\Hc\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\BankFlow\Services\PayFlowService;
use App\Modules\Hc\Services\BudgetSummaryService;

class BudgetSummaryController extends BaseController
{
	/**
	 * @description:  hc 预算汇总列表
	 *
	 * @param null
	 *
	 * @return     :
	 * <AUTHOR> L.J
	 * @time       : 2021/11/9 13:36
	 * @Token
	 * @Permission(action='hc.budget_summary.get_list')
	 * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
	 */
	
	public function getListAction()
	{
		try {
            $param['department_id']         = $this->request->get('department_id', 'trim', 0);
            $param['job_title_id']          = $this->request->get('job_title_id', 'trim', 0);
            $param['store_id']              = $this->request->get('store_id', 'trim', '');
            $param['pageSize']              = $this->request->get('pageSize', 'int', 20);
            $param['pageNum']               = $this->request->get('pageNum', 'int', 1);
            $param['subsidiary_department'] = $this->request->get('subsidiary_department', 'int', 0);
            $param['hc_budget_state']       = $this->request->get('hc_budget_state', 'int', 0);
            $staff_info_id                  = $this->user['id'];           //登陆人id
			
			Validation::validate($param, BudgetSummaryService::$validate_search_param);
			
			
			$result = BudgetSummaryService::getInstance()->getList($param, $staff_info_id);
			return $this->returnJson(ErrCode::$SUCCESS, 'success', $result);
		} catch (ValidationException $e) {
			return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
		} catch (\Exception $e) {
			$this->getDI()->get('logger')->error('BudgetSummaryController getList----结果[Exception]' . $e->getMessage() . '---------' . $e->getLine());
			return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage(), []);
		}
	}
	
	
	/**
	 * @description:点击更新按钮
	 *
	 * @param null
	 *
	 * @return     :
	 * <AUTHOR> L.J
	 * @time       : 2021/11/9 19:19
	 * @Token
	 * @Permission(action='hc.budget_summary.add_summary')
	 * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
	 */
	
	public function addSummaryAction()
	{
		try {
			$staff_info_id = $this->user['id'];           //登陆人id
			$result = BudgetSummaryService::getInstance()->addSummary($staff_info_id);
			return $this->returnJson($result['code'], $result['message'], $result['data']);
		} catch (\Exception $e) {
			$this->getDI()->get('logger')->error('BudgetSummaryController getList----结果[Exception]' . $e->getMessage() . '---------' . $e->getLine());
			return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage(), []);
		}
		
	}
	
	/**
	 * @description:检查脚本是否执行完毕
	 *
	 * @param null
	 *
	 * @return     :
	 * <AUTHOR> L.J
	 * @time       : 2021/11/9 20:19
	 * @Token
	 *
	 */
	
	public function  getSummaryAction()
    {
		try {
			$param         = $this->request->get();
			$version_id = $param['version_id'] ?? 0;
			$result = BudgetSummaryService::getInstance()->getHeartbeatSummary($version_id);
			return $this->returnJson(ErrCode::$SUCCESS, 'success', $result);
		} catch (\Exception $e) {
			$this->getDI()->get('logger')->error('BudgetSummaryController getList----结果[Exception]' . $e->getMessage() . '---------' . $e->getLine());
			return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage(), []);
		}
	}
	
	
	/**
	 * @description:导出按钮
	 *
	 * @param null
	 *
	 * @return     :
	 * <AUTHOR> L.J
	 * @time       : 2021/11/10 19:57
	 * @Permission(action='hc.budget_summary.export_excel_list')
	 * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
	 * @Token
	 */
	
	public function exportExcelListAction()
    {
        ini_set('memory_limit', '1024M');
		try {
			$param         = $this->request->get();
			$staff_info_id = $this->user['id'];           //登陆人id
			
			$lock_key = md5('budget_summary.loan.export'.$this->user['id']);
			$res = $this->atomicLock(function() use ($param,$staff_info_id){
				return BudgetSummaryService::getInstance()->exportExcelList($param, $staff_info_id);
			}, $lock_key, 30);
			if ($res['code'] == ErrCode::$SUCCESS) {
				return $this->returnJson($res['code'], $res['message'], $res['data']);
			}
			return $this->returnJson($res['code'], $res['message']);
		} catch (\Exception $e) {
			$this->getDI()->get('logger')->error('BudgetSummaryController getList----结果[Exception]' . $e->getMessage() . '---------' . $e->getLine());
			return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage(), []);
		}
	}
}