<?php


namespace App\Modules\Hc\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Hc\Services\BudgetService;
use App\Modules\Hc\Services\SysService;
use App\Modules\Organization\Models\HrJobDepartmentRelationModel;

class SysController extends BaseController
{
    /**
     * 部门关联职位列表
     */
    public function getJobTitleListAction()
    {
        $dept_id = $this->request->get('dept_id');
        $params = ['dept_id' => $dept_id];
        $validate_param = ["dept_id" => "IntGe:0"];

        try {
            Validation::validate($params, $validate_param);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        if (empty($dept_id)) {
            $list = SysService::getInstance()->getAllJobTitleList();
        } else {
            $list = SysService::getInstance()->getDeptJobTitleList($dept_id);
        }

        return $this->returnJson(ErrCode::$SUCCESS, '', $list);
    }

    /**
     * 网点列表
     */
    public function getStoreListAction()
    {
        $list = SysService::getInstance()->getStoreListBySearchName();

        return $this->returnJson(ErrCode::$SUCCESS, '', $list);
    }

    /**
     * @Token
     * 获取部门列表
     */
    public function getDeptListAction()
    {
        $staff_info_id = $this->user['id']; //登陆人id
        $my_dept_id = $this->user['department_id'];
        $list = SysService::getInstance()->getMyDeptListV2($staff_info_id,$my_dept_id);
        return $this->returnJson(ErrCode::$SUCCESS, '', $list);
    }

    /**
     * 获取子部门列表
     */
    public function getNodeDeptListAction()
    {
        $dept_id = $this->request->get('dept_id');
        $params = ['dept_id' => $dept_id];
        $validate_param = ["dept_id" => "IntGe:0"];

        try {
            Validation::validate($params, $validate_param);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $list = SysService::getInstance()->getNodeDeptList($dept_id);
        return $this->returnJson(ErrCode::$SUCCESS, '', $list);
    }

    /**
     * 获取人员配备
     */
    public function getStaffingAction()
    {
        $param = $this->request->get();

        $validate_param = [
            "dept_id" => "Required|IntGe:0",
            "job_title_id" => "Required|IntGe:0",
            'month' => 'Required|StrLenGeLe:1,50',
        ];

        try {
            Validation::validate($param, $validate_param);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $result = SysService::getInstance()->Staffing([$param]);

        //获取计划HC人数
        $planHcInfo = BudgetService::getInstance()->getPlanHcNums($param['dept_id'], $param['job_title_id']);

        $data = [
            'on_the_job'     => $result[$param['dept_id'].'_'.$param['job_title_id'].'_'.$param['store_id']]['on_the_job'] ?? 0,    //在职人数
            'to_be_employed' => $result[$param['dept_id'].'_'.$param['job_title_id'].'_'.$param['store_id']]['to_be_employed'] ?? 0,//待入职人数
            'hiring'         => $result[$param['dept_id'].'_'.$param['job_title_id'].'_'.$param['store_id']]['hiring'] ?? 0,        //招聘中人数
            'pendingCon'     => $result[$param['dept_id'].'_'.$param['job_title_id'].'_'.$param['store_id']]['pendingCon'] ?? 0,    //已提交的待审批的hc数
            'plan_hc_num'    => $planHcInfo && $planHcInfo['is_plan'] == HrJobDepartmentRelationModel::PLAN_STATE_FORMULATE ? intval($planHcInfo['plan_hc_nums']): "",
            'is_plan'        => $planHcInfo ? intval($planHcInfo['is_plan']): HrJobDepartmentRelationModel::PLAN_STATE_NOT_FORMULATE,
        ];

        return $this->returnJson(ErrCode::$SUCCESS, '', $data);
    }

    /**
     * 获取区域
     */
    public function getRegionsAndPiecesAction()
    {
        $list = SysService::getInstance()->getRegionsAndPieces();
        return $this->returnJson(ErrCode::$SUCCESS, '', $list);
    }
	
	/**
	 * @description:获取共享部门和职位
	 * @return     :
	 * <AUTHOR> L.J
	 * @time       : 2022/1/12 17:46
	 */
	public  function  getShareDepartmentPositionAction(){
		$share_department_position = SysService::getInstance()->getShareDepartmentPosition();
		return $this->returnJson(ErrCode::$SUCCESS, '', $share_department_position);
	
	}

    /**
     * @description:  根据部门和job_id  获取计划 hc数量
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/5/12 18:58
     * @Token
     */

    public function getPlanHcNumAction()
    {
        try {
            $param['dept_id']      = $this->request->get('dept_id', 'trim', 0);
            $param['job_title_id'] = $this->request->get('job_title_id', 'trim', 0);

            $validate_param = [
                "dept_id"      => "Required|IntGe:0",
                "job_title_id" => "Required|IntGe:0",
            ];
            Validation::validate($param, $validate_param);

            $result = SysService::getInstance()->getPlanHcNum($param['dept_id'], $param['job_title_id']);

            return $this->returnJson(ErrCode::$SUCCESS, 'success', $result);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            $this->logger->error('getPlanHcNumAction ----结果[Exception]'.$e->getMessage().'---------'.$e->getLine());
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage(), []);
        }
    }

    /**
     * @description 获取HC预算枚举
     * @Token
     */
    public function getStateAction()
    {
        $share_department_position = SysService::getInstance()->getState();
        return $this->returnJson(ErrCode::$SUCCESS, '', $share_department_position);
    }
}