<?php
/**
 * 仓库管理 - 仓库需求管理
 */
namespace App\Modules\Warehouse\Controllers;

use App\Library\Enums\ImportCenterEnums;
use App\Library\Enums\WarehouseEnums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Services\ImportCenterService;
use App\Modules\Warehouse\Services\BaseService;
use App\Modules\Warehouse\Services\RequirementService;
use App\Modules\Warehouse\Services\ThreadService;
use App\Util\RedisKey;
use Exception;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class RequirementController extends BaseController
{
    /**
     * 仓库需求枚举
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/88817
     * @return Response|ResponseInterface
     */
    public function getOptionsDefaultAction()
    {
        $res = RequirementService::getInstance()->getOptionsDefault();
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 仓库需求添加-获得默认值
     * @Permission(action='warehouse.requirement.add')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/88820
     * @return mixed
     */
    public function getAddDefaultAction()
    {
        $res = RequirementService::getInstance()->getAddDefault();
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 仓库需求添加
     * @Permission(action='warehouse.requirement.add')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/88823
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function addAction()
    {
        $params = trim_array($this->request->get());
        RequirementService::addValidation($params);
        $lock_key = md5(RedisKey::WAREHOUSE_REQUIREMENT_ADD_LOCK_PREFIX . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return RequirementService::getInstance()->add($params, $this->user);
        }, $lock_key, 10);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 仓库需求列表
     * @Permission(action='warehouse.requirement.list')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/88826
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function listAction()
    {
        $params = trim_array($this->request->get());
        $params = BaseService::handleParams($params, RequirementService::$not_must_params);
        Validation::validate($params, RequirementService::$validate_list);
        $res = RequirementService::getInstance()->list($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 仓库需求导出
     * @Permission(action='warehouse.requirement.export')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/88829
     * @return Response|ResponseInterface
     * @throws Exception
     */
    public function exportAction()
    {
        $params = trim_array($this->request->get());
        $params = BaseService::handleParams($params, RequirementService::$not_must_params);
        Validation::validate($params, RequirementService::$validate_list);
        // 加锁处理
        $lock_key = md5(RedisKey::WAREHOUSE_REQUIREMENT_EXPORT_LOCK_PREFIX . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return RequirementService::getInstance()->exportMethod($params, $this->user['id']);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 仓库需求查看
     * @Permission(action='warehouse.requirement.view')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/89111
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function viewAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, RequirementService::$validate_id);
        $res = RequirementService::getInstance()->view($params['id']);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 仓库需求管理-关联线索-查看
     * @Permission(action='warehouse.requirement.view')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/89159
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function viewThreadAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, ThreadService::$validate_id);
        $res = ThreadService::getInstance()->view($params['id']);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 仓库需求查看-关联线索-转移
     * @Permission(action='warehouse.requirement.view')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/88946
     * @return Response|ResponseInterface
     * @throws Exception
     */
    public function viewThreadTransferAction()
    {
        $params = trim_array($this->request->get());
        // 加锁处理
        $lock_key = md5(RedisKey::WAREHOUSE_REQUIREMENT_TRANSFER_LOCK_PREFIX . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return RequirementService::getInstance()->transfer($params, $this->user);
        }, $lock_key, 5);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 仓库需求作废
     * @Permission(action='warehouse.requirement.cancel')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/88985
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function cancelAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, RequirementService::$validate_cancel);
        $lock_key = md5(RedisKey::WAREHOUSE_REQUIREMENT_CANCEL_LOCK_PREFIX . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return RequirementService::getInstance()->cancel($params, $this->user);
        }, $lock_key, 20);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 仓库需求查看报价
     * @Permission(action='warehouse.requirement.view_price')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/89300
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function viewPriceAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, RequirementService::$validate_id);
        $res = RequirementService::getInstance()->viewPrice($params['id'], $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 处理仓库需求-查看-关联线索-查看
     * @Permission(menu='warehouse.requirement.handle')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/89162
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function handleViewThreadAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, ThreadService::$validate_id);
        $res = ThreadService::getInstance()->view($params['id']);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 处理需求管理-待寻找-列表
     * @Permission(menu='warehouse.requirement.search')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/88844
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function searchListAction()
    {
        $params = trim_array($this->request->get());
        $params = BaseService::handleParams($params, RequirementService::$not_must_params);
        Validation::validate($params, RequirementService::$validate_list);
        $res = RequirementService::getInstance()->list($params, RequirementService::REQUIREMENT_SEARCH_LIST);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 处理需求管理-待寻找-导出
     * @Permission(menu='warehouse.requirement.search')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/88850
     * @return Response|ResponseInterface
     * @throws Exception
     */
    public function searchExportAction()
    {
        $params = trim_array($this->request->get());
        $params = BaseService::handleParams($params, RequirementService::$not_must_params);
        Validation::validate($params, RequirementService::$validate_list);
        // 加锁处理
        $lock_key = md5(RedisKey::WAREHOUSE_REQUIREMENT_SEARCH_EXPORT_LOCK_PREFIX . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return RequirementService::getInstance()->exportMethod($params, $this->user['id'], RequirementService::REQUIREMENT_SEARCH_LIST);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 处理需求管理-待寻找-查看
     * @Permission(action='warehouse.requirement.search.view')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/89117
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function searchViewAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, RequirementService::$validate_id);
        $res = RequirementService::getInstance()->view($params['id'], RequirementService::REQUIREMENT_SEARCH_LIST);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 处理需求管理-待寻找-录入线索-查看
     * @Permission(action='warehouse.requirement.search.add_thread')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/89078
     * @return Response|ResponseInterface
     * @throws Exception
     */
    public function searchAddThreadViewAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, RequirementService::$validate_id);
        $res = RequirementService::getInstance()->searchAddThreadView($params['id']);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 处理需求管理-待寻找-录入线索
     * @Permission(action='warehouse.requirement.search.add_thread')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/88904
     * @return Response|ResponseInterface
     * @throws Exception
     */
    public function searchAddThreadAction()
    {
        $params = trim_array($this->request->get());
        $lock_key = md5(RedisKey::WAREHOUSE_THREAD_ADD_LOCK_PREFIX . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return ThreadService::getInstance()->add($params, $this->user);
        }, $lock_key, 10);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 处理需求管理-待寻找-录入线索-关联线索-转移
     * @Permission(action='warehouse.requirement.search.add_thread')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/88949
     * @return Response|ResponseInterface
     * @throws Exception
     */
    public function searchAddThreadTransferAction()
    {
        $params = trim_array($this->request->get());
        // 加锁处理
        $lock_key = md5(RedisKey::WAREHOUSE_REQUIREMENT_TRANSFER_LOCK_PREFIX . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return RequirementService::getInstance()->transfer($params, $this->user);
        }, $lock_key, 5);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 处理需求管理-待寻找-录入线索-关联线索-作废
     * @Permission(action='warehouse.requirement.search.add_thread')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/88958
     * @return Response|ResponseInterface
     * @throws Exception
     */
    public function searchAddThreadCancelAction()
    {
        $params = trim_array($this->request->get());
        // 加锁处理
        $lock_key = md5(RedisKey::WAREHOUSE_THREAD_CANCEL_LOCK_PREFIX . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return ThreadService::getInstance()->cancel($params, $this->user);
        }, $lock_key, 5);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 处理需求管理-待寻找-录入线索-提交确认
     * @Permission(action='warehouse.requirement.search.add_thread')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/88961
     * @return Response|ResponseInterface
     * @throws Exception
     */
    public function searchAddThreadConfirmAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, RequirementService::$validate_confirm);
        // 加锁处理
        $lock_key = md5(RedisKey::WAREHOUSE_REQUIREMENT_CONFIRM_LOCK_PREFIX . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return RequirementService::getInstance()->addConfirm($params, $this->user);
        }, $lock_key, 5);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 处理需求管理-待确认-列表
     * @Permission(menu='warehouse.requirement.confirm')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/88853
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function confirmListAction()
    {
        $params = trim_array($this->request->get());
        $params = BaseService::handleParams($params, RequirementService::$not_must_params);
        Validation::validate($params, RequirementService::$validate_list);
        $res = RequirementService::getInstance()->list($params, RequirementService::REQUIREMENT_CONFIRM_LIST);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 处理需求管理-待确认-导出
     * @Permission(menu='warehouse.requirement.confirm')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/88859
     * @return Response|ResponseInterface
     * @throws Exception
     */
    public function confirmExportAction()
    {
        $params = trim_array($this->request->get());
        $params = BaseService::handleParams($params, RequirementService::$not_must_params);
        Validation::validate($params, RequirementService::$validate_list);
        // 加锁处理
        $lock_key = md5(RedisKey::WAREHOUSE_REQUIREMENT_CONFIRM_EXPORT_LOCK_PREFIX . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return RequirementService::getInstance()->exportMethod($params, $this->user['id'], RequirementService::REQUIREMENT_CONFIRM_LIST);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 处理需求管理-待确认-查看
     * @Permission(action='warehouse.requirement.confirm.view')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/89120
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function confirmViewAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, RequirementService::$validate_id);
        $res = RequirementService::getInstance()->view($params['id'], RequirementService::REQUIREMENT_CONFIRM_LIST);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 处理需求管理-待寻找-去确认-查看
     * @Permission(action='warehouse.requirement.confirm.confirm')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/89081
     * @return Response|ResponseInterface
     * @throws Exception
     */
    public function confirmConfirmViewAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, RequirementService::$validate_id);
        $res = RequirementService::getInstance()->confirmConfirmView($params['id']);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 处理需求管理-待确认-去确认-线索导出
     * @Permission(action='warehouse.requirement.confirm.confirm')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/88964
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function confirmThreadExportAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, RequirementService::$validate_id);
        // 加锁处理
        $lock_key = md5(RedisKey::WAREHOUSE_REQUIREMENT_CONFIRM_THREAD_EXPORT_LOCK_PREFIX . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return ThreadService::getInstance()->exportRequirementThread($params['id']);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 处理需求管理-待确认-去确认-满足需求
     * @Permission(action='warehouse.requirement.confirm.confirm')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/88982
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function confirmAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, RequirementService::$validate_confirm_ok);
        // 加锁处理
        $lock_key = md5(RedisKey::WAREHOUSE_REQUIREMENT_CONFIRM_OK_LOCK_PREFIX . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return RequirementService::getInstance()->updateConfirm($params, $this->user);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 处理需求管理-待确认-去确认-全部不符合满足
     * @Permission(action='warehouse.requirement.confirm.confirm')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/88979
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function confirmDontAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, RequirementService::$validate_confirm_dont);
        // 加锁处理
        $lock_key = md5(RedisKey::WAREHOUSE_REQUIREMENT_CONFIRM_DONT_LOCK_PREFIX . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return RequirementService::getInstance()->updateConfirmDont($params, $this->user);
        }, $lock_key, 5);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 处理需求管理-待入驻-列表
     * @Permission(menu='warehouse.requirement.settle')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/88856
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function settleListAction()
    {
        $params = trim_array($this->request->get());
        $params = BaseService::handleParams($params, RequirementService::$not_must_params);
        Validation::validate($params, RequirementService::$validate_list);
        $res = RequirementService::getInstance()->list($params, RequirementService::REQUIREMENT_SETTLE_LIST);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 处理需求管理-待入驻-导出
     * @Permission(menu='warehouse.requirement.settle')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/88862
     * @return Response|ResponseInterface
     * @throws Exception
     */
    public function settleExportAction()
    {
        $params = trim_array($this->request->get());
        $params = BaseService::handleParams($params, RequirementService::$not_must_params);
        Validation::validate($params, RequirementService::$validate_list);
        // 加锁处理
        $lock_key = md5(RedisKey::WAREHOUSE_REQUIREMENT_SETTLE_EXPORT_LOCK_PREFIX . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return RequirementService::getInstance()->exportMethod($params, $this->user['id'], RequirementService::REQUIREMENT_SETTLE_LIST);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 处理需求管理-待入驻-查看
     * @Permission(action='warehouse.requirement.settle.view')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/89129
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function settleViewAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, RequirementService::$validate_id);
        $res = RequirementService::getInstance()->view($params['id'], RequirementService::REQUIREMENT_SETTLE_LIST);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 处理需求管理-待入驻-入驻-查看
     * @Permission(action='warehouse.requirement.settle.settle')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/89084
     * @return Response|ResponseInterface
     * @throws Exception
     */
    public function settleSettleViewAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, RequirementService::$validate_id);
        $res = RequirementService::getInstance()->settleSettleView($params['id']);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }
    /**
     * 处理需求管理-待入驻-入驻-确认入驻
     * @Permission(action='warehouse.requirement.settle.settle')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/89075
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function settleAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, RequirementService::$validate_settle);
        // 加锁处理
        $lock_key = md5(RedisKey::WAREHOUSE_REQUIREMENT_SETTLE_LOCK_PREFIX . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return RequirementService::getInstance()->settle($params, $this->user);
        }, $lock_key, 10);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 需求管理-导入
     * @Permission(action='warehouse.requirement.import')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/91010
     * @return Response|ResponseInterface
     * @throws Exception
     */
    public function importAction()
    {
        $lock_key = md5(RedisKey::WAREHOUSE_REQUIREMENT_IMPORT_LOCK_PREFIX . $this->user['id']);
        $res      = $this->atomicLock(function () {
            return RequirementService::getInstance()->addBatchImportToImportCenter($this->user);
        }, $lock_key, 5);

        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing']);
    }

    /**
     * 需求管理-导入结果查询
     * @Permission(action='warehouse.requirement.import')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/91007
     * @return Response|ResponseInterface
     */
    public function importResultAction()
    {
        $res = ImportCenterService::getInstance()->getBarcodeImportResult(ImportCenterEnums::TYPE_WAREHOUSE_REQUIREMENT);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 处理仓库需求-待续约-列表
     * @Permission(menu='warehouse.requirement.renewed')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/91157
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function renewedListAction()
    {
        $params = trim_array($this->request->get());
        $params = BaseService::handleParams($params, RequirementService::$not_must_params);
        Validation::validate($params, RequirementService::$validate_list);
        $res = RequirementService::getInstance()->list($params, RequirementService::REQUIREMENT_RENEWED_LIST);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 处理仓库需求-待续约-导出
     * @Permission(menu='warehouse.requirement.renewed')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/91160
     * @return Response|ResponseInterface
     * @throws Exception
     */
    public function renewedExportAction()
    {
        $params = trim_array($this->request->get());
        $params = BaseService::handleParams($params, RequirementService::$not_must_params);
        Validation::validate($params, RequirementService::$validate_list);
        // 加锁处理
        $lock_key = md5(RedisKey::WAREHOUSE_REQUIREMENT_RENEWED_EXPORT_LOCK_PREFIX . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return RequirementService::getInstance()->exportMethod($params, $this->user['id'], RequirementService::REQUIREMENT_RENEWED_LIST);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 处理需求管理-待续约-查看
     * @Permission(action='warehouse.requirement.renewed.view')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/91163
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function renewedViewAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, RequirementService::$validate_id);
        $res = RequirementService::getInstance()->view($params['id'], RequirementService::REQUIREMENT_RENEWED_LIST);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 处理仓库需求-待续约-谈判失败-弹窗信息
     * @Permission(action='warehouse.requirement.renewed.negotiations_failed')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/91208
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function negotiationsFailedInfoAction()
    {
        $params = trim_array($this->request->get());

        Validation::validate($params, RequirementService::$validate_id);
        $data = RequirementService::getInstance()->getNegotiationsFailedInfo($params['id']);
        return $this->returnJson(ErrCode::$SUCCESS, $this->t['success'], $data);
    }

    /**
     * 处理仓库需求-待续约-谈判失败
     * @Permission(action='warehouse.requirement.renewed.negotiations_failed')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/91169
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function negotiationsFailedAction()
    {
        $params = trim_array($this->request->get());

        Validation::validate($params, [
            'id'     => 'Required|IntGt:0|>>>:' . $this->t->_('params_error', ['param' => 'id']),
            'remark' => 'Required|StrLenGeLe:1,1000|>>>:' . $this->t->_('params_error', ['param' => 'remark']),
        ]);
        $lock_key = md5(RedisKey::WAREHOUSE_REQUIREMENT_NEGOTIATIONS_FAILED_LOCK_PREFIX . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return RequirementService::getInstance()->negotiationsFailed($params, $this->user);
        }, $lock_key, 5);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing']);
    }

    /**
     * 处理仓库需求-待续约-续约报价
     * @Permission(action='warehouse.requirement.renewed.renewal_quote')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/91550
     * @return Response|ResponseInterface
     * @throws Exception
     */
    public function priceSubmitAction()
    {
        $params   = trim_array($this->request->get());
        $lock_key = md5(RedisKey::WAREHOUSE_REQUIREMENT_PRICE_ADD_LOCK_PREFIX . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return ThreadService::getInstance()->addPrice($params, $this->user, WarehouseEnums::THREAD_PRICE_SOURCE_TYPE_RM_RENEWED);
        }, $lock_key, 20);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing']);
    }

}
