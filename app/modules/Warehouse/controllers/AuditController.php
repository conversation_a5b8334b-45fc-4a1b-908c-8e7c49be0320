<?php
/**
 * 仓库管理 - 变更审批
 */

namespace App\Modules\Warehouse\Controllers;

use App\Library\ErrCode;
use App\Modules\Warehouse\Services\AuditService;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class AuditController extends BaseController
{
    /**
     * 仓库审批列表
     * @Permission(menu='warehouse_manage.audit')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/83912
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function listAction()
    {
        $params = trim_array($this->request->get());

        $validation = [
            'flag' => 'Required|IntIn:1,2|>>>:params error[flag]',
        ];
        if (!empty($params['created_start_date'])) {
            $validation['created_start_date'] = 'Required|Date|>>>:params error[created_start_date]';
        }

        if (!empty($params['created_end_date'])) {
            $validation['created_end_date'] = 'Required|Date|>>>:params error[created_end_date]';
        }
        Validation::validate($params, $validation);

        $res = AuditService::getInstance()->list($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 仓库审批详情
     * @Permission(menu='warehouse_manage.audit')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/83957
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function detailAction()
    {
        $params     = trim_array($this->request->get());
        $validation = ['id' => 'Required|IntGt:0|>>>:param error[id]'];
        Validation::validate($params, $validation);

        $res = AuditService::getInstance()->getDetail($params['id'], $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 审批-同意
     * @Permission(menu='warehouse_manage.audit')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/83978
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function approvalAction()
    {
        $params     = trim_array($this->request->get());
        $validation = [
            'id'     => 'Required|IntGt:0|>>>:param error[id]',
            'reason' => 'Required|StrLenGeLe:0,1000|>>>:' . $this->t['warehouse_audit_error_002'],
        ];
        Validation::validate($params, $validation);

        $lock_key = md5('warehouse_audit_approval_' . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return AuditService::getInstance()->approval($params, $this->user);
        }, $lock_key, 20);

        if (isset($res['code'])) {
            $code    = $res['code'];
            $message = $res['message'];
        } else {
            $code    = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
        }

        return $this->returnJson($code, $message);
    }

    /**
     * 审批驳回
     * @Permission(menu='warehouse_manage.audit')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/83981
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function rejectAction()
    {
        $params     = trim_array($this->request->get());
        $validation = [
            'id'     => 'Required|IntGt:0|>>>:param error[id]',
            'reason' => 'Required|StrLenGeLe:1,1000|>>>:' . $this->t['warehouse_audit_error_001'],
        ];
        Validation::validate($params, $validation);

        $lock_key = md5('warehouse_audit_reject_' . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return AuditService::getInstance()->reject($params, $this->user);
        }, $lock_key, 20);

        if (isset($res['code'])) {
            $code    = $res['code'];
            $message = $res['message'];
        } else {
            $code    = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
        }

        return $this->returnJson($code, $message);
    }

}
