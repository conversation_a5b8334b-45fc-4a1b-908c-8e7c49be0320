<?php
/**
 * 仓库管理 - 仓库线索管理
 */
namespace App\Modules\Warehouse\Controllers;

use App\Library\Enums\WarehouseEnums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Library\Exception\BusinessException;
use App\Modules\Warehouse\Services\BaseService;
use App\Modules\Warehouse\Services\ThreadService;
use App\Modules\Warehouse\Services\WarehouseService;
use App\Util\RedisKey;
use Exception;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class ThreadController extends BaseController
{
    /**
     * 仓库线索枚举
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/88865
     * @return Response|ResponseInterface
     */
    public function getOptionsDefaultAction()
    {
        $res = ThreadService::getInstance()->getOptionsDefault();
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 关联仓库
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/88868
     * @return Response|ResponseInterface
     */
    public function getRelateWarehouseListAction()
    {
        $params = trim_array($this->request->get());
        $res = ThreadService::getInstance()->getRelateWarehouseList($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 关联需求
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/88877
     * @return Response|ResponseInterface
     */
    public function getRelateRequirementListAction()
    {
        $params = trim_array($this->request->get());
        $res = ThreadService::getInstance()->getRelateRequirementList($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 仓库线索添加-获得默认值
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/88871
     * @return mixed
     */
    public function getAddDefaultAction()
    {
        $res = ThreadService::getInstance()->getAddDefault();
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 仓库线索管理-添加
     * @Permission(action='warehouse.thread.add')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/88898
     * @return Response|ResponseInterface
     * @throws Exception
     */
    public function addAction()
    {
        $params = trim_array($this->request->get());
        $lock_key = md5(RedisKey::WAREHOUSE_THREAD_ADD_LOCK_PREFIX . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return ThreadService::getInstance()->add($params, $this->user);
        }, $lock_key, 10);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 仓库线索管理-编辑
     * @Permission(action='warehouse.thread.edit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/88901
     * @return Response|ResponseInterface
     * @throws Exception
     */
    public function editAction()
    {
        $params = trim_array($this->request->get());
        $lock_key = md5(RedisKey::WAREHOUSE_THREAD_EDIT_LOCK_PREFIX . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return ThreadService::getInstance()->edit($params, $this->user);
        }, $lock_key, 10);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 仓库线索管理-列表
     * @Permission(action='warehouse.thread.list')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/88910
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function listAction()
    {
        $params = trim_array($this->request->get());
        $params = BaseService::handleParams($params, ThreadService::$not_must_params);
        Validation::validate($params, ThreadService::$validate_list);
        $res = ThreadService::getInstance()->list($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 仓库线索管理-导出
     * @Permission(action='warehouse.thread.export')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/88925
     * @return Response|ResponseInterface
     * @throws Exception
     */
    public function exportAction()
    {
        $params = trim_array($this->request->get());
        $params = BaseService::handleParams($params, ThreadService::$not_must_params);
        Validation::validate($params, ThreadService::$validate_list);
        // 加锁处理
        $lock_key = md5(RedisKey::WAREHOUSE_THREAD_EXPORT_LOCK_PREFIX . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return ThreadService::getInstance()->exportMethod($params, $this->user);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 仓库线索管理-查看
     * @Permission(action='warehouse.thread.view')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/89150
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function viewAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, ThreadService::$validate_id);
        $res = ThreadService::getInstance()->view($params['id']);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 仓库线索管理-查看 - 查看仓库信息
     * @Permission(action='warehouse.thread.view')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/88988
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function viewWarehouseAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, ThreadService::$validate_id);
        $res = WarehouseService::getInstance()->getDetail($params['id']);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 仓库线索管理-作废
     * @Permission(action='warehouse.thread.cancel')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/88955
     * @return Response|ResponseInterface
     * @throws Exception
     */
    public function cancelAction()
    {
        $params = trim_array($this->request->get());

        $lock_key = md5(RedisKey::WAREHOUSE_THREAD_CANCEL_LOCK_PREFIX . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return ThreadService::getInstance()->cancel($params, $this->user);
        }, $lock_key, 5);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 仓库线索管理-查看报价
     * @Permission(action='warehouse.thread.view_price')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/89297
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function viewPriceAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, ThreadService::$validate_id);
        $res = ThreadService::getInstance()->viewPrice($params['id'], $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 处理仓库线索-待报价-列表
     * @Permission(menu='warehouse.thread.handle.price')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/88928
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function priceListAction()
    {
        $params = trim_array($this->request->get());
        $params = BaseService::handleParams($params, ThreadService::$not_must_params);
        Validation::validate($params, ThreadService::$validate_list);
        $res = ThreadService::getInstance()->list($params, $this->user, ThreadService::THREAD_PRICE_LIST);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 处理仓库线索-待报价-导出
     * @Permission(menu='warehouse.thread.handle.price')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/88931
     * @return Response|ResponseInterface
     * @throws Exception
     */
    public function priceExportAction()
    {
        $params = trim_array($this->request->get());
        $params = BaseService::handleParams($params, ThreadService::$not_must_params);
        Validation::validate($params, ThreadService::$validate_list);
        // 加锁处理
        $lock_key = md5(RedisKey::WAREHOUSE_THREAD_PRICE_EXPORT_LOCK_PREFIX . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return ThreadService::getInstance()->exportMethod($params, $this->user, ThreadService::THREAD_PRICE_LIST);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 处理仓库线索-待报价-查看
     * @Permission(action='warehouse.thread.handle.price.view')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/89153
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function priceViewAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, ThreadService::$validate_id);
        $res = ThreadService::getInstance()->view($params['id'], ThreadService::THREAD_PRICE_LIST);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 处理仓库线索-待报价-去报价、处理仓库线索-待签约-重新报价、仓库信息管理-续签报价-获得默认值
     * 处理仓库需求-待续约-续约报价
     * @Permission(menu='warehouse_manage')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/89165
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function getAddPriceDefaultAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, ThreadService::$validate_add_price_default);
        $res = ThreadService::getInstance()->getAddPriceDefault($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 处理仓库线索-待报价-去报价、处理仓库线索-待签约-重新报价、仓库信息管理-续签报价-同区域已租赁仓库平均价格
     * 处理仓库需求-待续约-续约报价
     * @api https://yapi.flashexpress.pub/project/133/interface/api/89171
     * @Permission(menu='warehouse_manage')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function getLeasedAveragePriceAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, ThreadService::$validate_leased_average_price);
        $res = ThreadService::getInstance()->getLeasedAveragePrice($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 处理仓库线索-待报价-去报价、处理仓库线索-待签约-重新报价、仓库信息管理-续签报价-上次报价信息
     * 处理仓库需求-待续约-续约报价-上次报价信息
     * @api https://yapi.flashexpress.pub/project/133/interface/api/89174
     * @Permission(menu='warehouse_manage')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function getLastPriceInfoAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, ThreadService::$validate_warehouse_id);
        $res = ThreadService::getInstance()->getLastPriceInfo($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 处理仓库线索-待报价-去报价-提交
     * @Permission(action='warehouse.thread.handle.price.price')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/89192
     * @return Response|ResponseInterface
     * @throws Exception
     */
    public function priceSubmitAction()
    {
        $params = trim_array($this->request->get());
        $params['status'] = WarehouseEnums::THREAD_STATUS_PRICE;//待报价
        $lock_key = md5(RedisKey::WAREHOUSE_THREAD_PRICE_ADD_LOCK_PREFIX . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return ThreadService::getInstance()->addPrice($params, $this->user);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 处理仓库线索-待报价-谈判失败
     * @Permission(action='warehouse.thread.handle.price.talk_fail')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/89093
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function priceTalkFailAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, ThreadService::$validate_id);
        $lock_key = md5(RedisKey::WAREHOUSE_THREAD_PRICE_TALK_FAIL_LOCK_PREFIX . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return ThreadService::getInstance()->priceTalkFail($params, $this->user);
        }, $lock_key, 5);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 处理仓库线索-待签约-列表
     * @Permission(menu='warehouse.thread.handle.sign')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/88934
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function signListAction()
    {
        $params = trim_array($this->request->get());
        $params = BaseService::handleParams($params, ThreadService::$not_must_params);
        Validation::validate($params, ThreadService::$validate_list);
        $res = ThreadService::getInstance()->list($params, $this->user, ThreadService::THREAD_SIGN_LIST);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 处理仓库线索-待签约-导出
     * @Permission(menu='warehouse.thread.handle.sign')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/88937
     * @return Response|ResponseInterface
     * @throws Exception
     */
    public function signExportAction()
    {
        $params = trim_array($this->request->get());
        $params = BaseService::handleParams($params, ThreadService::$not_must_params);
        Validation::validate($params, ThreadService::$validate_list);
        // 加锁处理
        $lock_key = md5(RedisKey::WAREHOUSE_THREAD_SIGN_EXPORT_LOCK_PREFIX . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return ThreadService::getInstance()->exportMethod($params, $this->user, ThreadService::THREAD_SIGN_LIST);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 处理仓库线索-待签约-查看
     * @Permission(menu='warehouse.thread.handle.sign')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/89156
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function signViewAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, ThreadService::$validate_id);
        $res = ThreadService::getInstance()->view($params['id'], ThreadService::THREAD_SIGN_LIST);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 处理仓库线索-待签约-新建仓库
     * @Permission(action='warehouse.thread.handle.sign.add_warehouse')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/89009
     * @return Response|ResponseInterface
     * @throws ValidationException
     * @throws BusinessException
     */
    public function signAddWarehouseAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, ThreadService::$validate_id);
        WarehouseService::saveCommonParamsValidation($params);

        $lock_key = md5(RedisKey::CONTRACT_THREAD_SIGN_WAREHOUSE_ADD_LOCK_PREFIX . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return ThreadService::getInstance()->signAddWarehouse($params, $this->user);
        }, $lock_key, 10);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);

    }

    /**
     * 处理仓库线索-待签约-重新报价
     * @Permission(action='warehouse.thread.handle.sign.recommit_price')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/89195
     * @return Response|ResponseInterface
     * @throws Exception
     */
    public function signRecommitPriceAction()
    {
        $params = trim_array($this->request->get());
        $params['status'] = WarehouseEnums::THREAD_STATUS_SIGN;//待签约
        $lock_key = md5(RedisKey::WAREHOUSE_THREAD_SIGN_RECOMMIT_PRICE_LOCK_PREFIX . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return ThreadService::getInstance()->addPrice($params, $this->user);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 处理仓库线索-待验证-列表
     * @Permission(menu='warehouse.thread.handle.verify')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/88940
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function verifyListAction()
    {
        $params = trim_array($this->request->get());
        $params = BaseService::handleParams($params, ThreadService::$not_must_params);
        Validation::validate($params, ThreadService::$validate_list);
        $res = ThreadService::getInstance()->list($params, $this->user, ThreadService::THREAD_VERIFY_LIST);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 处理仓库线索-待验证-转交
     * @Permission(action='warehouse.thread.handle.verify.transfer')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/89096
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function verifyTransferAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, ThreadService::$validate_verify_transfer);
        $lock_key = md5(RedisKey::WAREHOUSE_THREAD_VERIFY_TRANSFER_LOCK_PREFIX . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return ThreadService::getInstance()->verifyTransfer($params, $this->user);
        }, $lock_key, 5);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 处理仓库线索-待验证-录入验证结果-查看
     * @Permission(action='warehouse.thread.handle.verify.verify')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/89099
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function verifyViewAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, ThreadService::$validate_id);
        $res = ThreadService::getInstance()->verifyView($params['id']);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 处理仓库线索-待验证-录入验证结果-完成录入
     * @Permission(action='warehouse.thread.handle.verify.verify')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/89105
     * @return Response|ResponseInterface
     * @throws Exception
     */
    public function verifyDoneAction()
    {
        $params = trim_array($this->request->get());

        ThreadService::checkValidateVerifyDone($params);

        $lock_key = md5(RedisKey::WAREHOUSE_THREAD_VERIFY_TRANSFER_LOCK_PREFIX . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return ThreadService::getInstance()->verifyDone($params, $this->user);
        }, $lock_key, 5);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

}
