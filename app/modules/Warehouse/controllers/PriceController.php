<?php
/**
 * 仓库管理 - 仓库报价审核
 */
namespace App\Modules\Warehouse\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Library\Exception\BusinessException;
use App\Modules\Warehouse\Services\BaseService;
use App\Modules\Warehouse\Services\PriceService;
use App\Modules\Warehouse\Services\RequirementService;
use App\Modules\Warehouse\Services\ThreadService;
use App\Modules\Warehouse\Services\WarehouseService;
use App\Util\RedisKey;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class PriceController extends BaseController
{
    /**
     * 待处理-列表
     * @Permission(menu='warehouse.price.wait')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/89267
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function waitListAction()
    {
        $params = trim_array($this->request->get());
        $params = BaseService::handleParams($params, PriceService::$not_must_params);
        Validation::validate($params, PriceService::$validate_list);
        $res = PriceService::getInstance()->list($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 待处理-驳回
     * @Permission(menu='warehouse.price.wait')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/89285
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function rejectAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, PriceService::$validate_reject);
        $lock_key = md5(RedisKey::WAREHOUSE_THREAD_PRICE_REJECT_LOCK_PREFIX . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return PriceService::getInstance()->reject($params, $this->user);
        }, $lock_key, 10);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 待处理-同意
     * @Permission(menu='warehouse.price.wait')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/89288
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function passAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, PriceService::$validate_pass);
        $lock_key = md5(RedisKey::WAREHOUSE_THREAD_PRICE_PASS_LOCK_PREFIX . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return PriceService::getInstance()->pass($params, $this->user);
        }, $lock_key, 10);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 已处理-列表
     * @Permission(menu='warehouse.price.done')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/89270
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function doneListAction()
    {
        $params = trim_array($this->request->get());
        $params = BaseService::handleParams($params, PriceService::$not_must_params);
        Validation::validate($params, PriceService::$validate_list);
        $res = PriceService::getInstance()->list($params, $this->user, PriceService::DONE_LIST);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 查看
     * @Permission(menu='warehouse.price')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/89294
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function viewAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, PriceService::$validate_id);
        $res = PriceService::getInstance()->view($params['id'], $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 查看-需求
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/90263
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function viewRequirementAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, RequirementService::$validate_id);
        $res = RequirementService::getInstance()->view($params['id']);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 查看-线索
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/90266
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function viewThreadAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, ThreadService::$validate_id);
        $res = ThreadService::getInstance()->view($params['id']);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }
}
