<?php

namespace App\Modules\Warehouse\Services;

use App\Library\Enums;
use App\Library\Enums\ContractEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\Enums\WarehouseEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Models\backyard\SysCityModel;
use App\Models\backyard\SysDistrictModel;
use App\Models\backyard\SysProvinceModel;
use App\Models\oa\WarehouseContractSnapshotModel;
use App\Models\oa\WarehouseStoreSnapshotModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Contract\Models\ContractStoreRentingModel;
use App\Modules\Contract\Models\SysManageRegionModel;
use App\Modules\Contract\Services\ContractStoreRentingService;
use App\Repository\backyard\PieceRepository;
use App\Repository\backyard\RegionRepository;
use App\Repository\HrStaffRepository;
use App\Repository\StoreRepository;

class BaseService extends \App\Library\BaseService
{
    //附件-参数验证
    public static $validate_attachments = [
        'attachments[*]'             => 'Required|Obj',
        'attachments[*].bucket_name' => 'Required|StrLenGeLe:1,63',
        'attachments[*].object_key'  => 'Required|StrLenGeLe:1,100',
        'attachments[*].file_name'   => 'Required|StrLenGeLe:1,200'
    ];

    //平面图-参数验证
    public static $validate_plan_attachments = [
        'plan_attachments[*]'             => 'Required|Obj',
        'plan_attachments[*].bucket_name' => 'Required|StrLenGeLe:1,63',
        'plan_attachments[*].object_key'  => 'Required|StrLenGeLe:1,100',
        'plan_attachments[*].file_name'   => 'Required|StrLenGeLe:1,200'
    ];

    /**
     * 过滤非必须参数
     * @param array $params 参数组
     * @param array $not_must 非必须参数组
     * @return mixed
     */
    public static function handleParams($params, $not_must)
    {
        if (empty($not_must) || !is_array($not_must)) {
            return $params;
        }
        foreach ($not_must as $value) {
            if (isset($params[$value]) && empty($params[$value])) {
                unset($params[$value]);
            }
        }
        return $params;
    }

    /**
     * 公共参数验证 - 附件、经纬度
     * @param array $params 参数组
     * @param array $validation 验证器
     * @param bool $is_price 是否是报价提交，true是，false否
     * @throws ValidationException
     */
    public static function commonValidate(array $params, array $validation, bool $is_price = false)
    {
        //附件
        if (!empty($params['attachments'])) {
            $validation = array_merge($validation, self::$validate_attachments);
        }

        //平面图
        if (!empty($params['plan_attachments'])) {
            $validation = array_merge($validation, self::$validate_plan_attachments);
        }

        // 各国仓库地理位置配置
        $warehouse_geo_config = EnumsService::getInstance()->getSettingEnvValueMap('warehouse_geo_config');
        if (empty($warehouse_geo_config)) {
            throw new ValidationException(static::$t->_('warehouse_geo_config_empty'), ErrCode::$VALIDATE_ERROR);
        }
        $base_rule          = $warehouse_geo_config['base_rule'];
        $geo_scope          = $warehouse_geo_config['geo_scope'];
        $lower_country_code = strtolower(get_country_code());

        // 必填和基本规则校验
        $warehouse_latitude_error  = $lower_country_code . '_warehouse_latitude_error';
        $warehouse_longitude_error = $lower_country_code . '_warehouse_longitude_error';
        $validation['warehouse_longitude'] = "Required|Regexp:{$base_rule['longitude']}|>>>:" . static::$t->_($warehouse_longitude_error); //经度
        $validation['warehouse_latitude']  = "Required|Regexp:{$base_rule['latitude']}|>>>:" . static::$t->_($warehouse_latitude_error);   //纬度

        //报价独有
        if ($is_price) {
            $validation['last_price_warehouse_longitude'] = "Required|Regexp:{$base_rule['longitude']}|>>>:" . static::$t->_($warehouse_longitude_error); //上次报价信息-仓库纬度
            $validation['last_price_warehouse_latitude']  = "Required|Regexp:{$base_rule['latitude']}|>>>:" . static::$t->_($warehouse_latitude_error);   //上次报价信息-仓库经度
        }

        Validation::validate($params, $validation);

        // 仓库经纬度范围区间判断
        if ($params['warehouse_longitude'] < $geo_scope['longitude']['min'] || $params['warehouse_longitude'] > $geo_scope['longitude']['max']) {
            throw new ValidationException(static::$t->_($warehouse_longitude_error), ErrCode::$VALIDATE_ERROR);
        }

        // 纬度
        if ($params['warehouse_latitude'] < $geo_scope['latitude']['min'] || $params['warehouse_latitude'] > $geo_scope['latitude']['max']) {
            throw new ValidationException(static::$t->_($warehouse_latitude_error), ErrCode::$VALIDATE_ERROR);
        }

        //报价独有
        if ($is_price) {
            // 仓库经纬度范围区间判断
            if ($params['last_price_warehouse_longitude'] < $geo_scope['longitude']['min'] || $params['last_price_warehouse_longitude'] > $geo_scope['longitude']['max']) {
                throw new ValidationException(static::$t->_($warehouse_longitude_error), ErrCode::$VALIDATE_ERROR);
            }

            // 纬度
            if ($params['last_price_warehouse_latitude'] < $geo_scope['latitude']['min'] || $params['last_price_warehouse_latitude'] > $geo_scope['latitude']['max']) {
                throw new ValidationException(static::$t->_($warehouse_latitude_error), ErrCode::$VALIDATE_ERROR);
            }

        }
    }

    /**
     * 仓库变更提交, 仓库状态变更为: 变更审批中
     *
     * @param $warehouse_model
     * @param $user
     * @return bool
     * @throws BusinessException
     */
    protected function syncUpdateWarehouseStatus($warehouse_model, $user)
    {
        $update_data = [
            'warehouse_status' => ContractEnums::WAREHOUSE_STATUS_APPROVALING,
            'updated_id'       => $user['id'],
            'updated_at'       => date('Y-m-d H:i:s'),
        ];
        if ($warehouse_model->i_update($update_data) === false) {
            throw new BusinessException('仓库变更提交, 仓库主表状态更新失败=' . get_data_object_error_msg($warehouse_model),
                ErrCode::$BUSINESS_ERROR);
        }

        return true;
    }

    /**
     * 仓库变更提交, 添加仓库关联合同的快照
     *
     * @param int $change_apply_id
     * @param string $warehouse_id
     * @param array $user
     * @return bool
     * @throws BusinessException
     */
    protected function addWarehouseContractSnapshot(int $change_apply_id, string $warehouse_id, array $user)
    {
        $contract_snapshot_data = [];
        $releated_contract_list = ContractStoreRentingService::getInstance()->getRelatedListByWarehouseId($warehouse_id);
        foreach ($releated_contract_list as $contract) {
            $contract_snapshot_data[] = [
                'change_apply_id'     => $change_apply_id,
                'warehouse_id'        => $warehouse_id,
                'contract_no'         => $contract['contract_no'],
                'contract_type'       => $contract['contract_type'],
                'contract_begin_date' => $contract['contract_begin_date'],
                'contract_end_date'   => $contract['contract_end_date'],
                'contract_status'     => $contract['contract_status'],
                'archive_status'      => $contract['archive_status'],
                'created_id'          => $user['id'],
                'created_at'          => date('Y-m-d H:i:s'),
                'updated_id'          => $user['id'],
                'updated_at'          => date('Y-m-d H:i:s'),
            ];
        }
        $this->logger->info('仓库变更提交: warehouse_contract_snapshot=' . json_encode($contract_snapshot_data,
                JSON_UNESCAPED_UNICODE));
        if (!empty($contract_snapshot_data)) {
            $contract_snapshot_model = new WarehouseContractSnapshotModel();
            if ($contract_snapshot_model->batch_insert($contract_snapshot_data) === false) {
                throw new BusinessException('仓库变更提交-关联合同快照表写入失败', ErrCode::$BUSINESS_ERROR);
            }
        }

        return true;
    }

    /**
     * 仓库变更提交, 添加仓库网点的快照
     *
     * @param int $change_apply_id
     * @param string $warehouse_id
     * @param array $store_list
     * @param array $user
     * @return bool
     * @throws BusinessException
     */
    protected function addWarehouseStoreSnapshot(
        int $change_apply_id,
        string $warehouse_id,
        array $store_list,
        array $user
    ) {
        $store_snapshot_data = [];
        $all_store_list      = $this->getWarehouseStoreDetail($store_list);
        foreach ($all_store_list as $store) {
            $store_snapshot_data[] = [
                'change_apply_id' => $change_apply_id,
                'warehouse_id'    => $warehouse_id,
                'store_id'        => $store['store_id'],
                'store_name'      => $store['store_name'],
                'store_category'  => $store['store_category'],
                'province_code'   => $store['province_code'],
                'province_name'   => $store['province_name'],
                'city_code'       => $store['city_code'],
                'city_name'       => $store['city_name'],
                'district_code'   => $store['district_code'],
                'district_name'   => $store['district_name'],
                'manage_region'   => $store['manage_region'],
                'region_name'     => $store['region_name'],
                'postal_code'     => $store['postal_code'],
                'detail_address'  => $store['detail_address'],
                'store_flag'      => $store['store_flag'],
                'begin_date'      => $store['begin_date'],
                'end_date'        => !empty($store['end_date']) ? $store['end_date'] : null,
                'use_status'      => ContractEnums::WAREHOUSE_STORE_USE_STATUS_ING,
                'created_id'      => $user['id'],
                'created_at'      => date('Y-m-d H:i:s'),
                'updated_id'      => $user['id'],
                'updated_at'      => date('Y-m-d H:i:s'),
            ];
        }

        $this->logger->info('仓库变更提交: warehouse_store_snapshot=' . json_encode($store_snapshot_data,
                JSON_UNESCAPED_UNICODE));
        if (!empty($store_snapshot_data)) {
            $store_snapshot_model = new WarehouseStoreSnapshotModel();
            if ($store_snapshot_model->batch_insert($store_snapshot_data) === false) {
                throw new BusinessException('仓库变更提交-网点快照表写入失败', ErrCode::$BUSINESS_ERROR);
            }
        }

        return true;
    }

    /**
     * 获取网点详细区域信息
     *
     * @param array $warehouse_store_list
     * @return array
     */
    protected function getWarehouseStoreDetail(array $warehouse_store_list)
    {
        $warehouse_store_list = array_values(array_filter($warehouse_store_list));
        if (empty($warehouse_store_list)) {
            return [];
        }

        // 获取仓库网点信息
        $store_ids  = array_column($warehouse_store_list, 'store_id');
        $store_list = (new StoreRepository())->getStoreListByIds($store_ids, null);

        // 获取网点的省/市/区/大区/地址
        $store_list = $this->getAreaInfo($store_list);

        // 大区ID
        $manage_region_ids = array_filter(array_unique(array_column($store_list, 'manage_region')));

        // 大区
        $manage_region_map = [];
        if (!empty($manage_region_ids)) {
            $manage_region_map = SysManageRegionModel::find([
                'conditions' => 'id IN ({ids:array})',
                'bind'       => ['ids' => array_values($manage_region_ids)],
                'columns'    => ['id', 'name'],
            ])->toArray();
            $manage_region_map = array_column($manage_region_map, 'name', 'id');
        }

        foreach ($store_list as &$info) {
            // 大区
            $info['region_name'] = $manage_region_map[$info['manage_region']] ?? '';

            // 网点省份大区
            $info['province_region_name'] = $info['province_name'] . ',' . $info['region_name'];

            // 详细地址[网点信息用: 网点详细地址和省/市/区的拼接]
            $info['full_detail_address'] = trim($info['detail_address'] . ' ' . $info['district_name'] . '-' . $info['city_name'] . '-' . $info['province_name'] . '-' . $info['postal_code']);
        }

        // 网点类型和名称映射
        $store_cate_map = $this->getStoreCategoryMap();

        // 仓库网点类型
        foreach ($warehouse_store_list as &$val) {
            // 获取网点类型
            $val['store_category_code'] = $store_cate_map[$val['store_category']] ?? '';

            // 网点信息
            $store_info = $store_list[$val['store_id']] ?? [];
            if (!empty($store_info)) {
                $val['store_name']           = $store_info['name'];
                $val['manage_region']        = $store_info['manage_region'] ?? '';
                $val['region_name']          = $store_info['region_name'] ?? '';
                $val['province_code']        = $store_info['province_code'] ?? '';
                $val['province_name']        = $store_info['province_name'] ?? '';
                $val['city_code']            = $store_info['city_code'] ?? '';
                $val['city_name']            = $store_info['city_name'] ?? '';
                $val['district_code']        = $store_info['district_code'] ?? '';
                $val['district_name']        = $store_info['district_name'] ?? '';
                $val['province_region_name'] = $store_info['province_region_name'] ?? '';
                $val['detail_address']       = $store_info['detail_address'] ?? '';
                $val['full_detail_address']  = $store_info['full_detail_address'] ?? '';
                $val['postal_code']          = $store_info['postal_code'] ?? '';
            } else {
                $val['store_name']           = $val['store_id'] == Enums::PAYMENT_HEADER_STORE_ID ? Enums::PAYMENT_HEADER_STORE_NAME : '';
                $val['manage_region']        = '';
                $val['region_name']          = '';
                $val['province_code']        = '';
                $val['province_name']        = '';
                $val['city_code']            = '';
                $val['city_name']            = '';
                $val['district_code']        = '';
                $val['district_name']        = '';
                $val['province_region_name'] = '';
                $val['detail_address']       = '';
                $val['full_detail_address']  = '';
                $val['postal_code']          = '';
            }
        }

        return $warehouse_store_list;
    }

    /**
     * 实时获取仓库/网点的省/市/区名称
     *
     * @param array $warehouse_list
     * @return array
     */
    public function getAreaInfo(array $warehouse_list = [])
    {
        $warehouse_list = array_filter($warehouse_list);
        if (empty($warehouse_list)) {
            return [];
        }

        $province_codes = array_filter(array_unique(array_column($warehouse_list, 'province_code')));
        $city_codes     = array_filter(array_unique(array_column($warehouse_list, 'city_code')));
        $district_codes = array_filter(array_unique(array_column($warehouse_list, 'district_code')));

        $name_field = static::$language == 'th' ? 'name' : 'en_name';

        // 省
        $province_map = [];
        if (!empty($province_codes)) {
            $province_map = SysProvinceModel::find([
                'conditions' => 'code IN ({codes:array})',
                'bind'       => ['codes' => array_values($province_codes)],
                'columns'    => ['code', "$name_field AS name"],
            ])->toArray();
            $province_map = array_column($province_map, 'name', 'code');
        }

        // 市
        $city_map = [];
        if (!empty($city_codes)) {
            $city_map = SysCityModel::find([
                'conditions' => 'code IN ({codes:array})',
                'bind'       => ['codes' => array_values($city_codes)],
                'columns'    => ['code', "$name_field AS name"],
            ])->toArray();
            $city_map = array_column($city_map, 'name', 'code');
        }

        // 区
        $district_map = [];
        if (!empty($district_codes)) {
            $district_map = SysDistrictModel::find([
                'conditions' => 'code IN ({codes:array})',
                'bind'       => ['codes' => array_values($district_codes)],
                'columns'    => ['code', "$name_field AS name"],
            ])->toArray();
            $district_map = array_column($district_map, 'name', 'code');
        }

        foreach ($warehouse_list as &$info) {
            $info['province_name'] = $province_map[$info['province_code']] ?? '';
            $info['city_name']     = $city_map[$info['city_code']] ?? '';
            $info['district_name'] = $district_map[$info['district_code']] ?? '';
        }

        return $warehouse_list;
    }

    /**
     * 格式化变更记录
     *
     * @param array $current_change_records
     * @return array
     */
    protected function formatChangeRecord(array $current_change_records)
    {
        if (empty($current_change_records)) {
            return [];
        }

        foreach ($current_change_records as &$record) {
            $record['created_at'] = mb_substr($record['created_at'], 0, 10);

            // 修改前
            $record['before_data'] = $this->decodeChangeRecordData($record['before_data']);

            // 修改后
            $record['after_data'] = $this->decodeChangeRecordData($record['after_data']);
        }

        return $current_change_records;
    }

    /**
     * 解析变更记录中的json结构
     *
     * @param string $json_data
     * @return array
     */
    protected function decodeChangeRecordData(string $json_data)
    {
        $json_data_item = json_decode($json_data, true);
        if (empty($json_data_item)) {
            return (object)[];
        }

        // 共用网点排序处理: 按开始使用时间倒序
        if (isset($json_data_item['warehouse_share_store_info'])) {
            $json_data_item['warehouse_share_store_info'] = array_sort($json_data_item['warehouse_share_store_info'],
                'begin_date', SORT_DESC);
        }

        $store_cate_map = $this->getStoreCategoryMap();

        $result      = [];
        $translation = static::$t;
        foreach ($json_data_item as $base_item_k => $base_item) {
            $item = [];

            foreach ($base_item as $sub_item_index => $sub_item) {
                // 数据项为索引二维数组
                if (is_numeric($sub_item_index)) {
                    $all_sub_item = [];
                    foreach ($sub_item as $sub_item_field_k => $sub_item_field_v) {
                        if ($sub_item_field_k == 'store_category') {
                            $all_sub_item[$translation[$sub_item_field_k]] = $store_cate_map[$sub_item_field_v] ?? '';
                        } else {
                            if ($sub_item_field_k == 'warehouse_status') {
                                $all_sub_item[$translation[$sub_item_field_k]] = $translation[ContractEnums::$warehouse_status_enums[$sub_item_field_v] ?? ''] ?? '';
                            } else {
                                $all_sub_item[$translation[$sub_item_field_k]] = $sub_item_field_v;
                            }
                        }
                    }
                    $item[$sub_item_index] = $all_sub_item;
                } else {
                    // 数据项为一维数组
                    if ($sub_item_index == 'store_category') {
                        $item[0][$translation[$sub_item_index]] = $store_cate_map[$sub_item] ?? '';
                    } else {
                        if ($sub_item_index == 'warehouse_status') {
                            $item[0][$translation[$sub_item_index]] = $translation[ContractEnums::$warehouse_status_enums[$sub_item] ?? ''] ?? '';
                        } else {
                            $item[0][$translation[$sub_item_index]] = $sub_item;
                        }
                    }
                }
            }

            $result[$translation[$base_item_k]] = $item;
        }

        return $result;
    }

    /**
     * 获取网点类型和名称映射结构
     */
    protected function getStoreCategoryMap()
    {
        static $store_cate_map = null;
        if (is_null($store_cate_map)) {
            $store_cate_map = ContractStoreRentingService::getInstance()->getAllStoreCate();
            $store_cate_map = array_column($store_cate_map, 'name', 'id');
        }

        return $store_cate_map;
    }


    /**
     * 格式化网点详细区域信息
     *
     * @param array $store_list
     * @return array
     */
    protected function formatStoreSnapshotDetail(array $store_list)
    {
        $store_list = array_values(array_filter($store_list));
        if (empty($store_list)) {
            return [];
        }

        // 网点类型和名称映射
        $store_cate_map = $this->getStoreCategoryMap();

        foreach ($store_list as &$info) {
            $info['store_category_code'] = $store_cate_map[$info['store_category']] ?? '';

            // 网点省份大区
            $info['province_region_name'] = $info['province_name'] . ',' . $info['region_name'];

            // 详细地址[网点信息用: 网点详细地址和省/市/区的拼接]
            $info['full_detail_address'] = trim($info['detail_address'] . ' ' . $info['district_name'] . '-' . $info['city_name'] . '-' . $info['province_name'] . '-' . $info['postal_code']);

            $info['end_date'] = !empty($info['end_date']) ? $info['end_date'] : '';

            unset($info['province_name'], $info['city_name'], $info['district_name'], $info['region_name'], $info['detail_address'], $info['postal_code']);
        }

        return $store_list;
    }


    /**
     * 网点信息是否变更
     *
     * @param array $new_store_info
     * @param array $old_store_info
     * @return bool true-有变更; false-无变更
     */
    protected function isChangedStoreInfo(array $new_store_info, array $old_store_info)
    {
        $_new_store_info = [
            $new_store_info['store_category'] ?? '',
            $new_store_info['store_id'] ?? '',
            $new_store_info['begin_date'] ?? '',
            $new_store_info['end_date'] ?? '',
        ];

        $_old_store_info = [
            $old_store_info['store_category'] ?? '',
            $old_store_info['store_id'] ?? '',
            $old_store_info['begin_date'] ?? '',
            $old_store_info['end_date'] ?? '',
        ];

        return !arrays_is_equal($_new_store_info, $_old_store_info);
    }

    /**
     * 获取网点名字
     *
     * @param string $store_id
     * @return mixed|string
     */
    protected function getStoreNameByStoreId(string $store_id)
    {
        if ($store_id == Enums::PAYMENT_HEADER_STORE_ID) {
            $store_name = Enums::PAYMENT_HEADER_STORE_NAME;
        } else {
            $store_name = (new StoreRepository())->getStoreDetail($store_id, null)['name'] ?? '';
        }

        return $store_name;
    }

    /**
     * 仓库管理-仓库需求管理-地区配置
     * @param bool $is_format 是否格式化为枚举，true是，false否
     * @return array
     */
    public function getRequirementArea($is_format = false)
    {
        $area = EnumsService::getInstance()->getSettingEnvValueIds('warehouse_requirement_area');
        if (!$is_format) {
            return $area;
        }

        $data = [];
        foreach ($area as $item) {
            $data[] = [
                'value' => $item,
                'label' => $item
            ];
        }
        return $data;
    }

    /**
     * 获取验证责任人
     * 验证责任人取需求网点中的在职(不含待离职)的网点主管，
     * 如果没有网点主管或者网点主管非在职，
     * 则为DM，如果DM非在职或无DM则为AM,
     * 如果AM非在职或无AM，则取固定配置的工号，只会配置一个工号，系统参数中增加配置
     * @param string $store_id 网点编号
     * @return array
     * @throws ValidationException
     */
    public function getPersonLiable($store_id)
    {
        $hr_staff_repository = new HrStaffRepository();
        $store_info = (new StoreRepository())->getStoreDetail($store_id, 0);
        if ($store_info) {
            //获取网点主管
            $person_liable_info = $hr_staff_repository->getStaffById($store_info['manager_id']);
            if ($person_liable_info && $person_liable_info['state'] == StaffInfoEnums::STAFF_STATE_IN && $person_liable_info['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_NO) {
                return $person_liable_info;
            }

            //网点主管非在职，则取DM
            $piece_info = PieceRepository::getInstance()->getPieceInfoById($store_info['manage_piece'], 2);
            $person_liable_info = $hr_staff_repository->getStaffById($piece_info['manager_id'] ?? 0);
            if ($person_liable_info && $person_liable_info['state'] == StaffInfoEnums::STAFF_STATE_IN && $person_liable_info['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_NO) {
                return $person_liable_info;
            }

            //DM非在职，则取AM
            $region_info = RegionRepository::getInstance()->getRegionInfoById($store_info['manage_region'], 2);
            $person_liable_info = $hr_staff_repository->getStaffById($region_info['manager_id'] ?? 0);
            if ($person_liable_info && $person_liable_info['state'] == StaffInfoEnums::STAFF_STATE_IN && $person_liable_info['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_NO) {
                return $person_liable_info;
            }
        }

        //若都不满足，则取配置
        $warehouse_thread_person_liable_id = EnumsService::getInstance()->getSettingEnvValue('warehouse_thread_person_liable_id');
        $person_liable_info = $hr_staff_repository->getStaffById($warehouse_thread_person_liable_id);

        //配置不存，或者非在职，告警
        if (empty($person_liable_info) || $person_liable_info['state'] != StaffInfoEnums::STAFF_STATE_IN || $person_liable_info['wait_leave_state'] != StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_NO) {
            throw new ValidationException(static::$t->_('warehouse_thread_verify_staff_empty'), ErrCode::$VALIDATE_ERROR);
        }

        return $person_liable_info;
    }

    /**
     * 获取某个仓库下的合同数
     * @param string $warehouse_id 仓库ID
     * @param int $contract_status 合同状态：1待审核,2审核驳回,3审核通过,4已撤回 (迁移新建)
     * @return mixed
     */
    public function getWarehouseContractCount($warehouse_id, $contract_status = 0)
    {
        return ContractStoreRentingModel::count([
            'conditions' => 'warehouse_id = :warehouse_id:' . ($contract_status ? ' and contract_status = :contract_status:' : ''),
            'bind' => $contract_status ? ['warehouse_id' => $warehouse_id, 'contract_status' => $contract_status] : ['warehouse_id' => $warehouse_id]
        ]);
    }

    /**
     * 获取某个仓库下的合同数据
     * @param string $warehouse_id 仓库ID
     * @param int $contract_status 合同状态：1待审核,2审核驳回,3审核通过,4已撤回 (迁移新建)
     * @return mixed
     */
    public function getWarehouseContractList($warehouse_id, $contract_status = 0)
    {
        return ContractStoreRentingModel::find([
            'columns'    => 'id, contract_leader_id',
            'conditions' => 'warehouse_id = :warehouse_id:' . ($contract_status ? ' and contract_status = :contract_status:' : ''),
            'bind'       => $contract_status ? ['warehouse_id' => $warehouse_id, 'contract_status' => $contract_status] : ['warehouse_id' => $warehouse_id],
        ])->toArray();
    }

    /**
     * 获取仓库类型枚举值
     * @param int $scope_type 0-全部; 1-可用的
     * @param int $return_type 0-数组  1-字符串
     * @return array
     */
    public static function getWarehouseTypeEnums(int $scope_type = 0, int $return_type = 0)
    {
        $warehouse_type_config = EnumsService::getInstance()->getSettingEnvValueMap('warehouse_management_warehouse_type');
        if ($scope_type) {
            $warehouse_type_config = filter_array_by_field_value($warehouse_type_config, 'is_available', '1');
        }

        $enums = array_column($warehouse_type_config, 'value');
        return $return_type == 1 ? implode(',', $enums) : $enums;
    }

    /**
     * 获取仓库类型常量定义
     */
    public static function getWarehouseTypeConstantConfig()
    {
        static $enums = [];
        if (empty($enums)) {
            $enums = EnumsService::getInstance()->getSettingEnvValueMap('warehouse_type_const_definition');
        }

        return $enums;
    }

    /**
     * 获取续约仓库类型的枚举值
     */
    public static function getWarehouseTypeRenewedEnums()
    {
        static $enums_value = null;
        if (is_null($enums_value)) {
            $enums_value = static::getWarehouseTypeConstantConfig()[WarehouseEnums::REQUIREMENT_WAREHOUSE_TYPE_RENEWED] ?? null;
        }

        return $enums_value;
    }

    /**
     * 获取更换地址仓库类型的枚举值
     */
    public static function getWarehouseTypeChangeAddrEnums()
    {
        static $enums_value = null;
        if (is_null($enums_value)) {
            $enums_value = static::getWarehouseTypeConstantConfig()[WarehouseEnums::REQUIREMENT_WAREHOUSE_TYPE_CHANGE_ADDR] ?? null;
        }

        return $enums_value;
    }

    /**
     * 获取仓库类型kv
     */
    public static function getWarehouseTypeKeyValueMap()
    {
        static $kv = [];
        if (empty($kv)) {
            $warehouse_type_config = EnumsService::getInstance()->getSettingEnvValueMap('warehouse_management_warehouse_type');
            $kv                    = array_column($warehouse_type_config, 'key', 'value');
        }

        return $kv;
    }

    /**
     * 获取自定义配置的网点类型kvMap
     */
    public static function getSettingEnvStoreCategoryMap()
    {
        static $store_category_map = [];
        if (empty($store_category_map)) {
            $store_category_map = EnumsService::getInstance()->getSettingEnvValueMap('warehouse_store_category');
            $store_category_map = array_column($store_category_map, 'label', 'value');
        }

        return $store_category_map;
    }

    /**
     * 获取自定义配置的网点类型枚举配置
     */
    public static function getSettingEnvStoreCategoryEnums()
    {
        static $store_category = [];
        if (empty($store_category)) {
            $store_category = EnumsService::getInstance()->getSettingEnvValueMap('warehouse_store_category');
        }

        return $store_category;
    }

}