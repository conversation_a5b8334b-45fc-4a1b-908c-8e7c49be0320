<?php
/**
 * 仓库管理 - 仓库需求管理
 */

namespace App\Modules\Warehouse\Services;

use App\Library\Enums;
use App\Library\Enums\DownloadCenterEnum;
use App\Library\Enums\ImportCenterEnums;
use App\Library\Enums\WarehouseEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\ByWorkflowEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\OssHelper;
use App\Library\Validation\ValidationException;
use App\Models\backyard\SysCityModel;
use App\Models\backyard\SysDistrictModel;
use App\Models\backyard\SysProvinceModel;
use App\Models\oa\SysAttachmentModel;
use App\Models\oa\WarehouseRequirementConfirmRecordModel;
use App\Models\oa\WarehouseRequirementModel;
use App\Models\oa\WarehouseThreadModel;
use App\Models\oa\WarehouseThreadPriceRecordModel;
use App\Models\oa\WarehouseThreadVerifyRecordModel;
use App\Modules\Common\Services\DownloadCenterService;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Common\Services\ImportCenterService;
use App\Modules\Common\Services\StoreService;
use App\Modules\Contract\Services\ContractStoreRentingService;
use App\Modules\Third\Services\ByWorkflowService;
use App\Modules\User\Models\StaffPermissionModel;
use App\Modules\User\Services\UserService;
use App\Repository\oa\ContractWarehouseRepository;
use App\Repository\oa\SysAttachmentRepository;
use App\Repository\oa\WarehouseRequirementRepository;
use App\Repository\oa\WarehouseThreadRepository;
use App\Util\RedisKey;
use Exception;
use GuzzleHttp\Exception\GuzzleException;

class RequirementService extends BaseService
{
    //列表类型
    const REQUIREMENT_LIST = 1;        //仓库需求管理-列表
    const REQUIREMENT_SEARCH_LIST = 2; //处理仓库需求-待寻找
    const REQUIREMENT_CONFIRM_LIST = 3;//处理仓库需求-待确认
    const REQUIREMENT_SETTLE_LIST = 4; //处理仓库需求-待入驻
    const REQUIREMENT_RENEWED_LIST = 5; //处理仓库需求-待续约

    //列表与导出类型映射
    public static $task_type = [
        self::REQUIREMENT_LIST         => DownloadCenterEnum::FINANCE_WAREHOUSE_REQUIREMENT_EXPORT, //仓库需求管理-导出
        self::REQUIREMENT_SEARCH_LIST  => DownloadCenterEnum::FINANCE_WAREHOUSE_REQUIREMENT_SEARCH_EXPORT, //处理仓库需求-待寻找-导出
        self::REQUIREMENT_CONFIRM_LIST => DownloadCenterEnum::FINANCE_WAREHOUSE_REQUIREMENT_CONFIRM_EXPORT, //处理仓库需求-待确认-导出
        self::REQUIREMENT_SETTLE_LIST  => DownloadCenterEnum::FINANCE_WAREHOUSE_REQUIREMENT_SETTLE_EXPORT,//处理仓库需求-待入驻-导出
        self::REQUIREMENT_RENEWED_LIST  => DownloadCenterEnum::FINANCE_WAREHOUSE_REQUIREMENT_RENEWED_EXPORT,//处理仓库需求-待续约-导出
    ];

    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    //仓库需求管理-新增-参数验证
    public static $validate_add = [
        'no'             => 'Required|StrLenGeLe:1,32',
        'store_id'       => 'Required|StrLenGeLe:1,10',                                             //网点编码
        'store_name'     => 'Required|StrLenGeLe:1,50',                                             //网点名称
        'region_id'      => 'StrLenGeLe:0,32',                                                      //大区id
        'region_name'    => 'StrLenGeLe:0,100',                                                     //大区名称
        'piece_id'       => 'StrLenGeLe:0,32',                                                      //片区id
        'piece_name'     => 'StrLenGeLe:0,100',                                                     //片区名称
        'opening_status' => 'Required|IntIn:' . WarehouseEnums::REQUIREMENT_OPENING_STATUS_VALIDATE,//网点开业状态
        'priority_level' => 'Required|IntIn:' . WarehouseEnums::REQUIREMENT_PRIORITY_LEVEL_VALIDATE,//优先级
        'province_code'  => 'Required|StrLenGeLe:1,10',                                             //意向地区-省编码
        'city_code'      => 'Required|StrLenGeLe:1,10',                                             //意向地区-市编码
        'district_code'  => 'Required|StrLenGeLe:1,10',                                             //意向地区-区编码
        'remark'         => 'StrLenGeLe:0,500',                                                     //备注
        'attachments'    => 'Arr|ArrLenGeLe:0,20',                                                  //附件
    ];

    //非必须参数
    public static $not_must_params = [
        'status',
        'opening_status',
        'warehouse_type',
        'priority_level',
        'area_min',
        'area_max',
        'expect_settled_date_start',
        'expect_settled_date_end',
        'actual_settled_date_start',
        'actual_settled_date_end',
        'store_categorys',
        'original_warehouse_ids',
        'parking_num_min',
        'parking_num_max',
        'pageNum',
        'pageSize'
    ];

    //仓库需求管理-列表-参数验证
    public static $validate_list = [
        'no'                        => 'Str',
        'store_id'                  => 'Str',
        'status'                    => 'Arr',
        'status[*]'                 => 'Int',
        'opening_status'            => 'IntIn:' . WarehouseEnums::REQUIREMENT_OPENING_STATUS_VALIDATE,
        'warehouse_type'            => 'Arr',
        'warehouse_type[*]'         => 'Int',
        'priority_level'            => 'Arr',
        'priority_level[*]'         => 'IntIn:' . WarehouseEnums::REQUIREMENT_PRIORITY_LEVEL_VALIDATE,
        'region_id'                 => 'Str',
        'piece_id'                  => 'Str',
        'province_code'             => 'Str',
        'city_code'                 => 'Str',
        'district_code'             => 'Str',
        'area_min'                  => 'FloatGe:0',
        'area_max'                  => 'FloatGe:0',
        'expect_settled_date_start' => 'Date',
        'expect_settled_date_end'   => 'Date',
        'actual_settled_date_start' => 'Date',
        'actual_settled_date_end'   => 'Date',
        'pageNum'                   => 'IntGt:0',
        'pageSize'                  => 'IntGt:0',
    ];

    //ID
    public static $validate_id = [
        'id' => 'Required|IntGt:0',
    ];

    //仓库需求管理-作废-参数验证
    public static $validate_cancel = [
        'id'            => 'Required|IntGt:0',
        'cancel_reason' => 'Required|StrLenGeLe:1,500',
    ];

    //处理需求管理-待寻找-录入线索-提交确认
    public static $validate_confirm = [
        'id'     => 'Required|IntGt:0',
        'remark' => 'Required|StrLenGeLe:1,500',
    ];

    //处理需求管理-待确认-去确认-满足需求
    public static $validate_confirm_ok = [
        'id'        => 'Required|IntGt:0',
        'thread_id' => 'Required|IntGt:0',
    ];

    //处理需求管理-待确认-去确认-全部不符合满足
    public static $validate_confirm_dont = [
        'id'             => 'Required|IntGt:0',
        'confirm_reason' => 'Required|StrLenGeLe:1,500',
    ];

    //处理需求管理-待入驻-待入驻-确认入驻
    public static $validate_settle = [
        'id'                        => 'Required|IntGt:0',
        'actual_settled_date'       => 'Required|Date',
        'actual_settled_store_id'   => 'Required|StrLenGeLe:1,10',
        'actual_settled_store_name' => 'Required|StrLenGeLe:1,50',
    ];

    /**
     * 仓库需求枚举
     * @return array
     */
    public function getOptionsDefault()
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        try {
            //需求状态、优先级、仓库类型、网点开业状态
            $data         = $this->getEnums();
            $data['area'] = self::getRequirementArea(true);

            $data['store_category'] = self::getSettingEnvStoreCategoryEnums();


        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->warning('仓库管理-仓库需求管理-获取枚举异常: ' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 获取需求状态、优先级、仓库类型、网点开业状态
     * @return array
     */
    public function getEnums()
    {
        $data = [];

        $enums_arr = [
            'status'         => WarehouseEnums::getRequirementStatus(),
            'priority_level' => WarehouseEnums::$priority_level,
            'opening_status' => WarehouseEnums::$opening_status,
        ];
        foreach ($enums_arr as $key => $value) {
            foreach ($value as $k => $v) {
                $data[$key][] = [
                    'value' => $k,
                    'label' => static::$t->_($v),
                ];
            }
        }

        $data['warehouse_type'] = $this->getWarehouseType();

        return $data;
    }

    /**
     * 仓库类型配置
     * @param int $scope_type 0-全部; 1-可用的
     * @return array
     */
    public function getWarehouseType(int $scope_type = 0)
    {
        static $warehouse_type_item = [];

        if (empty($warehouse_type_item)) {
            $warehouse_type_config = EnumsService::getInstance()->getSettingEnvValueMap('warehouse_management_warehouse_type');
            foreach ($warehouse_type_config as $config) {
                if ($scope_type && !$config['is_available']) {
                    continue;
                }

                $warehouse_type_item[] = [
                    'value'        => $config['value'],
                    'label'        => static::$t->_($config['key']),
                    'is_available' => $config['is_available'],
                ];
            }
        }

        return $warehouse_type_item;
    }

    /**
     * 仓库需求添加-获得默认值
     * @return array
     */
    public function getAddDefault()
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';

        $data = [];
        try {
            $data['no'] = static::genSerialNo(WarehouseEnums::REQUIREMENT_NO_PREFIX, RedisKey::WAREHOUSE_REQUIREMENT_ADD_COUNTER);
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('仓库管理-仓库需求管理-添加-获得默认值:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 添加参数拦截
     * @param array $params 参数组
     * @throws ValidationException
     */
    public function addValidation(array $params)
    {
        $validation                        = self::$validate_add;
        $validation['expect_settled_date'] = 'Required|Date|DateFrom:' . date('Y-m-d');//预期入驻日期
        $validation['area_min']            = 'Required|Regexp:' . WarehouseEnums::AREA_RULE . '|>>>:' . static::$t->_('warehouse_requirement_area_error');
        $validation['area_max']            = 'Required|Regexp:' . WarehouseEnums::AREA_RULE . '|>>>:' . static::$t->_('warehouse_requirement_area_error');

        // 仓库类型
        $warehouse_type_enums         = static::getWarehouseTypeEnums(1, 1);
        $validation['warehouse_type'] = 'Required|IntIn:' . $warehouse_type_enums . '|>>>:' . static::$t->_('params_error', ['param' => 'warehouse_type']);

        $country_code = get_country_code();

        //地区
        if ($country_code == GlobalEnums::PH_COUNTRY_CODE) {
            $area = self::getRequirementArea();
            if (empty($area)) {
                throw new ValidationException(static::$t->_('warehouse_requirement_area_set_empty'), ErrCode::$BUSINESS_ERROR);
            }

            $validation['area'] = 'Required|StrIn:' . implode(',', $area) . '|>>>:' . static::$t->_('warehouse_requirement_area_set_error');
        }

        if ($country_code == GlobalEnums::TH_COUNTRY_CODE) {
            $validation['original_warehouse_id'] = 'Required|StrLenGe:0|>>>:' . static::$t->_('params_error', ['param' => 'original_warehouse_id']);
            $validation['parking_num_min']       = 'Required|IntGtLe:0,9999|>>>:' . static::$t->_('params_error', ['param' => 'parking_num_min']);
            $validation['parking_num_max']       = 'Required|IntGtLe:0,9999|>>>:' . static::$t->_('params_error', ['param' => 'parking_num_max']);
        }

        self::commonValidate($params, $validation);

        //最大面积必须大于最小面积
        if ($params['area_max'] < $params['area_min']) {
            throw new ValidationException(static::$t->_('warehouse_requirement_save_error_003'), ErrCode::$VALIDATE_ERROR);
        }

        if ($country_code == GlobalEnums::TH_COUNTRY_CODE) {
            // 续约默认值
            $renewed_warehouse_type = static::getWarehouseTypeRenewedEnums();
            if (empty($renewed_warehouse_type)) {
                throw new ValidationException(static::$t->_('warehouse_requirement_save_error_001'), ErrCode::$VALIDATE_ERROR);
            }

            if ($params['warehouse_type'] == $renewed_warehouse_type && empty($params['original_warehouse_id'])) {
                throw new ValidationException(static::$t->_('params_error', ['param' => 'original_warehouse_id']), ErrCode::$VALIDATE_ERROR);
            }

            if ($params['parking_num_max'] < $params['parking_num_min']) {
                throw new ValidationException(static::$t->_('warehouse_requirement_save_error_002'), ErrCode::$VALIDATE_ERROR);
            }
        }

    }

    /**
     * 获取需求信息
     * @param integer $id 需求ID
     * @return mixed
     * @throws ValidationException
     */
    public function getRequirementInfo(int $id)
    {
        $requirement_model = WarehouseRequirementModel::findFirst([
            'conditions' => 'id = :id:',
            'bind' => ['id' => $id]
        ]);
        if (empty($requirement_model)) {
            throw new ValidationException(static::$t->_('warehouse_requirement_not_exists'), ErrCode::$VALIDATE_ERROR);
        }
        return $requirement_model;
    }

    /**
     * 不允许重复，新增保存时如果与现有数据中的编号重复，则自动取库中当前日期最大的序号+1，不需要拦截提交
     * @param string $no 需求编号
     * @return string
     * @throws BusinessException
     * @throws ValidationException
     */
    private function getRequirementNo(string $no)
    {
        // 验证单号是否已创建 或 占用
        $exist_no = WarehouseRequirementModel::findFirst([
            'conditions' => 'no = :no:',
            'bind'       => ['no' => $no],
            'columns'    => ['id'],
        ]);
        $make_count = 0;
        if (!empty($exist_no)) {
            $no = static::genSerialNo(WarehouseEnums::REQUIREMENT_NO_PREFIX, RedisKey::WAREHOUSE_REQUIREMENT_ADD_COUNTER);
            $make_count++;
            $requirement_nos[] = $no;
            if ($make_count >= 3) {
                throw new BusinessException("仓库需求ID生成已重试 {$make_count} 次, 请检查, 尝试生成的: " . json_encode($requirement_nos), ErrCode::$BUSINESS_ERROR);
            }
            return $this->getRequirementNo($no);
        }
        return $no;
    }

    /**
     * 仓库需求添加
     * @param array $params 参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function add(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $now = date('Y-m-d H:i:s');

            $status = WarehouseEnums::REQUIREMENT_STATUS_SEARCH;

            if (get_country_code() == GlobalEnums::TH_COUNTRY_CODE && $params['warehouse_type'] == static::getWarehouseTypeRenewedEnums()) {
                $original_warehouse_id        = $params['original_warehouse_id'];
                $renewed_enums_warehouse_type = static::getWarehouseTypeRenewedEnums();
                $pending_status               = [
                    WarehouseEnums::REQUIREMENT_STATUS_SIGN,
                    WarehouseEnums::REQUIREMENT_STATUS_PAY,
                    WarehouseEnums::REQUIREMENT_STATUS_PENDING_RENEWED,
                ];
                if (WarehouseRequirementRepository::getInstance()->isExistByOriginalWarehouseIdAndStatus($original_warehouse_id, $renewed_enums_warehouse_type, $pending_status)) {
                    throw new ValidationException(static::$t->_('warehouse_requirement_save_error_004'), ErrCode::$VALIDATE_ERROR);
                }

                $status = WarehouseEnums::REQUIREMENT_STATUS_PENDING_RENEWED;
            }

            $model = (new WarehouseRequirementModel());
            $data = [
                'no'                    => $this->getRequirementNo($params['no']),
                'apply_date'            => date('Y-m-d'),
                'store_id'              => $params['store_id'],
                'store_name'            => $params['store_name'],
                'region_id'             => $params['region_id'] ?? '',
                'region_name'           => $params['region_name'] ?? '',
                'piece_id'              => $params['piece_id'] ?? '',
                'piece_name'            => $params['piece_name'] ?? '',
                'area'                  => $params['area'] ?? '',
                'province_code'         => $params['province_code'],
                'city_code'             => $params['city_code'],
                'district_code'         => $params['district_code'],
                'warehouse_longitude'   => $params['warehouse_longitude'],
                'warehouse_latitude'    => $params['warehouse_latitude'],
                'area_min'              => $params['area_min'],
                'area_max'              => $params['area_max'],
                'expect_settled_date'   => $params['expect_settled_date'],
                'opening_status'        => $params['opening_status'],
                'warehouse_type'        => $params['warehouse_type'],
                'priority_level'        => $params['priority_level'],
                'status'                => $status,
                'remark'                => $params['remark'],
                'created_id'            => $user['id'],
                'created_name'          => $user['name'],
                'updated_id'            => $user['id'],
                'updated_name'          => $user['name'],
                'created_at'            => $now,
                'updated_at'            => $now,
                'store_category'        => $params['store_category'] ?? 0,
                'original_warehouse_id' => $params['original_warehouse_id'] ?? '',
                'parking_num_min'       => $params['parking_num_min'] ?? 0,
                'parking_num_max'       => $params['parking_num_max'] ?? 0,
            ];
            $bool  = $model->i_create($data);
            if ($bool === false) {
                throw new BusinessException('仓库管理-仓库需求管理-添加失败: ' . json_encode($data, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($model), ErrCode::$BUSINESS_ERROR);
            }

            //添加附件
            if (!empty($params['attachments'])) {
                $attachment_model = new SysAttachmentModel();
                $attach_arr       = [];
                foreach ($params['attachments'] as $attachment) {
                    $attach_arr[] = [
                        'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_WAREHOUSE_REQUIREMENT_INFO_FILE,
                        'oss_bucket_key'  => $model->id,
                        'sub_type'        => 0,
                        'bucket_name'     => $attachment['bucket_name'],
                        'object_key'      => $attachment['object_key'],
                        'file_name'       => $attachment['file_name'],
                    ];
                }
                if (!$attachment_model->batch_insert($attach_arr)) {
                    throw new BusinessException('仓库管理-仓库需求管理-添加附件失败: ' . json_encode($attach_arr, JSON_UNESCAPED_UNICODE) . '; 可能的原因是: ' . get_data_object_error_msg($attachment_model), ErrCode::$BUSINESS_ERROR);
                }
            }

            $db->commit();
        } catch (ValidationException $e) {
            $code         = $e->getCode();
            $message      = $e->getMessage();
        }  catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        } catch (Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }

        if ($message) {
            $db->rollback();
        }
        if ($real_message) {
            $this->logger->error('仓库管理-仓库需求管理-添加失败: ' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => [],
        ];
    }

    /**
     * 处理需求管理-待寻找-录入线索-提交确认
     * @param array $params 参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function addConfirm(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $requirement_info = $this->getRequirementInfo($params['id']);
            //只有待寻找的需求才能做此操作
            if ($requirement_info->status != WarehouseEnums::REQUIREMENT_STATUS_SEARCH) {
                throw new ValidationException(static::$t->_('warehouse_requirement_status_error', ['status' => static::$t->_(WarehouseEnums::getRequirementStatus()[WarehouseEnums::REQUIREMENT_STATUS_SEARCH])]), ErrCode::$VALIDATE_ERROR);
            }

            //先校验是否有关联的状态为待确认或被淘汰的线索，如果没有则提示“请先录入线索后再发起确认！”
            $relate_thread_list = WarehouseThreadRepository::getInstance()->onlySearchThread(['requirement_id' => $requirement_info->id, 'status' => [WarehouseEnums::THREAD_STATUS_CONFIRM, WarehouseEnums::THREAD_STATUS_ELIMINATE]]);
            if (empty($relate_thread_list)) {
                throw new ValidationException(static::$t->_('warehouse_requirement_confirm_thread_error'), ErrCode::$VALIDATE_ERROR);
            }

            $now = date('Y-m-d H:i:s');
            //1. 将仓库需求状态更新为待确认
            $requirement_info->status       = WarehouseEnums::REQUIREMENT_STATUS_CONFIRM;//待确认
            $requirement_info->updated_id   = $user['id'];
            $requirement_info->updated_name = $user['name'];
            $requirement_info->updated_at   = $now;
            $bool = $requirement_info->save();
            if ($bool === false) {
                throw new BusinessException('需求状态改为待确认失败: ' . json_encode($params, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($requirement_info), ErrCode::$BUSINESS_ERROR);
            }

            //2. 将当前需求关联的状态为待确认+被淘汰的线索状态改为确认中
            $relate_thread_ids = implode(',', array_column($relate_thread_list, 'id'));
            $db->updateAsDict(
                (new WarehouseThreadModel())->getSource(),
                ['status' => WarehouseEnums::THREAD_STATUS_CONFIRM_ING, 'updated_id' => $user['id'], 'updated_name' => $user['name'], 'updated_at' => $now],
                ['conditions' => "requirement_id = {$requirement_info->id} and id IN (" . $relate_thread_ids . ')']
            );

            //3. 记录需求确认记录
            $warehouse_requirement_confirm_record = new WarehouseRequirementConfirmRecordModel();
            $data = [
                'requirement_id' => $requirement_info->id,
                'requirement_no' => $requirement_info->no,
                'apply_date'     => date('Y-m-d'),
                'created_id'     => $user['id'],
                'created_name'   => $user['name'],
                'confirm_status' => 0,
                'remark'         => $params['remark'],
                'created_at'     => $now,
                'updated_at'     => $now,
            ];
            $bool = $warehouse_requirement_confirm_record->i_create($data);
            if ($bool === false) {
                throw new BusinessException('记录需求确认记录失败: ' . json_encode($data, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($warehouse_requirement_confirm_record), ErrCode::$BUSINESS_ERROR);
            }

            $db->commit();
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        } catch (Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }

        if ($message) {
            $db->rollback();
        }

        if ($real_message) {
            $this->logger->error('仓库管理-处理仓库需求-待确认-发起确认失败: ' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => [],
        ];
    }

    /**
     * 处理需求管理-待确认-去确认-满足需求
     * @param array $params 参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function updateConfirm(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $requirement_info = $this->getRequirementInfo($params['id']);
            //只有待确认的需求才能做此操作
            if ($requirement_info->status != WarehouseEnums::REQUIREMENT_STATUS_CONFIRM) {
                throw new ValidationException(static::$t->_('warehouse_requirement_status_error', ['status' => static::$t->_(WarehouseEnums::getRequirementStatus()[WarehouseEnums::REQUIREMENT_STATUS_CONFIRM])]), ErrCode::$VALIDATE_ERROR);
            }

            //选中的线索是否是确认中
            $select_thread_info = ThreadService::getInstance()->getThreadInfo($params['thread_id']);
            if ($select_thread_info->status != WarehouseEnums::THREAD_STATUS_CONFIRM_ING || $select_thread_info->requirement_id != $requirement_info->id) {
                throw new ValidationException(static::$t->_('warehouse_thread_confirm_status_error'), ErrCode::$VALIDATE_ERROR);
            }

            //获取验证责任人
            $person_liable_info = self::getPersonLiable($requirement_info->store_id);

            $now = date('Y-m-d H:i:s');
            //1. 将仓库需求状态更新为待报价，同时记录选中的线索ID
            $requirement_info->status       = WarehouseEnums::REQUIREMENT_STATUS_PRICE;//待报价
            $requirement_info->thread_id    = $select_thread_info->id;
            $requirement_info->thread_no    = $select_thread_info->no;
            $requirement_info->updated_id   = $user['id'];
            $requirement_info->updated_name = $user['name'];
            $requirement_info->updated_at   = $now;
            $bool = $requirement_info->save();
            if ($bool === false) {
                throw new BusinessException('需求状态改为待报价失败: ' . json_encode($params, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($requirement_info), ErrCode::$BUSINESS_ERROR);
            }

            //2. 将选中的线索状态更新为待报价
            $select_thread_info->status       = WarehouseEnums::THREAD_STATUS_PRICE;   //待报价
            $select_thread_info->updated_id   = $user['id'];
            $select_thread_info->updated_name = $user['name'];
            $select_thread_info->updated_at   = $now;
            $bool                             = $select_thread_info->save();
            if ($bool === false) {
                throw new BusinessException('线索状态改为待报价失败: ' . json_encode($params, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($select_thread_info), ErrCode::$BUSINESS_ERROR);
            }

            //3. 将未选中的当前需求关联的确认中的线索状态更新为待关联，同时解除线索与需求的关联关系
            $db->updateAsDict(
                (new WarehouseThreadModel())->getSource(),
                ['status' => WarehouseEnums::THREAD_STATUS_ASSOCIATION, 'requirement_id' => 0, 'requirement_no' => '', 'updated_id' => $user['id'], 'updated_name' => $user['name'], 'updated_at' => $now],
                ['conditions' => "requirement_id = {$requirement_info->id} and id != {$select_thread_info->id} and status = " . WarehouseEnums::THREAD_STATUS_CONFIRM_ING]
            );

            //4.更新需求确认记录
            $warehouse_requirement_confirm_record = WarehouseRequirementConfirmRecordModel::findFirst([
                'conditions' => 'requirement_id = :requirement_id: and confirm_status = :confirm_status:',
                'bind' => ['requirement_id' => $requirement_info->id, 'confirm_status' => 0]
            ]);
            $data = [
                'thread_id'      => $select_thread_info->id,
                'thread_no'      => $select_thread_info->no,
                'confirm_id'     => $user['id'],
                'confirm_name'   => $user['name'],
                'confirm_at'     => $now,
                'confirm_status' => WarehouseEnums::REQUIREMENT_CONFIRM_STATUS_OK,
                'confirm_reason' => '',
                'created_at'     => $now,
                'updated_at'     => $now,
            ];
            $bool = $warehouse_requirement_confirm_record->save($data);
            if ($bool === false) {
                throw new BusinessException('变更确认记录信息失败: ' . json_encode($data, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($warehouse_requirement_confirm_record), ErrCode::$BUSINESS_ERROR);
            }

            //5. 同时新增一条待验证数据，记录创建时间，验证责任人、状态为待验证
            $warehouse_thread_verify_record = new WarehouseThreadVerifyRecordModel();
            $verify_data = [
                'thread_id'          => $select_thread_info->id,
                'thread_no'          => $select_thread_info->no,
                'person_liable_id'   => $person_liable_info['staff_info_id'],
                'person_liable_name' => $person_liable_info['name'],
                'status'             => WarehouseEnums::THREAD_VERIFY_STATUS_VERIFY,
                'verify_date'        => null,
                'created_at'         => $now,
                'updated_at'         => $now,
            ];
            $bool = $warehouse_thread_verify_record->i_create($verify_data);
            if ($bool === false) {
                throw new BusinessException('保存验证记录信息失败: ' . json_encode($verify_data, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($warehouse_thread_verify_record), ErrCode::$BUSINESS_ERROR);
            }

            // 6.如果验证责任人无OA-仓库管理-处理仓库线索-待验证的菜单权限，则同时给责任人增加菜单权限
            $this->addVerifyPermission($person_liable_info['staff_info_id'], $user);
            $db->commit();
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        } catch (Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }

        if ($message) {
            $db->rollback();
        }

        if ($real_message) {
            $this->logger->error('仓库管理-处理需求管理-待确认-去确认-全部不符合满足失败: ' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => [],
        ];
    }

    /**
     * 给验证责任人增加验证的菜单权限
     * @param int $staff_info_id 验证责任人工号
     * @param array $user
     * @throws BusinessException
     */
    public function addVerifyPermission(int $staff_info_id, array $user)
    {
        $staff_permission = StaffPermissionModel::findFirst([
            'conditions' => 'staff_id = :staff_id:',
            'bind'       => ['staff_id' => $staff_info_id],
        ]);
        $permission_ids   = [];
        if ($staff_permission) {
            $has_permission_ids = explode(',', $staff_permission->permission_ids);
            if (!in_array(WarehouseEnums::THREAD_VERIFY_MENU_ID, $has_permission_ids)) {
                $permission_ids = array_unique(array_merge($has_permission_ids, WarehouseEnums::ADD_PERMISSION_IDS));
            }
        } else {
            $permission_ids = WarehouseEnums::ADD_PERMISSION_IDS;
        }

        $this->logger->info(['warehouse_add_verify_permission' => ['permission_ids' => $permission_ids, 'staff_id' => $staff_info_id]]);

        if (!empty($permission_ids)) {
            $bool = (new UserService())->setUserPermissions($staff_info_id, $permission_ids, $user);
            if ($bool === false) {
                throw new BusinessException('给责任人增加验证菜单权限失败: ' . json_encode(['person_liable_id' => $staff_info_id],
                        JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }
        }
    }

    /**
     * 处理需求管理-待确认-去确认-全部不符合满足
     * @param array $params 参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function updateConfirmDont(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $requirement_info = $this->getRequirementInfo($params['id']);
            //只有待确认的需求才能做此操作
            if ($requirement_info->status != WarehouseEnums::REQUIREMENT_STATUS_CONFIRM) {
                throw new ValidationException(static::$t->_('warehouse_requirement_status_error', ['status' => static::$t->_(WarehouseEnums::getRequirementStatus()[WarehouseEnums::REQUIREMENT_STATUS_CONFIRM])]), ErrCode::$VALIDATE_ERROR);
            }

            //先校验是否有关联的状态确认中的线索
            $relate_thread_list = WarehouseThreadRepository::getInstance()->onlySearchThread(['requirement_id' => $requirement_info->id, 'status' => WarehouseEnums::THREAD_STATUS_CONFIRM_ING]);
            if (empty($relate_thread_list)) {
                throw new ValidationException(static::$t->_('warehouse_requirement_confirm_thread_empty'), ErrCode::$VALIDATE_ERROR);
            }

            $now = date('Y-m-d H:i:s');
            //1. 将仓库需求状态更新为待寻找
            $requirement_info->status       = WarehouseEnums::REQUIREMENT_STATUS_SEARCH;//待寻找
            $requirement_info->updated_id   = $user['id'];
            $requirement_info->updated_name = $user['name'];
            $requirement_info->updated_at   = $now;
            $bool                           = $requirement_info->save();
            if ($bool === false) {
                throw new BusinessException('需求状态改为待寻找失败: ' . json_encode($params, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($requirement_info), ErrCode::$BUSINESS_ERROR);
            }

            //2. 将当前需求关联的状态为确认中的线索状态改为被淘汰
            $relate_thread_ids = implode(',', array_column($relate_thread_list, 'id'));
            $db->updateAsDict(
                (new WarehouseThreadModel())->getSource(),
                ['status' => WarehouseEnums::THREAD_STATUS_ELIMINATE, 'updated_id' => $user['id'], 'updated_name' => $user['name'], 'updated_at' => $now],
                ['conditions' => "requirement_id = {$requirement_info->id} and id IN (" . $relate_thread_ids . ')']
            );

            //3.更新需求确认记录
            $warehouse_requirement_confirm_record = WarehouseRequirementConfirmRecordModel::findFirst([
                'conditions' => 'requirement_id = :requirement_id: and confirm_status = :confirm_status:',
                'bind' => ['requirement_id' => $requirement_info->id, 'confirm_status' => 0]
            ]);
            $data = [
                'confirm_id'     => $user['id'],
                'confirm_name'   => $user['name'],
                'confirm_at'     => $now,
                'confirm_status' => WarehouseEnums::REQUIREMENT_CONFIRM_STATUS_DONT,
                'confirm_reason' => $params['confirm_reason'],
                'created_at'     => $now,
                'updated_at'     => $now,
            ];
            $bool = $warehouse_requirement_confirm_record->save($data);
            if ($bool === false) {
                throw new BusinessException('变更确认记录信息失败: ' . json_encode($data, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($warehouse_requirement_confirm_record), ErrCode::$BUSINESS_ERROR);
            }

            $db->commit();
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        } catch (Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }

        if ($message) {
            $db->rollback();
        }

        if ($real_message) {
            $this->logger->error('仓库管理-处理需求管理-待确认-去确认-全部不符合满足失败: ' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => [],
        ];
    }

    /**
     * 仓库需求列表
     * @param array $params 请求参数组
     * @param int $type 列表 1仓库需求管理-列表、2处理仓库需求-待寻找、3处理仓库需求-待确认、4处理仓库需求-待入驻
     * @return array
     */
    public function list(array $params, int $type = self::REQUIREMENT_LIST)
    {
        $page_size = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : (int)$params['pageSize'];
        $page_num  = empty($params['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : (int)$params['pageNum'];

        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $data    = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];

        try {
            $count = $this->getDataTotal($params, $type);
            $items = [];
            if ($count) {
                $offset = $page_size * ($page_num - 1);
                $builder = $this->modelsManager->createBuilder();
                $builder->from(['main' => WarehouseRequirementModel::class]);
                $builder->leftJoin(WarehouseThreadPriceRecordModel::class, 'price.requirement_id = main.id and main.thread_id > 0', 'price');
                $builder = $this->getCondition($builder, $params, $type);
                $builder->columns([
                    'main.id',
                    'main.no',
                    'main.status',
                    'main.store_id',
                    'main.store_name',
                    'main.region_id',
                    'main.region_name',
                    'main.piece_id',
                    'main.piece_name',
                    'main.opening_status',
                    'main.warehouse_type',
                    'main.priority_level',
                    'main.expect_settled_date',
                    'main.area',
                    'main.province_code',
                    'main.city_code',
                    'main.district_code',
                    'main.area_min',
                    'main.area_max',
                    'main.created_id',
                    'main.created_name',
                    'main.apply_date',
                    'main.store_category',
                    'main.original_warehouse_id',
                    'main.parking_num_min',
                    'main.parking_num_max',
                    'price.id as price_id'
                ]);
                $builder->orderby($type == self::REQUIREMENT_LIST ? 'main.id DESC' : 'main.no ASC');
                $builder->limit($page_size, $offset);
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleItems($items);
            }
            $data['pagination']['total_count'] = $count;
            $data['items']                     = $items;
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('仓库管理-仓库需求管理-列表获取失败: ' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * @param array $params 参数组
     * @param int $type 列表 1仓库需求管理-列表、2处理仓库需求-待寻找、3处理仓库需求-待确认、4处理仓库需求-待入驻
     * @param int $user_id 登陆者工号
     * @return array|mixed
     * @throws BusinessException
     * @throws GuzzleException
     */
    public function exportMethod(array $params, int $user_id, int $type = self::REQUIREMENT_LIST)
    {
        $excel_type = self::$task_type[$type];
        // 大于指定数量, 添加异步任务 导出
        if ($this->getDownloadDataTotal($params, $type) > WarehouseEnums::SYNC_EXPORT_MAX_COUNT) {
            $result         = DownloadCenterService::getInstance()->addDownloadCenter($user_id, $excel_type, $params);
            $result['data'] = [
                'export_method' => DownloadCenterEnum::EXPORT_METHOD_ASYNC,
                'file_url'      => '',
            ];
        } else {
            // 小于等于指定数量, 同步导出
            $params['pageSize'] = WarehouseEnums::SYNC_EXPORT_MAX_COUNT;
            $data               = $this->export($params, $type);
            $result             = $this->generateInfoExportFile($data, $excel_type);
            $result['data']     = [
                'export_method' => DownloadCenterEnum::EXPORT_METHOD_SYNC,
                'file_url'      => is_string($result['data']) ? $result['data'] : '',
            ];
        }
        return $result;
    }

    /**
     * 获得列表总数或小红点
     * @param array $params 请求参数组
     * @param int $type 列表 1仓库需求管理-列表、2处理仓库需求-待寻找、3处理仓库需求-待确认、4处理仓库需求-待入驻
     * @return int
     */
    public function getDataTotal(array $params, int $type = self::REQUIREMENT_LIST)
    {
        $total_count = 0;
        try {
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['main' => WarehouseRequirementModel::class]);
            $builder = $this->getCondition($builder, $params, $type);
            $total_count = (int)$builder->columns('COUNT(main.id) AS total')->getQuery()->getSingleResult()->total;
        } catch (\Exception $e) {
            $this->logger->error('仓库管理-仓库需求管理-获得下载数据的总数失败: ' . $e->getMessage());
        }

        return $total_count;
    }

    /**
     * 获得下载数据查询对象
     * @param array $params 请求参数组
     * @param int $type 列表 1仓库需求管理-列表、2处理仓库需求-待寻找、3处理仓库需求-待确认、4处理仓库需求-待入驻
     * @return object
     */
    private function exportBuilder(array $params,int $type = self::REQUIREMENT_LIST)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['main' => WarehouseRequirementModel::class]);
        if (in_array($type, [self::REQUIREMENT_LIST, self::REQUIREMENT_SETTLE_LIST])) {
            // 关联线索 - 仓库需求管理-列表、处理仓库需求-待入驻：每一个仓库需求导出一行数据 获取仓库需求最终确认的线索ID，如果无最终确认的线索，则为空(所有与仓库线索绑定的导出字段均按照该逻辑进行处理)
            $builder->leftjoin(WarehouseThreadModel::class, 'thread.id = main.thread_id', 'thread');
        } else {
            // 关联线索 - 处理仓库需求-待寻找、处理仓库需求-待入驻：每一个仓库线索导出一行数据，仓库线索不包含作废状态的线索，如果无关联的仓库线索，则仅展示一行数据
            $builder->leftjoin(WarehouseThreadModel::class, 'thread.requirement_id = main.id AND thread.status != ' . WarehouseEnums::THREAD_STATUS_CANCEL, 'thread');
        }
        return $this->getCondition($builder, $params, $type);
    }

    /**
     * 获得下载数据的总数
     * @param array $params 请求参数组
     * @param int $type 列表 1仓库需求管理-列表、2处理仓库需求-待寻找、3处理仓库需求-待确认、4处理仓库需求-待入驻
     * @return int
     */
    public function getDownloadDataTotal(array $params,int $type = self::REQUIREMENT_LIST)
    {
        $total_count = 0;
        try {
            $builder     = $this->exportBuilder($params, $type);
            $total_count = (int)$builder->columns('COUNT(main.id) AS total')->getQuery()->getSingleResult()->total;
        } catch (\Exception $e) {
            $this->logger->error('仓库管理-仓库需求管理-获得下载数据的总数失败: ' . $e->getMessage());
        }

        return $total_count;
    }

    /**
     * 仓库需求导出
     * @param array $params 参数组
     * @param int $type 列表 1仓库需求管理-列表、2处理仓库需求-待寻找、3处理仓库需求-待确认、4处理仓库需求-待入驻
     * @return array
     */
    public function export(array $params, int $type)
    {
        $page_size = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : (int)$params['pageSize'];
        $page_num  = empty($params['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : (int)$params['pageNum'];
        $builder   = $this->exportBuilder($params, $type);
        $offset    = $page_size * ($page_num - 1);
        $builder->columns([
            'main.id',
            'main.no',
            'main.store_name',
            'main.store_id',
            'main.store_category',
            'main.opening_status',
            'main.warehouse_type',
            'main.original_warehouse_id',
            'main.priority_level',
            'main.expect_settled_date',
            'main.province_code',
            'main.city_code',
            'main.district_code',
            'main.area_min',
            'main.area_max',
            'main.warehouse_latitude',
            'main.warehouse_longitude',
            'main.parking_num_min',
            'main.parking_num_max',
            'main.status',
            'main.cancel_reason',
            'main.created_id',
            'main.created_name',
            'main.apply_date',
            'main.remark',
            'main.actual_settled_store_id',
            'main.actual_settled_store_name',
            'main.actual_settled_date',
            ($type == self::REQUIREMENT_SEARCH_LIST || $type == self::REQUIREMENT_CONFIRM_LIST) ? 'thread.id as thread_id' : 'main.thread_id',
            'thread.no as thread_no',
            'thread.staff_id',
            'thread.staff_name',
            'thread.sys_department_name',
            'thread.job_name',
            'thread.warehouse_address',
            'thread.warehouse_latitude as thread_warehouse_latitude',
            'thread.warehouse_longitude as thread_warehouse_longitude',
            'thread.warehouse_area',
            'thread.warehouse_month_rent',
            'thread.contract_period',
            'thread.contract_period_unit',
            'thread.deposit',
            'thread.down_payment',
            'thread.currency',
            'thread.rent_payment_method',
            'thread.water_bill_payment_type',
            'thread.electricity_bill_payment_type',
            'thread.water_billing_method',
            'thread.water_usage_units',
            'thread.electricity_billing_method',
            'thread.electricity_usage_units',
            'thread.is_install_fire_extinguishers',
            'thread.withholding_tax_liability_bearer',
            'thread.land_tax_liability_bearer',
            'thread.stamp_duty_liability_bearer',
            'thread.surrounding_stores_average_rent',
            'thread.other_options_average_cost',
            'thread.cusa_fee',
            'thread.maintenance_fee',
            'thread.entrance_fee',
            'thread.garbage_fee',
            'thread.parking_fee',
            'thread.other_fee',
            'thread.warehouse_cr',
            'thread.warehouse_electricity',
            'thread.warehouse_water',
        ]);
        $builder->orderby('main.id DESC');
        $builder->limit($page_size, $offset);
        $items = $builder->getQuery()->execute()->toArray();
        return $this->handleItems($items, $type, true);
    }

    /**
     * 仓库管理 - 仓库需求管理 - 导出表头
     * @param array $data 导出数据
     * @param int $excel_type 1301 、1302、1303、1304
     * @return array
     * @throws BusinessException
     * @throws GuzzleException
     */
    public function generateInfoExportFile(array $data, int $excel_type)
    {
        $excel_header = $this->getExportFileHeader($excel_type);

        $excel_data = [];
        foreach ($data as $value) {
            $excel_data[] = $value;
        }
        unset($data);

        $excel_file_name = str_replace( '{YmdHis}', date('YmdHis'), DownloadCenterEnum::$download_center_excel_setting_item[$excel_type]['file_name']);
        return $this->exportExcel($excel_header, $excel_data, $excel_file_name);
    }

    /**
     * 获取仓库需求导出模板
     */
    public function getExportFileHeader($excel_type)
    {
        if (get_country_code() == GlobalEnums::TH_COUNTRY_CODE) {
            $excel_header = [
                static::$t['warehouse_requirement_no'],                              // 需求ID
                static::$t['warehouse_requirement_store_name'],                      // 需求网点名称
                static::$t['warehouse_requirement_store_id'],                        // 需求网点编码
                static::$t['warehouse_export_001'],                                  // 需求网点类型
                static::$t['warehouse_requirement_opening_status'],                  //创建仓库需求时网点开业状态
                static::$t['warehouse_requirement_warehouse_type'],                  //仓库类型
                ];

            if (in_array($excel_type,
                [DownloadCenterEnum::FINANCE_WAREHOUSE_REQUIREMENT_EXPORT, DownloadCenterEnum::FINANCE_WAREHOUSE_REQUIREMENT_RENEWED_EXPORT])) {
                $excel_header[] = static::$t['warehouse_export_002']; // 原有仓库名称
            }

            $excel_header = array_merge($excel_header, [
                static::$t['warehouse_requirement_priority_level'],                  //优先级
                static::$t['warehouse_requirement_expect_settled_date'],             //预计入驻日期
                static::$t['warehouse_requirement_intended_regions'],                // 意向地区
                static::$t['warehouse_requirement_intended_area'],                   // 意向面积
                static::$t['warehouse_requirement_intended_longitude_latitude'],     // 意向经纬度
                static::$t['warehouse_export_003'],                                  // van停车位数量
                static::$t['warehouse_requirement_status'],                          // 需求状态
            ]);

            if ($excel_type == DownloadCenterEnum::FINANCE_WAREHOUSE_REQUIREMENT_EXPORT) {
                $excel_header[] = static::$t['warehouse_requirement_cancel_reason']; // 作废原因
            }

            $excel_header = array_merge($excel_header, [
                static::$t['warehouse_requirement_created_name'],                    // 需求提出人
                static::$t['warehouse_requirement_created_id'],                      // 需求提出人工号
                static::$t['warehouse_requirement_apply_date'],                      // 需求提出日期
                static::$t['warehouse_requirement_remark'],                          // 备注
                static::$t['warehouse_requirement_attachment'],                      // 附件
                static::$t['warehouse_requirement_thread_no'],                       //关联线索ID
                static::$t['warehouse_thread_staff_name'],                           //寻仓人姓名
                static::$t['warehouse_thread_staff_id'],                             //寻仓人工号
                static::$t['warehouse_thread_sys_department_id'],                    //寻仓人部门
                static::$t['warehouse_thread_job_id'],                               //寻仓人职位
                static::$t['warehouse_thread_warehouse_address'],                    //仓库地址
                static::$t['warehouse_thread_longitude_latitude'],                   //仓库经纬度
                static::$t['warehouse_thread_warehouse_area'],                       //仓库面积
                static::$t['warehouse_thread_warehouse_month_rent'],                 //仓库每月租金
                static::$t['warehouse_thread_contract_period'],                      //合同期
                static::$t['warehouse_thread_deposit'],                              //押金
                static::$t['warehouse_thread_down_payment'],                         //预付金
                static::$t['warehouse_export_004'],                                  // 租金支付方式
                static::$t['warehouse_export_005'],                                  // 水费支付类型
                static::$t['warehouse_export_006'],                                  // 电费支付类型
                static::$t['warehouse_export_007'],                                  // 水费使用计费方式
                static::$t['warehouse_export_008'],                                  // 水费使用单位数
                static::$t['warehouse_export_009'],                                  // 电费使用计费方式
                static::$t['warehouse_export_010'],                                  // 电费使用单位数
                static::$t['warehouse_export_011'],                                  // 是否安装灭火器
                static::$t['warehouse_export_012'],                                  // 扣缴税责任承担方
                static::$t['warehouse_export_013'],                                  // 土地税责任承担方
                static::$t['warehouse_export_014'],                                  // 印花税责任承担方
                static::$t['warehouse_export_015'],                                  // 周边门店平均租金
                static::$t['warehouse_export_016'],                                  // 其他选址方案平均费用
                static::$t['warehouse_thread_plan_attachment'],                      //平面图
                static::$t['warehouse_thread_attachment'],                           //仓库线索附件
            ]);

            //仓库需求管理-导出 - 多导出几列
            if ($excel_type == DownloadCenterEnum::FINANCE_WAREHOUSE_REQUIREMENT_EXPORT) {
                $excel_header = array_merge($excel_header, [
                    static::$t['warehouse_requirement_actual_settled_store_name'], //实际入驻网点
                    static::$t['warehouse_requirement_actual_settled_store_id'],   //实际入驻网点编码
                    static::$t['warehouse_requirement_actual_settled_date'],       //实际入驻日期
                ]);
            }

        } else {
            $excel_header = [
                static::$t['warehouse_requirement_no'],                                   // 需求ID
                static::$t['warehouse_requirement_store_name'],                           // 需求网点名称
                static::$t['warehouse_requirement_store_id'],                             // 需求网点编码
                static::$t['warehouse_requirement_opening_status'],                       //创建仓库需求时网点开业状态
                static::$t['warehouse_requirement_warehouse_type'],                       //仓库类型
                static::$t['warehouse_requirement_priority_level'],                       //优先级
                static::$t['warehouse_requirement_expect_settled_date'],                  //预计入驻日期
                static::$t['warehouse_requirement_intended_regions'],                     // 意向地区
                static::$t['warehouse_requirement_intended_area'],                        // 意向面积
                static::$t['warehouse_requirement_intended_longitude_latitude'],          // 意向经纬度
                static::$t['warehouse_requirement_status'],                               // 需求状态
                static::$t['warehouse_requirement_created_name'],                         // 需求提出人
                static::$t['warehouse_requirement_created_id'],                           // 需求提出人工号
                static::$t['warehouse_requirement_apply_date'],                           // 需求提出日期
                static::$t['warehouse_requirement_remark'],                               // 备注
                static::$t['warehouse_requirement_attachment'],                           // 附件
                static::$t['warehouse_requirement_thread_no'],                            //关联线索ID
                static::$t['warehouse_thread_staff_name'],                                //寻仓人姓名
                static::$t['warehouse_thread_staff_id'],                                  //寻仓人工号
                static::$t['warehouse_thread_sys_department_id'],                         //寻仓人部门
                static::$t['warehouse_thread_job_id'],                                    //寻仓人职位
                static::$t['warehouse_thread_warehouse_address'],                         //仓库地址
                static::$t['warehouse_thread_longitude_latitude'],                        //仓库经纬度
                static::$t['warehouse_thread_warehouse_area'],                            //仓库面积
                static::$t['warehouse_thread_warehouse_month_rent'],                      //仓库每月租金
                static::$t['warehouse_thread_contract_period'],                           //合同期
                static::$t['warehouse_thread_deposit'],                                   //押金
                static::$t['warehouse_thread_down_payment'],                              //预付金
                static::$t['warehouse_thread_cusa_fee'],                                  //Cusa费用
                static::$t['warehouse_thread_maintenance_fee'],                           //维护费
                static::$t['warehouse_thread_entrance_fee'],                              //入场费
                static::$t['warehouse_thread_garbage_fee'],                               //垃圾收集费
                static::$t['warehouse_thread_parking_fee'],                               //停车费
                static::$t['warehouse_thread_other_fee'],                                 //其他费用
                static::$t['warehouse_thread_warehouse_cr'],                              //仓库是否有CR
                static::$t['warehouse_thread_plan_attachment'],                           //平面图
                static::$t['warehouse_thread_warehouse_electricity'],                     //仓库电供应
                static::$t['warehouse_thread_warehouse_water'],                           //仓库水供应
                static::$t['warehouse_thread_attachment'],                                //仓库线索附件
            ];

            //仓库需求管理-导出 - 多导出几列
            if ($excel_type == DownloadCenterEnum::FINANCE_WAREHOUSE_REQUIREMENT_EXPORT) {
                $excel_header = array_merge($excel_header, [
                    static::$t['warehouse_requirement_actual_settled_store_name'],        //实际入驻网点
                    static::$t['warehouse_requirement_actual_settled_store_id'],          //实际入驻网点编码
                    static::$t['warehouse_requirement_actual_settled_date'],              //实际入驻日期
                    static::$t['warehouse_requirement_cancel_reason'],                    //作废原因
                ]);
            }
        }

        return $excel_header;
    }

    /**
     * 处理仓库需求列表数据格式
     *
     * @param array $items
     * @param int $type 列表 1仓库需求管理-列表、2处理仓库需求-待寻找、3处理仓库需求-待确认、4处理仓库需求-待入驻
     * @param bool $is_export 是否导出，false否，true是
     * @return array
     */
    protected function handleItems(array $items, int $type = self::REQUIREMENT_LIST, bool $is_export = false)
    {
        if (empty($items)) {
            return [];
        }
        $data  = [];
        $items = $this->getAreaInfo($items);

        // 网点类型
        $store_category_map = self::getSettingEnvStoreCategoryMap();

        // 原有仓库
        $warehouse_ids = array_column($items, 'original_warehouse_id');
        $original_warehouse_map = ContractWarehouseRepository::getInstance()->getListByWarehouseIds($warehouse_ids);

        if ($is_export) {
            //需求附件
            $requirement_attachment = SysAttachmentRepository::getInstance()->getAttachmentsUrlsById(array_values(array_unique(array_column($items, 'id'))), Enums::OSS_BUCKET_TYPE_WAREHOUSE_REQUIREMENT_INFO_FILE);
            $thread_ids = array_values(array_unique(array_filter(array_column($items, 'thread_id'))));
            //线索平面图
            $thread_plan_attachment = SysAttachmentRepository::getInstance()->getAttachmentsUrlsById($thread_ids, Enums::OSS_BUCKET_TYPE_WAREHOUSE_THREAD_PLAN_FILE);
            //仓库线索附件
            $thread_attachment  = SysAttachmentRepository::getInstance()->getAttachmentsUrlsById($thread_ids, Enums::OSS_BUCKET_TYPE_WAREHOUSE_THREAD_INFO_FILE);

            if (get_country_code() == GlobalEnums::TH_COUNTRY_CODE) {
                $thread_enums = ThreadService::getInstance()->getEnums();
                $rent_payment_method_map              = array_column($thread_enums['rent_payment_method'], 'label', 'value');
                $water_bill_payment_type_map          = array_column($thread_enums['water_bill_payment_type'], 'label', 'value');
                $electricity_bill_payment_type_map    = array_column($thread_enums['electricity_bill_payment_type'], 'label', 'value');
                $water_billing_method_map             = array_column($thread_enums['water_billing_method'], 'label', 'value');
                $electricity_billing_method_map       = array_column($thread_enums['electricity_billing_method'], 'label', 'value');
                $is_install_fire_extinguishers_map    = array_column($thread_enums['is_install_fire_extinguishers'], 'label', 'value');
                $withholding_tax_liability_bearer_map = array_column($thread_enums['withholding_tax_liability_bearer'], 'label', 'value');
                $land_tax_liability_bearer_map        = array_column($thread_enums['land_tax_liability_bearer'], 'label', 'value');
                $stamp_duty_liability_bearer_map      = array_column($thread_enums['stamp_duty_liability_bearer'], 'label', 'value');

                $th_fee_amount_unit = static::$t->_('th_fee_amount_unit');

                foreach ($items as $item) {
                    $rent_payment_method = !empty($item['rent_payment_method']) ? explode(',', $item['rent_payment_method']) : [];

                    $rent_payment_method_item = [];
                    foreach ($rent_payment_method as $method_val) {
                        $rent_payment_method_item[] = $rent_payment_method_map[$method_val] ?? '';
                    }

                    $water_usage_units       = '';
                    $electricity_usage_units = '';
                    if (!empty($item['thread_no'])) {
                        $water_fee_unit = '';
                        if ($item['water_billing_method'] == WarehouseEnums::THREAD_WATER_BILLING_METHOD_UNIT) {
                            $water_fee_unit = static::$t->_('currency_thai_baht') . '/' . static::$t->_('water_billing_unit_ton');
                        } elseif ($item['water_billing_method'] == WarehouseEnums::THREAD_WATER_BILLING_METHOD_UNITY) {
                            $water_fee_unit = static::$t->_('currency_thai_baht');
                        }

                        $water_usage_units = $item['water_usage_units'] . $water_fee_unit;

                        $electricity_fee_unit = '';
                        if ($item['electricity_billing_method'] == WarehouseEnums::THREAD_ELECTRICITY_BILLING_METHOD_UNIT) {
                            $electricity_fee_unit = static::$t->_('currency_thai_baht') . '/' . static::$t->_('electricity_billing_unit_kwh');
                        } elseif ($item['electricity_billing_method'] == WarehouseEnums::THREAD_ELECTRICITY_BILLING_METHOD_UNITY) {
                            $electricity_fee_unit = static::$t->_('currency_thai_baht');
                        }

                        $electricity_usage_units = $item['electricity_usage_units'] . $electricity_fee_unit;
                    }

                    $rows = [
                        $item['no'],
                        $item['store_name'],
                        $item['store_id'],
                        $store_category_map[$item['store_category']] ?? '',
                        static::$t->_(WarehouseEnums::$opening_status[$item['opening_status']]),
                        static::$t->_(static::getWarehouseTypeKeyValueMap()[$item['warehouse_type']]),
                    ];

                    if (in_array($type, [self::REQUIREMENT_LIST, self::REQUIREMENT_RENEWED_LIST])) {
                        $rows[] = $original_warehouse_map[$item['original_warehouse_id']]['warehouse_name'] ?? '';
                    }

                    $rows = array_merge($rows, [
                        static::$t->_(WarehouseEnums::$priority_level[$item['priority_level']]),
                        $item['expect_settled_date'],
                        $item['district_name'] . ' ' . $item['city_name'] . ' ' . $item['province_name'],
                        $item['area_min'] . '-' . $item['area_max'],
                        $item['warehouse_latitude'] . ',' . $item['warehouse_longitude'],
                        $item['parking_num_min'] . '-' . $item['parking_num_max'],
                        static::$t->_(WarehouseEnums::getRequirementStatus()[$item['status']]),
                    ]);

                    if ($type == self::REQUIREMENT_LIST) {
                        $rows[] = $item['cancel_reason'];
                    }

                    $rows = array_merge($rows, [
                        $item['created_name'],
                        $item['created_id'],
                        $item['apply_date'],
                        $item['remark'],
                        implode(PHP_EOL, $requirement_attachment[$item['id']] ?? []),//需求附件
                        $item['thread_no'] ?? '',
                        $item['staff_name'] ?? '',
                        $item['staff_id'] ?? '',
                        $item['sys_department_name'] ?? '',//寻仓人部门
                        $item['job_name'] ?? '',           //寻仓人职位
                        $item['warehouse_address'] ?? '',
                        !empty($item['thread_no']) ? ($item['thread_warehouse_latitude'] . ',' . $item['thread_warehouse_longitude']) : '',
                        $item['warehouse_area'] ?? '',
                        $item['warehouse_month_rent'] ?? '',
                        !empty($item['thread_no']) ? ($item['contract_period'] . static::$t->_(WarehouseEnums::$contract_period_unit[$item['contract_period_unit']])) : '',
                        $item['deposit'] ?? '',
                        $item['down_payment'] ?? '',
                        implode(',', $rent_payment_method_item), // 租金支付方式
                        !empty($item['water_bill_payment_type']) ? $water_bill_payment_type_map[$item['water_bill_payment_type']] : '',
                        !empty($item['electricity_bill_payment_type']) ? $electricity_bill_payment_type_map[$item['electricity_bill_payment_type']] : '',
                        !empty($item['water_billing_method']) ? $water_billing_method_map[$item['water_billing_method']] : '',
                        $water_usage_units,
                        !empty($item['electricity_billing_method']) ? $electricity_billing_method_map[$item['electricity_billing_method']] : '',
                        $electricity_usage_units,
                        !empty($item['is_install_fire_extinguishers']) ? $is_install_fire_extinguishers_map[$item['is_install_fire_extinguishers']] : '',
                        !empty($item['withholding_tax_liability_bearer']) ? $withholding_tax_liability_bearer_map[$item['withholding_tax_liability_bearer']] : '',
                        !empty($item['land_tax_liability_bearer']) ? $land_tax_liability_bearer_map[$item['land_tax_liability_bearer']] : '',
                        !empty($item['stamp_duty_liability_bearer']) ? $stamp_duty_liability_bearer_map[$item['stamp_duty_liability_bearer']] : '',
                        !empty($item['surrounding_stores_average_rent']) ? ($item['surrounding_stores_average_rent'] . $th_fee_amount_unit) : '',
                        !empty($item['other_options_average_cost']) ? ($item['other_options_average_cost'] . $th_fee_amount_unit) : '',
                        implode(PHP_EOL, $thread_plan_attachment[$item['thread_id']] ?? []),//平面图
                        implode(PHP_EOL, $thread_attachment[$item['thread_id']] ?? []),     //仓库线索附件
                    ]);

                    //仓库需求管理-列表 - 多导出几列
                    if ($type == self::REQUIREMENT_LIST) {
                        $rows[] = $item['actual_settled_store_name'];
                        $rows[] = $item['actual_settled_store_id'];
                        $rows[] = $item['actual_settled_date'] ?: '';
                    }

                    $data[] = $rows;
                }

            } else {
                foreach ($items as $item) {
                    $currency = static::$t->_(GlobalEnums::$currency_item[$item['currency']]);
                    $rows     = [
                        $item['no'],
                        $item['store_name'],
                        $item['store_id'],
                        static::$t->_(WarehouseEnums::$opening_status[$item['opening_status']]),
                        static::$t->_(static::getWarehouseTypeKeyValueMap()[$item['warehouse_type']]),
                        static::$t->_(WarehouseEnums::$priority_level[$item['priority_level']]),
                        $item['expect_settled_date'],
                        $item['district_name'] . ' ' . $item['city_name'] . ' ' . $item['province_name'],
                        $item['area_min'] . '-' . $item['area_max'],
                        $item['warehouse_latitude'] . ',' . $item['warehouse_longitude'],
                        static::$t->_(WarehouseEnums::getRequirementStatus()[$item['status']]),
                        $item['created_name'],
                        $item['created_id'],
                        $item['apply_date'],
                        $item['remark'],
                        implode(PHP_EOL, $requirement_attachment[$item['id']] ?? []),//需求附件
                        $item['thread_no'] ?? '',
                        $item['staff_name'] ?? '',
                        $item['staff_id'] ?? '',
                        $item['sys_department_name'] ?? '',//寻仓人部门
                        $item['job_name'] ?? '',//寻仓人职位
                        $item['warehouse_address'] ?? '',
                        !empty($item['thread_no'] ?? '') ? ($item['thread_warehouse_latitude'] . ',' . $item['thread_warehouse_longitude']) : '',
                        $item['warehouse_area'] ?? '',
                        $item['warehouse_month_rent'] ?? '',
                        !empty($item['thread_no'] ?? '') ? ($item['contract_period'] . static::$t->_(WarehouseEnums::$contract_period_unit[$item['contract_period_unit']])) : '',
                        $item['deposit'] ?? '',
                        $item['down_payment'] ?? '',
                        !empty($item['cusa_fee']) ? ($item['cusa_fee'] . ' ' . $currency) : '',
                        !empty($item['maintenance_fee']) ? ($item['maintenance_fee'] . $currency) : '',
                        !empty($item['entrance_fee']) ? ($item['entrance_fee'] . ' ' . $currency) : '',
                        !empty($item['garbage_fee']) ? ($item['garbage_fee'] . ' ' . $currency) : '',
                        !empty($item['parking_fee']) ? ($item['parking_fee'] . ' ' . $currency) : '',
                        !empty($item['other_fee']) ? ($item['other_fee'] . ' ' . $currency) : '',
                        !empty($item['warehouse_cr']) ? static::$t->_(WarehouseEnums::$thread_has[$item['warehouse_cr']]) : '',//仓库是否有CR
                        implode(PHP_EOL, $thread_plan_attachment[$item['thread_id']] ?? []),//平面图
                        !empty($item['warehouse_electricity']) ? static::$t->_(WarehouseEnums::$thread_has[$item['warehouse_electricity']]) : '',//仓库电供应
                        !empty($item['warehouse_water']) ? static::$t->_(WarehouseEnums::$thread_has[$item['warehouse_water']]) : '',//仓库水供应
                        implode(PHP_EOL, $thread_attachment[$item['thread_id']] ?? []),//仓库线索附件
                    ];
                    //仓库需求管理-列表 - 多导出几列
                    if ($type == self::REQUIREMENT_LIST) {
                        $rows = array_merge($rows, [
                            $item['actual_settled_store_name'],
                            $item['actual_settled_store_id'],
                            $item['actual_settled_date'] ? $item['actual_settled_date'] : '',
                            $item['cancel_reason'],
                        ]);
                    }
                    $data[] = $rows;
                }
            }

        } else {
            foreach ($items as &$val) {
                $val['status_text']         = static::$t->_(WarehouseEnums::getRequirementStatus()[$val['status']]);
                $val['opening_status_text'] = static::$t->_(WarehouseEnums::$opening_status[$val['opening_status']]);
                $val['warehouse_type_text'] = static::$t->_(static::getWarehouseTypeKeyValueMap()[$val['warehouse_type']]);
                $val['priority_level_text'] = static::$t->_(WarehouseEnums::$priority_level[$val['priority_level']]);
                $val['price_id']            = $val['price_id'] ? $val['price_id'] : 0;

                $val['store_category']          = !empty($val['store_category']) ? (int)$val['store_category'] : '';
                $val['store_category_label']    = $store_category_map[$val['store_category']] ?? '';
                $val['original_warehouse_id']   = $val['original_warehouse_id'] ?: '';
                $val['original_warehouse_name'] = $original_warehouse_map[$val['original_warehouse_id']]['warehouse_name'] ?? '';
            }

            $data = $items;
        }
        return $data;
    }

    /**
     * 查询条件
     * @param object $builder
     * @param array $params 请求参数组
     * @param int $type 列表 1仓库需求管理-列表、2处理仓库需求-待寻找、3处理仓库需求-待确认、4处理仓库需求-待入驻
     * @return object
     */
    protected function getCondition(object $builder, array $params, int $type)
    {
        $no                        = $params['no'] ?? '';                       //需求ID
        $store_id                  = $params['store_id'] ?? '';                 //网点
        $status                    = $params['status'] ?? [];                   //需求状态
        $priority_level            = $params['priority_level'] ?? [];           //优先级
        $warehouse_type            = $params['warehouse_type'] ?? [];           //仓库类型
        $province_code             = $params['province_code'] ?? '';            //意向地区-省编码
        $city_code                 = $params['city_code'] ?? '';                //意向地区-市编码
        $district_code             = $params['district_code'] ?? '';            //意向地区-区编码
        $area_min                  = $params['area_min'] ?? '';                 //意向面积-最小面积
        $area_max                  = $params['area_max'] ?? '';                 //意向面积-最大面积
        $region_id                 = $params['region_id'] ?? '';                //大区
        $piece_id                  = $params['piece_id'] ?? '';                 //片区
        $opening_status            = $params['opening_status'] ?? 0;            //网点开业状态
        $expect_settled_date_start = $params['expect_settled_date_start'] ?? '';//预计入驻日期-起始
        $expect_settled_date_end   = $params['expect_settled_date_end'] ?? '';  //预计入驻日期-截止
        $actual_settled_date_start = $params['actual_settled_date_start'] ?? '';//实际入驻日期-起始
        $actual_settled_date_end   = $params['actual_settled_date_end'] ?? '';  //实际入驻日期-截止
        $store_categorys           = $params['store_categorys'] ?? [];          //网点类型
        $original_warehouse_ids    = $params['original_warehouse_ids'] ?? [];   //原有仓库ID
        $parking_num_min           = $params['parking_num_min'] ?? '';          //van停车位数量-最低数量
        $parking_num_max           = $params['parking_num_max'] ?? '';          //van停车位数量-最多数量

        if ($type == self::REQUIREMENT_SEARCH_LIST) {
            //处理仓库需求-待寻找
            $status = WarehouseEnums::REQUIREMENT_STATUS_SEARCH;
        } elseif ($type == self::REQUIREMENT_CONFIRM_LIST) {
            //处理仓库需求-待确认
            $status = WarehouseEnums::REQUIREMENT_STATUS_CONFIRM;
        } elseif ($type == self::REQUIREMENT_SETTLE_LIST) {
            //处理仓库需求-待入驻
            $status = WarehouseEnums::REQUIREMENT_STATUS_SETTLE;
        } elseif ($type == self::REQUIREMENT_RENEWED_LIST) {
            //处理仓库需求-待续约
            $status = WarehouseEnums::REQUIREMENT_STATUS_PENDING_RENEWED;
        }

        if (!empty($no)) {
            $builder->andWhere('main.no LIKE :no:', ['no' => '%' . $params['no'] . '%']);
        }
        if (!empty($store_id)) {
            $builder->andWhere('main.store_id = :store_id:', ['store_id' => $store_id]);
        }
        if (!empty($status)) {
            if (is_array($status)) {
                $builder->inWhere('main.status', $status);
            } else {
                $builder->andWhere('main.status = :status:', ['status' => $status]);
            }
        }
        if (!empty($priority_level)) {
            $builder->inWhere('main.priority_level', $priority_level);
        }
        if (!empty($warehouse_type)) {
            $builder->inWhere('main.warehouse_type', $warehouse_type);
        }
        if (!empty($province_code)) {
            $builder->andWhere('main.province_code = :province_code:', ['province_code' => $province_code]);
        }
        if (!empty($city_code)) {
            $builder->andWhere('main.city_code = :city_code:', ['city_code' => $city_code]);
        }
        if (!empty($district_code)) {
            $builder->andWhere('main.district_code = :district_code:', ['district_code' => $district_code]);
        }
        //意向面积：输入数字区间，与仓库需求中的意向面积有交集则符合查询条件，存储输入的最小面积<=存储的最大面积并且输入的最大面积>=存储的最小面积
        if (!empty($area_min)) {
            $builder->andWhere(':area_min: <= main.area_max', ['area_min' => $area_min]);
        }
        if (!empty($area_max)) {
            $builder->andWhere(':area_max: >= main.area_min', ['area_max' => $area_max]);
        }
        if (!empty($region_id)) {
            $builder->andWhere('main.region_id = :region_id:', ['region_id' => $region_id]);
        }
        if (!empty($piece_id)) {
            $builder->andWhere('main.piece_id = :piece_id:', ['piece_id' => $piece_id]);
        }
        if (!empty($opening_status)) {
            $builder->andWhere('main.opening_status = :opening_status:', ['opening_status' => $opening_status]);
        }
        if (!empty($expect_settled_date_start)) {
            $builder->andWhere('main.expect_settled_date >= :expect_settled_date_start:', ['expect_settled_date_start' => $expect_settled_date_start]);
        }
        if (!empty($expect_settled_date_end)) {
            $builder->andWhere('main.expect_settled_date <= :expect_settled_date_end:', ['expect_settled_date_end' => $expect_settled_date_end]);
        }
        if (!empty($actual_settled_date_start)) {
            $builder->andWhere('main.actual_settled_date >= :actual_settled_date_start:', ['actual_settled_date_start' => $actual_settled_date_start]);
        }
        if (!empty($actual_settled_date_end)) {
            $builder->andWhere('main.actual_settled_date <= :actual_settled_date_end:', ['actual_settled_date_end' => $actual_settled_date_end]);
        }

        if (!empty($store_categorys)) {
            $builder->inWhere('main.store_category', $store_categorys);
        }

        if (!empty($original_warehouse_ids)) {
            $builder->inWhere('main.original_warehouse_id', $original_warehouse_ids);
        }

        if (!empty($parking_num_min)) {
            $builder->andWhere('main.parking_num_max >= :parking_num_min:', ['parking_num_min' => $parking_num_min]);
        }

        if (!empty($parking_num_max)) {
            $builder->andWhere('main.parking_num_min <= :parking_num_max:', ['parking_num_max' => $parking_num_max]);
        }

        return $builder;
    }

    /**
     * 获取需求详情 - 查看
     * @param object $requirement_info 需求对象
     * @return array
     */
    public function getRequirementDetail($requirement_info)
    {
        $area_info = $this->getAreaInfo([$requirement_info->toArray()])[0] ?? [];

        // 网点类型
        $store_category_map = self::getSettingEnvStoreCategoryMap();

        // 原有仓库
        $original_warehouse_map = ContractWarehouseRepository::getInstance()->getListByWarehouseIds([$requirement_info->original_warehouse_id]);

        return [
            'id'                  => $requirement_info->id,
            'no'                  => $requirement_info->no,
            'thread_id'           => $requirement_info->thread_id,
            'store_id'            => $requirement_info->store_id,
            'store_name'          => $requirement_info->store_name,
            'region_name'         => $requirement_info->region_name,
            'piece_name'          => $requirement_info->piece_name,
            'opening_status'      => $requirement_info->opening_status,
            'opening_status_text' => static::$t->_(WarehouseEnums::$opening_status[$requirement_info->opening_status]),
            'warehouse_type'      => $requirement_info->warehouse_type,
            'warehouse_type_text' => static::$t->_(static::getWarehouseTypeKeyValueMap()[$requirement_info->warehouse_type]),
            'priority_level'      => $requirement_info->priority_level,
            'priority_level_text' => static::$t->_(WarehouseEnums::$priority_level[$requirement_info->priority_level]),
            'expect_settled_date' => $requirement_info->expect_settled_date,
            'district_name'       => $area_info['district_name'],
            'city_name'           => $area_info['city_name'],
            'province_name'       => $area_info['province_name'],
            'area'                => $requirement_info->area,
            'warehouse_latitude'  => $requirement_info->warehouse_latitude,
            'warehouse_longitude' => $requirement_info->warehouse_longitude,
            'area_min'            => $requirement_info->area_min,
            'area_max'            => $requirement_info->area_max,
            'status'              => $requirement_info->status,
            'status_text'         => static::$t->_(WarehouseEnums::getRequirementStatus()[$requirement_info->status]),
            'created_id'          => $requirement_info->created_id,
            'created_name'        => $requirement_info->created_name,
            'apply_date'          => $requirement_info->apply_date,
            'remark'              => $requirement_info->remark,
            'cancel_reason'       => $requirement_info->cancel_reason,
            'attachments'         => $requirement_info->getAttachment()->toArray(),

            'store_category'          => convert_type($requirement_info->store_category),
            'store_category_label'    => $store_category_map[$requirement_info->store_category] ?? '',
            'original_warehouse_id'   => $requirement_info->original_warehouse_id ?: '',
            'original_warehouse_name' => $original_warehouse_map[$requirement_info->original_warehouse_id]['warehouse_name'] ?? '',
            'parking_num_min'         => $requirement_info->parking_num_min,
            'parking_num_max'         => $requirement_info->parking_num_max,
        ];
    }

    /**
     * 获取需求 - 确认记录
     * @param object $requirement_info 需求对象
     * @return array
     */
    public function getRequirementConfirmList($requirement_info)
    {
        $list = [];
        $confirm_list = $requirement_info->getConfirmList()->toArray();
        foreach ($confirm_list as $item) {
            if ($item['confirm_status'] == WarehouseEnums::REQUIREMENT_CONFIRM_STATUS_OK) {
                //满足，则展示确认线索+线索编号+满足需求，例如确认线索WT202412110001满足需求，固定的描述信息需要支持翻译
                $confirm_reason = static::$t->_('warehouse_requirement_confirm_reason_ok' , ['thread_no' => $item['thread_no']]);
            } elseif ($item['confirm_status'] == WarehouseEnums::REQUIREMENT_CONFIRM_STATUS_DONT) {
                //不满足，则显示输入的不满足原因
                $confirm_reason = $item['confirm_reason'];
            } elseif ($item['confirm_status'] == WarehouseEnums::REQUIREMENT_CONFIRM_STATUS_NEGOTIATIONS_FAILED) {
                $confirm_reason = $item['confirm_reason'];
            } else {
                $confirm_reason = '';
            }
            $list[] = [
                'created_id'     => $item['created_id'], //发起人工号
                'created_name'   => $item['created_name'], //发起人工号
                'created_at'     => $item['created_at'], //发起时间
                'remark'         => $item['remark'], //发起说明
                'confirm_id'     => $item['confirm_id'] ? $item['confirm_id'] : '', //确认人工号
                'confirm_name'   => $item['confirm_name'], //确认人工号
                'confirm_at'     => $item['confirm_at'] ?? '', //确认时间
                'confirm_status' => $item['confirm_status'] ? static::$t->_(WarehouseEnums::$confirm_status[$item['confirm_status']]) : '', //确认结果
                'confirm_reason' => $confirm_reason,
            ];
        }
        return $list;
    }

    /**
     * 获取需求 - 无确认线索
     * @param int $id 需求id
     * @param int $thread_status 线索状态
     * @return array
     */
    public function getRequirementThreadList(int $id, int $thread_status = WarehouseEnums::THREAD_STATUS_CANCEL)
    {
        $list = [];

        $thread_enums = ThreadService::getInstance()->getEnums();
        $rent_payment_method_map              = array_column($thread_enums['rent_payment_method'], 'label', 'value');
        $water_bill_payment_type_map          = array_column($thread_enums['water_bill_payment_type'], 'label', 'value');
        $electricity_bill_payment_type_map    = array_column($thread_enums['electricity_bill_payment_type'], 'label', 'value');
        $water_billing_method_map             = array_column($thread_enums['water_billing_method'], 'label', 'value');
        $electricity_billing_method_map       = array_column($thread_enums['electricity_billing_method'], 'label', 'value');
        $is_install_fire_extinguishers_map    = array_column($thread_enums['is_install_fire_extinguishers'], 'label', 'value');
        $withholding_tax_liability_bearer_map = array_column($thread_enums['withholding_tax_liability_bearer'], 'label', 'value');
        $land_tax_liability_bearer_map        = array_column($thread_enums['land_tax_liability_bearer'], 'label', 'value');
        $stamp_duty_liability_bearer_map      = array_column($thread_enums['stamp_duty_liability_bearer'], 'label', 'value');

        if ($thread_status == WarehouseEnums::THREAD_STATUS_CANCEL) {
            //展示所有关联需求ID为当前仓库需求的仓库线索并且线索状态不为已作废的线索, 按照线索ID升序
            $relate_thread_list = WarehouseThreadRepository::getInstance()->onlySearchThread(['requirement_id' => $id, 'not_status' => WarehouseEnums::THREAD_STATUS_CANCEL]);
            foreach ($relate_thread_list as $item) {
                $rent_payment_method_item = [];
                $rent_payment_method      = !empty($item['rent_payment_method']) ? explode(',', $item['rent_payment_method']) : [];
                foreach ($rent_payment_method as $method_val) {
                    $rent_payment_method_item[] = $rent_payment_method_map[$method_val] ?? '';
                }

                $list[] = [
                    'id'                         => $item['id'],
                    'no'                         => $item['no'], //线索编号
                    'status'                     => $item['status'], //状态
                    'status_text'                => static::$t->_(WarehouseEnums::$thread_status[$item['status']]),
                    'warehouse_latitude'         => $item['warehouse_latitude'], //仓库纬度
                    'warehouse_longitude'        => $item['warehouse_longitude'], //仓库经度
                    'warehouse_area'             => $item['warehouse_area'], //仓库面积
                    'warehouse_month_rent'       => $item['warehouse_month_rent'], //每月租金
                    'contract_period'            => $item['contract_period'] . static::$t->_(WarehouseEnums::$contract_period_unit[$item['contract_period_unit']]), //合同期
                    'deposit'                    => $item['deposit'], //押金
                    'down_payment'               => $item['down_payment'], //预付款
                    'cusa_fee'                   => $item['cusa_fee'],//Cusa费用
                    'maintenance_fee'            => $item['maintenance_fee'],//维护费
                    'entrance_fee'               => $item['entrance_fee'],//入场费
                    'garbage_fee'                => $item['garbage_fee'],//垃圾收理费
                    'parking_fee'                => $item['parking_fee'],//停车费
                    'other_fee'                  => $item['other_fee'],//其他费用
                    'currency_text'              => static::$t->_(GlobalEnums::$currency_item[$item['currency']]),
                    'warehouse_cr_text'          => $item['warehouse_cr'] ? static::$t->_(WarehouseEnums::$thread_has[$item['warehouse_cr']]) : '',//是否有CR
                    'warehouse_electricity_text' => $item['warehouse_electricity'] ? static::$t->_(WarehouseEnums::$thread_has[$item['warehouse_electricity']]) : '',//电供应
                    'warehouse_water_text'       => $item['warehouse_water'] ? static::$t->_(WarehouseEnums::$thread_has[$item['warehouse_water']]) : '',//水供应
                    'apply_date'                 => $item['apply_date'], //线索提供日期
                    'warehouse_address'          => $item['warehouse_address'], //仓库地址
                    'remark'                     => $item['remark'],//备注

                    'is_install_fire_extinguishers_text'    => $is_install_fire_extinguishers_map[$item['is_install_fire_extinguishers']] ?? '',
                    'rent_payment_method_item'              => implode(',', $rent_payment_method_item),
                    'water_bill_payment_type_text'          => $water_bill_payment_type_map[$item['water_bill_payment_type']] ?? '',
                    'electricity_bill_payment_type_text'    => $electricity_bill_payment_type_map[$item['electricity_bill_payment_type']] ?? '',
                    'water_billing_method_text'             => $water_billing_method_map[$item['water_billing_method']] ?? '',
                    'electricity_billing_method_text'       => $electricity_billing_method_map[$item['electricity_billing_method']] ?? '',
                    'withholding_tax_liability_bearer_text' => $withholding_tax_liability_bearer_map[$item['withholding_tax_liability_bearer']] ?? '',
                    'land_tax_liability_bearer_text'        => $land_tax_liability_bearer_map[$item['land_tax_liability_bearer']] ?? '',
                    'stamp_duty_liability_bearer_text'      => $stamp_duty_liability_bearer_map[$item['stamp_duty_liability_bearer']] ?? '',
                    'water_usage_units'                     => $item['water_usage_units'],
                    'electricity_usage_units'               => $item['electricity_usage_units'],
                    'surrounding_stores_average_rent'       => $item['surrounding_stores_average_rent'],
                    'other_options_average_cost'            => $item['other_options_average_cost'],
                ];
            }
        } elseif ($thread_status == WarehouseEnums::THREAD_STATUS_CONFIRM_ING) {
            //展示所有关联需求ID为当前仓库需求的仓库线索并且线索状态为确认中的线索, 先按照意向地址距离升序，再按照线索ID升序
            $relate_thread_list = WarehouseThreadRepository::getInstance()->onlySearchThread(['requirement_id' => $id, 'status' => WarehouseEnums::THREAD_STATUS_CONFIRM_ING], 'intention_address_distance,no ASC');

            $thread_ids = array_values(array_unique(array_filter(array_column($relate_thread_list, 'id'))));

            //线索平面图
            $thread_plan_attachment = SysAttachmentRepository::getInstance()->getAttachmentsListById($thread_ids, Enums::OSS_BUCKET_TYPE_WAREHOUSE_THREAD_PLAN_FILE);
            //仓库线索附件
            $thread_attachment  = SysAttachmentRepository::getInstance()->getAttachmentsListById($thread_ids, Enums::OSS_BUCKET_TYPE_WAREHOUSE_THREAD_INFO_FILE);

            foreach ($relate_thread_list as $item) {
                $rent_payment_method_item = [];
                $rent_payment_method      = !empty($item['rent_payment_method']) ? explode(',', $item['rent_payment_method']) : [];
                foreach ($rent_payment_method as $method_val) {
                    $rent_payment_method_item[] = $rent_payment_method_map[$method_val] ?? '';
                }

                $list[] = [
                    'id'                         => $item['id'],
                    'no'                         => $item['no'], //线索编号
                    'warehouse_latitude'         => $item['warehouse_latitude'], //仓库纬度
                    'warehouse_longitude'        => $item['warehouse_longitude'], //仓库经度
                    'intention_address_distance' => $item['intention_address_distance'], //意向地址距离
                    'warehouse_area'             => $item['warehouse_area'], //仓库面积
                    'warehouse_month_rent'       => $item['warehouse_month_rent'], //每月租金
                    'contract_period'            => $item['contract_period'] . static::$t->_(WarehouseEnums::$contract_period_unit[$item['contract_period_unit']]), //合同期
                    'deposit'                    => $item['deposit'], //押金
                    'down_payment'               => $item['down_payment'], //预付款
                    'cusa_fee'                   => $item['cusa_fee'],//Cusa费用
                    'maintenance_fee'            => $item['maintenance_fee'],//维护费
                    'entrance_fee'               => $item['entrance_fee'],//入场费
                    'garbage_fee'                => $item['garbage_fee'],//垃圾收理费
                    'parking_fee'                => $item['parking_fee'],//停车费
                    'other_fee'                  => $item['other_fee'],//其他费用
                    'currency_text'              => static::$t->_(GlobalEnums::$currency_item[$item['currency']]),
                    'warehouse_cr_text'          => $item['warehouse_cr'] ? static::$t->_(WarehouseEnums::$thread_has[$item['warehouse_cr']]) : '',//是否有CR
                    'warehouse_electricity_text' => $item['warehouse_electricity'] ? static::$t->_(WarehouseEnums::$thread_has[$item['warehouse_electricity']]) : '',//电供应
                    'warehouse_water_text'       => $item['warehouse_water'] ? static::$t->_(WarehouseEnums::$thread_has[$item['warehouse_water']]) : '',//水供应
                    'landlord_type_text'         => static::$t->_(WarehouseEnums::$landlord_type[$item['landlord_type']]),//房东类型
                    'landlord_aptitude_text'     => static::$t->_(WarehouseEnums::$landlord_aptitude[$item['landlord_aptitude']]),//资质文件是否齐全
                    'warehouse_address'          => $item['warehouse_address'], //仓库地址
                    'remark'                     => $item['remark'],//备注
                    'plan_attachments'           => $thread_plan_attachment[$item['id']] ?? [],//平面图
                    'attachments'                => $thread_attachment[$item['id']] ?? [],//附件

                    'is_install_fire_extinguishers_text'    => $is_install_fire_extinguishers_map[$item['is_install_fire_extinguishers']] ?? '',
                    'rent_payment_method_item'              => implode(',', $rent_payment_method_item),
                    'water_bill_payment_type_text'          => $water_bill_payment_type_map[$item['water_bill_payment_type']] ?? '',
                    'electricity_bill_payment_type_text'    => $electricity_bill_payment_type_map[$item['electricity_bill_payment_type']] ?? '',
                    'water_billing_method_text'             => $water_billing_method_map[$item['water_billing_method']] ?? '',
                    'electricity_billing_method_text'       => $electricity_billing_method_map[$item['electricity_billing_method']] ?? '',
                    'withholding_tax_liability_bearer_text' => $withholding_tax_liability_bearer_map[$item['withholding_tax_liability_bearer']] ?? '',
                    'land_tax_liability_bearer_text'        => $land_tax_liability_bearer_map[$item['land_tax_liability_bearer']] ?? '',
                    'stamp_duty_liability_bearer_text'      => $stamp_duty_liability_bearer_map[$item['stamp_duty_liability_bearer']] ?? '',
                    'water_usage_units'                     => $item['water_usage_units'],
                    'electricity_usage_units'               => $item['electricity_usage_units'],
                    'surrounding_stores_average_rent'       => $item['surrounding_stores_average_rent'],
                    'other_options_average_cost'            => $item['other_options_average_cost'],
                ];
            }
        }
        return $list;
    }

    /**
     * 获取需求 - 有确认线索
     * @param object $requirement_info 需求对象
     * @throws ValidationException
     * @return array
     */
    public function getRequirementThreadInfo($requirement_info)
    {
        $thread_enums = ThreadService::getInstance()->getEnums();
        $rent_payment_method_map              = array_column($thread_enums['rent_payment_method'], 'label', 'value');
        $water_bill_payment_type_map          = array_column($thread_enums['water_bill_payment_type'], 'label', 'value');
        $electricity_bill_payment_type_map    = array_column($thread_enums['electricity_bill_payment_type'], 'label', 'value');
        $water_billing_method_map             = array_column($thread_enums['water_billing_method'], 'label', 'value');
        $electricity_billing_method_map       = array_column($thread_enums['electricity_billing_method'], 'label', 'value');
        $is_install_fire_extinguishers_map    = array_column($thread_enums['is_install_fire_extinguishers'], 'label', 'value');
        $withholding_tax_liability_bearer_map = array_column($thread_enums['withholding_tax_liability_bearer'], 'label', 'value');
        $land_tax_liability_bearer_map        = array_column($thread_enums['land_tax_liability_bearer'], 'label', 'value');
        $stamp_duty_liability_bearer_map      = array_column($thread_enums['stamp_duty_liability_bearer'], 'label', 'value');

        //有确认线索 - 展示仓库需求关联的确认的线索数据
        $relate_thread_info = ThreadService::getInstance()->getThreadInfo($requirement_info->thread_id);

        $rent_payment_method = [];
        if (!empty($relate_thread_info->rent_payment_method)) {
            $rent_payment_method = array_map(function ($v) {
                return (int)$v;
            }, explode(',', $relate_thread_info->rent_payment_method));
        }

        return [
            'thread_no'                  => $relate_thread_info->no,
            'staff_id'                   => $relate_thread_info->staff_id,
            'staff_name'                 => $relate_thread_info->staff_name,
            'sys_department_name'        => $relate_thread_info->sys_department_name,
            'job_name'                   => $relate_thread_info->job_name,
            'warehouse_address'          => $relate_thread_info->warehouse_address,
            'warehouse_latitude'         => $relate_thread_info->warehouse_latitude,
            'warehouse_longitude'        => $relate_thread_info->warehouse_longitude,
            'warehouse_area'             => $relate_thread_info->warehouse_area,
            'warehouse_month_rent'       => $relate_thread_info->warehouse_month_rent,
            'contract_period'            => $relate_thread_info->contract_period . static::$t->_(WarehouseEnums::$contract_period_unit[$relate_thread_info->contract_period_unit]),
            'deposit'                    => $relate_thread_info->deposit,
            'down_payment'               => $relate_thread_info->down_payment,
            'cusa_fee'                   => $relate_thread_info->cusa_fee,
            'maintenance_fee'            => $relate_thread_info->maintenance_fee,
            'entrance_fee'               => $relate_thread_info->entrance_fee,
            'garbage_fee'                => $relate_thread_info->garbage_fee,
            'parking_fee'                => $relate_thread_info->parking_fee,
            'other_fee'                  => $relate_thread_info->other_fee,
            'currency'                   => $relate_thread_info->currency,
            'currency_text'              => static::$t->_(GlobalEnums::$currency_item[$relate_thread_info->currency]),
            'landlord_type'              => $relate_thread_info->landlord_type,
            'landlord_type_text'         => static::$t->_(WarehouseEnums::$landlord_type[$relate_thread_info->landlord_type]),
            'landlord_name'              => $relate_thread_info->landlord_name,
            'landlord_mobile'            => $relate_thread_info->landlord_mobile,
            'landlord_email'             => $relate_thread_info->landlord_email,
            'warehouse_cr_text'          => $relate_thread_info->warehouse_cr ? static::$t->_(WarehouseEnums::$thread_has[$relate_thread_info->warehouse_cr]) : '',
            'warehouse_electricity_text' => $relate_thread_info->warehouse_electricity ? static::$t->_(WarehouseEnums::$thread_has[$relate_thread_info->warehouse_electricity]) : '',
            'warehouse_water_text'       => $relate_thread_info->warehouse_water ? static::$t->_(WarehouseEnums::$thread_has[$relate_thread_info->warehouse_water]) : '',
            'warehouse_cr'               => $relate_thread_info->warehouse_cr,
            'warehouse_electricity'      => $relate_thread_info->warehouse_electricity,
            'warehouse_water'            => $relate_thread_info->warehouse_water,
            'landlord_aptitude'          => $relate_thread_info->landlord_aptitude,
            'landlord_aptitude_text'     => static::$t->_(WarehouseEnums::$landlord_aptitude[$relate_thread_info->landlord_aptitude]),
            'remark'                     => $relate_thread_info->remark,
            'attachments'                => $relate_thread_info->getAttachment()->toArray(),
            'plan_attachments'           => $relate_thread_info->getPlanAttachment()->toArray(),


            'rent_payment_method'                   => $rent_payment_method,
            'water_bill_payment_type'               => $relate_thread_info->water_bill_payment_type ? (int)$relate_thread_info->water_bill_payment_type : '',
            'water_bill_payment_type_text'          => $water_bill_payment_type_map[$relate_thread_info->water_bill_payment_type] ?? '',
            'electricity_bill_payment_type'         => $relate_thread_info->electricity_bill_payment_type ? (int)$relate_thread_info->electricity_bill_payment_type : '',
            'electricity_bill_payment_type_text'    => $electricity_bill_payment_type_map[$relate_thread_info->electricity_bill_payment_type] ?? '',
            'water_billing_method'                  => $relate_thread_info->water_billing_method ? (int)$relate_thread_info->water_billing_method : '',
            'water_billing_method_text'             => $water_billing_method_map[$relate_thread_info->water_billing_method] ?? '',
            'electricity_billing_method'            => $relate_thread_info->electricity_billing_method ? (int)$relate_thread_info->electricity_billing_method : '',
            'electricity_billing_method_text'       => $electricity_billing_method_map[$relate_thread_info->electricity_billing_method] ?? '',
            'is_install_fire_extinguishers'         => $relate_thread_info->is_install_fire_extinguishers ? (int)$relate_thread_info->is_install_fire_extinguishers : '',
            'is_install_fire_extinguishers_text'    => $is_install_fire_extinguishers_map[$relate_thread_info->is_install_fire_extinguishers] ?? '',
            'withholding_tax_liability_bearer'      => $relate_thread_info->withholding_tax_liability_bearer ? (int)$relate_thread_info->withholding_tax_liability_bearer : '',
            'withholding_tax_liability_bearer_text' => $withholding_tax_liability_bearer_map[$relate_thread_info->withholding_tax_liability_bearer] ?? '',
            'land_tax_liability_bearer'             => $relate_thread_info->land_tax_liability_bearer ? (int)$relate_thread_info->land_tax_liability_bearer : '',
            'land_tax_liability_bearer_text'        => $land_tax_liability_bearer_map[$relate_thread_info->land_tax_liability_bearer] ?? '',
            'stamp_duty_liability_bearer'           => $relate_thread_info->stamp_duty_liability_bearer ? (int)$relate_thread_info->stamp_duty_liability_bearer : '',
            'stamp_duty_liability_bearer_text'      => $stamp_duty_liability_bearer_map[$relate_thread_info->stamp_duty_liability_bearer] ?? '',
            'surrounding_stores_average_rent'       => $relate_thread_info->surrounding_stores_average_rent,
            'other_options_average_cost'            => $relate_thread_info->other_options_average_cost,
            'electricity_usage_units'               => $relate_thread_info->electricity_usage_units,
            'water_usage_units'                     => $relate_thread_info->water_usage_units,

        ];
    }

    /**
     * 仓库需求查看
     * @param int $id 需求ID
     * @param int $type 列表 1仓库需求管理-列表、2处理仓库需求-待寻找、3处理仓库需求-待确认、4处理仓库需求-待入驻
     * @return array
     */
    public function view(int $id, int $type = self::REQUIREMENT_LIST)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $data    = [];

        try {
            //需求信息
            $requirement_info = $this->getRequirementInfo($id);

            //处理仓库需求-只能看各自状态下的数据
            if (($type == self::REQUIREMENT_SEARCH_LIST && $requirement_info->status != WarehouseEnums::REQUIREMENT_STATUS_SEARCH)
                || ($type == self::REQUIREMENT_CONFIRM_LIST && $requirement_info->status != WarehouseEnums::REQUIREMENT_STATUS_CONFIRM)
                || ($type == self::REQUIREMENT_SETTLE_LIST && $requirement_info->status != WarehouseEnums::REQUIREMENT_STATUS_SETTLE)
                || ($type == self::REQUIREMENT_RENEWED_LIST && $requirement_info->status != WarehouseEnums::REQUIREMENT_STATUS_PENDING_RENEWED)
            ) {
                throw new ValidationException(static::$t->_('warehouse_requirement_not_exists'), ErrCode::$VALIDATE_ERROR);
            }

            $data = $this->getRequirementDetail($requirement_info);

            //关联线索
            $data['relate_thread_list'] = [];
            $data['relate_thread_info'] = $data['verify_info'] = $data['settled_info'] = (object)[];
            if ($requirement_info->thread_id) {
                //有确认线索 - 展示仓库需求关联的确认的线索数据
                $data['relate_thread_info'] = $this->getRequirementThreadInfo($requirement_info);

                //验仓结果 - 当提交了验仓结果以后展示具体字段界面，取需求关联的最终线索获取的验仓结果
                $data['verify_info'] = ThreadService::getInstance()->getThreadVerifySubmitDetail($requirement_info->thread_id);
            } else {
                //无确认线索 - 展示所有关联需求ID为当前仓库需求的仓库线索并且线索状态不为已作废的线索(按照线索ID升序)
                $data['relate_thread_list'] = $this->getRequirementThreadList($id);
            }

            //入驻信息
            if (!empty($requirement_info->actual_settled_store_id)) {
                $data['settled_info'] = [
                    'actual_settled_store_id'   => $requirement_info->actual_settled_store_id,  //实际入驻网点编码
                    'actual_settled_store_name' => $requirement_info->actual_settled_store_name,//实际入驻网点
                    'actual_settled_date'       => $requirement_info->actual_settled_date,      //实际入驻日期
                ];
            }

            //需求确认记录
            $data['confirm_list'] = $this->getRequirementConfirmList($requirement_info);
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('仓库管理-仓库需求管理-详情获取失败:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 处理需求管理-待寻找-录入线索-查看
     * @param int $id 需求ID
     * @return array
     */
    public function searchAddThreadView(int $id)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $data    = [];

        try {
            //需求信息
            $requirement_info = $this->getRequirementInfo($id);
            $data             = $this->getRequirementDetail($requirement_info);
            //关联线索 - 无确认线索 - 展示所有关联需求ID为当前仓库需求的仓库线索并且线索状态不为已作废的线索(按照线索ID升序)
            $data['relate_thread_list'] = $this->getRequirementThreadList($id);
            foreach ($data['relate_thread_list'] as &$item) {
                //费用相关的不可被看到
                unset($item['cusa_fee'], $item['maintenance_fee'], $item['entrance_fee'], $item['garbage_fee'], $item['parking_fee'], $item['other_fee'], $item['currency_text']);
            }
            //需求确认记录
            $data['confirm_list'] = $this->getRequirementConfirmList($requirement_info);
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('仓库管理-处理需求管理-待寻找-录入线索-查看失败:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 处理需求管理-待寻找-去确认-查看
     * @param int $id 需求ID
     * @return array
     */
    public function confirmConfirmView(int $id)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $data    = [];

        try {
            //需求信息
            $requirement_info = $this->getRequirementInfo($id);
            $data             = $this->getRequirementDetail($requirement_info);
            //关联线索 - 无确认线索 - 展示所有关联需求ID为当前仓库需求的仓库线索并且线索状态为确认中的线索
            $data['relate_thread_list'] = $this->getRequirementThreadList($id, WarehouseEnums::THREAD_STATUS_CONFIRM_ING);
            //需求确认记录
            $data['confirm_list'] = $this->getRequirementConfirmList($requirement_info);
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('仓库管理-处理需求管理-待寻找-去确认-查看失败:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 处理需求管理-待入驻-入驻-查看
     * @param int $id 需求ID
     * @return array
     */
    public function settleSettleView(int $id)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $data    = [];

        try {
            //需求信息
            $requirement_info = $this->getRequirementInfo($id);
            $data             = $this->getRequirementDetail($requirement_info);
            //关联线索 - 展示仓库需求关联的确认的线索数据
            $data['relate_thread_info'] = $this->getRequirementThreadInfo($requirement_info);
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('仓库管理-处理需求管理-待入驻-入驻-查看失败:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 仓库需求管理-查看-关联线索-转移/处理需求管理-待寻找-查看-关联线索-转移
     * @param array $params 参数组
     * @param array $user 当前登陆人信息组
     * @return array
     */
    public function transfer(array $params, array $user)
    {
        return ThreadService::getInstance()->transferThread($params, $user);
    }

    /**
     * 仓库需求作废
     * @param array $params 参数组
     * @param array $user 当前登陆人信息组
     * @return array
     */
    public function cancel(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            //需求状态为待寻找、待确认、待报价、报价审核中、待签约、待续约时才可作废
            $requirement_info = $this->getRequirementInfo($params['id']);
            if (!in_array($requirement_info->status, [
                WarehouseEnums::REQUIREMENT_STATUS_SEARCH,
                WarehouseEnums::REQUIREMENT_STATUS_CONFIRM,
                WarehouseEnums::REQUIREMENT_STATUS_PRICE,
                WarehouseEnums::REQUIREMENT_STATUS_PRICE_AUDIT,
                WarehouseEnums::REQUIREMENT_STATUS_SIGN,
                WarehouseEnums::REQUIREMENT_STATUS_PENDING_RENEWED,
            ])) {
                throw new ValidationException(static::$t->_('warehouse_requirement_cancel_error'), ErrCode::$VALIDATE_ERROR);
            }

            $now = date('Y-m-d H:i:s');
            //1. 将仓库需求状态更新为已作废、记录作废原因以及作废时间、作废操作人
            $requirement_info->status        = WarehouseEnums::REQUIREMENT_STATUS_CANCEL;
            $requirement_info->cancel_reason = $params['cancel_reason'];
            $requirement_info->cancel_at     = $now;
            $requirement_info->updated_id    = $user['id'];
            $requirement_info->updated_name  = $user['name'];
            $requirement_info->updated_at    = $now;
            $bool = $requirement_info->save();
            if ($bool === false) {
                throw new BusinessException('记录需求作废信息失败：' . json_encode($params, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($requirement_info), ErrCode::$BUSINESS_ERROR);
            }

            //2. 如果仓库需求有关联的线索，则将所有关联的线索状态更新为待关联，并释放线索与仓库需求的关联关系
            $db->updateAsDict(
                (new WarehouseThreadModel())->getSource(),
                ['status' => WarehouseEnums::THREAD_STATUS_ASSOCIATION, 'requirement_id' => 0, 'requirement_no' => '', 'updated_id' => $user['id'], 'updated_name' => $user['name'], 'updated_at' => $now],
                ['conditions' => "requirement_id = {$requirement_info->id} and status != " . WarehouseEnums::THREAD_STATUS_CANCEL]
            );

            //3. 如果仓库需求有关联的确认线索
            if (!empty($requirement_info->thread_id)) {
                $thread_info = ThreadService::getInstance()->getThreadInfo($requirement_info->thread_id);
                //3.1. 如果仓库需求有关联的正在审批中的报价申请，则撤回报价申请审批流，撤回人为报价申请发起人，撤回原因固定为“The warehouse demand is invalidated, and the associated quotation application is invalidated”
                $price_info = $thread_info->getPrice();
                if ($price_info && $price_info->status == Enums::WF_STATE_PENDING) {
                    //调取by接口，by撤销成功，记录撤回信息
                    $by_workflow = new ByWorkflowService();
                    $by_workflow->audit([
                        'serial_no'   => $price_info->workflow_no,
                        'biz_type'    => ByWorkflowEnums::BY_BIZ_TYPE_WAREHOUSE_THREAD_PRICE,
                        'reason'      => 'The warehouse demand is invalidated, and the associated quotation application is invalidated',
                        'status'      => ByWorkflowEnums::BY_OPERATE_CANCEL,
                        'operator_id' => $price_info->created_id,
                    ]);

                    $cancel_data = [
                        'reason'       => 'The warehouse demand is invalidated, and the associated quotation application is invalidated',
                        'status'       => Enums::WF_STATE_CANCEL,
                        'updated_id'   => $user['id'],
                        'updated_name' => $user['name'],
                        'updated_at'   => $now,
                    ];
                    $bool = $price_info->i_update($cancel_data);
                    if ($bool === false) {
                        throw new BusinessException('正在审批中的报价申请，审核状态更新为撤回失败: '. json_encode($cancel_data, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($price_info), ErrCode::$BUSINESS_ERROR);
                    }
                }

                //3.2. 如果仓库需求有关联的待验证的验仓数据，状态更新为取消验证
                $verify_info = $thread_info->getVerify();
                if ($verify_info) {
                    $verify_info->status     = WarehouseEnums::THREAD_VERIFY_STATUS_CANCEL;
                    $verify_info->updated_at = $now;
                    $bool                    = $verify_info->save();
                    if ($bool === false) {
                        throw new BusinessException('待验证的验仓数据，状态更新为取消验证失败：' . json_encode($params, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($verify_info), ErrCode::$BUSINESS_ERROR);
                    }
                }

                //3.3. 如果仓库需求有关联的正在审批中的网点租房合同，则撤回网点租房合同审批流，撤回人为网点租房合同发起人，撤回原因固定为“The warehouse demand is invalidated, and the related outlet rental contract is invalidated”
                if ($thread_info->warehouse_id) {
                    //线索有关联的仓库，仓库有关联的正在审批中网点租房合同
                    $this->cancelContractStoreRenting($thread_info->warehouse_id);
                }
            }
            $db->commit();
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        } catch (Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }

        if ($message) {
            $db->rollback();
        }
        if ($real_message) {
            $this->logger->error('仓库管理-仓库需求管理-作废失败: ' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => [],
        ];
    }

    /**
     * 如果仓库需求有关联的正在审批中的网点租房合同，则撤回网点租房合同审批流，撤回人为网点租房合同发起人，撤回原因固定为“The warehouse demand is invalidated, and the related outlet rental contract is invalidated”
     * 可能会出现一个仓库对应多份合同的场景一般是一份
     * @param string $warehouse_id 仓库ID
     * @return bool
     * @throws BusinessException
     * @throws ValidationException
     */
    private function cancelContractStoreRenting(string $warehouse_id)
    {
        $contract_list = $this->getWarehouseContractList($warehouse_id, Enums::WF_STATE_PENDING);
        if (!$contract_list) {
            return true;
        }

        //撤回正在审批中网点租房合同
        $user_service = new UserService();
        foreach ($contract_list as $contract) {
            $user_info = $user_service->getLoginUser($contract['contract_leader_id']);
            ContractStoreRentingService::getInstance()->cancel($user_info, ['id'=> $contract['id'], 'cancel_reason' => 'The warehouse demand is invalidated, and the related outlet rental contract is invalidated',]);
        }
        return true;
    }

    /**
     * 仓库需求查看报价
     * @param int $id 报价单id
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function viewPrice(int $id, array $user)
    {
        return PriceService::getInstance()->view($id, $user);
    }

    /**
     * 处理需求管理-待入驻-入驻-确认入驻
     * @param array $params 参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function settle(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db      = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $requirement_info = $this->getRequirementInfo($params['id']);
            //只有待入驻的需求才能做此操作
            if ($requirement_info->status != WarehouseEnums::REQUIREMENT_STATUS_SETTLE) {
                throw new ValidationException(static::$t->_('warehouse_requirement_status_error', ['status' => static::$t->_(WarehouseEnums::getRequirementStatus()[WarehouseEnums::REQUIREMENT_STATUS_SETTLE])]), ErrCode::$VALIDATE_ERROR);
            }

            $thread_info = ThreadService::getInstance()->getThreadInfo($requirement_info->thread_id);

            $now = date('Y-m-d H:i:s');
            //1. 保存填写的入驻信息，同时记录操作人及操作时间、将仓库需求状态更新为已入驻
            $requirement_info->status                    = WarehouseEnums::REQUIREMENT_STATUS_SETTLED;//已入驻
            $requirement_info->actual_settled_date       = $params['actual_settled_date'];
            $requirement_info->actual_settled_store_id   = $params['actual_settled_store_id'];
            $requirement_info->actual_settled_store_name = $params['actual_settled_store_name'];
            $requirement_info->updated_id                = $user['id'];
            $requirement_info->updated_name              = $user['name'];
            $requirement_info->updated_at                = $now;
            $bool                                        = $requirement_info->save();
            if ($bool === false) {
                throw new BusinessException('需求状态保存入驻信息失败: ' . json_encode($params, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($requirement_info), ErrCode::$BUSINESS_ERROR);
            }

            //2. 将关联的线索(唯一)状态更新为已入驻
            $thread_info->status       = WarehouseEnums::THREAD_STATUS_SETTLED;//已入驻
            $thread_info->updated_id   = $user['id'];
            $thread_info->updated_name = $user['name'];
            $thread_info->updated_at   = $now;
            $bool = $thread_info->save();
            if ($bool === false) {
                throw new BusinessException('线索状态改为已入驻失败: ' . json_encode(['status' => WarehouseEnums::THREAD_STATUS_SETTLED], JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($thread_info), ErrCode::$BUSINESS_ERROR);
            }

            $db->commit();
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        } catch (Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }

        if ($message) {
            $db->rollback();
        }

        if ($real_message) {
            $this->logger->error('仓库管理-处理需求管理-待入驻-待入驻-入驻失败: ' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => [],
        ];
    }

    /**
     * 批量导入文件添加到上传中心
     *
     * @param array $user
     * @return bool|mixed
     * @throws BusinessException
     * @throws GuzzleException
     * @throws ValidationException
     */
    public function addBatchImportToImportCenter(array $user)
    {
        if (!$this->request->hasFiles()) {
            throw new ValidationException(static::$t->_('bank_flow_not_found_file'), ErrCode::$VALIDATE_ERROR);
        }

        $file      = $this->request->getUploadedFiles()[0];
        $extension = $file->getExtension();
        if ($extension != 'xlsx') {
            throw new ValidationException(static::$t->_('bank_flow_upload_file_type_error'), ErrCode::$VALIDATE_ERROR);
        }

        $config = ['path' => ''];
        $excel  = new \Vtiful\Kernel\Excel($config);

        // 读取上传文件数据
        $excel_data = $excel->openFile($file->getTempName())
            ->openSheet()
            ->setType(ImportCenterEnums::$task_excel_columns_type_config[ImportCenterEnums::TYPE_WAREHOUSE_REQUIREMENT])
            ->getSheetData();

        $this->logger->info(['excel_file_data' => $excel_data]);

        // Excel空数据校验: 不含表头
        $excel_content_data = array_slice($excel_data, 1);
        if (empty($excel_content_data)) {
            throw new ValidationException(static::$t->_('bank_flow_data_empty'), ErrCode::$VALIDATE_ERROR);
        }

        // 单次导入最多行数校验
        if (count($excel_content_data) > WarehouseEnums::WAREHOUSE_REQUIREMENT_IMPORT_MAX_COUNT) {
            throw new ValidationException(static::$t->_('warehouse_requirement_import_error_001',
                ['max_count' => WarehouseEnums::WAREHOUSE_REQUIREMENT_IMPORT_MAX_COUNT]), ErrCode::$VALIDATE_ERROR);
        }

        // 文件生成OSS链接
        $file_path = sys_get_temp_dir() . '/' . mt_rand(10, 10000) . '_' . $file->getName();
        $file->moveTo($file_path);
        $oss_result = OssHelper::uploadFile($file_path);
        $this->logger->info(['excel_file_data_oss_result' => $oss_result]);
        if (empty($oss_result['object_url'])) {
            throw new ValidationException(static::$t->_('file_upload_error'), ErrCode::$VALIDATE_ERROR);
        }

        ImportCenterService::getInstance()->addImportCenter($user, $oss_result['object_url'], ImportCenterEnums::TYPE_WAREHOUSE_REQUIREMENT);

        return [
            'code'    => ErrCode::$SUCCESS,
            'message' => static::$t->_('success'),
        ];
    }

    /**
     * 处理批量导入的数据
     *
     * @param array $excel_data
     * @param array $user
     * @return array
     * @throws GuzzleException
     */
    public function batchHandleImportFileData(array $excel_data, array $user)
    {
        // 返回结果
        $code    = ErrCode::$SUCCESS;
        $message = static::$t->_('success');
        $data    = [];

        // 表头
        $excel_header = array_shift($excel_data);

        // Excel业务数据行处理
        foreach ($excel_data as $k => $v) {
            $v              = trim_array(array_slice($v, 0, 16));
            $excel_data[$k] = $v;
        }

        // 获取网点类型
        $store_category_item = self::getSettingEnvStoreCategoryEnums();
        $store_category_item = array_column($store_category_item, 'value');

        $store_name_item     = array_column($excel_data, 0);
        $store_params        = [
            'pagesize'            => WarehouseEnums::SYS_STORE_DEFAULT_MAX_TOTAL,
            'store_category_item' => $store_category_item,
            'name_item'           => array_filter($store_name_item),
        ];

        // 获取网点信息
        $store_item    = (new StoreService())->getAllStoreList(false, $store_params);
        $all_store_ids = array_column($store_item, 'id');
        $store_item    = array_map(function ($item) {
            $item['lower_name'] = strtolower(trim($item['name']));
            return $item;
        }, $store_item);
        $store_item    = array_group_by_column($store_item, 'lower_name');

        // 获取仓库类型等相关枚举配置
        $enums_item         = $this->getEnums();
        $opening_status_map = array_map(function ($item) {
            $item['label'] = strtolower($item['label']);
            return $item;
        }, $enums_item['opening_status']);
        $opening_status_map = array_column($opening_status_map, 'value', 'label');


        $priority_level_map = array_map(function ($item) {
            $item['label'] = strtolower($item['label']);
            return $item;
        }, $enums_item['priority_level']);
        $priority_level_map = array_column($priority_level_map, 'value', 'label');

        $warehouse_type_map = array_map(function ($item) {
            if ($item['is_available'] == 1) {
                $item['label'] = strtolower($item['label']);
                return $item;
            }
        }, $enums_item['warehouse_type']);
        $warehouse_type_map = array_column($warehouse_type_map, 'value', 'label');

        // 获取仓库信息
        $warehouse_name_item = array_column($excel_data, 3);
        $warehouse_info_item = ContractWarehouseRepository::getInstance()->getListByName($warehouse_name_item);
        $warehouse_info_item = array_map(function ($item) {
            $item['lower_warehouse_name'] = strtolower(trim($item['warehouse_name']));
            return $item;
        }, $warehouse_info_item);
        $warehouse_info_item = array_group_by_column($warehouse_info_item, 'lower_warehouse_name');

        // 获取区市省级联信息(与页面取值规则保持一致)
        $lang_arr = [
            'en'    => 'en_name',
            'th'    => 'name',
            'zh-CN' => 'en_name',
            'zh'    => 'en_name',
        ];
        $district_item = $this->getAllDistrictItem($lang_arr[static::$language] ?? 'en_name');

        // 获取所有网点的大区片区
        $store_region_piece_item = (new StoreService())->getStoreListRegionAndPiece($all_store_ids);

        // 获取仓库需求关联的原仓库ID: 仓库类型是续约 且 状态是待续约/待续签
        $warehouse_type_renewed_enums   = static::getWarehouseTypeRenewedEnums();
        $pending_status                 = [
            WarehouseEnums::REQUIREMENT_STATUS_SIGN,
            WarehouseEnums::REQUIREMENT_STATUS_PAY,
            WarehouseEnums::REQUIREMENT_STATUS_PENDING_RENEWED,

        ];
        $all_original_warehouse_id_list = WarehouseRequirementRepository::getInstance()->getOriginalWarehouseIdListByStatus($warehouse_type_renewed_enums, $pending_status);

        $today_date_time = strtotime(date('Y-m-d 00:00:00'));

        // 2. 文件逐行校验
        $error_list      = [];
        $correct_list    = [];
        foreach ($excel_data as $row_k => $row_v) {
            $error_remark = [];

            $store_name              = strtolower($row_v[0] ?? '');                                     // 网点名称
            $opening_status_text     = strtolower($row_v[1] ?? '');                                     // 开业状态
            $warehouse_type_text     = strtolower($row_v[2] ?? '');                                     // 仓库类型
            $original_warehouse_name = strtolower($row_v[3] ?? '');                                     // 原有仓库名称
            $priority_level_text     = strtolower($row_v[4] ?? '');                                     // 优先级
            $expect_settled_date     = $row_v[5] ?? '';                                                 // 预计入驻日期
            $area_min                = $row_v[9] ?? '';                                                 // 意向最小面积
            $area_max                = $row_v[10] ?? '';                                                // 意向最大面积
            $warehouse_longitude     = $row_v[11] ?? '';                                                // 意向经度
            $warehouse_latitude      = $row_v[12] ?? '';                                                // 意向纬度
            $parking_num_min         = $row_v[13] ?? '';                                                // van最少车位
            $parking_num_max         = $row_v[14] ?? '';                                                // van最多车位
            $remark                  = $row_v[15] ?? '';                                                // 备注
            $district_name_key       = strtolower($row_v[6] . '_' . $row_v[7] . '_' . $row_v[8]);       // 意向省市区名称

            $store_info = $store_item[$store_name] ?? [];
            if (mb_strlen($store_name) < 1 || empty($store_info[0]) || count($store_info) > 1) {
                $error_remark[] = static::$t->_('warehouse_requirement_import_error_002');
            }

            $opening_status = $opening_status_map[$opening_status_text] ?? null;
            if (mb_strlen($opening_status_text) < 1 || is_null($opening_status)) {
                $error_remark[] = static::$t->_('warehouse_requirement_import_error_003');
            }

            $warehouse_type = $warehouse_type_map[$warehouse_type_text] ?? null;
            if (mb_strlen($warehouse_type_text) < 1 || is_null($warehouse_type)) {
                $error_remark[] = static::$t->_('warehouse_requirement_import_error_004');
            }

            $original_warehouse_id = '';
            if (empty($warehouse_type_renewed_enums)) {
                $error_remark[] = static::$t->_('warehouse_requirement_import_error_018');
            } elseif ($warehouse_type == $warehouse_type_renewed_enums) {
                if (mb_strlen($original_warehouse_name) < 1) {
                    $error_remark[] = static::$t->_('warehouse_requirement_import_error_005');
                } else {
                    $warehouse_info = $warehouse_info_item[$original_warehouse_name] ?? [];
                    if (empty($warehouse_info) || count($warehouse_info) > 1) {
                        $error_remark[] = static::$t->_('warehouse_requirement_import_error_005');
                    } else {
                        $original_warehouse_id = $warehouse_info[0]['warehouse_id'] ?? '';

                        // 原有仓库是否已关联续约类型且待续约/待签约状态的需求
                        if (in_array($original_warehouse_id, $all_original_warehouse_id_list)) {
                            $error_remark[] = static::$t->_('warehouse_requirement_import_error_020');
                        }
                    }
                }
            }

            $priority_level = $priority_level_map[$priority_level_text] ?? null;
            if (mb_strlen($priority_level_text) < 1 || is_null($priority_level)) {
                $error_remark[] = static::$t->_('warehouse_requirement_import_error_006');
            }

            if (preg_match('/^\d{10}$/', $expect_settled_date)) {
                $expect_settled_date = date('Y-m-d', $expect_settled_date);
            }

            $expect_settled_date_time = strtotime($expect_settled_date);
            if ($expect_settled_date_time === false || (!validate_date($expect_settled_date, '/') && !validate_date($expect_settled_date, '-'))) {
                $error_remark[] = static::$t->_('warehouse_requirement_import_error_007');
            } elseif ($expect_settled_date_time < $today_date_time) {
                $error_remark[] = static::$t->_('warehouse_requirement_import_error_019');
            }

            $district_info = $district_item[$district_name_key] ?? [];
            if (empty($district_info) || $district_info['name_repeat_count'] > 1) {
                $error_remark[] = static::$t->_('warehouse_requirement_import_error_008');
            }

            // 经度校验
            $longitude_check_res = self::checkLongitude($warehouse_longitude);
            if (!empty($longitude_check_res)) {
                $error_remark[] = $longitude_check_res;
            }

            // 纬度校验
            $latitude_check_result = self::checkLatitude($warehouse_latitude);
            if (!empty($latitude_check_result)) {
                $error_remark[] = $latitude_check_result;
            }

            // 最小面积
            if (!preg_match(WarehouseEnums::AREA_RULE, $area_min)) {
                $error_remark[] = static::$t->_('warehouse_requirement_import_error_011');
            }

            // 最大面积
            if (!preg_match(WarehouseEnums::AREA_RULE, $area_max)) {
                $error_remark[] = static::$t->_('warehouse_requirement_import_error_013');
            }

            if (is_numeric($area_min) && is_numeric($area_max) && $area_min > $area_max) {
                $error_remark[] = static::$t->_('warehouse_requirement_import_error_012');
            }

            if (!preg_match(WarehouseEnums::PARKING_NUM_RULE, $parking_num_min)) {
                $error_remark[] = static::$t->_('warehouse_requirement_import_error_014');
            }

            if (!preg_match(WarehouseEnums::PARKING_NUM_RULE, $parking_num_max)) {
                $error_remark[] = static::$t->_('warehouse_requirement_import_error_016');
            }

            if (is_numeric($parking_num_min) && is_numeric($parking_num_max) && $parking_num_min > $parking_num_max) {
                $error_remark[] = static::$t->_('warehouse_requirement_import_error_015');
            }

            if (mb_strlen($remark) > 500) {
                $error_remark[] = static::$t->_('warehouse_requirement_import_error_017');
            }

            // 校验有误的
            if (!empty($error_remark)) {
                $row_v[5]     = $expect_settled_date;
                $row_v[16]    = implode(';', $error_remark);
                $error_list[] = $row_v;
                continue;
            }

            $store_region_info = $store_region_piece_item[$store_info[0]['id']] ?? [];

            $status = WarehouseEnums::REQUIREMENT_STATUS_SEARCH;
            if ($warehouse_type == $warehouse_type_renewed_enums) {
                $status = WarehouseEnums::REQUIREMENT_STATUS_PENDING_RENEWED;
            }

            $correct_list[] = [
                'apply_date'            => date('Y-m-d'),
                'store_id'              => $store_info[0]['id'],
                'store_name'            => $store_info[0]['name'],
                'region_id'             => $store_region_info['region_id'] ?? '',
                'region_name'           => $store_region_info['region_name'] ?? '',
                'piece_id'              => $store_region_info['piece_id'] ?? '',
                'piece_name'            => $store_region_info['piece_name'] ?? '',
                'area'                  => '',
                'province_code'         => $district_info['province_code'],
                'city_code'             => $district_info['city_code'],
                'district_code'         => $district_info['district_code'],
                'warehouse_longitude'   => $warehouse_longitude,
                'warehouse_latitude'    => $warehouse_latitude,
                'area_min'              => $area_min,
                'area_max'              => $area_max,
                'expect_settled_date'   => $expect_settled_date,
                'opening_status'        => $opening_status,
                'warehouse_type'        => $warehouse_type,
                'priority_level'        => $priority_level,
                'status'                => $status,
                'remark'                => $remark,
                'created_id'            => $user['id'],
                'created_name'          => $user['name'],
                'updated_id'            => $user['id'],
                'updated_name'          => $user['name'],
                'created_at'            => date('Y-m-d H:i:s'),
                'updated_at'            => date('Y-m-d H:i:s'),
                'store_category'        => $store_info[0]['category'],
                'original_warehouse_id' => $original_warehouse_id,
                'parking_num_min'       => $parking_num_min,
                'parking_num_max'       => $parking_num_max,
            ];
        }

        $data['error_num']   = count($error_list);
        $data['success_num'] = count($correct_list);
        $data['result_file_url'] = '';

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            // 将有误的写入错误文件
            if (!empty($error_list)) {
                $file_name        = 'Warehouse_Requirement_Import_Results_' . date('YmdHis') . '.xlsx';
                $excel_header[16] = static::$t->_('check_result');
                $export_res       = $this->exportExcel($excel_header, $error_list, $file_name);
                if (empty($export_res['data'])) {
                    throw new BusinessException('生成结果文件失败, 请检查', ErrCode::$BUSINESS_ERROR);
                }

                $data['result_file_url'] = $export_res['data'];
            } else {
                // 写入校验通过
                foreach ($correct_list as $item) {
                    $requirement_no = static::genSerialNo(WarehouseEnums::REQUIREMENT_NO_PREFIX, RedisKey::WAREHOUSE_REQUIREMENT_ADD_COUNTER);
                    $item['no']     = $this->getRequirementNo($requirement_no);

                    $requirement_model = new WarehouseRequirementModel();
                    if ($requirement_model->i_create($item) === false) {
                        $this->logger->notice(['warehousere_quirement_async_fail_data' => $item]);
                        throw new BusinessException('仓库需求-异步导入任务异常,msg=' . get_data_object_error_msg($requirement_model), ErrCode::$BUSINESS_ERROR);
                    }
                }
            }

            $db->commit();
        } catch (BusinessException $e) {
            $db->rollback();
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            $db->rollback();
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = $e->getMessage();
        }

        if ($code != ErrCode::$SUCCESS) {
            $this->logger->error('仓库管理-仓库需求-异步导入异常, 原因可能是=' . $message);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 获取所有省市区名称
     * @param string $name_field
     * @return array
     */
    public function getAllDistrictItem(string $name_field = 'en_name')
    {
        if (!in_array($name_field, ['name', 'en_name'])) {
            return [];
        }

        $columns = [
            'district.code AS district_code',
            "district.{$name_field} AS district_name",
            'city.code AS city_code',
            "city.{$name_field} AS city_name",
            'province.code AS province_code',
            "province.{$name_field} AS province_name",
        ];

        $list = $this->modelsManager->createBuilder()
            ->from(['district' => SysDistrictModel::class])
            ->leftjoin(SysCityModel::class, 'district.city_code = city.code', 'city')
            ->leftjoin(SysProvinceModel::class, 'district.province_code = province.code', 'province')
            ->andWhere('district.deleted = :deleted: AND city.deleted = :deleted: AND province.deleted = :deleted:', [
                'deleted' => GlobalEnums::IS_NO_DELETED,
            ])
            ->columns($columns)
            ->getQuery()->execute()->toArray();

        $item = [];
        foreach ($list as $val) {
            $unique_key = strtolower($val['province_name'] . '_' . $val['city_name'] . '_' . $val['district_name']);

            $exist_item = $item[$unique_key] ?? [];
            if (empty($exist_item)) {
                $val['name_repeat_count'] = 1;
            } else {
                $val['name_repeat_count'] = $exist_item['name_repeat_count'] + 1;
            }

            $item[$unique_key] = $val;
        }

        return $item;
    }

    /**
     * 经度校验
     * @param string $warehouse_longitude 经度
     */
    public static function checkLongitude(string $warehouse_longitude)
    {
        if (empty($warehouse_longitude)) {
            return static::$t->_('warehouse_requirement_import_error_010');
        }

        static $warehouse_geo_config = [];
        if (empty($warehouse_geo_config)) {
            $warehouse_geo_config = EnumsService::getInstance()->getSettingEnvValueMap('warehouse_geo_config');
        }

        $base_rule = $warehouse_geo_config['base_rule'];
        $geo_scope = $warehouse_geo_config['geo_scope'];

        // 仓库经度范围区间判断
        if ($warehouse_longitude < $geo_scope['longitude']['min'] || $warehouse_longitude > $geo_scope['longitude']['max']) {
            return static::$t->_('warehouse_requirement_import_error_010');
        }

        if (!preg_match($base_rule['longitude'], $warehouse_longitude)) {
            return static::$t->_('warehouse_requirement_import_error_010');
        }

        return '';
    }

    /**
     * 纬度校验
     * @param string $warehouse_latitude 纬度
     */
    public static function checkLatitude(string $warehouse_latitude)
    {
        if (empty($warehouse_latitude)) {
            return static::$t->_('warehouse_requirement_import_error_009');
        }

        static $warehouse_geo_config = [];
        if (empty($warehouse_geo_config)) {
            $warehouse_geo_config = EnumsService::getInstance()->getSettingEnvValueMap('warehouse_geo_config');
        }

        $base_rule = $warehouse_geo_config['base_rule'];
        $geo_scope = $warehouse_geo_config['geo_scope'];

        // 仓库纬度范围区间判断
        if ($warehouse_latitude < $geo_scope['latitude']['min'] || $warehouse_latitude > $geo_scope['latitude']['max']) {
            return static::$t->_('warehouse_requirement_import_error_009');
        }

        if (!preg_match($base_rule['latitude'], $warehouse_latitude)) {
            return static::$t->_('warehouse_requirement_import_error_009');
        }

        return '';
    }

    /**
     * 处理仓库需求-待续约-谈判失败信息
     * @param int $id 需求ID
     * @return array
     * @throws ValidationException
     */
    public function getNegotiationsFailedInfo(int $id)
    {
        $data = [];

        //需求信息
        $requirement_model = $this->getRequirementInfo($id);

        //处理仓库需求-只能看各自状态下的数据
        if ($requirement_model->status != WarehouseEnums::REQUIREMENT_STATUS_PENDING_RENEWED) {
            throw new ValidationException(static::$t->_('warehouse_requirement_status_error', ['status' => static::$t->_(WarehouseEnums::getRequirementStatus()[WarehouseEnums::REQUIREMENT_STATUS_PENDING_RENEWED])]), ErrCode::$VALIDATE_ERROR);
        }

        // 获取原有仓库信息
        $warehouse_model = ContractWarehouseRepository::getInstance()->getOneByWarehouseId($requirement_model->original_warehouse_id);

        // 使用中的主网点信息
        $main_store_info = [];
        if (!empty($warehouse_model)) {
            $main_store_info = $warehouse_model->getMainStoreInfo();
            $main_store_info = !empty($main_store_info) ? $this->getWarehouseStoreDetail([$main_store_info->toArray()])[0] : [];
        }

        $data['id']                      = $requirement_model->id;
        $data['no']                      = $requirement_model->no;
        $data['store_id']                = $requirement_model->store_id;
        $data['store_name']              = $requirement_model->store_name;
        $data['original_warehouse_id']   = $requirement_model->original_warehouse_id;
        $data['original_warehouse_name'] = $warehouse_model->warehouse_name ?? '';
        $data['warehouse_address']       = $main_store_info['full_detail_address'] ?? '';

        return $data;
    }

    /**
     * 处理需求管理-待续约-谈判失败
     * @param array $params 参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function negotiationsFailed(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = static::$t->_('success');
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $requirement_model = $this->getRequirementInfo($params['id']);

            //只有待续约的需求才能做此操作
            if ($requirement_model->status != WarehouseEnums::REQUIREMENT_STATUS_PENDING_RENEWED) {
                throw new ValidationException(static::$t->_('warehouse_requirement_status_error', ['status' => static::$t->_(WarehouseEnums::getRequirementStatus()[WarehouseEnums::REQUIREMENT_STATUS_PENDING_RENEWED])]), ErrCode::$VALIDATE_ERROR);
            }

            // 获取关联线索
            $relate_thread_model = WarehouseThreadRepository::getInstance()->getOneById($requirement_model->thread_id);
            if (!empty($relate_thread_model)) {
                $thread_update_data = [
                    'status'        => WarehouseEnums::THREAD_STATUS_CANCEL,
                    'cancel_at'     => date('Y-m-d H:i:s'),
                    'cancel_reason' => static::$t->_('warehouse_thread_negotiations_failed_reason'),
                    'updated_id'    => $user['id'],
                    'updated_name'  => $user['name'],
                    'updated_at'    => date('Y-m-d H:i:s'),
                ];
                if ($relate_thread_model->i_update($thread_update_data) === false) {
                    throw new BusinessException('线索作废状态更新失败，data=' . json_encode($thread_update_data, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($relate_thread_model), ErrCode::$BUSINESS_ERROR);
                }
            }

            // 将仓库需求状态更新为待寻找
            $requirement_update_data = [
                'status'         => WarehouseEnums::REQUIREMENT_STATUS_SEARCH,
                'warehouse_type' => static::getWarehouseTypeChangeAddrEnums(),
                'thread_id'      => 0,
                'thread_no'      => '',
                'updated_id'     => $user['id'],
                'updated_name'   => $user['name'],
                'updated_at'     => date('Y-m-d H:i:s'),
            ];
            if ($requirement_model->i_update($requirement_update_data) === false) {
                throw new BusinessException('仓库需求改为待寻找/更换地址失败: ' . json_encode($requirement_update_data, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($requirement_model), ErrCode::$BUSINESS_ERROR);
            }

            // 记录需求确认记录
            $requirement_confirm_record_model = new WarehouseRequirementConfirmRecordModel();
            $record_data                      = [
                'requirement_id' => $requirement_model->id,
                'requirement_no' => $requirement_model->no,
                'thread_id'      => $relate_thread_model->id ?? 0,
                'thread_no'      => $relate_thread_model->no ?? '',
                'apply_date'     => date('Y-m-d'),
                'created_id'     => $user['id'],
                'created_name'   => $user['name'],
                'remark'         => '',
                'confirm_id'     => $user['id'],
                'confirm_name'   => $user['name'],
                'confirm_at'     => date('Y-m-d H:i:s'),
                'confirm_status' => WarehouseEnums::REQUIREMENT_CONFIRM_STATUS_NEGOTIATIONS_FAILED,
                'confirm_reason' => $params['remark'],
                'created_at'     => date('Y-m-d H:i:s'),
                'updated_at'     => date('Y-m-d H:i:s'),
            ];
            if ($requirement_confirm_record_model->i_create($record_data) === false) {
                throw new BusinessException('仓库需求确认记录添加失败: ' . json_encode($record_data, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($requirement_confirm_record_model), ErrCode::$BUSINESS_ERROR);
            }

            $db->commit();
        } catch (ValidationException $e) {
            $db->rollback();
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $db->rollback();
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $this->logger->warning('处理仓库需求-待续约-谈判失败提交异常: ' . $e->getMessage() . $e->getTraceAsString());
        } catch (Exception $e) {
            $db->rollback();
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $this->logger->error('处理仓库需求-待续约-谈判失败提交异常: ' . $e->getMessage() . $e->getTraceAsString());
        }

        return [
            'code'    => $code,
            'message' => $message,
        ];
    }


}
