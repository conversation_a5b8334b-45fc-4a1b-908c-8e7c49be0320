<?php
/**
 * 仓库管理
 */

namespace App\Modules\Warehouse\Services;

use App\Library\Enums;
use App\Library\Enums\ByWorkflowEnums;
use App\Library\Enums\ContractEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Models\oa\ContractWarehouseModel;
use App\Models\oa\WarehouseChangeApplyModel;
use App\Models\oa\WarehouseChangeRecordModel;
use App\Models\oa\WarehouseStoreModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Contract\Models\ContractStoreRentingModel;
use App\Modules\Contract\Services\ContractStoreRentingService;
use App\Modules\Third\Services\ByWorkflowService;
use App\Repository\HrStaffRepository;
use App\Repository\oa\ContractWarehouseRepository;
use App\Repository\StoreRepository;
use App\Repository\oa\ByWorkflowBusinessRelRepository;
use App\Util\RedisKey;
use Exception;
use GuzzleHttp\Exception\GuzzleException;

class WarehouseService extends BaseService
{
    // 信息详情查看类型[不同类型返回的数据组合不同]
    const DETAIL_VIEW_TYPE_COMMON = 1;       // 查看[通用的]
    const DETAIL_VIEW_TYPE_CHANGE_STATUS = 2;// 编辑查看[变更状态]
    const DETAIL_VIEW_TYPE_EDIT_STORE = 3;   // 编辑查看[编辑网点]
    const DETAIL_VIEW_TYPE_EDIT_INFO = 4;    // 编辑查看[编辑基础信息]

    // 仓库信息同步导出的最大限制, 超出则走异步
    const SYNC_EXPORT_MAX_COUNT = 10000;

    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 变更状态提交校验
     *
     * @param array $params
     * @throws ValidationException
     */
    public static function changeStatusSaveValidation(array $params)
    {
        $validation['id']               = 'Required|IntGt:0|>>>:params error[id]';
        $validation['warehouse_status'] = 'Required|IntIn:' . ContractEnums::$warehouse_status_chars . '|>>>:params error[warehouse_status]';

        if ($params['warehouse_status'] == ContractEnums::WAREHOUSE_STATUS_VACANT) {
            $validation['vacant_start_date'] = 'Required|Date|>>>:' . static::$t->_('warehouse_save_error_003');
            $validation['vacant_reason']     = 'Required|StrLenGeLe:1,1000|>>>:' . static::$t->_('warehouse_save_error_004');
        } else {
            if ($params['warehouse_status'] == ContractEnums::WAREHOUSE_STATUS_DEACTIVATED) {
                $validation['deactivate_date']   = 'Required|Date|>>>:' . static::$t->_('warehouse_save_error_001');
                $validation['deactivate_reason'] = 'Required|StrLenGeLe:1,1000|>>>:' . static::$t->_('warehouse_save_error_002');
            } else {
                if ($params['warehouse_status'] == ContractEnums::WAREHOUSE_STATUS_USING) {
                    $validation['store_category'] = 'Required|Int|>>>:' . static::$t->_('warehouse_save_error_005');
                    $validation['store_name']     = 'Required|StrLenGeLe:1,50|>>>:' . static::$t->_('warehouse_save_error_006');
                    $validation['store_id']       = 'Required|StrLenGeLe:1,10|>>>:' . static::$t->_('warehouse_save_error_007');
                    $validation['begin_date']     = 'Required|Date|>>>:' . static::$t->_('warehouse_save_error_008');
                }
            }
        }

        Validation::validate($params, $validation);
    }

    /**
     * 变更网点提交校验
     *
     * @param array $params
     * @return array
     * @throws ValidationException
     */
    public static function changeStoreSaveValidation(array $params)
    {
        $validation = [
            'id'                => 'Required|IntGt:0|>>>:params error[id]',
            'is_confirm_submit' => 'Required|IntIn:0,1|>>>:params error[is_confirm_submit]',
            'main_store_info'   => 'Required|obj|>>>:params error[main_store_info]',
            'share_store_list'  => 'Required|Arr|>>>:params error[share_store_list]',
        ];

        // 主网点各项值为空, 不处理
        $main_store_info           = array_filter(array_values($params['main_store_info']));
        $params['main_store_info'] = !empty($main_store_info) ? $params['main_store_info'] : [];
        if (!empty($params['main_store_info'])) {
            $validation['main_store_info.store_category'] = 'Required|Int|>>>:' . static::$t->_('warehouse_save_error_005');
            $validation['main_store_info.store_id']       = 'Required|StrLenGeLe:1,10|>>>:' . static::$t->_('warehouse_save_error_007');
            $validation['main_store_info.store_name']     = 'Required|StrLenGeLe:1,50|>>>:' . static::$t->_('warehouse_save_error_006');
            $validation['main_store_info.begin_date']     = 'Required|Date|>>>:' . static::$t->_('warehouse_save_error_008');
        }

        if (!empty($params['share_store_list'])) {
            $validation['share_store_list[*].store_category'] = 'Required|Int|>>>:' . static::$t->_('warehouse_save_error_012');
            $validation['share_store_list[*].store_id']       = 'Required|StrLenGeLe:1,10|>>>:' . static::$t->_('warehouse_save_error_014');
            $validation['share_store_list[*].store_name']     = 'Required|StrLenGeLe:1,50|>>>:' . static::$t->_('warehouse_save_error_013');
            $validation['share_store_list[*].begin_date']     = 'Required|Date|>>>:' . static::$t->_('warehouse_save_error_015');
        }

        Validation::validate($params, $validation);

        // 共用网点的结束使用日期格式校验
        $using_share_store_count = 0;
        foreach ($params['share_store_list'] as $store) {
            if (!empty($store['end_date'])) {
                if (!is_date_format($store['end_date'])) {
                    throw new ValidationException(static::$t->_('warehouse_save_error_017'), ErrCode::$VALIDATE_ERROR);
                }

                // 结束使用日期 不可 早于 开始使用日期
                if ($store['end_date'] < $store['begin_date']) {
                    throw new ValidationException(static::$t->_('warehouse_save_error_018'), ErrCode::$VALIDATE_ERROR);
                }
            } else {
                $using_share_store_count++;
            }
        }

        $allow_using_count = 10;
        if ($using_share_store_count > $allow_using_count) {
            throw new ValidationException(static::$t->_('warehouse_save_error_019',
                ['allow_total' => $allow_using_count]), ErrCode::$VALIDATE_ERROR);
        }

        // 共用网点不可重复
        $share_store_count = count(array_unique(array_column($params['share_store_list'], 'store_id')));
        if ($share_store_count < count($params['share_store_list'])) {
            throw new ValidationException(static::$t->_('warehouse_save_error_021'), ErrCode::$VALIDATE_ERROR);
        }

        return $params;
    }

    /**
     * 提交参数的公共校验
     *
     * @param array $params
     * @param bool $is_update
     * @throws BusinessException
     * @throws ValidationException
     */
    public static function saveCommonParamsValidation(array $params, bool $is_update = false)
    {
        // 各国仓库地理位置配置
        $warehouse_geo_config = EnumsService::getInstance()->getSettingEnvValueMap('warehouse_geo_config');
        if (empty($warehouse_geo_config)) {
            throw new BusinessException('该国仓库经纬度未配置, 请检查', ErrCode::$BUSINESS_ERROR);
        }

        $base_rule = $warehouse_geo_config['base_rule'];
        $geo_scope = $warehouse_geo_config['geo_scope'];

        $country_code       = get_country_code();
        $lower_country_code = strtolower($country_code);

        // 必填和基本规则校验(若后续其他国家迁移该模块, 注意校验规则的差异性)
        $warehouse_latitude_error  = $lower_country_code . '_warehouse_latitude_error';
        $warehouse_longitude_error = $lower_country_code . '_warehouse_longitude_error';

        // 公共校验项
        $validation = [
            'warehouse_name'      => 'Required|StrLenGeLe:1,100|>>>:' . static::$t->_('warehouse_add_name_error'),
            'province_code'       => 'Required|StrLenGeLe:1,10|>>>:' . static::$t->_('warehouse_add_province_error'),
            'city_code'           => 'Required|StrLenGeLe:1,10|>>>:' . static::$t->_('warehouse_add_city_error'),
            'district_code'       => 'Required|StrLenGeLe:1,10|>>>:' . static::$t->_('warehouse_add_district_error'),
            'warehouse_longitude' => "Required|Regexp:{$base_rule['longitude']}|>>>:" . static::$t->_($warehouse_longitude_error),
            // 仓库经度
            'warehouse_latitude'  => "Required|Regexp:{$base_rule['latitude']}|>>>:" . static::$t->_($warehouse_latitude_error),
            // 仓库纬度
            'real_area'           => 'Required|Regexp:/^(([1-9]{1}[0-9]*)|([0-9]+\.[0-9]{1,2}))$/|>>>:' . static::$t->_('warehouse_add_real_area_error'),
        ];

        // 菲律宾
        if ($country_code == GlobalEnums::PH_COUNTRY_CODE) {
            $validation['step'] = 'Required|IntIn:1,2|>>>:param error[step]'; // 操作步骤
        }

        // 更新校验, ID 是否一致
        if ($is_update) {
            $validation['id'] = 'Required|IntGt:0|>>>:' . static::$t->_('params_error', ['param' => 'id']);
        }

        Validation::validate($params, $validation);

        // 仓库经纬度范围区间判断
        // 经度
        if ($params['warehouse_longitude'] < $geo_scope['longitude']['min'] || $params['warehouse_longitude'] > $geo_scope['longitude']['max']) {
            throw new ValidationException(static::$t->_($warehouse_longitude_error), ErrCode::$VALIDATE_ERROR);
        }

        // 纬度
        if ($params['warehouse_latitude'] < $geo_scope['latitude']['min'] || $params['warehouse_latitude'] > $geo_scope['latitude']['max']) {
            throw new ValidationException(static::$t->_($warehouse_latitude_error), ErrCode::$VALIDATE_ERROR);
        }

        if (isset($params['real_area']) && $params['real_area'] <= 0) {
            throw new ValidationException(static::$t->_('warehouse_add_real_area_error'), ErrCode::$VALIDATE_ERROR);
        }
    }

    /**
     * 获取该模块静态枚举配置
     *
     * @return array
     */
    public function getStaticEnums()
    {
        $return['warehouse_status'] = [];

        // 仓库状态枚举
        foreach (ContractEnums::$warehouse_status_enums as $key => $value) {
            $return['warehouse_status'][] = [
                'value' => (string)$key,
                'label' => static::$t->_($value),
            ];
        }

        return $return;
    }

    /**
     * 获取相似地点的仓库列表
     *
     * @param string $warehouse_latitude
     * @param string $warehouse_longitude
     * @param string $warehouse_id
     * @return mixed
     */
    protected function getSameLocationWarehouseList(
        string $warehouse_latitude,
        string $warehouse_longitude,
        string $warehouse_id = ''
    ) {
        // 查找地址相近仓库: 整数位 + 小数位前5位
        $warehouse_latitude_item  = explode('.', $warehouse_latitude);
        $warehouse_longitude_item = explode('.', $warehouse_longitude);

        $same_latitude  = $warehouse_latitude_item[0] . '.' . mb_substr($warehouse_latitude_item[1], 0, 5);
        $same_longitude = $warehouse_longitude_item[0] . '.' . mb_substr($warehouse_longitude_item[1], 0, 5);
        return ContractWarehouseRepository::getInstance()->getListBySameLocation($same_latitude, $same_longitude,
            $warehouse_id);
    }

    /**
     * 仓库名称是否重复
     *
     * @param string $warehouse_name
     * @param int $id
     * @return array
     */
    public function checkName(string $warehouse_name, $id = 0)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $data    = ['is_exist' => false];

        try {
            // 仓库名称是否重复
            $exist_warehouse = ContractWarehouseRepository::getInstance()->getOneByName($warehouse_name);
            if ((empty($id) && !empty($exist_warehouse)) || (!empty($id) && !empty($same_name_warehouse) && $exist_warehouse->id != $id)) {
                $data['is_exist'] = true;
            }
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('仓库管理-仓库信息-名称检查:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 仓库管理-添加[通用版]
     *
     * @param array $params
     * @param array $user
     * @return array
     */
    public function commonAdd(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $data    = [
            'save_result'                  => false,
            'curr_warehouse_info'          => [],
            'same_location_warehouse_list' => [],
        ];

        try {
            $is_can_add = false;

            // 仓库名称是否重复
            $exist_warehouse = ContractWarehouseRepository::getInstance()->getOneByName($params['warehouse_name']);
            if (!empty($exist_warehouse)) {
                throw new ValidationException(static::$t->_('warehouse_add_name_exist_error'),
                    ErrCode::$VALIDATE_ERROR);
            }

            // PH 特有逻辑处理
            $same_location_warehouse_list = [];
            if (GlobalEnums::PH_COUNTRY_CODE == get_country_code()) {
                // 仓库ID生成
                $warehouse_id = $this->makeWarehouseId($params['warehouse_latitude']);

                // 查看仓库ID是否存在
                $exist_warehouse_model = ContractWarehouseRepository::getInstance()->getOneByWarehouseId($warehouse_id);
                if (!empty($exist_warehouse_model)) {
                    throw new ValidationException(static::$t->_('warehouse_id_exist_hint',
                        ['warehouse_id' => $exist_warehouse_model->warehouse_id]), ErrCode::$VALIDATE_ERROR);
                }

                // 查找地址相近仓库: 整数位 + 小数位前5位
                $same_location_warehouse_list = $this->getSameLocationWarehouseList($params['warehouse_latitude'],
                    $params['warehouse_longitude']);

                // 添加的第二步(确认提交) 或 没有相近位置的仓库, 直接创建
                if ($params['step'] == 2 || empty($same_location_warehouse_list)) {
                    $is_can_add = true;
                }
            } else {
                $is_can_add = true;

                // 仓库ID生成
                $warehouse_id = $this->commonMakeWarehouseId();
            }

            // 是否要入库
            if ($is_can_add) {
                $warehouse_model = new ContractWarehouseModel();
                $warehouse_info  = [
                    'warehouse_id'        => $warehouse_id,
                    'warehouse_name'      => $params['warehouse_name'],
                    'real_area'           => $params['real_area'],
                    'province_code'       => $params['province_code'],
                    'city_code'           => $params['city_code'],
                    'district_code'       => $params['district_code'],
                    'warehouse_latitude'  => $params['warehouse_latitude'],
                    'warehouse_longitude' => $params['warehouse_longitude'],
                    'warehouse_status'    => ContractEnums::WAREHOUSE_STATUS_USING,
                    'created_id'          => $user['id'],
                    'created_at'          => date('Y-m-d H:i:s'),
                    'updated_id'          => $user['id'],
                    'updated_at'          => date('Y-m-d H:i:s'),
                ];
                if ($warehouse_model->i_create($warehouse_info) === false) {
                    throw new BusinessException('仓库创建失败, 原因可能是=' . get_data_object_error_msg($warehouse_model) . '; 数据=' . json_encode($warehouse_info,
                            JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                }

                $data['save_result'] = true;
            }

            $data['curr_warehouse_info'] = [
                'id'                  => $is_can_add ? $warehouse_model->id : 0,
                'warehouse_id'        => $warehouse_id,
                'warehouse_latitude'  => $params['warehouse_latitude'],
                'warehouse_longitude' => $params['warehouse_longitude'],
            ];

            $data['same_location_warehouse_list'] = $same_location_warehouse_list;
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code    = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning('仓库管理-仓库信息-添加失败:' . $e->getMessage());
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('仓库管理-仓库信息-添加失败:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 仓库管理-添加[通用版]
     *
     * @param array $params
     * @param array $user
     * @return array
     */
    public function editSave(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $data    = [
            'save_result'                  => false,
            'curr_warehouse_info'          => [],
            'same_location_warehouse_list' => [],
        ];

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $is_can_save = false;

            // 当前仓库模型
            $warehouse_model = ContractWarehouseRepository::getInstance()->getOneById($params['id']);
            if (empty($warehouse_model)) {
                throw new ValidationException(static::$t->_('data_not_exist_by_id', ['id' => $params['id']]),
                    ErrCode::$VALIDATE_ERROR);
            }

            // 仓库名称是否重复
            $same_name_warehouse = ContractWarehouseRepository::getInstance()->getOneByName($params['warehouse_name']);
            if (!empty($same_name_warehouse) && $same_name_warehouse->id != $warehouse_model->id) {
                throw new ValidationException(static::$t->_('warehouse_info_save_error_001'), ErrCode::$VALIDATE_ERROR);
            }

            // 当前用户可编辑的字段范围
            $user_edit_permission_info = $this->getUserCanEditWarehouseFields($warehouse_model, $user);
            if (empty($user_edit_permission_info['can_edit_fields'])) {
                throw new ValidationException(static::$t->_('warehouse_info_save_error_002'), ErrCode::$VALIDATE_ERROR);
            }

            // 获取仓库的新省市区
            $params = $this->getAreaInfo([$params])[0] ?? [];

            // 补全仓库信息的原省市区
            $warehouse_info = $this->getAreaInfo([$warehouse_model->toArray()])[0] ?? [];

            // 验证变更字段
            $change_before_data    = [];
            $change_after_data     = [];
            $update_warehouse_data = [];
            foreach ($user_edit_permission_info['can_edit_fields'] as $field) {
                if (strtolower($params[$field]) === strtolower($warehouse_info[$field])) {
                    continue;
                }

                // 数值型对比, 扩大13倍后比对: 面积, 纬度, 经度
                if (in_array($field, ['real_area', 'warehouse_latitude', 'warehouse_longitude'])) {
                    $before_field_value = bcmul($warehouse_info[$field], 10000000000000);
                    $after_field_value  = bcmul($params[$field], 10000000000000);
                    if (bccomp($before_field_value, $after_field_value) == 0) {
                        continue;
                    }
                }

                // 有变更的
                $change_before_data[$field]    = $warehouse_info[$field];
                $change_after_data[$field]     = $params[$field];
                $update_warehouse_data[$field] = $params[$field];

                // 省市区对比, code 不同时, 拼接名称
                if ($field == 'province_code') {
                    $change_after_data[$field]  = $params['province_name'] . "({$params[$field]})";
                    $change_before_data[$field] = $warehouse_info['province_name'] . "({$warehouse_info[$field]})";
                }

                if ($field == 'city_code') {
                    $change_after_data[$field]  = $params['city_name'] . "({$params[$field]})";
                    $change_before_data[$field] = $warehouse_info['city_name'] . "({$warehouse_info[$field]})";
                }

                if ($field == 'district_code') {
                    $change_after_data[$field]  = $params['district_name'] . "({$params[$field]})";
                    $change_before_data[$field] = $warehouse_info['district_name'] . "({$warehouse_info[$field]})";
                }
            }

            $this->logger->info('仓库信息编辑, 仓库信息, before_data=' . json_encode($warehouse_model->toArray(),
                    JSON_UNESCAPED_UNICODE));
            $this->logger->info('仓库信息编辑, 仓库信息, 待更新数据' . json_encode($update_warehouse_data, JSON_UNESCAPED_UNICODE));

            // PH 特有逻辑处理
            $same_location_warehouse_list = [];
            if (get_country_code() == GlobalEnums::PH_COUNTRY_CODE) {
                $same_location_warehouse_list = $this->getSameLocationWarehouseList($params['warehouse_latitude'],
                    $params['warehouse_longitude'], $warehouse_model->warehouse_id);

                // 添加的第二步(确认提交) 或 没有相近位置的仓库, 直接创建
                if ($params['step'] == 2 || empty($same_location_warehouse_list)) {
                    $is_can_save = true;
                }
            } else {
                $is_can_save = true;
            }

            // 有真实的变更记录
            if (!empty($update_warehouse_data) && $is_can_save) {
                // 生成变更记录
                $before_data        = !empty($change_before_data) ? json_encode(['warehouse_base_info' => [$change_before_data]],
                    JSON_UNESCAPED_UNICODE) : '';
                $after_data         = !empty($change_after_data) ? json_encode(['warehouse_base_info' => [$change_after_data]],
                    JSON_UNESCAPED_UNICODE) : '';
                $change_record_data = [
                    'warehouse_main_id' => $warehouse_model->id,
                    'warehouse_id'      => $warehouse_model->warehouse_id,
                    'before_data'       => $before_data,
                    'after_data'        => $after_data,
                    'data_type'         => ContractEnums::WAREHOUSE_EDIT_INFO_DATA_TYPE,
                    'data_source'       => ContractEnums::WAREHOUSE_DATA_CHANGE_SOURCE_EDIT_INFO,
                    'is_effective'      => ContractEnums::WAREHOUSE_DATA_CHANGE_EFFECTIVED,
                    'created_id'        => $user['id'],
                    'created_name'      => $user['name'],
                    'created_at'        => date('Y-m-d H:i:s'),
                    'updated_id'        => $user['id'],
                    'updated_at'        => date('Y-m-d H:i:s'),
                ];

                $this->logger->info('仓库信息编辑: warehouse_change_record=' . json_encode($change_record_data,
                        JSON_UNESCAPED_UNICODE));
                $change_record_model = new WarehouseChangeRecordModel();
                if ($change_record_model->i_create($change_record_data) === false) {
                    throw new BusinessException('warehouse_change_record 写入失败,' . get_data_object_error_msg($change_record_model),
                        ErrCode::$BUSINESS_ERROR);
                }

                // 更新仓库信息
                $update_warehouse_data['updated_id'] = $user['id'];
                $update_warehouse_data['updated_at'] = date('Y-m-d H:i:s');
                if ($warehouse_model->i_update($update_warehouse_data) === false) {
                    throw new BusinessException('contract_warehouse 更新失败,' . get_data_object_error_msg($warehouse_model),
                        ErrCode::$BUSINESS_ERROR);
                }

                // 如果是特殊管理员, 则更新关联合同的仓库信息(不含仓库面积)
                $is_only_real_area = isset($update_warehouse_data['real_area']) && count($update_warehouse_data) == 1;
                if (!$is_only_real_area && $user_edit_permission_info['permission_level'] == ContractEnums::WAREHOUSE_INFO_CAN_EDIT_PERMISSION_LEVEL_1) {
                    // 获取已关联的租房合同
                    $rental_models = $warehouse_model->getRentalContractList();

                    $new_warehouse_info             = $this->getAreaInfo([$warehouse_model->toArray()])[0] ?? [];
                    $update_contract_warehouse_data = [
                        'warehouse_name'          => $new_warehouse_info['warehouse_name'],
                        'warehouse_latitude'      => $new_warehouse_info['warehouse_latitude'],
                        'warehouse_longitude'     => $new_warehouse_info['warehouse_longitude'],
                        'warehouse_province_name' => $new_warehouse_info['province_name'],
                        'warehouse_city_name'     => $new_warehouse_info['city_name'],
                        'warehouse_district_name' => $new_warehouse_info['district_name'],
                    ];

                    $this->logger->info('仓库信息编辑, 关联合同待更新的仓库信息=' . json_encode($update_contract_warehouse_data,
                            JSON_UNESCAPED_UNICODE));

                    foreach ($rental_models as $rental_model) {
                        $this->logger->info('仓库信息编辑, 关联的合同信息, 更新前=' . json_encode($rental_model->toArray(),
                                JSON_UNESCAPED_UNICODE));
                        if ($rental_model->i_update($update_contract_warehouse_data) === false) {
                            throw new BusinessException("关联合同的仓库信息更新失败," . get_data_object_error_msg($rental_model),
                                ErrCode::$BUSINESS_ERROR);
                        }
                        $this->logger->info('仓库信息编辑, 关联的合同信息, 更新后=' . json_encode($rental_model->toArray(),
                                JSON_UNESCAPED_UNICODE));
                    }
                }
            }

            $this->logger->info('仓库信息编辑, 仓库信息, after_data=' . json_encode($warehouse_model->toArray(),
                    JSON_UNESCAPED_UNICODE));

            $db->commit();

            $data['save_result']                  = $is_can_save;
            $data['curr_warehouse_info']          = [
                'warehouse_id'        => $warehouse_model->warehouse_id,
                'warehouse_latitude'  => $params['warehouse_latitude'],
                'warehouse_longitude' => $params['warehouse_longitude'],
            ];
            $data['same_location_warehouse_list'] = $same_location_warehouse_list;
        } catch (ValidationException $e) {
            $db->rollback();

            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $db->rollback();

            $code    = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning('仓库管理-仓库信息-编辑保存失败:' . $e->getMessage());
        } catch (Exception $e) {
            $db->rollback();

            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('仓库管理-仓库信息-编辑保存失败:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 获取空仓数量
     */
    public function getVacantWarehouseTotal()
    {
        return ContractWarehouseRepository::getInstance()->vacantWarehouseCount(ContractEnums::WAREHOUSE_STATUS_VACANT);
    }

    /**
     * 生成仓库ID[通用版]
     *
     * @return string
     * @throws BusinessException
     * @throws ValidationException
     */
    protected function commonMakeWarehouseId()
    {
        static $make_count = 0;
        static $warehouse_ids = [];

        // 年月日+3位递增数字
        $warehouse_id = static::genSerialNo('', RedisKey::WAREHOUSE_ID_COUNTER, 3, date('Ymd'));

        $exist_model = ContractWarehouseRepository::getInstance()->getOneByWarehouseId($warehouse_id);
        if (!empty($exist_model)) {
            $make_count++;
            $warehouse_ids[] = $warehouse_id;
            if ($make_count >= 3) {
                throw new BusinessException("仓库ID生成已重试 {$make_count} 次, 请检查, 尝试生成的: " . json_encode($warehouse_ids),
                    ErrCode::$BUSINESS_ERROR);
            }

            return $this->commonMakeWarehouseId();
        }

        // 正式的仓库ID
        return $warehouse_id;
    }

    /**
     * 生成仓库ID[ph在用]
     *
     * @param string $warehouse_latitude
     * @return string
     * @throws ValidationException
     */
    protected function makeWarehouseId(string $warehouse_latitude)
    {
        // 构造仓库ID
        $warehouse_latitude_item = explode('.', $warehouse_latitude);

        // 纬度部分: 取纬度前8位, 不含小数点
        $latitude_part = $warehouse_latitude_item[0] . mb_substr($warehouse_latitude_item[1], 0, 6);

        // 整数位1位, 补前导0
        if (mb_strlen($warehouse_latitude_item[0]) == 1) {
            $latitude_part = '0' . $latitude_part;
        }

        // 日期 + 纬度8位 + 序号 (0~9)
        $warehouse_id = date('ymd') . $latitude_part;

        $latest_same_warehouse_model = ContractWarehouseRepository::getInstance()->getLatestByWarehouseId($warehouse_id);
        if (empty($latest_same_warehouse_model)) {
            // 该仓库ID未被占用, 序号初始为0
            $curr_day_serial_number = '0';
        } else {
            // 该仓库ID已被占用, 序号在已有的基础上 + 1
            $curr_day_serial_number = mb_substr($latest_same_warehouse_model->warehouse_id, -1, 1) + 1;
        }

        // 当前序号超出了个位数, 给出拦截提示
        if ($curr_day_serial_number > 9) {
            throw new ValidationException(static::$t->_('warehouse_id_serial_number_error'), ErrCode::$VALIDATE_ERROR);
        }

        // 正式的仓库ID
        return $warehouse_id . $curr_day_serial_number;
    }

    /**
     * 仓库管理-列表
     * @param array $params
     * @return array
     */
    public function list(array $params)
    {
        $page_size = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : (int)$params['pageSize'];
        $page_num  = empty($params['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : (int)$params['pageNum'];

        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $data    = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];

        try {
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['main' => ContractWarehouseModel::class]);

            $builder = $this->getInfoListConditions($builder, $params);

            $count = (int)$builder->columns('COUNT(DISTINCT(main.id)) AS total')->getQuery()->getSingleResult()->total;
            $items = [];
            if ($count) {
                $offset = $page_size * ($page_num - 1);
                $builder->columns([
                    'main.id',
                    'main.warehouse_id',
                    'main.warehouse_name',
                    'main.real_area',
                    'main.province_code',
                    'main.city_code',
                    'main.district_code',
                    'main.warehouse_longitude',
                    'main.warehouse_latitude',
                    'main.warehouse_status',
                    'main.deactivate_date',
                    'main.deactivate_reason',
                    'main.deactivate_staff_id',
                    'main.deactivate_at',
                    'main.created_id',
                    'main.created_at',
                    'main.updated_at',
                ]);
                $builder->orderby('main.warehouse_status ASC, main.updated_at DESC');
                $builder->limit($page_size, $offset);
                $builder->groupBy('main.id');
                $items = $builder->getQuery()->execute()->toArray();

                $warehouse_status_item = ContractEnums::$warehouse_status_enums;
                foreach ($items as &$item) {
                    // 仓库状态
                    $item['warehouse_status_label'] = static::$t->_($warehouse_status_item[$item['warehouse_status']]);

                    // 其他字段null值重置
                    $item['deactivate_date']     = $item['deactivate_date'] ?? '';
                    $item['deactivate_staff_id'] = $item['deactivate_staff_id'] ?? '';
                    $item['deactivate_at']       = $item['deactivate_at'] ?? '';
                    $item['sign_contract']       = $this->canShowSignOrPrice($item);//去签约
                    $item['renew_price']         = $this->canShowSignOrPrice($item, 'renew_price');//续签报价
                }

                // 省/市/区 名称
                $items = $this->getAreaInfo($items);
            }

            $data['pagination']['total_count'] = $count;
            $data['items']                     = $items;
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('仓库管理-仓库信息-列表获取失败:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 去签约 、 续签报价按钮是否可显示
     * @param array $item 仓库信息
     * @param string $type [sign_contract => '去签约', 'renew_price' => '续签报价']
     * @return bool
     */
    private function canShowSignOrPrice($item, $type = 'sign_contract')
    {
        $can_show = false;
        $country_code = get_country_code();
        if (!in_array($country_code, [GlobalEnums::PH_COUNTRY_CODE, GlobalEnums::TH_COUNTRY_CODE]) || $item['warehouse_status'] != ContractEnums::WAREHOUSE_STATUS_USING) {
            return $can_show;
        }

        if ($country_code == GlobalEnums::TH_COUNTRY_CODE && $type == 'renew_price') {
            return $can_show;
        }

        switch ($type) {
            case 'sign_contract':
                //去签约-当仓库状态为使用中并且关联的合同为空时显示该按钮
                $count = $this->getWarehouseContractCount($item['warehouse_id']);
                $can_show = $count ? false : true;
                break;
            case 'renew_price':
                //续签报价 - 当仓库状态为使用中并且有关联审批通过的合同时显示该按钮
                $count = $this->getWarehouseContractCount($item['warehouse_id'], Enums::WF_STATE_APPROVED);
                $can_show = $count ? true : false;
                break;
        }
        return $can_show;
    }

    /**
     * 仓库管理-信息列表-导出的数量计算
     *
     * @param array $params
     * @return int
     */
    public function infoExportCount(array $params)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['main' => ContractWarehouseModel::class]);
        $builder = $this->getInfoListConditions($builder, $params);
        return (int)$builder->columns('COUNT(DISTINCT(main.id)) AS total')->getQuery()->getSingleResult()->total;
    }

    /**
     * 获取仓库管理-列表-导出数据
     *
     * @param array $params
     * @return mixed
     */
    public function getInfoExportData(array $params)
    {
        $page_size = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : (int)$params['pageSize'];
        $page_num  = empty($params['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : (int)$params['pageNum'];

        $builder = $this->modelsManager->createBuilder();
        $builder->from(['main' => ContractWarehouseModel::class]);

        $builder = $this->getInfoListConditions($builder, $params);

        // 关联合同
        $builder->leftjoin(ContractStoreRentingModel::class, 'contract.warehouse_id = main.warehouse_id', 'contract');

        $offset = $page_size * ($page_num - 1);
        $builder->columns([
            'main.id',
            'main.warehouse_id',
            'main.warehouse_name',
            'main.real_area',
            'main.province_code',
            'main.city_code',
            'main.district_code',
            'main.warehouse_longitude',
            'main.warehouse_latitude',
            'main.warehouse_status',
            'GROUP_CONCAT(contract.contract_id) AS contract_nos',
        ]);
        $builder->orderby('main.warehouse_status ASC, main.updated_at DESC');
        $builder->limit($page_size, $offset);
        $builder->groupBy('main.id');
        $items = $builder->getQuery()->execute()->toArray();

        // 取已关联的使用中的主网点/共用网点
        $warehouse_store_list = [];
        $warehouse_main_ids   = array_column($items, 'id');
        if (!empty($warehouse_main_ids)) {
            $warehouse_store_list = WarehouseStoreModel::find([
                'conditions' => 'warehouse_main_id IN ({warehouse_main_ids:array}) AND use_status = :use_status:',
                'bind'       => [
                    'warehouse_main_ids' => $warehouse_main_ids,
                    'use_status'         => ContractEnums::WAREHOUSE_STORE_USE_STATUS_ING,
                ],
                'columns'    => [
                    "CONCAT_WS('_', warehouse_main_id, store_flag) AS warehouse_store_flag",
                    'GROUP_CONCAT(store_id) AS store_ids',
                ],
                'group'      => 'warehouse_id, store_flag',
            ])->toArray();

            $store_ids = array_filter(array_unique(explode(',',
                implode(',', array_column($warehouse_store_list, 'store_ids')))));

            // 获取仓库网点的名称
            $store_list = (new StoreRepository())->getStoreListByIds(array_values($store_ids), Enums::STORE_STATE_ALL);
            $store_list = array_column($store_list, 'name', 'id');

            // 总部网点名称追加
            $store_list[Enums::PAYMENT_HEADER_STORE_ID] = Enums::PAYMENT_HEADER_STORE_NAME;

            foreach ($warehouse_store_list as &$warehouse_store) {
                $_store_ids  = explode(',', $warehouse_store['store_ids']);
                $_store_item = [];
                foreach ($_store_ids as $_id) {
                    $_store_item[] = !empty($store_list[$_id]) ? $store_list[$_id] . "({$_id})" : "({$_id})";
                }
                $warehouse_store['store_ids'] = implode(',', $_store_item);
            }

            $warehouse_store_list = array_column($warehouse_store_list, 'store_ids', 'warehouse_store_flag');
        }

        $warehouse_status_item = ContractEnums::$warehouse_status_enums;
        foreach ($items as &$item) {
            // 仓库状态
            $item['warehouse_status_label'] = static::$t->_($warehouse_status_item[$item['warehouse_status']]);

            // 主网点 和 共用网点
            $item['main_store_item']   = $warehouse_store_list[$item['id'] . '_' . ContractEnums::WAREHOUSE_STORE_FLAG_MAIN] ?? '';
            $item['common_store_item'] = $warehouse_store_list[$item['id'] . '_' . ContractEnums::WAREHOUSE_STORE_FLAG_SHARE] ?? '';
        }

        // 省/市/区 名称
        return $this->getAreaInfo($items);
    }

    /**
     * 仓库管理-信息-同步导出
     *
     * @param $params
     * @return array
     * @throws BusinessException
     * @throws GuzzleException
     */
    public function syncExportInfoData($params)
    {
        return $this->generateInfoExportFile($this->getInfoExportData($params));
    }

    /**
     * 仓库管理-信息-同步导出-表头
     * @param array $data
     * @return array
     * @throws BusinessException
     * @throws GuzzleException
     */
    public function generateInfoExportFile(array $data)
    {
        $excel_header = [
            static::$t['warehouse_id'],           // 仓库ID
            static::$t['warehouse_name'],         // 仓库名称
            static::$t['real_area'],              // 仓库实际面积
            static::$t['province_code'],          // 仓库所在省
            static::$t['city_code'],              // 仓库所在市
            static::$t['district_code'],          // 仓库所在区
            static::$t['warehouse_longitude'],    // 仓库经度
            static::$t['warehouse_latitude'],     // 仓库纬度
            static::$t['warehouse_status'],       // 仓库状态
            static::$t['warehouse_contract'],     // 关联合同
            static::$t['warehouse_main_store'],   // 主网点
            static::$t['warehouse_common_store'], // 共用网点
        ];

        $excel_data = [];
        foreach ($data as $value) {
            $excel_data[] = [
                $value['warehouse_id'],
                $value['warehouse_name'],
                $value['real_area'],
                $value['province_name'],
                $value['city_name'],
                $value['district_name'],
                $value['warehouse_longitude'],
                $value['warehouse_latitude'],
                $value['warehouse_status_label'],
                $value['contract_nos'],
                $value['main_store_item'],
                $value['common_store_item'],
            ];
        }
        unset($data);

        $excel_file_name = 'Warehouse_Information_Export_' . date('YmdHis') . '.xlsx';
        return $this->exportExcel($excel_header, $excel_data, $excel_file_name);
    }

    /**
     * 仓库信息列表搜索条件组合
     *
     * @param object $builder
     * @param array $params
     * @return object
     */
    protected function getInfoListConditions(object $builder, array $params)
    {
        $country_code = get_country_code();

        if (!empty($params['warehouse_id'])) {
            $builder->andWhere('main.warehouse_id = :warehouse_id:', ['warehouse_id' => $params['warehouse_id']]);
        }

        if (mb_strlen($params['warehouse_name']) > 0) {
            $builder->andWhere('main.warehouse_name LIKE :warehouse_name:',
                ['warehouse_name' => "{$params['warehouse_name']}%"]);
        }

        if (mb_strlen($params['warehouse_longitude']) > 0) {
            $builder->andWhere('main.warehouse_longitude LIKE :warehouse_longitude:',
                ['warehouse_longitude' => "{$params['warehouse_longitude']}%"]);
        }

        if (mb_strlen($params['warehouse_latitude']) > 0) {
            $latitude_search_mode = $country_code == GlobalEnums::PH_COUNTRY_CODE ? "%{$params['warehouse_latitude']}%" : "{$params['warehouse_latitude']}%";
            $builder->andWhere('main.warehouse_latitude LIKE :warehouse_latitude:',
                ['warehouse_latitude' => $latitude_search_mode]);
        }

        if (!empty($params['province_code'])) {
            $builder->andWhere('main.province_code = :province_code:', ['province_code' => $params['province_code']]);
        }

        if (!empty($params['city_code'])) {
            $builder->andWhere('main.city_code = :city_code:', ['city_code' => $params['city_code']]);
        }

        if (!empty($params['district_code'])) {
            $builder->andWhere('main.district_code = :district_code:', ['district_code' => $params['district_code']]);
        }

        if (mb_strlen($params['warehouse_status']) > 0) {
            $builder->andWhere('main.warehouse_status = :warehouse_status:',
                ['warehouse_status' => $params['warehouse_status']]);
        }

        // 关联网点
        if (!empty($params['store_id'])) {
            $builder->leftjoin(WarehouseStoreModel::class, 'store.warehouse_main_id = main.id', 'store');
            $builder->andWhere('store.store_id = :store_id:', ['store_id' => $params['store_id']]);
            $builder->andWhere('store.use_status = :use_status:',
                ['use_status' => ContractEnums::WAREHOUSE_STORE_USE_STATUS_ING]);
        }

        return $builder;
    }

    /**
     * 仓库管理-详情获取
     * @param int $id
     * @param int $detail_view_type
     * @param array $user
     * @return array
     */
    public function getDetail(int $id, int $detail_view_type = 1, array $user = [])
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $data    = [];

        try {
            $warehouse_model = ContractWarehouseRepository::getInstance()->getOneById($id);
            if (empty($warehouse_model)) {
                throw new ValidationException(static::$t->_('warehouse_info_is_null_hint', ['warehouse_id' => $id]),
                    ErrCode::$VALIDATE_ERROR);
            }

            // 非 使用中 和 空置的, 不可 查看变更仓库状态的详情
            if ($detail_view_type == self::DETAIL_VIEW_TYPE_CHANGE_STATUS && !in_array($warehouse_model->warehouse_status,
                    [ContractEnums::WAREHOUSE_STATUS_USING, ContractEnums::WAREHOUSE_STATUS_VACANT])) {
                throw new ValidationException(static::$t->_('warehouse_info_view_error_002'), ErrCode::$VALIDATE_ERROR);
            }

            // 非 使用中 的, 不可 查看编辑网点信息的详情
            if ($detail_view_type == self::DETAIL_VIEW_TYPE_EDIT_STORE && $warehouse_model->warehouse_status != ContractEnums::WAREHOUSE_STATUS_USING) {
                throw new ValidationException(static::$t->_('warehouse_info_view_error_001'), ErrCode::$VALIDATE_ERROR);
            }

            // 非 使用中 和 空置 的, 不可 查看编辑基础信息的详情
            if ($detail_view_type == self::DETAIL_VIEW_TYPE_EDIT_INFO && !in_array($warehouse_model->warehouse_status,
                    [ContractEnums::WAREHOUSE_STATUS_USING, ContractEnums::WAREHOUSE_STATUS_VACANT])) {
                throw new ValidationException(static::$t->_('warehouse_info_view_error_003'), ErrCode::$VALIDATE_ERROR);
            }

            // 仓库省/市/区信息
            $warehouse_area_info = $this->getAreaInfo([$warehouse_model->toArray()])[0] ?? [];

            // 使用中的主网点信息
            $main_store_info = $warehouse_model->getMainStoreInfo();
            $main_store_info = !empty($main_store_info) ? $this->getWarehouseStoreDetail([$main_store_info->toArray()])[0] : [];

            // 编辑网点详情 和 通用的详情
            $related_contract_list = [];
            $share_store_list      = [];
            if (in_array($detail_view_type, [self::DETAIL_VIEW_TYPE_EDIT_STORE, self::DETAIL_VIEW_TYPE_COMMON])) {
                // 关联合同信息
                $related_contract_list = ContractStoreRentingService::getInstance()->getRelatedListByWarehouseId($warehouse_model->warehouse_id);

                // 使用中的共用网点信息
                $_share_store_list = $warehouse_model->getShareStoreList()->toArray();
                $_share_store_list = $this->getWarehouseStoreDetail($_share_store_list);
                foreach ($_share_store_list as $_store) {
                    $share_store_list[] = [
                        'store_category'       => $_store['store_category'] ?? '',
                        'store_category_code'  => $_store['store_category_code'] ?? '',
                        'store_id'             => $_store['store_id'] ?? '',
                        'store_name'           => $_store['store_name'] ?? '',
                        'province_region_name' => $_store['province_region_name'] ?? '',
                        'full_detail_address'  => $_store['full_detail_address'] ?? '',
                        'begin_date'           => $_store['begin_date'] ?? '',
                        'end_date'             => $_store['end_date'] ?? '',
                    ];
                }

                // 共用网点: 按开始使用时间倒序
                $share_store_list = array_sort($share_store_list, 'begin_date', SORT_DESC);
            }

            // 通用的详情
            $change_record_list = [];
            $audit_log_list     = [];
            if ($detail_view_type == self::DETAIL_VIEW_TYPE_COMMON) {
                // 仓库信息变更记录(已生效的变更记录)
                $change_record_list = $warehouse_model->getEffectivedChangeRecord()->toArray();
                $change_record_list = $this->formatChangeRecord($change_record_list);

                // 审批记录
                $audit_log_list = $warehouse_model->getChangeApplyList()->toArray();
                foreach ($audit_log_list as &$apply) {
                    // 审批业务类型
                    $apply['biz_type'] = static::$t->_(ByWorkflowEnums::$by_workflow_biz_type_item[$apply['biz_type']]);

                    // 审批状态
                    $apply['approval_status'] = static::$t->_(Enums::$payment_apply_status[$apply['approval_status']]);
                }
            }

            $base_data = [
                'id'                     => $warehouse_model->id,
                'warehouse_id'           => $warehouse_model->warehouse_id,
                'warehouse_name'         => $warehouse_model->warehouse_name,
                'real_area'              => $warehouse_model->real_area,
                'province_code'          => $warehouse_model->province_code,
                'province_name'          => $warehouse_area_info['province_name'],
                'city_code'              => $warehouse_model->city_code,
                'city_name'              => $warehouse_area_info['city_name'],
                'district_code'          => $warehouse_model->district_code,
                'district_name'          => $warehouse_area_info['district_name'],
                'warehouse_latitude'     => $warehouse_model->warehouse_latitude,
                'warehouse_longitude'    => $warehouse_model->warehouse_longitude,
                'warehouse_status'       => $warehouse_model->warehouse_status,
                'warehouse_status_label' => static::$t->_(ContractEnums::$warehouse_status_enums[$warehouse_model->warehouse_status]),
            ];

            // 如果是信息编辑来源, 则判断当前用户的编辑字段范围
            if ($detail_view_type == self::DETAIL_VIEW_TYPE_EDIT_INFO) {
                $extra_data = [
                    'can_edit_fields' => $this->getUserCanEditWarehouseFields($warehouse_model,
                        $user)['can_edit_fields'],
                ];
            } else {
                $extra_data = [
                    'deactivate_date'       => $warehouse_model->deactivate_date ?? '',
                    'deactivate_staff_id'   => $warehouse_model->deactivate_staff_id ?? '',
                    'deactivate_reason'     => $warehouse_model->deactivate_reason,
                    'vacant_start_date'     => $warehouse_model->vacant_start_date ?? '',
                    'vacant_reason'         => $warehouse_model->vacant_reason,

                    // 主网点信息
                    'main_store_info'       => [
                        'store_category'       => $main_store_info['store_category'] ?? '',
                        'store_category_code'  => $main_store_info['store_category_code'] ?? '',
                        'store_id'             => $main_store_info['store_id'] ?? '',
                        'store_name'           => $main_store_info['store_name'] ?? '',
                        'province_region_name' => $main_store_info['province_region_name'] ?? '',
                        'full_detail_address'  => $main_store_info['full_detail_address'] ?? '',
                        'begin_date'           => $main_store_info['begin_date'] ?? '',
                    ],

                    // 共用网点信息
                    'share_store_list'      => $share_store_list,

                    // 关联合同信息
                    'related_contract_list' => $related_contract_list,

                    // 仓库信息变更记录
                    'change_record_list'    => $change_record_list,

                    // 审批记录
                    'audit_log_list'        => $audit_log_list,
                ];
            }

            $data = array_merge($base_data, $extra_data);
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('仓库管理-仓库信息-详情获取失败:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 获取当前用户可编辑的仓库信息字段
     *
     * @param object $warehouse_model 仓库信息模型
     * @param array $user 当前用户
     * @return array
     */
    protected function getUserCanEditWarehouseFields(object $warehouse_model, array $user)
    {
        // 特殊工号
        $special_administrator_ids = EnumsService::getInstance()->getSettingEnvValueIds('warehouse_info_special_administrator_ids');

        if (in_array($user['id'], $special_administrator_ids)) {
            $permission_level = ContractEnums::WAREHOUSE_INFO_CAN_EDIT_PERMISSION_LEVEL_1;
        } else {
            // 有无关联的合同
            $rental_contract_model = $warehouse_model->getOneRentalContract();

            // 有无关联的网点
            $store_info_model = $warehouse_model->getOneStoreInfo();

            if (empty($rental_contract_model) && empty($store_info_model)) {
                $permission_level = ContractEnums::WAREHOUSE_INFO_CAN_EDIT_PERMISSION_LEVEL_2;
            } else {
                $permission_level = ContractEnums::WAREHOUSE_INFO_CAN_EDIT_PERMISSION_LEVEL_3;
            }
        }

        return [
            'permission_level' => $permission_level,
            'can_edit_fields'  => ContractEnums::$warehouse_info_can_edit_permission_level_fields_map[$permission_level] ?? [],
        ];
    }

    /**
     * 仓库管理-变更状态保存
     * @param array $params
     * @param array $user
     * @return array
     */
    public function changeStatusSave(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $warehouse_model = ContractWarehouseRepository::getInstance()->getOneById($params['id']);
            if (empty($warehouse_model)) {
                throw new ValidationException(static::$t->_('warehouse_info_is_null_hint',
                    ['warehouse_id' => $params['id']]), ErrCode::$VALIDATE_ERROR);
            }

            if (!in_array($warehouse_model->warehouse_status,
                [ContractEnums::WAREHOUSE_STATUS_VACANT, ContractEnums::WAREHOUSE_STATUS_USING])) {
                throw new ValidationException(static::$t->_('warehouse_save_error_009'), ErrCode::$VALIDATE_ERROR);
            }

            // 状态未变, 也不允许提交
            if ($params['warehouse_status'] == $warehouse_model->warehouse_status) {
                throw new ValidationException(static::$t->_('warehouse_save_error_011'), ErrCode::$VALIDATE_ERROR);
            }

            // 仓库信息变更记录
            $change_before_data = [];
            $change_after_data  = [];
            // 使用中 -> 空置 / 停用
            $exist_main_store_info = $warehouse_model->getMainStoreInfo();
            $exist_main_store_info = !empty($exist_main_store_info) ? $exist_main_store_info->toArray() : [];
            if ($warehouse_model->warehouse_status == ContractEnums::WAREHOUSE_STATUS_USING) {
                // 变更前仓库状态
                $change_before_data[ContractEnums::WAREHOUSE_BASE_INFO_KEY] = [
                    'warehouse_status' => $warehouse_model->warehouse_status,
                ];

                // 变更前主网点数据
                if (!empty($exist_main_store_info)) {
                    $exist_main_store_info['store_name']                              = $this->getStoreNameByStoreId($exist_main_store_info['store_id']);
                    $change_before_data[ContractEnums::WAREHOUSE_MAIN_STORE_INFO_KEY] = [
                        'store_category' => $exist_main_store_info['store_category'],
                        'store_name'     => $exist_main_store_info['store_name'],
                        'store_id'       => $exist_main_store_info['store_id'],
                        'begin_date'     => $exist_main_store_info['begin_date'],
                    ];

                    if (!empty($exist_main_store_info['end_date'])) {
                        $change_before_data[ContractEnums::WAREHOUSE_MAIN_STORE_INFO_KEY]['end_date'] = $exist_main_store_info['end_date'];
                    }
                }

                // 变更后状态相关属性
                if ($params['warehouse_status'] == ContractEnums::WAREHOUSE_STATUS_VACANT) {
                    // 空置
                    $change_after_data[ContractEnums::WAREHOUSE_BASE_INFO_KEY] = [
                        'warehouse_status'  => $params['warehouse_status'],
                        'vacant_start_date' => $params['vacant_start_date'],
                        'vacant_reason'     => $params['vacant_reason'],
                    ];
                } else {
                    // 停用
                    $change_after_data[ContractEnums::WAREHOUSE_BASE_INFO_KEY] = [
                        'warehouse_status'  => $params['warehouse_status'],
                        'deactivate_date'   => $params['deactivate_date'],
                        'deactivate_reason' => $params['deactivate_reason'],
                    ];
                }
            } else {
                // 空置 -> 使用中 / 停用
                // 变更前状态相关属性
                $change_before_data[ContractEnums::WAREHOUSE_BASE_INFO_KEY] = [
                    'warehouse_status'  => $warehouse_model->warehouse_status,
                    'vacant_start_date' => $warehouse_model->vacant_start_date,
                    'vacant_reason'     => $warehouse_model->vacant_reason,
                ];

                // 变更后状态相关属性
                if ($params['warehouse_status'] == ContractEnums::WAREHOUSE_STATUS_USING) {
                    // 使用中
                    $change_after_data[ContractEnums::WAREHOUSE_BASE_INFO_KEY] = [
                        'warehouse_status' => $params['warehouse_status'],
                    ];

                    $change_after_data[ContractEnums::WAREHOUSE_MAIN_STORE_INFO_KEY] = [
                        'store_category' => $params['store_category'],
                        'store_name'     => $params['store_name'],
                        'store_id'       => $params['store_id'],
                        'begin_date'     => $params['begin_date'],
                    ];

                    // 主网点快照: 取变更后的主网点
                    $exist_main_store_info['store_id']       = $params['store_id'];
                    $exist_main_store_info['store_category'] = $params['store_category'];
                    $exist_main_store_info['store_flag']     = ContractEnums::WAREHOUSE_STORE_FLAG_MAIN;
                    $exist_main_store_info['begin_date']     = $params['begin_date'];
                    $exist_main_store_info['use_status']     = ContractEnums::WAREHOUSE_STORE_USE_STATUS_ING;
                } else {
                    // 停用
                    $change_after_data[ContractEnums::WAREHOUSE_BASE_INFO_KEY] = [
                        'warehouse_status'  => $params['warehouse_status'],
                        'deactivate_date'   => $params['deactivate_date'],
                        'deactivate_reason' => $params['deactivate_reason'],
                    ];
                }
            }

            // 生成OA侧 状态变更单号
            $oa_audit_no = static::genSerialNo(ContractEnums::WAREHOUSE_STATUS_CHANGE_AUDIT_NO_PREFIX,
                RedisKey::WAREHOUSE_STATUS_CHANGE_AUDIT_NO_COUNTER, 4, date('Ymd'));

            // 构造变更申请数据
            // 构造快照数据
            $change_apply_data = [
                'oa_audit_no'         => $oa_audit_no,
                'biz_type'            => ByWorkflowEnums::BY_BIZ_TYPE_WAREHOUSE_CHANGE_STATUS,
                'warehouse_main_id'   => $warehouse_model->id,
                'warehouse_id'        => $warehouse_model->warehouse_id,
                'warehouse_name'      => $warehouse_model->warehouse_name,
                'real_area'           => $warehouse_model->real_area,
                'province_code'       => $warehouse_model->province_code,
                'province_name'       => '',
                'city_code'           => $warehouse_model->city_code,
                'city_name'           => '',
                'district_code'       => $warehouse_model->district_code,
                'district_name'       => '',
                'warehouse_latitude'  => $warehouse_model->warehouse_latitude,
                'warehouse_longitude' => $warehouse_model->warehouse_longitude,
                'warehouse_status'    => $params['warehouse_status'],
                'approval_status'     => Enums::WF_STATE_PENDING,
                'deactivate_date'     => null,
                'deactivate_reason'   => '',
                'deactivate_staff_id' => null,
                'deactivate_at'       => null,
                'vacant_start_date'   => null,
                'vacant_reason'       => '',
                'vacant_staff_id'     => null,
                'vacant_at'           => null,
                'created_id'          => $user['id'],
                'created_at'          => date('Y-m-d H:i:s'),
                'updated_id'          => $user['id'],
                'updated_at'          => date('Y-m-d H:i:s'),
            ];

            if ($params['warehouse_status'] == ContractEnums::WAREHOUSE_STATUS_VACANT) {
                $change_apply_data['vacant_start_date'] = $params['vacant_start_date'];
                $change_apply_data['vacant_reason']     = $params['vacant_reason'];
                $change_apply_data['vacant_staff_id']   = $user['id'];
                $change_apply_data['vacant_at']         = date('Y-m-d H:i:s');
            } else {
                if ($params['warehouse_status'] == ContractEnums::WAREHOUSE_STATUS_DEACTIVATED) {
                    $change_apply_data['deactivate_date']     = $params['deactivate_date'];
                    $change_apply_data['deactivate_reason']   = $params['deactivate_reason'];
                    $change_apply_data['deactivate_staff_id'] = $user['id'];
                    $change_apply_data['deactivate_at']       = date('Y-m-d H:i:s');
                }
            }

            // 仓库省/市/区名称
            $warehouse_area_info = $this->getAreaInfo([$warehouse_model->toArray()])[0] ?? [];
            if (!empty($warehouse_area_info)) {
                $change_apply_data['province_name'] = $warehouse_area_info['province_name'];
                $change_apply_data['city_name']     = $warehouse_area_info['city_name'];
                $change_apply_data['district_name'] = $warehouse_area_info['district_name'];
            }

            $this->logger->info('仓库状态变更: warehouse_change_apply=' . json_encode($change_apply_data,
                    JSON_UNESCAPED_UNICODE));
            $change_apply_model = new WarehouseChangeApplyModel();
            if ($change_apply_model->i_create($change_apply_data) === false) {
                throw new BusinessException('仓库状态变更-变更申请表写入失败, 原因可能是=' . get_data_object_error_msg($change_apply_model),
                    ErrCode::$BUSINESS_ERROR);
            }

            // 写入变更记录表
            $change_record_data = [
                'warehouse_main_id' => $warehouse_model->id,
                'warehouse_id'      => $warehouse_model->warehouse_id,
                'change_apply_id'   => $change_apply_model->id,
                'oa_audit_no'       => $oa_audit_no,
                'before_data'       => !empty($change_before_data) ? json_encode($change_before_data,
                    JSON_UNESCAPED_UNICODE) : '',
                'after_data'        => !empty($change_after_data) ? json_encode($change_after_data,
                    JSON_UNESCAPED_UNICODE) : '',
                'data_type'         => ContractEnums::WAREHOUSE_STATUS_CHANGE_DATA_TYPE,
                'data_source'       => ContractEnums::WAREHOUSE_DATA_CHANGE_SOURCE_APPLY,
                'is_effective'      => ContractEnums::WAREHOUSE_DATA_CHANGE_TO_BE_EFFECTIVE,
                'created_id'        => $user['id'],
                'created_name'      => $user['name'],
                'created_at'        => date('Y-m-d H:i:s'),
                'updated_id'        => $user['id'],
                'updated_at'        => date('Y-m-d H:i:s'),
            ];

            $this->logger->info('仓库状态变更: warehouse_change_record=' . json_encode($change_record_data,
                    JSON_UNESCAPED_UNICODE));
            $change_record_model = new WarehouseChangeRecordModel();
            if ($change_record_model->i_create($change_record_data) === false) {
                throw new BusinessException('仓库状态变更-变更记录表写入失败, 原因可能是=' . get_data_object_error_msg($change_record_model),
                    ErrCode::$BUSINESS_ERROR);
            }

            // 写入仓库网点快照表: 主网点 + 共用网点
            $share_store_list = $warehouse_model->getShareStoreList()->toArray();
            $all_store_list   = array_merge([$exist_main_store_info], $share_store_list);
            $this->addWarehouseStoreSnapshot($change_apply_model->id, $warehouse_model->warehouse_id, $all_store_list,
                $user);

            // 写入关联合同的快照表
            $this->addWarehouseContractSnapshot($change_apply_model->id, $warehouse_model->warehouse_id, $user);

            // 更新仓库状态 -> 变更审批中
            $this->syncUpdateWarehouseStatus($warehouse_model, $user);

            // 发起BY审批
            $by_add_result = (new ByWorkflowService())->add([
                'submitter_id' => $user['id'],
                'summary_data' => [],
                'biz_type'     => ByWorkflowEnums::BY_BIZ_TYPE_WAREHOUSE_CHANGE_STATUS,
                'audit_params' => [],
            ]);

            $this->logger->info('仓库状态变更: BY审批流创建结果=' . json_encode($by_add_result, JSON_UNESCAPED_UNICODE));

            // 回更变更申请表BY审批单号
            $change_apply_update_data = [
                'workflow_no' => $by_add_result['serial_no'],
            ];
            if ($change_apply_model->i_update($change_apply_update_data) === false) {
                throw new BusinessException('仓库状态变更, 变更申请表更新失败=' . get_data_object_error_msg($change_apply_model),
                    ErrCode::$BUSINESS_ERROR);
            }

            // OA审批业务 与 BY审批流关系
            ByWorkflowBusinessRelRepository::getInstance()->addRel($change_apply_model,
                Enums::WF_WAREHOUSE_STATUS_CHANGE_BIZ_TYPE, $by_add_result['serial_no']);

            $db->commit();
        } catch (ValidationException $e) {
            $db->rollback();

            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $db->rollback();

            $code    = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning('仓库管理-仓库信息-变更状态提交:' . $e->getMessage());
        } catch (Exception $e) {
            $db->rollback();

            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('仓库管理-仓库信息-变更状态提交:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
        ];
    }

    /**
     * 仓库管理-变更网点保存
     * @param array $params
     * @param array $user
     * @return array
     */
    public function changeStoreSave(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $data    = [
            'is_can_submit' => false,// 是否符合提交条件
            'submit_result' => false,// 确认提交后的处理结果
        ];

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $warehouse_model = ContractWarehouseRepository::getInstance()->getOneById($params['id']);
            if (empty($warehouse_model)) {
                throw new ValidationException(static::$t->_('warehouse_info_is_null_hint',
                    ['warehouse_id' => $params['id']]), ErrCode::$VALIDATE_ERROR);
            }

            if ($warehouse_model->warehouse_status != ContractEnums::WAREHOUSE_STATUS_USING) {
                throw new ValidationException(static::$t->_('warehouse_save_error_010'), ErrCode::$VALIDATE_ERROR);
            }

            $main_store_params = $params['main_store_info'];
            if (!empty($main_store_params)) {
                $main_store_params['store_flag'] = ContractEnums::WAREHOUSE_STORE_FLAG_MAIN;
            }
            $share_store_params = $params['share_store_list'];

            // 网点信息是否变更
            // 主网点
            $exist_main_store_info = $warehouse_model->getMainStoreInfo();
            $exist_main_store_info = !empty($exist_main_store_info) ? $exist_main_store_info->toArray() : [];
            $is_change_main_store  = $this->isChangedStoreInfo($main_store_params, $exist_main_store_info);

            // 共用网点
            $is_change_share_store  = false;
            $exist_share_store_list = $warehouse_model->getShareStoreList()->toArray();
            $exist_share_store_list = array_column($exist_share_store_list, null, 'store_id');
            foreach ($share_store_params as &$share_store) {
                $share_store['store_flag'] = ContractEnums::WAREHOUSE_STORE_FLAG_SHARE;

                // 发现有一个有变更, 即不必再关注其他网点是否有变更
                if ($is_change_share_store) {
                    continue;
                }

                $is_change_share_store = $this->isChangedStoreInfo($share_store,
                    $exist_share_store_list[$share_store['store_id']] ?? []);
            }

            // 网点信息未变更
            if (!$is_change_main_store && !$is_change_share_store) {
                throw new ValidationException(static::$t->_('warehouse_save_error_016'), ErrCode::$SUCCESS);
            }

            // 符合提交条件, 但非确认后的提交, 所以
            if (!$params['is_confirm_submit']) {
                // 首次点击提交, 返回是否可提交的校验结果
                $data['is_can_submit'] = true;
                throw new ValidationException(static::$t->_('warehouse_save_error_020'), ErrCode::$SUCCESS);
            }

            // 仓库信息变更记录
            $change_before_data = [];
            $change_after_data  = [];

            // 变更前主网点数据
            if (!empty($exist_main_store_info)) {
                $exist_main_store_info['store_name']                              = $this->getStoreNameByStoreId($exist_main_store_info['store_id']);
                $change_before_data[ContractEnums::WAREHOUSE_MAIN_STORE_INFO_KEY] = [
                    'store_category' => $exist_main_store_info['store_category'],
                    'store_name'     => $exist_main_store_info['store_name'],
                    'store_id'       => $exist_main_store_info['store_id'],
                    'begin_date'     => $exist_main_store_info['begin_date'],
                ];

                if (!empty($exist_main_store_info['end_date'])) {
                    $change_before_data[ContractEnums::WAREHOUSE_MAIN_STORE_INFO_KEY]['end_date'] = $exist_main_store_info['end_date'];
                }
            }

            // 变更前共有网点数据
            if (!empty($exist_share_store_list)) {
                $store_ids  = array_column($exist_share_store_list, 'store_id');
                $store_list = (new StoreRepository())->getStoreListByIds($store_ids, null);
                if (!isset($store_list[Enums::PAYMENT_HEADER_STORE_ID])) {
                    $store_list[Enums::PAYMENT_HEADER_STORE_ID]['name'] = Enums::PAYMENT_HEADER_STORE_NAME;
                }

                foreach ($exist_share_store_list as $_store) {
                    $share_store_data = [
                        'store_category' => $_store['store_category'],
                        'store_name'     => $store_list[$_store['store_id']]['name'] ?? '',
                        'store_id'       => $_store['store_id'],
                        'begin_date'     => $_store['begin_date'],
                    ];

                    if (!empty($_store['end_date'])) {
                        $share_store_data['end_date'] = $_store['end_date'];
                    }

                    $change_before_data[ContractEnums::WAREHOUSE_SHARE_STORE_INFO_KEY][] = $share_store_data;
                }
            }

            // 变更后主网点数据
            if (!empty($main_store_params)) {
                $change_after_data[ContractEnums::WAREHOUSE_MAIN_STORE_INFO_KEY] = [
                    'store_category' => $main_store_params['store_category'],
                    'store_name'     => $main_store_params['store_name'],
                    'store_id'       => $main_store_params['store_id'],
                    'begin_date'     => $main_store_params['begin_date'],
                ];
            }

            // 变更后共有网点数据
            if (!empty($share_store_params)) {
                foreach ($share_store_params as $_store) {
                    $change_share_store_data = [
                        'store_category' => $_store['store_category'],
                        'store_name'     => $_store['store_name'],
                        'store_id'       => $_store['store_id'],
                        'begin_date'     => $_store['begin_date'],
                    ];

                    if (!empty($_store['end_date'])) {
                        $change_share_store_data['end_date'] = $_store['end_date'];
                    }

                    $change_after_data[ContractEnums::WAREHOUSE_SHARE_STORE_INFO_KEY][] = $change_share_store_data;
                }
            }

            // 生成OA侧 状态变更单号
            $oa_audit_no = static::genSerialNo(ContractEnums::WAREHOUSE_STORE_CHANGE_AUDIT_NO_PREFIX,
                RedisKey::WAREHOUSE_STORE_CHANGE_AUDIT_NO_COUNTER, 4, date('Ymd'));

            // 构造变更申请数据
            // 构造快照数据
            $change_apply_data = [
                'oa_audit_no'         => $oa_audit_no,
                'biz_type'            => ByWorkflowEnums::BY_BIZ_TYPE_WAREHOUSE_CHANGE_STORE,
                'warehouse_main_id'   => $warehouse_model->id,
                'warehouse_id'        => $warehouse_model->warehouse_id,
                'warehouse_name'      => $warehouse_model->warehouse_name,
                'real_area'           => $warehouse_model->real_area,
                'province_code'       => $warehouse_model->province_code,
                'province_name'       => '',
                'city_code'           => $warehouse_model->city_code,
                'city_name'           => '',
                'district_code'       => $warehouse_model->district_code,
                'district_name'       => '',
                'warehouse_latitude'  => $warehouse_model->warehouse_latitude,
                'warehouse_longitude' => $warehouse_model->warehouse_longitude,
                'warehouse_status'    => $warehouse_model->warehouse_status,
                'approval_status'     => Enums::WF_STATE_PENDING,
                'deactivate_date'     => $warehouse_model->deactivate_date,
                'deactivate_reason'   => $warehouse_model->deactivate_reason,
                'deactivate_staff_id' => $warehouse_model->deactivate_staff_id,
                'deactivate_at'       => $warehouse_model->deactivate_at,
                'vacant_start_date'   => $warehouse_model->vacant_start_date,
                'vacant_reason'       => $warehouse_model->vacant_reason,
                'vacant_staff_id'     => $warehouse_model->vacant_staff_id,
                'vacant_at'           => $warehouse_model->vacant_at,
                'created_id'          => $user['id'],
                'created_at'          => date('Y-m-d H:i:s'),
                'updated_id'          => $user['id'],
                'updated_at'          => date('Y-m-d H:i:s'),
            ];

            // 仓库省/市/区名称
            $warehouse_area_info = $this->getAreaInfo([$warehouse_model->toArray()])[0] ?? [];
            if (!empty($warehouse_area_info)) {
                $change_apply_data['province_name'] = $warehouse_area_info['province_name'];
                $change_apply_data['city_name']     = $warehouse_area_info['city_name'];
                $change_apply_data['district_name'] = $warehouse_area_info['district_name'];
            }

            $this->logger->info('仓库网点变更: warehouse_change_apply=' . json_encode($change_apply_data,
                    JSON_UNESCAPED_UNICODE));
            $change_apply_model = new WarehouseChangeApplyModel();
            if ($change_apply_model->i_create($change_apply_data) === false) {
                throw new BusinessException('仓库网点变更-变更申请表写入失败, 原因可能是=' . get_data_object_error_msg($change_apply_model),
                    ErrCode::$BUSINESS_ERROR);
            }

            // 写入变更记录表
            $change_record_data = [
                'warehouse_main_id' => $warehouse_model->id,
                'warehouse_id'      => $warehouse_model->warehouse_id,
                'change_apply_id'   => $change_apply_model->id,
                'oa_audit_no'       => $oa_audit_no,
                'before_data'       => !empty($change_before_data) ? json_encode($change_before_data,
                    JSON_UNESCAPED_UNICODE) : '',
                'after_data'        => !empty($change_after_data) ? json_encode($change_after_data,
                    JSON_UNESCAPED_UNICODE) : '',
                'data_type'         => ContractEnums::WAREHOUSE_STORE_CHANGE_DATA_TYPE,
                'data_source'       => ContractEnums::WAREHOUSE_DATA_CHANGE_SOURCE_APPLY,
                'is_effective'      => ContractEnums::WAREHOUSE_DATA_CHANGE_TO_BE_EFFECTIVE,
                'created_id'        => $user['id'],
                'created_name'      => $user['name'],
                'created_at'        => date('Y-m-d H:i:s'),
                'updated_id'        => $user['id'],
                'updated_at'        => date('Y-m-d H:i:s'),
            ];

            $this->logger->info('仓库网点变更: warehouse_change_record=' . json_encode($change_record_data,
                    JSON_UNESCAPED_UNICODE));
            $change_record_model = new WarehouseChangeRecordModel();
            if ($change_record_model->i_create($change_record_data) === false) {
                throw new BusinessException('仓库网点变更-变更记录表写入失败, 原因可能是=' . get_data_object_error_msg($change_record_model),
                    ErrCode::$BUSINESS_ERROR);
            }

            // 写入仓库网点快照表: 主网点 + 共用网点
            $all_store_list = array_merge([$main_store_params], $share_store_params);
            $this->addWarehouseStoreSnapshot($change_apply_model->id, $warehouse_model->warehouse_id, $all_store_list,
                $user);

            // 写入关联合同的快照表
            $this->addWarehouseContractSnapshot($change_apply_model->id, $warehouse_model->warehouse_id, $user);

            // 更新仓库状态 -> 变更审批中
            $this->syncUpdateWarehouseStatus($warehouse_model, $user);

            // 发起BY审批
            $by_add_result = (new ByWorkflowService())->add([
                'submitter_id' => $user['id'],
                'summary_data' => [],
                'biz_type'     => ByWorkflowEnums::BY_BIZ_TYPE_WAREHOUSE_CHANGE_STORE,
                'audit_params' => [],
            ]);

            $this->logger->info('仓库网点变更: BY审批流创建结果=' . json_encode($by_add_result, JSON_UNESCAPED_UNICODE));

            // 回更变更申请表BY审批单号
            $change_apply_update_data = [
                'workflow_no' => $by_add_result['serial_no'],
            ];
            if ($change_apply_model->i_update($change_apply_update_data) === false) {
                throw new BusinessException('仓库网点变更, 变更申请表更新失败=' . get_data_object_error_msg($change_apply_model),
                    ErrCode::$BUSINESS_ERROR);
            }

            // 添加 OA审批业务 与 BY审批流关系
            ByWorkflowBusinessRelRepository::getInstance()->addRel($change_apply_model,
                Enums::WF_WAREHOUSE_STORE_CHANGE_BIZ_TYPE, $by_add_result['serial_no']);

            $db->commit();

            $data['submit_result'] = true;
        } catch (ValidationException $e) {
            $db->rollback();

            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $db->rollback();

            $code    = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning('仓库管理-仓库信息-变更网点提交:' . $e->getMessage());
        } catch (Exception $e) {
            $db->rollback();

            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('仓库管理-仓库信息-变更网点提交:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }


    /**
     * 仓库管理-停用仓库
     * @param array $params
     * @param array $user
     * @return array
     */
    public function deactivateWarehouse(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';

        try {
            $warehouse_model = ContractWarehouseRepository::getInstance()->getOneById($params['id']);
            if (empty($warehouse_model)) {
                throw new ValidationException(static::$t->_('warehouse_info_is_null_hint',
                    ['warehouse_id' => $params['id']]), ErrCode::$VALIDATE_ERROR);
            }

            if ($warehouse_model->warehouse_status == ContractEnums::WAREHOUSE_STATUS_DEACTIVATED) {
                throw new ValidationException(static::$t->_('warehouse_info_status_error',
                    ['warehouse_id' => $params['id']]), ErrCode::$VALIDATE_ERROR);
            }

            $update_data = [
                'warehouse_status'    => $params['warehouse_status'],
                'deactivate_date'     => $params['deactivate_date'],
                'deactivate_reason'   => $params['deactivate_reason'],
                'deactivate_staff_id' => $user['id'],
                'deactivate_at'       => date('Y-m-d H:i:s'),
                'updated_id'          => $user['id'],
                'updated_at'          => date('Y-m-d H:i:s'),
            ];
            if ($warehouse_model->i_update($update_data) === false) {
                throw new BusinessException('仓库停用更新失败, 原因可能是=' . get_data_object_error_msg($warehouse_model) . '; 数据=' . json_encode($warehouse_model->toArray(),
                        JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code    = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning('仓库管理-仓库信息-停用操作失败:' . $e->getMessage());
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('仓库管理-仓库信息-停用操作失败:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
        ];
    }

    /**
     * 仓库管理-搜索列表
     * @param array $params
     * @return array
     */
    public function getSearchList(array $params)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $item    = [];

        try {
            $item = ContractWarehouseRepository::getInstance()->getSearchList($params, GlobalEnums::DEFAULT_PAGE_SIZE);

            // 获取仓库的省/市/区名称
            $item = $this->getAreaInfo($item);
            foreach ($item as &$val) {
                unset($val['province_code'], $val['city_code'], $val['district_code']);
            }
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('仓库管理-仓库列表-关键词搜索失败:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $item,
        ];
    }

    /**
     * 获取仓库基本信息
     *
     * @param string $warehouse_id
     * @return array
     * @throws ValidationException
     */
    public function getBaseInfo(string $warehouse_id)
    {
        $warehouse_model = ContractWarehouseRepository::getInstance()->getOneByWarehouseId($warehouse_id);
        if (empty($warehouse_model)) {
            throw new ValidationException(static::$t->_('warehouse_info_is_null_hint',
                ['warehouse_id' => $warehouse_id]), ErrCode::$VALIDATE_ERROR);
        }

        // 仓库省/市/区信息
        $warehouse_area_info = $this->getAreaInfo([$warehouse_model->toArray()])[0] ?? [];

        return [
            'warehouse_id'        => $warehouse_model->warehouse_id,
            'warehouse_name'      => $warehouse_model->warehouse_name,
            'real_area'           => $warehouse_model->real_area,
            'province_name'       => $warehouse_area_info['province_name'],
            'city_name'           => $warehouse_area_info['city_name'],
            'district_name'       => $warehouse_area_info['district_name'],
            'warehouse_latitude'  => $warehouse_model->warehouse_latitude,
            'warehouse_longitude' => $warehouse_model->warehouse_longitude,
        ];
    }

    /**
     * 合同终审通过, 回更关联仓库的网点信息 和 变更记录
     *
     * @param array $contract_info
     * @return bool
     */
    public function syncRelatedWarehouseData(array $contract_info)
    {
        $this->logger->info('租房合同终审通过, 待回更仓库网点的合同信息=' . json_encode($contract_info, JSON_UNESCAPED_UNICODE));

        // 仓库信息
        $warehouse_model = ContractWarehouseRepository::getInstance()->getOneByWarehouseId($contract_info['warehouse_id']);
        if (empty($warehouse_model)) {
            $this->logger->notice('租房合同终审通过, 回更仓库网点时, 仓库信息为空, 仓库ID=' . $contract_info['warehouse_id']);
        }

        // 使用中的主网点
        $main_store_model = $warehouse_model->getMainStoreInfo();
        $main_store_info  = !empty($main_store_model) ? [$main_store_model->store_id => $main_store_model->toArray()] : [];
        $this->logger->info('租房合同终审通过, 回更仓库网点时, 仓库主网点信息=' . json_encode($main_store_info, JSON_UNESCAPED_UNICODE));

        // 在主网点中, 略过
        if (!empty($main_store_info[$contract_info['store_id']])) {
            return true;
        }

        // 使用中的共用网点
        $share_store_models = $warehouse_model->getShareStoreList();
        $share_store_list   = array_column($share_store_models->toArray(), null, 'store_id');
        $this->logger->info('租房合同终审通过, 回更仓库网点时, 仓库共用网点信息=' . json_encode($share_store_list, JSON_UNESCAPED_UNICODE));

        // 在共用网点中, 略过
        if (!empty($share_store_list[$contract_info['store_id']])) {
            return true;
        }

        // 增加网点
        $add_store_data = [
            'warehouse_main_id' => $warehouse_model->id,
            'warehouse_id'      => $warehouse_model->warehouse_id,
            'store_id'          => $contract_info['store_id'],
            'store_category'    => $contract_info['store_category'],
            'begin_date'        => $contract_info['begin_date'],
            'use_status'        => ContractEnums::WAREHOUSE_STORE_USE_STATUS_ING,
            'created_at'        => date('Y-m-d H:i:s'),
            'created_id'        => $contract_info['created_id'],
            'updated_at'        => date('Y-m-d H:i:s'),
            'updated_id'        => $contract_info['created_id'],
        ];

        // 增加变更记录
        $add_change_record_data = [
            'warehouse_main_id' => $warehouse_model->id,
            'warehouse_id'      => $warehouse_model->warehouse_id,
            'before_data'       => '',
            'after_data'        => '',
            'data_type'         => ContractEnums::WAREHOUSE_STORE_CHANGE_DATA_TYPE,
            'data_source'       => ContractEnums::WAREHOUSE_DATA_CHANGE_SOURCE_CONTRACT,
            'is_effective'      => ContractEnums::WAREHOUSE_DATA_CHANGE_EFFECTIVED,
            'created_at'        => date('Y-m-d H:i:s'),
            'created_id'        => $contract_info['created_id'],
            'created_name'      => (new HrStaffRepository())->getStaffById($contract_info['created_id'])['name'] ?? '',
            'updated_at'        => date('Y-m-d H:i:s'),
            'updated_id'        => $contract_info['created_id'],
        ];

        // 主网点为空, 则 置为主网点
        $change_before_data = [];
        $change_after_data  = [];

        $store_ids  = array_merge(array_column($share_store_list, 'store_id'), [$contract_info['store_id']]);
        $store_list = (new StoreRepository())->getStoreListByIds($store_ids, null);
        if (empty($main_store_info)) {
            $add_store_data['store_flag'] = ContractEnums::WAREHOUSE_STORE_FLAG_MAIN;

            $change_after_data[ContractEnums::WAREHOUSE_MAIN_STORE_INFO_KEY] = [
                'store_category' => $contract_info['store_category'],
                'store_name'     => $store_list[$contract_info['store_id']]['name'] ?? '',
                'store_id'       => $contract_info['store_id'],
                'begin_date'     => $contract_info['begin_date'],
            ];

            $add_change_record_data['after_data'] = json_encode($change_after_data, JSON_UNESCAPED_UNICODE);
        } else {
            // 主网点不空, 则置为共用网点
            $add_store_data['store_flag'] = ContractEnums::WAREHOUSE_STORE_FLAG_SHARE;

            // 变更后的共用网点
            $change_after_data[ContractEnums::WAREHOUSE_SHARE_STORE_INFO_KEY][] = [
                'store_category' => $contract_info['store_category'],
                'store_name'     => $store_list[$contract_info['store_id']]['name'] ?? '',
                'store_id'       => $contract_info['store_id'],
                'begin_date'     => $contract_info['begin_date'],
            ];

            // 变更前共有网点数据: 空数组不会进入循环体
            foreach ($share_store_list as $store) {
                $share_store_data = [
                    'store_category' => $store['store_category'],
                    'store_name'     => $store_list[$store['store_id']]['name'] ?? '',
                    'store_id'       => $store['store_id'],
                    'begin_date'     => $store['begin_date'],
                ];

                if (!empty($store['end_date'])) {
                    $share_store_data['end_date'] = $store['end_date'];
                }

                $change_before_data[ContractEnums::WAREHOUSE_SHARE_STORE_INFO_KEY][] = $share_store_data;

                // 变更后的共用网点(需含变更前的共用网点)
                $change_after_data[ContractEnums::WAREHOUSE_SHARE_STORE_INFO_KEY][] = $share_store_data;
            }

            $add_change_record_data['before_data'] = !empty($change_before_data) ? json_encode($change_before_data,
                JSON_UNESCAPED_UNICODE) : '';
            $add_change_record_data['after_data']  = json_encode($change_after_data, JSON_UNESCAPED_UNICODE);
        }

        // 增加仓库网点
        $warehouse_store_model = new WarehouseStoreModel();
        if ($warehouse_store_model->i_create($add_store_data) === false) {
            $this->logger->error('租房合同终审通过, 回更仓库信息-增加网点失败,error=' . get_data_object_error_msg($warehouse_store_model));
        }

        // 增加变更记录
        $change_record_model = new WarehouseChangeRecordModel();
        if ($change_record_model->i_create($add_change_record_data) === false) {
            $this->logger->error('租房合同终审通过, 回更仓库信息-增加变更记录失败,error=' . get_data_object_error_msg($change_record_model));
        }

        return true;
    }

    /**
     * 是否可发起 - 仓库管理-仓库信息管理-续签报价
     * 当仓库状态为使用中并且有关联审批通过的合同时才可发起
     * @param int $warehouse_id 仓库id
     * @throws ValidationException
     */
    public function validateCanPrice(int $warehouse_id)
    {
        // 当前仓库模型
        $warehouse_model = ContractWarehouseRepository::getInstance()->getOneByWarehouseId($warehouse_id);
        if (empty($warehouse_model)) {
            throw new ValidationException(static::$t->_('data_not_exist_by_id', ['id' => $warehouse_id]), ErrCode::$VALIDATE_ERROR);
        }

        //仓库状态非使用中
        if ($warehouse_model->warehouse_status != ContractEnums::WAREHOUSE_STATUS_USING) {
            throw new ValidationException(static::$t->_('warehouse_add_price_error'), ErrCode::$VALIDATE_ERROR);
        }

        //有关联审批通过的合同时
        $count = $this->getWarehouseContractCount($warehouse_id, Enums::WF_STATE_APPROVED);
        if (!$count) {
            throw new ValidationException(static::$t->_('warehouse_add_price_error'), ErrCode::$VALIDATE_ERROR);
        }
    }

    /**
     * 简易仓库信息列表
     * @param array $params
     * @return array
     */
    public function simpleList(array $params)
    {
        $page_size = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : (int)$params['pageSize'];
        $page_num  = empty($params['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : (int)$params['pageNum'];

        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $data    = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];

        try {
            $builder = $this->modelsManager->createBuilder();
            $builder->from(ContractWarehouseModel::class);

            if (!empty($params['keyword'])) {
                $builder->where('warehouse_id LIKE :keyword: OR warehouse_name LIKE :keyword:', ['keyword' => "%{$params['keyword']}%"]);
            }

            $count = (int)$builder->columns('COUNT(id) AS total')->getQuery()->getSingleResult()->total;
            $items = [];
            if ($count) {
                $offset = $page_size * ($page_num - 1);
                $builder->columns([
                    'warehouse_id',
                    'warehouse_name',
                ]);
                $builder->limit($page_size, $offset);
                $items = $builder->getQuery()->execute()->toArray();
            }

            $data['pagination']['total_count'] = $count;
            $data['items']                     = $items;
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('仓库管理-简易仓库信息列表获取失败:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }
}
