<?php
/**
 * 仓库管理 - 变更审批
 */

namespace App\Modules\Warehouse\Services;

use App\Library\Enums;
use App\Library\Enums\ByWorkflowEnums;
use App\Library\Enums\ContractEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Models\oa\ByWorkflowAuditLogModel;
use App\Models\oa\WarehouseChangeApplyModel;
use App\Models\oa\WarehouseStoreModel;
use App\Modules\Third\Services\ByWorkflowService;
use App\Repository\oa\ByWorkflowAuditLogRepository;
use App\Repository\oa\ContractWarehouseRepository;
use App\Repository\oa\WarehouseChangeApplyRepository;
use Exception;

class AuditService extends BaseService
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 仓库管理-列表
     * @param array $params
     * @param array $user
     * @return array
     */
    public function list(array $params, array $user)
    {
        $page_size = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : (int)$params['pageSize'];
        $page_num  = empty($params['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : (int)$params['pageNum'];

        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $data    = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];

        try {
            $columns = [
                'main.id',
                'main.warehouse_id',
                'main.warehouse_name',
                'main.workflow_no',
                'main.biz_type',
                'main.real_area',
                'main.province_name',
                'main.city_name',
                'main.district_name',
                'main.warehouse_longitude',
                'main.warehouse_latitude',
                'main.created_id',
                'main.created_at',
            ];

            $builder = $this->modelsManager->createBuilder();
            $builder->from(['main' => WarehouseChangeApplyModel::class]);
            $this->buildListCondition($builder, $params, $user);

            if ($params['flag'] == GlobalEnums::AUDIT_TAB_PENDING) {
                // 待处理: 先取全量待处理, 再过滤当前用户的待处理
                $builder->columns($columns);
                $builder->orderby('main.id ASC');
                $all_items = $builder->getQuery()->execute()->toArray();
                if (!empty($all_items)) {
                    $workflow_nos   = array_column($all_items, 'workflow_no');
                    $by_list_params = [
                        'serial_no'   => $workflow_nos,
                        'biz_type'    => [
                            ByWorkflowEnums::BY_BIZ_TYPE_WAREHOUSE_CHANGE_STATUS,
                            ByWorkflowEnums::BY_BIZ_TYPE_WAREHOUSE_CHANGE_STORE,
                        ],
                        'approval_id' => $user['id'],
                        'state'       => [ByWorkflowEnums::BY_OPERATE_WAIT_AUDIT],
                        'page_num'    => $page_num,
                        'page_size'   => $page_size,
                    ];
                    $by_result      = (new ByWorkflowService())->getList($by_list_params);
                    $count          = !empty($by_result['total_count']) ? (int)$by_result['total_count'] : 0;
                    if ($count) {
                        $by_result_list = array_column($by_result['list'], 'serial_no');
                        foreach ($all_items as $key => $value) {
                            if (!in_array($value['workflow_no'], $by_result_list)) {
                                unset($all_items[$key]);
                            }
                        }

                        $items = array_values($all_items);
                    }
                }
            } else {
                // 已处理
                $count = (int)$builder->columns('COUNT(DISTINCT(main.id)) AS total')->getQuery()->getSingleResult()->total;
                if ($count) {
                    $columns[] = 'audit_log.created_at AS audited_at';
                    $builder->columns($columns);
                    $builder->orderby('audit_log.id DESC');

                    $offset = $page_size * ($page_num - 1);
                    $builder->limit($page_size, $offset);
                    $items = $builder->getQuery()->execute()->toArray();
                }
            }

            $items = $this->handleItems($items ?? []);

            $data['pagination']['total_count'] = $count ?? 0;
            $data['items']                     = $items;
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('仓库管理-变更审批-列表获取失败:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 处理审批列表数据格式
     *
     * @param array $items
     * @return array
     */
    protected function handleItems(array $items)
    {
        if (empty($items)) {
            return [];
        }

        foreach ($items as &$val) {
            // 审批业务类型
            $val['biz_type_label'] = static::$t->_(ByWorkflowEnums::$by_workflow_biz_type_item[$val['biz_type']]);

            // 审批操作时间
            $val['audited_at'] = $val['audited_at'] ?? '';
        }

        return $items;
    }

    /**
     * 审批列表取数条件组合
     *
     * @param object $builder
     * @param array $params
     * @param array $user
     * @return object
     */
    protected function buildListCondition(object $builder, array $params, array $user)
    {
        if (!empty($params['warehouse_id'])) {
            $builder->andWhere('main.warehouse_id = :warehouse_id:', ['warehouse_id' => $params['warehouse_id']]);
        }

        if (!empty($params['province_code'])) {
            $builder->andWhere('main.province_code = :province_code:', ['province_code' => $params['province_code']]);
        }

        if (!empty($params['city_code'])) {
            $builder->andWhere('main.city_code = :city_code:', ['city_code' => $params['city_code']]);
        }

        if (!empty($params['district_code'])) {
            $builder->andWhere('main.district_code = :district_code:', ['district_code' => $params['district_code']]);
        }

        if (!empty($params['created_id'])) {
            $builder->andWhere('main.created_id = :created_id:', ['created_id' => $params['created_id']]);
        }

        if (!empty($params['created_start_date'])) {
            $builder->andWhere('main.created_at >= :created_start_date:',
                ['created_start_date' => $params['created_start_date'] . ' 00:00:00']);
        }

        if (!empty($params['created_end_date'])) {
            $builder->andWhere('main.created_at <= :created_end_date:',
                ['created_end_date' => $params['created_end_date'] . ' 23:59:59']);
        }

        // 待处理
        if ($params['flag'] == GlobalEnums::AUDIT_TAB_PENDING) {
            $builder->andWhere('main.approval_status = :approval_status:',
                ['approval_status' => Enums::WF_STATE_PENDING]);
        } else {
            // 已处理
            $builder->leftjoin(ByWorkflowAuditLogModel::class, 'main.id = audit_log.biz_value', 'audit_log');
            $builder->inWhere('audit_log.biz_type',
                [Enums::WF_WAREHOUSE_STATUS_CHANGE_BIZ_TYPE, Enums::WF_WAREHOUSE_STORE_CHANGE_BIZ_TYPE]);
            $builder->andWhere('audit_log.approval_id = :approval_id:', ['approval_id' => $user['id']]);
        }

        return $builder;
    }

    /**
     * 仓库审批-详情获取
     * @param int $id
     * @param array $user
     * @return array
     */
    public function getDetail(int $id, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $data    = [];

        try {
            $change_apply_model = WarehouseChangeApplyRepository::getInstance()->getOneById($id);
            if (empty($change_apply_model)) {
                throw new ValidationException(static::$t->_('warehouse_change_info_is_null', ['change_id' => $id]),
                    ErrCode::$VALIDATE_ERROR);
            }

            // 本次变更内容
            $current_change_info = $change_apply_model->getCurrentChangeRecord()->toArray();
            $current_change_info = $this->formatChangeRecord($current_change_info)[0] ?? (object)[];

            // 关联合同信息
            $related_contract_list = $change_apply_model->getContractSnapshot()->toArray();
            foreach ($related_contract_list as &$contract) {
                if (!empty($contract['archive_status'])) {
                    $contract['contract_status_label'] = static::$t->_(ContractEnums::$contract_archive_status[$contract['archive_status']]);
                } else {
                    $contract['contract_status_label'] = static::$t->_(Enums::$contract_status[$contract['contract_status']]);
                }

                $contract['contract_type_label'] = static::$t->_(Enums::$contract_is_master[$contract['contract_type']]);
                unset($contract['archive_status'], $contract['contract_status'], $contract['contract_type']);
            }

            // 主网点信息
            $main_store_info = $change_apply_model->getMainStoreSnapshot()->toArray();
            $main_store_info = $this->formatStoreSnapshotDetail($main_store_info)[0] ?? [];

            // 共用网点信息
            $share_store_list = $change_apply_model->getShareStoreSnapshot()->toArray();
            $share_store_list = $this->formatStoreSnapshotDetail($share_store_list);

            // 共用网点: 按开始使用时间倒序
            $share_store_list = array_sort($share_store_list, 'begin_date', SORT_DESC);

            // 审批流日志
            $audit_log = (new ByWorkflowService())->log([
                'serial_no'   => $change_apply_model->workflow_no,
                'operator_id' => $user['id'],
                'biz_type'    => $change_apply_model->biz_type,
            ]);

            $data = [
                'id'                     => $change_apply_model->id,
                'warehouse_id'           => $change_apply_model->warehouse_id,
                'warehouse_name'         => $change_apply_model->warehouse_name,
                'real_area'              => $change_apply_model->real_area,
                'province_name'          => $change_apply_model->province_name,
                'city_name'              => $change_apply_model->city_name,
                'district_name'          => $change_apply_model->district_name,
                'warehouse_latitude'     => $change_apply_model->warehouse_latitude,
                'warehouse_longitude'    => $change_apply_model->warehouse_longitude,
                'warehouse_status'       => $change_apply_model->warehouse_status,
                'warehouse_status_label' => static::$t->_(ContractEnums::$warehouse_status_enums[$change_apply_model->warehouse_status]),

                // 本次变更内容
                'current_change_info'    => $current_change_info,

                // 关联合同信息
                'related_contract_list'  => $related_contract_list,

                // 主网点信息
                'main_store_info'        => [
                    'store_category'       => $main_store_info['store_category'] ?? '',
                    'store_category_code'  => $main_store_info['store_category_code'] ?? '',
                    'store_id'             => $main_store_info['store_id'] ?? '',
                    'store_name'           => $main_store_info['store_name'] ?? '',
                    'province_region_name' => $main_store_info['province_region_name'] ?? '',
                    'full_detail_address'  => $main_store_info['full_detail_address'] ?? '',
                    'begin_date'           => $main_store_info['begin_date'] ?? '',
                ],

                // 共用网点信息
                'share_store_list'       => $share_store_list,

                // 审批日志
                'audit_log'              => $audit_log,
            ];
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('仓库管理-变更审批-详情获取失败:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 仓库管理-变更审批-通过
     *
     * @param array $params
     * @param array $user
     * @return array
     */
    public function approval(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            // 变更申请
            $change_apply_model = WarehouseChangeApplyRepository::getInstance()->getOneById($params['id']);
            if (empty($change_apply_model)) {
                throw new ValidationException(static::$t->_('warehouse_change_info_is_null',
                    ['change_id' => $params['id']]), ErrCode::$VALIDATE_ERROR);
            }

            if ($change_apply_model->approval_status != Enums::WF_STATE_PENDING) {
                throw new ValidationException(static::$t->_('audited_error_001'), ErrCode::$VALIDATE_ERROR);
            }

            // 仓库信息
            $warehouse_model = ContractWarehouseRepository::getInstance()->getOneById($change_apply_model->warehouse_main_id);
            if (empty($warehouse_model)) {
                throw new ValidationException(static::$t->_('warehouse_info_is_null_hint',
                    ['warehouse_id' => $change_apply_model->warehouse_main_id]), ErrCode::$VALIDATE_ERROR);
            }

            // 创建审批日志
            ByWorkflowAuditLogRepository::getInstance()->addAuditLog(
                ByWorkflowEnums::$by_oa_biz_type_map[$change_apply_model->biz_type],
                $change_apply_model->id,
                $change_apply_model->created_id,
                $user['id'],
                Enums::WF_STATE_APPROVED
            );

            // 审批
            $audit_result = (new ByWorkflowService())->audit([
                'serial_no'   => $change_apply_model->workflow_no,
                'biz_type'    => $change_apply_model->biz_type,
                'reason'      => $params['reason'],
                'status'      => ByWorkflowEnums::BY_OPERATE_PASS,
                'operator_id' => $user['id'],
            ]);

            // 终态: 同步更新 变更申请 / 变更记录 + 仓库信息 / 仓库网点
            if (!empty($audit_result) && !empty($audit_result['is_final']) && $audit_result['is_final'] == 1) {
                $this->logger->info('仓库终审通过, 仓库变更申请数据=' . json_encode($change_apply_model->toArray(),
                        JSON_UNESCAPED_UNICODE));
                $this->logger->info('仓库终审通过, 仓库原数据=' . json_encode($warehouse_model->toArray(),
                        JSON_UNESCAPED_UNICODE));

                // 变更申请审批状态更新
                $change_apply_data = [
                    'approval_status' => Enums::WF_STATE_APPROVED,
                    'approval_at'     => date('Y-m-d H:i:s'),
                    'updated_id'      => $user['id'],
                    'updated_at'      => date('Y-m-d H:i:s'),
                ];
                if ($change_apply_model->i_update($change_apply_data) === false) {
                    throw new BusinessException('仓库变更审批状态更新失败,error=' . get_data_object_error_msg($change_apply_model),
                        ErrCode::$BUSINESS_ERROR);
                }

                // 变更记录同步为已生效
                $change_record_model = $change_apply_model->getChangeRecordModel();
                $change_record_data  = [
                    'is_effective' => ContractEnums::WAREHOUSE_DATA_CHANGE_EFFECTIVED,
                    'updated_id'   => $user['id'],
                    'updated_at'   => date('Y-m-d H:i:s'),
                ];
                if ($change_record_model->i_update($change_record_data) === false) {
                    throw new BusinessException('仓库变更记录状态更新失败,error=' . get_data_object_error_msg($change_record_model),
                        ErrCode::$BUSINESS_ERROR);
                }

                // 仓库信息 / 仓库网点信息 同步
                // 仓库信息以快照为准
                $warehouse_update_data = [
                    'warehouse_status' => $change_apply_model->warehouse_status,
                    'updated_id'       => $change_apply_model->created_id,
                    'updated_at'       => date('Y-m-d H:i:s'),
                ];

                // 变更状态: -> 停用 / 空置
                if ($change_apply_model->biz_type == ByWorkflowEnums::BY_BIZ_TYPE_WAREHOUSE_CHANGE_STATUS && $change_apply_model->warehouse_status != ContractEnums::WAREHOUSE_STATUS_USING) {
                    // 仓库信息以快照为准(快照是变更提交时, 变更后的数据快照)
                    $warehouse_update_data['deactivate_date']     = $change_apply_model->deactivate_date;
                    $warehouse_update_data['deactivate_reason']   = $change_apply_model->deactivate_reason;
                    $warehouse_update_data['deactivate_staff_id'] = $change_apply_model->deactivate_staff_id;
                    $warehouse_update_data['deactivate_at']       = $change_apply_model->deactivate_at;
                    $warehouse_update_data['vacant_start_date']   = $change_apply_model->vacant_start_date;
                    $warehouse_update_data['vacant_reason']       = $change_apply_model->vacant_reason;
                    $warehouse_update_data['vacant_staff_id']     = $change_apply_model->vacant_staff_id;
                    $warehouse_update_data['vacant_at']           = $change_apply_model->vacant_at;

                    // 使用中 -> 停用
                    // 空置 -> 停用
                    // 使用中 -> 空置
                    $store_end_date = $change_apply_model->warehouse_status == ContractEnums::WAREHOUSE_STATUS_VACANT ? $change_apply_model->vacant_start_date : $change_apply_model->deactivate_date;

                    // 将使用中且结束日期为空的网点 的结束日期 置为 结束使用, 结束日期为 停用日期 或 空置日期
                    $warehouse_all_shore_list = $warehouse_model->getAllStoreList($warehouse_model->id);

                    $this->logger->info('仓库终审通过, 待更新网点的结束日期=' . $store_end_date);
                    $this->logger->info('仓库终审通过, 待更新为结束使用的网点=' . json_encode($warehouse_all_shore_list->toArray(),
                            JSON_UNESCAPED_UNICODE));

                    // 空对象, 不会进入循环体
                    foreach ($warehouse_all_shore_list as $store_model) {
                        if (empty($store_model->end_date)) {
                            $store_update = [
                                'end_date'   => $store_end_date,
                                'use_status' => ContractEnums::WAREHOUSE_STORE_USE_STATUS_END,
                                'updated_at' => date('Y-m-d H:i:s'),
                                'updated_id' => $change_apply_model->created_id,
                            ];
                            if ($store_model->i_update($store_update) === false) {
                                throw new BusinessException('仓库网点更新失败,error=' . get_data_object_error_msg($store_model),
                                    ErrCode::$BUSINESS_ERROR);
                            }
                        }
                    }
                } else {
                    // 变更网点(仓库状态未变:使用中) + 状态变更->进行中 (两个场景的: 主网点 和 共用网点的维护 逻辑一致)
                    // 需新增的仓库网点
                    $warehouse_store_add_data = [];

                    // 须更新的共用网点 -> 结束态
                    $share_store_update_data = [];

                    // 仓库原主网点
                    $main_shore_model = $warehouse_model->getMainStoreInfo($warehouse_model->id);
                    $main_shore_info  = !empty($main_shore_model) ? $main_shore_model->toArray() : [];
                    $this->logger->info('仓库终审通过, 仓库原主网点=' . json_encode($main_shore_info, JSON_UNESCAPED_UNICODE));

                    // 仓库主网点快照
                    $main_store_snapshot = $change_apply_model->getMainStoreSnapshot($change_apply_model->id)->toArray();
                    $this->logger->info('仓库终审通过, 仓库主网点快照=' . json_encode($main_store_snapshot, JSON_UNESCAPED_UNICODE));

                    // 仓库原共用网点
                    $share_shore_models = $warehouse_model->getShareStoreList($warehouse_model->id);
                    $share_shore_list   = array_column($share_shore_models->toArray(), null, 'store_id');
                    $this->logger->info('仓库终审通过, 仓库原共用网点=' . json_encode($share_shore_list, JSON_UNESCAPED_UNICODE));

                    // 仓库共用网点快照
                    $share_store_snapshot = $change_apply_model->getShareStoreSnapshot($change_apply_model->id)->toArray();
                    $this->logger->info('仓库终审通过, 仓库共用网点快照=' . json_encode($share_store_snapshot,
                            JSON_UNESCAPED_UNICODE));

                    // 1. 仓库主网点维护逻辑: 若有 快照主网点存在, 则原主网点置为结束, 相同的共用网点置为结束; 不存在, 则略过此逻辑
                    $main_store_snapshot_info = $main_store_snapshot[0] ?? [];
                    if (!empty($main_store_snapshot_info)) {
                        // 原主网点结束日期更新为新主网点开始日期的前一天, 同时置为结束使用状态
                        $main_store_snapshot_begin_before_date = date('Y-m-d',
                            strtotime("{$main_store_snapshot_info['begin_date']} -1 day"));

                        // 新、旧主网点 是否一样
                        $is_same_main_store = !empty($main_shore_info) && $main_shore_info['store_id'] == $main_store_snapshot_info['store_id'];

                        // 1.1 原有主网点信息维护
                        if (!empty($main_shore_model)) {
                            $main_store_update = [
                                'updated_at' => date('Y-m-d H:i:s'),
                                'updated_id' => $change_apply_model->created_id,
                            ];

                            // 新、旧主网点相同, 更新旧主网点相关字段
                            if ($is_same_main_store) {
                                $main_store_update['store_category'] = $main_store_snapshot_info['store_category'];
                                $main_store_update['begin_date']     = $main_store_snapshot_info['begin_date'];
                                $main_store_update['end_date']       = $main_store_snapshot_info['end_date'];
                                $main_store_update['use_status']     = ContractEnums::WAREHOUSE_STORE_USE_STATUS_ING;
                            } else {
                                // 新、旧主网点不同, 结束旧主网点
                                $main_store_update['end_date']   = $main_store_snapshot_begin_before_date;
                                $main_store_update['use_status'] = ContractEnums::WAREHOUSE_STORE_USE_STATUS_END;
                            }

                            if ($main_shore_model->i_update($main_store_update) === false) {
                                throw new BusinessException('原主网点更新失败,error=' . get_data_object_error_msg($main_shore_model),
                                    ErrCode::$BUSINESS_ERROR);
                            }
                        }

                        // 1.2 构造待批量写入的网点数据(新、旧主网点不同, 写入新的主网点)
                        if (!$is_same_main_store) {
                            $warehouse_store_add_data[] = [
                                'warehouse_main_id' => $change_apply_model->warehouse_main_id,
                                'warehouse_id'      => $change_apply_model->warehouse_id,
                                'store_id'          => $main_store_snapshot_info['store_id'],
                                'store_category'    => $main_store_snapshot_info['store_category'],
                                'store_flag'        => ContractEnums::WAREHOUSE_STORE_FLAG_MAIN,
                                'begin_date'        => $main_store_snapshot_info['begin_date'],
                                'end_date'          => $main_store_snapshot_info['end_date'],
                                'use_status'        => !empty($main_store_snapshot_info['end_date']) ? ContractEnums::WAREHOUSE_STORE_USE_STATUS_END : ContractEnums::WAREHOUSE_STORE_USE_STATUS_ING,
                                'created_at'        => date('Y-m-d H:i:s'),
                                'created_id'        => $change_apply_model->created_id,
                                'updated_at'        => date('Y-m-d H:i:s'),
                                'updated_id'        => $change_apply_model->created_id,
                            ];
                        }

                        // 1.3 与新主网点一致的原共用网点 -> 更新为结束使用
                        // 空对象, 不会进入循环体
                        foreach ($share_shore_models as $share_store_model) {
                            if ($share_store_model->store_id == $main_store_snapshot_info['store_id']) {
                                $share_store_update_data[$main_store_snapshot_info['store_id']] = $main_store_snapshot_begin_before_date;
                            }
                        }
                    }

                    // 2. 共用网点维护(空数组, 不会进入循环体)
                    foreach ($share_store_snapshot as $store_snapshot) {
                        // 若和主网点相同 且 此共用网点非结束态, 则此共用网点置为已结束
                        if (!empty($main_store_snapshot_info) && $store_snapshot['store_id'] == $main_store_snapshot_info['store_id']) {
                            $store_snapshot['end_date'] = !empty($store_snapshot['end_date']) ? $store_snapshot['end_date'] : $main_store_snapshot_begin_before_date;
                        }

                        // 2.1 找出本次新增的共用网点
                        if (!isset($share_shore_list[$store_snapshot['store_id']])) {
                            $warehouse_store_add_data[] = [
                                'warehouse_main_id' => $change_apply_model->warehouse_main_id,
                                'warehouse_id'      => $change_apply_model->warehouse_id,
                                'store_id'          => $store_snapshot['store_id'],
                                'store_category'    => $store_snapshot['store_category'],
                                'store_flag'        => ContractEnums::WAREHOUSE_STORE_FLAG_SHARE,
                                'begin_date'        => $store_snapshot['begin_date'],
                                'end_date'          => $store_snapshot['end_date'],
                                'use_status'        => !empty($store_snapshot['end_date']) ? ContractEnums::WAREHOUSE_STORE_USE_STATUS_END : ContractEnums::WAREHOUSE_STORE_USE_STATUS_ING,
                                'created_at'        => date('Y-m-d H:i:s'),
                                'created_id'        => $change_apply_model->created_id,
                                'updated_at'        => date('Y-m-d H:i:s'),
                                'updated_id'        => $change_apply_model->created_id,
                            ];
                        } else {
                            // 2.2 找出原共用网点且标注了结束使用的, 更新原共用网点结束使用
                            if (!empty($store_snapshot['end_date'])) {
                                $share_store_update_data[$store_snapshot['store_id']] = $store_snapshot['end_date'];
                            }
                        }
                    }

                    // 待更新的原共用网点
                    $this->logger->info('仓库终审通过, 待更新为结束态的共用网点=' . json_encode($share_store_update_data,
                            JSON_UNESCAPED_UNICODE));
                    if (!empty($share_store_update_data)) {
                        foreach ($share_shore_models as $share_store_model) {
                            if (!empty($share_store_update_data[$share_store_model->store_id])) {
                                $share_store_update = [
                                    'end_date'   => $share_store_update_data[$share_store_model->store_id],
                                    'use_status' => ContractEnums::WAREHOUSE_STORE_USE_STATUS_END,
                                    'updated_at' => date('Y-m-d H:i:s'),
                                    'updated_id' => $change_apply_model->created_id,
                                ];

                                if ($share_store_model->i_update($share_store_update) === false) {
                                    throw new BusinessException('原共用网点更新失败,error=' . get_data_object_error_msg($share_store_model),
                                        ErrCode::$BUSINESS_ERROR);
                                }
                            }
                        }
                    }

                    // 待写入的新增网点
                    $this->logger->info('仓库终审通过, 待批量写入的仓库网点=' . json_encode($warehouse_store_add_data,
                            JSON_UNESCAPED_UNICODE));
                    if (!empty($warehouse_store_add_data)) {
                        $warehouse_store_model = new WarehouseStoreModel();
                        if ($warehouse_store_model->batch_insert($warehouse_store_add_data) === false) {
                            throw new BusinessException('仓库网点批量写入失败', ErrCode::$BUSINESS_ERROR);
                        }
                    }
                }

                // 更新仓库主信息
                $this->logger->info('仓库终审通过, 待更新的仓库数据=' . json_encode($warehouse_update_data, JSON_UNESCAPED_UNICODE));
                if ($warehouse_model->i_update($warehouse_update_data) === false) {
                    throw new BusinessException('仓库信息更新失败,error=' . get_data_object_error_msg($warehouse_model),
                        ErrCode::$BUSINESS_ERROR);
                }
            }

            $db->commit();
        } catch (ValidationException $e) {
            $db->rollback();
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $db->rollback();
            $code    = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning('仓库管理-变更审批-同意操作:' . $e->getMessage());
        } catch (Exception $e) {
            $db->rollback();
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('仓库管理-变更审批-同意操作:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
        ];
    }

    /**
     * 仓库管理-变更审批-驳回
     *
     * @param array $params
     * @param array $user
     * @return array
     */
    public function reject(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            // 变更申请
            $change_apply_model = WarehouseChangeApplyRepository::getInstance()->getOneById($params['id']);
            if (empty($change_apply_model)) {
                throw new ValidationException(static::$t->_('warehouse_change_info_is_null',
                    ['change_id' => $params['id']]), ErrCode::$VALIDATE_ERROR);
            }

            if ($change_apply_model->approval_status != Enums::WF_STATE_PENDING) {
                throw new ValidationException(static::$t->_('audited_error_001'), ErrCode::$VALIDATE_ERROR);
            }

            // 仓库信息
            $warehouse_model = ContractWarehouseRepository::getInstance()->getOneById($change_apply_model->warehouse_main_id);
            if (empty($warehouse_model)) {
                throw new ValidationException(static::$t->_('warehouse_info_is_null_hint',
                    ['warehouse_id' => $change_apply_model->warehouse_main_id]), ErrCode::$VALIDATE_ERROR);
            }

            // 变更申请审批状态更新
            $change_apply_data = [
                'approval_status' => Enums::WF_STATE_REJECTED,
                'approval_at'     => date('Y-m-d H:i:s'),
                'updated_id'      => $user['id'],
                'updated_at'      => date('Y-m-d H:i:s'),
            ];
            if ($change_apply_model->i_update($change_apply_data) === false) {
                throw new BusinessException('仓库变更审批状态更新失败,error=' . get_data_object_error_msg($change_apply_model),
                    ErrCode::$BUSINESS_ERROR);
            }

            // 变更记录同步为未生效
            $change_record_model = $change_apply_model->getChangeRecordModel();
            $change_record_data  = [
                'is_effective' => ContractEnums::WAREHOUSE_DATA_CHANGE_NO_EFFECTIVE,
                'updated_id'   => $user['id'],
                'updated_at'   => date('Y-m-d H:i:s'),
            ];
            if ($change_record_model->i_update($change_record_data) === false) {
                throw new BusinessException('仓库变更记录状态更新失败,error=' . get_data_object_error_msg($change_record_model),
                    ErrCode::$BUSINESS_ERROR);
            }

            // 仓库信息状态回滚: 如果是状态变更, 则 修改前状态 从 变更记录 before_data 中取
            if ($change_apply_model->biz_type == ByWorkflowEnums::BY_BIZ_TYPE_WAREHOUSE_CHANGE_STATUS) {
                $change_before_data = json_decode($change_record_model->before_data, true);
                if (!isset($change_before_data['warehouse_base_info']['warehouse_status'])) {
                    throw new BusinessException('仓库变更记录-原数据有误,before_data=' . $change_record_model->before_data,
                        ErrCode::$BUSINESS_ERROR);
                }
                $before_warehouse_status = $change_before_data['warehouse_base_info']['warehouse_status'];
            } else {
                $before_warehouse_status = $change_apply_model->warehouse_status;
            }

            $warehouse_data = [
                'warehouse_status' => $before_warehouse_status,
                'updated_id'       => $change_apply_model->created_id,
                'updated_at'       => date('Y-m-d H:i:s'),
            ];
            if ($warehouse_model->i_update($warehouse_data) === false) {
                throw new BusinessException('仓库状态回滚失败,error=' . get_data_object_error_msg($warehouse_model),
                    ErrCode::$BUSINESS_ERROR);
            }

            // 创建审批日志
            ByWorkflowAuditLogRepository::getInstance()->addAuditLog(
                ByWorkflowEnums::$by_oa_biz_type_map[$change_apply_model->biz_type],
                $change_apply_model->id,
                $change_apply_model->created_id,
                $user['id'],
                Enums::WF_STATE_REJECTED
            );

            // 审批
            (new ByWorkflowService())->audit([
                'serial_no'   => $change_apply_model->workflow_no,
                'biz_type'    => $change_apply_model->biz_type,
                'reason'      => $params['reason'],
                'status'      => ByWorkflowEnums::BY_OPERATE_REJECT,
                'operator_id' => $user['id'],
            ]);

            $db->commit();
        } catch (ValidationException $e) {
            $db->rollback();
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $db->rollback();
            $code    = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning('仓库管理-变更审批-驳回操作:' . $e->getMessage());
        } catch (Exception $e) {
            $db->rollback();
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('仓库管理-变更审批-驳回操作:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
        ];
    }

}
