<?php

namespace App\Modules\Shop\Services;

use App\Library\Enums;
use App\Library\ErrCode;
use App\Util\RedisKey;
use App\Library\Enums\ShopEnums;
use App\Library\Enums\GlobalEnums;
use App\Models\backyard\SettingEnvModel;
use App\Models\backyard\SysStoreModel;
use App\Library\Enums\StaffInfoEnums;
use App\Repository\HrStaffRepository;
use App\Repository\StoreRepository;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\SysDepartmentModel;
use App\Repository\DepartmentRepository;
use App\Modules\Shop\Models\InteriorGoodsModel;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Models\backyard\SysManageRegionModel;
use App\Models\backyard\SysManagePieceModel;
use App\Models\backyard\InteriorOrdersModel;
use App\Models\backyard\InteriorBatchApplyModel;
use App\Modules\Shop\Models\InteriorGoodsSkuModel;
use App\Models\backyard\InteriorGoodsStoreCateRelModel;
use App\Models\backyard\InteriorOrdersGoodsSkuModel;
use App\Models\backyard\InteriorBatchApplyProductModel;
use App\Models\backyard\InteriorGoodsManageDepartmentPermissionModel;

class GoodsBatchService extends BaseService
{

    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }


    // 员工商城-批量下单工服-非必要的筛选条件
    public static $not_must_params = [
        'apply_no',
        'start_created_at',
        'end_created_at',
        'batch_grant_department_id',
        'staff_id',
        'status',
        'start_check_at',
        'end_check_at',
        'grant_staff_id',
        'type',
        'pageNum',
        'pageSize',
    ];

    // 员工商城-批量下单工服-列表-搜索条件
    public static $validate_list_search = [
        'apply_no'                  => 'StrLenGe:0|>>>:apply_no error',
        'start_created_at'          => 'Date|>>>:start_created_at error',
        'end_created_at'            => 'Date|>>>:end_created_at error',
        'batch_grant_department_id' => 'IntGe:0|>>>:batch_grant_department_id error',
        'staff_id'                  => 'StrLenGe:0|>>>: staff_id error',
        'status'                    => 'Arr|ArrLenGeLe:0,4|>>>:status error',
        'start_check_at'            => 'Date|>>>:start_check_at error',
        //核对开始时间
        'end_check_at'              => 'Date|>>>:end_check_at error',
        //核对结束时间
        'type'                      => 'IntIn:' . ShopEnums::BATCH_ORDER_CHECK_TYPE_WAIT . ',' . ShopEnums::BATCH_ORDER_CHECK_TYPE_FINISH . '|>>>:type error',
        //type
        'pageNum'                   => 'IntGe:1|>>>:page error',
        //当前页码
        'pageSize'                  => 'IntGe:1|>>>:page_size error',
        //每页条数
    ];

    // 员工商城-批量下单工服-列表-查看 、修改、 删除
    public static $validate_share = [
        'id' => 'Required|IntGt:0|>>>:id error',
    ];

    // 员工商城-批量下单工服-列表-新建
    public static $validate_add = [
        'apply_no'                   => 'Required|StrLenGeLe:15,50|>>>:apply_no error',
        'staff_id'                   => 'Required|IntGt:0|>>>:staff_id error',
        'node_department_id'         => 'Required|IntGt:0|>>>: node_department_id error',
        'batch_grant_department_id'  => 'Required|IntGt:0|>>>: batch_grant_department_id error',
        'cost_store_type'            => 'Required|IntIn:' . Enums::PAYMENT_COST_STORE_TYPE_01 . ',' . Enums::PAYMENT_COST_STORE_TYPE_02 . '|>>>: cost_store_type error',
        'is_reissue'                 => 'Required|IntIn:' . ShopEnums::BATCH_ORDER_IS_REISSUE_YES . ',' . ShopEnums::BATCH_ORDER_IS_REISSUE_NO . '|>>>: is_reissue error',
        'remark'                     => 'StrLenGeLe:0,200|>>>: remark error',
        'products'                   => 'Required|Arr|ArrLenGeLe:1,' . ShopEnums::INTERIOR_BATCH_ORDER_APPLY_DETAIL_MAX_NUM . '|>>>:Employees who place orders for work clothes cannot be empty',
        'products[*]'                => 'Required|Obj',
        'products[*].grant_staff_id' => 'Required|IntGt:0|>>>:grant_staff_id error',
        'products[*].goods_id'       => 'Required|IntGt:0|>>>:goods_id error',
        'products[*].barcode'        => 'Required|StrLenGeLe:1,30|>>>:barcode error',//barcode
        'products[*].size'           => 'Required|StrLenGeLe:1,20|>>>:size error',
        'products[*].buy_num'        => 'Required|IntGeLe:1,100000|>>>:buy_num error',//申请数量
    ];

    // 员工商城-批量下单工服-列表-查看 、修改、删除
    public static $validate_add_info_list = [
        'staff_id'                  => 'IntGe:1|>>>:staff_id error',//员工工号
        'batch_grant_department_id' => 'Required|IntGe:0|>>>:batch_grant_department_id error',
        'cost_store_type'           => 'Required|IntIn:' . Enums::PAYMENT_COST_STORE_TYPE_01 . ',' . Enums::PAYMENT_COST_STORE_TYPE_02 . '|>>>:cost_store_type error',
        'is_reissue'                => 'IntIn:' . ShopEnums::BATCH_ORDER_IS_REISSUE_YES . ',' . ShopEnums::BATCH_ORDER_IS_REISSUE_NO . '|>>>:is_reissue error',
    ];

    //员工商城-批量下单工服-添加-批量下发工服部门
    public static $validate_auto_orders_department = [
        'department_name' => 'StrLenGe:0|>>>:department_name error',
    ];


    //员工商城-批量下单工服确认-核对-详细信息-核对数据提交 ,确定无误
    public static $validate_check_save = [
        'id'                         => 'Required|IntGe:1|>>>:id error',
        'products'                   => 'Required|Arr|ArrLenGeLe:1,' . ShopEnums::INTERIOR_BATCH_ORDER_APPLY_DETAIL_MAX_NUM . '|>>>:Employees who place orders for work clothes cannot be empty',
        'products[*]'                => 'Required|Obj',
        'products[*].grant_staff_id' => 'Required|IntGt:0|>>>:grant_staff_id error',
        'products[*].goods_id'       => 'Required|IntGt:0|>>>:goods_id error',
        'products[*].barcode'        => 'Required|StrLenGeLe:1,30|>>>:barcode error',//barcode
        'products[*].size'           => 'Required|StrLenGeLe:1,20|>>>:size error',
        'products[*].buy_num'        => 'Required|IntGeLe:1,100000|>>>:buy_num error',//申请数量
    ];


    //员工商城-批量下单工服确认-核对-详细信息-覆盖导入
    public static $validate_check_import = [
        'staff_id'                  => 'IntGe:1|>>>:staff_id error',//申请人工号
        'batch_grant_department_id' => 'Required|IntGe:0|>>>:batch_grant_department_id error',
        'cost_store_type'           => 'Required|IntIn:' . Enums::PAYMENT_COST_STORE_TYPE_01 . ',' . Enums::PAYMENT_COST_STORE_TYPE_02 . '|>>>:cost_store_type error',
        'is_reissue'                => 'IntIn:' . ShopEnums::BATCH_ORDER_IS_REISSUE_YES . ',' . ShopEnums::BATCH_ORDER_IS_REISSUE_NO . '|>>>:is_reissue error',
    ];

    //根据工所属网点、部门获取商品数据
    public static $validate_goods_list = [
        'staff_info_id'      => 'IntGe:1|>>>:staff_info_id error',
        'sys_store_id'       => 'Required|StrLenGeLe:1,32|>>>: sys_store_id  error',
        'node_department_id' => 'Required|IntGe:0|>>>:node_department_id error',
    ];

    // 员工商城-批量下单工服-核对-列表-搜索条件
    public static $validate_check_list_search = [
        'apply_no'                  => 'StrLenGe:0|>>>:apply_no error',
        //员工工号
        'start_created_at'          => 'Date|>>>:start_created_at error',
        //下单开始时间
        'end_created_at'            => 'Date|>>>:end_created_at error',
        //下单结束时间
        'batch_grant_department_id' => 'IntGe:0|>>>:batch_grant_department_id error',
        'staff_id'                  => 'StrLenGe:0|>>>: staff_id error',
        'status'                    => 'IntIn:' . ShopEnums::BATCH_ORDER_APPLY_STATUS_VERIFY . ',' . ShopEnums::BATCH_ORDER_APPLY_STATUS_PASS . ',' . ShopEnums::BATCH_ORDER_APPLY_STATUS_REPEAL . ',' . ShopEnums::BATCH_ORDER_APPLY_STATUS_CANCEL . '|>>>:status error',
        //款项状态类型
        'start_check_at'            => 'Date|>>>:start_check_at error',
        //核对开始时间
        'end_check_at'              => 'Date|>>>:end_check_at error',
        //核对结束时间
        'type'                      => 'IntIn:' . ShopEnums::BATCH_ORDER_CHECK_TYPE_WAIT . ',' . ShopEnums::BATCH_ORDER_CHECK_TYPE_FINISH . '|>>>:type error',
        //type
        'pageNum'                   => 'IntGe:1|>>>:page error',
        //当前页码
        'pageSize'                  => 'IntGe:1|>>>:page_size error',
        //每页条数
    ];


    /**
     * 批量下单工服-添加-初始化数据-默认项
     * @param array $user 当前登陆者信息
     * @return array
     */
    public function getAddDefault(array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = '';
        $data    = [];
        try {
            $data['apply_no']                   = static::genSerialNo(ShopEnums::INTERIOR_BATCH_ORDER_APPLY_NO_PREFIX,
                RedisKey::INTERIOR_BATCH_ORDER_APPLY_COUNTER, 5);
            $data['staff_id']                   = $user['id'];
            $data['staff_name']                 = $user['name'];
            $data['node_department_id']         = $user['department_id'] ?? 0;
            $data['node_department_name']       = $user['department'] ?? '';
            $data['batch_grant_department_arr'] = $this->getAutoOrdersDepartment()['data'];
            $store_type_arr                     = [];
            foreach (Enums::$payment_cost_store_type as $type => $lang_key) {
                $store_type_arr[] = [
                    'value' => $type,
                    'label' => static::$t->_($lang_key),
                ];
            }
            $data['store_type_arr'] = $store_type_arr;

            $is_reissue_arr = [];
            foreach (ShopEnums::$batch_order_is_reissue as $reissue => $lang_key) {
                $is_reissue_arr[] = [
                    'value' => $reissue,
                    'label' => static::$t->_($lang_key),
                ];
            }
            $data['batch_is_reissue_arr'] = $is_reissue_arr;
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('shop_goods_batch_add_default_failed:' . $e->getMessage());
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }


    /**
     * 添加初始化选择部门和网点后的详细信息数据
     * @param array $params 基本信息选择的数据
     * @return array
     */
    public function getAddInfoList(array $params)
    {
        $code       = ErrCode::$SUCCESS;
        $message    = '';
        $staff_data = [];
        try {
            $limit = $params['limit'] ?? ShopEnums::INTERIOR_BATCH_ORDER_APPLY_DETAIL_MAX_NUM;
            //自动下单工服职位ID
            $job_ids = (new SettingEnvModel())->getValByCode('auto_interior_orders_job_ids');
            $job_ids = explode(',', $job_ids);
            if (!empty($job_ids)) {
                //批量下发工服入职天数
                $batch_entry_day = (new SettingEnvModel())->getValByCode('batch_interior_orders_entry_day') ?? 0;
                $end_hire_date   = date('Y-m-d H:i:s', time() - 86400 * $batch_entry_day);
                $start_hire_date = date('Y-m-d H:i:s', time() - 86400 * 365);
                //查询符合条件的数据

                $columns = 'hsi.name as grant_staff_name, hsi.staff_info_id, hsi.staff_info_id as staff_id, hsi.hire_date, hsi.state,hsi.sys_store_id, hsi.wait_leave_state, ss.manage_region, ss.manage_piece, ss.name as sys_store_name, hsi.node_department_id';
                $builder = $this->modelsManager->createBuilder();
                $builder->columns($columns);
                $builder->from(['hsi' => HrStaffInfoModel::class]);
                $builder->leftjoin(SysStoreModel::class, 'hsi.sys_store_id = ss.id', 'ss');
                $builder->andWhere('hsi.state = :state: and wait_leave_state = :wait_leave_state: and  hire_type = :hire_type: and  hsi.formal = :formal:',
                    [
                        'state'            => StaffInfoEnums::STAFF_STATE_IN,
                        'wait_leave_state' => StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_NO,
                        'hire_type'        => StaffInfoEnums::HIRE_TYPE_PERMANENT_EMPLOYEE,
                        'formal'           => StaffInfoEnums::FORMAL_IN,
                    ]);
                $builder->inWhere('hsi.job_title', $job_ids);
                $builder->andWhere('hsi.node_department_id = :node_department_id:',
                    ['node_department_id' => $params['batch_grant_department_id']]);
                if ($params['cost_store_type'] == Enums::PAYMENT_COST_STORE_TYPE_01) {
                    $builder->andWhere('hsi.sys_store_id = :sys_store_id:',
                        ['sys_store_id' => Enums::HEAD_OFFICE_STORE_FLAG]);
                } else {
                    $builder->andWhere('hsi.sys_store_id != :sys_store_id:',
                        ['sys_store_id' => Enums::HEAD_OFFICE_STORE_FLAG]);
                }
                $builder->betweenWhere('hsi.hire_date', $start_hire_date, $end_hire_date);
                $builder->orderBy('hsi.hire_date asc, hsi.staff_info_id asc');
                $builder->limit($limit);
                $staff_arr = $builder->getQuery()->execute()->toArray();
                if (!empty($staff_arr)) {
                    //过滤超出最大免费单身的员工id
                    $orders_goods_sku_num = $this->getInteriorOrdersGoodsSkuNum(array_column($staff_arr,
                        'staff_info_id'));
                    //最大数量限制
                    $max_free_num = (new SettingEnvModel())->getValByCode('interior_free_limit_num');
                    foreach ($staff_arr as $staff_info) {
                        //获取一个用户下的多个免费订单数量
                        $orders_goods_buy_nums = !empty($orders_goods_sku_num[$staff_info['staff_info_id']]) ? $orders_goods_sku_num[$staff_info['staff_info_id']]['buy_nums'] : 0;
                        if ($orders_goods_buy_nums < $max_free_num) {
                            $staff_data[] = $staff_info;
                        }
                    }
                    if (!empty($staff_data)) {
                        //获取大区集合
                        $manage_region_arr = $this->getManageRegion(array_column($staff_data, 'manage_region'));
                        //获取片区集合
                        $manage_piece_arr = $this->getManagePiece(array_column($staff_data, 'manage_piece'));
                        //根据配置中的映射关系 返回对应部门和商品的数据集合
                        $goods_size = GoodsSizeService::getInstance();
                        [$goods_arr, $department_spu_arr] = $goods_size->getDepartmentGoodsArr($staff_data);

                        $barcode_size_arr = $this->getLastInteriorGoodsSizeList(array_column($staff_arr,
                            'staff_info_id'));
                        $goods_name       = 'goods_name_' . static::getLanguage();
                        foreach ($staff_data as &$staff_info) {
                            $staff_info['hire_date'] = date('Y-m-d', strtotime($staff_info['hire_date']));
                            $goods                   = $goods_arr[$department_spu_arr[$staff_info['node_department_id']]] ?? [];
                            $goods_id                = $goods[0]['goods_id'];
                            //处理每件工服的最大剩余数量
                            $goods_buy_nums = !empty($orders_goods_sku_num[$staff_info['staff_info_id']]) ? $orders_goods_sku_num[$staff_info['staff_info_id']]['buy_nums'] : 0;
                            foreach ($goods as &$goods_info) {
                                $max_num                                 = $max_free_num - $goods_buy_nums;
                                $goods_info['max_residue_purchased_num'] = $max_num > 0 ? $max_num : 0;
                            }
                            $staff_info['sys_store_name']            = $staff_info['sys_store_id'] == Enums::HEAD_OFFICE_STORE_FLAG ? Enums::PAYMENT_HEADER_STORE_NAME : $staff_info['sys_store_name'];//在职状态
                            $staff_info['job_state']                 = $goods_size->staffJobState($staff_info);                                                                                        //在职状态
                            $staff_info['manage_region_name']        = $manage_region_arr[$staff_info['manage_region']] ?? '';                                                                         //所属大区名称
                            $staff_info['manage_piece_name']         = $manage_piece_arr[$staff_info['manage_piece']] ?? '';                                                                           //所属片区名称
                            $staff_info['goods_id']                  = $barcode_size_arr[$staff_info['staff_info_id']][$goods_id]['goods_id'] ?? '';
                            $staff_info['size']                      = $barcode_size_arr[$staff_info['staff_info_id']][$goods_id]['attr_1'] ?? '';
                            $staff_info['barcode']                   = $barcode_size_arr[$staff_info['staff_info_id']][$goods_id]['goods_sku_code'] ?? '';
                            $staff_info['purchased_num']             = $goods_buy_nums;//已购买免费工服数量
                            $staff_info['interior_goods_arr'][]      =
                                [
                                    'goods_name'     => $goods[0][$goods_name] ?? '',
                                    'goods_id'       => $goods_id ?? '',
                                    'last_size'      => $barcode_size_arr[$staff_info['staff_info_id']][$goods_id]['attr_1'] ?? '',
                                    'purchased_num'  => $goods_buy_nums,
                                    'goods_size_arr' => $goods,
                                ];
                            $purchased_num_by_barcode                = array_column($goods, 'max_residue_purchased_num',
                                'barcode');
                            $buy_num                                 = $purchased_num_by_barcode[$goods[0]['barcode']] ?? $max_free_num;
                            $staff_info['buy_num']                   = $buy_num;//默认下单数量
                            $staff_info['max_residue_purchased_num'] = $buy_num;//最大可以输入的值

                        }
                    }
                }
            }
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('shop_goods_batch_order_add_default_failed:' . $e->getMessage());
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $staff_data,
        ];
    }


    /**
     * 员工商城-批量下单工服-列表
     * @param array $condition 查询参数组
     * @param array $user 登录用户
     * @return array
     */
    public function getList(array $condition, array $user)
    {
        $page_size = empty($condition['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $condition['pageSize'];
        $page_num  = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['pageNum'];
        $offset    = $page_size * ($page_num - GlobalEnums::DEFAULT_PAGE_NUM);

        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $items   = [];
        try {
            $count = $this->getListCount($condition, $user);
            if ($count > 0) {
                $builder = $this->modelsManager->createBuilder();
                $builder->columns([
                    'main.id',
                    'main.apply_no',
                    'main.staff_id',
                    'main.staff_name',
                    'main.node_department_id',
                    'main.node_department_name',
                    'main.batch_grant_department_id',
                    'main.batch_grant_department_name',
                    'main.status',
                    'main.created_at',
                    'main.checked_at',
                    'main.check_staff_name',
                    'main.check_staff_id',
                ]);
                $builder->from(['main' => InteriorBatchApplyModel::class]);
                $builder->leftJoin(InteriorBatchApplyProductModel::class, 'main.id = product.apply_id', 'product');
                $builder = $this->getCondition($builder, $condition, $user);
                $builder->groupBy('main.id');
                $builder->orderBy('main.created_at DESC, main.apply_no DESC');
                $builder->limit($page_size, $offset);
                $items = $builder->getQuery()->execute()->toArray();
            }
            $items = $this->handleData($items);
            $data  = [
                'items'      => $items,
                'pagination' => [
                    'current_page' => $page_num,
                    'per_page'     => $page_size,
                    'total_count'  => (int)$count,
                ],
            ];
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->warning('员工商城-批量下单工服-列表: ' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }


    /**
     * 员工商城-批量下单工服-记录数
     * @param array $condition 查询条件组
     * @param array $user 用户数据
     * @return mixed
     */
    public function getListCount(array $condition, array $user)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(distinct main.id) as total');
        $builder->from(['main' => InteriorBatchApplyModel::class]);
        $builder->leftJoin(InteriorBatchApplyProductModel::class, 'main.id = product.apply_id', 'product');
        $builder    = $this->getCondition($builder, $condition, $user);
        $total_info = $builder->getQuery()->getSingleResult();
        return intval($total_info->total);
    }

    /**
     * 组装列表搜索条件
     * @param object $builder 查询器对象
     * @param array $condition 查询条件组
     * @param array $user 用户数据
     * @return object
     */
    private function getCondition(object $builder, array $condition, array $user)
    {
        $apply_no                  = $condition['apply_no'] ?? '';
        $start_created_at          = $condition['start_created_at'] ?? '';
        $end_created_at            = $condition['end_created_at'] ?? '';
        $batch_grant_department_id = $condition['batch_grant_department_id'] ?? '';
        $grant_staff_id            = $condition['grant_staff_id'] ?? '';
        $apply_status              = $condition['status'] ?? '';
        $start_checked_at          = $condition['start_check_at'] ?? '';
        $end_checked_at            = $condition['end_check_at'] ?? '';
        $type                      = $condition['type'] ?? '';

        //数据权限
        $data_permission = GoodsSizeService::getInstance()->getEntrySizeStaffAll($user, $condition['is_check']);

        if ($data_permission['res_status'] == ShopEnums::DATA_PERMISSION_DEPARTMENT_RES_STATUS) {
            //配置的部门
            $builder->inWhere('main.batch_grant_department_id', $data_permission['res_data']);
        }

        if (!empty($grant_staff_id)) {
            $builder->andWhere('product.staff_id = :staff_id:', ['staff_id' => $grant_staff_id]);
        }

        if (!empty($apply_no)) {
            $builder->andWhere('main.apply_no = :apply_no:', ['apply_no' => $apply_no]);
        }

        if (!empty($start_created_at) && !empty($end_created_at)) {
            $start_created_at = $start_created_at . ' 00:00:00';
            $end_created_at   = $end_created_at . ' 23:59:59';
            $builder->betweenWhere('main.created_at', $start_created_at, $end_created_at);
        }

        if (!empty($batch_grant_department_id)) {
            $builder->andWhere('main.batch_grant_department_id = :batch_grant_department_id:',
                ['batch_grant_department_id' => $batch_grant_department_id]);
        }

        if (!empty($start_checked_at) && !empty($end_checked_at)) {
            $start_checked_at = $start_checked_at . ' 00:00:00';
            $end_checked_at   = $end_checked_at . ' 23:59:59';
            $builder->betweenWhere('main.created_at', $start_checked_at, $end_checked_at);
        }

        if (!empty($type)) {
            //批量下单工服确认页面的搜索 待核对和已经核对的页面查询  待核对传入1 已核对传入2
            if ($type == ShopEnums::BATCH_ORDER_CHECK_TYPE_WAIT) {
                $builder->andWhere('main.status = :status:', ['status' => ShopEnums::BATCH_ORDER_APPLY_STATUS_VERIFY]);
            } else {
                if (empty($apply_status)) {
                    //批量下单工服确认页面的搜索待核对页面
                    $builder->inWhere('main.status',
                        [ShopEnums::BATCH_ORDER_APPLY_STATUS_PASS, ShopEnums::BATCH_ORDER_APPLY_STATUS_CANCEL]);
                } else {
                    //批量下单工服确认页面的搜索待核对页面
                    $builder->andWhere('main.status = :status:', ['status' => $apply_status]);
                }
            }
        } else {
            //批量下单工服页面的状态搜索 传入的是数组 type 为空
            if (!empty($apply_status)) {
                $builder->inWhere('main.status', $apply_status);
            }
        }


        return $builder;
    }

    /**
     * 格式化列表数据
     * @param array $items 列表数据
     * @return array
     */
    public function handleData($items)
    {
        if (empty($items)) {
            return [];
        }
        foreach ($items as &$value) {
            $value['checked_at']     = $value['checked_at'] ?? '';
            $value['status_name']    = static::$t->_(ShopEnums::$batch_order_apply_status_arr[$value['status']]);
            $value['check_staff_id'] = !empty($value['check_staff_id']) ? $value['check_staff_id'] : '';
        }
        return $items;
    }

    /**
     * 员工商城-批量下单工服-新建-详细信息-导出（附录2标头）
     * @param array $params 详细信息-导出条件
     * @return array
     */
    public function getExportInfoList($params)
    {
        $code    = ErrCode::$SUCCESS;
        $message = '';
        $data    = $items = [];
        try {
            set_time_limit(0);
            ini_set('memory_limit', '512M');
            $data  = $this->getAddInfoList($params);
            $items = [];
            if (!empty($data['data'])) {
                $results = $goods_size_res = [];
                foreach ($data['data'] as $key => $staff) {
                    $results[] = [
                        $key + 1,
                        $staff['grant_staff_name'],
                        $staff['staff_info_id'],
                        date('Y-m-d', strtotime($staff['hire_date'])),
                        $staff['job_state'],
                        $staff['manage_region_name'],
                        $staff['manage_piece_name'],
                        $staff['sys_store_name'],
                        $staff['goods_name'] ?? '',
                        $staff['goods_size'] ?? '',
                        $staff['goods_barcode'] ?? '',
                        $staff['buy_num'] ?? '',
                        $staff['purchased_num'] ?? '',
                    ];
                }
                //部门相同所以找到商品也相同
                $goods_size_arr = $this->getGoodsSkuAll();
                foreach ($goods_size_arr as $goods_size) {
                    $goods_size_res[] = [
                        $goods_size['goods_name'],
                        $goods_size['size'],
                        $goods_size['barcode'],
                    ];
                }
                $items = [$results, $goods_size_res];
            }
            $header = [
                $this->getBatchInfoHeader(),
                [
                    self::$t['interior_goods_shop_goods_name'],   //商品名称
                    self::$t['interior_goods_shop_goods_size'],   //尺码
                    self::$t['interior_goods_shop_goods_barcode'],//商品编码
                ],
            ];

            $fileName = 'shop_goods_batch_info_export_' . date('YmdHis"');
            $data     = $this->exportManyExcel($header, $items, $fileName);
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = $e->getMessage();
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
            $this->logger->warning('shop_goods_batch_export_failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data['data'],
        ];
    }


    /**
     * 导出文件表头参见附录2 表头
     * @return array
     */
    public function getBatchInfoHeader()
    {
        return [
            self::$t['global.no'],                        //序号
            self::$t['interior_goods_staff_name'],        //员工姓名
            self::$t['interior_goods_staff_id'],          //员工工号
            self::$t['interior_goods_hire_date'],         //入职时间
            self::$t['interior_goods_job_status'],        //在职状态
            self::$t['interior_goods_manage_region'],     //所属大区
            self::$t['interior_goods_manage_piece'],      //所属片区
            self::$t['interior_goods_sys_store_name'],    //所属网点
            self::$t['interior_goods_shop_goods_name'],   //商品名称
            self::$t['interior_goods_shop_goods_size'],   //尺码
            self::$t['interior_goods_shop_goods_barcode'],//商品编码
            self::$t['interior_goods_buy_num'],           // 下单数量
            self::$t['interior_goods_last_buy_num']       // 已购买免费工服数量
        ];
    }


    /**
     * 员工商城-批量下单工服-查看
     * @param int $id
     * @param bool $is_check 是否从批量工服确定的详情
     * @return array
     */
    public function getDetail(int $id, $is_check = false)
    {
        $code    = ErrCode::$SUCCESS;
        $message = '';
        $data    = [];
        try {
            $batch_apply = InteriorBatchApplyModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $id],
                'columns'    => [
                    'id',
                    'apply_no',
                    'staff_id',
                    'node_department_id',
                    'node_department_name',
                    'batch_grant_department_id',
                    'batch_grant_department_name',
                    'cost_store_type',
                    'check_staff_id',
                    'check_staff_name',
                    'remark',
                    'checked_at',
                    'status',
                    'is_reissue',
                ],
            ]);
            if (empty($batch_apply)) {
                throw new ValidationException(static::$t->_('empty_data'), ErrCode::$VALIDATE_ERROR);
            }
            $data                = $batch_apply->toArray();
            $data['status_text'] = static::$t->_(ShopEnums::$batch_order_apply_status_arr[$data['status']]);
            $data                = $this->handleDetailData($data, $is_check);
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('员工商城-批量下单工服-查看异常: ' . $e->getMessage());
        }


        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 员工商城-批量下单工服-添加
     * @param array $params
     * @param array $user
     * @return array
     */
    public function addOne(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = '';
        $db      = $this->getDI()->get('db_backyard');
        $db->begin();

        try {
            $current_time = date('Y-m-d H:i:s', time());
            //非法操作
            if ($params['staff_id'] != $user['id']) {
                throw new ValidationException(static::$t->_('save_data_user_error'), ErrCode::$VALIDATE_ERROR);
            }

            //判断申请单号是否已存在
            $apply_no    = $params['apply_no'];
            $batch_apply = InteriorBatchApplyModel::findFirst([
                'conditions' => 'apply_no = :apply_no:',
                'bind'       => ['apply_no' => $apply_no],
            ]);
            if (!empty($batch_apply)) {
                throw new ValidationException(static::$t->_('apply_no_existed'), ErrCode::$VALIDATE_ERROR);
            }

            //一次获取提交数据全部的用户数据
            $grant_staff_ids = array_values(array_unique(array_column($params['products'], 'grant_staff_id')));

            $staff_arr = (new HrStaffRepository())->getStaffListFormalArr(array_merge([$params['staff_id']],
                $grant_staff_ids));
            //一次获取提交数据里面的全部部门
            $department_ids = array_values(array_unique(array_merge([$params['node_department_id']],
                [$params['batch_grant_department_id']])));
            $department_arr = (new DepartmentRepository())->getDepartmentByIds($department_ids);

            //用户的网点数据
            $store_ids = array_values(array_unique(array_column($staff_arr, 'sys_store_id')));
            $store_arr = (new StoreRepository())->getStoreListByIds($store_ids);

            $manage_region_map_id = array_column($store_arr, 'manage_region', 'id');
            $manage_piece_map_id  = array_column($store_arr, 'manage_piece', 'id');
            //一次获取详情里面的全部大区和片区数据
            $manage_region_ids = array_values(array_unique(array_column($store_arr, 'manage_region')));
            $manage_piece_ids  = array_values(array_unique(array_column($store_arr, 'manage_piece')));
            $manage_region     = $this->getManageRegion($manage_region_ids);
            $manage_piece      = $this->getManagePiece($manage_piece_ids);

            $apply = [
                'apply_no'                    => $params['apply_no'],
                'staff_id'                    => $params['staff_id'],
                'staff_name'                  => $staff_arr[$params['staff_id']]['name'],
                'node_department_id'          => $params['node_department_id'],
                'node_department_name'        => $department_arr[$params['node_department_id']]['name'],
                'batch_grant_department_id'   => $params['batch_grant_department_id'],
                'batch_grant_department_name' => $department_arr[$params['batch_grant_department_id']]['name'],
                'cost_store_type'             => $params['cost_store_type'],
                'is_reissue'                  => $params['is_reissue'],
                'remark'                      => $params['remark'],
                'created_at'                  => $current_time,
                'updated_at'                  => $current_time,
            ];

            $batch_apply_model = new InteriorBatchApplyModel();
            if ($batch_apply_model->i_create($apply) === false) {
                throw new BusinessException('员工商城-批量下单工服-添加失败, 原因可能是: ' . get_data_object_error_msg($batch_apply_model) . '; 数据: ' . json_encode($apply,
                        JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }

            $goods_ids = array_values(array_unique(array_column($params['products'], 'goods_id')));
            $goods_arr = GoodsSizeService::getInstance()->getGoodsSizeAll($goods_ids);
            $goods_arr = array_column($goods_arr, null, 'goods_id');
            //批量添加详情
            $product_arr = [];
            foreach ($params['products'] as $product) {
                $goods    = $goods_arr[$product['goods_id']];
                $store_id = $staff_arr[$product['grant_staff_id']]['sys_store_id'] ?? '';
                if ($store_id == Enums::HEAD_OFFICE_STORE_FLAG) {
                    $sys_store_name = Enums::PAYMENT_HEADER_STORE_NAME;
                } else {
                    $sys_store_name = !empty($store_arr[$store_id]) ? $store_arr[$store_id]['name'] : '';
                }
                $product_arr[] = [
                    'apply_id'       => $batch_apply_model->id,
                    'staff_id'       => $product['grant_staff_id'],
                    'goods_id'       => $product['goods_id'],
                    'barcode'        => $product['barcode'],
                    'size'           => $product['size'],
                    'goods_name_zh'  => $goods['goods_name_zh'],
                    'goods_name_th'  => $goods['goods_name_th'],
                    'goods_name_en'  => $goods['goods_name_en'],
                    'buy_num'        => $product['buy_num'],
                    'manage_region'  => $manage_region[$manage_region_map_id[$store_id]] ?? '',
                    'manage_piece'   => $manage_piece[$manage_piece_map_id[$store_id]] ?? '',
                    'sys_store_id'   => $store_id,
                    'sys_store_name' => $sys_store_name,
                    'created_at'     => $current_time,
                    'updated_at'     => $current_time,
                ];
            }
            $product_model = new InteriorBatchApplyProductModel();

            if (!empty($product_model) && $product_model->batch_insert($product_arr, 'db_backyard') === false) {
                throw new BusinessException('员工商城-批量下单工服-添加详情失败,数据: ' . json_encode($product_arr,
                        JSON_UNESCAPED_UNICODE), ErrCode::$INTERIOR_BATCH_APPLY_ADD_ERROR);
            }
            $db->commit();
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code    = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning('员工商城 - 商品后台管理 - 商品信息添加异常: ' . $e->getMessage());
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('员工商城 - 商品后台管理 - 商品信息添加异常: ' . $e->getMessage());
        }

        if (!empty($message)) {
            $db->rollback();
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
        ];
    }


    /**
     * 格式化详情数据
     * @param array $apply 申请单数据
     * @param bool $is_check 是否从批量工服确定的详情
     * @return array
     */
    public function handleDetailData($apply, $is_check)
    {
        if (empty($apply)) {
            return [];
        }
        $staff_arr               = (new HrStaffRepository())->getStaffById($apply['staff_id']);
        $apply['staff_name']     = $staff_arr['name'] ?? '';
        $apply['check_staff_id'] = !empty($apply['check_staff_id']) ? $apply['check_staff_id'] : '';
        $apply['checked_at']     = $apply['checked_at'] ?? '';
        $product_list            = $this->getApplyProduct($apply['id'], $apply['status']);
        $product_first           = $product_list[0];//详情不为空
        $apply['product_list']   = $product_list;
        $staff_ids               = array_values(array_column($product_list, 'staff_id'));
        if ($is_check && !empty($staff_ids)) {
            $staff_arr        = (new HrStaffRepository())->getStaffListFormalArr($staff_ids);
            $depart_staff_nub = $suspension_staff_nub = $pending_depart_staff_nub = 0;
            foreach ($staff_arr as $staff) {
                if ($staff['state'] == StaffInfoEnums::STAFF_STATE_LEAVE) {
                    $depart_staff_nub++;
                } elseif ($staff['state'] == StaffInfoEnums::STAFF_STATE_STOP) {
                    $suspension_staff_nub++;
                } elseif ($staff['state'] == StaffInfoEnums::STAFF_STATE_IN && $staff['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES) {
                    $pending_depart_staff_nub++;
                }
            }
            //已下单2单免费服务人员
            $goods_sku_num = $this->getInteriorOrdersGoodsSkuNum($staff_ids);

            $buy_nums_arr = array_column($goods_sku_num, 'buy_nums');
            $count        = 0;

            foreach ($buy_nums_arr as $num) {
                if ($num >= $product_first['interior_free_limit_num']) {
                    $count++;
                }
            }
            $apply['depart_staff_nub']         = $depart_staff_nub;                             //离职人员
            $apply['suspension_staff_nub']     = $suspension_staff_nub;                         //停职人员
            $apply['pending_depart_staff_nub'] = $pending_depart_staff_nub;                     //待离职人员
            $apply['order_free_staff_nub']     = $count;                                        //已下单2单免费服务人员
            $apply['interior_free_limit_num']  = $product_first['interior_free_limit_num'] ?? 0;//最大免费数量
            $apply['check_staff_id']           = !empty($apply['check_staff_id']) ? $apply['check_staff_id'] : '';
        }

        return $apply;
    }


    /**
     * apply_id 返回所有的批量下单工服申请明细
     * @param integer $apply_id 订单id
     * @param integer $status 订单状态
     * @return array
     */
    public function getApplyProduct(int $apply_id, int $status)
    {
        if (empty($apply_id)) {
            return [];
        }
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['iboap' => InteriorBatchApplyProductModel::class]);
        $builder->where('iboap.is_deleted = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);
        $builder->andWhere('iboap.apply_id = :apply_id:', ['apply_id' => $apply_id]);
        $product_arr = $builder->getQuery()->execute()->toArray();
        if (!empty($product_arr)) {
            $staff_ids                = array_column($product_arr, 'staff_id');
            $staff_arr                = (new HrStaffRepository())->getStaffListFormalArr($staff_ids);
            $goods_size               = GoodsSizeService::getInstance();
            $orders_goods_sku_num_arr = $this->getInteriorOrdersGoodsSkuNum(array_column($product_arr, 'staff_id'));
            $max_free_num             = (new SettingEnvModel())->getValByCode('interior_free_limit_num');
            $goods_name               = 'goods_name_' . static::getLanguage();

            $goods_arr = GoodsSizeService::getInstance()->getGoodsSizeAll(array_values(array_unique(array_column($product_arr,
                'goods_id'))));
            $goods_all = [];
            foreach ($goods_arr as $goods) {
                $goods_all[$goods['goods_id']][] = $goods;
            }
            foreach ($product_arr as &$product) {
                $staff_info                         = $staff_arr[$product['staff_id']];
                $goods_sku_num                      = $orders_goods_sku_num_arr[$product['staff_id']] ? $orders_goods_sku_num_arr[$product['staff_id']]['buy_nums'] : 0;//已购商品数量
                $product['grant_staff_name']        = $staff_info['name'] ?? '';                                                                                        //员工姓名
                $product['hire_date']               = date('Y-m-d',
                    strtotime($staff_info['hire_date']));                                                                                                               //入职时间
                $product['job_state']               = $status == ShopEnums::BATCH_ORDER_APPLY_STATUS_VERIFY ? $goods_size->staffJobState($staff_info) : self::$t->_(StaffInfoEnums::$staff_state[$product['staff_state']]);
                $product['node_department_id']      = $staff_info['node_department_id'];
                $product['manage_region_name']      = $product['manage_region'];//所属大区id
                $product['manage_piece_name']       = $product['manage_piece']; //所属片区
                $product['interior_free_limit_num'] = $max_free_num;
                $is_order_status                    = $status != ShopEnums::BATCH_ORDER_APPLY_STATUS_PASS ? ShopEnums::BATCH_IS_ORDER_STATUS_NOT_ORDER : $product['is_order_status'];
                $product['is_order_status']         = static::$t->_(ShopEnums::$batch_is_order_status[$is_order_status]);
                $product['order_reason']            = $status == ShopEnums::BATCH_ORDER_APPLY_STATUS_PASS ? $product['order_reason'] : static::$t->_('not_placed_an_order');
                $product['goods_name']              = $product[$goods_name];                                                                           //商品名称
                $product['purchased_num']           = $status == ShopEnums::BATCH_ORDER_APPLY_STATUS_VERIFY ? $goods_sku_num : $product['sum_buy_num'];//已购商品数量
                $max_residue_purchased_num          = $max_free_num - $goods_sku_num;
                $max_residue_purchased_num          = $max_residue_purchased_num <= 0 ? 0 : $max_residue_purchased_num;
                //格式保持和下拉格式一致
                $product['interior_goods_arr'][]      = [
                    'goods_name'     => $product[$goods_name],//商品名称
                    'goods_id'       => $product['goods_id'],
                    'purchased_num'  => $goods_sku_num,//已购商品数量
                    'goods_size_arr' => $goods_all[$product['goods_id']],
                ];
                $product['max_residue_purchased_num'] = $max_residue_purchased_num;//最大可以输入的值
            }
        }
        return $product_arr;
    }


    /**
     * 获取大区数据
     * @param array $ids 大区id集合
     * @return array
     **/
    public function getManageRegion($ids)
    {
        if (empty($ids)) {
            return [];
        }
        $manage_region_arr = SysManageRegionModel::find([
            'conditions' => 'id in ({ids:array}) and deleted = :deleted: ',
            'bind'       => ['ids' => $ids, 'deleted' => GlobalEnums::IS_NO_DELETED],
            'columns'    => ['name', 'id'],
        ])->toArray();
        return array_column($manage_region_arr, 'name', 'id');
    }

    /**
     * 获取片区数据
     * @param array $ids 大区id集合
     * @return array
     **/
    public function getManagePiece($ids)
    {
        if (empty($ids)) {
            return [];
        }
        $manage_piece_arr = SysManagePieceModel::find([
            'conditions' => 'manage_region_id in ({manage_region_id:array}) and deleted = :deleted:',
            'bind'       => ['manage_region_id' => $ids, 'deleted' => GlobalEnums::IS_NO_DELETED],
            'columns'    => ['name', 'manage_region_id'],
        ])->toArray();
        return array_column($manage_piece_arr, 'name', 'manage_region_id');
    }


    /**
     * 获取商品购买的总件数
     * @param array $staff_ids 用户id集合
     * @return array
     **/
    public function getInteriorOrdersGoodsSkuNum(array $staff_ids)
    {
        if (empty($staff_ids)) {
            return [];
        }
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('iogs.goods_id, sum(iogs.buy_num) as buy_nums, io.staff_id');
        $builder->from(['iogs' => InteriorOrdersGoodsSkuModel::class]);
        $builder->leftJoin(InteriorOrdersModel::class, 'iogs.order_code = io.order_code', 'io');
        $builder->where('iogs.is_free = :is_free: and  io.goods_type = :goods_type: ',
            ['is_free' => ShopEnums::FREE_BUY_STATUS_YES, 'goods_type' => ShopEnums::GOODS_TYPE_WORK_CLOTHES]);
        $builder->notInWhere('io.order_status',
            [ShopEnums::ORDER_STATUS_CUSTOMER_CANCEL_CODE, ShopEnums::ORDER_STATUS_SYSTEM_CANCEL_CODE]);
        $builder->inWhere('io.staff_id', $staff_ids);
        $builder->groupBy('io.staff_id');
        $orders_goods_sku_num = $builder->getQuery()->execute()->toArray();
        return array_column($orders_goods_sku_num, null, 'staff_id');
    }


    /**
     * 获取配置中的部门 带搜索
     * @param array $params 条件
     * @return array
     */
    public function getAutoOrdersDepartment(array $params = [])
    {
        $code    = ErrCode::$SUCCESS;
        $message = '';
        $data    = [];
        try {
            $department_ids = (new SettingEnvModel())->getValByCode('auto_interior_orders_department_ids');
            $department_ids = explode(',', $department_ids);
            if (!empty($department_ids)) {
                $builder = $this->modelsManager->createBuilder();
                $builder->columns(['id as department_id, name as department_name']);
                $builder->from(['sd' => SysDepartmentModel::class]);
                //增加了搜索条件
                if (!empty($params['department_name'])) {
                    $builder->andWhere(' (id = :id: or name like :department_name:)', [
                        'id'              => $params['department_name'],
                        'department_name' => '%' . $params['department_name'] . '%',
                    ]);
                }
                $builder->andWhere('deleted = :deleted: ', ['deleted' => GlobalEnums::IS_NO_DELETED]);
                $builder->inWhere('id', $department_ids);
                $builder->limit($params['limit'] ?? GlobalEnums::DEFAULT_PAGE_SIZE);
                $data = $builder->getQuery()->execute()->toArray();
            }
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('员工商城-员工工服尺码-新建-员工数据异常: ' . $e->getMessage());
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }


    /**
     * 员工商城-批量下单工服-导出 (附录4)
     * @param array $params 导出条件
     * @param array $user 用户数据
     * @return array
     */
    public function getExportList(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = '';
        $data    = $items = [];
        try {
            set_time_limit(0);
            ini_set('memory_limit', '512M');
            $params['pageSize'] = ShopEnums::INTERIOR_BATCH_ORDER_MAX_NUM;
            $data               = $this->getList($params, $user);
            if (!empty($data['data']['items'])) {
                foreach ($data['data']['items'] as $key => $apply) {
                    $items[] = [
                        $key + 1,
                        $apply['apply_no'],
                        $apply['staff_name'],
                        $apply['staff_id'],
                        $apply['node_department_name'],
                        $apply['batch_grant_department_name'],
                        $apply['status_name'],
                        $apply['created_at'],
                        $apply['checked_at'],
                        $apply['check_staff_name'],
                        $apply['check_staff_id'],
                    ];
                }
            }
            $header    = [
                self::$t['global.no'],                           //序号
                self::$t['interior_goods_apply_no'],             //申请编号
                self::$t['interior_goods_apply_staff_name'],     //申请人姓名
                self::$t['interior_goods_apply_staff_id'],       //申请人工号
                self::$t['interior_goods_apply_department_name'],//申请人所属部门
                self::$t['interior_goods_batch_department_name'],//批量下发工服部门
                self::$t['interior_goods_batch_status'],         //状态
                self::$t['interior_goods_apply_created_at'],     //申请时间
                self::$t['interior_goods_checked_at'],           //核对时间
                self::$t['interior_goods_checked_staff_name'],   //核对人
                self::$t['interior_goods_checked_staff_id'],     //核对人工号
            ];
            $file_name = 'shop_goods_batch_export_' . date('YmdHis');
            $result    = $this->exportExcel($header, $items, $file_name);
            $data      = $result['data'] ?? [];
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = $e->getMessage();
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
            $this->logger->warning('shop_goods_batch_export_failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }


    /**
     * 员工商城-批量下单工服-列表-(申请)作废、（核对）取消发放
     * @param int $id id
     * @param array $user 操作用户
     * @param int $source 1 作废 2 取消发放
     * @return array
     */
    public function cancel(int $id, array $user, int $source = ShopEnums::INTERIOR_BATCH_PAGE_SOURCE_REPEAL)
    {
        $code    = ErrCode::$SUCCESS;
        $message = '';
        $db      = $this->getDI()->get('db_backyard');
        $db->begin();
        $current_time = date('Y-m-d H:i:s', time());
        try {
            $batch_apply_obj = $this->getBatchApplyInfo($id);
            if (empty($batch_apply_obj)) {
                throw new ValidationException(static::$t->_('empty_data'), ErrCode::$VALIDATE_ERROR);
            }
            if ($batch_apply_obj->status != ShopEnums::BATCH_ORDER_APPLY_STATUS_VERIFY) {
                if ($source == ShopEnums::INTERIOR_BATCH_PAGE_SOURCE_CANCEL) {
                    throw new ValidationException(static::$t->_('interior_batch_apply_not_cancel'),
                        ErrCode::$VALIDATE_ERROR);
                } else {
                    throw new ValidationException(static::$t->_('interior_batch_apply_not_operate'),
                        ErrCode::$VALIDATE_ERROR);
                }
            }
            if ($source == ShopEnums::INTERIOR_BATCH_PAGE_SOURCE_CANCEL) {
                $batch_apply_obj->status           = ShopEnums::BATCH_ORDER_APPLY_STATUS_CANCEL;
                $batch_apply_obj->check_staff_id   = $user['id'];
                $batch_apply_obj->check_staff_name = $user['name'];
                $batch_apply_obj->checked_at       = $current_time;
            } else {
                $batch_apply_obj->status = ShopEnums::BATCH_ORDER_APPLY_STATUS_REPEAL;
            }
            $batch_apply_obj->updated_at = $current_time;
            if ($batch_apply_obj->save() === false) {
                throw new BusinessException('员工商城-批量下单工服-列表-作废/取消失败, 原因可能是: ' . get_data_object_error_msg($batch_apply_obj) . '; 数据: ' . json_encode($batch_apply_obj->toArray(),
                        JSON_UNESCAPED_UNICODE), ErrCode::$INTERIOR_BATCH_APPLY_EDIT_ERROR);
            }

            //更新申请单中所有员工的在职状态以及已下单免费工服数量
            $this->batchUpdateApplyProduct($id);
            $db->commit();
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code    = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning('员工商城-批量下单工服-列表-作废/取消异常: ' . $e->getMessage());
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('员工商城-批量下单工服-列表-作废/取消错误: ' . $e->getMessage());
        }
        if (!empty($message)) {
            $db->rollback();
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
        ];
    }

    /**
     * 获取申请订单详情数据
     * @param int $id 申请单id
     * @return mixed
     */
    public function getBatchApplyInfo(int $id)
    {
        return InteriorBatchApplyModel::findFirst([
            'id = :id:',
            'bind' => ['id' => $id],
        ]);
    }

    /**
     * 获取申请订单详情信息
     * @param int $id 详情信息id
     * @return mixed
     */
    public function getBatchApplyProduct(int $id)
    {
        return InteriorBatchApplyProductModel::findFirst([
            'id = :id:',
            'bind' => ['id' => $id],
        ]);
    }


    /**
     * 批量获取申请订单详情信息
     * @param array $ids 详情信息id集合
     * @return mixed
     */
    public function getBatchApplyProductList(array $ids)
    {
        return InteriorBatchApplyProductModel::find([
            'apply_id in ({apply_id:array}) and  is_deleted = :is_deleted:',
            'bind' => ['apply_id' => $ids, 'is_deleted' => GlobalEnums::IS_NO_DELETED],
        ]);
    }


    /**
     * 员工商城-批量下单工服-查看-导出 (附录3)
     * @param array $params 导出条件
     * @return array
     */
    public function getExportDetail($params)
    {
        $code    = ErrCode::$SUCCESS;
        $message = '';
        $data    = $row_values = [];
        try {
            set_time_limit(0);
            ini_set('memory_limit', '512M');
            $apply        = $this->getBatchApplyInfo($params['id'])->toArray();
            $product_list = $this->getApplyProduct($params['id'], $apply['status']);
            if (!empty($product_list)) {
                foreach ($product_list as $key => $product) {
                    $row_values[] = [
                        $key + 1,
                        $product['grant_staff_name'],
                        $product['staff_id'],
                        $product['hire_date'],
                        $product['job_state'],
                        $product['manage_region_name'],
                        $product['manage_piece_name'],
                        $product['sys_store_name'],
                        $product['goods_name'],
                        $product['size'],
                        $product['barcode'],
                        $product['buy_num'],
                        $product['purchased_num'],
                        $product['is_order_status'],
                        $product['order_reason'],
                    ];
                }
            }
            $header    = [
                self::$t['global.no'],                        //序号
                self::$t['interior_goods_staff_name'],        //员工姓名
                self::$t['interior_goods_staff_id'],          //工号
                self::$t['interior_goods_hire_date'],         //入职时间
                self::$t['interior_goods_job_status'],        //在职状态
                self::$t['interior_goods_manage_region'],     //所属大区
                self::$t['interior_goods_manage_piece'],      //所属片区
                self::$t['interior_goods_sys_store_name'],    //所属网点
                self::$t['interior_goods_shop_goods_name'],   //商品名称
                self::$t['interior_goods_shop_goods_size'],   //尺码
                self::$t['interior_goods_shop_goods_barcode'],//商品编码
                self::$t['interior_goods_buy_num'],           //下单数量
                self::$t['interior_goods_last_buy_num'],      //已购买免费工服数量
                self::$t['interior_goods_order_status'],      //下单状态
                self::$t['interior_goods_remark'],            //说明
            ];
            $file_name = 'shop_goods_batch_detail_list_export_' . date('YmdHis"');
            $result    = $this->exportExcel($header, $row_values, $file_name);
            $data      = $result['data'] ?? [];
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = $e->getMessage();
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
            $this->logger->warning('shop_goods_batch_detail_list_export_failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }


    /**
     * 员工商城-批量工服确认-列表-核对-详情-删除
     * @param int $id id
     * @return array
     */
    public function del(int $id)
    {
        $code    = ErrCode::$SUCCESS;
        $message = '';
        $data    = [];
        try {
            $apply_product_model = $this->getBatchApplyProduct($id);

            if (empty($apply_product_model)) {
                throw new ValidationException(static::$t->_('empty_data'), ErrCode::$VALIDATE_ERROR);
            }
            if ($apply_product_model->is_deleted == GlobalEnums::IS_DELETED) {
                throw new ValidationException(static::$t->_('interior_staff_repeat_del'), ErrCode::$VALIDATE_ERROR);
            }
            $apply_product_model->is_deleted = GlobalEnums::IS_DELETED;
            $apply_product_model->updated_at = date('Y-m-d H:i:s', time());
            if ($apply_product_model->save() === false) {
                throw new BusinessException('员工商城-批量工服确认-列表-核对-详情-删除失败, 原因可能是: ' . get_data_object_error_msg($apply_product_model) . '; 数据: ' . json_encode($apply_product_model->toArray(),
                        JSON_UNESCAPED_UNICODE), ErrCode::$INTERIOR_BATCH_APPLY_CHECK_DEL_ERROR);
            }
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code    = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning('员工商城-批量工服确认-列表-核对-详情-删除异常: ' . $e->getMessage());
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('员工商城-批量工服确认-列表-核对-详情-删除错误: ' . $e->getMessage());
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
        ];
    }


    /**
     * 员工商城-批量下单工服确认-核对-详细信息-核对数据提交 ,确定无误
     * @param array $params 数据
     * @param array $user 操作用户
     * @return array
     */
    public function checkSave(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = '';
        $db      = $this->getDI()->get('db_backyard');
        $db->begin();
        try {
            $batch_apply_obj = $this->getBatchApplyInfo($params['id']);
            if (empty($batch_apply_obj)) {
                throw new ValidationException(static::$t->_('empty_data'), ErrCode::$VALIDATE_ERROR);
            }
            if ($batch_apply_obj->status != ShopEnums::BATCH_ORDER_APPLY_STATUS_VERIFY) {
                throw new ValidationException(static::$t->_('interior_batch_apply_not_operate'),
                    ErrCode::$VALIDATE_ERROR);
            }

            //一次获取提交数据全部的用户数据
            $staff_ids              = array_values(array_unique(array_column($params['products'], 'grant_staff_id')));
            $staff_arr              = (new HrStaffRepository())->getStaffListFormalArr($staff_ids);
            $goods_sku_num          = $this->getInteriorOrdersGoodsSkuNum($staff_ids);
            $staff_state            = array_unique(array_column($staff_arr, 'state'));
            $staff_wait_leave_state = array_unique(array_column($staff_arr, 'wait_leave_state'));
            if (in_array(StaffInfoEnums::STAFF_STATE_LEAVE, $staff_state) || in_array(StaffInfoEnums::STAFF_STATE_STOP,
                    $staff_state) || in_array(StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES, $staff_wait_leave_state)) {
                throw new ValidationException(static::$t->_('goods_batch_check_staff_state_err'),
                    ErrCode::$VALIDATE_ERROR);
            }
            //用户的网点数据
            $store_ids = array_values(array_unique(array_column($staff_arr, 'sys_store_id')));
            $store_arr = (new StoreRepository())->getStoreListByIds($store_ids);
            //一次获取详情里面的全部大区和片区数据
            $manage_region_ids = array_values(array_unique(array_column($store_arr, 'manage_region')));
            $manage_piece_ids  = array_values(array_unique(array_column($store_arr, 'manage_piece')));
            $manage_region     = $this->getManageRegion($manage_region_ids);
            $manage_piece      = $this->getManagePiece($manage_piece_ids);
            //查询商品数据
            $goods_ids    = array_values(array_unique(array_column($params['products'], 'goods_id')));
            $goods_arr    = GoodsSizeService::getInstance()->getGoodsSizeAll($goods_ids);
            $goods_arr    = array_column($goods_arr, null, 'barcode');
            $current_time = date('Y-m-d H:i:s', time());
            $ids          = array_values(array_unique(array_filter(array_column($params['products'], 'id'))));
            if (!empty($ids)) {
                //仅修改
                $product_arr        = array_column($params['products'], null, 'id');
                $product_list_model = InteriorBatchApplyProductModel::find([
                    'id in ({id:array}) and  is_deleted = :is_deleted:',
                    'bind' => ['id' => $ids, 'is_deleted' => GlobalEnums::IS_NO_DELETED],
                ]);

                if (!empty($product_list_model->toArray())) {
                    foreach ($product_list_model as $product) {
                        $edit_data               = $product_arr[$product->id];
                        $goods_info              = $goods_arr[$edit_data['barcode']];
                        $staff_info              = $staff_arr[$edit_data['grant_staff_id']];
                        $store_info              = $store_arr[$staff_info['sys_store_id']] ?? [];
                        $product->staff_id       = $edit_data['grant_staff_id'];
                        $product->staff_state    = $staff_info['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES ? StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE : $staff_info['state'];
                        $product->sum_buy_num    = $goods_sku_num[$edit_data['grant_staff_id']]['buy_nums'] ?? 0;
                        $product->goods_id       = $edit_data['goods_id'];
                        $product->barcode        = $edit_data['barcode'];
                        $product->size           = $edit_data['size'];
                        $product->goods_name_zh  = $goods_info['goods_name_zh'];
                        $product->goods_name_th  = $goods_info['goods_name_th'];
                        $product->goods_name_en  = $goods_info['goods_name_en'];
                        $product->buy_num        = $edit_data['buy_num'];
                        $product->manage_region  = !empty($store_info['manage_region']) ? $manage_region[$store_info['manage_region']] : '';
                        $product->manage_piece   = !empty($store_info['manage_piece']) ? $manage_piece[$store_info['manage_piece']] : '';
                        $product->sys_store_id   = $staff_info['sys_store_id'];
                        $product->sys_store_name = $staff_info['sys_store_id'] == Enums::HEAD_OFFICE_STORE_FLAG ? Enums::PAYMENT_HEADER_STORE_NAME : $store_info['name'];
                        $product->updated_at     = $current_time;
                        $bool                    = $product->save();
                        if ($bool === false) {
                            throw new BusinessException('员工商城-批量下单工服确认-核对-详细信息-核对数据提交失败: ' . json_encode($product->toArray(),
                                    JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($product),
                                ErrCode::$INTERIOR_BATCH_APPLY_CHECK_EDIT_ERROR);
                        }
                    }
                }
            } else {
                //覆盖导入
                $product_arr = [];
                //如果是工服确认的覆盖导入,处理之前的详细信息变更为删除
                $old_product_list = $this->getBatchApplyProductList([$params['id']]);
                if (!empty($old_product_list->toArray())) {
                    foreach ($old_product_list as $old_product) {
                        $old_product->is_deleted = GlobalEnums::IS_DELETED;
                        $old_product->updated_at = date('Y-m-d H:i:s', time());
                        if ($old_product->save() === false) {
                            throw new BusinessException('员工商城-批量下单工服确认-核对-详细信息-核对数据提交失败: ' . json_encode($old_product->toArray(),
                                    JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($old_product),
                                ErrCode::$INTERIOR_BATCH_APPLY_CHECK_EDIT_ERROR);
                        }
                    }
                }
                foreach ($params['products'] as $product) {
                    $goods_info    = $goods_arr[$product['barcode']];
                    $staff_info    = $staff_arr[$product['grant_staff_id']];
                    $store_info    = $store_arr[$staff_info['sys_store_id']];
                    $product_arr[] = [
                        'apply_id'       => $params['id'],
                        'staff_id'       => $product['grant_staff_id'],
                        'staff_state'    => $staff_info['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES ? StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE : $staff_info['state'],
                        'sum_buy_num'    => $goods_sku_num[$product['grant_staff_id']]['buy_nums'] ?? 0,
                        'goods_id'       => $product['goods_id'],
                        'barcode'        => $product['barcode'],
                        'size'           => $product['size'],
                        'goods_name_zh'  => $goods_info['goods_name_zh'],
                        'goods_name_th'  => $goods_info['goods_name_th'],
                        'goods_name_en'  => $goods_info['goods_name_en'],
                        'buy_num'        => $product['buy_num'],
                        'manage_region'  => !empty($store_info['manage_region']) ? $manage_region[$store_info['manage_region']] : '',
                        'manage_piece'   => !empty($store_info['manage_piece']) ? $manage_piece[$store_info['manage_piece']] : '',
                        'sys_store_id'   => $staff_info['sys_store_id'],
                        'sys_store_name' => $staff_info['sys_store_id'] == Enums::HEAD_OFFICE_STORE_FLAG ? Enums::PAYMENT_HEADER_STORE_NAME : $store_info['name'],
                        'created_at'     => $current_time,
                        'updated_at'     => $current_time,
                    ];
                }
                $product_model = new InteriorBatchApplyProductModel();
                if (!empty($product_model) && $product_model->batch_insert($product_arr, 'db_backyard') === false) {
                    throw new BusinessException('员工商城-批量下单工服确认-核对-详细信息-核对数据提交失败,数据: ' . json_encode($product_arr,
                            JSON_UNESCAPED_UNICODE), ErrCode::$INTERIOR_BATCH_APPLY_CHECK_EDIT_ERROR);
                }
            }
            $batch_apply_obj->status           = ShopEnums::BATCH_ORDER_APPLY_STATUS_PASS;
            $batch_apply_obj->check_staff_id   = $user['id'];
            $batch_apply_obj->check_staff_name = $user['name'];
            $batch_apply_obj->checked_at       = $current_time;
            $batch_apply_obj->updated_at       = $current_time;
            if ($batch_apply_obj->save() === false) {
                throw new BusinessException('员工商城-批量下单工服确认-核对-详细信息-核对数据提交失败: ' . json_encode($batch_apply_obj->toArray(),
                        JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($batch_apply_obj),
                    ErrCode::$INTERIOR_BATCH_APPLY_EDIT_ERROR);
            }
            $db->commit();
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code    = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning('员工商城-批量下单工服确认-核对-详细信息-核对数据提交异常: ' . $e->getMessage());
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('员工商城-批量下单工服确认-核对-详细信息-核对数据提交错误: ' . $e->getMessage());
        }
        if (!empty($message)) {
            $db->rollback();
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
        ];
    }

    /**
     *  员工商城-批量下单工服-列表-(申请)作废、（核对）取消发放
     *  申请单中所有员工的在职状态以及已下单免费工服数量
     * @param int id  数据
     */
    public function batchUpdateApplyProduct(int $id)
    {
        $product_model = InteriorBatchApplyProductModel::find([
            'apply_id = :apply_id: and  is_deleted = :is_deleted:',
            'bind' => ['apply_id' => $id, 'is_deleted' => GlobalEnums::IS_NO_DELETED],
        ]);
        $product_arr   = $product_model->toArray();
        if (!empty($product_arr)) {
            $staff_ids     = array_values(array_unique(array_column($product_arr, 'staff_id')));
            $staff_arr     = (new HrStaffRepository())->getStaffListFormalArr($staff_ids);
            $goods_sku_num = $this->getInteriorOrdersGoodsSkuNum($staff_ids);

            foreach ($product_model as $product) {
                $product->staff_state = $staff_arr[$product->staff_id]['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES ? StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE : $staff_arr[$product->staff_id]['state'];
                $product->sum_buy_num = $goods_sku_num[$product->staff_id]['buy_nums'] ?? 0;
                $product->updated_at  = date('Y-m-d H:i:s');
                $bool                 = $product->save();
                if ($bool === false) {
                    throw new BusinessException('(申请)作废、（核对）取消发放-更新在职状态、免费工服数量失败: ' . json_encode($product->toArray(),
                            JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($product),
                        ErrCode::$INTERIOR_BATCH_APPLY_CHECK_EDIT_ERROR);
                }
            }
        }
    }


    /**
     * 员工商城-批量下单工服确认-核对-详细信息-一建清理
     * @param int $id id
     * @return array
     */
    public function clear(int $id)
    {
        $code    = ErrCode::$SUCCESS;
        $message = '';
        try {
            $product_model = InteriorBatchApplyProductModel::find([
                'apply_id = :apply_id: and  is_deleted = :is_deleted:',
                'bind' => ['apply_id' => $id, 'is_deleted' => GlobalEnums::IS_NO_DELETED],
            ]);
            $product_arr   = $product_model->toArray();
            if (empty($product_arr)) {
                throw new ValidationException(static::$t->_('empty_data'), ErrCode::$VALIDATE_ERROR);
            }
            $staff_ids = array_values(array_unique(array_column($product_arr, 'staff_id')));
            $staff_arr = (new HrStaffRepository())->getStaffListFormalArr($staff_ids);
            $res       = [];
            if (!empty($staff_arr)) {
                foreach ($staff_arr as $staff) {
                    if ($staff['state'] != StaffInfoEnums::STAFF_STATE_IN || ($staff['state'] == StaffInfoEnums::STAFF_STATE_IN && $staff['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES)) {
                        $res[] = $staff['staff_info_id'];
                    }
                }
            }
            //去除掉不在职的用户
            $order_staff_ids   = array_values(array_unique(array_filter(array_diff($staff_ids, $res))));
            $goods_order_staff = [];
            if (!empty($order_staff_ids)) {
                //按照用户查询免费商品的数量
                $goods_sku_num = $this->getInteriorOrdersGoodsSkuNum($staff_ids);
                //最大数量限制
                $max_free_num = (new SettingEnvModel())->getValByCode('interior_free_limit_num');
                foreach ($goods_sku_num as $sku_num) {
                    if ($sku_num['buy_nums'] >= $max_free_num) {
                        $goods_order_staff[] = $sku_num['staff_id'];
                    }
                }
            }
            $del_staff_id = array_values(array_unique(array_filter(array_merge($res, $goods_order_staff))));
            if (!empty($del_staff_id)) {
                $staff_ids      = implode(',', $del_staff_id);
                $db             = $this->getDI()->get('db_backyard');
                $update_success = $db->updateAsDict(
                    (new InteriorBatchApplyProductModel)->getSource(),
                    [
                        'is_deleted' => GlobalEnums::IS_DELETED,
                        'updated_at' => date('Y-m-d H:i:s', time()),
                    ],
                    [
                        'conditions' => "staff_id IN ($staff_ids) and apply_id = " . $id,
                    ]
                );
                if (!$update_success) {
                    throw new BusinessException('员工商城-批量下单工服确认-核对-详细信息-一建清理失败', ErrCode::$UPDATE_ASSET_DATA_ERROR);
                }
            }
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code    = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning('员工商城-批量下单工服确认-核对-详细信息-一建清理异常: ' . $e->getMessage());
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('员工商城-批量下单工服确认-核对-详细信息-一建清理错误: ' . $e->getMessage());
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
        ];
    }


    /**
     * 读取上传文件内容
     * @param $file
     * @return array
     **@throws ValidationException
     */

    public function openFileData($file)
    {
        $file      = $file[0];
        $extension = $file->getExtension();
        if (!in_array($extension, ['xlsx'])) {
            throw new ValidationException(static::$t->_('goods_batch_check_import_err_extension'),
                ErrCode::$VALIDATE_ERROR);
        }
        $config = ['path' => ''];
        $excel  = new \Vtiful\Kernel\Excel($config);
        // 读取文件
        $excel_data = $excel->openFile($file->getTempName())
            ->openSheet()
            ->setSkipRows(1)
            ->getSheetData();
        if (empty($excel_data) || empty($excel_data[0])) {
            throw new ValidationException(static::$t->_('goods_batch_check_import_not_empty'),
                ErrCode::$VALIDATE_ERROR);
        }
        return $excel_data;
    }


    /**
     * 员工商城-批量下单工服-新建-详细信息-覆盖导入
     * @param array $params 订单编号
     * @param array $data 上传的数据
     * @return array
     */
    public function coverImport(array $params, array $data)
    {
        $code                      = ErrCode::$SUCCESS;
        $message                   = '';
        $res['error_file']         = '';
        $res['interior_goods_arr'] = [];
        $res                       = $this->importStaffHandle($data);
        $staff_arr                 = $res['staff_arr'];     //获取上传表格里面用户id
        $department_ids            = $res['department_ids'];//自动下单工服部门ID
        $job_ids                   = $res['job_ids'];       //自动下单工服职位ID
        $goods_buy_nums_arr        = $res['goods_arr'];     //查询已购商品数据
        $max_free_num              = $res['max_free_num'];  //最大免费数量
        $barcode_arr               = $res['barcode_arr'];   //获取上传表格里面barcode对应的数据
        $store_arr                 = $res['store_arr'];     //用户的网点数据集合
        $goods_ids                 = $res['goods_all'];     //商品免费权限网点类型集合
        try {
            $detail     = [];
            $goods_name = 'goods_name_' . static::getLanguage();
            foreach ($data as $k => $v) {
                $msg         = [];
                $staff       = $staff_arr[$v[2]];
                $max_buy_num = $max_free_num - $goods_buy_nums_arr[$v[2]]['buy_nums'];
                //工号校验
                if (empty($v[2])) {
                    //工号不能为空
                    $msg[] = self::$t->_('goods_batch_check_import_staff_err');
                } elseif ($staff['state'] != StaffInfoEnums::STAFF_STATE_IN || $staff['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES) {
                    //员工非在职
                    $msg[] = self::$t->_('goods_batch_check_import_job_status_err');
                } elseif (!in_array($staff['node_department_id'], $department_ids)) {
                    //员工所属部门非批量下单工服部门
                    $msg[] = self::$t->_('goods_batch_check_import_department_err');
                } elseif (
                    ($params['cost_store_type'] == Enums::PAYMENT_COST_STORE_TYPE_01 && $staff['sys_store_id'] != Enums::HEAD_OFFICE_STORE_FLAG)
                    ||
                    ($params['cost_store_type'] == Enums::PAYMENT_COST_STORE_TYPE_02 && $staff['sys_store_id'] == Enums::HEAD_OFFICE_STORE_FLAG)
                ) { //1 :总部 2:网点
                    //所属网点非批量下单网点
                    $msg[] = self::$t->_('goods_batch_check_import_store_err');
                } elseif (!in_array($staff['job_title'], $job_ids)) {
                    //员工职位不属于下单范围
                    $msg[] = self::$t->_('goods_batch_check_import_job_err');
                } elseif ($max_buy_num <= 0 || $max_buy_num < $v[11]) {
                    //员工最多允许购买2件免费工服
                    $msg[] = self::$t->_('goods_batch_check_import_max_buy_limit', ['buy_num' => $max_buy_num]);
                }
                $goods_arr = $this->getDepartmentByStoreGoodsList(['sys_store_id'       => $staff['sys_store_id'],
                                                                   'node_department_id' => $staff['node_department_id'],
                                                                   'staff_info_id'      => $staff['staff_info_id'],
                ]);
                $goods_arr = array_column($goods_arr['data'], 'goods_id');
                //商品编码校验
                $barcode = $v[10];
                if (empty($barcode)) {
                    //商品编码不允许为空
                    $msg[] = self::$t->_('goods_batch_check_import_not_null_barcode');
                } elseif (!in_array($barcode, array_keys($barcode_arr))) {
                    //商品编码不存在
                    $msg[] = self::$t->_('goods_batch_check_import_not_barcode');
                } elseif ($barcode_arr[$barcode]['status'] == 0) {
                    //商品编码非在售
                    $msg[] = self::$t->_('goods_batch_check_import_goods_onlie_err');
                } elseif (!in_array($barcode_arr[$barcode]['goods_id'], $goods_arr)) {
                    //商品超出免费购买权限
                    $msg[] = self::$t->_('goods_batch_check_import_goods_buy_err');
                }
                //下单数量校验
                $buy_num = $v[11];
                if (empty($buy_num)) {
                    //下单数量不允许为空
                    $msg[] = self::$t->_('goods_batch_check_import_null_buy_num');
                } elseif ($buy_num <= 0 || is_float($buy_num) || $max_buy_num < $buy_num) {
                    //下单数量不符合规范
                    $msg[] = self::$t->_('goods_batch_check_import_buy_num_err');
                }
                $data[$k][] = implode(";", $msg);
                //处理需要返回详情显示的数据
                $detail[$k]['grant_staff_name']   = $staff['name'];
                $detail[$k]['staff_info_id']      = $staff['staff_info_id'];
                $detail[$k]['hire_date']          = date('Y-m-d', strtotime($staff['hire_date']));
                $detail[$k]['state']              = $staff['state'];
                $detail[$k]['sys_store_id']       = $staff['sys_store_id'];
                $detail[$k]['wait_leave_state']   = $staff['wait_leave_state'];
                $detail[$k]['manage_region']      = $store_arr[$staff['sys_store_id']]['manage_region'];
                $detail[$k]['manage_piece']       = $store_arr[$staff['sys_store_id']]['manage_piece'];
                $detail[$k]['sys_store_name']     = $staff['sys_store_id'] == Enums::HEAD_OFFICE_STORE_FLAG ? Enums::PAYMENT_HEADER_STORE_NAME : $store_arr[$staff['sys_store_id']]['name'];
                $detail[$k]['goods_name']         = $barcode_arr[$barcode][$goods_name];
                $detail[$k]['goods_id']           = $barcode_arr[$barcode]['goods_id'];
                $detail[$k]['barcode']            = $barcode;
                $detail[$k]['size']               = $barcode_arr[$barcode]['size'];
                $detail[$k]['purchased_num']      = $goods_buy_nums_arr[$v[2]]['buy_nums'] ?? 0;
                $detail[$k]['buy_num']            = $buy_num;
                $detail[$k]['node_department_id'] = $staff['node_department_id'];
            }

            if (!empty(array_unique(array_filter(array_column($data, 13))))) {
                $msg    = self::$t->_('interior_goods_msg');//错误原因
                $header = $this->getBatchInfoHeader();
                //在最后一列增加原因标头
                array_push($header, $msg);
                $fileName          = 'shop_goods_batch_info_error_export' . date('YmdHis"');
                $res['error_file'] = $this->exportExcel($header, $data, $fileName)['data'];
            } else {
                //如果校验全部成功开始处理数据
                $goods_size           = GoodsSizeService::getInstance();
                $manage_region_ids    = array_values(array_unique(array_filter(array_column($detail,
                    'manage_region'))));
                $manage_piece_ids     = array_values(array_unique(array_filter(array_column($detail, 'manage_piece'))));
                $sys_manage_region    = $this->getManageRegion($manage_region_ids);
                $sys_manage_piece     = $this->getManagePiece($manage_piece_ids);
                $orders_goods_sku_num = $this->getInteriorOrdersGoodsSkuNum(array_column($detail, 'staff_info_id'));
                //最大数量限制
                $max_free_num = (new SettingEnvModel())->getValByCode('interior_free_limit_num');

                foreach ($detail as &$info) {
                    $orders_goods_arr                  = !empty($orders_goods_sku_num[$info['staff_info_id']]) ? $orders_goods_sku_num[$info['staff_info_id']]['buy_nums'] : 0;
                    $info['staff_id']                  = $info['staff_info_id'];
                    $max_residue_purchased_num         = $max_free_num - ($orders_goods_arr ?? 0);
                    $max_residue_purchased_num         = $max_residue_purchased_num <= 0 ? 0 : $max_residue_purchased_num;
                    $goods                             = $goods_ids[$info['goods_id']] ?? [];
                    $info['job_state']                 = $goods_size->staffJobState([
                        'state'            => $info['state'],
                        'wait_leave_state' => $info['wait_leave_state'],
                    ]);//在职状态
                    $info['manage_region_name']        = $sys_manage_region[$info['manage_region']] ?? '';
                    $info['manage_piece_name']         = $sys_manage_piece[$info['manage_piece']] ?? '';
                    $info['max_residue_purchased_num'] = $max_residue_purchased_num;//最大可以输入的值
                    $info['interior_goods_arr']        = [
                        [
                            'goods_name'     => $goods[0]['goods_name_zh'] ?? '',
                            'goods_id'       => $goods[0]['goods_id'] ?? '',
                            'goods_size_arr' => $goods,
                        ],
                    ];
                }
                $res['interior_goods_arr'] = $detail;
            }
        } catch (\Exception $e) {
            $code    = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
            $this->logger->error('shop_goods_batch_info_error_export_err' . $message);
        }

        return ['code' => $code, 'message' => $code == ErrCode::$SUCCESS ? 'success' : $message, 'data' => $res];
    }


    /**
     * 对导入的数据做基本信息查询
     * @param array $data
     **/

    public function importStaffHandle(array $data)
    {
        //获取上传表格里面用户id
        $staff_ids = array_values(array_unique(array_column($data, 2)));
        $staff_arr = (new HrStaffRepository())->getStaffListFormalArr($staff_ids);
        //自动下单工服部门ID
        $department_ids = (new SettingEnvModel())->getValByCode('auto_interior_orders_department_ids');
        $department_ids = explode(',', $department_ids);

        //自动下单工服职位ID
        $job_ids = (new SettingEnvModel())->getValByCode('auto_interior_orders_job_ids');
        $job_ids = explode(',', $job_ids);
        //查询商品数据
        $goods_arr = $this->getInteriorOrdersGoodsSkuNum($staff_ids);

        $max_free_num = (new SettingEnvModel())->getValByCode('interior_free_limit_num');

        //获取上传表格里面barcode对应的
        $barcode_arr = $this->getBarcodeInteriorGoodsAll(array_values(array_unique(array_column($data, 10))));

        $goods_id_arr = $this->getGoodsInteriorGoodsAll(array_values(array_unique(array_column($barcode_arr,
            'goods_id'))));
        //用户的网点数据集合
        $store_ids = array_values(array_unique(array_column($staff_arr, 'sys_store_id')));
        $store_arr = (new StoreRepository())->getStoreListByIds($store_ids);
        //商品免费权限网点类型集合
        $goods_all = [];
        foreach ($goods_id_arr as $goods) {
            $goods_all[$goods['goods_id']][] = $goods;
        }

        $res['staff_arr']      = $staff_arr;
        $res['department_ids'] = $department_ids;
        $res['job_ids']        = $job_ids;
        $res['goods_arr']      = $goods_arr;
        $res['max_free_num']   = $max_free_num;
        $res['barcode_arr']    = $barcode_arr;
        $res['store_arr']      = $store_arr;
        $res['goods_all']      = $goods_all;
        return $res;
    }


    /**
     * 根据barcode 返回商品对应的数据
     * @param array $barcode 集合
     * @return array
     */

    public function getBarcodeInteriorGoodsAll(array $barcode)
    {
        if (empty($barcode)) {
            return [];
        }
        $builder = $this->modelsManager->createBuilder();
        $builder->columns(['igs.goods_id, igs.goods_sku_code, igs.attr_1 as size,ig.size_img_path, ig.goods_name_zh, ig.goods_name_th, ig.goods_name_en, igs.status']);
        $builder->from(['igs' => InteriorGoodsSkuModel::class]);
        $builder->leftJoin(InteriorGoodsModel::class, 'igs.goods_id = ig.id', 'ig');
        $builder->inWhere('igs.goods_sku_code', $barcode);
        return array_column($builder->getQuery()->execute()->toArray(), null, 'goods_sku_code');
    }


    /**
     * 根据商品id 获取商品下的免费权限的网点类型
     * @param array $goods_ids 商品id集合
     * @return array
     */

    public function getGoodsStoreCateRelArr(array $goods_ids)
    {
        if (empty($goods_ids)) {
            return [];
        }
        $goods_store_cate_rel = InteriorGoodsStoreCateRelModel::find([
            'conditions' => 'goods_id in ({goods_ids:array}) and permission_type = :permission_type: ',
            'bind'       => ['goods_ids'       => $goods_ids,
                             'permission_type' => ShopEnums::INTERIOR_GOODS_PURCHASE_TYPE_FREE,
            ],
            'columns'    => [
                'id',
                'goods_id',
                'store_cate_id',
            ],
        ])->toArray();

        $res = [];
        foreach ($goods_store_cate_rel as $cate_rel) {
            $res[$cate_rel['goods_id']][] = $cate_rel['store_cate_id'];
        }

        return $res;
    }


    /**
     * 根据员工所属网点、部门获取商品数据
     * @param array $params 数据条件
     * @return array
     */
    public function getDepartmentByStoreGoodsList(array $params)
    {
        $code    = ErrCode::$SUCCESS;
        $message = '';
        $groups  = [];
        //员工商城非免费工服大区
        $store_manage_region = (new SettingEnvModel())->getValByCode('shop_free_manage_region_ids');
        $store_arr           = $params['sys_store_id'] == Enums::HEAD_OFFICE_STORE_FLAG ? ['category' => Enums::HEAD_OFFICE_STORE_FLAG] : (new StoreRepository())->getStoreDetail($params['sys_store_id']);
        //非免费工服大区为空情况需要继续查询网点类型数据
        $store_manage_region_arr = explode(',', $store_manage_region);
        if (!in_array($store_arr['manage_region'], $store_manage_region_arr)) {
            $cate_rel_arr = [];
            if (!empty($store_arr['category'])) {
                $store_cate_ids = [$store_arr['category']];
                $cate_rel_arr   = InteriorGoodsStoreCateRelModel::find([
                    'conditions' => 'store_cate_id in ({store_cate_id:array}) and  permission_type = :permission_type:',
                    'bind'       => [
                        'store_cate_id'   => $store_cate_ids,
                        'permission_type' => ShopEnums::INTERIOR_GOODS_PURCHASE_TYPE_FREE,
                    ],
                    'columns'    => [
                        'goods_id',
                    ],
                ])->toArray();
            }
            $department_permission_arr = [];
            if (!empty($params['node_department_id'])) {
                //当前部门的上级部门
                $department              = (new DepartmentRepository())->getDepartmentDetail($params['node_department_id']);
                $ancestry_department_arr = explode('/', $department['ancestry_v3']);
                array_pop($ancestry_department_arr);
                $department_permission_arr = InteriorGoodsManageDepartmentPermissionModel::find([
                    'conditions' => '(department_id in ({department_ids:array}) and permission_type = :permission_type: and  is_include_sub = :is_include_sub:) or (department_id = :department_id: and permission_type = :permission_type:)',
                    'bind'       => [
                        'department_ids'  => $ancestry_department_arr,
                        'permission_type' => ShopEnums::INTERIOR_GOODS_PURCHASE_TYPE_FREE,
                        'is_include_sub'  => Enums\SettingEnums::IS_INCLUDE_SUB,
                        'department_id'   => $params['node_department_id'],
                    ],
                    'columns'    => [
                        'goods_id',
                    ],
                ])->toArray();
            }
            $goods_ids = array_values(array_unique(array_column(array_merge($cate_rel_arr, $department_permission_arr),
                'goods_id')));

            $goods_intersect = array_intersect(array_column($cate_rel_arr, 'goods_id'),
                array_column($department_permission_arr, 'goods_id'));


            $goods_diff     = array_values(array_diff($goods_ids, $goods_intersect));
            $diff_goods_ids = [];
            if (!empty($goods_diff)) {
                $diff_cate_rel_goods              = InteriorGoodsStoreCateRelModel::find([
                    'conditions' => 'goods_id in ({goods_id:array}) and  permission_type = :permission_type:',
                    'bind'       => [
                        'goods_id'        => $goods_diff,
                        'permission_type' => ShopEnums::INTERIOR_GOODS_PURCHASE_TYPE_FREE,
                    ],
                    'columns'    => [
                        'goods_id',
                    ],
                ])->toArray();
                $diff_cate_rel_goods              = array_column($diff_cate_rel_goods, 'goods_id');
                $diff_department_permission_goods = InteriorGoodsManageDepartmentPermissionModel::find([
                    'conditions' => 'goods_id in ({goods_id:array}) and  permission_type = :permission_type:',
                    'bind'       => [
                        'goods_id'        => $goods_diff,
                        'permission_type' => ShopEnums::INTERIOR_GOODS_PURCHASE_TYPE_FREE,
                    ],
                    'columns'    => [
                        'goods_id',
                    ],
                ])->toArray();
                $diff_department_permission_goods = array_column($diff_department_permission_goods, 'goods_id');
                foreach ($goods_diff as $goods_id) {
                    if (
                        (in_array($goods_id, $diff_cate_rel_goods) && !in_array($goods_id,
                                $diff_department_permission_goods))
                        ||
                        ((in_array($goods_id, $diff_department_permission_goods) && !in_array($goods_id,
                                $diff_cate_rel_goods)))) {
                        $diff_goods_ids[] = $goods_id;
                    }
                }
            }
            $goods_ids = array_merge($goods_intersect, $diff_goods_ids);
            $groups    = $data = [];
            if (!empty($goods_ids)) {
                $goods_arr = GoodsSizeService::getInstance()->getGoodsSizeAll($goods_ids);
                if (!empty($goods_arr)) {
                    $goods_sku_num = $this->getInteriorOrdersGoodsSkuNum([$params['staff_info_id']]);
                    $goods_name    = 'goods_name_' . static::getLanguage();
                    $max_free_num  = (new SettingEnvModel())->getValByCode('interior_free_limit_num');

                    $buy_nums = $goods_sku_num[$params['staff_info_id']]['buy_nums'] ?? 0;
                    foreach ($goods_arr as $goods) {
                        $key = $goods['goods_id'];
                        if (!isset($groups[$key])) {
                            $groups[$key]['goods_id']         = $goods['goods_id'];
                            $groups[$key]['barcode']          = $goods['barcode'];
                            $groups[$key]['goods_name']       = $goods[$goods_name];
                            $res['barcode']                   = $goods['barcode'];
                            $res['size']                      = $goods['size'];
                            $res['goods_id']                  = $goods['goods_id'];
                            $res['max_residue_purchased_num'] = $max_free_num - $buy_nums;
                            $res['purchased_num']             = $goods_sku_num[$params['staff_info_id']]['buy_nums'] ?? 0;
                            $groups[$key]['goods_size_arr'][] = $res;
                        } else {
                            $res['barcode']                   = $goods['barcode'];
                            $res['size']                      = $goods['size'];
                            $res['goods_id']                  = $goods['goods_id'];
                            $res['max_residue_purchased_num'] = $max_free_num - $buy_nums;
                            $res['purchased_num']             = $buy_nums;
                            $groups[$key]['goods_size_arr'][] = $res;
                        }
                    }
                }
            }
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => array_values($groups),
        ];
    }


    /**
     * 根据用户id返回用户最近下单的尺码数据
     * @param array $staff_id 集合
     * @return array
     */

    public function getLastInteriorGoodsSizeList(array $staff_id)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('iogs.goods_id, iogs.attr_1, io.staff_id, iogs.goods_sku_code, iogs.id');
        $builder->from(['iogs' => InteriorOrdersGoodsSkuModel::class]);
        $builder->leftJoin(InteriorOrdersModel::class, 'iogs.order_code = io.order_code', 'io');
        $builder->where('iogs.is_free = :is_free: and  io.goods_type = :goods_type: ',
            ['is_free' => ShopEnums::FREE_BUY_STATUS_YES, 'goods_type' => ShopEnums::GOODS_TYPE_WORK_CLOTHES]);
        $builder->notInWhere('io.order_status',
            [ShopEnums::ORDER_STATUS_CUSTOMER_CANCEL_CODE, ShopEnums::ORDER_STATUS_SYSTEM_CANCEL_CODE]);
        $builder->inWhere('io.staff_id', $staff_id);
        $builder->groupBy('iogs.attr_1, iogs.goods_id, io.staff_id');
        $builder->orderBy('iogs.id desc');
        $goods_sku_arr = $builder->getQuery()->execute()->toArray();
        $res           = [];
        if (!empty($goods_sku_arr)) {
            foreach ($goods_sku_arr as $goods_sku) {
                $key = $goods_sku['goods_id'] . '_' . $goods_sku['staff_id'];
                if (empty($groups[$key])) {
                    $res[$goods_sku['staff_id']][$goods_sku['goods_id']] = $goods_sku;
                    $groups[$key]                                        = 1;
                }
            }
        }
        return $res;
    }


    /**
     * 根据barcode 返回商品对应的goods_id的全部的数据
     * @param array $goods_ids 集合
     * @return array
     */

    public function getGoodsInteriorGoodsAll(array $goods_ids)
    {
        if (empty($goods_ids)) {
            return [];
        }
        $builder = $this->modelsManager->createBuilder();
        $builder->columns(['igs.goods_id, igs.goods_sku_code as barcode , igs.attr_1 as size, ig.goods_name_zh, ig.goods_name_th, ig.goods_name_en, igs.status']);
        $builder->from(['igs' => InteriorGoodsSkuModel::class]);
        $builder->leftJoin(InteriorGoodsModel::class, 'igs.goods_id = ig.id', 'ig');
        $builder->inWhere('igs.goods_id', $goods_ids);
        $builder->andWhere('igs.status = :status: and ig.status =:ig_status: ',
            ['status' => ShopEnums::GOODS_STATUS_ON_SALE, 'ig_status' => ShopEnums::GOODS_STATUS_ON_SALE]);
        return $builder->getQuery()->execute()->toArray();
    }


    /**
     * 员工商城-批量下单工服-核对-导出（附录2标头）
     * @param array $params 详细信息-导出条件
     * @return array
     */
    public function getCheckExportInfoList($params)
    {
        $code    = ErrCode::$SUCCESS;
        $message = '';
        $data    = $items = [];
        try {
            set_time_limit(0);
            ini_set('memory_limit', '512M');
            $apply        = $this->getBatchApplyInfo($params['id'])->toArray();
            $product_list = $this->getApplyProduct($params['id'], $apply['status']);
            $items        = [];
            if (!empty($product_list)) {
                $results    = $goods_size_res = [];
                $goods_name = 'goods_name_' . static::getLanguage();
                foreach ($product_list as $key => $staff) {
                    $results[] = [
                        $key + 1,
                        $staff['grant_staff_name'],
                        $staff['staff_id'],
                        date('Y-m-d', strtotime($staff['hire_date'])),
                        $staff['job_state'],
                        $staff['manage_region_name'],
                        $staff['manage_piece_name'],
                        $staff['sys_store_name'],
                        $staff[$goods_name] ?? '',
                        $staff['size'] ?? '',
                        $staff['barcode'] ?? '',
                        $staff['buy_num'] ?? '',
                        $staff['purchased_num'] ?? '',
                    ];
                }
                //部门相同所以找到商品也相同
                $goods_size_arr = $this->getGoodsSkuAll();
                foreach ($goods_size_arr as $goods_size) {
                    $goods_size_res[] = [
                        $goods_size['goods_name'],
                        $goods_size['size'],
                        $goods_size['barcode'],
                    ];
                }
                $items = [$results, $goods_size_res];
            }
            $header = [
                $this->getBatchInfoHeader(),
                [
                    self::$t['interior_goods_shop_goods_name'],   //商品名称
                    self::$t['interior_goods_shop_goods_size'],   //尺码
                    self::$t['interior_goods_shop_goods_barcode'],//商品编码
                ],
            ];

            $fileName = 'shop_goods_batch_info_export_' . date('YmdHis"');
            $data     = $this->exportManyExcel($header, $items, $fileName);
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = $e->getMessage();
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
            $this->logger->warning('shop_goods_batch_export_failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data['data'],
        ];
    }


    /**
     * 返回商品对应的goods_id的全部的数据
     * @return array
     */
    public function getGoodsSkuAll()
    {
        $goods_name = 'ig.goods_name_' . static::getLanguage();
        $builder    = $this->modelsManager->createBuilder();
        $builder->columns(['igs.goods_id, igs.goods_sku_code as barcode , igs.attr_1 as size, ' . $goods_name . ' as goods_name']);
        $builder->from(['igs' => InteriorGoodsSkuModel::class]);
        $builder->leftJoin(InteriorGoodsModel::class, 'igs.goods_id = ig.id', 'ig');
        $builder->andWhere('igs.status = :status: and ig.status =:ig_status: and ig.goods_type = :goods_type: ', [
            'status'     => ShopEnums::GOODS_STATUS_ON_SALE,
            'ig_status'  => ShopEnums::GOODS_STATUS_ON_SALE,
            'goods_type' => ShopEnums::GOODS_TYPE_WORK_CLOTHES,
        ]);
        return $builder->getQuery()->execute()->toArray();
    }


}
