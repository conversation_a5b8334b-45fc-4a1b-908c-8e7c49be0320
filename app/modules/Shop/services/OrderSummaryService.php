<?php
/**
 * Created by PhpStorm.
 * Date: 2022/02/28
 * Time: 11:21
 */

namespace App\Modules\Shop\Services;

use App\Library\ApiClient;
use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\ShopEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Common\Services\StoreService;
use App\Modules\Organization\Models\SysDepartmentModel;
use App\Modules\Shop\Models\InteriorOrders;
use App\Modules\Shop\Models\InteriorOrdersGoodsSku;
use App\Modules\Shop\Models\InteriorOrdersRefundRecordModel;
use App\Modules\User\Models\StaffInfoModel;
use App\Modules\Transfer\Models\SysAttachmentModel;
use App\Repository\backyard\BankListRepository;
use App\Repository\HrStaffRepository;
use GuzzleHttp\Exception\GuzzleException;

class OrderSummaryService extends BaseService
{
    private static $instance;

    private function __construct()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    // 员工商城-订单汇总列表-非必要的筛选条件
    public static $not_must_params = [
        'order_status',
        'staff_id',
        'pay_method',
        'order_type',
        'fund_status',
        'order_start_date',
        'order_end_date',
        'canceled_at_start',
        'canceled_at_end',
        'start_send_at',
        'end_send_at',
        'goods_type',
        'out_sn',
        'payment_voucher_status',
        'payment_voucher_audit_status',

    ];

    // 员工商城-订单汇总列表-搜索条件
    public static $validate_list_search = [
        'order_code'                   => 'StrLenLe:20|>>>:order_code error',
        //订单编号
        'order_status'                 => 'Arr|ArrLenGeLe:0,8|>>>:order_status error',
        //订单状态
        'order_status[*]'              => 'IntGe:1|>>>:order_status error',
        //订单状态
        'staff_id'                     => 'IntGe:1|>>>:staff_id error',
        //员工工号
        'pay_method'                   => 'IntIn:' . ShopEnums::PAY_METHOD_WAGE_DEDUCTION . ',' . ShopEnums::PAY_METHOD_FLASH_PAY . ',' . ShopEnums::PAY_METHOD_OFFLINE_PAY . '|>>>:pay_method error',
        //支付方式
        'order_start_date'             => 'DateTime|>>>:order_start_date error',
        //下单开始时间
        'order_end_date'               => 'DateTime|>>>:order_end_date error',
        //下单结束时间
        'canceled_at_start'            => 'DateTime|>>>:canceled_at_start error',
        //取消订单开始时间
        'canceled_at_end'              => 'DateTime|>>>:canceled_at_end error',
        //取消订单结束时间
        'start_send_at'                => 'DateTime|>>>:start_send_at error',
        //发货开始时间
        'end_send_at'                  => 'DateTime|>>>:end_send_at error',
        //发货结束时间
        'order_type'                   => 'IntIn:' . ShopEnums::ORDER_TYPE_FREE . ',' . ShopEnums::ORDER_TYPE_PAY . '|>>>:order_type error',
        //订单类型
        'fund_status'                  => 'IntIn:' . ShopEnums::FUND_STATUS_REFUNDING . ',' . ShopEnums::FUND_STATUS_DONE . '|>>>:fund_status error',
        //款项状态类型
        'pageNum'                      => 'IntGe:1|>>>:page error',
        //当前页码
        'pageSize'                     => 'IntGe:1|>>>:page_size error',
        //每页条数
        'goods_type'                   => 'IntIn:' . ShopEnums::GOODS_TYPE_WORK_CLOTHES . ',' . ShopEnums::GOODS_TYPE_UNCLAIMED_PIECE . '|>>>:params error goods_type',
        //商品类型
        'out_sn'                       => 'StrLenGeLe:1,50|>>>:out_sn error',
        //出库单号
        'payment_voucher_status'       => 'IntIn:' . ShopEnums::GOODS_PAYMENT_VOUCHER_STATUS_ING . ',' . ShopEnums::GOODS_PAYMENT_VOUCHER_STATUS_ABNORMAL . ',' . ShopEnums::GOODS_PAYMENT_VOUCHER_STATUS_UPLOADED . '|>>>:payment_voucher_status error',
        //支付凭证状态
        'payment_voucher_audit_status' => 'Arr|>>>:payment_voucher_audit_status error',
        //支付凭证审核状态

    ];

    // 员工商城-订单编号
    public static $validate_order_code = [
        'order_code' => 'Required|StrLen:20|>>>:order_code error', //订单编号
    ];

    // 员工商城-批量确定退款
    public static $validate_batch_order_code = [
        'batch_order_code' => 'Required|ArrLenGe:1|>>>:batch_order_code error', //订单编号数组
        'is_not_refund'    => 'Required|IntIn:0,1|>>>:is_not_refund error',     //0-已退; 1-无需退
    ];

    // 员工商城-退款
    public static $validate_refund_params = [
        'fund_amount' => 'Required|FloatGtLe:0,99999999.99|>>>:fund_amount error',
        //退款金额
        'fund_status' => 'Required|IntIn:' . ShopEnums::FUND_STATUS_REFUNDING . ',' . ShopEnums::FUND_STATUS_DONE . '|>>>:fund_status error',
        //款项状态类型
        'fund_at'     => 'Required|Date|>>>:fund_at error',
        //退款日期
        'fund_code'   => 'StrLenLe:100|>>>:fund_code error',
        //退款编号
        'fund_remark' => 'StrLenLe:500|>>>:fund_remark error',
        //退款金额

        'attachment_arr'                => 'ArrLenGeLe:0,5',
        //附件信息
        'attachment_arr[*].bucket_name' => "StrLenGeLe:0,63",
        'attachment_arr[*].object_key'  => "StrLenGeLe:0,100",
    ];

    /**
     * 订单汇总列表
     * @param array $condition 查询参数组
     * @return array
     */
    public function getList($condition)
    {
        $page_size = empty($condition['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $condition['pageSize'];
        $page_num  = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['pageNum'];
        $offset    = $page_size * ($page_num - GlobalEnums::DEFAULT_PAGE_NUM);

        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        $items   = [];

        $is_export = isset($condition['is_export']) && $condition['is_export'] == 1;
        if ($is_export) {
            ini_set('memory_limit', '2048M');
        }
        $lang = static::$language;
        try {
            $count = $this->getListCount($condition);
            if ($count > 0) {
                if ($is_export && $count > ShopEnums::ORDER_SUMMARY_DOWNLOAD_LIMIT) {
                    throw new ValidationException(static::$t->_('inventory_check_asset_download_limit'));
                }
                $builder = $this->modelsManager->createBuilder();
                $builder->columns([
                    'main.order_code',
                    'main.staff_id',
                    'main.staff_name',
                    'main.order_status',
                    'main.pay_method',
                    'main.pay_amount',
                    'main.submit_at',
                    'main.send_at',
                    'main.flash_pay_code',
                    'main.fund_status',
                    'main.fund_code',
                    'main.fund_at',
                    'main.fund_remark',
                    'main.staff_store_id',
                    'main.node_department_id',
                    'main.flash_pay_at',
                    'main.goods_type',
                    'main.out_sn',
                    'main.payment_voucher_status',
                    'main.payment_voucher',
                    'main.canceled_at',
                    'main.id',
                    'main.payment_voucher_at',
                    'main.created_at',
                    'main.payment_voucher_audit_status',
                    'main.payment_voucher_check_staff_id',
                    'main.payment_voucher_reason',
                    'main.payment_voucher_audit_at',
                ]);
                $builder->from(['main' => InteriorOrders::class]);
                $builder = $this->getCondition($builder, $condition);
                $builder->orderBy('main.submit_at DESC');
                if (!$is_export) {
                    $builder->limit($page_size, $offset);
                } else {
                    $builder->limit(ShopEnums::ORDER_SUMMARY_DOWNLOAD_LIMIT);
                }
                $items = $builder->getQuery()->execute()->toArray();
            }
            $items = $this->handleData($items, $lang, $is_export);
            if ($is_export) {
                $excel_data = $this->exportOrder($items);
                if (empty($excel_data['data'])) {
                    $data    = '';
                    $code    = ErrCode::$BUSINESS_ERROR;
                    $message = 'Excel export failed';
                } else {
                    $data = !empty($excel_data['data']) ? $excel_data['data'] : '';
                }
            } else {
                $data = [
                    'items'      => $items,
                    'pagination' => [
                        'current_page' => $page_num,
                        'per_page'     => $page_size,
                        'total_count'  => (int)$count,
                    ],
                ];
            }
        } catch (ValidationException $e) {
            $code    = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage(); //. $e->getTraceAsString();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('员工商城 - 订单汇总列表异常: ' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 员工商城-订单汇总列表-记录数
     * @param array $condition 查询条件组
     * @return mixed
     */
    public function getListCount(array $condition)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(main.id) as total');
        $builder->from(['main' => InteriorOrders::class]);
        $builder    = $this->getCondition($builder, $condition);
        $total_info = $builder->getQuery()->getSingleResult();
        return intval($total_info->total);
    }

    /**
     * 组装列表搜索条件
     * @param object $builder 查询器对象
     * @param array $condition ['order_code'=>'订单编号','order_status'=>'订单状态','staff_id'=>'员工工号','pay_method'=>'支付方式','order_start_date'=>'下单时间起始时间',
     * 'order_end_date'=>'下单时间截止时间', 'start_send_at'=>'发货时间起始时间', 'end_send_at'=>'发货时间截止时间', 'order_type'=>'订单类型', 'fund_type'=>'款项类型']查询条件组
     * @return object
     */
    private function getCondition(object $builder, array $condition)
    {
        $order_code             = $condition['order_code'] ?? '';           //订单编号
        $order_status           = $condition['order_status'] ?? [];         //订单状态
        $staff_id               = $condition['staff_id'] ?? '';             //员工工号
        $pay_method             = $condition['pay_method'] ?? 0;            //支付方式
        $order_start_date       = $condition['order_start_date'] ?? '';     //下单时间起始时间
        $order_end_date         = $condition['order_end_date'] ?? '';       //下单时间截止时间
        $canceled_at_start      = $condition['canceled_at_start'] ?? '';    //取消订单时间起始时间
        $canceled_at_end        = $condition['canceled_at_end'] ?? '';      //取消订单时间截止时间
        $start_send_at          = $condition['start_send_at'] ?? '';        //发货时间起始时间
        $end_send_at            = $condition['end_send_at'] ?? '';          //发货时间截止时间
        $order_type             = $condition['order_type'] ?? 0;            //订单类型
        $fund_status            = $condition['fund_status'] ?? 0;           //款项类型
        $out_sn                 = $condition['out_sn'] ?? '';               //出库单号
        $goods_type             = $condition['goods_type'] ?? 0;            //商品类型
        $payment_voucher_status = $condition['payment_voucher_status'] ?? 0;//支付凭证状态

        $start_payment_voucher_at       = $condition['start_payment_voucher_at'] ?? '';      //上传支付凭证开始时间
        $end_payment_voucher_at         = $condition['end_payment_voucher_at'] ?? '';        //上传支付凭证结束时间
        $start_payment_voucher_audit_at = $condition['start_payment_voucher_audit_at'] ?? '';//核对开始时间
        $end_payment_voucher_audit_at   = $condition['end_payment_voucher_audit_at'] ?? '';  //核对结束时间
        $payment_voucher_audit_status   = $condition['payment_voucher_audit_status'] ?? [];  //支付凭证审核状态
        $payment_voucher_check_staff_id = $condition['grant_staff_id'] ?? 0;                 //核对人工号

        if (!empty($payment_voucher_audit_status)) {
            $builder->inWhere('main.payment_voucher_audit_status', $payment_voucher_audit_status);
        }
        if (!empty($pay_method)) {
            $builder->andWhere('main.pay_method = :pay_method:', ['pay_method' => $pay_method]);
        }

        //上传支付凭证时间
        if (!empty($start_payment_voucher_at) && !empty($end_payment_voucher_at)) {
            $builder->betweenWhere('main.payment_voucher_at', $start_payment_voucher_at, $end_payment_voucher_at);
        }

        //核对时间
        if (!empty($start_payment_voucher_audit_at) && !empty($end_payment_voucher_audit_at)) {
            $builder->betweenWhere('main.payment_voucher_audit_at', $start_payment_voucher_audit_at,
                $end_payment_voucher_audit_at);
        }

        if (!empty($order_code)) {
            $builder->andWhere('main.order_code = :order_code:', ['order_code' => $order_code]);
        }
        if (!empty($order_status)) {
            if (is_array($order_status)) {
                $builder->inWhere('main.order_status', $order_status);
            } else {
                $builder->andWhere('main.order_status = :order_status:', ['order_status' => $order_status]);
            }
        }
        if (!empty($staff_id)) {
            $builder->andWhere('main.staff_id = :staff_id:', ['staff_id' => $staff_id]);
        }
        if (!empty($pay_method)) {
            $builder->andWhere('main.pay_method = :pay_method:', ['pay_method' => $pay_method]);
        }
        if (!empty($order_start_date)) {
            $builder->andWhere('main.submit_at >= :start_date:', ['start_date' => $order_start_date]);
        }
        if (!empty($order_end_date)) {
            $builder->andWhere('main.submit_at <= :end_date:', ['end_date' => $order_end_date]);
        }
        if (!empty($canceled_at_start)) {
            $builder->andWhere('main.canceled_at >= :canceled_at_start:', ['canceled_at_start' => $canceled_at_start]);
        }
        if (!empty($canceled_at_end)) {
            $builder->andWhere('main.canceled_at <= :canceled_at_end:', ['canceled_at_end' => $canceled_at_end]);
        }
        if (!empty($start_send_at)) {
            $builder->andWhere('main.send_at >= :send_start_date:', ['send_start_date' => $start_send_at]);
        }
        if (!empty($end_send_at)) {
            $builder->andWhere('main.send_at <= :send_end_date:', ['send_end_date' => $end_send_at]);
        }
        if (!empty($order_type)) {
            $builder->andWhere('main.order_type = :order_type:', ['order_type' => $order_type]);
        }
        if (!empty($fund_status)) {
            $builder->andWhere('main.fund_status = :fund_status:', ['fund_status' => $fund_status]);
        }

        if (!empty($out_sn)) {
            $builder->andWhere('main.out_sn = :out_sn:', ['out_sn' => $out_sn]);
        }
        if (!empty($goods_type)) {
            $builder->andWhere('main.goods_type = :goods_type:', ['goods_type' => $condition['goods_type']]);
        }
        if (!empty($payment_voucher_status)) {
            $builder->andWhere('main.payment_voucher_status = :payment_voucher_status:',
                ['payment_voucher_status' => $payment_voucher_status]);
        }
        if (!empty($payment_voucher_check_staff_id)) {
            $builder->andWhere('main.payment_voucher_check_staff_id = :payment_voucher_check_staff_id:',
                ['payment_voucher_check_staff_id' => $payment_voucher_check_staff_id]);
        }
        return $builder;
    }

    /**
     * 格式化订单列表
     * @param array $items 订单列表
     * @param string $lang 当前语言包
     * @param bool $is_export
     * @return array
     */
    public function handleData($items, $lang, $is_export = false)
    {
        if (empty($items)) {
            return [];
        }
        $order_enums = ShopEnums::getOrderStatus($lang);
        $pay_enums   = ShopEnums::payMethod($lang);
        $fund_items  = ShopEnums::fundStatus($lang);

        //获取购买者用户列表
        $buyer_staff_ids     = array_column($items, 'staff_id');
        $hr_staff_repository = new HrStaffRepository();
        $staffs              = $hr_staff_repository->getStaffListByStaffIds($buyer_staff_ids);

        //获取支付凭证核对人列表
        $staff_ids = array_values(array_unique(array_filter(array_column($items, 'payment_voucher_check_staff_id'))));
        $staff_arr = [];
        if (!empty($staff_ids)) {
            $staff_arr = $hr_staff_repository->getStaffListByStaffIds($staff_ids);
        }

        //雇佣类型
        $hire_type_enum = EnumsService::getInstance()->getHireTypeEnum();//雇佣类型枚举
        // 导出的数据结构
        if ($is_export) {
            // 获取持卡人姓名
            $buyer_bank_account_map = $hr_staff_repository->getStaffInfoListByStaffIds($buyer_staff_ids,
                'BANK_NO_NAME');
            $buyer_bank_account_map = array_column($buyer_bank_account_map, 'bank_no_name', 'staff_info_id');

            // 银行名称
            $bank_type_map = BankListRepository::getInstance()->getAllList();
            $bank_type_map = array_column($bank_type_map, 'bank_name', 'bank_id');

            //获取所有的网点组
            $store_ids  = array_values(array_unique(array_column($items, 'staff_store_id')));
            $store_list = [];
            if ($store_ids) {
                $common_service = new StoreService();
                $store_list     = $common_service->getSysStoreListByCondition(['store_id' => $store_ids]);
                if ($store_list) {
                    $store_list = array_column($store_list, 'name', 'id');
                }
            }
            //获取所有的部门组
            $department_ids  = array_values(array_unique(array_column($items, 'node_department_id')));
            $department_list = [];
            if ($department_ids) {
                $department_list = SysDepartmentModel::find([
                    'columns'    => ['id', 'name'],
                    'conditions' => 'id in ({ids:array})',
                    'bind'       => ['ids' => $department_ids],
                ])->toArray();
                if ($department_list) {
                    $department_list = array_column($department_list, 'name', 'id');
                }
            }

            //获取当前国家币种
            $default_currency = EnumsService::getInstance()->getSysCurrencyInfo();
            $new_data         = $top = $middle = $bottom = [];
            $country          = get_country_code();

            foreach ($items as $key => $value) {
                // 下单人信息
                $buyer_info = $staffs[$value['staff_id']] ?? [];

                $top = [
                    $key + 1,            //序号
                    $value['order_code'],//订单编号
                    $value['out_sn'],
                    $value['flash_pay_code'],//pay交易号
                    empty($value['order_status']) && $value['goods_type'] == ShopEnums::GOODS_TYPE_UNCLAIMED_PIECE ? static::$t->_('wait_pay') : $order_enums[$value['order_status']],
                    $pay_enums[$value['pay_method']],
                    $fund_items[$value['fund_status']] ?? '',//款项状态
                    $value['payment_voucher_status'] ? static::$t->_(ShopEnums::$payment_voucher_status[$value['payment_voucher_status']]) : '',
                ];

                if ($country == GlobalEnums::PH_COUNTRY_CODE) {
                    $middle = [
                        !empty($value['payment_voucher_audit_status']) ? static::$t->_(ShopEnums::$payment_voucher_audit_status[$value['payment_voucher_audit_status']]) : '',
                        !empty($value['payment_voucher_check_staff_id']) ? $staff_arr[$value['payment_voucher_check_staff_id']]['name'] . '(' . $value['payment_voucher_check_staff_id'] . ')' : '',
                        !empty($value['payment_voucher_audit_at']) ? $value['payment_voucher_audit_at'] : '',
                        !empty($value['payment_voucher_reason']) ? $value['payment_voucher_reason'] : '',
                    ];
                }
                $bottom = [
                    $value['pay_amount'],
                    // 订单总额
                    $default_currency['symbol'],
                    //订单支付币种
                    $value['staff_id'],
                    //员工工号
                    $value['staff_name'],
                    $hire_type_enum[$staffs[$value['staff_id']]['hire_type']] ?? '',
                    $value['staff_store_id'] == Enums::HEAD_OFFICE_STORE_FLAG ? Enums::PAYMENT_HEADER_STORE_NAME : $store_list[$value['staff_store_id']] ?? '',
                    //所属网点
                    $department_list[$value['node_department_id']] ?? '',
                    //所属部门
                    $value['submit_at'],
                    //订单日期
                    $value['canceled_at'],
                    //取消订单时间
                    $value['flash_pay_at'],
                    //支付日期
                    $value['send_at'],
                    $value['payment_voucher'] ?? '',
                    $bank_type_map[$buyer_info['bank_type']] ?? '',
                    // 银行名称
                    $buyer_bank_account_map[$value['staff_id']] ?? '',
                    // 账户名称
                    $buyer_info['bank_no'] ?? '',
                    // 银行账号
                    $buyer_info['mobile'] ?? ''
                    // 员工手机号
                ];

                $new_data[] = array_merge($top, $middle, $bottom);
            }

            unset($items);
            return $new_data;
        } else {
            // 页面列表的数据结构
            $staff_state_enums = self::staffStateEnums();
            // 员工状态
            foreach ($items as &$value) {
                $value['fund_at']                           = substr($value['fund_at'], 0, 10);
                $value['staff_state']                       = $staff_state_enums[$staffs[$value['staff_id']]['state']] ?? '';
                $value['hire_type_text']                    = $hire_type_enum[$staffs[$value['staff_id']]['hire_type']] ?? '';
                $value['pay_method_text']                   = $pay_enums[$value['pay_method']];
                $value['order_status_text']                 = empty($value['order_status']) && $value['goods_type'] == ShopEnums::GOODS_TYPE_UNCLAIMED_PIECE ? static::$t->_('wait_pay') : $order_enums[$value['order_status']];
                $value['fund_status_text']                  = $fund_items[$value['fund_status']] ?? '';
                $value['goods_type_name']                   = static::$t->_(ShopEnums::$goods_type[$value['goods_type']]);
                $value['payment_voucher_check_staff_name']  = $staff_arr[$value['payment_voucher_check_staff_id']]['name'] ?? '';
                $value['payment_voucher_audit_status_name'] = !empty($value['payment_voucher_audit_status']) ? static::$t->_(ShopEnums::$payment_voucher_audit_status[$value['payment_voucher_audit_status']]) : '';
            }
            return $items;
        }
    }

    /**
     * 导出订单
     *
     * @param array $items 订单列表组
     * @return array
     * @throws BusinessException
     * @throws GuzzleException
     */
    public function exportOrder($items)
    {
        $header = [
            self::$t['global.no'],                       //序号
            self::$t['csr_field_order_code'],            //订单编号
            self::$t['csr_field_out_sn'],                //出库单号
            self::$t['csr_field_order_flash_pay_code'],  //pay交易号
            self::$t['csr_field_order_status'],          //订单状态
            self::$t['csr_field_pay_method'],            //支付方式
            self::$t['csr_field_order_fund_code'],       //款项状态
            self::$t['csr_field_payment_voucher_status'],//支付凭证状态
        ];

        $bottom = [
            self::$t['csr_field_order_pay_amount'],  //订单金额
            self::$t['csr_field_order_currency'],    //币种
            self::$t['csr_field_staff_id'],          //员工号
            self::$t['csr_field_staff_name'],        //员工姓名
            self::$t['hire_type'],                   //雇佣类型
            self::$t['csr_field_store_name'],        //网点名称
            self::$t['csr_field_department_name'],   //部门
            self::$t['csr_field_submit_time'],       //下单时间
            self::$t['csr_field_canceled_at'],       //取消订单时间
            self::$t['csr_field_flash_pay_at'],      //支付日期
            self::$t['csr_field_send_at'],           //发货时间
            self::$t['csr_field_payment_voucher'],   //支付凭证
            self::$t['csr_field_order_bank_name'],   //银行名称
            self::$t['csr_field_order_account_name'],//账户名称
            self::$t['csr_field_order_bank_account'],//银行账号
            self::$t['csr_field_order_staff_mobile'],//员工手机号
        ];

        $middle = [];
        if (get_country_code() == GlobalEnums::PH_COUNTRY_CODE) {
            $middle = [
                self::$t['payment_voucher_audit_status_name'],//审核状态
                self::$t['payment_voucher_check_staff_name'], //支付凭证审核人-姓名(工号)
                self::$t['payment_voucher_audit_at'],         //支付凭证审核时间
                self::$t['payment_voucher_reason'],           //支付凭证审核不通过原因
            ];
        }

        $header   = array_merge($header, $middle, $bottom);
        $fileName = 'shop_summary_export_order_' . date('YmdHis');
        return $this->exportExcel($header, $items, $fileName);
    }

    /**
     * 在职状态
     * @return array
     */
    static public function staffStateEnums()
    {
        return [
            '1' => self::$t->_('on_the_job'),
            '2' => self::$t->_('leave_the_job'),
            '3' => self::$t->_('stop_the_job'),
        ];
    }

    /**
     * 获取订单信息
     * @param string $order_code 订单编号
     * @return mixed
     */
    public function getOrderInfo($order_code)
    {
        return InteriorOrders::findFirst([
            'order_code = :order_code:',
            'bind' => ['order_code' => $order_code],
        ]);
    }

    /**
     * 订单详情
     * @param string $order_code 订单编号
     * @return array
     */
    public function getInfo($order_code)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = ['staff_info' => [], 'goods_list' => [], 'fund_info' => [], 'refund_record_info' => []];
        try {
            $order_info = $this->getOrderInfo($order_code);
            if ($order_info) {
                $lang = static::$language;
                //获取网点名称
                if ($order_info->staff_store_id == -1) {
                    $store_name = 'Header Office';
                } else {
                    $common_service = new StoreService();
                    $store_info     = $common_service->getSysStoreListByCondition(['store_id' => $order_info->staff_store_id]);
                    $store_name     = $store_info[0]['name'] ?? '';
                }
                //获取部门名称
                $department_info = SysDepartmentModel::findFirst([
                    'conditions' => 'id = :id:',
                    'bind'       => ['id' => $order_info->node_department_id],
                    'columns'    => ['id', 'name'],
                ]);
                $department_info = $department_info ? $department_info->toArray() : [];
                $pay_method      = ShopEnums::payMethod($lang);
                $staff_ids       = array_values(array_unique(array_filter([
                    $order_info->staff_id,
                    $order_info->payment_voucher_check_staff_id,
                ])));
                $staff_arr       = (new HrStaffRepository())->getStaffListByStaffIds($staff_ids);
                //员工基本信息
                $data['staff_info'] = [
                    'staff_id'                         => $order_info->staff_id,
                    'staff_name'                       => $order_info->staff_name,
                    'staff_state'                      => self::staffStateEnums()[$staff_arr[$order_info->staff_id]['state'] ?? ''] ?? '',
                    'store_name'                       => $store_name,
                    'department_name'                  => $department_info['name'] ?? '',
                    'order_code'                       => $order_info->order_code,
                    'pay_amount'                       => $order_info->pay_amount,
                    'flash_pay_code'                   => $order_info->flash_pay_code,
                    'pay_method'                       => $order_info->pay_method,
                    'pay_method_name'                  => $pay_method[$order_info->pay_method] ?? '',
                    'payment_voucher'                  => $order_info->payment_voucher,
                    'payment_voucher_audit_status'     => !empty($order_info->payment_voucher_audit_status) ? static::$t->_(ShopEnums::$payment_voucher_audit_status[$order_info->payment_voucher_audit_status]) : '',
                    'payment_voucher_reason'           => $order_info->payment_voucher_reason,
                    'payment_voucher_check_staff_id'   => $order_info->payment_voucher_check_staff_id,
                    'payment_voucher_check_staff_name' => $staff_arr[$order_info->payment_voucher_check_staff_id]['name'] ?? '',
                ];

                //订单明细
                $goods_list = InteriorOrdersGoodsSku::find([
                    'conditions' => 'order_code = :order_code:',
                    'bind'       => ['order_code' => $order_info->order_code],
                    'columns'    => [
                        'goods_sku_code',
                        'buy_num',
                        'buy_price',
                        'goods_name_' . ($lang == 'zh-CN' ? 'zh' : $lang) . ' as goods_name',
                    ],
                ])->toArray();
                if ($goods_list) {
                    $default_currency = (new EnumsService())->getSysCurrencyInfo();
                    foreach ($goods_list as &$goods) {
                        $goods['cur'] = $default_currency['symbol'];
                    }
                    $data['goods_list'] = $goods_list;
                }
                //退款处理信息
                $fund_items        = ShopEnums::fundStatus($lang);
                $data['fund_info'] = [
                    'submit_at'        => $order_info->submit_at,
                    'pay_amount'       => $order_info->pay_amount,
                    'fund_status'      => $order_info->fund_status,
                    'fund_status_text' => $fund_items[$order_info->fund_status] ?? '',
                    'fund_amount'      => $order_info->fund_amount,
                    'fund_at'          => substr($order_info->fund_at, 0, 10),
                    'fund_code'        => $order_info->fund_code,
                    'fund_remark'      => $order_info->fund_remark,
                    'attachment'       => [],
                ];
                //附件信息,只有退款成功的才需要获取
                if ($order_info->fund_status == ShopEnums::FUND_STATUS_DONE) {
                    $data['fund_info']['attachment'] = $this->getRefundImages($order_info->id);
                }
                //退款记录信息
                $refund_record_info = InteriorOrdersRefundRecordModel::findFirst([
                    'conditions' => 'order_code = :order_code:',
                    'bind'       => ['order_code' => $order_info->order_code],
                ]);
                if ($refund_record_info) {
                    $fund_type_enums            = ShopEnums::fundType($lang);
                    $data['refund_record_info'] = [
                        'staff_name' => '[' . $refund_record_info->staff_id . ']' . $refund_record_info->staff_name,
                        'type'       => $fund_type_enums[$refund_record_info->type] ?? '',
                        'file_path'  => $refund_record_info->file_path,
                        'remark'     => $refund_record_info->remark,
                        'submit_at'  => $refund_record_info->submit_at,
                    ];
                }
            }
        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-order-summary-info-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data ?? [],
        ];
    }

    /**
     * 获取退费凭证附件
     * @param int $id 订单ID
     * @return array
     */
    public function getRefundImages($id): array
    {
        $images = SysAttachmentModel::find([
            'conditions' => "oss_bucket_key = :oss_bucket_key: and oss_bucket_type = '" . ShopEnums::INTERIOR_ORDER_REFUND_OSS_TYPE . "' and deleted = 0",
            'bind'       => [
                'oss_bucket_key' => $id,
            ],
            'columns'    => 'bucket_name,object_key',
        ])->toArray();
        // 兼容马来本地节点服务
        $urlPrefix = 'MY' == strtoupper(env('country_code')) ? "https://%s.oss-ap-southeast-3.aliyuncs.com/%s" : "https://%s.oss-ap-southeast-1.aliyuncs.com/%s";
        if (!empty($images)) {
            $imageArr = [];
            foreach ($images as $image) {
                $imageArr[] = sprintf($urlPrefix, $image['bucket_name'], $image['object_key']);
            }
        }
        return $imageArr ?? [];
    }

    /**
     * 退款
     * @param array $params 退款入参
     * @param array $user 当前登录者
     * @return array
     */
    public function refund($params, $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            $order_code = $params['order_code'];
            $order_info = $this->getOrderInfo($order_code);
            if ($order_info) {
                //针对是用户自助取消&&款项状态为退款中&&支付方式为flashpay的才可退款
                $this->logger->info('确定无头件退款前数据:' . json_encode($order_info->toArray(),
                        JSON_UNESCAPED_UNICODE) . '批量人是 ' . json_encode($user, JSON_UNESCAPED_UNICODE));
                if ($order_info->order_status == ShopEnums::ORDER_STATUS_CUSTOMER_CANCEL_CODE && $order_info->fund_status == ShopEnums::FUND_STATUS_REFUNDING && $order_info->pay_method == $params['pay_method']) {
                    // 公共参数
                    $params['order_code']     = $order_info->order_code;
                    $params['fund_code']      = $params['fund_code'] ?? '';
                    $params['fund_remark']    = $params['fund_remark'] ?? '';
                    $params['attachment_arr'] = $params['attachment_arr'] ?? [];

                    //如果是线下支付且是无头件的时候 从新初始化数据
                    if ($params['pay_method'] == ShopEnums::PAY_METHOD_OFFLINE_PAY && $order_info->goods_type == ShopEnums::GOODS_TYPE_UNCLAIMED_PIECE) {
                        if (!empty($params['is_not_refund'])) {
                            $params['fund_status']            = 0;//还原为初始化状态
                            $params['payment_voucher_status'] = ShopEnums::GOODS_PAYMENT_VOUCHER_STATUS_ABNORMAL;
                            $params['fund_at']                = '';
                            $params['fund_amount']            = 0;
                        } else {
                            $params['fund_status']            = ShopEnums::FUND_STATUS_DONE;
                            $params['payment_voucher_status'] = $order_info->payment_voucher_status;
                            $params['fund_at']                = $params['type'] == ShopEnums::FUND_TYPE_BATCH ? $params['fund_at'] : date('Y-m-d H:i:s');
                            $params['fund_amount']            = $order_info->pay_amount;
                        }
                    } elseif (empty($params['is_not_refund']) && $order_info->pay_amount != $params['fund_amount']) {
                        //退款金额与订单总金额对比
                        throw new ValidationException(static::$t->_('order_refund_amount_error'),
                            ErrCode::$VALIDATE_ERROR);
                    }

                    //款项状态选择了退款中
                    if (isset($params['fund_status']) && $params['fund_status'] == ShopEnums::FUND_STATUS_REFUNDING) {
                        throw new ValidationException(static::$t->_('order_refund_status_error'),
                            ErrCode::$VALIDATE_ERROR);
                    }

                    //退款日期比下单日期还小
                    if (empty($params['is_not_refund']) && strtotime($params['fund_at']) < strtotime(substr($order_info->submit_at,
                            0, 10))) {
                        throw new ValidationException(static::$t->_('order_refund_at_error'), ErrCode::$VALIDATE_ERROR);
                    }

                    //都满足，则可以退款，调取by接口操作退款，记录退款日志。
                    $params['type'] = $params['type'] ?? ShopEnums::FUND_TYPE;
                    $this->submitRefund($params, $user);
                } else {
                    //其他状态情况不可操作退款
                    throw new ValidationException(static::$t->_('order_can_not_refund'), ErrCode::$VALIDATE_ERROR);
                }
            } else {
                throw new ValidationException(static::$t->_('order_info_not_found'), ErrCode::$VALIDATE_ERROR);
            }
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-order-summary-refund-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => '',
        ];
    }

    /**
     * 退款
     * @param array $refund 退款信息
     * @param array $user 当前登陆者
     * @return bool
     * @throws ValidationException
     */
    public function submitRefund(array $refund, array $user)
    {
        $ac = new ApiClient('by', '', 'interior_goods_flash_pay_refund', static::$language);
        $ac->setParams(
            [
                [
                    'order_code'             => $refund['order_code'],                                       //订单编号
                    'fund_status'            => $refund['fund_status'],                                      //款项状态
                    'fund_at'                => $refund['fund_at'],                                          //退款日期
                    'fund_amount'            => $refund['fund_amount'],                                      //退款金额
                    'fund_code'              => $refund['fund_code'] ?? '',                                  //退款编号
                    'fund_remark'            => $refund['fund_remark'] ?? '',                                //退款备注
                    'payment_voucher_status' => $refund['payment_voucher_status'] ?? '',                     //支付凭证状态
                    'staff_id'               => $user['id'],                                                 //操作人ID
                    'staff_name'             => $this->getNameAndNickName($user['name'], $user['nick_name']),//操作人姓名
                    'type'                   => $refund['type'],                                             //操作类型
                    'attachment_arr'         => $refund['attachment_arr'],                                   //附件信息
                ],
            ]
        );
        $res  = $ac->execute();
        $code = $res['result']['code'] ?? $res['code'];
        $msg  = $res['result']['msg'] ?? $res['msg'];
        if ($code == 1) {
            //请求成功
            return true;
        }
        throw new ValidationException($msg, ErrCode::$VALIDATE_ERROR);
    }


    /**
     * 批量确定无头件退款确定
     *
     * @param array $params 退款确定信息
     * @param array $user 当前登陆者
     * @return array
     */
    public function batchRefundSubmit(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            $order_code = $params['batch_order_code'];
            $order_list = $this->getOrderList($order_code)->toArray();
            if (empty($order_list)) {
                throw new ValidationException(static::$t->_('shop_data_not_null'), ErrCode::$VALIDATE_ERROR);
            }
            $this->logger->info('批量确定无头件退款前数据:' . json_encode($order_list,
                    JSON_UNESCAPED_UNICODE) . '批量人是 ' . json_encode($user, JSON_UNESCAPED_UNICODE));
            if (count($order_list) != count($params['batch_order_code'])) {
                throw new ValidationException(static::$t->_('shop_submit_date_by_db_select_data_inconsistent'),
                    ErrCode::$VALIDATE_ERROR);
            }

            // 无需退款的原逻辑 校验
            if ($params['is_not_refund']) {
                if (in_array(ShopEnums::GOODS_TYPE_WORK_CLOTHES, array_column($order_list, 'goods_type'))) {
                    throw new ValidationException(static::$t->_('shop_batch_submit_unclaimed_err'),
                        ErrCode::$VALIDATE_ERROR);
                }

                if (in_array(ShopEnums::PAY_METHOD_FLASH_PAY, array_column($order_list, 'pay_method'))) {
                    throw new ValidationException(static::$t->_('shop_batch_submit_unclaimed_pay_method_err'),
                        ErrCode::$VALIDATE_ERROR);
                }
            }

            foreach ($order_list as $item) {
                $one_refund_data = [
                    'type'          => $params['type'],
                    'order_code'    => $item['order_code'],
                    'pay_method'    => $item['pay_method'],
                    'fund_amount'   => empty($params['is_not_refund']) ? $item['pay_amount'] : 0,
                    'is_not_refund' => $params['is_not_refund'],
                    'fund_at'       => $params['fund_at'] ?? '',
                    'fund_status'   => $params['fund_status'],
                ];
                $refund_info     = $this->refund($one_refund_data, $user);
                if ($refund_info['code'] != ErrCode::$SUCCESS) {
                    if ($refund_info['code'] == ErrCode::$VALIDATE_ERROR) {
                        throw new ValidationException($refund_info['message'], ErrCode::$VALIDATE_ERROR);
                    } else {
                        throw new \Exception($refund_info['message'], ErrCode::$SYSTEM_ERROR);
                    }
                }
            }
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->warning('batch_refund_submit_failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
        ];
    }

    /**
     * 批量获取订单信息
     * @param array $order_code 订单编号
     * @return mixed
     */
    public function getOrderList(array $order_code)
    {
        return InteriorOrders::find([
            'order_code in ({order_code:array}) and  order_status = :order_status:  and fund_status = :fund_status: ',
            'bind' => [
                'order_code'   => $order_code,
                'order_status' => ShopEnums::ORDER_STATUS_CUSTOMER_CANCEL_CODE,
                'fund_status'  => ShopEnums::FUND_STATUS_REFUNDING,
            ],
        ]);
    }

}
