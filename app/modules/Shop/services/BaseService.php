<?php

namespace App\Modules\Shop\Services;


class BaseService extends \App\Library\BaseService
{
    /**
     * 过滤非必需的参数
     * @param array $params 参数组
     * @param array $not_must 非必需的参数组
     * @return mixed
     */
    public static function handleParams($params, $not_must)
    {
        if (empty($not_must) || !is_array($not_must)) {
            return $params;
        }
        foreach ($not_must as $value) {
            if (isset($params[$value]) && empty($params[$value])) {
                unset($params[$value]);
            }
        }
        return $params;
    }

}
