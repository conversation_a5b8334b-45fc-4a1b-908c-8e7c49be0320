<?php

namespace App\Modules\Shop\Services;

use App\Library\ErrCode;
use App\Library\Enums;
use App\Library\Enums\ShopEnums;
use App\Library\Enums\GlobalEnums;
use App\Repository\StoreRepository;
use App\Library\Enums\StaffInfoEnums;
use App\Library\Enums\SysConfigEnums;
use App\Repository\HrStaffRepository;
use App\Models\backyard\SettingEnvModel;
use App\Repository\DepartmentRepository;
use App\Models\backyard\HrStaffInfoModel;
use App\Library\Exception\BusinessException;
use App\Modules\Shop\Models\InteriorGoodsModel;
use App\Library\Validation\ValidationException;
use App\Modules\Organization\Models\SysStoreModel;
use App\Modules\Shop\Models\InteriorGoodsSkuModel;
use App\Models\backyard\InteriorStaffEntrySizeModel;
use App\Models\oa\DataPermissionManagePowerListModel;
use App\Models\oa\DataPermissionModuleConfigPowerModel;
use App\Modules\Organization\Models\SysDepartmentModel;

use App\Modules\Setting\Services\DataPermissionModuleConfigService;


class GoodsSizeService extends BaseService
{

    private static $instance;


    private function __construct()
    {
    }


    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }


    // 员工商城-员工工服尺码-非必要的筛选条件
    public static $not_must_params = [
        'staff_id',
        'start_entry_time',
        'end_entry_time',
        'node_department_id',
        'sys_store_name',
        'goods_name',
        'barcode',
        'start_updated_at',
        'end_updated_at',
        'source',
        'pageNum',
        'pageSize',
    ];


    // 员工商城-员工工服尺码-列表-搜索条件
    public static $validate_list_search = [
        'staff_id'           => 'StrLenGeLe:0,20|>>>:staff_id error',
        //员工工号
        'start_entry_time'   => 'DateTime|>>>:start_entry_time error',
        //下单开始时间
        'end_entry_time'     => 'DateTime|>>>:end_entry_time error',
        //下单结束时间
        'node_department_id' => 'IntGe:1|>>>:node_department_id error',
        'sys_store_name'     => 'StrLenGe:0|>>>: sys_store_name error',
        'barcode'            => 'StrLenGeLe:0,20|>>>: barcode error',
        'start_updated_at'   => 'DateTime|>>>:start_updated_at error',
        //发货开始时间
        'end_updated_at'     => 'DateTime|>>>:end_updated_at error',
        //发货结束时间
        'source'             => 'IntIn:' . ShopEnums::BATCH_ORDER_APPLY_SOURCE_BY . ',' . ShopEnums::BATCH_ORDER_APPLY_SOURCE_OA . '|>>>:source error',
        //款项状态类型
        'pageNum'            => 'IntGe:1|>>>:page error',
        //当前页码
        'pageSize'           => 'IntGe:1|>>>:page_size error',
        //每页条数
    ];


    // 员工商城-员工工服尺码-查看 、修改、删除
    public static $validate_share = [
        'id' => 'Required|IntGt:0|>>>:id error',
    ];


    // 员工商城-员工工服尺码-新建
    public static $validate_add = [
        'staff_id' => 'Required|IntGe:1|>>>:staff_id error',//员工工号
        'barcode'  => 'Required|StrLenGeLe:1,20|>>>: barcode error',
    ];


    // 员工商城-员工工服尺码-编辑
    public static $validate_edit = [
        'barcode' => 'Required|StrLenGeLe:1,20|>>>: barcode error',
    ];


    //员工商城-员工工服尺码-新建-员工数据
    public static $validate_staff_list = [
        'staff_name' => 'StrLenGe:0|>>>:staff_name error',
    ];


    /**
     * 员工工服尺码枚举配置  (批量下单工服申请状态)
     * @return array
     */
    public static function getEnumsSetting()
    {
        //批量下单工服申请状态
        $batch_order_status = [];
        foreach (ShopEnums::$batch_order_apply_status_arr as $status => $lang_key) {
            $batch_order_status[] = [
                'value' => (string)$status,
                'label' => static::$t->_($lang_key),
            ];
        }
        //OA-员工商城-批量工服确认-核对
        $batch_check_status = [];
        foreach (ShopEnums::$batch_order_apply_check_status_arr as $status => $lang_key) {
            $batch_check_status[] = [
                'value' => (string)$status,
                'label' => static::$t->_($lang_key),
            ];
        }
        //来源
        $batch_order_source = [];
        foreach (ShopEnums::$batch_order_apply_source_arr as $source => $lang_key) {
            $batch_order_source[] = [
                'value' => (string)$source,
                'label' => static::$t->_($lang_key),
            ];
        }

        //员工所属网点类型
        $store_type_arr = [];
        foreach (Enums::$payment_cost_store_type as $type => $lang_key) {
            $store_type_arr[] = [
                'value' => (string)$type,
                'label' => static::$t->_($lang_key),
            ];
        }

        //是否补录
        $is_reissue_arr = [];
        foreach (ShopEnums::$batch_order_is_reissue as $reissue => $lang_key) {
            $is_reissue_arr[] = [
                'value' => (string)$reissue,
                'label' => static::$t->_($lang_key),
            ];
        }

        return [
            'batch_order_apply_status_arr' => $batch_order_status,
            'batch_order_source_arr'       => $batch_order_source,
            'batch_order_store_type_arr'   => $store_type_arr,
            'batch_order_is_reissue_arr'   => $is_reissue_arr,
            'batch_check_status'           => $batch_check_status,
        ];
    }


    /**
     * 员工工服尺码列表
     * @param array $condition 查询参数组
     * @param array $user 当前用户
     * @return array
     */
    public function getList(array $condition, array $user)
    {
        $page_size = empty($condition['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $condition['pageSize'];
        $page_num  = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['pageNum'];
        $offset    = $page_size * ($page_num - GlobalEnums::DEFAULT_PAGE_NUM);

        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        $items   = [];
        $lang    = static::getLanguage();
        try {
            $count = $this->getListCount($condition, $lang, $user);
            if ($count > 0) {
                $builder    = $this->modelsManager->createBuilder();
                $goods_name = 'goods_name_' . $lang;
                $builder->columns([
                    'main.id',
                    'main.staff_id',
                    'main.entry_time',
                    'main.node_department_id',
                    'main.sys_store_id',
                    'main.sys_store_name',
                    'main.goods_id',
                    'main.barcode',
                    'main.size',
                    'main.' . $goods_name,
                    'main.entry_size',
                    'main.entry_goods_id',
                    'main.entry_source',
                    'main.updated_id',
                    'main.updated_at',
                ]);
                $builder->from(['main' => InteriorStaffEntrySizeModel::class]);
                $builder = $this->getCondition($builder, $condition, $lang, $user);
                $builder->orderBy('main.updated_at DESC, entry_time DESC');
                $builder->limit($page_size, $offset);
                $items = $builder->getQuery()->execute()->toArray();
            }
            $items = $this->handleData($items, $lang);

            $data = [
                'items'      => $items,
                'pagination' => [
                    'current_page' => $page_num,
                    'per_page'     => $page_size,
                    'total_count'  => (int)$count,
                ],
            ];
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->warning('员工商城 - 员工工服尺码列表: ' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }


    /**
     * 员工商城-订单汇总列表-记录数
     * @param array $condition 查询条件组
     * @param string $lang 语言
     * @param array $user 当前用户
     * @return int
     */
    public function getListCount(array $condition, string $lang, array $user)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(main.id) as total');
        $builder->from(['main' => InteriorStaffEntrySizeModel::class]);
        $builder    = $this->getCondition($builder, $condition, $lang, $user);
        $total_info = $builder->getQuery()->getSingleResult();
        return intval($total_info->total);
    }


    /**
     * 组装列表搜索条件
     * @param object $builder 查询器对象
     * @param array $condition 查询条件组
     * @param string $lang 语言
     * @param array $user 当前用户
     * @return object
     */
    public function getCondition(object $builder, array $condition, string $lang, array $user)
    {
        $staff_id           = $condition['staff_id'] ?? '';
        $start_entry_time   = $condition['start_entry_time'] ?? '';  //入职时间
        $end_entry_time     = $condition['end_entry_time'] ?? '';    //入职时间
        $node_department_id = $condition['node_department_id'] ?? '';//部门
        $sys_store_name     = $condition['sys_store_name'] ?? '';    //网点名称
        $goods_name         = $condition['goods_name'] ?? '';        //商品名称
        $barcode            = $condition['barcode'] ?? '';           //商品编码
        $start_updated_at   = $condition['start_updated_at'];        //更新时间
        $end_updated_at     = $condition['end_updated_at'];          //更新时间
        $source             = $condition['source'] ?? '';            //商品编码

        //数据权限
        $staff_arr = $this->getEntrySizeStaffAll($user);

        if ($staff_arr['res_status'] == ShopEnums::DATA_PERMISSION_DEPARTMENT_RES_STATUS) {
            //配置的部门
            $builder->inWhere('main.node_department_id', $staff_arr['res_data']);
        } elseif ($staff_arr['res_status'] == ShopEnums::DATA_PERMISSION_ALL_STORE_STATUS) {
            //按照网点
            $builder->inWhere('main.sys_store_id', $staff_arr['res_data']);
        }

        if (!empty($staff_id)) {
            $builder->andWhere('main.staff_id = :staff_id:', ['staff_id' => $staff_id]);
        }

        if (!empty($start_entry_time) && !empty($end_entry_time)) {
            $builder->betweenWhere('main.entry_time', $start_entry_time, $end_entry_time);
        }

        if (!empty($node_department_id)) {
            $builder->andWhere('main.node_department_id = :node_department_id:',
                ['node_department_id' => $node_department_id]);
        }

        if (!empty($sys_store_name)) {
            $builder->andWhere('main.sys_store_name like :sys_store_name:',
                ['sys_store_name' => $sys_store_name . '%']);
        }

        if (!empty($goods_name)) {
            $goods_name_title = 'goods_name_' . $lang;
            $builder->andWhere('main.' . $goods_name_title . ' like :' . $goods_name_title . ':',
                [$goods_name_title => $goods_name . '%']);
        }
        if (!empty($barcode)) {
            $builder->andWhere('main.barcode = :barcode:', ['barcode' => $barcode]);
        }

        if (!empty($start_updated_at) && !empty($end_updated_at)) {
            $builder->betweenWhere('main.updated_at', $start_updated_at, $end_updated_at);
        }
        if (!empty($source)) {
            $builder->andWhere('main.entry_source = :entry_source:', ['entry_source' => $source]);
        }

        $builder->andWhere('main.is_deleted = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);
        return $builder;
    }


    /**
     * 格式化列表数据
     * @param array $items 订单列表
     * @param string $lang 当前语言包
     * @param bool $is_export 是否是导出
     * @return array
     */
    public function handleData($items, $lang, $is_export = false)
    {
        if (empty($items)) {
            return [];
        }
        $staff_ids = array_values(array_unique(array_filter(array_merge(array_column($items, 'staff_id'),
            array_column($items, 'updated_id')))));
        $staff_arr = (new HrStaffRepository())->getStaffListFormalArr($staff_ids);
        //获取所有的部门组
        $department_ids  = array_values(array_unique(array_filter(array_column($staff_arr, 'node_department_id'))));
        $department_list = [];
        if ($department_ids) {
            $department_list = (new DepartmentRepository())->getDepartmentByIds($department_ids);
        }
        $export_data = [];
        $goods_name  = 'goods_name_' . $lang;
        if ($is_export) {
            foreach ($items as $key => $value) {
                $staff_info    = $staff_arr[$value['staff_id']];
                $export_data[] = [
                    $key + 1,
                    $staff_info['name'] ?? '',
                    $value['staff_id'],
                    date('Y-m-d', strtotime($value['entry_time'])),
                    $this->staffJobState($staff_info),
                    $department_list[$value['node_department_id']]['name'] ?? '',
                    $value['sys_store_name'],
                    $value[$goods_name],
                    $value['barcode'],
                    $value['size'],
                    self::$t->_(ShopEnums::$batch_order_apply_source_arr[$value['entry_source']]),
                    $value['updated_at'] ?? '',
                    $value['updated_id'] ? $staff_arr[$value['updated_id']]['name'] : '',
                ];
            }
        } else {
            foreach ($items as &$value) {
                $staff_info                      = $staff_arr[$value['staff_id']];
                $value['staff_name']             = $staff_info['name'] ?? '';
                $value['online_job_status_name'] = $this->staffJobState($staff_info);
                $value['node_department_name']   = $department_list[$value['node_department_id']]['name'] ?? '';
                $value['goods_name']             = $value[$goods_name];
                $value['entry_time']             = date('Y-m-d', strtotime($value['entry_time']));
                $value['source']                 = self::$t->_(ShopEnums::$batch_order_apply_source_arr[$value['entry_source']]);
                $value['updated_id_name']        = $value['updated_id'] ? $staff_arr[$value['updated_id']]['name'] : '';
            }
        }
        return $is_export ? $export_data : $items;
    }


    /**
     * 在职状态 包含待离职
     * @param array $info 员工详情信息
     * @return array
     */
    public function staffJobState($info)
    {
        //待离职
        if ($info['state'] == StaffInfoEnums::STAFF_STATE_IN && $info['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES) {
            return self::$t->_('staff_state.999');
        } else {
            return self::$t->_(StaffInfoEnums::$staff_state[$info['state']]);
        }
    }


    /**
     * 员工商城-员工工服尺码-编辑提交
     * @param array $params 编辑入参
     * @param array $user 当前登录者
     * @return array
     */
    public function saveOne($params, $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            $entry_size = InteriorStaffEntrySizeModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $params['id']],
            ]);
            if (empty($entry_size)) {
                throw new ValidationException(static::$t->_('data_not_exist_by_id', ['id' => $params['id']]),
                    ErrCode::$VALIDATE_ERROR);
            }

            if (!empty($entry_size->is_deleted)) {
                throw new ValidationException(static::$t->_('interior_staff_repeat_edit'), ErrCode::$VALIDATE_ERROR);
            }
            $staff_arr = (new HrStaffRepository())->getStaffById($entry_size->staff_id);

            //查询配置的部门和商品映射关系
            $department_spu_ids = $this->getEnvDepartmentSpu();
            $department_spu_arr = array_column($department_spu_ids, 'spu', 'department_id');

            if (!in_array($staff_arr['node_department_id'], array_keys($department_spu_arr))) {
                throw new ValidationException(static::$t->_('interior_orders_department_id_spu_not_config'),
                    ErrCode::$VALIDATE_ERROR);
            }
            $goods_size_arr = $this->getGoodsSizeAll([$department_spu_arr[$staff_arr['node_department_id']]]);
            if (empty($goods_size_arr) || !in_array($params['barcode'], array_column($goods_size_arr, 'barcode'))) {
                throw new ValidationException(static::$t->_('barcode_not_satisfiable'), ErrCode::$VALIDATE_ERROR);
            }
            $goods_info_arr            = $this->getBarcodeInteriorGoods($params['barcode']);
            $entry_size->goods_id      = $goods_info_arr['goods_id'];
            $entry_size->barcode       = $goods_info_arr['goods_sku_code'];
            $entry_size->size          = $goods_info_arr['attr_1'];
            $entry_size->goods_name_zh = $goods_info_arr['goods_name_zh'];
            $entry_size->goods_name_th = $goods_info_arr['goods_name_th'];
            $entry_size->goods_name_en = $goods_info_arr['goods_name_en'];
            $entry_size->updated_id    = $user['id'];
            $entry_size->updated_at    = date('Y-m-d H:i:s', time());

            if ($entry_size->save() === false) {
                throw new BusinessException('员工商城-员工工服尺码-编辑提交失败, 原因可能是: ' . get_data_object_error_msg($entry_size) . '; 数据: ' . json_encode($goods_info_arr,
                        JSON_UNESCAPED_UNICODE), ErrCode::$INTERIOR_STAFF_ENTRY_SIZE_EDIT_ERROR);
            }
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code    = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning('员工商城-员工工服尺码-添加商品异常: ' . $e->getMessage());
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('员工商城-员工工服尺码-添加商品错误:' . $e->getMessage());
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
        ];
    }


    /**
     * 员工商城-员工工服尺码-详情
     * @param int $id
     * @return array
     */
    public function getDetail(int $id)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $data    = [];
        try {
            $entry_size = InteriorStaffEntrySizeModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $id],
                'columns'    => [
                    'id',
                    'staff_id',
                    'node_department_id',
                    'sys_store_id',
                    'sys_store_name',
                    'goods_id',
                    'barcode',
                    'size',
                ],
            ]);
            if (empty($entry_size)) {
                throw new ValidationException(static::$t->_('empty_data'), ErrCode::$VALIDATE_ERROR);
            }
            $data = $entry_size->toArray();
            $data = $this->handleDetailData($data);
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('员工商城-员工工服尺码-详情异常: ' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }


    /**
     * 员工商城-员工工服尺码-添加商品
     * @param array $params
     * @param array $user
     * @return array
     */
    public function addOne(array $params = [], array $user = [])
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        try {
            //校验选择的员工符不符合要求
            $permission_data = $this->getEntrySizeStaffAll($user);
            $builder         = $this->modelsManager->createBuilder();
            $builder->columns(['staff_info_id']);
            $builder->from(['hsi' => HrStaffInfoModel::class]);
            //增加了搜索条件
            if ($permission_data['res_status'] == ShopEnums::DATA_PERMISSION_DEPARTMENT_RES_STATUS) {
                //配置的部门
                $builder->inWhere('hsi.node_department_id', $permission_data['res_data']);
            } elseif ($permission_data['res_status'] == ShopEnums::DATA_PERMISSION_ALL_STORE_STATUS) {
                //按照网点
                $builder->inWhere('hsi.sys_store_id', $permission_data['res_data']);
            }
            $builder->andWhere('staff_info_id = :staff_info_id:', ['staff_info_id' => $params['staff_id']]);
            //过滤在职
            $builder->andWhere('state = :state: and wait_leave_state = :wait_leave_state: and is_sub_staff = :is_sub_staff: and  hire_type = :hire_type: and formal = :formal:',
                [
                    'state'            => StaffInfoEnums::STAFF_STATE_IN,
                    'wait_leave_state' => StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_NO,
                    'is_sub_staff'     => StaffInfoEnums::IS_SUB_STAFF_NO,
                    'hire_type'        => StaffInfoEnums::HIRE_TYPE_PERMANENT_EMPLOYEE,
                    'formal'           => StaffInfoEnums::FORMAL_IN,
                ]);
            $builder->limit($params['limit'] ?? GlobalEnums::DEFAULT_PAGE_SIZE);
            $data = $builder->getQuery()->execute()->toArray();
            if (empty($data)) {
                throw new ValidationException(static::$t->_('interior_staff_not_satisfiable'),
                    ErrCode::$VALIDATE_ERROR);
            }

            //校验员工是否曾经添加
            $staff_entry_size = InteriorStaffEntrySizeModel::findFirst([
                'conditions' => 'staff_id = :staff_id: and is_deleted = :is_deleted:',
                'bind'       => ['staff_id' => $params['staff_id'], 'is_deleted' => GlobalEnums::IS_NO_DELETED],
                'columns'    => ['id'],
            ]);
            if (!empty($staff_entry_size)) {
                throw new ValidationException(static::$t->_('interior_staff_repeat_add'), ErrCode::$VALIDATE_ERROR);
            }

            $staff_arr = (new HrStaffRepository())->getStaffById($params['staff_id']);

            //查询配置的部门和商品映射关系
            $department_spu_ids = $this->getEnvDepartmentSpu();
            $department_spu_arr = array_column($department_spu_ids, 'spu', 'department_id');

            if (!in_array($staff_arr['node_department_id'], array_keys($department_spu_arr))) {
                throw new ValidationException(static::$t->_('interior_orders_department_id_spu_not_config'),
                    ErrCode::$VALIDATE_ERROR);
            }

            //校验选择的barcode符不符合要求
            $goods_size_arr = $this->getGoodsSizeAll([$department_spu_arr[$staff_arr['node_department_id']]]);
            if (empty($goods_size_arr) || !in_array($params['barcode'], array_column($goods_size_arr, 'barcode'))) {
                throw new ValidationException(static::$t->_('barcode_not_satisfiable'), ErrCode::$VALIDATE_ERROR);
            }
            $interior_goods_arr = $this->getBarcodeInteriorGoods($params['barcode']);

            $store_arr        = (new StoreRepository())->getStoreDetail($staff_arr['sys_store_id']);
            $new_time         = date('Y-m-d H:i:s', time());
            $size_data        = [
                'staff_id'           => $params['staff_id'],
                'entry_time'         => $staff_arr['hire_date'],
                'node_department_id' => $staff_arr['node_department_id'],
                'sys_store_id'       => $staff_arr['sys_store_id'],
                'sys_store_name'     => $staff_arr['sys_store_id'] == Enums::HEAD_OFFICE_STORE_FLAG ? Enums::PAYMENT_HEADER_STORE_NAME : $store_arr['name'],
                'goods_id'           => $interior_goods_arr['goods_id'],
                'barcode'            => $params['barcode'],
                'size'               => $interior_goods_arr['attr_1'],
                'goods_name_zh'      => $interior_goods_arr['goods_name_zh'],
                'goods_name_th'      => $interior_goods_arr['goods_name_th'],
                'goods_name_en'      => $interior_goods_arr['goods_name_en'],
                'entry_size'         => $interior_goods_arr['attr_1'],
                'entry_goods_id'     => $interior_goods_arr['goods_id'],
                'entry_source'       => ShopEnums::BATCH_ORDER_APPLY_SOURCE_OA,
                'is_deleted'         => GlobalEnums::IS_NO_DELETED,
                'created_id'         => $user['id'],
                'updated_id'         => $user['id'],
                'created_at'         => $new_time,
                'updated_at'         => $new_time,
            ];
            $entry_size_model = new InteriorStaffEntrySizeModel();
            if ($entry_size_model->i_create($size_data) === false) {
                throw new BusinessException('员工商城-员工工服尺码-添加商品失败, 原因可能是: ' . get_data_object_error_msg($entry_size_model) . '; 数据: ' . json_encode($size_data,
                        JSON_UNESCAPED_UNICODE), ErrCode::$INTERIOR_STAFF_ENTRY_SIZE_ADD_ERROR);
            }
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code    = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning('员工商城-员工工服尺码-添加商品异常: ' . $e->getMessage());
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('员工商城-员工工服尺码-添加商品错误: ' . $e->getMessage());
        }
        return [
            'code'    => $code,
            'message' => $message,
        ];
    }


    /**
     * 员工商城-员工工服尺码-删除
     * @param array $user 当前登录者
     * @param int $id 删除的id
     * @return array
     */
    public function del(int $id, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            $entry_size = InteriorStaffEntrySizeModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $id],
            ]);
            if (empty($entry_size)) {
                throw new ValidationException(static::$t->_('data_not_exist_by_id', ['id' => $id]),
                    ErrCode::$VALIDATE_ERROR);
            }

            if (!empty($entry_size->is_deleted)) {
                throw new ValidationException(static::$t->_('interior_staff_repeat_del'), ErrCode::$VALIDATE_ERROR);
            }
            $entry_size->is_deleted = GlobalEnums::IS_DELETED;
            $entry_size->updated_id = $user['id'];
            $entry_size->updated_at = date('Y-m-d H:i:s', time());

            if ($entry_size->save() === false) {
                throw new BusinessException('员员工商城-员工工服尺码-删除失败, 原因可能是: ' . get_data_object_error_msg($entry_size),
                    ErrCode::$INTERIOR_STAFF_ENTRY_SIZE_DEL_ERROR);
            }
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code    = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning('员员工商城-员工工服尺码-删除异常: ' . $e->getMessage());
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('员员工商城-员工工服尺码-删除错误:' . $e->getMessage());
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
        ];
    }


    /**
     * 员工商城-员工工服尺码-新建-员工数据
     * @param array $params 搜索条件
     * @param array $user
     * @return array
     */
    public function staffList(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $data    = [];
        try {
            $permission_data = $this->getEntrySizeStaffAll($user);
            //数据在配置里面且不在全量里面
            //查询增加like查询
            $builder = $this->modelsManager->createBuilder();
            $builder->columns(['staff_info_id as consignee_id, name as consignee_name, node_department_id, sys_store_id']);
            $builder->from(['hsi' => HrStaffInfoModel::class]);
            //增加了搜索条件
            if (!empty($params['staff_name'])) {
                $builder->andWhere(' (staff_info_id like :staff_info_id:)',
                    ['staff_info_id' => $params['staff_name'] . '%']);
            }

            if ($permission_data['res_status'] == ShopEnums::DATA_PERMISSION_DEPARTMENT_RES_STATUS) {
                //配置的部门
                $builder->inWhere('hsi.node_department_id', $permission_data['res_data']);
            } elseif ($permission_data['res_status'] == ShopEnums::DATA_PERMISSION_ALL_STORE_STATUS) {
                //按照网点
                $builder->inWhere('hsi.sys_store_id', $permission_data['res_data']);
            }
            //过滤在职
            $builder->andWhere('state = :state: and wait_leave_state = :wait_leave_state: and is_sub_staff = :is_sub_staff: and  hire_type = :hire_type: and formal = :formal:',
                [
                    'state'            => StaffInfoEnums::STAFF_STATE_IN,
                    'wait_leave_state' => StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_NO,
                    'is_sub_staff'     => StaffInfoEnums::IS_SUB_STAFF_NO,
                    'hire_type'        => StaffInfoEnums::HIRE_TYPE_PERMANENT_EMPLOYEE,
                    'formal'           => StaffInfoEnums::FORMAL_IN,
                ]);
            $builder->limit($params['limit'] ?? GlobalEnums::DEFAULT_PAGE_SIZE);
            $data = $builder->getQuery()->execute()->toArray();
            if (!empty($data)) {
                $data = $this->handStaffListData($data);
            } else {
                $data = [];
            }
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('员工商城-员工工服尺码-新建-员工数据异常: ' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }


    /**
     * 格式化数据
     * @param array $staff_list 数据
     * @return  array
     **/
    public function handStaffListData($staff_list)
    {
        [$goods_arr, $department_spu_arr] = $this->getDepartmentGoodsArr($staff_list);
        $store_ids      = array_values(array_unique(array_column($staff_list, 'sys_store_id')));
        $store_arr      = (new StoreRepository())->getStoreListByIds($store_ids);
        $department_arr = (new DepartmentRepository())->getDepartmentByIds(array_values(array_unique(array_column($staff_list,
            'node_department_id'))));
        foreach ($staff_list as &$staff) {
            $staff['sys_store_name']       = $staff['sys_store_id'] == Enums::HEAD_OFFICE_STORE_FLAG ? Enums::PAYMENT_HEADER_STORE_NAME : $store_arr[$staff['sys_store_id']]['name'] ?? '';
            $size_arr                      = $goods_arr[$department_spu_arr[$staff['node_department_id']]] ?? [];
            $staff['node_department_name'] = $department_arr[$staff['node_department_id']]['name'] ?? '';
            $staff['size_img_path']        = $size_arr[0]['size_img_path'] ?? '';
            $staff['size_arr']             = $size_arr;
        }
        return $staff_list;
    }


    /**
     * 获取员工工服尺码下对应的员工
     * @param array $user
     * @param bool $is_check 是否是批量工服确认
     * @return  array
     **/
    public function getEntrySizeStaffAll(array $user, $is_check = false)
    {
        if ($is_check) {
            $entry_staff_ids_key = 'all_batch_interior_orders_entry_ids';
            $module_key          = SysConfigEnums::SYS_MODULE_SHOP_BATCH_ORDER;
        } else {
            $entry_staff_ids_key = 'all_interior_orders_entry_ids';
            $module_key          = SysConfigEnums::SYS_MODULE_SHOP_WORK_SIZE;
        }

        //优先级为1 全量配置查找
        $entry_staff_ids               = (new SettingEnvModel())->getValByCode($entry_staff_ids_key);
        $all_interior_orders_entry_ids = array_values(array_unique(explode(',', $entry_staff_ids)));
        if (!empty($all_interior_orders_entry_ids) && in_array($user['id'], $all_interior_orders_entry_ids)) {
            return [
                'res_status' => ShopEnums::DATA_PERMISSION_ALL_RES_STATUS,//全量状态
                'res_data'   => [],
            ];
        }

        //优先级为2 符合部门权限的数据
        $res['res_status']    = ShopEnums::DATA_PERMISSION_DEPARTMENT_RES_STATUS;
        $res['res_data']      = [];
        $department_staff_arr = DataPermissionModuleConfigService::getInstance()->getDataConfigPermission($module_key,
            $user['id']);
        if (!empty($department_staff_arr)) {
            $res['res_data'] = $department_staff_arr;//符合部门
            return $res;
        }

        //优先级为3 批量下单工服列表且部门权限的数据为空的 走网点权限数据
        if (!$is_check) {
            $store_arr = SysStoreModel::find([
                'conditions' => 'manager_id = :manager_id:',
                'bind'       => ['manager_id' => $user['id']],
                'columns'    => ['id'],
            ])->toArray();

            $res['res_status'] = ShopEnums::DATA_PERMISSION_ALL_STORE_STATUS;//符合网点
            $res['res_data']   = array_values(array_column($store_arr, 'id')) ?? [];
        }
        return $res;
    }


    /**
     * 格式化详情数据
     * @param array $info 订单列表
     * @return array
     */
    public function handleDetailData($info)
    {
        if (empty($info)) {
            return [];
        }
        $staff_arr                    = (new HrStaffRepository())->getStaffById($info['staff_id']);
        $info['staff_name']           = $staff_arr['name'] ?? '';
        $info['node_department_name'] = SysDepartmentModel::findFirst($info['node_department_id'])->name ?? '';
        //重新获取部门，查找映射关系，找到最新的映射id
        $department_spu_ids = $this->getEnvDepartmentSpu();
        $department_spu_arr = array_column($department_spu_ids, 'spu', 'department_id');
        $goods_size_arr     = [];
        if (in_array($staff_arr['node_department_id'], array_keys($department_spu_arr))) {
            $goods_size_arr = $this->getGoodsSizeAll([$department_spu_arr[$staff_arr['node_department_id']]]);
        }
        $info['size_img_path'] = $goods_size_arr[0]['size_img_path'] ?? '';
        $info['size_arr']      = $goods_size_arr;
        return $info;
    }


    /**
     * 根据goods_id 返回所有的尺码数据
     * @param array $goods_id 订单列表
     * @return array
     */
    public function getGoodsSizeAll(array $goods_id)
    {
        if (empty($goods_id)) {
            return [];
        }
        $builder = $this->modelsManager->createBuilder();
        $builder->columns(['igs.goods_id, igs.goods_sku_code as barcode, igs.attr_1 as size,ig.size_img_path, ig.goods_name_zh, ig.goods_name_th, ig.goods_name_en']);
        $builder->from(['igs' => InteriorGoodsSkuModel::class]);
        $builder->leftJoin(InteriorGoodsModel::class, 'igs.goods_id = ig.id', 'ig');
        $builder->where('igs.status = :status:', ['status' => ShopEnums::GOODS_STATUS_ON_SALE]);
        $builder->andWhere('ig.status = :status: and goods_type = :goods_type:',
            ['status' => ShopEnums::GOODS_STATUS_ON_SALE, 'goods_type' => ShopEnums::GOODS_TYPE_WORK_CLOTHES]);
        $builder->inWhere('igs.goods_id', $goods_id);
        return $builder->getQuery()->execute()->toArray();
    }


    /**
     * 根据barcode 返回商品对应的数据
     * @param string $barcode barcode
     * @return array
     */
    public function getBarcodeInteriorGoods(string $barcode)
    {
        if (empty($barcode)) {
            return [];
        }
        $builder = $this->modelsManager->createBuilder();
        $builder->columns(['igs.goods_sku_code, igs.attr_1, ig.id as goods_id, ig.goods_name_zh, ig.goods_name_th, ig.goods_name_en, ig.img_path, ig.size_img_path,ig.info']);
        $builder->from(['igs' => InteriorGoodsSkuModel::class]);
        $builder->leftJoin(InteriorGoodsModel::class, 'igs.goods_id = ig.id', 'ig');
        $builder->where('igs.goods_sku_code = :goods_sku_code:', ['goods_sku_code' => $barcode]);
        return $builder->getQuery()->execute()->getFirst()->toArray();
    }


    /**
     * BY中获取配置中的映射关系
     * @return array
     */
    public function getEnvDepartmentSpu()
    {
        $department_spu_ids = (new SettingEnvModel())->getValByCode('auto_interior_orders_department_id_spu_ids');
        return json_decode($department_spu_ids, true);
    }


    /**
     * 根据配置中的映射关系 返回对应的部分和商品的数据集合
     * @param array $staff_list 用户数据
     * @return array
     */
    public function getDepartmentGoodsArr(array $staff_list)
    {
        //查询配置的部门和商品映射关系
        $department_spu_ids = $this->getEnvDepartmentSpu();
        $department_spu_arr = array_column($department_spu_ids, 'spu', 'department_id');
        //得到本次需要查询的部门数据
        $intersect_department_spu_ids = array_intersect(array_unique(array_column($staff_list, 'node_department_id')),
            array_column($department_spu_ids, 'department_id'));
        //处理部门对应的spu
        $spu_department_ids = $goods_arr = [];
        if (!empty($intersect_department_spu_ids)) {
            foreach ($intersect_department_spu_ids as $department_id) {
                if (in_array($department_id, array_keys($department_spu_arr))) {
                    $spu_arr['department_id'] = $department_id;
                    $spu_arr['spu']           = $department_spu_arr[$department_id];
                    $spu_department_ids[]     = $spu_arr;
                }
            }
            $spu_arr = array_values(array_unique(array_filter(array_column($spu_department_ids, 'spu'))));
            //查询spu对应的尺码数据
            $goods_size_arr = $this->getGoodsSizeAll($spu_arr);
            if (!empty($goods_size_arr)) {
                $goods_arr = [];
                foreach ($goods_size_arr as $goods) {
                    $goods_arr[$goods['goods_id']][] = $goods;
                }
            }
        }
        return [$goods_arr, $department_spu_arr];
    }


    /**
     * 导出文件表头参见附录1 表头
     * @return array
     */
    public function getBatchSizeHeader()
    {
        return [
            self::$t['global.no'],                          //序号
            self::$t['interior_goods_staff_name'],          //员工姓名
            self::$t['interior_goods_staff_id'],            //员工工号
            self::$t['interior_goods_hire_date'],           //入职时间
            self::$t['interior_goods_job_status'],          //在职状态
            self::$t['interior_goods_node_department_name'],//所属部门
            self::$t['interior_goods_sys_store_name'],      //所属网点
            self::$t['interior_goods_shop_goods_name'],     //商品名称
            self::$t['interior_goods_shop_goods_barcode'],  //商品编码
            self::$t['interior_goods_shop_goods_size'],     //尺码
            self::$t['interior_goods_shop_goods_source'],   //来源
            self::$t['interior_goods_updated_at'],          //更新时间
            self::$t['interior_goods_updated_id']           //更新人
        ];
    }


    /**
     * 获取耗材申请单下载列表
     * @param string $language 语言
     * @param array $condition 查询条件组
     * @param int $count 总记录数
     * @return array
     */
    public function getExportList($language = '', $condition, $count, $user)
    {
        $page_size = empty($condition['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $condition['pageSize'];
        $page_num  = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['pageNum'];
        $offset    = $page_size * ($page_num - GlobalEnums::DEFAULT_PAGE_NUM);
        $code      = ErrCode::$SUCCESS;
        $message   = $real_message = '';
        $data      = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];
        try {
            if ($count > 0) {
                $lang    = static::getLanguage($language);
                $builder = $this->modelsManager->createBuilder();
                $columns = [
                    'main.id',
                    'main.staff_id',
                    'main.entry_time',
                    'main.node_department_id',
                    'main.sys_store_id',
                    'main.sys_store_name',
                    'main.goods_id',
                    'main.barcode',
                    'main.size',
                    'main.goods_name_' . $lang,
                    'main.entry_size',
                    'main.entry_goods_id',
                    'main.entry_source',
                    'main.updated_id',
                    'main.updated_at',
                ];
                $builder->columns($columns);
                $builder->from(['main' => InteriorStaffEntrySizeModel::class]);
                $builder = $this->getCondition($builder, $condition, $lang, $user);
                $builder->limit($page_size, $offset);
                $builder->orderBy('main.updated_at DESC, entry_time DESC');
                $items_obj = $builder->getQuery()->execute();
                $items     = $items_obj ? $items_obj->toArray() : [];
                $items     = $this->handleData($items, $lang, true);
            }
            $data['items']                     = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->warning('员工商城 - 员工工服尺码列表异步导出 失败:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }


}
