<?php
/**
 * Created by PhpStorm.
 * Date: 2021/10/21
 * Time: 16:59
 */

namespace App\Modules\Shop\Services;

use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\ShopEnums;
use App\Library\ErrCode;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Contract\Models\SysCityModel;
use App\Modules\Contract\Models\SysDistrictModel;
use App\Modules\Contract\Models\SysProvinceModel;
use App\Modules\Hc\Models\HrStaffInfoModel;
use App\Modules\Organization\Services\DepartmentService;
use App\Modules\Shop\Models\InteriorOrders;
use App\Modules\Shop\Models\SysStoreModel as SysStore;
use App\Modules\Shop\Models\InteriorOrdersGoodsSku;
use App\Modules\User\Models\StaffInfoModel;
use App\Modules\User\Models\DepartmentModel;
use App\Library\ApiClient;
use App\Modules\Hc\Models\SysStoreModel;
use App\Models\backyard\InteriorOrdersModel;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Repository\HrStaffRepository;

class OrderService extends BaseService
{


    //OA-员工商城-订单明细查询-非必要的筛选条件
    public static $not_must_params = [
        'order_code',
        'order_status',
        'fund_status',
        'order_start_date',
        'order_end_date',
        'staff_id',
        'goods_name',
        'goods_sku_code',
        'goods_type',
        'department_id',
        'region',
        'store_id',
        'start_send_at',
        'end_send_at',
        'goods_type',
        'payment_voucher_status',
        'pageNum',
        'pageSize',
    ];

    // OA-员工商城-订单明细查询
    public static $validate_list_search = [
        'order_code'             => 'StrLenLe:20|>>>:order_code error',
        //订单编号
        'order_status'           => 'Arr|ArrLenGeLe:0,8|>>>:order_status error',
        'staff_id'               => 'IntGe:1|>>>:staff_id error',
        //员工工号
        'grant_staff_id'         => 'IntGe:1|>>>:grant_staff_id error',
        //核对人工号
        'region'                 => 'IntIn:' . ShopEnums::PAY_METHOD_WAGE_DEDUCTION . ',' . ShopEnums::PAY_METHOD_FLASH_PAY . ',' . ShopEnums::PAY_METHOD_OFFLINE_PAY . '|>>>:params error region',
        //支付方式
        'goods_name'             => 'StrLenGeLe:1,80|>>>:goods_name error',
        'goods_sku_code'         => 'StrLenGeLe:1,30|>>>:goods_sku_code error',
        'department_id'          => 'StrLenGeLe:1,30|>>>:department_id error',
        'order_start_date'       => 'DateTime|>>>:order_start_date error',
        //下单开始时间
        'order_end_date'         => 'DateTime|>>>:order_end_date error',
        //下单结束时间
        'start_send_at'          => 'DateTime|>>>:start_send_at error',
        //发货开始时间
        'end_send_at'            => 'DateTime|>>>:end_send_at error',
        //发货结束时间
        'goods_type'             => 'IntIn:' . ShopEnums::GOODS_TYPE_WORK_CLOTHES . ',' . ShopEnums::GOODS_TYPE_UNCLAIMED_PIECE . '|>>>:params error goods_type',
        //商品类型
        'payment_voucher_status' => 'IntIn:' . ShopEnums::GOODS_PAYMENT_VOUCHER_STATUS_ING . ',' . ShopEnums::GOODS_PAYMENT_VOUCHER_STATUS_ABNORMAL . ',' . ShopEnums::GOODS_PAYMENT_VOUCHER_STATUS_UPLOADED . '|>>>:params error payment_voucher_status',
        //支付凭证
        'fund_status'            => 'IntIn:' . ShopEnums::FUND_STATUS_REFUNDING . ',' . ShopEnums::FUND_STATUS_DONE . '|>>>:params error fund_status',
        //款项状态
        'pageNum'                => 'IntGe:1|>>>:page error',
        //当前页码
        'pageSize'               => 'IntGe:1|>>>:page_size error',
        //每页条数
    ];

    public static $validate_export = [
        'is_export' => 'Required|IntIn:0,1|>>>:param error is_export',
    ];

    // OA-员工商城-收款核对-非必要的筛选条件
    public static $not_must_collection_check_params = [
        'payment_voucher_at',
        'payment_voucher_audit_at',
        'start_payment_voucher_at',
        'end_payment_voucher_at',
        'order_start_date',
        'order_end_date',
        'created_at',
        'order_code',
        'staff_id',
        'grant_staff_id',
        'payment_voucher_status',
        'pageNum',
        'pageSize',
    ];

    // OA-员工商城-收款核对-待核对
    public static $validate_check_search = [
        'start_payment_voucher_at' => 'DateTime|>>>:start_payment_voucher_at error',//支付凭证上传开始时间
        'end_payment_voucher_at'   => 'DateTime|>>>:end_payment_voucher_at error',  //支付凭证上传结束时间
        'order_start_date'         => 'DateTime|>>>:order_start_date error',        //下单开始时间
        'order_end_date'           => 'DateTime|>>>:order_end_date error',          //下单结束时间
        'order_code'               => 'StrLenLe:20|>>>:order_code error',           //订单编号
        'staff_id'                 => 'IntGe:1|>>>:staff_id error',                 //员工工号
        'pageNum'                  => 'IntGe:1|>>>:page error',                     //当前页码
        'pageSize'                 => 'IntGe:1|>>>:page_size error',                //每页条数
    ];

    //OA-员工商城-收款核对-批量通过
    public static $validate_batch_approval = [
        'ids' => 'Required|Arr|ArrLenGeLe:1,100|>>>: ids error',
    ];

    //OA-员工商城-收款核对-通过 ，不通过
    public static $validate_share = [
        'id' => 'Required|IntGt:0|>>>:id error',
    ];

    //OA-员工商城-收款核对-不通过原因
    public static $validate_payment_voucher_reason = [
        'payment_voucher_reason' => 'Required|StrLenGeLe:0,500|>>>:payment_voucher_reason error',
    ];


    //OA-员工商城-收款核对-查看
    public static $validate_view = [
        'order_code' => 'Required|StrLenLe:20|>>>:order_code error', //订单编号
    ];

    private static $instance;

    private function __construct()
    {
    }


    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 获取订单状态、支付类型、订单类型、退款款项状态等枚举列表
     * @return array
     */
    public static function orderStatusEnums()
    {
        $lang                 = static::$language;
        $order_enums          = ShopEnums::getOrderStatus($lang);
        $pay_enums            = ShopEnums::payMethod($lang);
        $order_type_enums     = ShopEnums::orderType($lang);
        $fund_type_enums      = ShopEnums::fundStatus($lang);
        $order_enums_arr      = [];
        $pay_enums_arr        = [];
        $order_type_enums_arr = [];
        $fund_type_enums_arr  = [];
        foreach ($order_enums as $k => $v) {
            $order_enums_arr[$k] = ['key' => $k, 'value' => $v];
        }

        foreach ($pay_enums as $k1 => $v1) {
            $pay_enums_arr[$k1] = ['key' => $k1, 'value' => $v1];
        }

        foreach ($order_type_enums as $k1 => $v1) {
            $order_type_enums_arr[$k1] = ['key' => $k1, 'value' => $v1];
        }

        foreach ($fund_type_enums as $k1 => $v1) {
            $fund_type_enums_arr[$k1] = ['key' => $k1, 'value' => $v1];
        }

        //工服、无头件
        $goods_type_item = [];
        foreach (ShopEnums::$goods_type as $type_value => $type_lang_key) {
            $goods_type_item[] = [
                'value' => $type_value,
                'label' => static::$t->_($type_lang_key),
            ];
        }

        //支付凭证状态
        $payment_voucher_status = [];
        foreach (ShopEnums::$payment_voucher_status as $payment_voucher_value => $payment_voucher_lang_key) {
            $payment_voucher_status[] = [
                'value' => $payment_voucher_value,
                'label' => static::$t->_($payment_voucher_lang_key),
            ];
        }

        //支付凭证审核状态
        $payment_voucher_audit_status = [];
        foreach (ShopEnums::$payment_voucher_audit_status as $payment_voucher_audit_value => $payment_voucher_audit_lang_key) {
            $payment_voucher_audit_status[] = [
                'value' => $payment_voucher_audit_value,
                'label' => static::$t->_($payment_voucher_audit_lang_key),
            ];
        }

        return [
            'order_status'                 => $order_enums_arr,
            'pay_type'                     => $pay_enums_arr,
            'order_type'                   => $order_type_enums_arr,
            'fund_type'                    => $fund_type_enums_arr,
            'goods_type'                   => $goods_type_item,
            'payment_voucher_status'       => $payment_voucher_status,
            'payment_voucher_audit_status' => $payment_voucher_audit_status,
        ];
    }

    public function getList($condition, $type = 0)
    {
        $page_size = empty($condition['pageSize']) ? 20 : $condition['pageSize'];
        $page_num  = empty($condition['pageNum']) ? 1 : $condition['pageNum'];
        $offset    = $page_size * ($page_num - 1);

        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];

        $is_export = isset($condition['is_export']) && $condition['is_export'] == 1;

        if ($is_export) {
            ini_set('memory_limit', '2048M');
        }

        $lang = static::$language;
        try {
            $builder = $this->modelsManager->createBuilder();

            $builder->from(['sku' => InteriorOrdersGoodsSku::class]);

            $builder->leftjoin(InteriorOrders::class, 'main.order_code = sku.order_code', 'main');
            $builder->where('sku.id > 0');

            if (!empty($condition['order_start_date']) && !empty($condition['order_end_date'])) {
                $builder->betweenWhere('main.submit_at', $condition['order_start_date'], $condition['order_end_date']);
            }

            if (!empty($condition['order_status'])) {
                $builder->inWhere('main.order_status', $condition['order_status']);
            }
            if (!empty($condition['order_code'])) {
                $builder->andWhere('main.order_code = :order_code:', ['order_code' => $condition['order_code']]);
            }
            if (!empty($condition['staff_id'])) {
                $builder->andWhere('main.staff_id = :staff_id:', ['staff_id' => $condition['staff_id']]);
            }
            if (!empty($condition['store_id'])) {
                if (is_numeric($condition['store_id'])) {
                    $condition['store_id'] = [Enums::HEAD_OFFICE_STORE_FLAG];
                } else {
                    $condition['store_id'] = [$condition['store_id']];
                }
                $builder->inWhere('main.staff_store_id', $condition['store_id']);
            }
            if (!empty($condition['department_id'])) {
                // 部门以及子部门
                $deps                       = (new DepartmentService())->getChildrenListByDepartmentIdV2($condition['department_id'],
                    true);
                $condition['department_id'] = !empty($deps) ? array_values(array_merge([$condition['department_id']],
                    $deps)) : [$condition['department_id']];
                $builder->inWhere('main.node_department_id', $condition['department_id']);
            }

            if (!empty($condition['goods_name'])) {
                $goods_name = 'goods_name_' . static::getLanguage($lang);
                $builder->andWhere('sku.' . $goods_name . ' LIKE :goods_name:',
                    ['goods_name' => "%{$condition['goods_name']}%"]);
            }

            if (!empty($condition['goods_sku_code'])) {
                $builder->andWhere('sku.goods_sku_code = :goods_sku_code:',
                    ['goods_sku_code' => $condition['goods_sku_code']]);
            }

            if (!empty($condition['start_send_at']) && !empty($condition['end_send_at'])) {
                $builder->betweenWhere('main.send_at', $condition['start_send_at'], $condition['end_send_at']);
            }

            if (!empty($condition['region'])) {
                $builder->andWhere('main.pay_method = :pay_method:', ['pay_method' => $condition['region']]);
            }
            if ($type == ShopEnums::OUT_STATUS_SUCCEED) {
                $builder->andWhere('main.out_status = :out_status:', ['out_status' => ShopEnums::OUT_STATUS_FAIL]);
            }

            if (!empty($condition['goods_type'])) {
                $builder->andWhere('main.goods_type = :goods_type:', ['goods_type' => $condition['goods_type']]);
            }

            if (!empty($condition['payment_voucher_status'])) {
                $builder->andWhere('main.payment_voucher_status = :payment_voucher_status:',
                    ['payment_voucher_status' => $condition['payment_voucher_status']]);
            }
            if (!empty($condition['fund_status'])) {
                $builder->andWhere('main.fund_status = :fund_status:', ['fund_status' => $condition['fund_status']]);
            }
            $count_info = $builder->columns('COUNT(sku.id) as t_count')->getQuery()->getSingleResult();
            $count      = $count_info->t_count ?? 0;

            $items = [];
            if ($count) {
                if ($type == ShopEnums::OUT_STATUS_SUCCEED) {
                    $builder->columns([
                        'main.id',
                        'main.order_code',
                        'main.staff_id',
                        'main.staff_name',
                        'main.node_department_id',
                        'main.staff_store_id',
                        'main.created_at',
                        'main.out_status',
                        'main.fail_num',
                        'main.fail_reason',
                        'main.goods_type',
                        'main.order_status',
                    ]);
                    $builder->orderBy('main.id desc');
                } else {
                    $builder->columns([
                        'main.order_code',
                        'main.staff_store_id',
                        'main.node_department_id',
                        'main.staff_id',
                        'main.staff_name',
                        'main.send_at',
                        'main.order_code',
                        'main.order_status',
                        'main.pay_method',
                        'main.submit_at',
                        'main.out_sn',
                        'sku.goods_name_zh',
                        'sku.goods_name_en',
                        'sku.goods_name_th',
                        'main.goods_type',
                        'sku.goods_sku_code',
                        'sku.buy_price',
                        'sku.buy_num',
                    ]);

                    $builder->orderBy('sku.id desc');
                }
                if (!$is_export) {
                    $builder->limit($page_size, $offset);
                } else {
                    $builder->limit(200000);
                }
                $items = $builder->getQuery()->execute()->toArray();
            }

            if ($type == ShopEnums::OUT_STATUS_SUCCEED) {
                if (!empty($items)) {
                    $store_ids    = array_values(array_filter(array_unique(array_column($items, 'staff_store_id'))));
                    $staff_id_ids = array_values(array_filter(array_unique(array_column($items, 'staff_id'))));

                    $storeArr = SysStoreModel::find(
                        [
                            'conditions' => "id in ({ids:array})",
                            'bind'       => ['ids' => $store_ids],
                            'columns'    => ['id', 'name'],
                        ]);
                    if ($storeArr) {
                        $storeArrList = array_column($storeArr->toArray(), 'name', 'id');
                    }

                    $staff_ids   = HrStaffInfoModel::find(
                        [
                            'conditions' => "staff_info_id in ({ids:array})",
                            'bind'       => ['ids' => $staff_id_ids],
                            'columns'    => ['staff_info_id as id', 'node_department_id as department_id'],
                        ]);
                    $staffIdsArr = [];
                    if ($staff_ids) {
                        $staffIdsArr = array_column($staff_ids->toArray(), 'department_id', 'id');
                    }

                    $departmentArr = DepartmentModel::find(
                        [
                            'conditions' => "id in ({ids:array})",
                            'bind'       => ['ids' => array_values($staffIdsArr)],
                            'columns'    => ['id', 'name'],
                        ]);
                    if ($departmentArr) {
                        $departmentArrList = array_column($departmentArr->toArray(), 'name', 'id');
                        foreach ($staffIdsArr as $key => $staff) {
                            $rs[$key] = $departmentArrList[$staff];
                        }
                    }

                    foreach ($items as &$value) {
                        if (is_numeric($value['staff_store_id'])) {
                            $value['staff_store_name'] = 'Header Office';
                        } else {
                            $value['staff_store_name'] = $storeArrList[$value['staff_store_id']] ?? '';
                        }

                        $value['node_department_name'] = $rs[$value['staff_id']] ?? '';
                        $value['out_status']           = $value['out_status'] == ShopEnums::OUT_STATUS_SUCCEED ? static::$t->_('hc_success') : static::$t->_('hc_failure');
                        $value['created_at']           = date('Y-m-d H:i:s', strtotime($value['created_at']));
                        $value['fail_reason']          = $value['fail_reason'] ?? '';
                        $value['goods_type_name']      = static::$t->_(ShopEnums::$goods_type[$value['goods_type']]);
                    }
                }
            } else {
                $items = $this->handleData($items, $lang, $is_export);
            }
            if ($is_export) {
                $excel_data = $this->exportOrder($items);
                if (empty($excel_data['data'])) {
                    $data    = '';
                    $code    = ErrCode::$BUSINESS_ERROR;
                    $message = 'Excel export failed';
                } else {
                    $data = !empty($excel_data['data']) ? $excel_data['data'] : '';
                }
            } else {
                $data = [
                    'items'      => $items,
                    'pagination' => [
                        'current_page' => $page_num,
                        'per_page'     => $page_size,
                        'total_count'  => (int)$count,
                    ],
                ];
            }
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning('员工商城 - 列表异常: ' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    public function handleData($items, $lang, $is_export = false)
    {
        if (empty($items)) {
            return [];
        }

        $order_enums = ShopEnums::getOrderStatus($lang);
        $pay_enums   = ShopEnums::payMethod($lang);

        $store_ids           = array_values(array_filter(array_unique(array_column($items, 'staff_store_id'))));
        $node_department_ids = array_values(array_filter(array_unique(array_column($items, 'node_department_id'))));
        $store_arr_list      = $staff_ids = [];
        if (!empty($store_ids)) {
            $store_arr = SysStoreModel::find(
                [
                    'conditions' => "id in ({ids:array})",
                    'bind'       => ['ids' => $store_ids],
                    'columns'    => ['id', 'name'],
                ]);
            if ($store_arr) {
                $store_arr_list = array_column($store_arr->toArray(), 'name', 'id');
            }
        }

        if ($node_department_ids) {
            $department_arr = DepartmentModel::find(
                [
                    'conditions' => "id in ({ids:array})",
                    'bind'       => ['ids' => $node_department_ids],
                    'columns'    => ['id', 'name'],
                ]);
        }
        $rs = [];
        if ($department_arr) {
            $rs = array_column($department_arr->toArray(), 'name', 'id');
        }

        // 员工列表
        $staff_ids = array_column($items, 'staff_id');
        $staffs    = (new HrStaffRepository())->getStaffListByStaffIds($staff_ids);

        //雇佣类型
        $hire_type_enum = EnumsService::getInstance()->getHireTypeEnum();//雇佣类型枚举
        // 导出的数据结构
        if ($is_export) {
            $new_data = [];
            foreach ($items as $key => $value) {
                $new_data[] = [
                    $value['staff_id'],
                    $value['staff_name'],
                    $hire_type_enum[$staffs[$value['staff_id']]['hire_type']] ?? '',
                    $value['staff_store_id'],
                    is_numeric($value['staff_store_id']) ? 'Header Office' : ($store_arr_list[$value['staff_store_id']] ?? ''),
                    $value['node_department_id'],
                    $rs[$value['node_department_id']] ?? '',
                    $value['order_code'],
                    empty($value['order_status']) && $value['goods_type'] == ShopEnums::GOODS_TYPE_UNCLAIMED_PIECE ? static::$t->_('wait_pay') : $order_enums[$value['order_status']],
                    static::$t->_(ShopEnums::$goods_type[$value['goods_type']]),
                    $value['goods_sku_code'],
                    $value['goods_name_' . static::getLanguage($lang)],
                    $value['buy_price'],
                    $value['buy_num'],
                    $pay_enums[$value['pay_method']] ?? '',
                    $value['submit_at'],
                    $value['send_at'],
                    $value['out_sn'],
                ];
            }

            unset($items);

            return $new_data;
        } else {
            // 页面列表的数据结构
            $staff_state_enums = self::staffStateEnums();

            foreach ($items as &$value) {
                $value['staff_state']       = $staff_state_enums[$staffs[$value['staff_id']]['state'] ?? ''] ?? '';
                $value['hire_type_text']    = $hire_type_enum[$staffs[$value['staff_id']]['hire_type']] ?? '';
                $value['pay_method']        = $pay_enums[$value['pay_method']];
                $value['order_status_text'] = empty($value['order_status']) && $value['goods_type'] == ShopEnums::GOODS_TYPE_UNCLAIMED_PIECE ? static::$t->_('wait_pay') : $order_enums[$value['order_status']];

                if (is_numeric($value['staff_store_id'])) {
                    $value['staff_store_name'] = 'Header Office';
                } else {
                    $value['staff_store_name'] = $store_arr_list[$value['staff_store_id']] ?? '';
                }
                $value['node_department_name'] = $rs[$value['node_department_id']] ?? '';
                $value['goods_type_name']      = static::$t->_(ShopEnums::$goods_type[$value['goods_type']]);
            }

            return $items;
        }
    }

    public function exportOrder($items)
    {
        $header = [
            self::$t['csr_field_staff_id'],             //员工号
            self::$t['csr_field_staff_name'],           //员工姓名
            self::$t['hire_type'],                      //雇佣类型
            self::$t['csr_field_cost_store_id'],        //网点ID
            self::$t['csr_field_staff_department_name'],//所属部门名称
            self::$t['csr_field_staff_department_id'],  //部门ID
            self::$t['csr_field_cost_store_name'],      //网点名称
            self::$t['csr_field_order_code'],           //订单编号
            self::$t['csr_field_order_status'],         //订单状态
            self::$t['mall_excel_field_goods_type'],    //商品类型
            self::$t['csr_field_bar_code'],             //商品编码
            self::$t['interior_goods_shop_goods_name'], //商品名称
            self::$t['csr_field_price'],                //商品单价
            self::$t['csr_field_buy_num'],              //购买数量
            self::$t['csr_field_pay_type'],             //支付方式
            self::$t['csr_field_submit_time'],          //下单时间
            self::$t['csr_field_send_time'],            //发货时间
            self::$t['csr_field_out_sn'],               //scm 出库单号
        ];

        $fileName = 'shop_export_order_' . date('YmdHis"');

        return $this->exportExcel($header, $items, $fileName);
    }

    static public function staffStateEnums()
    {
        return [
            '1' => self::$t->_('on_the_job'),
            '2' => self::$t->_('leave_the_job'),
            '3' => self::$t->_('stop_the_job'),
        ];
    }


    /**
     * 修改
     * @Date: 2022-02-18 16:50
     * @return array
     **/
    public function saveInteriorOrders($params)
    {
        $code           = ErrCode::$SUCCESS;
        $message        = '';
        $interiorOrders = InteriorOrders::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => ['id' => $params['id']],
        ]);
        $info           = [];
        if (!empty($interiorOrders)) {
            $info = $interiorOrders->toArray();
            //修复多邮编同步scm失败问题，修改为只同步第一个邮编到scm
            if ($params['receive_postal_code']) {
                $postal_code_arr               = explode(',', $params['receive_postal_code']);
                $params['receive_postal_code'] = $postal_code_arr[0];
            }
            $ac = new ApiClient('by', '', 'save_interior_orders_scm', static::$language);
            $ac->setParams([
                $params,
            ]);
            $res  = $ac->execute();
            $code = $res['result']['code'] ?? '';
        } else {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
        }
        $info_data = [];
        if (!empty($code)) {
            $info_data = InteriorOrders::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $params['id']],
            ])->toArray();
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $code == ErrCode::$SUCCESS ? $info_data : '',
        ];
    }

    /**
     * 查询详情
     * @Date: 2022-02-17 15:57
     * @return array
     */
    public function detailInfo($params)
    {
        $code           = ErrCode::$SUCCESS;
        $message        = '';
        $interiorOrders = InteriorOrders::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => ['id' => $params['id']],
            'columns'    => [
                'id',
                'order_code',
                'staff_id',
                'staff_name',
                'node_department_id',
                'staff_store_id',
                'node_sn',
                'receive_province_name',
                'receive_city_name',
                'receive_district_name',
                'receive_postal_code',
                'created_at',
                'fail_num',
                'fail_reason',
                'out_status',
            ],
        ]);
        $info           = [];
        if (!empty($interiorOrders)) {
            $info                         = $interiorOrders->toArray();
            $sysStore                     = SysStore::findFirst(
                [
                    'conditions' => 'id = :id:',
                    'bind'       => ['id' => $info['staff_store_id']],
                ]);
            $staff_id                     = HrStaffInfoModel::getUserInfo($info['staff_id']);
            $departmentArr                = DepartmentModel::findFirst(
                [
                    'conditions' => 'id = :id:',
                    'bind'       => ['id' => $staff_id['node_department_id']],
                ]);
            $info['node_department_name'] = $departmentArr->name;
            $info['staff_store_name']     = is_numeric($info['staff_store_id']) ? 'Header Office' : $sysStore->name;
            $info['out_status']           = $info['out_status'] == 1 ? static::$t->_('hc_success') : static::$t->_('hc_failure');
            $info['created_at']           = date('Y-m-d', strtotime($info['created_at']));
            $info['fail_reason']          = $info['fail_reason'] ? $info['fail_reason'] : '';
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $info,
        ];
    }

    /**
     * 省市区邮编
     *
     * @Date: 2022-02-17 16:42
     * @param $params
     * @return array
     */
    public static function getAreaList($params)
    {
        $code     = ErrCode::$SUCCESS;
        $message  = '';
        $data     = [];
        $lang_arr = [
            'en'    => 'en_name',
            'th'    => 'name',
            'zh-CN' => 'en_name',
            'zh'    => 'en_name',
        ];

        $name = $lang_arr[static::$language] ?? 'en_name';

        if (empty($params['level'])) {
            $params['level'] = 1;
        }
        switch ($params['level']) {
            case 1:
                $data = SysProvinceModel::find([
                    'conditions' => 'deleted = :deleted:',
                    'bind'       => ['deleted' => GlobalEnums::IS_NO_DELETED],
                    'columns'    => 'code, ' . $name . ' as name',
                ])->toArray();
                break;
            case 2:
                $data = SysCityModel::find([
                    'conditions' => 'province_code = :code: AND deleted = :deleted:',
                    'bind'       => [
                        'code'    => $params['code'],
                        'deleted' => GlobalEnums::IS_NO_DELETED,
                    ],
                    'columns'    => 'code, ' . $name . ' as name',
                ])->toArray();
                break;
            case 3:
                $data = SysDistrictModel::find([
                    'conditions' => 'city_code = :city_code: AND deleted = :deleted:',
                    'bind'       => [
                        'city_code' => $params['code'],
                        'deleted'   => GlobalEnums::IS_NO_DELETED,
                    ],
                    'columns'    => 'code, postal_code, ' . $name . ' as name',
                ])->toArray();
                break;
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => ['list' => $data],
        ];
    }


    /**
     * 员工商城-待核对-通过
     * @param int $id 通过的订单id
     * @param array $user 登录用户数据
     * @return array
     */
    public function approval(int $id, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = '';
        try {
            $interior_orders = InteriorOrdersModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $id],
            ]);
            if (empty($interior_orders)) {
                throw new ValidationException(static::$t->_('empty_data'), ErrCode::$VALIDATE_ERROR);
            }
            if ($interior_orders->payment_voucher_audit_status == ShopEnums::PAYMENT_VOUCHER_AUDIT_STATUS_FINISH) {
                throw new ValidationException(static::$t->_('payment_voucher_approval_pass'), ErrCode::$VALIDATE_ERROR);
            }
            $this->approvalSave(['id' => $id, 'is_approval' => true], $user);
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code    = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning('员工商城-待核对-通过异常: ' . $e->getMessage());
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('员工商城-待核对-通过错误: ' . $e->getMessage());
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
        ];
    }


    /**
     * 员工商城-待核对-通过修改
     * @param array $params 通过的订单数据
     * @param array $user 登录用户数据
     * @return array
     */
    public function approvalSave(array $params, array $user)
    {
        $interior_orders = InteriorOrdersModel::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => ['id' => $params['id']],
        ]);
        if (empty($interior_orders)) {
            throw new ValidationException(static::$t->_('data_not_exist_by_id', ['id' => $params['id']]),
                ErrCode::$VALIDATE_ERROR);
        }
        if ($interior_orders->payment_voucher_audit_status == ShopEnums::PAYMENT_VOUCHER_AUDIT_STATUS_WAIT) {
            //审核通过
            if ($params['is_approval']) {
                $interior_orders->payment_voucher_audit_status = ShopEnums::PAYMENT_VOUCHER_AUDIT_STATUS_FINISH;
                $interior_orders->payment_voucher_status       = ShopEnums::GOODS_PAYMENT_VOUCHER_STATUS_UPLOADED;
                $interior_orders->out_status                   = 0;
            } else {
                //审核不通过
                $interior_orders->payment_voucher_audit_status = ShopEnums::PAYMENT_VOUCHER_AUDIT_STATUS_FAIL;
                $interior_orders->payment_voucher_status       = ShopEnums::GOODS_PAYMENT_VOUCHER_STATUS_ABNORMAL;
                $interior_orders->payment_voucher_reason       = $params['payment_voucher_reason'];
                $interior_orders->order_status                 = ShopEnums::ORDER_STATUS_CUSTOMER_CANCEL_CODE;
            }
            $interior_orders->payment_voucher_check_staff_id = $user['id'];
            $interior_orders->payment_voucher_audit_at       = date('Y-m-d H:i:s', time());
            if ($interior_orders->save() === false) {
                throw new BusinessException('员工商城-核对-通过失败, 原因可能是: ' . get_data_object_error_msg($interior_orders),
                    ErrCode::$INTERIOR_COLLECTION_CHECK_SAVE_ERROR);
            }
        } elseif (!$params['is_approval']) { //不是待核对 提交的是不通过就提示
            throw new ValidationException(static::$t->_('payment_voucher_audit_status_err'), ErrCode::$VALIDATE_ERROR);
        }
        return true;
    }

    /**
     * 员工商城-待核对-批量通过
     * @param array $params 批量通过的订单id
     * @param array $user 登录用户数据
     * @return array
     */
    public function batchApproval(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = '';
        try {
            $interior_orders = InteriorOrdersModel::find([
                'conditions' => 'id in ({ids:array})',
                'bind'       => ['ids' => $params['ids']],
            ])->toArray();
            if (empty($interior_orders)) {
                throw new ValidationException(static::$t->_('empty_data'), ErrCode::$VALIDATE_ERROR);
            }

            foreach ($interior_orders as $orders) {
                $this->approvalSave(['id' => $orders['id'], 'is_approval' => true], $user);
            }
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code    = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning('员工商城-待核对-批量通过异常: ' . $e->getMessage());
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('员工商城-待核对-批量通过错误: ' . $e->getMessage());
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
        ];
    }


    /**
     * 员工商城-待核对-不通过
     * @param array $params 不通过数据
     * @param array $user 登录用户数据
     * @return array
     */
    public function noApproval(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = '';
        try {
            $interior_orders = InteriorOrdersModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $params['id']],
            ]);
            if (empty($interior_orders)) {
                throw new ValidationException(static::$t->_('empty_data'), ErrCode::$VALIDATE_ERROR);
            }
            if ($interior_orders->payment_voucher_audit_status == ShopEnums::PAYMENT_VOUCHER_AUDIT_STATUS_FAIL) {
                throw new ValidationException(static::$t->_('payment_voucher_approval_no_pass'),
                    ErrCode::$VALIDATE_ERROR);
            }

            $this->approvalSave($params, $user);
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code    = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning('员工商城-待核对-不通过异常: ' . $e->getMessage());
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('员工商城-待核对-不通过错误: ' . $e->getMessage());
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
        ];
    }


    /**
     *  员工商城-收款核对-待核对-统计金额
     * @param array $params 不通过数据
     * @return array
     */
    public function amountSum(array $params)
    {
        $code    = ErrCode::$SUCCESS;
        $message = '';
        try {
            $interior_orders = InteriorOrdersModel::find([
                'conditions' => 'id in ({ids:array})',
                'bind'       => ['ids' => $params['ids']],
                'columns'    => 'pay_amount',
            ])->toArray();
            if (empty($interior_orders)) {
                throw new ValidationException(static::$t->_('empty_data'), ErrCode::$VALIDATE_ERROR);
            }
            $amount = 0;
            foreach ($interior_orders as $orders) {
                $amount = bcadd($amount, $orders['pay_amount'], 2);
            }
            $data['amount_sum'] = $amount;
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code    = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning('员工商城-待核对-不通过异常: ' . $e->getMessage());
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('员工商城-待核对-不通过错误: ' . $e->getMessage());
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }


}
