<?php

namespace App\Modules\Shop\Controllers;

use App\Library\Validation\Validation;
use App\Modules\Material\Services\BaseService;
use App\Modules\Shop\Services\GoodsBatchService;
use App\Util\RedisKey;

/**
 * 员工商城-批量下单工服
 * Class GoodsBatchController
 * @package App\Modules\Shop\Controllers
 */
class GoodsBatchController extends BaseController
{
    /**
     * 员工商城-批量下单工服-列表
     * @Permission(action='shop.goods_batch.list')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/75957
     **/
    public function listAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, GoodsBatchService::$not_must_params);
        Validation::validate($params, GoodsBatchService::$validate_list_search);
        $data = GoodsBatchService::getInstance()->getList($params, $this->user);
        return $this->returnJson($data['code'], $data['message'], $data['data']);
    }


    /**
     * 员工商城-批量下单工服-列表-导出
     * @Permission(action='shop.goods_batch.export')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/75922
     **/
    public function exportListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, GoodsBatchService::$not_must_params);
        Validation::validate($params, GoodsBatchService::$validate_list_search);
        $lock_key = md5(RedisKey::BATCH_EXPORT . $this->user['id']);
        $data     = $this->atomicLock(function () use ($params) {
            return GoodsBatchService::getInstance()->getExportList($params, $this->user);
        }, $lock_key, 15);
        return $this->returnJson($data['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR,
            $data['message'] ?? $this->t->_('sys_processing'), $data['data'] ?? []);
    }

    /**
     * 员工商城-批量下单工服-查看
     * @Permission(action='shop.goods_batch.view')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/75907
     */
    public function viewAction()
    {
        $params = $this->request->get();
        Validation::validate($params, GoodsBatchService::$validate_share);
        $data = GoodsBatchService::getInstance()->getDetail($params['id']);
        return $this->returnJson($data['code'], $data['message'], $data['data']);
    }

    /**
     * 员工商城-批量下单工服-添加
     * @Permission(action='shop.goods_batch.add')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/75932
     **/
    public function addAction()
    {
        $params = $this->request->get();
        Validation::validate($params, GoodsBatchService::$validate_add);
        $lock_key = md5('goods_goods_batch_add' . $this->user['id'] . '_' . $params['apply_no']);
        $data     = $this->atomicLock(function () use ($params) {
            return GoodsBatchService::getInstance()->addOne($params, $this->user);
        }, $lock_key, 10);

        return $this->returnJson($data['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR,
            $data['message'] ?? $this->t->_('sys_processing'), $data['data'] ?? []);
    }


    /**
     * 员工商城-批量下单工服-添加-初始化数据
     * @Permission(action='shop.goods_batch.add')
     * @api  https://yapi.flashexpress.pub/project/133/interface/api/75912
     **/
    public function getAddDefaultAction()
    {
        $data = GoodsBatchService::getInstance()->getAddDefault($this->user);
        return $this->returnJson($data['code'], $data['message'], $data['data']);
    }


    /**
     * 员工商城-批量下单工服-添加-批量下发工服部门
     * @Permission(action='shop.goods_batch.add')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/76367
     **/
    public function getAutoOrdersDepartmentAction()
    {
        $params = $this->request->get();
        Validation::validate($params, GoodsBatchService::$validate_auto_orders_department);
        $data = GoodsBatchService::getInstance()->getAutoOrdersDepartment($params);
        return $this->returnJson($data['code'], $data['message'], $data['data']);
    }


    /**
     * 获取添加初始化选择部门和网点后的详细信息数据
     * @Permission(action='shop.goods_batch.add')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/75917
     **/
    public function getAddInfoListAction()
    {
        $params = $this->request->get();
        Validation::validate($params, GoodsBatchService::$validate_add_info_list);
        $data = GoodsBatchService::getInstance()->getAddInfoList($params, $this->user);
        return $this->returnJson($data['code'], $data['message'], $data['data']);
    }


    /**
     * 员工商城-批量下单工服-新建-详细信息-导出
     * @Permission(action='shop.goods_batch.view')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/75922
     **/
    public function exportAction()
    {
        $params = $this->request->get();
        Validation::validate($params, GoodsBatchService::$validate_add_info_list);
        $lock_key = md5(RedisKey::BATCH_INFO_EXPORT . $this->user['id']);
        $data     = $this->atomicLock(function () use ($params) {
            return GoodsBatchService::getInstance()->getExportInfoList($params);
        }, $lock_key, 15);
        return $this->returnJson($data['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR,
            $data['message'] ?? $this->t->_('sys_processing'), $data['data'] ?? []);
    }


    /**
     * 员工商城-批量下单工服-新建-详细信息-覆盖导入
     * @Permission(action='shop.goods_batch.export')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/75927
     **/
    public function importAction()
    {
        $params = $this->request->get();
        Validation::validate($params, GoodsBatchService::$validate_add_info_list);
        try {
            $excel_file = $this->request->getUploadedFiles();
            $excel_data = GoodsBatchService::getInstance()->openFileData($excel_file);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }

        $lock_key = md5(RedisKey::GOODS_BATCH_IMPORT . $this->user['id']);
        $data     = $this->atomicLock(function () use ($params, $excel_data) {
            return GoodsBatchService::getInstance()->coverImport($params, $excel_data);
        }, $lock_key, 15);

        return $this->returnJson($data['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR,
            $data['message'] ?? $this->t->_('sys_processing'), $data['data'] ?? []);
    }


    /**
     * 员工商城-批量下单工服-列表-作废
     * @Permission(action='shop.goods_batch.cancel')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/75942
     **/
    public function cancelAction()
    {
        $params = $this->request->get();
        Validation::validate($params, GoodsBatchService::$validate_share);
        $data = GoodsBatchService::getInstance()->cancel($params['id'], $this->user);
        return $this->returnJson($data['code'], $data['message']);
    }


    /**
     * 员工商城-批量下单工服-查看-导出
     * @Permission(action='shop.goods_batch.view')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/76557
     **/
    public function exportDetailAction()
    {
        $params = $this->request->get();
        Validation::validate($params, GoodsBatchService::$validate_share);
        $lock_key = md5(RedisKey::BATCH_DETAIL_EXPORT . $this->user['id']);
        $data     = $this->atomicLock(function () use ($params) {
            return GoodsBatchService::getInstance()->getExportDetail($params);
        }, $lock_key, 15);

        return $this->returnJson($data['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR,
            $data['message'] ?? $this->t->_('sys_processing'), $data['data'] ?? []);
    }


    /**
     * 根据员工所属网点、部门获取商品数据
     * @Permission(action='shop.goods_batch.add')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/75917
     **/
    public function getDepartmentByStoreGoodsListAction()
    {
        $params = $this->request->get();
        Validation::validate($params, GoodsBatchService::$validate_goods_list);
        $data = GoodsBatchService::getInstance()->getDepartmentByStoreGoodsList($params);
        return $this->returnJson($data['code'], $data['message'], $data['data']);
    }


}
