<?php

namespace App\Modules\Shop\Controllers;

use App\Library\Enums\ShopEnums;
use App\Library\Validation\Validation;
use App\Modules\Material\Services\BaseService;
use App\Modules\Shop\Services\GoodsBatchOrderService;
use App\Modules\Shop\Services\GoodsBatchService;
use App\Library\Validation\ValidationException;
use App\Library\ErrCode;
use App\Util\RedisKey;


/**
 * 员工商城-批量工服确认
 * Class GoodsBatchCheckController
 * @package App\Modules\Shop\Controllers
 */
class GoodsBatchCheckController extends BaseController
{
    /**
     * 员工商城-批量工服确认-列表
     * @Permission(action='shop.goods_batch_check.list')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/75947
     **/
    public function listAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, GoodsBatchService::$not_must_params);
        Validation::validate($params, GoodsBatchService::$validate_check_list_search);
        $params['is_check'] = true;
        $data               = GoodsBatchService::getInstance()->getList($params, $this->user);
        return $this->returnJson($data['code'], $data['message'], $data['data']);
    }

    /**
     * 员工商城-批量工服确认-列表-导出
     * @Permission(action='shop.goods_batch_check.export')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/75952
     **/
    public function exportListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, GoodsBatchService::$not_must_params);
        Validation::validate($params, GoodsBatchService::$validate_check_list_search);
        $params['is_check'] = true;
        $lock_key           = md5(RedisKey::BATCH_CHECK_EXPORT . $this->user['id']);
        $data               = $this->atomicLock(function () use ($params) {
            return GoodsBatchService::getInstance()->getExportList($params, $this->user);
        }, $lock_key, 15);
        return $this->returnJson($data['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR,
            $data['message'] ?? $this->t->_('sys_processing'), $data['data'] ?? []);
    }

    /**
     * 员工商城-批量工服确认-列表-查看
     * @Permission(action='shop.goods_batch_check.view')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/76022
     */
    public function viewAction()
    {
        $params = $this->request->get();
        Validation::validate($params, GoodsBatchService::$validate_share);
        $data = GoodsBatchService::getInstance()->getDetail($params['id'], true);
        return $this->returnJson($data['code'], $data['message'], $data['data']);
    }

    /**
     * 员工商城-批量工服确认-列表-核对-详情-删除
     * @Permission(action='shop.goods_batch_check.check')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/76007
     */
    public function delAction()
    {
        $params = $this->request->get();
        Validation::validate($params, GoodsBatchService::$validate_share);
        $data = GoodsBatchService::getInstance()->del($params['id']);
        return $this->returnJson($data['code'], $data['message'], $data['data'] ?? []);
    }


    /**
     * 员工商城-批量下单工服确认-核对-详细信息-取消发放
     * @Permission(action='shop.goods_batch_check.check')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/76787
     **/
    public function cancelAction()
    {
        $params = $this->request->get();
        Validation::validate($params, GoodsBatchService::$validate_share);
        $data = GoodsBatchService::getInstance()->cancel($params['id'], $this->user,
            ShopEnums::INTERIOR_BATCH_PAGE_SOURCE_CANCEL);
        return $this->returnJson($data['code'], $data['message'], $data['data'] ?? []);
    }

    /**
     * 员工商城-批量下单工服确认-核对-详细信息-核对数据提交 ,确定无误
     * @Permission(action='shop.goods_batch_check.check')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/76037
     **/
    public function checkSaveAction()
    {
        $params = $this->request->get();
        Validation::validate($params, GoodsBatchService::$validate_check_save);
        $data = GoodsBatchService::getInstance()->checkSave($params, $this->user);
        return $this->returnJson($data['code'], $data['message'], $data['data'] ?? []);
    }


    /**
     * 员工商城-批量下单工服确认-核对-导出
     * @Permission(action='shop.goods_batch.view')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/75922
     **/
    public function exportAction()
    {
        $params = $this->request->get();
        Validation::validate($params, GoodsBatchService::$validate_share);
        $lock_key = md5(RedisKey::BATCH_INFO_CHECK_EXPORT . $this->user['id']);
        $data     = $this->atomicLock(function () use ($params) {
            return GoodsBatchService::getInstance()->getCheckExportInfoList($params);
        }, $lock_key, 15);
        return $this->returnJson($data['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR,
            $data['message'] ?? $this->t->_('sys_processing'), $data['data'] ?? []);
    }


    /**
     * 员工商城-批量下单工服确认-核对-详细信息-查看-导出
     * @Permission(action='shop.goods_batch_check.check')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/76017
     **/
    public function exportDetailAction()
    {
        $params = $this->request->get();
        Validation::validate($params, GoodsBatchService::$validate_share);
        $lock_key = md5(RedisKey::BATCH_DETAIL_CHECK_EXPORT . $this->user['id']);
        $data     = $this->atomicLock(function () use ($params) {
            return GoodsBatchService::getInstance()->getExportDetail($params);
        }, $lock_key, 15);
        return $this->returnJson($data['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR,
            $data['message'] ?? $this->t->_('sys_processing'), $data['data'] ?? []);
    }

    /**
     * 员工商城-批量下单工服确认-核对-详细信息-一建清理
     * @Permission(action='shop.goods_batch_check.check')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/76012
     **/
    public function clearAction()
    {
        $params = $this->request->get();
        Validation::validate($params, GoodsBatchService::$validate_share);
        $data = GoodsBatchService::getInstance()->clear($params['id']);
        return $this->returnJson($data['code'], $data['message'], $data['data'] ?? []);
    }


    /**
     * 员工商城-批量下单工服确认-核对-详细信息-覆盖导入
     * @Permission(action='shop.goods_batch_check.check')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/76032
     **/
    public function importAction()
    {
        $params = $this->request->get();
        Validation::validate($params, GoodsBatchService::$validate_check_import);
        try {
            $excel_file = $this->request->getUploadedFiles();
            $excel_data = GoodsBatchService::getInstance()->openFileData($excel_file);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }

        $lock_key = md5(RedisKey::BATCH_CHECK_IMPORT . $this->user['id']);
        $data     = $this->atomicLock(function () use ($params, $excel_data) {
            return GoodsBatchService::getInstance()->coverImport($params, $excel_data);
        }, $lock_key, 10);

        return $this->returnJson($data['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR,
            $data['message'] ?? $this->t->_('sys_processing'), $data['data'] ?? []);
    }


}
