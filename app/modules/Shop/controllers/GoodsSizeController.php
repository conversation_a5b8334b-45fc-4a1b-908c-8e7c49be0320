<?php

namespace App\Modules\Shop\Controllers;


use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Enums\DownloadCenterEnum;
use App\Modules\Material\Services\BaseService;
use App\Modules\Shop\Services\GoodsSizeService;
use App\Modules\Common\Services\DownloadCenterService;
use App\Util\RedisKey;

/**
 * 员工商城-员工工服尺码
 * Class GoodsSizeController
 * @package App\Modules\Shop\Controllers
 */
class GoodsSizeController extends BaseController
{
    /**
     * 员工工服尺码枚举配置
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/75847
     **/
    public function enumsAction()
    {
        $data = GoodsSizeService::getInstance()->getEnumsSetting();
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }

    /**
     * 员工商城-员工工服尺码-列表
     * @Permission(action='shop.goods_size.list')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/75852
     **/
    public function listAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, GoodsSizeService::$not_must_params);
        Validation::validate($params, GoodsSizeService::$validate_list_search);
        $params['is_check'] = false;//是否是工服确认
        $data               = GoodsSizeService::getInstance()->getList($params, $this->user);
        return $this->returnJson($data['code'], $data['message'], $data['data']);
    }

    /**
     * 员工商城-员工工服尺码-列表-查看
     * @Permission(action='shop.goods_size.view')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/75857
     */
    public function viewAction()
    {
        $params = $this->request->get();
        Validation::validate($params, GoodsSizeService::$validate_share);
        $data = GoodsSizeService::getInstance()->getDetail($params['id']);
        return $this->returnJson($data['code'], $data['message'], $data['data']);
    }

    /**
     * 员工商城-员工工服尺码-添加
     * @Permission(action='shop.goods_size.add')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/75862
     **/
    public function addAction()
    {
        $params = $this->request->get();
        Validation::validate($params, GoodsSizeService::$validate_add);
        $data = GoodsSizeService::getInstance()->addOne($params, $this->user);
        return $this->returnJson($data['code'], $data['message']);
    }

    /**
     * 员工商城-员工工服尺码-新建-员工数据
     * @Permission(action='shop.goods_size.add')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/75867
     **/
    public function staffListAction()
    {
        $params = $this->request->get();
        Validation::validate($params, GoodsSizeService::$validate_staff_list);
        $data = GoodsSizeService::getInstance()->staffList($params, $this->user);
        return $this->returnJson($data['code'], $data['message'], $data['data']);
    }


    /**
     * 员工商城-员工工服尺码-编辑
     * @Permission(action='shop.goods_size.save')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/75872
     **/
    public function saveAction()
    {
        $params = $this->request->get();
        Validation::validate($params, array_merge(GoodsSizeService::$validate_share, GoodsSizeService::$validate_edit));
        $data = GoodsSizeService::getInstance()->saveOne($params, $this->user);
        return $this->returnJson($data['code'], $data['message']);
    }

    /**
     * 员工商城-员工工服尺码-导出
     * @Permission(action='shop.goods_size.export')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/75877
     **/
    public function exportAction()
    {
        $params            = $this->request->get();
        $params            = BaseService::handleParams($params, GoodsSizeService::$not_must_params);
        $params['user_id'] = $this->user['id'];
        Validation::validate($params, GoodsSizeService::$validate_list_search);
        $lock_key = md5(RedisKey::SIZE_EXPORT . $this->user['id']);
        $data     = $this->atomicLock(function () use ($params) {
            return DownloadCenterService::getInstance()->addDownloadCenter($this->user['id'],
                DownloadCenterEnum::INTERIOR_STAFF_ENTRY_SIZE, $params);
        }, $lock_key, 15);

        return $this->returnJson($data['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR,
            $data['message'] ?? $this->t->_('sys_processing'), $data['data'] ?? []);
    }

    /**
     * 员工商城-员工工服尺码-删除
     * @Permission(action='shop.goods_size.del')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/75882
     **/
    public function delAction()
    {
        $params = $this->request->get();
        Validation::validate($params, GoodsSizeService::$validate_share);
        $data = GoodsSizeService::getInstance()->del($params['id'], $this->user);
        return $this->returnJson($data['code'], $data['message'], $data['data']);
    }

}
