<?php
/**
 * Created by PhpStorm.
 * Date: 2021/10/21
 * Time: 16:22
 */

namespace App\Modules\Shop\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Shop\Services\OrderService;


class InteriorOrdersController extends BaseController
{

    /**
     * 员工商城列表
     * @Permission(action='shop.interior_orders.list')
     * @Date: 2022-02-17 15:06
     * @return:
     **@author: peak pan
     */
    public function listAction()
    {
        try {
            // 参数校验
            $params = $this->request->get();

            $validate_params = [
                'order_code'       => 'Required',
                'order_start_date' => 'Required',
                'order_end_date'   => 'Required',
                'staff_id'         => 'Required',
                'department_id'    => 'Required',
                'store_id'         => 'Required',
            ];

            Validation::validate($params, $validate_params);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $data = OrderService::getInstance()->getList($params, 1);

        return $this->returnJson($data['code'], $data['message'], $data['data']);
    }

    /**
     * 员工商城列表->查看
     * @Permission(action='shop.interior_orders.detail')
     * @Date: 2022-02-17 15:06
     * @return:
     **@author: peak pan
     */
    public function detailAction()
    {
        try {
            // 参数校验
            $params          = $this->request->get();
            $validate_params = [
                'id' => 'Required|Int|>>>:param error[id]',
            ];

            Validation::validate($params, $validate_params);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $data = OrderService::getInstance()->detailInfo($params);

        return $this->returnJson($data['code'], $data['message'], $data['data']);
    }


    /**
     * 员工商城列表->修改提交
     * @Permission(action='shop.interior_orders.save')
     * @Date: 2022-02-17 15:06
     * @return:
     **@author: peak pan
     */
    public function saveAction()
    {
        try {
            // 参数校验
            $params = $this->request->get();

            $validate_params = [
                'id'                    => 'Required|Int|>>>:param error[id]',
                'receive_province_name' => 'Str|>>>:param error[receive_province_name]',
                'receive_city_name'     => 'Str|>>>:param error[receive_city_name]',
                'receive_district_name' => 'Str|>>>:param error[receive_district_name]',
                'receive_postal_code'   => 'Str|>>>:param error[receive_postal_code]',
            ];
            Validation::validate($params, $validate_params);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $data = OrderService::getInstance()->saveInteriorOrders($params);

        return $this->returnJson($data['code'], $data['message'], $data['data']);
    }


    /**
     * 省市区邮编四级联动
     * @Token
     * @Date: 2022-02-17 15:06
     * @return:
     **@author: peak pan
     */
    public function areaverbAction()
    {
        try {
            // 参数校验
            $params = $this->request->get();


            $validate_params = [
                'level' => 'IntGeLe:1,3|>>>:param error[level]',
                'code'  => 'Str',//如果name为空 code就是上级code

            ];
            Validation::validate($params, $validate_params);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $data = OrderService::getInstance()::getAreaList($params);

        return $this->returnJson($data['code'], $data['message'], $data['data']);
    }


}
