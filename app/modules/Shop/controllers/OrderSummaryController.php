<?php
/**
 * Created by PhpStorm.
 * Date: 2021/10/21
 * Time: 16:22
 */

namespace App\Modules\Shop\Controllers;

use App\Library\Enums\ShopEnums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Shop\Services\BaseService;
use App\Modules\Shop\Services\OrderSummaryService;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

/**
 * 员工商城～订单汇总列表
 * Class OrderSummaryController
 * @package App\Modules\Shop\Controllers
 */
class OrderSummaryController extends BaseController
{
    /**
     * 订单汇总列表
     * @Permission(action='shop.order.summary.list')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/46039
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function listAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, OrderSummaryService::$not_must_params);
        Validation::validate($params, OrderSummaryService::$validate_list_search);
        $res = OrderSummaryService::getInstance()->getList($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 订单汇总列表～导出
     * @Permission(action='shop.order.summary.export')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/46045
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function exportAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, OrderSummaryService::$not_must_params);
        Validation::validate($params, OrderSummaryService::$validate_list_search);
        $lock_key = md5('shop_summary_order.all.export' . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            $params['is_export'] = 1;
            return OrderSummaryService::getInstance()->getList($params);
        }, $lock_key, 1);
        return $this->returnJson($res['code'] ?? ErrCode::$SYSTEM_ERROR, $res['message'] ?? $this->t->_('retry_later'),
            $res['data'] ?? []);
    }

    /**
     * 订单汇总列表～详情
     * @Permission(action='shop.order.summary.info')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/46075
     * @return Response|ResponseInterface
     */
    public function infoAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, OrderSummaryService::$validate_order_code);
            $res = OrderSummaryService::getInstance()->getInfo($params['order_code']);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 退款
     * @Permission(action='shop.order.summary.refund')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/46129
     * @return Response|ResponseInterface
     */
    public function refundAction()
    {
        $params = $this->request->get();
        Validation::validate($params,
            array_merge(OrderSummaryService::$validate_order_code, OrderSummaryService::$validate_refund_params));
        $params['pay_method'] = ShopEnums::PAY_METHOD_FLASH_PAY;
        $res                  = OrderSummaryService::getInstance()->refund($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }


    /**
     * 无头件 确认退款
     * (此次退款对无头件线下支付退款和flash pay 退款有所不同，和产品商量会再出一期整合退款)
     * @Permission(action='shop.order.summary.refund_submit')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/73407
     * @return Response|ResponseInterface
     */
    public function refundSubmitAction()
    {
        $params = $this->request->get();
        Validation::validate($params, OrderSummaryService::$validate_order_code);
        $params['pay_method'] = ShopEnums::PAY_METHOD_OFFLINE_PAY;
        $res                  = OrderSummaryService::getInstance()->refund($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }


    /**
     * 批量退款(已退款操作: 不区分商品类型 和 支付方式)
     * @Permission(action='shop.order.summary.batch_refund_submit')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/73412
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function batchRefundSubmitAction()
    {
        $params = $this->request->get();

        $validate = OrderSummaryService::$validate_batch_order_code;

        // 要退, 需校验退款日期
        if (empty($params['is_not_refund'])) {
            $validate['fund_at']   = 'Required|Date|>>>:fund_at error';
            $params['fund_status'] = ShopEnums::FUND_STATUS_DONE;
        } else {
            // 无需退
            $params['fund_status'] = ShopEnums::FUND_STATUS_INIT;
        }

        Validation::validate($params, $validate);

        $params['type'] = ShopEnums::FUND_TYPE_BATCH;
        $lock_key       = md5('shop_summary_order.batch_refund_submit_' . $this->user['id']);
        $res            = $this->atomicLock(function () use ($params) {
            return OrderSummaryService::getInstance()->batchRefundSubmit($params, $this->user);
        }, $lock_key, 20);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR,
            $res['message'] ?? $this->t['sys_processing']);
    }

}
