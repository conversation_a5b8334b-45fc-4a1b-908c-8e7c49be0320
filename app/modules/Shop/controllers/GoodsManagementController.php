<?php
/**
 * 商品管理
 */

namespace App\Modules\Shop\Controllers;

use App\Library\Enums\ImportCenterEnums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Modules\Common\Services\ImportCenterService;
use App\Modules\Shop\Services\GoodsManagementService;
use App\Modules\Shop\Services\BaseService;
use App\Library\Validation\ValidationException;
use Exception;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class GoodsManagementController extends BaseController
{

    /**
     * 商品相关枚举配置
     * @Permission(action='shop.goods_management.list')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/62543
     * @return Response|ResponseInterface
     */
    public function enumsAction()
    {
        $data = GoodsManagementService::getInstance()::getEnumsSetting();
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }


    /**
     * 商品列表
     * @Permission(action='shop.goods_management.list')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/62547
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function listAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, GoodsManagementService::$not_must_params);
        Validation::validate($params, GoodsManagementService::$validate_list_search);
        $data = GoodsManagementService::getInstance()->getList($params);
        return $this->returnJson($data['code'], $data['message'], $data['data']);
    }

    /**
     * 商品详情 - 查看
     * @Permission(action='shop.goods_management.view')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/62549
     * @return mixed
     * @throws ValidationException
     */
    public function viewAction()
    {
        $params = $this->request->get();
        Validation::validate($params, GoodsManagementService::$validate_detail);
        $data = GoodsManagementService::getInstance()->getDetail($params['id']);
        return $this->returnJson($data['code'], $data['message'], $data['data']);
    }


    /**
     * 商品添加
     * @Permission(action='shop.goods_management.add')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/62570
     * @return mixed
     * @throws ValidationException
     */
    public function addAction()
    {
        $params = trim_array($this->request->get());
        GoodsManagementService::checkSubmitValidation($params);
        $data = GoodsManagementService::getInstance()->addOne($params, $this->user);
        return $this->returnJson($data['code'], $data['message'], $data['data']);
    }

    /**
     * 商品编辑提交 - 更新
     * @Permission(action='shop.goods_management.edit')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/62584
     * @return mixed
     * @throws ValidationException
     */
    public function updateAction()
    {
        $params = trim_array($this->request->get());
        GoodsManagementService::checkSubmitValidation($params, true);
        $data = GoodsManagementService::getInstance()->updateOne($params, $this->user);
        return $this->returnJson($data['code'], $data['message'], $data['data']);
    }


    /**
     * 商品导出
     * @Permission(action='shop.goods_management.export')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/62548
     * @return mixed
     * @throws ValidationException
     */
    public function exportAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, GoodsManagementService::$not_must_params);
        Validation::validate($params, GoodsManagementService::$validate_list_search);
        $lock_key = md5('goods_management_sku_export_' . $this->user['id']);
        $data     = $this->atomicLock(function () use ($params) {
            return GoodsManagementService::getInstance()->getExportList($params);
        }, $lock_key, 30);
        return $this->returnJson($data['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR,
            $data['message'] ?? $this->t['sys_processing'], $data['data'] ?? []);
    }


    /**
     * 查看商品更多部门
     * @Permission(action='shop.goods_management.view')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/74752
     * @return mixed
     * @throws ValidationException
     */
    public function manageDepartmentListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, GoodsManagementService::$not_must_params);
        Validation::validate($params, GoodsManagementService::$validate_manage_department_list);
        $data = GoodsManagementService::getInstance()->getGoodsManageDepartmentList($params);
        return $this->returnJson($data['code'], $data['message'], $data['data']);
    }

    /**
     * 计算商品价值
     * @Permission(action='shop.goods_management.add')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/85082
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function calculateCommodityValueAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params,
            ['price' => 'Required|FloatGeLe:0,999999999999.99|>>>:' . $this->t->_('mall_goods_submit_check_8')]);
        $data = GoodsManagementService::getInstance()->calculateCommodityValue($params['price']);
        return $this->returnJson($data['code'], $data['message'], $data['data']);
    }

    /**
     * 生成链接
     * @Permission(action='shop.goods_management.generate_link')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/87014
     * @return mixed
     * @throws ValidationException
     */
    public function generateLinkAction()
    {
        $params = $this->request->get();

        $validate = [
            'files'                => 'Required|ArrLenGe:1',
            'files[*].object_name' => 'Required|StrLenGe:4',
            'files[*].object_url'  => 'Required|Url',
        ];
        Validation::validate($params, $validate);

        $lock_key = md5('goods_management_generate_link_' . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return GoodsManagementService::getInstance()->generateBatchLink($params['files'], $this->user);
        }, $lock_key, 20);

        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR,
            $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? '');
    }

    /**
     * 批量导入(无头件)结果查询(最近一次)
     * @Permission(action='shop.goods_management.batch_import')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/87020
     * @return mixed
     */
    public function batchImportResultAction()
    {
        $res = ImportCenterService::getInstance()->getBarcodeImportResult(ImportCenterEnums::TYPE_SHOP_GOODS_HEADLESS);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 批量导入(无头件)
     * @Permission(action='shop.goods_management.batch_import')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/87017
     * @return mixed
     * @throws Exception
     */
    public function batchImportAction()
    {
        $lock_key = md5('goods_management_batch_import_' . $this->user['id']);
        $res      = $this->atomicLock(function () {
            return GoodsManagementService::getInstance()->addBatchImportToImportCenter($this->user);
        }, $lock_key, 20);

        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR,
            $res['message'] ?? $this->t['sys_processing']);
    }

}
