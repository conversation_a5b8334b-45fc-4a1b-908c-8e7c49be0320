<?php
/**
 * 商品管理
 */

namespace App\Modules\Shop\Controllers;

use App\Library\Enums\ShopEnums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Modules\Shop\Services\BaseService;
use App\Modules\Shop\Services\OrderService;
use App\Modules\Shop\Services\OrderSummaryService;
use App\Util\RedisKey;


class GoodsCollectionCheckController extends BaseController
{

    /**
     * 员工商城-收款核对-待核对
     * @Permission(action='shop.collection_check.audit_wait')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/79327
     **/
    public function waitListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, OrderService::$not_must_collection_check_params);
        Validation::validate($params, OrderService::$validate_check_search);
        $params['pay_method']                   = ShopEnums::PAY_METHOD_OFFLINE_PAY;
        $params['order_status']                 = ShopEnums::ORDER_STATUS_WARTING_SEND_CODE;
        $params['payment_voucher_audit_status'] = [ShopEnums::PAYMENT_VOUCHER_AUDIT_STATUS_WAIT];
        $data                                   = OrderSummaryService::getInstance()->getList($params, $this->user);
        return $this->returnJson($data['code'], $data['message'], $data['data']);
    }

    /**
     * 员工商城-收款核对-待核对-导出
     * @Permission(action='shop.audit_wait.export')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/79332
     **/
    public function exportWaitAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, OrderService::$not_must_collection_check_params);
        Validation::validate($params, OrderService::$validate_list_search);
        $params['is_export']                    = 1;
        $params['pay_method']                   = ShopEnums::PAY_METHOD_OFFLINE_PAY;
        $params['order_status']                 = ShopEnums::ORDER_STATUS_WARTING_SEND_CODE;
        $params['payment_voucher_audit_status'] = [ShopEnums::PAYMENT_VOUCHER_AUDIT_STATUS_WAIT];
        $lock_key                               = md5(RedisKey::EXPORT_WAIT_COLLECTION . $this->user['id']);
        $data                                   = $this->atomicLock(function () use ($params) {
            return OrderSummaryService::getInstance()->getList($params);
        }, $lock_key, 15);
        return $this->returnJson($data['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR,
            $data['message'] ?? $this->t->_('sys_processing'), $data['data'] ?? []);
    }


    /**
     * 员工商城-待核对-查看
     * @Permission(action='shop.audit_wait.view')
     * @api  https://yapi.flashexpress.pub/project/133/interface/api/79347
     */
    public function waitViewAction()
    {
        $params = $this->request->get();
        Validation::validate($params, OrderService::$validate_view);
        $data = OrderSummaryService::getInstance()->getInfo($params['order_code']);
        return $this->returnJson($data['code'], $data['message'], $data['data']);
    }


    /**
     * 员工商城-待核对-通过
     * @Permission(action='shop.audit_wait.approval')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/79352
     */
    public function approvalAction()
    {
        $params = $this->request->get();
        Validation::validate($params, OrderService::$validate_share);
        $data = OrderService::getInstance()->approval($params['id'], $this->user);
        return $this->returnJson($data['code'], $data['message']);
    }


    /**
     * 员工商城-待核对-批量通过
     * @Permission(action='shop.audit_wait.batch_approval')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/79357
     */
    public function batchApprovalAction()
    {
        $params = $this->request->get();
        Validation::validate($params, OrderService::$validate_batch_approval);
        $data = OrderService::getInstance()->batchApproval($params, $this->user);
        return $this->returnJson($data['code'], $data['message']);
    }


    /**
     * 员工商城-待核对-不通过
     * @Permission(action='shop.audit_wait.no_approval')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/79367
     */
    public function noApprovalAction()
    {
        $params = $this->request->get();
        Validation::validate($params,
            array_merge(OrderService::$validate_share, OrderService::$validate_payment_voucher_reason));
        $params['is_approval'] = false;
        $data                  = OrderService::getInstance()->noApproval($params, $this->user);
        return $this->returnJson($data['code'], $data['message']);
    }


    /**
     * 员工商城-收款核对-已核对
     * @Permission(action='shop.collection_check.audit_processed')
     * @api  https://yapi.flashexpress.pub/project/133/interface/api/79342
     **/
    public function processedListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, OrderService::$not_must_collection_check_params);
        Validation::validate($params, OrderService::$validate_list_search);
        $params['pay_method']                   = ShopEnums::PAY_METHOD_OFFLINE_PAY;
        $params['payment_voucher_audit_status'] = $params['payment_voucher_audit_status'] ? [$params['payment_voucher_audit_status']] : [
            ShopEnums::PAYMENT_VOUCHER_AUDIT_STATUS_FINISH,
            ShopEnums::PAYMENT_VOUCHER_AUDIT_STATUS_FAIL,
        ];
        $data                                   = OrderSummaryService::getInstance()->getList($params, $this->user);
        return $this->returnJson($data['code'], $data['message'], $data['data']);
    }


    /**
     * 员工商城-收款核对-已核对-导出
     * @Permission(action='shop.audit_processed.export')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/79337
     **/
    public function exportProcessedAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, OrderService::$not_must_collection_check_params);
        Validation::validate($params, OrderService::$validate_list_search);
        $params['is_export']                    = 1;
        $params['pay_method']                   = ShopEnums::PAY_METHOD_OFFLINE_PAY;
        $params['payment_voucher_audit_status'] = $params['payment_voucher_audit_status'] ? [$params['payment_voucher_audit_status']] : [
            ShopEnums::PAYMENT_VOUCHER_AUDIT_STATUS_FINISH,
            ShopEnums::PAYMENT_VOUCHER_AUDIT_STATUS_FAIL,
        ];
        $lock_key                               = md5(RedisKey::EXPORT_PROCESSED_COLLECTION . $this->user['id']);
        $data                                   = $this->atomicLock(function () use ($params) {
            return OrderSummaryService::getInstance()->getList($params);
        }, $lock_key, 15);
        return $this->returnJson($data['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR,
            $data['message'] ?? $this->t->_('sys_processing'), $data['data'] ?? []);
    }


    /**
     * 员工商城-收款核对-已核对-查看
     * @Permission(action='shop.audit_processed.view')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/79452
     */
    public function processedViewAction()
    {
        $params = $this->request->get();
        Validation::validate($params, OrderService::$validate_view);
        $data = OrderSummaryService::getInstance()->getInfo($params['order_code']);
        return $this->returnJson($data['code'], $data['message'], $data['data']);
    }


    /**
     * 员工商城-收款核对-待核对-统计金额
     * @Permission(action='shop.collection_check.audit_wait')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/79382
     */
    public function amountSumAction()
    {
        $params = $this->request->get();
        Validation::validate($params, OrderService::$validate_batch_approval);
        $data = OrderService::getInstance()->amountSum($params);
        return $this->returnJson($data['code'], $data['message'], $data['data']);
    }


}
