<?php
/**
 * Created by PhpStorm.
 * Date: 2021/10/21
 * Time: 16:22
 */

namespace App\Modules\Shop\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Services\StoreService;
use App\Modules\Shop\Services\BaseService;
use App\Modules\Shop\Services\OrderService;


class OrderController extends BaseController
{

    /**
     * 获取订单状态、支付方式、订单类型、款项状态等枚举列表
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/46009
     */
    public function orderStatusEnumsAction()
    {
        $data = OrderService::getInstance()->orderStatusEnums();
        return $this->returnJson(ErrCode::$SUCCESS, '', $data);
    }


    /**
     * 订单查询
     * @Permission(action='shop.order.list')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/51133
     * @return mixed
     */
    public function listAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, OrderService::$not_must_params);
        Validation::validate($params, OrderService::$validate_list_search);
        $data = OrderService::getInstance()->getList($params);
        return $this->returnJson($data['code'], $data['message'], $data['data']);
    }


    /**
     * 订单导出
     * @Permission(action='shop.order.export')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/51163
     * @return mixed
     */
    public function exportAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, OrderService::$not_must_params);
        Validation::validate($params, array_merge(OrderService::$validate_list_search, OrderService::$validate_export));
        $lock_key = md5('shop_order.all.export' . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return OrderService::getInstance()->getList($params);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR,
            $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }


    /**
     * 网点名称搜索
     * @Permission(action='shop.order.list')
     */
    public function storeSearchAction()
    {
        try {
            // 参数校验
            $store_name = $this->request->get('store_name');
            $params     = ['store_name' => $store_name];

            $validate_params = [
                'store_name' => 'Required|StrLenGe:1|>>>:param error[store_name]',
            ];

            Validation::validate($params, $validate_params);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $data = (new StoreService())->getSysStoreListByCondition($params);
        // 模糊搜索总部
        if (false !== strpos('header office', strtolower($store_name))) {
            $data = array_merge($data, [["id" => -1, "name" => "Header Office", "sap_pc_code" => ""]]);
        }

        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }

}
