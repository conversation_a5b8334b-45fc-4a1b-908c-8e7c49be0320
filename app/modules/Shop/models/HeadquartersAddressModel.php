<?php
/**
 * Created by PhpStorm.
 * Date: 2021/10/21
 * Time: 16:10
 */

namespace App\Modules\Shop\Models;

use App\Models\Base;

class HeadquartersAddressModel extends Base
{
    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_backyard');
        // setSource()方法指定数据库表
        $this->setSource('headquarters_address');
    }

}
