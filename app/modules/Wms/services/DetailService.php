<?php

namespace App\Modules\Wms\Services;

use App\Library\ApiClient;
use App\Library\Enums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Modules\Wms\Models\WmsOrderModel;


class DetailService extends BaseService
{
    public static $validate_detail = [
        'id' => 'Required|IntGe:1',                                  //ID
    ];

    private static $detail_repeat_keys = [
        '物品名称','อุปกรณ์', 'name of material',
        '数量', 'จำนวน', 'number',
        '使用者', 'คนใช้', 'user',
        '网点当前库存', 'สต็อคคงเหลือในสาขา', 'remaining stock',
        '建议申请数量', 'จำนวนการเบิกที่แนะนำ', 'recommanded number',
        'SCM当前库存', 'สต็อคสินค้าในระบบSCM', 'SCM current inventory'
    ];

    private static $detail_no_repeat_keys = [
        '申请理由', 'สาเหตุยื่นขอ', 'reason for application'
//        '网点人数',
//        '派件数量',
//        '揽件数量',
    ];

    private static $detail_repeat_map = [
        '物品名称' => 'goods_name',
        'อุปกรณ์' => 'goods_name',
        'name of material' => 'goods_name',
        '数量' => 'num',
        'จำนวน' => 'num',
        'number' => 'num',
        '使用者' => 'staff_id',
        'คนใช้' => 'staff_id',
        'user' => 'staff_id',
        '网点当前库存' => 'stock',
        'สต็อคคงเหลือในสาขา' => 'stock',
        'remaining stock' => 'stock',
        '建议申请数量' => 'recommend_num',
        'จำนวนการเบิกที่แนะนำ' => 'recommend_num',
        'recommanded number' => 'recommend_num',
        '申请理由' => 'apply_reason',
        'สาเหตุยื่นขอ' => 'apply_reason',
        'reason for application' => 'apply_reason',
        'SCM当前库存' => 'scm_current_inventory',
        'สต็อคสินค้าในระบบSCM' => 'scm_current_inventory',
        'SCM current inventory' => 'scm_current_inventory',
    ];

    private static $detail_no_repeat_map = [
        '申请理由' => 'apply_reason',
        'สาเหตุยื่นขอ' => 'apply_reason',
        'reason for application' => 'apply_reason',
//        '网点人数' => 'outlets_num',
//        '派件数量' => 'delivery_num',
//        '揽件数量' => 'collection_num',
    ];

    private static $collar_key = [
        0 => 'goods_name',
        1 => 'nums',
        2 => 'date',
    ];

    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return DetailService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * @param $params
     * @param $uid
     * @return array
     */
    public function getAuditDetail($params, $uid)
    {
        $id = $params['id'] ?? 0;
        //首先查询一下数据,获取一下相关信息
        $order_info  = WmsOrderModel::findFirst([
                                                         'id = :id:',
                                                         'bind' => ['id' =>$id],
                                                         "columns" => "id,created_at",
                                                     ]);

        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $ac = new ApiClient('by', '', 'get_audit_detail', static::$language);
        $ac->setParams(
            [
                [
                    "type" => Enums::WF_WMS_TYPE,
                    "id" => "id_" . $id,
                    "staff_id" => $uid,
                    "date_created"=>$order_info->created_at ?? "",//申请时间
                ]
            ]
        );
        $res = $ac->execute();
        $data = $res['result']['data'] ?? [];
        $data = $this->handleData($data);

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * @param $data
     * @return array
     */
    private function handleData($data)
    {
        if (empty($data) || !is_array($data)) {
            return [];
        }

        $new_detail = $collar_record = [];
        $detail = empty($data['detail']) ? [] : $data['detail'];
        $stream = empty($data['stream']) ?[] : $data['stream'];
        $edit = empty($data['edit']) ?[] : $data['edit'];
        $td = empty($data['extend'][0]['td']) ? [] : $data['extend'][0]['td'];
        $i = 0;
        $count = count(self::$detail_repeat_keys) / 3;
        foreach ($detail as $key => $value) {
            if (in_array($value['key'], self::$detail_repeat_keys)) {
                $new_detail[$i][self::$detail_repeat_map[$value['key']]] = $value['value'];
                unset($detail[$key]);
                if (count($new_detail[$i]) >= $count) $i++;
            }
            if (in_array($value['key'], self::$detail_no_repeat_keys)) {
                foreach ($new_detail as $kk => &$record) {
                    $record[self::$detail_no_repeat_map[$value['key']]] = $value['value'];
                    $record['approval_num'] = $edit[$kk]['approval_num'];
                }
                unset($detail[$key]);
            }
        }
        $stream = array_reverse($stream);
        $data['detail'] = $detail;
        $data['stream'] = $stream;
        $data['comment'] = $new_detail;
        foreach ($td as $key => $value) {
            $value = empty($value) ? [] : $value;
            foreach ($value as $k => $v) {
                $collar_record[$key][self::$collar_key[$k]] = $v;
            }
        }
        $data['collar_record'] = $collar_record;
        return $data;
    }
}
