<?php

namespace App\Modules\Wms\Services;

use App\Library\ApiClient;
use App\Library\Enums;
use App\Modules\Wms\Models\WmsGoods;

class ListService extends BaseService
{
    private static $language_fields = [
        'zh-CN' => 'goods_name_zh',
        'en' => 'goods_name_en',
        'th' => 'goods_name_th'
    ];

    public static $not_must_params = [
        'serial_no',
        'goods_id',
        'store_id',
        'created_at_start',
        'created_at_end',
    ];

    public static $validate_list_search = [
        'pageSize' => 'Required|IntGt:0',                  //每页条数
        'pageNum' => 'Required|IntGt:0',                   //页码
        'status' => 'Required|IntIn:1,2,3',                //物料申请状态
        'serial_no' => 'StrLenGeLe:1,50',                  //审批编号
        'goods_id' => 'StrLenGeLe:1,50',                   //物料名称
        'store_id' => 'StrLenGeLe:1,50',                   //网点名称
        'created_at_start' => 'DateTime',                  //物料申请开始时间
        'created_at_end' => 'DateTime',                    //物料申请结束时间
    ];

    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return ListService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 获取物品列表
     * @param $locale
     * @return array
     */
    public function getWmsGoodsList($locale)
    {
        $columns = 'bar_code as id,' . (self::$language_fields[$locale] ?? 'goods_name_zh') . ' as goods_name';
        $builder = $this->modelsManager->createBuilder();
        $builder->columns($columns);
        $builder->from(WmsGoods::class);
        $builder->where('deleted = 0');
        $builder->limit(0, 10000);
        $items = $builder->getQuery()->execute()->toArray();
        return [
            'items' => $items,
        ];
    }

    /**
     * 获取物料审核列表
     * @param $condition
     * @param $uid
     * @return array
     */
    public function getAuditList($condition, $uid)
    {
        $page_num = empty($condition['pageNum']) ? 1 : $condition['pageNum'];
        $page_size = empty($condition['pageSize']) ? 20 : $condition['pageSize'];
        $page_size = ($page_size > 1000) ? 1000 : $page_size;
        $ac = new ApiClient('by', '', 'get_audit_list', static::$language);
        $ac->setParams(
            [
                [
                    'type' => Enums::WF_WMS_TYPE,
                    'page_num' => $page_num,
                    'page_size' => $page_size,
                    'start_date' => $condition['created_at_start'] ?? '',
                    'end_date' => $condition['created_at_end'] ?? '',
                    'status' => $condition['status'] ?? 1,
                    'serial_no' => $condition['serial_no'] ?? '',
                    'wms_id' => $condition['goods_id'] ?? '',
                    'store_id' => $condition['store_id'] ?? '',
                    'staff_approval_id' => $uid,
                ]
            ]
        );
        $res = $ac->execute();
        $items = empty($res['result']['dataList']) ? [] : $res['result']['dataList'];
        $items = $this->handleItems($items);

        $this->delUnReadNumsKeyByStaffIds([$uid]);

        return [
            'items' => $items,
            'pagination' => [
                'current_page' => $page_num,
                'per_page' => $page_size,
                'total_count' => intval($res['result']['pagination']['count'] ?? 0),
            ]
        ];
    }

    /**
     * 物料审核列表下载
     * @param $condition
     * @param $uid
     * @return array
     */
    public function download($condition, $uid)
    {
        $params['status'] = $condition['status'];
        $params['pageNum'] = 1;
        $params['pageSize'] = 10000;
        $res = $this->getAuditList($params, $uid);
        $data = $res['items'] ?? [];
        $new_data = [];
        $i = 0;
        foreach ($data as $key => $val) {
            ++$i;
            $new_data[$key][0] = $i;
            $new_data[$key][1] = $val['serial_no'];
            $new_data[$key][2] = $val['created_date'];
            $new_data[$key][3] = $val['status_title'];
            $new_data[$key][4] = $val['reject_reason'];
            $new_data[$key][5] = $val['staff_name'];
            $new_data[$key][6] = $val['store_name'];
        }
        $file_name = static::$t->_('wms_collection_approval_form') . date('YmdHis');
        $header = [
            static::$t->_('global.no'),
            static::$t->_('global.approval_no'),
            static::$t->_('global.approval_update'),
            static::$t->_('global.approval_status'),
            static::$t->_('global.reject_reason'),
            static::$t->_('global.applicant'),
            static::$t->_('global.store_name')
        ];
        return $this->exportExcel($header, $new_data, $file_name);
    }

    /**
     * @param $items
     * @return array
     */
    private function handleItems($items)
    {
        if (empty($items) || !is_array($items)) {
            return [];
        }
        foreach ($items as &$item) {
            $status = Enums::$wms_audit_status[$item['status']] ?? '';
            $item['status_title'] = static::$t->_($status);
//            $item['contract_file'] = $this->getRemotePath($item['contract_file']);
        }
        return $items;
    }
}
