<?php

namespace App\Modules\Wms\Services;

use App\Library\ApiClient;
use App\Library\Enums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;

class AuditService extends BaseService
{
    public static $not_must_params = [
    ];

    public static $validate_currency = [
        'id' => 'Required|IntGe:1',                                             //审核ID
//        'organization_name' => 'Required|StrLenGeLe:2,300',                     //网点名称
        'status' => 'Required|IntIn:2,3',                                       //审批状态
        'approval_arr' => 'IfIntEq:status,2|Required|ArrLenGe:1',           //审批同意修改资产数量
        'approval_arr[*].bar_code' => 'Required|StrLenGeLe:1,300',              //资产ID
        'approval_arr[*].approval_num' => 'Required|IntGe:0',                   //数量
        'reject_reason' => 'IfIntEq:status,3|Required|StrLenGeLe:1,300',        //拒绝原因
    ];

    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return AuditService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    public function audit($params, $user)
    {
        $ac = new ApiClient('by', '', 'edit_wms', static::$language);
        $ac->setParams(
            [
                [
                    'id' => $user['id'],
                    'name' => $user['name'],
                    'organization_name' => $params['organization_name'] ?? '',
                    'audit_id' => $params['id'],
                    'order_id' => $params['id'],
                    'status' => $params['status'] ?? Enums::WMS_AUDIT_STATUS_APPROVAL,
                    'order_status' => $params['status'] ?? Enums::WMS_AUDIT_STATUS_APPROVAL,
                    'approval_arr' => $params['approval_arr'] ?? [],
                    'reject_reason' => $params['reject_reason'] ?? '',
                ]
            ]
        );

        $this->delUnReadNumsKeyByStaffIds([$user['id']]);

        $res = $ac->execute();
        $code = $res['result']['code'] ?? $res['code'];
        $message = $res['result']['msg'] ?? $res['msg'];
        $data = $res['result']['data'] ?? [];
        return [
            'code' => $code,
            'message' => $message,
            'data' => $data,
        ];
    }

    /**
     * @param $data
     * @param $user
     * @return array
     */
    private function handleData($data, $user)
    {
        if (empty($data) || !is_array($data)) {
            return [];
        }

        $data['asset_no'] = uniqid('AS');
        $data['status'] = Enums::ASSET_AUDIT_STATUS_PENDING;
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['organization_id'] = $user['organization_id'] ?? '';
        $data['organization_name'] = $user['organization_name'] ?? '';
        $data['create_id'] = $user['id'] ?? 0;
        $data['create_name'] = $user['name'] ?? '';
//        $data['attachment'] = $this->handle_oss_file($data['attachment_arr'] ?? '');

        return $data;
    }
}