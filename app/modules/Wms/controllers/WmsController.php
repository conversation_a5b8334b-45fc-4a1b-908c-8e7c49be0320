<?php

namespace App\Modules\Wms\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Wms\Services\AuditService;
use App\Modules\Wms\Services\BaseService;
use App\Modules\Wms\Services\DetailService;
use App\Modules\Wms\Services\ListService;

class WmsController extends BaseController
{
    /**
     * 物料列表
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function getGoodsListAction()
    {
        $list = ListService::getInstance()->getWmsGoodsList($this->locale);
        return $this->returnJson(ErrCode::$SUCCESS, '', $list);
    }

    /**
     * 审核列表
     * @Permission(action='wms.audit.search')
     * @return mixed
     */
    public function auditListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, ListService::$not_must_params);
        try {
            Validation::validate($params, ListService::$validate_list_search);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $lock_key = md5('wms_by_audit_list_' . $this->user['id']);
        $list = $this->atomicLock(function() use ($params){
            return ListService::getInstance()->getAuditList($params, $this->user['id']);
        }, $lock_key, 30);

        return $this->returnJson(ErrCode::$SUCCESS, '', $list);
    }

    /**
     * 审核详情
     * @Permission(action='wms.audit.view')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function auditDetailAction()
    {
        $data = $this->request->get();

        try {
            Validation::validate($data, DetailService::$validate_detail);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }
        $res = DetailService::getInstance()->getAuditDetail($data, $this->user['id']);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 审核
     * @Permission(action='wms.audit.audit')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function auditAction()
    {
        $data = $this->request->get();
        try {
            Validation::validate($data, AuditService::$validate_currency);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = AuditService::getInstance()->audit($data, $this->user);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 下载
     * @Permission(action='wms.audit.download')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function downloadAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, ListService::$not_must_params);
        try {
            Validation::validate($params, ListService::$validate_list_search);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $lock_key = md5('wms_by_download_' . $this->user['id']);
        $res = $this->atomicLock(function() use ($params){
            return ListService::getInstance()->download($params, $this->user['id']);
        }, $lock_key, 30);

        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }
}
