<?php

namespace App\Modules\Wms\Models;

use App\Models\Base;

class WmsGoods extends Base
{
    public $id;

    public $bar_code;

    public $goods_name_en;

    public $goods_name_th;

    public $goods_name_zh;

    public $image_path;

    public $nuit_en;

    public $nuit_th;

    public $nuit_zh;

    public $nuit_detail;

    public $price;

    public $specification;

    public $deleted;

    public $created_at;

    public $udpated_at;


    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_backyard');
        // setSource()方法指定数据库表
        $this->setSource('wms_goods');
    }
}
