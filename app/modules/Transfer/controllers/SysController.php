<?php


namespace App\Modules\Transfer\Controllers;


use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Transfer\Services\DetailService;
use App\Modules\Transfer\Services\SysService;
use App\Traits\TokenTrait;

class SysController extends BaseController
{
    /**
     * 获取转岗类型下拉
     * @Token
     */
    public function getJobTransferTypesAction()
    {
        //[1]业务处理
        $data = SysService::getInstance()->getJobTransferTypes();

        //[2]数据返回
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }

    /**
     * 获取转岗状态下拉
     * @Token
     */
    public function getJobTransferStatusAction()
    {
        //[1]业务处理
        $data = SysService::getInstance()->getJobTransferState();

        //[2]数据返回
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }

    /**
     * 获取转岗审核状态下拉
     * @Token
     */
    public function getApprovalStatusAction()
    {
        //[1]业务处理
        $data = SysService::getInstance()->getJobTransferAuditState();

        //[2]数据返回
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }

    /**
     * 获取转岗部门下拉
     * @Token
     */
    public function getJobTransferDepartmentAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, DetailService::$validate_department);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        //[1]业务处理
        $data = SysService::getInstance()->getDepartmentList($params);

        //[2]数据返回
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }

    /**
     * 获取转岗职位下拉
     * @Token
     */
    public function getJobTransferPositionAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, DetailService::$validate_position);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        //[1]业务处理
        $data = SysService::getInstance()->getPositionList($params);

        //[2]数据返回
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }

    /**
     *  获取转岗网点下拉
     * @Token
     */
    public function getJobTransferStoreAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, DetailService::$validate_position);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        //[1]业务处理
        $data = SysService::getInstance()->getStoreList($params);

        //[2]数据返回
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }

    /**
     * 获取转岗HCId下拉
     * @Token
     */
    public function getJobTransferHcIdAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, DetailService::$validate_hc_list);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        //[1]业务处理
        $data = SysService::getInstance()->getHcList($params);

        //[2]数据返回
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }

    /**
     * 获取车辆来源下拉
     * @Token
     */
    public function getCarOwnerAction()
    {
        //[1]业务处理
        $data = SysService::getInstance()->getJobTransferCarOwner();

        //[2]数据返回
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }

    /**
     * 转岗来源下拉
     * @Token
     */
    public function getDataSourceAction()
    {
        //[1]业务处理
        $data = SysService::getInstance()->getJobTransferDataSource();

        //[2]数据返回
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }

    /**
     * 获取转岗角色下拉
     * @Token
     */
    public function getRoleListAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, DetailService::$validate_role_list);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        //[1]业务处理
        $data = SysService::getInstance()->getRoleList($params);

        //[2]数据返回
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }

    /**
     * 获取批号
     * @Token
     * @throws \Exception
     */
    public function getBatchNumberAction()
    {
        //[1]业务处理
        $res = SysService::getInstance()->getBatchNumber();

        //[2]数据返回
        return $this->returnJson($res['code'], $res['message'] ?? 'success', $res['data'] ?? null);
    }
}