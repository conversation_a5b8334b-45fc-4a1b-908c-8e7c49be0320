<?php


namespace App\Modules\Transfer\Controllers;

use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Modules\Transfer\Services\AuditService;
use App\Modules\Transfer\Services\BaseService;
use App\Modules\Transfer\Services\DetailService;
use App\Modules\Transfer\Services\JobTransferService;
use App\Modules\Transfer\Services\ListService;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;

class JobTransferController extends BaseController
{
    /**
     * 批量转岗申请-查询列表
     * @Permission(action='transfer.apply.search')
     * @Token
     */
    public function searchListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, ListService::$not_must_params);
        try {
            Validation::validate($params, ListService::$validate_list_search);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $params['submitter_id'] = $this->user['id'];
        $params['data_source']  = Enums::DATA_FROM_OA;
        $returnArr = ListService::getInstance()->getJobTransferApplyList($params);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $returnArr);
    }

    /**
     * 查看转岗申请详情
     * @Permission(action='transfer.apply.view')
     * @Token
     * @throws \Exception
     */
    public function getDetailAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, DetailService::$validate_detail);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $returnArr = DetailService::getInstance()->getJobTransferApplyDetail($params, $this->user['id']);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $returnArr);
    }

    /**
     * 查看转岗流程
     * @Permission(action='transfer.apply.flow')
     * @throws \Exception
     * @Token
     */
    public function getProcessOperationAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, DetailService::$validate_detail);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $returnArr = DetailService::getInstance()->getAuditDetail($params, $this->user['id']);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $returnArr);
    }

    /**
     * 数据模块表格导出接口
     * @Permission(action='transfer.apply.export')
     */
    public function exportAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, ListService::$not_must_params);
        try {
            Validation::validate($params, ListService::$validate_list_search);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $params['submitter_id'] = $this->user['id'];
        $params['data_source']  = Enums::DATA_FROM_OA;
        if (get_country_code() == 'MY') {

            $res = ListService::getInstance()->exportMY($params, $this->user['id']);
        } else {

            $res = ListService::getInstance()->export($params, $this->user['id']);
        }
        return $this->returnJson($res['code'], $res['message'] ?? 'success', $res['data']);
    }

    /**
     * 批量添加转岗-查看转岗人详情
     * @Token
     */
    public function searchStaffDetailAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, DetailService::$validate_staff_detail);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        try {
            $returnArr = DetailService::getInstance()->getStaffDetailInfo($params, $this->user['id']);
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $returnArr);
    }

    /**
     * 批量转岗审核-查询列表
     * @Permission(action='transfer.audit.search')
     */
    public function searchAuditListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, ListService::$not_must_params);
        try {
            Validation::validate($params, ListService::$validate_list_search);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $returnArr = ListService::getInstance()->getAuditList($params, $this->user['id']);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $returnArr);
    }

    /**
     * 查看转岗申请详情
     * @Permission(action='transfer.audit.view')
     * @throws \Exception
     */
    public function getAuditDetailAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, DetailService::$validate_detail);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $returnArr = DetailService::getInstance()->getJobTransferApplyDetail($params, $this->user['id']);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $returnArr);
    }

    /**
     * 查看转岗流程
     * @Permission(action='transfer.audit.flow')
     * @throws \Exception
     */
    public function getAuditProcessOperationAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, DetailService::$validate_detail);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $returnArr = DetailService::getInstance()->getAuditDetail($params, $this->user['id']);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $returnArr);
    }

    /**
     * 批量转岗审核-查询列表
     * @Permission(action='transfer.search.search')
     */
    public function searchJobTransferListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, ListService::$not_must_params);
        try {
            Validation::validate($params, ListService::$validate_list_search);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $returnArr = ListService::getInstance()->getJobTransferApplyList($params);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $returnArr);
    }

    /**
     * 数据模块表格导出接口
     * @Permission(action='transfer.audit.export')
     */
    public function exportAuditAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, ListService::$not_must_params);
        try {
            Validation::validate($params, ListService::$validate_list_search);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        if (get_country_code() == 'MY') {

            $res = ListService::getInstance()->exportAuditMY($params, $this->user['id']);
        } else {

            $res = ListService::getInstance()->exportAudit($params, $this->user['id']);
        }
        return $this->returnJson($res['code'], $res['message'] ?? 'success', $res['data']);
    }

    /**
     * 查看转岗申请详情
     * @Permission(action='transfer.search.view')
     * @throws \Exception
     */
    public function getJobTransferDetailAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, DetailService::$validate_detail);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $returnArr = DetailService::getInstance()->getJobTransferApplyDetail($params, $this->user['id']);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $returnArr);
    }

    /**
     * 查看转岗流程
     * @throws \Exception
     * @Permission(action='transfer.search.flow')
     */
    public function getJobTransferProcessOperationAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, DetailService::$validate_detail);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $returnArr = DetailService::getInstance()->getAuditDetail($params, $this->user['id']);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $returnArr);
    }

    /**
     * 数据模块表格导出接口
     * @Permission(action='transfer.search.export')
     */
    public function exportJobTransferAction()
    {
        ini_set('memory_limit', '1536M');
        
        $params = $this->request->get();
        $params = BaseService::handleParams($params, ListService::$not_must_params);
        try {
            Validation::validate($params, ListService::$validate_list_search);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        if (get_country_code() == 'MY') {

            $res = ListService::getInstance()->exportMY($params, $this->user['id']);
        } else {

            $res = ListService::getInstance()->export($params, $this->user['id']);
        }
        return $this->returnJson($res['code'], $res['message'] ?? 'success', $res['data']);
    }

    /**
     * 上传工号并返回结果集
     * @Token
     */
    public function uploadApplyAction()
    {
        try {
            $excel_file = $this->request->getUploadedFiles();
            if (empty($excel_file)) {
                return $this->returnJson(ErrCode::$VALIDATE_ERROR, 'Excel File Empty', []);
            }

            $config = ['path' => ''];
            $excel = new \Vtiful\Kernel\Excel($config);

            // 读取文件
            $excel_data = $excel->openFile($excel_file[0]->getTempName())
                ->openSheet()
                ->setSkipRows(0)
                ->getSheetData();

            $res = JobTransferService::getInstance()->uploadApplyStaffIds($excel_data, $this->user['id']);
            return $this->returnJson($res['code'], $res['msg'], $res['data']);
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->warning('uploadApplyAction----结果' . $e->getMessage().'---------'.$e->getLine());
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage(), []);
        }
    }

    /**
     * 批量申请提交
     * @Permission(action='transfer.apply.add')
     */
    public function batchApplyAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, ListService::$validate_batch_add);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        try {
            $res = AuditService::getInstance()->batchApplyJobTransfer($params, $this->user['id']);
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }

    /**
     * 批量申请提交
     * @Permission(action='transfer.apply.add')
     */
    public function checkBatchApplyAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, ListService::$validate_batch_add);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        try {
            $returnArr = AuditService::getInstance()->checkBatchApplyJobTransfer($params, $this->user['id']);
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $returnArr);
    }

    /**
     * 审核同意
     * @Permission(action='transfer.audit.audit')
     */
    public function approvalAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, AuditService::$not_must_params);
        try {
            Validation::validate($params, AuditService::$validate_approval);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $params['status'] = 2;

        $lock_key = md5('transfer_job_transfer_approval_' . $params['id']);
        if (get_country_code() == GlobalEnums::MY_COUNTRY_CODE) {

            $res = $this->atomicLock(function() use ($params){
                return AuditService::getInstance()->auditMY($params, $this->user['id']);
            }, $lock_key, 10, false);

        } else {

            $res = $this->atomicLock(function() use ($params){
                return AuditService::getInstance()->audit($params, $this->user['id']);
            }, $lock_key, 10, false);

        }

        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 审核驳回
     * @Permission(action='transfer.audit.audit')
     */
    public function rejectAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, AuditService::$not_must_params);
        try {
            Validation::validate($params, AuditService::$validate_reject);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $params['status'] = 3;
        $lock_key = md5('transfer_job_transfer_reject_' . $params['id']);
        if (get_country_code() == GlobalEnums::MY_COUNTRY_CODE) {

            $res = $this->atomicLock(function() use ($params){
                return AuditService::getInstance()->auditMY($params, $this->user['id']);
            }, $lock_key, 10, false);
        } else {

            $res = $this->atomicLock(function() use ($params){
                return AuditService::getInstance()->audit($params, $this->user['id']);
            }, $lock_key, 10, false);
        }

        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 审核撤销
     * @Permission(action='transfer.audit.audit')
     */
    public function cancelAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, AuditService::$not_must_params);
        try {
            Validation::validate($params, AuditService::$validate_approval);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $params['status'] = 4;

        $lock_key = md5('transfer_job_transfer_cancel_' . $params['id']);
        $res = $this->atomicLock(function() use ($params){
            return AuditService::getInstance()->audit($params, $this->user['id']);
        }, $lock_key, 10, false);

        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 更新审批数据(更新转岗日期、转岗HCId)
     * @Token
     */
    public function updateAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, ListService::$validate_update);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $lock_key = md5('transfer_job_transfer_update_' . $params['id']);
        $res = $this->atomicLock(function() use ($params){
            return ListService::getInstance()->update($params, $this->user['id']);
        }, $lock_key, 10, false);

        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 立即转岗-校验hc是否有效
     * @Token
     */
    public function checkHcValidateAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, ListService::$validate_update);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = ListService::getInstance()->checkHcValidate($params);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }

    /**
     * 立即转岗
     * @Token
     */
    public function doTransferAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, ListService::$validate_update);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = ListService::getInstance()->doTransfer($params, $this->user['id']);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }

    /**
     * 批量审批-获取审批列表
     * @Token
     * @throws \Exception
     */
    public function getApprovalListAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, ListService::$validate_update);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        if (get_country_code() == 'MY') {
            $res = ListService::getInstance()->getJobTransferApprovalListMY($params, $this->user['id']);
        } else {

            $res = ListService::getInstance()->getJobTransferApprovalList($params, $this->user['id']);
        }
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }

    /**
     * 批量审批-获取审批列表
     * @Token
     */
    public function getJobTransferDateAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, ListService::$validate_update);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = ListService::getInstance()->getJobTransferDate($params);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }

    /**
     * 获取批量添加进度
     */
    public function getBatchAddProgressAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, ListService::$validate_batch);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = ListService::getInstance()->getBatchAddProgress($params);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }

    /**
     * 获取立即转岗转岗进度
     * @Token
     */
    public function getDoTransferProgressAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, ListService::$validate_do_transfer);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = ListService::getInstance()->getDoTransferProgress($params, $this->user['id']);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }


    /**
     * @description:转岗后编辑提供过角色列表
     * <AUTHOR> L.J
     * @time       : 2021/7/7 16:02
     */
    public function getCharacterListAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, DetailService::$validate_detail);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $returnArr = DetailService::getInstance()->getCharacterList([],$params['id']);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $returnArr);
    }


    /**
     * @Permission(action='transfer.search.update_info')
     * @description: 修改转岗角色和上级
     * <AUTHOR> L.J
     * @time       : 2021/7/7 16:29
     */
    public function updateInfoAction(){
        $params = $this->request->get();
        try {
            Validation::validate($params, DetailService::$validate_update_info);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $returnArr = DetailService::getInstance()->updateInfo($params,$this->user['id']);
        return $this->returnJson($returnArr['code'] ?? ErrCode::$SUCCESS, $returnArr['message'] ?? 'success', $returnArr['data'] ?? null);
    }
}

