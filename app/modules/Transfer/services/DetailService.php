<?php

namespace App\Modules\Transfer\Services;

use App\Library\ApiClient;
use App\Library\Enums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Modules\Hc\Models\SysStoreModel;
use App\Modules\Transfer\Models\JobTransferModel;
use App\Modules\Transfer\Models\SysAttachmentModel;
use App\Modules\Transfer\Models\SysDepartmentModel;
use App\Modules\User\Models\HrJobTitleModel;
use App\Modules\User\Models\HrStaffInfoModel;
use Exception;

class DetailService extends BaseService
{
    public static $validate_detail = [
        'id' => 'Required|IntGe:1',
    ];

    public static $validate_staff_detail = [
        'staff_id' => 'Required|IntGe:1',
    ];

    public static $validate_department = [
        'staff_id' => 'Required|IntGe:1',
    ];

    public static $validate_position = [
        'department_id' => 'Required|IntGe:1',
    ];

    public static $validate_hc_list = [
        "department_id" => "Required|Int",
        "store_id" => "Required|Str",
        "job_title_id" => "Required|Int",
    ];

    public static $validate_role_list = [
        "department_id" => "Required|Int",
        "job_title_id" => "Required|Int",
    ];

    public static $validate_update_info = [
        'id' => 'Required|IntGe:1',
        "after_role_ids" => "Required|Str",
        "after_manager_id" =>  'Required|IntGe:1|>>>:after_manager_id error',
    ];

    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return DetailService
     */
    public static function getInstance(): DetailService
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 获取详情
     * @param $params
     * @param $uid
     * @return array
     * @throws Exception
     */
    public function getAuditDetail($params, $uid): array
    {
        $id = $params['id'] ?? 0;

        $transferInfo = JobTransferModel::findFirst($id);
        if (empty($transferInfo)) {
            throw new Exception("can not find job transfer id: $id");
        }

        //获取转岗人信息
        $staffInfo = HrStaffInfoModel::findFirst([
            "conditions" => "staff_info_id = :staff_info_id:",
            "columns" => "emp_id, staff_info_id, name, name_en, sex, personal_email",
            "bind" => [ "staff_info_id" => $transferInfo->getStaffId()]
        ])->toArray();

        $senior_auditor = explode(',',$transferInfo->senior_auditor);
        if (in_array($uid,$senior_auditor)){
            $reject_salary_detail = $this->decodeRejectSalaryDetail($transferInfo->reject_salary_detail);
        }else{
            $reject_salary_detail = '';
        }

        $requestData = [
            'staff_id'  => $transferInfo->getStaffId(),
            'hr_id'     => $staffInfo['emp_id'],
            'name'      => $staffInfo['name'],
            'name_en'   => $staffInfo['name_en'],
            'sex'       => self::$t->_('staff_sex.' . $staffInfo['sex']),
            'personal_email' => $staffInfo['personal_email'],
            'reject_salary_detail' => $reject_salary_detail,
            'detail'    => [],
        ];
        $this->logger->info('getDetail----参数：' . json_encode($requestData, JSON_UNESCAPED_UNICODE));

        //获取审批流
        $ac = new ApiClient('by', '', 'get_apply_detail', static::$language);
        $ac->setParams([[
            'type'      => $transferInfo->data_source == 1 ? 18 : 20, //如果是by渠道，类型是18 否则是20
            'user'      => $transferInfo->getStaffId(),
            'id'        => $id,
            "come_from" => 1,
            'date_created'=>$transferInfo->created_at ?? '', //申请时间
        ]]);
        $detail = $ac->execute();
        $this->logger->info('getDetail----结果' . json_encode($detail));

        if (isset($detail['result']['data']) && $detail['result']['data']) {
            $requestData['detail'] = $detail['result']['data']['stream'] ?? [];
        }

        if (isset($detail['result']['log']) && $detail['result']['log']) {
            $requestData['log'] = $detail['result']['log'] ? array_reverse($detail['result']['log']) : [];
            //时间处理
            $add_hour = $this->config->application->add_hour;
            if($requestData['log']){
                foreach ($requestData['log'] as $k => $v) {
                    $requestData['log'][$k]['created_at'] = date('Y-m-d H:i:s',strtotime($v['created_at']) + $add_hour  * 3600);
                    $requestData['log'][$k]['updated_at'] = date('Y-m-d H:i:s',strtotime($v['updated_at']) + $add_hour  * 3600);
                }
            }
        }

        return $requestData ?? [];
    }

    /**
     * 查看转岗详情
     * @param $params
     * @param $uid
     * @return array
     * @throws Exception
     */
    public function getJobTransferApplyDetail($params, $user): array
    {
        $id = intval($params['id']);
        $transferInfo = JobTransferModel::findFirst($id);
        if (empty($transferInfo)) {
            throw new \Exception("can not find job transfer id: $id");
        }
        $transferInfo = $transferInfo->toArray();

        //只有hrbp以及hrbp之后的审批人才能看到图片
        //获取图片
        if (isset($transferInfo['senior_auditor']) && $transferInfo['senior_auditor']) {
            $imageArr = $this->getJobTransferImages($id);
        }

        list($department_info, $job_info, $user_info, $store_info, $roles, $manager) = JobTransferService::getInstance()->
            genBaseData([$transferInfo],Enums::FETCH_DATA_DETAIL);

        $res = [];
        $res['staff_id'] = $transferInfo['staff_id'];
        $res['hrid'] = $user_info[$transferInfo['staff_id']]['emp_id'] ?? '';
        $res['name'] = $user_info[$transferInfo['staff_id']]['name'];
        $res['name_en'] = $user_info[$transferInfo['staff_id']]['name_en'];
        $res['identity'] = $user_info[$transferInfo['staff_id']]['identity'];
        $res['mobile'] = $user_info[$transferInfo['staff_id']]['mobile'];
        $res['sex'] = self::$t->_('staff_sex.' . $user_info[$transferInfo['staff_id']]['sex']);
        $res['personal_email'] = $user_info[$transferInfo['staff_id']]['personal_email'];

        //转岗类型
        $type = SysService::getInstance()->getTransferType();
        $res['transfer_type'] = $type[$transferInfo['type']] ?? '';
        $state = SysService::getInstance()->getTransferState();
        $res['transfer_state'] = $state[$transferInfo['state']] ?? '';

        $res['old_department'] = $department_info[$transferInfo['current_department_id']]['name'];
        $res['old_store_name'] = $store_info[$transferInfo['current_store_id']]['name'];
        $res['old_job_title'] = $job_info[$transferInfo['current_position_id']]['job_name'];
        $res['old_leader'] = $transferInfo['current_manager_id'] ?
            $transferInfo['current_manager_id'] . ' ' . $user_info[$transferInfo['current_manager_id']]['name']
            : '';
        $res['old_indirect_leader'] = $transferInfo['current_indirect_manger_id'] ?
            $transferInfo['current_indirect_manger_id'] . ' ' . $user_info[$transferInfo['current_indirect_manger_id']]['name']
            : '';
        $res['old_role'] = $this->formatRoles($transferInfo['current_role_id']);

        $res['new_department'] = $department_info[$transferInfo['after_department_id']]['name'];
        $res['new_store_name'] = $store_info[$transferInfo['after_store_id']]['name'];
        $res['new_job_title'] = $job_info[$transferInfo['after_position_id']]['job_name'];
        $res['new_leader'] = $transferInfo['after_manager_id'] . ' ' . $user_info[$transferInfo['after_manager_id']]['name'];
        $res['new_indirect_leader'] = isset($manager[$transferInfo['staff_id']])
            ? $manager[$transferInfo['staff_id']]['manager_staff_id'] . ' ' . $manager[$transferInfo['staff_id']]['name']
            : '';

        //角色
        $res['new_role'] = '';
        $rolesList = SysService::getInstance()->getAllRoleList();
        $roleName = [];
        if (isset($transferInfo['after_role_ids']) && !is_null($transferInfo['after_role_ids'])) {
            $roleIds = explode(',', $transferInfo['after_role_ids']);
            foreach ($roleIds as $role) {
                $roleName[] = in_array(self::$language, ['zh-CN', 'zh']) ? ($rolesList[$role]['role_name_zh'] ?? "")
                    : (self::$language == 'en' ? $rolesList[$role]['role_name_en'] ?? "" : $rolesList[$role]['role_name_th'] ?? '');
            }
        }
        $res['new_role'] = isset($roleName) && is_array($roleName) && $roleName ? implode(',', $roleName): "";
        $res['reason'] = $transferInfo['reason'];
        $res['job_transfer_img'] = $imageArr ?? [];
        $res['after_date'] = $transferInfo['after_date'];

        $res['update_type'] = 1;
        $res['new_role_ids'] = $transferInfo['after_role_ids'];
        $res['new_leader_id'] = $transferInfo['after_manager_id'];

        $res['old_working_day_rest_type'] = !empty($transferInfo['before_working_day_rest_type']) ? self::$t->_('working_day_rest_type_' . $transferInfo['before_working_day_rest_type']) : '';
        $res['new_working_day_rest_type'] = !empty($transferInfo['after_working_day_rest_type']) ? self::$t->_('working_day_rest_type_' . $transferInfo['after_working_day_rest_type']) : '';

        if(in_array($transferInfo['state'],[enums::JOBTRANSFER_STATE_TO_BE_TRANSFERED, enums::JOBTRANSFER_STATE_TRANSFERE_ERR]) && $transferInfo['approval_state'] == enums::CONTRACT_STATUS_REJECTED){
            $res['update_type'] = 2;
        }

        return $res;
    }

    /**
     * 获取转岗图片
     * @param $id
     * @return array
     */
    public function getJobTransferImages($id): array
    {
        $images = SysAttachmentModel::find([
            'conditions' => "oss_bucket_key = :oss_bucket_key: and oss_bucket_type = 'JOB_TRANSFER' and deleted = 0",
            'bind'       => [
                'oss_bucket_key' => $id
            ],
            'columns'    => 'bucket_name,object_key,original_name',
        ])->toArray();
        // 兼容马来本地节点服务
        $urlPrefix = 'MY' == strtoupper(env('country_code')) ? "https://%s.oss-ap-southeast-3.aliyuncs.com/%s" : "https://%s.oss-ap-southeast-1.aliyuncs.com/%s";
        if (!empty($images)) {
            $imageArr = [];
            foreach ($images as $image) {
                $imageArr[] = [
                    'url' => sprintf($urlPrefix, $image['bucket_name'], $image['object_key']),
                    'file_name' => $image['original_name'] ?? '',
                    'name' => $image['original_name'] ?? '',
                ];
            }
        }
        return $imageArr ?? [];
    }

    /**
     * 查询转岗人信息
     * @param $params
     * @param $submitter_id
     * @return array
     * @throws Exception
     */
    public function getStaffDetailInfo($params, $submitter_id): array
    {
        $staffId = $params['staff_id'] ?? 0;

        list($errCode, $errMsg) = JobTransferService::getInstance()->checkStaffInfo($staffId, $submitter_id);
        if ($errCode != ErrCode::$SUCCESS) {
            throw new Exception($errMsg);
        }

        $staffInfo = HrStaffInfoModel::findFirst([
            "conditions" => "staff_info_id = :staff_info_id:",
            "columns" => "staff_info_id as staff_id, name, node_department_id, sys_store_id, job_title",
            "bind" => [ "staff_info_id" => $staffId]
        ]);
        if (empty($staffInfo)) {
            throw new Exception("can not find staff id: $staffId");
        }
        $staffInfoArr = $staffInfo->toArray();

        //部门
        $departmentInfo = SysDepartmentModel::findFirst([
            "conditions" => "id = :department_id:",
            "columns" => "id,name",
            "bind" => [
                "department_id" => $staffInfo['node_department_id']
            ]
        ]);
        //职位
        $positionInfo = HrJobTitleModel::findFirst([
            "conditions" => "id = :job_title:",
            "columns" => "id,job_name",
            "bind" => [
                "job_title" => $staffInfo['job_title']
            ]
        ]);
        //网点
        $storeInfo = SysStoreModel::findFirst([
            "conditions" => "id = :store_id:",
            "columns" => "id,name",
            "bind" => [
                "store_id" => $staffInfo['sys_store_id']
            ]
        ]);

        $staffInfoArr['current_department_name'] = $departmentInfo->name ?? '';
        $staffInfoArr['current_position_name'] = $positionInfo->job_name ?? '';
        $staffInfoArr['current_store_name'] = $storeInfo->name ?? ($staffInfo['sys_store_id'] == '-1' ? 'Head Office' : '');

        return $staffInfoArr ?? [];
    }

    // 替换角色为文案
    private function formatRoles($str) {
        return preg_replace_callback('#\b(\d+)\b#', function($match) {
            return self::$t->_('role_'. $match[1]);
        }, $str);
    }

    //查询转岗的角色列表
    public function getCharacterList($info,$id=0){
        if(!empty($id)){
            $transferInfo = JobTransferModel::findFirst($id);
            if (empty($transferInfo)) {
                throw new Exception("can not find job transfer id: $id");
            }
            $info = $transferInfo->toArray();
        }

        //获取角色列表
        $roleList = SysService::getInstance()->getRoleList([
                                                               "department_id" => $info['after_department_id'],
                                                               "job_title_id"  => $info['after_position_id'],
                                                           ]);
        return $roleList ?? [];
    }


    //查询相关
    public function updateInfo($params,$user){
        try {
            $transferInfo = JobTransferModel::findFirst($params['id']);
            if (empty($transferInfo)) {
                throw new Exception("can not find job transfer id: {$params['id']}");
            }
            $info = $transferInfo->toArray();
            if ($info['after_role_ids'] == $params['after_role_ids'] && $info['after_manager_id'] == $params['after_manager_id']) {
                return [];
            }
            //查询上级是否存在
            $HrStaffInfo    = HrStaffInfoModel::findFirst(  [
                                                                 "conditions" => "staff_info_id = :staff_info_id:",
                                                                 "columns" => "staff_info_id as staff_id",
                                                                 "bind" => [ "staff_info_id" => $params['after_manager_id']]
                                                             ]);
            if(empty($HrStaffInfo)){
                throw new Exception(self::$t->_('transfer.1'));
            }

            if (is_string($params['after_role_ids'])) {
                $roles = explode(',', $params['after_role_ids']);
            } elseif (is_array($params['after_role_ids'])) {
                $roles = $params['after_role_ids'];
            } else {
                $roles = [];
            }


            $ac = new ApiClient('by', '', 'update_job_transfer', static::$language);

            $params = array_merge($params, [
                'staff_info_id' => $user,
                'type'          => 5,
                'role_ids'      => $roles
            ]);
            $ac->setParams([$params]);
            $res = $ac->execute();

            $code    = $res['result']['code'] ?? "";
            $message = $res['result']['msg'] ?? "";
            $data    = $res['data'] ?? [];
            return [
                'code'    => $code,
                'message' => $message,
                'data'    => $data,
            ];
        }catch (Exception $e) {
            $this->logger->warning('updateInfo 失败：' . $e->getMessage());
            return [
                'code'    => ErrCode::$VALIDATE_ERROR,
                'message' => $e->getMessage(),
                'data'    => [],
            ];
        }

    }

}
