<?php


namespace App\Modules\Transfer\Services;


use App\Library\ApiClient;
use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\JobTransferEnums;
use App\Library\ErrCode;
use app\modules\Hc\models\HrJobDepartmentRelationModel;
use App\Modules\Hc\Models\SysManageRegionModel;
use App\Modules\Hc\Models\SysStoreModel;
use App\Modules\Organization\Models\SysManagePieceModel;
use App\Modules\Transfer\Models\SettingEnvModel;
use App\Models\backyard\SysDepartmentModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\HrStaffManageDepartmentModel;
use App\Models\backyard\HrStaffManagePieceModel;
use App\Models\backyard\HrStaffManageRegionModel;
use App\Models\backyard\HrStaffManageStoreCategoryModel;
use App\Models\backyard\HrStaffManageStoreModel;
use App\Models\backyard\HrStaffInfoPositionModel;
use App\Repository\StaffDepartmentAreasStoreRepository;
use App\Traits\TokenTrait;

class SysService extends BaseService
{
    use TokenTrait;

    public static $not_must_params = [
    ];

    public static $validate_currency = [
        'id' => 'Required|IntGe:1',                                             //审核ID
        //'organization_name' => 'Required|StrLenGeLe:2,300',                     //网点名称
        'audit_id' => 'Required',                                             //ID
        'status' => 'Required|IntIn:2,3',                                       //审批状态
        'approval_arr' => 'IfIntEq:status,2|Required|ArrLenGe:1',           //审批同意修改资产数量
        'approval_arr[*].id' => 'Required|StrLenGeLe:1,300',                    //资产ID
        'approval_arr[*].nums' => 'Required|IntGe:1',                           //数量
        'reject_reason' => 'IfIntEq:status,3|Required|StrLenGeLe:1,300',        //拒绝原因
    ];

    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return SysService
     */
    public static function getInstance(): SysService
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 转岗-部门下拉
     * @param array $paramIn
     * @return array|false
     */
    public function getDepartmentList($paramIn = [])
    {
        $staffId = $paramIn["staff_id"] ?? "";
        $sysDepartment = HrStaffInfoModel::findFirst([
            "conditions" => "staff_info_id = :staff_info_id:",
            "columns" => "sys_department_id,node_department_id",
            "bind" => [ "staff_info_id" => $staffId]
        ]);
        if (empty($sysDepartment)) {
            return false;
        }
        $getAncestry = SysDepartmentModel::findFirst([
            'conditions' => 'id = :str: and deleted = 0',
            'bind' => [
                'str' => $sysDepartment->sys_department_id,
            ],
            'columns' => 'id,ancestry_v3'
        ]);
        if (empty($getAncestry)) {
            return false;
        }
        $getAncestry = $getAncestry->toArray();
        return SysDepartmentModel::find([
            'conditions' => '( ancestry_v3 like :chain: or id = :department_id: ) and deleted = 0',
            'bind' => [
                'chain'         => $getAncestry['ancestry_v3'] . "/%",
                'department_id' => $getAncestry['id']
            ],
            'columns' => 'id,name',
            'order' => 'name asc'
        ])->toArray();
    }

    /**
     * 转岗获取职位下拉列表
     * @Access  public
     * @Param   array
     * @Return  array
     * @return array
     */
    public function getPositionList($paramIn = []): array
    {
        $departmentId = $paramIn["department_id"] ?? "";

        if (empty($departmentId)) {
            return [];
        }

        $builder = $this->modelsManager->createBuilder()
            ->columns('hr_job_department_relation.job_id as id, hr_job_title.job_name,hr_job_department_relation.working_day_rest_type')
            ->addFrom(HrJobDepartmentRelationModel::class, 'hr_job_department_relation')
            ->leftJoin(\App\Modules\Hc\Models\HrJobTitleModel::class, 'hr_job_department_relation.job_id = hr_job_title.id', 'hr_job_title')
            ->where('hr_job_title.status = 1');
        if (is_array($departmentId)) {
            $builder->inWhere('hr_job_department_relation.department_id', $departmentId);
        } else {
            $builder->andWhere('hr_job_department_relation.department_id = :sys_department_id:', ['sys_department_id' => $departmentId]);
        }
        $builder->orderby('CONVERT(hr_job_title.job_name USING gbk) ASC');
        $data =  $builder->getQuery()->execute()->toArray();

        if(!empty($data)) {
            foreach ($data as $key => $value) {
                if (!empty($value['working_day_rest_type'])) {
                    $working_day_rest_type_arr = explode(',', $value['working_day_rest_type']);
                    foreach ($working_day_rest_type_arr as $w_k => $w_v) {
                        if ($w_v == Enums::WORKING_DAY_REST_TYPE_91) {
                            continue;
                        }

                        $data[$key]['working_day_rest_type_items'][] = [
                            'key'   => $w_v,
                            'value' => self::$t->_('working_day_rest_type_' . $w_v),
                        ];
                    }
                } else {
                    $data[$key]['working_day_rest_type'] = [];
                }
            }
        }
        return  $data;
    }

    /**
     * 获取网点
     * @param array $paramIn
     * @return array
     */
    public function getStoreList($paramIn = []): array
    {
        $departmentId = $paramIn["department_id"] ?? "";

        //校验参数
        if (empty($departmentId)) {
            return [];
        }
        $store_data = [];
	    $department_list = $this->getStoreDepartmentList();
        if (in_array($departmentId, $department_list["NetworkManagement"])) {
            //Network Management
	        // 查询一下 env   env 存在用 env 的 没有 走默认的
	        $networkCategory = (new SettingEnvModel())->getSetVal('jobtransfer_network_category');
            $category = empty($networkCategory) ? [1,2,10] : explode(',',$networkCategory);
            
	        
        } elseif (in_array($departmentId, $department_list["ShopProject"])) {
            //Shop Project
	        //这里是shop 部门的可选网点类型
	        $shopCategory = (new SettingEnvModel())->getSetVal('jobtransfer_shop_category');
            $category =  empty($shopCategory) ?  [4,5,7] : explode(',',$shopCategory);
        } elseif (in_array($departmentId, $department_list["Hub"])) {
            //Hub
	        //这里是hub 部门的可选网点类型
	        $hubCategory = (new SettingEnvModel())->getSetVal('jobtransfer_hub_category');
            $category = empty($hubCategory) ? [8,9] : explode(',',$hubCategory);
            $store_data[] = ["id"=> '-1', "name"=> 'Head Office'];
        } elseif (in_array($departmentId, $department_list["Bhub"])){
            //BHub
	        //这里是bhub 部门的可选网点类型
	        $bhubCategory = (new SettingEnvModel())->getSetVal('jobtransfer_bhub_category');
            $category = empty($bhubCategory) ? [12] : explode(',',$bhubCategory);
        }elseif (in_array($departmentId, $department_list["NetworkBulky"])){
	        //这里是Network Bulky 的可选网点类型
	        $bulkyCategory = (new SettingEnvModel())->getSetVal('jobtransfer_bulky_category');
	        $category = empty($bulkyCategory) ? [] : explode(',',$bulkyCategory);
        }else {
            return [];
        }
		if(empty($category)){
			return [];
		}
        $res_data =  SysStoreModel::find([
            "conditions" => "category in({category:array}) and state=1",
            "columns" => "id,name",
            "bind" => [
                'category' => $category
            ]
        ])->toArray();

        return array_merge($res_data,$store_data);
    }

    /**
     * 获取network hub shop 部门列表
     */
    public function getStoreDepartmentList(): array
    {
        $model = new SysDepartmentModel();
        $network = $model->getNetworkDepartmentIds();

        $shop = $model->getShopDepartmentIds();

        $hub = $model->getHubDepartmentIds();

        $bhub =$model->getBhubDepartmentIds();
	
	    $NetworkBulky = $model->getNetworkBulkyDepartmentIds();

        return [
            'NetworkManagement' => $network ?? [],
            'ShopProject'       => $shop ?? [],
            'Hub'               => $hub ?? [],
            'Bhub'              => $bhub ?? [],
            'NetworkBulky'      => $NetworkBulky ??[],
        ];
    }

    /**
     * 转岗获取hc下拉列表
     * @Access  public
     * @Param   array
     * @return array
     */
    public function getHcList($paramIn = []): array
    {
        //[1]获取参数
        $departmentId = $paramIn["department_id"] ?? "";
        $storeId      = $paramIn["store_id"] ?? "";
        $jobTitle     = $paramIn["job_title_id"] ?? "";

        if (empty($paramIn["department_id"]) || empty($paramIn["store_id"]) || empty($paramIn['job_title_id'])) {
            return [];
        }

        $ac = new ApiClient('by', '', 'get_job_transfer_hc_list', static::$language);
        $ac->setParams([
            [
                'department_id' => $departmentId,
                'store_id' => $storeId,
                'job_title_id' => $jobTitle,
            ]
        ]);
        $res = $ac->execute();
        return $res['result']['data'] ?? [];
    }

    /**
     * 将数组打包成key => value
     * @param $data
     * @param bool $needTranslate
     * @return array
     */
    public function package($data, $needTranslate = true): array
    {
        $list = [];
        if (is_array($data)) {
            foreach ($data as $key => $value) {
                $list[] = [
                    'key'   => $key,
                    'value' => $needTranslate == true ? self::$t->_($value) : $value
                ];
            }
        }
        return $list;
    }

    /**
     * 获取转岗类型下拉
     * @return array
     */
    public function getJobTransferTypes(): array
    {
        $type = $this->getTransferType();

        return $this->package($type);
    }

    /**
     * 获取转岗状态下拉
     * @return array
     */
    public function getJobTransferState(): array
    {
        $type = $this->getTransferState();

        return $this->package($type);
    }

    /**
     * 获取审批状态下拉
     * @return array
     */
    public function getJobTransferAuditState(): array
    {
        $type = $this->getAuditState();

        return $this->package($type);
    }

    /**
     * 获取转岗状态下拉
     * @return array
     */
    public function getJobTransferCarOwner(): array
    {
        $type = $this->getCarOwner();

        return $this->package($type);
    }

    /**
     * 获取审批状态下拉
     * @return array
     */
    public function getJobTransferDataSource(): array
    {
        $type = $this->getDataSource();

        return $this->package($type);
    }

    /**
     * 获取状态下拉列表
     * @Return array
     */
    public function getAuditState(): array
    {
        return [
            Enums::BY_APPROVAL_STATUS_PENDING => self::$t->_('wms_audit_status.1'),
            Enums::BY_APPROVAL_STATUS_APPROVAL  => self::$t->_('wms_audit_status.2'),
            Enums::BY_APPROVAL_STATUS_REJECT  => self::$t->_('wms_audit_status.3'),
            Enums::BY_APPROVAL_STATUS_CANCEL  => self::$t->_('wms_audit_status.4'),
        ];
    }

    /**
     * 获取转岗类型下拉列表
     * @Return array
     */
    public function getTransferType(): array
    {
        return [
            Enums::JOB_TRANSFER_TYPE_IN_DEP => self::$t->_('job_transfer_type.1'),
        ];
    }

    /**
     * 获取转岗状态下拉列表
     * @Return array
     */
    public function getTransferState(): array
    {
        return [
            Enums::JOB_TRANSFER_STATE_PENDING => self::$t->_('job_transfer_state.1'),
            Enums::JOB_TRANSFER_STATE_NO  => self::$t->_('job_transfer_state.2'),
            Enums::JOB_TRANSFER_STATE_SUCCESS  => self::$t->_('job_transfer_state.3'),
            Enums::JOB_TRANSFER_STATE_FAIL  => self::$t->_('job_transfer_state.4'),
        ];
    }

    /**
     * 获取状态下拉列表
     * @Return array
     */
    public function getTransferDataSource(): array
    {
        return [
            Enums::DATA_FROM_BY  => 'Backyard',
            Enums::DATA_FROM_OA  => 'OA',
        ];
    }

    /**
     * 获取车辆归属下拉列表
     * @Return array
     */
    public function getCarOwner(): array
    {
        return [
            Enums::CAR_OWNER_PERSONAL_STATUS  => self::$t->_('car_owner.1'),
            Enums::CAR_OWNER_COMPANY_STATUS   => self::$t->_('car_owner.2'),
            Enums::CAR_OWNER_BORROW_STATUS   => self::$t->_('car_owner.3'),
        ];
    }

    /**
     * 获取数据来源下拉列表
     * @Return array
     */
    private function getDataSource(): array
    {
        return [
            Enums::DATA_FROM_BY  => 'Backyard',
            Enums::DATA_FROM_OA  => 'OA',
        ];
    }

    /**
     * 获取网点对应的大区片区
     * @param $storeId
     * @return array
     */
    public function getStoreRegionPiece($storeId): array
    {
        if (empty($storeId)) {
            return [];
        }

        $info_data = SysStoreModel::findFirst([
            "conditions" => "id = :store_id:",
            "columns" => "manage_region, manage_piece",
            "bind" => [
                'store_id' => $storeId
            ]
        ]);
        return isset($info_data) && $info_data ? $info_data->toArray() : [];
    }

    /**
     * 获取员工信息
     * @param $staff_info_id
     * @return array
     */
    public function getStaffInfo($staff_info_id): array
    {

        $StaffInfo = HrStaffInfoModel::findFirst([
            "conditions" => "staff_info_id = :staff_info_id: and state = 1 and formal in (1,4) and is_sub_staff = 0",
            "columns" => "state,sys_store_id,job_title,sys_department_id,node_department_id",
            "bind" => [
                "staff_info_id" => $staff_info_id
            ]
        ]);
        return isset($StaffInfo) && $StaffInfo ? $StaffInfo->toArray() : [];
    }

    /**
     * 获取指定部门的hrbp   这里是 员工管辖范围的查找
     * @param $department_id
     * @param $extend
     * @return string
     */
    public function findHRBP($department_id, $extend): string
    {
        $storeId   = $extend['store_id'] ?? '';
        $storeInfo = SysStoreModel::findFirst([
            'conditions' => 'id = :store_id:',
            'bind'       => ['store_id' => $storeId],
        ]);

        $region   = isset($storeInfo->manage_region) && !empty($storeInfo->manage_region) ? $storeInfo->manage_region : 0;
        $piece    = isset($storeInfo->manage_piece) && !empty($storeInfo->manage_piece) ? $storeInfo->manage_piece : 0;
        $category = isset($storeInfo->category) && !empty($storeInfo->category) ? $storeInfo->category : 0;
        $role_id  = JobTransferEnums::ROLES_HRBP_ID;//hrbp
        //员工ID
        $staff_info_id = $extend['staff_info_id'] ?? 0;

        $staff_ids = [];
        do {
            //[1]先获取网点对应的HRBP
            //网点不能管辖总部网点
            if (!empty($storeId) && $storeId != '-1') {
                //[1]先获取网点对应的HRBP
                $builder = $this->modelsManager->createBuilder();
                $builder->columns('position.staff_info_id');
                $builder->from(['position' => HrStaffInfoPositionModel::class]);
                $builder->leftjoin(HrStaffInfoModel::class, 'position.staff_info_id = staff.staff_info_id', 'staff');
                $builder->leftjoin(HrStaffManageStoreModel::class, 'position.staff_info_id = region.staff_info_id',
                    'region');
                //在职 在编的
                $builder->Where(
                    'staff.state = :state: and staff.formal = :formal: and position.position_category = :position_category: and region.deleted = :deleted: and region.type = :type: ',
                    ['state'             => StaffInfoEnums::STAFF_STATE_IN,
                     'formal'            => StaffInfoEnums::FORMAL_IN,
                     'deleted'           => GlobalEnums::IS_NO_DELETED,
                     'position_category' => $role_id,
                     'type'              => StaffDepartmentAreasStoreRepository::$TYPE_1,
                    ]
                );
                $builder->inWhere('region.store_id', [HrStaffManageStoreModel::$all_id, $storeId]);
                if (!empty($staff_info_id)) {
                    $builder->andWhere('position.staff_info_id = :staff_info_id:', ['staff_info_id' => $staff_info_id]);
                }
                $staff_ids = $builder->getQuery()->execute()->toArray();
                if (!empty($staff_ids)) {
                    break;
                }
            }

            //[2]获取片区对应的HRBP
            if (!empty($piece)) {
                //获取片区对应的HRBP
                $builder = $this->modelsManager->createBuilder();
                $builder->columns('position.staff_info_id');
                $builder->from(['position' => HrStaffInfoPositionModel::class]);
                $builder->leftjoin(HrStaffInfoModel::class, 'position.staff_info_id = staff.staff_info_id', 'staff');
                $builder->leftjoin(HrStaffManagePieceModel::class, 'position.staff_info_id = region.staff_info_id',
                    'region');
                //在职 在编的
                $builder->Where(
                    'staff.state = :state: and staff.formal = :formal: and position.position_category = :position_category: and region.deleted = :deleted: and region.type = :type: ',
                    ['state'             => StaffInfoEnums::STAFF_STATE_IN,
                     'formal'            => StaffInfoEnums::FORMAL_IN,
                     'deleted'           => GlobalEnums::IS_NO_DELETED,
                     'position_category' => $role_id,
                     'type'              => StaffDepartmentAreasStoreRepository::$TYPE_1,
                    ]
                );
                $builder->inWhere('region.piece_id', [HrStaffManagePieceModel::$all_id, $piece]);
                if (!empty($staff_info_id)) {
                    $builder->andWhere('position.staff_info_id = :staff_info_id:', ['staff_info_id' => $staff_info_id]);
                }
                $staff_ids = $builder->getQuery()->execute()->toArray();
                if (!empty($staff_ids)) {
                    break;
                }
            }

            //[3]获取大区对应的HRBP
            if (!empty($region)) {
                $builder = $this->modelsManager->createBuilder();
                $builder->columns('position.staff_info_id');
                $builder->from(['position' => HrStaffInfoPositionModel::class]);
                $builder->leftjoin(HrStaffInfoModel::class, 'position.staff_info_id = staff.staff_info_id', 'staff');
                $builder->leftjoin(HrStaffManageRegionModel::class, 'position.staff_info_id = region.staff_info_id',
                    'region');
                //在职 在编的
                $builder->Where(
                    'staff.state = :state: and staff.formal = :formal: and position.position_category = :position_category: and region.deleted = :deleted: and region.type = :type: ',
                    ['state'             => StaffInfoEnums::STAFF_STATE_IN,
                     'formal'            => StaffInfoEnums::FORMAL_IN,
                     'deleted'           => GlobalEnums::IS_NO_DELETED,
                     'position_category' => $role_id,
                     'type'              => StaffDepartmentAreasStoreRepository::$TYPE_1,
                    ]
                );
                $builder->inWhere('region.region_id', [HrStaffManageRegionModel::$all_id, $region]);
                if (!empty($staff_info_id)) {
                    $builder->andWhere('position.staff_info_id = :staff_info_id:', ['staff_info_id' => $staff_info_id]);
                }
                $staff_ids = $builder->getQuery()->execute()->toArray();
                if (!empty($staff_ids)) {
                    break;
                }
            }

            if (!empty($category)) {
                //[4]获取网点类型对应的HRBP
                $builder = $this->modelsManager->createBuilder();
                $builder->columns('position.staff_info_id');
                $builder->from(['position' => HrStaffInfoPositionModel::class]);
                $builder->leftjoin(HrStaffInfoModel::class, 'position.staff_info_id = staff.staff_info_id', 'staff');
                $builder->leftjoin(HrStaffManageStoreCategoryModel::class,
                    'position.staff_info_id = region.staff_info_id', 'region');
                //在职 在编的
                $builder->Where(
                    'staff.state = :state: and staff.formal = :formal:  and position.position_category = :position_category: and region.deleted = :deleted: and region.type = :type: and region.store_category = :store_category: ',
                    ['state'             => StaffInfoEnums::STAFF_STATE_IN,
                     'formal'            => StaffInfoEnums::FORMAL_IN,
                     'deleted'           => GlobalEnums::IS_NO_DELETED,
                     'position_category' => $role_id,
                     'type'              => StaffDepartmentAreasStoreRepository::$TYPE_1,
                     'store_category'    => $category,
                    ]
                );
                if (!empty($staff_info_id)) {
                    $builder->andWhere('position.staff_info_id = :staff_info_id:', ['staff_info_id' => $staff_info_id]);
                }
                $staff_ids = $builder->getQuery()->execute()->toArray();
                if (!empty($staff_ids)) {
                    break;
                }
            }
            if (!empty($department_id)) {
                //[5]获取部门对应的角色人员
                //获取部门对应的角色
                $builder = $this->modelsManager->createBuilder();
                $builder->columns('position.staff_info_id');
                $builder->from(['position' => HrStaffInfoPositionModel::class]);
                $builder->leftjoin(HrStaffInfoModel::class, 'position.staff_info_id = staff.staff_info_id', 'staff');
                $builder->leftjoin(HrStaffManageDepartmentModel::class, 'position.staff_info_id = region.staff_info_id',
                    'region');
                //在职 在编的
                $builder->Where(
                    'staff.state = :state: and staff.formal = :formal: and position.position_category = :position_category: and region.deleted = :deleted: and region.type = :type: and region.department_id = :department_id:',
                    ['state'             => StaffInfoEnums::STAFF_STATE_IN,
                     'formal'            => StaffInfoEnums::FORMAL_IN,
                     'deleted'           => GlobalEnums::IS_NO_DELETED,
                     'position_category' => $role_id,
                     'department_id'     => $department_id,
                     'type'              => StaffDepartmentAreasStoreRepository::$TYPE_1,
                    ]
                );
                if (!empty($staff_info_id)) {
                    $builder->andWhere('position.staff_info_id = :staff_info_id:', ['staff_info_id' => $staff_info_id]);
                }
                $staff_ids = $builder->getQuery()->execute()->toArray();

                //获取部门的部门链
                $department  = SysDepartmentModel::findFirst($department_id);
                $ancestry_v3 = isset($department->ancestry_v3) ? explode('/', $department->ancestry_v3) : [];
                //获取是否为包含子部门的管辖范围
                if (!empty($ancestry_v3)) {
                    $builder = $this->modelsManager->createBuilder();
                    $builder->columns('position.staff_info_id');
                    $builder->from(['position' => HrStaffInfoPositionModel::class]);
                    $builder->leftjoin(HrStaffInfoModel::class, 'position.staff_info_id = staff.staff_info_id',
                        'staff');
                    $builder->leftjoin(HrStaffManageDepartmentModel::class,
                        'position.staff_info_id = region.staff_info_id', 'region');
                    //在职 在编的  并且包含子部门的
                    $builder->Where(
                        'staff.state = :state: and staff.formal = :formal: and position.position_category = :position_category: and region.deleted = :deleted: and region.is_include_sub = :is_include_sub: and region.type = :type: ',
                        ['state'             => StaffInfoEnums::STAFF_STATE_IN,
                         'formal'            => StaffInfoEnums::FORMAL_IN,
                         'deleted'           => GlobalEnums::IS_NO_DELETED,
                         'position_category' => $role_id,
                         'type'              => StaffDepartmentAreasStoreRepository::$TYPE_1,
                         'is_include_sub'    => HrStaffManageDepartmentModel::$is_include_sub_1,
                        ]
                    );
                    $builder->inWhere('region.department_id', $ancestry_v3);
                    if (!empty($staff_info_id)) {
                        $builder->andWhere('position.staff_info_id = :staff_info_id:',
                            ['staff_info_id' => $staff_info_id]);
                    }
                    $position_category_include_sub = $builder->getQuery()->execute()->toArray();
                    if (!empty($position_category_include_sub)) {
                        $staff_ids = array_merge($staff_ids, $position_category_include_sub);
                    }
                }

                if (!empty($staff_ids)) {
                    break;
                }
            }
        } while (0);

        $staffInfoIds = array_unique(array_filter(array_column($staff_ids, 'staff_info_id')));
        return $staffInfoIds ? implode(',', $staffInfoIds) : "";
    }

    /**
     * 获取员工管理的片区
     * @param $staff_info_id
     * @return array
     */
    public function getManagerPieces($staff_info_id): array
    {
        $managerInfo = SysManagePieceModel::find([
            'conditions' => 'manager_id = :manager_id:',
            'bind'       => [
                'manager_id' => $staff_info_id,
            ],
        ]);
        if (!empty($managerInfo)) {
            $managerInfoArr = $managerInfo->toArray();
            $managerInfoArr = array_column($managerInfoArr, 'id');
        }
        return $managerInfoArr ?? [];
    }

    /**
     * 获取员工管理的大区
     * @param $staff_info_id
     * @return array
     */
    public function getManagerRegions($staff_info_id): array
    {
        $managerInfo = SysManageRegionModel::find([
            'conditions' => 'manager_id = :manager_id:',
            'bind'       => [
                'manager_id' => $staff_info_id,
            ],
        ]);
        if (!empty($managerInfo)) {
            $managerInfoArr = $managerInfo->toArray();
            $managerInfoArr = array_column($managerInfoArr, 'id');
        }
        return $managerInfoArr ?? [];
    }

    /**
     * 获取员工管理的网点
     * @param $staff_info_id
     * @return array
     */
    public function getStoreManager($staff_info_id): array
    {
        $managerInfo = SysStoreModel::find([
            'conditions' => 'manager_id = :manager_id:',
            'bind'       => [
                'manager_id' => $staff_info_id,
            ],
        ]);
        if (!empty($managerInfo)) {
            $managerInfoArr = $managerInfo->toArray();
            $managerInfoArr = array_column($managerInfoArr, 'id');
        }
        return $managerInfoArr ?? [];
    }

    /**
     * 获取转岗角色下拉
     * @param array $paramIn
     * @return array
     */
    public function getRoleList($paramIn = []): array
    {
        $ac = new ApiClient('hris', '', 'department_job_title_role', static::$language);
        $ac->setParams([
            [
                "department_id" => $paramIn["department_id"],
                "job_title_id"  => $paramIn["job_title_id"],
            ]
        ]);
        $res = $ac->execute();

        return $res['result'] ?? [];
    }

    /**
     * 获取全部角色下拉列表
     */
    public function getAllRoleList(): array
    {
        $ac = new ApiClient('hris','', 'role_list', static::$language);
        $ac->setParams(['']);
        $ret = $ac->execute();
        return $ret['result'] ? array_column($ret['result'], null, 'role_id') : [];
    }

    /**
     * 获取批号
     * @throws \Exception
     */
    public function getBatchNumber(): array
    {
        $key = $this->getTokenId();

        return [
            'code' => ErrCode::$SUCCESS,
            'data' => $key,
        ];
    }


    /**
     * 大区负责人 或者片区负责人 获取大区//片区下的网点
     * @param $staff_info_id
     * @return array
     */
    public function getManagerAllStoreIds($staff_info_id): array
    {
        $allManagerStores = [];
        //管辖大区
        $mange_region_ids = $this->getManagerRegions($staff_info_id);
        //管辖片区
        $mange_piece_ids = $this->getManagerPieces($staff_info_id);

        //管辖网点
        $stores_ids = $this->getStoreManager($staff_info_id);
        if (!empty($stores_ids)) {
            $allManagerStores = array_merge($allManagerStores, $stores_ids);  //管辖网点
        }

        //管辖片区
        if (!empty($mange_piece_ids)) {
            //获取片区下所有网点
            $store_ids        = $this->getStoreListByPieceNew($mange_piece_ids);
            $allManagerStores = array_merge($allManagerStores, $store_ids);
        }

        //管辖大区
        if (!empty($mange_region_ids)) {
            // 获取大区下所有网点
            $store_ids        = $this->getStoreListByRegionNew($mange_region_ids);
            $allManagerStores = array_merge($allManagerStores, $store_ids);
        }

        return array_values(array_unique($allManagerStores));
    }

    /**
     * 获取片区下所有网点
     * @param array $manage_piece
     * @return array
     */
    public function getStoreListByPieceNew(array $manage_piece)
    {
        if (empty($manage_piece)) {
            return [];
        }
        $store_ids = SysStoreModel::find([
            'conditions' => "state = 1 and manage_piece in ({manage_piece_ids:array})",
            'bind'       => [
                'manage_piece_ids' => $manage_piece,
            ],
            'columns'    => 'id',
        ])->toArray();

        return array_column($store_ids, 'id');
    }

    /**
     * 获取指定大区下所有网点
     * @param array $manage_region
     * @return array
     */
    public function getStoreListByRegionNew(array $manage_region)
    {
        if (empty($manage_region)) {
            return [];
        }

        $store_ids = SysStoreModel::find([
            'conditions' => "state = 1 and manage_region in ({manage_region_ids:array})",
            'bind'       => [
                'manage_region_ids' => $manage_region,
            ],
            'columns'    => 'id',
        ])->toArray();

        return array_column($store_ids, 'id');
    }


}