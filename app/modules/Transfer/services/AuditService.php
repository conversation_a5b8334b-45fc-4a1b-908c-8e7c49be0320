<?php

namespace App\Modules\Transfer\Services;

use App\Library\ApiClient;
use App\Library\Enums;
use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Library\RocketMQ;
use App\Modules\Organization\Models\HrStaffInfoPositionModel;
use App\Modules\Transfer\Models\HrHcModel;
use App\Modules\Transfer\Models\JobTransferModel;
use App\Modules\User\Models\HrStaffInfoModel;
use App\Traits\TokenTrait;
use App\Util\RedisKey;

class AuditService extends BaseService
{
    public static $not_must_params = [
    ];

    public static $validate_approval = [
        'id' => 'Required',                                     //ID
    ];

    public static $validate_reject = [
        'id' => 'Required',                                    //ID
        'reject_reason' => 'Required|StrLenGeLe:1,300',        //拒绝原因
    ];

    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return AuditService
     */
    public static function getInstance(): AuditService
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 批量申请转岗
     * @param $params
     * @param $submitter_id
     * @return mixed
     * @throws \Exception
     */
    public function batchApplyJobTransfer($paramIn, $submitter_id)
    {
        try {
            //校验申请数据
            $res = $this->checkBatchApplyJobTransfer($paramIn, $submitter_id);
            if (!empty($res)) {
                return ['fail' => $res];
            }
            $batchNumber = $paramIn['batch_number'];

            $this->logger->info('set_batch_add_number----参数：' . json_encode($batchNumber));
            $ac = new ApiClient('by', '', 'set_batch_add_number', static::$language);
            $ac->setParams([
                [
                    "batch_number" => $batchNumber,
                    "ids" => array_column($paramIn['data'], 'staff_id'),
                ]
            ]);

            $res = $ac->execute();
            $this->logger->info('setBatchAddNumber----结果' . json_encode($res));

            //批量添加申请
            $rmq = new RocketMQ('create-approval');
            $params = [
                'apply_timestamp' => time(),
                'apply_user'      => $submitter_id,
                'apply_type'      => 20,
                'apply_data'      => $paramIn['data'],
                'apply_uuid'      => $batchNumber,
            ];

            $rid = $rmq->sendToMsg($params);
        } catch (\Exception $e) {
            $this->logger->warning("batchApplyJobTransfer:" . $e->getMessage() . $e->getTraceAsString());
            return false;
        }
        return $rid;
    }

    /**
     * 批量申请转岗
     * @param $params
     * @param $submitter_id
     * @return array
     * @throws \Exception
     */
    public function checkBatchApplyJobTransfer($params, $submitter_id): array
    {
        //批量校验转岗申请
        $ac = new ApiClient('by', '', 'check_batch_apply', static::$language);
        $ac->setParams([
            [
                'data' => $params['data'],
                'submitter_id' => $submitter_id,
            ]
        ]);
        $res = $ac->execute();

        return $res['result'] ?? [];
    }

    /**
     * 审批
     * @param $params
     * @param $user
     * @return array
     */
    public function audit($params, $user): array
    {
    	//一些参数初始化
	    $params['after_base_salary']  = $params['after_base_salary'] ?? 0; // 基本工资
	    $params['after_exp_allowance'] = $params['after_exp_allowance'] ?? 0; //经验津贴
	    $params['after_position_allowance'] = $params['after_position_allowance'] ?? 0; //职位津贴
	    $params['after_car_rental'] = $params['after_car_rental'] ?? 0; //租车津贴
	    $params['after_trip_payment'] =  $params['after_trip_payment'] ?? 0; //油补
	    $params['after_notebook_rental'] = $params['after_notebook_rental'] ?? 0; //电脑补贴
	    $params['after_recommended'] = $params['after_recommended'] ?? 0; //推荐补贴
	    $params['after_food_allowance'] = $params['after_food_allowance'] ?? 0; //餐补
	    $params['after_dangerous_area'] = $params['after_dangerous_area'] ?? 0; //危险地区补贴
	    $params['after_house_rental'] = $params['after_house_rental'] ?? 0; //租房津贴
	    $params['after_gasoline_allowance'] = $params['after_gasoline_allowance'] ?? 0; //销售油补
	    $params['after_island_allowance'] =  $params['after_island_allowance'] ?? 0; //海岛补助
	    $params['after_deminimis_benefits'] = $params['after_deminimis_benefits'] ??0; //无税补贴
	    $params['after_performance_allowance'] =  $params['after_performance_allowance'] ?? 0; //绩效补贴
	    $params['after_other_non_taxable_allowance'] = $params['after_other_non_taxable_allowance'] ?? 0 ; //其他无税金补贴
	    $params['after_other_taxable_allowance'] = $params['after_other_taxable_allowance'] ?? 0; //其他纳税津贴
	    $params['after_phone_subsidy'] = $params['after_phone_subsidy'] ?? '' ; //转岗后通话补贴
        $url = isset($params['upload_files']) && $params['upload_files'] ? array_column($params['upload_files'], 'url'): [];
        if (is_string($params['after_role_ids'])){
            $roles = explode(',',$params['after_role_ids']);
        }elseif (is_array($params['after_role_ids'])){
            $roles = $params['after_role_ids'];
        }else{
            $roles = [];
        }
        //获取转岗详情
        $detailInfo = JobTransferModel::findFirst($params['id']);
        if (!isset($detailInfo) || empty($detailInfo)) {
            return [
                'code' => ErrCode::$VALIDATE_ERROR,
                'message' => self::$t->_('no valid job transfer id'),
                'data' => null,
            ];
        }
        if (empty($detailInfo->hc_expiration_date)) {
            $info = HrHcModel::findFirst([
                'conditions' => 'hc_id = :hc_id:',
                'columns' => 'expirationdate, hc_id',
                'bind' => [
                    'hc_id' => $detailInfo->hc_id
                ]
            ]);
        }
        $staffInfo = HrStaffInfoModel::findFirst(
            [
                'conditions' => 'staff_info_id = :staff_id:',
                'bind' => [
                    'staff_id' => $detailInfo->staff_id
                ]
            ]
        );
        if(empty($staffInfo)){
            return [
                'code' => ErrCode::$VALIDATE_ERROR,
                'message' => 'no valid staff id',
                'data' => null,
            ];
        }
        if($staffInfo->job_title_grade_v2 >=16 ){

            if(!in_array($params['after_phone_subsidy'],[0,1000])){
                return [
                    'code' => ErrCode::$VALIDATE_ERROR,
                    'message' => self::$t->_('communication_allowance_big'),
                    'data' => null,
                ];
            }
        }else{
            if(!in_array($params['after_phone_subsidy'],[0,500])){
                return [
                    'code' => ErrCode::$VALIDATE_ERROR,
                    'message' => self::$t->_('communication_allowance_small'),
                    'data' => null,
                ];
            }
        }

        //申请人角色
        $positions = HrStaffInfoPositionModel::find([
            'conditions' => 'staff_info_id = :staff_info_id:',
            'bind' => ['staff_info_id' => $user],
        ])->toArray();
        $positions = array_column($positions, 'position_category');
        if (!in_array(68, $positions) && isset($detailInfo->senior_auditor) && !empty($detailInfo->senior_auditor) &&
            $params['status'] == 2 && (
                $params['after_base_salary'] != ($detailInfo->after_base_salary / 100)
                || $params['after_exp_allowance'] != ($detailInfo->after_exp_allowance / 100)
                || $params['after_position_allowance'] != ($detailInfo->after_position_allowance / 100)
                || $params['after_car_rental'] !=( $detailInfo->after_car_rental / 100)
                || $params['after_trip_payment'] != ($detailInfo->after_trip_payment / 100)
                || $params['after_notebook_rental'] != ($detailInfo->after_notebook_rental / 100)
                || $params['after_recommended'] != ($detailInfo->after_recommended / 100)
                || $params['after_food_allowance'] != ($detailInfo->after_food_allowance / 100)
                || $params['after_dangerous_area'] != ($detailInfo->after_dangerous_area / 100)
                || $params['after_house_rental'] != ($detailInfo->after_house_rental / 100)
                || $params['after_gasoline_allowance'] != ($detailInfo->after_gasoline_allowance / 100)
                || $params['after_island_allowance'] != ($detailInfo->after_island_allowance / 100)
                || $params['after_deminimis_benefits'] != ($detailInfo->after_deminimis_benefits / 100)
                || $params['after_performance_allowance'] != ($detailInfo->after_performance_allowance / 100)
                || $params['after_other_non_taxable_allowance'] != ($detailInfo->after_other_non_taxable_allowance / 100)
                || $params['after_other_taxable_allowance'] != ($detailInfo->after_other_taxable_allowance / 100)
                || $params['after_phone_subsidy'] != ($detailInfo->after_phone_subsidy / 100)
            )) {

            return [
                'code' => ErrCode::$VALIDATE_ERROR,
                'message' => self::$t->_('job_transfer.msg_1'),
                'data' => null,
            ];
        }

        $ac = new ApiClient('by', '', 'audit_job_transfer', static::$language);
        $ac->setParams([
            [
                'audit_id' => $params['id'],
                'audit_type' => 20, //转岗OA渠道
                'staff_id' => $user, //审批人
                'status' => $params['status'],
                'reject_reason' => $params['reject_reason'] ?? '',
                'base_salary' => $params['after_base_salary'],
                'exp_allowance' => $params['after_exp_allowance'],
                'position_allowance' => $params['after_position_allowance'],
                'food_allowance' => $params['after_food_allowance'],
                'dangerous_area' => $params['after_dangerous_area'],
                'notebook_rental' => $params['after_notebook_rental'],
                'house_rental' => $params['after_house_rental'],
                'car_rental' => $params['after_car_rental'],
                'gasoline_allowance' => $params['after_gasoline_allowance'],
                'island_allowance' => $params['after_island_allowance'],
                'recommended'  => $params['recommended'] ?? '',
                'deminimis_benefits' => $params['after_deminimis_benefits'],
                'performance_allowance' => $params['after_performance_allowance'],
                'other_non_taxable_allowance' => $params['after_other_non_taxable_allowance'],
                'other_taxable_allowance' => $params['after_other_taxable_allowance'],
                'phone_subsidy' => $params['after_phone_subsidy'],
                'car_owner' => $params['car_owner'],
                'rental_car_cteated_at' => date("Y-m-d", strtotime($params['rental_car_cteated_at'])) ?? '',
                'hc_expiration_date'  => isset($detailInfo->hc_expiration_date) && $detailInfo->hc_expiration_date
                    ? date("Y-m-d", strtotime($detailInfo->hc_expiration_date))
                    : (isset($info->expirationdate) ? date("Y-m-d", strtotime($info->expirationdate)) : null),
                'upload_files'  => $url,
                'positions' => $positions,
                'role_ids' => $roles,
                'after_date' => $params['after_date'],
                'after_working_day_rest_type' => $params['after_working_day_rest_type']
            ]
        ]);
        $res = $ac->execute();

        $code = $res['result']['code'] ?? $res['code'];
        $message = $res['result']['msg'] ?? $res['msg'];
        $data = $res['data'] ?? [];
        return [
            'code' => $code,
            'message' => $message,
            'data' => $data,
        ];
    }



    /**
     * 审批
     * @param $params
     * @param $user
     * @return array
     */
    public function auditMY($params, $user): array
    {
	
	    //一些参数初始化
	    $params['after_base_salary']  = $params['after_base_salary'] ?? 0; // 基本工资
	    $params['after_exp_allowance'] = $params['after_exp_allowance'] ?? 0; //经验津贴
	    $params['after_position_allowance'] = $params['after_position_allowance'] ?? 0; //职位津贴
	    $params['after_car_rental'] = $params['after_car_rental'] ?? 0; //租车津贴
	    $params['after_trip_payment'] =  $params['after_trip_payment'] ?? 0; //油补
	    $params['after_notebook_rental'] = $params['after_notebook_rental'] ?? 0; //电脑补贴
	    $params['after_recommended'] = $params['after_recommended'] ?? 0; //推荐补贴
	    $params['after_food_allowance'] = $params['after_food_allowance'] ?? 0; //餐补
	    $params['after_dangerous_area'] = $params['after_dangerous_area'] ?? 0; //危险地区补贴
	    $params['after_house_rental'] = $params['after_house_rental'] ?? 0; //租房津贴
	    $params['after_gasoline_allowance'] = $params['after_gasoline_allowance'] ?? 0; //销售油补
	    $params['after_island_allowance'] =  $params['after_island_allowance'] ?? 0; //海岛补助
	    $params['after_deminimis_benefits'] = $params['after_deminimis_benefits'] ??0; //无税补贴
	    $params['after_performance_allowance'] =  $params['after_performance_allowance'] ?? 0; //绩效补贴
	    $params['after_other_non_taxable_allowance'] = $params['after_other_non_taxable_allowance'] ?? 0 ; //其他无税金补贴
	    $params['after_other_taxable_allowance'] = $params['after_other_taxable_allowance'] ?? 0; //其他纳税津贴
	    $params['after_phone_subsidy'] = $params['after_phone_subsidy'] ?? '' ; //转岗后通话补贴
	
	    $params['after_mobile_allowance'] = $params['after_mobile_allowance'] ?? 0; //手机津贴
	    $params['after_attendance_allowance'] = $params['after_attendance_allowance'] ?? 0; // 出勤津贴
	    $params['after_fuel_allowance'] = $params['after_fuel_allowance'] ?? 0; //油补津贴
	    $params['after_car_allowance'] = $params['after_car_allowance'] ?? 0; //私家车车补津贴
	    $params['after_vehicle_allowance'] = $params['after_vehicle_allowance'] ?? 0; //货车车补津贴
	    $params['after_gdl_allowance'] =  $params['after_gdl_allowance'] ?? 0; //GDL补贴
	    $params['after_site_allowance'] =  $params['after_site_allowance'] ?? 0; //区域津贴
        $url = isset($params['upload_files']) && $params['upload_files'] ? array_column($params['upload_files'], 'url'): [];
        if (is_string($params['after_role_ids'])){
            $roles = explode(',',$params['after_role_ids']);
        }elseif (is_array($params['after_role_ids'])){
            $roles = $params['after_role_ids'];
        }else{
            $roles = [];
        }
        //获取转岗详情
        $detailInfo = JobTransferModel::findFirst($params['id']);
        if (!isset($detailInfo) || empty($detailInfo)) {
            return [
                'code' => ErrCode::$VALIDATE_ERROR,
                'message' => self::$t->_('no valid job transfer id'),
                'data' => null,
            ];
        }
        if (empty($detailInfo->hc_expiration_date)) {
            $info = HrHcModel::findFirst([
                'conditions' => 'hc_id = :hc_id:',
                'columns' => 'expirationdate, hc_id',
                'bind' => [
                    'hc_id' => $detailInfo->hc_id
                ]
            ]);
        }
        $staffInfo = HrStaffInfoModel::findFirst(
            [
                'conditions' => 'staff_info_id = :staff_id:',
                'bind' => [
                    'staff_id' => $detailInfo->staff_id
                ]
            ]
        );
        if(empty($staffInfo)){
            return [
                'code' => ErrCode::$VALIDATE_ERROR,
                'message' => 'no valid staff id',
                'data' => null,
            ];
        }
        if($staffInfo->job_title_grade_v2 >=16 ){

            if(!in_array($params['after_phone_subsidy'],[0,1000])){
                return [
                    'code' => ErrCode::$VALIDATE_ERROR,
                    'message' => self::$t->_('communication_allowance_big'),
                    'data' => null,
                ];
            }
        }else{
            if(!in_array($params['after_phone_subsidy'],[0,500])){
                return [
                    'code' => ErrCode::$VALIDATE_ERROR,
                    'message' => self::$t->_('communication_allowance_small'),
                    'data' => null,
                ];
            }
        }

        //申请人角色
        $positions = HrStaffInfoPositionModel::find([
            'conditions' => 'staff_info_id = :staff_info_id:',
            'bind' => ['staff_info_id' => $user],
        ])->toArray();
        $positions = array_column($positions, 'position_category');
        if (!in_array(68, $positions) && isset($detailInfo->senior_auditor) && !empty($detailInfo->senior_auditor) &&
            $params['status'] == 2 && (
                $params['after_base_salary'] != ($detailInfo->after_base_salary / 100)
                || $params['after_exp_allowance'] != ($detailInfo->after_exp_allowance / 100)
                || $params['after_position_allowance'] != ($detailInfo->after_position_allowance / 100)
                || $params['after_car_rental'] !=( $detailInfo->after_car_rental / 100)
                || $params['after_trip_payment'] != ($detailInfo->after_trip_payment / 100)
                || $params['after_notebook_rental'] != ($detailInfo->after_notebook_rental / 100)
                || $params['after_recommended'] != ($detailInfo->after_recommended / 100)
                || $params['after_food_allowance'] != ($detailInfo->after_food_allowance / 100)
                || $params['after_dangerous_area'] != ($detailInfo->after_dangerous_area / 100)
                || $params['after_house_rental'] != ($detailInfo->after_house_rental / 100)
                || $params['after_gasoline_allowance'] != ($detailInfo->after_gasoline_allowance / 100)
                || $params['after_island_allowance'] != ($detailInfo->after_island_allowance / 100)
                || $params['after_deminimis_benefits'] != ($detailInfo->after_deminimis_benefits / 100)
                || $params['after_performance_allowance'] != ($detailInfo->after_performance_allowance / 100)
                || $params['after_other_non_taxable_allowance'] != ($detailInfo->after_other_non_taxable_allowance / 100)
                || $params['after_other_taxable_allowance'] != ($detailInfo->after_other_taxable_allowance / 100)
                || $params['after_phone_subsidy'] != ($detailInfo->after_phone_subsidy / 100)

                || $params['after_mobile_allowance'] != ($detailInfo->after_mobile_allowance / 100)
                || $params['after_attendance_allowance'] != ($detailInfo->after_attendance_allowance / 100)
                || $params['after_fuel_allowance'] != ($detailInfo->after_fuel_allowance / 100)
                || $params['after_car_allowance'] != ($detailInfo->after_car_allowance / 100)
                || $params['after_vehicle_allowance'] != ($detailInfo->after_vehicle_allowance / 100)
                || $params['after_gdl_allowance'] != ($detailInfo->after_gdl_allowance / 100)
                || $params['after_site_allowance'] != ($detailInfo->after_site_allowance / 100)
            )) {

            return [
                'code' => ErrCode::$VALIDATE_ERROR,
                'message' => self::$t->_('job_transfer.msg_1'),
                'data' => null,
            ];
        }

        $ac = new ApiClient('by', '', 'audit_job_transfer', static::$language);
        $ac->setParams([
            [
                'audit_id' => $params['id'],
                'audit_type' => 20, //转岗OA渠道
                'staff_id' => $user, //审批人
                'status' => $params['status'],
                'reject_reason' => $params['reject_reason'] ?? '',
                'base_salary' => $params['after_base_salary'],
                'exp_allowance' => $params['after_exp_allowance'],
                'position_allowance' => $params['after_position_allowance'],
                'food_allowance' => $params['after_food_allowance'],
                'dangerous_area' => $params['after_dangerous_area'],
                'notebook_rental' => $params['after_notebook_rental'],
                'house_rental' => $params['after_house_rental'],
                'car_rental' => $params['after_car_rental'],
                'gasoline_allowance' => $params['after_gasoline_allowance'],
                'island_allowance' => $params['after_island_allowance'],
                'site_allowance' => $params['after_site_allowance'],
                'recommended'  => $params['recommended'] ?? '',
                'deminimis_benefits' => $params['after_deminimis_benefits'],
                'performance_allowance' => $params['after_performance_allowance'],
                'other_non_taxable_allowance' => $params['after_other_non_taxable_allowance'],
                'other_taxable_allowance' => $params['after_other_taxable_allowance'],
                'phone_subsidy' => $params['after_phone_subsidy'],

                'mobile_allowance' => $params['after_mobile_allowance'],
                'attendance_allowance' => $params['after_attendance_allowance'],
                'fuel_allowance' => $params['after_fuel_allowance'],
                'car_allowance' => $params['after_car_allowance'],
                'vehicle_allowance' => $params['after_vehicle_allowance'],
                'gdl_allowance' => $params['after_gdl_allowance'],

                'car_owner' => $params['car_owner'],
                'rental_car_cteated_at' => date("Y-m-d", strtotime($params['rental_car_cteated_at'])) ?? '',
                'hc_expiration_date'  => isset($detailInfo->hc_expiration_date) && $detailInfo->hc_expiration_date
                    ? date("Y-m-d", strtotime($detailInfo->hc_expiration_date))
                    : (isset($info->expirationdate) ? date("Y-m-d", strtotime($info->expirationdate)) : null),
                'upload_files'  => $url,
                'positions' => $positions,
                'role_ids' => $roles,
                'after_date' => $params['after_date'],
                'after_working_day_rest_type' => $params['after_working_day_rest_type']
            ]
        ]);
        $res = $ac->execute();

        $code = $res['result']['code'] ?? $res['code'];
        $message = $res['result']['msg'] ?? $res['msg'];
        $data = $res['data'] ?? [];
        return [
            'code' => $code,
            'message' => $message,
            'data' => $data,
        ];
    }
}