<?php

namespace App\Modules\Transfer\Services;

use App\Library\ApiClient;
use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Modules\Hc\Models\SysStoreModel;
use App\Modules\Organization\Models\HrStaffInfoPositionModel;
use App\Modules\Organization\Services\DepartmentService;
use App\Modules\Transfer\Models\SettingEnvModel;
use App\Modules\Transfer\Models\SysDepartmentModel;
use App\Modules\Transfer\Models\HrHcModel;
use App\Modules\Transfer\Models\JobTransferModel;
use App\Modules\Transfer\Models\SysManagePieceModel;
use App\Modules\Transfer\Models\SysManageRegionModel;
use App\Modules\User\Models\HrJobTitleModel;
use App\Modules\User\Models\HrStaffInfoModel;
use App\Modules\User\Models\HrStaffItemsModel;
use MQ\Model\TopicMessage;
use MQ\MQClient;


class JobTransferService extends BaseService
{
    private static $instance;
    private $jobTransfer;
    private $sysDepartment;
    public $job_transfer_apply_queue = 'job_transfer_apply_queue';

    protected static $max_excel_count = 40;
    protected static $min_excel_count = 1;

    protected static $hub_management_ancestry_v3 = '999/222/1/25';
    protected static $hub_supervisor = '272';

    public function __construct()
    {
        $this->jobTransfer = JobTransferModel::class;
        $this->sysDepartment = SysDepartmentModel::class;
    }

    /**
     * @return JobTransferService
     */
    public static function getInstance(): JobTransferService
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 上传员工ID
     * @param $excel_data
     * @param $submitter_id
     * @return array
     * @throws \Exception
     */
    public function uploadApplyStaffIds($excel_data, $submitter_id): array
    {
        //输入行数限制
        if (count($excel_data) < self::$min_excel_count) {
            return ['code' => 2, 'msg' => 'Excel error', 'data' => []];
        }

        if (count($excel_data) > self::$max_excel_count) {
            return ['code' => 2, 'msg' => static::$t->_('job_transfer_err.7'), 'data' => []];
        }

        $successList = [];
        $failList = [];

        //校验
        foreach ($excel_data as $key => $value) {
            $staffId = $value[0];

            //校验转岗人
            [$errCode, $errMsg] = $this->checkStaffInfo($staffId, $submitter_id);
            if ($errCode == ErrCode::$SUCCESS) {
                //获取详情
                $successList[] = DetailService::getInstance()->getStaffDetailInfo(['staff_id' => $staffId], $submitter_id);
            } else {
                $failList[] = [
                    'staff_id' => $staffId,
                    'fail_msg' => $errMsg ?? '',
                ];
            }
        }

        return [
            'code' => ErrCode::$SUCCESS,
            'msg' => 'success',
            'data' => [
                "success" => $successList,
                "fail"    => $failList,
            ]
        ];
    }

    /**
     * 校验转岗员工信息
     * @param $job_transfer_staff_id
     * @param $submitter_id
     * @return mixed
     */
    public function checkStaffInfo($job_transfer_staff_id, $submitter_id): array
    {
        //获取转岗员工信息
        $transferInfo = SysService::getInstance()->getStaffInfo($job_transfer_staff_id);
        if (empty($transferInfo)) {
            //不存在员工id
            return [ErrCode::$JOB_TRANSFER_STAFF_NOT_EXIST_ERROR, self::$t->_('department.staff_id_error1')];
        }

        $storeInfo = SysStoreModel::findFirst([
            'id = :id:',
            'bind' => ['id' => $transferInfo['sys_store_id']]
        ]);

        //转岗人不在职
        if (!isset($transferInfo["state"]) || $transferInfo["state"] != 1) {
            return [ErrCode::$JOB_TRANSFER_STAFF_NOT_ON_JOB_ERROR, self::$t->_('department.staff_id_error2')];
        }

        //转岗人和审核人不能是同一个人
        if ($job_transfer_staff_id == $submitter_id) {
            return [ErrCode::$JOB_TRANSFER_STAFF_BE_SAME_ERROR, self::$t->_('department.staff_id_error4')];
        }

        //job_transfer表中拿到数据相关员工的转岗状态
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('staff_id,id,after_department_id,after_store_id,after_position_id,approval_state,state');
        $builder->from(['r' => JobTransferModel::class]);
        $builder->where('deleted = 0');
        $builder->andWhere('id = (SELECT MAX(id) FROM ' . JobTransferModel::class . ' WHERE staff_id = :staff_info_id:)', ["staff_info_id" => $job_transfer_staff_id]);
        $jobTsfStatus = $builder->getQuery()->execute()->toArray();

        if (!empty($jobTsfStatus)) {
            //验证是否存在转岗
            $jobTsfStatus = current($jobTsfStatus);
            if (isset($jobTsfStatus['approval_state']) && isset($jobTsfStatus['state']) && (
                    $jobTsfStatus['approval_state'] == 1 ||
                    $jobTsfStatus['approval_state'] == 2 && $jobTsfStatus['state'] == 1
                )
            ) {
                //存在转岗申请，或者存在待转岗
                return [ErrCode::$JOB_TRANSFER_EXIST_TRANSFER_ERROR, self::$t->_('job_transfer_err.1')];
            }
        }

        //获取审核人一级部门
        $submitterInfo = SysService::getInstance()->getStaffInfo($submitter_id);

        //一级部门不一样的审核人和转岗人不能申请
        //https://l8bx01gcjr.feishu.cn/docs/doccn5krVYyPEDKcH8UNDGOGk4d
        if ($transferInfo['sys_department_id'] !== $submitterInfo['sys_department_id']) {
            //申请人跟转岗人部门不一致
            return [ErrCode::$JOB_TRANSFER_DEPARTMENT_ERROR, self::$t->_('job_transfer_err.2')];
        }

        //校验转岗前网点 & 部门是否存在hrbp
        //如果不存在，则不能申请
        $hrbp_data = $this->findHRBPToBy($transferInfo['node_department_id'], $transferInfo['sys_store_id']);
        if ($hrbp_data['code'] != ErrCode::$SUCCESS || !isset($hrbp_data['data']['hrbp'])) {
            return [ErrCode::$VALIDATE_ERROR, self::$t->_('retry_later')];
        }
        $hrbp = $hrbp_data['data']['hrbp'];
        if (empty($hrbp)) {
            return [ErrCode::$JOB_TRANSFER_NOT_EXIST_HRBP_ERROR, self::$t->_('job_transfer_err.3')];
        }

        //获取片区经理的管理片区
        $pieces = SysService::getInstance()->getManagerPieces($submitter_id);
        //获取大区经理的管理片区
        $regions = SysService::getInstance()->getManagerRegions($submitter_id);
        //验证网点负责人
        $storeManager = SysService::getInstance()->getStoreManager($submitter_id);

        $envModel = new SettingEnvModel();
        $hubManagementId = $envModel->getSetVal('dept_hub_management_id');
        $shopManagementId = $envModel->getSetVal('dept_shop_management_id');
        $networkManagementId = $envModel->getSetVal('dept_network_management_id');
        $networkPlanId = $envModel->getSetVal('dept_network_planning_id');
        $networkOperationsId = $envModel->getSetVal('dept_network_operations_id');
        //Network Bulky Planning
	    $BulkyPlanId = $envModel->getSetVal('dept_network_bulky_planning_id');
        //Network Bulky Operations
	    $BulkyOperationsId = $envModel->getSetVal('dept_network_bulky_operations_id');
	    //Network Bulky
	    $networkBulkyId = $envModel->getSetVal('dept_network_bulky_id');

        if($hubManagementId) {
            $hub_management = SysDepartmentModel::findFirst([
                                                        "conditions" => " id = :id:",
                                                        'bind'       => ['id' => $hubManagementId],
                                                    ]);
            if($hub_management){
                static::$hub_management_ancestry_v3 = $hub_management->ancestry_v3;
            }
        }

        //2. hub management及下级各部门部门负责人：限制可为“负责的部门”内员工申请转岗
        $sysDeptInfo= SysDepartmentModel::find([
            "conditions" => " (ancestry_v3 like :ancestry_v3: or ancestry_v3 = :ancestry_v3_id:)and deleted=0 ",
            'bind' => [
                'ancestry_v3' => static::$hub_management_ancestry_v3."/%",
                'ancestry_v3_id' => static::$hub_management_ancestry_v3
            ]
        ])->toArray();
        if($sysDeptInfo){
            $sysDeptInfo = array_column($sysDeptInfo,'manager_id');
        }
        if (in_array($submitter_id,$sysDeptInfo)){
            //获取申请人所有管辖的部门
            $curStaffDepf=SysDepartmentModel::find(
                [
                    "conditions" => " manager_id= :manager_id:",
                    "bind"=>[
                        "manager_id" => $submitter_id
                    ],
                    "columns"=>"id,ancestry_v3"
                ]
            )->toArray();
            foreach ($curStaffDepf as $key=>$item){
                if($item['ancestry_v3']== static::$hub_management_ancestry_v3){
                    continue;
                }
                if(strpos($item['ancestry_v3'],static::$hub_management_ancestry_v3.'/' )===false){
                    unset($curStaffDepf[$key]);
                }
            }
            $staffIdDepf=SysDepartmentModel::findFirst(
                [
                    "conditions" => " id= :node_department_id:",
                    "bind"=>[
                        "node_department_id" => $transferInfo['node_department_id']
                    ],
                    "columns"=>"id,ancestry_v3"
                ]
            );
            if($staffIdDepf){
                $staffIdDepf=$staffIdDepf->toArray();
                foreach ($curStaffDepf as $item){
                    if(is_numeric(strpos($staffIdDepf['ancestry_v3'],$item['ancestry_v3']))){
                        $this->logger->info("符合 hub负责的员工");
                        return [ErrCode::$SUCCESS, 'success'];
                    }
                }
                $this->logger->info("hub不是负责的员工");

            }
        }

        //获取 $networkPlanId  部门负责人
        $networkPlanId_manager_id = '';
        if (!empty($networkPlanId)) {
            $networkPlanId_manager = SysDepartmentModel::findFirst(
                [
                    "conditions" => " id= :node_department_id:",
                    "bind"       => [
                        "node_department_id" => $networkPlanId
                    ],
                    "columns"    => "id,ancestry_v3,manager_id"
                ]
            );
            $networkPlanId_manager_id = $networkPlanId_manager ? $networkPlanId_manager->manager_id : $networkPlanId_manager_id;
        }
        //获取$BulkyPlanId  部门负责人
        $BulkyPlanId_manager_id = '';
        if(!empty($BulkyPlanId)){
            $BulkyPlanId_manager = SysDepartmentModel::findFirst(
                [
                    "conditions" => " id= :node_department_id:",
                    "bind"       => [
                        "node_department_id" => $BulkyPlanId
                    ],
                    "columns"    => "id,ancestry_v3,manager_id"
                ]
            );
            $BulkyPlanId_manager_id = $BulkyPlanId_manager ? $BulkyPlanId_manager->manager_id : $BulkyPlanId_manager_id;
        }

        //检查申请人权限
        //shop只有AM可以申请
        //如果既不是AM、DM、网点负责人、不在Network Planning部门，也不可以申请  并且 不是 Network Planning部门负责人
        //按照Network 部门 增加  Bulky部门的权限  为了防止 泰国以外的 $networkBulkyId 不存在 增加  !empty($networkBulkyId)
        if ((isset($submitterInfo['sys_department_id']) && $submitterInfo['sys_department_id'] == $shopManagementId && isset($regions) && empty($regions)) ||
            (isset($submitterInfo['sys_department_id']) && $submitterInfo['sys_department_id'] == $networkManagementId && !(isset($storeManager) && $storeManager) && !(isset($regions) && $regions) &&
             !(isset($pieces) && $pieces) && isset($submitterInfo['node_department_id']) && !in_array($submitterInfo['node_department_id'], [$networkPlanId, $BulkyPlanId]) && !in_array($submitter_id, [$networkPlanId_manager_id, $BulkyPlanId_manager_id]))
        ) {
            $this->logger->info("没有权限");
            return [ErrCode::$JOB_TRANSFER_NO_PERMISSION_ERROR, self::$t->_('job_transfer_err.6')];
        }


        if (isset($regions) && $regions && is_array($regions)) {

            if (!in_array($storeInfo->manage_region ?? '', $regions)) {
                $this->logger->info("不是负责区域的员工");
            } else {
                $this->logger->info("符合 负责区域的员工");

                return [ErrCode::$SUCCESS, 'success'];
            }
        }
        if (isset($pieces) && $pieces && is_array($pieces)) {
            if (!in_array($storeInfo->manage_piece ?? '', $pieces)) {
                $this->logger->info("不是负责piece的员工");
            } else {
                $this->logger->info("符合 负责piece的员工");
                return [ErrCode::$SUCCESS, 'success'];
            }
        }

        //获取network operations 部门ID
        $deptInfo = SysDepartmentModel::findFirst($networkOperationsId);
        if (!empty($deptInfo)) {
            $chain = $deptInfo->ancestry_v3 ?? '';
        }

        $operationsDepartmentArr = [];
        if (isset($chain) && $chain) {
            $operationsDepartments = SysDepartmentModel::find([
                'conditions' => 'ancestry_v3 like :department_chain: or id = :department_id:',
                'bind'       => [
                    'department_chain' => $chain . '/%',
                    'department_id' => $networkOperationsId,
                ],
            ])->toArray();
            $operationsDepartmentArr = array_column($operationsDepartments, 'id');
        }

        //PH
        if (get_country_code() == GlobalEnums::PH_COUNTRY_CODE) {
            //Network Operations（South）部门ID
            $networkOperationsSouthId = $envModel->getSetVal('dept_network_operations_south_id');

            //获取Network Operations（South）及其子部门
            $operationsSouthDepartmentArr   = (new DepartmentService())->getChildrenListByDepartmentIdV2($networkOperationsSouthId,true);
            $operationsSouthDepartmentArr[] = $networkOperationsSouthId;
            $operationsDepartmentArr        = array_merge($operationsDepartmentArr, $operationsSouthDepartmentArr);
        }

        //Network Planning部门 或者负责人 只可以给network operations及其子部门申请
        if (isset($submitterInfo['node_department_id']) && ($submitterInfo['node_department_id'] == $networkPlanId ||  $submitter_id == $networkPlanId_manager_id)) {
            if (
                !in_array($transferInfo['node_department_id'], $operationsDepartmentArr)
            ) {
                $this->logger->info(" 不符合 Network Planning部门只可以给network operations及其子部门申请");
            }else{
                $this->logger->info("符合 Network Planning部门只可以给network operations及其子部门申请");
                return [ErrCode::$SUCCESS, 'success'];
            }
        }
        
        //  5. Network Bulky Planning 限制可为Network Bulky Operations 部门及下级部门员工申请
	    //获取network Bulky operations 部门ID
	    if(!empty($BulkyOperationsId)){
		    $deptInfo = SysDepartmentModel::findFirst($BulkyOperationsId);
		    $operationsDepartmentArr= [];
		    if (!empty($deptInfo)) {
			    $chain = $deptInfo->ancestry_v3 ?? '';
		    }
		    if (isset($chain) && $chain) {
			    $operationsDepartments = SysDepartmentModel::find([
				                                                      'conditions' => 'ancestry_v3 like :department_chain: or id = :department_id:',
				                                                      'bind'       => [
					                                                      'department_chain' => $chain . '/%',
					                                                      'department_id' => $BulkyOperationsId,
				                                                      ],
			                                                      ])->toArray();
			    $operationsDepartmentArr = array_column($operationsDepartments, 'id');
		    }
		
		    //NNetwork Bulky Planning 限制可为Network Bulky Operations 部门及下级部门员工申请
            if (isset($submitterInfo['node_department_id']) && ($submitterInfo['node_department_id'] == $BulkyPlanId || $submitter_id == $BulkyPlanId_manager_id )) {
                if (!in_array($transferInfo['node_department_id'], $operationsDepartmentArr ?? [])
                ) {
                    $this->logger->info(" 不符合 Network Bulky Planning 限制可为Network Bulky Operations 部门及下级部门员工申请");
                } else {
                    $this->logger->info("符合 Network Bulky Planning 限制可为Network Bulky Operations 部门及下级部门员工申请");
                    return [ErrCode::$SUCCESS, 'success'];
                }
            }

	    }
	  
	    
		//这里是判断网点
        if (isset($storeManager) && $storeManager ) {
            if( !in_array($transferInfo["sys_store_id"], $storeManager)) {
                $this->logger->info(" 不符合 负责的网点内员工");
            }else{
                $this->logger->info("符合 负责的网点内员工");

                return [ErrCode::$SUCCESS, 'success'];
            }
        }

        //Hub Supervisor职位：限制仅可为所属网点内员工转岗
        if ($submitterInfo['job_title'] == static::$hub_supervisor ) {
            if( $submitterInfo["sys_store_id"] != $transferInfo["sys_store_id"]) {
                $this->logger->info(" 不符合 Hub Supervisor职位：限制仅可为所属网点内员工转岗");
            }else{
                $this->logger->info("符合 Hub Supervisor职位：限制仅可为所属网点内员工转岗");
                return [ErrCode::$SUCCESS, 'success'];
            }
        }
        $this->logger->info("全部不符合");
        return [ErrCode::$JOB_TRANSFER_NO_PERMISSION_ERROR, self::$t->_('job_transfer_err.6')];
    }

    /**
     * 获取各种数据
     * @param $list
     * @param $type
     * @return array
     */
    public function genBaseData($list, $type): array
    {
        // 获取部门信息
        $old_department = array_column($list, 'after_department_id');
        $new_department = array_column($list, 'current_department_id');
        $departmentIds  = array_unique(array_merge($old_department, $new_department));
        $sys_department = new SysDepartmentModel();
        $department_info = $sys_department->getDataByIds($departmentIds, 'id, name');

        // 获取职位信息
        $old_job = array_column($list, 'after_position_id');
        $new_job = array_column($list, 'current_position_id');
        $jobs = array_unique(array_merge($old_job, $new_job));
        $job = new HrJobTitleModel();
        $job_info = $job->getDataByIds($jobs);

        // 获取员工信息
        $staff_ids = array_column($list, 'staff_id');
        // 当前直线上级
        $manager = array_column($list, 'current_manager_id');
        // 当前虚线上级
        $indirect_manager = array_column($list, 'current_indirect_manger_id');
        // 转岗后直线上级
        $after_manager_id = array_column($list, 'after_manager_id');
        $all_staff_ids = array_unique(array_merge($staff_ids, $manager, $indirect_manager, $after_manager_id));
        $staff = HrStaffInfoModel::find([
            "conditions" => "staff_info_id IN ({ids:array})",
            "columns" => "emp_id, staff_info_id, name, name_en, sex, personal_email, identity, mobile, state, sys_store_id,hire_date",
            "bind" => ["ids" => array_values($all_staff_ids)]
        ]) -> toArray();
        $user_info = array_column($staff, null, 'staff_info_id');

        if ($type == Enums::FETCH_DATA_LIST) {
            return [$department_info, $job_info, $user_info];
        }

        // 网点信息
        $old_store = array_column($list, 'current_store_id');
        $new_store = array_column($list, 'after_store_id');
        $store_ids = array_unique(array_merge($old_store, $new_store));
        $store = new SysStoreModel();
        $store_info = $store->getDataByIds($store_ids, 'id, name,manage_region,manage_piece');
        $store_info['-1'] =[
            'id' => "-1",
            'name' => 'Head Office',
            'manage_region'=>'-1',
            'manage_piece'=>'-1',
        ];


        // 大区
        $regionIds = array_column($store_info, 'manage_region');
        $regions = \App\Modules\Hc\Models\SysManageRegionModel::find([
                                                                         'columns' => 'id, name',
                                                                         'conditions' => ' id in ({ids:array}) ',
                                                                         'bind' => ['ids' => $regionIds]
                                                                     ])->toArray();
        $regions = array_column($regions, 'name', 'id');

        // 片区
        $pieceIds = array_column($store_info, 'manage_piece');
        $pieces = SysManagePieceModel::find([
                                                 'columns' => 'id, name',
                                                 'conditions' => ' id in ({ids:array}) ',
                                                 'bind' => ['ids' => $pieceIds]
                                             ])->toArray();
        $pieces = array_column($pieces, 'name', 'id');



        //HC信息
        $hcIds = array_column($list, 'hc_id');
        $hcIds = array_unique($hcIds);
        $hc = new HrHcModel();
        $hc_info = $hc->getDataByIds($hcIds, 'hc_id, expirationdate', 'hc_id');

        if ($type == Enums::FETCH_DATA_EXPORT) {
            return [$department_info, $job_info, $user_info, $store_info, $hc_info,$manager,$regions,$pieces];
        }

        // 获取角色信息
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('staff_info_id, GROUP_CONCAT(position_category) position_category');
        $builder->from(['s' => HrStaffInfoPositionModel::class]);
        $builder->inWhere('staff_info_id', $staff_ids);
        $builder->groupBy('staff_info_id');
        $roles = $builder->getQuery()->execute()->toArray();
        $roles = array_column($roles, 'position_category', 'staff_info_id');

        // 获取到所有已转岗的员工
        $done_ids = array();
        foreach ($list as $item) {
            if ($item['state'] == Enums::JOB_TRANSFER_STATE_SUCCESS) {
                $done_ids[] = $item['staff_id'];
            }
        }

        $manager = array();
        if ($done_ids) {

            $builder = $this->modelsManager->createBuilder();
            $builder->columns('b.staff_info_id staff_id, a.value manager_staff_id, b.name');
            $builder->from(['a' => HrStaffItemsModel::class]);
            $builder->innerJoin(HrStaffInfoModel::class, 'a.value = b.staff_info_id', 'b');
            $builder->inWhere('a.staff_info_id', $done_ids);
            $builder->andWhere('a.item = :item:', ['item' => 'INDIRECT_MANGER']);
            $manager = $builder->getQuery()->execute()->toArray();
            $manager = array_column($manager, null, 'staff_id');
        }

        return [$department_info, $job_info, $user_info, $store_info, $roles, $manager,$regions,$pieces];
    }

    /**
     * @description:调用 by  查找 hrbp 逻辑
     * @param string $department_id 部门 id
     * @param string $store_id   网点 id
     * @return    array : ['code'=>1,'data'=>['hrbp'=>'123']];
     * <AUTHOR> L.J
     * @time       : 2022/10/12 19:26
     */

    public function  findHRBPToBy($department_id='',$store_id = ''){
        if(empty($department_id) || empty($store_id)){
            return [];
        }
        //获取管辖范围
        $rpc        = new ApiClient('by', '', 'svc_find_hrbp',static::$language);
        $api_params = [
            [
                'department_id' => $department_id,   //部门 id
                'store_id'     => $store_id,   //网点 id
            ],
        ];
        $rpc->setParams($api_params);
        $data = $rpc->execute();
        return $data['result'] ?? [];
    }
}