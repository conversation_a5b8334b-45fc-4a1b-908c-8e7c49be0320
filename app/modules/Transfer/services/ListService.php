<?php

namespace App\Modules\Transfer\Services;

use App\Library\ApiClient;
use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use app\library\Enums\HrHcEnums;
use App\Library\ErrCode;
use App\Library\RocketMQ;
use App\Modules\Hc\Models\HrJobTitleModel;
use App\Modules\Organization\Models\HrJobDepartmentRelationModel;
use App\Modules\Organization\Models\HrStaffInfoPositionModel;
use App\Modules\Organization\Services\HrStaffInfoPositionService;
use App\Modules\Transfer\Models\AuditApprovalModel;
use App\Modules\Transfer\Models\HrHcModel;
use App\Modules\Transfer\Models\HrStoreCarRentalModel;
use App\Modules\Transfer\Models\HrStoreSubsidiesCategoryModel;
use App\Modules\Transfer\Models\JobTransferModel;
use App\Modules\Transfer\Models\SettingEnvModel;
use App\Modules\Transfer\Models\SysDepartmentModel;
use App\Modules\User\Models\HrStaffInfoModel;
use App\Util\RedisKey;

class ListService extends BaseService
{
    public static $not_must_params = [
        'name',
        'state',
        'after_date_start',
        'after_date_end',
        'hc_id',
        'approval_state',
    ];

    public static $validate_list_search = [
        'page_size' => 'IntGt:0',                          //每页条数
        'page_num' => 'IntGt:0',                           //页码
        "name" => "Str",                                   //工号、姓名、HRId、英文名
        "state" => "Int",                                  //转岗状态
        "after_date_start" => "date",                      //转岗日期
        "after_date_end" => "date",                        //转岗日期
        "hc_id" => "Int",                                  //HCId
        "approval_state" => "Int",                         //转岗状态
    ];

    public static $validate_batch_add = [
        'batch_number' => 'Required|StrLenGe:1',            //批次号
        'data' => 'Required|ArrLenGe:1',                    //转岗数据
        'data[*].staff_id' => 'Required|IntGt:0',           //转岗人工号
        "data[*].type" => 'Required|IntGt:0',               //转岗类型
        "data[*].after_department_id" => "Required|IntGt:0",//转岗人前部门
        "data[*].after_position_id" => "Required|IntGt:0",  //转岗人前职位
        "data[*].after_store_id" => "Required|StrLenGe:1",  //转岗人前网点
        "data[*].hc_id" => "Required|IntGt:0",              //HCId
        "data[*].job_handover_staff_id" => "Required|IntGt:0", //转岗交接人
        "data[*].reason" => "Required|StrLenGeLe:1,500",    //转岗原因
        "data[*].after_working_day_rest_type" => "Required|Int",    //转岗工作天数轮休规则
    ];

    public static $validate_update = [
        'id' => 'IntGt:0',                                  //转岗id
        'after_date' => 'IfExist:after_date|Required|Date', //转岗日期
        'hc_id' => 'IfExist:hc_id|Required|IntGt:0',        //Hc id
    ];

    public static $validate_batch = [
        'batch_number' => 'Required|StrLenGe:1',            //转岗批次号
    ];

    public static $validate_do_transfer = [
        'id' => 'Required|IntGe:0',                         //转岗ID
    ];

    //网点类型
    public static $stores_category = [
        1  => 'sp',
        2  => 'dc',
        3  => 'branch_agent',
        4  => 'shop_pickup_only',
        5  => 'shop_pickup_delivery',
        6  => 'fh',
        7  => 'shop_ushop',
        8  => 'hub',
        9  => 'os',
        10 => 'bdc',
        12 => 'bhub',
        13 => 'cdc',
    ];
    //网点分类对应的租车津贴默认值 关系数组
    public static $store_car_rental_map = [
        'sp'  => [
            1 => 220,
            2 => 270,
            3 => 370,
            4 => 220,
            5 => 270,
            6 => 370,
        ],
        'bdc' => [
            1 => 270,
            2 => 320,
            3 => 370,
            4 => 270,
            5 => 320,
            6 => 370,
        ],
    ];


    private static $instance;

    private function __construct()
    {
    }

    /**
     * 获取实例
     * @return ListService
     */
    public static function getInstance(): ListService
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 获取列表数据
     * @param $param
     * @param $start
     * @param $limit
     * @return mixed
     */
    public function getBaseTransferData($param, $start, $limit): array
    {
        //查询列表
        $builder = $this->modelsManager->createBuilder();
        $builder->from(JobTransferModel::class);
        $builder->where('1=1');
        if (!empty($param['name'])) {
            $staff = HrStaffInfoModel::find([
                "conditions" => "staff_info_id like :name: or name like :name: or emp_id like :name: or name_en like :name:",
                "columns" => "staff_info_id",
                "bind" => ["name" => "%{$param['name']}%"]
            ])->toArray();
            if ($staff) {
                $staffArr = array_column($staff, 'staff_info_id');
                $builder->inWhere('staff_id', $staffArr);
            }
        }
        if (!empty($param['type'])) {
            $builder->andWhere('type = :type:', ['type' => $param['type']]);
        }
        if (!empty($param['state'])) {
            $builder->andWhere('state = :state:', ['state' => $param['state']]);
        }
        if (!empty($param['after_date_start'])) {
            $builder->andWhere('after_date >= :after_date_start:', ['after_date_start' => $param['after_date_start']]);
        }
        if (!empty($param['after_date_end'])) {
            $builder->andWhere('after_date <= :after_date_end:', ['after_date_end' => $param['after_date_end']]);
        }
        if (!empty($param['hc_id'])) {
            $builder->andWhere('hc_id = :hc_id:', ['hc_id' => $param['hc_id']]);
        }
        if (!empty($param['approval_state'])) {
            $builder->andWhere('approval_state = :approval_state:', ['approval_state' => $param['approval_state']]);
        }
        if (!empty($param['ids'])) {
            $builder->inWhere('id', $param['ids']);
        }
        if (!empty($param['data_source'])) {
            $builder->andWhere('data_source = :data_source:', ['data_source' => $param['data_source']]);
        }
        if (!empty($param['submitter_id'])) {
            $builder->andWhere('submitter_id = :submitter_id:', ['submitter_id' => $param['submitter_id']]);
        }
        $count = $builder->columns('count(1) as cou')->getQuery()->getSingleResult()->cou;
        if ($count == 0) {
            return [[], 0];
        }
        $list = $builder->columns('*')->orderBy('id desc')->limit($limit, $start)->getQuery()->execute()->toArray();
        return [$list, $count];
    }
    /**
     * 获取列表数据
     * @param $param
     * @param $start
     * @param $limit
     * @return mixed
     */
    public function getBaseTransferDataMY($param, $start, $limit): array
    {
        //查询列表
        $builder = $this->modelsManager->createBuilder();
        $builder->from(JobTransferModel::class);
        $builder->where('1=1');
        if (!empty($param['name'])) {
            $staff = HrStaffInfoModel::find([
                "conditions" => "staff_info_id like :name: or name like :name: or emp_id like :name: or name_en like :name:",
                "columns" => "staff_info_id",
                "bind" => ["name" => "%{$param['name']}%"]
            ])->toArray();
            if ($staff) {
                $staffArr = array_column($staff, 'staff_info_id');
                $builder->inWhere('staff_id', $staffArr);
            }
        }
        if (!empty($param['type'])) {
            $builder->andWhere('type = :type:', ['type' => $param['type']]);
        }
        if (!empty($param['state'])) {
            $builder->andWhere('state = :state:', ['state' => $param['state']]);
        }
        if (!empty($param['after_date_start'])) {
            $builder->andWhere('after_date >= :after_date_start:', ['after_date_start' => $param['after_date_start']]);
        }
        if (!empty($param['after_date_end'])) {
            $builder->andWhere('after_date <= :after_date_end:', ['after_date_end' => $param['after_date_end']]);
        }
        if (!empty($param['hc_id'])) {
            $builder->andWhere('hc_id = :hc_id:', ['hc_id' => $param['hc_id']]);
        }
        if (!empty($param['approval_state'])) {
            $builder->andWhere('approval_state = :approval_state:', ['approval_state' => $param['approval_state']]);
        }
        if (!empty($param['ids'])) {
            $builder->inWhere('id', $param['ids']);
        }
        if (!empty($param['data_source'])) {
            $builder->andWhere('data_source = :data_source:', ['data_source' => $param['data_source']]);
        }
        if (!empty($param['submitter_id'])) {
            $builder->andWhere('submitter_id = :submitter_id:', ['submitter_id' => $param['submitter_id']]);
        }

        $count = $builder->columns('count(1) as cou')->getQuery()->getSingleResult()->cou;
        if ($count == 0) {
            return [[], 0];
        }
        $list = $builder->columns('id,
            serial_no,
            staff_id,
            hc_id,
            state,
            type,
            current_department_id,
            current_position_id,
            current_store_id,
            current_manager_id,
            current_indirect_manger_id,
            after_department_id,
            after_position_id,
            after_store_id,
            after_manager_id,
            after_date,
            submitter_id,
            approval_state,
            before_base_salary,
            before_exp_allowance,
            before_position_allowance,
            before_car_rental,
            before_trip_payment,
            before_notebook_rental,
            before_recommended,
            before_food_allowance,
            before_dangerous_area,
            before_house_rental,
            before_island_allowance,
            before_gasoline_allowance,
            before_deminimis_benefits,
            before_performance_allowance,
            before_other_non_taxable_allowance,
            before_other_taxable_allowance,
            before_phone_subsidy,
            before_mobile_allowance,
            before_attendance_allowance,
            before_fuel_allowance,
            before_car_allowance,
            before_vehicle_allowance,
            before_gdl_allowance,
            before_site_allowance,
            after_base_salary,
            after_exp_allowance,
            after_position_allowance,
            after_car_rental,
            after_trip_payment,
            after_notebook_rental,
            after_recommended,
            after_food_allowance,
            after_dangerous_area,
            after_house_rental,
            after_island_allowance,
            after_gasoline_allowance,
            after_deminimis_benefits,
            after_performance_allowance,
            after_other_non_taxable_allowance,
            after_other_taxable_allowance,
            after_phone_subsidy,
            after_mobile_allowance,
            after_attendance_allowance,
            after_fuel_allowance,
            after_car_allowance,
            after_vehicle_allowance,
            after_gdl_allowance,
            after_site_allowance,
            after_role_ids,
            data_source,
            car_owner,
            reject_salary_detail,
            reason,
            job_handover_staff_id,
            senior_auditor,
            rental_car_cteated_at,
            before_working_day_rest_type,
            after_working_day_rest_type')->orderBy('id desc')->limit($limit, $start)->getQuery()->execute()->toArray();
        return [$list, $count];
    }

    /**
     * 获取列表数据
     * @param $param
     * @param $start
     * @param $limit
     * @return mixed
     */
    public function getBaseTransferDataExMY($param, $start, $limit): array
    {
        //查询列表
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['j' => JobTransferModel::class]);
        $builder->join(AuditApprovalModel::class, "j.id = aa.biz_value and aa.biz_type = 20", "aa");
        $builder->where('1=1');
        if (!empty($param['name'])) {
            $staff = HrStaffInfoModel::find([
                "conditions" => "staff_info_id like :name: or name like :name: or emp_id like :name: or name_en like :name:",
                "columns" => "staff_info_id",
                "bind" => ["name" => "%{$param['name']}%"]
            ])->toArray();
            if ($staff) {
                $staffArr = array_column($staff, 'staff_info_id');
                $builder->inWhere('j.staff_id', $staffArr);
            }
        }
        if (!empty($param['state'])) {
            $builder->andWhere('j.state = :state:', ['state' => $param['state']]);
        }
        if (!empty($param['after_date_start'])) {
            $builder->andWhere('j.after_date >= :after_date_start:', ['after_date_start' => $param['after_date_start']]);
        }
        if (!empty($param['after_date_end'])) {
            $builder->andWhere('j.after_date <= :after_date_end:', ['after_date_end' => $param['after_date_end']]);
        }
        if (!empty($param['hc_id'])) {
            $builder->andWhere('j.hc_id = :hc_id:', ['hc_id' => $param['hc_id']]);
        }
        if (!empty($param['approval_state'])) {
            $builder->andWhere('aa.state = :approval_state:', ['approval_state' => $param['approval_state']]);
        }
        if (!empty($param['ids'])) {
            $builder->inWhere('j.id', $param['ids']);
        }
        if (!empty($param['data_source'])) {
            $builder->andWhere('j.data_source = :data_source:', ['data_source' => $param['data_source']]);
        }
        if (!empty($param['approval_id'])) {
            $builder->andWhere('aa.approval_id = :approval_id:', ['approval_id' => $param['approval_id']]);
        }
        $count = $builder->columns('count(1) as cou')->getQuery()->getSingleResult()->cou;
        if ($count == 0) {
            return [[], 0];
        }
        $list = $builder->columns('j.id,
            j.serial_no,
            j.staff_id,
            j.hc_id,
            j.state,
            j.type,
            j.current_department_id,
            j.current_position_id,
            j.current_store_id,
            j.current_manager_id,
            j.current_indirect_manger_id,
            j.after_department_id,
            j.after_position_id,
            j.after_store_id,
            j.after_manager_id,
            j.after_date,
            j.submitter_id,
            j.approval_state,
            j.before_base_salary,
            j.before_exp_allowance,
            j.before_position_allowance,
            j.before_car_rental,
            j.before_trip_payment,
            j.before_notebook_rental,
            j.before_recommended,
            j.before_food_allowance,
            j.before_mobile_allowance,
            j.before_attendance_allowance,
            j.before_fuel_allowance,
            j.before_car_allowance,
            j.before_vehicle_allowance,
            j.before_gdl_allowance,
            j.before_dangerous_area,
            j.before_house_rental,
            j.before_deminimis_benefits,
            j.before_performance_allowance,
            j.before_other_non_taxable_allowance,
            j.before_other_taxable_allowance,
            j.before_phone_subsidy,
            j.after_base_salary,
            j.after_exp_allowance,
            j.after_position_allowance,
            j.after_car_rental,
            j.after_trip_payment,
            j.after_notebook_rental,
            j.after_recommended,
            j.after_food_allowance,
            j.after_mobile_allowance,
            j.after_attendance_allowance,
            j.after_fuel_allowance,
            j.after_car_allowance,
            j.after_vehicle_allowance,
            j.after_gdl_allowance,
            j.after_dangerous_area,
            j.after_house_rental,
            j.after_deminimis_benefits,
            j.after_performance_allowance,
            j.after_other_non_taxable_allowance,
            j.after_other_taxable_allowance,
            j.after_phone_subsidy,
            j.data_source,
            j.car_owner,
            j.reason,
            j.job_handover_staff_id,
            j.senior_auditor,
            j.rental_car_cteated_at,
            j.before_working_day_rest_type,
            j.after_working_day_rest_type')->orderBy('aa.id desc')->limit($limit, $start)->getQuery()->execute()->toArray();
        return [$list, $count];
    }

    /**
     * 获取转岗在oa左侧菜单的待审批小红点数量
     * @param array $param['approval_state' => '待审批','approval_id' => '审批人ID']
     * @return int
     */
    public function getAuditCount($param)
    {
        //查询列表
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(j.id) as total');
        $builder->from(['j' => JobTransferModel::class]);
        $builder->join(AuditApprovalModel::class, "j.id = aa.biz_value and aa.biz_type = 20", "aa");
        $builder->where('1=1');
        if (!empty($param['approval_state'])) {
            $builder->andWhere('aa.state = :approval_state:', ['approval_state' => $param['approval_state']]);
        }
        if (!empty($param['approval_id'])) {
            $builder->andWhere('aa.approval_id = :approval_id:', ['approval_id' => $param['approval_id']]);
        }
        $total_info = $builder->getQuery()->getSingleResult();
        return intval($total_info->total);
    }

    /**
     * 获取列表数据
     * @param $param
     * @param $start
     * @param $limit
     * @return mixed
     */
    public function getBaseTransferDataEx($param, $start, $limit): array
    {
        //查询列表
        $builder = $this->modelsManager->createBuilder();

        $builder->from(['j' => JobTransferModel::class]);
        $builder->join(AuditApprovalModel::class, "j.id = aa.biz_value and aa.biz_type = 20", "aa");
        $builder->where('1=1');
        if (!empty($param['name'])) {
            $staff = HrStaffInfoModel::find([
                "conditions" => "staff_info_id like :name: or name like :name: or emp_id like :name: or name_en like :name:",
                "columns" => "staff_info_id",
                "bind" => ["name" => "%{$param['name']}%"]
            ])->toArray();
            if ($staff) {
                $staffArr = array_column($staff, 'staff_info_id');
                $builder->inWhere('j.staff_id', $staffArr);
            }
        }
        if (!empty($param['state'])) {
            $builder->andWhere('j.state = :state:', ['state' => $param['state']]);
        }
        if (!empty($param['after_date_start'])) {
            $builder->andWhere('j.after_date >= :after_date_start:', ['after_date_start' => $param['after_date_start']]);
        }
        if (!empty($param['after_date_end'])) {
            $builder->andWhere('j.after_date <= :after_date_end:', ['after_date_end' => $param['after_date_end']]);
        }
        if (!empty($param['hc_id'])) {
            $builder->andWhere('j.hc_id = :hc_id:', ['hc_id' => $param['hc_id']]);
        }
        if (!empty($param['approval_state'])) {
            $builder->andWhere('aa.state = :approval_state:', ['approval_state' => $param['approval_state']]);
        }
        if (!empty($param['ids'])) {
            $builder->inWhere('j.id', $param['ids']);
        }
        if (!empty($param['data_source'])) {
            $builder->andWhere('j.data_source = :data_source:', ['data_source' => $param['data_source']]);
        }
        if (!empty($param['approval_id'])) {
            $builder->andWhere('aa.approval_id = :approval_id:', ['approval_id' => $param['approval_id']]);
        }
        $count = $builder->columns('count(1) as cou')->getQuery()->getSingleResult()->cou;
        if ($count == 0) {
            return [[], 0];
        }
        $list = $builder->columns('j.id,
            j.serial_no,
            j.staff_id,
            j.hc_id,
            j.state,
            j.type,
            j.current_department_id,
            j.current_position_id,
            j.current_store_id,
            j.current_manager_id,
            j.current_indirect_manger_id,
            j.after_department_id,
            j.after_position_id,
            j.after_store_id,
            j.after_manager_id,
            j.after_date,
            j.submitter_id,
            j.approval_state,
            j.before_base_salary,
            j.before_exp_allowance,
            j.before_position_allowance,
            j.before_car_rental,
            j.before_trip_payment,
            j.before_notebook_rental,
            j.before_recommended,
            j.before_food_allowance,
            j.before_dangerous_area,
            j.before_house_rental,
            j.before_deminimis_benefits,
            j.before_performance_allowance,
            j.before_other_non_taxable_allowance,
            j.before_other_taxable_allowance,
            j.before_phone_subsidy,
            j.after_base_salary,
            j.after_exp_allowance,
            j.after_position_allowance,
            j.after_car_rental,
            j.after_trip_payment,
            j.after_notebook_rental,
            j.after_recommended,
            j.after_food_allowance,
            j.after_dangerous_area,
            j.after_house_rental,
            j.after_deminimis_benefits,
            j.after_performance_allowance,
            j.after_other_non_taxable_allowance,
            j.after_other_taxable_allowance,
            j.after_phone_subsidy,
            j.data_source,
            j.car_owner,
            j.reason,
            j.job_handover_staff_id,
            j.senior_auditor,
            j.rental_car_cteated_at,
            aa.audit_time,
            j.before_working_day_rest_type,
            j.after_working_day_rest_type
        ')->orderBy('aa.id desc')->limit($limit, $start)->getQuery()->execute()->toArray();
        return [$list, $count];
    }

    /**
     * 申请查询列表
     * @param $paramIn
     * @return array
     */
    public function getJobTransferApplyList($paramIn): array
    {
        //获取传入参数
        $param['name'] = $paramIn['name'] ?? '';
        $param['type'] = $paramIn['type'] ?? 0;
        $param['state'] = $paramIn['state'] ?? 0;
        $param['after_date_start'] = $paramIn['after_date_start'] ?? 0;
        $param['after_date_end'] = $paramIn['after_date_end'] ?? 0;
        $param['hc_id'] = $paramIn['hc_id'] ?? 0;
        $param['approval_state'] = $paramIn['approval_state'] ?? 0;
        $param['page_num'] = $paramIn['page_num'] ?? 1;
        $param['page_size'] = $paramIn['page_size'] ?? 20;
        $offset = $param['page_size'] * ($param['page_num'] - 1);
        $param['name'] = trim($param['name']);
        $param['data_source'] = $paramIn['data_source'] ?? '';
        $param['submitter_id'] = $paramIn['submitter_id'] ?? '';

        //查询列表
        [$items, $count] = $this->getBaseTransferData($param, $offset, $param['page_size']);

        //转义字段
        if (!empty($items) && is_array($items)) {

            //转义字段
            if (!empty($items) && is_array($items)) {
                $items = $this->translateColumn($items);
            }
        }

        return [
            "dataList" => $items ?? [],
            "count"    => intval($count),
        ];
    }

    /**
     * 获取审批列表
     * @param $paramIn
     * @param $currentStaff
     * @return array
     */
    public function getAuditList($paramIn, $currentStaff): array
    {
        //获取传入参数
        $param['name'] = $paramIn['name'] ?? '';
        $param['type'] = $paramIn['type'] ?? 0;
        $param['state'] = $paramIn['state'] ?? 0;
        $param['after_date_start'] = $paramIn['after_date_start'] ?? 0;
        $param['after_date_end'] = $paramIn['after_date_end'] ?? 0;
        $param['hc_id'] = $paramIn['hc_id'] ?? 0;
        $param['approval_state'] = $paramIn['approval_state'] ?? 0;
        $param['page_num'] = $paramIn['page_num'] ?? 1;
        $param['page_size'] = $paramIn['page_size'] ?? 20;
        $offset = $param['page_size'] * ($param['page_num'] - 1);
        $param['name'] = trim($param['name']);
        $param['data_source'] = $paramIn['data_source'] ?? '';
        $param['approval_id'] = $currentStaff ?? '';

        //查询列表
        [$items, $count] = $this->getBaseTransferDataEx($param, $offset, $param['page_size']);

        //转义字段
        if (!empty($items) && is_array($items)) {
            $items = $this->translateColumn($items);
        }

        return [
            "dataList" => $items ?? [],
            "count" => $count ?? 0
        ];
    }

    /**
     * 转义字段
     * @param $data
     * @return mixed
     */
    private function translateColumn($data)
    {
        //获取部门、职位、用户信息、网点信息
        [$departmentInfo, $positionInfo, $userInfo, $storeInfo, $hcInfo] =
            JobTransferService::getInstance()->genBaseData($data,Enums::FETCH_DATA_EXPORT);

        //审批状态
        $stateList = SysService::getInstance()->getAuditState();
        //转岗状态
        $transferStateList = SysService::getInstance()->getTransferState();
        //转岗类型
        $typeList = SysService::getInstance()->getTransferType();
        //获取数据源
        $sourceList = SysService::getInstance()->getTransferDataSource();

        foreach ($data as &$v) {
            $v['staff_name'] = $userInfo[$v['staff_id']]['name'] ?? '';
            $v['current_department_name'] = $departmentInfo[$v['current_department_id']]['name'] ?? '';
            $v['after_department_name'] = $departmentInfo[$v['after_department_id']]['name'] ?? '';
            $v['current_position_name'] = $positionInfo[$v['current_position_id']]['job_name'] ?? '';
            $v['after_position_name'] = $positionInfo[$v['after_position_id']]['job_name'] ?? '';
            $v['current_store_name'] = $storeInfo[$v['current_store_id']]['name'] ?? '';
            $v['after_store_name'] = $storeInfo[$v['after_store_id']]['name'] ?? '';
            $v['approval_state_title'] = $stateList[$v['approval_state']] ?? '';
            $v['state_title'] = $transferStateList[$v['state']] ?? '';
            $v['type_title'] = $typeList[$v['type']] ?? '';
            $v['data_source_title'] = $sourceList[$v['data_source']] ?? '';
            //更改转岗日期按钮是否显示
            $v['is_show_after_date_btn'] = $v['state'] == 1 && $v['approval_state'] == 2 ? 1: 0;
            //立即转岗按钮是否显示
            $v['is_show_do_transfer_btn'] = $v['state'] == 4 ? 1: 0;

            $v['audit_time'] = !empty($v['audit_time']) ? date('Y-m-d H:i:s', strtotime($v['audit_time']) + 3600 * get_sys_time_offset()) : '';

            $v['before_working_day_rest_type_text'] = !empty($v['before_working_day_rest_type']) ? self::$t->_('working_day_rest_type_'.$v['before_working_day_rest_type']) : '';
            $v['after_working_day_rest_type_text'] = !empty($v['after_working_day_rest_type']) ? self::$t->_('working_day_rest_type_'.$v['after_working_day_rest_type']) : '';
        }

        return $data;
    }

    /**
     * 申请查询列表
     * @param $paramIn
     * @param $currentStaff
     * @return array
     * @throws \Exception
     */
    public function getJobTransferApprovalListMY($paramIn, $currentStaff): array
    {
        $param['page_num']  = $paramIn['page_num'] ?? 1;
        $param['page_size'] = $paramIn['page_size'] ?? 100;
        $param['ids']       = $paramIn['ids'] ?? [];
        $offset             = $param['page_size'] * ($param['page_num'] - 1);
        if (empty($param['ids'])) {
            return [];
        }

        //查询列表
        [$items, $count] = $this->getBaseTransferData($param, $offset, $param['page_size']);

        //获取当前登录人信息
        //$currentStaff
        $currentStaffInfo = HrStaffInfoPositionService::getInstance()->getStaffPosition([$currentStaff]);

        //转义字段
        if (!empty($items) && is_array($items)) {
            $vehicleJobTitle = explode(',',(new SettingEnvModel())->getSetVal('job_title_vehicle_type'));

            //获取部门、职位、用户信息、网点信息
            [$departmentInfo, $positionInfo, $userInfo, $storeInfo, $hcInfo] =
                JobTransferService::getInstance()->genBaseData($items,Enums::FETCH_DATA_EXPORT);

            //审批状态
            $stateList = SysService::getInstance()->getAuditState();
            //转岗状态
            $transferStateList = SysService::getInstance()->getTransferState();
            //转岗类型
            $typeList = SysService::getInstance()->getTransferType();
            //获取数据源
            $sourceList = SysService::getInstance()->getTransferDataSource();
            //角色
            $rolesList = SysService::getInstance()->getAllRoleList();

            $after_department_ids = array_column($items, 'after_department_id');
            $after_job_title_ids = array_column($items, 'after_position_id');
            $working_day_rest_type_list = $this->getWorkingDayRestTypeList(['department_ids' => $after_department_ids, 'job_title_ids' => $after_job_title_ids]);
            $vehicle_source_config = SysService::getInstance()->getCarOwner();

            foreach ($items as &$v) {

                if (in_array(68, $currentStaffInfo[$currentStaff]['position_category'])) { //是hrBP
                    $v['step'] = 'approval_hrbp';
                } else if (isset($v['senior_auditor']) && $v['senior_auditor']) { //hrBP之后的审批人
                    $v['step'] = 'approval_senior';
                } else { //普通审批人
                    $v['step'] = 'approval_normal';
                }

                $v['staff_name'] = $userInfo[$v['staff_id']]['name'] ?? '';
                $v['current_department_name'] = $departmentInfo[$v['current_department_id']]['name'] ?? '';
                $v['after_department_name'] = $departmentInfo[$v['after_department_id']]['name'] ?? '';
                $v['current_position_name'] = $positionInfo[$v['current_position_id']]['job_name'] ?? '';
                $v['after_position_name'] = $positionInfo[$v['after_position_id']]['job_name'] ?? '';
                $v['current_store_name'] = $storeInfo[$v['current_store_id']]['name'] ?? '';
                $v['after_store_name'] = $storeInfo[$v['after_store_id']]['name'] ?? '';
                $v['approval_state_title'] = $stateList[$v['approval_state']] ?? '';
                $v['state_title'] = $transferStateList[$v['state']] ?? '';
                $v['type_title'] = $typeList[$v['type']] ?? '';
                $v['data_source_title'] = $sourceList[$v['data_source']] ?? '';
                //更改转岗日期按钮是否显示
                $v['is_show_after_date_btn'] = $v['state'] == 1 && $v['approval_state'] == 2 ? 1: 0;
                $v['car_owner'] = intval($v['car_owner']);
                $v['before_base_salary'] = ($v['before_base_salary'] ?? 0) / 100;
                $v['before_car_rental'] = ($v['before_car_rental'] ?? 0) / 100;
                $v['before_dangerous_area'] = ($v['before_dangerous_area'] ?? 0) / 100;
                $v['before_exp_allowance'] = ($v['before_exp_allowance'] ?? 0) / 100;
                $v['before_food_allowance'] = ($v['before_food_allowance'] ?? 0) / 100;
                $v['before_gasoline_allowance'] = ($v['before_gasoline_allowance'] ?? 0) / 100;
                $v['before_house_rental'] = ($v['before_house_rental'] ?? 0) / 100;
                $v['before_island_allowance'] = ($v['before_island_allowance'] ?? 0) / 100;
                $v['before_notebook_rental'] = ($v['before_notebook_rental'] ?? 0) / 100;
                $v['before_position_allowance'] = ($v['before_position_allowance'] ?? 0) / 100;
                $v['before_recommended'] = ($v['before_recommended'] ?? 0) / 100;
                $v['before_deminimis_benefits'] = ($v['before_deminimis_benefits'] ?? 0) / 100;
                $v['before_performance_allowance'] = ($v['before_performance_allowance'] ?? 0) / 100;
                $v['before_other_non_taxable_allowance'] = ($v['before_other_non_taxable_allowance'] ?? 0) / 100;
                $v['before_other_taxable_allowance'] = ($v['before_other_taxable_allowance'] ?? 0) / 100;
                $v['before_phone_subsidy'] = ($v['before_phone_subsidy'] ?? 0) / 100;
                $v['before_mobile_allowance'] = ($v['before_mobile_allowance'] ?? 0) / 100;
                $v['before_attendance_allowance'] = ($v['before_attendance_allowance'] ?? 0) / 100;
                $v['before_fuel_allowance'] = ($v['before_fuel_allowance'] ?? 0) / 100;
                $v['before_car_allowance'] = ($v['before_car_allowance'] ?? 0) / 100;
                $v['before_vehicle_allowance'] = ($v['before_vehicle_allowance'] ?? 0) / 100;
                $v['before_gdl_allowance'] = ($v['before_gdl_allowance'] ?? 0) / 100;
                $v['after_base_salary'] = ($v['after_base_salary'] ?? 0) / 100;
                $v['after_car_rental'] = ($v['after_car_rental'] ?? 0) / 100;
                $v['after_dangerous_area'] = ($v['after_dangerous_area'] ?? 0) / 100;
                $v['after_exp_allowance'] = ($v['after_exp_allowance'] ?? 0) / 100;
                $v['after_food_allowance'] = ($v['after_food_allowance'] ?? 0) / 100;
                $v['after_gasoline_allowance'] = ($v['after_gasoline_allowance'] ?? 0) / 100;
                $v['after_house_rental'] = ($v['after_house_rental'] ?? 0) / 100;
                $v['after_island_allowance'] = ($v['after_island_allowance'] ?? 0) / 100;
                $v['after_notebook_rental'] = ($v['after_notebook_rental'] ?? 0) / 100;
                $v['after_position_allowance'] = ($v['after_position_allowance'] ?? 0) / 100;
                $v['after_recommended'] = ($v['after_recommended'] ?? 0) / 100;
                $v['after_mobile_allowance'] = ($v['after_mobile_allowance'] ?? 0) / 100;
                $v['after_attendance_allowance'] = ($v['after_attendance_allowance'] ?? 0) / 100;
                $v['after_fuel_allowance'] = ($v['after_fuel_allowance'] ?? 0) / 100;
                $v['after_car_allowance'] = ($v['after_car_allowance'] ?? 0) / 100;
                $v['after_vehicle_allowance'] = ($v['after_vehicle_allowance'] ?? 0) / 100;
                $v['after_gdl_allowance'] = ($v['after_gdl_allowance'] ?? 0) / 100;
                $v['after_deminimis_benefits'] = ($v['after_deminimis_benefits'] ?? 0) / 100;
                $v['after_performance_allowance'] = ($v['after_performance_allowance'] ?? 0) / 100;
                $v['after_other_non_taxable_allowance'] = ($v['after_other_non_taxable_allowance'] ?? 0) / 100;
                $v['after_other_taxable_allowance'] = ($v['after_other_taxable_allowance'] ?? 0) / 100;
                $v['after_phone_subsidy'] = ($v['after_phone_subsidy'] ?? 0) / 100;
                //是否能选择车辆来源
                $v['is_select_vehicle_source'] = in_array($v['after_position_id'], $vehicleJobTitle) ? 1:0;

                //获取角色列表
                $v['after_role_ids_arr'] = DetailService::getInstance()->getCharacterList($v);
                $roleName = [];
                if (isset($v['after_role_ids']) && $v['after_role_ids']) {
                    $roleIds = explode(',', $v['after_role_ids']);
                    foreach ($roleIds as $role) {
                        $roleName[] = in_array(self::$language, ['zh-CN', 'zh']) ? ($rolesList[$role]['role_name_zh'] ?? "")
                            : (self::$language == 'en' ? $rolesList[$role]['role_name_en'] ?? "" : $rolesList[$role]['role_name_th'] ?? '');
                    }
                    $v['after_role_ids'] = $roleIds;
                }
                //获取转岗图片
                $v['upload_files'] = DetailService::getInstance()->getJobTransferImages($v['id']);
                $v['car_owner'] = $v['car_owner'] == 0 ? null :$v['car_owner'];
                $v['after_role_name'] = isset($roleName) && is_array($roleName) && $roleName ? implode(',', $roleName): "";
                $v['hire_date'] = $userInfo[$v['staff_id']]['hire_date'] ?? '';
                //转岗日期
                $v['after_date_begin'] = gmdate("Y-m-d", time() + get_sys_time_offset() * 3600 + 86400);
                $v['after_date_end'] = date("Y-m-d", strtotime($hcInfo[$v['hc_id']]['expirationdate']));
                $senior_auditor = explode(',',$v['senior_auditor']);
                if (in_array($currentStaff,$senior_auditor)){
                    $v['reject_salary_detail'] = $this->decodeRejectSalaryDetail($v['reject_salary_detail']);
                }else{
                    $v['reject_salary_detail'] = '';
                }

                $v['before_working_day_rest_type_text'] = !empty($v['before_working_day_rest_type']) ? self::$t->_('working_day_rest_type_'.$v['before_working_day_rest_type']) : '';
                $v['after_working_day_rest_type_text'] = !empty($v['after_working_day_rest_type']) ? self::$t->_('working_day_rest_type_'.$v['after_working_day_rest_type']) : '';
                $working_day_rest_type_list_key = $v['after_department_id'] . '_' . $v['after_position_id'];
                $v['working_day_rest_type_items'] = $working_day_rest_type_list[$working_day_rest_type_list_key] ?? [];
                //车辆来源
                $v['vehicle_source_list'] = $this->getStaffVehicleSourceList($vehicle_source_config,$v['after_position_id']);

            }
        }

        return [
            "dataList" => $items ?? [],
            "count" => $count ?? 0
        ];
    }
    /**
     * 申请查询列表
     * @param $paramIn
     * @param $currentStaff
     * @return array
     * @throws \Exception
     */
    public function getJobTransferApprovalList($paramIn, $currentStaff): array
    {
        $param['page_num']  = $paramIn['page_num'] ?? 1;
        $param['page_size'] = $paramIn['page_size'] ?? 100;
        $param['ids']       = $paramIn['ids'] ?? [];
        $offset             = $param['page_size'] * ($param['page_num'] - 1);
        if (empty($param['ids'])) {
            return [];
        }

        //查询列表
        [$items, $count] = $this->getBaseTransferData($param, $offset, $param['page_size']);

        //获取当前登录人信息
        //$currentStaff
        $currentStaffInfo = HrStaffInfoPositionService::getInstance()->getStaffPosition([$currentStaff]);

        //转义字段
        if (!empty($items) && is_array($items)) {
            $vehicleJobTitle = explode(',',(new SettingEnvModel())->getSetVal('job_title_vehicle_type'));

            //获取部门、职位、用户信息、网点信息
            [$departmentInfo, $positionInfo, $userInfo, $storeInfo, $hcInfo] =
                JobTransferService::getInstance()->genBaseData($items,Enums::FETCH_DATA_EXPORT);

            //审批状态
            $stateList = SysService::getInstance()->getAuditState();
            //转岗状态
            $transferStateList = SysService::getInstance()->getTransferState();
            //转岗类型
            $typeList = SysService::getInstance()->getTransferType();
            //获取数据源
            $sourceList = SysService::getInstance()->getTransferDataSource();
            //角色
            $rolesList = SysService::getInstance()->getAllRoleList();

            $after_department_ids = array_column($items, 'after_department_id');
            $after_job_title_ids = array_column($items, 'after_position_id');
            $working_day_rest_type_list = $this->getWorkingDayRestTypeList(['department_ids' => $after_department_ids, 'job_title_ids' => $after_job_title_ids]);
            $vehicle_source_config = SysService::getInstance()->getCarOwner();

            foreach ($items as &$v) {

                if (in_array(68, $currentStaffInfo[$currentStaff]['position_category'])) { //是hrBP
                    $v['step'] = 'approval_hrbp';
                } else if (isset($v['senior_auditor']) && $v['senior_auditor']) { //hrBP之后的审批人
                    $v['step'] = 'approval_senior';
                } else { //普通审批人
                    $v['step'] = 'approval_normal';
                }

                $v['staff_name'] = $userInfo[$v['staff_id']]['name'] ?? '';
                $v['current_department_name'] = $departmentInfo[$v['current_department_id']]['name'] ?? '';
                $v['after_department_name'] = $departmentInfo[$v['after_department_id']]['name'] ?? '';
                $v['current_position_name'] = $positionInfo[$v['current_position_id']]['job_name'] ?? '';
                $v['after_position_name'] = $positionInfo[$v['after_position_id']]['job_name'] ?? '';
                $v['current_store_name'] = $storeInfo[$v['current_store_id']]['name'] ?? '';
                $v['after_store_name'] = $storeInfo[$v['after_store_id']]['name'] ?? '';
                $v['approval_state_title'] = $stateList[$v['approval_state']] ?? '';
                $v['state_title'] = $transferStateList[$v['state']] ?? '';
                $v['type_title'] = $typeList[$v['type']] ?? '';
                $v['data_source_title'] = $sourceList[$v['data_source']] ?? '';
                //更改转岗日期按钮是否显示
                $v['is_show_after_date_btn'] = $v['state'] == 1 && $v['approval_state'] == 2 ? 1: 0;
                $v['car_owner'] = intval($v['car_owner']);
                $v['before_base_salary'] = ($v['before_base_salary'] ?? 0) / 100;
                $v['before_car_rental'] = ($v['before_car_rental'] ?? 0) / 100;
                $v['before_dangerous_area'] = ($v['before_dangerous_area'] ?? 0) / 100;
                $v['before_exp_allowance'] = ($v['before_exp_allowance'] ?? 0) / 100;
                $v['before_food_allowance'] = ($v['before_food_allowance'] ?? 0) / 100;
                $v['before_gasoline_allowance'] = ($v['before_gasoline_allowance'] ?? 0) / 100;
                $v['before_house_rental'] = ($v['before_house_rental'] ?? 0) / 100;
                $v['before_island_allowance'] = ($v['before_island_allowance'] ?? 0) / 100;
                $v['before_notebook_rental'] = ($v['before_notebook_rental'] ?? 0) / 100;
                $v['before_position_allowance'] = ($v['before_position_allowance'] ?? 0) / 100;
                $v['before_recommended'] = ($v['before_recommended'] ?? 0) / 100;
                $v['before_deminimis_benefits'] = ($v['before_deminimis_benefits'] ?? 0) / 100;
                $v['before_performance_allowance'] = ($v['before_performance_allowance'] ?? 0) / 100;
                $v['before_other_non_taxable_allowance'] = ($v['before_other_non_taxable_allowance'] ?? 0) / 100;
                $v['before_other_taxable_allowance'] = ($v['before_other_taxable_allowance'] ?? 0) / 100;
                $v['before_phone_subsidy'] = ($v['before_phone_subsidy'] ?? 0) / 100;
                $v['after_base_salary'] = ($v['after_base_salary'] ?? 0) / 100;
                $v['after_car_rental'] = ($v['after_car_rental'] ?? 0) / 100;
                $v['after_dangerous_area'] = ($v['after_dangerous_area'] ?? 0) / 100;
                $v['after_exp_allowance'] = ($v['after_exp_allowance'] ?? 0) / 100;
                $v['after_food_allowance'] = ($v['after_food_allowance'] ?? 0) / 100;
                $v['after_gasoline_allowance'] = ($v['after_gasoline_allowance'] ?? 0) / 100;
                $v['after_house_rental'] = ($v['after_house_rental'] ?? 0) / 100;
                $v['after_island_allowance'] = ($v['after_island_allowance'] ?? 0) / 100;
                $v['after_notebook_rental'] = ($v['after_notebook_rental'] ?? 0) / 100;
                $v['after_position_allowance'] = ($v['after_position_allowance'] ?? 0) / 100;
                $v['after_recommended'] = ($v['after_recommended'] ?? 0) / 100;
                $v['after_deminimis_benefits'] = ($v['after_deminimis_benefits'] ?? 0) / 100;
                $v['after_performance_allowance'] = ($v['after_performance_allowance'] ?? 0) / 100;
                $v['after_other_non_taxable_allowance'] = ($v['after_other_non_taxable_allowance'] ?? 0) / 100;
                $v['after_other_taxable_allowance'] = ($v['after_other_taxable_allowance'] ?? 0) / 100;
                $v['after_phone_subsidy'] = ($v['after_phone_subsidy'] ?? 0) / 100;
                //是否能选择车辆来源
                $v['is_select_vehicle_source'] = in_array($v['after_position_id'], $vehicleJobTitle) ? 1:0;
                //获取角色列表
                $v['after_role_ids_arr'] = DetailService::getInstance()->getCharacterList($v);
                $roleName = [];
                if (isset($v['after_role_ids'])) {
                    $roleIds = explode(',', $v['after_role_ids']);
                    foreach ($roleIds as $role) {
                        $roleName[] = in_array(self::$language, ['zh-CN', 'zh']) ? ($rolesList[$role]['role_name_zh'] ?? "")
                            : (self::$language == 'en' ? $rolesList[$role]['role_name_en'] ?? "" : $rolesList[$role]['role_name_th'] ?? '');
                    }
                    $v['after_role_ids'] = $roleIds;
                }
                //获取转岗图片
                $v['upload_files'] = DetailService::getInstance()->getJobTransferImages($v['id']);
                $v['car_owner'] = $v['car_owner'] == 0 ? null :$v['car_owner'];
                $v['after_role_name'] = isset($roleName) && is_array($roleName) && $roleName ? implode(',', $roleName): "";
                $v['hire_date'] = $userInfo[$v['staff_id']]['hire_date'] ?? '';
                //转岗日期
                $v['after_date_begin'] = gmdate("Y-m-d", time() + get_sys_time_offset() * 3600 + 86400);
                $v['after_date_end'] = date("Y-m-d", strtotime($hcInfo[$v['hc_id']]['expirationdate']));
                $senior_auditor = explode(',',$v['senior_auditor']);
                if (in_array($currentStaff,$senior_auditor)){
                    $v['reject_salary_detail'] = $this->decodeRejectSalaryDetail($v['reject_salary_detail']);
                }else{
                    $v['reject_salary_detail'] = '';
                }

                $v['car_rental_can_edit'] = 1; //车补薪资项是否可修改
                if(get_country_code() == 'TH' && $v['after_position_id'] == 110) {// TH && Van Courier
                    //van类型职位 租车津贴默认值规则 https://l8bx01gcjr.feishu.cn/docs/doccnAfsfoTur9xv3ABY19LcP2e
                    if ($v['step'] != 'approval_normal') {
                        $default_car_rental =  $this->getDefaultCarRental($v['after_store_id']);
                        if ($default_car_rental) { //van职位且有薪资默认值，不可编辑车补
                            $v['after_car_rental'] = $default_car_rental;
                            $v['after_car_rental_can_edit'] = 0;
                        }
                    }
                }

                $v['before_working_day_rest_type_text'] = !empty($v['before_working_day_rest_type']) ? self::$t->_('working_day_rest_type_'.$v['before_working_day_rest_type']) : '';
                $v['after_working_day_rest_type_text'] = !empty($v['after_working_day_rest_type']) ? self::$t->_('working_day_rest_type_'.$v['after_working_day_rest_type']) : '';
                $working_day_rest_type_list_key = $v['after_department_id'] . '_' . $v['after_position_id'];
                $v['working_day_rest_type_items'] = $working_day_rest_type_list[$working_day_rest_type_list_key] ?? [];
                //车辆来源
                $v['vehicle_source_list'] = $this->getStaffVehicleSourceList($vehicle_source_config,$v['after_position_id']);
            }
        }

        return [
            "dataList" => $items ?? [],
            "count" => $count ?? 0
        ];
    }

    /**
     * 根据职位获取车辆卡来源 转成数组
     * @param $vehicle_source_config
     * @param $job_title
     * @return array
     */
    protected function getStaffVehicleSourceList($vehicle_source_config, $job_title): array
    {
        $enums = $this->getStaffVehicleSourceEnums($vehicle_source_config, $job_title);
        $list = [];
        foreach ($enums as $key => $value) {
            $list[] = [
                'key'   => $key,
                'value' => $value
            ];
        }
        return $list;
    }

    /**
     * 车辆来源配置项
     * @param $vehicle_source_config
     * @param $job_title
     * @return array
     */
    protected function getStaffVehicleSourceEnums($vehicle_source_config, $job_title): array
    {
        //非泰国 默认返回全部
        if (get_country_code() != GlobalEnums::TH_COUNTRY_CODE) {
            return $vehicle_source_config;
        }

        if ($job_title != Enums::TH_JOB_TITLE_PICKUP_DRIVER) {
            return $vehicle_source_config;
        }
        return [Enums::CAR_OWNER_COMPANY_STATUS => self::$t->_('car_owner.2')];
    }


    /**
     * 根据部门职位获取工作天数&轮休规则
     * @param $params
     * @return array
     */
    public function getWorkingDayRestTypeList($params) {
        $department_ids = $params['department_ids'] ?? [];
        $job_title_ids = $params['job_title_ids'] ?? [];

        $working_day_rest_type_list = [];

        if(empty($department_ids) || empty($job_title_ids)) {
            return $working_day_rest_type_list;
        }

        $list = HrJobDepartmentRelationModel::find([
            'columns' => 'department_id, job_id, working_day_rest_type',
            'conditions' => ' department_id in ({department_ids:array}) and job_id in  ({job_ids:array})',
            'bind' => [
                'department_ids' => $department_ids,
                'job_ids' => $job_title_ids
            ]
        ])->toArray();
        if(!empty($list)) {
            foreach ($list as $key => $value) {
                $working_day_rest_type_str = $value['working_day_rest_type'];
                if(!empty($working_day_rest_type_str)) {
                    $working_day_rest_type_arr = explode(',', $working_day_rest_type_str);
                    foreach ($working_day_rest_type_arr as $k => $v) {
                        if ($v == Enums::WORKING_DAY_REST_TYPE_91) {
                            continue;
                        }
                        $working_day_rest_type_list[$value['department_id'] . '_' . $value['job_id']][] = [
                            'key' => $v,
                            'value' => static::$t->_('working_day_rest_type_'.$v)
                        ];
                    }
                }
            }
        }
        return $working_day_rest_type_list;
    }

    /**
     * 导出
     * @param array $paramIn
     * @param $user
     * @return array
     */
    public function exportMY($paramIn = [], $user): array
    {
        //获取传入参数
        $param['name'] = $paramIn['name'] ?? '';
        $param['type'] = $paramIn['type'] ?? 0;
        $param['state'] = $paramIn['state'] ?? 0;
        $param['after_date_start'] = $paramIn['after_date_start'] ?? 0;
        $param['after_date_end'] = $paramIn['after_date_end'] ?? 0;
        $param['hc_id'] = $paramIn['hc_id'] ?? 0;
        $param['approval_state'] = $paramIn['approval_state'] ?? 0;
        $param['page_num'] = $paramIn['page_num'] ?? 1;
        $param['page_size'] = $paramIn['page_size'] ?? 10000;
        $offset = $param['page_size'] * ($param['page_num'] - 1);
        $param['name'] = trim($param['name']);
        $param['data_source'] = $paramIn['data_source'] ?? '';
        $param['submitter_id'] = $paramIn['submitter_id'] ?? '';

        //查询列表
        [$items, $count]  = $this->getBaseTransferDataMY($param, $offset, $param['page_size']);

        return $this->exportDataMY($items, $user);
    }

    /**
     *
     * @param $items
     * @param $user
     * @return array
     */
    private function exportDataMY($items, $user)
    {
        $new_data = [];
        if (!empty($items) && is_array($items)) {

            //获取部门、职位、用户信息、网点信息
            [$departmentInfo, $positionInfo, $userInfo, $storeInfo, $hcInfo,$manager,$regions,$pieces] =
                JobTransferService::getInstance()->genBaseData($items,Enums::FETCH_DATA_EXPORT);

            //审批状态
            $stateList = SysService::getInstance()->getAuditState();
            //转岗状态
            $transferStateList = SysService::getInstance()->getTransferState();
            //转岗类型
            $typeList = SysService::getInstance()->getTransferType();
            //获取数据源
            $sourceList = SysService::getInstance()->getTransferDataSource();
            //车辆类型
            $carType = SysService::getInstance()->getJobTransferCarOwner();
            $carTypeName = array_column($carType, 'value', 'key');
            //查询失败原因
            $ids = implode(",", array_column($items, 'id'));
            $sql = "SELECT 
                        id,pid,failure_reason
                    FROM 
                        job_transfer_operate_log 
                    WHERE 
                        id in(SELECT max(id) AS id FROM  job_transfer_operate_log WHERE operate_id in (1,2) AND pid in ({$ids}) GROUP BY pid )";
            $log_list = $this->getDI()->get('db_backyard')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            $log_list = array_column($log_list, 'failure_reason', 'pid');

            foreach ($items as $k => $item) {
                //用户姓名
                $items[$k]['staff_name'] = $userInfo[$item['staff_id']]['name'] ?? '';
                //身份信息
                $items[$k]['staff_identity'] = $userInfo[$item['staff_id']]['identity'] ?? '';
                //手机号
                $items[$k]['staff_mobile'] = $userInfo[$item['staff_id']]['mobile'] ?? '';
                //部门
                $items[$k]['current_department_name'] = $departmentInfo[$item['current_department_id']]['name'] ?? '';
                $items[$k]['after_department_name'] = $departmentInfo[$item['after_department_id']]['name'] ?? '';
                //职位
                $items[$k]['current_position_name'] = $positionInfo[$item['current_position_id']]['job_name'] ?? '';
                $items[$k]['after_position_name'] = $positionInfo[$item['after_position_id']]['job_name'] ?? '';
                //网点
                $items[$k]['current_store_name'] = $storeInfo[$item['current_store_id']]['name'] ?? '';
                $items[$k]['after_store_name'] = $storeInfo[$item['after_store_id']]['name'] ?? '';
                //审批状态
                $items[$k]['approval_state_title'] = $stateList[$item['approval_state']] ?? '';
                //转岗状态
                $items[$k]['state_title'] = $transferStateList[$item['state']] ?? '';
                //转岗类型
                $items[$k]['type_title'] = $typeList[$item['type']] ?? '';
                //来源
                $items[$k]['data_source_title'] = $sourceList[$item['data_source']] ?? '';
                //直线上级
                $items[$k]['current_manager_name'] = $item['current_manager_id'] . ' ' . ($userInfo[$item['current_manager_id']]['name'] ?? '');
                //直线上级
                $items[$k]['after_manager_name'] = $item['after_manager_id'] . ' ' . ($userInfo[$item['after_manager_id']]['name'] ?? '');
                //车辆类型
                $items[$k]['car_owner_title'] = $carTypeName[$item['car_owner']] ?? '';
                //大区
                $items[$k]['current_regions_title'] = $regions[$storeInfo[$item['current_store_id']]['manage_region']] ?? '';
                $items[$k]['after_regions_title'] = $regions[$storeInfo[$item['after_store_id']]['manage_region']] ?? '';
                //片区
                $items[$k]['current_pieces_title'] = $pieces[$storeInfo[$item['current_store_id']]['manage_piece']] ?? '';
                $items[$k]['after_pieces_title'] = $pieces[$storeInfo[$item['after_store_id']]['manage_piece']] ?? '';
                //失败原因
                $failure_reason = !empty($log_list[$item['id']]) ? json_decode( $log_list[$item['id']],true) : [];
                $items[$k]['failure_reason'] = $failure_reason[static::$language] ?? '';

                $items[$k]['before_working_day_rest_type'] = !empty($item['before_working_day_rest_type']) ? static::$t->_('working_day_rest_type_'.$item['before_working_day_rest_type']) : '';
                $items[$k]['after_working_day_rest_type'] = !empty($item['after_working_day_rest_type']) ? static::$t->_('working_day_rest_type_'.$item['after_working_day_rest_type']) : '';
            }
        }

        $positions = HrStaffInfoPositionModel::find([
            'conditions' => 'staff_info_id = :staff_info_id:',
            'columns' => 'staff_info_id, position_category',
            'bind' => [
                'staff_info_id' => $user
            ]
        ])->toArray();
        $positionArr = array_column($positions, 'position_category');

        foreach ($items as $key => $val) {
            $new_data[$key][] = $val['staff_id'];
            $new_data[$key][] = $val['hc_id'];
            $new_data[$key][] = $val['staff_name'];
            $new_data[$key][] = $val['staff_identity'];
            $new_data[$key][] = $val['staff_mobile'];
            $new_data[$key][] = $val['serial_no'];
            $new_data[$key][] = $val['type_title'];
            $new_data[$key][] = $val['state_title'];
            $new_data[$key][] = $val['approval_state_title'];
            $new_data[$key][] = $val['data_source_title'];
            $new_data[$key][] = 'Flash express';
            $new_data[$key][] = $val['current_department_name'];
            $new_data[$key][] = $val['current_store_name'];
            $new_data[$key][] = $val['current_position_name'];
            $new_data[$key][] = $val['current_manager_name'];
            $new_data[$key][] = $val['before_working_day_rest_type'];//转岗前工作天数轮休规则
            $new_data[$key][] = 'Flash express';
            $new_data[$key][] = $val['after_department_name'];
            $new_data[$key][] = $val['after_store_name'];
            $new_data[$key][] = $val['after_position_name'];
            $new_data[$key][] = $val['after_manager_name'];
            $new_data[$key][] = $val['after_working_day_rest_type'];//转岗后工作天数轮休规则
            $new_data[$key][] = $val['after_date'];
            if (array_intersect([42, 14, 99], $positionArr)) { //Payroll、系统管理员、超级管理员角色
                $new_data[$key][] = $val['before_base_salary'] / 100; // 基本工资
                $new_data[$key][] = $val['before_notebook_rental'] / 100; // 电脑津贴
                $new_data[$key][] = $val['before_mobile_allowance'] / 100; // 手机津贴
                $new_data[$key][] = $val['before_attendance_allowance'] / 100; // 出勤津贴
                $new_data[$key][] = $val['before_fuel_allowance'] / 100; // 油补津贴
                $new_data[$key][] = $val['before_car_allowance'] / 100; // 私家车津贴
                $new_data[$key][] = $val['before_vehicle_allowance'] / 100; // 货车津贴
                $new_data[$key][] = $val['before_gdl_allowance'] / 100; // gdl
                $new_data[$key][] = $val['before_house_rental'] / 100; // 租房津贴
                $new_data[$key][] = $val['before_site_allowance'] / 100; // 区域津贴

                $new_data[$key][] = $val['after_base_salary'] / 100; // 基本工资
                $new_data[$key][] = $val['after_notebook_rental'] / 100; // 电脑津贴
                $new_data[$key][] = $val['after_mobile_allowance'] / 100; // 手机津贴
                $new_data[$key][] = $val['after_attendance_allowance'] / 100; // 出勤津贴
                $new_data[$key][] = $val['after_fuel_allowance'] / 100; // 油补津贴
                $new_data[$key][] = $val['after_car_allowance'] / 100; // 私家车津贴
                $new_data[$key][] = $val['after_vehicle_allowance'] / 100; // 货车津贴
                $new_data[$key][] = $val['after_gdl_allowance'] / 100; // gdl
                $new_data[$key][] = $val['after_house_rental'] / 100; // 租房津贴
                $new_data[$key][] = $val['after_site_allowance'] / 100; // 区域津贴

            }
            $new_data[$key][] = $val['car_owner_title'];
            $new_data[$key][] = $val['rental_car_cteated_at'];

            $new_data[$key][] = $val['current_regions_title'];
            $new_data[$key][] = $val['current_pieces_title'];
            $new_data[$key][] = $val['after_regions_title'];
            $new_data[$key][] = $val['after_pieces_title'];
            $new_data[$key][] = $val['failure_reason'];
        }
        $file_name = static::$t->_('Transfer_') . date('YmdHis');

        if (array_intersect([42, 14, 99], $positionArr)) {
            $header = [
                static::$t->_('job_transfer.36'),               //工号
                static::$t->_('HCId'),                          //HCId
                static::$t->_('job_transfer.38'),               //姓名
                static::$t->_('id_card_no'),                    //id_card_no
                static::$t->_('transfer_staff_phone'),          //transfer_staff_phone
                static::$t->_('job_transfer.35'),               //审批编号
                static::$t->_('job_transfer.33'),               //转岗类型
                static::$t->_('job_transfer.34'),               //转岗状态
                static::$t->_('job_transfer.32'),               //审批状态
                static::$t->_('job_transfer.39'),               //来源
                static::$t->_('job_transfer.10'),               //转岗前公司
                static::$t->_('job_transfer.11'),               //转岗前部门
                static::$t->_('job_transfer.14'),               //转岗前所属网点
                static::$t->_('job_transfer.13'),               //转岗前职位
                static::$t->_('job_transfer.12'),               //转岗前直线上级
                static::$t->_('before_working_day_rest_type'),  //转岗前工作天数轮休规则
                static::$t->_('job_transfer.20'),               //转岗后公司
                static::$t->_('job_transfer.22'),               //转岗后部门
                static::$t->_('job_transfer.30'),               //转岗后所属网点
                static::$t->_('job_transfer.28'),               //转岗后职位
                static::$t->_('job_transfer.26'),               //转岗后直线上级
                static::$t->_('after_working_day_rest_type'),   //转岗后工作天数轮休规则
                static::$t->_('job_transfer.15'),               //转岗日期

                static::$t->_('job_transfer.1'),                //转岗前基本工资
                static::$t->_('job_transfer.7'),                //转岗前电脑补贴
                static::$t->_('job_transfer.61'),                //转岗前手机津贴
                static::$t->_('job_transfer.62'),                //转岗前出勤津贴
                static::$t->_('job_transfer.63'),                //转岗前油补津贴
                static::$t->_('job_transfer.64'),                //转岗前私家车车补津贴
                static::$t->_('job_transfer.65'),                //转岗前货车车补津贴
                static::$t->_('job_transfer.66'),                //转岗前GDL补贴
                static::$t->_('job_transfer.6'),                //转岗前租房津贴
                static::$t->_('job_transfer.73'),                //转岗前区域津贴

                static::$t->_('job_transfer.18'),               //转岗后基本工资
                static::$t->_('job_transfer.27'),               //转岗后电脑补贴
                static::$t->_('job_transfer.67'),               //转岗后手机津贴
                static::$t->_('job_transfer.68'),               //转岗后出勤津贴
                static::$t->_('job_transfer.69'),               //转岗后油补津贴
                static::$t->_('job_transfer.70'),               //转岗后私家车车补津贴
                static::$t->_('job_transfer.71'),               //转岗后货车车补津贴
                static::$t->_('job_transfer.72'),               //转岗后gdl补贴
                static::$t->_('job_transfer.25'),               //转岗后租房津贴
                static::$t->_('job_transfer.74'),               //转岗后区域津贴

                static::$t->_('job_transfer.16'),               //车辆类型
                static::$t->_('job_transfer.17'),               //开始日期
                static::$t->_('job_transfer.40'),               //转岗前大区
                static::$t->_('job_transfer.41'),               //转岗前片区
                static::$t->_('job_transfer.42'),               //转岗后大区
                static::$t->_('job_transfer.43'),               //转岗后片区
                static::$t->_('job_transfer.44'),               //转岗失败原因
            ];
        } else {
            $header = [
                static::$t->_('job_transfer.36'),               //工号
                static::$t->_('HCId'),                          //HCId
                static::$t->_('job_transfer.38'),               //姓名
                static::$t->_('id_card_no'),                    //id_card_no
                static::$t->_('transfer_staff_phone'),          //transfer_staff_phone
                static::$t->_('job_transfer.35'),               //审批编号
                static::$t->_('job_transfer.33'),               //转岗类型
                static::$t->_('job_transfer.34'),               //转岗状态
                static::$t->_('job_transfer.32'),               //审批状态
                static::$t->_('job_transfer.39'),               //来源
                static::$t->_('job_transfer.10'),               //转岗前公司
                static::$t->_('job_transfer.11'),               //转岗前部门
                static::$t->_('job_transfer.14'),               //转岗前所属网点
                static::$t->_('job_transfer.13'),               //转岗前职位
                static::$t->_('job_transfer.12'),               //转岗前直线上级
                static::$t->_('before_working_day_rest_type'),  //转岗前工作天数轮休规则
                static::$t->_('job_transfer.20'),               //转岗后公司
                static::$t->_('job_transfer.22'),               //转岗后部门
                static::$t->_('job_transfer.30'),               //转岗后所属网点
                static::$t->_('job_transfer.28'),               //转岗后职位
                static::$t->_('job_transfer.26'),               //转岗后直线上级
                static::$t->_('after_working_day_rest_type'),   //转岗后工作天数轮休规则
                static::$t->_('job_transfer.15'),               //转岗日期
                static::$t->_('job_transfer.16'),               //车辆类型
                static::$t->_('job_transfer.17'),               //开始日期
                static::$t->_('job_transfer.40'),               //转岗前大区
                static::$t->_('job_transfer.41'),               //转岗前片区
                static::$t->_('job_transfer.42'),               //转岗后大区
                static::$t->_('job_transfer.43'),               //转岗后片区
                static::$t->_('job_transfer.44'),               //转岗失败原因
            ];
        }
        return $this->exportExcel($header, $new_data, $file_name);
    }

    /**
     * 导出
     * @param array $paramIn
     * @param $user
     * @return array
     */
    public function export($paramIn = [], $user): array
    {
        //获取传入参数
        $param['name'] = $paramIn['name'] ?? '';
        $param['type'] = $paramIn['type'] ?? 0;
        $param['state'] = $paramIn['state'] ?? 0;
        $param['after_date_start'] = $paramIn['after_date_start'] ?? 0;
        $param['after_date_end'] = $paramIn['after_date_end'] ?? 0;
        $param['hc_id'] = $paramIn['hc_id'] ?? 0;
        $param['approval_state'] = $paramIn['approval_state'] ?? 0;
        $param['page_num'] = $paramIn['page_num'] ?? 1;
        $param['page_size'] = $paramIn['page_size'] ?? 50000;
        $offset = $param['page_size'] * ($param['page_num'] - 1);
        $param['name'] = trim($param['name']);
        $param['data_source'] = $paramIn['data_source'] ?? '';
        $param['submitter_id'] = $paramIn['submitter_id'] ?? '';

        //查询列表
        [$items, $count]  = $this->getBaseTransferData($param, $offset, $param['page_size']);

        return $this->exportData($items, $user);
    }

    /**
     * 导出
     * @param array $paramIn
     * @param $user
     * @return array
     */
    public function exportAudit($paramIn = [], $user): array
    {
        //获取传入参数
        $param['name'] = $paramIn['name'] ?? '';
        $param['type'] = $paramIn['type'] ?? 0;
        $param['state'] = $paramIn['state'] ?? 0;
        $param['after_date_start'] = $paramIn['after_date_start'] ?? 0;
        $param['after_date_end'] = $paramIn['after_date_end'] ?? 0;
        $param['hc_id'] = $paramIn['hc_id'] ?? 0;
        $param['approval_state'] = $paramIn['approval_state'] ?? 0;
        $param['page_num'] = $paramIn['page_num'] ?? 1;
        $param['page_size'] = $paramIn['page_size'] ?? 10000;
        $offset = $param['page_size'] * ($param['page_num'] - 1);
        $param['name'] = trim($param['name']);
        $param['data_source'] = $paramIn['data_source'] ?? '';
        $param['approval_id'] = $user ?? '';

        //查询列表
        [$items, $count]  = $this->getBaseTransferDataEx($param, $offset, $param['page_size']);

        return $this->exportData($items, $user);
    }
    /**
     * 导出
     * @param array $paramIn
     * @param $user
     * @return array
     */
    public function exportAuditMY($paramIn = [], $user): array
    {
        //获取传入参数
        $param['name'] = $paramIn['name'] ?? '';
        $param['type'] = $paramIn['type'] ?? 0;
        $param['state'] = $paramIn['state'] ?? 0;
        $param['after_date_start'] = $paramIn['after_date_start'] ?? 0;
        $param['after_date_end'] = $paramIn['after_date_end'] ?? 0;
        $param['hc_id'] = $paramIn['hc_id'] ?? 0;
        $param['approval_state'] = $paramIn['approval_state'] ?? 0;
        $param['page_num'] = $paramIn['page_num'] ?? 1;
        $param['page_size'] = $paramIn['page_size'] ?? 10000;
        $offset = $param['page_size'] * ($param['page_num'] - 1);
        $param['name'] = trim($param['name']);
        $param['data_source'] = $paramIn['data_source'] ?? '';
        $param['approval_id'] = $user ?? '';

        //查询列表
        [$items, $count]  = $this->getBaseTransferDataExMY($param, $offset, $param['page_size']);

        return $this->exportDataMY($items, $user);
    }

    /**
     * 更新转岗HC | 更新转岗日期
     * @param array $paramIn
     * @param $user
     * @return array
     */
    public function update($paramIn = [], $user)
    {
        $params = $paramIn;
        $ac = new ApiClient('by', '', 'update_job_transfer', static::$language);

        $params = array_merge($params, [
            'staff_info_id' => $user,
            'type'  => isset($paramIn['after_date']) ? 3: 4
        ]);
        $ac->setParams([$params]);
        $res = $ac->execute();

        $code = $res['result']['code'] ?? "";
        $message = $res['result']['msg'] ?? "";
        $data = $res['data'] ?? [];
        return [
            'code' => $code,
            'message' => $message,
            'data' => $data,
        ];
    }

    /**
     * 检验HC是否有效
     * @param array $paramIn
     * @return bool
     */
    public function checkHcValidate($paramIn = []): bool
    {
        $jobTransferId = $paramIn['id'] ?? 0;
        if (empty($jobTransferId)) {
            return false;
        }

        $jobInfo = JobTransferModel::findFirst($jobTransferId);
        $hcInfo  = HrHcModel::findFirst([
            'conditions' => 'hc_id = :id:',
            'bind' => [
                'id' => $jobInfo->hc_id
            ]
        ]);

        //校验截止日期 & hc剩余人数
        if (isset($hcInfo->surplusnumber) && $hcInfo->surplusnumber > 0 &&
            $hcInfo->state_code == HrHcEnums::STATE_RECRUITING &&
            strtotime($hcInfo->expirationdate) >= strtotime(date('Y-m-d'))) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 立即转岗
     * @param array $paramIn
     * @param $staff_id
     * @return array|false
     */
    public function doTransfer($paramIn = [], $staff_id)
    {
        //禁止频繁使用立即转岗
        if($this->checkLock(RedisKey::JOB_TRANSFER_DO_TRANSFER_LOCK.$paramIn['id'])){
            return ['code' => ErrCode::$SYSTEM_ERROR,'message' => self::$t->_('job_transfer_err.8')];
        } else {
            $this->setLock(RedisKey::JOB_TRANSFER_DO_TRANSFER_LOCK.$paramIn['id'],1,3);
        }

        $jobTransferId = $paramIn['id'] ?? 0;
        $staffId = $staff_id;
        if (empty($jobTransferId)) {
            return false;
        }

        //立即转岗
        $rmq = new RocketMQ('do-job-transfer');
        $params = [
            'transfer_id' => $jobTransferId,
            'operator_id' => $staffId
        ];

        return $rmq->sendToMsg($params);
    }

    /**
     * 获取转岗日期区间
     * @param $params
     */
    public function getJobTransferDate($params): array
    {
        $job_transfer_id = $params['id'];
        if (empty($job_transfer_id)) {
            return [];
        }
        $jobTransferInfo = JobTransferModel::findFirst($job_transfer_id);
        if (empty($jobTransferInfo)) {
            return [];
        }

        $hcInfo = HrHcModel::findFirst([
            'conditions' => 'hc_id = :hc_id:',
            'columns' => 'expirationdate, hc_id',
            'bind' => [
                'hc_id' => $jobTransferInfo->hc_id
            ]
        ]);
        if (empty($hcInfo)) {
            return [];
        }

        //张帆需求
        //2-20号申请转岗假如可以选择日期范围[2-22 ~ 4-1]
        //选择2-24号转岗，当22号hrbp审批时，只能修改转岗日期 范围 [2-23 ~ 4-1]

        //开始时间 当前时间的后一天 与 转岗日期较大的
        //结束时间 该转岗关联的hc对应的过期时间
        $startTime = strtotime(gmdate("Y-m-d", time() + get_sys_time_offset() * 3600));
        $endTime   = strtotime($hcInfo->expirationdate);

        return [
            'start' => date("Y-m-d", $startTime),
            'end'   => date("Y-m-d", $endTime),
        ];
    }
    /**
     *
     * @param $items
     * @param $user
     * @return array
     */
    private function exportData($items, $user)
    {
        $new_data = [];
        if (!empty($items) && is_array($items)) {

            //获取部门、职位、用户信息、网点信息
            [$departmentInfo, $positionInfo, $userInfo, $storeInfo, $hcInfo,$manager,$regions,$pieces] =
                JobTransferService::getInstance()->genBaseData($items,Enums::FETCH_DATA_EXPORT);

            //审批状态
            $stateList = SysService::getInstance()->getAuditState();
            //转岗状态
            $transferStateList = SysService::getInstance()->getTransferState();
            //转岗类型
            $typeList = SysService::getInstance()->getTransferType();
            //获取数据源
            $sourceList = SysService::getInstance()->getTransferDataSource();
            //车辆类型
            $carType = SysService::getInstance()->getJobTransferCarOwner();
            $carTypeName = array_column($carType, 'value', 'key');
            //查询失败原因
            $ids = implode(",", array_column($items, 'id'));
            $sql = "SELECT 
                        id,pid,failure_reason
                    FROM 
                        job_transfer_operate_log 
                    WHERE 
                        id in(SELECT max(id) AS id FROM  job_transfer_operate_log WHERE operate_id in (1,2) AND pid in ({$ids}) GROUP BY pid )";
            $log_list = $this->getDI()->get('db_backyard')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            $log_list = array_column($log_list, 'failure_reason', 'pid');

            foreach ($items as $k => $item) {
                //用户姓名
                $items[$k]['staff_name'] = $userInfo[$item['staff_id']]['name'] ?? '';
                //身份信息
                $items[$k]['staff_identity'] = $userInfo[$item['staff_id']]['identity'] ?? '';
                //手机号
                $items[$k]['staff_mobile'] = $userInfo[$item['staff_id']]['mobile'] ?? '';
                //部门
                $items[$k]['current_department_name'] = $departmentInfo[$item['current_department_id']]['name'] ?? '';
                $items[$k]['after_department_name'] = $departmentInfo[$item['after_department_id']]['name'] ?? '';
                //职位
                $items[$k]['current_position_name'] = $positionInfo[$item['current_position_id']]['job_name'] ?? '';
                $items[$k]['after_position_name'] = $positionInfo[$item['after_position_id']]['job_name'] ?? '';
                //网点
                $items[$k]['current_store_name'] = $storeInfo[$item['current_store_id']]['name'] ?? '';
                $items[$k]['after_store_name'] = $storeInfo[$item['after_store_id']]['name'] ?? '';
                //审批状态
                $items[$k]['approval_state_title'] = $stateList[$item['approval_state']] ?? '';
                //转岗状态
                $items[$k]['state_title'] = $transferStateList[$item['state']] ?? '';
                //转岗类型
                $items[$k]['type_title'] = $typeList[$item['type']] ?? '';
                //来源
                $items[$k]['data_source_title'] = $sourceList[$item['data_source']] ?? '';
                //直线上级
                $items[$k]['current_manager_name'] = $item['current_manager_id'] . ' ' . ($userInfo[$item['current_manager_id']]['name'] ?? '');
                //直线上级
                $items[$k]['after_manager_name'] = $item['after_manager_id'] . ' ' . ($userInfo[$item['after_manager_id']]['name'] ?? '');
                //车辆类型
                $items[$k]['car_owner_title'] = $carTypeName[$item['car_owner']] ?? '';
                //大区
                $items[$k]['current_regions_title'] = $regions[$storeInfo[$item['current_store_id']]['manage_region']] ?? '';
                $items[$k]['after_regions_title'] = $regions[$storeInfo[$item['after_store_id']]['manage_region']] ?? '';
                //片区
                $items[$k]['current_pieces_title'] = $pieces[$storeInfo[$item['current_store_id']]['manage_piece']] ?? '';
                $items[$k]['after_pieces_title'] = $pieces[$storeInfo[$item['after_store_id']]['manage_piece']] ?? '';
                //失败原因
                $failure_reason = !empty($log_list[$item['id']]) ? json_decode( $log_list[$item['id']],true) : [];
                $items[$k]['failure_reason'] = $failure_reason[static::$language] ?? '';

                $items[$k]['before_working_day_rest_type'] = !empty($item['before_working_day_rest_type']) ? static::$t->_('working_day_rest_type_'.$item['before_working_day_rest_type']) : '';
                $items[$k]['after_working_day_rest_type'] = !empty($item['after_working_day_rest_type']) ? static::$t->_('working_day_rest_type_'.$item['after_working_day_rest_type']) : '';
            }
        }
        unset($log_list, $departmentInfo, $positionInfo, $userInfo, $storeInfo, $hcInfo, $manager, $regions, $pieces);

        $positions = HrStaffInfoPositionModel::find([
            'conditions' => 'staff_info_id = :staff_info_id:',
            'columns' => 'staff_info_id, position_category',
            'bind' => [
                'staff_info_id' => $user
            ]
        ])->toArray();
        $positionArr = array_column($positions, 'position_category');

        foreach ($items as $key => $val) {
            $new_data[$key][] = $val['staff_id'];
            $new_data[$key][] = $val['hc_id'];
            $new_data[$key][] = $val['staff_name'];
            $new_data[$key][] = $val['staff_identity'];
            $new_data[$key][] = $val['staff_mobile'];
            $new_data[$key][] = $val['serial_no'];
            $new_data[$key][] = $val['type_title'];
            $new_data[$key][] = $val['state_title'];
            $new_data[$key][] = $val['approval_state_title'];
            $new_data[$key][] = $val['data_source_title'];
            $new_data[$key][] = 'Flash express';
            $new_data[$key][] = $val['current_department_name'];
            $new_data[$key][] = $val['current_store_name'];
            $new_data[$key][] = $val['current_position_name'];
            $new_data[$key][] = $val['current_manager_name'];
            $new_data[$key][] = $val['before_working_day_rest_type'];//转岗前工作天数轮休规则
            $new_data[$key][] = 'Flash express';
            $new_data[$key][] = $val['after_department_name'];
            $new_data[$key][] = $val['after_store_name'];
            $new_data[$key][] = $val['after_position_name'];
            $new_data[$key][] = $val['after_manager_name'];
            $new_data[$key][] = $val['after_working_day_rest_type'];//转岗后工作天数轮休规则
            $new_data[$key][] = $val['after_date'];
            if (array_intersect([42, 14, 99], $positionArr)) { //Payroll、系统管理员、超级管理员角色
                $new_data[$key][] = $val['before_base_salary'] / 100;
                $new_data[$key][] = $val['before_exp_allowance'] / 100;
                $new_data[$key][] = $val['before_position_allowance'] / 100;
                $new_data[$key][] = $val['before_car_rental'] / 100;
                $new_data[$key][] = $val['before_trip_payment'] / 100;
                $new_data[$key][] = $val['before_notebook_rental'] / 100;
                $new_data[$key][] = $val['before_house_rental'] / 100;
                $new_data[$key][] = $val['before_food_allowance'] / 100;
                $new_data[$key][] = $val['before_dangerous_area'] / 100;
                $new_data[$key][] = $val['before_deminimis_benefits'] / 100;
                $new_data[$key][] = $val['before_performance_allowance'] / 100;
                $new_data[$key][] = $val['before_other_non_taxable_allowance'] / 100;
                $new_data[$key][] = $val['before_other_taxable_allowance'] / 100;
                $new_data[$key][] = $val['before_phone_subsidy'] / 100;
                $new_data[$key][] = $val['after_base_salary'] / 100;
                $new_data[$key][] = $val['after_exp_allowance'] / 100;
                $new_data[$key][] = $val['after_position_allowance'] / 100;
                $new_data[$key][] = $val['after_car_rental'] / 100;
                $new_data[$key][] = $val['after_trip_payment'] / 100;
                $new_data[$key][] = $val['after_notebook_rental'] / 100;
                $new_data[$key][] = $val['after_house_rental'] / 100;
                $new_data[$key][] = $val['after_food_allowance'] / 100;
                $new_data[$key][] = $val['after_dangerous_area'] / 100;
                $new_data[$key][] = $val['after_deminimis_benefits'] / 100;
                $new_data[$key][] = $val['after_performance_allowance'] / 100;
                $new_data[$key][] = $val['after_other_non_taxable_allowance'] / 100;
                $new_data[$key][] = $val['after_other_taxable_allowance'] / 100;
                $new_data[$key][] = $val['after_phone_subsidy'] / 100;
            }
            $new_data[$key][] = $val['car_owner_title'];
            $new_data[$key][] = $val['rental_car_cteated_at'];

            $new_data[$key][] = $val['current_regions_title'];
            $new_data[$key][] = $val['current_pieces_title'];
            $new_data[$key][] = $val['after_regions_title'];
            $new_data[$key][] = $val['after_pieces_title'];
            $new_data[$key][] = $val['failure_reason'];
        }
        $file_name = static::$t->_('Transfer_') . date('YmdHis');

        if (array_intersect([42, 14, 99], $positionArr)) {
            $header = [
                static::$t->_('job_transfer.36'),               //工号
                static::$t->_('HCId'),                          //HCId
                static::$t->_('job_transfer.38'),               //姓名
                static::$t->_('id_card_no'),                    //id_card_no
                static::$t->_('transfer_staff_phone'),          //transfer_staff_phone
                static::$t->_('job_transfer.35'),               //审批编号
                static::$t->_('job_transfer.33'),               //转岗类型
                static::$t->_('job_transfer.34'),               //转岗状态
                static::$t->_('job_transfer.32'),               //审批状态
                static::$t->_('job_transfer.39'),               //来源
                static::$t->_('job_transfer.10'),               //转岗前公司
                static::$t->_('job_transfer.11'),               //转岗前部门
                static::$t->_('job_transfer.14'),               //转岗前所属网点
                static::$t->_('job_transfer.13'),               //转岗前职位
                static::$t->_('job_transfer.12'),               //转岗前直线上级
                static::$t->_('before_working_day_rest_type'),  //转岗前工作天数轮休规则
                static::$t->_('job_transfer.20'),               //转岗后公司
                static::$t->_('job_transfer.22'),               //转岗后部门
                static::$t->_('job_transfer.30'),               //转岗后所属网点
                static::$t->_('job_transfer.28'),               //转岗后职位
                static::$t->_('job_transfer.26'),               //转岗后直线上级
                static::$t->_('after_working_day_rest_type'),   //转岗后工作天数轮休规则
                static::$t->_('job_transfer.15'),               //转岗日期
                static::$t->_('job_transfer.1'),                //转岗前基本工资
                static::$t->_('job_transfer.4'),                //转岗前经验津贴
                static::$t->_('job_transfer.8'),                //转岗前职位津贴
                static::$t->_('job_transfer.2'),                //转岗前租车津贴
                static::$t->_('job_transfer.9'),                //转岗前油补
                static::$t->_('job_transfer.7'),                //转岗前电脑补贴
                static::$t->_('job_transfer.6'),                //转岗前租房津贴
                static::$t->_('job_transfer.5'),                //转岗前餐补
                static::$t->_('job_transfer.3'),                //转岗前危险区域补贴
                static::$t->_('job_transfer.45'),               //转岗前无税金补贴
                static::$t->_('job_transfer.46'),               //转岗前绩效补贴
                static::$t->_('job_transfer.47'),               //转岗前其他无税金补贴
                static::$t->_('job_transfer.48'),               //转岗前其他纳税津贴
                static::$t->_('job_transfer.53'),               //转岗前通话补贴
                static::$t->_('job_transfer.18'),               //转岗后基本工资
                static::$t->_('job_transfer.23'),               //转岗后经验津贴
                static::$t->_('job_transfer.29'),               //转岗后职位津贴
                static::$t->_('job_transfer.19'),               //转岗后租车津贴
                static::$t->_('job_transfer.31'),               //转岗后油补
                static::$t->_('job_transfer.27'),               //转岗后电脑补贴
                static::$t->_('job_transfer.25'),               //转岗后租房津贴
                static::$t->_('job_transfer.24'),               //转岗后餐补
                static::$t->_('job_transfer.21'),               //转岗后危险区域补贴
                static::$t->_('job_transfer.49'),               //转岗后无税金补贴
                static::$t->_('job_transfer.50'),               //转岗后绩效补贴
                static::$t->_('job_transfer.51'),               //转岗后其他无税金补贴
                static::$t->_('job_transfer.52'),               //转岗后其他纳税津贴
                static::$t->_('job_transfer.54'),               //转岗后通化补贴
                static::$t->_('job_transfer.16'),               //车辆类型
                static::$t->_('job_transfer.17'),               //开始日期
                static::$t->_('job_transfer.40'),               //转岗前大区
                static::$t->_('job_transfer.41'),               //转岗前片区
                static::$t->_('job_transfer.42'),               //转岗后大区
                static::$t->_('job_transfer.43'),               //转岗后片区
                static::$t->_('job_transfer.44'),               //转岗失败原因
            ];
        } else {
            $header = [
                static::$t->_('job_transfer.36'),               //工号
                static::$t->_('HCId'),                          //HCId
                static::$t->_('job_transfer.38'),               //姓名
                static::$t->_('id_card_no'),                    //id_card_no
                static::$t->_('transfer_staff_phone'),          //transfer_staff_phone
                static::$t->_('job_transfer.35'),               //审批编号
                static::$t->_('job_transfer.33'),               //转岗类型
                static::$t->_('job_transfer.34'),               //转岗状态
                static::$t->_('job_transfer.32'),               //审批状态
                static::$t->_('job_transfer.39'),               //来源
                static::$t->_('job_transfer.10'),               //转岗前公司
                static::$t->_('job_transfer.11'),               //转岗前部门
                static::$t->_('job_transfer.14'),               //转岗前所属网点
                static::$t->_('job_transfer.13'),               //转岗前职位
                static::$t->_('job_transfer.12'),               //转岗前直线上级
                static::$t->_('before_working_day_rest_type'),  //转岗前工作天数轮休规则
                static::$t->_('job_transfer.20'),               //转岗后公司
                static::$t->_('job_transfer.22'),               //转岗后部门
                static::$t->_('job_transfer.30'),               //转岗后所属网点
                static::$t->_('job_transfer.28'),               //转岗后职位
                static::$t->_('job_transfer.26'),               //转岗后直线上级
                static::$t->_('after_working_day_rest_type'),   //转岗后工作天数轮休规则
                static::$t->_('job_transfer.15'),               //转岗日期
                static::$t->_('job_transfer.16'),               //车辆类型
                static::$t->_('job_transfer.17'),               //开始日期
                static::$t->_('job_transfer.40'),               //转岗前大区
                static::$t->_('job_transfer.41'),               //转岗前片区
                static::$t->_('job_transfer.42'),               //转岗后大区
                static::$t->_('job_transfer.43'),               //转岗后片区
                static::$t->_('job_transfer.44'),               //转岗失败原因
            ];
        }
        return $this->exportExcel($header, $new_data, $file_name);
    }

    /**
     * 获取批量添加进度
     * @param array $paramIn
     * @return array|false
     */
    public function getBatchAddProgress($paramIn = [])
    {
        $batchNumber = $paramIn['batch_number'] ?? "";

        $this->getDI()->get('logger')->info('getBatchAddProgress----参数：' . json_encode($batchNumber));
        $ac = new ApiClient('by', '', 'get_batch_add_progress', static::$language);
        $ac->setParams([
            [
                "batch_number" => $batchNumber
            ]
        ]);

        $res = $ac->execute();
        $this->getDI()->get('logger')->info('getBatchAddProgress----结果' . json_encode($res));

        return $res['result'] ?? [];
    }

    /**
     * 获取立即转岗极端
     * @param array $paramIn
     * @param $user
     */
    public function getDoTransferProgress($paramIn = [], $user)
    {
        $transferId = $paramIn['id'] ?? "";

        $this->getDI()->get('logger')->info('getDoTransferProgress----参数：' . json_encode($transferId));
        $ac = new ApiClient('by', '', 'get_transfer_process', static::$language);
        $ac->setParams([
            [
                'transfer_id' => $transferId,
                'operator_id' => $user
            ]
        ]);

        $res = $ac->execute();
        $this->getDI()->get('logger')->info('getDoTransferProgress----结果' . json_encode($res));

        return $res['result'] ?? [];
    }

    /**
     *
     * 获取van 职位 网点对应的租车津贴默认值
     *
     * 需求文档：https://l8bx01gcjr.feishu.cn/docs/doccnAfsfoTur9xv3ABY19LcP2e
     *
     * @param $after_store_id 转岗后网点
     *
     * @return int
     */
    private function getDefaultCarRental($after_store_id){

//        //step1:检查是否是特殊指定的网点，如果是是直接返回默认车补
//        $is_specail_store = HrStoreCarRentalModel::findFirst([
//            'conditions' => 'store_id = :store_id:',
//            'bind' => [ 'store_id' => $after_store_id ],
//        ]);
//        if($is_specail_store){
//            return 320;
//        }
        //step2:非特殊网点，则根据网点类型和分类来获取默认车补
        $category = HrStoreSubsidiesCategoryModel::findFirst([
            'conditions' => 'store_id = :store_id:',
            'bind' => [ 'store_id' => $after_store_id ],
        ]);
        if($category){
            return $category->money;
        }

        return 0;

    }
}
