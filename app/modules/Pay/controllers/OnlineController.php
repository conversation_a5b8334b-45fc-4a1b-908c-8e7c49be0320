<?php
namespace App\Modules\Pay\Controllers;

use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Modules\Pay\Services\BaseService;
use App\Modules\Pay\Services\FinalPayService;
use App\Library\Validation\ValidationException;
use App\Modules\Pay\Services\OnlineService;
use App\Util\RedisKey;

/**
 * 在线支付控制器层
 * Class OnlineController
 * @package App\Modules\Pay\Controllers
 */
class OnlineController extends BaseController
{
    /**
     * 在线支付-查询
     * @Permission(action='pay.online.list')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/70097
     */
    public function listAction()
    {
        $params = trim_array($this->request->get());
        $params['flag'] = GlobalEnums::PAYMENT_TAB_ONLINE;
        $res = FinalPayService::getInstance()->getList($this->user['id'], $params, false);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 获取数量
     * @Permission(action='pay.online.list')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/89207
     */
    public function getCountAction()
    {
        $count = OnlineService::getInstance()->getCount();
        return $this->returnJson(ErrCode::$SUCCESS, '', $count);
    }

    /**
     * 在线支付-导出
     * @Permission(action='pay.online.export')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/70102
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \Exception
     */
    public function exportAction()
    {
        $params = trim_array($this->request->get());
        $params['uid'] = $this->user['id'];
        $params['flag'] = GlobalEnums::PAYMENT_TAB_ONLINE;
        // 加锁
        $lock_key = md5(RedisKey::PAYMENT_ONLINE_PAY_LOCK . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            return FinalPayService::getInstance()->onlineExportTask($this->user['id'], $params, false);
        }, $lock_key, 5);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 在线支付-查看
     * @Permission(action='pay.online.detail')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/70107
     */
    public function detailAction()
    {
        $id = trim_array($this->request->get('id'));
        if (empty($id)) {
            throw new ValidationException('param id must have', ErrCode::$VALIDATE_ERROR);
        }
        $res = FinalPayService::getInstance()->getDetail($id, $this->user['id'], false);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 在线支付-撤回
     * @Permission(action='pay.online.cancel')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/70112
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function cancelAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, BaseService::$validate_withdraw);
        $params = array_only($params, array_keys(BaseService::$validate_withdraw));
        $res = FinalPayService::getInstance()->cancel($params, $this->user, false);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }
}
