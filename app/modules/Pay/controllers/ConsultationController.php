<?php
/**
 * Created by PhpStorm.
 * Date: 2021/4/27
 * Time: 19:31
 */

namespace App\Modules\Pay\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Modules\Pay\Services\BaseService;
use App\Modules\Pay\Services\PayFlowService;
use App\Modules\Pay\Services\PayService;
use App\Modules\User\Services\UserService;
use App\Modules\Workflow\Services\FYRService;
use App\Modules\Workflow\Services\WorkflowCommentService;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class ConsultationController extends BaseController
{
    /**
     * 待回复征询列表
     *
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/47869
     * @return Response|ResponseInterface
     */
    public function listAction()
    {
        $params = $this->request->get();
        $res = PayService::getInstance()->getList($this->user['id'], $params, BaseService::LIST_REPLY_TYPE);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson($res['code'], $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 回复详情
     *
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/47875
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function detailAction()
    {
        $id = $this->request->get('id');
        if (empty($id)) {
            throw new ValidationException('param id must have');
        }
        $res = PayService::getInstance()->getDetail($id, $this->user['id'], true);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson($res['code'], $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 回复征询
     *
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/47887
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function replyAction()
    {
        $ask_id = $this->request->get('ask_id', 'int');
        $note = $this->request->get('note', 'trim');

        $attachments = $this->request->get('attachments');

        // 执行参数校验
        $check_data = [
            'id' => $ask_id,
            'note' => $note,
            'attachments' => $attachments
        ];
        FYRService::getInstance()->replyValidation($check_data);

        $result = FYRService::getInstance()->createReply($ask_id, $note, $this->user, $attachments);
        return $this->returnJson($result['code'], $result['message']);
    }

    /**
     * 征询
     *
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/47881
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function askAction()
    {
        $biz_id = $this->request->get('id', 'int');
        $note = $this->request->get('note', 'trim');
        $to_staffs = $this->request->get('to_staff');
        $attachments = $this->request->get('attachments');

        // 执行参数校验
        $check_data = [
            'id' => $biz_id,
            'note' => $note,
            'to_staff' => $to_staffs,
            'attachments' => $attachments
        ];
        FYRService::getInstance()->askValidation($check_data);

        $request = (new PayFlowService())->getRequest($biz_id);
        $to_staffs = (new UserService())->getUserInfos($to_staffs);
        $result = FYRService::getInstance()->createAsk($request, $note, $to_staffs, $this->user, $attachments);

        return $this->returnJson($result['code'], $result['message']);
    }

    /**
     * 审批流节点添加评论
     *
     * @Token
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function submitCommentAction()
    {
        $params = $this->request->get();

        $validation = [
            'progress_mark_id' => 'Required|IntGe:1|>>>:params error[progress_mark_id]',
        ];
        WorkflowCommentService::getInstance()->submitValidation($params, $validation);

        $result = PayService::getInstance()->addWorkflowComment($params, $this->user);
        return $this->returnJson($result['code'], $result['message']);
    }
}