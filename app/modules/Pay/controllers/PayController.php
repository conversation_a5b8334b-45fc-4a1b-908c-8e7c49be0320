<?php
namespace App\Modules\Pay\Controllers;

use App\Library\Enums\PayEnums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Library\Exception\BusinessException;
use App\Modules\Pay\Services\BaseService;
use App\Modules\Pay\Services\PayService;
use App\Util\RedisKey;
use Exception;
use GuzzleHttp\Exception\GuzzleException;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;
use App\Library\Enums;

class PayController extends BaseController
{
    /**
     * 批量更新计划支付日期
     * @Permission(action='pay.pay.list')
     * @return Response|ResponseInterface
     * @throws ValidationException
     * @throws BusinessException
     */
    public function uploadBatchPlannedPayDateAction()
    {
        $excelFile = $this->request->getUploadedFiles();
        if (empty($excelFile[0])) {
            throw new BusinessException('Not found file');
        }
        $config      = ['path' => ''];
        $excel       = new \Vtiful\Kernel\Excel($config);
        $extension   = $excelFile[0]->getExtension();
        $tmpFilePath = $excelFile[0]->getTempName();
        if ($extension !== "xlsx") { //仅支持的文件格式xlsx
            throw new ValidationException($this->t->_('file_format_error'));
        }
        // 读取上传文件数据
        $excel_data = $excel->openFile($tmpFilePath)
            ->openSheet()->setType([1 => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP])
            ->getSheetData();
        $header     = array_shift($excel_data);

        $importData = [];
        foreach ($excel_data as $value) {
            if (!empty($value[0]) || !empty($value[1])) {
                $importData[] = $value;
            }
        }
        if (empty($importData)) {
            throw new ValidationException($this->t->_('pay_upload_cheque_file_error_03'));
        }
        //超过最大条数
        if ((count($importData)) > 5000) {
            throw new ValidationException($this->t->_('max_import_count_tip', [
                'max' => 5000,
            ]));
        }
        $userId   = $this->user['id'];
        $lock_key = md5('upload_batch_planned_pay_date' . $this->user['id']);
        $res      = $this->atomicLock(function () use ($header, $importData, $userId) {
            return PayService::getInstance()->uploadBatchPlannedPayDate($header, $importData, $userId);
        }, $lock_key, 1);

        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR,
            $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }
    /**
     * 我的支付-列表
     * @Permission(action='pay.pay.list')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/28580
     * @return Response|ResponseInterface
     */
    public function listAction()
    {
        $params = $this->request->get();
        $res = PayService::getInstance()->getList($this->user['id'], $params, BaseService::LIST_AUDIT_TYPE);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson($res['code'], $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 数据查询-列表
     * @Permission(action='pay.data.list')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/28615
     * @return Response|ResponseInterface
     */
    public function dataListAction()
    {
        $params = $this->request->get();
        $res = PayService::getInstance()->getList($this->user['id'], $params, BaseService::LIST_DATA_TYPE);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson($res['code'], $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 我的申请-列表
     * @Permission(action='pay.apply.list')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/47677
     * @return Response|ResponseInterface
     */
    public function applyListAction()
    {
        $params = $this->request->get();
        $res = PayService::getInstance()->getList($this->user['id'], $params, BaseService::LIST_APPLY_TYPE);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson($res['code'], $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 我的申请-查看
     * @Permission(action='pay.apply.view')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/47689
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function myDetailAction()
    {
        $id = $this->request->get('id');
        if (empty($id)) {
            throw new ValidationException('param id must have', ErrCode::$VALIDATE_ERROR);
        }
        $res = PayService::getInstance()->getMyDetail($id, $this->user['id'], false);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson($res['code'], $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 我的支付-查看
     * @Permission(action='pay.pay.view')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/28587
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function detailAction()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'id' => 'Required|IntGt:0|>>>:params error[id]',
        ]);

        $res = PayService::getInstance()->getDetail($params['id'], $this->user['id'], false);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 数据查询-查看
     * @Permission(action='pay.data.view')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/28622
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function dataDetailAction()
    {
        $id = $this->request->get('id');
        if (empty($id)) {
            throw new ValidationException('param id must have', ErrCode::$VALIDATE_ERROR);
        }
        $res = PayService::getInstance()->getDetail($id, $this->user['id'], true);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson($res['code'], $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 我的申请-撤回
     * @Permission(action='pay.apply.withdraw')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/47695
     * @return Response|ResponseInterface
     */
    public function withdrawAction()
    {
        try {
            $params = $this->request->get();
            Validation::validate($params, BaseService::$validate_withdraw);
            $params = array_only($params, array_keys(BaseService::$validate_withdraw));

            $res = PayService::getInstance()->withdraw($params['id'], $params['reason'], $this->user);
            if (!$res) {
                throw new Exception('return_error:' . json_encode($res), ErrCode::$SYSTEM_ERROR);
            }
            return $this->returnJson(ErrCode::$SUCCESS, '');
        } catch (ValidationException $e) {      //校验错误，可对外抛出
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (Exception $e) {               //系统错误，不可对外抛出
            $this->logger->warning('pay-withdraw-failed:' . $e->getMessage());
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $this->t->_('retry_later'));
        }
    }

    /**
     * 我的申请-获取支付信息
     * @Permission(action='pay.apply.updatepay')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/47707
     * @return Response|ResponseInterface
     */
    public function getPayAction()
    {
        try {
            $params = $this->request->get();
            Validation::validate($params, BaseService::$validate_get_pay);
            $params = array_only($params, array_keys(BaseService::$validate_get_pay));

            $res = PayService::getInstance()->getPay($params);

            return $this->returnJson(ErrCode::$SUCCESS, '', $res);
        } catch (ValidationException $e) {      //校验错误，可对外抛出
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (Exception $e) {               //系统错误，不可对外抛出
            $this->logger->warning('pay-get-pay-failed:' . $e->getMessage());
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $this->t->_('retry_later'));
        }
    }

    /**
     * 我的申请-更新支付信息
     * @Permission(action='pay.apply.updatepay')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/47701
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function updatePayAction()
    {
        $params = $this->request->get();
        Validation::validate($params, BaseService::$validate_update_pay);
        $params = array_only($params, array_keys(BaseService::$validate_update_pay));

        $lock_key = md5('pay_apply_update_payinfo_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            return PayService::getInstance()->updatePayData($params, $this->user);
        }, $lock_key, 20);

        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing']);
    }

    /**
     * 我的支付-提交
     * @Permission(action='pay.pay.audit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/28594
     * @return Response|ResponseInterface
     * @throws ValidationException
     * @throws BusinessException
     */
    public function submitAction()
    {
        $params = $this->request->get();
        $nullPayCheck = BaseService::$validate_check_submit_payment_null_pay_check;
        $payCheck = BaseService::$validate_check_submit_payment_pay_check;
        if (get_country_code() == 'MY') {
            $nullPayCheck['planned_pay_date']  = $payCheck['planned_pay_date'] = 'IfIntEq:is_pay,1|Required';
            //是否支付为是并且支付方式为银行转账并且支付银行不等于flash pay
            $nullPayCheck['bank_batch_number'] = $payCheck['bank_batch_number'] = 'IfIntEq:is_pay,1|IfIntEq:pay_method,2|IfStrNe:pay_bank_name,Flash Pay|Required|StrLenGeLe:1,50';
            $nullPayCheck['bank_pay_type']     = $payCheck['bank_pay_type'] = 'IfIntEq:is_pay,1|IfIntEq:pay_method,2|IfStrNe:pay_bank_name,Flash Pay|Required|Required|IntIn:'.implode(',',array_keys(PayService::getInstance()->getBankPaymentTypeMap()));
        }
        Validation::validate($params, $nullPayCheck);
        //兼容前端不区分支付方式胡乱传递参数
        if ($params['pay_method'] == Enums::PAYMENT_METHOD_CHECK && $params['is_pay'] == PayEnums::IS_PAY_YES) {
            Validation::validate($params, $payCheck);
        }
        $params = array_only($params, array_keys($payCheck));
        if (get_country_code() == 'MY') {
            //兼容审批级别大于以一级,日期单独校验
            if (!empty($params['planned_pay_date'])) {
                Validation::validate($params, ['planned_pay_date' => 'Date']);
            }
            //防止前置条件没有符合传递参数
            if (!empty($params['bank_batch_number'])) {
                Validation::validate($params, ['bank_batch_number' => 'StrLenGeLe:1,50']);
            }
            if (!empty($params['bank_pay_type']) && !in_array($params['bank_pay_type'],
                    array_keys(PayService::getInstance()->getBankPaymentTypeMap()))) {
                throw new ValidationException('param bank payment type error');
            }
        }
        //获取审批级别
        $level = PayService::getInstance()->checkLevel([$params['id']], $this->user['id']);
        $res = PayService::getInstance()->audit($params, $this->user, $level);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson($res['code'], $this->t->_('pay_submit_ok'), $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 我的支付-拒绝，暂时只有第二级有拒绝，所以只要拒绝就回到第一级 驳回
     * @Permission(action='pay.pay.audit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/28601
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function rejectAction()
    {
        $params = $this->request->get();
        $valiations = [
            'id' => 'Required|IntGt:0',
            'reject_reason' => 'Required|StrLenGeLe:1,1000',
        ];
        Validation::validate($params, $valiations);

        $params = array_only($params, array_keys($valiations));
        $res = PayService::getInstance()->reject($params, $this->user);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson($res['code'], $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 我的支付-导出
     * @Permission(action='pay.pay.export')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/28608
     * @return Response|ResponseInterface
     * @throws Exception
     */
    public function exportAction()
    {
        $params = $this->request->get();
        $params['uid'] = $this->user['id'];

        // 加锁处理
        $lock_key = md5('pay_pay_export_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            return PayService::getInstance()->getExportList($params, BaseService::LIST_AUDIT_TYPE);
        }, $lock_key, 30);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson($res['code'], $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 数据查询导出
     * @Permission(action='pay.data.export')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/28629
     * @return Response|ResponseInterface
     * @throws Exception
     */
    public function dataExportAction()
    {
        $params = $this->request->get();
        // 加锁处理
        $lock_key = md5('pay_data_export_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            return PayService::getInstance()->getExportList($params, BaseService::LIST_DATA_TYPE);
        }, $lock_key, 30);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson($res['code'], $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 支付模块对应付款银行列表
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/28783
     * @return Response|ResponseInterface
     */
    public function getBankListAction()
    {
        $res = PayService::getInstance()->getBankList();
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson($res['code'], $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 我的支付-批量审批
     * @Permission(action='pay.pay.auditBatch')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/47905
     * @return Response|ResponseInterface
     */
    public function submitBatchAction()
    {
        try {
            $params = $this->request->get();
            Validation::validate($params, BaseService::$base_validations);
            //判断审批层级
            $level = PayService::getInstance()->checkLevel($params['id_batch'], $this->user['id']);
            $firstValidate = BaseService::$validate_batch_submit_first;
            if (get_country_code() == 'MY') {
                //是否支付为是并且支付方式为银行转账并且支付银行不等于flash pay
                $firstValidate['bank_batch_number'] = 'IfIntEq:is_pay,1|IfIntEq:pay_method,2|IfStrNe:pay_bank_name,Flash Pay|Required|StrLenGeLe:1,50';
                $firstValidate['bank_pay_type']     = 'IfIntEq:is_pay,1|IfIntEq:pay_method,2|IfStrNe:pay_bank_name,Flash Pay|Required|IntIn:'.implode(',',array_keys(PayService::getInstance()->getBankPaymentTypeMap()));
            }
            if ($level == PayEnums::PAYMENT_ONE_LEVEL_AUDIT) {
                Validation::validate($params,$firstValidate);
            } else if ($level == PayEnums::PAYMENT_TWO_LEVEL_AUDIT) {
                Validation::validate($params, BaseService::$validate_batch_submit_second);
                //二级审批不能否
                $params['is_pay'] = PayEnums::IS_PAY_YES;
            } else if ($level == PayEnums::PAYMENT_THREE_LEVEL_AUDIT) {
                Validation::validate($params, BaseService::$validate_batch_submit_third);
            }
            if (!empty($params['pay_check'])) {
                Validation::validate($params, BaseService::$validate_batch_submit_pay_check);
            }
            $params = array_only($params, array_keys($firstValidate));
            if (get_country_code() == 'MY') {
                //防止前置条件没有符合传递参数
                if (!empty($params['bank_batch_number'])) {
                    Validation::validate($params, ['bank_batch_number' => 'StrLenGeLe:1,50']);
                }
                if (!empty($params['bank_pay_type']) && !in_array($params['bank_pay_type'],array_keys(PayService::getInstance()->getBankPaymentTypeMap()))) {
                    throw new ValidationException('param bank payment type error');
                }
            }
            $lock_key = md5('batch_audit' . '_' . $this->user['id']);
            $res = $this->atomicLock(function () use ($params, $level) {
                return PayService::getInstance()->batchAudit($params, $this->user, $level);
            }, $lock_key, 30);
            if (!$res) {
                //false 加锁失败
                $this->logger->info('pay-batch-audit-failed: lock be defeated result=' . $res);
                return $this->returnJson(ErrCode::$FREQUENT_VISIT_ERROR, $this->t->_('retry_later'));
            }
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', []);
        } catch (ValidationException $e) {      //校验错误，可对外抛出
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage(), []);
        } catch (Exception $e) {               //系统错误，不可对外抛出
            $this->logger->warning('pay-batch-audit-failed:' . $e->getMessage());
            return $this->returnJson($e->getCode() ?? ErrCode::$SYSTEM_ERROR, $e->getMessage() ?? $this->t->_('retry_later'));
        }
    }

    /**
     * 我的支付-批量驳回
     * @Permission(action='pay.pay.auditBatch')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/47911
     * @return Response|ResponseInterface
     */
    public function rejectBatchAction()
    {
        try {
            $params = $this->request->get();
            Validation::validate($params, BaseService::$validate_batch_reject);
            $params = array_only($params, array_keys(BaseService::$validate_batch_reject));

            $lock_key = md5('batch_audit' . '_' . $this->user['id']);
            $res = $this->atomicLock(function () use ($params) {
                return PayService::getInstance()->batchReject($params, $this->user);
            }, $lock_key, 30);
            if (!$res) {
                throw new Exception('return_error:' . json_encode($res), ErrCode::$SYSTEM_ERROR);
            }
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', []);
        } catch (ValidationException $e) {      //校验错误，可对外抛出
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage(), []);
        } catch (Exception $e) {               //系统错误，不可对外抛出
            $this->logger->warning('pay-batch-reject-failed:' . $e->getMessage());
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $this->t->_('retry_later'));
        }
    }

    /**
     * 支付管理-获取枚举
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/47725
     * @return Response|ResponseInterface
     */
    public function getEnumsAction()
    {
        $data = PayService::getInstance()->getEnums();
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }

    /**
     * 查询当前用户审批级别 - 16325需求梳理时发现没有地方用到
     * @Token
     */
    public function getMyAuditLevelAction()
    {
        try{
            $params = $this->request->get();
            Validation::validate($params,BaseService::$validate_get_pay);
            $params = array_only($params,array_keys(BaseService::$validate_get_pay));

            $res = PayService::getInstance()->getPay($params);

            return $this->returnJson(ErrCode::$SUCCESS, '',$res);
        } catch (ValidationException $e) {      //校验错误，可对外抛出
            return $this->returnJson(ErrCode::$VALIDATE_ERROR,$e->getMessage());
        } catch (Exception $e) {               //系统错误，不可对外抛出
            $logger = $this->getDI()->get('logger');
            $logger->warning('pay-get-pay-failed:' . $e->getMessage());
            return $this->returnJson(ErrCode::$SYSTEM_ERROR,$this->t->_('retry_later'));
        }
    }


    /**
     * 批量支付-使用支票支付-校验
     *
     * @Token
     * @Date: 1/4/23 3:28 PM
     * @return Response|ResponseInterface
     */
    public function checkBatchPaymentAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, BaseService::$validate_check_batch_payment);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = PayService::getInstance()->checkBatchPayment($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 数据查询-补充附件
     * @Permission(action='pay.data.add_attachment')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/79447
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function addAttachmentAction()
    {
        $data = $this->request->get();
        Validation::validate($data, BaseService::$validate_attachment);

        $lock_key = md5(RedisKey::PAYMENT_ADD_SUPPLEMENT . $data['id']);
        $res = $this->atomicLock(function () use ($data) {
            return PayService::getInstance()->addAttachment($data);
        }, $lock_key, 5);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 支付模块对应付款银行账号列表
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/85049
     * @return Response|ResponseInterface
     */
    public function getBankAccountListAction()
    {
        $res = PayService::getInstance()->getBankList();
        $data = [];
        foreach ($res['data'] as $bank) {
            foreach ($bank['child'] as $account) {
                $data[] = $account;
            }
        }
        return $this->returnJson($res['code'], $res['message'], $data);
    }

    /**
     * 我的支付-下载导入支票的模板
     * @Permission(action='pay.pay.audit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/86522
     * @return Response|ResponseInterface
     * @throws BusinessException
     * @throws GuzzleException
     */
    public function downloadChequeTemplateAction()
    {
        $res = PayService::getInstance()->generateChequeUploadTemplate($this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 我的支付-导入支票
     * @Permission(action='pay.pay.audit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/86525
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function uploadChequeFileAction()
    {
        $params = $this->request->get();
        $valiations = [
            'order_currency' => 'Required|Int|>>>:params error[order_currency]',
        ];
        Validation::validate($params, $valiations);

        if (!$this->request->hasFiles()) {
            throw new ValidationException($this->t['pay_upload_cheque_file_error_01'], ErrCode::$VALIDATE_ERROR);
        }

        $file = $this->request->getUploadedFiles()[0];
        $extension = $file->getExtension();
        if (!in_array($extension, ['xlsx'])) {
            throw new ValidationException($this->t['pay_upload_cheque_file_error_02'], ErrCode::$VALIDATE_ERROR);
        }

        $config = ['path' => ''];
        $excel = new \Vtiful\Kernel\Excel($config);

        // 读取文件内容
        $excel_data = $excel->openFile($file->getTempName())
            ->openSheet()
//            ->setSkipRows(1)
            ->setType([
                0 => \Vtiful\Kernel\Excel::TYPE_STRING,// 支票号, 可能会有前导0
                2 => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP,
                3 => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP,
            ])
            ->getSheetData();
        if (empty($excel_data) || count($excel_data) == 1) {
            throw new ValidationException($this->t['pay_upload_cheque_file_error_03'], ErrCode::$VALIDATE_ERROR);
        }

        $lock_key = md5('pay_upload_cheque_file_' . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params, $excel_data) {
            return PayService::getInstance()->batchUploadChequeFile($params['order_currency'], $excel_data, $this->user);;
        }, $lock_key, 20);
        if (empty($res)) {
            $code = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
        } else {
            $code = ErrCode::$SUCCESS;
            $message = $this->t['success'];
        }

        return $this->returnJson($code, $message, $res);
    }
}
