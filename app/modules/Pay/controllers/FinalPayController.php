<?php
namespace App\Modules\Pay\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Pay\Services\BaseService;
use App\Modules\Pay\Services\FinalPayService;
use App\Library\Enums;
use App\Util\RedisKey;
use Phalcon\Http\ResponseInterface;

class FinalPayController extends BaseController
{
    /**
     * 最终支付人-列表
     * @Permission(action='pay.finalpay.list')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/54085
     * @return Response|ResponseInterface
     */
    public function listAction()
    {
        $params = $this->request->get();
        $res = FinalPayService::getInstance()->getList($this->user['id'], $params);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson($res['code'], $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 我的支付-查看
     * @Permission(action='pay.finalpay.view')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/54091
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function detailAction()
    {
        $params = $this->request->get();
        Validation::validate($params, ['id' => 'Required|IntGt:0|>>>:params error [id]']);
        $res = FinalPayService::getInstance()->getDetail($params['id'], $this->user['id']);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 我的支付-提交
     * @Permission(action='pay.finalpay.submit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/54103
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function submitAction()
    {
        $params = $this->request->get();
        $valiations = [
            'id' => 'Required|IntGt:0',
            'is_pay' => 'Required|IntIn:1,2',
            'pay_remark' => 'Str',
            'attachments' => 'Arr',
            'pay_check'   => 'Arr'
        ];
        if ($params['is_pay'] == Enums\PayEnums::IS_PAY_YES) {
            $valiations['pay_method'] = 'Required|IntIn:1,2,3';
            $valiations['pay_bank_flow_date'] = 'IfIntEq:pay_method,2|Required|Str';
        } else if ($params['is_pay'] == Enums\PayEnums::IS_PAY_NO) {
            $valiations['not_pay_reason'] = 'Required|StrLenGeLe:1,1000';
            $valiations['pay_method'] = 'Required|IntIn:1,2,3';
        }
        Validation::validate($params, $valiations);
        $paramsFilter = array_only($params, array_keys($valiations));
        $paramsFilter['not_pay_reason_category'] = $params['not_pay_reason_category'] ?? null;
        $res = FinalPayService::getInstance()->submit($paramsFilter, $this->user, true);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson($res['code'], $this->t->_('pay_submit_ok'), $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 我的支付-合并支付
     * @Permission(action='pay.finalpay.submitBatch')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/65897
     * @return Response|ResponseInterface
     */
    public function submitBatchAction()
    {
        try {
            $params = $this->request->get();
            Validation::validate($params, BaseService::$validate_union_batch_submit);

            $lock_key = md5('final_pay_submit_batch_' . '_' . $this->user['id']);
            $res      = $this->atomicLock(function () use ($params) {
                return FinalPayService::getInstance()->submitBatch($params, $this->user);

            }, $lock_key, 30);
            if (!$res) {
                $this->logger->info('final_pay_submit_batch_: lock be defeated result=' . $res);
                return $this->returnJson(ErrCode::$FREQUENT_VISIT_ERROR, $this->t->_('retry_later'));
            }
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', []);
        } catch (ValidationException $e) {      //校验错误，可对外抛出
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage(), []);
        } catch (\Exception $e) {               //系统错误，不可对外抛出
            $this->logger->warning('final_pay_submit_batch_:' . $e->getMessage());
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $this->t->_('retry_later'));
        }
    }

    /**
     * 我的支付-导出
     * @Permission(action='pay.finalpay.export')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/54097
     * @return Response|ResponseInterface
     */
    public function exportAction()
    {
        $params = $this->request->get();
        $params['uid'] = $this->user['id'];
        // 加锁
        $lock_key = md5('finalpay_export_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            return FinalPayService::getInstance()->getExportList($this->user['id'], $params);
        }, $lock_key, 30);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson($res['code'], $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 我的支付-银行支付-在线支付-查询
     * @Permission(action='pay.finalpay.online.list')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/70077
     * @return Response|ResponseInterface
     */
    public function onlineListAction()
    {
        $params = trim_array($this->request->get());
        $params['flag'] = Enums\GlobalEnums::PAYMENT_TAB_ONLINE;
        $res = FinalPayService::getInstance()->getList($this->user['id'], $params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 我的支付-银行支付-在线支付-导出
     * @Permission(action='pay.finalpay.online.export')
     * @return Response|ResponseInterface
     * @api https://yapi.flashexpress.pub/project/133/interface/api/70087
     * @throws \Exception
     */
    public function onlineExportAction()
    {
        $params = trim_array($this->request->get());
        $params['uid'] = $this->user['id'];
        $params['flag'] = Enums\GlobalEnums::PAYMENT_TAB_ONLINE;
        // 加锁
        $lock_key = md5(RedisKey::PAYMENT_ONLINE_PAY_LOCK . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            return FinalPayService::getInstance()->onlineExportTask($this->user['id'], $params);
        }, $lock_key, 5);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 我的支付-银行支付-在线支付-查看
     * @Permission(action='pay.finalpay.online.detail')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/70082
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function onlineDetailAction()
    {
        $id = trim_array($this->request->get('id'));
        if (empty($id)) {
            throw new ValidationException('param id must have', ErrCode::$VALIDATE_ERROR);
        }
        $res = FinalPayService::getInstance()->getDetail($id, $this->user['id']);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 我的支付-银行支付-在线支付-撤回
     * @Permission(action='pay.finalpay.online.cancel')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/70117
     * @return \Phalcon\Http\Response|ResponseInterface
     * @throws ValidationException
     */
    public function onlineCancelAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, BaseService::$validate_withdraw);
        $params = array_only($params, array_keys(BaseService::$validate_withdraw));
        $res = FinalPayService::getInstance()->cancel($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }
}
