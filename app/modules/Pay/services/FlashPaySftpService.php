<?php

namespace App\Modules\Pay\Services;

use App\Library\BaseService;
use App\Library\Enums\PayEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\FlashPayHelper;
use App\Library\Logger;
use App\Models\oa\PaymentFlashPayConfigModel;
use App\Models\oa\PaymentOnlinePayModel;
use App\Modules\Pay\Models\Payment;
use Exception;
use Phalcon\Di;

/**
 * FlashPay SFTP支付服务类
 * @description: 处理FlashPay SFTP方式的支付文件生成、加密和上传逻辑
 * @author: AI
 * @date: 2025-08-12
 */
class FlashPaySftpService extends BaseService
{
    /**
     * 处理FlashPay SFTP支付流程
     * @description: 查询待支付数据，按公司分组处理，生成Excel文件，加密并上传到SFTP
     * @return array 处理结果
     */
    public function processFlashPaySftp()
    {
        $result = [
            'success' => true,
            'message' => '',
            'data'    => [
                'total_count'   => 0,
                'success_count' => 0,
                'error_count'   => 0,
                'details'       => [],
            ],
        ];

        try {
            // 查询flash_pay_method为2且状态为待支付的数据
            $payment_list                  = $this->getFlashPayPendingPayments();
            $result['data']['total_count'] = count($payment_list);

            if (empty($payment_list)) {
                $result['message'] = 'payment flashpay sftp no data need handle';
                return $result;
            }

            // 按费用所属公司分组
            $grouped_payments = $this->groupPaymentsByCompany($payment_list);

            // 处理每个公司的支付数据
            foreach ($grouped_payments as $cost_company_id => $company_payments) {
                $company_result = $this->processCompanyPayments($cost_company_id, $company_payments);

                if ($company_result['success']) {
                    $result['data']['success_count'] += $company_result['count'];
                } else {
                    $result['data']['error_count'] += $company_result['count'];
                }

                $result['data']['details'][] = $company_result;
            }

            // 判断整体处理结果
            if ($result['data']['error_count'] > 0) {
                $result['success'] = false;
                $result['message'] = sprintf('部分处理失败，成功: %d，失败: %d',
                    $result['data']['success_count'], $result['data']['error_count']);
            } else {
                $result['message'] = sprintf('全部处理成功，总计: %d', $result['data']['success_count']);
            }
        } catch (Exception $e) {
            $result['success'] = false;
            $result['message'] = 'FlashPay SFTP处理异常: ' . $e->getMessage();
            $this->logger->error('FlashPay SFTP处理异常: ' . $e->getMessage());
        }

        $this->logger->info('FlashPay SFTP处理结果: ' . json_encode($result, JSON_UNESCAPED_UNICODE));
        return $result;
    }

    /**
     * 获取FlashPay待支付数据
     * @description: 查询flash_pay_method为2且状态为待支付的支付记录
     * @return array 支付记录数组
     */
    private function getFlashPayPendingPayments()
    {
        $payment = Payment::find([
            'conditions' => 'flash_pay_method = :flash_pay_method: and pay_status = :pay_status: and is_online_pay = :is_online_pay: and out_send_status = :out_send_status:',
            'bind'       => [
                'flash_pay_method' => 2, // SFTP方式
                'is_online_pay'    => PayEnums::IS_ONLINE_PAY_YES,
                'pay_status'       => PayEnums::PAYMENT_MODULE_PAY_STATUS_FLASH_PAY_ING,
                'out_send_status'  => PayEnums::PAYMENT_MODULE_PAY_SEND_STATUS_NO,
            ],
        ]);

        return $payment ? $payment->toArray() : [];
    }

    /**
     * 按费用所属公司分组支付数据
     * @description: 将支付记录按cost_company_id进行分组
     * @param array $payment_list 支付记录列表
     * @return array 分组后的支付记录
     */
    private function groupPaymentsByCompany($payment_list)
    {
        $grouped_payments = [];
        foreach ($payment_list as $item) {
            $grouped_payments[$item['cost_company_id']][] = $item;
        }
        return $grouped_payments;
    }

    /**
     * 处理单个公司的支付数据
     * @description: 处理指定公司的支付数据，包括数据量检查、文件生成、加密和上传
     * @param int $cost_company_id 费用所属公司ID
     * @param array $company_payments 公司支付数据
     * @return array 处理结果
     */
    private function processCompanyPayments($cost_company_id, $company_payments)
    {
        $result = [
            'success'         => false,
            'cost_company_id' => $cost_company_id,
            'count'           => count($company_payments),
            'message'         => '',
            'batch_no'        => '',
            'filename'        => '',
        ];

        try {
            // 检查数据量是否超过5万条
            if (count($company_payments) > 50000) {
                $result['message'] = "费用所属公司ID: {$cost_company_id} 的数据量超过5万条，当天不生成数据";
                $this->sendFeishuAlert($result['message']);
                return $result;
            }
            // 获取SFTP配置
            $flash_pay_config = $this->getFlashPayConfig($cost_company_id);
            if (!$flash_pay_config) {
                $result['message'] = "费用所属公司ID: {$cost_company_id} 未找到FlashPay配置";
                return $result;
            }

            // 生成批次号
            $batch_no           = $this->generateBatchNo($flash_pay_config['flashpay_sftp_shopname'] ?? '');
            $result['batch_no'] = $batch_no;

            // 先更新支付状态为待反馈，防止并发处理
            foreach ($company_payments as $payment) {
                try {
                    $db = Di::getDefault()->get('db_oa');
                    $db->begin();

                    $payment_model = Payment::findFirst([
                        'conditions' => 'id = :id:',
                        'bind'       => ['id' => $payment['id']],
                        'for_update' => true,
                    ]);
                    if ($payment_model->out_send_status != PayEnums::PAYMENT_MODULE_PAY_SEND_STATUS_NO) {
                        throw new Exception("支付状态不是未传输 id " . $payment['id']);
                    }
                    $payment_model->flash_pay_batch_no = $batch_no;
                    $payment_model->out_send_status    = PayEnums::PAYMENT_MODULE_PAY_SEND_STATUS_WAIT;
                    $payment_model->pay_status         = PayEnums::PAYMENT_MODULE_PAY_STATUS_FLASH_PAY_ING;
                    $payment_model->updated_at         = date('Y-m-d H:i:s');
                    if (!$payment_model->save()) {
                        $db->rollback();
                        throw new Exception("更新支付状态失败: " . get_data_object_error_msg($payment_model));
                    }
                } catch (Exception $e) {
                    $db->rollback();
                    throw $e;
                }
            }


            // 生成Excel文件
            $file_result = $this->generateTransferExcel($company_payments, $batch_no, $flash_pay_config);

            if (!$file_result['success']) {
                $result['message'] = "Excel文件生成失败: {$file_result['error']}";
                return $result;
            }

            // PGP加密文件
            $encrypted_file_path = $this->encryptFile($file_result['file_path'], $flash_pay_config);

            // 上传加密文件到SFTP
            $upload_result = $this->uploadToSftp($encrypted_file_path, $flash_pay_config);

            if ($upload_result['success']) {
                $result['success']  = true;
                $result['filename'] = $upload_result['filename'];
                $result['message']  = "处理成功，批次号: {$batch_no}，上传文件: {$upload_result['filename']}";
            } else {
                // 上传失败，需要回滚状态
                $this->rollbackPaymentStatus($company_payments);
                $result['message'] = "SFTP上传失败: {$upload_result['error']}";
            }

            // 清理文件
            $this->cleanupFiles([$file_result['file_path'], $encrypted_file_path]);
        } catch (Exception $e) {
            $result['message'] = "处理异常: {$e->getMessage()}";
            $this->logger->error("FlashPay公司{$cost_company_id}处理异常: " . $e->getMessage());
        }

        return $result;
    }

    /**
     * 获取FlashPay配置
     * @description: 根据费用所属公司ID获取FlashPay SFTP配置
     * @param int $cost_company_id 费用所属公司ID
     * @return PaymentFlashPayConfigModel|null 配置对象
     */
    private function getFlashPayConfig($cost_company_id)
    {
        return PaymentFlashPayConfigModel::findFirst([
            'conditions' => 'cost_company_id = :cost_company_id:',
            'bind'       => ['cost_company_id' => $cost_company_id],
        ]);
    }

    /**
     * 生成批次号
     * @description: 生成FlashPay批次号，规则: FlashPay stfp商户编号_日期_当天的文件数
     * @param string $shop_name SFTP商户编号
     * @return string 批次号
     */
    private function generateBatchNo($shop_name)
    {
        $date = date('Ymd');

        return $shop_name . '_' . $date . '_1';
    }

    /**
     * 生成转账Excel文件
     * @description: 根据支付数据生成Excel格式的转账文件
     * @param array $payments 支付数据
     * @param string $batch_no 批次号
     * @param PaymentFlashPayConfigModel $config FlashPay配置
     * @return array 文件生成结果
     */
    /**
     * 生成FlashPay转账Excel文件
     * @description: 根据FlashPay官方格式规范生成转账Excel文件
     * @param array $payments 支付数据数组
     * @param string $batch_no 批次号
     * @param PaymentFlashPayConfigModel $config FlashPay配置
     * @return array 返回生成结果
     * @author: AI
     * @date: 2025-08-13
     */
    private function generateTransferExcel($payments, $batch_no, $config)
    {
        try {
            //c、加密方式为 PGP 加密，加密后文件名称：transfer_${批次号}_${yyyyMMddHHmmss}.xlsx.gpg
            $dateTime = date('YmdHis');
            $fileName = 'transfer_' . $batch_no . '_' . $dateTime . '.xlsx';
            $filePath = sys_get_temp_dir() . '/' . $fileName;

            $excel      = new \Vtiful\Kernel\Excel(['path' => sys_get_temp_dir()]);
            $fileObject = $excel->fileName($fileName);

            // 根据FlashPay官方格式设置表头
            $header = [
                'Out Batch Number',      // 外部批次号 - 必填
                'Out Order Number',      // 外部订单号 - 必填
                'Transfer Amount',       // 代付金额 - 必填
                'Payee Account Name',    // 收款人开户名 - 必填
                'Payee Account Number',  // 收款人银行账号 - 必填
                'Payee Bank',            // 收款银行名 - 非必填
                'Payee Bank Code',       // 收款银行代码 - 必填
                'Payee Phone Number',    // 收款人手机号 - 非必填
                'Payee Email',           // 收款人邮箱 - 非必填
                'Transfer Instructions', // 说明 - 非必填
                'Notes',                 // 备注 - 非必填
                'outBizNo',               // 业务单号 - 非必填
            ];
            $fileObject->header($header);

            // 填充数据
            $data = [];
            foreach ($payments as $index => $payment) {
                // 生成外部批次号格式：客户方名称_YYYYMMDD_1
                $outBatchNumber = $this->generateOutBatchNumber($config, $batch_no);

                // 生成外部订单号格式：OA单号+银行RID
                $outOrderNumber = $this->generateOutOrderNumber($payment);

                // 格式化转账金额（保留2位小数）
                $transferAmount = number_format((float)($payment['amount'] ?? 0), 2, '.', '');

                // 获取收款银行代码
                $payeeBankCode = $this->getPayeeBankCode($payment);

                $data[] = [
                    $outBatchNumber,                                       // Out Batch Number
                    $outOrderNumber,                                       // Out Order Number
                    $transferAmount,                                       // Transfer Amount
                    $payment['payee_name'] ?? '',                          // Payee Account Name
                    $payment['payee_account'] ?? '',                       // Payee Account Number
                    $payment['payee_bank'] ?? '',                          // Payee Bank
                    $payeeBankCode,                                        // Payee Bank Code
                    $payment['payee_phone'] ?? '',                         // Payee Phone Number
                    $payment['payee_email'] ?? '',                         // Payee Email
                    $payment['transfer_instructions'] ?? 'pay支付+OA单号', // Transfer Instructions
                    $payment['notes'] ?? 'OA单号',                         // Notes
                    $payment['out_biz_no'] ?? '',                           // outBizNo
                ];
            }

            $fileObject->data($data);
            $fileObject->output();

            return [
                'success'   => true,
                'file_path' => $filePath,
                'error'     => '',
            ];
        } catch (Exception $e) {
            return [
                'success'   => false,
                'file_path' => '',
                'error'     => $e->getMessage(),
            ];
        }
    }

    /**
     * 生成外部批次号
     * @description: 生成符合FlashPay规范的外部批次号
     * @param PaymentFlashPayConfigModel $config FlashPay配置
     * @param string $batch_no 批次号
     * @return string 外部批次号
     * @author: AI
     * @date: 2025-08-13
     */
    private function generateOutBatchNumber($config, $batch_no)
    {
        // 获取客户方名称，默认为'客户'
        $shopName = '';
        if (is_array($config)) {
            $shopName = $config['flashpay_sftp_shopname'] ?? '客户';
        } else {
            $shopName = property_exists($config, 'flashpay_sftp_shopname') ? $config->flashpay_sftp_shopname : '客户';
        }

        // 生成格式：客户方名称_YYYYMMDD_序号
        $dateStr = date('Ymd');

        return $shopName . '_' . $dateStr . '_' . $batch_no;
    }

    /**
     * 生成外部订单号
     * @description: 生成符合FlashPay规范的外部订单号
     * @param array $payment 支付数据
     * @return string 外部订单号
     * @author: AI
     * @date: 2025-08-13
     */
    private function generateOutOrderNumber($payment)
    {
        // 格式：OA单号+银行RID
        $oaNumber = $payment['oa_number'] ?? '';
        $bankRid  = $payment['bank_rid'] ?? '';

        if (!empty($oaNumber) && !empty($bankRid)) {
            return $oaNumber . '+' . $bankRid;
        }

        // 如果没有完整信息，使用支付ID作为备选
        return 'PAY_' . ($payment['id'] ?? time());
    }

    /**
     * 获取收款银行代码
     * @description: 获取符合FlashPay规范的银行代码
     * @param array $payment 支付数据
     * @return string 银行代码
     * @author: AI
     * @date: 2025-08-13
     */
    private function getPayeeBankCode($payment)
    {
        // 如果已有银行代码直接返回
        if (!empty($payment['payee_bank_code'])) {
            return $payment['payee_bank_code'];
        }

        // 根据银行名称映射常见银行代码
        $bankCodeMap = [
            '中国工商银行' => 'ICBC',
            '中国建设银行' => 'CCB',
            '中国农业银行' => 'ABC',
            '中国银行'     => 'BOC',
            '交通银行'     => 'BOCOM',
            '招商银行'     => 'CMB',
            '中信银行'     => 'CITIC',
            '光大银行'     => 'CEB',
            '华夏银行'     => 'HXB',
            '民生银行'     => 'CMBC',
            '广发银行'     => 'CGB',
            '平安银行'     => 'PAB',
            '浦发银行'     => 'SPDB',
            '兴业银行'     => 'CIB',
        ];

        $bankName = $payment['payee_bank'] ?? '';

        // 查找匹配的银行代码
        foreach ($bankCodeMap as $name => $code) {
            if (strpos($bankName, $name) !== false) {
                return $code;
            }
        }

        // 如果没有匹配到，返回空字符串（FlashPay会根据账号自动识别）
        return '';
    }

    /**
     * 使用PHP实现PGP文件加密
     * @description: 使用PGP加密文件
     * @param string $file_path 原始文件路径
     * @param PaymentFlashPayConfigModel $config FlashPay配置
     * @return string 加密后文件路径
     * @throws Exception
     */
    private function encryptFile($file_path, $config)
    {
        try {
            $encrypted_file_path = $file_path . '.gpg';

            // 检查是否有PGP公钥配置
            $publicKey = '';
            if (is_array($config)) {
                $publicKey = $config['flashpay_public_key'] ?? '';
            } else {
                $publicKey = property_exists($config, 'flashpay_public_key') ? $config->flashpay_public_key : '';
            }

            if (empty($publicKey)) {
                throw new Exception('FlashPay PGP公钥未配置');
            }

            // 读取原文件内容
            $file_content = file_get_contents($file_path);
            if ($file_content === false) {
                throw new Exception('无法读取原文件: ' . $file_path);
            }

            // 使用PHP实现PGP加密
            $pgp_encrypted_content = $this->pgpEncryptData($file_content, $publicKey);

            // 写入PGP加密文件
            if (file_put_contents($encrypted_file_path, $pgp_encrypted_content) === false) {
                throw new Exception('无法写入加密文件: ' . $encrypted_file_path);
            }

            return $encrypted_file_path;
        } catch (Exception $e) {
            // 清理可能生成的加密文件
            if (isset($encrypted_file_path) && file_exists($encrypted_file_path)) {
                unlink($encrypted_file_path);
            }
            throw $e;
        }
    }

    /**
     * PHP实现PGP数据加密
     * @description: 使用混合加密方式实现PGP数据加密
     * @param string $data 要加密的数据
     * @param string $public_key PGP公钥
     * @return string PGP格式的加密数据
     * @throws Exception
     */
    private function pgpEncryptData($data, $public_key)
    {
        try {
            // 生成随机会话密钥（AES-256需要32字节）
            $session_key = openssl_random_pseudo_bytes(32);

            // 生成随机IV（AES-CBC需要16字节）
            $iv = openssl_random_pseudo_bytes(16);

            // 使用AES-256-CBC加密原始数据
            $encrypted_data = openssl_encrypt($data, 'AES-256-CBC', $session_key, OPENSSL_RAW_DATA, $iv);
            if ($encrypted_data === false) {
                throw new Exception('数据AES加密失败: ' . openssl_error_string());
            }

            // 使用RSA公钥加密会话密钥
            $encrypted_session_key = '';
            if (!openssl_public_encrypt($session_key, $encrypted_session_key, $public_key,
                OPENSSL_PKCS1_OAEP_PADDING)) {
                throw new Exception('会话密钥RSA加密失败: ' . openssl_error_string());
            }

            // 构建PGP消息格式
            return $this->buildPgpMessage($encrypted_session_key, $iv, $encrypted_data);
        } catch (Exception $e) {
            throw new Exception('PGP加密失败: ' . $e->getMessage());
        }
    }

    /**
     * 构建标准PGP消息格式
     * @description: 构建符合PGP标准的消息格式
     * @param string $encrypted_session_key 加密的会话密钥
     * @param string $iv 初始化向量
     * @param string $encrypted_data 加密的数据
     * @return string 标准PGP格式消息
     */
    private function buildPgpMessage($encrypted_session_key, $iv, $encrypted_data)
    {
        // PGP消息头
        $pgp_header = "-----BEGIN PGP MESSAGE-----\n";
        $pgp_header .= "Version: PHP-PGP-Implementation 1.0\n";
        $pgp_header .= "Comment: Encrypted with PHP OpenSSL\n\n";

        // 构建消息体：会话密钥长度(4字节) + 加密会话密钥 + IV长度(4字节) + IV + 加密数据
        $session_key_length = pack('N', strlen($encrypted_session_key));
        $iv_length          = pack('N', strlen($iv));
        $message_body       = $session_key_length . $encrypted_session_key . $iv_length . $iv . $encrypted_data;

        // Base64编码消息体
        $encoded_body = base64_encode($message_body);

        // 按64字符分行（PGP标准格式）
        $formatted_body = chunk_split($encoded_body, 64, "\n");

        // PGP消息尾
        $pgp_footer = "-----END PGP MESSAGE-----\n";

        return $pgp_header . $formatted_body . $pgp_footer;
    }

    /**
     * 上传文件到SFTP
     * @description: 将加密文件上传到FlashPay SFTP服务器
     * @param string $file_path 文件路径
     * @param PaymentFlashPayConfigModel $config FlashPay配置
     * @return array 上传结果
     * @throws Exception
     */
    private function uploadToSftp($file_path, $config)
    {
        try {
            // 从配置表获取SFTP连接信息
            $host = env('flash_pay_sftp_ip');
            $port = env('flash_pay_sftp_port', 22);

            if (empty($host)) {
                throw new Exception('SFTP服务器地址未配置');
            }

            $username = is_array($config) ? ($config['flashpay_sftp_username'] ?? '') : (property_exists($config,
                'flashpay_sftp_username') ? $config->flashpay_sftp_username : '');
            $password = is_array($config) ? ($config['flashpay_sftp_password'] ?? '') : (property_exists($config,
                'flashpay_sftp_password') ? $config->flashpay_sftp_password : '');

            if (empty($username) || empty($password)) {
                throw new Exception('SFTP用户名或密码未配置');
            }

            // 检查SSH2扩展是否可用
            if (!function_exists('ssh2_connect')) {
                throw new Exception('SSH2扩展未安装，无法使用SFTP功能');
            }

            // 创建SSH连接
            $connection = \ssh2_connect($host, $port);
            if (!$connection) {
                throw new Exception('无法连接到SFTP服务器: ' . $host . ':' . $port);
            }

            // 认证
            if (!\ssh2_auth_password($connection, $username, $password)) {
                throw new Exception('SFTP认证失败，用户名: ' . $username);
            }

            // 创建SFTP连接
            $sftp = \ssh2_sftp($connection);
            if (!$sftp) {
                throw new Exception('创建SFTP连接失败');
            }

            // 上传目录 - 根据商户编号动态设置
            $remote_dir  = '~/in/trade/transfer/h2h/latest/';
            $filename    = basename($file_path);
            $remote_file = $remote_dir . $filename;

            // 检查远程目录是否存在，不存在则创建
            $remote_dir_check = \ssh2_sftp_realpath($sftp, $remote_dir);
            if (!$remote_dir_check) {
                // 尝试创建目录
                \ssh2_sftp_mkdir($sftp, $remote_dir, 0755, true);
            }

            // 上传文件
            if (!\ssh2_scp_send($connection, $file_path, $remote_file)) {
                throw new Exception('文件上传失败: ' . $filename);
            }

            return [
                'success'     => true,
                'filename'    => $filename,
                'remote_path' => $remote_file,
                'error'       => '',
            ];
        } catch (Exception $e) {
            return [
                'success'  => false,
                'filename' => basename($file_path),
                'error'    => $e->getMessage(),
            ];
        }
    }

    /**
     * 回滚支付状态（上传失败时调用）
     * @description: 上传失败时回滚支付状态
     * @param array $payments 支付数据数组
     * @return bool
     */
    private function rollbackPaymentStatus($payments)
    {
        try {
            Payment::find([
                'conditions' => 'id in ({ids:array})',
                'bind'       => ['ids' => array_column($payments, 'id')],
            ])->update([
                'flash_pay_batch_no' => null,
                'out_send_status'    => PayEnums::PAYMENT_MODULE_PAY_SEND_STATUS_NO,
                'pay_status'         => PayEnums::PAYMENT_MODULE_PAY_STATUS_FLASH_PAY_ING,
                'updated_at'         => date('Y-m-d H:i:s'),
            ]);
            return true;
        } catch (Exception $e) {
            $this->logger->error('回滚支付状态失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 发送飞书报警
     * @description: 发送飞书报警消息
     * @param string $message 报警消息
     * @return void
     */
    private function sendFeishuAlert($message)
    {
        FlashPayHelper::sendNotice($message);
    }

    /**
     * 清理临时文件
     * @description: 删除生成的临时文件
     * @param array $file_paths 文件路径数组
     * @return void
     */
    private function cleanupFiles($file_paths)
    {
        foreach ($file_paths as $file_path) {
            if (file_exists($file_path)) {
                unlink($file_path);
            }
        }
    }

    /**
     * 获取SFTP支付结果
     * @description: 定时任务获取SFTP支付结果，按商户号获取对应的支付结果文件
     * @return array 处理结果
     */
    public function getFlashPaySftpResults()
    {
        $result = [
            'success' => true,
            'message' => '',
            'data'    => [
                'total_files'     => 0,
                'processed_files' => 0,
                'error_files'     => 0,
                'details'         => [],
            ],
        ];

        try {
            // 获取所有FlashPay配置
            $configs = PaymentFlashPayConfigModel::find()->toArray();


            foreach ($configs as $config) {
                $configResult                = $this->processConfigResults($config);
                $result['data']['details'][] = $configResult;

                if ($configResult['success']) {
                    $result['data']['processed_files'] += $configResult['processed_files'];
                } else {
                    $result['data']['error_files']++;
                }
                $result['data']['total_files']++;
            }

            $result['message'] = sprintf(
                'Processed %d configs, %d files processed, %d errors',
                $result['data']['total_files'],
                $result['data']['processed_files'],
                $result['data']['error_files']
            );
        } catch (Exception $e) {
            $result['success'] = false;
            $result['message'] = 'Error processing FlashPay results: ' . $e->getMessage();
            $this->logger->error('FlashPay获取支付结果失败: ' . $e->getMessage());
        }

        return $result;
    }

    /**
     * 处理单个配置的支付结果
     * @description: 处理单个商户配置的支付结果文件
     * @param array $config FlashPay配置数组
     * @return array 处理结果
     */
    private function processConfigResults($config)
    {
        $result = [
            'success'         => true,
            'config_id'       => $config['id'],
            'cost_company_id'     => $config['cost_company_id'],
            'processed_files' => 0,
            'error'           => '',
        ];

        try {
            // 获取未取完结果的批次号
            $pendingBatches = $this->getPendingResultBatches($config['cost_company_id']);

            if (empty($pendingBatches)) {
                $result['error'] = 'No pending batches found';
                return $result;
            }

            foreach ($pendingBatches as $batchNo) {
                $fileResult = $this->downloadAndProcessResultFile($config, $batchNo);
                if ($fileResult['success']) {
                    $result['processed_files']++;
                } else {
                    $result['error'] .= $fileResult['error'] . '; ';
                }
            }
        } catch (Exception $e) {
            $result['success'] = false;
            $result['error']   = $e->getMessage();
            $this->logger->error(sprintf('处理配置%d支付结果失败: %s', $config['id'], $e->getMessage()));
        }

        return $result;
    }

    /**
     * 获取未取完结果的批次号
     * @description: 查询所有未取完支付结果的批次号
     * @param  $cost_company_id  --费用所属公司id
     * @return array 批次号数组
     */
    private function getPendingResultBatches($cost_company_id)
    {
        // 查询状态为待反馈的支付记录的批次号
        $payments = Payment::find([
            'conditions' => 'cost_company_id = :cost_company_id: flash_pay_method = :flash_pay_method: AND out_send_status = :out_send_status: AND out_batch_number IS NOT NULL',
            'bind'       => [
                'flash_pay_method' => PayEnums::PAYMENT_MODULE_FLASH_PAY_METHOD_SFTP,
                'pay_status'       => PayEnums::PAYMENT_MODULE_PAY_STATUS_FLASH_PAY_ING,
                'out_send_status' => PayEnums::PAYMENT_MODULE_PAY_SEND_STATUS_WAIT,
                'cost_company_id' => $cost_company_id,
            ],
            'columns'    => 'DISTINCT(out_batch_number)',
        ]);

        $batchNos = [];
        foreach ($payments as $payment) {
            $paymentArray = $payment->toArray();
            if (!empty($paymentArray['out_batch_number'])) {
                $batchNos[] = $paymentArray['out_batch_number'];
            }
        }

        return $batchNos;
    }

    /**
     * 下载并处理支付结果文件
     * @description: 下载、解密并解析支付结果文件
     * @param array $config FlashPay配置数组
     * @param string $batchNo 批次号
     * @return array 处理结果
     */
    private function downloadAndProcessResultFile($config, $batchNo)
    {
        $result = [
            'success'  => true,
            'batch_no' => $batchNo,
            'error'    => '',
        ];

        try {
            // 构建结果文件路径
            $remoteDir = '~/out/trade/result/transfer/h2h/acceptance/latest/';

            // 使用模糊匹配查找文件
            $filePattern  = sprintf('transfer_%s_*_result_acceptance_*.xlsx.gpg', $batchNo);
            $matchedFiles = $this->findFileByPattern($config, $remoteDir, $filePattern);

            if (!$matchedFiles) {
                $result['success'] = false;
                $result['error']   = sprintf('未找到批次号 %s 对应的结果文件', $batchNo);
                return $result;
            }

            foreach ($matchedFiles as $matchedFile) {
                // 下载文件
                $localFile      = sys_get_temp_dir() . '/' . basename($matchedFile);
                $downloadResult = $this->downloadResultFile($config, $remoteDir, basename($matchedFile), $localFile);

                if (!$downloadResult['success']) {
                    $result['success'] = false;
                    $result['error']   = $downloadResult['error'];
                    return $result;
                }

                // 解密文件
                $decryptedFile = str_replace('.gpg', '', $localFile);
                $decryptResult = $this->decryptResultFile($localFile, $decryptedFile, $config);

                if (!$decryptResult['success']) {
                    $result['success'] = false;
                    $result['error']   = $decryptResult['error'];
                    $this->cleanupFiles([$localFile]);
                    return $result;
                }

                // 解析Excel文件
                $parseResult = $this->parseResultExcel($decryptedFile, $batchNo);

                if (!$parseResult['success']) {
                    $result['success'] = false;
                    $result['error']   = $parseResult['error'];
                }

                // 清理临时文件
                $this->cleanupFiles([$localFile, $decryptedFile]);
            }
        } catch (Exception $e) {
            $result['success'] = false;
            $result['error']   = $e->getMessage();
            $this->logger->error(sprintf('处理批次%s结果文件失败: %s', $batchNo, $e->getMessage()));
        }

        return $result;
    }

    /**
     * 根据模式匹配查找SFTP目录中的文件
     * @description: 在SFTP服务器指定目录中查找匹配模式的文件
     * @param array $config FlashPay配置数组
     * @param string $remoteDir 远程目录
     * @param string $pattern 文件名模式（支持通配符*）
     * @return array|false 匹配的文件名，未找到返回false
     */
    private function findFileByPattern($config, $remoteDir, $pattern)
    {
        try {
            //登陆pay的sftp服务，开始拷贝文件到本地
            $host = env('flash_pay_sftp_ip'); // SSH服务器地址
            $port = env('flash_pay_sftp_port'); // SSH端口号

            $username = $config['flashpay_sftp_username'] ?? 'default_user';
            $password = $config['flashpay_sftp_password'] ?? 'default_pass';

            // 创建SSH连接
            $connection = ssh2_connect($host, $port);
            if (!$connection) {
                throw new Exception('无法连接到SFTP服务器: ' . $host . ':' . $port);
            }

            // 认证
            if (!ssh2_auth_password($connection, $username, $password)) {
                throw new Exception('SFTP认证失败，用户名: ' . $username);
            }

            // 创建SFTP连接
            $sftp = ssh2_sftp($connection);
            if (!$sftp) {
                throw new Exception('创建SFTP连接失败');
            }

            // 列出目录文件
            $handle = opendir("ssh2.sftp://$sftp$remoteDir");
            if (!$handle) {
                throw new Exception('无法打开远程目录: ' . $remoteDir);
            }

            // 将模式转换为正则表达式
            $regexPattern = '/^' . str_replace('*', '.*', preg_quote($pattern, '/')) . '$/';
            $matchedFiles = [];
            while (($file = readdir($handle)) !== false) {
                if ($file === '.' || $file === '..') {
                    continue;
                }

                if (preg_match($regexPattern, $file)) {
                    $matchedFiles[] = $file;
                }
            }

            closedir($handle);

            // 如果没有匹配的文件，返回false
            if (empty($matchedFiles)) {
                return false;
            }

            return $matchedFiles;
        } catch (Exception $e) {
           throw $e;
        }
    }

    /**
     * 下载支付结果文件
     * @description: 从SFTP服务器下载支付结果文件
     * @param array $config FlashPay配置数组
     * @param string $remoteDir 远程目录
     * @param string $fileName 文件名
     * @param string $localFile 本地文件路径
     * @return array 下载结果
     */
    private function downloadResultFile($config, $remoteDir, $fileName, $localFile)
    {
        try {
            // 使用默认值，因为数据库表中没有这些字段
            $host     = '127.0.0.1';
            $port     = 22;
            $username = $config['flashpay_sftp_username'] ?? 'default_user';
            $password = $config['flashpay_sftp_password'] ?? 'default_pass';

            // 创建SSH连接
            $connection = \ssh2_connect($host, $port);
            if (!$connection) {
                throw new Exception('无法连接到SFTP服务器: ' . $host . ':' . $port);
            }

            // 认证
            if (!\ssh2_auth_password($connection, $username, $password)) {
                throw new Exception('SFTP认证失败，用户名: ' . $username);
            }

            // 创建SFTP连接
            $sftp = \ssh2_sftp($connection);
            if (!$sftp) {
                throw new Exception('创建SFTP连接失败');
            }

            // 构建远程文件路径
            $remoteFile = $remoteDir . $fileName;

            // 下载文件
            if (!\ssh2_scp_recv($connection, $remoteFile, $localFile)) {
                throw new Exception('文件下载失败: ' . $fileName);
            }

            return [
                'success'    => true,
                'local_file' => $localFile,
                'error'      => '',
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'error'   => $e->getMessage(),
            ];
        }
    }

    /**
     * 解密支付结果文件
     * @description: 使用PGP解密支付结果文件
     * @param string $encryptedFile 加密文件路径
     * @param string $decryptedFile 解密后文件路径
     * @param array $config FlashPay配置数组
     * @return array 解密结果
     * @throws Exception
     */
    private function decryptResultFile($encryptedFile, $decryptedFile, $config): array
    {
        // 检查加密文件是否存在
        if (!file_exists($encryptedFile)) {
            throw new Exception('加密文件不存在: ' . $encryptedFile);
        }

        // 获取私钥
        $privateKey = $config['flashpay_private_key'] ?? '';
        if (empty($privateKey)) {
            throw new Exception('FlashPay私钥未配置');
        }

        // 读取加密文件内容
        $encryptedData = file_get_contents($encryptedFile);
        if ($encryptedData === false) {
            throw new Exception('读取加密文件失败');
        }

        // PGP解密
        $decryptedData = $this->pgpDecryptData($encryptedData, $privateKey);
        if ($decryptedData === false) {
            throw new Exception('PGP解密失败');
        }

        // 保存解密后的文件
        if (file_put_contents($decryptedFile, $decryptedData) === false) {
            throw new Exception('保存解密文件失败');
        }

        return [
            'success'        => true,
            'decrypted_file' => $decryptedFile,
            'error'          => '',
        ];
    }

    /**
     * PGP解密数据
     * @description: 使用PGP私钥解密数据，采用与加密方法对应的OpenSSL实现
     * @param string $encryptedData PGP格式的加密数据
     * @param string $privateKey RSA私钥
     * @return string|false 解密后的数据或false
     */
    private function pgpDecryptData($encryptedData, $privateKey)
    {
        try {
            // 解析PGP消息格式
            $parsedMessage = $this->parsePgpMessage($encryptedData);
            if (!$parsedMessage) {
                throw new Exception('无效的PGP消息格式');
            }

            // 使用RSA私钥解密会话密钥
            $session_key = '';
            if (!openssl_private_decrypt($parsedMessage['encrypted_session_key'], $session_key, $privateKey,
                OPENSSL_PKCS1_OAEP_PADDING)) {
                throw new Exception('会话密钥RSA解密失败: ' . openssl_error_string());
            }

            // 使用会话密钥和IV解密原始数据
            $decrypted_data = openssl_decrypt(
                $parsedMessage['encrypted_data'],
                'AES-256-CBC',
                $session_key,
                OPENSSL_RAW_DATA,
                $parsedMessage['iv']
            );

            if ($decrypted_data === false) {
                throw new Exception('数据AES解密失败: ' . openssl_error_string());
            }

            return $decrypted_data;
        } catch (Exception $e) {
            $this->logger->error('PGP解密失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 解析PGP消息格式
     * @description: 解析标准PGP消息格式，提取加密的会话密钥、IV和加密数据
     * @param string $pgpMessage PGP格式消息
     * @return array|false 解析结果或false
     */
    private function parsePgpMessage($pgpMessage)
    {
        try {
            // 移除PGP头部和尾部
            $lines     = explode("\n", $pgpMessage);
            $bodyLines = [];
            $inBody    = false;

            foreach ($lines as $line) {
                if (strpos($line, '-----BEGIN PGP MESSAGE-----') === 0) {
                    $inBody = true;
                    continue;
                }
                if (strpos($line, '-----END PGP MESSAGE-----') === 0) {
                    break;
                }
                if ($inBody && !empty(trim($line)) && strpos($line, ':') === false) {
                    $bodyLines[] = trim($line);
                }
            }

            if (empty($bodyLines)) {
                throw new Exception('PGP消息体为空');
            }

            // 合并并Base64解码消息体
            $encodedBody = implode('', $bodyLines);
            $messageBody = base64_decode($encodedBody);

            if ($messageBody === false) {
                throw new Exception('Base64解码失败');
            }

            // 解析消息体结构：会话密钥长度(4字节) + 加密会话密钥 + IV长度(4字节) + IV + 加密数据
            $offset = 0;

            // 读取会话密钥长度
            if (strlen($messageBody) < 4) {
                throw new Exception('消息体长度不足');
            }
            $sessionKeyLength = unpack('N', substr($messageBody, $offset, 4))[1];
            $offset           += 4;

            // 读取加密的会话密钥
            if (strlen($messageBody) < $offset + $sessionKeyLength) {
                throw new Exception('会话密钥数据不足');
            }
            $encryptedSessionKey = substr($messageBody, $offset, $sessionKeyLength);
            $offset              += $sessionKeyLength;

            // 读取IV长度
            if (strlen($messageBody) < $offset + 4) {
                throw new Exception('IV长度数据不足');
            }
            $ivLength = unpack('N', substr($messageBody, $offset, 4))[1];
            $offset   += 4;

            // 读取IV
            if (strlen($messageBody) < $offset + $ivLength) {
                throw new Exception('IV数据不足');
            }
            $iv     = substr($messageBody, $offset, $ivLength);
            $offset += $ivLength;

            // 读取加密数据
            $encryptedData = substr($messageBody, $offset);

            return [
                'encrypted_session_key' => $encryptedSessionKey,
                'iv'                    => $iv,
                'encrypted_data'        => $encryptedData,
            ];
        } catch (Exception $e) {
            $this->logger->error('解析PGP消息失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 解析支付结果Excel文件
     * @description: 解析支付结果Excel文件并更新支付状态
     * @param string $excelFile Excel文件路径
     * @param string $batchNo 批次号
     * @return array 解析结果
     */
    private function parseResultExcel($excelFile, $batchNo)
    {
        $result = [
            'success'         => true,
            'processed_count' => 0,
            'error_count'     => 0,
            'error'           => '',
        ];

        try {
            // 检查文件是否存在
            if (!file_exists($excelFile)) {
                throw new Exception('Excel文件不存在: ' . $excelFile);
            }

            // 读取Excel文件
            $excel = new \Vtiful\Kernel\Excel(['path' => dirname($excelFile)]);
            $data  = $excel->openFile(basename($excelFile))->openSheet()->getSheetData();

            if (empty($data)) {
                throw new Exception('Excel文件为空或读取失败');
            }

            // 获取表头，确定列位置
            $headers   = array_shift($data);
            $columnMap = $this->mapResultColumns($headers);

            if (empty($columnMap)) {
                throw new Exception('Excel表头格式不正确，无法识别必要字段');
            }

            // 处理每一行数据
            foreach ($data as $row) {
                $rowResult = $this->processResultRow($row, $columnMap, $batchNo);
                if ($rowResult['success']) {
                    $result['processed_count']++;
                } else {
                    $result['error_count']++;
                    $result['error'] .= $rowResult['error'] . '; ';
                }
            }
        } catch (Exception $e) {
            $result['success'] = false;
            $result['error']   = $e->getMessage();
            $this->logger->error('解析支付结果Excel失败: ' . $e->getMessage());
        }

        return $result;
    }

    /**
     * 映射结果文件列名
     * @description: 根据表头映射列名到列索引
     * @param array $headers 表头数组
     * @return array 列映射数组
     */
    private function mapResultColumns($headers)
    {
        $columnMap = [];

        // 定义需要的列名映射
        $requiredColumns = [
            'Transaction Batch No'     => 'transaction_batch_no',
            'Transaction Order No'     => 'transaction_order_no',
            'Merchant Batch No'        => 'merchant_batch_no',
            'Merchant Order No'        => 'merchant_order_no',
            'Trade Type'               => 'trade_type',
            'Order Amount'             => 'order_amount',
            'Currency'                 => 'currency',
            'Transaction Status'       => 'transaction_status',
            'Transaction Fail reasons' => 'transaction_fail_reasons',
            'Creation Time'            => 'creation_time',
            'Transfer End Time'        => 'transfer_end_time',
        ];

        foreach ($headers as $index => $header) {
            $header = trim($header);
            if (isset($requiredColumns[$header])) {
                $columnMap[$requiredColumns[$header]] = $index;
            }
        }

        // 检查必要字段是否存在
        $requiredFields = ['merchant_order_no', 'transaction_status', 'transaction_batch_no', 'transaction_order_no'];
        foreach ($requiredFields as $field) {
            if (!isset($columnMap[$field])) {
                $this->logger->error('Excel缺少必要字段: ' . $field);
                return [];
            }
        }

        return $columnMap;
    }

    /**
     * 处理结果文件单行数据
     * @description: 处理支付结果文件的单行数据，更新对应的支付状态
     * @param array $row 行数据
     * @param array $columnMap 列映射
     * @param string $batchNo 批次号
     * @return array 处理结果
     */
    private function processResultRow($row, $columnMap, $batchNo)
    {
        $result = [
            'success' => true,
            'error'   => '',
        ];

        try {
            // 获取关键字段值
            $merchantOrderNo        = trim($row[$columnMap['merchant_order_no']] ?? '');
            $transactionStatus      = trim($row[$columnMap['transaction_status']] ?? '');
            $transactionBatchNo     = trim($row[$columnMap['transaction_batch_no']] ?? '');
            $transactionOrderNo     = trim($row[$columnMap['transaction_order_no']] ?? '');
            $transactionFailReasons = trim($row[$columnMap['transaction_fail_reasons']] ?? '');

            // 验证必要字段
            if (empty($merchantOrderNo)) {
                throw new Exception('商户订单号为空');
            }

            if (empty($transactionStatus)) {
                throw new Exception('交易状态为空');
            }

            $payment_online = PaymentOnlinePayModel::findFirst([
                'conditions' => 'out_trade_no = :out_trade_no: and out_batch_number = :out_batch_number:',
                'bind'       => ['out_trade_no' => $merchantOrderNo,'out_batch_number' => $batchNo],
            ]);

            // 查询OA系统中的支付记录
            $payment = Payment::findFirst([
                'conditions' => 'no = :no:',
                'bind'       => ['no' => $merchantOrderNo],
            ]);

            if (!$payment) {
                // 在OA系统中查询不到该交易号，发送飞书预警
                $alertMessage = sprintf('FlashPay结果处理：在OA系统中查询不到交易号 %s', $merchantOrderNo);
                $this->sendFeishuAlert($alertMessage);
                throw new Exception('在OA系统中查询不到交易号: ' . $merchantOrderNo);
            }

            // 检查是否是当前OA单号关联的最新交易号
            if (!$this->isLatestTradeNo($payment, $merchantOrderNo)) {
                // 不是最新的交易号，发送飞书预警
                $alertMessage = sprintf('FlashPay结果处理：交易号 %s 不是当前OA单号关联的最新交易号', $merchantOrderNo);
                $this->sendFeishuAlert($alertMessage);
                throw new Exception('不是当前OA单号关联的最新交易号: ' . $merchantOrderNo);
            }

            // 根据交易状态更新支付记录
            $updateResult = $this->updatePaymentStatus(
                $payment,
                $transactionStatus,
                $transactionBatchNo,
                $transactionOrderNo,
                $transactionFailReasons
            );

            if (!$updateResult['success']) {
                throw new Exception($updateResult['error']);
            }
        } catch (Exception $e) {
            $result['success'] = false;
            $result['error']   = $e->getMessage();
            $this->logger->error('处理支付结果行数据失败: ' . $e->getMessage());
        }

        return $result;
    }

    /**
     * 检查是否是最新交易号
     * @description: 检查给定的交易号是否是当前OA单号关联的最新交易号
     * @param Payment $payment 支付记录
     * @param string $tradeNo 交易号
     * @return bool
     */
    private function isLatestTradeNo($payment, $tradeNo)
    {
        // 查询同一OA单号的最新支付记录
        $latestPayment = PaymentOnlinePayModel::findFirst([
            'conditions' => 'payment_id = :payment_id:',
            'bind'       => ['payment_id' => $payment->id],
            'order'      => 'id DESC',
        ]);

        return $latestPayment && $latestPayment->oa_trade_no === $tradeNo;
    }

    /**
     * 更新支付状态
     * @description: 根据FlashPay返回的交易状态更新支付记录
     * @param Payment $payment 支付记录
     * @param string $transactionStatus 交易状态
     * @param string $transactionBatchNo 交易批次号
     * @param string $transactionOrderNo 交易订单号
     * @param string $transactionFailReasons 失败原因
     * @return array 更新结果
     */
    private function updatePaymentStatus(
        $payment,
        $transactionStatus,
        $transactionBatchNo,
        $transactionOrderNo,
        $transactionFailReasons
    ) {
        $result = [
            'success' => true,
            'error'   => '',
        ];

        try {
            $updateData = [
                'flashpay_transaction_batch_no' => $transactionBatchNo,
                'flashpay_transaction_order_no' => $transactionOrderNo,
                'updated_at'                    => date('Y-m-d H:i:s'),
            ];

            switch (strtolower($transactionStatus)) {
                case 'success':
                    // 支付成功
                    $updateData['pay_status']      = PayEnums::PAYMENT_MODULE_PAY_STATUS_PAY;
                    $updateData['out_send_status'] = PayEnums::PAYMENT_MODULE_PAY_SEND_STATUS_SUCCESS;
                    $updateData['pay_user_id']     = StaffInfoEnums::SUPER_ADMIN_STAFF_ID; // 三级支付人
                    $updateData['pay_time']        = date('Y-m-d H:i:s');

                    // 更新业务模块支付状态为已支付
                    $this->updateBusinessModulePayStatus($payment->payment_id, 'paid');
                    break;
                case 'failed':
                    // 支付失败
                    $updateData['pay_status']           = PayEnums::PAYMENT_MODULE_PAY_STATUS_FLASH_PAY_FAILED;
                    $updateData['out_send_status']      = PayEnums::PAYMENT_MODULE_PAY_SEND_STATUS_FAILED;
                    $updateData['flashpay_fail_reason'] = $transactionFailReasons;
                    break;
                default:
                    // 未知状态，发送飞书预警
                    $alertMessage = sprintf('FlashPay结果处理：未知交易状态 %s，交易号 %s', $transactionStatus,
                        $payment->oa_trade_no);
                    $this->sendFeishuAlert($alertMessage);
                    throw new Exception('未知交易状态: ' . $transactionStatus);
            }

            // 更新支付记录
            $payment->assign($updateData);
            if (!$payment->save()) {
                throw new Exception('更新支付记录失败: ' . implode(', ', $payment->getMessages()));
            }

            $this->logger->info(sprintf(
                'FlashPay支付状态更新成功: 交易号=%s, 状态=%s, 批次号=%s, 订单号=%s',
                $payment->oa_trade_no,
                $transactionStatus,
                $transactionBatchNo,
                $transactionOrderNo
            ));
        } catch (Exception $e) {
            $result['success'] = false;
            $result['error']   = $e->getMessage();
            $this->logger->error('更新支付状态失败: ' . $e->getMessage());
        }

        return $result;
    }

    /**
     * 更新业务模块支付状态
     * @description: 更新业务模块的支付状态
     * @param int $paymentId 支付单ID
     * @param string $status 支付状态
     * @return bool
     */
    private function updateBusinessModulePayStatus($paymentId, $status)
    {
        try {
            // 这里需要根据具体的业务模块实现状态更新逻辑
            // 可能需要调用不同业务模块的服务来更新状态

            $this->logger->info(sprintf('更新业务模块支付状态: 支付单ID=%d, 状态=%s', $paymentId, $status));
            return true;
        } catch (Exception $e) {
            $this->logger->error('更新业务模块支付状态失败: ' . $e->getMessage());
            return false;
        }
    }
}