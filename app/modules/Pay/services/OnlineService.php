<?php
namespace App\Modules\Pay\Services;

use App\Library\Enums\GlobalEnums;
use App\Library\Enums\PayEnums;
use App\Modules\Pay\Models\Payment;

/**
 * 在线支付服务层
 * Class OnlineService
 * @package App\Modules\Pay\Services
 */
class OnlineService extends BaseService
{
    private static $instance;
    private function __construct()
    {
    }
    private function __clone()
    {
    }

    /**
     * @return OnlineService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 在“在线支付”增加小红点提示：显示在线支付中，发送状态为“失败”的单据数量
     * @return int
     */
    public function getCount()
    {
        $condition = [
            'flag' => GlobalEnums::PAYMENT_TAB_ONLINE,
            'out_send_status' => PayEnums::PAYMENT_MODULE_PAY_SEND_STATUS_FAILED
        ];

        $builder = $this->modelsManager->createBuilder();
        $builder->from(['p' => Payment::class]);
        $builder = FinalPayService::getInstance()->getCondition($builder, $condition);

        return intval($builder->columns('COUNT(DISTINCT(p.id)) AS total')->getQuery()->getSingleResult()->total);
    }
}
