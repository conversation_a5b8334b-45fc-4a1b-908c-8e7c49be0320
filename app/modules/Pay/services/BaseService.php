<?php

namespace App\Modules\Pay\Services;

use App\Library\Enums;

class BaseService extends \App\Library\BaseService
{
    const LIST_AUDIT_TYPE = 1;//支付列表
    const LIST_APPLY_TYPE = 2;//我的申请
    const LIST_REPLY_TYPE = 3;//意见征询
    const LIST_DATA_TYPE = 4;//数据查询

    //申请人撤回或在线支付-三级审批人撤回
    public static $validate_withdraw = [
        'id' => 'Required|IntGe:1',
        'reason' => 'Required|StrLenGeLe:1,1000',
    ];

    public static $base_validations = [
        'id_batch' => 'Required|Arr|ArrLenGe:1|>>>:id_batch error',
        'id_batch[*]' => 'Required|IntGt:0',
    ];

    public static $validate_batch_submit_first = [
        'id_batch'           => 'Required|Arr|ArrLenGe:1|>>>:id_batch error',
        'is_pay'             => 'Required|IntIn:1,2|>>>:is_pay error',
        'pay_method'         => 'Required|IntIn:1,2,3|>>>:pay_method error',
        'not_pay_reason'     => 'IfIntEq:is_pay,2|Required|StrLenGeLe:1,1000|>>>:not_pay_reason error',
        'pay_date'           => 'IfIntEq:is_pay,1|IfIntEq:pay_method,1|Required|Date',
        'pay_bank_name'      => 'IfIntEq:is_pay,1|IfIntEq:pay_method,2|Required|StrLenGeLe:0,255',
        'pay_bank_account'   => 'IfIntEq:is_pay,1|IfIntEq:pay_method,2|Required|StrLenGeLe:0,255',
        'pay_bank_flow_date' => 'Str',
        'pay_check'          => 'IfIntEq:is_pay,1|IfIntEq:pay_method,3|Required|Arr|ArrLenGeLe:1,50|>>>:pay_check error',
        'pay_remark'         => 'StrLenGeLe:0,1000|>>>:pay_remark  error',
        'attachments'        => 'Arr',
        'note'               => 'StrLenGeLe:0,1000',
    ];

    //校验支付详情
    public static $validate_batch_submit_pay_check = [
        'pay_check'                      => 'Required|Arr|ArrLenGeLe:1,50|>>>:pay_check error',
        'pay_check[*].bank_name'         => 'StrLenGeLe:0,255|>>>:bank_name  error',
        'pay_check[*].amount'            => 'Required|FloatGeLe:0,*****************.00|>>>:amount  error',
        'pay_check[*].check_date'        => 'Required|Date|>>>:check_date  error',
        'pay_check[*].date'              => 'Required|Date|>>>:date  error',
        'pay_check[*].ticket_no'         => 'Required|ByteLenGe:1|>>>:ticket_no  error',
        'pay_check[*].cheque_account_id' => 'Required|Int|>>>:cheque_account_id  error',
        'pay_check[*].currency'          => 'Required|Int|>>>:currency  error',
        'pay_check[*].currency_text'     => 'Required|ByteLenGe:1|>>>:currency_text  error',
        'pay_check[*].payee_account'     => 'StrLenGe:0|>>>:payee_account  error',
        'pay_check[*].payee_name'        => 'Required|ByteLenGe:1|>>>:payee_name  error',
        'pay_check[*].remark'            => 'StrLenGeLe:0,500|>>>:remark  error'
    ];

    public static $validate_batch_submit_second = [
        'id_batch' => 'Required|Arr|ArrLenGe:1|>>>:id_batch error',
        'is_pay'   => 'Required|IntIn:1',
    ];

    public static $validate_batch_submit_third = [
        'id_batch'       => 'Required|Arr|ArrLenGe:1|>>>:id_batch error',
        'is_pay'         => 'Required|IntIn:1,2',
        'pay_method'     => 'Required|IntEq:3',
        'not_pay_reason' => 'IfIntEq:is_pay,2|Required|StrLenGeLe:1,1000',
        'pay_remark'     => 'Str',
        'attachments'    => 'Arr',
        'note'           => 'Str'
    ];

    //由于三级支付人-合并支付规则调整所以这里需要单独定义验证规则从上面的规则衍生出来的
    public static $validate_union_batch_submit = [
        //支票支付方式
        'is_pay' => 'Required|IntIn:1,2',
        'pay_method' => 'Required|IntIn:' . Enums\GlobalEnums::PAYMENT_METHOD_BANK_TRANSFER . ',' . Enums\GlobalEnums::PAYMENT_METHOD_CHECK,
        'not_pay_reason' => 'IfIntEq:is_pay,2|Required|StrLenGeLe:1,1000',
        'pay_remark' => 'Str',
        'attachments' => 'Arr',
        'note' => 'Str',
        'id_batch' => 'IfIntEq:pay_method,' . Enums\GlobalEnums::PAYMENT_METHOD_CHECK . '|Required|Arr|ArrLenGe:1|>>>:id_batch error',
        'bank_batch' => 'IfIntEq:pay_method,' . Enums\GlobalEnums::PAYMENT_METHOD_BANK_TRANSFER . '|Required|Arr|ArrLenGe:1|>>>:bank_batch error',//银行支付方式
        'bank_batch[*].payment_ids' => 'Required|Arr|ArrLenGe:1',//银行支付方式
        'bank_batch[*].payment_ids[*]' => 'Required|IntGe:1',//银行支付方式
        'bank_batch[*].pay_bank_flow_date' => 'Str',//银行流水
    ];

    public static $validate_batch_reject = [
        'id_batch' => 'Required|Arr',
        'reject_reason' => 'StrLenGeLe:1,1000',
    ];
    public static $validate_get_pay = [
        'id' => 'Required|IntGe:1',
        'pay_id' => 'Required|IntGe:1',
    ];

    public static $validate_update_pay = [
        'payment_id' => 'Required|IntGe:1',
        'pay' => 'Required|ArrLenGe:1',
        'pay[*].id' => 'Required|IntGe:1',
        'pay[*].bank_name' => 'Required|StrLenGeLe:0,255',
        'pay[*].bank_account' => 'Required|StrLenGeLe:0,255',
        'pay[*].bank_account_name' => 'Required|StrLenGeLe:0,255',
        'pay[*].bank_address' => 'StrLenGeLe:0,255',
        'pay[*].swift_code' => 'StrLenGeLe:0,255',
    ];

    //批量提交校验
    public static $validate_check_batch_payment = [
        'id_batch' => 'Required|Arr|ArrLenGe:1|>>>:id_batch error'
    ];

    //单个提交校验 没有金额详情校验
    public static $validate_check_submit_payment_null_pay_check = [
        'id'                 => 'Required|IntGt:0',
        'is_pay'             => 'Required|IntIn:1,2',
        'pay_method'         => 'Required|IntIn:1,2,3',
        'not_pay_reason'     => 'IfIntEq:is_pay,2|Required|StrLenGeLe:1,1000',
        'pay_date'           => 'IfIntEq:is_pay,1|IfIntEq:pay_method,1|Required|Date',
        'pay_bank_name'      => 'IfIntEq:is_pay,1|IfIntEq:pay_method,2|Required|StrLenGeLe:0,255',
        'pay_bank_account'   => 'IfIntEq:is_pay,1|IfIntEq:pay_method,2|Required|StrLenGeLe:0,255',
        'pay_bank_flow_date' => 'Str',
        'pay_remark'         => 'Str',
        'attachments'        => 'Arr',
        'note'               => 'Str'
    ];

    //单个提交校验 有金额详情校验
    public static $validate_check_submit_payment_pay_check = [
        'id' => 'Required|IntGt:0',
        'is_pay' => 'Required|IntIn:1,2',
        'pay_method' => 'Required|IntIn:1,2,3',
        'not_pay_reason' => 'IfIntEq:is_pay,2|Required|StrLenGeLe:1,1000',
        'pay_date' =>  'IfIntEq:is_pay,1|IfIntEq:pay_method,1|Required|Date',
        'pay_bank_name' => 'IfIntEq:is_pay,1|IfIntEq:pay_method,2|Required|StrLenGeLe:0,255',
        'pay_bank_account' => 'IfIntEq:is_pay,1|IfIntEq:pay_method,2|Required|StrLenGeLe:0,255',
        'pay_bank_flow_date' => 'Str',
        'pay_remark' => 'Str',
        'attachments' => 'Arr',
        'note'        => 'Str',
        'pay_check'                      => 'IfIntEq:is_pay,1|IfIntEq:pay_method,3|Required|Arr|ArrLenGeLe:1,1000|>>>:pay_check error',
        'pay_check[*].bank_name'         => 'StrLenGeLe:0,255|>>>:bank_name  error',
        'pay_check[*].amount'            => 'Required|FloatGeLe:0,*****************.00|>>>:amount  error',
        'pay_check[*].check_date'        => 'Required|Date|>>>:check_date  error',
        'pay_check[*].date'              => 'Required|Date|>>>:date  error',
        'pay_check[*].ticket_no'         => 'Required|StrLenGe:0|>>>:ticket_no  error',
        'pay_check[*].cheque_account_id' => 'Required|Int|>>>:cheque_account_id  error',
        'pay_check[*].currency'          => 'Required|Int|>>>:currency  error',
        'pay_check[*].currency_text'     => 'Required|StrLenGe:0|>>>:currency_text  error',
        'pay_check[*].payee_account'     => 'StrLenGe:0|>>>:payee_account  error',
        'pay_check[*].payee_name'        => 'Required|StrLenGe:0|>>>:payee_name  error',
        'pay_check[*].remark'            => 'StrLenGeLe:0,500|>>>:remark  error',

    ];

    //补充附件
    public static $validate_attachment = [
        'id' => 'Required|IntGe:1',
        'attachment_arr' => 'Required|Arr|ArrLenGeLe:0,30',
        'attachment_arr[*].bucket_name' => 'StrLenGeLe:0,63',
        'attachment_arr[*].object_key' => 'StrLenGeLe:0,100',
        'attachment_arr[*].file_name' => 'StrLenGeLe:0,200',
    ];


}
