<?php

namespace App\Modules\Pay\Services;

use App\Library\BaseService;
use App\Library\Enums\BankFlowEnums;
use App\Library\Enums\PayEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\FlashPayHelper;
use App\Modules\BankFlow\Models\BankAccountModel;
use App\Modules\Pay\Models\Payment;
use App\Modules\Pay\Services\FinalPayService;
use App\Modules\Third\Services\OaExternalApiService;
use App\Modules\User\Services\UserService;
use App\Library\BaseController;
use Exception;

/**
 * FlashPay支付状态更新通用服务类
 * @description: 提供FlashPay支付状态更新的通用方法，支持SFTP和API两种方式
 * @author: AI
 * @date: 2025-08-18
 */
class FlashPayStatusUpdateService extends BaseService
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    /**
     * 获取单例实例
     * @return FlashPayStatusUpdateService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 更新支付状态 - 通用方法
     * @description: 根据FlashPay返回的交易状态更新支付记录，支持SFTP和API两种方式
     * @param array $params 参数数组
     * @return array 更新结果
     */
    public function updatePaymentStatus($params)
    {
        $result = [
            'success' => true,
            'error'   => '',
        ];

        // 参数验证
        $this->validateParams($params);

        $payment = $params['payment'];
        $transactionStatus = $params['transaction_status'];
        $paymentMethod = $params['payment_method'] ?? 'api'; // 默认API方式
        $logger = $params['logger'] ?? $this->logger;

        // 根据支付方式选择不同的更新策略
        if ($paymentMethod === 'sftp') {
            $result = $this->updateSftpPaymentStatus($params);
        } else {
            $result = $this->updateApiPaymentStatus($params);
        }

        return $result;
    }

    /**
     * 更新SFTP方式的支付状态
     * @description: 处理SFTP方式的支付状态更新逻辑
     * @param array $params 参数数组
     * @return array 更新结果
     */
    private function updateSftpPaymentStatus($params)
    {
        $result = ['success' => true, 'error' => ''];

        $payment                = $params['payment'];
        $transactionStatus      = $params['transaction_status'];
        $transactionBatchNo     = $params['transaction_batch_no'] ?? '';
        $transactionOrderNo     = $params['transaction_order_no'] ?? '';
        $transactionFailReasons = $params['transaction_fail_reasons'] ?? '';

        $updateData = [
            'flashpay_transaction_batch_no' => $transactionBatchNo,
            'flashpay_transaction_order_no' => $transactionOrderNo,
            'updated_at'                    => date('Y-m-d H:i:s'),
        ];

        switch (strtolower($transactionStatus)) {
            case 'success':
                // 支付成功
                $updateData['pay_status']      = PayEnums::PAYMENT_MODULE_PAY_STATUS_PAY;
                $updateData['out_send_status'] = PayEnums::PAYMENT_MODULE_PAY_SEND_STATUS_SUCCESS;
                $updateData['pay_user_id']     = StaffInfoEnums::SUPER_ADMIN_STAFF_ID; // 三级支付人
                $updateData['pay_time']        = date('Y-m-d H:i:s');

                // 更新业务模块支付状态为已支付
                $this->updateBusinessModulePayStatus($payment->payment_id, 'paid');
                break;
            case 'failed':
                // 支付失败
                $updateData['pay_status']           = PayEnums::PAYMENT_MODULE_PAY_STATUS_FLASH_PAY_FAILED;
                $updateData['out_send_status']      = PayEnums::PAYMENT_MODULE_PAY_SEND_STATUS_FAILED;
                $updateData['flashpay_fail_reason'] = $transactionFailReasons;
                break;
            default:
                // 未知状态，发送飞书预警
                $alertMessage = sprintf('FlashPay结果处理：未知交易状态 %s，交易号 %s', $transactionStatus,
                    $payment->oa_trade_no);
                $this->sendFeishuAlert($alertMessage);
                throw new Exception('未知交易状态: ' . $transactionStatus);
        }

        // 更新支付记录
        $payment->assign($updateData);
        if (!$payment->save()) {
            throw new Exception('更新支付记录失败: ' . implode(', ', $payment->getMessages()));
        }

        $this->logger->info(sprintf(
            'FlashPay SFTP支付状态更新成功: 交易号=%s, 状态=%s, 批次号=%s, 订单号=%s',
            $payment->oa_trade_no,
            $transactionStatus,
            $transactionBatchNo,
            $transactionOrderNo
        ));

        return $result;
    }

    /**
     * 更新API方式的支付状态
     * @description: 处理API方式的支付状态更新逻辑
     * @param array $params 参数数组
     * @param object $logger 日志对象
     * @return array 更新结果
     */
    private function updateApiPaymentStatus($params)
    {
        $result = ['success' => true, 'error' => ''];

        $payment           = $params['payment'];
        $paymentData       = $params['payment_data'] ?? [];
        $transactionStatus = $params['transaction_status'] ?? $paymentData['tradeStatus'];

        $zeroTime = gmdate('Y-m-d H:i:s'); // 0时区

        // 交易状态：0交易待支付、2交易处理中、3交易成功、4交易失败、5交易关闭
        $payment->out_trade_status = $transactionStatus;

        if ($transactionStatus == PayEnums::PAYMENT_MODULE_PAY_OUT_TRADE_STATUS_SUCCESS) {
            // 交易成功处理
            $this->handleApiSuccessStatus($payment, $paymentData, $zeroTime);
        } else {
            if (in_array($transactionStatus, [
                PayEnums::PAYMENT_MODULE_PAY_OUT_TRADE_STATUS_FAILED,
                PayEnums::PAYMENT_MODULE_PAY_OUT_TRADE_STATUS_CLOSE,
            ])) {
                // 交易失败或关闭处理
                $this->handleApiFailedStatus($payment, $zeroTime);
            }
        }

        $payment->out_trade_code = $params['payment_result']['code'] ?? 0; // 外部交易code码
        $payment->updated_at     = $zeroTime;                              // 更新时间需要0时区存储

        if (!$payment->save()) {
            throw new Exception('保存支付记录失败: ' . implode(', ', $payment->getMessages()));
        }

        $this->logger->info(sprintf(
            'FlashPay API支付状态更新成功: 交易号=%s, 状态=%s',
            $payment->oa_trade_no ?? '',
            $transactionStatus
        ));

        return $result;
    }

    /**
     * 处理API方式支付成功状态
     * @description: 处理API方式支付成功的状态更新逻辑
     * @param Payment $payment 支付对象
     * @param array $paymentData 支付数据
     * @param string $zeroTime 0时区时间
     * @param object $logger 日志对象
     */
    private function handleApiSuccessStatus($payment, $paymentData, $zeroTime)
    {
        // 交易成功，将该支付状态由待付款（pay支付中）变更为已支付,记录pay交易号、支付时间
        $payment->pay_status = PayEnums::PAYMENT_MODULE_PAY_STATUS_PAY; // 支付状态：已支付
        $payment->out_send_status = PayEnums::PAYMENT_MODULE_PAY_SEND_STATUS_SUCCESS; // 外部交易发送状态:成功
        $payment->pay_bank_flow_date = $paymentData['completeTime'] ?? null; // 银行流水日期=交易完成时间
        $payment->payer_id = $this->getPayer(); // 最终支付人
        $payment->payer_date = $zeroTime; // 最终支付人操作时间需要0时区存储

        $paymentArr = $payment->toArray();
        
        // 只有报销模块支付才需要固化银行id
        if ($payment->oa_type == BankFlowEnums::BANK_FLOW_OA_TYPE_REIMBURSEMENT) {
            $bankAccount = BankAccountModel::findFirst([
                'columns' => 'bank_id',
                'conditions' => 'account = :account:',
                'bind' => ['account' => $payment->pay_bank_account]
            ]);
            if (!empty($bankAccount)) {
                $paymentArr['pay_bank_id'] = $bankAccount->bank_id;
            }
        }

        // 业务数据支付
        $us = new UserService();
        $user = $us->getUserById($payment->payer_id);
        $user = (new BaseController())->format_user($user);

        // V21615 代理支付-异步通知
        if ($paymentArr['oa_type'] == BankFlowEnums::BANK_FLOW_OA_TYPE_AGENCY_PAYMENT) {
            $paymentArr['send_crowd'] = true;
        }
        
        FinalPayService::getInstance()->businessPay($paymentArr, $user);
        OaExternalApiService::getInstance()->addPaymentPushRecord($payment);
    }

    /**
     * 处理API方式支付失败状态
     * @description: 处理API方式支付失败的状态更新逻辑
     * @param Payment $payment 支付对象
     * @param string $zeroTime 0时区时间
     * @param object $logger 日志对象
     */
    private function handleApiFailedStatus($payment, $zeroTime)
    {
        // 交易失败，交易关闭将该支付状态由pay支付中改为pay支付失败
        $payment->pay_status = PayEnums::PAYMENT_MODULE_PAY_STATUS_FLASH_PAY_FAILED; // 支付状态：pay支付失败
        $payment->out_send_status = PayEnums::PAYMENT_MODULE_PAY_SEND_STATUS_FAILED; // 外部交易发送状态:失败
        $payment->payer_id = $this->getPayer(); // 最终支付人
        $payment->payer_date = $zeroTime; // 最终支付人操作时间需要0时区存储
        
        // 调用拒绝方法
        $this->reject($payment, $payment->payer_id);
    }

    /**
     * 参数验证
     * @description: 验证更新支付状态所需的参数
     * @param array $params 参数数组
     * @throws Exception
     */
    private function validateParams($params)
    {
        if (empty($params['payment'])) {
            throw new Exception('支付记录参数不能为空');
        }

        if (empty($params['transaction_status'])) {
            throw new Exception('交易状态参数不能为空');
        }

        $paymentMethod = $params['payment_method'] ?? 'api';
        if (!in_array($paymentMethod, ['api', 'sftp'])) {
            throw new Exception('不支持的支付方式: ' . $paymentMethod);
        }

        // SFTP方式需要额外参数验证
        if ($paymentMethod === 'sftp') {
            if (!isset($params['transaction_batch_no']) || !isset($params['transaction_order_no'])) {
                throw new Exception('SFTP方式需要提供交易批次号和订单号');
            }
        }

        // API方式需要额外参数验证
        if ($paymentMethod === 'api') {
            if (empty($params['payment_data'])) {
                throw new Exception('API方式需要提供支付数据');
            }
        }
    }

    /**
     * 获取pay在线支付人
     * @description: 从pay_module_payer设置中获取第一个三级支付人作为驳回人
     * @return array
     */
    private function getPayer()
    {
        // 从pay_module_payer设置中获取第一个三级支付人作为驳回人
        $payerArr = \App\Modules\Common\Services\EnumsService::getInstance()->getPayModulePayer();
        return array_shift($payerArr);
    }

    /**
     * pay支付失败的单据需要驳回到一级支付人
     * @description: 调用FinalPayService的驳回方法
     * @param object $payment 支付单据对象信息
     * @param array $userId 三级支付人用户id
     * @throws \App\Library\Exception\BusinessException
     */
    private function reject($payment, $userId)
    {
        return FinalPayService::getInstance()->flashPayFailedReject($payment, $userId);
    }

    /**
     * 更新业务模块支付状态
     * @description: 更新业务模块的支付状态
     * @param int $paymentId 支付单ID
     * @param string $status 支付状态
     * @param object $logger 日志对象
     * @return bool
     */
    private function updateBusinessModulePayStatus($paymentId, $status, $logger)
    {
        try {
            // 这里需要根据具体的业务模块实现状态更新逻辑
            // 可能需要调用不同业务模块的服务来更新状态

            $this->logger->info(sprintf('更新业务模块支付状态: 支付单ID=%d, 状态=%s', $paymentId, $status));
            return true;
        } catch (Exception $e) {
            $this->logger->error('更新业务模块支付状态失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 发送飞书预警
     * @description: 发送飞书预警消息
     * @param string $message 预警消息
     * @return bool
     */
    private function sendFeishuAlert($message)
    {
        try {
            return FlashPayHelper::sendNotice($message);
        } catch (Exception $e) {
            $this->logger->error('发送飞书预警失败: ' . $e->getMessage());
            return false;
        }
    }
}
