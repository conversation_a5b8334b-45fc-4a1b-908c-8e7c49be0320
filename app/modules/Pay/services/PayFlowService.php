<?php

namespace App\Modules\Pay\Services;


use App\Library\Enums;
use App\Library\Enums\BankFlowEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\PayEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Models\oa\ChequeAccountBusinessRelModel;
use App\Models\oa\PaymentStoreRentingDetailModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\OrdinaryPayment\Models\OrdinaryPayment;
use App\Modules\OrdinaryPayment\Models\OrdinaryPaymentExtend;
use App\Modules\OrdinaryPayment\Services\BaseService as PaymentBaseService;
use App\Modules\Pay\Models\Payment;
use App\Modules\Pay\Models\PaymentCheck;
use App\Modules\Payment\Services\StoreRentingListService;
use App\Modules\Purchase\Models\PurchasePayment;
use App\Modules\User\Models\AttachModel;
use App\Modules\User\Services\UserService;
use App\Modules\Workflow\Models\WorkflowNodeModel;
use App\Modules\Workflow\Models\WorkflowRequestModel;
use App\Modules\Workflow\Services\AbstractFlowService;
use App\Modules\Workflow\Services\WorkflowCommentService;
use App\Modules\Workflow\Services\WorkflowServiceV2;
use App\Repository\HrStaffRepository;

class PayFlowService extends AbstractFlowService
{

    //通过，外面会包一层SQL
    public function approve($id, $note, $user, $update_data = [])
    {
        $work_req = $this->getRequest($id);
        if (empty($work_req->id)) {
            throw new BusinessException('获取工作流批次失败', ErrCode::$CONTRACT_GET_WORK_REQUEST_ERROR);
        }
        $item = Payment::findFirst(
            [
                'id = :id:',
                'bind' => ['id' => $id]
            ]
        );
        //如果更新数据不为空
        if (!empty($update_data)) {
            $can_edit_field = $this->getCanEditFieldByReq($work_req, $user['id']);
            if (!empty($can_edit_field)) {
                $this->dealEditField($item, $can_edit_field, $update_data, $user);
            }
        }
        //不要跳过的逻辑，submitter_id=0
        $result = (new WorkflowServiceV2())->doApprove($work_req, $user, ['submitter_id'=>0], $note);
        if ($result === false) {
            throw new BusinessException('审批流拒绝失败', ErrCode::$CONTRACT_WORK_FLOW_REJECT_ERROR);
        }
        return $result;
    }

    /**
     * 驳回
     * @param integer $id 单据ID
     * @param string $note 驳回原因
     * @param array $user 驳回人信息组
     * @return bool|mixed
     * @throws BusinessException
     * @throws ValidationException
     */
    public function reject($id, $note, $user)
    {
        $work_req = $this->getRequest($id);
        if (empty($work_req->id)) {
            throw new BusinessException('获取工作流批次失败', ErrCode::$CONTRACT_GET_WORK_REQUEST_ERROR);
        }
        //不要跳过的逻辑，submitter_id=0
        $result = (new WorkflowServiceV2())->doReject($work_req, $user, ['submitter_id' => 0], $note);
        if ($result === false) {
            throw new BusinessException('审批流拒绝失败', ErrCode::$CONTRACT_WORK_FLOW_REJECT_ERROR);
        }
        return true;
    }

    /**
     * 撤销审批
     * @param $id
     * @param $note
     * @param $user
     * @return bool|mixed
     * @throws BusinessException
     * @throws ValidationException
     * @date 2022/3/16
     */
    public function cancel($id, $note, $user)
    {
        $request = $this->getRequest($id);
        if (empty($request->id)) {
            throw new BusinessException('获取工作流失败', ErrCode::$BUSINESS_ERROR);
        }
        if ($request->state != Enums::WF_STATE_PENDING) {
            throw new BusinessException('审批状态错误，不允许进行该操作', ErrCode::$BUSINESS_ERROR);
        }
        $result = (new WorkflowServiceV2())->doCancel($request, $user, ['submitter_id' => 0], $note);
        if ($result === false) {
            throw new BusinessException('审批流撤回失败', ErrCode::$BUSINESS_ERROR);
        }
        return true;
    }

    /**
     * 重新发起审批流
     * @param $item
     * @param $user
     * @return mixed
     * @throws BusinessException
     */
    public function recommit($item, $user)
    {
        $req = $this->getRequest($item->id);
        if (empty($req)) {
            throw new BusinessException('没有找到req=' . $item->id);
        }

        //老的改成被遗弃
        $req->is_abandon = GlobalEnums::WORKFLOW_ABANDON_STATE_YES;
        if ($req->save() === false) {
            throw new BusinessException('原审批流废弃状态更新失败, 原因可能是=' . get_data_object_error_msg($req) . '; 数据=' . json_encode($req->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
        }

        return $this->createRequest($item, $user);
    }

    public function getRequest($id)
    {
        return WorkflowRequestModel::findFirst(
            [
                'biz_type = :type: and biz_value= :lid: and is_abandon = :is_abandon:',
                'bind' => ['type' => Enums::WF_PAY_TYPE, 'lid' => $id, 'is_abandon' => GlobalEnums::WORKFLOW_ABANDON_STATE_NO],
                'order' => 'id desc'
            ]
        );
    }

    /**
     * 获取每条单据的审批流申请记录
     * @param array $ids id 组
     * @return array
     */
    public function getRequestByIds($ids)
    {
        $work_requests = WorkflowRequestModel::find(
            [
                'biz_type = :type: and biz_value in ({lid:array}) and is_abandon = :is_abandon:',
                'bind' => [
                    'type' => Enums::WF_PAY_TYPE,
                    'lid' => $ids,
                    'is_abandon' => GlobalEnums::WORKFLOW_ABANDON_STATE_NO,
                ],
                'order' => 'id desc',
            ]
        );
        return $work_requests ? $work_requests->toArray() : [];
    }

    public function createRequest($item, $user)
    {
        $data = [];
        $data['id'] = $item->id;
        $data['name'] = $item->no . '支付模块审批申请';
        $data['biz_type'] = Enums::WF_PAY_TYPE;
        $data['flow_id'] = $this->getFlowId($item);

        //是申请人id，申请人名字
        $tmp = $this->getUser($item->apply_staff_id);
        //审批日志里面，需要这些数据
        $user = [];
        $user['name'] = $tmp['staff_name'];
        $user['nick_name'] = $tmp['staff_nick_name'];
        $user['department'] = $tmp['department_name'];
        $user['job_title'] = $tmp['job_title_name'];
        $user['id'] = $item->apply_staff_id;
        return (new WorkflowServiceV2())->createRequest($data, $user, []);
    }

    /**
     * 获取flow_id
     * @param null $model
     * @return int
     */
    public function getFlowId($model = null)
    {
        $flow_id = Enums::WF_PAY_TYPE_FLOW;
        if (isset($model->cost_company_id)) {
            $company_ids = EnumsService::getInstance()->getSysDepartmentCompanyIds();
            if (get_country_code() == GlobalEnums::TH_COUNTRY_CODE) {
                if ($model->cost_company_id == $company_ids['FlashExpress']) {
                    $flow_id = Enums::WF_PAY_TYPE_FLASH_EXPRESS;
                } elseif ($model->cost_company_id == $company_ids['FlashFullfillment']) {
                    $flow_id = Enums::WF_PAY_TYPE_FLASH_FULLFILLMENT;
                } elseif ($model->cost_company_id == $company_ids['FlashPay']) {
                    $flow_id = Enums::WF_PAY_TYPE_FLASH_PAY;
                } elseif ($model->cost_company_id == $company_ids['FlashMoney']) {
                    $flow_id = Enums::WF_PAY_TYPE_FLASH_MONEY;
                } elseif ($model->cost_company_id == $company_ids['FlashHr']) {
                    $flow_id = Enums::WF_PAY_TYPE_FLASH_HR;
                } elseif ($model->cost_company_id == $company_ids['FCommerce']) {
                    $flow_id = Enums::WF_PAY_TYPE_FCOMMERCE;
                } elseif (in_array($model->cost_company_id, [$company_ids['FlashThailand'], $company_ids['FlashHolding']])) {
                    $flow_id = Enums::WF_PAY_TYPE_FLASH_THAILAND_HOLDING;
                } elseif ($model->cost_company_id == $company_ids['ShopWinnerCoLtd']) {
                    $flow_id = Enums::WF_PAY_TYPE_SHOP_WINNER;
                } elseif (in_array($model->cost_company_id, [$company_ids['FlashIncorporation'], $company_ids['FlashPico']])) {
                    $flow_id = Enums::WF_PAY_TYPE_FLASH_INCORPORATION_PICO;
                } elseif (in_array($model->cost_company_id, [$company_ids['FlashHomeOperation'], $company_ids['FlashHomeHolding']])) {
                    $flow_id = Enums::WF_PAY_TYPE_FLASH_HOME;
                } else {
                    $flow_id = Enums::WF_PAY_TYPE_FLOW;
                }
            } else {
                //其他国家
                if (in_array($model->cost_company_id, [$company_ids['FlashFullfillment'], $company_ids['FCommerce'], $company_ids['FlashPay']])) {
                    $flow_id = Enums::WF_PAY_TYPE_FLOW_FFM;
                }
            }
        }
        return $flow_id;
    }

    public function getUser($apply_staff_id)
    {
        return (new WorkflowServiceV2())->getUserInfo($apply_staff_id);
    }


    /**
     * 处理修改字段
     * @param $item
     * @param $can_edit_field
     * @param $update_data
     * @param $user
     * @return bool
     * @throws BusinessException
     */
    function dealEditField($item, $can_edit_field, $update_data, $user)
    {
        $before = [];
        $after = [];
        $attachments = [];
        //发票支付方式～新发票数据
        $check_data = [];
        //发票支付方式～旧发票数据
        $old_checks_list = [];
        $db = $this->getDI()->get('db_oa');
        if (!empty($can_edit_field['main'])) {
            foreach ($can_edit_field['main'] as $key) {
                if (isset($update_data[$key])) {
                    //发票是个json
                    if ($key == 'pay_check') {
                        /**
                         * 13784【MY|OA|银行流水】 银行流水及支付模块迁移
                         * https://flashexpress.feishu.cn/docx/doxcn8oZcw7n1PUJRdbcOuHdeTd
                         * 将pay_check由支付方式存储为表记录
                         */
                        $check_data = $update_data[$key];
                        $old_checks_list = $item->getChecks();
                        $before[$key] = json_encode(!empty($old_checks_list) ? $old_checks_list->toArray() : [], JSON_UNESCAPED_UNICODE);
                        $after[$key] = json_encode($update_data[$key], JSON_UNESCAPED_UNICODE);
                    } elseif ($item->$key != $update_data[$key]) {
                        //其他字段编辑还是直接存储到payment表
                        $before[$key] = $item->$key;
                        $update_data[$key] = ($key == 'pay_bank_flow_date' && empty($update_data[$key])) ? null : $update_data[$key];
                        $after[$key] = $update_data[$key];
                        $item->$key = $update_data[$key];
                    }
                }
            }
            if (!empty($after)) {
                $item->save();
            }

            if (!empty($check_data)) {
                //先将原来的数据删除
                $old_checks_arr = $old_checks_list->toArray();
                if (!empty($old_checks_arr)) {
                    $ids                  = implode(',', array_column($old_checks_arr, 'id'));
                    $update_payment_check = $db->updateAsDict(
                        (new PaymentCheck())->getSource(),
                        [
                            'is_deleted' => GlobalEnums::IS_DELETED,
                            'updated_at' => date('Y-m-d H:i:s', time())
                        ],
                        [
                            'conditions' => " id IN ($ids)",
                        ]
                    );
                    if ($update_payment_check === false) {
                        throw new BusinessException('支票数据情况老数据失败, 数据: ' . json_encode($old_checks_arr, JSON_UNESCAPED_UNICODE) . '; 原因可能是: ' . get_data_object_error_msg($update_payment_check), ErrCode::$BUSINESS_ERROR);
                    }
                }
                //16325需求需要区分是否对接了支票模块
                if (PayService::getInstance()->checkChequeIsOpen()) {
                    //对接支票模块，走以下逻辑，只是加了开关代码逻辑无任何改动；
                    if (isset($update_data['is_batch']) && !empty($update_data['is_batch']) && !empty($update_data['pay_check']) ) {
                        $check_data_arr = [];
                        $pays           = $item->getPays();
                        $pays_arr       = $pays->toArray();
                        if (!empty($pays_arr)) {
                            $check_item = [];
                            foreach ($pays_arr as $item_pays) {
                                $check_item['payment_id']        = $item->id;
                                $check_item['payment_pay_id']    = $item_pays['id'];
                                $check_item['ticket_no']         = $check_data[0]['ticket_no'];
                                $check_item['cheque_account_id'] = $check_data[0]['cheque_account_id'];
                                $check_item['date']              = $check_data[0]['date'];
                                $check_item['amount']            = $item_pays['amount'];
                                $check_item['currency']          = $check_data[0]['currency'];
                                $check_item['currency_text']     = $check_data[0]['currency_text'];
                                $check_item['payee_name']        = $check_data[0]['payee_name'];
                                $check_item['bank_name']         = $item_pays['bank_name'];
                                $check_item['check_date']        = $check_data[0]['check_date'];
                                $check_item['remark']            = $check_data[0]['remark'];
                                $check_item['payee_account']     = $check_data[0]['payee_account'];
                                $check_data_arr[]                = $check_item;
                            }
                        }
                        $check_data = $check_data_arr;
                    }

                    if (!empty($update_data['pay_check'])) {
                        $business_rel = [];
                        $current_time = date('Y-m-d H:i:s', time());
                        foreach ($check_data as $payment_check) {
                            $payment_check_obj        = new PaymentCheck();
                            $tmp                      = [];
                            $tmp['payment_id']        = $payment_check['payment_id'];
                            $tmp['payment_pay_id']    = $payment_check['payment_pay_id'];
                            $tmp['ticket_no']         = $payment_check['ticket_no'] ?? '';
                            $tmp['cheque_account_id'] = $payment_check['cheque_account_id'];
                            $tmp['date']              = $payment_check['date'];
                            $tmp['amount']            = $payment_check['amount'];
                            $tmp['currency']          = $payment_check['currency'];
                            $tmp['currency_text']     = $payment_check['currency_text'];
                            $tmp['payee_name']        = $payment_check['payee_name'];
                            $tmp['bank_name']         = $payment_check['bank_name'];
                            $tmp['check_date']        = $payment_check['check_date'];
                            $tmp['remark']            = $payment_check['remark'];
                            $tmp['is_deleted']        = GlobalEnums::IS_NO_DELETED;
                            $tmp['created_at']        = $current_time;
                            $tmp['updated_at']        = $current_time;
                            $payment_check_add        = $payment_check_obj->create($tmp);
                            if ($payment_check_add) {
                                if (!empty($payment_check['ticket_no'])) {
                                    //根据业务获取商户号
                                    $supplier_no = '';
                                    if ($item->oa_type == BankFlowEnums::BANK_FLOW_OA_TYPE_PURCHASE_PAYMENT) {

                                        $purchase_payment_model = PurchasePayment::findFirst([
                                            'ppno = :ppno:',
                                            'bind' => ['ppno' => $item->no],
                                        ]);
                                        $supplier_no            = $purchase_payment_model->vendor_id ?? '';

                                    } else if ($item->oa_type == BankFlowEnums::BANK_FLOW_OA_TYPE_ORDINARY_PAYMENT) {

                                        $ordinary_payment_model = OrdinaryPayment::findFirst([
                                            'apply_no = :apply_no:',
                                            'bind' => ['apply_no' => $item->no],
                                        ]);
                                        if (!empty($ordinary_payment_model) && $ordinary_payment_model->payee_type == PaymentBaseService::PAYEE_TYPE_1) {
                                            $ordinary_payment_extend = OrdinaryPaymentExtend::findFirst([
                                                'ordinary_payment_id = :ordinary_payment_id:',
                                                'bind' => ['ordinary_payment_id' => $ordinary_payment_model->id],
                                            ]);
                                            $supplier_no             = $ordinary_payment_extend->supplier_id ?? '';
                                        }
                                    }
                                    $rel                         = [];
                                    $cheque_account_business_rel = new ChequeAccountBusinessRelModel();
                                    $rel['cheque_account_id']    = $payment_check['cheque_account_id'];
                                    $rel['payment_id']           = $payment_check['payment_id'];
                                    $rel['payment_check_id']     = $payment_check_obj->id;
                                    $rel['oa_biz_no']            = $item->no;
                                    $rel['oa_type']              = $item->oa_type;
                                    $rel['payee_name']           = $payment_check['payee_name'];
                                    $rel['payee_account']        = $payment_check['payee_account'];
                                    $rel['supplier_no']          = $supplier_no;
                                    $rel['created_at']           = $current_time;
                                    if (!$cheque_account_business_rel->create($rel)) {
                                        throw new BusinessException('写入 cheque_account_business_rel表 失败: ' . json_encode($business_rel, JSON_UNESCAPED_UNICODE) . '; 原因可能是: ' . get_data_object_error_msg($cheque_account_business_rel), ErrCode::$BUSINESS_ERROR);
                                    }
                                }
                            } else {
                                throw new BusinessException('支票-支付管理-我的支付-操作失败' . json_encode(['check_code_arr' => $tmp, 'save_cheque_data' => $payment_check], JSON_UNESCAPED_UNICODE), ErrCode::$CHEQUE_ACCOUNT_UPDATE_ERROR);
                            }
                        }
                    }
                } else {
                    //未对接支付模块，填写了发票流水信息，记录发票流水信息
                    $payment_check = new PaymentCheck();
                    $payment_check->batch_insert($check_data);
                }
            }
        }

        //只有附件，有可能清空，所以每次都删除，再添加
        if (!empty($can_edit_field['meta'])) {
            $files = $item->getFiles();
            if (!empty($files)) {
                foreach ($files as $file) {
                    $file->deleted = 1;
                    $file->save();
                }
            }
            if (!empty($update_data['attachments'])) {
                $attachments = [];
                foreach ($update_data['attachments'] as $attachment) {
                    $tmp = [];
                    $tmp['oss_bucket_type'] = Enums::OSS_BUCKET_TYPE_PAY_MODULE;
                    $tmp['oss_bucket_key'] = $item->id;
                    $tmp['bucket_name'] = $attachment['bucket_name'];
                    $tmp['object_key'] = $attachment['object_key'];
                    $tmp['file_name'] = $attachment['file_name'];
                    $attachments[] = $tmp;
                }
                $attach_bool = (new AttachModel())->batch_insert($attachments);
                if ($attach_bool === false) {
                    throw new BusinessException('浮夸模块-附件创建失败', ErrCode::$CONTRACT_CREATE_ERROR);
                }
            }
        }

        $this->logger->info(
            "付款模块==单号==" . $item->no . "===before==" . json_encode(
                $before,
                JSON_UNESCAPED_UNICODE
            ) . "===after==" . json_encode($after, JSON_UNESCAPED_UNICODE) . "===attachments==" . json_encode(
                $attachments
            ) . "===" . $user['id']
        );


        return true;
    }

    /**
     * 获取支付模块审批日志
     *
     * @param $payment_data
     * @param bool $if_download
     * @return array
     * @date 2022/3/12
     */
    public function getAuditLogs($payment_data, $if_download = false)
    {
        //查询审批数据
        $payment_id = $payment_data['id'];
        $req = $this->getRequest($payment_id);
        $auth_logs = (new WorkflowServiceV2())->getAuditLogs($req);
        // 下载的时候不要申请
        if ($if_download) {
            $temp = [];
            foreach ($auth_logs as $k => $v) {
                // 如果申请的就跳过
                if ($v['action'] == 0) {
                    continue;
                }

                $temp[] = $v;
            }

            $auth_logs = $temp;
        }

        /**
         * 第一级审批首次审批时, 待审核改为待支付 , 二级驳回到一级的,一级显示 支付中
         * 第一级往后的待审核改为 支付中
         * 终审通过显示 已支付/未支付
         * 驳回正常显示
         */
        $reverse_log = array_reverse($auth_logs);
        $log_level = 1;
        foreach ($reverse_log as &$item) {
            //申请节点不做特殊处理
            if ($item['action'] == 0) {
                continue;
            }

            //第一个节点待审核改为待支付
            if ($log_level == 1 && $item['action'] == 3) {
                $item['action_name'] = static::$t->_(PayEnums::$payment_module_pay_status[PayEnums::PAYMENT_MODULE_PAY_STATUS_PENDING]);
            }

            //后续节点待审核改为支付中
            if ($log_level > 1 && $item['action'] == 3) {
                $item['action_name'] = static::$t->_(PayEnums::$payment_module_pay_status[PayEnums::PAYMENT_MODULE_PAY_STATUS_ING]);
            }

            //撤回显示 未支付
            if ($item['action'] == 6) {
                $item['action_name'] = static::$t->_(PayEnums::$payment_module_pay_status[PayEnums::PAYMENT_MODULE_PAY_STATUS_NOTPAY]);
            }

            $log_level += 1;
        }
        $auth_logs = array_reverse($reverse_log);

        //审批通过最后一级 拼接第三级支付人信息
        if ($req->state == 3) {
            //前两级通过, 第三级支付时申请人撤回, 只修改状态, 所以撤回能走到这个分支
            //"银行支付中"显示"银行支付中","支付失败"显示"支付失败","未支付"(申请人撤回)显示"未支付"
            //$first_value = array_shift($auth_logs);
            $us = new UserService();
            if (in_array($payment_data['pay_status'], [PayEnums::PAYMENT_MODULE_PAY_STATUS_BANKING, PayEnums::PAYMENT_MODULE_PAY_STATUS_FLASH_PAY_ING])) {
                $pay_staff_id = EnumsService::getInstance()->getPayModulePayer();
                $first_value = [
                    'action_name' => static::$t->_(PayEnums::$payment_module_pay_status[$payment_data['pay_status']]),
                    'audit_at' => '',
                    'audit_at_datetime' => '',
                    'action' => 5,
                    'info' => '',
                    'list' => []
                ];

                if (!empty($pay_staff_id)) {
                    foreach ($pay_staff_id as $staff_id) {
                        $current = $us->getUserById($staff_id);
                        if (!empty($current)) {
                            $first_value['list'][] = [
                                'staff_id' => $staff_id,
                                'staff_name' => $this->getNameAndNickName($current->name, $current->nick_name ?? ''),
                                'staff_department' => $current->getDepartment()->name ?? '',
                                'job_title' => $current->getJobTitle()->name ?? ''
                            ];
                        }
                    }
                }
            } else {
                $current = $us->getUserById($payment_data['payer_id']);
                $audit_date = !empty($payment_data['payer_date']) ? show_time_zone($payment_data['payer_date']) : '';
                $first_value = [
                    'action_name' => static::$t->_(PayEnums::$payment_module_pay_status[$payment_data['pay_status']]),
                    'staff_id' => $payment_data['payer_id'],
                    'staff_name' => '',
                    'staff_department' => '',
                    'job_title' => '',
                    'audit_at' => $audit_date,
                    'audit_at_datetime' => $audit_date,
                    'action' => 5,
                    "info" => ''
                ];
                if (!empty($current)) {
                    $first_value['staff_name'] = $this->getNameAndNickName($current->name, $current->nick_name ?? '');
                    $first_value['staff_department'] = $current->getDepartment()->name ?? '';
                    $first_value['job_title'] = $current->getJobTitle()->name ?? '';
                }
            }

            // 第三级支付人的评论
            $first_value['comment_list'] = WorkflowCommentService::getInstance()->getLogsByNodeId($req->id, $req->current_flow_node_id);

            array_unshift($auth_logs, $first_value);
        }

        return $auth_logs;
    }

    /**
     * 批量获取业务数据当前审批节点上,当前用户的审批级别
     * ( 如果不是当前审批人,则不判定级别 eg: 当前处于3级审批节点,$user_id是1级审批人和3级审批人,此处返回3不返回1, 因为要对应弹框中3级审批人的特有字段而不应该是1级)
     * @param $business_ids
     * @param $user_id
     * @date 2022/3/17
     * @return array
     * @throws BusinessException
     */
    public function getAuditLevelById(array $business_ids, $user_id)
    {
        //查询审批数据
        $workflow_request_array = $this->getRequestByIds($business_ids);
        //$node_level[node] = level 当前员工在此节点的审批级别, 避免重复查询
        $node_level = [];
        //$business_level[$business_id] = level 存储结果集, 当前用户在每条业务数据[当前审批节点]中的级别
        $business_level = [];
        //查询审批数据中包含的所有审批流节点配置(用来判断当前节点级别
        //所有审批当前审批节点
        foreach ($workflow_request_array as $one_request) {
            $current_node_auditor_id = explode(',', $one_request['current_node_auditor_id']);
            if (in_array($user_id, $current_node_auditor_id)) {
                //此审批流此节点的级别
                if (!isset($node_level[$one_request['current_flow_node_id']])) {
                    $node_level[$one_request['current_flow_node_id']] = $this->getNodeLevel($one_request['flow_id'], $one_request['current_flow_node_id']);
                }
                $business_level[$one_request['biz_value']] = $node_level[$one_request['current_flow_node_id']];
            }else{
                $business_level[$one_request['biz_value']] = 0;
            }
        }
        return $business_level;
    }

    /**
     * 查询此审批节点level
     * @param $flow_id
     * @param $node //要判断审批级别的节点
     * @param int $last_node_id  //递归传递的节点id
     * @param int $level 0无级别 1.一级 2.二级 以此类推
     * @return int
     * @throws BusinessException
     * @date 2022/3/17
     */
    public function getNodeLevel($flow_id, $node, $last_node_id = 0, $level = 1)
    {
        $wf = new WorkflowServiceV2();
        if (empty($last_node_id)) {
            //查询申请节点
            $first_node = WorkflowNodeModel::findFirst([
                'conditions' => 'flow_id = :flow_id: and type = 1',
                'bind' => ['flow_id' => $flow_id]
            ]);
            if (!$first_node) {
                return 0;
            }
            $last_node_id = $first_node->id;
        }
        //下一节点
        $next_node_id = $wf->getNextNode($flow_id, $last_node_id, []);
        //无下一节点不继续递归
        if (empty($next_node_id)) {
            return 0;
        }
        if ($next_node_id == $node) {
            return $level;
        } else {
            return $this->getNodeLevel($flow_id, $node, $next_node_id, $level + 1);
        }
    }

    /**
     * 获取一级审批人
     * @param $flow_id
     * @return int|mixed
     * @throws BusinessException
     * @date 2022/3/18
     */
    public function getFirstNode($flow_id)
    {
        $wf = new WorkflowServiceV2();
        //查询申请节点
        $apply_node = WorkflowNodeModel::findFirst([
            'conditions' => 'flow_id = :flow_id: and type = 1',
            'bind' => ['flow_id' => $flow_id]
        ]);
        if (!$apply_node) {
            return 0;
        }
        $last_node_id = $apply_node->id;
        //下一节点
        $next_node_id = $wf->getNextNode($flow_id, $last_node_id, []);
        $next_node = WorkflowNodeModel::findFirst([
            'conditions' => 'id = :id:',
            'bind' => ['id' => $next_node_id]
        ]);
        if (!$next_node) {
            return 0;
        }
        return $next_node;
    }

    /**
     * 审批流状态通过视为到达三级支付人(审批流调整为二级)
     * @date 2022/4/19
     * @param $business_ids
     * @return bool
     */
    public function checkThirdAudit($business_ids){
        //审批通过视为到达第三级审批人
        $workflow_request_array = $this->getRequestByIds($business_ids);
        if(count($workflow_request_array) < count($business_ids)){
            $is_all_third = false;
        }else{
            $is_all_third = true;
            foreach ($workflow_request_array as $v){
                if ($v['state'] != Enums::WF_STATE_APPROVED){
                    $is_all_third = false;
                    break;
                }
            }
        }


        return $is_all_third;
    }

    /**
     * 银行流水上传批量修改支付状态
     *
     * @date 2022/4/19
     * @param $user
     * @param $type_id //模块id
     * @param array $nos //单号
     * @return bool
     * @throws ValidationException
     * @throws \Exception
     */
    public function autoUpdatePayStatus($user, $type_id, array $nos)
    {
        $is_update_payment = get_country_code() == GlobalEnums::PH_COUNTRY_CODE && in_array($type_id, array_keys(BankFlowEnums::$cheque_account_type_relationship));
        //如果是支票工本费，直接返回
        if ($type_id == BankFlowEnums::BANK_FLOW_OA_TYPE_CHEQUE_APPLY || $is_update_payment) {
            return true;
        }

        if (in_array($type_id, array_keys(BankFlowEnums::$cheque_account_type_relationship))) {
            $cheque_arr = [];
            foreach ($nos as $no) {
                $cheque       = explode('_', $no);
                $cheque_arr[] = $cheque[0];
            }
            $type_id = BankFlowEnums::$cheque_account_type_relationship[$type_id];
            $nos     = StoreRentingListService::getInstance()->getPaymentStoreRentingApplyNo($cheque_arr);
            $nos     = array_column($nos, 'oa_biz_no');
            $nos     = array_values(array_unique($nos));
            if (empty($nos)) {
                return true;
            }
        }

        //校验单号的申请状态=支付中，且到达第三支付节点
        $payment_data = Payment::find([
            'conditions' => 'oa_type = :oa_type: and no in ({nos:array})',
            'bind' => ['oa_type' => $type_id, 'nos' => $nos],
        ])->toArray();
        //支付数据中没有的是老数据,有多少验证多少
        if (!empty($payment_data)) {
            //验证支付状态必须是"银行支付中"
            $ids = [];
            foreach ($payment_data as $v) {
                if ($v['pay_status'] != PayEnums::PAYMENT_MODULE_PAY_STATUS_BANKING) {
                    throw new ValidationException(self::$t->_('payment_data_pay_status_not_paying'), ErrCode::$VALIDATE_ERROR);
                }
                $ids[] = $v['id'];
            }
            //验证审批到达第三节点
            if (!$this->checkThirdAudit($ids)) {
                throw new ValidationException(self::$t->_('payment_data_audit_not_third'), ErrCode::$VALIDATE_ERROR);
            }
            //符合条件更新数据
            $db = $this->getDI()->get('db_oa');
            try {
                $db->begin();
                //更新状态为撤回
                $payment_model = new Payment();
                $update_data = [
                    'pay_status' => PayEnums::PAYMENT_MODULE_PAY_STATUS_PAY,
                    'payer_id' => $user['id'],
                    'payer_date' => gmdate('Y-m-d H:i:s'),
                ];
                $ids = implode(',', $ids);
                $update_success = $db->updateAsDict(
                    $payment_model->getSource(),
                    $update_data,
                    ['conditions' => "id in ({$ids})"]
                );
                if (!$update_success) {
                    throw new BusinessException('支付模块更新支付状态失败=', ErrCode::$PAYMENT_UPDATE_PAY_STATUS_ERROR);
                }
                $db->commit();
            } catch (\Exception $e) {
                $db->rollback();
                throw $e;
            }
        }
        return true;
    }

    /**
     * 取消支付
     * @param $type_id
     * @param $no
     * @param $item
     * @return bool
     * @throws \Exception
     * @date 2022/5/30
     */
    public function cancelPay($type_id, $no, $item)
    {
        $is_update_payment = get_country_code() == GlobalEnums::PH_COUNTRY_CODE && in_array($type_id, array_keys(BankFlowEnums::$cheque_account_type_relationship));
        if ($type_id == BankFlowEnums::BANK_FLOW_OA_TYPE_CHEQUE_APPLY || $is_update_payment) {
            return true;
        }
        if (in_array($type_id, array_keys(BankFlowEnums::$cheque_account_type_relationship))) {
            if ($type_id == BankFlowEnums::BANK_FLOW_OA_TYPE_CHEQUE_ACCOUNT && get_country_code() == GlobalEnums::MY_COUNTRY_CODE) {
                // 如果是支票租房付款撤销 必须是全部撤销 才可撤回支付单状态
                $detail = PaymentStoreRentingDetailModel::findFirst([
                    'conditions' => 'id = :id:',
                    'bind'       => ['id' => $item->oa_value]
                ]);
                //如果详情里面有已支付的，就不能撤回支付模块，只有全部是待支付的时候才会撤回支付模块
                //注意此处是事务，当前的这条数据已经被前面修改，所以不用考虑当前数据是否是已支付的
                $detail_arr = PaymentStoreRentingDetailModel::find([
                    'conditions' => 'store_renting_id = :store_renting_id: and pay_status =:pay_status:',
                    'bind'       => ['store_renting_id' => $detail->store_renting_id, 'pay_status' => 2]
                ])->toArray();

                if (!empty($detail_arr)) {
                    return true;
                }
            }

            $type_id = BankFlowEnums::$cheque_account_type_relationship[$type_id];
            //通过支票号到台账找到对应的业务号
            $nos = StoreRentingListService::getInstance()->getPaymentStoreRentingApplyNo([$no]);
            $nos = array_column($nos, 'oa_biz_no');
            //一个业务号可以关联两个相同的支票号 反之查询也是，所以此处取一个即可
            $no = array_values($nos)[0];
            if (empty($no)) {
                return true;
            }
        }

        //校验单号的申请状态=已支付
        $payment_data = Payment::findFirst([
            'conditions' => 'oa_type = :oa_type: and no = :no:',
            'bind' => ['oa_type' => $type_id, 'no' => $no],
        ]);
        if ($payment_data) {
            $payment_data = $payment_data->toArray();
            if ($payment_data['pay_status'] != PayEnums::PAYMENT_MODULE_PAY_STATUS_PAY) {
                return false;
            }
            $db = $this->getDI()->get('db_oa');
            try {
                $db->begin();
                //更新状态为撤回
                $payment_model = new Payment();
                $update_data = [
                    'pay_status' => PayEnums::PAYMENT_MODULE_PAY_STATUS_BANKING,
                    'payer_id' => null,
                    'payer_date' => null,
                ];
                $update_success = $db->updateAsDict(
                    $payment_model->getSource(),
                    $update_data,
                    ['conditions' => 'id=?', 'bind' => $payment_data['id']]
                );
                if (!$update_success) {
                    throw new BusinessException("支付模块更新支付状态失败=", ErrCode::$PAYMENT_UPDATE_PAY_STATUS_ERROR);
                }
                $db->commit();
            } catch (\Exception $e) {
                $db->rollback();
                throw $e;
            }
        }
        return true;
    }

    /**
     * 自动改成未支付
     * @param $no
     * @param $reason
     * @return array
     */
    public function autoNonPay($no, $reason)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $db = $this->getDI()->get('db_oa');

        try {
            //校验单号的申请状态=已支付
            $item = Payment::findFirst([
                'conditions' => 'no = :no:',
                'bind' => ['no' => $no],
            ]);
            if (!$item) {
                throw new BusinessException('支付单据不存在, 单号=' . $no, ErrCode::$BUSINESS_ERROR);
            }
            if (in_array($item->pay_status, [PayEnums::PAYMENT_MODULE_PAY_STATUS_PAY, PayEnums::PAYMENT_MODULE_PAY_STATUS_NOTPAY])) {
                throw new BusinessException('支付单据状态已经是终态', ErrCode::$BUSINESS_ERROR);
            }
            //如果是支付审批中, 自动驳回
            $work_req = $this->getRequest($item->id);
            if (!$work_req) {
                throw new BusinessException('支付单据没有审批流', ErrCode::$BUSINESS_ERROR);
            }
            $db->begin();
            if ($work_req->state == Enums::WF_STATE_PENDING) {
                //审批中 说明是一二级审批人
                if (empty($work_req->current_node_auditor_id)) {
                    throw new BusinessException('支付单据审批流当前没有审批人', ErrCode::$BUSINESS_ERROR);
                }
                //获取工号最小的审批人
                $pay_staff_id = 0;
                $staff_repository = new HrStaffRepository();
                $pay_staff = explode(',', $work_req->current_node_auditor_id);
                foreach ($pay_staff as $staff_id) {
                    $staff_info = $staff_repository->getStaffById($staff_id);
                    if (isset($staff_info['state']) && $staff_info['state'] == StaffInfoEnums::STAFF_STATE_IN && $staff_info['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_NO) {
                        $pay_staff_id = $staff_id;
                        break;
                    }
                }
                if (empty($pay_staff_id)) {
                    throw new BusinessException('支付单据驳回审批流失败, 没有可用的审批人: request_id=' . $work_req->id . '; 审批人=' . $work_req->current_node_auditor_id, ErrCode::$BUSINESS_ERROR);
                }
                //给$user赋值
                $us = new UserService();
                $user = $us->getLoginUser($pay_staff_id);
                $this->reject($item->id, $reason, $user);
            }
            //支付模块支付人
            $pay_module_user = FinalPayService::getInstance()->getPayStaff('pay_module_payer');
            if (empty($pay_module_user)) {
                throw new BusinessException('业务单据支付否失败, 没有三级支付人', ErrCode::$BUSINESS_ERROR);
            }
            //如果审批通过, 说明到三级支付人了, 要给三级支付人赋值
            if ($work_req->state == Enums::WF_STATE_APPROVED) {
                $item->payer_id = $pay_module_user['id'];
                $item->payer_date = gmdate('Y-m-d H:i:s');
            }
            //更新支付模块支付状态为未支付
            $item->is_pay = PayEnums::IS_PAY_NO;
            $item->not_pay_reason = $reason;
            $item->pay_status = PayEnums::PAYMENT_MODULE_PAY_STATUS_NOTPAY;
            $item->updated_at = date('Y-m-d H:i:s');
            if ($item->save() === false) {
                throw new BusinessException('支付单据更新失败, 数据是:'. json_encode($item->toArray(), JSON_UNESCAPED_UNICODE) .'; 可能的原因是' . get_data_object_error_msg($item), ErrCode::$BUSINESS_ERROR);
            }
            //更新业务模块
            $payment_data = $item->toArray();
            //业务数据未支付, 用配置的支付人操作
            //进入支付模块的单据
            $pay_result = FinalPayService::getInstance()->businessPay($payment_data, $pay_module_user);
            if (!$pay_result) {
                throw new BusinessException('业务单据支付否失败, 返回=' . $pay_result, ErrCode::$BUSINESS_ERROR);
            }
            $db->commit();

        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            //业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
            $this->logger->warning('autoNonPay执行异常: 单号=' . $no . '; massage=' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => [],
        ];
    }

}
