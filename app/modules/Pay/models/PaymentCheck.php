<?php
namespace App\Modules\Pay\Models;

use App\Models\Base;

class PaymentCheck extends Base
{

    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        // setSource()方法指定数据库表
        $this->setSource('payment_check');
        // bank_name不能为空导致软删除失败,跳过它
        $this->skipAttributesOnUpdate(
            [
                'bank_name',
            ]
        );
    }
}
