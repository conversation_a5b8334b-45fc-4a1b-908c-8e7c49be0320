<?php


namespace App\Modules\Extinguisher\Models;


use App\Library\BaseModel;

class SettingEnvModel extends BaseModel
{
    public function initialize()
    {
        $this->setConnectionService('db_backyard');
        $this->setSource('setting_env');
    }

    public static function getEnvByCode($code, $default = '')
    {
        $item = static::findFirst(
            [
                'conditions' => 'code=:code:',
                'bind' => ['code' => $code]
            ]
        );

        if (empty($item)) {
            return $default;
        }
        return $item->set_val;
    }
}