
<!doctype HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=no">
    <style>
        body {
            font: 3.2mm/1.2 Tahoma, sans-serif;
            color: #000;
            -webkit-print-color-adjust: exact;
        }
        body, ul, p {
            margin: 0;
            padding: 0;
        }
        ul, li {
            list-style: none;
        }
        .clearfix::after {
            display: block;
            content: "";
            clear: both;
        }
        .break-after {
            page-break-after: always;
            page-break-inside: avoid;
        }
        .tal {
            text-align: left!important;
        }
        .table {
            width: 100%;
            border-left: .5mm solid #666;
            border-right: .5mm solid #666;
            border-collapse: collapse;
            border-spacing: 0;
        }
        .table th, .table td {
            border: .3mm solid #999;
            padding: 2mm;
            text-align: center;
            word-break: break-all;
        }
        .table .bt {
            border-top: .5mm solid #666;
        }
        .table .bb {
            border-bottom: .5mm solid #666;
        }
        .table .cell-title {
            font-weight: 600;
            text-align: left;
        }
        .borrow-wrap {
            width: 210mm;
            height: 297mm;
            padding: 4mm;
            margin-bottom: 4mm;
            box-sizing: border-box;
        }
    </style>
</head>

<body>
<div class="borrow-wrap">
    <table class="table">
        <tr class="bt" style="font-size:3.4mm; font-weight:600;">
            <td colspan="3" style="text-align: center"><?=$t['wa_field_title']  ?></td>
            <td><?=$t['re_field_id']  ?></td>
            <td colspan="2"><?=$no ?></td>
        </tr>
        <tr>
            <th colspan="6"><?=$t['re_field_base_info']  ?></th>
        </tr>
        <tr>
            <td><?=$t['re_field_apply_id']  ?></td>
            <td colspan="2"><?=$apply_id  ?></td>
            <td><?=$t['re_field_apply_name']  ?></td>
            <td colspan="2"><?=$apply_name  ?></td>
        </tr>
        <tr>
            <td><?=$t['re_field_apply_company_name']  ?></td>
            <td colspan="2"><?=$apply_company_name  ?></td>
            <td><?=$t['re_field_apply_department_name']  ?></td>
            <td colspan="2"><?=$apply_department_name  ?></td>
        </tr>
        <tr>
            <td><?=$t['re_field_apply_store_name']  ?></td>
            <td colspan="2"><?=$apply_store_name  ?></td>
            <td><?=$t['re_field_apply_center_code']  ?></td>
            <td colspan="2"><?=$apply_center_code  ?></td>
        </tr>
        <tr>
            <td><?=$t['global.apply.date']  ?></td>
            <td colspan="2"><?=$apply_date  ?></td>
            <td><?=$t['exp_pay_date']  ?></td>
            <td colspan="2"><?=$exp_pay_date_min ?> - <?=$exp_pay_date_max?></td>
        </tr>
        <tr>
            <th colspan="6"><?=$t['payment_info']?></th>
        </tr>
        <tr>
            <td><?=$t['payment_method']  ?></td>
            <td colspan="2"><?=$pay_method_text ?></td>
            <td><?=$t['re_field_currency_text'] ?></td>
            <td colspan="2"><?=$pay_currency_text ?></td>
        </tr>
        <tr>
            <td style="width: 20%"><?=$t['re_field_bank_account']  ?></td>
            <td style="width: 15%"><?=$payee_no?></td>
            <td style="width: 15%"><?=$t['re_field_bank_name'] ?></td>
            <td style="width: 20%"><?=$payee_name  ?></td>
            <td style="width: 15%"><?=$t['re_field_bank_type'] ?></td>
            <td style="width: 15%"><?=$payee_bank ?></td>
        </tr>
        <tr>
            <th colspan="6"><?=$t['expense_info']  ?></th>
        </tr>
        <tr>
            <td><?=$t['expense_company']  ?></td>
            <td colspan="2"><?=$exp_company_name?></td>
            <td><?=$t['expense_type']  ?></td>
            <td colspan="2"><?=$item_type_text?></td>
        </tr>
        <tr>
            <td><?=$t['expense_time']  ?></td>
            <td colspan="2"><?=$term_start?> -- <?=$term_end?></td>
            <td><?=$t['re_field_category_b']?></td>
            <td colspan="2"><?=$item_detail?></td>
        </tr>
        <tr>
            <th colspan="6"></th>
        </tr>
        <tr>
            <th colspan="2"><?=$t['re_field_invoices_ids']?></th>
            <th><?=$t['re_field_detail_amount']?></th>
            <th><?=$t['re_field_rate']  ?></th>
            <th><?=$t['re_field_tax']?></th>
            <th><?=$t['re_field_tax_not']?></th>
        </tr>
        <tr>
            <td colspan="2"><?=$invoice_no  ?></td>
            <td><?=number_format($amount,2)?></td>
            <td><?=$tax_rate?></td>
            <td><?=number_format($tax,2)?></td>
            <td><?=number_format($amount_without_tax,2)?></td>
        </tr>
        <tr>
            <th colspan="2"><?=$t['wages_wht_category'] ?></th>
            <th colspan="2"><?=$t['wages_category_tax'] ?></th>
            <th colspan="2"><?=$t['wages_wht_amount'] ?></th>
        </tr>
        <tr>
            <td colspan="2"><?=$wht_category ?></td>
            <td colspan="2"><?=$wht_tax_rate ?></td>
            <td colspan="2"><?=number_format($wht_amount,2) ?></td>
        </tr>
        <tr>
            <td><?=$t['re_field_amount']  ?></td>
            <td colspan="2"><?=number_format($amount,2)?></td>
            <td><?=$t['re_field_loan_amount']?></td>
            <td colspan="2">0.00</td>
        </tr>
        <tr>
            <td><?=$t['re_field_other_amount']  ?></td>
            <td colspan="2">0.00</td>
            <td><?=$t['re_field_real_amount']?></td>
            <td colspan="2"><?=number_format($actually_amount,2) ?></td>
        </tr>
        <tr>
            <th colspan="6"><?=$t['re_field_auth_logs']  ?></th>
        </tr>
        <tr>
            <td><?=$t['re_field_step']  ?></td>
            <td ><?=$t['re_field_finished_at']  ?></td>
            <td ><?=$t['re_field_deal_id']  ?></td>
            <td><?=$t['re_field_deal_name']  ?></td>
            <td ><?=$t['re_field_deal_res']  ?></td>
            <td ><?=$t['re_field_deal_mark']  ?></td>
        </tr>
        <?php
        foreach ($auth_logs as $k=>$item){
            echo "<tr>";
                echo "<td>".$t['re_field_approve'].($k+1)."</td>";
                echo "<td>".$item['audit_at_datetime']."</td>";
                echo "<td>".$item['staff_id']."</td>";
                echo "<td>".$item['staff_name']."</td>";
                echo "<td>".$item['action_name']."</td>";
                echo "<td>".$item['info']."</td>";
            echo "</tr>";
        }
        ?>
        <tr>
            <th colspan="6"><?=$t['payer_info']?></th>
        </tr>
        <tr>
            <td><?=$t['payer_bank']?></td>
            <td colspan="2"><?=$payer_bank?></td>
            <td><?=$t['payer_no']  ?></td>
            <td colspan="2"><?=$payer_no?></td>
        </tr>
        <tr>
            <td><?=$t['pay_date']  ?></td>
            <td colspan="2"><?=$pay_date?></td>
            <td><?=$t['signatory']  ?></td>
            <td colspan="2"><?=$apply_name?></td>
        </tr>
        <tr class="bb">
            <td><?=$t['remark']  ?></td>
            <td colspan="5"><?=$pay_remark?></td>
        </tr>
    </table>
</div>
</body>
</html>