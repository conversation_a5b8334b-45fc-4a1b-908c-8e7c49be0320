<?php

namespace App\Modules\Wages\Services;

use App\Modules\Common\Models\EnvModel;

class BaseService extends \App\Library\BaseService
{
    public function getWagesPayStaffIds(){
        $pay_staff_id = EnvModel::getEnvByCode('wages_pay_staff_id','54255,54249,33306,28989,26808,24905,24904,24902,23116,21958,19432,17348,17178,32739,20254');
        $pay_staff_idArr = explode(",",$pay_staff_id);

        return $pay_staff_idArr?? [];
    }

}
