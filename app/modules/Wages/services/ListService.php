<?php

namespace App\Modules\Wages\Services;

use App\Library\Enums;
use App\Library\Enums\PayEnums;
use App\Library\Enums\DownloadCenterEnum;
use App\Library\Exception\BusinessException;
use App\Modules\Wages\Models\WagesModel;
use App\Library\Enums\GlobalEnums;
use App\Modules\Workflow\Services\FYRService;
use App\Modules\Workflow\Services\WorkflowServiceV2;
use GuzzleHttp\Exception\GuzzleException;

class ListService extends BaseService
{
    const LIST_TYPE_DATA = 0;
    const LIST_TYPE_APPLY = 1;
    const LIST_TYPE_AUDIT = 2;
    const LIST_TYPE_FYR = 3;
    const LIST_TYPE_PAY = 4;


    protected $list_columns =  [
        'w.id',
        'w.no',
        'w.apply_id',
        'w.apply_name',
        'w.apply_company_name',
        'w.apply_department_id',
        'w.apply_department_name',
        'w.apply_store_id',
        'w.apply_store_name',
        'w.apply_center_code',
        'w.apply_date',
        'w.exp_pay_date_min',
        'w.exp_pay_date_max',
        'w.pay_method',
        'w.pay_currency',
        'w.payee_name',
        'w.payee_no',
        'w.payee_bank',
        'w.item_type',
        'w.item_detail',
        'w.term_start',
        'w.term_end',
        'w.invoice_no',
        'w.amount_without_tax',
        'w.tax',
        'w.amount',
        'w.tax_rate',
        'w.created_id',
        'w.created_at',
        'w.status',
        'w.pay_status',
        'w.payer_id',
        'w.payer_bank',
        'w.payer_no',
        'w.pay_date',
        'w.pay_remark',
        'w.payed_at',
        'w.pay_from',
        'w.wht_category',
        'w.wht_tax_rate',
        'w.wht_amount',
        'w.actually_amount',
        'w.exp_company_id',
        'w.exp_company_name',
    ];


    public function handleExportData($params,$staff_info_id): array
    {
        $params['pageNum']  = DownloadCenterEnum::INIT_PAGE_NUM;
        $params['pageSize'] = DownloadCenterEnum::DEFAUL_PER_PAGE_SIZE;
        $excel_data = [];
        // 2.3获取数据
        while (true) {
            $list = $this->getExportList($params,$staff_info_id);
            if (empty($list)) {
                break;
            }

            foreach ($list as $v) {
                $excel_data[] = [
                    $v['no'], // 编号
                    $v['apply_date'], // 申请日期
                    $v['apply_name'], // 申请人姓名
                    $v['apply_id'], // 申请人ID
                    self::$t->_(GlobalEnums::$payment_method_item[$v['pay_method']]), // 付款方式
                    $v['pay_method'] == GlobalEnums::PAYMENT_METHOD_BANK_TRANSFER ? $v['payee_no'] : '', // 收款人账号
                    $v['payee_bank'], // 收款人开户银行
                    $v['apply_department_name'], // 申请人所属部门
                    $v['exp_company_name'], // 费用所属公司
                    $v['item_type_text'], // 费用类型
                    $v['item_detail'], // 费用明细
                    $v['tax'], // 税额
                    $v['amount_without_tax'] . ' ' .$v['pay_currency_text'], // 不含税金额
                    $v['tax_rate'], // 税率
                    $v['amount'], //  含税金额
                    $v['wht_category'], // wht类型
                    is_numeric($v['wht_tax_rate']) ? $v['wht_tax_rate'] . '%' : '', // wht税率
                    $v['wht_amount'], // wht金额
                    $v['actually_amount'], // 实付金额
                    $v['status_text'], // 申请状态
                    $v['pay_status_text'], // 支付状态
                    empty($v['pay_date']) ? '' : $v['pay_date'], // 付款日期
                ];
            }
            $params['pageNum']++;
        }
        return $excel_data;
    }



    /**
     * @param $condition
     * @param $uid
     * @param int $type
     * @return array
     */
    public function getExportList($condition, $uid = null, $type = 0)
    {
        $condition['uid'] = $uid;
        $page_size = empty($condition['pageSize']) ? 20 : $condition['pageSize'];
        $page_num = empty($condition['pageNum']) ? 1 : $condition['pageNum'];
        $offset = $page_size * ($page_num - 1);

        $columns = $this->list_columns;

        // 审核模块的已处理列表, 展示处理时间
        if ($type == self::LIST_TYPE_AUDIT && isset($condition['flag']) && $condition['flag'] == GlobalEnums::AUDIT_TAB_PROCESSED) {
            $columns[] = 'log.audit_at';
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->from(['w' => WagesModel::class]);
        $builder = $this->getCondition($builder, $condition, $type);

        $builder->columns($columns);
        $builder->groupBy('w.id');

        if(!in_array($type, [self::LIST_TYPE_AUDIT, self::LIST_TYPE_FYR])) {
            $builder->orderBy('w.id desc');
        }

        $builder->limit($page_size, $offset);
        $items = $builder->getQuery()->execute()->toArray();
        if(empty($items)){
            return [];
        }
        return $this->handleItems($items);

    }

    /**
     * @param $condition
     * @param $uid
     * @param int $type
     * @return array
     */
    public function getList($condition, $uid = null, $type = 0)
    {
        $condition['uid'] = $uid;
        $page_size = empty($condition['pageSize']) ? 20 : $condition['pageSize'];
        $page_num = empty($condition['pageNum']) ? 1 : $condition['pageNum'];
        $offset = $page_size * ($page_num - 1);

        $columns  = $this->list_columns;
        // 审核模块的已处理列表, 展示处理时间
        if ($type == self::LIST_TYPE_AUDIT && isset($condition['flag']) && $condition['flag'] == GlobalEnums::AUDIT_TAB_PROCESSED) {
            $columns[] = 'log.audit_at';
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->from(['w' => WagesModel::class]);
        $builder = $this->getCondition($builder, $condition, $type);

        $count = (int) $builder->columns('COUNT(DISTINCT w.id) AS total')->getQuery()->getSingleResult()->total;

        $items = [];
        if ($count) {
            $builder->columns($columns);
            $builder->groupBy('w.id');

            if(!in_array($type, [self::LIST_TYPE_AUDIT, self::LIST_TYPE_FYR])) {
                $builder->orderBy('w.id desc');
            }

            $builder->limit($page_size, $offset);
            $items = $builder->getQuery()->execute()->toArray();
            $items = $this->handleItems($items);
        }

        return [
            'items' => $items,
            'pagination' => [
                'current_page' => $page_num,
                'per_page' => $page_size,
                'total_count' => $count,
            ]
        ];
    }

    /**
     * @param $builder
     * @param $condition
     * @param $type
     * @return mixed
     */
    public function getCondition($builder, $condition, $type)
    {
        $no = $condition['no'] ?? '';
        $status = $condition['status'] ?? 0;
        $pay_status = $condition['pay_status'] ?? 0;

        $apply_date_start = $condition['apply_date_start'] ?? '';
        $apply_date_end = $condition['apply_date_end'] ?? '';
        $apply_id = $condition['apply_id'] ?? 0;//申请人工号
        $item_type = $condition['item_type'] ?? [];//费用类型

        // 征询回复进度
        $is_reply = $condition['is_reply'] ?? GlobalEnums::CONSULTED_REPLY_STATE_PENDING;
        $is_reply = in_array($is_reply, GlobalEnums::CONSULTED_REPLY_STATE_ITEM) ? $is_reply : GlobalEnums::CONSULTED_REPLY_STATE_PENDING;

        // 审批处理进度
        $flag = $condition['flag'] ?? GlobalEnums::AUDIT_TAB_PENDING;
        $flag = in_array($flag, GlobalEnums::AUDIT_TAB_STATE_ITEM) ? $flag : GlobalEnums::AUDIT_TAB_PENDING;

        $builder->andWhere('w.execute_status = :execute_status:', ['execute_status' => Enums\WagesEnums::WAGES_EXECUTE_STATUS_SUCCESS]);
        if (!empty($no)) {
            $builder->andWhere('w.no = :no:', ['no' => $no]);
        }

        if (!empty($status)) {
            $builder->andWhere('w.status = :status:', ['status' => $status]);
        }

        if (!empty($pay_status)) {
            $builder->andWhere('w.pay_status = :pay_status:', ['pay_status' => $pay_status]);
        }

        if (!empty($apply_date_start)) {
            $builder->andWhere('w.apply_date >= :apply_date_start:', ['apply_date_start' => $apply_date_start]);
        }

        if (!empty($apply_date_end)) {
            $builder->andWhere('w.apply_date <= :apply_date_end:', ['apply_date_end' => $apply_date_end]);
        }

        if ($type == self::LIST_TYPE_AUDIT) {
            $builder = (new WorkflowServiceV2())->getBizWorkflowOrderListByProcessState($builder, $flag, [Enums::WF_WAGES_TYPE], $condition['uid'], 'w');

        } else if ($type == self::LIST_TYPE_APPLY) {
            $builder->andWhere('w.created_id = :uid:', ['uid' => $condition['uid']]);

        } else if ($type == self::LIST_TYPE_FYR) {
            // 意见征询回复列表 'pay_status_field_name'
            $biz_table_info = ['table_alias' => 'w', 'pay_status_field_name' => ''];
            $builder = (new FYRService())->getBizOrderConsultationReplyListByProcessState($builder, $is_reply, [Enums::WF_WAGES_TYPE], $condition['uid'], $biz_table_info);

        } else if ($type == self::LIST_TYPE_PAY) {
            $builder->andWhere('w.status = :status: and w.is_pay_module = :is_pay_module:', ['status' => Enums::WF_STATE_APPROVED, 'is_pay_module' => PayEnums::BIZ_DATA_IS_PAY_MODULE_NO]);

            if ($pay_status == Enums::LOAN_PAY_STATUS_PAY || $pay_status == Enums::LOAN_PAY_STATUS_NOTPAY){
                $builder->andWhere('w.payer_id = :payer_id:',['payer_id'=>$condition['uid']]);
            }
        }
        if (!empty($apply_id)) {
            $builder->andWhere('w.apply_id = :apply_id:', ['apply_id' => $condition['apply_id']]);
        }
        if (!empty($item_type) && is_array($item_type)) {
            $builder->inWhere('w.item_type', $item_type);
        }
        //费用所属公司
        if (!empty($condition['exp_company_id'])) {
            if (is_array($condition['exp_company_id'])) {
                $builder->andWhere('w.exp_company_id IN ({exp_company_id:array})', ['exp_company_id' => array_values($condition['exp_company_id'])]);
            } else {
                $builder->andWhere('w.exp_company_id = :exp_company_id:', ['exp_company_id' => $condition['exp_company_id']]);
            }
        }
        //付款日期
        if (!empty($condition['pay_date_start']) && !empty($condition['pay_date_end'])) {
            $builder->andWhere('w.pay_date >= :pay_date_start: and w.pay_date <= :pay_date_end:', ['pay_date_start' => $condition['pay_date_start'], 'pay_date_end' => $condition['pay_date_end']]);
        }

        return $builder;
    }

    /**
     * @param $items
     * @return array
     */
    public function handleItems($items)
    {
        if (empty($items)) {
            return [];
        }

        foreach ($items as &$item) {
            $status = Enums::$loan_status[$item['status']] ?? '';
            $pay_status = Enums::$loan_pay_status[$item['pay_status']] ?? '';
            $pay_currency = GlobalEnums::$currency_item[$item['pay_currency']];
            $item['status_text'] = static::$t->_($status);
            $item['pay_status_text'] = static::$t->_($pay_status);
            $item['item_type_text'] = static::$t->_('item_type.'.$item['item_type']);
            $item['pay_currency_text'] = static::$t->t($pay_currency);
            $item['pay_date'] = empty($item['pay_date']) ? '' : $item['pay_date'];
        }

        return $items;
    }


    public function getExportExcelHeaderFields(): array
    {
        return [
            static::$t->t('re_field_id'), // 编号
            static::$t->t('global.apply.date'), // 申请日期
            static::$t->t('re_field_apply_name'), // 申请人姓名
            static::$t->t('re_field_apply_id'), // 申请人ID
            static::$t->t('payment_method'), // 付款方式
            static::$t->t('re_field_bank_account'), // 收款人账号
            static::$t->t('re_field_bank_type'), // 收款人开户银行
            static::$t->t('re_field_apply_department_name'), // 申请人所属部门
            static::$t->t('expense_company'), // 费用所属公司
            static::$t->t('expense_type'), // 费用类型
            static::$t->t('re_field_category_b'), // 费用明细
            static::$t->t('re_field_tax'), // 税额
            static::$t->t('purchase_order_product_field_total_price'), // 不含税金额
            static::$t->t('re_field_rate'), // 税率
            static::$t->t('purchase_order_product_field_all_total'), // 含税金额
            static::$t->t('purchase_product_field_wht_type'), // WHT类别
            static::$t->t('purchase_product_field_wht_ratio'), // WHT税率
            static::$t->t('wages_wht_amount'), // WHT 金额
            static::$t->t('payment_store_renting_actually_amount'), // 实付金额
            static::$t->t('global.apply.status.text'), //  申请状态
            static::$t->t('global_pay_status'), // 支付状态
            static::$t->t('pay_date'), // 付款日期
        ];
    }

}
