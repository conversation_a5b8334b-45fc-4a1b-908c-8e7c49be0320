<?php


namespace App\Modules\Wages\Services;


use App\Library\Enums;
use App\Library\Enums\WagesEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\SysConfigEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Pay\Services\PayService;
use App\Modules\Wages\Models\WagesModel;
use App\Modules\Workflow\Models\WorkflowNodeModel;
use App\Modules\Workflow\Services\AbstractFlowService;
use App\Modules\Workflow\Services\WorkflowServiceV2;

class WagesFlowService extends AbstractFlowService
{

    /**
     * @param $id
     * @param $note
     * @param $user
     * @param $data
     * @return array
     */
    public function approve($id, $note, $user, $data = [])
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            $db = $this->getDI()->get('db_oa');

            $db->begin();
            $work_req = $this->getRequest($id);
            if (empty($work_req->id)) {
                throw new BusinessException('获取工作流批次失败', ErrCode::$BUSINESS_ERROR);
            }
            $item = WagesModel::findFirst([
                'id = :id:',
                'bind' => ['id' => $id]
            ]);
            if ($item->status != Enums::WF_STATE_PENDING) {
                throw new ValidationException(static::$t->_('workflow_process_do_failed'), ErrCode::$BUSINESS_ERROR);
            }

            //判断是否可编辑的审批节点
            $node = WorkflowNodeModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $work_req->current_flow_node_id]
            ]);
            if(!empty($node->can_edit_field)) {
                $update_date = [];

                $wht_tax_config = EnumsService::getInstance()->getWhtRateMap();

                //可编辑字段：WHT类别/WHT税率/WHT金额/实付金额
                $wht_category_id = $data['wht_category_id'];
                if (empty($wht_tax_config[$wht_category_id])) {
                    throw new ValidationException(static::$t->_('payment_upload_error_007'), ErrCode::$VALIDATE_ERROR);
                }

                if (empty($wht_tax_config[$wht_category_id]['rate_list'][$data['wht_tax_rate_id']])) {
                    throw new ValidationException(static::$t->_('payment_upload_error_008'), ErrCode::$VALIDATE_ERROR);
                }

                $update_date['wht_category'] =  $wht_tax_config[$wht_category_id]['label'];
                $update_date['wht_tax_rate'] =  $data['wht_tax_rate_id'];
                $update_date['wht_amount'] = $data['wht_amount'];
                $update_date['actually_amount'] = $data['actually_amount'];
                if(empty($data['wht_amount']) || empty($data['actually_amount'])) {
                    $update_date['wht_amount'] = $item->amount_without_tax * ($update_date['wht_tax_rate']/100);
                    $update_date['actually_amount'] = $item->amount - $update_date['wht_amount'];
                }

                $after = [
                    'wht_category' => $item->wht_category,
                    'wht_tax_rate' => $item->wht_tax_rate,
                    'wht_amount' => $item->wht_amount,
                    'actually_amount' => $item->actually_amount
                ];
                $update_result = $item->i_update($update_date);
                if($update_result === false) {
                    throw new BusinessException('数据更新失败', ErrCode::$BUSINESS_ERROR);
                }
                $info = [
                    'no' => $item->no,
                    'apply_id' => $item->apply_id,
                    'operator' => $user['id'],
                    'before' => $after,
                    'after' => $update_date
                ];

                $this->logger->info('薪酬扣款审批AP审核编辑数据:'.json_encode($info));
            }

            $result = (new WorkflowServiceV2())->doApprove($work_req, $user, $this->getWorkflowParams($item, $user), $note);
            if (!empty($result->approved_at)) {
                $bool = $item->i_update([
                    'status' => Enums::CONTRACT_STATUS_APPROVAL,
                    'updated_at' => date('Y-m-d H:i:s'),
                    'approved_at' => $result->approved_at,
                ]);
                if ($bool === false) {
                    throw new BusinessException('数据更新失败', ErrCode::$BUSINESS_ERROR);
                }else{

                    $pay_staff_idArr = (new BaseService())->getWagesPayStaffIds();

                    if(!empty($pay_staff_idArr)){
                        $this->sendEmailToAuditors($work_req,$pay_staff_idArr,1);
                        $this->delUnReadNumsKeyByStaffIds($pay_staff_idArr);
                    }

                    //16325需求，泰国需要将薪酬扣款审批通过的申请同步数据到支付模块
                    if(EnumsService::getInstance()->getPayModuleStatus(SysConfigEnums::SYS_MODULE_SALARY, $item->exp_company_id)) {
                        PayService::getInstance()->saveOne($item);
                    }
                }
            }

            $db->commit();
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = $real_message = $e->getMessage();

        } catch (\Exception $e){
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->warning(__METHOD__ . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
        ];
    }

    /**
     * @param $id
     * @param $note
     * @param $user
     * @return array
     */
    public function reject($id, $note, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            $db = $this->getDI()->get('db_oa');
            $db->begin();
            $work_req = $this->getRequest($id);
            if (empty($work_req->id)) {
                throw new BusinessException('获取审批流失败', ErrCode::$CONTRACT_GET_WORK_REQUEST_ERROR);
            }
            $item = WagesModel::findFirst([
                'id = :id:',
                'bind' => ['id' => $id]
            ]);
            if ($item->status != Enums::WF_STATE_PENDING) {
                throw new BusinessException('申请状态错误', ErrCode::$BUSINESS_ERROR);
            }
            $result = (new WorkflowServiceV2())->doReject($work_req, $user, $this->getWorkflowParams($item, $user), $note);
            if ($result === false) {
                throw new BusinessException('审批流拒绝失败', ErrCode::$CONTRACT_WORK_FLOW_REJECT_ERROR);
            }
            $bool = $item->i_update([
                'status' => Enums::CONTRACT_STATUS_REJECTED,
                'updated_at' => date('Y-m-d H:i:s'),
                'remark' => $note,
                'pay_status'=> Enums::LOAN_PAY_STATUS_NOTPAY
            ]);
            if ($bool === false) {
                throw new BusinessException('数据更新失败', ErrCode::$CONTRACT_UPDATE_ERROR);
            }
            $db->commit();
        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->warning(__METHOD__ . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
        ];
    }

    /**
     * @param $id
     * @param $note
     * @param $user
     * @return array
     */
    public function cancel($id, $note, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';

        try {
            $db = $this->getDI()->get('db_oa');
            $db->begin();
            $work_req = $this->getRequest($id);

            $item = WagesModel::findFirst([
                'id = :id:',
                'bind' => ['id' => $id]
            ]);
            if (empty($item)) {
                throw new BusinessException(static::$t->_('contract_get_info_failed_when_update'), ErrCode::$CONTRACT_GET_INFO_ERROR);
            }
            if ($item->status != Enums::WF_STATE_PENDING) {
                throw new BusinessException('申请状态错误', ErrCode::$BUSINESS_ERROR);
            }
            $result = (new WorkflowServiceV2())->doCancel($work_req, $user, $this->getWorkflowParams($item, $user), $note);
            if ($result === false) {
                throw new BusinessException('审批流拒绝失败', ErrCode::$CONTRACT_WORK_FLOW_REJECT_ERROR);
            }
            $bool = $item->i_update([
                'status' => Enums::WF_STATE_CANCEL,
                'updated_at' => date('Y-m-d H:i:s'),
                'remark' => $note,
                'pay_status' => Enums::LOAN_PAY_STATUS_NOTPAY
            ]);
            if ($bool === false) {
                throw new BusinessException(static::$t->_('contract_withdrawal_failed'), ErrCode::$CONTRACT_CANCEL_ERROR);
            }
            $db->commit();
        } catch (BusinessException $e) {               //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {                       //系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning(__METHOD__ . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
        ];
    }

    /**
     * @param $id
     * @return \Phalcon\Mvc\Model
     */
    public function getRequest($id)
    {
        return $this->getRequestByBiz($id, Enums::WF_WAGES_TYPE);
    }

    /**
     * @param $id
     * @param $user
     * @return \Phalcon\Mvc\Model\Resultset|\Phalcon\Mvc\Phalcon\Mvc\Model
     * @throws BusinessException
     */
    public function createRequest($id, $user)
    {
        $item = WagesModel::findFirst([
            'id = :id:',
            'bind' => ['id' => $id]
        ]);

        $data['id'] = $item->id;
        $data['name'] = $item->no . '审批申请';
        $data['biz_type'] = Enums::WF_WAGES_TYPE;
        $data['flow_id'] = $this->getFlowId($item);
        return (new WorkflowServiceV2())->createRequest($data, $user, $this->getWorkflowParams($item, $user));
    }

    /**
     * 获取审批流需要数据
     * @param $item
     * @param $user
     * @return array
     */
    public function getWorkflowParams($item, $user)
    {
        return [
            'amount'             => $item->amount,
            'currency'           => $item->pay_currency,
            'submitter_id'       => $item->apply_id,
            'department_id'      => $item->apply_department_id,
            'node_department_id' => $item->apply_department_id,
            'store_id'           => $item->apply_store_id,
            'create_staff_id'    => $item->created_id,
            'KEY'                => $item->item_type,
        ];
    }

    /**
     * @description 获取审批流id
     * @param null $model
     * @return int
     */
    public function getFlowId($model = null): int
    {
        if ($model->apply_type == WagesEnums::APPLY_TYPE_CROWD_SOURCING) {
            return Enums::WF_FLOW_ID_WAGES_APPLY_CROWD_SOURCING;
        } else if ($model->apply_type == WagesEnums::APPLY_TYPE_PERSONAL_AGENT) {
            return Enums::WF_FLOW_ID_WAGES_APPLY_PERSONAL_AGENT;
        }
        $company_ids = EnumsService::getInstance()->getSysDepartmentCompanyIds();
        $country_code = get_country_code();
        if ($country_code == GlobalEnums::TH_COUNTRY_CODE) {
            if (empty($model) || $model->exp_company_id == $company_ids['FlashExpress']) {
                //FlashExpress
                return Enums::WF_FLOW_ID_WAGES_APPLY_FLASH_EXPRESS;
            } else if ($model->exp_company_id == $company_ids['FlashHomeOperation']) {
                //Flash Home Operation
                return Enums::WF_FLOW_ID_WAGES_APPLY_FLASH_HOME_OPERATION;
            } else {
                //其他公司
                return Enums::WF_FLOW_ID_WAGES_APPLY_OTHERS;
            }
        } else {
            if (empty($model) || $model->exp_company_id == $company_ids['FlashExpress']) {
                //FlashExpress
                return Enums::WF_FLOW_ID_WAGES_APPLY_FLASH_EXPRESS;
            } else {
                //其他公司
                return Enums::WF_FLOW_ID_WAGES_APPLY_OTHERS;
            }
        }
    }
}
