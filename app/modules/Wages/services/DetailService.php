<?php


namespace App\Modules\Wages\Services;


use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\PayEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\OssHelper;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Common\Services\WaterMarkerService;
use App\Modules\Pay\Services\PayFlowService;
use App\Modules\Pay\Services\PayService;
use App\Modules\User\Services\UserService;
use App\Modules\Wages\Models\WagesModel;
use App\Modules\Workflow\Models\WorkflowNodeModel;
use App\Modules\Workflow\Services\FYRService;
use App\Modules\Workflow\Services\WorkflowServiceV2;
use GuzzleHttp\Exception\GuzzleException;
use Mpdf\Mpdf;
use Mpdf\MpdfException;
use Mpdf\Output\Destination;
use App\Library\Enums\BankFlowEnums;

class DetailService extends BaseService
{

    /**
     * @param $id
     * @param $uid
     * @param $isDownload
     * @return array
     * @throws BusinessException
     */
    public function  getDetail($id,$uid,$isDownload=false)
    {
        $item = WagesModel::findFirst([
            'id = :id:',
            'bind' => ['id' => $id]
        ]);

        if (empty($item)) {
            return [];
        }

        $req = (new WagesFlowService())->getRequest($id);
        if (empty($req->id)) {
            throw new BusinessException('获取工作流批次失败');
        }

        $data = $item->toArray();

        //判断节点值是否可以修改
        $data['is_edit'] = 0;
        $node = WorkflowNodeModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $req->current_flow_node_id]
            ]);
        if(!empty($node->can_edit_field)) {
            $data['is_edit'] = 1;
        }

        $data['payment_attachments'] = $item->getPaymentAttachments();
        $data['item_attachments'] = $item->getItemAttachments();
        //待回复征询ID
        $ask = (new FYRService())->getRequestToByReplyAsk($req,$uid);
        $data['ask_id'] = $ask ? $ask->id:'';
        $data['auth_logs'] = $this->getAuditLogs($req, $item, $isDownload);

        $data = $this->handleData($data);
        return $data;
    }

    /**
     * @param $data
     * @return mixed
     */
    public function handleData($data)
    {
        $wht_tax_rate_item = EnumsService::getInstance()->getWhtRateMap(0);
        $wht_category = EnumsService::getInstance()->getWhtRateCategoryMap(0, true);

        $data['pay_method_text'] = static::$t->t('global.payment.method.'.$data['pay_method']);
        $data['pay_currency_text'] = static::$t->t('payment_currency.'.$data['pay_currency']);
        $data['item_type_text'] = static::$t->_('item_type.'.$data['item_type']);

        $data['wht_category_id'] = $wht_category[$data['wht_category']] ?? 0;

        $wht_tax = $wht_tax_rate_item[$data['wht_category_id']]['rate_list'] ?? [];
        $data['wht_tax_rate_id'] = array_key_exists((string) $data['wht_tax_rate'], $wht_tax) ? $data['wht_tax_rate'] : '';
        $data['wht_tax_rate'] = is_numeric($data['wht_tax_rate']) ? $data['wht_tax_rate'] . '%' : '';

        return $data;
    }
    /**
     * @param $req
     * @param $item
     * @param bool $isDownload
     * @return array
     */
    private function getAuditLogs($req, $item, $isDownload = false)
    {
        $auth_logs = (new WorkflowServiceV2())->getAuditLogs($req);
        //下载的时候不要申请
        if ($isDownload) {
            $temp = [];
            foreach ($auth_logs as $k => $v) {
                //如果申请的就跳过
                if ($v['action'] == 0) {
                    continue;
                }
                $temp[] = $v;
            }
            $auth_logs = $temp;
        }

        //查询支付模块的审批流
        if ($item->is_pay_module == PayEnums::BIZ_DATA_IS_PAY_MODULE_YES) {
            $payment_data = PayService::getInstance()->getPaymentByBusinessNo(BankFlowEnums::BANK_FLOW_OA_TYPE_WAGE, $item->no);
            if (!empty($payment_data)) {
                $pay_flow_service = new PayFlowService();
                $payment_audit_logs = $pay_flow_service->getAuditLogs($payment_data, true);
                $auth_logs = array_merge($payment_audit_logs, $auth_logs);
                return $auth_logs;
            }
        }

        //下载不要支付的
        if (!$isDownload && $item->status == Enums::CONTRACT_STATUS_APPROVAL && ($item->pay_status == Enums::LOAN_PAY_STATUS_PAY || $item->pay_status == Enums::LOAN_PAY_STATUS_NOTPAY)) {
            $us = new UserService();
            $current = $us->getUserById($item->payer_id);
            //放入付款
            if ($current && !is_string($current)) {
                $payLogs = [
                    'staff_id' => $item->payer_id,
                    'staff_name' => $current->name,
                    'staff_department' => $current->getDepartment()->name ?? '',
                    'job_title' => $current->getJobTitle()->name ??'',
                    'action_name' => self::$t->_(Enums::$loan_pay_status[$item->pay_status]),
                    'audit_at' => $item->payed_at,
                    'audit_at_datetime' => $item->payed_at,
                    'action' => 5,
                    "info" => $item->pay_remark
                ];
                array_unshift($auth_logs, $payLogs);
            }
        }
        //下载要正序
        if($isDownload){
            $auth_logs = array_reverse($auth_logs);
        }
        return $auth_logs;
    }

    /**
     * @param $id
     * @param $uid
     * @param $lang
     * @return array
     * @throws BusinessException
     * @throws GuzzleException
     * @throws MpdfException
     */
    public function download($id, $uid,$lang)
    {
        self::setLanguage($lang);
        $data = $this->getDetail($id, $uid,true);

        $data['wht_category'] = empty($data['wht_category']) ? '-' : $data['wht_category'];
        $data['wht_tax_rate'] = empty($data['wht_tax_rate']) ? '-' : $data['wht_tax_rate'];
        $data['wht_amount'] = empty($data['wht_amount']) ? '-' : $data['wht_amount'];
        $data['actually_amount'] = empty($data['actually_amount']) ? $data['amount'] : $data['actually_amount'];

        $file_path = sys_get_temp_dir()."/wages-".md5($id) . '_' . date('ymdHis') .".pdf";

        $view = $this->view;
        $view->setVar('t',self::getTranslation($lang));
        $view->setVars($data);
        $view->start();
        $view->render('pdf',"wage");
        $view->finish();
        $content = $view->getContent();
        $mpdf = new Mpdf([
            'format' => 'A4',
            'mode'=>'zh-CN'
        ]);
        $mpdf->useAdobeCJK = true;
        $mpdf->autoScriptToLang = true;
        $mpdf->autoLangToFont   = true;
        $mpdf->SetDisplayMode('fullpage');
        $mpdf->SetHTMLHeader("");
        $mpdf->SetHTMLFooter("");
        $mpdf->WriteHTML($content);
        $mpdf->Output($file_path ,Destination::FILE);

        // pdf 加水印
        WaterMarkerService::getInstance()->addWaterMarkerToPdfFile($file_path, $file_path);

        // 生成成功, 上传OSS
        $upload_res = OssHelper::uploadFile($file_path);
        $return = [
            'code' => ErrCode::$SUCCESS,
            'msg' => '',
            'data' => ''
        ];

        if (!empty($upload_res['object_url'])) {
            $return['data'] = $upload_res['object_url'];
            $return['msg'] = 'success';
        } else {
            $return['code'] = ErrCode::$BUSINESS_ERROR;
            $return['msg'] = 'pdf file download failed';
        }

        return $return;
    }

}
