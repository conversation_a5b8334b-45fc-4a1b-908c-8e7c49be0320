<?php

namespace App\Modules\Wages\Controllers;

use App\Library\ErrCode;
use App\Modules\Wages\Services\DetailService;

class PdfController extends BaseController
{

    /**
     * @Token
     */
    public function outputAction()
    {
        $hash = $this->request->get('hash');
        $cached = $this->cache->get('DL:' . $hash);
        if ($cached) {
            $res = (new DetailService())->download($cached['id'], $cached['uid'], $cached['lang']);
            if ($res['code'] == ErrCode::$SUCCESS) {
                $this->response->redirect($res['data'], true, 301);
            } else {
                return $this->returnJson(ErrCode::$BUSINESS_ERROR, $res['msg'], []);
            }
        } else {
            return $this->returnJson(0, 'error');
        }

    }
}