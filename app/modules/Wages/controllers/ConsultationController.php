<?php

namespace App\Modules\Wages\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Wages\Services\DetailService;
use App\Modules\Wages\Services\ListService;
use App\Modules\Wages\Services\WagesFlowService;
use App\Modules\User\Services\UserService;
use App\Modules\Workflow\Services\FYRService;

class ConsultationController extends BaseController
{

    /**
     * 待回复征询列表
     * @Token
     */
    public function listAction()
    {
        $params = $this->request->get();
        $list = (new ListService())->getList($params, $this->user['id'], ListService::LIST_TYPE_FYR);
        return $this->returnJson(ErrCode::$SUCCESS, '', $list['data']);
    }

    /**
     *
     * @Token
     */
    public function detailAction()
    {
        $id = $this->request->get('id');

        $res = (new DetailService())->getDetail($id,$this->user['id']);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     *
     * @Token
     */
    public function askAction()
    {
        $biz_id = $this->request->get('id', 'int');
        $note = $this->request->get('note','trim');
        $to_staffs = $this->request->get('to_staff');

        // 执行参数校验
        $check_data = [
            'id' => $biz_id,
            'note' => $note,
            'to_staff' => $to_staffs
        ];
        FYRService::getInstance()->askValidation($check_data);

        $request = (new WagesFlowService())->getRequest($biz_id);
        $to_staffs = (new UserService())->getUserInfos($to_staffs);
        $result = FYRService::getInstance()->createAsk($request, $note, $to_staffs, $this->user);

        return $this->returnJson($result['code'], $result['message']);
    }

    /**
     * 回复征询
     *
     * @Token
     */
    public function replyAction()
    {
        $ask_id = $this->request->get('ask_id','int');
        $note = $this->request->get('note','trim');

        // 执行参数校验
        $check_data = [
            'id' => $ask_id,
            'note' => $note
        ];
        FYRService::getInstance()->replyValidation($check_data);

        $result = FYRService::getInstance()->createReply($ask_id, $note, $this->user);
        return $this->returnJson($result['code'], $result['message']);
    }
}