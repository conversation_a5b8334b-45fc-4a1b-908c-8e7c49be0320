<?php

namespace App\Modules\Wages\Controllers;

use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Wages\Services\DetailService;
use App\Modules\Wages\Services\ListService;
use App\Modules\Wages\Services\WagesFlowService;

class AuditController extends BaseController
{
    /**
     * @Permission(action='salary.audit.search')
     * @throws ValidationException
     */
    public function listAction()
    {
        $data = Validation::validate($this->request->get(),[]);

        $result = (new ListService())->getList($data, $this->user['id'],ListService::LIST_TYPE_AUDIT);

        return $this->returnJson(ErrCode::$SUCCESS,$this->t['success'],$result);
    }

    /**
     * @Permission(action='salary.audit.view')
     * @throws BusinessException
     * @throws ValidationException
     */
    public function detailAction()
    {
        $data = Validation::validate($this->request->get(), ['id' => 'Required|IntGe:1|>>>:params error[id]']);
        $result = (new DetailService())->getDetail($data['id'], $this->user['id']);
        return $this->returnJson(ErrCode::$SUCCESS, $this->t['success'], $result);
    }

    /**
     * @Permission(action='salary.audit.audit')
     * @throws ValidationException
     */
    public function auditAction()
    {
        $id = $this->request->get('id', 'int');
        $note = $this->request->get('note', 'trim');

        $update_data['wht_category_id'] = $this->request->get('wht_category_id', 'int');
        $update_data['wht_tax_rate_id'] = $this->request->get('wht_tax_rate_id', 'float');
        $update_data['wht_amount'] = $this->request->get('wht_amount', 'float');
        $update_data['actually_amount'] = $this->request->get('actually_amount', 'float');

        $this->logger->info('薪酬扣款审批 - 审批 - 请求参数:' . json_encode($this->request->get(), JSON_UNESCAPED_UNICODE));

        $data = Validation::validate($this->request->get(), ['id' => 'Required|IntGe:1|>>>:params error[id]']);

        $service = new WagesFlowService();
        if (empty($data['flag'])) {
            //通过
            $res = $service->approve($id, $note, $this->user, $update_data);
        } else {
            //拒绝
            $res = $service->reject($id, $note, $this->user);
        }

        $this->logger->info('薪酬扣款审批 - 审批 - 响应参数:' . json_encode($res, JSON_UNESCAPED_UNICODE));

        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', []);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * @Permission(action='salary.audit.download')
     * @throws ValidationException
     */
    public function downloadAction()
    {
        $data = Validation::validate($this->request->get(), ['id' => 'Required|IntGe:1|>>>:params error[id]']);

        try {
            // 加锁处理
            $lock_key = md5('wages_audit_download_' . $data['id'] . '_' . $this->user['id']);
            $res = $this->atomicLock(function() use ($data){
                return (new DetailService())->download($data['id'], $this->user['id'], $this->locale);
            }, $lock_key, 30);

            if ($res['code'] == ErrCode::$SUCCESS) {
                return $this->returnJson(ErrCode::$SUCCESS, $res['msg'], ['url' => $res['data']]);
            }

            return $this->returnJson($res['code'], $res['msg']);

        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
    }
}