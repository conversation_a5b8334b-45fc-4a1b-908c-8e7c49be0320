<?php

namespace App\Modules\Wages\Controllers;

use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Wages\Services\AddService;
use App\Modules\Wages\Services\DetailService;
use App\Modules\Wages\Services\ListService;
use App\Modules\Wages\Services\WagesFlowService;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class ApplicationController extends BaseController
{

    /**
     * @Token
     * @return Response|ResponseInterface
     */
    public function getDefaultAction()
    {
        $data = (new AddService())->defaultData();

        return $this->returnJson(ErrCode::$SUCCESS,$this->t['success'],$data);
    }

    /**
     * @Permission(action='salary.apply.add')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function addAction()
    {
        $data = Validation::validate($this->request->get(),AddService::$validate,false,true);
        $result = (new AddService())->add($data,$this->user);

        return $this->returnJson($result['code'], $result['message']);

    }

    /**
     * @Permission(action='salary.apply.view')
     * @return Response|ResponseInterface
     * @throws ValidationException
     * @throws BusinessException
     */
    public function detailAction()
    {
        $data = Validation::validate($this->request->get(),[]);

        $result = (new DetailService())->getDetail($data['id'],$this->user['id']);

        return $this->returnJson(ErrCode::$SUCCESS,$this->t['success'],$result);
    }

    /**
     * @Permission(action='salary.apply.search')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function listAction()
    {
        $data = Validation::validate($this->request->get(),[]);

        $result = (new ListService())->getList($data,$this->user['id'],ListService::LIST_TYPE_APPLY);

        return $this->returnJson(ErrCode::$SUCCESS,$this->t['success'],$result);
    }

    /**
     * @Permission(action='salary.apply.cancel')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function cancelAction()
    {
        $data = Validation::validate($this->request->get(),[]);
        $result = (new WagesFlowService())->cancel($data['id'],$data['note'], $this->user);

        return $this->returnJson($result['code'], $result['message']);

    }

    /**
     * @Permission(action='salary.apply.download')
     * @throws ValidationException
     */
    public function downloadAction()
    {
        $data = Validation::validate($this->request->get(),[]);

        try {
            // 加锁处理
            $lock_key = md5('wages_apply_download_' . $data['id'] . '_' . $this->user['id']);
            $res = $this->atomicLock(function() use ($data){
                return (new DetailService())->download($data['id'], $this->user['id'], $this->locale);
            }, $lock_key, 30);

            if ($res['code'] == ErrCode::$SUCCESS) {
                return $this->returnJson(ErrCode::$SUCCESS, $res['msg'], ['url' => $res['data']]);
            }

            return $this->returnJson($res['code'], $res['msg']);

        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
    }
}