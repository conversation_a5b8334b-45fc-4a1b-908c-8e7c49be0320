<?php

namespace App\Modules\Wages\Controllers;

use App\Library\Enums\DownloadCenterEnum;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Services\DownloadCenterService;
use App\Modules\Wages\Services\AddService;
use App\Modules\Wages\Services\DetailService;
use App\Modules\Wages\Services\ListService;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class DataController extends BaseController
{

    /**
     * @Permission(action='salary.data.search')
     */
    public function listAction()
    {
        $data = Validation::validate($this->request->get(),[]);

        $result = (new ListService())->getList($data,$this->user['id'],ListService::LIST_TYPE_DATA);

        return $this->returnJson(ErrCode::$SUCCESS,$this->t['success'],$result);
    }

    /**
     * @Permission(action='salary.data.view')
     */
    public function detailAction()
    {
        $data = Validation::validate($this->request->get(),[]);

        $result = (new DetailService())->getDetail($data['id'],$this->user['id']);

        return $this->returnJson(ErrCode::$SUCCESS,$this->t['success'],$result);
    }

    /**
     * @Permission(action='salary.data.export')
     */
    public function exportAction()
    {
        $data = Validation::validate($this->request->get(), []);
        // 加锁
        $lock_key = md5('wages_data_export_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($data) {
            return DownloadCenterService::getInstance()->addDownloadCenter($this->user['id'], DownloadCenterEnum::WAGES_PAYMENT_APPROVAL_EXPORT, $data);
        }, $lock_key, 5);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * @Permission(action='salary.data.download')
     * @throws ValidationException
     */
    public function downloadAction()
    {
        $data = Validation::validate($this->request->get(),[]);

        try {
            // 加锁处理
            $lock_key = md5('wages_data_download_' . $data['id'] . '_' . $this->user['id']);
            $res = $this->atomicLock(function() use ($data){
                return (new DetailService())->download($data['id'], $this->user['id'], $this->locale);
            }, $lock_key, 30);

            if ($res['code'] == ErrCode::$SUCCESS) {
                return $this->returnJson(ErrCode::$SUCCESS, $res['msg'], ['url' => $res['data']]);
            }

            return $this->returnJson($res['code'], $res['msg']);

        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

    }

    /**
     * 枚举配置
     *
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/82643
     * @return Response|ResponseInterface
     */
    public function getOptionsDefaultAction()
    {
        $res =  (new AddService())->getOptionsDefault();
        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res);
    }
}