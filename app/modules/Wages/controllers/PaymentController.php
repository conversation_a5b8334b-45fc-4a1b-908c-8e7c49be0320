<?php

namespace App\Modules\Wages\Controllers;

use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Wages\Services\DetailService;
use App\Modules\Wages\Services\ListService;
use App\Modules\Wages\Services\UpdateService;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class PaymentController extends BaseController
{

    /**
     * @Permission(action='salary.payment.search')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function listAction()
    {
        $data = Validation::validate($this->request->get(),[]);

        $result = (new ListService())->getList($data,$this->user['id'],ListService::LIST_TYPE_PAY);

        return $this->returnJson(ErrCode::$SUCCESS,$this->t['success'],$result);
    }

    /**
     * @Permission(action='salary.payment.view')
     * @return Response|ResponseInterface
     * @throws BusinessException
     * @throws ValidationException
     */
    public function detailAction()
    {
        $data = Validation::validate($this->request->get(),[]);

        $result = (new DetailService())->getDetail($data['id'],$this->user['id']);

        return $this->returnJson(ErrCode::$SUCCESS,$this->t['success'],$result);
    }

    /**
     * @Permission(action='salary.payment.pay')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function payAction()
    {
        $data = Validation::validate($this->request->get(),UpdateService::$validate_pay,false,true);

        $result = (new UpdateService())->updatePayment($data['id'],$data,$this->user);

        return $this->returnJson($result['code'], $result['message']);
    }

    /**
     * @Permission(action='salary.payment.download')
     * @throws ValidationException
     */
    public function downloadAction()
    {
        $data = Validation::validate($this->request->get(),[]);

        try {
            // 加锁处理
            $lock_key = md5('wages_payment_download_' . $data['id'] . '_' . $this->user['id']);
            $res = $this->atomicLock(function() use ($data){
                return (new DetailService())->download($data['id'], $this->user['id'], $this->locale);
            }, $lock_key, 30);

            if ($res['code'] == ErrCode::$SUCCESS) {
                return $this->returnJson(ErrCode::$SUCCESS, $res['msg'], ['url' => $res['data']]);
            }

            return $this->returnJson($res['code'], $res['msg']);

        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
    }
}