<?php

namespace App\Modules\Budget\Services;



use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\RedisClient;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Hc\Models\SysDepartmentModel;
use App\Modules\Hc\Models\SysStoreModel;
use App\Modules\User\Models\DepartmentModel;
use App\Modules\User\Services\UserService;
use App\Util\RedisExpire;
use App\Util\RedisKey;

class BaseService extends \App\Library\BaseService
{
    const LIST_TYPE_APPLY = 1;
    const LIST_TYPE_AUDIT = 2;
    const LIST_TYPE_FYR = 3;
    const LIST_TYPE_QUERY = 4;
    const IS_BUDGET = 1;//参与预算管控
    const IS_NO_BUDGET = 0;//不参与预算管控
    const IS_DEDUCTION = 1;//扣减
    const IS_RE_BACK = 2;//返还

    const BUDGET_SOURCE_TYPE_1 = 1;//预算追加
    const BUDGET_SOURCE_TYPE_2 = 2;//预算导入覆盖



    const IS_COM_USE = 2;//共用
    const IS_NO_COM_USE = 1;//独占不共用
    const COST_STORE = 1;//网点
    const HEAD_OFFICE = 2;//总部
    //总部 网点语言枚举
    const ORGANIZATION_TYPE_ZH_1 = '网点';
    const ORGANIZATION_TYPE_ZH_2 = '总部';
    const ORGANIZATION_TYPE_EN_1 = 'branch';
    const ORGANIZATION_TYPE_EN_2 = 'head office';
    const ORGANIZATION_TYPE_TH_1 = 'สาขา';
    const ORGANIZATION_TYPE_TH_2 = 'สำนักงานใหญ่';
    //是否共用语言枚举
    const IS_COM_USE_ZH = '是';
    const IS_COM_USE_TH = 'ใช่';
    const IS_COM_USE_EN = 'yes';

    const IS_NO_COM_USE_ZH = '否';
    const IS_NO_COM_USE_TH = 'ไม่ใช่';
    const IS_NO_COM_USE_EN = 'no';


    //总部网点枚举
    public static $organization_type_item = [ //网点校验
        self::ORGANIZATION_TYPE_ZH_1 => self::COST_STORE,
        self::ORGANIZATION_TYPE_EN_1 => self::COST_STORE,
        self::ORGANIZATION_TYPE_ZH_2 => self::HEAD_OFFICE,
        self::ORGANIZATION_TYPE_EN_2 => self::HEAD_OFFICE,
        self::ORGANIZATION_TYPE_TH_1 => self::COST_STORE,
        self::ORGANIZATION_TYPE_TH_2 => self::HEAD_OFFICE,
    ];
    public static $amount_type_item = [//是否共用规则校验
        self::IS_NO_COM_USE_ZH => self::IS_NO_COM_USE,
        self::IS_NO_COM_USE_EN => self::IS_NO_COM_USE,
        self::IS_COM_USE_ZH    => self::IS_COM_USE,
        self::IS_COM_USE_EN    => self::IS_COM_USE,
        self::IS_NO_COM_USE_TH => self::IS_NO_COM_USE,
        self::IS_COM_USE_TH    => self::IS_COM_USE,
    ];

    public static $amount_type_status =[
        self::IS_COM_USE=>'budget_common_use',
        self::IS_NO_COM_USE=>'budget_no_common_use',
    ];

    public static $budget_status = [
        self::IS_BUDGET  => 'payment_contract_status_1',
        self::IS_NO_BUDGET => 'payment_contract_status_0',
    ];

    public static $budget_import_type = [
        self::BUDGET_SOURCE_TYPE_1 => 'budget_import_source_type_1',
        self::BUDGET_SOURCE_TYPE_2 => 'budget_import_source_type_2'
    ];

    public static $validate_param_detail = [
        'id' => 'Required|Int',
    ];

    public static $validate_param_cancel = [
        'id' => 'Required|IntGe:1',
        'note' => 'Required|StrLenGeLe:1,1000'
    ];

    public static $validate_param_reject = [
        'id' => 'Required|IntGe:1',
        'remark' => 'Required|StrLenGeLe:1,1000',
        'attachments' => 'Arr|ArrLenGeLe:0,5'
    ];

    public static $validate_param_excel = [
        'id' => 'Required|IntGe:1',
        'attachment' => 'IfIntEq:check,1|Required',
    ];

    // 详情查看校验
    public static $validate_detail_param = [
        'id' => 'Required|IntGe:1|>>>:id error',
    ];

    public static $validate_param = [
        'adjust_no' => 'Required|StrLenGeLe:10,20',
        'apply_id' => 'Required|Int',
        'apply_name' => 'Required',
        'apply_store_id' => 'Required',
        'apply_department_id' => 'Required|Int',
        'cost_company_id' => 'Required|Int'
    ];

    public static $audit_validate_param = [
        'adjust_detail' => 'Required|Arr|ArrLenGe:1',
        'adjust_detail[*].object_code' => 'Required',
        'adjust_detail[*].reason' => 'Required|StrLenGeLe:0,1000',
        'adjust_detail[*].currency' => 'Required',
        'adjust_detail[*].apply_department_id' => 'Required',
        'adjust_detail[*].budget_department_id' => 'Required',
        'adjust_detail[*].organization_type' => 'Required|IntIn:1,2',
        'adjust_detail[*].amount_type' => 'Required|IntIn:1,2',
        'adjust_detail[*].budget_adjust_type' => 'Required|IntIn:1,2,3',
        'adjust_detail[*].jan_amount' => 'Required|FloatGeLe:-99999999999.99,99999999999.99',
        'adjust_detail[*].feb_amount' => 'Required|FloatGeLe:-99999999999.99,99999999999.99',
        'adjust_detail[*].mar_amount' => 'Required|FloatGeLe:-99999999999.99,99999999999.99',
        'adjust_detail[*].apr_amount' => 'Required|FloatGeLe:-99999999999.99,99999999999.99',
        'adjust_detail[*].may_amount' => 'Required|FloatGeLe:-99999999999.99,99999999999.99',
        'adjust_detail[*].jun_amount' => 'Required|FloatGeLe:-99999999999.99,99999999999.99',
        'adjust_detail[*].jul_amount' => 'Required|FloatGeLe:-99999999999.99,99999999999.99',
        'adjust_detail[*].aug_amount' => 'Required|FloatGeLe:-99999999999.99,99999999999.99',
        'adjust_detail[*].sep_amount' => 'Required|FloatGeLe:-99999999999.99,99999999999.99',
        'adjust_detail[*].oct_amount' => 'Required|FloatGeLe:-99999999999.99,99999999999.99',
        'adjust_detail[*].nov_amount' => 'Required|FloatGeLe:-99999999999.99,99999999999.99',
        'adjust_detail[*].dec_amount' => 'Required|FloatGeLe:-99999999999.99,99999999999.99',
        'adjust_detail[*].total_amount' => 'Required|FloatGeLe:-999999999999.99,999999999999.99',
    ];

    /**
     * 根据 语言环境 获取对应字段名 涉及表 share_center_dir  share_center_file 字段名(name_th,name_en,name_cn)
     * @param $lang
     * @return string
     */
    public function get_lang_column($lang = 'th')
    {
        $lang_arr = array(
            'en' => 'name_en',
            'th' => 'name_th',
            'zh-CN' => 'name_cn',
            'zh' => 'name_cn'
        );
        return empty($lang_arr[$lang]) ? 'name_en' : $lang_arr[$lang];
    }

    /**
     * 获取编号
     * @return string
     */
    public static function getNo($date, $key = NULL)
    {
        $key = self::getCounterKey($date, $key);
        if (self::getCounter($key)) {           //有计数器
            $lno = self::incrCounter($key);
        } else {
            $lno = self::setCounter($key);
        }
        return $date . sprintf('%04s', $lno);
    }

    /**
     * 判断计数器是否存在
     * @param string $key
     * @return bool|int
     */
    private static function getCounter($key)
    {
        return RedisClient::getInstance()->getClient()->exists($key);
    }

    /**
     * 计数器不存在的情况下
     * @param string $key
     * @return bool|int
     */
    private static function setCounter($key)
    {
        $lno = 1;
        RedisClient::getInstance()->getClient()->setex($key, RedisExpire::ONE_DAY, $lno);
        return $lno;
    }

    /**
     * 计数器存在的情况下
     * @param string $key
     * @return int
     */
    private static function incrCounter($key)
    {
        return RedisClient::getInstance()->getClient()->incrBy($key, 1);
    }

    private static function getCounterKey($date, $key = NULL)
    {
        $key = $key ?? RedisKey::PURCHASE_ORDER_COUNTER;
        return $key . "_" . $date;
    }

    // 获取当前年份（统一接口，方便测试设置当前基准时间，调试预算调整）
    public static function getCurrentYear()
    {
        return date('Y');
    }

    // 获取当前年月（统一接口，方便测试设置当前基准时间，调试预算调整）
    public static function getCurrentYearMonth()
    {
        return date('Y-m');
    }

    public function getUserMetaFromBi($userId)
    {

        $model = (new UserService())->getUserByIdInRbi($userId);
        if (empty($model)) {
            return [];
        }

        $data = [];
        $data['apply_id'] = $model->staff_info_id ?? "";
        $data['apply_name'] = $this->getNameAndNickName($model->name ?? "",$model->nick_name??"");

        $department_id = $model->sys_department_id;
        $node_department_id = $model->node_department_id;
        $store_id = $model->sys_store_id;

        $data['apply_department'] = !empty($node_department_id) ? $node_department_id : $department_id;
        $data['cost_store'] = $store_id;
        if ($store_id == -1) {
            $data['cost_store_name'] = 'Header Office';
        } else {
            $storeInfo = SysStoreModel::findFirst([
                'columns' => 'id, name',
                'conditions' => 'id = :id:',
                'bind' => ['id' => $store_id]
            ]);
            $data['cost_store_name'] = isset($storeInfo->name) ? $storeInfo->name : '';
        }

        $t = DepartmentModel::findFirst([
            "conditions" => "id = :id:",
            "bind" => [
                "id" => $data['apply_department'],
            ]
        ]);
        $data['apply_department_name'] = !empty($t) ? $t->name : "";
        //COO/CEO下的BU级公司列表
        $data['cost_company_list'] = $this->getCooCostCompany();
        //根据费用所属部门查询对应的COO/CEO下的BU级部门
        $data['cost_company_id'] = '';
        $data['cost_company_name'] = '';
        $cost_company_kv = array_column($data['cost_company_list'],'cost_company_name','cost_company_id');
        if(!empty($t) && key_exists($t->company_id,$cost_company_kv)){
            $data['cost_company_id'] = $t->company_id;
            $data['cost_company_name'] = $cost_company_kv[$t->company_id];
        }
        $data['apply_department'] = $node_department_id;
        $data['sys_department'] = $department_id;

        return $data;
    }

    /**
     * 获取COO\CEO 级别下的所有BU级列表
     * @return array
     */
    public function getCooCostCompany()
    {
        $data = [];
        $coo_department_val = EnvModel::getEnvByCode("coo_department_id");
        if (!empty($coo_department_val)) {
            $coo_department_id_arr = explode(",", $coo_department_val);
            $item = SysDepartmentModel::find(
                [
                    'conditions' => 'ancestry IN ({ancestry:array}) and deleted = 0 and type =1 and level =0',
                    'bind'       => ['ancestry' => $coo_department_id_arr]
                ]
            );
            if ($item) {
                $department_arr = $item->toArray();
                foreach ($department_arr as $d_key=>$d_value){
                    $data[] = [
                        'cost_company_id'=>$d_value['id'],
                        'cost_company_name'=>$d_value['name'],
                    ];
                }
            }
        }
        return $data;
    }

    /**
     * 获取组织类型列表
     * @return array
     */
    public function getOrganizationTypeItems()
    {
        return [
            Enums::PAYMENT_COST_STORE_TYPE_01 => 'payment_cost_store_type_2',// 网点
            Enums::PAYMENT_COST_STORE_TYPE_02 => 'payment_cost_store_type_1',// 总部
        ];
    }

    /**
     * 过滤空值 和 非必要参数
     * @param array $params
     * @param array $not_must
     * @return mixed
     */
    public static function handleParams($params, $not_must)
    {
        if (empty($not_must) || !is_array($not_must)) {
            return $params;
        }
        foreach ($not_must as $value) {
            if (isset($params[$value]) && empty($params[$value])) {
                unset($params[$value]);
            }
        }
        return $params;
    }
}
