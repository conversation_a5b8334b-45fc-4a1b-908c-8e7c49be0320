<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 2020/12/3
 * Time: 5:14 PM
 */


namespace App\Modules\Budget\Services;


use App\Library\Enums;
use App\Library\Enums\SysConfigEnums;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Library\ErrCode;
use App\Modules\Budget\Models\BudgetDetail;
use App\Modules\Budget\Models\BudgetDetailAmount;
use App\Modules\Budget\Models\BudgetDetailLog;
use App\Modules\Budget\Models\BudgetObject;
use App\Modules\Budget\Models\BudgetObjectDepartment;
use App\Modules\Budget\Models\BudgetObjectDepartmentAmount;
use App\Modules\Budget\Models\BudgetObjectOrder;
use App\Modules\Budget\Models\BudgetObjectProduct;
use App\Modules\Common\Services\EnumsService;
use App\Modules\OrdinaryPayment\Models\OrdinaryPayment;
use App\Modules\OrdinaryPayment\Models\OrdinaryPaymentDetail;
use App\Modules\Organization\Services\DepartmentService;
use App\Modules\Purchase\Models\PurchaseApply;
use App\Modules\Purchase\Models\PurchaseOrder;
use App\Modules\Purchase\Models\PurchasePayment;
use App\Modules\Purchase\Models\PurchasePaymentReceipt;
use App\Modules\Reimbursement\Models\Detail;
use App\Modules\Reimbursement\Models\Reimbursement;
use App\Modules\Setting\Services\DataPermissionModuleConfigService;
use App\Modules\User\Models\DepartmentModel;
use App\Modules\User\Services\UserService;
use App\Modules\Budget\Models\LedgerAccount;
use App\Library\Enums\GlobalEnums;
use App\Modules\Wms\Services\ListService;
use App\Repository\oa\BudgetObjectProductRepository;
use App\Repository\oa\BudgetObjectRepository;

class BudgetService extends BaseService{
    const BUDGET_CODE_LEN = 3;
    const ORDER_TYPE_1 = 1;//报销
    const ORDER_TYPE_2 = 2;//采购
    const ORDER_TYPE_3 = 3;//普通付款
    const ORDER_TYPE_5 = 5;//预算预提
    /**
     * 根据部门 和 用户信息 获取对应的当月的一级分类
     * @param  $user_info
     * @param $order_type integer 订单类型 1-报销 2 采购 ，3 普通付款
     * @param $code string 需要数据的等级 默认顶级
     * @return array
     */
    public function get_object_by_dep($user_info,$order_type,$code = ''){

        //查询所有预算科目
        $object_code=[];
        $budget_object_code =[];
        $month = date('Y-m');

        $conditions = 'is_delete = :is_delete:';
        $bind       = ['is_delete' => GlobalEnums::IS_NO_DELETED];
        if (!empty($user_info['budget_ids']) && is_array($user_info['budget_ids'])) {
            $conditions  .= ' AND id IN ({ids:array})';
            $bind['ids'] = $user_info['budget_ids'];
        }

        $budget_obj_data = BudgetObject::find([
            'conditions' => $conditions,
            'bind'       => $bind,
        ])->toArray();

        //处理预算科目
        foreach ($budget_obj_data as $k => $v) {
            if ($v['is_budget'] == self::IS_BUDGET) {
                $budget_object_code[] = $v['level_code'];
            } else if ($v['is_budget'] == self::IS_NO_BUDGET) {
                $object_code[] = $v['level_code'];

            }
        }

        //根据预算科目 查询是否有预算
        $budget_object_str = "'".implode("','", $budget_object_code)."'";

        $budget_object_code =  BudgetObjectDepartment::find([
            'conditions'=>"department_id =:department_id: and object_code in ({$budget_object_str}) and organization_type = :organization_type: and  month ='{$month}'  and is_delete=0",
            'bind' => ['department_id'=>$user_info['department_id'],'organization_type'=>$user_info['organization_type']],
            'columns' => "distinct(object_code) as object_code",
        ])->toArray();

        if(!empty( $budget_object_code)){
            $budget_object_code = array_column($budget_object_code,'object_code');
        }
        //by 不展示不管控的预算

        if (!isset($user_info['source_type'])) {
            $object_codes = array_unique(array_merge($budget_object_code, $object_code));
        } else {
            $object_codes = $budget_object_code;
        }
        //没有预算科目列表为空
        if(empty($object_codes)){
            return  [];
        }
        //该部门 关联的所有 最底级别科目
        $builder = $this->modelsManager->createBuilder();
        //整理语言环境
        $name = $this->get_lang_column(static::$language);

        $column = "distinct(o.level_code),o.id,o.level_code,o.{$name},o.name_en,o.template_type,o.bar_code,o.is_budget";
        $builder->columns($column);
        $builder->from(['o' => BudgetObject::class]);
        $builder->join(BudgetObjectOrder::class, "o.level_code=ot.level_code and ot.type = {$order_type}", "ot");
        //默认不传code  取顶级
        if(empty($code)){
            $builder->andWhere('o.level = :object_level:', ['object_level' => 1]);
        }else{
            $level = (strlen($code) / self::BUDGET_CODE_LEN )+ 1;
            $builder->andWhere('o.level = :object_level:', ['object_level' => $level]);
            $builder->andWhere('o.level_code LIKE :code:', ['code' => $code . '%']);
        }
       $builder->andWhere('o.level_code IN ({object_codes:array})', ['object_codes' => $object_codes]);

        $whereSql = '(ot.type = :order_type:
                        and o.is_delete = :is_delete:
                        and ot.is_delete = :is_deleted:
                        )';
        $p['order_type'] = intval($order_type);
        $p['is_delete']= 0;
        $p['is_deleted']= 0;
        $builder->andWhere($whereSql, $p);
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 根据一级部门和用户ID获取  科目目录
     * 当前科目一集下级科目目录
     *
     * @param $userInfo
     * @param $orderType
     * @param string $code
     * @return array
     */
    public function objectList($userInfo, $orderType, string $code = '')
    {
        $budgetStatus = EnumsService::getInstance()->getBudgetStatus();
        // 当前国家是否开启预算
        if ($budgetStatus) {
            $budgets = $this->get_object_by_dep($userInfo, $orderType, $code);
            $results = [];
            foreach ($budgets as $budget) {
                $tmp = [
                    'id' => $budget['id'],
                    'level_code' => $budget['level_code'],
                    'name' => $budget[$this->get_lang_column(static::$language)],
                    'name_en' => $budget[$this->get_lang_column('en')],
                    'template_type'=>$budget['template_type'],
                    'bar_code' => $budget['bar_code'],
                    'is_budget'=>$budget['is_budget'],
                ];

                $subObjects = $this->objectList($userInfo, $orderType, $budget['level_code']);
                if ($subObjects) {
                    $tmp['list'] = $subObjects;
                }
                $results[] = $tmp;
            }
        } else {
            $name = $this->get_lang_column(static::$language);
            $results = BudgetObject::find([
                'condition' => 'is_delete=0',
                'columns' => "id,level_code,{$name} as name,template_type,bar_code"
            ])->toArray();
        }
        return $results;
    }


    /**
     * 获取末级科目ID
     *
     * @param $budgetTrees
     */
    public function endBudgetIds($budgetTrees)
    {
        $endBudgetIds = [];
        foreach ($budgetTrees as $budgetTree) {
            if(isset($budgetTree['list'])) {
                $endBudgetIds = array_merge($endBudgetIds, $this->endBudgetIds($budgetTree['list']));
            } else {
                $endBudgetIds[] = $budgetTree['id'];
            }
        }
        return $endBudgetIds;
    }


    /**
     * @param $code 对应的级别code
     * @return array
     */
    protected function split_code($code){
        $length = strlen($code);
        $return = [];
        if(empty($code) || $length < self::BUDGET_CODE_LEN)
            return $return;

        for ($m = 3; $m <= $length; $m += self::BUDGET_CODE_LEN){
            $return[] = substr($code,0,$m);
        }
        return $return;
    }

    /**
     * 详情页 获取 对应科目名称显示 接口
     * 参数 多个code 数组 返回对应的名称 k => v
     * @param $obj_codes
     * @param $type 对应科目类型 1-报销 2 采购
     * @return array
     */
    public function get_object_info($obj_codes,$type){
        if(empty($obj_codes))
            return [];
        $code_param = array();
        foreach ($obj_codes as $v){
            $r = $this->split_code($v);
            $code_param = array_merge($code_param,$r);
        }
        $obj_list = BudgetObject::find(
            [
                "conditions" => "level_code in ({codes:array}) and is_delete = 0",
                "bind"=>["codes"=>$code_param],
            ]
        )->toArray();
        $return = array();
        if(!empty($obj_list)){
            $name = $this->get_lang_column(static::$language);
            $return = array_column($obj_list,$name,'level_code');
        }
        return $return;
    }

    /**
     验证 预算用
     * @param $user_info array 用户信息 用于查询 所在部门的对应的科目 剩余预算
     * @param $order_type integer 订单类型
     * @param $amount_info array 每个最小科目 对应需要的金额 array('001001' => 2211.23,'001002' => 321.25)
     * @param $add_flag = 0 是否 扣除操作调用 0 默认验证调用 1 扣除操作调用
     * @return array
     *
     */
    public function check_budget($user_info,$order_type,$amount_info, $add_flag = 0){
        //获取对应当月预算
        $current_month = $user_info['current_month'];
//        $month_int = intval(date('m'));
        $codes = array_keys($amount_info);//底级科目集合
        //组织机构有可能传错
        if($user_info['organization_type'] == '-1')
            $user_info['organization_type'] = 2;
        $amount_list = $this->get_budget($user_info['department_id'],$user_info['organization_type'],$codes,$current_month);
        $this->logger->info("check_budget ".json_encode($user_info).json_encode($amount_list));

        //没有配置预算额度 不能申请
        if(empty($amount_list))
            return array(
                'code' => ErrCode::$VALIDATE_ERROR,
                'message' => static::$t->_('no_budget_quota')
            );
        $check_amount = array();//拼接 key  月份_底级科目编码
        $need_parent = array();
        foreach ($amount_list as $v){
            if($v['amount_type'] == 2) {//公用额度 需要找顶级部门
                $need_parent[] = $v['object_code'];
            }else{
                $k = "{$v['month']}_{$v['object_code']}";
                $check_amount[$k] = $v;
            }
        }

        //获取顶级部门id 重新获取 对应预算额度
        if($need_parent){
            $_amount = $_amount_left = 0;
            $obj_amount = [];
            foreach ($need_parent as $object_code){
                $obj_amount[$object_code] = false;

            }
            //判断当前部门是否是二级以下部门
            $is_sub_department=DepartmentModel::isSubSecondDepartment($user_info['department_id']);
            if($is_sub_department){
                //获取二级部门
                $parent_id =DepartmentModel::getSecondDepartmentIdByDepartmentId($user_info['department_id']);
                $amount_list = $this->getDepartmentCommonBudget($parent_id, $user_info['organization_type'], $need_parent, $current_month);
                $this->logger->info("check_budget parent department {$user_info['department_id']}_{$parent_id} {$user_info['organization_type']} ".json_encode($amount_list));
                if (!empty($amount_list)) {
                    foreach ($amount_list as $_val) {
                        $_amount += $_val['amount'];
                        $_amount_left += $_val['amount_left'];
                        if($_val['amount']>0||$_val['amount_left']){
                            $obj_amount[$_val['object_code']] = true;
                        }
                    }

                    foreach ($obj_amount as $k_1=>$v_1){
                        if($v_1){
                            unset($obj_amount[$k_1]);
                        }
                    }
                }
            }



            if ($obj_amount) {
                // 获取一级部门共用预算
                $need_parent = array_keys($obj_amount);
                $parent_id   = DepartmentModel::getFirstDepartmentIdByDepartmentId($user_info['department_id']);
                $amount_first_list = $this->getDepartmentCommonBudget($parent_id, $user_info['organization_type'], $need_parent, $current_month);
                if(!empty($amount_list)){
                   foreach ($amount_list as $k_2 =>$v_2) {
                    if(in_array($v_2['object_code'],$need_parent)){
                        unset($amount_list[$k_2]);
                    }
                   }
                }
               $amount_list = array_merge($amount_list,$amount_first_list);

                $this->logger->info("check_budget parent department {$user_info['department_id']}_{$parent_id} {$user_info['organization_type']} " . json_encode($amount_list));
                if (!empty($amount_list)) {
                    foreach ($amount_list as $_val) {
                        $_amount += $_val['amount'];
                        $_amount_left += $_val['amount_left'];
                    }
                }
            }

            // 一级部门共用预算为空，继续查询公司共用预算
            if (!$_amount && !$_amount_left) {
                $parent_id = DepartmentModel::getCompanyIdByDepartmentId($user_info['department_id']);
                $amount_list = $this->getDepartmentCommonBudget($parent_id, $user_info['organization_type'], $need_parent, $current_month);
                $this->logger->info("check_budget parent company {$user_info['department_id']}_{$parent_id} {$user_info['organization_type']} ".json_encode($amount_list));
            }

            if (empty($amount_list)) {
                return array(
                    'code' => ErrCode::$VALIDATE_ERROR,
                    'message' => static::$t->_('no_budget_quota')
                );
            }

            //组装 key
            foreach ($amount_list as $v){
                $k = "{$v['month']}_{$v['object_code']}";
                $check_amount[$k] = $v;
            }
        }
        unset($amount_list);
        //遍历 验证额度
        $check_result = array();//如果是扣除操作调用 整理 code 对应多个月份和分别钱数
        foreach ($amount_info as $obj_code => $amount_apply){
            $k = "{$current_month}_{$obj_code}";
            if(empty($check_amount[$k])){
                $row['month'] = '';
                $row['amount'] = 0;//当月额度全占用了
                $row['obj_code'] = $obj_code;
                $row['obj_dep_id'] = '';
                $row['code'] = -1;
                $check_result = array_merge($check_result,array($row));
                continue;
            }
            $left_amount = $check_amount[$k]['amount_left'];
            if($left_amount >= $amount_apply){//预算够用
                $obj_dep_id = "{$check_amount[$k]['department_id']}_{$check_amount[$k]['object_code']}_{$check_amount[$k]['organization_type']}_{$check_amount[$k]['month']}";
                if($add_flag == 1){
                    $row['month'] = $check_amount[$k]['month'];
                    $row['amount'] = $amount_apply;
                    $row['obj_code'] = $obj_code;
                    $row['obj_dep_id'] = $obj_dep_id;//{department_id]}_{object_code}_{organization_type}_{month}";
                    $row['code'] = 1;
                    $check_result = array_merge($check_result,[$row]);
                }
//                else{
//                    $row['obj_code'] = $obj_code;
//                    $row['over_amount'] = 0;//额度当月够用 没有超出
//                    $row['alert_flag'] = 'success';
//                    $row['code'] = 1;
//                    $check_result = array_merge($check_result,array($row));
//                }

                continue;
            }else{//当月不够 取季度
                $res = $this->check_budget_detail($check_amount,$obj_code,$amount_apply,$left_amount,$add_flag);
                $check_result = array_merge($check_result,$res);
            }
        }

        if($add_flag){//入库操作
//            $flags = array_column($check_result,'code');
//            if(in_array(-1,$flags))
//                return false;
        }else{//页面验证调用
            if(empty($check_result))
                return true;//都是扣除当月预算 没有提示

            //整理 科目名称提示信息 返回
            $obj_info = $this->get_object_info($codes,$order_type);
            foreach ($check_result as &$v){
                $v['obj_name'] = $obj_info[$v['obj_code']];
            }
        }
        return $check_result;
    }

    /**
     *
     * 本方法 在当月额度不够情况下 需要取当季度的 其他月份额度 计算是否够用
     * @param $check_amount 该部门 该科目 多个月预算集合
     * @param $obj_code 科目code
     * @param $amount_apply 申请总额
     * @param $left_amount 当月剩余额度
     * @param $add_flag 根据flag  返回对应 提示语 或者 拼接入库数据  0 页面验证调用  1 操作扣除调用
     * @return mixed
     */
    protected function check_budget_detail($check_amount,$obj_code,$amount_apply,$left_amount,$add_flag){
        $month_order = $this->get_month_order();
        $current_month = date('Y-m');
        list($m,$n) = $month_order;
        $return = array();
        $month_1 = date('Y-m',strtotime("{$current_month} {$m} month"));
        $k = "{$current_month}_{$obj_code}";
        $k_1 = "{$month_1}_{$obj_code}";
        $left_amount_1 = $check_amount[$k_1]['amount_left'];
        if(($left_amount + $left_amount_1) >= $amount_apply){
            if($add_flag == 1){
                //当月额度
                $row['month'] = $current_month;
                $row['amount'] = $left_amount;//当月额度全占用了
                $row['obj_code'] = $obj_code;
                $obj_dep_id = "{$check_amount[$k]['department_id']}_{$check_amount[$k]['object_code']}_{$check_amount[$k]['organization_type']}_{$check_amount[$k]['month']}";
                $row['obj_dep_id'] = $obj_dep_id;
                $row['code'] = 1;
                $return[] = $row;
                //后月
                $row['month'] = $month_1;
                $row['amount'] = $amount_apply - $left_amount;
                $row['obj_code'] = $obj_code;
                $k1_id = "{$check_amount[$k_1]['department_id']}_{$check_amount[$k_1]['object_code']}_{$check_amount[$k_1]['organization_type']}_{$check_amount[$k_1]['month']}";
                $row['obj_dep_id'] = $k1_id;
                $row['code'] = 1;
                $return[] = $row;

            }else{
                //页面申请提示
                $r['obj_code'] = $obj_code;
                $r['over_amount'] = round(($amount_apply - $left_amount) / 1000 ,2);
                $r['alert_flag'] = 'month';// month => 月份提示语 season 季度提示语
                $r['code'] = 1;
                $return[] = $r;
            }
            //推算 一个月 额度够用 返回数据
            return $return;
        }

        //推算一个月 不够
        $month_2 = date('Y-m',strtotime("{$current_month} {$n} month"));
        $k_2 = "{$month_2}_{$obj_code}";
        $left_amount_2 = $check_amount[$k_2]['amount_left'];
        if(($left_amount + $left_amount_1 + $left_amount_2) < $amount_apply){
            if($add_flag) {//数据入库 扣除操作 不能存在超预算情况
                $row['month'] = '';
                $row['amount'] = 0;//当月额度全占用了
                $row['obj_code'] = $obj_code;
                $obj_dep_id = "{$check_amount[$k]['department_id']}_{$check_amount[$k]['object_code']}_{$check_amount[$k]['organization_type']}_{$check_amount[$k]['month']}";
                $row['obj_dep_id'] = $obj_dep_id;
                $row['code'] = -1;
                $return[] = $row;
            }else{
                //季度预算不够 拼提示语 然后下一个
                $r['obj_code'] = $obj_code;
                $r['alert_flag'] = 'season';// 1 => 月份提示语 2 季度提示语
                $r['over_amount'] = round($amount_apply - ($left_amount + $left_amount_1 + $left_amount_2)/1000, 2);
                $r['code'] = -1;
                $return[] = $r;
            }
            return $return;
        }
        //季度 预算够用
        if($add_flag == 1){
            //当月额度
            $row['month'] = $current_month;
            $row['amount'] = $left_amount;//当月额度全占用了
            $row['obj_code'] = $obj_code;
            $obj_dep_id = "{$check_amount[$k]['department_id']}_{$check_amount[$k]['object_code']}_{$check_amount[$k]['organization_type']}_{$check_amount[$k]['month']}";
            $row['obj_dep_id'] = $obj_dep_id;
            $row['code'] = 1;
            $return[] = $row;
            //后月
            $row['month'] = $month_1;
            $row['amount'] = $left_amount_1;
            $row['obj_code'] = $obj_code;
            $k1_id = "{$check_amount[$k_1]['department_id']}_{$check_amount[$k_1]['object_code']}_{$check_amount[$k_1]['organization_type']}_{$check_amount[$k_1]['month']}";
            $row['obj_dep_id'] = $k1_id;
            $row['code'] = 1;
            $return[] = $row;
            //后后月
            $row['month'] = $month_2;
            $row['amount'] = $amount_apply - $left_amount - $left_amount_1;
            $row['obj_code'] = $obj_code;
            $k2_id = "{$check_amount[$k_2]['department_id']}_{$check_amount[$k_2]['object_code']}_{$check_amount[$k_2]['organization_type']}_{$check_amount[$k_2]['month']}";
            $row['obj_dep_id'] = $k2_id;
            $row['code'] = 1;
            $return[] = $row;

        }else{
            $r['obj_code'] = $obj_code;
            $r['over_amount'] = round(($amount_apply - $left_amount) / 1000 , 2);
            $r['alert_flag'] = 'month';// 1 => 月份提示语 2 季度提示语
            $r['code'] = 1;
            $return[] = $r;
        }
        return $return;
    }

    /**
     * 科目占用预算 验证
     *
     * @param $orderSn
     * @param $amountInfo
     *  末级分类， 分类下含税金额
     * [
     *      {"budget_id" => 3, "amount" => "36.00"},
     *      {"budget_id" => 4, "amount" => "90.00"},
     *      {"budget_id" => 7, "amount" => "90.00"},
     *      {"budget_id" => 7, "amount" => "95.00"}
     * ]
     * @param $orderType
     * @param $data
     * @param $isSubmit
     * @param int $submit_id 提交人工号
     * @return bool
     * @throws ValidationException
     */
    public function checkBudgets($orderSn, $amountInfo, $orderType, $data, $isSubmit, $submit_id = 0)
    {
        // 验证默认国家是否开启预算
        $budgetStatus = EnumsService::getInstance()->getBudgetStatus();
        if (!$budgetStatus) {
            $results = true;
            $isSubmit = true;
        } else {
            $budgetIds = array_values(array_unique(array_column($amountInfo, 'budget_id')));

            //参与预算管控的科目
            $budgetList = BudgetObject::find([
                'conditions' => ' id in ({ids:array}) and is_budget =1',
                'bind' => ['ids' => $budgetIds]
            ])->toArray();
            if (empty($budgetList)) {
                return true;
            }

            $budgetList = array_column($budgetList, null, 'id');
            $amounts = [];
            foreach ($amountInfo as $item) {
                if (isset($budgetList[$item['budget_id']])) {
                    $levelCode = $budgetList[$item['budget_id']]['level_code'];
                    if (isset($amounts[$levelCode])) {
                        $amounts[$levelCode] += $item['amount'];
                    } else {
                        $amounts[$levelCode] = $item['amount'];
                    }
                }
            }

            if (in_array($orderType, [self::ORDER_TYPE_1, self::ORDER_TYPE_3, self::ORDER_TYPE_5])) {
                $organizationType = $data['cost_store_type'] ?? 0;
            } else {
                if ($data['cost_store'] == -1) {
                    $organizationType = 2;
                } else {
                    $organizationType = 1;
                }
            }

            $results = $this->check_budget([
                'department_id' => $data['cost_department'] ?? '0',
                'organization_type' => $organizationType,
                'current_month' => $data['current_month'] ?? date('Y-m')
            ], $orderType, $amounts);

            $this->logger->info('check_budget  占用预算判断 params ' . json_encode([
                    'department_id' => $data['cost_department'] ?? '0',
                    'organization_id' => $data['cost_store'] ?? '0',
                    'organization_type' => $organizationType,
                    'order_type' => $orderType,
                    'amounts' => $amounts,
                    'order_sn' => $orderSn,
                ], JSON_UNESCAPED_UNICODE) . ' results ' . json_encode([$results], JSON_UNESCAPED_UNICODE));
        }

        if ($results === true && $isSubmit == 0 && $budgetStatus) {
            throw new ValidationException(static::$t->_('purchase_budget_code_2'), ErrCode::$BUDGET_OVERAMOUNT_MONTH);
        }

        if (isset($results['code']) && $results['code'] == ErrCode::$VALIDATE_ERROR) {
            throw new ValidationException($results['message'], $results['code']);
        }

        $canOccupy = []; // 可占用的提醒
        $notOccupy = []; // 不可占用的提醒
        if (is_array($results)) {
            foreach ($results as $result) {
                if ($result['code'] == 1) {
                    if (isset($result['alert_flag'])) {
                        $canOccupy[] = ['obj_name' => $result['obj_name'], 'amount' => $result['over_amount']];
                    }
                } else if ($result['code'] == -1) {
                    $notOccupy[] = ['obj_name' => $result['obj_name']];
                }
            }
        }

        if ($budgetStatus) {
            if ($isSubmit) {
                // 如果是提交
                if (isset($notOccupy) && $notOccupy) {
                    $prom = implode("、", array_column($notOccupy, 'obj_name'));
                    throw new ValidationException(sprintf(static::$t->_('purchase_budget_code_1'), $prom), ErrCode::$BUDGET_OVERAMOUNT_QUARTERLY);
                }
                // 提交操作
                $result = $this->deduct_budget($orderSn, [
                    'department_id' => $data['cost_department'] ?? '0',
                    'organization_type' => $organizationType,
                    'staff_info_id' => $submit_id,
                    'current_month' => $data['current_month'] ?? date('Y-m')
                ], $orderType, $amounts);

                $this->logger->info('check_budget  占用预算 params ' . json_encode([
                        'department_id' => $data['cost_department'] ?? '0',
                        'organization_id' => $data['cost_store'] ?? '0',
                        'organization_type' => $organizationType,
                        'order_type' => $orderType,
                        'amounts' => $amounts,
                        'order_sn' => $orderSn,
                    ], JSON_UNESCAPED_UNICODE) . ' results ' . json_encode([$results], JSON_UNESCAPED_UNICODE));

                if ($result['code'] != ErrCode::$SUCCESS) {
                    throw new ValidationException($result['message'], ErrCode::$VALIDATE_ERROR);
                }
            } else {
                // 验证
                $errCode = ErrCode::$BUDGET_OVERAMOUNT_MONTH;
                if (isset($notOccupy) && $notOccupy) {
                    $message = sprintf(static::$t->_('purchase_budget_code_1'), implode('、', array_column($notOccupy, 'obj_name'))) . "</br>";
                    $errCode = ErrCode::$BUDGET_OVERAMOUNT_QUARTERLY;
                }

                if (isset($canOccupy) && $canOccupy) {
                    $message = isset($message ) && $message ? $message . "\r\n" : "";
                    $objNames = implode("、", array_column($canOccupy, 'obj_name'));
                    $overAmount = implode("、", array_column($canOccupy, 'amount'));
                    $message .= sprintf(static::$t->_('purchase_budget_code_3'), $objNames, $overAmount);
                }

                if (isset($message)) {
                    throw new ValidationException($message, $errCode);
                }
            }
        }

        return true;
    }


    /**
     *
     * 添加 报销或采购申请单 驳回后再提交  验证预算 并占用 计算字段 含税金额总计
     * 占用优先级
    1月预算剩余200，2月剩余500，3月剩余500。2月用1000，其中，1月额度占了200，2月额度占了500，3月额度占了300。
    从前面的月份开始返：
    返100，给1月
    返300，给1月200，2月100
    返800，给1月200，2月500，3月100
    返900，给1月200，2月500，3月200
     * 扣除 也是这个逻辑 优先前面
     * @param $order_sn 订单号
     * @param $user_info
     * @param $order_type
     * @param $amount_info
     * @return array
     */
    public function deduct_budget($order_sn, $user_info, $order_type, $amount_info)
    {
        $this->logger->info("deduct_budget {$order_sn} " . json_encode($amount_info, JSON_UNESCAPED_UNICODE));

        $check_result = $this->check_budget($user_info, $order_type, $amount_info, true);
        $this->logger->info("deduct_budget {$order_sn} " . json_encode($check_result, JSON_UNESCAPED_UNICODE));
        if (empty($check_result)) {
            return array(
                'code'    => -88,
                'message' => static::$t->_('验证额度失败')
            );
        }

        $codes = array_column($check_result,'code');
        if (in_array(-1,$codes)) {
            return array(
                'code' => -73,
                'message' => static::$t->_('额度刚刚被占用')
            );
        }

        //写数据 开事务 验证 是否存在
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        $detail_model = new BudgetDetail();
        $detail_amount_model = new BudgetDetailAmount();
        $detail_log_model = new BudgetDetailLog();

        $department_obj_model = new BudgetObjectDepartmentAmount();
        //budget_detail 主表 单条记录
        $amount_list = array_values($amount_info);
        $in_all = array_sum($amount_list);
        $detail_info = BudgetDetail::findFirst("order_no = '{$order_sn}' and type = {$order_type}");


        $return['code'] = ErrCode::$SUCCESS;
        $return['message'] = '';
        if (!empty($detail_info)) {//update
            $detail_info->act_amount = $in_all;
            $detail_info->updated_at = date("Y-m-d H:i:s");
            $detail_info->update();
            //删除 关联表数据 针对已存在 并且撤销以后 再次提交的
            $detail_amount_model::find("detail_id = " .$detail_info->id)->delete();

        } else {//插入
            $de_insert['type']          = intval($order_type);
            $de_insert['order_no']      = $order_sn;
            $de_insert['staff_info_id'] = $user_info['staff_info_id'];
            $de_insert['amount']        = $de_insert['act_amount'] = $in_all;
            $flag                       = $detail_model->create($de_insert);
            if (!$flag) {
                $db->rollback();
                $return['code']    = ErrCode::$MYSQL_ERROR;
                $return['message'] = static::$t->_('server error');
                return $return;
            }
        }

        $detail_id = empty($detail_model->id) ? $detail_info->id : $detail_model->id;
        foreach ($check_result as $c) {
            //budget_detail_amount 扣除哪个月 记录表 对应多条 obj_dep_id
            $exp = explode('_',$c['obj_dep_id']);
            $row['detail_id'] = $detail_id;
            $row['obj_dep_id'] = $c['obj_dep_id'];
            $row['department_id'] = $exp[0];
            $row['object_code'] = $exp[1];
            $row['organization_type'] = $exp[2];
            $row['month'] = $c['month'];
            $row['act_amount'] = $c['amount'];
            $clone_a = clone $detail_amount_model;
            $flag = $clone_a->create($row);
            if (!$flag) {
                $db->rollback();
                $return['code']    = ErrCode::$MYSQL_ERROR;
                $return['message'] = static::$t->_('server error');
                break;
            }

            //操作 科目 月份 预算额度表 更新 剩余额度
//            "{department_id}_{object_code}_{organization_type}_{month}";
            $info = $department_obj_model::findFirst(
                "department_id = '{$exp[0]}' and object_code = {$exp[1]}
                and organization_type = '{$exp[2]}' and month = '{$exp[3]}'
                and is_delete = 0
                "
            );

            if (empty($info)) {
                $db->rollback();
                $return['code']    = ErrCode::$VALIDATE_ERROR;
                $return['message'] = static::$t->_('找不到对应预算额度');
                break;
            }
            $this->logger->info("deduct_amount {$order_sn} 准备扣减 {$c['amount']} ".json_encode($info, JSON_UNESCAPED_UNICODE));
            $real_amount = bcsub($info->amount_left, $c['amount']);

            if ($info->amount_left < 0) {
                $db->rollback();
                $return['code']    = ErrCode::$VALIDATE_ERROR;
                $return['message'] = static::$t->_('budget_amount_occupy_not_enough');
                break;
            }
            $info->amount_left = $real_amount;
            $flag = $info->update();
            if (!$flag) {
                $db->rollback();
                $return['code']    = ErrCode::$MYSQL_ERROR;
                $return['message'] = static::$t->_('server error');
                $this->logger->info("deduct_amount {$order_sn} 扣减 {$c['amount']} 失败 " . json_encode($info, JSON_UNESCAPED_UNICODE));
                break;
            }
            $this->logger->info("deduct_amount {$order_sn} 扣减 {$c['amount']} 成功 ".json_encode($info, JSON_UNESCAPED_UNICODE));

            //budget_detail_log 记录操作日志 只增不减
            $log_row['type'] = $order_type;
            $log_row['order_no'] = $order_sn;
            $log_row['obj_dep_id'] = $c['obj_dep_id'];
            $log_row['amount'] = $c['amount'];
            $clone_l = clone $detail_log_model;//咋这么坑
            $flag = $clone_l->create($log_row);
            if (!$flag) {
                $db->rollback();
                $return['code']    = ErrCode::$MYSQL_ERROR;
                $return['message'] = static::$t->_('server error');
                break;
            }
        }
        if ($return['code'] == ErrCode::$SUCCESS) {
            $db->commit();
        }

        return $return;
    }

    /**
     * @param $department_id 部门id
     * @param $organization_type 1 网点 2 总部
     * @param $codes 对应 科目的底级code
     * @param $current_month 预算占用月份
     * @return mixed
     */
    public function get_budget($department_id,$organization_type,$codes,$current_month){
        $month_int = intval(date('m', strtotime($current_month)));
        //公用预算 类型  需要找到顶级部门的预算额度
        $p['department_id'] = $department_id;
        $p['season'] = intval((($month_int -1) / 3) + 1);//取月份对应季度
        $p['organization_type'] = $organization_type;
        $p['codes'] = $codes;
        $p['is_delete'] = 0;
        $p['year'] = date('Y').'%';
        return BudgetObjectDepartmentAmount::find(
            [
                "conditions" => "department_id = :department_id: and season = :season:
                and is_delete = :is_delete: 
                and organization_type = :organization_type: and object_code in ({codes:array}) and month like :year:",
                "bind"=>$p,
            ]
        )->toArray();
    }

    /**
     * 获取指定部门共用的预算
     * @param int $department_id 指定部门id
     * @param int $organization_type 1 网点 2 总部
     * @param array $codes 对应 科目的底级code
     * @param string $current_month 传递的月份（如2025-07）
     * @return mixed
     */
    public function getDepartmentCommonBudget(int $department_id, int $organization_type, array $codes, string $current_month)
    {
        // 查找当前部门的一级部门 及 公司

        $month_int = intval(date('m', strtotime($current_month)));
        //公用预算 类型  需要找到顶级部门的预算额度
        $p['department_id'] = $department_id;
        $p['season'] = intval((($month_int -1) / 3) + 1);//取月份对应季度
        $p['organization_type'] = $organization_type;
        $p['codes'] = $codes;
        $p['is_delete'] = 0;
        $p['amount_type'] = 2;
        $p['year'] = date('Y').'%';

        return BudgetObjectDepartmentAmount::find([
            "conditions" => "department_id = :department_id: 
                    AND object_code in ({codes:array}) 
                    AND season = :season:
                    AND organization_type = :organization_type: 
                    AND amount_type = :amount_type: 
                    AND is_delete = :is_delete: 
                    AND month like :year:",
            "bind" => $p,
        ])->toArray();
    }


    /**
     * 撤销驳回和关闭  返还预算接口
     * @param $order_no 对应订单编号
     * @param $user_info array 日志用
     * @param $order_type 订单类型
     * @param $thai_amount 实际使用金额
     * @return array
     */
    public function re_back_budget($order_no, $user_info, $order_type, $thai_amount)
    {
        $return['code'] = ErrCode::$SUCCESS;
        //如果全是未管控的科目跳过
        $object_codes    = array_keys($thai_amount);
        $budget_obj_data = BudgetObject::find([
            'columns'    => 'level_code,is_budget',
            "conditions" => "level_code in ({level_code:array}) and is_end = :is_end: and is_delete = 0",
            "bind"       => ["level_code" => $object_codes, "is_end" => 1],
        ])->toArray();

        $is_budget = false;
        $uncontrolled_budget_object_amounts = [];
        if (!empty($budget_obj_data)) {
            foreach ($budget_obj_data as $k_1 => $v_1) {
                if ($v_1['is_budget'] == self::IS_BUDGET) {
                    $is_budget = true;
                } else {
                    // 科目未开启预算管控, 返还时, 该科目无已占用的预算金额无需处理
                    $uncontrolled_budget_object_amounts[$v_1['level_code']] = $thai_amount[$v_1['level_code']];
                    unset($thai_amount[$v_1['level_code']]);
                }
            }
        }

        $this->logger->info('业务侧未管控的预算科目及金额: ' . json_encode($uncontrolled_budget_object_amounts, JSON_UNESCAPED_UNICODE));

        if (!$is_budget) {//不存在管控的科目直接结束
            $return['message'] ='不存在管控科目';
            return  $return;
        }

        //查主表信息
        $detail_info = BudgetDetail::findFirst("order_no = '{$order_no}' and type = {$order_type}" );

        if (empty($detail_info)) {
            $return['code']    = ErrCode::$VALIDATE_ERROR;
            $return['message'] = static::$t->_('re_back_budget_order_not_exist');
            return $return;
        }

        $this->logger->info("re_back_budget {$order_no}, 参与了管控的预算科目及金额: ".json_encode($thai_amount, JSON_UNESCAPED_UNICODE));

        //如果当前参数 额度 大于 最初占用额度 不能操作
        $sum = array_values($thai_amount);
        $sum = array_sum($sum);
        if($sum > $detail_info->act_amount){
            $this->logger->info("biz_sum_amount: {$sum}, budget_act_amount: {$detail_info->act_amount}");

            $return['code'] = ErrCode::$VALIDATE_ERROR;
            $return['message'] = static::$t->_('re_back_budget_actual_amount_error');
            return $return;
        }

        //查询对应多月份占用额度数据
        $detail_id = $detail_info->id;
        $builder = $this->modelsManager->createBuilder();
        $column = "o.id oid,o.department_id ,o.object_code,o.organization_type, o.month,a.id,a.act_amount";
        $builder->columns($column);
        $builder->from(['a' => BudgetDetailAmount::class]);
        $condition_str = "a.department_id = o.department_id and a.object_code = o.object_code 
                            and a.organization_type = o.organization_type and a.month = o.month";
        $builder->Join(BudgetObjectDepartmentAmount::class, $condition_str, "o");
        $builder->andWhere('a.detail_id = :detail_id:', ['detail_id' => $detail_id]);
        $builder->andWhere('o.is_delete = :is_delete:', ['is_delete' => 0]);

        $detail_amount_list = $builder->getQuery()->execute()->toArray();

        if(empty($detail_amount_list)){
            $return['code'] = ErrCode::$VALIDATE_ERROR;
            $return['message'] = static::$t->_('re_back_budget_order_not_exist');
            return $return;
        }

        $check_amount = $re_back_amount = array();
        foreach ($detail_amount_list as $v){
            //用于 返还对应月份额度
            $k = "{$v['month']}_{$v['object_code']}";
            $check_amount[$k] = $v;
            //用于对比参数操作每个科目用的钱数 如果不想等 并且实际占用变小了 说明需要操作返还
            if (isset($re_back_amount[$v['object_code']])) {

                $re_back_amount[$v['object_code']] += $v['act_amount'];
            } else {

                $re_back_amount[$v['object_code']] = $v['act_amount'];
            }
        }
        unset($detail_amount_list);

        foreach ($thai_amount as $obj_code => $act_amount){
            if($re_back_amount[$obj_code] == $act_amount){//之前占用 额度相等不用操作
                unset($re_back_amount[$obj_code]);
                continue;
            }

            if($re_back_amount[$obj_code] < $act_amount){//这种情况 已经占用 小于 实际占用 不应该出现
                $return['code'] = ErrCode::$VALIDATE_ERROR;
                $return['message'] = static::$t->_('re_back_budget_origin_amount_error');
                break;
            }
        }
        //出现 非正常情况
        if($return['code'] != ErrCode::$SUCCESS)
            return $return;

        if(empty($re_back_amount))//不需要返还
            return $return;

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        $detail_amount_model = new BudgetDetailAmount();
        $detail_log_model = new BudgetDetailLog();
        $department_obj_model = new BudgetObjectDepartmentAmount();

        //获取需要返还的对应list
        $de_amount_update = $this->re_back_format($re_back_amount,$thai_amount,$check_amount);
        $this->logger->info("re_back_amount {$order_no} ".json_encode($de_amount_update));

        //1 操作主表实际占用额度 update
        if($sum < $detail_info->act_amount){//不等操作返还
            $detail_info->act_amount = $sum;
            $detail_info->updated_at = date('Y-m-d H:i:s');
            $detail_info->update();
        }

        //2 操作 对应 预算额度表 和 预算详情表
        if(!empty($de_amount_update)){
            foreach ($de_amount_update as $v){
                //budget_object_department_amount
                $current_time = date('Y-m-d H:i:s');
//            "{department_id}_{object_code}_{organization_type}_{month}";
                $exp = explode('_',$v['obj_dep_id']);
                $obj_info = $department_obj_model::findFirst(
                    "department_id = '{$exp[0]}' and object_code = {$exp[1]}
                    and organization_type = '{$exp[2]}' and month = '{$exp[3]}'
                    and is_delete = 0
                    "
                );
//                $obj_info = $department_obj_model::findFirst($v['obj_dep_id']);
                $obj_info->amount_left = $obj_info->amount_left +  $v['need_back'];
                $obj_info->updated_at = $current_time;
                $flag = $obj_info->update();
                if(!$flag){
                    $db->rollback();
                    $return['code'] = ErrCode::$MYSQL_ERROR;
                    $return['message'] = static::$t->_('server error');
                    break;
                }
                //budget_detail_amount
                $de_info = $detail_amount_model::findFirst($v['id']);
                $de_info->act_amount = $v['act_amount'];
                $de_info->updated_at = $current_time;
                $flag = $de_info->update();
                if(!$flag){
                    $db->rollback();
                    $return['code'] = ErrCode::$MYSQL_ERROR;
                    $return['message'] = static::$t->_('server error');
                    break;
                }
                //拼接 log 表 budget_detail_log
                $insert['type'] = $order_type;
                $insert['order_no'] = $order_no;
                $insert['obj_dep_id'] = $v['obj_dep_id'];
                $insert['amount'] = 0 - $v['need_back'];
                $clone = clone $detail_log_model;
                $flag = $clone->create($insert);
                if(!$flag){
                    $db->rollback();
                    $return['code'] = ErrCode::$MYSQL_ERROR;
                    $return['message'] = static::$t->_('server error');
                    break;
                }
            }
        }
        if($return['code'] == ErrCode::$SUCCESS)
            $db->commit();

        //日志
        return $return;
    }

    /**
     *
     * 获取末级科目下的商品，并且以code聚合分类
     *
     * @param $objCodes
     * @param int $orderType
     * @return array
     */
    public function purchaseProducts($objCodes, $orderType = self::ORDER_TYPE_2)
    {
        $objCodes = array_values(array_filter(array_unique($objCodes)));
        if (empty($objCodes)) {
            return [];
        }
        $name = $this->get_lang_column(static::$language);
        $budgetProducts = BudgetObjectProduct::find([
            "columns" => "id, pno, object_code, {$name} as name, name_en, template_type, bar_code",
            "conditions" => "object_code in ({obj_codes:array}) and type = :order_type: and is_delete = 0 ",
            "bind"=>[
                "obj_codes" => $objCodes,
                "order_type" => $orderType,
            ],
        ])->toArray();
        $result = [];
        foreach ($budgetProducts as $budgetProduct) {
            $result[$budgetProduct['object_code']][] = $budgetProduct;
        }
        return $result;
    }

    /**
     * 整理 返还对应额度 用于更新 budget_detail_amount 和 budget_object_department_amount
     * @param $re_back_amount
     * @param $thai_amount
     * @param $check_amount
     * @return array
     */
    public function re_back_format($re_back_amount,$thai_amount,$check_amount){

        //获取订单的 随意month
        $current_month = '';
        foreach($check_amount as $v){
            $m_int = intval(date('m',strtotime($v['month'])));
            $current_month = $v['month'];
            break;
        }

        //按优先级 返还对应额度 当月 m n
        $month_order = $this->get_month_order($m_int);
        list($m,$n) = $month_order;
//        $current_month = date('Y-m');
        $month_1 = date('Y-m',strtotime("{$current_month} {$m} month"));
        $month_2 = date('Y-m',strtotime("{$current_month} {$n} month"));

        $de_amount_update = array();
        foreach ($re_back_amount as $obj_code => $amount){
            $param_amount = $thai_amount[$obj_code];//参数 传的 实际使用金额
            //需要返还的对应code  总额
            $back_amount = $amount - $param_amount;
            if($back_amount == 0)
                continue;

            //与当月对比 1 不足额 或等额 返还 $back_amount 2 超额 算出差值 去找mn
            $k = "{$current_month}_{$obj_code}";
            $k_1 = "{$month_1}_{$obj_code}";
            $k_2 = "{$month_2}_{$obj_code}";
            if(!empty($check_amount[$k])){//当月不为空
                //"{department_id}_{object_code}_{organization_type}_{month}";
                $obj_dep_id = "{$check_amount[$k]['department_id']}_{$check_amount[$k]['object_code']}_{$check_amount[$k]['organization_type']}_{$check_amount[$k]['month']}";
                if($check_amount[$k]['act_amount'] >= $back_amount){//额度够返还当月
                    $row['id'] = $check_amount[$k]['id'];
                    $row['obj_dep_id'] = $obj_dep_id;
                    $row['need_back'] = $back_amount;
                    $row['act_amount'] = $check_amount[$k]['act_amount'] - $back_amount;
                    $de_amount_update[] = $row;
                    continue;
                }
                //需要返还多个月
                $row['id'] = $check_amount[$k]['id'];
                $row['obj_dep_id'] = $obj_dep_id;
                $row['need_back'] = $check_amount[$k]['act_amount'];
                $row['act_amount'] = 0;//当月全额返还 占用变0
                $de_amount_update[] = $row;
                //返还额度 递减
                $back_amount = $back_amount - $check_amount[$k]['act_amount'];
                //判断m月
                if(!empty($check_amount[$k_1]['act_amount'])){
                    $k1_id = "{$check_amount[$k_1]['department_id']}_{$check_amount[$k_1]['object_code']}_{$check_amount[$k_1]['organization_type']}_{$check_amount[$k_1]['month']}";
                    if($check_amount[$k_1]['act_amount'] >= $back_amount){
                        $row['id'] = $check_amount[$k_1]['id'];
                        $row['obj_dep_id'] = $k1_id;
                        $row['need_back'] = $back_amount;
                        $row['act_amount'] = $check_amount[$k_1]['act_amount'] - $back_amount;
                        $de_amount_update[] = $row;
                        continue;
                    }
                    //需要返还3个月的 对应额度
                    $row['id'] = $check_amount[$k_1]['id'];
                    $row['obj_dep_id'] = $k1_id;
                    $row['need_back'] = $check_amount[$k_1]['act_amount'];
                    $row['act_amount'] = 0;//k1月全额返还 占用变0
                    $de_amount_update[] = $row;
                    //额度递减
                    $back_amount = $back_amount - $check_amount[$k_1]['act_amount'];
                }
                if(!empty($check_amount[$k_2])){
                    $k2_id = "{$check_amount[$k_2]['department_id']}_{$check_amount[$k_2]['object_code']}_{$check_amount[$k_2]['organization_type']}_{$check_amount[$k_2]['month']}";
                    $row['id'] = $check_amount[$k_2]['id'];
                    $row['obj_dep_id'] = $k2_id;
                    $row['need_back'] = $back_amount;
                    $row['act_amount'] = $check_amount[$k_2]['act_amount'] - $back_amount;
                    $de_amount_update[] = $row;
                    continue;
                }


            }else{//只有 k1 k2
                if(!empty($check_amount[$k_1]['act_amount'])){
                    $k1_id = "{$check_amount[$k_1]['department_id']}_{$check_amount[$k_1]['object_code']}_{$check_amount[$k_1]['organization_type']}_{$check_amount[$k_1]['month']}";
                    if($check_amount[$k_1]['act_amount'] >= $back_amount){//额度够返还当月
                        $row['id'] = $check_amount[$k_1]['id'];
                        $row['obj_dep_id'] = $k1_id;
                        $row['need_back'] = $back_amount;
                        $row['act_amount'] = $check_amount[$k_1]['act_amount'] - $back_amount;
                        $de_amount_update[] = $row;
                        continue;
                    }
                    //需要返还多个月
                    $row['id'] = $check_amount[$k_1]['id'];
                    $row['obj_dep_id'] = $k1_id;
                    $row['need_back'] = $check_amount[$k_1]['act_amount'];
                    $row['act_amount'] = 0;//当月全额返还 占用变0
                    $de_amount_update[] = $row;

                    $back_amount = $back_amount - $check_amount[$k_1]['act_amount'];

                }
                if(!empty($check_amount[$k_2])){
                    //k2
                    $k2_id = "{$check_amount[$k_2]['department_id']}_{$check_amount[$k_2]['object_code']}_{$check_amount[$k_2]['organization_type']}_{$check_amount[$k_2]['month']}";
                    $row['id'] = $check_amount[$k_2]['id'];
                    $row['obj_dep_id'] = $k2_id;
                    $row['need_back'] = $back_amount;
                    $row['act_amount'] = $check_amount[$k_2]['act_amount'] - $back_amount;
                    $de_amount_update[] = $row;
                    continue;
                }

            }
        }
        return $de_amount_update;
    }


    /**
     * 通过ids获取类目列表 并且 以 level code 聚合
     *
     * @param $objectIds
     *
     * @return array
     */
    public function budgetObjectList($objectIds)
    {
        $objectIds = array_values(array_unique(array_filter($objectIds)));
        $budgetList = [];
        if ($objectIds) {
            $budgetList = BudgetObject::find([
                'conditions' => ' id in ({ids:array})',
                 'bind' => ['ids' => $objectIds]
            ])->toArray();
            $budgetList = array_column($budgetList, null, 'id');
        }

        return $budgetList;
    }

    /**
     * 通过ids获取类目下产品列表 （明细数据）
     *
     * @param $productIds
     *
     * @return array
     */
    public function budgetObjectProductList($productIds)
    {
        $productIds = array_values(array_unique(array_filter($productIds)));
        $productList = [];
        if ($productIds) {
            $productList = BudgetObjectProduct::find([
                'conditions' => ' id in ({ids:array})',
                'bind' => ['ids' => $productIds]
            ])->toArray();
            $productList = array_column($productList, null, 'id');
        }

        return $productList;
    }

    //· 采购单报销单 下拉核算分类接口（采购单和商品公用）不包括删除商品
    public function get_purchase_product($obj_code,$order_type){
        if(empty($obj_code) || empty($order_type))
            return [];
        $name = $this->get_lang_column(static::$language);
        $condition = "object_code = :obj_code: and type = :order_type: and is_delete = 0 ";
        if(is_array($obj_code))//批量传 code by 申请报销用
            $condition = "object_code in ({obj_code:array}) and type = :order_type: and is_delete = 0 ";
        $product_list = BudgetObjectProduct::find(
            [
                "columns" => "id,pno,object_code,{$name},template_type,budget_product_account",
                "conditions" => $condition,
                "bind"=>["obj_code"=>$obj_code,"order_type"=>$order_type],
            ]
        )->toArray();
        if($product_list) {
            $account_ids = array_column(LedgerAccount::find([
                'conditions' => 'account IN ({account:array})',
                'bind' => ['account' => array_column($product_list, 'budget_product_account')],
                'columns' => 'id,account'
            ])->toArray(), null, 'account');
        }else{
            return [];
        }
        foreach ($product_list as &$var) {
            $var['budget_product_account_id'] = !empty($var['budget_product_account']) && !empty($account_ids[$var['budget_product_account']]) ? $account_ids[$var['budget_product_account']]['id'] : '';
            unset($var['budget_product_account']);
        }
        return $product_list;
    }

    //获取商品名称 包括已删除
    public function get_product_info($product_id){
        if(empty($product_id))
            return [];

        $info = BudgetObjectProduct::findFirst("id = {$product_id}");
        $return = array();
        if(!empty($info)){
            $name = $this->get_lang_column(static::$language);
            $info = $info->toArray();
            $return['id'] = $info['id'];
            $return['pno'] = $info['pno'];
            $return['name'] = $info[$name];
//            $return['amount'] = $info['amount'];
        }
        return $return;
    }


    //根据当前月 所在季度的 位置 获取优先级 m 优先 n 次级优先
    protected function get_month_order($month_int = 0){
        if(empty($month_int))
            $month_int = intval(date('m'));
        //季度首  后月 =》 后后月
        $m = $n = '';
        if($month_int % 3 == 1){
            $m = '+1';
            $n = '+2';
        }
        //季度中 前月 =》 后月
        if($month_int % 3 == 2){
            $m = '-1';
            $n = '+1';
        }
        //季度 末 前月 =》 前前月
        if($month_int % 3 == 0){
            $m = '-1';
            $n = '-2';
        }
        return [$m,$n];
    }



    public function export_check($new_data,$header){
        $file_name = "budget_" . date("YmdHis");
        $new_data = array_map('array_values',$new_data);
        return $this->exportExcel($header, $new_data, $file_name);
    }

    //-----------------------------------释放单-------------------------------------
    //针对采购申请单 释放 返还预算
    public function budget_release(){

    }


    //生成释放单编号


    //列表页


    //添加



    //详情



    public function getAllObjectList($is_budget=null){
        $budgets = BudgetObject::find(
            [
                'conditions' => 'is_delete=0'
            ]
        )->toArray();
        $data = [];
        foreach ($budgets as $budget) {
            if ($is_budget && $budget['is_budget'] == self::IS_NO_BUDGET) {
                continue;
            }
            $data[] = [
                'id' => $budget['id'],
                'level_code' => $budget['level_code'],
                'name' => $budget[$this->get_lang_column(static::$language)],
            ];
        }
        return $data;
    }

    /**
     * 实时查询-查询列表
     * @param array $params 请求入参
     * @param array $user 当前登录用户
     * @return array
     * @throws ValidationException
     */
    public function getAmount(array $params, array $user)
    {
        DataPermissionModuleConfigService::getInstance()->queryAuthority($params['department_id'], $user, SysConfigEnums::SYS_MODULE_BUDGET);
        $page_size = empty($params['pageSize']) ? 20 : $params['pageSize'];
        $page_num = empty($params['pageNum']) ? 1 : $params['pageNum'];
        $offset = $page_size * ($page_num-1);
        $deadline_at = $params['deadline_at'];
        $department_id = intval($params['department_id']);
        $cost_store_type = intval($params['cost_store_type']);
        $level_code = empty($params['level_code']) ? '' : $params['level_code'];

        $season = ceil(date("m",strtotime($deadline_at))/3);
        $year = date("Y",strtotime($deadline_at));
        $monthArr = [1=>$year."-01",2=>$year."-04",3=>$year."-07",4=>$year."-10"];
        $start_month = $monthArr[$season];
        $end_month = date("Y-m",strtotime($deadline_at));

        $str = $season*3;
        if($str<=9){
            $str="0".$str;
        }
        $season_end_month = date("Y-".$str,strtotime($start_month));

        $tmp=$start_month;
        $searchMonth = [];
        while($tmp<=$end_month){
            $searchMonth[] = $tmp;
            $tmp = date("Y-m",strtotime('+1 months',strtotime($tmp)));
        }
        // 部门名称
        $departModel = DepartmentModel::findFirst(['conditions' => 'id='.$department_id])->toArray();
        $departName = $departModel['name'] ?? '';
        $firstDepartmentId = DepartmentModel::getFirstDepartmentIdByDepartmentId($department_id);

        // 组织名称
        $orgName = 1 == $cost_store_type ? '网点' : '总部';
        // 取所有的科目
        $budgets = BudgetObject::find(
            [
                'conditions' => 'is_delete=0'
            ]
        )->toArray();
        $allLevelCode = array_column($budgets,'name_cn','level_code');

        if (!empty($level_code)) {
            $levelCodeList = [$level_code];
        } else {
            $levelCodeList = array_keys($allLevelCode);
        }
        // 统计总记录数
        $total = count($levelCodeList);
        $startPoint = $offset + 1;
        $endPoint = $startPoint + $page_size;

        $allAmount = $useList = $allAmountArr = $occupyList = [];
        $reUsedList = $opUsedList = $puUsedList = [];
        $reOccList = $opOccList = $puOccList = [];
        foreach ($levelCodeList as $k => $levelcode) {
            if ($k+1 < $startPoint) {
                continue;
            }
            $sys_department_id = $department_id;
            if ($k+1 >= $endPoint) {
                break;
            }
            // 该部门自己的,如果有自己的，就没有共用的: 该部门可能是任何层级的
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['c' => BudgetObjectDepartmentAmount::class]);
            $builder->columns('c.object_code,sum(c.amount) amount');
            $builder->andWhere('c.department_id = :department_id:', ['department_id' => $sys_department_id]);
            $builder->andWhere('c.object_code = :level_code:', ['level_code' => $levelcode]);
            $builder->andWhere('c.organization_type = :organization_type:', ['organization_type' => $cost_store_type]);
            $builder->andWhere('c.is_delete=0');
            $builder->andWhere('c.amount_type = 1');
            $builder->betweenWhere('c.month', $start_month, $season_end_month);
            $builder->groupBy('c.object_code');
            $codeitem = $builder->getQuery()->execute()->toArray();

            // 该部门如果为null，证明一个都没有: 在取其一级部门共用的预算额度
            if (empty($codeitem) || empty($codeitem[0]['amount'])) {
                // 一级部门共用的
                $builder = $this->modelsManager->createBuilder();
                $builder->from(['c' => BudgetObjectDepartmentAmount::class]);
                $builder->columns('object_code,sum(amount) as amount');
                $builder->andWhere('department_id = :department_id:', ['department_id' => $firstDepartmentId]);
                $builder->andWhere('object_code = :level_code:', ['level_code' => $levelcode]);
                $builder->andWhere('organization_type = :organization_type:', ['organization_type' => $cost_store_type]);
                $builder->andWhere('is_delete=0');
                $builder->andWhere('amount_type=2');
                $builder->betweenWhere('month', $start_month, $season_end_month);
                $builder->groupBy('object_code');
                $codeitem = $builder->getQuery()->execute()->toArray();

                // 下面都用1级部门Id
                $sys_department_id = (empty($codeitem) || empty($codeitem[0]['amount'])) ? $sys_department_id : $firstDepartmentId;
            }

            // 如果该部门的一级部门预算为null: 则继续查找所属公司的共用预算
            if (empty($codeitem) || empty($codeitem[0]['amount'])) {
                $company_id = DepartmentModel::getCompanyIdByDepartmentId($sys_department_id);
                $builder = $this->modelsManager->createBuilder();
                $builder->from(['c' => BudgetObjectDepartmentAmount::class]);
                $builder->columns('object_code,sum(amount) as amount');
                $builder->andWhere('department_id = :department_id:', ['department_id' => $company_id]);
                $builder->andWhere('object_code = :level_code:', ['level_code' => $levelcode]);
                $builder->andWhere('organization_type = :organization_type:', ['organization_type' => $cost_store_type]);
                $builder->andWhere('is_delete=0');
                $builder->andWhere('amount_type=2');
                $builder->betweenWhere('month', $start_month, $season_end_month);
                $builder->groupBy('object_code');
                $codeitem = $builder->getQuery()->execute()->toArray();

                $sys_department_id = (empty($codeitem) || empty($codeitem[0]['amount'])) ? $sys_department_id : $company_id;
            }

            if (!empty($codeitem)) {
                $allAmountArr[] = $codeitem[0];

                $reNos = [];
                $puNos = [];
                $opNos = [];

                $obj_dep_idArr = $houle_month = [];
                $stmp = $start_month;
                while($stmp<=$season_end_month){
                    $houle_month[] = $stmp;
                    $stmp = date("Y-m",strtotime('+1 months',strtotime($stmp)));
                }
                foreach ($houle_month as $k){
                    $tmp = $sys_department_id."_".$levelcode."_".$cost_store_type."_".$k;
                    $obj_dep_idArr[] = $tmp;
                }

                $builder = $this->modelsManager->createBuilder();
                $builder->from(['c' => BudgetDetailLog::class]);
                $builder->inWhere('obj_dep_id',$obj_dep_idArr);
                $a_time = date("Y-m-d H:i:s",strtotime("-7 hours",strtotime($start_month."-01")));
                $b_time = date("Y-m-d H:i:s",strtotime("-7 hours",strtotime($deadline_at." 23:59:59")));
                $builder->betweenWhere('created_at',$a_time,$b_time);

                $logs = $builder->getQuery()->execute()->toArray();
                foreach ($logs as $log){
                    $amount = $log['amount'];
                    //报销
                    if($log['type'] == self::ORDER_TYPE_1){
                        $reOccList[$levelcode] = isset($reOccList[$levelcode]) ? bcadd($reOccList[$levelcode],$amount) : $amount;
                        $reNos[] = $log['order_no'];
                    }elseif($log['type'] == self::ORDER_TYPE_2){
                        $puOccList[$levelcode] = isset($puOccList[$levelcode]) ? bcadd($puOccList[$levelcode],$amount) : $amount;
                        $puNos[] = $log['order_no'];
                    }elseif($log['type'] == self::ORDER_TYPE_3){
                        $opOccList[$levelcode] = isset($opOccList[$levelcode]) ? bcadd($opOccList[$levelcode],$amount) : $amount;
                        $opNos[] = $log['order_no'];
                    }
                    $occupyList[$levelcode] = isset($occupyList[$levelcode]) ? bcadd($occupyList[$levelcode],$amount) : $amount;

                    if(!isset($allAmount[$levelcode][$log['order_no']])){
                        $allAmount[$levelcode][$log['order_no']] = 0;
                    }
                    $allAmount[$levelcode][$log['order_no']] += $amount;
                }
            } else {
                $allAmountArr[] = [
                    'object_code' => $levelcode,
                    'amount' => 0
                ];
                $reNos = $puNos = $opNos = [];
                $reOccList[$levelcode] = $puOccList[$levelcode] = $opOccList[$levelcode]
                    = $occupyList[$levelcode] = $allAmount[$levelcode] = 0;
            }
        }

        if(!empty($reNos)){
            $reNos = array_unique($reNos);

            // 取预算详情
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['r' => Reimbursement::class]);
            $builder->leftjoin(Detail::class, "d.re_id=r.id", "d");
            $builder->leftjoin(BudgetObject::class, "b.id=d.budget_id", "b");
            $builder->andWhere('r.pay_status = :pay_status:', ['pay_status' => Enums::LOAN_PAY_STATUS_PAY]);
            $builder->andWhere('r.no in("'.implode('","',$reNos).'")');
            $builder->groupBy('b.level_code,r.no');
            $builder->columns('b.level_code,r.no');
            $reList = $builder->getQuery()->execute()->toArray();

            foreach ($reList as $reItem) {
                // 总的使用金额
                $useList[$reItem['level_code']] = !isset($useList[$reItem['level_code']]) ? $allAmount[$reItem['level_code']][$reItem['no']] : bcadd($useList[$reItem['level_code']],$allAmount[$reItem['level_code']][$reItem['no']]);

                // 已使用的报销金额
                $reUsedList[$reItem['level_code']] = !isset($reUsedList[$reItem['level_code']]) ? $allAmount[$reItem['level_code']][$reItem['no']] : bcadd($reUsedList[$reItem['level_code']],$allAmount[$reItem['level_code']][$reItem['no']]);
            }
        }

        if(!empty($opNos)){
            $opNos = array_unique($opNos);
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['o' => OrdinaryPayment::class]);
            $builder->leftjoin(OrdinaryPaymentDetail::class, "od.ordinary_payment_id=o.id", "od");
            $builder->leftjoin(BudgetObject::class, "b.id=od.budget_id", "b");
            $builder->andWhere('o.pay_status = '.Enums::LOAN_PAY_STATUS_PAY);
            $builder->andWhere('o.apply_no in ("'.implode('","',$opNos).'")');
            $builder->groupBy('o.apply_no');
            $builder->columns('o.apply_no,b.level_code');
            $reList = $builder->getQuery()->execute()->toArray();

            foreach ($reList as $reItem) {
                // 总的使用金额
                $useList[$reItem['level_code']] = !isset($useList[$reItem['level_code']]) ? $allAmount[$reItem['level_code']][$reItem['apply_no']] : bcadd($useList[$reItem['level_code']],$allAmount[$reItem['level_code']][$reItem['apply_no']]);
                // 已使用的普通付款金额
                $opUsedList[$reItem['level_code']] = !isset($opUsedList[$reItem['level_code']]) ? $allAmount[$reItem['level_code']][$reItem['apply_no']] : bcadd($opUsedList[$reItem['level_code']],$allAmount[$reItem['level_code']][$reItem['apply_no']]);
            }
        }

        if(!empty($puNos)){
            $puNos = array_unique($puNos);
            $puList = PurchaseApply::find(
                [
                    'columns'=>'id',
                    'conditions' => 'pano in ("'.implode('","',$puNos).'") and status='.Enums::CONTRACT_STATUS_APPROVAL,
                ]
            )->toArray();
            $paids = array_column($puList,"id");

            $poids = [];
            if(!empty($paids)){
                $poList = PurchaseOrder::find(
                    [
                        'columns'=>'id',
                        'conditions'=> 'pa_id in ('.implode(',',$paids).') and status='.Enums::CONTRACT_STATUS_APPROVAL,
                    ]
                )->toArray();
                $poids = array_column($poList,"id");
            }

            if(!empty($poids) || !empty($paids)){
                $poids = !empty($poids) ? $poids : [0];
                $paids = !empty($paids) ? $paids : [0];
                // 采购订单以及采购申请单相关联
                $ppList = PurchasePayment::find(
                    [
                        'conditions' => 'pay_status='.Enums::LOAN_PAY_STATUS_PAY.' and (po_id in ('.implode(',',$poids).') or pa_id in('.implode(',',$paids).'))',
                    ]
                )->toArray();
                $ppids = array_column($ppList,"id");
                $ppidToRate = array_column($ppList,'exchange_rate','id');
                if(!empty($ppids)){
                    $builder = $this->modelsManager->createBuilder();
                    $builder->columns('p.ppid,p.level_code,sum(p.ticket_amount) ticket_amount');
                    $builder->from(["p" => PurchasePaymentReceipt::class]);
                    $builder->andWhere("p.ppid in (".implode(',',$ppids).")");
                    $builder->groupBy('p.level_code');
                    $reList = $builder->getQuery()->execute()->toArray();
                    foreach ($reList as $reItem) {
                        $exchange_rate = $ppidToRate[$reItem['ppid']] ?? '0.000';
                        if (bccomp($exchange_rate, 0.000, 3) == 1) {
                            // 货币不是泰铢
                            $t_amount = bcmul($reItem['ticket_amount'],$exchange_rate, 3);
                        } else {
                            $t_amount = bcmul($reItem['ticket_amount'],$exchange_rate, 3);
                        }
                        $useList[$reItem['level_code']] = !isset($useList[$reItem['level_code']]) ? $t_amount : bcadd($useList[$reItem['level_code']],$t_amount,3);
                        // 已使用的采购金额
                        $puUsedList[$reItem['level_code']] = !isset($puUsedList[$reItem['level_code']]) ? $t_amount : bcadd($puUsedList[$reItem['level_code']],$t_amount,3);
                    }
                }
            }
        }

        $list = [];
        // 组装表格数据
        Enums\GlobalEnums::init();
        foreach ($allAmountArr as $item) {
            $objectCode = $item['object_code'];
            $list[] = [
                'code_name' => $allLevelCode[$objectCode] ?? '',
                'department_name' => $departName,
                'organization_name' => $orgName,
                'deadline' => $deadline_at,
                'currency' => Enums\GlobalEnums::$sys_default_currency_symbol,
                'all_amount' => bcdiv($item['amount'], 1000, 2),
                'can_use_amount' => bcdiv($item['amount'] - ($occupyList[$objectCode] ?? 0),1000, 2),
                'occupy_amount' => isset($occupyList[$objectCode]) ? bcdiv($occupyList[$objectCode],1000, 2) : 0,
                'used_amount' => isset($useList[$objectCode]) ? bcdiv($useList[$objectCode],1000, 2) : 0,
                'import_amount' => bcdiv($item['amount'],1000, 2),
                'reimbursement_occupy_amount' => isset($reOccList[$objectCode]) ? bcdiv($reOccList[$objectCode],1000, 2) : 0,
                'ordinary_payment_occupy_amount' => isset($opOccList[$objectCode]) ? bcdiv($opOccList[$objectCode],1000, 2) : 0,
                'purchase_occupy_amount' => isset($puOccList[$objectCode]) ? bcdiv($puOccList[$objectCode],1000, 2) : 0,
                'reimbursement_used_amount' => isset($reUsedList[$objectCode]) ? bcdiv($reUsedList[$objectCode],1000, 2) : 0,
                'ordinary_payment_used_amount' => isset($opUsedList[$objectCode]) ? bcdiv($opUsedList[$objectCode],1000, 2) : 0,
                'purchase_occupy_used_amount' => isset($puUsedList[$objectCode]) ? bcdiv($puUsedList[$objectCode],1000, 2) : 0,
            ];
        }

        return  [
            'total'=> $total,
            'list' => $list
        ];
    }


    /**
     * 导出占用预算
     *
     * @param array $params 请求入参
     * @param array $user 当前登录用户
     * @return array
     * @throws ValidationException
     * @throws BusinessException
     */
    public function exportAmount(array $params, array $user){
        DataPermissionModuleConfigService::getInstance()->queryAuthority($params['department_id'], $user, SysConfigEnums::SYS_MODULE_BUDGET);
        $deadline_at = $params['deadline_at'];
        $department_id = intval($params['department_id']);
        $cost_store_type = intval($params['cost_store_type']);
        $level_code = empty($params['level_code']) ? '' : $params['level_code'];

        $season = ceil(date("m",strtotime($deadline_at))/3);
        $year = date("Y",strtotime($deadline_at));
        $monthArr = [1=>$year."-01",2=>$year."-04",3=>$year."-07",4=>$year."-10"];
        $start_month = $monthArr[$season];
        $end_month = date("Y-m",strtotime($deadline_at));

        $str = $season*3;
        if($str<=9){
            $str="0".$str;
        }
        $season_end_month = date("Y-".$str,strtotime($start_month));

        $tmp=$start_month;
        $searchMonth = [];
        while($tmp<=$end_month){
            $searchMonth[] = $tmp;
            $tmp = date("Y-m",strtotime('+1 months',strtotime($tmp)));
        }
        // 部门名称
        $departModel = DepartmentModel::findFirst(['conditions' => 'id='.$department_id])->toArray();
        $departName = $departModel['name'] ?? '';
        $firstDepartmentId = DepartmentModel::getFirstDepartmentIdByDepartmentId($department_id);

        // 组织名称
        $orgName = 1 == $cost_store_type ? '网点' : '总部';
        // 取所有的科目
        $budgets = BudgetObject::find(
            [
                'conditions' => 'is_delete=0'
            ]
        )->toArray();
        $allLevelCode = array_column($budgets,'name_cn','level_code');

        if (!empty($level_code)) {
            $levelCodeList = [$level_code];
        } else {
            $levelCodeList = array_keys($allLevelCode);
        }
        $allAmount = $useList = $allAmountArr = $occupyList = [];
        $reUsedList = $opUsedList = $puUsedList = [];
        $reOccList = $opOccList = $puOccList = [];
        foreach ($levelCodeList as $k => $levelcode) {
            // 该部门自己的,如果有自己的，就没有共用的: 该部门可能是任何层级的
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['c' => BudgetObjectDepartmentAmount::class]);
            $builder->columns('c.object_code,sum(c.amount) amount');
            $builder->andWhere('c.department_id = :department_id:', ['department_id' => $department_id]);
            $builder->andWhere('c.object_code = :level_code:', ['level_code' => $levelcode]);
            $builder->andWhere('c.organization_type = :organization_type:', ['organization_type' => $cost_store_type]);
            $builder->andWhere('c.is_delete=0');
            $builder->andWhere('c.amount_type = 1');
            $builder->betweenWhere('c.month', $start_month, $season_end_month);
            $builder->groupBy('c.object_code');
            $codeitem = $builder->getQuery()->execute()->toArray();

            // 该部门如果为null，证明一个都没有: 在取其一级部门共用的预算额度
            if (empty($codeitem) || empty($codeitem[0]['amount'])) {
                // 一级部门共用的
                $builder = $this->modelsManager->createBuilder();
                $builder->from(['c' => BudgetObjectDepartmentAmount::class]);
                $builder->columns('object_code,sum(amount) as amount');
                $builder->andWhere('department_id = :department_id:', ['department_id' => $firstDepartmentId]);
                $builder->andWhere('object_code = :level_code:', ['level_code' => $levelcode]);
                $builder->andWhere('organization_type = :organization_type:', ['organization_type' => $cost_store_type]);
                $builder->andWhere('is_delete=0');
                $builder->andWhere('amount_type=2');
                $builder->betweenWhere('month', $start_month, $season_end_month);
                $builder->groupBy('object_code');
                $codeitem = $builder->getQuery()->execute()->toArray();

                // 下面都用1级部门Id
                $sys_department_id = (empty($codeitem) || empty($codeitem[0]['amount'])) ? $sys_department_id : $firstDepartmentId;
            }

            // 如果该部门的一级部门预算为null: 则继续查找所属公司的共用预算
            if (empty($codeitem) || empty($codeitem[0]['amount'])) {
                $company_id = DepartmentModel::getCompanyIdByDepartmentId($sys_department_id);
                $builder = $this->modelsManager->createBuilder();
                $builder->from(['c' => BudgetObjectDepartmentAmount::class]);
                $builder->columns('object_code,sum(amount) as amount');
                $builder->andWhere('department_id = :department_id:', ['department_id' => $company_id]);
                $builder->andWhere('object_code = :level_code:', ['level_code' => $levelcode]);
                $builder->andWhere('organization_type = :organization_type:', ['organization_type' => $cost_store_type]);
                $builder->andWhere('is_delete=0');
                $builder->andWhere('amount_type=2');
                $builder->betweenWhere('month', $start_month, $season_end_month);
                $builder->groupBy('object_code');
                $codeitem = $builder->getQuery()->execute()->toArray();

                $sys_department_id = (empty($codeitem) || empty($codeitem[0]['amount'])) ? $sys_department_id : $company_id;
            }

            $obj_dep_idArr = [];
            foreach ($searchMonth as $k){
                $tmp = $sys_department_id."_".$levelcode."_".$cost_store_type."_".$k;
                $obj_dep_idArr[] = $tmp;
            }
            $reNos = [];
            $puNos = [];
            $opNos = [];

            $builder = $this->modelsManager->createBuilder();
            $builder->from(['c' => BudgetDetailLog::class]);
            $builder->inWhere('obj_dep_id',$obj_dep_idArr);

            $a_time = date("Y-m-d H:i:s",strtotime("-7 hours",strtotime($start_month."-01")));
            $b_time = date("Y-m-d H:i:s",strtotime("-7 hours",strtotime($deadline_at." 23:59:59")));
            $builder->betweenWhere('created_at',$a_time,$b_time);

            $logs = $builder->getQuery()->execute()->toArray();
            foreach ($logs as $log){
                $amount = $log['amount'];
                //报销
                if($log['type'] == self::ORDER_TYPE_1){
                    $reOccList[$levelcode] = isset($reOccList[$levelcode]) ? bcadd($reOccList[$levelcode],$amount) : $amount;
                    $reNos[] = $log['order_no'];
                }elseif($log['type'] == self::ORDER_TYPE_2){
                    $puOccList[$levelcode] = isset($puOccList[$levelcode]) ? bcadd($puOccList[$levelcode],$amount) : $amount;
                    $puNos[] = $log['order_no'];
                }elseif($log['type'] == self::ORDER_TYPE_3){
                    $opOccList[$levelcode] = isset($opOccList[$levelcode]) ? bcadd($opOccList[$levelcode],$amount) : $amount;
                    $opNos[] = $log['order_no'];
                }
                $occupyList[$levelcode] = isset($occupyList[$levelcode]) ? bcadd($occupyList[$levelcode],$amount) : $amount;

                if(!isset($allAmount[$levelcode][$log['order_no']])){
                    $allAmount[$levelcode][$log['order_no']] = 0;
                }
                $allAmount[$levelcode][$log['order_no']] += $amount;
            }


            if (!empty($codeitem)) {
                $allAmountArr[] = $codeitem[0];
            } else {
                $allAmountArr[] = [
                    'object_code' => $levelcode,
                    'amount' => 0
                ];
            }
        }

        if(!empty($reNos)){
            $reNos = array_values(array_unique($reNos));

            // 取预算详情
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['r' => Reimbursement::class]);
            $builder->leftjoin(Detail::class, "d.re_id=r.id", "d");
            $builder->leftjoin(BudgetObject::class, "b.id=d.budget_id", "b");
            $builder->andWhere('r.pay_status = :pay_status:', ['pay_status' => Enums::LOAN_PAY_STATUS_PAY]);
            $builder->inWhere('r.no', $reNos);
            $builder->groupBy('b.level_code,r.no');
            $builder->columns('b.level_code,r.no');
            $reList = $builder->getQuery()->execute()->toArray();
            foreach ($reList as $reItem) {
                // 总的使用金额
                $useList[$reItem['level_code']] = !isset($useList[$reItem['level_code']]) ? $allAmount[$reItem['level_code']][$reItem['no']] : bcadd($useList[$reItem['level_code']],$allAmount[$reItem['level_code']][$reItem['no']]);
                // 已使用的报销金额
                $reUsedList[$reItem['level_code']] = !isset($reUsedList[$reItem['level_code']]) ? $allAmount[$reItem['level_code']][$reItem['no']] : bcadd($reUsedList[$reItem['level_code']],$allAmount[$reItem['level_code']][$reItem['no']]);
            }
        }

        if(!empty($opNos)){
            $opNos = array_values(array_unique($opNos));
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['o' => OrdinaryPayment::class]);
            $builder->leftjoin(OrdinaryPaymentDetail::class, "od.ordinary_payment_id=o.id", "od");
            $builder->leftjoin(BudgetObject::class, "b.id=od.budget_id", "b");
            $builder->andWhere('o.pay_status = :pay_status:', ['pay_status' => Enums::LOAN_PAY_STATUS_PAY]);
            $builder->inWhere('o.apply_no', $opNos);
            $builder->groupBy('o.apply_no');
            $builder->columns('o.apply_no,b.level_code');
            $reList = $builder->getQuery()->execute()->toArray();

            foreach ($reList as $reItem) {
                // 总的使用金额
                $useList[$reItem['level_code']] = !isset($useList[$reItem['level_code']]) ? $allAmount[$reItem['level_code']][$reItem['apply_no']] : bcadd($useList[$reItem['level_code']],$allAmount[$reItem['level_code']][$reItem['apply_no']]);
                // 已使用的普通付款金额
                $opUsedList[$reItem['level_code']] = !isset($opUsedList[$reItem['level_code']]) ? $allAmount[$reItem['level_code']][$reItem['apply_no']] : bcadd($opUsedList[$reItem['level_code']],$allAmount[$reItem['level_code']][$reItem['apply_no']]);
            }
        }

        if(!empty($puNos)){
            $puNos = array_values(array_unique($puNos));
            $puList = PurchaseApply::find(
                [
                    'columns'=>'id',
                    'conditions' => 'pano in (:ids:) and status='.Enums::CONTRACT_STATUS_APPROVAL,
                    'bind' => ['ids' => implode(',',$puNos)]
                ]
            )->toArray();
            $paids = !empty($puList) ? array_values(array_column($puList,"id")) : [];

            $poids = [];
            if(!empty($paids)){
                $poList = PurchaseOrder::find(
                    [
                        'columns'=>'id',
                        'conditions'=> 'pa_id in ({ids:array}) and status='.Enums::CONTRACT_STATUS_APPROVAL,
                        'bind'=>['ids'=>$paids]
                    ]
                )->toArray();
                $poids = !empty($poList) ? array_values(array_column($poList,"id")) : [];
            }

            if(!empty($poids)){
                $ppList = PurchasePayment::find(
                    [
                        'conditions' => 'po_id in ({ids:array}) and pay_status='.Enums::LOAN_PAY_STATUS_PAY,
                        'bind'=>['ids'=>$poids]
                    ]
                )->toArray();

                $ppids = !empty($ppList) ? array_values(array_column($ppList,"id")) : [];
                $ppidToRate = !empty($ppList) ? array_column($ppList,'exchange_rate','id') : [];

                if(!empty($ppids)){
                    $builder = $this->modelsManager->createBuilder();
                    $builder->leftjoin(PurchasePaymentReceipt::class, "b.id=ppr.budget_id", "ppr");
                    $builder->leftjoin(BudgetObject::class, "b.id=ppr.budget_id", "b");
                    $builder->inWhere('ppr.ppid',$ppids);
                    $builder->groupBy('b.level_code');
                    $builder->columns('ppr.ppid,b.level_code,count(ppr.ticket_amount) ticket_amount');
                    $reList = $builder->getQuery()->execute()->toArray();
                    foreach ($reList as $reItem) {
                        $exchange_rate = $ppidToRate[$reItem['ppid']] ?? '0.000';
                        $t_amount = 0;
                        if (bccomp($exchange_rate, 0.000, 3) == 1) {
                            // 货币不是泰铢
                            $t_amount = bcmul($reItem['ticket_amount'],$exchange_rate, 3);
                        } else {
                            $t_amount = $reItem['ticket_amount'];
                        }
                        $useList[$reItem['level_code']] = !isset($useList[$reItem['level_code']]) ? $t_amount : bcadd($useList[$reItem['level_code']],$t_amount,3);
                        // 已使用的采购金额
                        $puUsedList[$reItem['level_code']] = !isset($puUsedList[$reItem['level_code']]) ? $t_amount : bcadd($puUsedList[$reItem['level_code']],$t_amount,3);
                    }
                }
            }
        }

        $list = [];
        GlobalEnums::init();
        // 组装表格数据
        foreach ($allAmountArr as $item) {
            $objectCode = $item['object_code'];
            $list[] = [
                $allLevelCode[$objectCode],
                $departName,
                $orgName,
                $deadline_at,
                GlobalEnums::$sys_default_currency_symbol,
                bcdiv($item['amount'], 1000, 2),
                isset($occupyList[$objectCode]) ? bcdiv($item['amount'] - $occupyList[$objectCode], 1000, 2) : bcdiv($item['amount'], 1000, 2),
                isset($occupyList[$objectCode]) ? bcdiv($occupyList[$objectCode], 1000, 2) : 0,
                isset($useList[$objectCode]) ? bcdiv($useList[$objectCode], 1000, 2) : 0,
                bcdiv($item['amount'], 1000, 2),
                isset($reOccList[$objectCode]) ? bcdiv($reOccList[$objectCode], 1000, 2) : 0,
                isset($opOccList[$objectCode]) ? bcdiv($opOccList[$objectCode], 1000, 2) : 0,
                isset($puOccList[$objectCode]) ? bcdiv($puOccList[$objectCode], 1000, 2) : 0,
                isset($reUsedList[$objectCode]) ? bcdiv($reUsedList[$objectCode], 1000, 2) : 0,
                isset($opUsedList[$objectCode]) ? bcdiv($opUsedList[$objectCode], 1000, 2) : 0,
                isset($puUsedList[$objectCode]) ? bcdiv($puUsedList[$objectCode], 1000, 2) : 0,
            ];
        }

        $header = [
            '预算科目',
            '部门',
            '组织机构',
            '查询截止日期',
            '币种',
            '预算总金额',
            '可使用预算',
            '占用中预算',
            '已使用预算',
            '预算导入金额',
            '报销占用金额',
            '普通付款占用金额',
            '采购占用金额',
            '报销已使用金额',
            '普通付款已使用金额',
            '采购已使用金额'
        ];

        return $this->exportExcel($header,$list,'budget_'.date("YmdHis"));
    }


    /**
     * 月度查询-查询获得预算数量
     * @param array $param 请求入参
     * @param bool $export 是否为导出
     * @param array $user 当前登录用户
     * @return array
     */
    public function budget_search(array $param, bool $export, array $user)
    {
        ini_set('memory_limit', '1536M');

        $code         = ErrCode::$SUCCESS;
        $message      = 'success';
        $real_message = '';
        $data         = [
            'items' => []
        ];
        try {
            //该部门 关联的所有 最底级别科目
            if (empty($param['year_at'])) {
                $y = date('Y-01-01');
            } else {
                $y = $param['year_at'] . '-01-01';
            }
            $year  = date('Y', strtotime($y));
            $month = array();
            for ($i = 1; $i <= 12; $i++) {
                $month[] = $year . '-' . str_pad($i, 2, "0", STR_PAD_LEFT);
            }
            $department_id = $param['department_id'];
            if (!$export) {
                  DataPermissionModuleConfigService::getInstance()->queryAuthority($department_id, $user, SysConfigEnums::SYS_MODULE_BUDGET);
            }
            //先查一条记录 确认是否是 共用额度
            $is_sub_department  = false;
            $common_object_code = [];
            $no_use_object_code = [];
            if (!empty($param['level_code'])) {
                $check = BudgetObjectDepartmentAmount::findFirst([
                    "conditions" => "department_id = :department_id: 
                            and object_code = :level_code:
                            and organization_type = :organization_type: 
                            and month = :month:
                            and is_delete = :is_delete:",
                    "bind"       => [
                        "department_id"     => $department_id,
                        'month'             => $month[0],
                        "level_code"        => $param['level_code'],
                        "organization_type" => $param['cost_store_type'],
                        "is_delete" => GlobalEnums::IS_NO_DELETED,
                    ]
                ]);

            } else {
                $check_res = BudgetObjectDepartmentAmount::find([
                    "conditions" => "department_id = :department_id: 
                            and month = :month:
                            and organization_type = :organization_type: 
                            and is_delete = :is_delete:  group by object_code",
                    "bind"       => [
                        "department_id"     => $department_id,
                        'month'             => $month[0],
                        "organization_type" => $param['cost_store_type'],
                        "is_delete"         => GlobalEnums::IS_NO_DELETED,
                    ]
                ])->toArray();
                if (!empty($check_res)) {

                    foreach ($check_res as $k => $v) {
                        if ($v['amount_type'] == 2) {
                            $is_sub_department    = true;
                            $common_object_code[] = $v['object_code'];
                        } else {
                            $no_use_object_code[] = $v['object_code'];
                        }
                    }
                }


            }
            //原有逻辑需要变更 为 需要添加新的限制条件 是否共用 因为 一级部门共用公司并不一定共用
            //查询 一级部门 和 公司
            $is_common_use = false;
            if ((isset($check->amount_type) && $check->amount_type == 2) || $is_sub_department) {
                $second_department_id = DepartmentModel::getSecondDepartmentIdByDepartmentId($department_id);
                $first_department_id  = DepartmentModel::getFirstDepartmentIdByDepartmentId($department_id);
                $company_id           = DepartmentModel::getCompanyIdByDepartmentId($department_id);
                //查询二级部门
                if (!empty($second_department_id)) {
                    $department_id_item[] = $second_department_id;
                }
                // 如果查询到是空 还用 传入id
                if (!empty($first_department_id)) {
                    $department_id_item[] = $first_department_id;
                }

                if (!empty($company_id)) {
                    $department_id_item[] = $company_id;
                }
                $is_common_use = true;
            }

            $department_id_item[] = $department_id;


            $department_id_item = array_filter(array_unique($department_id_item));

            $params = [
                'organization_type'  => $param['cost_store_type'],
                'month'              => $month,
                'department_id_item' => $department_id_item,
                'level_code'         => $param['level_code']
            ];

            if (!empty($param['level_code'])) {
                if (!$is_common_use) {
                    $params['department_id_item'] = [$department_id];
                }
                $items = $this->getBudget($params, $is_common_use);
            } else {

                if (!empty($common_object_code)) {
                    $params['level_code'] = $common_object_code;
                    $items_1              = $this->getBudget($params, true);
                }

                if (!empty($no_use_object_code)) {
                    $params['level_code']         = $no_use_object_code;
                    $params['department_id_item'] = [$department_id];
                    $items_2                      = $this->getBudget($params, false);
                }


                $items = array_merge($items_1 ?? [], ($items_2 ?? []));
            }

            if (!empty($items)) {
                $department_info    = (new DepartmentService())->getDepartmentInfoByIds($department_id_item);
                $department_info    = array_column($department_info, 'name', 'id');
                $amount_type_status = self::$amount_type_status;
                $t                  = static::$t;

                // 初始化币种
                GlobalEnums::init();
                //整理 数据
                $add_hour = $this->config->application->add_hour;
                $level_code_arr = [];
                foreach ($items as &$li) {
                    $li['organization_type'] = ($li['organization_type']) == 1 ? 2 : 1;

                    //时间 不知道用不用加7小时
                    $li['created_at'] = date('Y-m-d H:i:s', strtotime($li['created_at']) + $add_hour * 3600);
                    $li['updated_at'] = date('Y-m-d H:i:s', strtotime($li['updated_at']) + $add_hour * 3600);

                    //金额 缩小1000

                    $li['amount']      = $li['amount'] / 1000;
                    $li['amount_left'] = $li['amount_left'] / 1000;

                    $li['currency']               = GlobalEnums::$sys_default_currency_symbol;
                    $li['department_name']        = $department_info[$department_id] ?? '';
                    $li['amount_type_text']       = $t[$amount_type_status[$li['amount_type']] ?? ''];
                    $li['organization_type_text'] = isset($li['organization_type']) ? static::$t->_(Enums::$payment_cost_store_type[$li['organization_type']]) : '';
                    if ($li['amount'] > 0 || $li['amount_left'] > 0) {
                        if (!in_array($li['level_code'], $level_code_arr)) {
                            $level_code_arr[] = $li['level_code'];
                        }
                    }


                }
                //剔除预算额度和占用均为0的数据
                foreach ($items as $k => $value) {
                    if (!in_array($value['level_code'], $level_code_arr)) {
                        unset($items[$k]);
                    }
                }
            }
            $items = array_values($items);

            if ($export) {
                return $items;
            }
            $data['items'] = $items;

        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('财务预算查询月度列表异常信息:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];

    }

    public function budgetHeader()
    {
        $header = [
            static::$t->_('budget_adjust_level_code'),//预算科目
            static::$t->_('csr_field_department_name'),//部门
            static::$t->_('csr_budget_month'),//月份
            static::$t->_('csr_budget_organization_type'),//网点/总部
            static::$t->_('csr_budget_is_use'),//是否共用
            static::$t->_('csr_budget_import'),//导入预算
            static::$t->_('csr_budget_adj'),//剩余预算
            static::$t->_('re_filed_currency_text'),//币种
            static::$t->_('create_time'),//创建时间
            static::$t->_('hc_summary_hc_update_time'),//更新时间
        ];
        return $header;
    }



    public function budgetExport($param, $user)
    {
        $code         = ErrCode::$SUCCESS;
        $message      = 'success';
        $real_message = '';

        try {
            DataPermissionModuleConfigService::getInstance()->queryAuthority($param['department_id'], $user, SysConfigEnums::SYS_MODULE_BUDGET);
            $file_name = 'budget_month_search_' . date('Ymd') . '.xlsx';
            $header   = $this->budgetHeader();
            $data     = $this->budget_search($param, true, $user);
            $new_data = [];
            if (!empty($data)) {
                foreach ($data as $k => $v) {
                    $new_data[] = [
                        $v['object_name'],
                        $v['department_name'],
                        $v['month'],
                        $v['organization_type_text'],
                        $v['amount_type_text'],
                        $v['amount'],
                        $v['amount_left'],
                        $v['currency'],
                        $v['created_at'],
                        $v['updated_at'],
                    ];
                }
            }
            $result = $this->exportExcel($header, $new_data, $file_name);

        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->getDI()->get('logger')->warning('财务预算-预算月度查询导出: ' . $real_message);
        }
        if (isset($result['code']) && $result['code'] == ErrCode::$SUCCESS) {
            $result['message'] = 'success';
            $result['data']    = $result['data'];
        } else {
            $result['code']    = $code;
            $result['message'] = $message;
            $result['data']    = '';
        }
        return $result;
    }
    /**
     * 导出系统预算
     * */
    public function exportAllBudget()
    {
        ini_set('memory_limit', '2024M');
        $code         = ErrCode::$SUCCESS;
        $message      = 'success';
        $real_message = '';

        try {
            Enums\GlobalEnums::init();
            $currency = Enums\GlobalEnums::$sys_default_currency_symbol;

            $budget_year = BaseService::getCurrentYear();
            $department_budget_list = BudgetObjectDepartmentAmount::find([
                'conditions' => 'month LIKE :budget_year: AND is_delete = 0',
                'bind'       => ['budget_year' => "$budget_year%"],
                'columns'    => ['department_id', 'object_code','month', 'organization_type', 'amount_type', 'amount', 'amount_left','created_at','updated_at'],
            ])->toArray();

            $all_dep = DepartmentModel::find([
                "conditions" => "deleted = :deleted: ",
                "bind"=>["deleted" => 0],
                "columns" => "id, name"
            ])->toArray();
            $all_dep = array_column($all_dep,'name','id');
            // 每个部门+科目 存一个数组
            $department_obj_data = [];
            $new_data = [];
            if (!empty($department_budget_list)) {
                $payment_cost_store_type=Enums::$payment_cost_store_type;
                $amount_type_status = self::$amount_type_status;
                $t = static::$t;
                $name   = $this->get_lang_column(static::$language);
                $budget_object_list = BudgetObject::find([
                    'columns' => 'level_code,name_th,name_en,name_cn',
                ])->toArray();

                $budget_object_list = array_column($budget_object_list,$name,'level_code');
                foreach ($department_budget_list as $k => $v) {
                    $key                         = trim($v['department_id']) . '_' . trim($v['object_code']).'_'.$v['amount_type'];
                    $department_obj_data[$key][] = $v;
                }
                unset($department_budget_list );
                foreach ($department_obj_data as $k_1 => &$v_1) {
                    $amount_flag = false;
                    foreach ($v_1 as $k_2 => &$v_2) {
                        $v_2['department_name']  = $all_dep[$v_2['department_id']] ?? '';
                        $v_2['amount_type_name'] = $t[$amount_type_status[$v_2['amount_type']] ?? ''] ?? '';
                        $v_2['object_code_name'] = $budget_object_list[$v_2['object_code']]??'';
                        $v_2['amount']           = $v_2['amount'] / 1000;
                        $v_2['amount_left']      = $v_2['amount_left'] / 1000;

                        if ($v_2['amount'] != 0) {
                            $amount_flag = true;
                        }
                    }
                    if (!$amount_flag) {
                        unset($department_obj_data[$k_1]);
                    }
                }
                foreach ($department_obj_data as $item){
                    foreach ($item as $item_1){
                        $item_1['organization_type'] = ($item_1['organization_type'] == 1) ? 2 : 1;

                        $new_data[] =[
                            $item_1['object_code_name'],
                            $item_1['department_name'],
                            $item_1['month'],
                            isset($item_1['organization_type']) ? static::$t->_($payment_cost_store_type[$item_1['organization_type']]) : '',
                            $item_1['amount_type_name'],
                            $item_1['amount'],
                            $item_1['amount_left'],
                            $currency,
                            date('Y-m-d H:i:s', strtotime($item_1['created_at']) + get_sys_time_offset() * 3600),
                            date('Y-m-d H:i:s', strtotime($item_1['updated_at']) + get_sys_time_offset() * 3600),
                        ];
                    }
                }
            }

            $file_name = 'budget_all_budget_' . date('Ymd') . '.xlsx';
            $header   = $this->budgetHeader();

              $result =   $this->exportExcel($header,$new_data,$file_name);


        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->getDI()->get('logger')->warning('财务预算-预算全年导出: ' . $real_message);
        }
        if (isset($result['code']) && $result['code'] == ErrCode::$SUCCESS) {
            $result['message'] = 'success';
            $result['data']    = $result['data'];
        } else {
            $result['code']    = $code;
            $result['message'] = $message;
            $result['data']    = '';
        }
        return $result;


    }

    public function getBudget($param, $is_common_use = false)
    {
        $builder = $this->modelsManager->createBuilder();
        //整理语言环境
        $name   = $this->get_lang_column(static::$language);
        $column = "o.id,o.level_code,o.{$name} as object_name,d.department_id,d.month,SUM(d.amount) AS amount, SUM(d.amount_left) AS amount_left,d.created_at,d.updated_at,d.amount_type,organization_type";
        $builder->from(['d' => BudgetObjectDepartmentAmount::class]);
        $builder->Join(BudgetObject::class, 'd.object_code=o.level_code', 'o');

        $builder->andWhere('d.organization_type = :organization_type:', ['organization_type' => $param['organization_type']]);
        $builder->andWhere('d.is_delete = :is_delete:', ['is_delete' => GlobalEnums::IS_NO_DELETED]);


        $builder->inWhere('d.month', $param['month']);

        $builder->inWhere('d.department_id', $param['department_id_item']);
        if (!empty($param['level_code'])) {
            if (is_array($param['level_code'])) {
                $builder->inWhere('o.level_code', $param['level_code']);

            } else {
                $builder->andWhere('o.level_code = :level_code:', ['level_code' => $param['level_code']]);

            }
        }

        if ($is_common_use) {
            $builder->andWhere('d.amount_type = :amount_type:', ['amount_type' => self::IS_COM_USE]);
        } else {
            $builder->andWhere('d.amount_type = :amount_type:', ['amount_type' => self::IS_NO_COM_USE]);
        }

        $builder->groupBy('d.object_code,d.month');

        $builder->columns($column);

        $items = $builder->getQuery()->execute()->toArray();

        return $items ?? [];

    }


    /**
     * 预算科目列表: 名称搜索
     *
     * @param string $name
     * @param integer $is_deleted 0未删除、1已删除、2全部
     * @return array
     */
    public function getBudgetObjectListByName(string $name, int $is_deleted = 0)
    {
        return BudgetObjectRepository::getInstance(static::$language)->getListByName($name, 'id', $is_deleted);
    }

    /**
     * 预算科目下的明细列表
     *
     * @param int $budget_id
     * @return array
     */
    public function getBudgetProductListByBudgetId(int $budget_id)
    {
        // 验证科目是否存在
        $budget_model = BudgetObjectRepository::getInstance()->getOneById($budget_id);
        if (empty($budget_model)) {
            return [];
        }

        // 获取科目下的明细
        return BudgetObjectProductRepository::getInstance(static::$language)->getListByBudgetId($budget_model->level_code);
    }

    /**
     * 通过科目id获取科目名称
     * @param $budget_ids
     * @return array
     */
    public function getBudgetByIds($budget_ids)
    {
        //获取科目名称
        $budget_name_key = $this->get_lang_column(static::$language);

        $budget_data = BudgetObjectRepository::getInstance()->getDataByIds($budget_ids, 'id,' . $budget_name_key);
        return array_column($budget_data, $budget_name_key, 'id');
    }

    /**
     * 获取特定条件下的预算科目
     * @param array $params 请求参数组
     * @return array
     */
    public function getBudgets(array $params)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        try {
            $params['pageSize']   = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $params['pageSize'];
            $data                 = BudgetObjectRepository::getInstance()->getBudgetListByParams($params, static::$language);
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('获取费用预提默认配置项异常信息: ' . $e->getMessage());
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }
}


