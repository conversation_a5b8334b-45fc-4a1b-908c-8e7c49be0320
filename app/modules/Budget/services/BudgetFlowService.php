<?php

namespace App\Modules\Budget\Services;


use App\Library\Enums;
use App\Library\Enums\BudgetAdjustEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;

use App\Modules\Workflow\Models\WorkflowRequestModel;
use App\Modules\Workflow\Services\AbstractFlowService;
use App\Modules\Workflow\Services\WorkflowServiceV2;
use App\Modules\Budget\Models\BudgetSourceMainModel;
use App\Modules\Budget\Models\BudgetSourceDataModel;
use Phalcon\Mvc\Model\Resultset;
use Phalcon\Mvc\Phalcon\Mvc\Model;

class BudgetFlowService extends AbstractFlowService
{

    /**
     * @param $budget_id
     * @param $note
     * @param $user
     * @return array
     */
    public function approve($budget_id,$note, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $work_req = $this->getRequest($budget_id);
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            if (empty($work_req->id)) {
                throw new BusinessException('获取工作流批次失败', ErrCode::$CONTRACT_GET_WORK_REQUEST_ERROR);
            }
            $budget = BudgetSourceMainModel::findFirst([
                'id = :id:',
                'bind' => ['id' =>  $budget_id],
            ]);
            if(empty($budget)){
                throw new BusinessException('获取工作流批次失败', ErrCode::$CONTRACT_GET_WORK_REQUEST_ERROR);
            }


            if ($budget->apply_status == BudgetAdjustEnums::BUDGET_IMPORT_STATUS_APPROVAL) {
                throw new ValidationException(static::$t->_('workflow_has_been_approval'), ErrCode::$VALIDATE_ERROR);
            }

            if ($budget->apply_status== BudgetAdjustEnums::BUDGET_IMPORT_STATUS_REJECTED) {
                throw new ValidationException(static::$t->_('workflow_has_been_rejected'), ErrCode::$VALIDATE_ERROR);
            }

            $biz_data = [
                'submitter_id' => $budget->create_id
            ];
            $result = (new WorkflowServiceV2())->doApprove($work_req, $user, $this->getBudgetWorkflowParams($biz_data),$note);
            if (!empty($result->approved_at)) {

                $bool = $budget->save([
                    'apply_status' =>  BudgetAdjustEnums::BUDGET_IMPORT_STATUS_APPROVAL,
                    'updated_at' => date('Y-m-d H:i:s'),
                ]);


                $db->updateAsDict(
                    (new BudgetSourceDataModel)->getSource(),
                    [
                        'approved_status' => BudgetAdjustEnums::BUDGET_IMPORT_STATUS_APPROVAL
                    ],
                    ['conditions' => 'main_id=?', 'bind' => $budget_id]
                );

                if ($bool === false) {
                    throw new BusinessException('财务预算-审批通过失败',ErrCode::$VALIDATE_ERROR);
                }
            }
            $db->commit();
            if (!empty($result->approved_at) && 1 == $budget->is_effective) {
                //终审且未生效
                $isEffective = BudgetSourceDataService::getInstance()->budgetEffective($budget_id, $user, $budget->year_at);
                if (!$isEffective) {
                    $this->logger->error('budget-effective-failed:' . $budget_id);
               }
            }

        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->warning('budget-approve-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
        ];
    }

    /**
     * @param $budget_id
     * @param $note
     * @param $user
     * @return array
     */
    public function reject($budget_id, $note, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $work_req = $this->getRequest($budget_id);
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            if (empty($work_req->id)) {
                throw new BusinessException('获取工作流批次失败', ErrCode::$CONTRACT_GET_WORK_REQUEST_ERROR);
            }
            $budget = BudgetSourceMainModel::findFirst([
                'id = :id:',
                'bind' => ['id' => $budget_id]
            ]);
            if ($budget->apply_status == BudgetAdjustEnums::BUDGET_IMPORT_STATUS_APPROVAL) {
                throw new ValidationException(static::$t->_('workflow_has_been_approval'), ErrCode::$VALIDATE_ERROR);
            }

            $biz_data = [
                'budget_id' => $budget_id,
                'submitter_id' => $budget->create_id
            ];
            $result = (new WorkflowServiceV2())->doReject($work_req, $user, $this->getBudgetWorkflowParams($biz_data), $note);
            if ($result === false) {
                throw new BusinessException('审批流拒绝失败', ErrCode::$CONTRACT_WORK_FLOW_REJECT_ERROR);
            }
            $bool = $budget->save([
                'apply_status' => BudgetAdjustEnums::BUDGET_IMPORT_STATUS_REJECTED,
                'updated_at' => date('Y-m-d H:i:s'),
            ]);

            if ($bool === false) {
                throw new BusinessException('更新预算审批状态失败', ErrCode::$CONTRACT_UPDATE_ERROR);
            }

            $db->updateAsDict(
                (new BudgetSourceDataModel)->getSource(),
                [
                    'approved_status' => 2
                ],
                ['conditions' => 'main_id=?', 'bind' => $budget_id]
            );
            $db->commit();
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->warning('budget-reject-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
        ];
    }



    /**
     * @param $budgetId
     * @return \Phalcon\Mvc\Model
     */
    public function getRequest($budgetId)
    {
        //找最新的
        return WorkflowRequestModel::findFirst(
            [
                'biz_type = :type: and biz_value= :lid:',
                'bind' => ['type'=> Enums::BUDGET_OB_TYPE ,'lid' => $budgetId],
                'order'=>'id desc'
            ]
        );
    }

    /**
     * @param $budgetId
     * @param $user
     * @return Resultset|Model
     * @throws BusinessException
     * @throws ValidationException
     */
    public function createRequest($budgetId, $user)
    {
        $budget = BudgetSourceMainModel::findFirst([
            'id = :id:',
            'bind' => ['id' => $budgetId]
        ]);

        $data['id'] =  $budget->id;
        $data['name'] =  $budget->no . '财务预算审批申请';
        $data['biz_type'] = Enums::BUDGET_OB_TYPE;
        $data['flow_id'] = $this->getFlowId();
        $data['submitter_id'] = $budget->create_id;

        return (new WorkflowServiceV2())->createRequest($data, $user, $this->getBudgetWorkflowParams($data));
    }
    public function cancel($budget_id, $note, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $work_req = $this->getRequest($budget_id);
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();

            $budget = BudgetSourceMainModel::findFirst([
                'id = :id:',
                'bind' => ['id' => $budget_id]
            ]);
            if (empty($budget)) {
                throw new ValidationException(static::$t->_('budget_main_not_exist'), ErrCode::$BUSINESS_ERROR);
            }
            $biz_data = [
                'budget_id' => $budget_id,
                'submitter_id' => $budget->create_id
            ];
            $result = (new WorkflowServiceV2())->doCancel($work_req, $user, $this->getBudgetWorkflowParams($biz_data), $note);
            if ($result === false) {
                throw new BusinessException('审批流拒绝失败', ErrCode::$CONTRACT_WORK_FLOW_REJECT_ERROR);
            }
            $bool = $budget->save([
                'apply_status' => BudgetAdjustEnums::BUDGET_IMPORT_STATUS_CANCEL,
                'updated_at'   => date('Y-m-d H:i:s'),
                'remark'       => $note
            ]);
            if ($bool === false) {
                throw new BusinessException(static::$t->_('budget_main_update_failed'), ErrCode::$BUSINESS_ERROR);
            }
            $db->updateAsDict(
                (new BudgetSourceDataModel)->getSource(),
                [
                    'approved_status' => BudgetAdjustEnums::BUDGET_IMPORT_STATUS_CANCEL
                ],
                ['conditions' => 'main_id=?', 'bind' => $budget_id]
            );

            $db->commit();
        } catch (ValidationException $e) {               //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {                 //业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {                       //系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->warning('budget-main-cancel-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
        ];
    }

    /**
     * 获取预算审批流需要数据
     * @param $budget
     * @return array
     */
    public function getBudgetWorkflowParams($budget)
    {
       return $budget;
    }

    /**
     * @inheritDoc
     */
    public function getFlowId($model=null)
    {
        return Enums::BUDGET_OBJECT_FLOW_ID;
    }
}