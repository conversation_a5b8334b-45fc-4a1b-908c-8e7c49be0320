<?php

namespace App\Modules\Budget\Models;

use App\Library\Enums;
use App\Models\Base;
use App\Modules\User\Models\AttachModel;

class BudgetAdjustModel extends Base
{

    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        // setSource()方法指定数据库表
        $this->setSource('budget_adjust');

        $this->hasMany(
            'id',
            BudgetAdjustDetailModel::class,
            'budget_adjust_id', [
                "alias" => "Details",
            ]
        );
    }

    /**
     * @param $condition
     * @return mixed
     */
    public static function getFirst($condition)
    {
        return parent::findFirst($condition);
    }

    /**
     * @param null $parameters
     * @return mixed
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }
}
