<?php
/**
 * Created by PhpStorm.
 * Date: 2021/7/16
 * Time: 20:20
 * 采购单入库相关
 */

namespace App\Modules\Purchase\Controllers;

use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Modules\Purchase\Services\BaseService;
use App\Modules\Purchase\Services\OrderService;
use App\Modules\Purchase\Services\ScmService;
use App\Modules\Purchase\Services\StorageService;
use App\Library\Validation\Validation;
use App\Util\RedisKey;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class StorageController extends BaseController
{
    /**
     * 新增入库单
     * @Permission(action='my.storage.apply')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/28468
     * @return Response|ResponseInterface
     */
    public function createAction()
    {
        try{
            if ($this->request->isPost()) {
                $data = $this->request->get();
                StorageService::getInstance()->validationCreate($data);

                // 加锁
                $lock_key = md5(RedisKey::PURCHASE_STORAGE_SAVE_LOCK . $this->user['id']);
                $res = $this->atomicLock(function () use ($data) {
                    return StorageService::getInstance()->createOrder($this->user, $data);
                }, $lock_key, 20);

                return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);

            } else {
                $res = StorageService::getInstance()->defaultData($this->user);

                if($res['code'] == ErrCode::$SUCCESS) {
                    return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
                }

                return $this->returnJson($res['code'], $res['message']);
            }
        } catch (ValidationException $e) {      //校验错误，可对外抛出
            return $this->returnJson(ErrCode::$VALIDATE_ERROR,$e->getMessage());
        } catch (BusinessException $e) {
            //业务错误（数据错误等），不可对外抛出
            $this->logger->warning('purchaseStorage-create-failed:' . $e->getMessage());
            return $this->returnJson($e->getCode(),$this->t->_('retry_later'));
        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $this->logger->warning('purchaseStorage-create-failed:' . $e->getMessage());
            return $this->returnJson(ErrCode::$SYSTEM_ERROR,$this->t->_('retry_later'));
        }
    }

    /**
     * 采购单入库详情
     *
     *  @Token
     *  @return Response|ResponseInterface
     * */
    public function  poStockDetailAction(){
        $data = $this->request->get();
        $id = $this->request->get('id');
        try {
            Validation::validate($data, ['id'=>'Required|IntGe:1|>>>:'.$this->t->_('purchase_order_id_required')]);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }
        $res = StorageService::getInstance()->purchaseOrderDetail($id, $this->user['id']);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }


    /**
     * 我的-入库单列表
     * @Permission(action='my.storage.search')
     * @return Response|ResponseInterface
     */
    public function listAction()
    {
        $params = $this->request->get();
        $res = StorageService::getInstance()->getList($params, $this->user);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '',$res['data']);
        }
        return $this->returnJson($res['code'],$res['message']);
    }
    /**
     * 数据查询-入库单列表
     * @Permission(action='data.storage.search')
     * @return Response|ResponseInterface
     */
    public function querylistAction()
    {
        $params = $this->request->get();
        $res = StorageService::getInstance()->getList($params, $this->user, BaseService::LIST_TYPE_DATA);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '',$res['data']);
        }
        return $this->returnJson($res['code'],$res['message']);
    }


    /**
     *我的入库单- 入库单详情
     * @Permission(action='my.storage.view')
     *
     * @return Response|ResponseInterface
     */
    public function detailAction()
    {
        $data = $this->request->get();
        $id = $this->request->get('id');
        try {
            Validation::validate($data, ['id'=>'Required|IntGe:1']);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }
        $res = StorageService::getInstance()->getDetail($id);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);

    }
    /**
     * 数据查询-入库单详情
     * @Permission(action='data.storage.view')
     * @return Response|ResponseInterface
     */
    public function publicDetailAction()
    {
        $data = $this->request->get();
        $id = $this->request->get('id');
        try {
            Validation::validate($data, ['id'=>'Required|IntGe:1']);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }
        $res = StorageService::getInstance()->getDetail($id);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);

    }


    /**
     * 回调oa
     * @api https://yapi.flashexpress.pub/project/133/interface/api/45259
     *
     */
    public function dealScmAction()
    {
        $data = $this->request->get();
        Validation::validate($data, StorageService::$validate_recall_param);
        $lock_key = md5(RedisKey::PURCHASE_STORAGE_DEAL_SCM_LOCK . $data['orderSn']);
        $res = $this->atomicLock(function () use ($data) {
            return StorageService::getInstance()->dealScmStatus($data);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t->_('sys_processing'), $res['data'] ?? []);
    }

    /**
     * 撤回scm 入库单
     * @Permission(action='my.storage.recall')
     *
     * @return Response|ResponseInterface
     */
    public function reCallOrderAction()
    {
        $data = $this->request->get();
        $id = $this->request->get('id');
        $mark = $this->request->get('recall_mark');
        try {
            Validation::validate($data, ['id'=>'Required|IntGe:1']);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }
        $res = StorageService::getInstance()->recallOrder($id,$mark);

        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);


    }

    /**
     * 删除入库单
     * @Permission(action='my.storage.delete')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/55753
     * @return Response|ResponseInterface
     */
    public function deleteAction()
    {
        $data = $this->request->get();
        $id = $this->request->get('id');
        try {
            Validation::validate($data, ['id'=>'Required|IntGe:1']);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }
        $res = StorageService::getInstance()->delete($id);

        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);

    }

    /**
     * 入库单公共枚举
     * @Token
     */
    public function getPurchaseParamsAction()
    {
        $res = StorageService::getInstance()->getPurchaseParams();
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }


    /**
    * 采购申请单-数据下载
    * @Token
    * @Date: 2021-11-03 14:35
    * @author: peak pan
    * @return:
    **/
    public function exportAction()
    {
        $params = $this->request->get();

        $lock_key = md5('purchase_storage_product_export_lock' . '_' . $this->user['id']);
        $result = $this->atomicLock(function() use ($params){
            return StorageService::getInstance()->export($params,$this->user, BaseService::LIST_TYPE_DATA);
        }, $lock_key, 30);

        if ($result['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '',$result['data']);
        }
        return $this->returnJson($result['code'],$result['message']);

    }


    /**
     * 仓库枚举
     * @Token
     */
    public function getWarehouseListAction()
    {
        $data = $this->request->get();
        try {
            Validation::validate($data, ['mach_code'=>'Required|StrLenGeLe:1,128']);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }
        $res = StorageService::getInstance()->getWarehouseList($data['mach_code']);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 入库单搜索po单号
     * @Token
     */
    public function getPurchaseOrderOptionsAction()
    {
        $data = $this->request->get();
        try {
            Validation::validate($data, ['keywords'=>'Required|StrLenGe:1']);
            $res = StorageService::getInstance()->getPurchaseOrderOptions($data['keywords']);
            return $this->returnJson(ErrCode::$SUCCESS, '', $res);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->error('getPurchaseOrderOptions error: code='.$e->getCode() .' message='.$e->getMessage().' trace='.$e->getTraceAsString());
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $this->t->_('retry_later'));
        }

    }

}
