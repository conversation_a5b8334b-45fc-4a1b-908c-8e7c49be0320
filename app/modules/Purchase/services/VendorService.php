<?php


namespace App\Modules\Purchase\Services;


use App\Library\ErrCode;
use App\Modules\Purchase\Models\Vendor;
use App\Modules\Vendor\Services\ListService;

class VendorService extends BaseService
{
    private static $instance;

    private function __construct(){
    }

    /**
     * @return VendorService
     */
    public static function getInstance(){
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 根据供应商id 或者 供应商名称搜索
     */
    public function searchVendorList($search_name) {
        try {
            $list = Vendor::find([
                'columns' => 'vendor_id, vendor_name as vendor, company_address as vendor_addr, contact as vendor_contact, contact_phone as vendor_phone, contact_email as vendor_email,sap_supplier_no,bank_no,bank_code,bank_account_name,tax_number as vendor_tax_number',
                'conditions' => '(vendor_id LIKE ?1 or vendor_name LIKE ?1 )',
                'bind'       => [
                    1 => '%'. $search_name .'%',
                ]
            ]);

            // 获取静态配置code 与 翻译key
            $list =$list->toArray();
            //self::setLanguage('en');
            $static_item = ListService::getInstance()::getStaticTranslationCodeItem();
            $t           = static::$t;
            foreach ($list as &$item) {
                $bank_name_key = $static_item['bank_item'][$item['bank_code']] ?? '';

                $item['bank_name'] = $t[$bank_name_key] ?? '';
                unset($item['bank_code']);
            }

            return [
                'code' => ErrCode::$SUCCESS,
                'message' => '',
                'data' =>$list
            ];

        } catch (\Exception $e) {
            return [
                'code' => ErrCode::$SYSTEM_ERROR,
                'message' => static::$t->_('retry_later'),
                'data' => $e->getMessage()
            ];
        }
    }
}