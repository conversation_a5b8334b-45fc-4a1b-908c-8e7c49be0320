<?php

namespace App\Modules\Purchase\Services;

use App\Library\Enums;
use App\Library\ErrCode;
use App\Modules\User\Models\DepartmentModel;

class DepartmentService extends BaseService
{
    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return CategoryService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self();
        }
        return self::$instance;
    }


    public function getList($pid){

        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];


        try {
            $list = DepartmentModel::find([
                "conditions"=>"ancestry_v3 like :pid: and deleted=0 and ( id!=".Enums::DEPARTMENT_ID_IT_PRODUCTS." or id = :id: )",
                "bind"=>["pid"=>$pid."/%", "id"=>$pid]
            ])->toArray();


            if(empty($list)){
                throw new \Exception("no department");
            }

            $data = [];

            foreach ($list as $k=>$v){
                $tmp = [];
                $tmp['id'] = $v['id'];
                $tmp['name']=$v['name'];
                $tmp['parent_id'] = $v['ancestry'];

                $data[] = $tmp;
            }

            //$data = array_values($data);
        }catch (\Exception $e){
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->getDI()->get('logger')->warning('apply-get-department-failed:' . $real_message);
        }


        return [
            'code' => $code,
            'message' => $message,
            'data' =>$data
        ];
    }
}
