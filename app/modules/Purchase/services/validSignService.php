<?php
/**
 * Created by PhpStorm.
 * Date: 2021/7/21
 * Time: 15:23
 */

namespace App\Modules\Purchase\Services;

use App\Library\Validation\ValidationException;

class validSignService extends BaseService
{

    private $app_secret;

    public function __construct()
    {
        $this->app_secret = env('app_sign_key', '3acf16259def65456fc2a68ab5e10d96');
    }

    /**
     * @param
     * @return bool
     */
    public function validator($data)
    {
        /**
         * 验证规则
         * 1. 验证 参数
         * 2. 验证时间 有效时间3s
         * 3. 验证签名
         */

        if (!$this->validatorTime($data['timestamp'])) {
            $this->abnormal("请求超时！");
        }
        if (!($this->makeSign($data) === $data['sign'])) {
            $this->abnormal("签名错误！");
        }
        // 验证通过
        return true;
    }


    public function getSign($param)
    {
        if (empty($param['timestamp'])) $param['timestamp'] = time();

        return [
            'timestamp' => $param['timestamp'],
            'sign'      => $this->makeSign($param)
        ];
    }

    // 验证签名
    private function makeSign($param)
    {
        foreach ($param as $k => $v) {
            if (is_array($v)) {
                $param[$k] = json_encode($v);
            }
        }

        unset($param['sign']);

        $param['app_secret'] = $this->app_secret;

        ksort($param);


        $str = http_build_query($param);
        $str = strtolower($str);

        $str = trim($str);
        return md5($str);
    }

    // 验证时间
    private function validatorTime($timestamp)
    {
        return (time() - $timestamp) <= 120;
    }

    // 抛出异常 弹窗 返回上一级页面
    private function abnormal($message)
    {
        throw new ValidationException ($message);
    }
}