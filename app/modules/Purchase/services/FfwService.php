<?php

namespace App\Modules\Purchase\Services;

use App\Library\ErrCode;
use App\Library\Validation\ValidationException;

/**
 * scm系统 接口Service
 */
class FfwService extends BaseService
{

    private static $apiPath;
    private static $merchantID;
    private static $merchantPW;
    private static $scmConfig;

    public function __construct($mach_code = ''){
        self::$apiPath = env('scm_interface_url','https://open.flashfulfillment.co.th/');
        self::$scmConfig  = $this->getScmConfig($mach_code);
        self::$merchantID = (self::$scmConfig)['mach_code'];
        self::$merchantPW = (self::$scmConfig)['mach_pwd'];
    }

    private function getScmConfig($mach_code)
    {
        $scm_config = '';
        $scm_configs = $this->scmCargoOwner(self::SCM_TYPE_PU);
        if (empty($scm_configs)) {
            $this->getDI()->get('logger')->warning('scm config fail');
            return false;
        }

        foreach ($scm_configs as $key => $value) {
            if ($value['mach_code'] == $mach_code) {
                $scm_config = $value;
                break;
            }

        }
        if(empty($scm_config)){
            $this->getDI()->get('logger')->warning('scm config fail'.$mach_code);
            return false;
        }
        return $scm_config;
    }
    /**
     * 发送请求
     *
     * @param String $method
     * @param Array $postData
     * @return Array
     */
    public function postRequest($method, $postData)
    {
        try {
            $this->getDI()->get('logger')->info('ffw_request_log:' .'method:'.$method.'====param:'.$postData);

            $curl = curl_init();
            $header[] = "Content-type: application/x-www-form-urlencoded";
            $header[] = "Accept: application/json";
            $header[] = "Accept-Language: " . static::$language;
            curl_setopt($curl, CURLOPT_URL, self::$apiPath . $method);
            curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false); // SSL certificate
            curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($curl, CURLOPT_HEADER, 0);
            curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($curl, CURLOPT_POST, true); // post
            curl_setopt($curl, CURLOPT_POSTFIELDS, $postData); // post data
            curl_setopt($curl, CURLOPT_TIMEOUT, 10);
            $responseText = curl_exec($curl);
            if (curl_errno($curl)) {
                echo 'Errno' . curl_error($curl);
            }
            curl_close ( $curl );
            $data = json_decode($responseText,true);
            $this->getDI()->get('logger')->info('scm_response_log:' .'method:'.$method.'====data:'.$responseText);

            if ($data['code'] != ErrCode::$SUCCESS) {
                new ValidationException('postRequest error', ErrCode::$SYSTEM_ERROR);
            }
            $data = $data['data'];
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
            $data = [];
        } catch (\Exception $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
            $this->getDI()->get('logger')->warning('flashfulfillment-postRequest-failed:' . $message);
        }

        return [
            'code' => $code ?? ErrCode::$SUCCESS,
            'message' => $message ?? '',
            'data' => $data,
        ];
    }

    /**
     * 绑定请求参数
     *
     * @param Array $data_arr
     * @return String
     * $is_over   0 是否参入延签   0 不参入   1参入  兼容老的 此处使用较多
     */
    public function buildRequestParam($data_arr, $is_over = 0)
    {
        $sign = '';
        $paramArr = array(
            "mchId" => self::$merchantID,
            "nonceStr" => time(),
        );
        $data_arr = array_merge($paramArr, $data_arr);
        ksort($data_arr);

        foreach ($data_arr as $k => $v) {
            if (!empty($is_over)) {
                if (((string)$v != null) && ($k != 'sign')) {
                    $sign .= $k . '=' . $v . '&';
                }
            } else {
                if (((string)$v != null) && ($k != 'sign')) {
                    $sign .= $k . '=' . $v . '&';
                }

            }
        }
        $sign .= "key=" . Self::$merchantPW;
        $data_arr['sign'] = $this->signParam($sign);

        $requestStr = '';
        foreach ($data_arr as $k => $v) {
            $requestStr .= $k . "=" . @urlencode($v) . '&';
        }
        return substr($requestStr, 0, -1);
    }

    /**
     * 处理字符串
     *
     * @param String $str
     * @return String
     */
    private function signParam($str)
    {
        return strtoupper(hash("sha256", $str));
    }

    /**
     * 发送请求
     *
     * @param String $method
     * @param Array $postData
     * @return Array
     */
    public function newPostRequest($method, $postData)
    {
        $message = '';
        try {
            $curl = curl_init();
            $header[] = "Content-type: application/x-www-form-urlencoded";
            $header[] = "Accept: application/json";
            $header[] = "Accept-Language: " . static::$language;
            curl_setopt($curl, CURLOPT_URL, self::$apiPath . $method);
            curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false); // SSL certificate
            curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($curl, CURLOPT_HEADER, 0);
            curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($curl, CURLOPT_POST, true); // post
            curl_setopt($curl, CURLOPT_POSTFIELDS, $postData); // post data
            curl_setopt($curl, CURLOPT_TIMEOUT, 10);
            $responseText = curl_exec($curl);
            if (curl_errno($curl)) {
                echo 'Errno' . curl_error($curl);
            }
            curl_close($curl);
            $data = json_decode($responseText, true);
            if ($data['code'] != ErrCode::$SUCCESS) {
                throw new ValidationException($data['msg'], $data['code']);
            }
            $message = $data['message'] ?? '';
            $data = $data['data'];
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
            $data = [];
        } catch (\Exception $e) {
            $code = $e->getCode();
            $message = $e->getMessage();

        }

        if ($message) {
            $this->getDI()->get('logger')->warning('flashfulfillment-newpostRequest-failed:' . $message);
        }

        return [
            'code' => $code ?? ErrCode::$SUCCESS,
            'message' => $message ?? '',
            'data' => $data,
        ];
    }

}
