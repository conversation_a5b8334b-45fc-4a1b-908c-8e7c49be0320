<?php

namespace App\Modules\Purchase\Services;

use App\Library\Enums;
use App\Library\Enums\MaterialClassifyEnums;
use App\Library\Enums\PurchaseEnums;

use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\OssHelper;
use App\Library\Validation\ValidationException;
use App\Modules\Budget\Services\BudgetService;
use App\Modules\Common\Services\WaterMarkerService;
use App\Modules\Organization\Models\SysDepartmentModel;
use App\Modules\Purchase\Models\PurchaseAcceptanceModel;
use App\Modules\Purchase\Models\PurchaseAcceptanceProductModel;
use App\Modules\Purchase\Models\PurchaseApply;
use App\Modules\Purchase\Models\PurchaseOrder;
use App\Modules\Purchase\Services\BaseService as PurchaseService;
use App\Modules\Setting\Services\CommonDataPermissionService;
use App\Modules\User\Models\AttachModel;
use App\Modules\User\Services\StaffService;
use App\Modules\Workflow\Services\FYRService;
use App\Modules\Workflow\Services\WorkflowServiceV2;
use App\Modules\Material\Models\MaterialFinanceCategoryModel;
use App\Util\RedisKey;
use Mpdf\Mpdf;

/**
 * Created by PhpStorm.
 * Date: 2022/2/28
 * Time: 11:08
 */
class AcceptanceService extends BaseService
{
    //add参数校验
    public static $validate_add_param = [
        'no'    => 'Required|StrLenGeLe:10,20',
        'po'    => 'Required|StrLenGeLe:10,20',
        'po_id' => 'Required|StrLenGeLe:1,11',

        'vendor'     => 'Required|StrLenGeLe:0,200',
        'remark'     => 'StrLenGeLe:0,5000',
        'attachment' => 'Arr|ArrLenGeLe:0,10',
        'acceptance_type'=>'Required|IntIn:1,2',
        'acceptance_cate'=>'Required|Int',
        'acceptance_officer' => 'IfIntEq:acceptance_cate,9|Required|StrLenGeLe:1,200',


        'product'                        => 'Required|Arr|ArrLenGe:1',
        'product[*].product_option_code' => 'Required|StrLenGeLe:0,64',
        'product[*].product_name'        => 'StrLenGeLe:0,200',
        'product[*].budget_id'           => 'Required|StrLenGeLe:0,64',
        'product[*].pc_code'             => 'Required|StrLenGeLe:0,64',
        'product[*].wrs_code'            => 'Required|StrLenGeLe:0,64',
        'product[*].product_desc'        => 'StrLenGeLe:0,500',
        'product[*].order_num'           => 'Required|Int',
        'product[*].check_num'           => 'Required|IntGe:1',

        'product[*].unit'             => 'Required|StrLenGeLe:1,30',
        'product[*].check_date'       => 'Required|Date',
        'product[*].check_store_id'   => 'Required|Str',
        'product[*].check_store_name' => 'Required|Str',
        'product[*].product_option'   => 'StrLenGeLe:0,255',//规格型号
        'product[*].metere_unit'      => 'StrLenGeLe:0,20',//计量单位
        'product[*].check_result'     => 'Required|IntIn:1,2',//通过
        'product[*].check_desc'     => 'IfIntEq:check_result,2|Required|StrLenGeLe:1,100',
        'product[*].cost_store_id'   => 'Required|Str',//费用所属网点ID
        'product[*].cost_store_name' => 'Required|Str',//费用所属网点名称
    ];

    public static $validate_update_param = [
        'id'            => 'Required|Int',
        'product'       => 'Required|Arr|ArrLenGe:1',
        'acceptance_cate' => 'Required|Int',
        'acceptance_officer' => 'IfIntEq:acceptance_cate,9|Required|StrLenGeLe:1,200',
        'product[*]'    => 'Required|Obj',
        'product[*].id' => 'Required|Int',
        'product[*].check_date' => 'Required|Date',
        'product[*].check_num' => 'Required|IntGe:1',
        'product[*].check_store_id' => 'Required|Str',
        'product[*].check_store_name' => 'Required|Str',
        'product[*].check_result' => 'Required|IntGe:1',
        'product[*].check_desc' => 'IfIntEq:check_result,2|Required|StrLenGeLe:1,100'
    ];



    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return AcceptanceService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 新增默认
     * */
    public function defaultData($user)
    {
        $code         = ErrCode::$SUCCESS;
        $message      = 'success';
        $real_message = '';

        $data = [];
        try {
            if (empty($user)) {
                throw new ValidationException("The employee information is null [{$user['id']}]", ErrCode::$VALIDATE_ERROR);
            }

            $arr = $this->getUserMetaFromBi($user['id']);
            if (empty($arr)) {
                throw new ValidationException(static::$t->_("re_staff_info_id_not_exist"), ErrCode::$VALIDATE_ERROR);
            }

            $data['no']         = 'YSD' . static::getNo(date("Ymd"));
            $data['apply_date'] = date("Y-m-d");
            $data['create_id']  = $user['id'];

            $data['created_name'] = $this->getNameAndNickName($user['name'] ?? '', $user['nick_name'] ?? '');

        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();

        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $data         = [];
        }

        if (!empty($real_message)) {
            $this->getDI()->get('logger')->warning('acceptance-get-default-data-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data
        ];

    }

    public function createOrder($user, $data)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $db      = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $exist_purchase_acceptance = PurchaseAcceptanceModel::findFirst([
                'conditions' => 'no = :no:',
                'bind'       => ['no' => $data['no']]
            ]);

            if (!empty($exist_purchase_acceptance)) {
                throw new ValidationException(static::$t->_('purchase_acceptance_no_exist', ['pa_no' => $data['no']]), ErrCode::$VALIDATE_ERROR);
            }

            if ($data['acceptance_type'] == PurchaseEnums::ACCEPTANCE_TYPE_PO) {
                //采购订单行数据验收数量校验
                $apply = PurchaseOrder::findFirst([
                    'conditions' => 'id = :id:',
                    'bind'       => ['id' => $data['po_id']]
                ]);
            } else {
                //采购订单行数据验收数量校验
                $apply = PurchaseApply::findFirst([
                    'conditions' => 'id = :id:',
                    'bind'       => ['id' => $data['po_id']]
                ]);
            }

            if (empty($apply)) {
                throw new BusinessException('po或者pu单不存在 ' . $data['po_id'], ErrCode::$BUSINESS_ERROR);
            }

            $purchase_o_products = $apply->getProducts()->toArray();//采购明细

            // v15423 去掉该校验
            // $check_arr = array_column($purchase_o_products, 'is_check', 'id');
            $check_num = array_column($purchase_o_products, 'total', 'id');
            $products       = $this->acceptanceProductCount($data['po']);
            $old_check_num = array_column($products, 'check_total_num', 'order_product_id');

            foreach ($data['product'] as $k => $v) {
                /* v15423 去掉该校验
                if (isset($check_arr[$v['order_product_id']]) && 0 == $check_arr[$v['order_product_id']] || !isset($check_arr[$v['order_product_id']])) {
                    throw new ValidationException($v['product_option_code'] . static::$t->_('acceptance_product_not'), ErrCode::$VALIDATE_ERROR);
                }*/

                if (isset($check_num[$v['order_product_id']]) && bcsub($check_num[$v['order_product_id']] ?? 0, $v['check_num']) < ($old_check_num[$v['order_product_id']] ?? 0)) {
                    throw new ValidationException($v['product_option_code'] . static::$t->_('acceptance_product_over_num'), ErrCode::$VALIDATE_ERROR);
                }
            }
            //财务分类是否启用验收,20240308邮件需求暂时去掉
            /*$finance_code = array_column($data['product'], 'wrs_code');
            $finance_list = (new MaterialFinanceCategoryModel())->getList(['code' => $finance_code]);
            $finance_list = array_column($finance_list, 'update_to_acceptance', 'code');
            foreach ($finance_list as $code_k => $code_v) {
                if ($code_v == MaterialClassifyEnums::MATERIAL_CATEGORY_OFF) {
                    throw new ValidationException($code_k . static::$t->_('fiance_code_not_enabled'), ErrCode::$VALIDATE_ERROR);
                }
            }*/


            $user_info = $this->getUserMetaFromBi($data['create_id']);

            $acceptance_data = [
                'no'                   => $data['no'],
                'po'                   => $data['po'],
                'po_id'                => $data['po_id'],
                'status'               => 1,
                'cost_company_name'    => $data['cost_company_name'] ?? '',
                'cost_company_id'      => $data['cost_company_id'] ?? '',
                'cost_department_name' => $data['cost_department_name'] ?? '',
                'cost_department_id'   => $data['cost_department_id'] ?? '',
                'vendor'               => $data['vendor']??'',
                'cno'                  => $data['cno'] ?? '',
                'create_id'            => $user['id'],
                'created_name'         => $data['created_name'],
                'sap_supplier_no'      => $data['sap_supplier_no'] ?? '',
                'vendor_id'            => $data['vendor_id'] ?? '',
                'acceptance_type'      => $data['acceptance_type'],
                'acceptance_cate'      => $data['acceptance_cate'],
                'remark'               => $data['remark'] ?? '',
                'acceptance_officer'   => $data['acceptance_officer'] ?? '',
                'node_department_id'   => $user_info['node_department_id'],
                'created_at'           => date('Y-m-d H:i:s'),
                'updated_at'           => date('Y-m-d H:i:s'),
            ];

            $model = new PurchaseAcceptanceModel();
            $bool  = $model->i_create($acceptance_data);
            if ($bool === false) {
                throw new \Exception('验收单主表创建失败, 原因可能是: ' . get_data_object_error_msg($model) . '; 数据: ' . json_encode($acceptance_data, JSON_UNESCAPED_UNICODE), ErrCode::$SYSTEM_ERROR);
            }

            //行数据写入
            foreach ($data['product'] as $key => $acceptance_product) {
                $acceptance_product_bool  = false;
                $acceptance_product_model = new PurchaseAcceptanceProductModel();
                $acceptance_product_bool  = $acceptance_product_model->i_create([
                    'pa_id'     => $model->id,
                    'budget_id' => $acceptance_product['budget_id'],
                    'product_name'        => $acceptance_product['product_name'] ?? '',
                    'product_option_code' => $acceptance_product['product_option_code'],
                    'pc_code'             => $acceptance_product['pc_code'],
                    'wrs_code'            => $acceptance_product['wrs_code'],
                    'product_desc'        => $acceptance_product['product_desc'] ?? '',
                    'order_num'           => $acceptance_product['order_num'],
                    'check_num'           => $acceptance_product['check_num'],
                    'unit'                => $acceptance_product['unit'],
                    'check_date'          => $acceptance_product['check_date'],
                    'create_at'           => date('Y-m-d H:i:s'),
                    'update_at'           => date('Y-m-d H:i:s'),
                    'check_store_id'      => $acceptance_product['check_store_id'] ?? '',
                    'check_store_name'    => $acceptance_product['check_store_name'],
                    'check_pc_code'       => $acceptance_product['check_pc_code'] ?? '',
                    'check_desc'          => $acceptance_product['check_desc'] ?? '',
                    'order_product_id'    => $acceptance_product['order_product_id'],
                    'metere_unit'         => $acceptance_product['metere_unit'],//计量单位
                    'product_option'      => $acceptance_product['product_option'],//规格型号
                    'check_result'        => $acceptance_product['check_result'],
                    'cost_store_id'       => $acceptance_product['cost_store_id'] ?? '',//费用所属网点ID
                    'cost_store_name'     => $acceptance_product['cost_store_name'] ?? '',//费用所属网点名称
                    'created_at'          => date('Y-m-d H:i:s'),
                    'updated_at'          => date('Y-m-d H:i:s'),

                ]);

                if ($acceptance_product_bool === false) {
                    throw new \Exception('采购验收单-产品创建失败, 原因可能是: ' . get_data_object_error_msg($acceptance_product_model), ErrCode::$SYSTEM_ERROR);
                }
            }

            if (!empty($data['attachment'])) {
                $attachments = [];
                foreach ($data['attachment'] as $attachment) {
                    $attachments[] = [
                        'file_name'       => $attachment['file_name'] ?? '',
                        'bucket_name'     => $attachment['bucket_name'] ?? '',
                        'object_key'      => $attachment['object_key'] ?? '',
                        'oss_bucket_key'  => $model->id,
                        'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_PURCHASE_ACCEPTANCE
                    ];
                }
                $t_model     = new AttachModel();
                $attach_bool = $t_model->batch_insert($attachments);
                if ($attach_bool === false) {
                    throw new BusinessException('采购验收单-附件创建失败, 原因可能是: ' . get_data_object_error_msg($t_model) . '; 数据: ' . json_encode($attachments, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                }
            }

            $flow_bool = (new PayFlowService(Enums::WF_PURCHASE_ACCEPTANCE))->createRequest($model->id, $user);
            if ($flow_bool === false) {
                throw new BusinessException(static::$t->_('acceptance_create_work_flow_failed'), ErrCode::$LOAN_CREATE_WORK_FLOW_ERROR);
            }

            $db->commit();
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            //业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->warning('purchaseAcceptance-create-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message
        ];
    }

    /**
     * 关联po下 产品数量
     * */
    public function acceptanceProductCount($po)
    {
        $builder    = $this->modelsManager->createBuilder();
        $column_str = 'sum(p.check_num) as check_total_num,p.order_product_id,p.product_option_code';
        $builder->from(['o' => PurchaseAcceptanceModel::class]);
        $builder->leftjoin(PurchaseAcceptanceProductModel::class, 'o.id=p.pa_id', 'p');
        $builder->columns($column_str);
        $builder->andWhere('o.po = :po: and o.status in (1,3)', ['po' => $po]);
        $builder->groupBy('p.order_product_id');
        return $builder->getQuery()->execute()->toArray();
    }


    public function getList($condition, $user = [], $type = 0, $export = false)
    {
        if (!empty($user['id'])) {
            $condition['uid'] = $user['id'];
        }

        $page_size = empty($condition['pageSize']) ? 20 : $condition['pageSize'];
        $page_num = empty($condition['pageNum']) ? 1 : $condition['pageNum'];
        $offset = $page_size * ($page_num - 1);

        $code    = ErrCode::$SUCCESS;
        $message = '';
        $data    = [];
        $items   = [];

        try {
            // v14162需求: 财务分类参数有值时, 需要取其及所有子类
            if (!empty($condition['wrs_code'])) {
                $finance_son_category = (new MaterialFinanceCategoryModel())->getAllSonListByCode($condition['wrs_code']);
                $condition['wrs_code'] = array_column($finance_son_category, 'code');
            }

            $column_str = 'o.id,o.no,o.po,o.po_id,o.status,o.cost_company_name,o.cost_company_id,o.cost_department_name,o.cost_department_id,o.vendor,o.cno,o.approve_at,o.created_name,o.create_id,o.sap_supplier_no,o.vendor_id,o.remark,o.created_at';

            $builder = $this->modelsManager->createBuilder();
            $builder->from(['o' => PurchaseAcceptanceModel::class]);

            if ($export) {
                $column_str = 'o.id,o.no,o.po,o.cost_company_name,o.cost_department_name,o.vendor,o.created_at,o.created_name,o.cno,o.create_id,o.sap_supplier_no,o.vendor_id,o.status,p.budget_id,p.product_name,p.pc_code,p.product_option_code,p.wrs_code,p.product_desc,p.order_num,p.check_num,p.unit,p.check_date,p.check_store_name,p.check_pc_code,p.check_desc,p.product_option';
                $builder->leftjoin(PurchaseAcceptanceProductModel::class, 'o.id=p.pa_id', 'p');
            }

            $builder = $this->getCondition($builder, $condition, $type, $user);
            $builder->andWhere('o.is_del = :is_del:', ['is_del' => 0]);

            $count = (int) $builder->columns('COUNT(DISTINCT o.id) AS total')->getQuery()->getSingleResult()->total;

            if ($count) {
                // 审核模块的已处理列表, 展示处理时间
                if ($type == self::LIST_TYPE_AUDIT && isset($condition['flag']) && $condition['flag'] == GlobalEnums::AUDIT_TAB_PROCESSED) {
                    $column_str .= ',log.audit_at';
                }

                $builder->columns($column_str);
                if (!$export) {
                    $builder->groupBy('o.id');
                    $builder->limit($page_size, $offset);
                }

                $items = $builder->getQuery()->execute()->toArray();

                if ($export) {
                    $items = $this->exportHandleList($items);
                } else {
                    $this->handleListItems($items);
                }
            }

            $data = [
                'items'      => $items,
                'pagination' => [
                    'current_page' => $page_num,
                    'per_page'     => $page_size,
                    'total_count'  => (int)$count,
                ]
            ];

        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $this->logger->error('purchase_acceptance-list-failed:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data
        ];
    }

    public function getCondition($builder, $condition, $type = 0, $user = [])
    {
        $create_id = $condition['create_id'] ?? '';

        // 征询回复进度
        $is_reply = $condition['is_reply'] ?? GlobalEnums::CONSULTED_REPLY_STATE_PENDING;
        $is_reply = in_array($is_reply, GlobalEnums::CONSULTED_REPLY_STATE_ITEM) ? $is_reply : GlobalEnums::CONSULTED_REPLY_STATE_PENDING;

        // 审批处理进度
        $flag = $condition['flag'] ?? GlobalEnums::AUDIT_TAB_PENDING;
        $flag = in_array($flag, GlobalEnums::AUDIT_TAB_STATE_ITEM) ? $flag : GlobalEnums::AUDIT_TAB_PENDING;

        // 审核列表
        if ($type == self::LIST_TYPE_AUDIT) {
            $builder = (new WorkflowServiceV2())->getBizWorkflowOrderListByProcessState($builder, $flag, [Enums::WF_PURCHASE_ACCEPTANCE], $condition['uid'], 'o');

        } else if ($type == self::LIST_TYPE_APPLY) {
            if (isset($condition['uid']) && !empty($condition['uid'])) {
                $builder->andWhere('o.create_id = :uid:', ['uid' => $condition['uid']]);
            }
            $builder->orderBy('o.created_at desc');

        } else if ($type == self::LIST_TYPE_FYR) {
            // 意见征询回复列表
            $biz_table_info = ['table_alias' => 'o'];
            $builder = (new FYRService())->getBizOrderConsultationReplyListByProcessState($builder, $is_reply, [Enums::WF_PURCHASE_ACCEPTANCE], $condition['uid'], $biz_table_info);
        } else if ($type == self::LIST_TYPE_DATA) {
            // 对接通用数据权限
            // 业务表参数
            $table_params = [
                'table_alias_name' => 'o',
                'create_id_field' => 'create_id',
                'create_node_department_id_filed' => 'cost_department_id',
            ];
            $builder = CommonDataPermissionService::getInstance()->getCommonDataPermission($user, $builder, Enums\SysConfigEnums::SYS_MODULE_PURCHASE_ACCEPTANCE, $table_params);
            $builder->orderBy('o.created_at desc');

        } else {
            $builder->orderBy('o.created_at desc');
        }

        //工号或者姓名
        if (!empty($create_id)) {
            $builder->andWhere('o.create_id = :create_id: or o.created_name=:create_id:', ['create_id' => $create_id]);
        }

        if (!empty($condition['po'])) {
            $builder->andWhere('o.po = :po:', ['po' => $condition['po']]);
        }
        if (!empty($condition['status'])) {
            $builder->andWhere('o.status = :status:', ['status' => $condition['status']]);
        }

        if (!empty($condition['cost_company_id'])) {
            if (is_array($condition['cost_company_id'])) {
                $builder->andWhere('o.cost_company_id IN ({cost_company_id:array})', ['cost_company_id' => array_values($condition['cost_company_id'])]);
            } else {
                $builder->andWhere('o.cost_company_id = :cost_company_id:', ['cost_company_id' => $condition['cost_company_id']]);
            }
        }

        if (!empty($condition['cost_department_id'])) {
            $builder->andWhere('o.cost_department_id = :cost_department_id:', ['cost_department_id' => $condition['cost_department_id']]);
        }

        if (!empty($condition['no'])) {
            $builder->andWhere('o.no = :no:', ['no' => $condition['no']]);
        }

        /**
         * 12614【ALL-OA】资产标准型号&采购入库&资产台账-P1需求
         * 发现历史bug，验收单，按照供应商名称搜索报系统错误问题修复
         */
        if (isset($condition['vendor_sap_no']) && !empty($condition['vendor_sap_no'])) {
            $builder->andWhere('(o.vendor LIKE :vendor: OR o.sap_supplier_no LIKE :sap_supplier_no:)', ['vendor' => "{$condition['vendor_sap_no']}", 'sap_supplier_no' => "{$condition['vendor_sap_no']}%"]);
        }

        if (!empty($condition['wrs_code']) || !empty($condition['product_option_code']) || !empty($condition['remark'])) {
            $builder->leftjoin(PurchaseAcceptanceProductModel::class, 'o.id=p.pa_id', 'p');

        }
        if (!empty($condition['wrs_code'])) {
            $builder->inWhere('p.wrs_code', $condition['wrs_code']);
        }

        if (!empty($condition['product_option_code'])) {
            $builder->andWhere('p.product_option_code = :product_option_code:', ['product_option_code' => $condition['product_option_code']]);
        }

        if (!empty($condition['remark'])) {
            $builder->andWhere('p.product_desc LIKE :remark:', ['remark' => "%{$condition['remark']}%"]);
        }
        //申请时间-起始日期
        if (isset($condition['apply_start_date']) && !empty($condition['apply_start_date'])) {
            $condition['apply_start_date'] .= ' 00:00:00';

            $builder->andWhere('o.created_at >= :apply_start_date:', ['apply_start_date' => $condition['apply_start_date']]);
        }

        //申请时间-截止日期
        if (isset($condition['apply_end_date']) && !empty($condition['apply_end_date'])) {
            $condition['apply_end_date'] .= ' 23:59:59';

            $builder->andWhere('o.created_at <= :apply_end_date:', ['apply_end_date' => $condition['apply_end_date']]);
        }


        return $builder;
    }

    private function handleListItems(&$items)
    {

        if (empty($items) || !is_array($items)) {
            return [];
        }
        $status = Enums::$acceptance_apply_status;

        foreach ($items as &$item) {

            $item['status_txt'] = static::$t->_($status[$item['status']]);
            $item['created_at'] = date('Y-m-d', strtotime($item['created_at']));
        }


        return $items;

    }

    public function getEnumsParams()
    {
        $code                    = ErrCode::$SUCCESS;
        $message                 = $real_message = '';
        $acceptance_status       = [];
        $acceptance_apply_status = Enums::$acceptance_apply_status;
        foreach ($acceptance_apply_status as $key => $value) {
            $row['id']           = (string)$key;
            $row['name_key']     = static::$t->_($value);
            $acceptance_status[] = $row;
        }
        $data = [
            'cost_company'    => (new PurchaseService())->getCooCostCompany(),
            'cost_department' => StaffService::getInstance()->departmentList(),
            'status'              => $acceptance_status,
            'acceptance_category' => $this->acceptanceCategory(),
            'check_result'        => $this->acceptanceResult(),
            'acceptance_type'     => $this->acceptanceType(),
        ];

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data
        ];
    }


    /**
     * po单入库详情
     * */
    public function purchaseOrderDetail($id)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            $apply = PurchaseOrder::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $id]
            ]);

            if (empty($apply)) {
                return [
                    'code'    => ErrCode::$SUCCESS,
                    'message' => 'No Data',
                    'data'    => []
                ];
            }
            $purchase_data = $apply->toArray();


            $data = [
                'cno'       => $purchase_data['cno'],
                'vendor'    => $purchase_data['vendor'],
                'vendor_id' => $purchase_data['vendor_id'],
                'po'        => $purchase_data['pono'],
                'po_id'     => $purchase_data['id'],

                'sap_supplier_no'      => $purchase_data['sap_supplier_no'],
                'cost_company_name'    => $purchase_data['cost_company_name'],
                'cost_company_id'      => $purchase_data['cost_company'],
                'cost_department_name' => $purchase_data['cost_department_name'],
                'cost_department_id'   => $purchase_data['cost_department'],
                'remark'               => empty($purchase_data['remark']) ? '' : $purchase_data['remark'],
            ];
            unset($purchase_data);
            $purchase_products = $apply->getProducts()->toArray();//采购明细
            $products          = $this->acceptanceProductCount($data['po']);
            $products          = array_column($products, 'check_total_num', 'order_product_id');
            $budgetIds = array_values(array_filter(array_column($purchase_products, 'budget_id')));
            if ($budgetIds) {
                $budgetService = new BudgetService();
                $budgets       = $budgetService->budgetObjectList($budgetIds);
            }

            $language = static::$language;
            
            foreach ($purchase_products as $key => $value) {
                $check_num = bcsub($value['total'], $products[$value['id']] ?? 0);
                $budget_text = '';
                if (isset($budgets) && isset($budgets[$value['budget_id']])) {
                    $budget_text = $budgets[$value['budget_id']]['name_' . strtolower(substr($language, -2))];
                }
                $data['product'][] = [
                    'budget_id'           => $value['budget_id'],
                    'budget_text'         => $budget_text,
                    'product_name'        => $value['product_name'],
                    'pc_code'             => $value['cost_center_name'],
                    'product_option_code' => $value['product_option_code'],
                    'wrs_code'            => $value['wrs_code'],
                    'unit'                => $value['unit'],
                    'product_desc'        => empty($value['desc']) ? '' : $value['desc'],//产品描述
                    'check_num'           => $check_num < 0 ? 0 : (int)$check_num,
                    'order_product_id'    => $value['id'],
                    'order_num'           => $value['total'],
                    'metere_unit'         => $value['metere_unit'],//计量单位
                    'product_option'      => $value['product_option'],//规格型号
                    'cost_store_id'       => $value['cost_store_id'],//费用所属网点ID
                    'cost_store_name'     => $value['cost_store_name']//费用所属网点名称
                ];
            }


        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $data         = [];
        }

        if (!empty($real_message)) {
            $this->getDI()->get('logger')->warning('storage-po-detail-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data
        ];

    }

    /**
     * 入库单详情
     * */
    public function getDetail($id, $uid, $type = '')
    {
        $code             = ErrCode::$SUCCESS;
        $message          = $real_message = '';
        $acceptance_order = [];
        try {
            $acceptance_order = PurchaseAcceptanceModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $id]
            ]);
            if (empty($acceptance_order)) {
                return [
                    'code'    => ErrCode::$SUCCESS,
                    'message' => 'No Data',
                    'data'    => []
                ];
            }

            $req = (new PayFlowService(Enums::WF_PURCHASE_ACCEPTANCE))->getRequest($id);

            if (empty($req->id)) {
                throw new BusinessException('获取工作流批次失败');
            }


            // 获取附件信息
            $file             = AttachModel::find([
                'conditions' => 'oss_bucket_key = :oss_bucket_key: AND oss_bucket_type = :oss_bucket_type: AND deleted =0',
                'bind'       => ['oss_bucket_key' => $id, 'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_PURCHASE_ACCEPTANCE],
                'columns'    => ['bucket_name', 'object_key', 'file_name']
            ]);

            $acceptance_order = $acceptance_order->toArray();

            $acceptance_order_products = PurchaseAcceptanceProductModel::find([
                'conditions' => 'pa_id =:id:',
                'bind'       => ['id' => $id]
            ])->toArray();


            $budgetIds = array_values(array_filter(array_column($acceptance_order_products, 'budget_id')));
            if ($budgetIds) {
                $budgetService = new BudgetService();
                $budgets       = $budgetService->budgetObjectList($budgetIds);
            }


            $it_attach_file = AttachModel::find([
                'conditions' => 'oss_bucket_key = :oss_bucket_key: AND oss_bucket_type = :oss_bucket_type: AND deleted = :deleted:',
                'bind'       => ['oss_bucket_key' => $id, 'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_PURCHASE_ACCEPTANCE_SU, 'deleted' => GlobalEnums::IS_NO_DELETED],
                'columns'    => ['bucket_name', 'object_key', 'file_name']
            ]);

            $language = static::$language;

            foreach ($acceptance_order_products as $k => $v) {
                $acceptance_order_products[$k]['budget_text'] = '';
                if (isset($budgets) && isset($budgets[$v['budget_id']])) {
                    $acceptance_order_products[$k]['budget_text'] = $budgets[$v['budget_id']]['name_' . strtolower(substr($language, -2))];
                }
                $acceptance_order_products[$k]['check_date'] = date('Y-m-d', strtotime(date($v['check_date'])));
                $acceptance_order_products[$k]['check_result_text'] =static::$t->_((PurchaseEnums::$check_result_type)[$v['check_result']]);


            }
            $acceptance_order['acceptance_cate_text'] = static::$t->_((PurchaseEnums::$acceptance_order_type)[$acceptance_order['acceptance_cate']]??'');
            $acceptance_order['acceptance_type_text'] = static::$t->_((PurchaseEnums::$acceptance_type)[$acceptance_order['acceptance_type']]??'');

            $acceptance_order['attachment_files'] = $file ? $file->toArray() : [];
            $acceptance_order['it_attach_files'] = $it_attach_file ? $it_attach_file->toArray() : [];

            $ask                          = (new FYRService())->getRequestToByReplyAsk($req, $uid);
            $acceptance_order['ask_id']   = $ask ? $ask->id : '';
            $acceptance_order['product']  = $acceptance_order_products;

            $acceptance_order['auth_log'] = (new WorkflowServiceV2())->getAuditLogs($req);

            //判断是否能更改
            $acceptance_order['can_edit']        = false;
            $acceptance_order['can_edit_fields'] = (object)[];
            if ($type == self::LIST_TYPE_AUDIT) {
                $can_edit_fields = (new PayFlowService(Enums::WF_PURCHASE_ACCEPTANCE))->getCanEditFieldByReq($req, $uid);
                if ($can_edit_fields !== false) {
                    $acceptance_order['can_edit_fields'] = $can_edit_fields;
                    $acceptance_order['can_edit']        = true;
                }
            }

        } catch (\Exception $e) {
            $code             = ErrCode::$SYSTEM_ERROR;
            $message          = static::$t->_('retry_later');
            $real_message     = $e->getMessage();
            $acceptance_order = [];

        }
        if (!empty($real_message)) {
            $this->getDI()->get('logger')->warning('storage-psno-detail-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $acceptance_order
        ];
    }

    /**
     * 获取启用中的标准型号barcode列表
     * @param string $name_key barcode
     * @return array
     */
    public function purchaseProductList($name_key)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => parent::purchaseProductList($name_key)
        ];
    }

    /**
     * wrs_code 搜索
     * */
    public function wrsCodeList($name_key)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        return [
            'code'    => $code,
            'message' => $message,
            'data'    => parent::wrsCodeList($name_key)
        ];
    }

    /**
     * 产品描述 搜索
     * */
    public function productDesc($name_key)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $code_list = PurchaseAcceptanceProductModel::find(
            [
                'columns'    => 'id,product_desc',
                'conditions' => 'product_desc LIKE :product_desc:',
                'bind'       => [
                    'product_desc' => '%' . $name_key . '%',
                ],
                'limit'      => 50,
            ]
        )->toArray();
        $code_data = [];
        if (!empty($code_list)) {
            foreach ($code_list as $c) {
                $row['id']       = $c['id'];
                $row['name_key'] = $c['product_desc'];
                $code_data[]     = $row;
            }
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $code_data
        ];
    }

    /**
     * 更新
     *
     * @param $user
     * @param $data
     * @return array
     */
    public function updateAcceptance($user, $data)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $db      = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            if (!isset($data['no']) || empty($data['no'])) {
                throw new BusinessException('验收单不存在', ErrCode::$BUSINESS_ERROR);
            }

            //必须是提交人，和已拒绝，加锁，防止重复点击
            $model = PurchaseAcceptanceModel::findFirst([
                'conditions' => 'id = :id: and create_id = :user_id: and status in ({status:array})',
                'bind'       => ['id' => $data['id'], 'user_id' => $user['id'], 'status' => [Enums::WF_STATE_REJECTED, Enums::WF_STATE_CANCEL]],
                'for_update' => true
            ]);

            if (empty($model)) {
                throw new ValidationException(static::$t->_("request_has_submit"), ErrCode::$CONTRACT_GET_INFO_ERROR);
            }


            if ($data['acceptance_type'] == PurchaseEnums::ACCEPTANCE_TYPE_PO) {
                //采购订单行数据验收数量校验
                $apply = PurchaseOrder::findFirst([
                    'conditions' => 'id = :id:',
                    'bind'       => ['id' => $data['po_id']]
                ]);
            } else {
                //采购订单行数据验收数量校验
                $apply = PurchaseApply::findFirst([
                    'conditions' => 'id = :id:',
                    'bind'       => ['id' => $data['po_id']]
                ]);
            }

            if (empty($apply)) {
                throw new BusinessException('po或者pu单不存在, id = ' . $data['po_id'], ErrCode::$BUSINESS_ERROR);
            }

            $purchase_o_products = $apply->getProducts()->toArray();//采购明细

            // v15423 去掉该校验
            // $check_arr = array_column($purchase_o_products, 'is_check', 'id');
            $check_num = array_column($purchase_o_products, 'total', 'id');

            $products       = $this->acceptanceProductCount($data['po']);
            $old_check_num = array_column($products, 'check_total_num', 'order_product_id');

            foreach ($data['product'] as $k => $v) {
                /* v15423 去掉该校验
                if (isset($check_arr[$v['order_product_id']]) && 0 == $check_arr[$v['order_product_id']] || !isset($check_arr[$v['order_product_id']])) {
                    throw new ValidationException($v['product_option_code'] . static::$t->_('acceptance_product_not'), ErrCode::$VALIDATE_ERROR);
                }*/

                if (isset($check_num[$v['order_product_id']]) && bcsub($check_num[$v['order_product_id']] ?? 0, $v['check_num']) < ($old_check_num[$v['order_product_id']] ?? 0)) {
                    throw new ValidationException($v['product_option_code'] . static::$t->_('acceptance_product_over_num'), ErrCode::$VALIDATE_ERROR);
                }
            }


            //更新附件
            if (!empty($data['attachment'])) {
                $attachments = [];
                foreach ($data['attachment'] as $attachment) {
                    $attachments[] = [
                        'file_name'       => $attachment['file_name'] ?? '',
                        'bucket_name'     => $attachment['bucket_name'] ?? '',
                        'object_key'      => $attachment['object_key'] ?? '',
                        'oss_bucket_key'  => $data['id'],
                        'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_PURCHASE_ACCEPTANCE
                    ];
                }
                $db->updateAsDict(
                    (new AttachModel())->getSource(),
                    [
                        'deleted' => GlobalEnums::IS_DELETED
                    ],
                    [
                        'conditions' => "oss_bucket_key = {$data['id']}  AND oss_bucket_type = " . Enums::OSS_BUCKET_TYPE_PURCHASE_ACCEPTANCE,
                    ]

                );

                $t_model     = new AttachModel();
                $attach_bool = $t_model->batch_insert($attachments);
                if ($attach_bool === false) {
                    throw new BusinessException('采购验收单-附件创建失败, 原因可能是: ' . get_data_object_error_msg($t_model) . '; 数据 = ' . json_encode($attachments, JSON_UNESCAPED_UNICODE), ErrCode::$CONTRACT_CREATE_ERROR);
                }
            }

            //行数据更新
            $purchase_acceptance_product = $model->getProducts()->toArray();
            $purchase_acceptance_product_ids = array_column($purchase_acceptance_product, 'id');
            $params_purchase_acceptance_product_ids = array_column($data['product'], 'id');
            $diff_purchase_acceptance_product_ids = array_diff($purchase_acceptance_product_ids, $params_purchase_acceptance_product_ids);
            if (isset($data['product'])) {
                foreach ($data['product'] as $key => $value) {
                    $acceptance_model = PurchaseAcceptanceProductModel::findFirst([
                        'conditions' => 'id = :id:',
                        'bind'       => ['id' => $value['id']]
                    ]);

                    $acceptance_model->check_num  = $value['check_num'];
                    $acceptance_model->check_date = $value['check_date'];
                    $acceptance_model->check_store_name = $value['check_store_name'];
                    $acceptance_model->check_store_id   = $value['check_store_id'];
                    $acceptance_model->check_result   = $value['check_result'];
                    $acceptance_model->check_desc   = $value['check_desc'];
                    $acceptance_model->updated_at       = date('Y-m-d H:i:s');
                    if ($acceptance_model->save() === false) {
                        throw new BusinessException('采购验收单-行数据更新失败, 原因可能是: ' . get_data_object_error_msg($acceptance_model) . '; 数据 = ' . json_encode($acceptance_model->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                    }
                }
            }
            //若前端操作了删除明细行，则需要从库里删除
            if ($diff_purchase_acceptance_product_ids) {
                PurchaseAcceptanceProductModel::find(['conditions' => 'id in ({ids:array}) and pa_id = :pa_id:', 'bind' => ['ids' =>  array_values($diff_purchase_acceptance_product_ids), 'pa_id' => $model->id]])->delete();
            }

            // 主数据更新
            $update_data = [
                'acceptance_cate' => $data['acceptance_cate'],
                'status' => Enums::WF_STATE_PENDING,
                'updated_at' => date('Y-m-d H:i:s'),
                'acceptance_officer' => $data['acceptance_officer'] ?? '',
            ];

            $bool = $model->i_update($update_data);
            if ($bool === false) {
                throw new BusinessException('验收单重新提交失败, 原因可能是: ' . get_data_object_error_msg($model) . '; 待更新数据: ' . json_encode($update_data, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }

            $flow_bool = (new PayFlowService(Enums::WF_PURCHASE_ACCEPTANCE))->recommit($data['id'], $user);
            if ($flow_bool === false) {
                throw new BusinessException(static::$t->_('acceptance_create_work_flow_failed'), ErrCode::$LOAN_CREATE_WORK_FLOW_ERROR);
            }

            $db->commit();
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            //业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->warning('purchaseAcceptance-recommit-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message
        ];
    }

    public function exportHandleList($items)
    {
        $data   = [];
        $lang   = static::$t;
        $status = Enums::$acceptance_apply_status;

        $budgetIds = array_values(array_filter(array_column($items, 'budget_id')));
        if ($budgetIds) {
            $budgetService = new BudgetService();
            $budgets       = $budgetService->budgetObjectList($budgetIds);
        }

        $language = static::$language;
        foreach ($items as $item) {
            $data[] = [
                $item['no'],
                $item['po'],
                $item['cost_company_name'],
                $item['cost_department_name'],
                $item['created_at'],
                $item['create_id'],
                $item['created_name'],
                $lang->_($status[$item['status']]),
                $item['cno'],
                $item['vendor'],
                $item['sap_supplier_no'],
                $item['vendor_id'],
                $budgets[$item['budget_id']]['name_' . strtolower(substr($language, -2))] ?? '',
                $item['product_name'],
                $item['pc_code'],
                $item['product_option_code'],
                $item['wrs_code'],
                $item['product_desc'],
                $item['product_option'],//规格型号
                $item['order_num'],
                $item['unit'],
                $item['check_num'],
                $item['check_date'],
                $item['check_store_name'],
                $item['check_pc_code'],
                $item['check_desc'],


            ];

        }
        return $data;

    }

    public function exportList($params, $user = [], $type = 0)
    {

        $data = $this->getList($params, $user, $type, true);


        if ($data['code'] != ErrCode::$SUCCESS) {
            return $data;
        }
        $new_data  = $data['data']['items'];
        $file_name = "acceptance_" . date("YmdHis");
        $header    = [
            static::$t->_('acceptance_no'),//验收单号
            static::$t->_('acceptance_po'),   //oa采购单号
            static::$t->_('acceptance_cost_company'),//费用所属公司
            static::$t->_('acceptance_cost_department'),//费用所属部门
            static::$t->_('acceptance_apply_date'),  //申请日期
            static::$t->_('acceptance_apply_id'),  //申请人工号
            static::$t->_('acceptance_apply_name'),  //申请姓名
            static::$t->_('acceptance_status'), //申请状态
            static::$t->_('acceptance_cno'),       //合同编号
            static::$t->_('acceptance_vendor'),   //供应商名称
            static::$t->_('acceptance_sap_no'),   //供应商sap编号
            static::$t->_('acceptance_vendor_id'),   //oa供应商编号
            static::$t->_('acceptance_budget'),   //预算分类
            static::$t->_('acceptance_product_name'),   //产品名称
            static::$t->_('acceptance_pc_code'),   //费用所属中心
            static::$t->_('acceptance_product_code'),   //产品编号
            static::$t->_('acceptance_wrs_code'),   //物料编码
            static::$t->_('acceptance_product_desc'),   //产品描述
            static::$t->_('purchase_apply_field_product_option'), //规格型号
            static::$t->_('acceptance_order_num'),   //订单数量
            static::$t->_('acceptance_unit'),   //单位
            static::$t->_('acceptance_check_num'),   //验收数量
            static::$t->_('acceptance_check_date'),   //验收日期
            static::$t->_('acceptance_store_name'),   //验收验收网点
            static::$t->_('acceptance_check_pc'),   //验收网点成本中心
            static::$t->_('acceptance_desc'),   //验收说明

        ];
        return $this->exportExcel($header, $new_data, $file_name);


    }

    /**
     * 入库单详情
     * */
    public function getDetailByNo($no, $uid)
    {
        $code             = ErrCode::$SUCCESS;
        $message          = $real_message = '';
        $acceptance_order = [];
        try {
            $acceptance_order = PurchaseAcceptanceModel::findFirst([
                'conditions' => 'no = :no:',
                'bind'       => ['no' => $no]
            ]);
            if (empty($acceptance_order)) {
                return [
                    'code'    => ErrCode::$SUCCESS,
                    'message' => 'No Data',
                    'data'    => []
                ];
            }

            $id = $acceptance_order->id;
            $acceptance_order = PurchaseAcceptanceModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $id]
            ]);
            if (empty($acceptance_order)) {
                return [
                    'code'    => ErrCode::$SUCCESS,
                    'message' => 'No Data',
                    'data'    => []
                ];
            }

            $req = (new PayFlowService(Enums::WF_PURCHASE_ACCEPTANCE))->getRequest($id);

            if (empty($req->id)) {
                throw new BusinessException('获取工作流批次失败');
            }


            // 获取附件信息
            $file             = AttachModel::find([
                'conditions' => 'oss_bucket_key = :oss_bucket_key: AND oss_bucket_type = :oss_bucket_type: AND deleted =0',
                'bind'       => ['oss_bucket_key' => $id, 'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_PURCHASE_ACCEPTANCE],
                'columns'    => ['bucket_name', 'object_key', 'file_name']
            ]);
            $acceptance_order = $acceptance_order->toArray();

            $acceptance_order_products = PurchaseAcceptanceProductModel::find([
                'conditions' => 'pa_id =:id:',
                'bind'       => ['id' => $id]
            ])->toArray();


            $budgetIds = array_values(array_filter(array_column($acceptance_order_products, 'budget_id')));
            if ($budgetIds) {
                $budgetService = new BudgetService();
                $budgets       = $budgetService->budgetObjectList($budgetIds);
            }

            $language = static::$language;

            foreach ($acceptance_order_products as $k => $v) {
                $acceptance_order_products[$k]['budget_text'] = '';
                if (isset($budgets) && isset($budgets[$v['budget_id']])) {
                    $acceptance_order_products[$k]['budget_text'] = $budgets[$v['budget_id']]['name_' . strtolower(substr($language, -2))];
                }
                $acceptance_order_products[$k]['check_date'] = date('Y-m-d', strtotime(date($v['check_date'])));

            }

            $acceptance_order['attachment_files'] = $file ? $file->toArray() : [];

            $ask                          = (new FYRService())->getRequestToByReplyAsk($req, $uid);
            $acceptance_order['ask_id']   = $ask ? $ask->id : '';
            $acceptance_order['product']  = $acceptance_order_products;
            $acceptance_order['auth_log'] = (new WorkflowServiceV2())->getAuditLogs($req);

        } catch (\Exception $e) {
            $code             = ErrCode::$SYSTEM_ERROR;
            $message          = static::$t->_('retry_later');
            $real_message     = $e->getMessage();
            $acceptance_order = [];

        }

        if (!empty($real_message)) {
            $this->getDI()->get('logger')->warning('storage-psno-detail-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $acceptance_order
        ];
    }

    /**
     * pu单入库详情
     * */
    public function purchaseApplyDetail($id)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            $apply = PurchaseApply::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $id]
            ]);

            if (empty($apply)) {
                return [
                    'code'    => ErrCode::$SUCCESS,
                    'message' => 'No Data',
                    'data'    => []
                ];
            }
            $purchase_data = $apply->toArray();


            $data = [
                'cno'       => '',
                'vendor'    =>'',
                'vendor_id' => '',
                'po'        => $purchase_data['pano'],
                'po_id'     => $purchase_data['id'],
                'sap_supplier_no'      => '',
                'cost_company_name'    => $purchase_data['cost_company_name'],
                'cost_company_id'      => $purchase_data['cost_company_id'],
                'cost_department_name' => $purchase_data['cost_department_name'],
                'cost_department_id'   => $purchase_data['cost_department'],
                'remark'               => !empty($purchase_data['apply_reason']) ? $purchase_data['apply_reason'] : '',
            ];

            unset($purchase_data);
            $purchase_products = $apply->getProducts()->toArray();//采购明细
            $products          = $this->acceptanceProductCount($data['po']);
            $products          = array_column($products, 'check_total_num', 'order_product_id');
            $budgetIds = array_values(array_filter(array_column($purchase_products, 'budget_id')));
            if ($budgetIds) {
                $budgetService = new BudgetService();
                $budgets       = $budgetService->budgetObjectList($budgetIds);
            }

            $language = static::$language;

            foreach ($purchase_products as $key => $value) {
                $check_num = bcsub($value['total'], $products[$value['id']] ?? 0);
                $budget_text = '';
                if (isset($budgets) && isset($budgets[$value['budget_id']])) {
                    $budget_text = $budgets[$value['budget_id']]['name_' . strtolower(substr($language, -2))];
                }
                $data['product'][] = [
                    'budget_id'           => $value['budget_id'],
                    'budget_text'         => $budget_text,
                    'product_name'        => $value['product_name'],
                    'pc_code'             => $value['cost_center_name'],
                    'product_option_code' => $value['product_option_code'],
                    'wrs_code'            => $value['finance_code'],
                    'unit'                => $value['unit'],
                    'product_desc'        => empty($value['desc']) ? '' : $value['desc'],//产品描述
                    'check_num'           => $check_num < 0 ? 0 : (int)$check_num,
                    'order_product_id'    => $value['id'],
                    'order_num'           => $value['total'],
                    'metere_unit'         => $value['metere_unit'],//计量单位
                    'product_option'      => $value['product_option'],//规格型号
                    'cost_store_id'       => $value['cost_store_id'],//费用所属网点ID
                    'cost_store_name'     => $value['cost_store_name'],//费用所属网点名称
                ];
            }


        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $data         = [];
        }

        if (!empty($real_message)) {
            $this->getDI()->get('logger')->warning('storage-po-detail-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data
        ];

    }
    private function getLang()
    {
        $lang = self::$language;
        if (empty($lang) || !in_array($lang, ["th", "en", "zh-CN"], 1)) {
            $lang = "en";
        }
        return $lang;
    }
    /**
     * 生成pdf
     * */

    public function downLoad($id, $uid = null)
    {
        //导出锁
        if ($this->checkLock(RedisKey::PURCHASE_DOWNLOAD_LOCK . 'acceptance' . $uid)) {
            return ['code' => ErrCode::$SYSTEM_ERROR, 'message' => 'exporting now Please wait!'];
        } else {
            $this->setLock(RedisKey::PURCHASE_DOWNLOAD_LOCK . 'sample' . $uid, 1, 10);
        }
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $download_url = '';
        $lang        = $this->getLang();

        try {
            // 文件临时目录
            $sys_tmp_dir = sys_get_temp_dir();
            $file_dir    = $file_dir = $sys_tmp_dir . '/';

            $file_name = 'purchase_sample_' . md5($id) . "_{$lang}.pdf";
            $file_path = $file_dir . $file_name;
            $data      = $this->getDetail($id, $uid);
            if ($data['code'] != ErrCode::$SUCCESS) {
                throw new BusinessException('获取采购订单信息失败', $data['code']);
            }
            $data = $data['data'];
            if (!empty($companyModel) && !empty($companyModel->company_name)) {
                $departId = $companyModel->company_id;
            } else {
                $companyFindName = 'PH' == get_country_code()? 'Flash Express_PH' : 'Flash Express';
                $companyModel = SysDepartmentModel::findFirst([
                    'conditions' => ' name = :name: ',
                    'bind' => [
                        'name' => $companyFindName
                    ],
                ]);
                $departId = $companyModel->company_id ?? 0;
            }
            $departModel = SysDepartmentModel::findFirst([
                'conditions' => ' id = :id: ',
                'bind' => [
                    'id' => $departId
                ],
            ]);
            // 公司地址
            $data['sap_company_address'] = !empty($departModel) ? $departModel->sap_company_address : '';
            // tax id
            $data['sap_tax_id'] = !empty($departModel) ? $departModel->sap_tax_id : '';

            $view = new \Phalcon\Mvc\View();
            $view->setViewsDir(APP_PATH . '/views');
            $view->setVars($data);

            $view->start();
            $view->disableLevel(
                [
                    \Phalcon\Mvc\View::LEVEL_LAYOUT      => false,
                    \Phalcon\Mvc\View::LEVEL_MAIN_LAYOUT => false,
                ]
            );

            $view->render("purchase", "acceptance_" . $lang);

            $view->finish();
            $content = $view->getContent();

            $mpdf = new Mpdf([
                'format' => 'A4',
                'mode'   => 'zh-CN'
            ]);

            $mpdf->useAdobeCJK = true;
            $mpdf->SetDisplayMode('fullpage');
            $mpdf->SetHTMLHeader("");
            $mpdf->SetHTMLFooter("");
            $mpdf->WriteHTML($content);
            $mpdf->Output($file_path, "f");
            // pdf 加水印
            WaterMarkerService::getInstance()->addWaterMarkerToPdfFile($file_path, $file_path);

            // 生成成功, 上传OSS
            $upload_res   = OssHelper::uploadFile($file_path);
            $download_url = !empty($upload_res['object_url']) ? $upload_res['object_url'] : '';


        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Mpdf\MpdfException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (Exception $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('Purchase-sample-detail-download-failed:' . $real_message);
        }
        $this->unLock(RedisKey::PURCHASE_DOWNLOAD_LOCK . 'sample' . $uid);
        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $download_url
        ];
    }

    /**
     * 获取验收类型相关订单
     * */
    public function getRelateOrder($param)
    {

        if ($param['type'] == PurchaseEnums::ACCEPTANCE_TYPE_PO) {


            $builder = $this->modelsManager->createBuilder();
            $builder->from(['o' => PurchaseOrder::class]);
            if (!empty($param['no'])) {
                $builder->andWhere('o.pono LIKE :no:', ['no' => '%' . $param['no'] . '%']);
            }
            $builder->andWhere('o.status = :status:', ['status' => Enums::WF_STATE_APPROVED]);

            $builder->orderBy('o.id desc');

            $builder->columns('id,pono');
            $builder->limit(GlobalEnums::DEFAULT_PAGE_SIZE, 0);
            $items = $builder->getQuery()->execute()->toArray();


        } else {

            $builder = $this->modelsManager->createBuilder();
            $builder->from(['o' => PurchaseApply::class]);
            if (!empty($param['no'])) {
                $builder->andWhere('o.pano LIKE :no:', ['no' => '%' . $param['no'] . '%']);
            }
            $builder->andWhere('o.status = :status:', ['status' => Enums::WF_STATE_APPROVED]);

            $builder->orderBy('o.id desc');

            $builder->columns('id,pano');
            $builder->limit(GlobalEnums::DEFAULT_PAGE_SIZE, 0);
            $items = $builder->getQuery()->execute()->toArray();
        }

        return $items;

    }


}