<?php

namespace App\Modules\Purchase\Services;

use App\Library\ErrCode;
use App\Modules\Purchase\Models\PurchaseProductCategory;
use App\Modules\Purchase\Models\PurchaseProducts;

class CategoryService extends BaseService
{
    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return CategoryService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self();
        }
        return self::$instance;
    }


    public function getList($uid){

        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];

        $user = $this->getUserMetaFromBi($uid);



        try {
            $list = PurchaseProductCategory::find()->toArray();
            $products = $this->getProductArray();

            if(empty($list)||empty($products)){
                throw new \Exception("no products");
            }

            foreach ($list as $k=>$v){
                //如果广告类，但不是市场部门，直接跳过
                /*if($v['id']==Enums::PURCHASE_PRODUCT_AD_CATEGORY&& !$this->isMarketing($user)){
                    continue;
                }*/

                $temp = [];
                //$temp['id'] = $v['id'];
                $temp['name'] =static::$t->_($v['name_key']);
                $temp['finance_code'] = $v['finance_code'];
                //$temp['is_only'] = $v['is_only'];
                $temp['list'] = [];

                if($v['pid']==0){
                    unset($temp['finance_code']);
                    $data[$v['id']] = $temp;
                }else{
                    if(isset($products[$v['id']])){
                        //$temp['list'] = array_keys($products[$v['id']]);
                        $temp['list'] = $products[$v['id']];
                    }
                    $data[$v['pid']]['list'][$v['id']] = $temp;
                }
            }
            //$data = array_values($data);
        }catch (\Exception $e){
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->getDI()->get('logger')->warning('contract-update-failed:' . $real_message);
        }


        return [
            'code' => $code,
            'message' => $message,
            'data' =>$data
        ];
    }


    public function getProductArray(){
        $list = PurchaseProducts::find()->toArray();

        $data = [];

        foreach ($list as $k=>$v){
            if(empty($data[$v['cid']])){
                $data[$v['cid']] =[];
            }

            $name_key = static::$t->_($v['name_key']);

            if(empty($data[$v['cid']][$name_key])){
                $data[$v['cid']][$name_key] = [];
            }

            $temp =[];
            $temp['id'] = $v['id'];
            //$temp['name'] = $name_key;
            $temp['unit'] = static::$t->_($v['unit']);
            $temp['option'] = $v['option'];
            $temp['option_code'] = $v['option_code'];

            $data[$v['cid']][$name_key][] = $temp;
        }
        return $data;
    }

    public function isMarketing($userArr){
        if(empty($userArr)){
            return false;
        }
        if($userArr['department_id']==11 || $userArr['node_department_id']==41){
            return true;
        }
        return false;
    }

}
