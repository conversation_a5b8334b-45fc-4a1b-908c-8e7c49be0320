<?php

namespace App\Modules\Purchase\Services;

use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\VendorEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\OssHelper;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Services\WaterMarkerService;
use App\Modules\Organization\Models\SysDepartmentModel;
use App\Modules\Purchase\Models\PurchaseSampleModel;
use App\Modules\Purchase\Models\PurchaseSampleProductModel;
use App\Modules\Purchase\Models\Vendor;
use App\Modules\Purchase\Services\BaseService as PurchaseService;
use App\Modules\Setting\Services\CommonDataPermissionService;
use App\Modules\User\Models\AttachModel;

use App\Modules\Workflow\Services\FYRService;
use App\Modules\Workflow\Services\WorkflowServiceV2;
use App\Util\RedisKey;
use Mpdf\Mpdf;
use App\Modules\Common\Services\StoreService;


/**
 * Created by PhpStorm.
 * Date: 2022/2/28
 * Time: 11:08
 */
class SampleService extends BaseService
{
    //add参数校验
    public static $validate_add_param = [
        'no'                  => 'Required|StrLenGeLe:10,20',
        'use_staff_id'        => 'Required|StrLenGeLe:0,500',
        'confirm_staff_id'    => 'StrLenGeLe:0,500',
        'product_attachments' => 'Required|Arr|ArrLenGeLe:1,5',//产品需求附件
        'supply_attachments'  => 'Required|Arr|ArrLenGeLe:1,5',//供应商样品附件
        'picture_attachments' => 'Required|Arr|ArrLenGeLe:1,10',//样品照片附件
        'test_attachments'    => 'Arr|ArrLenGeLe:0,5',//样品测试报告附件

        'product'                        => 'Required|Arr|ArrLenGeLe:1,20',
        'product[*].vendor_name'         => 'StrLenGeLe:1,128',
        'product[*].sample_cate'         => 'Required|StrLenGeLe:0,64',
        'product[*].pc_code'             => 'Required|StrLenGeLe:0,64',
        'product[*].send_reason'         => 'Required|Int',
        'product[*].product_option_code' => 'Required|StrLenGeLe:0,64',
        'product[*].sample_name'         => 'Required|StrLenGeLe:1,50',
        'product[*].sample_desc'         => 'Required|StrLenGeLe:0,50',
        'product[*].num'                 => 'Required|IntGeLe:0,1000',
        'product[*].unit'                => 'Required|StrLenGeLe:1,30',
        'product[*].appearance'          => 'Required|IntIn:1,2',
        'product[*].size'                => 'Required|IntIn:1,2',
        'product[*].material'            => 'Required|IntIn:1,2',//材质
        'product[*].performance'         => 'Required|IntIn:1,2',//
        'product[*].send_result'         => 'Required|IntIn:1,2',
        'product[*].send_result_desc'    => 'IfIntEq:send_result,2|Required|StrLenGeLe:1,100',

    ];

    public static $validate_update_param = [
        'id' => 'Required|Int',

    ];


    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return SampleService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 新增默认
     * */
    public function defaultData($user)
    {

        $code         = ErrCode::$SUCCESS;
        $message      = 'success';
        $real_message = '';


        $data = [];
        try {
            if (empty($user)) {
                throw new ValidationException("The employee information is null [{$user['id']}]", ErrCode::$VALIDATE_ERROR);
            }

            $arr = $this->getUserMetaFromBi($user['id']);
            if (empty($arr)) {
                throw new ValidationException(static::$t->_("re_staff_info_id_not_exist"), ErrCode::$VALIDATE_ERROR);
            }

            $data['no']        = 'YPQR' . static::getNo(date("Ymd"));
            $data['create_id'] = $user['id'];

            $data['created_name'] = $this->getNameAndNickName($user['name'] ?? '', $user['nick_name'] ?? '');


        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();

        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $data         = [];
        }

        if (!empty($real_message)) {
            $this->getDI()->get('logger')->warning('sample-get-default-data-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data
        ];

    }

    public function createOrder($user, $data)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db      = $this->getDI()->get('db_oa');
        if ($this->checkLock(RedisKey::PURCHASE_SAVE_LOCK . $data['no'])) {
            return ['code' => ErrCode::$SYSTEM_ERROR, 'message' => 'Please retry and refresh!'];
        } else {
            $this->setLock(RedisKey::PURCHASE_SAVE_LOCK . $data['no'], 1, 10);
        }

        $db->begin();

        try {
            if (!isset($data['no']) || empty($data['no'])) {
                throw new BusinessException('样品确认单不存在', ErrCode::$VALIDATE_ERROR);
            }
            //增加供应商判断
            $this->handleData($data);

            $sample_data = [
                'no'                => $data['no'],
                'status'            => Enums::ACCEPTANCE_APPLY_STATUS_PENDING,
                'cost_company_name' => $data['cost_company_name'] ?? '',
                'cost_company_id'   => $data['cost_company_id'] ?? '',
                'create_id'         => $user['id'],
                'created_name'      => $data['created_name'],
                'use_staff_id'      => $data['use_staff_id'],
                'confirm_staff_id'  => $data['confirm_staff_id'] ?? '',
                'created_at'        => date('Y-m-d H:i:s'),
                'updated_at'        => date('Y-m-d H:i:s'),
                'create_department_id'   => !empty($user['node_department_id']) ? $user['node_department_id'] : 0,
                'create_department_name' => !empty($user['department']) ? $user['department'] : NULL,
            ];

            $model = new PurchaseSampleModel();
            $bool  = $model->i_create($sample_data);
            if ($bool === false) {
                $messages = $model->getMessages();

                throw new \Exception('样品确认单主表创建失败=' . implode(",", $messages), ErrCode::$CONTRACT_CREATE_ERROR);
            }

            //行数据写入
            $sample_product_bool  = false;
            $sample_product_model = new PurchaseSampleProductModel();
            $product_data = [];
            foreach ($data['product'] as $key => $sample_product) {
                $product_data[]= [
                    'sample_id'           => $model->id,
                    'serial_id'           => $key+1,
                    'vendor_name'         => $sample_product['vendor_name'],
                    'vendor_id'           => $sample_product['vendor_id'],
                    'product_option_code' => $sample_product['product_option_code'],
                    'pc_code'             => $sample_product['pc_code'],
                    'store_name'          => $sample_product['store_name'],
                    'sample_cate'         => $sample_product['sample_cate'],
                    'send_reason'         => $sample_product['send_reason'],
                    'sample_name'         => $sample_product['sample_name'],
                    'sample_desc'         => $sample_product['sample_desc'],
                    'unit'                => $sample_product['unit'],
                    'num'                 => $sample_product['num'],
                    'appearance'          => $sample_product['appearance'],
                    'size'                => $sample_product['size'],
                    'material'            => $sample_product['material'],
                    'performance'         => $sample_product['performance'],
                    'send_result'         => $sample_product['send_result'],
                    'send_result_desc'    => $sample_product['send_result_desc'],
                ];

            }

            $sample_product_bool = $sample_product_model->batch_insert($product_data);
            if ($sample_product_bool === false) {
                $messages = $sample_product_bool->getMessages();
                throw new BusinessException('采购样品确认单-产品创建失败=' . implode(",", $messages), ErrCode::$CONTRACT_CREATE_ERROR);
            }
            $attachments = [];
            if (!empty($data['product_attachments'])) {

                foreach ($data['product_attachments'] as $attachment) {
                    $attachments[] = [
                        'file_name'       => $attachment['file_name'] ?? '',
                        'bucket_name'     => $attachment['bucket_name'] ?? '',
                        'object_key'      => $attachment['object_key'] ?? '',
                        'oss_bucket_key'  => $model->id,
                        'sub_type'        => Enums::OSS_SUB_TYPE_PURCHASE_SAMPLE_PRODUCT,
                        'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_PURCHASE_SAMPLE,
                    ];
                }
            }


            if (!empty($data['supply_attachments'])) {

                foreach ($data['supply_attachments'] as $attachment) {
                    $attachments[] = [
                        'file_name'       => $attachment['file_name'] ?? '',
                        'bucket_name'     => $attachment['bucket_name'] ?? '',
                        'object_key'      => $attachment['object_key'] ?? '',
                        'oss_bucket_key'  => $model->id,
                        'sub_type'        => Enums::OSS_SUB_TYPE_PURCHASE_SAMPLE_SUPPLY,
                        'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_PURCHASE_SAMPLE,
                    ];
                }
            }

            if (!empty($data['picture_attachments'])) {

                foreach ($data['picture_attachments'] as $attachment) {
                    $attachments[] = [
                        'file_name'       => $attachment['file_name'] ?? '',
                        'bucket_name'     => $attachment['bucket_name'] ?? '',
                        'object_key'      => $attachment['object_key'] ?? '',
                        'oss_bucket_key'  => $model->id,
                        'sub_type'        => Enums::OSS_SUB_TYPE_PURCHASE_SAMPLE_PICTURE,
                        'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_PURCHASE_SAMPLE,
                    ];
                }
            }

            if (!empty($data['test_attachments'])) {

                foreach ($data['test_attachments'] as $attachment) {
                    $attachments[] = [
                        'file_name'       => $attachment['file_name'] ?? '',
                        'bucket_name'     => $attachment['bucket_name'] ?? '',
                        'object_key'      => $attachment['object_key'] ?? '',
                        'oss_bucket_key'  => $model->id,
                        'sub_type'        => Enums::OSS_SUB_TYPE_PURCHASE_SAMPLE_TEST,
                        'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_PURCHASE_SAMPLE,
                    ];
                }
            }


            $t_model     = new AttachModel();
            $attach_bool = $t_model->batch_insert($attachments);
            if ($attach_bool === false) {
                $messages = $t_model->getMessages();
                throw new BusinessException('采购样品确认单-附件创建失败=' . implode(",", $messages), ErrCode::$CONTRACT_CREATE_ERROR);
            }

            $flow_bool = (new PayFlowService(Enums::WF_PURCHASE_SAMPLE))->createRequest($model->id, $user);
            if ($flow_bool === false) {
                throw new BusinessException(static::$t->_('sample_create_work_flow_failed'), ErrCode::$LOAN_CREATE_WORK_FLOW_ERROR);
            }

            $db->commit();


        } catch (ValidationException $e) {      //校验错误，可对外抛出

            $code    = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {        //业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {               //系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('purchaseSample-create-failed:' . $real_message);
            $db->rollback();

        }
        $this->unLock(RedisKey::PURCHASE_SAVE_LOCK . $data['no']);

        return [
            'code'    => $code,
            'message' => $message
        ];
    }


    public function getList($condition, $user = [], $type = 0, $export = false)
    {
        if (!empty($user['id'])) {
            $condition['uid'] = $user['id'];
        }

        $page_size = empty($condition['pageSize']) ? 20 : $condition['pageSize'];
        $page_num = empty($condition['pageNum']) ? 1 : $condition['pageNum'];
        $offset = $page_size * ($page_num - 1);

        $code    = ErrCode::$SUCCESS;
        $message = '';
        $data    = [];
        $items   = [];

        try {
            if ($export) {
                $column_str = 'o.id,o.no,o.cost_company_name,o.confirm_staff_id,o.created_name,o.create_id,o.status,p.vendor_name,p.sample_cate,p.pc_code,p.store_name,p.send_reason,p.product_option_code,p.sample_name,p.sample_desc,p.num,p.unit,p.appearance,p.size,p.material,p.performance,p.send_result,p.send_result_desc';
            } else {
                $column_str = 'o.id,o.no,o.status,o.cost_company_name,o.cost_company_id,o.approve_at,o.created_name,o.create_id,o.created_at';
            }

            // 审核模块的已处理列表, 展示处理时间
            if ($type == self::LIST_TYPE_AUDIT && isset($condition['flag']) && $condition['flag'] == GlobalEnums::AUDIT_TAB_PROCESSED) {
                $column_str .= ',log.audit_at';
            }

            $builder = $this->modelsManager->createBuilder();
            $builder->from(['o' => PurchaseSampleModel::class]);
            $builder->leftjoin(PurchaseSampleProductModel::class, 'o.id=p.sample_id', 'p');
            $builder = $this->getCondition($builder, $condition, $type, $user);
            $builder->andWhere('o.is_del = :is_del:', ['is_del' => 0]);

            $count = (int) $builder->columns('COUNT(DISTINCT o.id) AS total')->getQuery()->getSingleResult()->total;

            if ($count) {
                $builder->columns($column_str);

                if (!$export) {
                    $builder->groupBy('o.id');
                    $builder->limit($page_size, $offset);
                }

                $items = $builder->getQuery()->execute()->toArray();
                if ($export) {
                    $items = $this->exportHandleList($items);
                } else {
                    $this->handleListItems($items);
                }
            }

            $data = [
                'items'      => $items,
                'pagination' => [
                    'current_page' => $page_num,
                    'per_page'     => $page_size,
                    'total_count'  => $count,
                ]
            ];

        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $this->logger->error('purchase_sample-list-failed:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data
        ];

    }

    public function getCondition($builder, $condition, $type = 0, $user = [])
    {
        $create_id = $condition['create_id'] ?? '';

        // 征询回复进度
        $is_reply = $condition['is_reply'] ?? GlobalEnums::CONSULTED_REPLY_STATE_PENDING;
        $is_reply = in_array($is_reply, GlobalEnums::CONSULTED_REPLY_STATE_ITEM) ? $is_reply : GlobalEnums::CONSULTED_REPLY_STATE_PENDING;

        // 审批处理进度
        $flag = $condition['flag'] ?? GlobalEnums::AUDIT_TAB_PENDING;
        $flag = in_array($flag, GlobalEnums::AUDIT_TAB_STATE_ITEM) ? $flag : GlobalEnums::AUDIT_TAB_PENDING;

        $status = empty($condition['status'])?[]:explode(',',$condition['status']);
        //审核列表
        if ($type == self::LIST_TYPE_AUDIT) {
            $builder = (new WorkflowServiceV2())->getBizWorkflowOrderListByProcessState($builder, $flag, [Enums::WF_PURCHASE_SAMPLE], $condition['uid'], 'o');

        } else if ($type == self::LIST_TYPE_APPLY) {
            if (isset($condition['uid']) && !empty($condition['uid'])) {
                $builder->andWhere('o.create_id = :uid:', ['uid' => $condition['uid']]);
            }
            $builder->orderBy('o.created_at desc');

        } else if ($type == self::LIST_TYPE_FYR) {
            // 意见征询回复列表
            $biz_table_info = ['table_alias' => 'o'];
            $builder = (new FYRService())->getBizOrderConsultationReplyListByProcessState($builder, $is_reply, [Enums::WF_PURCHASE_SAMPLE], $condition['uid'], $biz_table_info);

        } else if ($type == self::LIST_TYPE_DATA) {
            // 对接通用数据权限
            // 业务表参数
            $table_params = [
                'table_alias_name' => 'o',
                'create_id_field' => 'create_id',
                'create_node_department_id_filed' => 'create_department_id',
            ];
            $builder = CommonDataPermissionService::getInstance()->getCommonDataPermission($user, $builder, Enums\SysConfigEnums::SYS_MODULE_PURCHASE_SAMPLE, $table_params);
            $builder->orderBy('o.created_at desc');

        } else {
            $builder->orderBy('o.created_at desc');
        }

        //工号或者姓名
        if (!empty($create_id)) {
            $builder->andWhere('o.create_id = :create_id: or o.created_name=:create_id:', ['create_id' => $create_id]);
        }

        if (!empty($status)) {
            $builder->inWhere('o.status', $status);
        }
        if (!empty($condition['cost_company_id'])) {
            //处理
            if (is_array($condition['cost_company_id'])) {
                $builder->andWhere('o.cost_company_id IN ({cost_company_id:array})', ['cost_company_id' => array_values($condition['cost_company_id'])]);
            } else {
                $builder->andWhere('o.cost_company_id = :cost_company_id:', ['cost_company_id' => $condition['cost_company_id']]);
            }
        }


        if (!empty($condition['no'])) {
            $builder->andWhere('o.no = :no:', ['no' => $condition['no']]);
        }

        if (!empty($condition['sample_name'])) {
            $builder->andWhere('p.sample_name  =:sample_name:', ['sample_name' => $condition['sample_name']]);
        }

        if (!empty($condition['vendor_name'])) {
            $builder->andWhere('p.vendor_name =:vendor_name:', ['vendor_name' => $condition['vendor_name']]);
        }
        //申请时间-起始日期
        if (isset($condition['apply_start_date']) && !empty($condition['apply_start_date'])) {
            $condition['apply_start_date'] .= ' 00:00:00';

            $builder->andWhere('o.created_at >= :apply_start_date:', ['apply_start_date' => $condition['apply_start_date']]);
        }

        //申请时间-截止日期
        if (isset($condition['apply_end_date']) && !empty($condition['apply_end_date'])) {
            $condition['apply_end_date'] .= ' 23:59:59';

            $builder->andWhere('o.created_at <= :apply_end_date:', ['apply_end_date' => $condition['apply_end_date']]);
        }

        //申请时间-起始日期
        if (isset($condition['approved_start_date']) && !empty($condition['approved_start_date'])) {
            $condition['approved_start_date'] .= ' 00:00:00';
            $builder->andWhere('o.approve_at >= :approved_start_date:', ['approved_start_date' => $condition['approved_start_date']]);
        }

        //申请时间-截止日期
        if (isset($condition['approved_end_date']) && !empty($condition['approved_end_date'])) {
            $condition['approved_end_date'] .= ' 23:59:59';

            $builder->andWhere('o.approve_at <= :approved_end_date:', ['approved_end_date' => $condition['approved_end_date']]);
        }


        return $builder;
    }

    private function handleListItems(&$items)
    {

        if (empty($items) || !is_array($items)) {
            return [];
        }
        $status = Enums::$acceptance_apply_status;

        foreach ($items as &$item) {

            $item['status_txt'] = static::$t->_($status[$item['status']]);
            $item['created_at'] = date('Y-m-d', strtotime($item['created_at']));
        }


        return $items;

    }

    public function getEnumsParams()
    {
        $code                    = ErrCode::$SUCCESS;
        $message                 = '';
        $data                    = [];
        $acceptance_status       = [];
        $acceptance_apply_status = Enums::$acceptance_apply_status;

        foreach ($acceptance_apply_status as $key => $value) {
            $row['id']           = (string)$key;
            $row['name_key']     = static::$t->_($value);
            $acceptance_status[] = $row;
        }
        $data = [
            'cost_company' => (new PurchaseService())->getCooCostCompany(),
            'status'       => $acceptance_status,
            'send_reason'  => $this->sampleSendReason(),
            'qualified'    => $this->sampleQualified()
        ];
        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data
        ];
    }


    /**
     * 入库单详情
     * */
    public function getDetail($id, $uid)
    {
        $code         = ErrCode::$SUCCESS;
        $message      = $real_message = '';
        $sample_order = [];
        try {
            $sample_order = PurchaseSampleModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $id]
            ]);
            if (empty($sample_order)) {
                return [
                    'code'    => ErrCode::$SUCCESS,
                    'message' => 'No Data',
                    'data'    => []
                ];
            }
            $sample_order = $sample_order->toArray();

            $req = (new PayFlowService(Enums::WF_PURCHASE_SAMPLE))->getRequest($id);

            if (empty($req->id)) {
                throw new BusinessException('获取工作流批次失败');
            }


            // 获取附件信息
            $attaches = AttachModel::find([
                'conditions' => 'oss_bucket_key = :oss_bucket_key: AND oss_bucket_type = :oss_bucket_type: AND deleted =0',
                'bind'       => ['oss_bucket_key' => $id, 'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_PURCHASE_SAMPLE],
                'columns'    => ['bucket_name', 'object_key', 'file_name', 'sub_type']
            ])->toArray();


            $sample_order['product_attachments'] = [];
            $sample_order['supply_attachments']  = [];
            $sample_order['picture_attachments'] = [];
            $sample_order['test_attachments']    = [];
            $send_reason = $this->sampleSendReason();
            $send_reason = array_column($send_reason, 'name', 'id');
            $qualified   = $this->sampleQualified();
            $qualified   = array_column($qualified, 'name', 'id');

            if (!empty($attaches)) {
                foreach ($attaches as $item) {
                    switch ($item['sub_type']) {
                        case Enums::OSS_SUB_TYPE_PURCHASE_SAMPLE_PRODUCT:
                            $sample_order['product_attachments'][] = $item;
                            break;
                        case Enums::OSS_SUB_TYPE_PURCHASE_SAMPLE_SUPPLY:
                            $sample_order['supply_attachments'][] = $item;
                            break;
                        case Enums::OSS_SUB_TYPE_PURCHASE_SAMPLE_PICTURE:
                            $sample_order['picture_attachments'][] = $item;
                            break;
                        case Enums::OSS_SUB_TYPE_PURCHASE_SAMPLE_TEST:
                            $sample_order['test_attachments'][] = $item;
                            break;
                        default:
                    }
                }
            }

            $sample_order_products = PurchaseSampleProductModel::find([
                'conditions' => 'sample_id =:id:',
                'bind'       => ['id' => $id]
            ])->toArray();

            foreach ($sample_order_products  as &$item){
                $item['appearance_text'] = $qualified[$item['appearance']];
                $item['size_text'] = $qualified[$item['size']];
                $item['material_text'] = $qualified[$item['material']];
                $item['performance_text'] = $qualified[$item['performance']];
                $item['send_result_text'] = $qualified[$item['send_result']];
                $item['send_reason_text']      = $send_reason[$item['send_reason']];
                if($item['pc_code']==-1) {
                    $item['pc_code'] = 'Head Office';
                }

            }

            $ask                     = (new FYRService())->getRequestToByReplyAsk($req, $uid);
            $sample_order['ask_id']  = $ask ? $ask->id : '';
            $sample_order['product'] = $sample_order_products;

            $sample_order['auth_log'] = (new WorkflowServiceV2())->getAuditLogs($req);

        } catch (BusinessException $e) {        //业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        }

        if (!empty($real_message)) {
            $this->getDI()->get('logger')->warning('sample-detail-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $sample_order
        ];
    }


    /**
     * 更新
     * */

    public function recommitSample($user, $data)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db      = $this->getDI()->get('db_oa');

        $db->begin();

        try {
            if (!isset($data['id']) || empty($data['id'])) {
                throw new BusinessException('样品确认单不存在', ErrCode::$VALIDATE_ERROR);
            }

            //必须是提交人，和已拒绝，加锁，防止重复点击
            $model = PurchaseSampleModel::findFirst([
                'conditions' => 'id=:id: and create_id=:user_id: and status in (2,4)',
                'bind'       => ['id' => $data['id'], "user_id" => $user['id']],
                'for_update' => true
            ]);

            if (empty($model)) {
                throw new ValidationException(static::$t->_("request_has_submit"), ErrCode::$CONTRACT_GET_INFO_ERROR);
            }

            // 非同一个报销单的重新提交
            if ($model->no != $data['no']) {
                throw new ValidationException(static::$t->_("data_error_prohibit_resubmission"), ErrCode::$VALIDATE_ERROR);
            }
            //更新主表数据

            $sample_data = [
                'status'            => Enums::ACCEPTANCE_APPLY_STATUS_PENDING,
                'cost_company_name' => $data['cost_company_name'] ?? '',
                'cost_company_id'   => $data['cost_company_id'] ?? '',
                'create_id'         => $user['id'],
                'created_name'      => $data['created_name'],
                'use_staff_id'      => $data['use_staff_id'],
                'confirm_staff_id'  => $data['confirm_staff_id'] ?? '',
                'updated_at'        => date('Y-m-d H:i:s'),
            ];

            $bool  = $model->update($sample_data);

            if ( $bool === false) {
                throw new BusinessException('样品确认 - 主表更新失败', ErrCode::$VALIDATE_ERROR);
            }
            //附件删除重建
            $attaches_bool = AttachModel::find([
                'conditions' => 'oss_bucket_key = :oss_bucket_key: AND oss_bucket_type = :oss_bucket_type: AND deleted =0',
                'bind'       => ['oss_bucket_key' => $model->id, 'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_PURCHASE_SAMPLE],
            ])->delete();
            if($attaches_bool ===false){
                throw new BusinessException('样品确认 - 删除附件失败', ErrCode::$VALIDATE_ERROR);

            }

            //删除附表数据 新增附表数据
           $detail_bool= $model->getDetails()->delete();

            if($detail_bool ===false){
                throw new BusinessException('样品确认 - 删除详情表失败', ErrCode::$VALIDATE_ERROR);

            }

            $attachments = [];
            if (!empty($data['product_attachments'])) {

                foreach ($data['product_attachments'] as $attachment) {
                    $attachments[] = [
                        'file_name'       => $attachment['file_name'] ?? '',
                        'bucket_name'     => $attachment['bucket_name'] ?? '',
                        'object_key'      => $attachment['object_key'] ?? '',
                        'oss_bucket_key'  => $model->id,
                        'sub_type'        => Enums::OSS_SUB_TYPE_PURCHASE_SAMPLE_PRODUCT,
                        'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_PURCHASE_SAMPLE,
                    ];
                }
            }


            if (!empty($data['supply_attachments'])) {

                foreach ($data['supply_attachments'] as $attachment) {
                    $attachments[] = [
                        'file_name'       => $attachment['file_name'] ?? '',
                        'bucket_name'     => $attachment['bucket_name'] ?? '',
                        'object_key'      => $attachment['object_key'] ?? '',
                        'oss_bucket_key'  => $model->id,
                        'sub_type'        => Enums::OSS_SUB_TYPE_PURCHASE_SAMPLE_SUPPLY,
                        'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_PURCHASE_SAMPLE,
                    ];
                }
            }

            if (!empty($data['picture_attachments'])) {

                foreach ($data['picture_attachments'] as $attachment) {
                    $attachments[] = [
                        'file_name'       => $attachment['file_name'] ?? '',
                        'bucket_name'     => $attachment['bucket_name'] ?? '',
                        'object_key'      => $attachment['object_key'] ?? '',
                        'oss_bucket_key'  => $model->id,
                        'sub_type'        => Enums::OSS_SUB_TYPE_PURCHASE_SAMPLE_PICTURE,
                        'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_PURCHASE_SAMPLE,
                    ];
                }
            }

            if (!empty($data['test_attachments'])) {

                foreach ($data['test_attachments'] as $attachment) {
                    $attachments[] = [
                        'file_name'       => $attachment['file_name'] ?? '',
                        'bucket_name'     => $attachment['bucket_name'] ?? '',
                        'object_key'      => $attachment['object_key'] ?? '',
                        'oss_bucket_key'  => $model->id,
                        'sub_type'        => Enums::OSS_SUB_TYPE_PURCHASE_SAMPLE_TEST,
                        'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_PURCHASE_SAMPLE,
                    ];
                }
            }


            $t_model     = new AttachModel();
            $attach_bool = $t_model->batch_insert($attachments);
            if ($attach_bool === false) {
                $messages = $t_model->getMessages();
                throw new BusinessException('采购样品确认单-附件创建失败=' . implode(",", $messages), ErrCode::$SYSTEM_ERROR);
            }
            //行数据写入
            $sample_product_bool  = false;
            $sample_product_model = new PurchaseSampleProductModel();
            $product_data = [];
            foreach ($data['product'] as $key => $sample_product) {
                $product_data[]= [
                    'sample_id'           => $model->id,
                    'serial_id'           => $key+1,
                    'vendor_name'         => $sample_product['vendor_name'],
                    'product_option_code' => $sample_product['product_option_code'],
                    'pc_code'             => $sample_product['pc_code'],
                    'store_name'          => $sample_product['store_name'],
                    'sample_cate'         => $sample_product['sample_cate'],
                    'send_reason'         => $sample_product['send_reason'],
                    'sample_name'         => $sample_product['sample_name'],
                    'sample_desc'         => $sample_product['sample_desc'],
                    'unit'                => $sample_product['unit'],
                    'num'                 => $sample_product['num'],
                    'appearance'          => $sample_product['appearance'],
                    'size'                => $sample_product['size'],
                    'material'            => $sample_product['material'],
                    'performance'         => $sample_product['performance'],
                    'send_result'         => $sample_product['send_result'],
                    'send_result_desc'    => $sample_product['send_result_desc'],
                ];

            }

            $sample_product_bool = $sample_product_model->batch_insert($product_data);
            if ($sample_product_bool === false) {
                $messages = $sample_product_bool->getMessages();
                throw new BusinessException('采购样品确认单-产品创建失败=' . implode(",", $messages), ErrCode::$SYSTEM_ERROR);
            }
            $flow_bool = (new PayFlowService(Enums::WF_PURCHASE_SAMPLE))->recommit($data['id'], $user);
            if ($flow_bool === false) {
                throw new BusinessException(static::$t->_('sample_create_work_flow_failed'), ErrCode::$SYSTEM_ERROR);
            }

            $db->commit();


        } catch (ValidationException $e) {      //校验错误，可对外抛出

            $code    = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {        //业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {               //系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('purchaseSample-recommit-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $message
        ];
    }

    public function exportHandleList($items)
    {
        $data        = [];
        $send_reason = $this->sampleSendReason();
        $send_reason = array_column($send_reason, 'name', 'id');
        $qualified   = $this->sampleQualified();
        $qualified   = array_column($qualified, 'name', 'id');
        foreach ($items as $item) {
            $data[] = [
                $item['no'],
                $item['create_id'],
                $item['created_name'],
                $item['cost_company_name'],
                $item['confirm_staff_id'],
                $item['store_name'],
                $item['vendor_name'],
                $item['sample_cate'],
                $send_reason[$item['send_reason']],
                $item['product_option_code'],
                $item['sample_name'],
                $item['sample_desc'],
                $item['num'],
                $item['unit'],
                $qualified[$item['appearance']],
                $qualified[$item['size']],
                $qualified[$item['material']],
                $qualified[$item['performance']],
                $qualified[$item['send_result']],
                $item['send_result_desc'],
            ];

        }
        return $data;

    }

    public function exportList($params, $user = [], $type = 0)
    {

        $data = $this->getList($params, $user, $type, true);


        if ($data['code'] != ErrCode::$SUCCESS) {
            return $data;
        }
        $new_data  = $data['data']['items'];
        $file_name = "sample_" . date("YmdHis");
        $header    = [
            static::$t->_('sample_no'),//样品确认号
            static::$t->_('sample_apply_id'),  //创建人工号
            static::$t->_('sample_apply_name'),  //创建人姓名
            static::$t->_('sample_cost_company_name'),  //费用所属公司
            static::$t->_('sample_confirm_staff'),   //样品确认工号
            static::$t->_('sample_pc_code'),   //费用所属中心
            static::$t->_('sample_vendor_id'),   //供应商名称
            static::$t->_('sample_cate'),   //样品分类
            static::$t->_('sample_send_reason'),   //送样原因
            static::$t->_('sample_product_option_code'),   //物料编码
            static::$t->_('sample_sample_name'),    //样品名称
            static::$t->_('sample_product_desc'),   //样品描述
            static::$t->_('sample_order_num'),   //样品数量
            static::$t->_('sample_unit'),   //单位
            static::$t->_('sample_appearance'),   //外观
            static::$t->_('sample_store_name'),   //尺寸
            static::$t->_('sample_material'),   //材质
            static::$t->_('sample_performance'),   //功能/性能
            static::$t->_('sample_send_result'),   //送样结果
            static::$t->_('sample_result_desc'),   //送样结果说明

        ];
        return $this->exportExcel($header, $new_data, $file_name);


    }
    private function getLang()
    {
        $lang = self::$language;
        if (empty($lang) || !in_array($lang, ["th", "en", "zh-CN"], 1)) {
            $lang = "en";
        }
        return $lang;
    }

    /**
     * 生成pdf
     * */

    public function downLoad($id, $uid = null)
    {
        //导出锁
        if ($this->checkLock(RedisKey::PURCHASE_DOWNLOAD_LOCK . 'sample' . $uid)) {
            return ['code' => ErrCode::$SYSTEM_ERROR, 'message' => 'exporting now Please wait!'];
        } else {
            $this->setLock(RedisKey::PURCHASE_DOWNLOAD_LOCK . 'sample' . $uid, 1, 10);
        }
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $download_url = '';
        $lang        = $this->getLang();

        try {
            // 文件临时目录
            $sys_tmp_dir = sys_get_temp_dir();
            $file_dir    = $file_dir = $sys_tmp_dir . '/';

            $file_name = 'purchase_sample_' . md5($id) . "_{$lang}.pdf";
            $file_path = $file_dir . $file_name;
            $data      = $this->getDetail($id, $uid);
            if ($data['code'] != ErrCode::$SUCCESS) {
                throw new BusinessException('获取采购订单信息失败', $data['code']);
            }
            $data = $data['data'];
            if (!empty($companyModel) && !empty($companyModel->company_name)) {
                $departId = $companyModel->company_id;
            } else {
                $companyFindName = 'PH' == get_country_code()? 'Flash Express_PH' : 'Flash Express';
                $companyModel = SysDepartmentModel::findFirst([
                    'conditions' => ' name = :name: ',
                    'bind' => [
                        'name' => $companyFindName
                    ],
                ]);
                $departId = $companyModel->company_id ?? 0;
            }
            $departModel = SysDepartmentModel::findFirst([
                'conditions' => ' id = :id: ',
                'bind' => [
                    'id' => $departId
                ],
            ]);

            // 公司地址
            $data['sap_company_address'] = !empty($departModel) ? $departModel->sap_company_address : '';
            // tax id
            $data['sap_tax_id'] = !empty($departModel) ? $departModel->sap_tax_id : '';

            $view = new \Phalcon\Mvc\View();
            $view->setViewsDir(APP_PATH . '/views');
            $view->setVars($data);

            $view->start();
            $view->disableLevel(
                [
                    \Phalcon\Mvc\View::LEVEL_LAYOUT      => false,
                    \Phalcon\Mvc\View::LEVEL_MAIN_LAYOUT => false,
                ]
            );

            $view->render("purchase", "sample_" . $lang);

            $view->finish();
            $content = $view->getContent();

            $mpdf = new Mpdf([
                'format' => 'A4',
                'mode'   => 'zh-CN'
            ]);

            $mpdf->useAdobeCJK = true;
            $mpdf->SetDisplayMode('fullpage');
            $mpdf->SetHTMLHeader("");
            $mpdf->SetHTMLFooter("");
            $mpdf->WriteHTML($content);
            $mpdf->Output($file_path, "f");
            // pdf 加水印
            WaterMarkerService::getInstance()->addWaterMarkerToPdfFile($file_path, $file_path);

            // 生成成功, 上传OSS
            $upload_res   = OssHelper::uploadFile($file_path);
            $download_url = !empty($upload_res['object_url']) ? $upload_res['object_url'] : '';


        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Mpdf\MpdfException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (Exception $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('Purchase-sample-detail-download-failed:' . $real_message);
        }
        $this->unLock(RedisKey::PURCHASE_DOWNLOAD_LOCK . 'sample' . $uid);
        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $download_url
        ];
    }

    /**
     * 添加时参数处理和校验 以后要处理参数可以返回$data,本期只有校验
     * @param $data
     * @throws ValidationException
     * @date 2023/4/28
     */
    public function handleData($data)
    {
        //查询详情中的供应商id
        $vendor_ids = [];
        foreach ($data['product'] as $one_product) {
            if (!empty($one_product['vendor_id'])) {
                $vendor_ids[] = $one_product['vendor_id'];
            }
        }
        //校验供应商信息
        if (!empty($vendor_ids)) {
            $vendor_ids = array_values(array_unique($vendor_ids));
            $vendor_data = Vendor::find([
                'columns' => 'vendor_id, grade_status',
                'conditions' => 'vendor_id in ({vendor_id:array})',
                'bind' => [
                    'vendor_id' => $vendor_ids,
                ]
            ])->toArray();
            if (count($vendor_data) != count($vendor_ids)) {
                throw new ValidationException(static::$t->_('purchase_sample_vendor_id_error'), ErrCode::$VALIDATE_ERROR);
            }
            foreach ($vendor_data as $vendor_info) {
                //供应商不能选择作废或草稿状态的
                if (in_array($vendor_info['grade_status'], [VendorEnums::VENDOR_GRADE_STATUS_DRAFT, VendorEnums::VENDOR_GRADE_STATUS_INVALID])) {
                    throw new ValidationException(static::$t->_('vendor_grade_status_error', ['vendor_id' => $vendor_info['vendor_id']]), ErrCode::$VALIDATE_ERROR);
                }
            }
        }
    }

}