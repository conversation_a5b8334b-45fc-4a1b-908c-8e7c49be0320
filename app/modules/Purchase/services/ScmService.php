<?php
/**
 * Created by PhpStorm.
 * Date: 2021/7/22
 * Time: 19:43
 */

namespace App\Modules\Purchase\Services;


use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
class ScmService extends BaseService
{
    private static $apiPath;
    private static $merchantID;
    private static $merchantPW;
    private static $scmConfig;
    private static $scmType;//1采购付款入库单，2scm 入库单，3同步物料到scm

    /**
     * ScmService constructor.
     * @param string $mach_code 货主mchid
     * @param int $type 1采购付款入库单，2scm 入库单，3同步物料到scm 默认2
     */
    public function __construct($mach_code, $type = self::SCM_TYPE_PO)
    {
        self::$apiPath    = env('scm_interface_url', 'https://open.flashfulfillment.co.th/');
        self::$scmConfig  = $this->getScmConfig($mach_code, $type);
        self::$merchantID = (self::$scmConfig)['mach_code'];
        self::$merchantPW = (self::$scmConfig)['mach_pwd'];
        self::$scmType = $type;
    }

    /**
     * 获取SCM配置
     * @param string $mach_code 货主mchid
     * @param int $type 1采购付款入库单，2scm 入库单，3同步物料到scm 默认2
     * @return bool|mixed|string
     */
    private function getScmConfig($mach_code, $type)
    {
        $scm_config = '';
        $scm_configs = $this->scmCargoOwner($type);
        if (empty($scm_configs)) {
            $this->getDI()->get('logger')->warning('scm config fail');
            return false;
        }

        foreach ($scm_configs as $key => $value) {
            if ($value['mach_code'] == $mach_code) {
                $scm_config = $value;
                break;
            }

        }
        if(empty($scm_config)){
            $this->getDI()->get('logger')->warning('scm config fail');
            return false;
        }
        return $scm_config;
    }




    /**
     * 绑定请求参数
     *
     * @param Array $data_arr
     * @return String
     */
    public function buildRequestParam($data_arr){
        $sign = '';
        $paramArr = array(
            "mchId" => self::$merchantID,
            "nonceStr" => time(),
        );
        $data_arr = array_merge($paramArr,$data_arr);
        ksort($data_arr);
        foreach($data_arr as $k => $v)
        {
            if(($v != null) && ($k != 'sign'))
            {
                $sign .= $k.'='.$v.'&';
            }
        }
        $sign .= "key=" . Self::$merchantPW;

        $data_arr['sign'] = $this->signParam($sign);

        $requestStr = '';
        foreach($data_arr as $k => $v)
        {
            $requestStr .= $k . "=" . urlencode($v) . '&';
        }
        return substr($requestStr, 0, -1);
    }

    /**
     * 处理字符串
     *
     * @param String $str
     * @return String
     */
    private function signParam($str){
        return strtoupper(hash("sha256", $str));
    }

    /**
     * 发送请求
     *
     * @param String $method
     * @param Array $postData
     * @param string $language 当前语种
     * @return Array
     */
    public function newPostRequest($method, $postData, $language = "")
    {
        $this->getDI()->get('logger')->info('scm_request_log:' .'method:'.$method.'====param:'.$postData);

        $real_message = '';
        //scm接口返回的code
        $scm_code = 0;
        try {
            $curl     = curl_init();
            $header[] = "Content-type: application/x-www-form-urlencoded";
            $header[] = "Accept: application/json";
            $header[] = "Accept-Language: " . ($language ? $language : static::$language);
            curl_setopt($curl, CURLOPT_URL, self::$apiPath . $method);
            curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false); // SSL certificate
            curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($curl, CURLOPT_HEADER, 0);
            curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($curl, CURLOPT_POST, true); // post
            curl_setopt($curl, CURLOPT_POSTFIELDS, $postData); // post data
            curl_setopt($curl, CURLOPT_TIMEOUT, 20);
            $responseText = curl_exec($curl);
            if (curl_errno($curl)) {
                $this->logger->warning('scm_curl_exec Errno' . curl_error($curl));
            }
            curl_close($curl);
            $data = json_decode($responseText, true);
            $this->getDI()->get('logger')->info('scm_response_log:' .'method:'.$method.'====data:'.$responseText);
            $scm_code = $data['code'] ?? ErrCode::$VALIDATE_ERROR;
            if ($scm_code != ErrCode::$SUCCESS) {
                throw new ValidationException($data['msg'] ?? static::$t->_('response_data_null'), $scm_code);
            }
            $message = $data['message'] ?? '';
            $data    = $data['data'];
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
            $real_message = $message;
            $data    = [];
            $this->getDI()->get('logger')->info(
                'flashfulfillment-newpostRequest-failed'.
                'file ' . $e->getFile() .
                ' line ' . $e->getLine() .
                ' message ' . $e->getMessage() .
                ' trace ' . $e->getTraceAsString()
            );
        } catch (\Exception $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
            $real_message = $message;
        }
        //标准型号同步的scm返回的code是以下code都不需要抛异常告警[1304=>商品不存在;1309=>商品条形码已存在错误码;12197=>不能开启ASSET;1378=>访问过于频繁，请稍后再试;100446=>商品有库存不可停用;]
        //采购模块同步的scm返回的code是以下code都不需要抛异常告警[1305=>商品未启用；1706=>出库单已取消]
        //标准型号的都不记录错误告警
        if (in_array($method, ['open/add_goods', 'open/goodsChangeStatus', 'open/goods', 'open/edit_goods', 'open/getOutboundTrackingInfo']) || (self::$scmType == 2 && in_array($scm_code, [1305, 1706]))) {
            $real_message = "";
        }
        if ($real_message) {
            $this->getDI()->get('logger')->warning('flashfulfillment-newpostRequest-failed:module===='.self::$scmType.'    ' . $message.'====param:'.$postData);
        }

        return [
            'code'    => $code ?? ErrCode::$SUCCESS,
            'message' => $message ?? '',
            'data'    => $data,
        ];
    }

    public function scmCreate($data)
    {

        foreach ($data as $k => $v) {
            if (is_array($v)) {
                $data[$k] = json_encode($v);
            }
        }
        $post_str = $this->buildRequestParam($data);
        $data     = $this->newPostRequest('arrival_notice/create', $post_str);

        return $data;
    }

    /**
     * scm 仓库基础信息
     * */

    public function getWarehouseList()
    {
        $redis               = $this->getDI()->get("redis");
        $ware_house_list_key = 'oa:scm_ware_house_list_key_'.self::$merchantID;
        $data                = $redis->get($ware_house_list_key);
        if (empty($data)) {
            $post_str = $this->buildRequestParam([]);

            $data = $this->newPostRequest('open/getWarehouseList', $post_str);
            if (1 == $data['code']) {
                $redis->setex($ware_house_list_key, 3600 * 12, json_encode($data['data']));
                return $data['data'];
            }
        }

        return json_decode($data, true) ?? [];
    }

    /**
     * scm 审核入库单
     * */
    public function audits($data)
    {
        $post_str = $this->buildRequestParam($data);
        $data     = $this->newPostRequest('Audit/inbound', $post_str);
        $logger   = $this->getDI()->get('logger');
        $logger->info('scm-audit-data:' . json_encode($data));

        return $data;
    }


    /**
     * scm 撤回入库单
     * */
    public function recall($data)
    {
        $post_str = $this->buildRequestParam($data);
        $data     = $this->newPostRequest('arrival_notice/rollback', $post_str);
        $logger   = $this->getDI()->get('logger');
        $logger->info('scm-recall-data:' . json_encode($data));

        return $data;
    }


    /**
     * scm 删除入库单
     * */
    public function delete($data)
    {
        $post_str = $this->buildRequestParam($data);
        $data     = $this->newPostRequest('arrival_notice/delete', $post_str);
        $logger   = $this->getDI()->get('logger');
        $logger->info('scm-delete-data:' . json_encode($data));
        return $data;
    }

    /**
     * 入库单详情
     * */
    public function getInBoundDetail($data)
    {
        $post_str = $this->buildRequestParam($data);
        $data     = $this->newPostRequest('Inbound/getInBoundDetail', $post_str);
        $logger   = $this->getDI()->get('logger');
        $logger->info('scm-detail-data:' . json_encode($data));
        return $data;
    }

}
