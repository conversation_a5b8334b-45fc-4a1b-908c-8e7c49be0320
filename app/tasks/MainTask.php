<?php


class MainTask extends BaseTask
{
    protected $command_list = [];
    /**
     * 进程main_pid
     */
    private $main_pid              = 0;
    public  $monitor_command_queue = 'monitor_command_queue';

    public function initialize()
    {
        parent::initialize();
        $this->schedule();
    }

    /**
     * 工作计划执行
     */
    public function schedule()
    {
        $this->commandAdd('translation gen', '', 0, 0, '生成翻译文件');
    }


    /**
     * 添加任务和时间
     * $time = * * * * *
     * $unique 是否唯一
     * $time_zone 前几个小时(-1/2/3)或后几个小时(1/2/3)
     * @param $command_name
     * @param $time
     * @param int $unique
     * @param int $time_zone
     * @param string $info
     */
    public function commandAdd($command_name, $time, $unique = 1, $time_zone = 0, $info = '')
    {
        $this->command_list[] = [
            'command'   => $command_name,
            'cron'      => $time,
            'info'      => $info,
            'unique'    => $unique,
            'time_zone' => $time_zone,
        ];
    }

    /**
     * 工作计划执行列表
     */
    public function mainAction()
    {
        echo $this->color_bym(
            PHP_EOL . __FUNCTION__ . ' [php bin/cli main main/run/start/restart]' . PHP_EOL,
            32
        );
        foreach ($this->command_list as $key => $val) {
            $__temp = str_pad($key, 2, " ", STR_PAD_RIGHT) . '  ' . str_pad($val['command'], 50, " ", STR_PAD_RIGHT);
            $__temp .= ' [' . str_pad($val['cron'], 15, " ", STR_PAD_RIGHT) . ']';
            $__temp .= ' hour_offset_time_zone ' . $val['time_zone'] . '  ' . $val['info'];
            $__temp = $this->color_bym($__temp, 33);
            echo $__temp;
        }
        echo PHP_EOL;
    }

    /**
     * 这个是列表运行
     */
    public function runAction()
    {
        $redis  = $this->getDI()->get('redisLib');
        $logger = $this->logger;
        foreach ($this->command_list as $key => $val) {
            //时间检测
            if ($this->crontab($val['cron'], get_sys_time_offset(), $val['time_zone'])) {
                //队列里是否存在代替crontab里面的flock
                if ($val['unique']) {
                    //检测这个里面是否已有相应队列
                    if (!in_array($val['command'], $redis->lrange($this->monitor_command_queue, 0, -1))) {
                        $redis->lpush($this->monitor_command_queue, $val['command']);
                    }
                } else {
                    $redis->lpush($this->monitor_command_queue, $val['command']);
                }
            }
            $logger->info(json_encode($val));
        }
    }

    /**
     * 时间计算,先设置支持固定的小时和天在 后在优化到每分钟 每小时的情况
     */
    protected function crontab($time, $time_zone = 7, $hour = 0)
    {
        try {
            //$time = '*/5 * * * *';
            $cront_list = explode(' ', $time);
            if (count($cront_list) != 5 || !is_numeric($time_zone) || !is_numeric($hour)) {
                echo "时间格式不正确";
                exit;
            }
            //Y-m-d H:i:s
            $time_str    = '';
            $Y           = 'Y'; //年
            $m           = 'm'; //月
            $d           = 'd'; //日
            $H           = 'H'; //时
            $i           = 'i'; //分
            $s           = '00';//秒 目前支持到分钟的计算秒就算了
            $before_time = gmdate("$Y-$m-$d $H:$i:$s", time() + $time_zone * 3600 + $hour * 3600);
            $after_time  = gmdate("Y-m-d H:i:$s", time() + $time_zone * 3600 + $hour * 3600);
            foreach ($cront_list as $key => $val) {
                if (intval($val) >= 60 || intval($val) < 0) {
                    echo 'time lage';
                    exit;
                }
                switch ($key) {
                    case 0://分钟
                        if ($val == '*') {
                            $i = 'i';
                        } elseif ($val == '0') {
                            $i = '00';
                        } else {
                            $i = sprintf('%02d', intval($val));
                        }
                        break;
                    case 1://小时
                        //当前小时
                        $H_time = gmdate("H", time() + $time_zone * 3600 + $hour * 3600);
                        if ($val == '*') {
                            $H = 'H';
                        } elseif ($val == '0') {
                            $H = '00';
                        } elseif (strstr($val, '/')) {
                            $H_num = explode('/', $val);//切开取后面那个数
                            if ($H_time % $H_num[1] == 0) {//处理每隔多长时间处理一下的情况
                                $H = $H_time;
                            } else {
                                $H = '00';//没有取到合适的给一个不相等的值就可以了
                            }
                        } else {
                            $H = sprintf('%02d', intval($val));
                        }
                        break;
                    case 2://天
                        if ($val == '*') {
                            $d = 'd';
                        } elseif ($val == '0') {
                            $d = '00';
                        } else {
                            $d = sprintf('%02d', intval($val));
                        }
                        break;
                    default:
                        # code...
                        break;
                }
            }
            $after_time = gmdate("$Y-$m-$d $H:$i:$s", time() + $time_zone * 3600 + $hour * 3600);
        } catch (\Exception $e) {
            print_r($e);
        }
        if (RUNTIME != 'pro' && env('monitorlist_open', false)) {
            echo __METHOD__ . $before_time . PHP_EOL;
            echo __METHOD__ . $after_time . PHP_EOL;
        }
        if ($before_time == $after_time) {
            return true;
        } else {
            return false;
        }
    }

    public function color_bym($str, $num = 32)
    {
        $color_str = "\033[1;";
        $color_str .= $num . "m" . $str . "\033[0m" . PHP_EOL;
        return $color_str;
    }

    /**
     * auto调整权限，共两部分工作
     *
     * 第一部分：bi系统新增员工追加申请借款权限
     * 第二部分：bi系统离职的员工删除申请借款权限，删除去掉了，因为有网点用户因为报销进入OA系统。离职后进入不进来，去掉删除。
     */
    public function generate_staff_permissionAction()
    {
        //[1]找出全部的在职的总部的用户id
        $staffList = \App\Modules\User\Models\HrStaffInfoModel::find([
            'conditions' => 'sys_store_id = -1 and state = 1 and formal = 1 and is_sub_staff = 0',
            'columns'    => 'staff_info_id',
        ])->toArray();
        $staffList = array_column($staffList, 'staff_info_id');

        //[2]oa系统中的权限表中涉及的全部用户
        $oaStaffList = \App\Modules\User\Models\StaffPermissionModel::find([
            'columns' => 'staff_id',
        ])->toArray();
        $oaStaffList = array_column($oaStaffList, 'staff_id');

        //[3]在A中不在B中的
        $intersect = array_intersect($staffList, $oaStaffList);
        $need2Add  = array_diff($staffList, $intersect);
        //$need2Delete = array_diff($oaStaffList, $intersect);
        //这不能删除，因为有网点账号进入了。离职进入不了OA系统
        $need2Delete = [];


        //默认权限
        //$oa_permission = env('default_staff_permission', '36,37,38,39,40,41,42,43,44,45,46,47,48,70,71,74,77,78,79,80,72,92,95,96,97');
        //$addList    = [];
        $oa_permission = env('default_staff_permission',
            '36,37,38,39,40,41,42,43,44,45,46,47,48,70,71,74,77,78,79,80,72,92,95,96,97,122,123,124,126,127,128,129,130,131,132,133,134,135');
        $addList       = [];

        echo "添加OA系统员工默认权限--START", PHP_EOL;
        echo "默认权限：{$oa_permission}", PHP_EOL;

        if (!empty($need2Add)) {
            foreach ($need2Add as $staff) {
                $addList[] = [
                    'staff_id'       => $staff,
                    'permission_ids' => $oa_permission,
                    'is_granted'     => 1,
                ];
                echo "需添加权限的工号：{$staff}", PHP_EOL;
            }
            echo "共有 " . count($addList) . " 个员工需添加权限的工号", PHP_EOL;
        } else {
            echo "无需添加权限的工号", PHP_EOL;
            return false;
        }

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $res = (new \App\Modules\User\Models\StaffPermissionModel())->batch_insert($addList);
            if ($res === false) {
                $db->rollback();
                throw new Exception('insert failed');
            }

            if (!empty($need2Delete)) {
                $result = \App\Modules\User\Models\StaffPermissionModel::find([
                    'conditions' => 'staff_id IN (?1)',
                    'bind'       => [
                        1 => implode(',', $need2Delete),
                    ],
                ]);
                $result->delete();
            }
        } catch (Exception $e) {
            $db->rollback();
            exit($e->getMessage() . PHP_EOL);
        }
        $db->commit();
        echo "添加OA系统员工默认权限--END", PHP_EOL;
    }

    //一次性给指定工号增加报销审核相关的权限
    public function add_reimbursement_permissionAction()
    {
        echo "报销权限--START", PHP_EOL;


        $staff_ids = env("reimbursement_permission_staff_id",
            '20254,54255,54249,33306,28989,26808,24905,24904,24902,23116,21958,19432,17348,17178,17152,35805,44230,54677,55849');

        $staffArr = explode(",", $staff_ids);

        $permission_ids     = "122,125,136,137,138,139";
        $permission_all_ids = "122,123,124,126,127,128,129,130,131,132,133,134,135";


        $oaStaffList = \App\Modules\User\Models\StaffPermissionModel::find([
            'columns' => 'staff_id',
        ])->toArray();


        $oaStaffList = array_column($oaStaffList, 'staff_id');
        $staffIds    = $staffArr;

        $intersect = array_intersect($staffIds, $oaStaffList);

        $add = array_diff($staffIds, $intersect);

        $last_updated_at = date("Y-m-d H:i:s");

        $addList = [];

        if (!empty($add)) {
            foreach ($add as $k => $v) {
                $addList[] = [
                    'staff_id'        => $v,
                    'permission_ids'  => $permission_ids,
                    'is_granted'      => 1,
                    'last_updated_at' => $last_updated_at,
                ];
            }
        }

        echo "添加OA系统相关员工报销权限--START" . PHP_EOL;
        echo "给数据库里现有的人都会加报销申请，报销审核" . PHP_EOL;
        echo "相关的人=>权限：" . json_encode($staffArr) . PHP_EOL;
        echo "修改的人====" . json_encode($intersect) . PHP_EOL;
        echo "添加的人====" . json_encode($add) . PHP_EOL;


        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            //需要更新的人
            if (!empty($intersect)) {
                foreach ($intersect as $k => $v) {
                    $sql  = "update staff_permission set permission_ids=concat(permission_ids,',','" . $permission_ids . "'),last_updated_at='" . $last_updated_at . "' where staff_id=" . $v;
                    $flag = $db->execute($sql);
                    if (empty($flag)) {
                        throw new Exception('update failed');
                    }
                }
            }
            if (!empty($addList)) {
                $res = (new \App\Modules\User\Models\StaffPermissionModel())->batch_insert($addList);
                if ($res === false) {
                    throw new Exception('insert failed');
                }
            }

            //给现有所有员工加报销申请，报销审核
            $sql  = "update staff_permission set permission_ids=concat(permission_ids,',','" . $permission_all_ids . "'),last_updated_at='" . $last_updated_at . "' where 1=1";
            $flag = $db->execute($sql);
            if (empty($flag)) {
                throw new Exception('update failed');
            }
        } catch (Exception $e) {
            $db->rollback();
            exit($e->getMessage() . PHP_EOL);
        }
        $db->commit();

        echo "报销权限--END", PHP_EOL;
    }


    /**
     * 给现有staff_id添加permission_ids权限。
     */
    public function update_staff_purchase_permissionAction()
    {
        //[1]找出全部的在职的总部的用户id
        $staffList = \App\Modules\User\Models\HrStaffInfoModel::find([
            'conditions' => 'sys_store_id = -1 and state = 1 and formal = 1 and is_sub_staff = 0',
            'columns'    => 'staff_info_id',
        ])->toArray();
        $staffList = array_column($staffList, 'staff_info_id');

        //[2]oa系统中的权限表中涉及的全部用户
        $oaStaffList = \App\Modules\User\Models\StaffPermissionModel::find([
            'columns' => 'staff_id',
        ])->toArray();
        $oaStaffList = array_column($oaStaffList, 'staff_id');

        $intersect = array_intersect($staffList, $oaStaffList);

        //默认权限采购管理+我的申请+我的申请_采购申请单+我的申请_采购申请单_列表，查看，新增，撤销   +我的审核菜单权限
        $oa_permission = '70,71,74,77,78,79,80,72,92,95,96,97';

        echo "补充OA系统员工默认权限--START", PHP_EOL;
        echo "默认权限：{$oa_permission}", PHP_EOL;

        $sql = "update staff_permission set permission_ids=concat(permission_ids,',','" . $oa_permission . "') where staff_id in (" . implode(",",
                $intersect) . ")";

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $res = $db->execute($sql);
            if ($res === false) {
                $db->rollback();
                throw new Exception('update failed');
            }
        } catch (Exception $e) {
            $db->rollback();
            exit($e->getMessage() . PHP_EOL);
        }
        $db->commit();
        echo "补充OA系统员工默认权限--END", PHP_EOL;
    }


    /**
     * 给相应staff_id添加permission_ids权限。
     */
    public function add_purchase_permissionAction()
    {
        $permission = [
            'apply' => [
                //申请
                0 => [70, 71, 74, 77, 78, 79, 80],
                //审核
                1 => [70, 72, 92, 95, 96, 97],
                //数据查询
                2 => [70, 73, 107, 110, 111, 112],
            ],
            'order' => [
                0 => [70, 71, 75, 81, 82, 83, 84, 85],
                1 => [70, 72, 93, 98, 99, 100, 101],
                2 => [70, 73, 108, 113, 114, 115, 116],
            ],

            'pay' => [
                0 => [70, 71, 76, 86, 87, 88, 89, 90, 91],
                1 => [70, 72, 94, 102, 103, 104, 105, 106],
                2 => [70, 73, 109, 117, 118, 119, 120, 121],
            ],
        ];


        $data = [];

        if (env('runtime') == 'dev') {
            $weiwei_id = 17152;
            $fafa_id   = 19515;
            $mary_id   = 20508;

            //21859=>28600;

            //20483,20596,20678
            //$purchase_ids = [41081,46774,52731];
            $purchase_ids = [20483, 20596, 20678];
            $ap_ids       = [];
        } else {
            $weiwei_id = 17152;
            $fafa_id   = 17008;
            $mary_id   = 20254;

            //52731training账号没有
            $purchase_ids = [52731, 41081, 46774, 29792, 19957, 41285, 28856, 34805, 44047, 23491, 31778];
            $ap_ids       = [
                54255,
                54249,
                33306,
                28989,
                26808,
                24905,
                24904,
                24902,
                23116,
                21958,
                19432,
                17348,
                17178,
                32739,
                55356,
            ];
        }


        $data[$weiwei_id] = [];
        $data[$fafa_id]   = [];
        $data[$mary_id]   = [];

        //采购申请单-审核权限
        $data[$weiwei_id][] = $permission['apply'][1];
        $data[$fafa_id][]   = $permission['apply'][1];

        //采购申请单-数据查询菜单权限
        $data[$weiwei_id][] = $permission['apply'][2];
        $data[$fafa_id][]   = $permission['apply'][2];
        $data[$mary_id][]   = $permission['apply'][2];


        //采购订单-审核
        $data[$weiwei_id][] = $permission['order'][1];
        $data[$fafa_id][]   = $permission['order'][1];

        //采购订单-数据查询菜单权限
        $data[$weiwei_id][] = $permission['order'][2];
        $data[$fafa_id][]   = $permission['order'][2];
        $data[$mary_id][]   = $permission['order'][2];


        //采购付款申请单-审核
        $data[$weiwei_id][] = $permission['pay'][1];
        $data[$fafa_id][]   = $permission['pay'][1];
        $data[$mary_id][]   = $permission['pay'][1];

        //采购付款申请单-数据查询菜单权限
        $data[$weiwei_id][] = $permission['pay'][2];
        $data[$fafa_id][]   = $permission['pay'][2];
        $data[$mary_id][]   = $permission['pay'][2];

        $data[33306][] = $permission['pay'][2];
        $data[17178][] = $permission['pay'][2];
        $data[23116][] = $permission['pay'][2];

        //采购订单数据查询
        $data[52731][] = $permission['order'][2];
        $data[41081][] = $permission['order'][2];
        $data[46774][] = $permission['order'][2];

        //采购付款申请单
        $data[52731][] = $permission['pay'][2];
        $data[41081][] = $permission['pay'][2];
        $data[46774][] = $permission['pay'][2];


        foreach ($purchase_ids as $staff_id) {
            if (empty($data[$staff_id])) {
                $data[$staff_id] = [];
            }
            //采购申请单-数据查询权限
            $data[$staff_id][] = $permission['apply'][2];

            //采购订单-申请+审核
            $data[$staff_id][] = $permission['order'][0];
            $data[$staff_id][] = $permission['order'][1];

            //采购付款申请单-申请+审核
            $data[$staff_id][] = $permission['pay'][0];
            $data[$staff_id][] = $permission['pay'][1];
        }

        //采购订单审核
        foreach ($ap_ids as $staff_id) {
            if (empty($data[$staff_id])) {
                $data[$staff_id] = [];
            }

            $data[$staff_id][] = $permission['order'][1];
            $data[$staff_id][] = $permission['pay'][1];
        }

        foreach ($data as $k => $v) {
            $tmp = [];
            foreach ($v as $kk => $vv) {
                $tmp = array_merge($tmp, $vv);
            }
            $data[$k] = array_values(array_unique($tmp));
            sort($data[$k]);
        }

        $oaStaffList = \App\Modules\User\Models\StaffPermissionModel::find([
            'columns' => 'staff_id',
        ])->toArray();
        $oaStaffList = array_column($oaStaffList, 'staff_id');
        $staffIds    = array_keys($data);

        $intersect = array_intersect($staffIds, $oaStaffList);

        $add = array_diff($staffIds, $intersect);

        $last_updated_at = date("Y-m-d H:i:s");

        $addList = [];

        if (!empty($add)) {
            foreach ($add as $k => $v) {
                $addList[] = [
                    'staff_id'        => $v,
                    'permission_ids'  => implode(",", $data[$v]),
                    'is_granted'      => 1,
                    'last_updated_at' => $last_updated_at,
                ];
            }
        }

        echo "添加OA系统相关员工采购权限--START" . PHP_EOL;
        echo "相关的人=>权限：" . json_encode($data) . PHP_EOL;
        echo "修改的人====" . json_encode($intersect) . PHP_EOL;
        echo "添加的人====" . json_encode($add) . PHP_EOL;

        //最多17个，单独执行了。
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            //需要更新的人
            if (!empty($intersect)) {
                foreach ($intersect as $k => $v) {
                    $sql  = "update staff_permission set permission_ids=concat(permission_ids,',','" . implode(",",
                            $data[$v]) . "'),last_updated_at='" . $last_updated_at . "' where staff_id=" . $v;
                    $flag = $db->execute($sql);
                    if (empty($flag)) {
                        throw new Exception('update failed');
                    }
                }
            }
            if (!empty($addList)) {
                $res = (new \App\Modules\User\Models\StaffPermissionModel())->batch_insert($addList);
                if ($res === false) {
                    throw new Exception('insert failed');
                }
            }
        } catch (Exception $e) {
            $db->rollback();
            exit($e->getMessage() . PHP_EOL);
        }
        $db->commit();

        echo "采购权限--END", PHP_EOL;
    }


    /**
     *
     * 第一部分：bi系统新增网点员工追加申请报销，报销审核权限
     */
    public function generate_staff_reimbursement_permissionAction()
    {
        //[1]找出全部的在职的非总部的用户id
        $staffList = \App\Modules\User\Models\HrStaffInfoModel::find([
            'conditions' => 'sys_store_id != -1 and state = 1 and formal = 1 and is_sub_staff = 0',
            'columns'    => 'staff_info_id',
        ])->toArray();

        $staffList = array_column($staffList, 'staff_info_id');
        //[2]oa系统中的权限表中涉及的全部用户
        $oaStaffList = \App\Modules\User\Models\StaffPermissionModel::find([
            'columns' => 'staff_id',
        ])->toArray();
        $oaStaffList = array_column($oaStaffList, 'staff_id');

        //[3]在A中不在B中的
        $intersect = array_intersect($staffList, $oaStaffList);
        $need2Add  = array_diff($staffList, $intersect);
        //不管删除了，离职的员工登录不进来。
        //$need2Delete = array_diff($oaStaffList, $intersect);

        //追加付款申请/付款审核菜单权限 218,219,220,224,225,226,227,228,229,230,231
        $oa_permission = env('default_staff_reimbursement_permission',
            '122,123,124,126,127,128,129,130,131,132,133,134,135,218,219,220,224,225,226,227,228,229,230,231');
        $addList       = [];

        echo "添加OA系统网点员工默认报销权限--START", PHP_EOL;
        echo "默认权限：{$oa_permission}", PHP_EOL;

        if (!empty($need2Add)) {
            foreach ($need2Add as $staff) {
                $addList[] = [
                    'staff_id'       => $staff,
                    'permission_ids' => $oa_permission,
                    'is_granted'     => 1,
                ];
                echo "需添加权限的工号：{$staff}", PHP_EOL;
            }
            echo "共有 " . count($addList) . " 个员工需添加权限的工号", PHP_EOL;
        } else {
            echo "无需添加权限的工号", PHP_EOL;
            return false;
        }

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $res = (new \App\Modules\User\Models\StaffPermissionModel())->batch_insert($addList);
            if ($res === false) {
                $db->rollback();
                throw new Exception('insert failed');
            }
        } catch (Exception $e) {
            $db->rollback();
            exit($e->getMessage() . PHP_EOL);
        }
        $db->commit();
        echo "添加OA系统网点员工默认报销权限--END", PHP_EOL;
    }


    /**
     * 删除Network部门采购申请权限
     */
    public function del_network_purchase_applyAction()
    {
        $staff_ids   = '["20447","20469","16890","16952","16987","17062","17109","19953","17160","17273","17334","19060","17672","17603","17715","19353","19616","19607","19761","19701","19992","19974","20166","20836","21318","20251","21536","21613","21848","22096","24015","24327","24901","25509","27047","27148","28162","28534","28590","29362","29798","17044","30989","31291","31342","31572","31803","31849","31850","32042","32310","32339","32520","32628","32946","33134","33486","33663","33763","33765","33796","33825","34007","34014","34015","34248","34596","34604","34786","34806","35172","37265","38168","38665","38668","38671","38675","39094","39465","39486","39863","40014","42321","45604","46704","48622","49950","50744","51285","51291","51849","20234","28588","52446","52546","52734","28560","17497","19689","30058","31163","31336","32003","32004","19372","53649","53800","31351","54254","54341","54897","54901","54903","54916","30111","55218","55393","55570","55883","19606","56245","32531","56579","56964","57186","57403"]';
        $staffArr    = json_decode($staff_ids, 1);
        $newStaffArr = [];
        foreach ($staffArr as $k => $v) {
            $newStaffArr[] = intval($v);
        }

        $list = \App\Modules\User\Models\StaffPermissionModel::find([
            "conditions" => 'staff_id in ({ids:array})',
            "bind"       => ['ids' => $newStaffArr],
        ])->toArray();

        $delAuthIds = [74, 77, 78, 79, 80];

        $data = [];

        foreach ($list as $k => $v) {
            $t = explode(",", $v['permission_ids']);
            if (empty($t)) {
                continue;
            }
            $data[$v['staff_id']] = implode(",", array_diff($t, $delAuthIds));
        }

        foreach ($data as $k => $v) {
            $item = \App\Modules\User\Models\StaffPermissionModel::findFirst([
                "conditions" => 'staff_id = :id:',
                "bind"       => ['id' => $k],
            ]);

            if (!empty($item)) {
                $item->permission_ids = $v;
                $item->save();
            }
        }
    }

    //一次性脚本（现有的ID）拥有“报销申请”的员工应也同时拥有普通付款功能下的“付款申请”+“付款审核”
    public function generate_staff_Append_reimbursement_permissionAction()
    {
        $oaStaffList = \App\Modules\User\Models\StaffPermissionModel::find([
            'conditions' => "FIND_IN_SET('122',permission_ids)",
            'columns'    => 'staff_id,permission_ids',
        ])->toArray();
        echo 'begin,需要操作总量:' . count($oaStaffList) . PHP_EOL;

        $permission = [218, 219, 220, 224, 225, 226, 227, 228, 229, 230, 231];
        foreach ($oaStaffList as $Key => $value) {
            $permission_id_arr = explode(',', $value['permission_ids']);
            $permission_id_arr = array_unique(array_merge($permission_id_arr, $permission));

            $sql = "update staff_permission set permission_ids = ? where staff_id = ? ;";
            $res = $this->getDI()->get('db_oa')->execute(
                $sql,
                [
                    implode(',', $permission_id_arr),
                    $value['staff_id'],
                ]
            );

            echo $value['staff_id'] . '-----结果：' . $res . PHP_EOL;
            unset($permission_id_arr);
        }
        echo 'end' . PHP_EOL;
    }

}