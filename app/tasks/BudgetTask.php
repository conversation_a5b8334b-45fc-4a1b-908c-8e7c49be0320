<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 2020/12/28
 * Time: 9:23 AM
 */

use App\Library\Enums\BudgetAdjustEnums;
use App\Library\Enums;
use App\Library\Enums\BudgetWithholdingEnums;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Modules\Budget\Models\BudgetAdjustDetailModel;
use App\Modules\Budget\Models\BudgetObject;
use App\Modules\Budget\Models\BudgetObjectOrder;
use App\Modules\Budget\Models\BudgetObjectProduct;
use App\Modules\Budget\Models\BudgetObjectDepartment;
use App\Modules\Budget\Models\BudgetObjectDepartmentAmount;
use App\Modules\Budget\Services\BudgetService;
use App\Modules\Budget\Services\BudgetSourceDataService;
use App\Modules\User\Models\DepartmentModel;
use App\Modules\Budget\Services\BudgetAdjustFlowService;
use App\Modules\User\Services\UserService;
use App\Library\ErrCode;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Budget\Services\BudgetWithholdingService;
use App\Repository\DepartmentRepository;

class BudgetTask extends BaseTask
{

    // 预算明细老数据pno的存储
    protected $exist_pno_array = [];
    protected $repeat_pno      = [];

    public function excelAction($param)
    {
        try {
            $type = $param[0]; //1 科目  2 商品
            if (empty($type)) {
                die('need type');
            }
            $name = intval($type) == 1 ? 'object' : 'product';

            $config = ['path' => BASE_PATH . '/public'];
            $excel  = new \Vtiful\Kernel\Excel($config);
            // 读取文件
            $excel_data = $excel->openFile($name . '.xlsx')
                ->openSheet()
                ->setSkipRows(1)
                ->getSheetData();

            if (empty($excel_data)) {
                return false;
            }


            if ($type == 1) {
                $this->obj($excel_data);
            }

            if ($type == 2) {
                $this->pro($excel_data);
            }
        } catch (\Exception $e) {
            die($e->getMessage());
        }
    }


    //!!!!层级 必须降序排列 子集才能找到对应上级的code
    protected function obj($excel_data)
    {
        $column = [
            0 => 'name_cn',
            1 => 'name_en',
            2 => 'name_th',
            3 => 'level',
            4 => 'is_end',
            5 => 'type',
            6 => 'p_name',
        ];
        $max    = 0;//最大层级
        foreach ($excel_data as $v) {
            $row = [];
            foreach ($v as $k => $m) {
                $m   = trim($m);
                $key = $column[$k];
                if ($key == 'is_end') {
                    $m = trim($m) == '是' ? 1 : 0;
                }

                if ($key == 'type') {//1 报销 2 采购申
                    if (trim($m) == '报销') {
                        $m = 1;
                    } elseif (trim($m) == '采购') {
                        $m = 2;
                    } elseif (trim($m) == '付款') {
                        $m = 3;
                    } else {
                        $m = intval($m);
                    }
                }
                $row[$key] = $m;
            }
            if (empty($row)) {
                continue;
            }
            if ($max != intval($row['level'])) {
                $max   = intval($row['level']) > $max ? intval($row['level']) : $max;
                $str[] = $arr_name = 'format_data_' . $max;
            }
            if (empty($$arr_name)) {
                $$arr_name = [];
            }
            //$$arr_name[] = $row;
        }

        //顶级code
        if (!empty($str)) {
            $exist_code = $exist_order = $obj_insert = $obj_order_insert = $parent_code = [];
            foreach ($str as $ar) {
                $level = explode('_', $ar);
                $level = end($level);
                //获取当前最大顶级code
                $start_code = '';
                if ($level == 1)//顶级
                {
                    $start_code = $this->get_level_code($level);
                }

                foreach ($$ar as $v) {
                    if (empty($exist_code[$v['name_cn']])) {
                        //获取上级code
                        if (!empty($v['p_name'])) {
                            $p_code     = empty($parent_code[$v['p_name']]) ? '' : $parent_code[$v['p_name']];
                            $start_code = $this->get_level_code($level, $p_code);
                        }
                        $o['level']      = $level;
                        $o['level_code'] = $start_code;
                        $o['is_end']     = intval($v['is_end']);
                        $o['name_th']    = $v['name_th'];
                        $o['name_en']    = $v['name_en'];
                        $o['name_cn']    = $v['name_cn'];

                        $obj_insert[] = $o;

                        $or_k = $v['name_cn'] . '_' . intval($v['type']);
                        if (!in_array($or_k, $exist_order)) {
                            $or['level_code']   = $start_code;
                            $or['type']         = intval($v['type']);
                            $obj_order_insert[] = $or;
                        }
                        $exist_order[] = $or_k;
                    } else {
                        $or['level_code']   = $exist_code[$v['name_cn']];
                        $or['type']         = intval($v['type']);
                        $obj_order_insert[] = $or;
                    }

                    $exist_code[$v['name_cn']]  = $start_code;
                    $or_k                       = $v['name_cn'] . '_' . intval($v['type']);
                    $exist_order[]              = $or_k;
                    $parent_code[$v['name_cn']] = $start_code;
                    $start_code                 = $this->code_add($start_code);
                }
            }

            //入库
            $db = $this->getDI()->get('db_oa');
            $db->begin();
            $obj_model = new BudgetObject();
            $flag      = $obj_model->batch_insert($obj_insert);
            if (!$flag) {
                $db->rollback();
                return false;
            }
            $obj_order_model = new BudgetObjectOrder();
            $flag            = $obj_order_model->batch_insert($obj_order_insert);
            if (!$flag) {
                $db->rollback();
                return false;
            }
            $db->commit();
        }
    }

    function code_add($code)
    {
        $len      = strlen($code);
        $new_code = intval($code) + 1;
        return str_pad($new_code, $len, "0", STR_PAD_LEFT);
    }

    protected function pro($excel_data)
    {
        try {
            $obj_name = array_column($excel_data, 0);

            $obj_name = array_map('trim', $obj_name);
            $obj_name = array_unique($obj_name);
            $obj_name = array_values($obj_name);
            $obj_data = BudgetObject::find([
                "conditions" => "name_cn in ({obj_name:array}) and is_end = :is_end: ",
                "bind"       => ["obj_name" => $obj_name, "is_end" => 1],
            ])->toArray();

            if (empty($obj_data)) {
                return false;
            }
            $obj_data = array_column($obj_data, 'level_code', 'name_cn');
            $column   = [
                0 => 'object_code',
                1 => 'name_cn',
                2 => 'name_en',
                3 => 'name_th',
                4 => 'type',
                5 => 'template_type',
            ];
            $insert   = [];
            foreach ($excel_data as $v) {
                $row = [];
                foreach ($v as $k => $m) {
                    $m   = trim($m);
                    $key = $column[$k];
                    if ($key == 'object_code') {
                        if (empty($obj_data[$m])) {
                            break;
                        }
                        $m = $obj_data[$m];
                    }
                    if ($key == 'type') {
                        if (trim($m) == '报销') {
                            $m = 1;
                        } elseif (trim($m) == '采购') {
                            $m = 2;
                        } elseif (trim($m) == '付款') {
                            $m = 3;
                        } else {
                            $m = intval($m);
                        }
                    }
                    if ($key == 'template_type') {
                        $m = intval($m);
                    }
                    $row[$key] = $m;
                }
                if (!empty($row)) {
                    $insert[] = $row;
                }
            }
            $insert = array_values($insert);
            $model  = new BudgetObjectProduct();
            return $model->batch_insert($insert);
        } catch (\Exception $e) {
            echo $e->getMessage();
        }
    }

    protected function get_level_code($level, $p_code = '')
    {
        $num                  = 3;//code 固定位数 每层级3位
        $con['conditions']    = "level = :level:";
        $con['bind']['level'] = $level;
        $con['columns']       = 'max(level_code)';

        //父目录  不为空
        if (!empty($p_code)) {
            $con['conditions']     .= " and level_code like :p_code: ";
            $con['bind']['p_code'] = "{$p_code}%";
        }
        $t    = BudgetObject::findFirst($con);
        $code = empty($t) ? '' : $t->toArray()[0];
        if (empty($code) && !empty($p_code)) {
            $code = $p_code . '001';
        } else {
            $code = intval($code) + 1;
        }
        return str_pad($code, $level * $num, "0", STR_PAD_LEFT);
    }

    public function excel_departmentAction()
    {
        set_time_limit(0);
        $year     = '2020';
        $is_check = 0;
        try {
            $config = ['path' => BASE_PATH . '/public'];
            $excel  = new \Vtiful\Kernel\Excel($config);
            // 读取文件
            $excel_data = $excel->openFile('obj_dep.xlsx')
                ->openSheet()
                ->setSkipRows(1)
                ->getSheetData();
            if (empty($excel_data)) {
                die('没有excel 数据');
            }


            //科目编码
            $all_min_obj = array_column($excel_data, 0);

            $obj_name = array_map('trim', $all_min_obj);
            $obj_name = array_unique($obj_name);
            $obj_name = array_values($obj_name);
            $obj_data = BudgetObject::find([
                "conditions" => "name_cn in ({obj_name:array}) and is_end = :is_end: ",
                "bind"       => ["obj_name" => $obj_name, "is_end" => 1],
            ])->toArray();

            if (empty($obj_data)) {
                die('没有对应科目');
            }
            $obj_data = array_column($obj_data, 'level_code', 'name_cn');

            //excel部门
            $need_dep = array_column($excel_data, 1);
            $need_dep = array_map('strtolower', $need_dep);
            $need_dep = array_unique($need_dep);
            $need_dep = array_diff($need_dep, ['']);
            $need_dep = array_values($need_dep);

            $all_dep = DepartmentModel::find([
                "conditions" => "name in ({need_dep:array}) and deleted = :deleted: ",
                "bind"       => ["need_dep" => $need_dep, "deleted" => 0],
                "columns"    => "id,LOWER(name) as name",
            ])->toArray();
            //数据库所有部门
            $all_dep = array_column($all_dep, 'id', 'name');
            //excel 字段转换
            $column = [
                0  => 'object_code',      //最小级code
                1  => 'department_id',    //部门id
                2  => 'organization_type',//组织机构
                3  => 'amount_type',      //是否共用
                4  => $year . '-01',
                5  => $year . '-02',
                6  => $year . '-03',
                7  => $year . '-04',
                8  => $year . '-05',
                9  => $year . '-06',
                10 => $year . '-07',
                11 => $year . '-08',
                12 => $year . '-09',
                13 => $year . '-10',
                14 => $year . '-11',
                15 => $year . '-12',
            ];
            //excel 横向记录 变成列
            $start     = $year . '-01';
            $month_arr = [];
            while ($start <= $year . '-12') {
                $month_int   = intval(date('m', strtotime($start)));
                $r['month']  = $start;
                $r['season'] = intval((($month_int - 1) / 3) + 1);//取月份对应季度
                $month_arr[] = $r;
                $start       = date('Y-m', strtotime("{$start} +1 month"));
            }

            $need_parent    = $need_parent_ids = [];
            $format_data    = [];                // 入库 budget_object_department数据
            $new_excel_data = [];                //excel 整理成 key val
            $error_num      = 0;                 //错误行数
            $all_num        = count($excel_data);//总数
            $is_unique      = true;              //是否有重复记录
            $check_unique   = [];

            foreach ($excel_data as &$v) {
                $error_str = '';
                $v         = array_map('trim', $v);
                $v[1]      = strtolower($v[1]);//部门转小写
                if (empty($obj_data[$v[0]]))//没科目 不管
                {
                    $error_str .= '科目匹配失败|';
                    $error_num++;
                }
                if (empty($all_dep[$v[1]]))//没部门 不管
                {
                    $error_str .= '部门匹配失败|';
                    $error_num++;
                }
                if (!in_array($v[2], ['总部', '网点']))//组织机构 非总部 网点
                {
                    $error_str .= '组织机构匹配失败|';
                    $error_num++;
                }


                $v[0] = $obj_data[$v[0]];//转科目code
                $v[1] = $all_dep[$v[1]];
                $v[2] = ($v[2] == '总部') ? 2 : 1;//根据申请人所属组织机构 1 网点 2总部'
                if ($v[3] == '是') {//需要找子部门 占用规则 1 单独自己部门额度 2 与子部门共用顶级部门预算
                    $v[3]              = 2;
                    $k                 = "{$v[1]}_{$v[0]}_{$v[2]}";
                    $need_parent[$k]   = $v;
                    $need_parent_ids[] = $v[1];
                } else {
                    $v[3] = 1;
                }

                //验证是否有重复记录
                $unique_k = "$v[0]_$v[1]_$v[2]";
                if (in_array($unique_k, $check_unique)) {
                    $is_unique = false;
                    break;
                }
                $check_unique[] = $unique_k;

                $new = [];
                foreach ($v as $index => $f) {
                    if (empty($column[$index])) {
                        continue;
                    }
                    $new[$column[$index]] = $f;
                    $new['error']         = empty($error_str) ? '' : $error_str;
                }
                $new_excel_data[] = $new;

                $row = [];
                foreach ($month_arr as $m) {
                    $row['department_id']     = $v[1];
                    $row['object_level']      = 1;
                    $row['object_code']       = $v[0];
                    $row['organization_type'] = $v[2];
                    $row['month']             = $m['month'];
                    $row['season']            = $m['season'];
                    $format_data[]            = $row;
                }
            }
            unset($excel_data);
            if (!$is_unique) {
                die('文件中数据有重复（预算科目ID·部门·组织机构相同）请删除重复行重新上传');
            }
            unset($check_unique);
            $check_data['data']      = $new_excel_data;
            $check_data['error_num'] = $error_num;

            if ($is_check == 1) {//只是验证数据 不需要入库
                $return             = $this->check($check_data, $year);
                $res                = $return['data'];
                $res['all_num']     = $all_num;
                $res['success_num'] = $all_num - $res['error_num'];
                die($return['message']);
            }

            $check_res   = $this->check($check_data, $year, 1);
            $need_delete = empty($check_res['delete']) ? [] : $check_res['delete'];//重复导入 可以删除的数据
            $need_update = empty($check_res['update']) ? [] : $check_res['update'];//已经存在占用额度 需要保留 已占用的预算额度


            //处理 共用部门 如果已经导入过数据 并且 类型没有变化 也需要 删除掉 重新导入
            if (!empty($need_parent)) {
                $need_parent_ids    = array_unique($need_parent_ids);
                $department_connect = DepartmentModel::get_sub_department($need_parent_ids);

                foreach ($need_parent as $k => $v) {
                    $exp        = explode('_', $k);
                    $par_dep_id = $exp[0];
                    $sub_ids    = empty($department_connect[$par_dep_id]) ? [] : $department_connect[$par_dep_id];
                    if (empty($sub_ids)) {
                        continue;
                    }
                    foreach ($sub_ids as $sub) {
                        foreach ($month_arr as $m) {
                            $row                      = [];
                            $row['department_id']     = $sub;
                            $row['object_level']      = 1;
                            $row['object_code']       = $v[0];
                            $row['organization_type'] = $v[2];
                            $row['month']             = $m['month'];
                            $row['season']            = $m['season'];
                            $format_data[]            = $row;
                        }
                    }
                }
            }


            //处理 最小级code 对应 上级 所有科目
            foreach ($format_data as $f) {
                $codes = $this->split_code($f['object_code']);
                $codes = array_diff($codes, [$f['object_code']]);
                if (!empty($codes)) {
                    foreach ($codes as $c) {
                        $f['object_code']  = $c;
                        $f['object_level'] = 1;
                        $format_data[]     = $f;
                    }
                }
            }
            $dep_amount = [];
            foreach ($new_excel_data as &$new) {
                $k = "{$new['department_id']}_{$new['object_code']}_{$new['organization_type']}";
                if (!empty($need_parent[$k])) {//处理子部门
//                    $new['amount_type'] = 1;
                    $sub_ids = empty($department_connect[$new['department_id']]) ? [] : $department_connect[$new['department_id']];
                    if (empty($sub_ids)) {
                        continue;
                    }
                    foreach ($sub_ids as $sub) {
                        $row                      = [];
                        $row['department_id']     = $sub;
                        $row['object_level']      = 1;
                        $row['object_code']       = $new['object_code'];
                        $row['organization_type'] = $new['organization_type'];
                        $row['amount_type']       = $new['amount_type'];
                        $dep_amount[]             = $row;
                    }
                }
            }

            $new_excel_data = array_merge($new_excel_data, $dep_amount);
            $amount_insert  = [];
            foreach ($new_excel_data as $new) {
                foreach ($month_arr as $m) {
                    //"department_id_object_code_{organization_type}_month";
                    $k                        = "{$new['department_id']}_{$new['object_code']}_{$new['organization_type']}_{$m['month']}";
                    $row                      = [];
                    $row['department_id']     = $new['department_id'];
                    $row['object_code']       = $new['object_code'];
                    $row['organization_type'] = $new['organization_type'];
                    $row['amount_type']       = $new['amount_type'];
                    $row['month']             = $m['month'];
                    $row['season']            = $m['season'];
                    $row['amount']            = empty($new[$m['month']]) ? 0 : ($new[$m['month']] * 1000);
                    $row['amount_left']       = empty($need_update[$k]) ? $row['amount'] : ($row['amount'] - $need_update[$k]);
                    $amount_insert[]          = $row;
                }
            }

            //调试
//            return $this->returnJson(ErrCode::$SUCCESS, 'success', array_merge($need_delete,$format_data,$amount_insert));

            //入库
            $db = $this->getDI()->get('db_oa');
            $db->begin();
            $obj_amount_model = new BudgetObjectDepartmentAmount();
            $obj_model        = new BudgetObjectDepartment();
            //存在需要删除的数据
            if (!empty($need_delete)) {
                foreach ($need_delete as $del) {
                    $deps_str          = implode(',', $del['department_id']);//多个部门id
                    $object_code       = $del['object_code'];
                    $organization_type = $del['organization_type'];
                    $obj_amount_model::find("department_id in ({$deps_str}) and object_code = '{$object_code}' and organization_type = $organization_type ")->delete();
                    $obj_model::find("department_id in ({$deps_str}) and object_code = '{$object_code}' and organization_type = $organization_type ")->delete();
                }
            }

            $flag = $obj_model->batch_insert($format_data);
            if (!$flag) {
                $db->rollback();
                return false;
            }
            $flag = $obj_amount_model->batch_insert($amount_insert);
            if (!$flag) {
                $db->rollback();
                return false;
            }
            $db->commit();
            echo 1;
        } catch (\Exception $e) {
            $this->logger->warning('import budget:' . $e->getMessage());
            echo $e->getMessage();
        }
    }

    protected function split_code($code)
    {
        $length = strlen($code);
        $return = [];
        if (empty($code) || $length < 3) {
            return $return;
        }

        for ($m = 3; $m <= $length; $m += 3) {
            $return[] = substr($code, 0, $m);
        }
        return $return;
    }

    //不导入 只验证数据  返回 原装excel 增加一列 错误表示
    public function check($data, $year, $is_insert = 0)
    {
        $error_num = $data['error_num'];//错误数据
        $data      = $data['data'];
        //验证额度变化  是否比 占用额度小
        $departments = array_column($data, 'department_id');
        $month_start = $year . '-01';
        $month_end   = $year . '-12';
        $all_amount  = BudgetObjectDepartmentAmount::find(
            [
                "conditions" => "department_id in ({departments:array}) and month >= :month_start: and month <= :month_end:",
                "bind"       => [
                    "departments" => $departments,
                    "month_start" => $month_start,
                    "month_end"   => $month_end,
                ],
            ])->toArray();
//        //第一次或者 新增部门 表里没数据 不需要验证
        if (empty($all_amount)) {
            if ($is_insert) {
                $return['need_delete'] = $return['need_parent_ids'] = [];
                return ['code' => 1, 'message' => 'success', 'data' => $return];
            }
            $res['error_num'] = 0;
            $res['data']      = $data;
            return ['code' => 1, 'message' => 'success', 'data' => $res];
        }
        $format_all_amount = [];
        foreach ($all_amount as $al) {
            $k                                    = "{$al['department_id']}_{$al['object_code']}_{$al['organization_type']}";
            $format_all_amount[$k]['amount_type'] = $al['amount_type'];
            $format_all_amount[$k][$al['month']]  = $al['amount'] - $al['amount_left'];
        }
//        var_dump($format_all_amount);exit;
        unset($all_amount);
        //$need_delete_sub 需删除当前部门和子部门   $need_delete 只删除当前部门 $need_update 记录 已经占用额度 用于计算 更新剩余额度
        $need_delete_sub = $need_delete = $need_update = [];
        foreach ($data as &$da) {
            $dk = "{$da['department_id']}_{$da['object_code']}_{$da['organization_type']}";
            if (!empty($format_all_amount[$dk])) {//库里有数据的情况
                //如果 共用类型不一样 占用规则 1 单独自己部门额度 2 与子部门共用顶级部门预算
                if ($da['amount_type'] != $format_all_amount[$dk]['amount_type']) {
                    if ($is_insert) {//插入操作 需要删除 验证逻辑 不用
                        // 第一次库里保存 共用2 二次不共用  删除子部门
                        if ($format_all_amount[$dk]['amount_type'] == 2) {
                            $need_delete_sub[$dk] = $da['department_id'];
                        }
                    }

                    // 第一次库里保存 不共用1 二次 共用 提示错误不能修改 因为我无法知道 该部门子部门 谁已经产生订单
                    if ($format_all_amount[$dk]['amount_type'] == 1) {
                        $da['error'] .= "数据库中存在该部门占用规则为【与子部门共用】无法修改｜";
                        $error_num++;
                    }
                } else {//共用额度相同 并且 都为 2 与子部门共用顶级部门预算 需要删除子部门 重新入库
                    if ($da['amount_type'] == 2 && $format_all_amount[$dk]['amount_type'] == 2) {
                        $need_delete_sub[$dk] = $da['department_id'];
                    }
                    if ($da['amount_type'] == 1 && $format_all_amount[$dk]['amount_type'] == 1) {
                        $need_delete[$dk] = $da['department_id'];
                    }
                }
                //验证修改后的额度 是否 小于 已经占用的 额度
                $err = [];
                foreach ($format_all_amount[$dk] as $month => $format) {
                    if ($month == 'amount_type') {
                        continue;
                    }
                    $db_amount     = $format;//数据库budget_object_department_amount存的记录 计算差值获取已经占用的额度 泰铢*1000
                    $change_amount = $da[$month] * 1000;
                    if ($db_amount > $change_amount)//已经占用的额度 大于 要修改的金额 提示错误
                    {
                        $err[] = $month;
                    }

                    if ($db_amount != 0) {//不为0 说明已经有占用额度 需要记录 如果占用为0  不需要记录 删了新增
                        $up_k               = "{$dk}_{$month}";
                        $need_update[$up_k] = $db_amount;
                    }
                }
                if (!empty($err)) {
                    $month       = implode(',', $err);
                    $da['error'] .= "{$month} 月份额度 已占用 超过修改后金额｜";
                    $error_num++;
                }
            }
        }
        //返回需要删除的 重新插入 数据
        if ($is_insert) {
            if ($error_num > 0) {
                return ['code' => -1, 'message' => '数据验证存在问题 无法导入', 'data' => $data];
            }
            //获取 需要删除 共用的 子部门
            $del = [];//整合 需要删除子部门的 和 不需要删除子部门的 数据
            if (!empty($need_delete_sub)) {
                $delete_dep = array_values($need_delete_sub);
                $delete_dep = DepartmentModel::get_sub_department($delete_dep);
                foreach ($need_delete_sub as $k => $v) {
                    //"{$da['department_id']}_{$da['object_code']}_{$da['organization_type']}";
                    $info                 = explode('_', $k);
                    $row['department_id'] = [$info[0]];
                    if (!empty($delete_dep[$info[0]])) {
                        $row['department_id'] = array_merge($row['department_id'], $delete_dep[$info[0]]);
                    }
                    $row['object_code']       = $info[1];
                    $row['organization_type'] = $info[2];
                    $del[]                    = $row;
                }
            }
            if (!empty($need_delete)) {
                foreach ($need_delete as $k => $v) {
                    $info                     = explode('_', $k);
                    $row['department_id']     = [$info[0]];
                    $row['object_code']       = $info[1];
                    $row['organization_type'] = $info[2];
                    $del[]                    = $row;
                }
            }
            $return['delete'] = $del;
            $return['update'] = $need_update;

            return ['code' => 1, 'message' => 'success', 'data' => $return];
        }


        //不需要入库 返回数据
        $result['error_num'] = $error_num;
        $result['url']       = '';
        if ($error_num > 0) {//验证有错误
//            $result['data'] = $data;
            $server        = new BudgetService();
            $column        = [
                '预算科目-中文',
                '部门',
                '组织机构',
                '是否共用',
                '一月',
                '二月',
                '三月',
                '四月',
                '五月',
                '六月',
                '七月',
                '八月',
                '九月',
                '十月',
                '十一月',
                '十二月',
                '错误信息',
            ];
            $r             = $server->export_check($data, $column);
            $result['url'] = $r['data'];
        }
        return ['code' => 1, 'message' => 'success', 'data' => $result];
    }


    /**
     * 给现有staff_id添加科目的查看权限。
     */
    public function addstaffAction($params)
    {
        //[2]oa系统中的权限表中涉及的全部用户
        $oaStaffList = \App\Modules\User\Models\StaffPermissionModel::find([
            'columns' => 'staff_id',
        ])->toArray();
        $oaStaffList = array_column($oaStaffList, 'staff_id');
        $intersect   = $oaStaffList;
        //默认权限预算科目_列表，明细查看
        $oa_permission = '216,328,336,337';
        if ($params[0]) {
            $oa_permission = $params[0];
            if (explode(',', $oa_permission) == 0) {
                echo "补充OA 权限--错误", PHP_EOL;
                return;
            }
        }

        echo "补充OA系统员工默认权限--START", PHP_EOL;
        echo "默认权限：{$oa_permission}", PHP_EOL;

        $sql = "update staff_permission set permission_ids=concat(permission_ids,',','" . $oa_permission . "') where staff_id in (" . implode(",",
                $intersect) . ")";

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $res = $db->execute($sql);
            if ($res === false) {
                $db->rollback();
                throw new Exception('update failed');
            }
        } catch (Exception $e) {
            $db->rollback();
            exit($e->getMessage() . PHP_EOL);
        }
        $db->commit();
        echo "补充OA系统员工科目查看权限--END", PHP_EOL;
    }

    /**
     *
     * 预算导入脚本补偿
     * */
    public function dealBudgetEffectiveAction($params)
    {
        $start_memory = memory_get_usage();

        $user_id   = $params[0] ?? '';
        $budget_id = $params[1] ?? '';
        $year      = $params[2] ?? '';
        $lang      = $params[3] ?? 'zh-CN';

        if (empty($user_id) || empty($year) || empty($budget_id)) {
            echo '参数输入错误';
            exit();
        }
        $user = BudgetSourceDataService::getInstance()->getUserMetaFromBi($user_id, $flag = 0);


        $isEffective = BudgetSourceDataService::getInstance()->budgetEffective($budget_id, $user, $year, $lang);
        var_dump($isEffective);

        $end_memory = memory_get_usage();

        $use_memory = $end_memory - $start_memory;

        $use_memory = $use_memory / 1024 / 1024;
        echo '使用内存' . $use_memory . 'M' . PHP_EOL;
        echo '执行结束';
        return false;
    }

    /**
     * 填充科目明细老数据的pno字段(明细的唯一标识) - 一次性刷数
     *
     * 老数据的pno生成规则: YYMMDD+4位随机数
     * YYMMDD 从明细的创建日期中取, 如果创建日期为空, 则取当前日期
     *
     * 逻辑:
     * 1. 根据科目的object_code 和 明细英文名称 name_en 分组
     * 2. 生成pno值(老数据), 反更新到上述分组的pno字段(每一组的pno是不同的)
     *
     * php app/cli.php budget gen_product_pno
     */
    public function gen_product_pnoAction()
    {
        $log = __FUNCTION__ . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        $logger_type = 'info';
        try {
            // 1. 根据科目的object_code 和 明细英文名称 name_en 分组
            $product_group_list = BudgetObjectProduct::find([
                'columns' => ['object_code', 'name_en', 'created_at', 'GROUP_CONCAT(id) AS group_ids'],
                'group'   => 'object_code, name_en',
            ])->toArray();

            $log .= '共有 ' . count($product_group_list) . ' 个(组) 明细的pno待初始化' . PHP_EOL;

            $curr_date = date('Y-m-d H:i:s');
            $db        = $this->getDI()->get('db_oa');

            // 失败的日志
            $error_log     = '';
            $success_count = 0;
            foreach ($product_group_list as $group) {
                // 生成pno
                $pno = $this->make_rand($group['created_at'] ? $group['created_at'] : $curr_date);

                // 更新明细
                $success = $db->updateAsDict(
                    'budget_object_product',
                    [
                        'pno' => $pno,
                    ],
                    "id IN ({$group['group_ids']})" // 警告！在这种情况下，值不会被转义
                );
                if ($success == false) {
                    $error_log .= "{$group['group_ids']} - $pno" . PHP_EOL;
                    continue;
                }

                $success_count++;
            }

            $log .= '成功更新: ' . $success_count . ' 组' . PHP_EOL;
            $log .= '唯一单号: ' . count(array_unique($this->exist_pno_array)) . ' 个' . PHP_EOL;
            $log .= '重复单号: ' . implode(',', $this->repeat_pno) . PHP_EOL;
            $log .= '失败日志: ' . $error_log . PHP_EOL;
        } catch (Exception $e) {
            $logger_type = 'error';
            $log         .= '异常: ' . $e->getMessage() . PHP_EOL;
        }

        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        $this->logger->$logger_type($log);
        exit($log);
    }

    /**
     * 返回pno
     *
     * @param $date_time
     * @return string
     */
    protected function make_rand($date_time)
    {
        $pno = date('ymd', strtotime($date_time)) . mt_rand(1000, 9999);

        if (in_array($pno, $this->exist_pno_array)) {
            $this->repeat_pno[] = $pno;
            return $this->make_rand($date_time);
        }

        $this->exist_pno_array[] = $pno;
        return $pno;
    }


    /**
     * 预算调整审核通过 但 数据执行异常处理
     *
     * 前提: 预算调整申请审批通过，预算数据生成失败
     *
     * 场景1: 适用预算调整 3
     *
     * @param array $params
     *
     * php app/cli.php budget budget_adjust_data
     */
    public function budget_adjust_dataAction(array $params = [])
    {
        $log = '';

        $db = $this->getDI()->get('db_oa');
        $db->begin();

        try {
            // 预算调整申请ID
            $budget_adjust_id = $params[0] ?? 0;
            $log              .= '预算调整ID: ' . $budget_adjust_id . PHP_EOL;
            if (empty($budget_adjust_id)) {
                throw new ValidationException('预算调整ID不能为空', ErrCode::$VALIDATE_ERROR);
            }

            // 当前操作人ID(终审用户工号)
            $staff_id = $params[1] ?? 0;
            $log      .= '操作人ID: ' . $staff_id . PHP_EOL;
            if (empty($staff_id)) {
                throw new ValidationException('操作人ID不能为空', ErrCode::$VALIDATE_ERROR);
            }

            // 获取操作人信息
            $user_info = (new UserService())->getLoginUser($staff_id);
            $log       .= '操作人信息: ' . json_encode($user_info, JSON_UNESCAPED_UNICODE) . PHP_EOL;
            if (empty($user_info)) {
                throw new ValidationException('操作人信息为空', ErrCode::$VALIDATE_ERROR);
            }


            $budget_adjust_service = new BudgetAdjustFlowService();
            $budget_adjust_service::setLanguage('zh-CN');
            $update_result = $budget_adjust_service->updateBudgetData($budget_adjust_id, $user_info, []);

            // 更新预算调整明细表导入字段
            $details = BudgetAdjustDetailModel::find([
                'budget_adjust_id = :budget_adjust_id:',
                'bind' => ['budget_adjust_id' => $budget_adjust_id],
            ]);
            foreach ($details as $detail) {
                if ($detail->status == BudgetAdjustEnums::BUDGET_ADJUST_DETAIL_STATUS_INVALID) {
                    continue;
                }
                $budget_adjust_detail_id = $detail->object_code;
                $amount_object           = $update_result['amount_objects'][$budget_adjust_detail_id] ?? [];
                $bool                    = $detail->i_update([
                    'before_amount' => $amount_object['before_amount'] ?? 0,
                    'after_amount'  => $amount_object['after_amount'] ?? 0,
                    'import_method' => $update_result['import_method'] ?? 0,
                ]);
                if ($bool === false) {
                    throw new BusinessException('预算明细表更新失败', ErrCode::$BUSINESS_ERROR);
                }
            }

            $db->commit();

            $log .= '执行结果: ' . json_encode($update_result, JSON_UNESCAPED_UNICODE) . PHP_EOL;
        } catch (ValidationException $e) {
            $db->rollback();

            $log .= '验证异常: msg = ' . $e->getMessage();
        } catch (Exception $e) {
            $db->rollback();

            $log .= '执行异常: msg = ' . $e->getMessage();
        }

        $this->logger->info('budget_adjust_data tast result = ' . $log);
        exit($log);
    }

    /**
     * 预提单-超时未关掉提醒邮箱
     * 每天当前早上10点定时获取状态为已通过，并且当前日期-审核通过日期>配置的科目超时未关单天数的所有预提单
     * php app/cli.php budget budget_withholding_unclosed_email
     */
    public function budget_withholding_unclosed_emailAction()
    {
        $this->checkLock(__METHOD__);
        $is_exception = false;
        $log = date('Ymd H:i:s') . ' 脚本开始执行' . PHP_EOL;
        try {
            //获取预提单-超时未关掉提醒邮箱
            $emails = EnumsService::getInstance()->getSettingEnvValueIds('budget_withholding_unclosed_email');
            if (!empty($emails)) {
                //获取状态为已通过，并且当前日期-审核通过日期>配置的科目超时未关单天数的所有预提单
                $budget_set = EnumsService::getInstance()->getSettingEnvValueMap('budget_withholding_unclosed_budget_set');
                if (!empty($budget_set)) {
                    $send_res = BudgetWithholdingService::getInstance()->sendRemind($emails, $budget_set);
                    $log .= $send_res . PHP_EOL;
                } else {
                    $log .= '未配置预提单-科目配置超时时未关单天数' . PHP_EOL;
                }
            } else {
                $log .= '未配置预提单-超时未关掉提醒邮箱' . PHP_EOL;
            }
        } catch (Exception $e) {
            $is_exception = true;
            $log .= date('Ymd H:i:s') . ' 给预提单-超时未关掉提醒邮箱发送失败[异常]' . $e->getMessage() . PHP_EOL;
        }
        $log .= date('Ymd H:i:s') . ' 脚本执行结束' . PHP_EOL;

        if ($is_exception) {
            $this->logger->warning('budget_withholding_unclosed_email_task_exception:' . $log);
        } else {
            $this->logger->info($log);
        }
        $this->clearLock(__METHOD__);
        exit($log);
    }

    /**
     * 预提单-对接SAP
     * 每10分钟跑一次
     * php app/cli.php budget budget_withholding_sap
     */
    public function budget_withholding_sapAction()
    {
        $this->checkLock(__METHOD__);
        $is_exception = false;
        $log          = date('Ymd H:i:s') . ' 脚本开始执行' . PHP_EOL;
        try {
            self::setLanguage('en');
            $cost_company_id = EnumsService::getInstance()->getSettingEnvValueIds('purchase_sap_company_ids');
            if (empty($cost_company_id)) {
                throw new ValidationException('purchase_sap_company_ids 配置为空, 请确认', ErrCode::$VALIDATE_ERROR);
            }

            //系统配置增加关账日期，如果当前日期小于关账日期，则为预算归属月的最后一天，如果当前日期大于等于关账日期，则取审批通过日期
            $posting_date = EnumsService::getInstance()->getSettingEnvValue('budget_withholding_sap_posting_date');
            if (empty($posting_date)) {
                throw new ValidationException('budget_withholding_sap_posting_date 配置为空, 请确认', ErrCode::$VALIDATE_ERROR);
            }

            //系统配置中增加暂估科目
            $budget_withholding_ledger_account = EnumsService::getInstance()->getSettingEnvValue('budget_withholding_ledger_account');
            if (empty($budget_withholding_ledger_account)) {
                throw new ValidationException('budget_withholding_ledger_account 配置为空, 请确认', ErrCode::$VALIDATE_ERROR);
            }

            //每个费用部门配置一个虚拟成本中心
            $budget_withholding_department_virtual_cost_center = EnumsService::getInstance()->getSettingEnvValueMap('budget_withholding_department_virtual_cost_center');
            if (empty($budget_withholding_department_virtual_cost_center)) {
                throw new ValidationException('budget_withholding_department_virtual_cost_center 配置为空, 请确认', ErrCode::$VALIDATE_ERROR);
            }

            $i             = 0;
            $condition     = ['status' => Enums::WF_STATE_APPROVED, 'cost_company_id' => $cost_company_id, 'sync_sap' => [0, 3]];
            $deal_sap_list = BudgetWithholdingService::getInstance()->getBudgetWithholdingSapList($i, $condition);
            $deal_sap_data = $deal_sap_list->toArray();
            while (!empty($deal_sap_data)) {
                //获取费用公司信息
                $cost_company_ids = array_values(array_unique(array_filter(array_column($deal_sap_data, 'cost_company_id'))));
                $department_list  = (new DepartmentRepository())->getDepartmentByIds($cost_company_ids, 2);

                //借方预算科目对应的核算科目
                $ledger_account_list = BudgetWithholdingService::getInstance()->getBorrowerLedgerAccount($deal_sap_data);

                foreach ($deal_sap_list as $main_model) {
                    $main_model = BudgetWithholdingService::getInstance()->sendToSap([
                        'budget_withholding_info'                           => $main_model,
                        'posting_date'                                      => $posting_date,
                        'budget_withholding_ledger_account'                 => $budget_withholding_ledger_account,
                        'budget_withholding_department_virtual_cost_center' => $budget_withholding_department_virtual_cost_center,
                        'department_list'                                   => $department_list,
                        'ledger_account_list'                               => $ledger_account_list,
                    ]);

                    //0未同步1已同步2同步失败3同步重试
                    if ($main_model->sync_sap == 1 || $main_model->sync_sap == 2) {
                        $main_model->updated_at = date('Y-m-d H:i:s');
                        $bool = $main_model->save();
                        $log .= '预提单：' . $main_model->no . ' 抛送总账接口 - 有（无）明细行- SAP结果 - ' . ($main_model->sync_sap == 1 ? '成功' : '失败');
                        $log .= '；变更预提单sync_sap状态值' . ($bool ? '成功' : '失败，请注意观察数据，无误需人工介入修改');
                    } else {
                        $log .= '预提单：' . $main_model->no . ' 多明细行分批抛未一次性抛完，请注意观察数据';
                    }
                    $log .= PHP_EOL;
                }
                sleep(1);
                $i             += 100;
                $deal_sap_list = BudgetWithholdingService::getInstance()->getBudgetWithholdingSapList($i, $condition);
                $deal_sap_data = $deal_sap_list->toArray();
            }
        } catch (Exception $e) {
            $is_exception = true;
            $log          .= '脚本执行有异常' . $e->getMessage() . $e->getTraceAsString() . PHP_EOL;
        }
        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        if ($is_exception) {
            $this->logger->warning('budget_withholding_sap-exception: ' . $log);
        } else {
            $this->logger->info('budget_withholding_sap : ' . $log);
        }
        $this->clearLock(__METHOD__);
        exit($log);
    }

    /**
     * 关闭预提单 - 红冲凭证 -对接SAP
     * 每10分钟跑一次
     * php app/cli.php budget budget_withholding_red_voucher_sap
     */
    public function budget_withholding_red_voucher_sapAction()
    {
        $this->checkLock(__METHOD__);
        $is_exception = false;
        $log          = date('Ymd H:i:s') . ' 脚本开始执行' . PHP_EOL;
        try {
            self::setLanguage('en');
            $cost_company_id = EnumsService::getInstance()->getSettingEnvValueIds('purchase_sap_company_ids');
            if (empty($cost_company_id)) {
                throw new ValidationException('purchase_sap_company_ids 配置为空, 请确认', ErrCode::$VALIDATE_ERROR);
            }

            //系统配置中增加暂估科目
            $budget_withholding_ledger_account = EnumsService::getInstance()->getSettingEnvValue('budget_withholding_ledger_account');
            if (empty($budget_withholding_ledger_account)) {
                throw new ValidationException('budget_withholding_ledger_account 配置为空, 请确认', ErrCode::$VALIDATE_ERROR);
            }

            //每个费用部门配置一个虚拟成本中心
            $budget_withholding_department_virtual_cost_center = EnumsService::getInstance()->getSettingEnvValueMap('budget_withholding_department_virtual_cost_center');
            if (empty($budget_withholding_department_virtual_cost_center)) {
                throw new ValidationException('budget_withholding_department_virtual_cost_center 配置为空, 请确认', ErrCode::$VALIDATE_ERROR);
            }

            $i             = 0;
            $condition     = ['status' => BudgetWithholdingEnums::STATUS_CLOSED, 'is_send_red_voucher' => BudgetWithholdingEnums::IS_SEND_RED_VOUCHER_YES, 'cost_company_id' => $cost_company_id, 'red_voucher_sync_sap' => [0, 3]];
            $deal_sap_list = BudgetWithholdingService::getInstance()->getBudgetWithholdingSapList($i, $condition);
            $deal_sap_data = $deal_sap_list->toArray();
            while (!empty($deal_sap_data)) {
                //获取费用公司信息
                $cost_company_ids = array_values(array_unique(array_filter(array_column($deal_sap_data, 'cost_company_id'))));
                $department_list  = (new DepartmentRepository())->getDepartmentByIds($cost_company_ids, 2);

                //借方预算科目对应的核算科目
                $ledger_account_list = BudgetWithholdingService::getInstance()->getBorrowerLedgerAccount($deal_sap_data);

                foreach ($deal_sap_list as $main_model) {
                    $main_model = BudgetWithholdingService::getInstance()->sendRedVoucherToSap([
                        'budget_withholding_info'                           => $main_model,
                        'budget_withholding_ledger_account'                 => $budget_withholding_ledger_account,
                        'budget_withholding_department_virtual_cost_center' => $budget_withholding_department_virtual_cost_center,
                        'department_list'                                   => $department_list,
                        'ledger_account_list'                               => $ledger_account_list,
                    ]);

                    //0未同步1已同步2同步失败3同步重试
                    if ($main_model->red_voucher_sync_sap == 1 || $main_model->red_voucher_sync_sap == 2) {
                        $main_model->updated_at = date('Y-m-d H:i:s');
                        $bool = $main_model->save();
                        $log .= '预提单：' . $main_model->no . ' 抛送红冲总账接口 - 有（无）明细行- SAP结果 - ' . ($main_model->red_voucher_sync_sap == 1 ? '成功' : '失败');
                        $log .= '；变更预提单red_voucher_sync_sap状态值' . ($bool ? '成功' : '失败，请注意观察数据，无误需人工介入修改');
                    } else {
                        $log .= '预提单：' . $main_model->no . ' 多明细行分批抛未一次性抛完，请注意观察数据';
                    }
                    $log .= PHP_EOL;
                }
                sleep(1);
                $i             += 100;
                $deal_sap_list = BudgetWithholdingService::getInstance()->getBudgetWithholdingSapList($i, $condition);
                $deal_sap_data = $deal_sap_list->toArray();
            }
        } catch (Exception $e) {
            $is_exception = true;
            $log          .= '脚本执行有异常' . $e->getMessage() . $e->getTraceAsString() . PHP_EOL;
        }
        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        if ($is_exception) {
            $this->logger->warning('budget_withholding_red_voucher_sap-exception: ' . $log);
        } else {
            $this->logger->info('budget_withholding_red_voucher_sap : ' . $log);
        }
        $this->clearLock(__METHOD__);
        exit($log);
    }
}
