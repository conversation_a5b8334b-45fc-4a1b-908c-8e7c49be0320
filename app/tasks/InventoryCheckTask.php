<?php

use App\Library\Enums;
use App\Library\Enums\StaffInfoEnums;
use App\Modules\Material\Models\MaterialInventoryCheckModel;
use App\Modules\Material\Services\InventoryCheckService;
use App\Library\ApiClient;

use App\Modules\Material\Models\MaterialWmsPlanModel;
use App\Modules\Material\Models\MaterialWmsPlantaskModel;
use App\Modules\Material\Models\MaterialWmsPlantaskInfoModel;
use App\Library\Enums\InventoryCheckEnums;
use App\Modules\Hc\Models\HrStaffInfoByModel;
use App\Modules\Organization\Models\SysDepartmentModel;
use App\Library\ErrCode;
use App\Modules\Material\Services\WmsPlanService;
use App\Repository\StoreRepository;
use App\Repository\HrStaffRepository;
use App\Models\oa\MaterialWmsPlanStoreModel;
use App\Models\oa\MaterialWmsPlanBarcodeModel;
use App\Library\Validation\ValidationException;

/**
 * 盘点单开始时汇总要盘点的员工范围并作入库、发消息、推PUSH操作
 * Class InventoryCheckTask
 */
class InventoryCheckTask extends BaseTask
{
    /**
     * 系统自动 - 下发盘点任务（启动盘点单）
     * 泰国：02:20:00/天、菲律宾/马来：05:40:00/天
     */
    public function mainAction()
    {
        // 进程加原子锁
        $this->checkLock(__METHOD__);
        $log = '任务名称: ' . __FUNCTION__ . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log .= '初始内存: ' . memory_usage() . PHP_EOL;
        try {
            //总计操作记录数
            $operate_count = 0;
            //先找到总数
            $count = InventoryCheckService::getInstance()->getUnStartInventoryCount();
            $log .= '本次脚本：' . date('Y-m-d H:i:s') . ' [共需执行盘点单' . $count . '条]' . PHP_EOL;
            if ($count > 0) {
                //有开始的盘点单，分页查询信息
                $page_size = 2000;
                $page = ceil($count / $page_size);
                for ($i = 1; $i <= $page; $i++) {
                    //分页获取开始的盘点单列表
                    $list_obj = InventoryCheckService::getInstance()->getUnStartInventoryList(['pageSize' => $page_size, 'offset' => $page_size * ($i - 1)]);
                    foreach ($list_obj as $inventory) {
                        $add_result = InventoryCheckService::getInstance()->startInventory(['id' => $inventory->id], ['id' => StaffInfoEnums::SUPER_ADMIN_STAFF_ID]);
                        if ($add_result['code'] == ErrCode::$SUCCESS) {
                            $inventory->status = InventoryCheckEnums::INVENTORY_CHECK_STATUS_ING;
                            $log .= '盘点单ID：【' . $inventory->id . '】启动成功；开始下发盘点任务' . PHP_EOL;
                            $operate_count += 1;
                            $result = InventoryCheckService::getInstance()->createStaffTask($inventory);
                            if ($result['code'] == ErrCode::$SUCCESS) {
                                $log .= '盘点单ID：【' . $inventory->id . '】给员工下发盘点任务成功' . PHP_EOL;
                            } else {
                                $log .= '盘点单ID：【' . $inventory->id . '】给员工下发盘点任务失败；原因可能是：' . $result['message'] . PHP_EOL;
                            }
                        } else {
                            $log .= '盘点单ID：【' . $inventory->id . '】启动失败；原因可能是：'. $add_result['message'] . PHP_EOL;
                        }
                        sleep(10);
                    }
                }
            }
            $log .= '操作结果：' . date('Y-m-d H:i:s') . ' [本次启动盘点单' . $operate_count . '个]' . PHP_EOL;
            $this->logger->info($log);
        } catch (Exception $e) {
            $log .= date('Ymd H:i:s') . ' 本次启动盘点单: 失败[异常]' . $e->getMessage() . PHP_EOL;
            $this->logger->warning($log);
        }
        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        // 释放进程锁
        $this->clearLock(__METHOD__);
        exit($log);
    }

    /**
     * 系统自动 - 下发启动、待发送盘点单的盘点任务
     * 每2小时执行一次
     */
    public function taskAction()
    {
        $this->checkLock(__METHOD__);
        $log = '任务名称: ' . __FUNCTION__ . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log .= '初始内存: ' . memory_usage() . PHP_EOL;
        $logger_type = 'info';
        try {
            //总计操作记录数
            $operate_count = 0;
            $inventory_obj = InventoryCheckService::getInstance()->getSendTaskInventoryList();
            $inventory_list = $inventory_obj->toArray();
            if (!empty($inventory_list)) {
                foreach ($inventory_obj as $inventory) {
                    $result = InventoryCheckService::getInstance()->createStaffTask($inventory);
                    if ($result['code'] == ErrCode::$SUCCESS) {
                        $operate_count += 1;
                        $log .= '盘点单ID：【' . $inventory->id . '】给员工下发盘点任务成功' . PHP_EOL;
                    } else {
                        $log .= '盘点单ID：【' . $inventory->id . '】给员工下发盘点任务失败；原因可能是：' . $result['message'] . PHP_EOL;
                    }
                    sleep(10);
                }
                $log .= '操作结果：' . date('Y-m-d H:i:s') . ' [本次成功下发任务的盘点单' . $operate_count . '个]' . PHP_EOL;
            } else {
                $log .= '操作结果：' . date('Y-m-d H:i:s') . ' [暂无需处理的盘点单（给员工下发盘点任务）数据]' . PHP_EOL;
            }
        } catch (\Exception $e) {
            $logger_type = 'error';
            $log .= '下发启动、待发送盘点单的盘点任务异常，原因是：' . $e->getMessage() . PHP_EOL;
        }

        $this->logger->$logger_type($log);

        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $this->clearLock(__METHOD__);
        exit($log);
    }

    /**
     * 旧资产～历史盘点单盘点范围数据刷新（一次性）
     */
    public function update_history_dataAction()
    {
        echo date('Ymd H:i:s').' 脚本开始执行'.PHP_EOL;
        try {
            //借款单审批状态=通过&&借款单支付状态=已支付&&is_new=0
            $list_obj = MaterialInventoryCheckModel::find([
                'conditions' => 'is_deleted = :is_deleted: and type in({type:array}) and is_new = :is_new:',
                'bind' => ['is_deleted' => Enums\GlobalEnums::IS_NO_DELETED, 'type' => [InventoryCheckEnums::INVENTORY_CHECK_TYPE_STAFF, InventoryCheckEnums::INVENTORY_CHECK_TYPE_JOB], 'is_new' => InventoryCheckEnums::IS_NEW_NO],
            ]);
            $list = $list_obj->toArray();
            echo '共查询到:' . count($list) . '条盘点单需要处理' . PHP_EOL;
            if (!empty($list)) {
                $i = 0;
                $now = date('Y-m-d H:i:s');
                foreach ($list_obj as $item) {
                    $this->logger->info('盘点单ID【' . $item->id . '】before数据：' . json_encode($item->toArray()));
                    echo '盘点单ID【' . $item->id . '】开始处理' . PHP_EOL;
                    $rel_list = $item->getCheckRel()->toArray();
                    if ($item->type == InventoryCheckEnums::INVENTORY_CHECK_TYPE_STAFF) {
                        $item->staff_ids = implode(',', array_column($rel_list, 'staff_id'));
                    } elseif ($item->type == InventoryCheckEnums::INVENTORY_CHECK_TYPE_JOB) {
                        $item->job_ids = implode(',', array_column($rel_list, 'job_id'));
                    }
                    $item->updated_at = $now;
                    $result = $item->save();
                    //借款单保存成功,组装归还单数据
                    if ($result === true) {
                        echo '盘点单ID【' . $item->id . '】处理完毕' . PHP_EOL;
                        $i ++;
                    } else {
                        echo '盘点单ID【' . $item->id . '】处理失败' . PHP_EOL;
                    }
                }
                echo '共处理：' . $i . '条盘点单' . PHP_EOL;
                echo '历史盘点单全部处理完毕'.PHP_EOL;
            } else {
                echo '暂无数据需要处理'.PHP_EOL;
                $this->logger->info('暂无数据需要处理');
            }
        } catch (Exception $e) {
            $this->logger->warning('inventory_update_history_data_task_exception:' . $e->getMessage());
            echo date('Ymd H:i:s') . ' 历史盘点单数据处理失败[异常]' . $e->getMessage() . PHP_EOL;
        }
        echo date('Ymd H:i:s'). ' 脚本执行结束'. PHP_EOL;
    }

    /**
     * 库存盘点 脚本任务
     * @Date: 2022-05-23 18:40
     * @return:
     **@author: peak pan
     * php  cli.php  inventory_check planwms
     */

    public function planwmsAction($time)
    {
        $this->checkLock(__METHOD__);

        $log = '';
        try {
            echo date('Ymd H:i:s') . ' 库存脚本开始执行' . PHP_EOL;
            //先找到总数
            $new_sta_time = empty($sta_time) ? date('Y-m-d 00:00:00', time()) : $time[0];
            $new_end_time = empty($end_time) ? date('Y-m-d 23:59:59', time()) : $time[1];
            $where        = ['sta_time' => $new_sta_time, 'end_time' => $new_end_time, 'is_task' => 0];
            $count        = $this->getStartPlanCheckCount($where);

            $n = 0;
            if ($count > 0) {
                //有开始的盘点单，分页查询信息
                $page_size = 2000;
                $page      = ceil($count / $page_size);
                echo date('Ymd H:i:s') . ' 本次脚本共需执行盘点单:' . $count . "条" . PHP_EOL;
                $log .= date('Ymd H:i:s') . '本次脚本共需执行盘点单:' . $count . "条" . PHP_EOL;
                for ($i = 1; $i <= $page; $i++) {
                    $where_page = array_merge($where, ['pageSize' => $page_size, 'offset' => $page_size * ($i - 1)]);
                    $list       = $this->getStartPlanCheckList($where_page);
                    if (empty($list->toArray())) {
                        break;
                    }
                    foreach ($list as $items) {
                        $nub = $this->operateData($items);
                        $n   += $nub;
                        sleep(1);
                    }
                }
            }

            echo date('Ymd H:i:s') . ' 脚本执行结束: [本次总生成任务数:' . $n . '个]' . PHP_EOL;
            $log .= date('Ymd H:i:s') . ' 脚本执行结束: [本次总生成任务数:' . $n . '个]' . PHP_EOL;
            $this->logger->info($log);

            //批量处理结束时间到了，改变计划和任务里面的状态
            $start_time = date('Y-m-d H:i:s', time() - 30 * 86400);
            $new_time   = date('Y-m-d H:i:s', time());
            echo date('Ymd H:i:s') . ' 开始处理结束时间到了，改变计划和任务里面的状态' . PHP_EOL;
            $db                  = $this->getDI()->get('db_oa');
            $update_success_plan = $db->updateAsDict(
                (new MaterialWmsPlanModel)->getSource(),
                [
                    'status'     => InventoryCheckEnums::PLAN_NOUN_STATUS_END,
                    'updated_at' => $new_time
                ],
                ['conditions' => "status in (1,2) and  sta_time > '$start_time' and  end_time < '$new_time'"]
            );

            if (!$update_success_plan) {
                echo date('Ymd H:i:s') . ' 盘点计划的盘点结束时间到了 强制吧状态变更我已完成' . PHP_EOL;
            }

            $update_success_task = $db->updateAsDict(
                (new MaterialWmsPlantaskModel)->getSource(),
                [
                    'status'     => InventoryCheckEnums::PLAN_NOUN_STATUS_END,
                    'updated_at' => $new_time
                ],
                ['conditions' => "status in (1,2) and  sta_time > '$start_time' and  end_time < '$new_time' "]
            );

            if (!$update_success_task) {
                echo date('Ymd H:i:s') . ' 盘点任务的盘点结束时间到了 强制吧状态变更我已完成' . PHP_EOL;
            }

            $update_success = $db->updateAsDict(
                (new MaterialWmsPlantaskInfoModel)->getSource(),
                [
                    'plan_status' => InventoryCheckEnums::PLAN_NOUN_STATUS_END,
                    'updated_at'  => $new_time
                ],
                ['conditions' => "plan_status in (1,2,3) and plan_sta_time > '$start_time' and  plan_end_time < '$new_time' "]
            );

            if (!$update_success) {
                echo date('Ymd H:i:s') . ' 盘点项目的结束时间到了 强制吧状态变更我已完成' . PHP_EOL;
            }

        } catch (Throwable $e) {
            echo date('Ymd H:i:s') . ' 计划生成任务: 失败[异常]' . $e->getMessage() . PHP_EOL;
            $log .= date('Ymd H:i:s') . ' 计划生成任务: 失败[异常]' . $e->getMessage() . PHP_EOL;
            $this->logger->warning($log);
        }
        $this->clearLock(__METHOD__);
        exit($log);
    }

    /**
     * 获取盘点计划数
     * @return mixed
     */
    private function getStartPlanCheckCount(array $condition = [])
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['main' => MaterialWmsPlanModel::class]);
        $builder = $this->getPlanCondition($builder, $condition);
        return $builder->getQuery()->execute()->count();
    }


    /**
     * 获取盘点任务记录数
     */
    private function getStartPlanTaskCheckCount(array $condition = [])
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['main' => MaterialWmsPlantaskModel::class]);
        $builder = $this->getPlanTaskCondition($builder, $condition);
        return $builder->getQuery()->execute()->count();
    }

    /**
     * 获取特定条件下的盘点单列表
     * @param array $condition 筛选条件组
     * @return mixed
     */
    private function getStartPlanCheckList(array $condition = [])
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['main' => MaterialWmsPlanModel::class]);
        $builder = $this->getPlanCondition($builder, $condition);
        $builder->limit($condition['pageSize'], $condition['offset']);
        return $builder->getQuery()->execute();
    }

    /**
     * 获取特定条件下的任务单列表
     * @param array $condition 筛选条件组
     * @return mixed
     */
    private function getStartPlanTaskList(array $condition = [])
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['main' => MaterialWmsPlantaskModel::class]);
        $builder = $this->getPlanTaskCondition($builder, $condition);
        $builder->limit($condition['pageSize'], $condition['offset']);
        return $builder->getQuery()->execute();
    }


    /**
     * 组装搜索条件
     * @return mixed
     */
    private function getPlanCondition($builder, $condition = [])
    {
        if (!empty($condition['sta_time']) && !empty($condition['end_time'])) {
            $builder->andWhere('main.sta_time >= :sta_time: and sta_time <= :end_time:', ['sta_time' => $condition['sta_time'], 'end_time' => $condition['end_time']]);
        }
        $builder->andWhere('main.is_deleted = :is_deleted:', ['is_deleted' => Enums\InventoryCheckEnums::IS_DELETED_NO]);
        $builder->andWhere('main.status = :status:', ['status' => Enums\InventoryCheckEnums::PLAN_NOUN_STATUS_NOT_STARTED]);
        $builder->andWhere('main.is_task = :is_task:', ['is_task' => Enums\InventoryCheckEnums::DEFAULT_VALUES]);
        return $builder;
    }

    /**
     * 计划事务处理
     * @return mixed
     */
    private function addStaffData(array $checkStaffList, object $MaterialWmsPlan)
    {
       $j=0;
       foreach ($checkStaffList as $item_nub){
           if($item_nub['plan_manager_id']>0 && $item_nub['status_job']==1){
               $j++;
           }
       }
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        $plan_check_ret = $MaterialWmsPlan->i_update([
            'store_nub' =>count($checkStaffList),
            'reality_store_nub' => $j,
            'is_task' => InventoryCheckEnums::DEFAULT_VALUES_END,
            'updated_at' => date('Y-m-d H:i:s'),

        ]);
        $this->logger->info(date('Ymd H:i:s') . ' 盘点计划ID:' . $MaterialWmsPlan->id . " 新增盘点任务为：" . json_encode($checkStaffList) . '成功' . PHP_EOL);
        if ($plan_check_ret) {
            $plan_task_model = new MaterialWmsPlantaskModel();
            $plan_add_task_result = $plan_task_model->batch_insert($checkStaffList);
            if ($plan_add_task_result) {
                //更新成功&&任务创建成功,事务提交
                $db->commit();
            } else {
                //事务回滚
                $db->rollback();
                $msgArr = [];
                $messages = $plan_task_model->getMessages();
                foreach ($messages as $message) {
                    $msgArr[] = $message->getMessage();
                }
                throw new Exception('物料盘点任务入库失败 = ' . json_encode(['inventory_check_staff_data' => $checkStaffList, 'message' => $msgArr], JSON_UNESCAPED_UNICODE), ErrCode::$INVENTORY_CHECK_CREATE_ERROR);
            }
        } else {
            $db->rollback();
            $msgArr = [];
            $messages = $MaterialWmsPlan->getMessages();
            foreach ($messages as $message) {
                $msgArr[] = $message->getMessage();
            }
            throw new Exception('物料盘点-盘点任务生成失败 = ' . json_encode(['plan_data' => $checkStaffList, 'message' => $msgArr], JSON_UNESCAPED_UNICODE), ErrCode::$INVENTORY_CHECK_CREATE_ERROR);
        }
    }

    /**
     * 计划批量处理
     * @param object $data 计划对象
     * @return mixed
     * @throws Exception
     */
    private function operateData(object $data)
    {
        $planCount = [];

        if ($data->plan_scope == InventoryCheckEnums::PLAN_STORE_TYPE) {
            /**
             * 如果盘点计划为按照网点类型盘点，在下发盘点任务筛选网点逻辑调整为：
             * 1. 按照选择的网点类型获取网点信息表中网点类型属于选择的网点类型的网点
             * 2. 并且网点的激活状态为激活
             * 3. 并且网点不属于虚拟网点
             * 4. 并且网点不属于不下发包材盘点任务网点
             * 5. 并且网点类型不是FH、SHOP、USHOP时，网点营业状态为营业，use_state=1
             */
            $search_store_data = WmsPlanService::getInstance()->searchStore(['category' => json_decode($data->store_type,true), 'store_ids' => ($data->store_ids ? json_decode($data->store_ids,true) : [])], false, true);
            $search_store_list = $search_store_data['data'] ?? [];
            foreach ($search_store_list as $store) {
                $planCount[] = [
                    'store_id' => $store['id'],
                    'store_name' => $store['name'],
                    'manager_id' => $store['manager_id'],
                    'manager_name' => '',
                    'category' => $store['category'],
                ];
                $arr_manager_id[] = $store['manager_id'];
            }
        } else if ($data->plan_scope == InventoryCheckEnums::PLAN_SCOPE_DEPARTMENT) {
            //指定部门   一般是总部 单个
            $planCount = SysDepartmentModel::find(
                [
                    'conditions' => 'id = :department_ids:',
                    'bind' => ['department_ids' => $data->department_ids],
                    "columns" => "id as department_id,name as department_name",
                ]
            )->toArray();
            $arr_manager_id = [$data->plan_manager_id];

        } else if ($data->plan_scope == InventoryCheckEnums::PLAN_SCOPE_SITE) {
            //1. 如果盘点计划为指定网点，则创建盘点任务时获取从盘点网点列表中获取需要盘点的网点，每一个网点创建一个盘点任务，并且盘点责任人获取盘点网点列表中的对应的盘点责任人
            $store_list = $data->getStore()->toArray();
            $search_store_list = (new StoreRepository())->getStoreListByIds(array_column($store_list, 'sys_store_id'), Enums::WF_LOAN_TYPE);
            foreach ($store_list as $store) {
                $planCount[] = [
                    'store_id' => $store['sys_store_id'],
                    'store_name' => $store['sys_store_name'],
                    'manager_id' => $store['manager_id'],
                    'manager_name' => $store['manager_name'],
                    'category' => !empty($search_store_list[$store['sys_store_id']]) ? $search_store_list[$store['sys_store_id']]['category'] : '',
                ];
                $arr_manager_id[] = $store['manager_id'];
            }

        }

        if (empty($planCount)) {
            echo  date('Ymd H:i:s') . ' 盘点计划ID:' . $data->id . " 新增盘点任务为数据为：" . json_encode($data) . ' ，但没有符合的数据' . json_encode($planCount) . PHP_EOL;
            $this->logger->info(date('Ymd H:i:s') . ' 盘点计划ID:' . $data->id . " 新增盘点任务为数据为：" . json_encode($data) . ' ，但没有符合的数据' . json_encode($planCount) . PHP_EOL);
        }

        $task_code = InventoryCheckEnums::PLAN_CHECK_SERIAL_NUMBER_PREFIX . date('YmdHis') . rand(1000, 9999);

        $director_emails = [];
        if (!empty($arr_manager_id)) {
            $director_Staff = HrStaffInfoByModel::find([
                'conditions' => 'staff_info_id in({ids:array})',
                'bind' => [
                    'ids' => $arr_manager_id
                ],
                "columns" => "staff_info_id,state,name",
            ]);
            if (!empty($director_Staff)) {
                $director_emails = $director_Staff->toArray();
            }
        }

        echo  date('Ymd H:i:s') . ' 盘点计划ID:' . $data->id . " 新增盘点任务网点负责人：" . json_encode($data) . ' ，盘点执行人是' . json_encode($arr_manager_id) . PHP_EOL;

        $staffInfo_job = array_filter(array_column($director_emails,'state','staff_info_id'));

        $staffInfo_name_arr = array_filter(array_column($director_emails,'name','staff_info_id'));


        echo  date('Ymd H:i:s') . ' 盘点计划ID:' . $data->id . ' ，查询盘点负责人的在职状态' . json_encode($staffInfo_job) . PHP_EOL;

        $data->status_job =1;
        $check_staff_list = [];
        foreach ($planCount as $pland) {
            if ($data->plan_scope == InventoryCheckEnums::PLAN_SCOPE_DEPARTMENT) {
                //指定部门-盘点负责人为盘点计划填写的盘点负责人
                $plan_manager_id = $data->plan_manager_id;
            } else {
                //其它-盘点负责人为网点上或者指定的网点负责人
                $plan_manager_id = $pland['manager_id'];
            }
            $data->status_job = empty($staffInfo_job[$plan_manager_id]) ? 0 : $staffInfo_job[$plan_manager_id];
            $check_staff = [
                'plan_id' =>$data->id,
                'plan_code' => $data->plan_code,
                'task_code' => $task_code,
                'plan_name' => $data->plan_name ?? 0,
                'plan_noun' => $data->plan_noun ?? 1,
                'plan_nub' => 1,
                'sta_month' => $data->sta_month ?? '',
                'end_month' => $data->end_month ?? '',
                'sta_time' => $data->sta_time ?? '',
                'end_time' => $data->end_time ?? '',
                'plan_scope' => $data->plan_scope ?? '',
                'store_type' => $pland['category'] ?? '',
                'store_id' => $pland['store_id'] ?? '',
                'store_name' => $pland['store_name'] ?? '',
                'department_id' => $pland['department_id'] ?? '',
                'department_name' => $pland['department_name'] ?? '',
                'plan_manager_id' => $plan_manager_id,
                'plan_manager_name' => !empty($staffInfo_name_arr[$plan_manager_id]) ? $staffInfo_name_arr[$plan_manager_id] : '-',
                'status_job' => $data->status_job,
                'is_photo' => $data->is_photo,
                'is_photo_camera' => $data->is_photo_camera,
                'is_relay' => $data->is_relay,
                'plan_remark' => $data->plan_remark ?? '',
                'status' => 1,
                'is_deleted' => 0,
                'staff_info_id' => $data->staff_info_id,
                'staff_info_name' => $data->staff_info_name,
                'up_staff_info_id' => 0,
                'is_task' => 0,
                'send_time' => date('Y-m-d H:i:s'),//消息发送时间
                'created_at' => date('Y-m-d H:i:s'),//创建时间
                'updated_at' => date('Y-m-d H:i:s')//更新时间
            ];
            $check_staff_list[] = $check_staff;
        }

        if ($check_staff_list) {
            $this->addStaffData($check_staff_list, $data);
        }

        return count($check_staff_list);
    }


    /**
     * 组装任务搜索条件
     * @return mixed
     */
    private function getPlanTaskCondition($builder, $condition = [])
    {
        if (!empty($condition['sta_time']) && !empty($condition['end_time'])) {
            $builder->andWhere('main.sta_time >= :sta_time: and sta_time <= :end_time:', ['sta_time' => $condition['sta_time'], 'end_time' => $condition['end_time']]);
        }
        $builder->andWhere('main.is_deleted = :is_deleted: and  main.plan_manager_id > :plan_manager_id: and status_job=:status_job: ', ['is_deleted' => InventoryCheckEnums::IS_DELETED_NO,'plan_manager_id' => InventoryCheckEnums::IS_DELETED_NO,'status_job'=>InventoryCheckEnums::STAFF_STATE_IN]);
        $builder->andWhere('main.status = :status:', ['status' => Enums\InventoryCheckEnums::PLAN_NOUN_STATUS_NOT_STARTED]);
        $builder->andWhere('main.is_task = :is_task:', ['is_task' => InventoryCheckEnums::DEFAULT_VALUES]);
        return $builder;
    }

    /**
     * 任务事务处理
     * @return mixed
     */
    private function addTaskStaffData(array $checkStaffList, object $MaterialWmsPlanTask)
    {
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        $plan_check_ret = $MaterialWmsPlanTask->i_update([
            //'status' =>InventoryCheckEnums::PLAN_NOUN_STATUS_ING,
            'is_task' => InventoryCheckEnums::DEFAULT_VALUES_END,
            'send_time' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),

        ]);
        $this->logger->info(date('Ymd H:i:s') . ' 盘点计划ID:' . $MaterialWmsPlanTask->id . " 新增盘点任务为：" . json_encode($checkStaffList) . '成功' . PHP_EOL);
        if ($plan_check_ret) {
            $plan_task_info_model = new MaterialWmsPlantaskInfoModel();
            $plan_add_task_info_result = $plan_task_info_model->batch_insert($checkStaffList);
            if ($plan_add_task_info_result) {
                //更新成功&&任务创建成功,事务提交
                $db->commit();
                //开始发送站内信
                echo  '开始发送消息,接收人是'.$MaterialWmsPlanTask->plan_manager_id.PHP_EOL;
                $message_title = $this->messagePlanTitle();
                $staff_users_msg[] = ['id' => $MaterialWmsPlanTask->plan_manager_id];
                $postUserInfo = $staff_users_msg;
                $post_content = [
                'message_title'=>$message_title,
                'message_content'=>$MaterialWmsPlanTask->id,
                'top'=>1,
                'category'=>60
                ];
                $msgdata = $this->sendMsg($postUserInfo, $post_content);

                echo  '发送结果是'.$msgdata.PHP_EOL;

                $plan_check_msg = $MaterialWmsPlanTask->i_update([
                    'msg_id' =>$msgdata,
                    'updated_at' => date('Y-m-d H:i:s'),
                ]);
                if (!$plan_check_msg) {
                    $this->logger->info(date('Ymd H:i:s') . ' 盘点计划ID:' . $MaterialWmsPlanTask->id . " 消息记录id：" . json_encode($msgdata) . '失败' . PHP_EOL);
                }
            } else {
                //事务回滚
                $db->rollback();
                $msgArr = [];
                $messages = $plan_task_info_model->getMessages();
                foreach ($messages as $message) {
                    $msgArr[] = $message->getMessage();
                }
                throw new Exception('物料盘点任务盘点入库失败 = ' . json_encode(['inventory_check_staff_data' => $checkStaffList, 'message' => $msgArr], JSON_UNESCAPED_UNICODE), ErrCode::$INVENTORY_CHECK_CREATE_ERROR);
            }
        } else {
            $db->rollback();
            $msgArr = [];
            $messages = $MaterialWmsPlanTask->getMessages();
            foreach ($messages as $message) {
                $msgArr[] = $message->getMessage();
            }
            throw new Exception('物料盘点-盘点任务生成盘点失败 = ' . json_encode(['plan_data' => $checkStaffList, 'message' => $msgArr], JSON_UNESCAPED_UNICODE), ErrCode::$INVENTORY_CHECK_CREATE_ERROR);
        }
    }

    /**
     * 任务批量处理
     * @param object $data 计划任务对象
     * @return mixed
     * @throws Exception
     */
    private function operateTaskData(object $data)
    {
        $skulist = $plan_task_list = [];
        //1. 创建盘点任务时，每个盘点任务盘点的包材范围调整为当前盘点计划中选中的所有包材
        $plan_info = WmsPlanService::getInstance()->getPlanInfoByOne($data->plan_id);
        if ($plan_info) {
            $skulist = WmsPlanService::getInstance()->getPlanBarcodeList($plan_info);
        }
        foreach ($skulist as $plansku) {
            $check_staff = [
                'plan_id' => $data->plan_id,
                'plan_task_id' => $data->id,
                'is_relay' => $data->is_relay,
                'plan_name' => $data->plan_name,
                'store_id' => $data->store_id,
                'store_name' => $data->store_name,
                'department_id' => $data->department_id,
                'department_name' => $data->department_name,
                'plan_sta_time' => $data->sta_time,
                'plan_end_time' => $data->end_time,
                'reality_sta_time' => null,
                'reality_end_time' => null,
                'plan_manager_id' => $data->plan_manager_id,
                'plan_relay_manager_id' => $data->plan_relay_manager_id??0,
                'status_job' => $data->status_job,
                'package_sku_id' => $plansku['id'],
                'barcode' => $plansku['barcode'],
                'category' => $plansku['category'],
                'goods_name_zh' => $plansku['goods_name_zh'],
                'goods_name_th' => $plansku['goods_name_th'],
                'goods_name_en' => $plansku['goods_name_en'],
                'specs_model' => $plansku['specs_model'],
                'unit' => $plansku['unit'],
                'image_path' => $plansku['image_path'],
                'plan_image_path' => '',
                'plan_nub' => null,
                'is_deleted' => 0,
                'plan_sku_end_time'=>null,
                'plan_status' => 1,
                'send_time'=>date('Y-m-d H:i:s'),
                'created_at' => date('Y-m-d H:i:s'),//创建时间
                'updated_at' => date('Y-m-d H:i:s')//更新时间
            ];
            $plan_task_list[] = $check_staff;
        }
        if ($plan_task_list) {
            $this->addTaskStaffData($plan_task_list, $data);
        }
        return count($skulist);

    }


    /**
     * 库存盘点脚本任务到对应的人 任务指派脚本
     * @Date: 2022-05-23 18:40
     * @return:
     **@author: peak pan
     * php  cli.php  inventory_check planwmsTask
     */

    public function planwmsTaskAction($time)
    {
        $this->checkLock(__METHOD__);
        $log = '';
        try {
            echo date('Ymd H:i:s') . ' 库存脚本任务开始执行'  . PHP_EOL;
            //先找到总数
            $new_sta_time = empty($sta_time) ? date('Y-m-d 00:00:00', time()) : $time[0];
            $new_end_time = empty($end_time) ? date('Y-m-d 23:59:59', time()) : $time[1];
            $where =['sta_time' => $new_sta_time, 'end_time' => $new_end_time, 'is_task' => 0];
            $count = $this->getStartPlanTaskCheckCount($where);
            $n = 0;
            if ($count > 0) {
                //有开始的盘点任务单，分页查询信息
                $page_size = 2000;
                $page = ceil($count / $page_size);
                echo date('Ymd H:i:s') . ' 本次脚本共需执行盘点任务单:' . $count . "条" . PHP_EOL;
                $log .= date('Ymd H:i:s') . '本次脚本共需执行盘点任务单:'  . $count . "条" . PHP_EOL;
                for ($i = 1; $i <= $page; $i++) {
                    $where_page = array_merge($where,['pageSize' => $page_size, 'offset' => $page_size * ($i - 1)]);
                    $list = $this->getStartPlanTaskList($where_page);
                    if (empty($list->toArray())) {
                        break;
                    }
                    foreach ($list as $items) {
                        $nub = $this->operateTaskData($items);
                        $n += $nub;
                        //sleep(2);
                    }
                }
            }
            echo date('Ymd H:i:s') . ' 脚本执行结束: [本次总生成盘点数:' . $n . '个]' . PHP_EOL;
            $log .= date('Ymd H:i:s') . ' 脚本执行结束: [本次总生成盘点数:' . $n . '个]' . PHP_EOL;
            $this->logger->info($log);
        } catch (Throwable $e) {
            echo date('Ymd H:i:s') . ' 计划生成任务: 失败[异常]' . $e->getMessage() . PHP_EOL;
            $log .= date('Ymd H:i:s') . ' 计划生成任务: 失败[异常]' . $e->getMessage() . PHP_EOL;
            $this->logger->warning($log);
        }
        $this->clearLock(__METHOD__);
        exit($log);
    }



    /**
     * 发送站内信 统一方法
     * @param  $postUserInfo [['id'=>12345],['id'=>233333]]
     * @param  $post_content
     *        [
     *          'message_title'=>'发送的标题',
     *          'message_content'=>'内容',
     *          'top'=>'',
     *          'category'=>''
     *        ]
     */
    private function sendMsg(array $postUserInfo, array $post_content)
    {
        //盘点任务id，发送范围，需发送员工数，发送结果，发送时间
        $staff_ids = implode(",", array_column($postUserInfo, "id"));
        $this->logger->info("发送站内信 DATA:" . json_encode(['plan_task_id' => $post_content['message_content'], 'staff_ids' => $staff_ids, 'date' => date('Y-m-d H:i:s')]));
        try {
            $kit_param = [];
            $kit_param['staff_users'] = $postUserInfo;
            $kit_param['message_title'] = $post_content['message_title'];
            $kit_param['message_content'] = $post_content['message_content'];
            $kit_param['top'] = $post_content['top'];
            $kit_param['category'] = $post_content['category'];
            echo '发送内容为'.json_encode($kit_param).PHP_EOL;
            $bi_rpc = (new ApiClient('bi_svc', '', 'add_kit_message'));
            $bi_rpc->setParams([$kit_param]);
            $res = $bi_rpc->execute();
            echo '发送消息返回的数据'.json_encode($res).PHP_EOL;
            $bi_return = $res && isset($res['result']) && $res['result']['code'] == 1;
            $res_msg = $bi_return ? '成功' : '失败';
            $res_log = '给员工工号组[' . $staff_ids . ']发送盘点单ID[' .$post_content['message_title'].'--'.$post_content['message_content'] . ']站内信消息，发送时间[' . date('Y-m-d H:i:s') . ']，发送结果[' . $res_msg . ']';
            $this->logger->info($res_log);
            if($bi_return){
                $msg_id =$res['result']['data'];
                return  $msg_id[0];
            }else{
                return  '0';
            }
        } catch (\Exception $e) {
            $this->logger->warning("给员工发送盘点站内信消息，失败[异常]" . $e->getMessage() . PHP_EOL);
        }
    }



    /**
     * 各个国家站内信标题
     * */
    public function messagePlanTitle()
    {
        $country_code = get_country_code();
        $title = '';
        switch ($country_code) {
            case 'PH':
            case  'MY':
                $title = 'Consumable Inventory Notice库存盘点通知';
                break;
            case  'TH':
                $title = 'แจ้งเตือนการนับสินค้าคงคลัง库存盘点通知';
                break;
            case  'LA':
                $title = 'ແຈ້ງການສິນຄ້າໃນສາງ库存盘点通知';
                break;

            default:
                $title = 'Consumable Inventory Notice库存盘点通知';
        }
        return $title;

    }

    /**
     * 指定网点的数据进行处理-一次性脚本
     */
    public function handle_plan_storeAction()
    {
        $log = date('Y-m-d H:i:s') . ' 脚本开始执行' . PHP_EOL;
        try {
            $plan_list = MaterialWmsPlanModel::find([
                'columns' => 'id, store_ids, plan_manager_id',
                'conditions' => 'plan_scope = :plan_scope:',
                'bind' => ['plan_scope' => InventoryCheckEnums::PLAN_SCOPE_SITE]
            ])->toArray();
            $plan_manager_ids = array_values(array_unique(array_unique(array_filter(array_column($plan_list, 'plan_manager_id')))));
            $plan_manager_list = (new HrStaffRepository())->getStaffListByStaffIds($plan_manager_ids);
            $store_repository = new StoreRepository();
            $now = date('Y-m-d H:i:s');
            if (empty($plan_list)) {
                $log .= '无历史数据需要处理' . PHP_EOL;
            }
            foreach ($plan_list as $plan) {
                //先查询下是否已处理过历史数据
                $count = MaterialWmsPlanStoreModel::count([
                    'conditions' => 'plan_id = :plan_id:',
                    'bind' => ['plan_id' => $plan['id']]
                ]);
                if ($count) {
                    $log .=  '计划ID ：' . $plan['id'] . '，历史数据已处理过，请勿重复处理' . PHP_EOL;
                    continue;
                }

                //遍历网点
                $store_ids = json_decode($plan['store_ids'], true);
                $store_list = $store_repository->getStoreListByIds($store_ids, Enums::STORE_STATE_ALL);
                $store_data = [];
                foreach ($store_ids as $sys_store_id) {
                    $store_data[] = [
                        'plan_id' => $plan['id'],
                        'sys_store_id' => $sys_store_id,
                        'sys_store_name' => !empty($store_list[$sys_store_id]) ? $store_list[$sys_store_id]['name'] : '',
                        'manager_id' => $plan['plan_manager_id'] ?? 0,
                        'manager_name' => !empty($plan_manager_list[$plan['plan_manager_id']]) ? $plan_manager_list[$plan['plan_manager_id']]['name'] : '',
                        'created_at' => $now,
                        'updated_at' => $now
                    ];
                }

                //处理历史数据
                if ($store_data) {
                    $material_wms_plan_store = new MaterialWmsPlanStoreModel();
                    $bool = $material_wms_plan_store->batch_insert($store_data);
                    if ($bool === false) {
                        $log .= '计划ID ：' . $plan['id'] . '，写入盘点网点关系表数据失败，可能的原因是：' . get_data_object_error_msg($material_wms_plan_store) . PHP_EOL;
                    } else {
                        $log .= '计划ID ：' . $plan['id'] . '，写入盘点网点关系表数据成功' . PHP_EOL;
                    }
                }
            }
        } catch (Exception $e) {
            $this->logger->warning('inventory_handle_plan_store_task_exception:' . $log . $e->getMessage());
        }
        $log .= date('Y-m-d H:i:s') . ' 脚本执行结束'. PHP_EOL;
        $this->logger->info('inventory_handle_plan_store_task:' . $log);
        exit($log);
    }

    /**
     * 盘点包材数据进行处理-一次性脚本
     */
    public function handle_plan_barcodeAction()
    {
        $log = date('Y-m-d H:i:s') . ' 脚本开始执行' . PHP_EOL;
        try {
            $plan_task_list = MaterialWmsPlantaskModel::find([
                'columns' => 'MIN(id) as task_id, plan_id',
                'group' => 'plan_id'
            ])->toArray();
            if (empty($plan_task_list)) {
                $log .= '无历史数据需要处理' . PHP_EOL;
            }
            $now = date('Y-m-d H:i:s');
            foreach ($plan_task_list as $task) {
                //先查询下是否已处理过历史数据
                $count = MaterialWmsPlanBarcodeModel::count([
                    'conditions' => 'plan_id = :plan_id:',
                    'bind' => ['plan_id' => $task['plan_id']]
                ]);
                if ($count) {
                    $log .=  '计划ID ：' . $task['plan_id'] . '，历史数据已处理过，请勿重复处理' . PHP_EOL;
                    continue;
                }

                //遍历barcode
                $barcode_data = [];
                $barcode_list = MaterialWmsPlantaskInfoModel::find([
                    'columns' => 'barcode',
                    'conditions' => 'plan_id = :plan_id: and plan_task_id = :plan_task_id:',
                    'bind' => ['plan_id' => $task['plan_id'], 'plan_task_id' => $task['task_id']]
                ])->toArray();
                foreach ($barcode_list as $item) {
                    $barcode_data[] = [
                        'plan_id' => $task['plan_id'],
                        'barcode' => $item['barcode'],
                        'created_at' => $now,
                        'updated_at' => $now
                    ];
                }

                //处理历史数据
                if ($barcode_data) {
                    $material_wms_plan_barcode = new MaterialWmsPlanBarcodeModel();
                    $bool = $material_wms_plan_barcode->batch_insert($barcode_data);
                    if ($bool === false) {
                        $log .= '计划ID ：' . $task['plan_id'] . '，写入盘点包材关系表数据失败，可能的原因是：' . get_data_object_error_msg($material_wms_plan_barcode) . PHP_EOL;
                    } else {
                        $log .= '计划ID ：' . $task['plan_id'] . '，写入盘点包材关系表数据成功' . PHP_EOL;
                    }
                }
            }
        } catch (Exception $e) {
            $this->logger->warning('inventory_handle_plan_barcode_task_exception:' . $log . $e->getMessage());
        }
        $log .= date('Y-m-d H:i:s') . ' 脚本执行结束'. PHP_EOL;
        $this->logger->info('inventory_handle_plan_barcode_task:' . $log);
        exit($log);
    }

    /**
     * 网点包材数据统计
     * @param $param
     * php app/cli.php inventory_check package_stock
     */
    public function package_stockAction($param)
    {
        // 步骤1：变更耗材出库单-箱单号、运单号-妥投状态、妥投日期
        $this->console->handle(
            [
                'task'   => 'material_wms',
                'action' => 'wms_out_storage_box'
            ]
        );

        sleep(5);

        // 步骤2：网点包材数据统计-可指定统计日期 、分批入库条数
        $this->console->handle(
            [
                'task'   => 'inventory_check',
                'action' => 'package_stock_statistical',
                'params' => $param,
            ]
        );
    }

    /**
     * 网点包材数据统计
     * @param $param
     */
    public function package_stock_statisticalAction($param)
    {
        $log = '任务名称: ' . __FUNCTION__ . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log .= '初始内存: ' . memory_usage() . PHP_EOL;
        $logger_type = 'info';
        try {
            //指定日期，若未指定则为前一天日期(脚本0点01分执行)
            $date = $param[0] ?? date('Y-m-d', strtotime('-1 day'));
            $batch_number = $param[1] ?? 5000;
            $log .= WmsPlanService::getInstance()->packageStock($date, $batch_number);
        } catch (ValidationException $e) {
            $logger_type = 'warning';
            $log .= '网点包材数据统计任务异常，原因是：' . $e->getMessage() . PHP_EOL;
        } catch (\Exception $e) {
            $logger_type = 'error';
            $log .= '网点包材数据统计任务异常，原因是：' . $e->getMessage() . PHP_EOL;
        }

        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        $this->logger->$logger_type($log);
        echo $log;
    }

    /**
     * 网点包材数据统计-归档
     * @param $param
     * php app/cli.php inventory_check package_stock_archive
     */
    public function package_stock_archiveAction($param)
    {
        $log = '任务名称: ' . __FUNCTION__ . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log .= '初始内存: ' . memory_usage() . PHP_EOL;
        $logger_type = 'info';
        try {
            //归档多少天之前的数据，不指定默认30天前
            $day = $param[0] ?? 30;
            $batch_number = $param[1] ?? 5000;
            $log .= WmsPlanService::getInstance()->packageStockArchive($day, $batch_number);
        } catch (\Exception $e) {
            $logger_type = 'error';
            $log .= '网点包材数据统计-归档任务异常，原因是：' . $e->getMessage() . PHP_EOL;
        }


        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $this->logger->$logger_type($log);
        exit($log);
    }
}
