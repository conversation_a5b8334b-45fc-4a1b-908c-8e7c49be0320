<?php
/**
 * 审批流相关的处理任务
 */

use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Models\oa\PaperDocumentModel;
use App\Models\oa\WorkflowModel;
use App\Models\oa\WorkflowRequestNodeAt;
use App\Models\oa\WorkflowRequestNodeAuditorModel;
use App\Models\oa\WorkflowRequestNodeFyrMiddleModel;
use App\Models\oa\SysModuleModel;
use App\Modules\User\Models\HrStaffInfoModel;
use App\Modules\Workflow\Models\WorkflowRequestNodeFYR;
use App\Modules\Workflow\Services\FYRService;
use App\Modules\Workflow\Models\WorkflowRequestModel;
use App\Modules\Workflow\Services\WorkflowMailService;
use App\Modules\Workflow\Services\WorkflowSmsService;
use App\Modules\Common\Services\EnumsService;
use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\SysConfigEnums;
use App\Library\Enums\OAWorkflowEnums;
use App\Modules\WorkflowManagement\Services\WorkflowCheckerService;

class WorkflowTask extends BaseTask
{
    /**
     * 初始化基本数据
     *
     * 说明: 已弃用(适用于 v13199 需求，在 v18276 中废弃)
     *
     * node_related_auditor_ids: 征询人所在节点的相关审批人
     * node_auditor_ids: 征询人直属节点审批人
     * node_node_audit_type: 征询人一级节点审批类型
     * staff_state: 征询人在职状态
     *
     * 新环境时, 执行一次即可
     * php app/cli.php workflow init_fyr_base_data
     */
    public function init_fyr_base_dataAction()
    {
        $log = '';
        $log .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        try {
            // 分批处理: 每次 1000 条, 每批次歇息 10 秒钟
            // 先全部取出
            $length     = 1000;
            $sleep_secs = 10;

            // 1. 取出指定日期范围的征询类型的数据
            $fyr_models = WorkflowRequestNodeFYR::find([
                'conditions' => 'action_type = :action_type:',
                'bind'       => ['action_type' => GlobalEnums::CONSULTED_ACTION_TYPE],
            ]);

            $log .= '待处理的数据条数: ' . count($fyr_models) . PHP_EOL;

            $staff_ids = array_values(array_unique(array_filter(array_column($fyr_models->toArray(), 'staff_id'))));

            $log .= '涉及征询人: ' . count($staff_ids) . PHP_EOL;

            // 2. 获取征询人在职状态
            $staff_list = [];
            if ($staff_ids) {
                $staff_list = HrStaffInfoModel::find([
                    'conditions' => 'staff_info_id IN ({ids:array})',
                    'bind'       => ['ids' => $staff_ids],
                    'columns'    => ['state', 'wait_leave_state', 'staff_info_id'],
                ])->toArray();
                $staff_list = array_column($staff_list, null, 'staff_info_id');
            }

            $log .= '征询人在HR系统的个数: ' . count($staff_list) . PHP_EOL;

            // 3. 获取征询人所在节点相关审批人
            $FYRService    = new FYRService();
            $request_model = new WorkflowRequestModel();

            $success_save                      = 0;
            $fail_save                         = 0;
            $fail_fyr_log                      = '';
            $sub_node_sign_record_fail_save    = 0;
            $sub_node_sign_record_fail_fyr_log = '';
            $no_pending_state_total            = 0;
            foreach ($fyr_models as $key => $model) {
                if ($key && ($key % $length == 0)) {
                    sleep($sleep_secs);
                }

                // 当前征询节点关联的审批流
                $request = $request_model->findFirst($model->request_id);

                // 待审批的节点: 实时取相关审批人 和 员工状态信息(已审过的节点, 不再处理)
                if ($request->state == Enums::WF_STATE_PENDING && $request->current_flow_node_id == $model->flow_node_id) {
                    // 征询人信息
                    // 如果未获取到征询人员工信息, 则默认在职状态
                    $user_info   = $staff_list[$model->staff_id] ?? [];
                    $staff_state = !empty($user_info) ? $FYRService->getStaffJobState($user_info) : GlobalEnums::CONSULTED_STAFF_JOB_STATE_ON;

                    // 当前征询节点的相关审批人: 初始化的实时取; 日常部分字段同步的, 取已固化的
                    $current_node_info        = $FYRService->getConsultantNodeAuditorInfo($request, $model->staff_id);
                    $node_related_auditor_ids = $current_node_info['node_related_auditor_ids'] ?? '';

                    // 初始化: 工号会签/子节点会签
                    if (in_array($current_node_info['node_audit_type'], [
                        Enums::WF_NODE_AUDIT_TYPE_AUDITOR_COUNTERSIGN,
                        Enums::WF_NODE_AUDIT_TYPE_SUB_NODE_COUNTERSIGN,
                    ])) {
                        // 当前征询节点的征询记录
                        $exist_fyr_records = WorkflowRequestNodeFYR::find([
                            'conditions' => 'request_id = :request_id: AND flow_id = :flow_id: AND flow_node_id = :flow_node_id: AND action_type = :action_type:',
                            'bind'       => [
                                'request_id'   => $request->id,
                                'flow_id'      => $request->flow_id,
                                'flow_node_id' => $request->current_flow_node_id,
                                'action_type'  => GlobalEnums::CONSULTED_ACTION_TYPE,
                            ],
                        ]);

                        // 组合多个子节点同期会签时 的 相关审批人
                        $exist_fyr_records_list   = $exist_fyr_records->toArray();
                        $exist_fyr_records_list   = !empty($exist_fyr_records_list) ? array_merge(array_column($exist_fyr_records_list,
                            'node_related_auditor_ids'), [$node_related_auditor_ids]) : [$node_related_auditor_ids];
                        $node_related_auditor_ids = explode(',', implode(',', $exist_fyr_records_list));
                        $node_related_auditor_ids = implode(',', array_filter(array_unique($node_related_auditor_ids)));

                        // 空对象不会遍历
                        foreach ($exist_fyr_records as $record_model) {
                            $record_model->node_related_auditor_ids = $node_related_auditor_ids;
                            if ($record_model->save() === false) {
                                $sub_node_sign_record_fail_save++;
                                $sub_node_sign_record_fail_fyr_log .= "record_fyr_id: {$record_model->id}, staff_id: {$model->staff_id}, node_related_auditor_ids: {$node_related_auditor_ids}" . PHP_EOL;
                            }
                        }
                    }

                    $model->staff_state              = $staff_state;
                    $model->node_related_auditor_ids = $node_related_auditor_ids;
                    $model->node_auditor_ids         = $current_node_info['node_auditor_ids'] ?? '';
                    $model->node_node_audit_type     = $current_node_info['node_audit_type'];

                    // 4. 更新当前征询数据的扩展字段
                    if ($model->save() === false) {
                        $fail_save++;
                        $fail_fyr_log .= "fyr_id: {$model->id}, staff_id: {$model->staff_id}, node_related_auditor_ids: {$node_related_auditor_ids}" . PHP_EOL;
                    } else {
                        $success_save++;
                    }
                } else {
                    // 非待审批: 因无法取到当时征询节点的真正审批人 且 非待审批状态的征询数据不影响"待处理"/"已征询"列表的取值
                    // 所以非待审批的 不再初始化征询相关数据
                    $no_pending_state_total++;
                    continue;
                }
            }

            $log .= "非待审批节点条数: $no_pending_state_total" . PHP_EOL;
            $log .= "初始成功条数: $success_save" . PHP_EOL;
            $log .= "初始失败条数: $fail_save" . PHP_EOL;
            $log .= "初始失败相关数据: $fail_fyr_log" . PHP_EOL;
            $log .= "初始失败条数[会签类型:多个子节点/工号同期发起征询场景]: $sub_node_sign_record_fail_save" . PHP_EOL;
            $log .= "初始失败数据[会签类型:多个子节点/工号同期发起征询场景]: $sub_node_sign_record_fail_fyr_log" . PHP_EOL;
        } catch (Exception $e) {
            $log .= '异常: ' . $e->getMessage() . PHP_EOL;
        }

        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        exit($log);
    }

    /**
     * 同步意见征询相关审批人和征询人在职状态等信息
     *
     * 说明: 已弃用(适用于 v13199 需求，在 v18276 中废弃)
     *
     * node_related_auditor_ids: 征询人所在节点的相关审批人
     * staff_state: 征询人在职状态
     *
     * 每天同步所有的: 各环境需要每天执行一次
     * php app/cli.php workflow sync_fyr_info
     */
    public function sync_fyr_infoAction()
    {
        $log = '';
        $log .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        $logger_type = 'info';

        try {
            // 分批处理: 每次 1000 条, 每批次歇息 10 秒钟
            // 先全部取出
            $length     = 1000;
            $sleep_secs = 10;

            // 获取发起的征询 且 征询节点仍在审批中的征询单
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['request' => WorkflowRequestModel::class]);
            $builder->leftJoin(WorkflowRequestNodeFYR::class,
                'request.id = fyr.request_id AND request.current_flow_node_id = fyr.flow_node_id', 'fyr');
            $builder->where('request.state = :pending_state: AND request.is_abandon = :is_abandon: AND fyr.action_type = :action_type:',
                [
                    'pending_state' => Enums::WF_STATE_PENDING,
                    'is_abandon'    => GlobalEnums::WORKFLOW_ABANDON_STATE_NO,
                    'action_type'   => GlobalEnums::CONSULTED_ACTION_TYPE,
                ]);
            $builder->columns(['fyr.id']);
            $fyr_list = $builder->getQuery()->execute()->toArray();
            $log      .= '待处理的数据条数: ' . count($fyr_list) . PHP_EOL;
            if (empty($fyr_list)) {
                throw new ValidationException('征询节点仍处于审批状态的征询单据为空, 无需处理', ErrCode::$VALIDATE_ERROR);
            }

            $fyr_ids    = array_column($fyr_list, 'id');
            $fyr_models = WorkflowRequestNodeFYR::find([
                'conditions' => 'id IN ({ids:array})',
                'bind'       => ['ids' => $fyr_ids],
            ]);

            $staff_ids = array_values(array_unique(array_filter(array_column($fyr_models->toArray(), 'staff_id'))));
            $log       .= '涉及征询人: ' . count($staff_ids) . PHP_EOL;

            // 2. 获取征询人在职状态
            $staff_list = [];
            if ($staff_ids) {
                $staff_list = HrStaffInfoModel::find([
                    'conditions' => 'staff_info_id IN ({ids:array})',
                    'bind'       => ['ids' => $staff_ids],
                    'columns'    => ['state', 'wait_leave_state', 'staff_info_id'],
                ])->toArray();
                $staff_list = array_column($staff_list, null, 'staff_info_id');
            }

            $log .= '征询人在HR系统的个数: ' . count($staff_list) . PHP_EOL;

            // 3. 获取征询人所在节点相关审批人
            $FYRService = new FYRService();

            $success_save                      = 0;
            $fail_save                         = 0;
            $fail_fyr_log                      = '';
            $sub_node_sign_record_fail_save    = 0;
            $sub_node_sign_record_fail_fyr_log = '';
            $no_pending_state_total            = 0;
            $no_sync_count                     = 0;
            foreach ($fyr_models as $key => $model) {
                if ($key && ($key % $length == 0)) {
                    sleep($sleep_secs);
                }

                // 征询节点待审批: 实时取相关审批人
                // 征询人在职状态: 如果未获取到征询人员工信息, 则默认在职状态
                $user_info   = $staff_list[$model->staff_id] ?? [];
                $staff_state = !empty($user_info) ? $FYRService->getStaffJobState($user_info) : GlobalEnums::CONSULTED_STAFF_JOB_STATE_ON;

                // 征询人在职状态未变: 无需维护审批干系人数据
                if ($model->staff_state == $staff_state) {
                    $no_sync_count++;
                    continue;
                }

                // 当前征询节点的相关审批人
                $node_related_auditor_ids = $model->node_related_auditor_ids;

                // 是否需重置征询人的审批干系人
                $is_reset_node_related_auditor_ids = false;

                // 由在职 -> 离职: 重置与征询人相关的审批人干系人
                if ($model->staff_state == GlobalEnums::CONSULTED_STAFF_JOB_STATE_ON && $staff_state == GlobalEnums::CONSULTED_STAFF_JOB_STATE_OFF) {
                    switch ($model->node_node_audit_type) {
                        // 或签: 干系人 == 征询人
                        case Enums::WF_NODE_AUDIT_TYPE_OR_SIGN;
                            // 考虑多个人同时征询的情况
                            $node_related_auditor_ids_arr = WorkflowRequestNodeFYR::find([
                                'conditions' => 'request_id = :request_id: AND flow_id = :flow_id: AND flow_node_id = :flow_node_id: AND action_type = :action_type:',
                                'bind'       => [
                                    'request_id'   => $model->request_id,
                                    'flow_id'      => $model->flow_id,
                                    'flow_node_id' => $model->flow_node_id,
                                    'action_type'  => GlobalEnums::CONSULTED_ACTION_TYPE,
                                ],
                                'columns'    => 'staff_id',
                            ])->toArray();
                            $node_related_auditor_ids_arr = !empty($node_related_auditor_ids_arr) ? array_unique(array_column($node_related_auditor_ids_arr,
                                'staff_id')) : [$model->staff_id];

                            // 只要征询人有一个在职的, 则干系人为节点的审批人
                            $is_have_on_job = false;
                            foreach ($node_related_auditor_ids_arr as $_staff_id) {
                                $_user_info   = $staff_list[$_staff_id] ?? [];
                                $_staff_state = !empty($_user_info) ? $FYRService->getStaffJobState($_user_info) : GlobalEnums::CONSULTED_STAFF_JOB_STATE_ON;
                                if ($_staff_state == GlobalEnums::CONSULTED_STAFF_JOB_STATE_ON) {
                                    $is_have_on_job = true;
                                    break;
                                }
                            }

                            // 都不在职
                            if ($is_have_on_job === false) {
                                $node_related_auditor_ids = implode(',', $node_related_auditor_ids_arr);
                            }

                            $is_reset_node_related_auditor_ids = true;
                            break;

                        // 子节点会签: 干系人中剔除征询人所在节点的非征询人
                        case Enums::WF_NODE_AUDIT_TYPE_SUB_NODE_COUNTERSIGN:
                            $node_related_auditor_ids_arr = explode(',', $model->node_related_auditor_ids);
                            $node_auditor_ids_arr         = explode(',', $model->node_auditor_ids);
                            $node_related_auditor_ids     = implode(',', array_diff($node_related_auditor_ids_arr,
                                array_diff($node_auditor_ids_arr, [$model->staff_id])));

                            $is_reset_node_related_auditor_ids = true;
                            break;
                        default:
                    }
                }

                // 由离职 -> 在职: 追加干系人中与征询人相关的人
                if ($model->staff_state == GlobalEnums::CONSULTED_STAFF_JOB_STATE_OFF && $staff_state == GlobalEnums::CONSULTED_STAFF_JOB_STATE_ON) {
                    switch ($model->node_node_audit_type) {
                        // 或签: 干系人 == 征询人所在节点的审批人
                        case Enums::WF_NODE_AUDIT_TYPE_OR_SIGN;
                            $node_related_auditor_ids = $model->node_auditor_ids;

                            $is_reset_node_related_auditor_ids = true;
                            break;

                        // 子节点会签: 征询人直属节点审批人 与 当前干系人合并
                        case Enums::WF_NODE_AUDIT_TYPE_SUB_NODE_COUNTERSIGN:
                            $node_related_auditor_ids_arr = explode(',', $model->node_related_auditor_ids);
                            $node_auditor_ids_arr         = explode(',', $model->node_auditor_ids);
                            $node_related_auditor_ids     = array_filter(array_unique(array_merge($node_related_auditor_ids_arr,
                                $node_auditor_ids_arr)));
                            $node_related_auditor_ids     = implode(',', $node_related_auditor_ids);

                            $is_reset_node_related_auditor_ids = true;
                            break;
                        default:
                    }
                }

                // 重置干系人
                if ($is_reset_node_related_auditor_ids) {
                    // 当前征询节点的征询记录
                    $exist_fyr_records = WorkflowRequestNodeFYR::find([
                        'conditions' => 'request_id = :request_id: AND flow_id = :flow_id: AND flow_node_id = :flow_node_id: AND action_type = :action_type:',
                        'bind'       => [
                            'request_id'   => $model->request_id,
                            'flow_id'      => $model->flow_id,
                            'flow_node_id' => $model->flow_node_id,
                            'action_type'  => GlobalEnums::CONSULTED_ACTION_TYPE,
                        ],
                    ]);

                    // 空对象不会遍历
                    foreach ($exist_fyr_records as $record_model) {
                        $record_model->node_related_auditor_ids = $node_related_auditor_ids;
                        if ($record_model->save() === false) {
                            $sub_node_sign_record_fail_save++;
                            $sub_node_sign_record_fail_fyr_log .= "record_fyr_id: {$record_model->id}, staff_id: {$model->staff_id}, node_related_auditor_ids: {$node_related_auditor_ids}" . PHP_EOL;
                        }
                    }
                }

                // 4. 更新当前征询数据的 征询人在职状态 和 审批干系人
                $model->staff_state              = $staff_state;
                $model->node_related_auditor_ids = $node_related_auditor_ids;
                if ($model->save() === false) {
                    $fail_save++;
                    $fail_fyr_log .= "fyr_id: {$model->id}, staff_id: {$model->staff_id}, node_related_auditor_ids: {$node_related_auditor_ids}" . PHP_EOL;
                } else {
                    $success_save++;
                }
            }

            $log .= "非待审批节点条数: $no_pending_state_total" . PHP_EOL;
            $log .= "无需同步条数(征询人状态未变): $no_sync_count" . PHP_EOL;
            $log .= "同步成功条数: $success_save" . PHP_EOL;
            $log .= "同步失败条数: $fail_save" . PHP_EOL;
            $log .= "失败相关数据: $fail_fyr_log" . PHP_EOL;
            $log .= "同步失败条数[会签类型:多个子节点/工号同期发起征询场景]: $sub_node_sign_record_fail_save" . PHP_EOL;
            $log .= "同步失败数据[会签类型:多个子节点/工号同期发起征询场景]: $sub_node_sign_record_fail_fyr_log" . PHP_EOL;
        } catch (ValidationException $e) {
            $log .= '提示: ' . $e->getMessage() . PHP_EOL;
        } catch (Exception $e) {
            $logger_type = 'warning';
            $log         .= '异常: ' . $e->getMessage() . PHP_EOL;
        }

        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $this->logger->$logger_type($log);

        exit($log);
    }

    /**
     * 16911【ALL|OA】采购申请优化
     * 针对节点audit_type = 4的自动通过，无需点击审批的节点下的审批人发送邮件
     */
    public function auto_approval_ignore_send_emailAction()
    {
        echo 'begin:' . date('Y-m-d H:i:s') . PHP_EOL;
        try {
            $redis = $this->getDI()->get('redis');
            $workflow_mail_service = new WorkflowMailService();
            while ($redis_data = $redis->rpop(WorkflowMailService::$redis_list_name)) {
                try {
                    //防止刚入队列，mysql自动审批sql还未执行导致找不到人，给放到消费增加个时间差
                    sleep(5);
                    if (!empty($redis_data)) {
                        $this->logger->info('workflow_auto_approval_ignore_redis_list_consumption data ' . $redis_data);
                        $redis_data = json_decode($redis_data, true);
                        $workflow_mail_service->consumptionRedisList($redis_data);
                    } else {
                        sleep(10);
                    }
                } catch (\Exception $e) {
                    $this->logger->warning('auto_approval_ignore_send_emailAction end ' . $e->getMessage() . $e->getTraceAsString());
                    break;
                }
            }
        } catch (Exception $e) {
            $this->logger->warning('auto_approval_ignore_send_emailAction end ' . $e->getMessage() . $e->getTraceAsString());
        }
        echo 'end:' . date('Y-m-d H:i:s') . PHP_EOL;
    }

    /**
     * 同步征询表征询人所在子节点ID(数量级: 两位数)
     *
     * 说明: 适用于 v18276 (一次性脚本)
     *
     * php app/cli.php workflow sync_fyr_sub_node_id
     */
    public function sync_fyr_sub_node_idAction()
    {
        $log         = '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log         .= '当前内存: ' . memory_usage() . PHP_EOL;
        $logger_type = 'info';

        try {
            // 1. 获取当前待审批的审批流
            $request_list = WorkflowRequestModel::find([
                'conditions' => 'state = :state: AND is_abandon = :is_abandon:',
                'bind'       => [
                    'state'      => Enums::WF_STATE_PENDING,
                    'is_abandon' => GlobalEnums::WORKFLOW_ABANDON_STATE_NO,
                ],
                'columns'    => ['id', 'current_flow_node_id'],
            ])->toArray();

            $log .= '待审批的审批流共 ' . count($request_list) . ' 条' . PHP_EOL;
            $log .= '当前内存: ' . memory_usage() . PHP_EOL;

            if (empty($request_list)) {
                throw new ValidationException('无待审批的审批流, 征询数据无需处理', ErrCode::$VALIDATE_ERROR);
            }

            $request_ids = array_column($request_list, 'id');
            $node_ids    = array_values(array_unique(array_column($request_list, 'current_flow_node_id')));

            // 2. 获取上述审批流中当前审批节点发起的征询数据(仅需处理一级节点是子节点会签类型的; 或签 和 工号会签类型的, 子节点ID默认0, 无需同步)
            $fyr_models = WorkflowRequestNodeFYR::find([
                'conditions' => 'request_id IN ({request_ids:array}) AND flow_node_id IN ({flow_node_ids:array}) AND node_node_audit_type = :node_node_audit_type: AND action_type = :action_type:',
                'bind'       => [
                    'request_ids'          => $request_ids,
                    'flow_node_ids'        => $node_ids,
                    'node_node_audit_type' => Enums::WF_NODE_AUDIT_TYPE_SUB_NODE_COUNTERSIGN,
                    'action_type'          => GlobalEnums::CONSULTED_ACTION_TYPE,
                ],
            ]);

            $log .= '子节点会签发起的征询共 ' . count($fyr_models) . ' 条' . PHP_EOL;
            $log .= '当前内存: ' . memory_usage() . PHP_EOL;
            if (empty($fyr_models)) {
                throw new ValidationException('无子节点会签发起的征询, 征询数据无需处理', ErrCode::$VALIDATE_ERROR);
            }

            // 3. 获取征询数据中 征询人所在的子节点 并 同步
            $node_auditor_entity = new WorkflowRequestNodeAuditorModel();
            $success_count       = 0;
            $fail_count          = 0;
            $fail_log            = '';
            $success_log         = '';
            foreach ($fyr_models as $fyr) {
                // 查找子节点征询人所在的子节点ID
                $node_auditor_model = $node_auditor_entity->findFirst([
                    'conditions' => 'request_id = :request_id: AND flow_node_id = :flow_node_id: AND auditor_id = :auditor_id:',
                    'bind'       => [
                        'request_id'   => $fyr->request_id,
                        'flow_node_id' => $fyr->flow_node_id,
                        'auditor_id'   => $fyr->staff_id,
                    ],
                    'columns'    => ['sub_node_id'],
                ]);

                if (empty($node_auditor_model) || $node_auditor_model->sub_node_id <= 0) {
                    $fail_count++;
                    $fail_log .= "未找到其子节点 或 子节点ID为0[征询ID-{$fyr->id}] ;";
                    continue;
                }

                $update_data = [
                    'flow_sub_node_id' => $node_auditor_model->sub_node_id,
                ];
                if ($fyr->save($update_data) === false) {
                    $fail_count++;
                    $fail_log .= "更新失败[征询ID-{$fyr->id}], flow_sub_node_id = $node_auditor_model->sub_node_id ;";
                    continue;
                }

                $success_count++;
                $success_log .= "{$fyr->id} => $node_auditor_model->sub_node_id ; ";
            }

            $log .= '更新成功: ' . $success_count . ' 条' . PHP_EOL;
            $log .= '征询更新成功的信息[fyr_id => sub_node_id]: ' . $success_log . PHP_EOL;
            $log .= '更新失败: ' . $fail_count . ' 条' . PHP_EOL;
            $log .= '失败日志: ' . $fail_log . PHP_EOL;
        } catch (ValidationException $e) {
            $log .= '任务异常: ' . $e->getMessage() . PHP_EOL;
        } catch (Exception $e) {
            $logger_type = 'warning';
            $log         .= '任务异常: ' . $e->getMessage() . PHP_EOL;
        }

        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $this->logger->$logger_type($log);
        exit($log);
    }

    /**
     *
     * 初始化征询中间表数据(数量级: pro / tra 100+; dev 300+)
     *
     * 说明: 适用于 v18276 (一次性脚本)
     *
     * php app/cli.php workflow init_fyr_middle
     */
    public function init_fyr_middleAction()
    {
        $log         = '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log         .= '当前内存: ' . memory_usage() . PHP_EOL;
        $logger_type = 'info';

        try {
            // 1. 获取审批中 且 审批节点 有征询的数据
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['request' => WorkflowRequestModel::class]);
            $builder->leftJoin(WorkflowRequestNodeFYR::class,
                'request.id = fyr.request_id AND request.current_flow_node_id = fyr.flow_node_id', 'fyr');
            $builder->where('request.state = :pending_state: AND request.is_abandon = :is_abandon: AND fyr.action_type = :action_type:',
                [
                    'pending_state' => Enums::WF_STATE_PENDING,
                    'is_abandon'    => GlobalEnums::WORKFLOW_ABANDON_STATE_NO,
                    'action_type'   => GlobalEnums::CONSULTED_ACTION_TYPE,
                ]);
            $builder->columns([
                'fyr.id',
                'fyr.request_id',
                'fyr.flow_node_id',
                'fyr.flow_sub_node_id',
                'fyr.staff_id',
                'fyr.node_related_auditor_ids',
                'fyr.node_auditor_ids',
                'fyr.node_node_audit_type',
                'fyr.created_at',
                'request.current_node_auditor_id',
                'request.name AS request_name',
            ]);
            $fyr_list = $builder->getQuery()->execute()->toArray();

            $log .= '审批中的征询记录共 ' . count($fyr_list) . ' 条' . PHP_EOL;
            $log .= '当前内存: ' . memory_usage() . PHP_EOL;
            if (empty($fyr_list)) {
                throw new ValidationException('待处理的征询数据为空, 征询中间表无需初始化', ErrCode::$VALIDATE_ERROR);
            }

            // 2. 计算 每个节点 征询 被回复的情况, 为 初始化征询中间表数据做准备
            $fyr_middle_list                = [];
            $fyr_node_auditor_null_log      = '';// 节点审批人为空异常
            $fyr_not_in_node_auditors_error = '';// 节点征询人非节点审批人异常
            $error_count                    = 0; // 异常总数
            foreach ($fyr_list as $fyr_info) {
                // 同一审批流的同一节点数据是否已计算
                $middle_key = $fyr_info['request_id'] . '_' . $fyr_info['flow_node_id'];

                // 或签的, 若已初始化, 则无需重复计算
                $exist_middle_data = $fyr_middle_list[$middle_key] ?? [];
                if (!empty($exist_middle_data) && $exist_middle_data['node_audit_type'] == Enums::WF_NODE_AUDIT_TYPE_OR_SIGN) {
                    continue;
                }

                // 2.1 实时取征询节点的审批人
                $request_model     = WorkflowRequestModel::findFirst($fyr_info['request_id']);
                $current_node_info = FYRService::getInstance()->getConsultantNodeAuditorInfo($request_model,
                    $fyr_info['staff_id']);

                // 征询人所在节点的审批人未找到的, 记录下来 人为排查
                if (empty($current_node_info['node_auditor_ids'])) {
                    $error_count++;
                    $fyr_node_auditor_null_log .= 'fyr_info=' . json_encode($fyr_info,
                            JSON_UNESCAPED_UNICODE) . ';node_info=' . json_encode($current_node_info,
                            JSON_UNESCAPED_UNICODE) . PHP_EOL;
                    continue;
                }

                // 征询人所在节点的相关审批人: 工号会签-征询人; 或签/子节点会签-征询人所在节点的审批人
                $current_node_related_auditor_ids = explode(',', $current_node_info['node_related_auditor_ids']);

                // 征询人是否是 征询节点的审批人之一, 若不是 则说明 征询人发起征询时是异常的(极个别老数据), 需人为介入排查
                if (!in_array($fyr_info['staff_id'], $current_node_related_auditor_ids)) {
                    // 验证征询相关审批人是否发起过征询, 若未发起过征询, 则忽略
                    $is_exist_fyr_data = WorkflowRequestNodeFYR::findFirst([
                        'conditions' => 'request_id = :request_id: AND flow_node_id = :flow_node_id: AND staff_id IN ({staff_ids:array}) AND action_type = :action_type:',
                        'bind'       => [
                            'request_id'   => $fyr_info['request_id'],
                            'flow_node_id' => $fyr_info['flow_node_id'],
                            'staff_ids'    => $current_node_related_auditor_ids,
                            'action_type'  => GlobalEnums::CONSULTED_ACTION_TYPE,
                        ],
                        'columns'    => ['id'],
                    ]);
                    if (empty($is_exist_fyr_data)) {
                        $error_count++;
                        $fyr_not_in_node_auditors_error .= 'fyr_info = ' . json_encode($fyr_info,
                                JSON_UNESCAPED_UNICODE) . '; node_auditor_ids = ' . $current_node_info['node_related_auditor_ids'] . PHP_EOL;
                        continue;
                    }
                }

                // 2.2 初始化同审批流同节点的中间表数据
                if (!empty($exist_middle_data)) {
                    $middle_data = $exist_middle_data;
                } else {
                    $middle_data = [
                        'request_id'             => $fyr_info['request_id'],
                        'flow_node_id'           => $fyr_info['flow_node_id'],
                        'node_fyr_auditor_ids'   => $current_node_info['node_related_auditor_ids'],
                        'node_reply_auditor_ids' => '',
                        'node_audit_type'        => $current_node_info['node_audit_type'],
                        'node_auditor_ids'       => $current_node_info['node_auditor_ids'],
                        'created_id'             => $fyr_info['staff_id'],
                        'created_at'             => $fyr_info['created_at'],
                        'updated_action_type'    => GlobalEnums::CONSULTED_ACTION_TYPE,
                    ];
                }

                $middle_data['updated_id'] = '10000';
                $middle_data['updated_at'] = date('Y-m-d H:i:s');

                // 为避免征询相关审批人的老数据有缺失, 此处再合并一次当前审批流当前节点的征询审批人(不影响结果)
                $exist_node_fyr_auditor_ids          = explode(',', $middle_data['node_fyr_auditor_ids']);
                $middle_data['node_fyr_auditor_ids'] = implode(',',
                    array_filter(array_unique(array_merge($exist_node_fyr_auditor_ids,
                        $current_node_related_auditor_ids))));

                // 2.3 计算回复情况
                // 是否存在未回复的
                $waiting_reply_info = WorkflowRequestNodeAt::findFirst([
                    'conditions' => 'request_id = :request_id: AND flow_node_id = :flow_node_id: AND is_reply = :is_reply: AND create_staff_id IN ({create_staff_ids:array})',
                    'bind'       => [
                        'request_id'       => $fyr_info['request_id'],
                        'flow_node_id'     => $fyr_info['flow_node_id'],
                        'is_reply'         => GlobalEnums::CONSULTED_REPLY_STATE_PENDING,
                        'create_staff_ids' => $current_node_related_auditor_ids,
                    ],
                    'columns'    => ['id'],
                ]);

                // 全部已回复
                if (empty($waiting_reply_info)) {
                    $exist_reply_auditor_ids               = explode(',', $middle_data['node_reply_auditor_ids']);
                    $middle_data['node_reply_auditor_ids'] = implode(',',
                        array_filter(array_unique(array_merge($current_node_related_auditor_ids,
                            $exist_reply_auditor_ids))));
                    $middle_data['updated_action_type']    = GlobalEnums::CONSULTED_REPLY_ACTION_TYPE;
                }

                // 2.3 征询中间表数据
                $fyr_middle_list[$middle_key] = $middle_data;
            }

            $log .= '待初始化中间表数据 ' . count($fyr_middle_list) . ' 条' . PHP_EOL;
            $log .= "初始化数据构造过程中的问题数据: 共 {$error_count} 条" . PHP_EOL;
            $log .= '节点审批人为空明细(征询数据的节点审批人为空[node_auditor_ids], 已跳过未处理): ' . $fyr_node_auditor_null_log . PHP_EOL;
            $log .= '节点征询人不在节点审批人中的明细(征询人不在节点审批人中, 且 节点审批人未发起过征询, 已跳过未处理): ' . $fyr_not_in_node_auditors_error . PHP_EOL;
            $log .= '当前内存: ' . memory_usage() . PHP_EOL;
            if (empty($fyr_middle_list)) {
                throw new ValidationException('无符合条件的待初始化征询数据', ErrCode::$VALIDATE_ERROR);
            }

            // 3. 写入征询中间表
            $fyr_middle_list = array_values($fyr_middle_list);
            if ((new WorkflowRequestNodeFyrMiddleModel())->batch_insert($fyr_middle_list) === false) {
                throw new BusinessException('中间表数据批量写入失败, 数据=' . json_encode($fyr_middle_list, JSON_UNESCAPED_UNICODE),
                    ErrCode::$BUSINESS_ERROR);
            }

            $log .= '中间表数据初始化完成, 共 ' . count($fyr_middle_list) . ' 条' . PHP_EOL;
        } catch (ValidationException $e) {
            $logger_type = 'notice';
            $log         .= '任务提示: ' . $e->getMessage() . PHP_EOL;
        } catch (BusinessException $e) {
            $logger_type = 'warning';
            $log         .= '任务异常: ' . $e->getMessage() . PHP_EOL;
        } catch (Exception $e) {
            $logger_type = 'error';
            $log         .= '任务异常: ' . $e->getMessage() . PHP_EOL;
        }

        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $this->logger->$logger_type($log);
        exit($log);
    }

    /**
     * 定期清理征询中间表
     *
     * 说明: 将征询中间表中 已是审批终态的 数据删除掉(默认一个月前的)
     *
     * php app/cli.php workflow clear_fyr_middle_useless_data
     *
     * @param array $params
     */
    public function clear_fyr_middle_useless_dataAction(array $params = [])
    {
        $log         = '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log         .= '当前内存: ' . memory_usage() . PHP_EOL;
        $logger_type = 'info';

        try {
            // 清理多少天前的
            $before_days = !empty($params[0]) ? intval($params[0]) : 30;
            if ($before_days <= 0) {
                throw new ValidationException("参数错误 before_days = {$before_days}", ErrCode::$VALIDATE_ERROR);
            }

            $before_time = date('Y-m-d H:i:s', strtotime("-{$before_days} days"));

            $log .= "准备清理 {$before_days} 天前 [{$before_time}] 生成的征询中间表数据" . PHP_EOL;

            // 找N天前的征询中间表数据
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['middle_fyr' => WorkflowRequestNodeFyrMiddleModel::class]);
            $builder->leftjoin(WorkflowRequestModel::class, 'request.id = middle_fyr.request_id', 'request');
            $builder->where('(request.state != :pending_state: OR request.is_abandon = :is_abandon:) AND middle_fyr.created_at <= :before_time:',
                [
                    'pending_state' => Enums::WF_STATE_PENDING,
                    'is_abandon'    => GlobalEnums::WORKFLOW_ABANDON_STATE_YES,
                    'before_time'   => $before_time,
                ]);

            $builder->columns([
                'middle_fyr.id',
                'middle_fyr.request_id',
            ]);
            $waiting_clear_middle_fyr_list = $builder->getQuery()->execute()->toArray();

            $log .= '共 ' . count($waiting_clear_middle_fyr_list) . ' 条 中间表数据的审批流已是终态(非待审批 或 已被废弃)' . PHP_EOL;

            if (empty($waiting_clear_middle_fyr_list)) {
                throw new ValidationException('无待清理的中间表数据', ErrCode::$VALIDATE_ERROR);
            }

            $waiting_clear_middle_fyr_ids = array_column($waiting_clear_middle_fyr_list, 'id');

            // 删除 终态的中间表数据
            $waiting_clear_middle_fyr_models = WorkflowRequestNodeFyrMiddleModel::find([
                'conditions' => 'id IN ({ids:array})',
                'bind'       => ['ids' => $waiting_clear_middle_fyr_ids],
            ]);

            $log .= '即将清理 ' . count($waiting_clear_middle_fyr_models) . ' 条 中间表数据' . PHP_EOL;
            if ($waiting_clear_middle_fyr_models->delete() === false) {
                throw new BusinessException('清理失败', ErrCode::$BUSINESS_ERROR);
            }

            $log .= '清理成功' . PHP_EOL;
        } catch (ValidationException $e) {
            $logger_type = 'info';
            $log         .= '任务提示: ' . $e->getMessage() . PHP_EOL;
        } catch (BusinessException $e) {
            $logger_type = 'notice';
            $log         .= '任务异常: ' . $e->getMessage() . PHP_EOL;
        } catch (Exception $e) {
            $logger_type = 'warning';
            $log         .= '任务异常: ' . $e->getMessage() . PHP_EOL;
        }

        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $this->logger->$logger_type($log);
        exit($log);
    }

    /**
     * 给申请人/发起人发送审批通过待支付BY消息提醒
     * @cli php app/cli.php workflow send_submitter_wait_pay_msg
     */
    public function send_submitter_wait_pay_msgAction($args = [])
    {
        $this->checkLock(__METHOD__);
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log = '任务名称: ' . $process_name . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log .= '初始内存: ' . memory_usage() . PHP_EOL;
        $is_exception = false;
        $type = $args[0] ?? '';

        try {
            //第一步：获取推送天数
            $send_submitter_wait_pay_days = EnumsService::getInstance()->getSettingEnvValue('send_submitter_wait_pay_days', 0);
            if ($send_submitter_wait_pay_days) {
                //第二步：获取开启消息提醒的模块列表对应的flow_id、biz_type
                $builder = $this->modelsManager->createBuilder();
                $builder->columns('flow.id, flow.biz_type, module.name_key as module_name_key');
                $builder->from(['flow' => WorkflowModel::class]);
                $builder->leftJoin(SysModuleModel::class, 'flow.module_id = module.id', 'module');
                $builder->where('module.send_submitter_wait_pay_msg = :send_submitter_wait_pay_msg:', ['send_submitter_wait_pay_msg' => SysConfigEnums::SEND_SUBMITTER_REJECT_SMS_YES]);
                $builder->andWhere('flow.enable_status != :enable_status: and flow.biz_type !=0 ', ['enable_status' => OAWorkflowEnums::WORKFLOW_ENABLE_STATUS_NOT]);
                if (!empty($type)) {
                    $typeList = explode(',', $type);
                    $builder->andWhere('flow.biz_type in ({typeList:array})', ['typeList' => $typeList]);
                }
                $flow_list = $builder->getQuery()->execute()->toArray();
                if (!empty($flow_list)) {
                    $biz_type = array_values(array_unique(array_column($flow_list, 'biz_type')));
                    $biz_type_module_name = array_column($flow_list, 'module_name_key', 'biz_type');
                    $approved_at_start = date('Y-m-d 00:00:00', strtotime('-' . $send_submitter_wait_pay_days . ' days'));
                    $approved_at_end = date('Y-m-d 00:00:00');
                    //第三步：找到从审批通过的次日开始BY提醒员工，连续3天发送BY消息提醒，让其提交纸质单据给财务。
                    foreach ($biz_type as $value) {
                        try {
                            switch ($value) {
                                case Enums::WF_REIMBURSEMENT_TYPE:
                                    $send_staff = $this->sendReimbursementWaitPayMsg($approved_at_start, $approved_at_end, $biz_type_module_name);
                                    break;
                                default:
                                    $list       = $this->getDefaultPendingSmsList($value, $approved_at_start, $approved_at_end);
                                    $send_staff = WorkflowSmsService::getInstance()->sendWaitPayMsg($list, $biz_type_module_name);
                                    break;
                            }

                            $log .= 'biz_type：【' . $value . '】' . ($send_staff ? '发送员工工号组：【' . implode(',', $send_staff) . '】' : '暂无员工需要提醒') . PHP_EOL;
                        } catch (ValidationException $e) {
                            $this->logger->warning('biz_type：【' . $value . '】' . ', send_submitter_wait_pay_msg error:' . $e->getMessage());
                            continue;
                        }
                    }
                } else {
                    $log .= 'send_submitter_wait_pay_msg 无数据需要处理' . PHP_EOL;
                }
            } else {
                $log .= 'send_submitter_wait_pay_msg 未配置推送天数' . PHP_EOL;
            }
        } catch (Exception $e) {
            $log .= $e->getMessage() . PHP_EOL;
            $is_exception = true;
        }

        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        if ($is_exception) {
            $this->logger->error('send_submitter_wait_pay_msg 给申请人/发起人发送审批通过待支付BY消息提醒-任务: ' . $log);
        } else {
            $this->logger->info('send_submitter_wait_pay_msg 给申请人/发起人发送审批通过待支付BY消息提醒-任务: ' . $log);
        }
        $this->clearLock(__METHOD__);
        exit($log);
    }

    /**
     * 检测审批流条件
     *
     * php app/cli.php workflow checkWorkflowConditions
     *
     * @return void
     * @throws ValidationException
     */
    public function checkWorkflowConditionsAction()
    {
        self::setLanguage('zh');
        (new WorkflowCheckerService())->check();
    }

    /**
     * 获取各个模块的待支付提醒列表
     * @param $type
     * @param $approved_at_start
     * @param $approved_at_end
     * @return mixed
     * @throws ValidationException
     */
    private function getDefaultPendingSmsList($type, $approved_at_start, $approved_at_end)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['request' => WorkflowRequestModel::class]);
        if ($type == Enums::WF_REIMBURSEMENT_TYPE) {
            $columns = 'main.created_id as create_staff_id';
        } else {
            if ($type == Enums::ORDINARY_PAYMENT_BIZ_TYPE) {
                $columns = 'main.create_id  as create_staff_id';
            } else {
                $columns = 'request.create_staff_id';
            }
        }
        $module_class = WorkflowSmsService::getInstance()->getModelClass($type);
        $builder->columns($columns . ', request.biz_type, request.created_at, request.approved_at, request.name');
        $builder->leftJoin($module_class, 'request.biz_value = main.id', 'main');
        $builder->where('request.state = :state: and request.is_abandon = :is_abandon:',
            ['state' => Enums::WF_STATE_APPROVED, 'is_abandon' => GlobalEnums::WORKFLOW_ABANDON_STATE_NO]);
        $builder->andWhere('main.pay_status = :pay_status:', ['pay_status' => Enums::PAYMENT_PAY_STATUS_PENDING]);
        $builder->andWhere('request.biz_type = :biz_type:', ['biz_type' => $type]);
        $builder->andWhere('request.approved_at >= :approved_at_start: and request.approved_at < :approved_at_end:',
            ['approved_at_start' => $approved_at_start, 'approved_at_end' => $approved_at_end]);
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 报销模块待支付提醒列表
     * @param $approved_at_start
     * @param $approved_at_end
     * @param $biz_type_module_name
     * @return array
     * @throws ValidationException
     */
    private function sendReimbursementWaitPayMsg($approved_at_start, $approved_at_end, $biz_type_module_name)
    {
        $countryCode = get_country_code();
        if (in_array($countryCode, [GlobalEnums::TH_COUNTRY_CODE, GlobalEnums::PH_COUNTRY_CODE, GlobalEnums::MY_COUNTRY_CODE])) {

            if ($countryCode == GlobalEnums::MY_COUNTRY_CODE) {
                $confirm_status = Enums\PaperDocumentEnums::CONFIRM_STATE_PENDING_SUBMIT;
            } else {
                $confirm_status = Enums\PaperDocumentEnums::CONFIRM_STATE_PENDING_CONFIRM;
            }
            // 待确认
            $list = $this->getReimbursementPendingConfirmSmsList($approved_at_start, $approved_at_end, $confirm_status);
            $pendingConfirmStaffIds = WorkflowSmsService::getInstance()->sendWaitPayMsg($list, $biz_type_module_name);

            // 待补全
            $fillList = $this->getReimbursementPendingFillSmsList($approved_at_start, $approved_at_end);
            $pendingFillStaffIds = WorkflowSmsService::getInstance()->sendReimbursementPendingFillWaitPayMsg($fillList, $biz_type_module_name);
        } else {
            $list = $this->getDefaultPendingSmsList(Enums::WF_REIMBURSEMENT_TYPE, $approved_at_start, $approved_at_end);
            $pendingConfirmStaffIds = WorkflowSmsService::getInstance()->sendWaitPayMsg($list, $biz_type_module_name);
            $pendingFillStaffIds = [];
        }

        return array_merge($pendingConfirmStaffIds, $pendingFillStaffIds);
    }

    /**
     * @param $approved_at_start
     * @param $approved_at_end
     * @return mixed
     * @throws ValidationException
     */
    private function getReimbursementPendingFillSmsList($approved_at_start, $approved_at_end)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['request' => WorkflowRequestModel::class]);
        $module_class = WorkflowSmsService::getInstance()->getModelClass(Enums::WF_REIMBURSEMENT_TYPE);
        $builder->columns('main.created_id as create_staff_id, request.biz_type, request.created_at, request.approved_at, request.name');
        $builder->leftJoin($module_class, 'request.biz_value = main.id', 'main');
        $builder->leftjoin(PaperDocumentModel::class, 'paper.serial_no = main.no', 'paper');
        $builder->where('request.state = :state: and request.is_abandon = :is_abandon:',
            ['state' => Enums::WF_STATE_APPROVED, 'is_abandon' => GlobalEnums::WORKFLOW_ABANDON_STATE_NO]);
        $builder->andWhere('main.pay_status = :pay_status:', ['pay_status' => Enums::PAYMENT_PAY_STATUS_PENDING]);
        $builder->andWhere('main.confirm_status = :confirm_status:', ['confirm_status' => Enums\PaperDocumentEnums::CONFIRM_STATE_PENDING_FILL]);
        $builder->andWhere('request.biz_type = :biz_type:', ['biz_type' => Enums::WF_REIMBURSEMENT_TYPE]);
        $builder->andWhere('paper.last_fill_operate_at >= :operate_at_start: and paper.last_fill_operate_at < :operate_at_start_end:',
            ['operate_at_start' => $approved_at_start, 'operate_at_start_end' => $approved_at_end]);
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 报销模块 待确认提醒列表
     * @param $approved_at_start
     * @param $approved_at_end
     * @param $confirm_status
     * @return mixed
     * @throws ValidationException
     */
    private function getReimbursementPendingConfirmSmsList($approved_at_start, $approved_at_end, $confirm_status)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['request' => WorkflowRequestModel::class]);
        $module_class = WorkflowSmsService::getInstance()->getModelClass(Enums::WF_REIMBURSEMENT_TYPE);
        $builder->columns('main.created_id as create_staff_id, request.biz_type, request.created_at, request.approved_at, request.name');
        $builder->leftJoin($module_class, 'request.biz_value = main.id', 'main');
        $builder->where('request.state = :state: and request.is_abandon = :is_abandon:',
            ['state' => Enums::WF_STATE_APPROVED, 'is_abandon' => GlobalEnums::WORKFLOW_ABANDON_STATE_NO]);
        $builder->andWhere('main.pay_status = :pay_status:', ['pay_status' => Enums::PAYMENT_PAY_STATUS_PENDING]);
        $builder->andWhere('main.confirm_status = :confirm_status:', ['confirm_status' => $confirm_status]);
        $builder->andWhere('request.biz_type = :biz_type:', ['biz_type' => Enums::WF_REIMBURSEMENT_TYPE]);
        $builder->andWhere('request.approved_at >= :approved_at_start: and request.approved_at < :approved_at_end:',
            ['approved_at_start' => $approved_at_start, 'approved_at_end' => $approved_at_end]);
        return $builder->getQuery()->execute()->toArray();
    }
}
