<?php

/**
 * Created by PhpStorm.
 * Date: 2021/9/15
 * Time: 14:19
 */
use App\Library\ErrCode;

use App\Library\Enums\GlobalEnums;
use App\Library\Validation\ValidationException;
use App\Modules\Budget\Services\BudgetService;
use App\Modules\Material\Models\MaterialFinanceCategoryModel;
use App\Modules\OrdinaryPayment\Models\OrdinaryPayment;
use App\Modules\OrdinaryPayment\Models\OrdinaryPaymentDetail;
use App\Modules\OrdinaryPayment\Models\OrdinaryPaymentExtend;
use App\Modules\OrdinaryPayment\Services\BaseService;
use App\Modules\OrdinaryPayment\Services\OrdinaryPaymentDetailService;
use App\Modules\OrdinaryPayment\Services\SapService;
use App\Modules\Training\Services\TaskService;
use App\Modules\Budget\Services\LedgerAccountService;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Organization\Models\SysDepartmentModel;
use App\Modules\Common\Services\EnumsService;
use App\Repository\DepartmentRepository;
use App\Repository\oa\LedgerAccountRepository;
use App\Modules\Budget\Services\BudgetWithholdingService;
use App\Library\Enums\BudgetWithholdingEnums;

class OrdinarypaymentTask extends BaseTask
{
    const COST_DEPARTMENT_ID = 87;//Flash Home Settlement
    const LIMIT_LEVEL_CODE = 117;//第三方运费-加盟商
    const FINANCE_CATEGORY_CODE = 'P001-5';//P001-5

    /**
     * 组装item sap所需数据格式
     *
     * @param $item
     * @param $ledger_info
     * @param array $finance_category
     * @return mixed
     */
    public function deal_ordinary_item($item, $ledger_info, $finance_category = [])
    {
        $lang          = 'en';
        $budgetIds     = array_column($item, 'budget_id'); //科目IDs（付款分类）
        $productIds    = array_column($item, 'product_id');//产品IDs(费用类型）
        $budgetService = new BudgetService();
        $budgets       = $budgetService->budgetObjectList($budgetIds);
        $products      = $budgetService->budgetObjectProductList($productIds);

        $wht_cat_map = EnumsService::getInstance()->getWhtRateCategoryMap(0);


        // 21885-大于100行时发票接口传参逻辑调整-按照付款分类以及费用类型分组，理论上只会有一行数据
        if (count($item) > 100) {
            $budget_product_group = [];
            //虚拟成本中心
            $sap_virtual_cost_center = EnumsService::getInstance()->getSettingEnvValue('sap_virtual_cost_center');
            foreach ($item as $value) {
                //付款分类以及费用类型
                $group_key = $value['budget_id'] . '_' . $value['product_id'];
                if (!isset($budget_product_group[$group_key])) {
                    //系统配置中增加虚拟成本中心配置，固定取各国的虚拟成本中心配置中的值
                    $value['cost_center_name'] = $sap_virtual_cost_center;
                    $budget_product_group[$group_key] = $value;
                } else {
                    //不含税金额求和
                    $budget_product_group[$group_key]['amount_no_tax'] = bcadd($budget_product_group[$group_key]['amount_no_tax'], $value['amount_no_tax'], 2);
                }
            }
            $item = $budget_product_group;
        }

        //获取国家码
        $country_code = get_country_code();
        foreach ($item as $key => &$value) {
            $value['budget_name']       = isset($budgets[$value['budget_id']]) ? $budgets[$value['budget_id']]['name_' . $lang] : '';
            $value['product_name']      = isset($products[$value['product_id']]) ? $products[$value['product_id']]['name_' . $lang] : '';
            $value['wht_category_name'] = $wht_cat_map[$value['wht_category']] ?? 0;
            $value['wht_rate_name']     = $value['wht_rate'] . '%';
            $value['attachment_list']   = $data['attachment_list'][$value['id']] ?? [];
            //核算科目
            $value['ledger_account_name'] = $ledger_info[$value['ledger_account_id']] ?? '';
            $value['wht_tax_code']        = sapService::getInstance()->wht_code($value['wht_category'], $value['wht_rate']);
            $value['vat_code']            = sapService::getInstance()->vat_code($value['vat_rate'], $value['deductible_vat_tax']);
            $value['finance_category_id'] = $finance_category[$value['finance_category_id']] ?? '';
            //15998针对马来/17688针对泰国菲律宾国家，当行上的不含税金额为负数时，需要传输003，不为负数，则保留原逻辑，默认002
            $value['item_type_code'] = (in_array($country_code, [GlobalEnums::MY_COUNTRY_CODE, GlobalEnums::TH_COUNTRY_CODE, GlobalEnums::PH_COUNTRY_CODE]) && $value['amount_no_tax'] < 0) ? '003' : '002';
            //15998需求NetAmount	净额、NetUnitPrice-Amount 净价 取绝对值
            $value['amount_no_tax'] = abs($value['amount_no_tax']);
        }

        return $item;
    }

    /**
     * 所有普通付款同步sap
     *
     * php app/cli.php ordinarypayment ordinary_payment_sap
     *
     * @param $params
     */
    public function ordinary_payment_sapAction($params)
    {
        $this->checkLock(__METHOD__, 10800);

        $logger_type = 'info';

        $log = '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        $days   = $params[0] ?? 31;

        $log .= $days . ' 天前的' . PHP_EOL;

        try {
            $end_time   = date('Y-m-d H:i:s');
            $apply_date = '2021-10-01 00:00:00';

            //计算本月日期1-设定日期 只执行上个月 11-31号只执行本月数据
            $start_date     = date('Y-m-01 00:00:00');
            $end_date       = date("Y-m-{$this->getSapTaskSendDate()} 00:00:00");
            $is_little_date = true;

            if (date('Y-m-d H:i:s') >= $start_date && date('Y-m-d H:i:s') < $end_date) {
                $end_time   = $start_date;
                $start_time = date('Y-m-d H:i:s', strtotime($end_time) - 3600 * 24 * $days);
            }

            $last_day   = sapService::getInstance()->getLastDay(ltrim(date('m')), ltrim(date('Y')));
            $end_date_2 = date('Y-m-d H:i:s', (strtotime($start_date) + 3600 * 24 * $last_day));
            if (date('Y-m-d H:i:s') >= $end_date && date('Y-m-d H:i:s') < $end_date_2) {
                $start_time     = $start_date;
                $end_time       = date('Y-m-d H:i:s');
                $is_little_date = false;
            }

            $log .= 'is_little_date = ' . ($is_little_date ? 'true' : 'false') . PHP_EOL;

            if ($start_time < '2022-03-01 00:00:00') {
                $start_time = '2022-03-01 00:00:00';
            }

            $country_code = get_country_code();

            $t           = TaskService::getTranslation($country_code);
            $ledger_res  = LedgerAccountService::getInstance()->getList();
            $ledger_info = [];
            if ($ledger_res['code'] == ErrCode::$SUCCESS) {
                $ledger_info = array_column($ledger_res['data'], 'account', 'id');
            }

            $cost_company_id = EnvModel::getEnvByCode('purchase_sap_company_ids');
            if (empty($cost_company_id)) {
                throw new ValidationException('purchase_sap_company_ids 配置为空', ErrCode::$VALIDATE_ERROR);
            }

            $sap_company_ids = SysDepartmentModel::find([
                'conditions' => 'id in ({ids:array})',
                'bind' => ['ids' => explode(',', $cost_company_id)],
            ])->toArray();
            $sap_company_ids = array_column($sap_company_ids, 'sap_company_id', 'id') ?? [];

            //发送邮件
            $email      = EnumsService::getInstance()->getSettingEnvValueIds('reimbursement_email');
            $level_code = EnumsService::getInstance()->getSettingEnvValueIds('ordinary_sap_level_code');

            $limit_level_product = EnumsService::getInstance()->getSettingEnvValueMap('ordinary_sap_level_product_limit');

            // V21975 系统配置中增加暂估科目
            $budget_withholding_ledger_account = EnumsService::getInstance()->getSettingEnvValue('budget_withholding_ledger_account');

            // V21975 每个费用部门配置一个虚拟成本中心
            $budget_withholding_department_virtual_cost_center = EnumsService::getInstance()->getSettingEnvValueMap('budget_withholding_department_virtual_cost_center');

            $currency_sql = '';
            switch ($country_code) {
                case GlobalEnums::TH_COUNTRY_CODE:
                    $th_currency = GlobalEnums::CURRENCY_THB . ',' . GlobalEnums::CURRENCY_USD;
                    $currency_sql = "`currency` in ({$th_currency})  and";
                    break;
                case GlobalEnums::PH_COUNTRY_CODE:
                    $ph_currency = GlobalEnums::CURRENCY_PHP . ',' . GlobalEnums::CURRENCY_USD;
                    $currency_sql = "`currency` in ({$ph_currency})  and";
                    break;
                case GlobalEnums::MY_COUNTRY_CODE:
                    break;
                default:
            }

            $log .= "取数范围: 审批日期 {$start_time} ~ {$end_time}, 费用公司 {$cost_company_id}, 币种条件 {$currency_sql}, 创建时间 {$apply_date}" . PHP_EOL;

            $i = 0;
            $sql = "select `id`,`cost_company_id`,`apply_no`,`created_at`,`extra_message`,`voucher_abstract`,`currency`,`amount_total_actually`,`amount_total_vat`,`amount_total_have_tax`,`ticket_number`,`ticket_date`,`payee_type`,`remark`,`amount_discount`,`approved_at`,`cost_department_id` from ordinary_payment  where  `approved_at`>'{$start_time}' AND `approved_at`<'{$end_time }' and `created_at`>'{$apply_date}' and `approval_status` =3 and  " . $currency_sql . " cost_company_id in ({$cost_company_id}) and sync_sap in (0,3) limit {$i} ,100";

            $db_obj = $this->db_oa;
            $data = $db_obj->query($sql);
            $deal_payment_data = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);

            $finance_category = MaterialFinanceCategoryModel::find([
                'columns' => 'id,code',
                'bind'    => ['status' => 1],
            ])->toArray();

            $finance_category = array_column($finance_category, 'code', 'id');

            // 收款人的预算科目配置
            $payee_config = BaseService::payeeConfig();
            $personal_agent_budget_code_arr = $payee_config[3] ?? [];

            $log .= '个人代理的预算科目code: ' . implode(',', $personal_agent_budget_code_arr) . PHP_EOL;

            $batch_num = 0;
            while (!empty($deal_payment_data)) {
                $batch_num++;
                $log .= "待处理批次[{$batch_num}] " . count($deal_payment_data) . ' 条' . PHP_EOL;

                $send_data = [];

                $curr_batch_log = "当前处理批次[{$batch_num}]: " . PHP_EOL;
                foreach ($deal_payment_data as $key => $value) {
                    $curr_batch_log .= "待处理单据: {$value['apply_no']}, id={$value['id']}, 收款人类型={$value['payee_type']}, 审批时间-{$value['approved_at']}";

                    // 1-10号不是上个月数据跳过  11-31不是本月数据跳过
                    if ($is_little_date) {
                        if (ltrim(date('m', strtotime($value['approved_at']))) == ltrim(date('m'))) {
                            $curr_batch_log .= ', m = m, 跳过' . PHP_EOL;
                            continue;
                        }
                    } else {
                        if (ltrim(date('m', strtotime($value['approved_at']))) != ltrim(date('m'))) {
                            $curr_batch_log .= ', m != m, 跳过' . PHP_EOL;
                            continue;
                        }
                    }

                    //获取付款申请主表信息
                    $main_model = OrdinaryPayment::findFirst([
                        'id = :id:',
                        'bind' => ['id' => $value['id']],
                    ]);
                    if (empty($main_model)) {
                        $curr_batch_log .= ', 未找到主数据, 跳过' . PHP_EOL;
                        continue;
                    }

                    // 已成功同步过
                    if ($main_model->sync_sap == 1) {
                        $curr_batch_log .= ', 已被成功同步过, 跳过' . PHP_EOL;
                        continue;
                    }

                    //金额详情
                    $amount_detail = OrdinaryPaymentDetail::find([
                        'conditions' => 'ordinary_payment_id = :ordinary_payment_id:',
                        'bind'       => ['ordinary_payment_id' => $value['id']],
                    ])->toArray();
                    if (empty($amount_detail)) {
                        $curr_batch_log .= ', 未找到明细数据, 跳过' . PHP_EOL;
                        continue;
                    }

                    $personal_agent_sap_code = '';
                    if (BaseService::PAYEE_TYPE_2 == $value['payee_type']) {
                        $curr_batch_log .= ', 收款人类型是个人';

                        // 非马来的不推
                        if ($country_code != GlobalEnums::MY_COUNTRY_CODE) {
                            $curr_batch_log .= ', 非马来, 跳过' . PHP_EOL;
                            continue;
                        }

                        // 马来 未取到payee_budget_config 或 配置中未配置类型为个人代理的预算科目，则不推送
                        if (empty($personal_agent_budget_code_arr)) {
                            $curr_batch_log .= ', 马来, payee_budget_config不存在 或 未配置个人代理的科目, 跳过' . PHP_EOL;
                            continue;
                        }

                        // 马来 金额详情中的任意一行的付款分类 是 个人代理配置中的预算科目时，则推
                        $detail_budget_code_arr = array_unique(array_column($amount_detail, 'level_code'));
                        if (!empty(array_intersect($detail_budget_code_arr, $personal_agent_budget_code_arr))) {
                            // 当个人代理预算分类并且收款类型为个人时固定传输OA00745
                            $personal_agent_sap_code = 'OA00745';
                            $curr_batch_log .= ', 马来, 明细中的科目存在个人代理的科目, 推送' . PHP_EOL;
                        } else {
                            $curr_batch_log .= ', 马来, 明细中的科目 不存在 个人代理的科目, 跳过' . PHP_EOL;
                            continue;
                        }
                    } else {
                        $curr_batch_log .= ', 收款人类型是供应商' . PHP_EOL;;
                    }

                    if (empty($personal_agent_sap_code)) {
                        //供应商信息
                        $extend_obj      = OrdinaryPaymentExtend::getFirst([
                            'conditions' => 'ordinary_payment_id = :ordinary_payment_id:',
                            'bind'       => ['ordinary_payment_id' => $value['id']],
                            'columns'    => 'sap_supplier_no',
                        ]);
                        $sap_supplier_no = $extend_obj->sap_supplier_no;
                    } else {
                        $sap_supplier_no = $personal_agent_sap_code;
                    }

                    //判断付款分类=押金跳过
                    $is_deposit = false;

                    //是否关联了采购订单,关联了跳过
                    $is_pono = false;

                    foreach ($amount_detail as $k1 => $v1) {
                        if (in_array($v1['level_code'], $level_code)) {
                            $is_deposit = true;
                        }
                        if ($v1['level_code'] == self::LIMIT_LEVEL_CODE && get_country_code() == GlobalEnums::TH_COUNTRY_CODE && isset($finance_category[$v1['finance_category_id']]) && $finance_category[$v1['finance_category_id']] == self::FINANCE_CATEGORY_CODE) {
                            $is_deposit = true;
                        }

                        //不配置不走此逻辑
                        if (!empty($limit_level_product)) {
                            foreach ($limit_level_product as $v2) {
                                if (!empty($v2['level_code']) && !empty($v2['cost_type']) && $v1['level_code'] == $v2['level_code'] && $v1['product_id'] == $v2['cost_type']) {
                                    $is_deposit = true;
                                }
                            }
                        }
                        //任意一行关联了采购订单,不同步sap
                        if (!empty($v1['pono'])) {
                            $is_pono = true;
                        }
                    }

                    if ($is_deposit || $is_pono) {
                        $curr_batch_log .= ', 任一行的科目code 属于 ordinary_sap_level_code  或 任一行关联了采购订单, 跳过' . PHP_EOL;;
                        continue;
                    }

                    $request_data = [
                        'currency'              => $t->_(GlobalEnums::$currency_item[$value['currency']]),
                        'cost_company_id'       => $sap_company_ids[$value['cost_company_id']] ?? 'FEX01',
                        'order_no'              => $value['apply_no'],
                        'apply_date'            => date('Y-m-d', strtotime($value['created_at'])),
                        'ticket_number'         => $value['ticket_number'],
                        'ticket_date'           => date('Y-m-d', strtotime($value['ticket_date'])),
                        'note'                  => str_replace('&', '', $value['voucher_abstract']),
                        'send_time'             => date('Y-m-d', strtotime($value['approved_at'])),
                        'extra_message'         => $value['extra_message'],
                        'amount_total_actually' => $value['amount_total_actually'],
                        'amount_total_vat'      => $value['amount_total_vat'],
                        'amount_total_have_tax' => $value['amount_total_have_tax'],
                        'remark'                => $value['remark'],
                        'payee_type'            => $value['payee_type'],
                        'sap_supplier_no'       => $sap_supplier_no,
                        'items'                 => $this->deal_ordinary_item($amount_detail, $ledger_info, $finance_category),
                    ];

                    $return_data = SapService::getInstance()->ordinary_payment_to_sap($request_data);
                    if (isset($return_data['UUID']) && !empty($return_data['UUID'])) {
                        $curr_batch_log .= ', 抛数成功';

                        $update_data = [
                            'sync_sap' => 1,
                            'sap_uuid' => $return_data['UUID'],
                            'updated_at' => date('Y-m-d H:i:s')
                        ];

                        if ($main_model->i_update($update_data) === false) {
                            $curr_batch_log .= ', 状态更新异常' . PHP_EOL;
                        } else {
                            $curr_batch_log .= ', 状态更新成功' . PHP_EOL;
                        }

                        $row['no']   = $value['apply_no'];
                        $send_data[] = $row;

                        //21885-大于100行时总账接口
                        $this->sendOrdinaryPaymentVoucherToSap($main_model, $amount_detail, $t);

                        //21975-关联的预提单-抛红冲凭证
                        $this->sendOrdinaryPaymentRedVoucherToSap($main_model, $amount_detail, $sap_company_ids, $budget_withholding_ledger_account, $budget_withholding_department_virtual_cost_center, $t);
                    } else {
                        $curr_batch_log .= ', 抛数失败';

                        $note = '';
                        if (isset($return_data['log']['Item']) && !empty($return_data['log']['Item'])) {
                            $note = array_column($return_data['log']['Item'], 'Note');
                            $note = implode(',', $note);
                        }

                        $update_data = [
                            'sync_sap' => 2,
                            'updated_at' => date('Y-m-d H:i:s'),
                            'sap_note' => $note
                        ];
                        if ($main_model->i_update($update_data) === false) {
                            $curr_batch_log .= ', 状态更新异常' . PHP_EOL;
                        } else {
                            $curr_batch_log .= ', 状态更新成功' . PHP_EOL;
                        }
                    }

                    sleep(1);
                }

                // 每批次记录日志
                $this->logger->info($curr_batch_log);
                $log .= $curr_batch_log;

                //拼接 html  发送邮件
                if (!empty($send_data) && !empty($email)) {
                    $title = "OA SAP document creation reminder";
                    $html  = $this->format_html($send_data);
                    $this->mailer->sendAsync($email, $title, $html);
                }

                sleep(1);

                // 下一批次
                $i   += 100;
                $sql = "select `id`,`cost_company_id`,`apply_no`,`created_at`,`extra_message`,`voucher_abstract`,`currency`,`amount_total_actually`,`amount_total_vat`,`amount_total_have_tax`,`ticket_number`,`ticket_date`,`payee_type`,`remark`,`amount_discount`,`approved_at`,`cost_department_id` from ordinary_payment  where  `approved_at`>'{$start_time}' AND `approved_at`<'{$end_time }' and `created_at`>'{$apply_date}' and `approval_status` =3 and " . $currency_sql . " cost_company_id in ({$cost_company_id}) and sync_sap in (0,3) limit {$i} ,100";

                $data              = $db_obj->query($sql);
                $deal_payment_data = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            }

        } catch (ValidationException $e) {
            $logger_type = 'notice';
            $log .= $e->getMessage() . PHP_EOL;

        } catch (Exception $e) {
            $logger_type = 'error';
            $log .= $e->getMessage() . PHP_EOL;
        }

        $this->clearLock(__METHOD__);
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $this->logger->$logger_type($log);
        exit($log);
    }

    //整理 sap 订单提醒邮件内容
    public function format_html($data)
    {
        $html = "<p>Dear all,<?p></br></br>";
        $html .= "<p>Below data are posted in SAP, please have a check! Thanks!</p></br></br>";
        $html .= "<p>以下数据已过账到SAP，请检查！谢谢</p>";

        $html .= "<table border='1'><tr><td>OA number</td></tr>";
        foreach ($data as $da) {
            $html .= "<tr><td>{$da['no']}</td></tr>";
        }

        $html .= "</table>";
        return $html;

    }

    /**
     * 21885-大于100行时传输总账至SAP
     * @param object $main_model 普通付款单据对象
     * @param array $amount_detail  普通付款单据明细
     * @param array $t 语言包
     * @return bool
     */
    public function sendOrdinaryPaymentVoucherToSap($main_model, $amount_detail, $t)
    {
        $amount_detail_count = count($amount_detail);
        if ($amount_detail_count <= 100) {
            return true;
        }

        $ledger_account_ids      = array_values(array_unique(array_filter(array_column($amount_detail, 'ledger_account_id'))));
        $ledger_account          = LedgerAccountRepository::getInstance()->getLedgerAccountByIds($ledger_account_ids);
        $department_info         = (new DepartmentRepository())->getDepartmentDetail($main_model->cost_company_id, 2);
        $sap_virtual_cost_center = EnumsService::getInstance()->getSettingEnvValue('sap_virtual_cost_center');

        //按照每450行金额详情分组进行抛数
        $amount_detail_child       = array_chunk($amount_detail, 450);
        $amount_detail_child_count = count($amount_detail_child);
        for ($i = 0; $i < $amount_detail_child_count; $i ++) {
            $borrower                     = [];
            $borrower_amount_no_tax_total = 0;
            foreach ($amount_detail_child[$i] as $detail) {
                $borrower[]                   = [
                    'ledger_account_name' => $ledger_account[$detail['ledger_account_id']]['account'] ?? '',
                    //明细行上核算科目字段对应的科目编号
                    'voucher_description' => $detail['voucher_description'],
                    //凭证描述
                    'amount_no_tax'       => $detail['amount_no_tax'],
                    //明细行的不含税金额
                    'cost_center_code'    => $detail['cost_center_name'],
                    //明细行上存储的成本中心
                ];
                $borrower_amount_no_tax_total = bcadd($borrower_amount_no_tax_total, $detail['amount_no_tax'], 2);
            }

            $request_data = [
                'cost_company_id' => $department_info ? $department_info['sap_company_id'] : 'FEX01',
                'apply_no'        => $main_model->apply_no,
                'approved_at'     => date('Y-m-d', strtotime($main_model->approved_at)),
                'currency'        => $t->_(GlobalEnums::$currency_item[$main_model->currency]),
                'borrower'        => $borrower,//借方
                'lender'          => [ //贷方
                    'ledger_account_name'   => $borrower[0]['ledger_account_name'] ?? '', //任意一行明细行上核算科目字段对应的科目编号
                    'voucher_description'   => $borrower[0]['voucher_description'] ?? '', //任意一行明细行上的凭证描述字段
                    'amount_total_actually' => $borrower_amount_no_tax_total, //借方明细行所有不含税金额的合计
                    'cost_center_code'      => $sap_virtual_cost_center,//配置里的sap成本中心
                ],
            ];
            SapService::getInstance()->ordinary_payment_general_ledger_voucher_to_sap($request_data);
        }
        return true;
    }

    /**
     * 21975-关联的预提单-抛红冲凭证
     * @param object $main_model 普通付款单据对象
     * @param array $ordinary_payment_detail  普通付款单据明细
     * @param array $sap_company_ids sap公司
     * @param string $budget_withholding_ledger_account 系统配置中增加暂估科目
     * @param array $budget_withholding_department_virtual_cost_center 每个费用部门配置一个虚拟成本中心
     * @param array $t 语言包
     * @return bool
     * @throws ValidationException
     */
    public function sendOrdinaryPaymentRedVoucherToSap($main_model, $ordinary_payment_detail, $sap_company_ids, $budget_withholding_ledger_account, $budget_withholding_department_virtual_cost_center, $t)
    {
        //未关联预提单则不抛
        if (empty($main_model->budget_withholding_id)) {
            return true;
        }

        //获取关联的预提单信息
        $budget_withholding_info = BudgetWithholdingService::getInstance()->getBudgetWithholdingInfoById($main_model->budget_withholding_id);

        //获取关联的预提单明细行信息
        $budget_withholding_detail = $budget_withholding_info->getDetails()->toArray();

        //预算科目对应的核算科目
        $ledger_account = LedgerAccountRepository::getInstance()->getBudgetLedgerAccount([$budget_withholding_info->budget_id]);

        //主表 - 红冲金额相反数
        $red_voucher_amount = bcsub(0, $main_model->red_voucher_amount, 2);

        //获取默认币种
        $default_currency = (new EnumsService())->getSysCurrencyInfo();
        //凭证基础参数组
        $request_data = [
            'cost_company_id' => $sap_company_ids[$main_model->cost_company_id] ?? 'FEX01',
            'apply_no'        => $budget_withholding_info->no,
            'approved_at'     => date('Y-m-d', strtotime($main_model->approved_at)),
            'currency'        => $t->_(GlobalEnums::$currency_item[$default_currency['code']]),
            'borrower'        => [],//借方
            'lender'          => [ //贷方
                'ledger_account_name'   => $budget_withholding_ledger_account,//系统配置中增加暂估科目
                'voucher_description'   => $budget_withholding_info->no, //预提单号
                'amount_total_actually' => $red_voucher_amount,
                'cost_center_code'      => '',
            ],
        ];

        //借方成本中心
        $borrower_cost_center_code = BudgetWithholdingService::getInstance()->getBorrowerCostCenterCode($budget_withholding_info, $budget_withholding_department_virtual_cost_center);

        //预提单 - 有明细行 - 按明细行抛红冲凭证
        if ($budget_withholding_detail) {
            $amount_detail = [];
            foreach ($ordinary_payment_detail as $item) {
                if ($item['is_send_red_voucher'] == BudgetWithholdingEnums::IS_SEND_RED_VOUCHER_NO || !in_array($item['red_voucher_sync_sap'], [0, 3])) {
                    continue;
                }
                $amount_detail[] = $item;
            }

            //若普通付款 - 明细行 - 无需抛送红冲凭证 - 直接返回不处理
            if (empty($amount_detail)) {
                return [];
            }

            //按照每450行金额详情分组进行抛数
            $amount_detail_child       = array_chunk($amount_detail, 450);
            $amount_detail_child_count = count($amount_detail_child);
            for ($i = 0; $i < $amount_detail_child_count; $i ++) {
                $borrower                     = [];
                $borrower_red_voucher_amount_total = 0;
                foreach ($amount_detail_child[$i] as $detail) {
                    $red_voucher_amount = bcsub(0, $detail['red_voucher_amount'], 2);
                    $borrower[]                   = [
                        'ledger_account_name' => $ledger_account[$budget_withholding_info->budget_id] ?? '', //预算科目对应的核算科目
                        'voucher_description' => $budget_withholding_info->no, //预提单号
                        'amount_no_tax'       => $red_voucher_amount, //明细行 - 红冲金额相反数
                        'cost_center_code'    => $detail['cost_center_name'],//明细行上存储的成本中心
                    ];
                    $borrower_red_voucher_amount_total = bcadd($borrower_red_voucher_amount_total, $red_voucher_amount, 2);
                }
                $request_data['borrower'] = $borrower;
                $request_data['lender']['amount_total_actually'] = $borrower_red_voucher_amount_total;
                $this->sendGeneralLedgerRedVoucherToSap($main_model, $request_data, $amount_detail_child[$i]);
            }
        } else {
            //预提单 - 无明细行 - 按表头抛红冲凭证
            if ($main_model->is_send_red_voucher == BudgetWithholdingEnums::IS_SEND_RED_VOUCHER_NO || !in_array($main_model->red_voucher_sync_sap, [0, 3])) {
                //若普通付款单据 - 无需抛送红冲凭证 - 直接返回不处理
                return true;
            }
            $request_data['borrower'] = [
                [
                    'ledger_account_name' => $ledger_account[$budget_withholding_info->budget_id] ?? '', //预算科目对应的核算科目
                    'voucher_description' => $budget_withholding_info->no, //预提单号
                    'amount_no_tax'       => $red_voucher_amount,
                    'cost_center_code'    => $borrower_cost_center_code,//借方成本中心
                ]
            ];

            $this->sendGeneralLedgerRedVoucherToSap($main_model, $request_data);
        }
        return true;
    }

    /**
     * 抛普通付款关联预提单总账凭证
     * @param object $main_model 普通付款单据对象
     * @param array $request_data sap 请求参数组
     * @param array $detail 费用明细组
     */
    private function sendGeneralLedgerRedVoucherToSap(object $main_model, array $request_data, array $detail = [])
    {
        $detail_z = 0;
        while (empty($return_data) && $detail_z < 1) {
            $detail_z++;
            $request_data['ordinary_payment_no']     = $main_model->apply_no;
            $return_data          = SapService::getInstance()->ordinary_payment_general_ledger_voucher_to_sap($request_data, 16);
            $red_voucher_sync_sap = (isset($return_data['ID']) && !empty($return_data['ID'])) ? 1 : 2;
            $detail_ids           = [];
            if ($detail) {
                $detail_ids = implode(',', array_column($detail, 'id'));
                $db         = $this->getDI()->get('db_oa');
                $bool       = $db->updateAsDict(
                    (new OrdinaryPaymentDetail())->getSource(),
                    [
                        'red_voucher_sync_sap' => $red_voucher_sync_sap,
                        'updated_at'           => date('Y-m-d H:i:s'),
                    ],
                    ['conditions' => "id IN ($detail_ids)"]
                );
            } else {
                $update_data = [
                    'red_voucher_sync_sap' => $red_voucher_sync_sap,
                    'updated_at'           => date('Y-m-d H:i:s'),
                ];
                $bool        = $main_model->i_update($update_data);
            }
            if ($bool === false) {
                $this->logger->warning('抛普通付款关联预提单总账凭证 - ' . ($detail_ids ? '按明细行抛红冲凭证，明细行ID组：【' . json_encode($detail_ids,
                            JSON_UNESCAPED_UNICODE) . '】' : '按表头抛红冲凭证') . '； Sap抛送结果为：【' . ($red_voucher_sync_sap == 1 ? '成功' : '失败') . '】； 变更red_voucher_sync_sap为：' . $red_voucher_sync_sap . '；失败，请观察数据，人工介入处理');
            }
            sleep(2);
        }
    }

    // 同步普通付款历史vat税率字段
    public function ordinary_vat_rateAction()
    {

        $process_name = str_replace('Action', '', __FUNCTION__);
        $this->check_process($process_name);
        echo 'begin: ' . date('Y-m-d H:i:s') . "\n";

        $total_success = 0;
        $total_failed  = 0;
        $db_obj        = $this->db_oa;
        try {
            $sql               = "select opd.id,opd.amount_no_tax,opd.amount_vat from ordinary_payment op left join `ordinary_payment_detail` opd on op.id=opd.ordinary_payment_id where op.approval_status=1";
            $data              = $db_obj->query($sql);
            $deal_payment_data = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            $total             = count($deal_payment_data);
            foreach ($deal_payment_data as $key => $value) {
                $main_model = OrdinaryPaymentDetail::findFirst([
                    'id = :id:',
                    'bind' => ['id' => $value['id']],
                ]);
                if (empty($main_model)) {
                    continue;
                }
                $vat_rate = !empty($value['amount_no_tax']) ? sprintf("%.0f", $value['amount_vat'] * 100 / $value['amount_no_tax']) : 0;
                $bool     = $main_model->i_update(['vat_rate' => $vat_rate, 'updated_at' => date('Y-m-d H:i:s')]);
                if ($bool === false) {
                    $total_failed++;
                } else {
                    $total_success++;
                }
            }
            echo '总记录数：' . $total . ', 成功更新记录数：' . $total_success . ', 失败更新记录数：' . $total_failed;
            echo PHP_EOL;
        } catch (Exception $e) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('ordinary-payment-sap-exception: ' . $e->getMessage());
            print_r($e->getMessage());
            exit;
        }
    }


    /**
     * 组装item sap所需数据格式
     * */
    public function deal_th_ordinary_item($item, $ledger_info)
    {
        $lang          = 'en';
        $budgetIds     = array_column($item, 'budget_id'); //科目IDs（付款分类）
        $productIds    = array_column($item, 'product_id');//产品IDs(费用类型）
        $budgetService = new BudgetService();
        $budgets       = $budgetService->budgetObjectList($budgetIds);
        $products      = $budgetService->budgetObjectProductList($productIds);

        $wht_cat_map = EnumsService::getInstance()->getWhtRateCategoryMap(0);
        foreach ($item as $key => &$value) {
            $value['budget_name']       = isset($budgets[$value['budget_id']]) ? $budgets[$value['budget_id']]['name_' . $lang] : '';
            $value['product_name']      = isset($products[$value['product_id']]) ? $products[$value['product_id']]['name_' . $lang] : '';
            $value['wht_category_name'] = $wht_cat_map[$value['wht_category']] ?? 0;
            $value['wht_rate_name']     = $value['wht_rate'] . '%';
            //核算科目
            $value['ledger_account_name'] = $ledger_info[$value['ledger_account_id']] ?? '';
            $value['wht_code']            = sapService::getInstance()->wht_code($value['wht_category'], $value['wht_rate']);
            $value['vat_code']            = sapService::getInstance()->vat_code($value['vat_rate'], $value['deductible_vat_tax']);

        }

        return $item;
    }


    /**
     * 泰国普通付款同步sap
     * 紧急需求处理特殊数据
     * */
    public function th_ordinary_payment_sapAction($params)
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $this->check_process($process_name);
        echo 'begin: ' . date('Y-m-d H:i:s');
        echo PHP_EOL;

        $db_obj = $this->db_oa;
        try {
            $pay_start_date = '2022-03-01 00:00:00';
            $pay_end_date   = '2022-04-01 00:00:00';
            $approved_date  = '2022-03-01 00:00:00';

            $t           = TaskService::getTranslation(get_country_code());
            $ledger_res  = LedgerAccountService::getInstance()->getList();
            $ledger_info = '';
            if ($ledger_res['code'] == ErrCode::$SUCCESS) {
                $ledger_info = array_column($ledger_res['data'], 'account', 'id');
            }

            $cost_company_id = EnvModel::getEnvByCode('purchase_sap_company_ids');

            $sap_company_ids = SysDepartmentModel::find(
                [
                    'conditions' => 'id in ({ids:array})',
                    'bind'       => ['ids' => explode(',', $cost_company_id)],
                ]
            )->toArray();
            $sap_company_ids = array_column($sap_company_ids, 'sap_company_id', 'id') ?? [];
            //发送邮件 目前只有泰国
            $email = EnvModel::getEnvByCode('reimbursement_email');
            $email = explode(',', $email);

            //默认币种
            $default_currency  = (new EnumsService())->getSysCurrencyInfo();

            $i                 = 0;
            $sql               = "select a.`id`,a.`cost_company_id`,a.`apply_no`,a.`created_at`,a.`extra_message`,a.`voucher_abstract`,a.`currency`,a.`amount_total_actually`,a.`amount_total_vat`,a.`amount_total_have_tax`,a.`ticket_number`,a.`ticket_date`,a.`payee_type`,a.`remark`,a.`amount_discount`,a.`approved_at`,a.`cost_department_id`,b.pay_bk_flow_date from ordinary_payment  as a LEFT JOIN `ordinary_payment_extend` as b on a.id=b.`ordinary_payment_id`  where  a.`approved_at`<'{$approved_date}'  and b.`pay_bk_flow_date`>'{$pay_start_date}' and b.`pay_bk_flow_date`<='{$pay_end_date}' and a.`approval_status` =3 and a.`currency` =1  and a.`amount_discount` =0 and a.cost_company_id in (1) and a.sync_sap in(0,3) limit 0, 1000";
            $data              = $db_obj->query($sql);
            $deal_payment_data = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);

            //获取默认币种
            $default_currency = (new EnumsService())->getSysCurrencyInfo();

            while (!empty($deal_payment_data)) {
                $send_data = [];

                foreach ($deal_payment_data as $key => $value) {
                    $request_data = [];
                    $return_data  = [];

                    //折扣>0 不传输
                    if ($value['amount_discount'] > 0) {
                        continue;
                    }

                    //获取付款申请主表信息
                    $main_model = OrdinaryPayment::findFirst([
                        'id = :id:',
                        'bind' => ['id' => $value['id']],
                    ]);
                    if (empty($main_model)) {
                        continue;
                    }

                    if (self::COST_DEPARTMENT_ID == $value['cost_department_id']) {//费用部门Flash Home Settlement 不传输
                        continue;
                    }

                    if (BaseService::PAYEE_TYPE_2 == $value['payee_type']) {
                        continue;//个人跳过
                    }

                    //供应商信息
                    $extend_obj      = OrdinaryPaymentExtend::getFirst([
                        'conditions' => 'ordinary_payment_id = :ordinary_payment_id:',
                        'bind'       => ['ordinary_payment_id' => $value['id']],
                        'columns'    => 'sap_supplier_no',
                    ]);
                    $sap_supplier_no = $extend_obj->sap_supplier_no;

                    //金额详情
                    $amount_detail = OrdinaryPaymentDetail::find([
                        'conditions' => 'ordinary_payment_id = :ordinary_payment_id:',
                        'bind'       => ['ordinary_payment_id' => $value['id']],
                    ]);
                    if (empty($amount_detail)) {
                        continue;
                    }

                    $amount_detail = $amount_detail->toArray();
                    //判断付款分类=押金跳过
                    $is_deposit = false;

                    foreach ($amount_detail as $k1 => $v1) {
                        if (in_array($v1['level_code'], self::$level_code_arr)) {
                            $is_deposit = true;
                        }
                    }
                    if ($is_deposit) {
                        continue;
                    }

                    $request_data = [
                        'currency'              => $t->_(GlobalEnums::$currency_item[$value['currency']]),
                        'cost_company_id'       => $sap_company_ids[$value['cost_company_id']] ?? 'FEX01',
                        'order_no'              => $value['apply_no'],
                        'apply_date'            => date('Y-m-d', strtotime($value['created_at'])),
                        'ticket_number'         => $value['ticket_number'],
                        'ticket_date'           => date('Y-m-d', strtotime($value['ticket_date'])),
                        'note'                  => str_replace('&', '', $value['voucher_abstract']),
                        'send_time'             => date('Y-m-d', strtotime($value['pay_bk_flow_date'])),
                        'extra_message'         => $value['extra_message'],
                        'amount_total_actually' => $value['amount_total_actually'],
                        'amount_total_vat'      => $value['amount_total_vat'],
                        'amount_total_have_tax' => $value['amount_total_have_tax'],
                        'remark'                => $value['remark'],
                        'payee_type'            => $value['payee_type'],
                        'sap_supplier_no'       => $sap_supplier_no,
                        'items'                 => $this->deal_ordinary_item($amount_detail, $ledger_info),
                    ];

                    $z            = 0;

                    echo "ordinary_payment_order id={$value['id']} [{$value['apply_no']}] " . PHP_EOL;

                    $return_data = SapService::getInstance()->ordinary_payment_th_to_sap($request_data);
                    if (isset($return_data['UUID']) && !empty($return_data['UUID'])) {
                        $main_model->i_update(['sync_sap' => 1, 'sap_uuid' => $return_data['UUID'] ?? '', 'updated_at' => date('Y-m-d H:i:s')]);
                        $row['no']   = $value['apply_no'];
                        $send_data[] = $row;
                        echo 'success ordinary_payment_order id:' . $value['id'] . PHP_EOL;
                    } else {
                        if (isset($return_data['log']['Item']) && !empty($return_data['log']['Item'])) {
                            $note = '';
                            $note = array_column($return_data['log']['Item'], 'Note');
                            $note = implode(',', $note);
                        }

                        $main_model->i_update(['sync_sap' => 2, 'updated_at' => date('Y-m-d H:i:s'), 'sap_note' => $note]);
                        echo 'fail ordinary_payment_order id:' . $value['id'] . PHP_EOL;
                    }

                    sleep(1);
                }

                //拼接 html  发送邮件
                if (!empty($send_data) && !empty($email)) {
                    $title = "OA SAP document creation reminder";
                    $html  = $this->format_html($send_data);
                    $this->mailer->sendAsync($email, $title, $html);
                }

                sleep(1);
                $i   += 100;
                $sql = "select a.`id`,a.`cost_company_id`,a.`apply_no`,a.`created_at`,a.`extra_message`,a.`voucher_abstract`,a.`currency`,a.`amount_total_actually`,a.`amount_total_vat`,a.`amount_total_have_tax`,a.`ticket_number`,a.`ticket_date`,a.`payee_type`,a.`remark`,a.`amount_discount`,a.`approved_at`,a.`cost_department_id`,b.pay_bk_flow_date from ordinary_payment  as a LEFT JOIN `ordinary_payment_extend` as b on a.id=b.`ordinary_payment_id`  where  a.`approved_at`<'{$approved_date}'  and b.`pay_bk_flow_date`>'{$pay_start_date}' and b.`pay_bk_flow_date`<='{$pay_end_date}' and a.`approval_status` =3 and a.`currency` =1  and a.`amount_discount` =0 and a.cost_company_id in (1) and a.sync_sap in(0,3) limit 0, 1000";

                $data              = $db_obj->query($sql);
                $deal_payment_data = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            }
            echo 'end: ' . date('Y-m-d H:i:s') . PHP_EOL;
        } catch (Exception $e) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('ordinary-payment-sap-exception: ' . $e->getMessage());
            print_r($e->getMessage());
        }

        exit();
    }

    /**
     * 补历史CostStoreId字段数据
     * 注意需要在sql执行完毕在执行一次性脚本
     * @return void
     */
    public function deal_detail_cost_store_idAction()
    {
        OrdinaryPaymentDetailService::getInstance()->dealHistoryCostStoreId();
    }
}