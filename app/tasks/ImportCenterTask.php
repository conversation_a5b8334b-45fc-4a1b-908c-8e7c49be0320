<?php

use App\Library\BaseController;
use App\Library\Enums\BankFlowEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\ImportCenterEnums;
use App\Library\Enums\DepositEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\OssHelper;
use App\Library\RedisClient;
use App\Library\Validation\ValidationException;
use App\Models\oa\ImportTaskModel;
use App\Modules\BankFlow\Models\BankAccountModel;
use App\Modules\BankFlow\Services\BaseService;
use App\Modules\BankFlow\Services\InitFlowService;
use App\Modules\BankFlow\Services\ListService;
use App\Modules\Common\Services\ImportCenterService;
use App\Modules\JobTransfer\Services\JobTransferService;
use App\Modules\Hc\Services\BudgetService;
use App\Modules\Material\Services\LeaveAssetsExcelService;
use App\Modules\Material\Services\StandardBatchService;
use App\Modules\Material\Services\WmsApplyService;
use App\Modules\Organization\Services\JobService;
use App\Modules\Shop\Services\GoodsManagementService;
use App\Modules\User\Services\UserService;
use App\Modules\Material\Services\AssetApplyService;
use App\Modules\Material\Services\AssetOutStorageService;
use App\Modules\Material\Services\WarehouseDivisionRuleService;
use App\Modules\Warehouse\Services\RequirementService;
use App\Util\RedisKey;
use App\Modules\Deposit\Services\DepositService;
use App\Modules\Payment\Services\StoreRentingAddService;

class ImportCenterTask extends BaseTask
{

    /**
     * 批量导入barcode新增
     * @date 2022/9/7
     * php app/cli.php import_center barcode_add
     */
    public function barcode_addAction()
    {
        $this->checkLock(__METHOD__);
        $log = '';
        // 不能开启事务, 时间长数据库连接自动关闭, 事务提交会报错(MySQL server has gone away )
        try {
            // 查询当前是否存在待处理的任务
            // 查询最近一次待下载任务
            $barcode_data = ImportTaskModel::findFirst([
                'conditions' => 'type = :type: and status = :status: and is_deleted = :is_deleted:',
                'bind' => [
                    'type' => ImportCenterEnums::TYPE_BARCODE_ADD,
                    'status' => ImportCenterEnums::STATUS_WAITING,
                    'is_deleted' => ImportCenterEnums::NOT_DELETED
                ],
                'order' => 'id asc',
            ]);
            if (!$barcode_data) {
                throw new ValidationException('没有等待导入的任务', ErrCode::$VALIDATE_ERROR);
            }

            // 存在下载任务,开始执行
            // 1. 先改为处理中
            $barcode_data->status = ImportCenterEnums::STATUS_DOING;
            if ($barcode_data->save() === false) {
                throw new Exception('下载任务启动异常: 更新为"处理中"失败', ErrCode::$BUSINESS_ERROR);
            }

            // 1. 下载文件
            $standard_batch_service = StandardBatchService::getInstance();
            $standard_batch_service::setLanguage($barcode_data->language ?? 'en');
            $excel_data = get_oss_file_excel_data($barcode_data->file_url, [\Vtiful\Kernel\Excel::TYPE_STRING]);
            // 2. 读取每行单元格数据时，可指定每个单元格数据类型进行读取
            if (empty($excel_data)) {
                // 正常情况下进入任务是会校验, 所以这种空文件基本不会出现, 如果出现记录异常
                throw new Exception('file is empty');
            }
            // 3. 组合和controllers一样的user信息
            $us = new UserService();
            $user = $us->getUserById($barcode_data->staff_info_id);
            $base_controller = new BaseController();
            $user_info = $base_controller->format_user($user);
            // 3. 批量导入
            // 提取Excel Header
            $excel_header_column_one = array_shift($excel_data);
            $excel_header_column_two = array_shift($excel_data);

            $res = $standard_batch_service->barcodeBatchUploadAdd($excel_data, $user_info);
            // 4. 生成结果文件
            if ($res['code'] == ErrCode::$SUCCESS) {
                // 处理成功, 生成上传结果文件
                $upload_result_data = $res['data']['excel_data'];
                array_unshift($upload_result_data, $excel_header_column_two);
                $excel_extra_config = [
                    'file_name' => date('YmdHis') . '_' . $user_info['id'] . '_import_result.xlsx',
                    //'file_desc' => $excel_header_column_one,
                    'end_column_char' => 'U',
                    'column_width' => 15,
                ];
                $path = self::customizeExcelToFile($excel_header_column_one, $upload_result_data, $excel_extra_config);
                $oss_result = OssHelper::uploadFile($path);
            }

            // 5. 写入结果
            // 再查一次数据,长时间处理, 上边的数据库连接会自动关闭,保存报错
            $after_barcode_data = ImportTaskModel::findFirst($barcode_data->id);
            if (!$after_barcode_data || $after_barcode_data->status != ImportCenterEnums::STATUS_DOING) {
                throw new Exception('result save error, not found task');
            }
            $after_barcode_data->success_num = $res['data']['success_num'];
            $after_barcode_data->error_num = $res['data']['failed_sum'];
            $after_barcode_data->result_file_url = $oss_result['object_url'] ?? '';
            $after_barcode_data->status = ImportCenterEnums::STATUS_DONE;
            $after_barcode_data->finished_at = date('Y-m-d H:i:s');
            if ($after_barcode_data->save() === false) {
                // 正常情况下进入任务是会校验, 所以这种空文件基本不会出现, 如果出现记录异常
                throw new Exception('result save error, message='.get_data_object_error_msg($after_barcode_data));
            }
            $this->logger->info('barcode_add_success: result=' . json_encode($res ?? []));
            $log .= 'barcode add success' . PHP_EOL;
        } catch (ValidationException $e) {
            $log .= '任务结果: ' . $e->getMessage() . PHP_EOL;
        } catch (Exception $e) {
            $this->logger->error('barcode_add_error: message=' . $e->getMessage() . '; trace=' . $e->getTraceAsString());

            // 导入结果置为失败
            $after_barcode_data = ImportTaskModel::findFirst($barcode_data->id);
            if (!$after_barcode_data || $after_barcode_data->status != ImportCenterEnums::STATUS_DOING) {
                echo 'error: result failed save error, not found task' . PHP_EOL;
                $this->clearLock(__METHOD__);
                exit();
            }
            $after_barcode_data->status = ImportCenterEnums::STATUS_FAILED;
            $after_barcode_data->finished_at = date('Y-m-d H:i:s');
            if ($after_barcode_data->save() === false) {
                $this->logger->error('barcode_add_set_failed_error: ' . get_data_object_error_msg($after_barcode_data));
            }

            $log .= 'error:' . $e->getMessage() . PHP_EOL;
        }

        $this->clearLock(__METHOD__);
        exit($log);
    }

    /**
     * 批量导入barcode修改
     * @date 2022/9/7
     * php app/cli.php import_center barcode_update batch_scrap
     */
    public function barcode_updateAction()
    {
        $this->checkLock(__METHOD__);
        $log = '';
        // 不能开启事务, 时间长数据库连接自动关闭, 事务提交会报错(MySQL server has gone away )
        try {
            // 查询当前是否存在待处理的任务
            // 查询最近一次待下载任务
            $barcode_data = ImportTaskModel::findFirst([
                'conditions' => 'type = :type: and status = :status: and is_deleted = :is_deleted:',
                'bind' => [
                    'type' => ImportCenterEnums::TYPE_BARCODE_UPDATE,
                    'status' => ImportCenterEnums::STATUS_WAITING,
                    'is_deleted' => ImportCenterEnums::NOT_DELETED
                ],
                'order' => 'id asc',
            ]);
            if (!$barcode_data) {
                throw new ValidationException('没有等待导入的任务', ErrCode::$VALIDATE_ERROR);
            }

            // 存在下载任务,开始执行
            // 1. 先改为处理中
            $barcode_data->status = ImportCenterEnums::STATUS_DOING;
            if ($barcode_data->save() === false) {
                throw new ValidationException('下载任务启动异常: 更新为"处理中"失败', ErrCode::$BUSINESS_ERROR);
            }

            // 1. 下载文件
            $standard_batch_service = StandardBatchService::getInstance();
            $standard_batch_service::setLanguage($barcode_data->language ?? 'en');
            $excel_data = get_oss_file_excel_data($barcode_data->file_url, [\Vtiful\Kernel\Excel::TYPE_STRING]);
            // 2. 读取每行单元格数据时，可指定每个单元格数据类型进行读取
            if (empty($excel_data)) {
                // 正常情况下进入任务是会校验, 所以这种空文件基本不会出现, 如果出现记录异常
                throw new Exception('file is empty');
            }
            // 3. 组合和controllers一样的user信息
            $us = new UserService();
            $user = $us->getUserById($barcode_data->staff_info_id);
            $base_controller = new BaseController();
            $user_info = $base_controller->format_user($user);
            // 3. 批量导入
            // 提取Excel Header
            $excel_header_column_one = array_shift($excel_data);
            $excel_header_column_two = array_shift($excel_data);
            $res = $standard_batch_service->barcodeBatchUploadEdit($excel_data, $user_info);
            // 4. 生成结果文件
            if ($res['code'] == ErrCode::$SUCCESS) {
                // 处理成功, 生成上传结果文件
                $upload_result_data = $res['data']['excel_data'];
                array_unshift($upload_result_data, $excel_header_column_two);
                $excel_extra_config = [
                    'file_name' => date('YmdHis') . '_' . $user_info['id'] . '_import_result.xlsx',
                    //'file_desc' => $excel_header_column_one,
                    'end_column_char' => 'U',
                    'column_width' => 15,
                ];
                $path = self::customizeExcelToFile($excel_header_column_one, $upload_result_data, $excel_extra_config);
                $oss_result = OssHelper::uploadFile($path);
            }

            // 5. 写入结果
            // 再查一次数据,长时间处理, 上边的数据库连接会自动关闭,保存报错
            $after_barcode_data = ImportTaskModel::findFirst($barcode_data->id);
            if (!$after_barcode_data || $after_barcode_data->status != ImportCenterEnums::STATUS_DOING) {
                throw new Exception('result save error, not found task');
            }

            $after_barcode_data->success_num = $res['data']['success_num'];
            $after_barcode_data->error_num = $res['data']['failed_sum'];
            $after_barcode_data->result_file_url = $oss_result['object_url'] ?? '';
            $after_barcode_data->status = ImportCenterEnums::STATUS_DONE;
            $after_barcode_data->finished_at = date('Y-m-d H:i:s');
            if ($after_barcode_data->save() === false) {
                // 正常情况下进入任务是会校验, 所以这种空文件基本不会出现, 如果出现记录异常
                throw new Exception('result save error, message='.get_data_object_error_msg($after_barcode_data));
            }
            $this->logger->info('barcode_update_success: result=' . json_encode($res ?? []));
            $log .= 'barcode update success' . PHP_EOL;
        } catch (ValidationException $e) {
            $log .= '任务结果: ' . $e->getMessage() . PHP_EOL;
        } catch (Exception $e) {
            $this->logger->error('barcode_update_error: message=' . $e->getMessage() . '; trace=' . $e->getTraceAsString());

            // 导入结果置为失败
            $after_barcode_data = ImportTaskModel::findFirst($barcode_data->id);
            if (!$after_barcode_data || $after_barcode_data->status != ImportCenterEnums::STATUS_DOING) {
                echo 'error: result failed save error, not found task' . PHP_EOL;
                $this->clearLock(__METHOD__);
                exit();
            }
            $after_barcode_data->status = ImportCenterEnums::STATUS_FAILED;
            $after_barcode_data->finished_at = date('Y-m-d H:i:s');
            if ($after_barcode_data->save() === false) {
                $this->logger->error('barcode_update_set_failed_error: ' . get_data_object_error_msg($after_barcode_data));
            }

            $log .= 'error:' . $e->getMessage() . PHP_EOL;
        }

        $this->clearLock(__METHOD__);
        exit($log);
    }

    /**
     * 批量导入HC计划人数
     * @date 2022/11/08
     * php app/cli.php import_center importHcPlanNum
     */
    public function import_hc_plan_numAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $this->check_process($process_name);
        // 查询当前是否存在待处理的任务
        // 查询最近一次待下载任务
        $importTask = ImportTaskModel::findFirst([
            'conditions' => 'type = :type: and status = :status: and is_deleted = :is_deleted:',
            'bind' => [
                'type' => ImportCenterEnums::TYPE_HC_PLAN_NUMBER,
                'status' => ImportCenterEnums::STATUS_WAITING,
                'is_deleted' => ImportCenterEnums::NOT_DELETED
            ],
            'order' => 'id asc',
        ]);
        if (empty($importTask)) {
            echo '没有等待导入的任务:' . PHP_EOL;
            exit();
        }
        // 存在下载任务,开始执行
        // 1. 先改为处理中
        $importTask->status = ImportCenterEnums::STATUS_DOING;
        if ($importTask->save() === false) {
            echo '下载任务启动异常: 更新为"处理中"失败' . PHP_EOL;
            exit();
        }
        // 不能开启事务, 时间长数据库连接自动关闭, 事务提交会报错(MySQL server has gone away )
        try {
            // 1. 下载文件
            $tmp_dir = sys_get_temp_dir() . '/';
            $standard_batch_service = StandardBatchService::getInstance();
            $standard_batch_service::setLanguage($barcode_data->language ?? 'en');
            $oss_info = $standard_batch_service->getOssInfoByUrl($importTask->file_url);
            $file_name = $oss_info['file_name'];
            ob_start();
            readfile($importTask->file_url);
            $file_data = ob_get_contents();
            ob_end_clean();
            file_put_contents($tmp_dir . $file_name, $file_data);
            // 2. 读取文件
            $config = ['path' => $tmp_dir];
            $excel = new \Vtiful\Kernel\Excel($config);

            // 读取上传文件数据
            $excelData = $excel->openFile($file_name)
                ->openSheet()
                ->getSheetData();

            // 读取每行单元格数据时，可指定每个单元格数据类型进行读取
            if (empty($excelData)) {
                // 正常情况下进入任务是会校验, 所以这种空文件基本不会出现, 如果出现记录异常
                throw new Exception('file is empty');
            }

            // 3. 组合和controllers一样的user信息
            $us = new UserService();
            $user = $us->getUserById($importTask->staff_info_id);
            $base_controller = new BaseController();
            $user_info = $base_controller->format_user($user);

            //获取语言环境
            $tmp = BaseService::getTranslation($importTask->language);

            // 3. 批量导入
            //生成Header
            array_shift($excelData);
            $excel_header_column = JobService::getInstance()->getExportExcelHeaderFields($importTask->language);
            $excel_header_column[] = $tmp->_('job_plan_hc_result_msg');

            $job_service = JobService::getInstance();
            $res = $job_service->batchUploadPlanHcNum($excelData, $user_info, $importTask);

            if (BudgetService::getInstance()->isConfiguredBudget()) {
                $columnsWidth = 7;
            } else {
                $columnsWidth = 6;
            }

            // 4. 生成结果文件
            if ($res['code'] == ErrCode::$SUCCESS) {
                // 处理成功, 生成上传结果文件
                $upload_result_data = $res['data']['excel_data'];
                $excel_extra_config = [
                    'file_name' => date('YmdHis') . '_' . $user_info['id'] . '_import_result.xlsx',
                    'end_column_char' => 'U',
                    'column_width' => $columnsWidth,
                ];
                $path = self::customizeExcelToFile($excel_header_column, $upload_result_data, $excel_extra_config);
                $oss_result = OssHelper::uploadFile($path);
            }

            // 5. 写入结果
            // 再查一次数据,长时间处理, 上边的数据库连接会自动关闭,保存报错
            $after_task_data = ImportTaskModel::findFirst($importTask->id);
            if (!$after_task_data || $after_task_data->status != ImportCenterEnums::STATUS_DOING) {
                throw new Exception('result save error, not found task');
            }
            $after_task_data->success_num = $res['data']['success_num'];
            $after_task_data->error_num = $res['data']['failed_num'];
            $after_task_data->result_file_url = $oss_result['object_url'] ?? '';
            $after_task_data->status = ImportCenterEnums::STATUS_DONE;
            $after_task_data->finished_at = date('Y-m-d H:i:s');
            if ($after_task_data->save() === false) {
                // 正常情况下进入任务是会校验, 所以这种空文件基本不会出现, 如果出现记录异常
                throw new Exception('result save error, message='.get_data_object_error_msg($after_task_data));
            }
        } catch (Exception $e) {
            $this->logger->error('importHcPlanNum error: message=' . $e->getMessage() . '; trace=' . $e->getTraceAsString());

            // 导入结果置为失败
            $after_task_data = ImportTaskModel::findFirst($importTask->id);
            if (!$after_task_data || $after_task_data->status != ImportCenterEnums::STATUS_DOING) {
                echo 'error: result failed save error, not found task' . PHP_EOL;exit();
            }
            $after_task_data->status = ImportCenterEnums::STATUS_FAILED;
            $after_task_data->finished_at = date('Y-m-d H:i:s');
            if ($after_task_data->save() === false) {
                $this->logger->error('importHcPlanNum_set_failed_error: ' . get_data_object_error_msg($after_task_data));
            }

            echo 'error:' . $e->getMessage() . PHP_EOL;exit();
        }
        $this->logger->info('importHcPlanNum_success: result=' . json_encode($res ?? []));
        echo 'importHcPlanNum success' . PHP_EOL;
    }

    /**
     * 离职资产-批量导入编辑(批量编辑按钮)
     * @date 2022/9/7
     * php app/cli.php import_center leave_asset_update
     */
    public function leave_asset_updateAction()
    {
        $this->checkLock(__METHOD__);
        $now_date = date('Y-m-d H:i:s');
        $log = '';
        //内部按数据组开启事物
        try {
            // 查询当前是否存在待处理的任务
            // 查询最近一次待下载任务
            $leave_asset_task_data = ImportTaskModel::findFirst([
                'conditions' => 'type = :type: and status = :status: and is_deleted = :is_deleted:',
                'bind' => [
                    'type' => ImportCenterEnums::TYPE_LEAVE_ASSET_BATCH_EDIT,
                    'status' => ImportCenterEnums::STATUS_WAITING,
                    'is_deleted' => ImportCenterEnums::NOT_DELETED
                ],
                'order' => 'id asc',
            ]);
            if (!$leave_asset_task_data) {
                throw new ValidationException('leave_asset_update 没有等待导入的任务; date=' . $now_date, ErrCode::$VALIDATE_ERROR);
            }

            // 存在下载任务,开始执行
            // 1. 先改为处理中
            $leave_asset_task_data->status = ImportCenterEnums::STATUS_DOING;
            if ($leave_asset_task_data->save() === false) {
                throw new Exception('leave_asset_update 下载任务启动异常: 更新为"处理中"失败; date=' . $now_date, ErrCode::$SYSTEM_ERROR);
            }

            // 1. 下载文件
            $excel_data = get_oss_file_excel_data($leave_asset_task_data->file_url, [
                5 => \Vtiful\Kernel\Excel::TYPE_STRING,
                6 => \Vtiful\Kernel\Excel::TYPE_STRING,
                7 => \Vtiful\Kernel\Excel::TYPE_STRING,
                8 => \Vtiful\Kernel\Excel::TYPE_STRING,
                9 => \Vtiful\Kernel\Excel::TYPE_STRING,
                10 => \Vtiful\Kernel\Excel::TYPE_STRING,
            ]);
            // 读取每行单元格数据时，可指定每个单元格数据类型进行读取
            if (empty($excel_data)) {
                // 正常情况下进入任务是会校验, 所以这种空文件基本不会出现, 如果出现记录异常
                throw new Exception('leave_asset_update file is empty', ErrCode::$SYSTEM_ERROR);
            }
            // 3. 组合和controllers一样的user信息
            $us = new UserService();
            $user = $us->getUserById($leave_asset_task_data->staff_info_id);
            $base_controller = new BaseController();
            $user_info = $base_controller->format_user($user);
            // 3. 批量导入
            // 提取Excel Header
            $excel_header_column_one = array_shift($excel_data);
            $excel_header_column_two = array_shift($excel_data);
            $country_code = get_country_code();
            if ($country_code == GlobalEnums::TH_COUNTRY_CODE) {
                $excel_header_column_three = array_shift($excel_data);
            }
            $leave_asset_service = LeaveAssetsExcelService::getInstance();
            $leave_asset_service::setLanguage($leave_asset_task_data->language ?? 'en');
            $res = $leave_asset_service->leaveAssetBatchUpload($excel_data, $user_info);
            // 4. 生成结果文件
            if ($res['code'] == ErrCode::$SUCCESS) {
                // 处理成功, 生成上传结果文件
                $upload_result_data = $res['data']['excel_data'];
                array_unshift($upload_result_data, $excel_header_column_two);
                if ($country_code == GlobalEnums::TH_COUNTRY_CODE) {
                    array_unshift($upload_result_data, $excel_header_column_three);
                }
                $excel_extra_config = [
                    'file_name' => date('YmdHis') . '_' . $user_info['id'] . '_import_result.xlsx',
                    //'file_desc' => $excel_header_column_one,
                    'end_column_char' => 'U',
                    'column_width' => 15,
                ];
                $path = self::customizeExcelToFile($excel_header_column_one, $upload_result_data, $excel_extra_config);
                $oss_result = OssHelper::uploadFile($path);
            }

            // 5. 写入结果
            // 再查一次数据,长时间处理, 上边的数据库连接会自动关闭,保存报错
            $after_task_data = ImportTaskModel::findFirst($leave_asset_task_data->id);
            if (!$after_task_data || $after_task_data->status != ImportCenterEnums::STATUS_DOING) {
                throw new Exception('result save error, not found task', ErrCode::$SYSTEM_ERROR);
            }
            $after_task_data->success_num = $res['data']['success_num'];
            $after_task_data->error_num = $res['data']['failed_sum'];
            $after_task_data->result_file_url = $oss_result['object_url'] ?? '';
            $after_task_data->status = ImportCenterEnums::STATUS_DONE;
            $after_task_data->finished_at = date('Y-m-d H:i:s');
            if ($after_task_data->save() === false) {
                // 正常情况下进入任务是会校验, 所以这种空文件基本不会出现, 如果出现记录异常
                throw new Exception('leave_asset_update result save error, message=' . get_data_object_error_msg($after_task_data), ErrCode::$SYSTEM_ERROR);
            }
            $this->logger->info('leave_asset_update_success: result=' . json_encode($res ?? []));
            $log .= 'leave asset update success' . PHP_EOL;
        } catch (ValidationException $e) {
            $log .= '任务结果: ' . $e->getMessage() . PHP_EOL;
        } catch (Exception $e) {
            $this->logger->error('leave_asset_update error: message=' . $e->getMessage() . '; trace=' . $e->getTraceAsString());
            // 导入结果置为失败
            $after_task_data = ImportTaskModel::findFirst($leave_asset_task_data->id);
            if (!$after_task_data || $after_task_data->status != ImportCenterEnums::STATUS_DOING) {
                $this->logger->error('error:leave_asset_update result failed save error, not found task');
                echo 'error:leave_asset_update result failed save error, not found task' . PHP_EOL;
                $this->clearLock(__METHOD__);
                exit();
            }
            $after_task_data->status = ImportCenterEnums::STATUS_FAILED;
            $after_task_data->finished_at = date('Y-m-d H:i:s');
            if ($after_task_data->save() === false) {
                $this->logger->error('leave_asset_update_error: ' . get_data_object_error_msg($after_task_data));
            }
            $log .= 'error:' . $e->getMessage() . PHP_EOL;
        }
        $this->clearLock(__METHOD__);
        exit($log);
    }

    /**
     * 分仓规则-批量导入
     */
    public function material_store_storage_addAction()
    {
        $this->checkLock(__METHOD__);
        $log = '';
        // 不能开启事务, 时间长数据库连接自动关闭, 事务提交会报错(MySQL server has gone away )
        try {
            //查询当前是否存在待处理的任务
            $task_info = ImportTaskModel::findFirst([
                'conditions' => 'type = :type: and status = :status: and is_deleted = :is_deleted:',
                'bind' => [
                    'type' => ImportCenterEnums::TYPE_MATERIAL_STORE_STORAGE_ADD,
                    'status' => ImportCenterEnums::STATUS_WAITING,
                    'is_deleted' => GlobalEnums::IS_NO_DELETED
                ],
                'order' => 'id asc',
            ]);
            if (empty($task_info)) {
                throw new ValidationException('没有等待导入的任务', ErrCode::$VALIDATE_ERROR);
            }

            // 存在下载任务,开始执行
            // 1. 先改为处理中
            $update_at = date('Y-m-d H:i:s');
            $task_info->status = ImportCenterEnums::STATUS_DOING;
            $task_info->updated_at = $update_at;
            $bool = $task_info->save();
            if ($bool === false) {
                throw new Exception('下载任务启动异常: 更新为"处理中"失败', ErrCode::$BUSINESS_ERROR);
            }

            // 2. 下载文件
            $excel_data = get_oss_file_excel_data($task_info->file_url, [\Vtiful\Kernel\Excel::TYPE_STRING]);
            // 读取每行单元格数据时，可指定每个单元格数据类型进行读取
            if (empty($excel_data)) {
                // 正常情况下进入任务是会校验, 所以这种空文件基本不会出现, 如果出现记录异常
                throw new Exception('file is empty');
            }

            // 3. 批量导入
            $warehouse_division_rule_service = WarehouseDivisionRuleService::getInstance();
            $warehouse_division_rule_service::setLanguage($task_info->language ?? 'en');
            $res = $warehouse_division_rule_service->handleRuleTask($excel_data, $task_info->staff_info_id);

            // 4. 生成结果文件
            if ($res['code'] == ErrCode::$SUCCESS) {
                // 处理成功, 生成上传结果文件
                $upload_result_data = $res['data']['excel_data'];
                $excel_extra_config = [
                    'file_name' => date('YmdHis') . '_' . $task_info->staff_info_id . '_import_result.xlsx',
                    'end_column_char' => 'I',
                    'column_width' => 15,
                ];
                $header = array_shift($upload_result_data);
                $path = self::customizeExcelToFile($header, $upload_result_data, $excel_extra_config);
                $oss_result = OssHelper::uploadFile($path);
            } else {
                throw new Exception($res['message'], $res['code']);
            }

            // 5. 写入结果
            // 再查一次数据,长时间处理, 上边的数据库连接会自动关闭,保存报错
            $after_task_info = ImportTaskModel::findFirst($task_info->id);
            if (empty($after_task_info) || $after_task_info->status != ImportCenterEnums::STATUS_DOING) {
                throw new Exception('error: result failed save error, not found task');
            }
            $after_task_info->success_num = $res['data']['success_num'];
            $after_task_info->error_num = $res['data']['failed_sum'];
            $after_task_info->result_file_url = $oss_result['object_url'] ?? '';
            $after_task_info->status = ImportCenterEnums::STATUS_DONE;
            $after_task_info->finished_at = $update_at;
            $after_task_info->updated_at = $update_at;
            $bool = $after_task_info->save();
            if ($bool === false) {
                // 正常情况下进入任务是会校验, 所以这种空文件基本不会出现, 如果出现记录异常
                throw new Exception('material_store_storage_add_task save result error, message=' . get_data_object_error_msg($after_task_info));
            }
            $this->logger->info('material_store_storage_add_task success: result=' . json_encode($res ?? []));
            $log .= 'material_store_storage_add_task success' . PHP_EOL;
        } catch (ValidationException $e) {
            $log .= '任务结果: ' . $e->getMessage() . PHP_EOL;
        } catch (Exception $e) {
            $this->logger->error('material_store_storage_add_task_error: message=' . $e->getMessage() . '; trace=' . $e->getTraceAsString());

            // 6.异常，导入结果置为失败
            $after_task_info = ImportTaskModel::findFirst($task_info->id);
            if (empty($after_task_info) || $after_task_info->status != ImportCenterEnums::STATUS_DOING) {
                echo 'error: result failed save error, not found task' . PHP_EOL;
                $this->clearLock(__METHOD__);
                exit();
            }
            $after_task_info->status = ImportCenterEnums::STATUS_FAILED;
            $after_task_info->finished_at = $update_at;
            $after_task_info->updated_at = $update_at;
            $bool = $after_task_info->save();
            if ($bool === false) {
                $this->logger->error('material_store_storage_add_task save result error, message=' . get_data_object_error_msg($after_task_info));
            }

            $log .= 'error:' . $e->getMessage() . PHP_EOL;
        }

        $this->clearLock(__METHOD__);
        exit($log);
    }

    /**
     * 资产申请-批量审核
     */
    public function asset_apply_auditAction()
    {
        $this->checkLock(__METHOD__);
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log = '任务名称: ' . $process_name . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        try {
            // 查询当前是否存在待处理的任务
            $audit_data = ImportTaskModel::findFirst([
                'conditions' => 'type = :type: and status = :status: and is_deleted = :is_deleted:',
                'bind' => [
                    'type' => ImportCenterEnums::TYPE_MATERIAL_ASSET_AUDIT,
                    'status' => ImportCenterEnums::STATUS_WAITING,
                    'is_deleted' => ImportCenterEnums::NOT_DELETED,
                ],
                'order' => 'id asc',
            ]);
            if (!$audit_data) {
                throw new ValidationException('没有等待处理的任务', ErrCode::$VALIDATE_ERROR);
            }

            // 存在下载任务,开始执行
            // 1. 先改为处理中
            $audit_data->status = ImportCenterEnums::STATUS_DOING;
            if ($audit_data->save() === false) {
                $log .= '资产批量审核任务处理异常: 更新为 处理中 失败' . PHP_EOL;
                throw new Exception('asset_apply_audit save status=1 error:' . get_data_object_error_msg($audit_data));
            }
            // 1. 下载文件
            $tmp_dir = sys_get_temp_dir() . '/';
            AssetApplyService::setLanguage($audit_data->language ?? 'en');
            $oss_info  = get_oss_info_by_url($audit_data->file_url);
            $file_name = $oss_info['file_name'];
            ob_start();
            readfile($audit_data->file_url);
            $file_data = ob_get_contents();
            ob_end_clean();
            $tmp_path = $tmp_dir . $file_name;
            file_put_contents($tmp_path, $file_data);
            // 2. 读取文件
            $config = ['path' => $tmp_dir];
            $excel  = new \Vtiful\Kernel\Excel($config);

            // 读取上传文件数据
            $excel_data = $excel->openFile($file_name)
                ->openSheet()
                ->setType([
                    0 => \Vtiful\Kernel\Excel::TYPE_STRING,//资产申请单
                    1 => \Vtiful\Kernel\Excel::TYPE_STRING,//barcode
                    13 => \Vtiful\Kernel\Excel::TYPE_STRING,//审批结果
                ])
                ->getSheetData();
            unlink($tmp_path);
            if (empty($excel_data)) {
                throw new Exception('file is empty');
            }
            $user_id = $audit_data->staff_info_id;
            $excel_header_column_one = array_shift($excel_data);
            $update_result_column = 19;//备注写入列
            $res = AssetApplyService::getInstance()->batchAudit($excel_data, $user_id, $update_result_column);
            $this->logger->info('资产批量审核,校验之后的数据' . json_encode($res, JSON_UNESCAPED_UNICODE));
            // 4. 生成结果文件
            if ($res['code'] == ErrCode::$SUCCESS) {
                // 处理成功, 生成上传结果文件
                $upload_result_data = $res['data']['excel_data'];
                $excel_extra_config = [
                    'file_name' => date('YmdHis') . '_' . $user_id . '_asset_batch_audit_result.xlsx',
                    'end_column_char' => 'U',
                    'column_width' => 15,
                ];
                $path = self::customizeExcelToFile($excel_header_column_one, $upload_result_data, $excel_extra_config);
                $oss_result = OssHelper::uploadFile($path);
            }
            // 5. 写入结果
            // 再查一次数据,长时间处理, 上边的数据库连接会自动关闭,保存报错
            $after_audit_data = ImportTaskModel::findFirst($audit_data->id);
            if (!$after_audit_data || $after_audit_data->status != ImportCenterEnums::STATUS_DOING) {
                throw new Exception('asset_apply_audit result save error, not found task');
            }
            $after_audit_data->success_num = $res['data']['success_num'];
            $after_audit_data->error_num = $res['data']['failed_sum'];
            $after_audit_data->result_file_url = $oss_result['object_url'] ?? '';
            $after_audit_data->status = ImportCenterEnums::STATUS_DONE;
            $after_audit_data->finished_at = date('Y-m-d H:i:s');
            if ($after_audit_data->save() === false) {
                // 正常情况下进入任务是会校验, 所以这种空文件基本不会出现, 如果出现记录异常
                throw new Exception('asset_apply_audit save status=2 error:' . get_data_object_error_msg($after_audit_data));
            }
            $log .= '脚本逻辑正常处理完毕' . PHP_EOL;
        } catch (ValidationException $e) {
            $log .= '任务结果: ' . $e->getMessage() . PHP_EOL;
        } catch (Exception $e) {
            $this->logger->error('asset_apply_audit exception save: message=' . $e->getMessage() . '; trace=' . $e->getTraceAsString());
            // 导入结果置为失败
            $after_audit_data = ImportTaskModel::findFirst($audit_data->id);
            $after_audit_data->status = ImportCenterEnums::STATUS_FAILED;
            $after_audit_data->finished_at = date('Y-m-d H:i:s');
            if ($after_audit_data->save() === false) {
                $this->logger->error('asset_apply_audit save status=3 error: ' . get_data_object_error_msg($after_audit_data));
            }
            $log .= '脚本逻辑异常处理完毕' . PHP_EOL;
        }
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        $this->clearLock(__METHOD__);
        exit($log);
    }

    /**
     * 资产领用出库-导入新增
     */
    public function asset_out_storageAction()
    {
        $this->checkLock(__METHOD__);
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log = '任务名称: ' . $process_name . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        try {
            // 查询当前是否存在待处理的任务
            $audit_data = ImportTaskModel::findFirst([
                'conditions' => 'type = :type: and status = :status: and is_deleted = :is_deleted:',
                'bind' => [
                    'type' => ImportCenterEnums::TYPE_MATERIAL_ASSET_OUT_STORAGE_ADD,
                    'status' => ImportCenterEnums::STATUS_WAITING,
                    'is_deleted' => ImportCenterEnums::NOT_DELETED,
                ],
                'order' => 'id asc',
            ]);
            if (!$audit_data) {
                throw new ValidationException('没有等待处理的任务', ErrCode::$VALIDATE_ERROR);
            }

            // 存在下载任务,开始执行
            // 1. 先改为处理中
            $audit_data->status = ImportCenterEnums::STATUS_DOING;
            if ($audit_data->save() === false) {
                $log .= '资产领用出库-导入新增 任务处理异常: 更新为 处理中 失败' . PHP_EOL;
                throw new Exception('asset_out_storage save status=1 error:' . get_data_object_error_msg($audit_data));
            }
            // 1. 下载文件
            $tmp_dir = sys_get_temp_dir() . '/';
            AssetOutStorageService::setLanguage($audit_data->language ?? 'en');
            $oss_info  = get_oss_info_by_url($audit_data->file_url);
            $file_name = $oss_info['file_name'];
            ob_start();
            readfile($audit_data->file_url);
            $file_data = ob_get_contents();
            ob_end_clean();
            $tmp_path = $tmp_dir . $file_name;
            file_put_contents($tmp_path, $file_data);
            // 2. 读取文件
            $config = ['path' => $tmp_dir];
            $excel  = new \Vtiful\Kernel\Excel($config);

            // 读取上传文件数据
            $excel_data = $excel->openFile($file_name)
                ->openSheet()
                ->getSheetData();
            unlink($tmp_path);
            if (empty($excel_data)) {
                throw new Exception('file is empty');
            }
            $user_id = $audit_data->staff_info_id;
            $excel_header_column_one = array_shift($excel_data);
            $excel_header_column_two = array_shift($excel_data);
            $update_result_column = 15;//备注写入列
            $res = AssetOutStorageService::getInstance()->batchAddAssetOutStorage($excel_data, $user_id, $update_result_column);
            $this->logger->info('资产领用出库-导入新增,校验之后的数据' . json_encode($res, JSON_UNESCAPED_UNICODE));
            // 4. 生成结果文件
            if ($res['code'] == ErrCode::$SUCCESS) {
                // 处理成功, 生成上传结果文件
                $upload_result_data = $res['data']['excel_data'];
                array_unshift($upload_result_data, $excel_header_column_two);
                $excel_extra_config = [
                    'file_name' => date('YmdHis') . '_' . $user_id . '_asset_out_storage_result.xlsx',
                    'end_column_char' => 'U',
                    'column_width' => 15,
                ];
                $path = self::customizeExcelToFile($excel_header_column_one, $upload_result_data, $excel_extra_config);
                $oss_result = OssHelper::uploadFile($path);
            }
            // 5. 写入结果
            // 再查一次数据,长时间处理, 上边的数据库连接会自动关闭,保存报错
            $after_audit_data = ImportTaskModel::findFirst($audit_data->id);
            if (!$after_audit_data || $after_audit_data->status != ImportCenterEnums::STATUS_DOING) {
                throw new Exception('asset_out_storage result save error, not found task');
            }
            $after_audit_data->success_num = $res['data']['success_num'];
            $after_audit_data->error_num = $res['data']['failed_sum'];
            $after_audit_data->result_file_url = $oss_result['object_url'] ?? '';
            $after_audit_data->status = ImportCenterEnums::STATUS_DONE;
            $after_audit_data->finished_at = date('Y-m-d H:i:s');
            if ($after_audit_data->save() === false) {
                // 正常情况下进入任务是会校验, 所以这种空文件基本不会出现, 如果出现记录异常
                throw new Exception('asset_out_storage save status=2 error:' . get_data_object_error_msg($after_audit_data));
            }
            $log .= '脚本逻辑正常处理完毕' . PHP_EOL;
        } catch (ValidationException $e) {
            $log .= '任务结果: ' . $e->getMessage() . PHP_EOL;
        } catch (Exception $e) {
            $this->logger->error('asset_out_storage exception save: message=' . $e->getMessage() . '; trace=' . $e->getTraceAsString());
            // 导入结果置为失败
            $after_audit_data = ImportTaskModel::findFirst($audit_data->id);
            $after_audit_data->status = ImportCenterEnums::STATUS_FAILED;
            $after_audit_data->finished_at = date('Y-m-d H:i:s');
            if ($after_audit_data->save() === false) {
                $this->logger->error('asset_out_storage save status=3 error: ' . get_data_object_error_msg($after_audit_data));
            }
            $log .= '脚本逻辑异常处理完毕' . PHP_EOL;
        }
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        $this->clearLock(__METHOD__);
        exit($log);
    }

    /**
     * 批量导入特殊转岗
     * @date 2024/03/21
     * php app/cli.php import_center import_special_job_transfer
     */
    public function import_special_job_transferAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $this->check_process($process_name);
        // 查询当前是否存在待处理的任务
        // 查询最近一次待下载任务
        $importTask = ImportTaskModel::findFirst([
            'conditions' => 'type = :type: and status = :status: and is_deleted = :is_deleted:',
            'bind' => [
                'type'       => ImportCenterEnums::TYPE_JOB_TRANSFER_BATCH_UPLOAD,
                'status'     => ImportCenterEnums::STATUS_WAITING,
                'is_deleted' => ImportCenterEnums::NOT_DELETED,
            ],
            'order' => 'id asc',
        ]);
        if (empty($importTask)) {
            echo '没有等待导入的任务:' . PHP_EOL;
            exit();
        }

        //添加任务锁目的
        //防止消费任务速度快于队列的消费速度，如消费过快可能出现重复的转岗数据都入到表中
        $redis = RedisClient::getInstance()->getClient();
        $addSpecialTransferKey = RedisKey::JOB_TRANSFER_ASYNC_IMPORT_SPECIAL_TRANSFER;
        $addTransferInfo = (int) $redis->get($addSpecialTransferKey);
        if (!empty($addTransferInfo)) {
            echo '存在锁, 剩余时间: ' . $redis->ttl($addSpecialTransferKey) . PHP_EOL;
            exit();
        }

        // 存在下载任务,开始执行
        // 1. 先改为处理中
        $importTask->status = ImportCenterEnums::STATUS_DOING;
        if ($importTask->save() === false) {
            echo '下载任务启动异常: 更新为"处理中"失败' . PHP_EOL;
            exit();
        }
        // 不能开启事务, 时间长数据库连接自动关闭, 事务提交会报错(MySQL server has gone away )
        try {
            // 1. 下载文件
            $tmp_dir = sys_get_temp_dir() . '/';
            $standard_batch_service = StandardBatchService::getInstance();
            $standard_batch_service::setLanguage($importTask->language);
            $oss_info = $standard_batch_service->getOssInfoByUrl($importTask->file_url);
            $file_name = $oss_info['file_name'];
            ob_start();
            readfile($importTask->file_url);
            $file_data = ob_get_contents();
            ob_end_clean();
            file_put_contents($tmp_dir . $file_name, $file_data);
            // 2. 读取文件
            $config = ['path' => $tmp_dir];
            $excel = new \Vtiful\Kernel\Excel($config);

            // 读取上传文件数据
            $excelData = $excel->openFile($file_name)
                ->openSheet()
                ->setType([
                    0 => \Vtiful\Kernel\Excel::TYPE_STRING,
                    1 => \Vtiful\Kernel\Excel::TYPE_STRING,
                    2 => \Vtiful\Kernel\Excel::TYPE_STRING,
                    3 => \Vtiful\Kernel\Excel::TYPE_STRING,
                    4 => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP,
                    5 => \Vtiful\Kernel\Excel::TYPE_STRING,
                    6 => \Vtiful\Kernel\Excel::TYPE_STRING,
                ])
                ->getSheetData();

            // 读取每行单元格数据时，可指定每个单元格数据类型进行读取
            if (empty($excelData)) {
                // 正常情况下进入任务是会校验, 所以这种空文件基本不会出现, 如果出现记录异常
                throw new Exception('file is empty');
            }

            // 3. 组合和controllers一样的user信息
            $us = new UserService();
            $user = $us->getUserById($importTask->staff_info_id);
            $base_controller = new BaseController();
            $user_info = $base_controller->format_user($user);

            //获取语言环境
            $tmp = BaseService::getTranslation($importTask->language);

            // 3. 批量导入
            //生成Header
            array_shift($excelData);
            $excel_header_column = JobTransferService::getInstance()->getExportExcelHeaderFields($importTask->language);
            $excel_header_column[] = $tmp->_('check_result'); //校验结果

            $job_service = JobTransferService::getInstance();
            $res = $job_service->batchCreateSpecialJobTransfer($excelData, $user_info, $importTask);

            // 4. 生成结果文件
            if ($res['code'] == ErrCode::$SUCCESS) {
                // 处理成功, 生成上传结果文件
                $upload_result_data = $res['data']['excel_data'];
                $fileName = sprintf('Result_import_%s_%s.xlsx', date('Ymd'), date('His'));
                $excel_extra_config = [
                    'file_name'       => $fileName,
                    'end_column_char' => 'H',
                    'column_width'    => 20,
                ];
                $path               = self::customizeExcelToFile($excel_header_column, $upload_result_data,
                    $excel_extra_config);
                $oss_result         = OssHelper::uploadFile($path);
            }

            // 5. 写入结果
            // 再查一次数据,长时间处理, 上边的数据库连接会自动关闭,保存报错
            $after_task_data = ImportTaskModel::findFirst($importTask->id);
            if (!$after_task_data || $after_task_data->status != ImportCenterEnums::STATUS_DOING) {
                throw new Exception('result save error, not found task');
            }
            $after_task_data->success_num = $res['data']['success_num'];
            $after_task_data->error_num = $res['data']['failed_num'];
            $after_task_data->result_file_url = $oss_result['object_url'] ?? '';
            $after_task_data->status = ImportCenterEnums::STATUS_DONE;
            $after_task_data->finished_at = date('Y-m-d H:i:s');
            if ($after_task_data->save() === false) {
                // 正常情况下进入任务是会校验, 所以这种空文件基本不会出现, 如果出现记录异常
                throw new Exception('result save error, message='.get_data_object_error_msg($after_task_data));
            }
            if (RUNTIME == 'dev') {
                $lockTime = 60;
            } else {
                $lockTime = 5 * 60;
            }
            $redis->setex($addSpecialTransferKey, $lockTime, '1');
        } catch (Exception $e) {
            $this->logger->error('import_special_job_transfer error: message=' . $e->getMessage() . '; trace=' . $e->getTraceAsString());

            // 导入结果置为失败
            $after_task_data = ImportTaskModel::findFirst($importTask->id);
            if (!$after_task_data || $after_task_data->status != ImportCenterEnums::STATUS_DOING) {
                echo 'error: result failed save error, not found task' . PHP_EOL;exit();
            }
            $after_task_data->status = ImportCenterEnums::STATUS_FAILED;
            $after_task_data->finished_at = date('Y-m-d H:i:s');
            if ($after_task_data->save() === false) {
                $this->logger->error('import_special_job_transfer_set_failed_error: ' . get_data_object_error_msg($after_task_data));
            }

            echo 'error:' . $e->getMessage() . PHP_EOL;exit();
        }
        $this->logger->info('import_special_job_transfer_success: result=' . json_encode($res ?? []));
        echo 'import_special_job_transfer success' . PHP_EOL;
    }

    /**
     * 每5分钟执行一次
     * 押金管理-数据查询-批量转交
     * php app/cli.php import_center deposit_batch_forward
     */
    public function deposit_batch_forwardAction()
    {
        $this->checkLock(__METHOD__);
        $log = '';
        // 不能开启事务, 时间长数据库连接自动关闭, 事务提交会报错(MySQL server has gone away )
        try {
            //查询当前是否存在待处理的任务
            $task_info = ImportTaskModel::findFirst([
                'conditions' => 'type = :type: and status = :status: and is_deleted = :is_deleted:',
                'bind' => [
                    'type' => ImportCenterEnums::TYPE_DEPOSIT_BATCH_FORWARD,
                    'status' => ImportCenterEnums::STATUS_WAITING,
                    'is_deleted' => GlobalEnums::IS_NO_DELETED
                ],
                'order' => 'id asc',
            ]);
            if (empty($task_info)) {
                throw new ValidationException('没有等待处理的任务', ErrCode::$VALIDATE_ERROR);
            }

            // 存在下载任务,开始执行
            // 1. 先改为处理中
            $update_at = date('Y-m-d H:i:s');
            $task_info->status = ImportCenterEnums::STATUS_DOING;
            $task_info->updated_at = $update_at;
            $bool = $task_info->save();
            if ($bool === false) {
                throw new Exception('下载任务启动异常: 更新为"处理中"失败', ErrCode::$BUSINESS_ERROR);
            }

            $success_num = 0;
            $error_num = 0;
            $params = !empty($task_info->args_json) ? json_decode(base64_decode($task_info->args_json, true), true) : [];
            $deposit_type = $params['deposit_type'];

            // 2. 组合和controllers一样的user信息
            $us = new UserService();
            $user = $us->getUserById($task_info->staff_info_id);
            $base_controller = new BaseController();
            $user_info = $base_controller->format_user($user);

            // 3. 转交开始
            foreach ($deposit_type as $type) {
                $list_params = [
                    'type' => $type,
                    'create_name' => $params['apply_id'],
                    'return_status' => DepositEnums::DEPOSIT_RETURN_STATUS_NOT,
                    'pageSize' => DepositEnums::DEPOSIT_DOWNLOAD_LIMIT,
                ];

                $result = DepositService::getInstance()->getList($list_params, $user_info, DepositEnums::LIST_TYPE_DATA);
                foreach ($result['data']['items'] as $item) {
                    $forward_res = DepositService::getInstance()->edit(['id' => $item['id'], 'type' => $type, 'edit_type' => DepositEnums::DEPOSIT_EDIT_LOG_APPLY, 'new_apply_id' => $params['new_apply_id'], 'apply_name' => $params['new_apply_name'], 'attachment' => $params['attachment']], $user_info);
                    if ($forward_res['code'] == ErrCode::$SUCCESS) {
                        $success_num ++;
                    } else {
                        $error_num ++;
                    }
                }
            }

            // 4. 写入结果
            // 再查一次数据,长时间处理, 上边的数据库连接会自动关闭,保存报错
            $after_task_info = ImportTaskModel::findFirst($task_info->id);
            if (empty($after_task_info) || $after_task_info->status != ImportCenterEnums::STATUS_DOING) {
                throw new Exception('error: result failed save error, not found task');
            }
            $after_task_info->success_num = $success_num;
            $after_task_info->error_num = $error_num;
            $after_task_info->result_file_url = '';
            $after_task_info->status = ImportCenterEnums::STATUS_DONE;
            $after_task_info->finished_at = $update_at;
            $after_task_info->updated_at = $update_at;
            $bool = $after_task_info->save();
            if ($bool === false) {
                // 正常情况下进入任务是会校验, 所以这种空文件基本不会出现, 如果出现记录异常
                throw new Exception('deposit_batch_forward_task save result error, message=' . get_data_object_error_msg($after_task_info));
            }
            $log .= 'deposit_batch_forward_task success' . PHP_EOL;
            $this->logger->info('deposit_batch_forward_task success: result=' . json_encode($res ?? []));
        } catch (ValidationException $e) {
            $log .= '任务结果: ' . $e->getMessage() . PHP_EOL;
        } catch (Exception $e) {
            $this->logger->error('deposit_batch_forward_task_error: message=' . $e->getMessage() . '; trace=' . $e->getTraceAsString());

            // 5.异常，导入结果置为失败
            $after_task_info = ImportTaskModel::findFirst($task_info->id);
            if (empty($after_task_info) || $after_task_info->status != ImportCenterEnums::STATUS_DOING) {
                echo 'error: result failed save error, not found task' . PHP_EOL;
                $this->clearLock(__METHOD__);
                exit();
            }
            $after_task_info->status = ImportCenterEnums::STATUS_FAILED;
            $after_task_info->finished_at = $update_at;
            $after_task_info->updated_at = $update_at;
            $bool = $after_task_info->save();
            if ($bool === false) {
                $this->logger->error('deposit_batch_forward_task save result error, message=' . get_data_object_error_msg($after_task_info));
            }

            $log .= 'error:' . $e->getMessage() . PHP_EOL;
        }

        $this->clearLock(__METHOD__);
        exit($log);
    }

    /**
     * 上传职位性质
     * @date 2024/03/21
     * php app/cli.php import_center import_special_job_transfer
     */
    public function import_position_typeAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $this->check_process($process_name);
        // 查询当前是否存在待处理的任务
        // 查询最近一次待下载任务
        $importTask = ImportTaskModel::findFirst([
            'conditions' => 'type = :type: and status = :status: and is_deleted = :is_deleted:',
            'bind' => [
                'type'       => ImportCenterEnums::TYPE_JOB_POSITION,
                'status'     => ImportCenterEnums::STATUS_WAITING,
                'is_deleted' => ImportCenterEnums::NOT_DELETED,
            ],
            'order' => 'id asc',
        ]);
        if (empty($importTask)) {
            echo '没有等待导入的任务:' . PHP_EOL;
            exit();
        }

        // 存在下载任务,开始执行
        // 1. 先改为处理中
        $importTask->status = ImportCenterEnums::STATUS_DOING;
        if ($importTask->save() === false) {
            echo '下载任务启动异常: 更新为"处理中"失败' . PHP_EOL;
            exit();
        }
        // 不能开启事务, 时间长数据库连接自动关闭, 事务提交会报错(MySQL server has gone away )
        try {
            // 1. 下载文件
            $tmp_dir = sys_get_temp_dir() . '/';
            $standard_batch_service = StandardBatchService::getInstance();
            $standard_batch_service::setLanguage($importTask->language);
            $oss_info = $standard_batch_service->getOssInfoByUrl($importTask->file_url);
            $file_name = $oss_info['file_name'];
            ob_start();
            readfile($importTask->file_url);
            $file_data = ob_get_contents();
            ob_end_clean();
            file_put_contents($tmp_dir . $file_name, $file_data);
            // 2. 读取文件
            $config = ['path' => $tmp_dir];
            $excel = new \Vtiful\Kernel\Excel($config);

            // 读取上传文件数据
            $excelData = $excel->openFile($file_name)
                ->openSheet()
                ->setType([
                    0 => \Vtiful\Kernel\Excel::TYPE_STRING,
                    1 => \Vtiful\Kernel\Excel::TYPE_STRING,
                    2 => \Vtiful\Kernel\Excel::TYPE_STRING,
                    3 => \Vtiful\Kernel\Excel::TYPE_STRING,
                    4 => \Vtiful\Kernel\Excel::TYPE_STRING,
                    5 => \Vtiful\Kernel\Excel::TYPE_STRING,
                ])
                ->getSheetData();

            // 读取每行单元格数据时，可指定每个单元格数据类型进行读取
            if (empty($excelData)) {
                // 正常情况下进入任务是会校验, 所以这种空文件基本不会出现, 如果出现记录异常
                throw new Exception('file is empty');
            }

            // 3. 组合和controllers一样的user信息
            $us = new UserService();
            $user = $us->getUserById($importTask->staff_info_id);
            $base_controller = new BaseController();
            $user_info = $base_controller->format_user($user);

            //获取语言环境
            $tmp = BaseService::getTranslation($importTask->language);

            // 3. 批量导入

            $header = $excelData[0];//获取Header
            $header[] = $tmp->_('hc_approval_batch_approval_result'); //结果列

            array_shift($excelData);

            $job_service = JobService::getInstance();
            $res = $job_service->importPositionType($excelData, $user_info, $importTask);

            // 4. 生成结果文件
            if ($res['code'] == ErrCode::$SUCCESS) {
                // 处理成功, 生成上传结果文件
                $upload_result_data = $res['data']['excel_data'];
                $fileName = sprintf('Result_import_%s_%s.xlsx', date('Ymd'), date('His'));
                $excel_extra_config = [
                    'file_name'       => $fileName,
                    'end_column_char' => 'H',
                    'column_width'    => 20,
                ];
                $path               = self::customizeExcelToFile($header, $upload_result_data,
                    $excel_extra_config);
                $oss_result         = OssHelper::uploadFile($path);
            }

            // 5. 写入结果
            // 再查一次数据,长时间处理, 上边的数据库连接会自动关闭,保存报错
            $after_task_data = ImportTaskModel::findFirst($importTask->id);
            if (!$after_task_data || $after_task_data->status != ImportCenterEnums::STATUS_DOING) {
                throw new Exception('result save error, not found task');
            }
            $after_task_data->success_num = $res['data']['success_num'];
            $after_task_data->error_num = $res['data']['failed_num'];
            $after_task_data->result_file_url = $oss_result['object_url'] ?? '';
            $after_task_data->status = ImportCenterEnums::STATUS_DONE;
            $after_task_data->finished_at = date('Y-m-d H:i:s');
            if ($after_task_data->save() === false) {
                // 正常情况下进入任务是会校验, 所以这种空文件基本不会出现, 如果出现记录异常
                throw new Exception('result save error, message='.get_data_object_error_msg($after_task_data));
            }
        } catch (Exception $e) {
            $this->logger->error('import_position_typeAction error: message=' . $e->getMessage() . '; trace=' . $e->getTraceAsString());

            // 导入结果置为失败
            $after_task_data = ImportTaskModel::findFirst($importTask->id);
            if (!$after_task_data || $after_task_data->status != ImportCenterEnums::STATUS_DOING) {
                echo 'error: result failed save error, not found task' . PHP_EOL;exit();
            }
            $after_task_data->status = ImportCenterEnums::STATUS_FAILED;
            $after_task_data->finished_at = date('Y-m-d H:i:s');
            if ($after_task_data->save() === false) {
                $this->logger->error('import_position_typeAction_set_failed_error: ' . get_data_object_error_msg($after_task_data));
            }

            echo 'error:' . $e->getMessage() . PHP_EOL;exit();
        }
        $this->logger->info('import_position_typeAction_success: result=' . json_encode($res ?? []));
        echo 'import_position_typeAction success' . PHP_EOL;
    }

    /**
     * 员工商城-商品管理-批量导入(无头件)
     *
     * php app/cli.php import_center shop_goods_headless
     */
    public function shop_goods_headlessAction()
    {
        $lock_key = __METHOD__;
        $this->checkLock($lock_key);

        $log = '任务名称: ' . __FUNCTION__ . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log .= '当前内存: ' . memory_usage() . PHP_EOL;

        $log_type = 'info';

        try {
            // 1. 查询最早的待处理任务
            $pending_task = ImportCenterService::getInstance()->getPendingTask([ImportCenterEnums::TYPE_SHOP_GOODS_HEADLESS]);
            if (empty($pending_task)) {
                throw new ValidationException('无待处理的任务', ErrCode::$VALIDATE_ERROR);
            }

            $log .= "待处理任务: id={$pending_task->id}, remark = {$pending_task->task_remark}" . PHP_EOL;

            // 2. 任务更新为处理中
            $pending_task->status = ImportCenterEnums::STATUS_DOING;
            if ($pending_task->save() === false) {
                throw new Exception('更新为「处理中」失败, 原因可能是=' . get_data_object_error_msg($pending_task), ErrCode::$SYSTEM_ERROR);
            }

            // 3. 下载文件 并 提取文件数据
            static::setLanguage($pending_task->language ?? 'en');
            $columns_type = ImportCenterEnums::$task_excel_columns_type_config[ImportCenterEnums::TYPE_SHOP_GOODS_HEADLESS];
            $excel_data = get_oss_file_excel_data($pending_task->file_url, $columns_type);

            $log .= '当前内存(下载文件/提取数据): ' . memory_usage() . PHP_EOL;

            // 正常情况下进入任务是会校验, 所以这种空文件基本不会出现, 如果出现记录异常
            if (empty($excel_data)) {
                throw new Exception('文件数据未取到[或为空], file_url=' . $pending_task->file_url, ErrCode::$SYSTEM_ERROR);
            }

            // 4. 处理导入数据的业务逻辑
            $user = UserService::getInstance()->getUserById($pending_task->staff_info_id);
            $user_info = UserService::getInstance()->format_user($user);
            $import_res = GoodsManagementService::getInstance()->batchHandleImportGoodsData($excel_data, $user_info);

            $log .= '当前内存(数据处理完毕): ' . memory_usage() . PHP_EOL;

            if ($import_res['code'] != ErrCode::$SUCCESS) {
                throw new Exception('任务处理异常,' . $import_res['message'], ErrCode::$SYSTEM_ERROR);
            }

            // 5. 验证task是否正常状态
            $pending_task = ImportTaskModel::findFirst($pending_task->id);
            if (!$pending_task || $pending_task->status != ImportCenterEnums::STATUS_DOING) {
                throw new Exception('任务获取异常, 该任务不存在或非处理中的', ErrCode::$SYSTEM_ERROR);
            }

            // 6. 更新任务
            $pending_task->success_num = $import_res['data']['success_num'];
            $pending_task->error_num = $import_res['data']['error_num'];
            $pending_task->result_file_url = $import_res['data']['result_file_url'];
            $pending_task->status = ImportCenterEnums::STATUS_DONE;
            $pending_task->finished_at = date('Y-m-d H:i:s');
            $pending_task->updated_at = date('Y-m-d H:i:s');
            if ($pending_task->save() === false) {
                throw new Exception('任务处理成功-更新异常, 原因可能是=' . get_data_object_error_msg($pending_task) . ';data=' . json_encode($pending_task->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$SYSTEM_ERROR);
            }

            $log .= '任务结果: 成功' . PHP_EOL;

        } catch (ValidationException $e) {
            $log .= '任务结果: ' . $e->getMessage() . PHP_EOL;

        } catch (Exception $e) {
            $log_type = 'error';
            $log .= '任务结果: 异常, 原因可能是=' . $e->getMessage() . PHP_EOL;

            // 导入结果置为失败
            if (isset($pending_task->id)) {
                $pending_task = ImportTaskModel::findFirst($pending_task->id);
                if (!empty($pending_task) || $pending_task->status == ImportCenterEnums::STATUS_DOING) {
                    $pending_task->failed_reason = $e->getMessage();
                    $pending_task->status = ImportCenterEnums::STATUS_FAILED;
                    $pending_task->finished_at = date('Y-m-d H:i:s');
                    $pending_task->updated_at = date('Y-m-d H:i:s');
                    if ($pending_task->save() === false) {
                        $log .= '任务处理失败-更新异常,原因可能是=' . get_data_object_error_msg($pending_task);
                    }
                }
            }
        }

        $this->clearLock($lock_key);

        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $this->logger->$log_type($log);
        exit($log);
    }

    /**
     * 网点租房付款 - 我的申请 - 创建 - 批量导入数据
     * php app/cli.php import_center payment_store_renting_import_add
     */
    public function payment_store_renting_import_addAction()
    {
        $this->checkLock(__METHOD__);
        $log = '';
        // 不能开启事务, 时间长数据库连接自动关闭, 事务提交会报错(MySQL server has gone away )
        try {
            //查询当前是否存在待处理的任务
            $task_info = ImportTaskModel::findFirst([
                'conditions' => 'type = :type: and status = :status: and is_deleted = :is_deleted:',
                'bind' => [
                    'type' => ImportCenterEnums::TYPE_PAYMENT_STORE_RENTING_IMPORT,
                    'status' => ImportCenterEnums::STATUS_WAITING,
                    'is_deleted' => GlobalEnums::IS_NO_DELETED
                ],
                'order' => 'id asc',
            ]);
            if (empty($task_info)) {
                throw new ValidationException('无待处理的任务', ErrCode::$VALIDATE_ERROR);
            }
            // 存在下载任务,开始执行
            // 1. 先改为处理中
            $update_at = date('Y-m-d H:i:s');
            $task_info->status = ImportCenterEnums::STATUS_DOING;
            $task_info->updated_at = $update_at;
            $bool = $task_info->save();
            if ($bool === false) {
                throw new Exception('更新为「处理中」失败, 原因可能是=' . get_data_object_error_msg($task_info), ErrCode::$SYSTEM_ERROR);
            }

            // 2. 下载文件
            $excel_data = get_oss_file_excel_data($task_info->file_url, [
                10 => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP,//应付日期
                11 => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP,//费用开始日期
                12 => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP,//费用结束日期
                14 => \Vtiful\Kernel\Excel::TYPE_STRING,//VAT税率
                17 => \Vtiful\Kernel\Excel::TYPE_STRING,//WHT类别
                18 => \Vtiful\Kernel\Excel::TYPE_STRING,//WHT税率
                22 => \Vtiful\Kernel\Excel::TYPE_STRING,//银行账户名称
                23 => \Vtiful\Kernel\Excel::TYPE_STRING,//银行账户号
                24 => \Vtiful\Kernel\Excel::TYPE_STRING,//联系人电话
                27 => \Vtiful\Kernel\Excel::TYPE_STRING,//房东税务号
                29 => \Vtiful\Kernel\Excel::TYPE_STRING,//Swift Code
            ]);
            // 读取每行单元格数据时，可指定每个单元格数据类型进行读取
            if (empty($excel_data)) {
                // 正常情况下进入任务是会校验, 所以这种空文件基本不会出现, 如果出现记录异常
                throw new Exception('file is empty');
            }

            // 3. 批量导入
            $payment_store_renting_service = StoreRentingAddService::getInstance();
            $payment_store_renting_service::setLanguage($task_info->language ?? 'en');

            //组合和controllers一样的user信息
            $us = new UserService();
            $user = $us->getUserById($task_info->staff_info_id);
            $base_controller = new \App\Library\BaseController();
            $user_info = $base_controller->format_user($user);
            $res = $payment_store_renting_service->handleImportAddTask($excel_data, $user_info);

            // 4. 生成结果文件
            if ($res['code'] == ErrCode::$SUCCESS) {
                // 处理成功, 生成上传结果文件
                $upload_result_data = $res['data']['excel_data'];
                $excel_extra_config = [
                    'file_name' => date('YmdHis') . '_' . $task_info->staff_info_id . 'payment_store_renting_import_result.xlsx',
                    'end_column_char' => 'AF',
                    'file_desc' => $upload_result_data[0][0],
                    'second_content' => [
                        ['file_desc' => $upload_result_data[1][0], 'start_column_char' => 'A', 'end_column_char' => 'G'],
                        ['file_desc' => $upload_result_data[1][7], 'start_column_char' => 'H', 'end_column_char' => 'AF'],
                    ],
                    'column_high' => 50,
                    'column_width' => 15,
                ];
                $header = array_shift($upload_result_data);
                $path = self::customizeExcelToFile($header, $upload_result_data, $excel_extra_config);
                $oss_result = OssHelper::uploadFile($path);
            } else {
                throw new Exception($res['message'], $res['code']);
            }

            // 5. 写入结果
            // 再查一次数据,长时间处理, 上边的数据库连接会自动关闭,保存报错
            $after_task_info = ImportTaskModel::findFirst($task_info->id);
            if (empty($after_task_info) || $after_task_info->status != ImportCenterEnums::STATUS_DOING) {
                throw new Exception('error: result failed save error, not found task');
            }
            $after_task_info->success_num = $res['data']['success_num'];
            $after_task_info->error_num = $res['data']['failed_sum'];
            $after_task_info->result_file_url = $oss_result['object_url'] ?? '';
            $after_task_info->status = ImportCenterEnums::STATUS_DONE;
            $after_task_info->finished_at = $update_at;
            $after_task_info->updated_at = $update_at;
            $bool = $after_task_info->save();
            if ($bool === false) {
                // 正常情况下进入任务是会校验, 所以这种空文件基本不会出现, 如果出现记录异常
                throw new Exception('payment_store_renting_import_add_task save result error, message=' . get_data_object_error_msg($after_task_info));
            }
            $log .= 'payment_store_renting_import_add_task success' . PHP_EOL;
            $this->logger->info('payment_store_renting_import_add_task success: result=' . json_encode($res ?? []));
        } catch (ValidationException $e) {
            $log .= '任务结果: ' . $e->getMessage() . PHP_EOL;
        } catch (Exception $e) {
            $this->logger->error('payment_store_renting_import_add_task_error: message=' . $e->getMessage() . '; trace=' . $e->getTraceAsString());

            // 6.异常，导入结果置为失败
            $after_task_info = ImportTaskModel::findFirst($task_info->id);
            if (empty($after_task_info) || $after_task_info->status != ImportCenterEnums::STATUS_DOING) {
                echo 'error: result failed save error, not found task' . PHP_EOL;
                $this->clearLock(__METHOD__);
                exit();
            }
            $after_task_info->status = ImportCenterEnums::STATUS_FAILED;
            $after_task_info->finished_at = $update_at;
            $after_task_info->updated_at = $update_at;
            $bool = $after_task_info->save();
            if ($bool === false) {
                $this->logger->error('payment_store_renting_import_add_task save result error, message=' . get_data_object_error_msg($after_task_info));
            }

            $log .= 'error:' . $e->getMessage() . PHP_EOL;
        }

        $this->clearLock(__METHOD__);
        exit($log);
    }

    /**
     * 物料/资产管理 - 耗材申请 - 批量新增
     *
     * php app/cli.php import_center wms_batch_add
     */
    public function wms_batch_addAction()
    {
        $lock_key = __METHOD__;
        $this->checkLock($lock_key);

        $log = '任务名称: ' . __FUNCTION__ . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log .= '当前内存: ' . memory_usage() . PHP_EOL;

        $log_type = 'info';

        try {
            // 1. 查询最早的待处理任务
            $pending_task = ImportCenterService::getInstance()->getPendingTask([ImportCenterEnums::TYPE_MATERIAL_WMS_BATCH_ADD]);
            if (empty($pending_task)) {
                throw new ValidationException('无待处理的任务', ErrCode::$VALIDATE_ERROR);
            }

            $log .= "待处理任务: id={$pending_task->id}, remark = {$pending_task->task_remark}" . PHP_EOL;

            // 2. 任务更新为处理中
            $pending_task->status = ImportCenterEnums::STATUS_DOING;
            if ($pending_task->save() === false) {
                throw new Exception('更新为「处理中」失败, 原因可能是=' . get_data_object_error_msg($pending_task), ErrCode::$SYSTEM_ERROR);
            }

            // 3. 下载文件 并 提取文件数据
            static::setLanguage($pending_task->language ?? 'en');
            $excel_data = get_oss_file_excel_data($pending_task->file_url, []);

            $log .= '当前内存(下载文件/提取数据): ' . memory_usage() . PHP_EOL;

            // 正常情况下进入任务是会校验, 所以这种空文件基本不会出现, 如果出现记录异常
            if (empty($excel_data)) {
                throw new Exception('文件数据未取到[或为空], file_url=' . $pending_task->file_url, ErrCode::$SYSTEM_ERROR);
            }

            // 4. 处理导入数据的业务逻辑
            $user = UserService::getInstance()->getUserById($pending_task->staff_info_id);
            $user_info = UserService::getInstance()->format_user($user);
            $import_res = WmsApplyService::getInstance()->batchHandleImportWmsApply($excel_data, $user_info);

            $log .= '当前内存(数据处理完毕): ' . memory_usage() . PHP_EOL;

            if ($import_res['code'] != ErrCode::$SUCCESS) {
                throw new Exception('任务处理异常,' . $import_res['message'], ErrCode::$SYSTEM_ERROR);
            }

            // 5. 验证task是否正常状态
            $pending_task = ImportTaskModel::findFirst($pending_task->id);
            if (!$pending_task || $pending_task->status != ImportCenterEnums::STATUS_DOING) {
                throw new Exception('任务获取异常, 该任务不存在或非处理中的', ErrCode::$SYSTEM_ERROR);
            }

            // 6. 更新任务
            $pending_task->success_num = $import_res['data']['success_num'];
            $pending_task->error_num = $import_res['data']['error_num'];
            $pending_task->result_file_url = $import_res['data']['result_file_url'];
            $pending_task->status = ImportCenterEnums::STATUS_DONE;
            $pending_task->finished_at = date('Y-m-d H:i:s');
            $pending_task->updated_at = date('Y-m-d H:i:s');
            if ($pending_task->save() === false) {
                throw new Exception('任务处理成功-更新异常, 原因可能是=' . get_data_object_error_msg($pending_task) . ';data=' . json_encode($pending_task->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$SYSTEM_ERROR);
            }

            $log .= '任务结果: 成功' . PHP_EOL;

        } catch (ValidationException $e) {
            $log .= '任务结果: ' . $e->getMessage() . PHP_EOL;

        } catch (Exception $e) {
            $log_type = 'error';
            $log .= '任务结果: 异常, 原因可能是=' . $e->getMessage() . PHP_EOL;

            // 导入结果置为失败
            if (isset($pending_task->id)) {
                $pending_task = ImportTaskModel::findFirst($pending_task->id);
                if (!empty($pending_task) || $pending_task->status == ImportCenterEnums::STATUS_DOING) {
                    $pending_task->failed_reason = $e->getMessage();
                    $pending_task->status = ImportCenterEnums::STATUS_FAILED;
                    $pending_task->finished_at = date('Y-m-d H:i:s');
                    $pending_task->updated_at = date('Y-m-d H:i:s');
                    if ($pending_task->save() === false) {
                        $log .= '任务处理失败-更新异常,原因可能是=' . get_data_object_error_msg($pending_task);
                    }
                }
            }
        }

        $this->clearLock($lock_key);

        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $this->logger->$log_type($log);
        exit($log);
    }

    /**
     * 银行流水管理 - 流水上传 - 异步上传
     *
     * php app/cli.php import_center bank_flow_upload
     */
    public function bank_flow_uploadAction()
    {
        $log = '任务名称: ' . __FUNCTION__ . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log .= '当前内存: ' . memory_usage() . PHP_EOL;

        $log_type = 'info';

        try {
            // 1. 查询最早的待处理任务
            $pending_task = ImportCenterService::getInstance()->getPendingTask([ImportCenterEnums::TYPE_BANK_FLOW_INIT_UPLOAD]);
            if (empty($pending_task)) {
                throw new ValidationException('无待处理的任务', ErrCode::$VALIDATE_ERROR);
            }

            // 对银行账号加锁
            $lock_key = __METHOD__ . '_' . $pending_task->other_condition;
            $this->checkLock($lock_key);

            $log .= "待处理任务: id={$pending_task->id}, other_condition={$pending_task->other_condition}, remark={$pending_task->task_remark}" . PHP_EOL;

            // 2. 任务更新为处理中
            $pending_task->status = ImportCenterEnums::STATUS_DOING;
            if ($pending_task->save() === false) {
                throw new BusinessException('更新为「处理中」失败, 原因可能是=' . get_data_object_error_msg($pending_task), ErrCode::$BUSINESS_ERROR);
            }

            // 3. 下载文件 并 提取文件数据
            static::setLanguage($pending_task->language ?? 'en');

            // 参数解析, 验证文件数据
            $params = !empty($pending_task->args_json) ? json_decode(base64_decode($pending_task->args_json, true), true) : [];

            // 验证账号
            $bank_account_info = BankAccountModel::findFirst($params['bank_account_id']);
            if (!$bank_account_info) {
                throw new Exception(static::$t->_('bank_account_id_find_error'), ErrCode::$SYSTEM_ERROR);
            }

            $bank_account_name = $bank_account_info->company_name;
            $th_bay_not_flash_pay_condition = get_country_code() == GlobalEnums::TH_COUNTRY_CODE && $params['bank_id'] == BankFlowEnums::BANK_CODE_BAY && $bank_account_name != BankFlowEnums::BANK_ACCOUNT_NAME_PAY;

            // 获取银行的Excel列属性设置,获取各个模板的读取起始行
            if (in_array($params['bank_id'], BankFlowEnums::get_common_bank_id()) || $th_bay_not_flash_pay_condition) {
                $common_template_id = BankFlowEnums::get_common_template_id();
                $columns_type = BankFlowEnums::get_flow_file_columns_type()[$common_template_id] ?? [];
                $header_index = BankFlowEnums::get_data_begin_index()[$common_template_id] ?? 0;
            } else {
                $columns_type = BankFlowEnums::get_flow_file_columns_type()[$params['bank_id']] ?? [];
                $header_index = BankFlowEnums::get_data_begin_index()[$params['bank_id']] ?? 0;
            }

            if (empty($columns_type)) {
                throw new Exception(static::$t->_('bank_flow_bank_name_error'), ErrCode::$SYSTEM_ERROR);
            }

            $excel_data = get_oss_file_excel_data($pending_task->file_url, $columns_type);

            $log .= '当前内存(下载文件/提取数据): ' . memory_usage() . PHP_EOL;

            // 正常情况下进入任务是会校验, 所以这种空文件基本不会出现, 如果出现记录异常
            if (empty($excel_data)) {
                throw new Exception(static::$t->_('bank_flow_file_data_get_null', ['file_url' => $pending_task->file_url]), ErrCode::$SYSTEM_ERROR);
            }

            // 读取文件的起始位置 默认0行是表头 1行是数据
            $header_index_next = $header_index + 1;
            // 读取每行单元格数据时，可指定每个单元格数据类型进行读取
            if (empty($excel_data) || empty($excel_data[$header_index]) || empty($excel_data[$header_index_next])) {
                throw new Exception(static::$t->_('bank_flow_upload_file_illegal_format'), ErrCode::$SYSTEM_ERROR);
            }

            // 删除起始行之前的数据
            array_splice($excel_data, 0, $header_index);

            // 4. 处理导入数据的业务逻辑
            $user = UserService::getInstance()->getUserById($pending_task->staff_info_id);
            $user_info = UserService::getInstance()->format_user($user);
            $import_res = InitFlowService::getInstance()->flowUpload($params, $excel_data, $user_info, ImportCenterEnums::CALL_METHOD_METHOD_TASK);

            $log .= '当前内存(数据处理完毕): ' . memory_usage() . PHP_EOL;
            $log .= '处理结果: ' . json_encode($import_res, JSON_UNESCAPED_UNICODE) . PHP_EOL;

            if ($import_res['code'] != ErrCode::$SUCCESS) {
                throw new Exception($import_res['message'], ErrCode::$SYSTEM_ERROR);
            }

            // 5. 验证task是否正常状态
            $pending_task = ImportTaskModel::findFirst($pending_task->id);
            if (!$pending_task || $pending_task->status != ImportCenterEnums::STATUS_DOING) {
                throw new BusinessException('任务获取异常, 该任务不存在 或 状态非处理中', ErrCode::$BUSINESS_ERROR);
            }

            // 6. 更新任务
            $pending_task->success_num = $import_res['data']['success_count'];
            $pending_task->error_num = $import_res['data']['error_count'];
            $pending_task->result_file_url = $import_res['data']['result_file_url'];
            $pending_task->status = ImportCenterEnums::STATUS_DONE;
            $pending_task->finished_at = date('Y-m-d H:i:s');
            $pending_task->updated_at = date('Y-m-d H:i:s');
            if ($pending_task->save() === false) {
                throw new BusinessException('任务处理成功-更新异常, 原因可能是=' . get_data_object_error_msg($pending_task) . ';data=' . json_encode($pending_task->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }

            $log .= '任务结果: 成功' . PHP_EOL;

        } catch (ValidationException $e) {
            $log .= '任务结果: ' . $e->getMessage() . PHP_EOL;

        }  catch (BusinessException $e) {
            $log .= '任务结果: ' . $e->getMessage() . PHP_EOL;
            $log_type = 'warning';

        } catch (Exception $e) {
            $log_type = 'notice';
            $log .= '任务结果: 异常, 原因可能是=' . $e->getMessage() . PHP_EOL;

            // 导入结果置为失败
            if (isset($pending_task->id)) {
                $pending_task = ImportTaskModel::findFirst($pending_task->id);
                if (!empty($pending_task) || $pending_task->status == ImportCenterEnums::STATUS_DOING) {
                    $pending_task->failed_reason = $e->getMessage();
                    $pending_task->status = ImportCenterEnums::STATUS_FAILED;
                    $pending_task->finished_at = date('Y-m-d H:i:s');
                    $pending_task->updated_at = date('Y-m-d H:i:s');
                    $pending_task->result_file_url = $pending_task->file_url;

                    if (isset($import_res['data']['error_count'])) {
                        $pending_task->error_num = $import_res['data']['error_count'];
                    }

                    if ($pending_task->save() === false) {
                        $log .= '任务处理失败-更新异常,原因可能是=' . get_data_object_error_msg($pending_task);
                    }
                }
            }
        }

        if (isset($lock_key)) {
            $this->clearLock($lock_key);
        }

        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $this->logger->$log_type($log);
        exit($log);
    }

    /**
     * 银行流水管理 - 流水管理 - 付款流水 - 异步上传费用类型
     *
     * php app/cli.php import_center bank_payflow_upload_expense
     */
    public function bank_payflow_upload_expenseAction()
    {
        $lock_key = __METHOD__;
        $this->checkLock($lock_key);

        $log = '任务名称: ' . __FUNCTION__ . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log .= '当前内存: ' . memory_usage() . PHP_EOL;

        $log_type = 'info';

        try {
            // 1. 查询最早的待处理任务
            $pending_task = ImportCenterService::getInstance()->getPendingTask([ImportCenterEnums::TYPE_BANK_FLOW_PAY_FLOW_UPLOAD_EXPENSE]);
            if (empty($pending_task)) {
                throw new ValidationException('无待处理的任务', ErrCode::$VALIDATE_ERROR);
            }

            $log .= "待处理任务: id={$pending_task->id}, staff_info_id={$pending_task->staff_info_id}, remark={$pending_task->task_remark}" . PHP_EOL;

            // 2. 任务更新为处理中
            $pending_task->status = ImportCenterEnums::STATUS_DOING;
            if ($pending_task->save() === false) {
                throw new BusinessException('更新为「处理中」失败, 原因可能是=' . get_data_object_error_msg($pending_task), ErrCode::$BUSINESS_ERROR);
            }

            // 3. 下载文件 并 提取文件数据
            static::setLanguage($pending_task->language ?? 'en');

            // 参数解析, 验证文件数据
            $params = !empty($pending_task->args_json) ? json_decode(base64_decode($pending_task->args_json, true), true) : [];

            $columns_type = ImportCenterEnums::$task_excel_columns_type_config[ImportCenterEnums::TYPE_BANK_FLOW_PAY_FLOW_UPLOAD_EXPENSE];

            $excel_data = get_oss_file_excel_data($pending_task->file_url, $columns_type);

            // 移除文件头
            array_shift($excel_data);

            $log .= '当前内存(下载文件/提取数据): ' . memory_usage() . PHP_EOL;

            // 4. 处理导入数据的业务逻辑
            $user = UserService::getInstance()->getUserById($pending_task->staff_info_id);
            $user_info = UserService::getInstance()->format_user($user);

            $params['excel_data'] = $excel_data;
            $import_res = ListService::getInstance()->importFlowExpense($params, BankFlowEnums::BANK_FLOW_TYPE_PAY_OUTLAY, $user_info, ImportCenterEnums::CALL_METHOD_METHOD_TASK);

            $log .= '当前内存(数据处理完毕): ' . memory_usage() . PHP_EOL;
            $log .= '处理结果: ' . json_encode($import_res, JSON_UNESCAPED_UNICODE) . PHP_EOL;

            if ($import_res['code'] != ErrCode::$SUCCESS) {
                throw new Exception($import_res['message'], ErrCode::$SYSTEM_ERROR);
            }

            // 5. 验证task是否正常状态
            $pending_task = ImportTaskModel::findFirst($pending_task->id);
            if (!$pending_task || $pending_task->status != ImportCenterEnums::STATUS_DOING) {
                throw new BusinessException('任务获取异常, 该任务不存在 或 状态非处理中', ErrCode::$BUSINESS_ERROR);
            }

            // 6. 更新任务
            $pending_task->success_num = $import_res['data']['success_count'];
            $pending_task->error_num = $import_res['data']['error_count'];
            $pending_task->result_file_url = $pending_task->file_url;
            $pending_task->status = ImportCenterEnums::STATUS_DONE;
            $pending_task->finished_at = date('Y-m-d H:i:s');
            $pending_task->updated_at = date('Y-m-d H:i:s');
            if ($pending_task->save() === false) {
                throw new BusinessException('任务处理成功-更新异常, 原因可能是=' . get_data_object_error_msg($pending_task) . ';data=' . json_encode($pending_task->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }

            $log .= '任务结果: 成功' . PHP_EOL;

        } catch (ValidationException $e) {
            $log .= '任务结果: ' . $e->getMessage() . PHP_EOL;

        }  catch (BusinessException $e) {
            $log .= '任务结果: ' . $e->getMessage() . PHP_EOL;
            $log_type = 'warning';

        } catch (Exception $e) {
            $log_type = 'notice';
            $log .= '任务结果: 异常, 原因可能是=' . $e->getMessage() . PHP_EOL;

            // 导入结果置为失败
            if (isset($pending_task->id)) {
                $pending_task = ImportTaskModel::findFirst($pending_task->id);
                if (!empty($pending_task) || $pending_task->status == ImportCenterEnums::STATUS_DOING) {
                    $pending_task->failed_reason = $e->getMessage();
                    $pending_task->status = ImportCenterEnums::STATUS_FAILED;
                    $pending_task->finished_at = date('Y-m-d H:i:s');
                    $pending_task->updated_at = date('Y-m-d H:i:s');
                    $pending_task->result_file_url = $pending_task->file_url;

                    if (isset($import_res['data']['error_count'])) {
                        $pending_task->error_num = $import_res['data']['error_count'];
                    }

                    if ($pending_task->save() === false) {
                        $log .= '任务处理失败-更新异常,原因可能是=' . get_data_object_error_msg($pending_task);
                    }
                }
            }
        }

        $this->clearLock($lock_key);

        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $this->logger->$log_type($log);
        exit($log);
    }

    /**
     * 银行流水管理-流水管理-收款流水-异步上传费用类型
     *
     * php app/cli.php import_center bank_getflow_upload_expense
     */
    public function bank_getflow_upload_expenseAction()
    {
        $lock_key = __METHOD__;
        $this->checkLock($lock_key);

        $log = '任务名称: ' . __FUNCTION__ . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log .= '当前内存: ' . memory_usage() . PHP_EOL;

        $log_type = 'info';

        try {
            // 1. 查询最早的待处理任务
            $pending_task = ImportCenterService::getInstance()->getPendingTask([ImportCenterEnums::TYPE_BANK_FLOW_GET_FLOW_UPLOAD_EXPENSE]);
            if (empty($pending_task)) {
                throw new ValidationException('无待处理的任务', ErrCode::$VALIDATE_ERROR);
            }

            $log .= "待处理任务: id={$pending_task->id}, staff_info_id={$pending_task->staff_info_id}, remark={$pending_task->task_remark}" . PHP_EOL;

            // 2. 任务更新为处理中
            $pending_task->status = ImportCenterEnums::STATUS_DOING;
            if ($pending_task->save() === false) {
                throw new BusinessException('更新为「处理中」失败, 原因可能是=' . get_data_object_error_msg($pending_task), ErrCode::$BUSINESS_ERROR);
            }

            // 3. 下载文件 并 提取文件数据
            static::setLanguage($pending_task->language ?? 'en');

            // 参数解析, 验证文件数据
            $params = !empty($pending_task->args_json) ? json_decode(base64_decode($pending_task->args_json, true), true) : [];

            $columns_type = ImportCenterEnums::$task_excel_columns_type_config[ImportCenterEnums::TYPE_BANK_FLOW_GET_FLOW_UPLOAD_EXPENSE];

            $excel_data = get_oss_file_excel_data($pending_task->file_url, $columns_type);

            // 移除文件头
            array_shift($excel_data);

            $log .= '当前内存(下载文件/提取数据): ' . memory_usage() . PHP_EOL;

            // 4. 处理导入数据的业务逻辑
            $user = UserService::getInstance()->getUserById($pending_task->staff_info_id);
            $user_info = UserService::getInstance()->format_user($user);

            $params['excel_data'] = $excel_data;
            $import_res = ListService::getInstance()->importFlowExpense($params, BankFlowEnums::BANK_FLOW_TYPE_GET_INCOME, $user_info, ImportCenterEnums::CALL_METHOD_METHOD_TASK);

            $log .= '当前内存(数据处理完毕): ' . memory_usage() . PHP_EOL;
            $log .= '处理结果: ' . json_encode($import_res, JSON_UNESCAPED_UNICODE) . PHP_EOL;

            if ($import_res['code'] != ErrCode::$SUCCESS) {
                throw new Exception($import_res['message'], ErrCode::$SYSTEM_ERROR);
            }

            // 5. 验证task是否正常状态
            $pending_task = ImportTaskModel::findFirst($pending_task->id);
            if (!$pending_task || $pending_task->status != ImportCenterEnums::STATUS_DOING) {
                throw new BusinessException('任务获取异常, 该任务不存在 或 状态非处理中', ErrCode::$BUSINESS_ERROR);
            }

            // 6. 更新任务
            $pending_task->success_num = $import_res['data']['success_count'];
            $pending_task->error_num = $import_res['data']['error_count'];
            $pending_task->result_file_url = $pending_task->file_url;
            $pending_task->status = ImportCenterEnums::STATUS_DONE;
            $pending_task->finished_at = date('Y-m-d H:i:s');
            $pending_task->updated_at = date('Y-m-d H:i:s');
            if ($pending_task->save() === false) {
                throw new BusinessException('任务处理成功-更新异常, 原因可能是=' . get_data_object_error_msg($pending_task) . ';data=' . json_encode($pending_task->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }

            $log .= '任务结果: 成功' . PHP_EOL;

        } catch (ValidationException $e) {
            $log .= '任务结果: ' . $e->getMessage() . PHP_EOL;

        }  catch (BusinessException $e) {
            $log .= '任务结果: ' . $e->getMessage() . PHP_EOL;
            $log_type = 'warning';

        } catch (Exception $e) {
            $log_type = 'notice';
            $log .= '任务结果: 异常, 原因可能是=' . $e->getMessage() . PHP_EOL;

            // 导入结果置为失败
            if (isset($pending_task->id)) {
                $pending_task = ImportTaskModel::findFirst($pending_task->id);
                if (!empty($pending_task) || $pending_task->status == ImportCenterEnums::STATUS_DOING) {
                    $pending_task->failed_reason = $e->getMessage();
                    $pending_task->status = ImportCenterEnums::STATUS_FAILED;
                    $pending_task->finished_at = date('Y-m-d H:i:s');
                    $pending_task->updated_at = date('Y-m-d H:i:s');
                    $pending_task->result_file_url = $pending_task->file_url;

                    if (isset($import_res['data']['error_count'])) {
                        $pending_task->error_num = $import_res['data']['error_count'];
                    }

                    if ($pending_task->save() === false) {
                        $log .= '任务处理失败-更新异常,原因可能是=' . get_data_object_error_msg($pending_task);
                    }
                }
            }
        }

        $this->clearLock($lock_key);

        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $this->logger->$log_type($log);
        exit($log);
    }

    /**
     * 仓库需求-导入
     *
     * php app/cli.php import_center warehouse_requirement
     */
    public function warehouse_requirementAction()
    {
        $lock_key = __METHOD__;
        $this->checkLock($lock_key, 600);

        $log = '任务名称: ' . __FUNCTION__ . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log .= '当前内存: ' . memory_usage() . PHP_EOL;

        $log_type = 'info';

        try {
            // 1. 查询最早地待处理任务
            $pending_task = ImportCenterService::getInstance()->getPendingTask([ImportCenterEnums::TYPE_WAREHOUSE_REQUIREMENT]);
            if (empty($pending_task)) {
                throw new ValidationException('无待处理的任务', ErrCode::$VALIDATE_ERROR);
            }

            $log .= "待处理任务: id={$pending_task->id}, remark = {$pending_task->task_remark}" . PHP_EOL;

            // 2. 任务更新为处理中
            $pending_task->status = ImportCenterEnums::STATUS_DOING;
            if ($pending_task->save() === false) {
                throw new Exception('更新为「处理中」失败, 原因可能是=' . get_data_object_error_msg($pending_task), ErrCode::$SYSTEM_ERROR);
            }

            // 3. 下载文件 并 提取文件数据
            static::setLanguage($pending_task->language ?? 'en');
            $columns_type = ImportCenterEnums::$task_excel_columns_type_config[ImportCenterEnums::TYPE_WAREHOUSE_REQUIREMENT];
            $excel_data = get_oss_file_excel_data($pending_task->file_url, $columns_type);

            $log .= '当前内存(下载文件/提取数据): ' . memory_usage() . PHP_EOL;

            // 正常情况下进入任务是会校验, 所以这种空文件基本不会出现, 如果出现记录异常
            if (empty($excel_data)) {
                throw new Exception('文件数据未取到[或为空], file_url=' . $pending_task->file_url, ErrCode::$SYSTEM_ERROR);
            }

            // 4. 处理导入数据的业务逻辑
            $user = UserService::getInstance()->getUserById($pending_task->staff_info_id);
            $user_info = UserService::getInstance()->format_user($user);
            $import_res = RequirementService::getInstance()->batchHandleImportFileData($excel_data, $user_info);

            $log .= '当前内存(数据处理完毕): ' . memory_usage() . PHP_EOL;

            if ($import_res['code'] != ErrCode::$SUCCESS) {
                throw new Exception('任务处理异常,' . $import_res['message'], ErrCode::$SYSTEM_ERROR);
            }

            // 5. 验证task是否正常状态
            $pending_task = ImportTaskModel::findFirst($pending_task->id);
            if (!$pending_task || $pending_task->status != ImportCenterEnums::STATUS_DOING) {
                throw new Exception('任务获取异常, 该任务不存在或非处理中的', ErrCode::$SYSTEM_ERROR);
            }

            // 6. 更新任务
            $pending_task->success_num = $import_res['data']['success_num'];
            $pending_task->error_num = $import_res['data']['error_num'];
            $pending_task->result_file_url = $import_res['data']['result_file_url'];
            $pending_task->status = ImportCenterEnums::STATUS_DONE;
            $pending_task->finished_at = date('Y-m-d H:i:s');
            $pending_task->updated_at = date('Y-m-d H:i:s');
            if ($pending_task->save() === false) {
                throw new Exception('任务处理成功-更新异常, 原因可能是=' . get_data_object_error_msg($pending_task) . ';data=' . json_encode($pending_task->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$SYSTEM_ERROR);
            }

            $log .= '任务结果: 成功' . PHP_EOL;

        } catch (ValidationException $e) {
            $log .= '任务结果: ' . $e->getMessage() . PHP_EOL;

        } catch (Exception $e) {
            $log_type = 'error';
            $log .= '任务结果: 异常, 原因可能是=' . $e->getMessage() . PHP_EOL;

            // 导入结果置为失败
            if (isset($pending_task->id)) {
                $pending_task = ImportTaskModel::findFirst($pending_task->id);
                if (!empty($pending_task) || $pending_task->status == ImportCenterEnums::STATUS_DOING) {
                    $pending_task->failed_reason = $e->getMessage();
                    $pending_task->status = ImportCenterEnums::STATUS_FAILED;
                    $pending_task->finished_at = date('Y-m-d H:i:s');
                    $pending_task->updated_at = date('Y-m-d H:i:s');
                    if ($pending_task->save() === false) {
                        $log .= '任务处理失败-更新异常,原因可能是=' . get_data_object_error_msg($pending_task);
                    }
                }
            }
        }

        $this->clearLock($lock_key);

        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $this->logger->$log_type($log);
        exit($log);
    }


}
