<?php

use App\Library\Enums\InvoiceEnums;
use App\Library\OssHelper;
use App\Models\oa\InvoiceSubmissionsDetailModel;
use App\Modules\Hc\Services\BaseService;
use App\Modules\Invoice\Module;

/**
 * 电子发票相关任务
 */
class InvoiceTask extends BaseTask
{
    const FEISHU_SYNC_FAILURE_ALERT = '3aa12463-e375-430a-b796-45525ee2ce1b';
    public function initialize()
    {
        require_once __DIR__ . '/../modules/Invoice/Module.php';
        parent::initialize();
    }

    /**
     * 定时任务：每天自动同步电子发票数据
     * @description 每天执行一次
     * @cli php app/cli.php invoice syncDaily
     */
    public function syncDailyAction($args = null)
    {
        //$this->checkLock(__METHOD__);
        try {
            $this->logger->info('开始执行电子发票数据同步任务');
            echo "开始执行电子发票数据同步任务" . PHP_EOL;

            // 定义需要处理的数据类型
            $dataTypes = [
                InvoiceEnums::ORDINARY_PAYMENT_TXT,
                InvoiceEnums::CREDIT_ORDINARY_PAYMENT_TXT,
                InvoiceEnums::PURCHASE_PAYMENT_TXT,
                InvoiceEnums::CREDIT_PURCHASE_PAYMENT_TXT,
                InvoiceEnums::AGENCY_PAYMENT_TXT,
                InvoiceEnums::CREDIT_AGENCY_PAYMENT_TXT,
            ];
            if (isset($args[0])) {
                $dataTypesParams = explode(',', $args[0]);
                $dataTypes       = array_values(array_intersect($dataTypes, $dataTypesParams));
            }

            $currentDate = null;
            if (isset($args[1])) {
                $currentDate = $args[1];
            }

            // 加载服务
            $module = new Module();
            $module->registerServices($this->getDI());

            // 获取所有公司ID
            $companyIds = $this->getCompanyIdsByDataType();

            // 处理每种类型的数据，并按公司分组
            foreach ($dataTypes as $dataType) {
                $this->logger->info("开始处理数据类型: {$dataType}");
                echo "开始处理数据类型: {$dataType}" . PHP_EOL;

                if (empty($companyIds)) {
                    $this->logger->info("数据类型 {$dataType} 没有关联的公司数据");
                    echo "数据类型 {$dataType} 没有关联的公司数据" . PHP_EOL;
                    continue;
                }
                
                $this->logger->info("数据类型 {$dataType} 关联了 " . count($companyIds) . " 个公司");
                echo "数据类型 {$dataType} 关联了 " . count($companyIds) . " 个公司" . PHP_EOL;
                
                // 对每个公司分别处理数据
                foreach ($companyIds as $companyId) {
                    $this->processCompanyData($dataType, $companyId, $currentDate);
                }
            }
            
            $this->logger->info('电子发票数据同步任务执行完成');
            echo "电子发票数据同步任务执行完成" . PHP_EOL;
        } catch (\Exception $e) {
            $this->logger->error('电子发票数据同步任务异常：' . $e->getMessage() . ' - ' . $e->getTraceAsString());
            echo "电子发票数据同步任务异常：" . $e->getMessage() . PHP_EOL;
        }
    }

    /**
     * 定时任务：每天自动同步电子发票的提交结果
     * @description 每天执行多次次
     * @cli php app/cli.php invoice syncRemoteResult
     */
    public function syncRemoteResultAction()
    {
        //$this->checkLock(__METHOD__);
        try {
            $this->logger->info('开始同步电子发票提交结果');
            echo "开始同步电子发票提交结果" . PHP_EOL;
            
            // 加载服务
            $module = new Module();
            $module->registerServices($this->getDI());
            
            // 获取需要同步的提交记录（status = 1 的记录）
            $pendingSubmissions = $this->getPendingSubmissions();
            
            if (empty($pendingSubmissions)) {
                $this->logger->info('没有待同步的提交记录');
                echo "没有待同步的提交记录" . PHP_EOL;
                return;
            }
            
            $this->logger->info('找到 ' . count($pendingSubmissions) . ' 条待同步的提交记录');
            echo "找到 " . count($pendingSubmissions) . " 条待同步的提交记录" . PHP_EOL;
            
            // 按提交ID分组处理
            $submissionGroups = array_group_by_column($pendingSubmissions, 'submission_uid');

            // 获取OAuth服务
            $clientService = $this->getDI()->get('invoice.oauth.OAuthService');
            
            // 处理每组提交记录
            foreach ($submissionGroups as $submissionUid => $details) {
                $this->processSubmissionResult($submissionUid, $details, $clientService);
            }
            
            $this->logger->info('电子发票提交结果同步任务执行完成');
            echo "电子发票提交结果同步任务执行完成" . PHP_EOL;
            
        } catch (\Exception $e) {
            $this->logger->error('电子发票提交结果同步任务异常：' . $e->getMessage() . ' - ' . $e->getTraceAsString());
            echo "电子发票提交结果同步任务异常：" . $e->getMessage() . PHP_EOL;
        }
    }

    /**
     * 定期（每月 1 号）发送电子发票提交结果
     * @description 每月执行一次，发送上个月的电子发票提交结果
     * @cli php app/cli.php invoice sendInvoiceMonthlyReport
     * @return void
     */
    public function sendInvoiceMonthlyReportAction()
    {
        //$this->checkLock(__METHOD__);
        try {
            $this->logger->info('开始执行电子发票月度报表发送任务');
            echo "开始执行电子发票月度报表发送任务" . PHP_EOL;

            // 加载服务
            $module = new Module();
            $module->registerServices($this->getDI());

            // 1. 获取系统配置中的推送结果接收邮箱
            $emailConfig = $this->getInvoiceEmailRecipients();
            if (empty($emailConfig)) {
                $this->logger->info('没有配置推送结果接收邮箱，任务中止');
                echo "没有配置推送结果接收邮箱，任务中止" . PHP_EOL;
                return;
            }

            // 2. 确定上个月的年月
            $lastMonth = date('Ym', strtotime('first day of last month'));
            $lastMonthStartDate = date('Y-m-d', strtotime('first day of last month'));
            $lastMonthEndDate = date('Y-m-d', strtotime('last day of last month'));

            $this->logger->info("准备生成 {$lastMonth} 月度电子发票报表");
            echo "准备生成 {$lastMonth} 月度电子发票报表" . PHP_EOL;

            // 3. 获取上个月的发票数据
            $invoiceData = $this->getMonthlyInvoiceData($lastMonthStartDate, $lastMonthEndDate);

            if (empty($invoiceData)) {
                $this->logger->info("上个月 {$lastMonth} 没有电子发票数据，任务中止");
                echo "上个月 {$lastMonth} 没有电子发票数据，任务中止" . PHP_EOL;
                return;
            }

            // 4. 生成Excel报表
            $excelFile = $this->generateInvoiceExcel($invoiceData, $lastMonth);
            if (empty($excelFile)) {
                $this->logger->error("生成Excel报表失败");
                echo "生成Excel报表失败" . PHP_EOL;
                return;
            }

            // 5. 发送邮件
            $this->sendInvoiceEmail($emailConfig, $excelFile['object_url'], $lastMonth);

            $this->logger->info('电子发票月度报表发送任务执行完成');
            echo "电子发票月度报表发送任务执行完成" . PHP_EOL;

        } catch (\Exception $e) {
            $this->logger->error('电子发票月度报表发送任务异常：' . $e->getMessage() . ' - ' . $e->getTraceAsString());
            echo "电子发票月度报表发送任务异常：" . $e->getMessage() . PHP_EOL;
        } finally {
            // 释放锁
            $this->clearLock(__METHOD__);
        }
    }

    /**
     * 同步异常数据告警，告警数据如下
     * 1. 当天存在推送失败的情况
     * 2. 长时间待提交状态的数据
     * 3. 提交过去后，无返回的数据
     *
     * * @description 每天执行一次
     * * @cli php app/cli.php invoice syncFailureAlert
     * * @return void
    */
    public function syncFailureAlertAction($args = [])
    {
        //$this->checkLock(__METHOD__);
        try {
            $this->logger->info('开始执行电子发票异常数据告警任务');
            echo "开始执行电子发票异常数据告警任务" . PHP_EOL;

            if (!empty($args[0])) {
                $date = $args[0];
            } else {
                $date = '';
            }

            // 加载服务
            $module = new Module();
            $module->registerServices($this->getDI());

            // 1. 获取异常数据
            $todayFailures = $this->getTodayFailures($date); // 当天存在推送失败的情况
            $longPendingData = $this->getLongPendingData(); // 长时间待提交状态的数据
            $noResponseData = $this->getNoResponseData(); // 提交过去后，无返回的数据
            // 2. 合并所有异常数据
            $allFailures = array_merge($todayFailures, $longPendingData, $noResponseData);

            // 如果没有异常数据，则不发送告警
            if (empty($allFailures)) {
                $this->logger->info('没有发现异常数据，无需发送告警');
                echo "没有发现异常数据，无需发送告警" . PHP_EOL;
                return;
            }

            // 3. 格式化告警内容并发送飞书消息
            $content = $this->format_content($allFailures);
            send_feishu_text_msg($content, self::FEISHU_SYNC_FAILURE_ALERT);

            $this->logger->info('电子发票异常数据告警任务执行完成');
            echo "电子发票异常数据告警任务执行完成" . PHP_EOL;

        } catch (\Exception $e) {
            $this->logger->error('电子发票异常数据告警任务异常：' . $e->getMessage() . ' - ' . $e->getTraceAsString());
            echo "电子发票异常数据告警任务异常：" . $e->getMessage() . PHP_EOL;
        }
    }

    /**
     * 格式化告警内容为飞书消息格式
     * @param array $allFailures 所有异常数据
     * @return string 格式化后的消息内容
     */
    private function format_content(array $allFailures): string
    {
        $date = date('Y-m-d');
        
        // 按异常类型分组
        $groupedData = [];
        foreach ($allFailures as $item) {
            $groupedData[$item['failure_type']][] = $item;
        }
        
        // 构建消息内容
        $content = "【E-invoice异常告警 {$date}】\n\n";
        
        // 添加汇总信息
        $content .= "异常数据汇总：\n";
        foreach ($groupedData as $type => $items) {
            $content .= "- {$type}：" . count($items) . " 条\n";
        }
        $content .= "\n";
        
        // 添加详细信息（每种类型最多显示5条）
        foreach ($groupedData as $type => $items) {
            $content .= "{$type}明细如下：\n";
            
            foreach ($items as $item) {

                $invoice_type = $item['invoice_type'] == 1 ? '正向' : '贷向';
                $content .= "OA单号: {$item['internal_invoice_no']}";
                $content .= "正向/贷向: {$invoice_type}, ";
                $content .= "\n";
            }
            $content .= "\n";
        }
        
        return $content;
    }

    /**
     * 获取系统配置中的E-invoice推送结果接收邮箱
     * @return array 邮箱地址数组
     */
    private function getInvoiceEmailRecipients(): array
    {
        // 从系统配置中获取邮箱配置
        $emails = $this->getDI()->get('invoiceDataService')->getEInvoiceEmailAddresses();
        if (empty($emails)) {
            return [];
        }

        // 过滤无效的邮箱地址
        $validEmails = [];
        foreach ($emails as $email) {
            $email = trim($email);
            if (!empty($email) && filter_var($email, FILTER_VALIDATE_EMAIL)) {
                $validEmails[] = $email;
            } else if (!empty($email)) {
                $this->logger->warning("无效的邮箱地址: {$email}");
                echo "无效的邮箱地址: {$email}" . PHP_EOL;
            }
        }

        return $validEmails;
    }

    /**
     * 获取月度发票数据
     * @param string $startDate 开始日期（格式：Y-m-d）
     * @param string $endDate 结束日期（格式：Y-m-d）
     * @return array 发票数据数组
     */
    private function getMonthlyInvoiceData(string $startDate, string $endDate): array
    {
        $db = $this->getDI()->get('db_oa');

        // 按照OA单号+发票类型分组，获取创建时间最大的数据
        $sql = "SELECT 
                    MAX(isd.id) as latest_id
                FROM 
                    invoice_submissions_detail isd
                WHERE 
                    isd.business_date BETWEEN :start_date AND :end_date
                GROUP BY 
                    isd.invoice_no,isd.invoice_type";
        $result = $db->query($sql, [
            'start_date' => $startDate,
            'end_date' => $endDate
        ]);
        $data = $result->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        if (empty($data)) {
            return [];
        }
        $dataList = array_column($data, 'latest_id');


        $latestRecord = InvoiceSubmissionsDetailModel::find([
            'conditions' => 'id in({ids:array})',
            'bind' => [
                'ids' => $dataList
            ],
            'columns' => 'internal_invoice_no,business_date,error_msg,created_at,status,internal_type,invoice_type,uuid,invoice_no'
        ])->toArray();

        // 获取每条记录的详细信息
        $add_hour = $this->config->application->add_hour;
        $finalData = [];
        foreach ($latestRecord as $row) {
            $statusText      = array_search($row['status'], InvoiceEnums::$mapApiStatusToSystemStatus);
            $typeName        = $this->getTypeName($row['internal_type']);
            $invoiceTypeName = $this->getInvoiceTypeName($row['invoice_type']);

            $finalData[] = [
                'type_name'           => $typeName,
                'internal_invoice_no' => $row['internal_invoice_no'],
                'invoice_no'          => $row['invoice_no'],
                'invoice_type'        => $invoiceTypeName,
                'status'              => $statusText,
                'uuid'                => $row['uuid'] ?? '',
                'error_msg'           => $row['error_msg'],
                'created_at'          => date('Y-m-d', strtotime($row['created_at']) + 3600 * $add_hour),
                'business_date'       => $row['business_date'],
            ];
        }

        return $finalData;
    }

    /**
     * 根据类型ID获取类型名称
     * @param int $typeId 类型ID
     * @return string 类型名称
     */
    private function getTypeName(int $typeId): string
    {
        $typeMap = [
            InvoiceEnums::ORDINARY_PAYMENT => 'Ordinary Payment',
            InvoiceEnums::PURCHASE_PAYMENT => 'Purchase Payment',
            InvoiceEnums::AGENCY_PAYMENT   => 'Agency Payment',
        ];

        return $typeMap[$typeId] ?? 'Unknown Type';
    }

    /**
     * 生成发票Excel报表
     * @param array $data 发票数据
     * @param string $yearMonth 年月（格式：YYYYMM）
     * @return array Excel文件路径
     * @throws \App\Library\Exception\BusinessException
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    private function generateInvoiceExcel(array $data, string $yearMonth): array
    {
        $headers = [
            'Modules 模块',
            'OA order number OA单号',
            'Invoice Type 发票类型',
            'status 状态	',
            'UUID of E-invoice E-invoice的UUID	',
            'Code Number of E-invoice E-invoice的CodeNumber ',
            'Cause of failure 失败原因 ',
            'Push time 推送时间	',
            'Business Date 业务日期(审批通过日期和未支付操作日期)'
        ];

        $excelData = [];

        foreach ($data as $row) {
            $excelData[] = [
                $row['type_name'],
                $row['internal_invoice_no'],
                $row['invoice_type'],
                $row['status'],
                $row['uuid'],
                $row['invoice_no'],
                $row['error_msg'],
                $row['created_at'],
                $row['business_date'],
            ];
        }
        $fileName = "{$yearMonth}_E-invoice_Report.xlsx";

        $path = BaseService::excelToFile($headers, $excelData, $fileName);
        return OssHelper::uploadFile($path);
    }

    /**
     * 发送发票邮件
     * @param array $recipients 收件人邮箱数组
     * @param string $attachment 附件路径
     * @param string $yearMonth 年月（格式：YYYYMM）
     * @return bool 是否发送成功
     */
    private function sendInvoiceEmail(array $recipients, string $attachment, string $yearMonth): bool
    {
        $title = "{$yearMonth} E-invoice docking results";

        $content = "此邮件是{$yearMonth} 马来电子发票上传数据汇总 {$yearMonth}_E-invoice_Report的报表，<a href=\"{$attachment}\">请点击这里下载</a>, 谢谢</br>";
        $content .= "This email is a report for {$yearMonth} Malaysia E-invoice submission summary {$yearMonth}_E-invoice_Report, <a href=\"{$attachment}\">Please click here download</a>, thanks.</br>";
        $content .= "</br></br>";
        $content .= "此邮件为系统自动发送，无需回复</br>";
        $content .= "This email is automatically sent by the system and does not require a reply.</br>";

        $sent = $this->mailer->send($recipients, $title, $content);
        if ($sent) {
            $this->logger->info("成功发送E-invoice月度报表邮件到: " . implode(', ', $recipients));
            echo "成功发送E-invoice月度报表邮件到: " . implode(', ', $recipients) . PHP_EOL;
        } else {
            $this->logger->error("发送E-invoice月度报表邮件失败");
            echo "发送E-invoice月度报表邮件失败" . PHP_EOL;
        }
        return (bool)$sent;
    }

    /**
     * 处理单个提交ID的结果同步
     * @param string $submissionUid 提交ID
     * @param array $details 提交详情记录
     * @param object $clientService OAuth客户端服务
     */
    private function processSubmissionResult(string $submissionUid, array $details, object $clientService)
    {
        $this->logger->info("正在同步提交ID: {$submissionUid} 的结果，共 " . count($details) . " 条记录");
        echo "正在同步提交ID: {$submissionUid} 的结果，共 " . count($details) . " 条记录" . PHP_EOL;
        
        try {
            // 获取第一条记录的公司ID，用于获取TIN代码
            $companyId = $details[0]['company_id'];
            $dataService = $this->getDI()->get('invoiceDataService');
            $tinCode = $dataService->getTinCodeByCompanyId($companyId);
            
            // 获取Client
            $client = $clientService->getClient($tinCode);
            
            // 获取提交结果
            $response = $client->documentSubmission->getSubmission($submissionUid);
            
            if (empty($response) || !isset($response['documentSummary'])) {
                $this->logger->error("获取提交ID: {$submissionUid} 的结果失败或返回数据格式不正确");
                echo "获取提交ID: {$submissionUid} 的结果失败或返回数据格式不正确" . PHP_EOL;
                return;
            }

            // 更新提交记录状态
            $this->updateSubmissionStatus($response, $details, $client);

            $this->logger->info("提交ID: {$submissionUid} 的结果同步完成");
            echo "提交ID: {$submissionUid} 的结果同步完成" . PHP_EOL;

        } catch (\Exception $e) {
            $this->logger->error("同步提交ID: {$submissionUid} 的结果时发生错误: " . $e->getMessage());
            echo "同步提交ID: {$submissionUid} 的结果时发生错误: " . $e->getMessage() . PHP_EOL;
        }
    }

    /**
     * 获取待同步的提交记录
     * @return array 待同步的提交记录
     */
    private function getPendingSubmissions(): array
    {
        // 从数据库中获取状态为待同步的提交记录
        return InvoiceSubmissionsDetailModel::find([
            'conditions' => 'status = :status:',
            'bind' => [
                'status' => InvoiceEnums::INVOICE_SUBMISSION_STATUS_PENDING,
            ],
            'order' => 'created_at ASC',
            'limit' => 1000,
        ])->toArray();

    }

    /**
     * 更新提交记录状态
     * @param array $response API响应数据
     * @param array $details 提交详情记录
     */
    private function updateSubmissionStatus(array $response, array $details, $client)
    {
        $db              = $this->getDI()->get('db_oa');
        $documentSummary = $response['documentSummary'];
        $detailsList     = array_column($details, null,'invoice_no');

        // 更新所有相关记录
        foreach ($documentSummary as $item) {
            // 已提交状态的数据不需要处理
            if ($item['status'] === InvoiceEnums::INVOICE_SUBMISSION_STATUS_PENDING_TXT) {
                continue;
            }
            // id、status
            $internalInfo = $detailsList[$item['internalId']] ?? null;
            if (empty($internalInfo)) { // 根据 submission_uid获取整个提交的数据，由于分页的存在这里可能会存在空的情况。
                //$this->logger->info("internalId: {$item['internalId']} , not exist.");
                continue;
            }
            $internalId     = $internalInfo['id'];
            $internalStatus = $internalInfo['status'];
            try {
                // 内部系统已经更新提交状态
                if ($internalStatus != InvoiceEnums::INVOICE_SUBMISSION_STATUS_PENDING) {
                    continue;
                }
                $systemStatus = $this->mapApiStatusToSystemStatus($item['status']);

                if (in_array($item['status'], [
                    InvoiceEnums::INVOICE_SUBMISSION_STATUS_VALID_TXT,
                    InvoiceEnums::INVOICE_SUBMISSION_STATUS_CANCELED_TXT,
                ])) {
                    $query = "UPDATE invoice_submissions_detail SET 
                      status = :status
                      WHERE id = :id";
                    $db->query($query, [
                        'status' => $systemStatus,
                        'id'     => $internalId,
                    ]);
                    $this->logger->info("更新提交记录ID: {$internalId} 的状态为: {$systemStatus}");

                    continue;
                }

                // 根据 uuid获取明细
                $detailResponse = $client->document->getDocumentDetail($item['uuid']);
                if (empty($detailResponse) || !isset($detailResponse['validationResults'])) {
                    $this->logger->error("获取UUID: {$item['uuid']} 的结果失败或返回数据格式不正确");
                    echo "获取提交ID: {$item['uuid']} 的结果失败或返回数据格式不正确" . PHP_EOL;
                    continue;
                }

                // 遍历所有验证步骤，查找状态为invalid的步骤并收集错误信息
                $errMsg = '';
                if (isset($detailResponse['validationResults']['validationSteps']) && 
                    is_array($detailResponse['validationResults']['validationSteps'])) {
                    
                    foreach ($detailResponse['validationResults']['validationSteps'] as $step) {
                        if (isset($step['status']) &&
                            $step['status'] === InvoiceEnums::INVOICE_SUBMISSION_STATUS_INVALID_TXT && 
                            isset($step['error']['innerError'])) {
                            
                            // 如果已经有错误信息，添加分隔符
                            $errList = array_column($step['error']['innerError'], 'error');
                            $errMsg  = join(';', $errList);
                            break;
                        }
                    }
                }

                $query = "UPDATE invoice_submissions_detail SET 
                          status = :status,
                          error_info = :error_info,
                          error_msg = :error_msg
                          WHERE id = :id";
                $db->query($query, [
                    'status'     => $systemStatus,
                    'id'         => $internalId,
                    'error_info' => json_encode($detailResponse['validationResults']),
                    'error_msg'  => $errMsg,
                ]);
                $this->logger->info("更新提交记录ID: {$internalId} 的状态为: {$systemStatus} error_msg为: {$errMsg}");
            } catch (\Exception $e) {
                // 记录错误但继续处理其他记录
                $internalId = $item['internalId'] ?? 'unknown';
                $uuid       = $item['uuid'] ?? 'unknown';
                $this->logger->error("更新提交记录状态失败 - internalId: {$internalId}, uuid: {$uuid}, 错误: " . $e->getMessage());
                echo "更新提交记录状态失败 - internalId: {$internalId}, uuid: {$uuid}, 错误: " . $e->getMessage() . PHP_EOL;
            }
        }
    }

    /**
     * 将API状态映射到系统状态
     * @param string $apiStatus API状态
     * @return int 系统状态代码
     */
    private function mapApiStatusToSystemStatus(string $apiStatus): int
    {
        return InvoiceEnums::$mapApiStatusToSystemStatus[$apiStatus] ?? InvoiceEnums::INVOICE_SUBMISSION_STATUS_PENDING; // 默认为处理中
    }

    /**
     * 获取数据类型关联的公司ID列表
     * @return array 公司ID数组
     */
    private function getCompanyIdsByDataType(): array
    {
        return $this->getDI()->get('invoiceDataService')->getCostCompanyId();
    }

    /**
     * 处理单个公司的数据
     * @param string $dataType 数据类型
     * @param int|string $companyId 公司ID
     */
    private function processCompanyData(string $dataType, $companyId, $date = null)
    {
        $page        = 1;
        $pageSize    = 10; // 每次处理100条数据
        $hasMoreData = true;
        $service     = $this->getDI()->get('invoiceSyncService');
        $dataService = $this->getDI()->get('invoiceDataService');
        
        $this->logger->info("开始处理公司ID: {$companyId} 的 {$dataType} 类型数据");
        echo "开始处理公司ID: {$companyId} 的 {$dataType} 类型数据" . PHP_EOL;

        while ($hasMoreData) {
            // 获取特定公司的特定类型数据
            $invoices = $this->getCompanyDataByType($dataType, $companyId, $page, $pageSize, $date);
            
            if (empty($invoices) || count($invoices) == 0) {
                $this->logger->info("公司 {$companyId} 的 {$dataType} 类型没有更多数据需要同步");
                echo "公司 {$companyId} 的 {$dataType} 类型没有更多数据需要同步" . PHP_EOL;
                break;
            }
            
            $this->logger->info("正在处理公司 {$companyId} 的 {$dataType} 第{$page}页数据，共" . count($invoices) . "条");
            echo "正在处理公司 {$companyId} 的 {$dataType} 第{$page}页数据，共" . count($invoices) . "条" . PHP_EOL;

            $tinCode = $dataService->getTinCodeByCompanyId($companyId);

            // 处理当前页数据
            $result = $service->batchSubmit($invoices, $tinCode);
            
            // 记录处理结果
            $this->logger->info("公司 {$companyId} 的 {$dataType} 第{$page}页数据处理完成，结果：" . json_encode($result));
            echo "公司 {$companyId} 的 {$dataType} 第{$page}页数据处理完成" . PHP_EOL;
            
            // 如果获取的数据少于页大小，说明已经没有更多数据了
            if (count($invoices) < $pageSize) {
                $hasMoreData = false;
            }
            
            $page++;
            
            // 避免过快请求，添加短暂延迟
            sleep(1); // 休眠1秒
        }
    }

    /**
     * 获取特定公司的特定类型的发票数据
     * @param string $dataType 数据类型
     * @param int|string $companyId 公司ID
     * @param int $page 当前页码
     * @param int $pageSize 每页数量
     * @return array 待同步的发票数据
     */
    private function getCompanyDataByType(string $dataType, $companyId, int $page, int $pageSize, $date = null): array
    {
        $this->logger->info("获取公司 {$companyId} 的 {$dataType} 第{$page}页数据，每页{$pageSize}条");
        echo "获取公司 {$companyId} 的 {$dataType} 第{$page}页数据，每页{$pageSize}条" . PHP_EOL;

        if (!empty($date)) {
            $this->getDI()->get('invoiceDataService')->setCurrentDate($date);
        }

        // 根据数据类型和公司ID获取数据
        switch ($dataType) {
            case InvoiceEnums::ORDINARY_PAYMENT_TXT:
            case InvoiceEnums::CREDIT_ORDINARY_PAYMENT_TXT:
                $invoiceData = $this->getOrdinaryPaymentDataByCompany($companyId, $page, $pageSize, $dataType);
                break;
            case InvoiceEnums::PURCHASE_PAYMENT_TXT:
            case InvoiceEnums::CREDIT_PURCHASE_PAYMENT_TXT:
                $invoiceData = $this->getPurchasePaymentDataByCompany($companyId, $page, $pageSize, $dataType);
                break;
            case InvoiceEnums::AGENCY_PAYMENT_TXT:
            case InvoiceEnums::CREDIT_AGENCY_PAYMENT_TXT:
                $invoiceData = $this->getAgencyPaymentDataByCompany($companyId, $page, $pageSize, $dataType);
                break;
            default:
                $this->logger->error("未知的数据类型: {$dataType}");
                echo "未知的数据类型: {$dataType}" . PHP_EOL;
                $invoiceData = [];
        }
        
        if (empty($invoiceData)) {
            $this->logger->info("公司 {$companyId} 的 {$dataType} 类型 - 第{$page}页，无有效数据");
            return [];
        }

        // 加载VoucherAdapterService
        $voucherAdapter = $this->getDI()->get('VoucherAdapterFactoryService');

        // 根据数据类型转换为对应的凭证对象
        $vouchers = [];
        foreach ($invoiceData as $item) {
            try {
                $voucher = $voucherAdapter::createVoucher($companyId, $item, $dataType);
                $vouchers[] = $voucher;
            } catch (\Exception $e) {
                // 捕获异常，记录错误信息，但继续处理其他记录
                $internalId = $item['no'] ?? $item['ppno'] ?? $item['apply_no'] ?? 'unknown';
                $this->logger->error("创建凭证失败 - 类型: {$dataType}, 公司ID: {$companyId}, 内部ID: {$internalId}, 错误: " . $e->getMessage());
                echo "创建凭证失败 - 类型: {$dataType}, 公司ID: {$companyId}, 内部ID: {$internalId}, 错误: " . $e->getMessage() . PHP_EOL;

            }
        }
        return $vouchers;
    }

    /**
     * 获取特定公司的普通付款数据
     * @param int|string $companyId 公司ID
     * @param int $page 当前页码
     * @param int $pageSize 每页数量
     * @param $dataType
     * @return array 普通付款数据
     */
    private function getOrdinaryPaymentDataByCompany($companyId, int $page, int $pageSize, $dataType): array
    {
        return $this->getDI()->get('invoiceDataService')->getOrdinaryPaymentDataByCompany($companyId, $page, $pageSize, $dataType);
    }

    /**
     * 获取特定公司的采购付款数据
     * @param int|string $companyId 公司ID
     * @param int $page 当前页码
     * @param int $pageSize 每页数量
     * @param $dataType
     * @return array 采购付款数据
     */
    private function getPurchasePaymentDataByCompany($companyId, int $page, int $pageSize, $dataType): array
    {
        return $this->getDI()->get('invoiceDataService')->getPurchasePaymentDataByCompany($companyId, $page, $pageSize, $dataType);
    }

    /**
     * 获取特定公司的代理付款数据
     * @param int|string $companyId 公司ID
     * @param int $page 当前页码
     * @param int $pageSize 每页数量
     * @param $dataType
     * @return array 代理付款数据
     */
    private function getAgencyPaymentDataByCompany($companyId, int $page, int $pageSize, $dataType): array
    {
        return $this->getDI()->get('invoiceDataService')->getAgencyPaymentDataByCompany($companyId, $page, $pageSize, $dataType);
    }

    public function testAction()
    {
        // 获取应用配置（假设模块配置在 `config/modules.php`）
        $module = new Module();
        $module->registerServices($this->getDI());

        $result = $this->getDI()->get('invoiceSyncService')->test();
        var_dump($result);die;
    }

    public function testLoginAction()
    {
        // 获取应用配置（假设模块配置在 `config/modules.php`）
        $module = new Module();
        $module->registerServices($this->getDI());

        $service = $this->getDI()->get('invoiceSyncService');

        $service->test();
    }

    /**
     * @param $internalType
     * @return string
     */
    private function getInvoiceTypeName($internalType): string
    {
        $typeMap = [
            InvoiceEnums::INVOICE_TYPE_NORMAL => 'Self-Billed Invoice',
            InvoiceEnums::INVOICE_TYPE_CREDIT => 'Self-Billed Credit Note',
        ];

        return $typeMap[$internalType] ?? 'Unknown Type';
    }

    /**
     * 获取当天推送失败的数据
     * @return array 推送失败的数据
     */
    private function getTodayFailures($date): array
    {
        $db = $this->getDI()->get('db_oa');

        if (empty($date)) {
            $today = gmdate('Y-m-d');
        } else {
            $today = $date;
        }

        $sql = "SELECT 
                    id, 
                    internal_invoice_no, 
                    invoice_no, 
                    business_date, 
                    status, 
                    error_msg, 
                    created_at,
                    submission_uid,
                    uuid,
                    company_id,
                    internal_type,
                    invoice_type
                FROM 
                    invoice_submissions_detail 
                WHERE 
                    created_at >= :today 
                    AND status IN (3, 5)";
                    
        $result = $db->query($sql, ['today' => $today]);
        $failures = $result->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        
        foreach ($failures as &$failure) {
            $failure['failure_type'] = '当天推送失败';
            $failure['type_name'] = $this->getTypeName($failure['internal_type']);
        }
        
        return $failures;
    }

    /**
     * 获取长时间待提交状态的数据（2天前创建且仍然是待提交状态）
     * @return array 长时间待提交的数据
     */
    private function getLongPendingData(): array
    {
        $db = $this->getDI()->get('db_oa');
        $twoDaysAgo = gmdate('Y-m-d', strtotime('-1 days'));
        
        $sql = "SELECT 
                    id, 
                    internal_invoice_no, 
                    invoice_no, 
                    business_date, 
                    status, 
                    error_msg, 
                    created_at,
                    submission_uid,
                    uuid,
                    company_id,
                    internal_type,
                    invoice_type
                FROM 
                    invoice_submissions_detail
                WHERE 
                    created_at <= :two_days_ago
                    AND status = 1";
                    
        $result = $db->query($sql, [
            'two_days_ago' => $twoDaysAgo,
        ]);
        $longPending = $result->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        
        foreach ($longPending as &$item) {
            $item['failure_type'] = '长时间待提交';
            $item['type_name'] = $this->getTypeName($item['internal_type']);
        }
        
        return $longPending;
    }

    /**
     * 获取提交后无返回的数据（status为1或5，且uid为null或response为null）
     * @return array 提交后无返回的数据
     */
    private function getNoResponseData(): array
    {
        $db = $this->getDI()->get('db_oa');
        
        $sql = "SELECT 
                    isd.id, 
                    isd.internal_invoice_no, 
                    isd.invoice_no, 
                    isd.business_date, 
                    isd.status, 
                    isd.error_msg, 
                    isd.created_at,
                    isd.submission_uid,
                    isd.uuid,
                    isd.company_id,
                    isd.internal_type,
                    isd.invoice_type
                FROM 
                    invoice_submissions_detail isd
                JOIN invoice_submissions iss ON isd.submission_id = iss.id
                WHERE 
                    isd.status IN (1, 5)
                    AND (iss.submission_uid = '' AND iss.response IS NULL)
                    AND (isd.created_at >= :today)";
                    
        $result = $db->query($sql, ['today' => gmdate('Y-m-d')]);
        $noResponse = $result->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        
        foreach ($noResponse as &$item) {
            $item['failure_type'] = '提交后无返回';
            $item['type_name'] = $this->getTypeName($item['internal_type']);
        }
        
        return $noResponse;
    }
}