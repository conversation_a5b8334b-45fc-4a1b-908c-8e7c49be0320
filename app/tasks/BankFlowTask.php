<?php

use App\Library\Enums\DownloadCenterEnum;
use App\Library\ErrCode;
use App\Modules\BankFlow\Models\BankAccountModel;
use App\Modules\BankFlow\Models\BankListModel;
use App\Modules\BankFlow\Models\BankFlowModel;
use App\Modules\BankFlow\Models\BankFlowOaModel;
use App\Modules\BankFlow\Models\BankFlowGetDetailModel;
use App\Models\oa\PaymentFlashPayConfigModel;
use App\Modules\BankFlow\Services\ListService;
use App\Modules\BankFlow\Models\BankFlowPayDetailModel;
use App\Modules\BankFlow\Services\PayFlowService as BankFlowPayFlowService;
use App\Modules\BankFlow\Services\BaseService;
use App\Modules\Common\Models\ExcelTaskModel;
use App\Modules\Pay\Models\Payment;
use App\Modules\Common\Models\CurrencyModel;
use App\Library\Enums\StaffInfoEnums;
use App\Library\Enums\BankFlowEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Services\EnumsService;

class BankFlowTask extends BaseTask
{

    /**
     * 生成银行流水测试数据
     * @param array $params
     */
    public function gen_test_flowAction(array $params)
    {
        ini_set('memory_limit', '1024M');

        echo '开始时间 ' . date('Y-m-d H:i:s') . PHP_EOL;
        $start_time = get_curr_micro_time();
        $make_total = $params[0] ?? 5000;// 生产条数

        // 取银行和账号
        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            'bank.bank_name',
            'acct.bank_id',
            'acct.id AS acct_id',
            'acct.account',
            'acct.currency'
        ]);

        $builder->from(['bank' => BankListModel::class]);
        $builder->leftjoin(BankAccountModel::class, 'bank.id = acct.bank_id','acct');
        $builder->where('bank.is_deleted = 0');
        $builder->andWhere('acct.is_deleted = 0');
        $items = $builder->getQuery()->execute()->toArray();

        $trade_desc_item = [
            'CL-CL CHQ DEP/WTD',
            'TA-TRF FROM ATM/ETB',
            'BP-BILL PAYMENT BY CASH/TR/ELECTRONIC',
            'TR-TRF DEP/WTD',
            ''
        ];

        // 日期
        $date_item = [];
        for ($i = 0; $i < 100; $i++) {
            $date_item[] = $this->GetRandomDate('********', '********');
        }

        // 构造sql
        $common_data = [];
        for ($i = 1; $i <= $make_total; $i++) {
//            shuffle($items);
            $curr_item = $items[array_rand($items, 1)];

            $type = mt_rand(1, 2);
            $get_amount = $pay_amount = 0;
            if ($type == 1) {
                $get_amount = mt_rand(1, 10000);
            } else if ($type == 2) {
                $pay_amount = mt_rand(2, 500);
            }

            $bank_left_amount = $get_amount - $pay_amount;
            $bank_left_amount = $bank_left_amount > 0 ? $bank_left_amount : 0;

            $trade_desc = $trade_desc_item[array_rand($trade_desc_item, 1)];

            $date = $date_item[array_rand($date_item, 1)];

            // 批量入库
            $common_data[] = [
                'type' => $type,
                'bank_id' => $curr_item['bank_id'],
                'bank_name' => $curr_item['bank_name'],
                'bank_account_id' => $curr_item['acct_id'],
                'bank_account' => $curr_item['account'],
                'date' => $date,
                'time' => '0:0:0',
                'ticket_no' => $this->GetRandStr(6),
                'get_amount' => $get_amount,
                'pay_amount' => $pay_amount,
                'real_amount' => $type == 1 ? $get_amount : '-'.$pay_amount,
                'bank_left_amount' => $bank_left_amount,
                'currency' => $curr_item['currency'],
                'trade_desc' => $trade_desc,
                'created_staff_id' => 10000,
                'created_staff_name' => 'admin',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_staff_id' => 10000,
                'updated_staff_name' => 'admin',
                'updated_at' => date('Y-m-d H:i:s'),
            ];
        }

        echo '待生成 ' . count($common_data) . ' 条银行流水数据' . PHP_EOL;
        if (!empty($common_data)) {
            if ((new BankFlowModel())->batch_insert($common_data) === false) {
                echo '生成失败' . PHP_EOL;
            } else {
                echo '生成成功' . PHP_EOL;
            }
        }

        echo '共耗时 ' . get_exec_time($start_time) . PHP_EOL;
        echo '结束时间 ' . date('Y-m-d H:i:s') . PHP_EOL;
    }

    // 生成随机字符串
    protected function GetRandStr($length)
    {
        //字符组合
        $str = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        $len = strlen($str)-1;
        $rand_str = '';
        for ($i=0; $i < $length; $i++) {
            $num = mt_rand(0, $len);
            $rand_str .= $str[$num];
        }

        return $rand_str;
    }

    // 生成随机日期
    protected function GetRandomDate($begin_time, $end_time="", $is = true)
    {
        $begin = strtotime($begin_time);
        $end = $end_time == "" ? mktime() : strtotime($end_time);
        $timestamp = rand($begin, $end);

        return $is ? date("Y-m-d", $timestamp) : $timestamp;
    }


    /**
     * 处理历史数据
     * */

    public function deal_get_remark_detailAction()
    {
        //需要删除的detail id
        $get_flow_detail_ids = [];
        //模版映射费用类型
        $bank_expense_arr = ListService::getInstance()->expense_template_relation();

        //查询
        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            'bank.id',
            'bank.bank_flow_expense_id',
            'get.id AS detail_id',
        ]);

        $builder->from(['get' => BankFlowGetDetailModel::class]);
        $builder->leftjoin(BankFlowModel::class, 'get.bank_flow_id = bank.id','bank');
        $items = $builder->getQuery()->execute()->toArray();


        foreach ($items as $key => $value) {
            if (isset($bank_expense_arr[$value['bank_flow_expense_id']]) && $bank_expense_arr[$value['bank_flow_expense_id']] == BaseService::BANK_PAY_FLOW_TEMPLATE) {
                continue;
            }
            $get_flow_detail_ids[] = $value['detail_id'];

        }

        if(!empty($get_flow_detail_ids)){
            $BankFlowGetDetailModel =  BankFlowGetDetailModel::find(
                ['conditions' => "id in ({ids:array})",
                 'bind'       => ['ids' => $get_flow_detail_ids]
                ]);
            $BankFlowGetDetailModel->delete();

        }



       echo '删除成功'.count($get_flow_detail_ids).'条数';
    }

    public function deal_pay_remark_detailAction()
    {
        //需要删除的detail id
        $get_flow_detail_ids = [];
        //模版映射费用类型
        $bank_expense_arr = ListService::getInstance()->expense_template_relation();
        //查询
        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            'bank.id',
            'bank.bank_flow_expense_id',
            'pay.id AS detail_id',
        ]);

        $builder->from(['pay' => BankFlowPayDetailModel::class]);
        $builder->leftjoin(BankFlowModel::class, 'pay.bank_flow_id = bank.id','bank');
        $items = $builder->getQuery()->execute()->toArray();

        foreach ($items as $key => $value) {
            if (isset($bank_expense_arr[$value['bank_flow_expense_id']]) && $bank_expense_arr[$value['bank_flow_expense_id']] == BaseService::BANK_GET_FLOW_TEMPLATE) {
                continue;
            }
            $get_flow_detail_ids[] = $value['detail_id'];
        }


        if (!empty($get_flow_detail_ids)) {
            $BankFlowGetDetailModel = BankFlowPayDetailModel::find(
                ['conditions' => "id in ({ids:array})",
                 'bind'       => ['ids' => $get_flow_detail_ids]
                ]);
            $BankFlowGetDetailModel->delete();
        }

        echo '删除成功'.count($get_flow_detail_ids).'条数';
    }

    /**
     * 流水上传-导出流水-异步导出
     */
    public function download_bank_flowAction()
    {
        echo 'begin ' . date('Y-m-d H:i:s') . PHP_EOL;
        try {
            // 取最先进入的任务 id asc
            $excel_detail = ExcelTaskModel::findFirst([
                'conditions' => 'type = :type: and is_deleted = 0  and status = 0',
                'bind' => [
                    'type' => DownloadCenterEnum::DOWNLOAD_CENTER_BANK_FLOW_INIT_DOWN
                ],
                'order' => 'id asc',
            ]);
            if (!$excel_detail) {
                echo '没有等待下载的任务:' . PHP_EOL;
                exit();
            }
            $params = !empty($excel_detail->args_json) ? json_decode(base64_decode($excel_detail->args_json, true), true) : [];
            $this->logger->info('bank_flow_download_begin_task_id: ' . json_encode($excel_detail->id));
            $this->logger->info('bank_flow_download_begin_params: ' . json_encode($params));
            $params['export'] = BaseService::EXPORT_BANK_FLOW_TYPE_ALL;
            $list_service = ListService::getInstance();
            $list_service::setLanguage($params['language'] ?? 'en');

            // 分页导出, 每次1万条数据
            $params['pageSize'] = 10000;
            $params['pageNum'] = 1;
            $all_data = [];
            do {
                $while_log = '准备获取 第 ' . $params['pageNum'] . ' 页数据' . PHP_EOL;
                echo $while_log;
                $this->logger->info($while_log);

                unset($one_page_data, $result);
                $result = $list_service->getFlowList($params, BaseService::BANK_FLOW_TYPE_ALL, false, true);
                $one_page_data = $result['data']['items'] ?? [];

                $one_page_data_count = count($one_page_data);
                $while_log = "'第 {$params['pageNum']} 页数据, 有 {$one_page_data_count} 条" . PHP_EOL;
                echo $while_log;
                $this->logger->info($while_log);

                $all_data = array_merge($all_data, $one_page_data);
                $params['pageNum'] += 1;
            } while (!empty($one_page_data) && count($all_data) < BankFlowEnums::BANK_FLOW_ASYNC_MAX_COUNT);

            $this->logger->info('bank_flow_download_find_data_number: ' . count($all_data) . '条');

            // 生成文件
            $export_data = $list_service->handleFlowListHeader($all_data);

            if (isset($export_data['code']) && $export_data['code'] == ErrCode::$SUCCESS) {
                $excel_detail->file_download_path = $export_data['data'];
                $excel_detail->status = 1;
                $excel_detail->finish_at = date('Y-m-d H:i:s');
                $excel_detail->updated_at = date('Y-m-d H:i:s');

                if ($excel_detail->save() === false) {
                    $msg = get_data_object_error_msg($excel_detail);
                    $this->logger->error('bank_flow_download_error:' . $msg);
                }
                $all_memory = round(memory_get_usage() / 1024 / 1024, 2);
                echo '导出成功结果：' . $export_data['data'] . '内存占用:' . $all_memory . PHP_EOL;
                $this->logger->info('bank_flow_download_success: ' . count($all_data) . '条, 文件地址: ' . $export_data['data'] . ', 内存占用:' . $all_memory . 'MB');
            } else {
                // 记录错误日志
                $this->logger->warning('bank_flow_download_warning: ' . count($all_data) . '条');
            }
        } catch (Exception $e) {
            $this->logger->error('bank_flow_download_error:' . $e->getMessage());
            echo 'error:' . $e->getMessage() . PHP_EOL;
        }

        echo 'end ' . date('Y-m-d H:i:s') . PHP_EOL;
        exit();
    }

    /**
     * 银行流水-流水管理-收款流水-异步导出
     */
    public function download_bank_get_flowAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $this->check_process($process_name);
        echo 'begin' . PHP_EOL;
        try {
            // 取最先进入的任务 id asc
            $excel_detail = ExcelTaskModel::findFirst([
                'conditions' => 'type = :type: and is_deleted = 0  and status = 0',
                'bind' => [
                    'type' => DownloadCenterEnum::DOWNLOAD_CENTER_BANK_FLOW_GAT_EXPORT
                ],
                'order' => 'id asc',
            ]);
            if (!$excel_detail) {
                echo '没有等待下载的任务:' . PHP_EOL;
                exit();
            }
            $params = !empty($excel_detail->args_json) ? json_decode(base64_decode($excel_detail->args_json, true), true) : [];
            $this->logger->info('bank_get_flow_download_begin_task_id: ' . json_encode($excel_detail->id));
            $this->logger->info('bank_get_flow_download_begin_params: ' . json_encode($params));
            $list_service = ListService::getInstance();
            $list_service::setLanguage($params['language'] ?? 'en');
            // 分页导出, 每次1万条数据
            $params['pageSize'] = 10000;
            $params['pageNum'] = 1;
            $all_data = [];
            do {
                unset($one_page_data, $result);
                $result = $list_service->getFlowList($params, BaseService::BANK_FLOW_TYPE_GET, true);

                $one_page_data = $result['data']['items'] ?? [];
                $all_data = array_merge($all_data, $one_page_data);
                $params['pageNum'] += 1;
            } while (!empty($one_page_data) && count($all_data) < BankFlowEnums::BANK_FLOW_ASYNC_MAX_COUNT);

            $this->logger->info('bank_get_flow_download_find_data_number: ' . count($all_data) . '条');

            // 生成文件
            $export_data = $list_service->exportFlow($params, BaseService::BANK_FLOW_TYPE_GET, $all_data);
            if (isset($export_data['code']) && $export_data['code'] == ErrCode::$SUCCESS) {
                $excel_detail->file_download_path = $export_data['data']['file_url'];
                $excel_detail->status = 1;
                $excel_detail->finish_at = date('Y-m-d H:i:s');
                $excel_detail->updated_at = date('Y-m-d H:i:s');

                if ($excel_detail->save() === false) {
                    $msg = get_data_object_error_msg($excel_detail);
                    $this->logger->error('bank_get_flow_download_error:' . $msg);
                }
                $all_memory = round(memory_get_usage() / 1024 / 1024, 2);
                echo '导出成功结果：' . json_encode($export_data, JSON_UNESCAPED_UNICODE) . '内存占用:' . $all_memory . PHP_EOL;
                $this->logger->info('bank_get_flow_download_success: ' . count($all_data) . '条, 返回: ' . json_encode($export_data, JSON_UNESCAPED_UNICODE) . ', 内存占用:' . $all_memory . 'MB');
            } else {
                // 记录错误日志
                $this->logger->warning('bank_get_flow_download_warning: ' . count($all_data) . '条');
            }
        } catch (Exception $e) {
            $this->logger->error('bank_flow_download_error:' . $e->getMessage());
            echo 'error:' . $e->getMessage() . PHP_EOL;
        }
        echo 'end' . PHP_EOL;
        exit();
    }

    /**
     * 银行流水-流水管理-付款流水-异步导出
     */
    public function download_bank_pay_flowAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $this->check_process($process_name);
        echo 'begin' . PHP_EOL;
        try {
            // 取最先进入的任务 id asc
            $excel_detail = ExcelTaskModel::findFirst([
                'conditions' => 'type = :type: and is_deleted = 0  and status = 0',
                'bind' => [
                    'type' => DownloadCenterEnum::DOWNLOAD_CENTER_BANK_FLOW_PAY_EXPORT
                ],
                'order' => 'id asc',
            ]);
            if (!$excel_detail) {
                echo '没有等待下载的任务:' . PHP_EOL;
                exit();
            }
            $params = !empty($excel_detail->args_json) ? json_decode(base64_decode($excel_detail->args_json, true), true) : [];
            $this->logger->info('bank_pay_flow_download_begin_task_id: ' . json_encode($excel_detail->id));
            $this->logger->info('bank_pay_flow_download_begin_params: ' . json_encode($params));
            $list_service = ListService::getInstance();
            $list_service::setLanguage($params['language'] ?? 'en');

            // 分页导出, 每次1万条数据
            $params['pageSize'] = 10000;
            $params['pageNum'] = 1;
            $all_data = [];
            do {
                unset($one_page_data, $result);
                $result = $list_service->getFlowList($params, BaseService::BANK_FLOW_TYPE_PAY, true);

                $one_page_data = $result['data']['items'] ?? [];
                $all_data = array_merge($all_data, $one_page_data);
                $params['pageNum'] += 1;
            } while (!empty($one_page_data) && count($all_data) < BankFlowEnums::BANK_FLOW_ASYNC_MAX_COUNT);

            $this->logger->info('bank_pay_flow_download_find_data_number: ' . count($all_data) . '条');
            // 生成文件
            $export_data = $list_service->exportFlow($params, BaseService::BANK_FLOW_TYPE_PAY, $all_data);
            if (isset($export_data['code']) && $export_data['code'] == ErrCode::$SUCCESS) {
                $excel_detail->file_download_path = $export_data['data']['file_url'];
                $excel_detail->status = 1;
                $excel_detail->finish_at = date('Y-m-d H:i:s');
                $excel_detail->updated_at = date('Y-m-d H:i:s');

                if ($excel_detail->save() === false) {
                    $msg = get_data_object_error_msg($excel_detail);
                    $this->logger->error('bank_pay_flow_download_error:' . $msg);
                }
                $all_memory = round(memory_get_usage() / 1024 / 1024, 2);
                echo '导出成功结果：' . json_encode($export_data, JSON_UNESCAPED_UNICODE) . '内存占用:' . $all_memory . PHP_EOL;
                $this->logger->info('bank_pay_flow_download_success: ' . count($all_data) . '条, 返回: ' . json_encode($export_data, JSON_UNESCAPED_UNICODE) . ', 内存占用:' . $all_memory . 'MB');
            } else {
                // 记录错误日志
                $this->logger->warning('bank_pay_flow_download_warning: ' . count($all_data) . '条');
            }
        } catch (Exception $e) {
            $this->logger->error('bank_pay_flow_download_error:' . $e->getMessage());
            echo 'error:' . $e->getMessage() . PHP_EOL;
        }
        echo 'end' . PHP_EOL;
        exit();
    }

    /**
     * 同步支付模块-flashpay-在线支付-银行流水到银行流水
     * 每天下午6点执行一次
     * @param array $params 参数组
     * @throws Exception
     */
    public function sync_flashpay_dataAction($params)
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log = '任务名称: ' . $process_name . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s')  . PHP_EOL;
        $log .= '初始内存: ' . memory_usage() . PHP_EOL . PHP_EOL;

        //登陆pay的sftp服务，开始拷贝文件到本地
        $host = env('flash_pay_sftp_ip'); // SSH服务器地址
        $port = env('flash_pay_sftp_port'); // SSH端口号

        //路径：/out/accountingflow/all/yyyyMMdd
        $day = $params[0] ?? 1;//不指定日期则默认跑前一天的
        $remote_dir = '/out/accountingflow/all/' . date('Ymd', strtotime('-' . $day . ' days')); // 远程目录
        $local_dir = sys_get_temp_dir() . '/';//本地目录

        $flash_pay_config = PaymentFlashPayConfigModel::find(['columns' => 'flashpay_sftp_shopname, flashpay_sftp_username, flashpay_sftp_password'])->toArray();
        $flashpay_sftp_shopname = $params[1] ?? '';//不指定商户则默认跑所有的
        foreach ($flash_pay_config as $item) {
            if ($flashpay_sftp_shopname && $item['flashpay_sftp_shopname'] != $flashpay_sftp_shopname) {
                continue;
            }
            $log .= '商户: ' . $item['flashpay_sftp_shopname'] . ' 银行流水文件获取开始' . PHP_EOL;
            // 创建SSH连接[default_socket_timeout 默认 60s，看看超时时间，需要调整不]
            $connection = ssh2_connect($host, $port);
            if (!$connection) {
                $this->logger->warning('银行流水模块-导入FlashPay在线支付银行流水-任务: Unable to connect to SSH server');
                die($log . 'Unable to connect to SSH server' . PHP_EOL);
            }
            $log .= 'connect to SSH server success' . PHP_EOL;

            // 认证SSH连接
            if (!ssh2_auth_password($connection, $item['flashpay_sftp_username'], $item['flashpay_sftp_password'])) {
                $this->logger->warning('银行流水模块-导入FlashPay在线支付银行流水-任务: Unable to authenticate SSH connection');
                $log .= 'Unable to authenticate SSH connection' . PHP_EOL;
                continue;
            }
            $log .= 'authenticate SSH connection success' . PHP_EOL;

            // 执行sftp命令
            $sftp = ssh2_sftp($connection);

            // 下载远程目录下的所有文件
            $files = scandir("ssh2.sftp://{$sftp}{$remote_dir}");
            foreach ($files as $file) {
                if ($file === '.' || $file === '..') {
                    continue;
                }
                $remote_file = "{$remote_dir}/{$file}";
                $local_file = "{$local_dir}/{$file}";
                if (!copy("ssh2.sftp://{$sftp}{$remote_file}", $local_file)) {
                    $this->logger->warning("银行流水模块-导入FlashPay在线支付银行流水-任务: Unable to download file: {$remote_file}");
                    die($log . "Unable to download file: {$remote_file}" . PHP_EOL);
                }
            }
            $log .= 'All files have been downloaded successfully' . PHP_EOL;

            //关闭连接
            ssh2_disconnect($connection);

            // 开始存储银行流水
            $this->operateBankFlow($item['flashpay_sftp_shopname'], $log);
            $log .= '商户: ' . $item['flashpay_sftp_shopname'] . ' 银行流水文件获取结束' . PHP_EOL . PHP_EOL;
        }
        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        exit($log);
    }

    /**
     * 遍历银行流水文件，写入银行流水表
     * @param string $shop_name 商户号也就是oa的银行账号
     * @param string $log 操作日志
     * @throws Exception
     */
    public function operateBankFlow($shop_name, &$log)
    {
        $log .= '写入银行流水操作处理开始' . PHP_EOL;
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        $is_exception = false;
        $message = '';
        $link_file = '';
        //本地目录
        $local_dir = sys_get_temp_dir() . '/';
        try {
            //获取银行账号信息
            $bank_account_info = BankAccountModel::findFirst([
                'conditions' => 'account = :account:',
                'bind' => ['account' => $shop_name]
            ]);
            if (empty($bank_account_info)) {
                throw new ValidationException('bank_account 中未找到account=' . $shop_name . ' 账号信息', ErrCode::$VALIDATE_ERROR);
            }
            //获取币种信息
            $currency = CurrencyModel::find([
                'columns' => 'id,currency_symbol'
            ])->toArray();
            $currency_enums = array_column($currency, 'id', 'currency_symbol');

            //银行流水费用类型id（费用表支出-OA）
            $bank_flow_expense_id = EnumsService::getInstance()->getSettingEnvValue('bank_flow_flashpay_expense_id', 0);

            // 开始遍历本地目录下的所有文件
            $files = scandir($local_dir);
            foreach ($files as $file) {
                //文件名：如果单个表格没有超过8万条数据，文件名称是Accounting Flow-UserNo-YYYYMMDD.xlsx
                //文件名：如果单个表格没有超过8万条数据，比如是10万条，文件名称分别是Accounting Flow-UserNo-YYYYMMDD-1.xlsx  和Accounting Flow-UserNo-YYYYMMDD-2.xlsx
                if ($file === '.' || $file === '..' || strpos($file, 'Accounting Flow-' . $shop_name . '-') === false) {
                    continue;
                }
                //解析文件
                $config = ['path' => $local_dir];
                $excel = new \Vtiful\Kernel\Excel($config);
                $excel_data = $excel->openFile($file)
                    ->openSheet()
                    ->setType([
                        '2' => \Vtiful\Kernel\Excel::TYPE_STRING,// 银行账号, 可能会有前导0
                    ])
                    ->getSheetData();
                //弹出excel标题第一行信息
                array_shift($excel_data);
                // 读取每行单元格数据时，可指定每个单元格数据类型进行读取
                if (empty($excel_data)) {
                    //若为空文件，说明当天没有产生交易记录;直接跳过不处理
                    continue;
                }

                //解析完的文件需要从临时目录删除
                $link_file = $file;
                $now = date('Y-m-d H:i:s');
                $log .= $file . ' 文件数据处理开始' . PHP_EOL;
                foreach ($excel_data as $key => $item) {
                    $trade_no = trim($item[1]);//pay交易号
                    $back_account = trim($item[2]);//银行账号
                    $datetime = trim($item[3]);//记账时间

                    $type = trim($item[4]);//流水类型;0-无，1-付款，2-收款
                    if (!in_array($type, [BankFlowEnums::BANK_FLOW_TYPE_GET_INCOME, BankFlowEnums::BANK_FLOW_TYPE_PAY_OUTLAY])) {
                        //SFTP文件里读取到流水类型为0，我们OA这边不写进银行流水表
                        continue;
                    }
                    //由于我们这边的银行流水类型:1:收款;2:付款，跟pay刚好反过来，所以这里翻转下
                    $type = ($type == 1) ? BankFlowEnums::BANK_FLOW_TYPE_PAY_OUTLAY : BankFlowEnums::BANK_FLOW_TYPE_GET_INCOME;

                    $amount = trim($item[5]);//流水金额
                    $bank_left_amount = trim($item[6]);//账户余额
                    $currency = trim($item[7]);//币种
                    //循环判断pay那边的必填参数
                    if (empty($datetime) || !is_numeric($amount) || !is_numeric($bank_left_amount) || empty($currency)) {
                        throw new ValidationException($file . ' 第' . ($key+1) . '行，交易文件信息缺失', ErrCode::$VALIDATE_ERROR);
                    } else if (!preg_match('/^\d\d\d\d-\d?\d-\d?\d \d\d:\d\d:\d\d$/', $datetime)) {
                        throw new ValidationException($file . ' 第' . ($key+1) . '行，交易日期格式错误，请确保日期格式为YYYY-MM-DD HH:II:SS', ErrCode::$VALIDATE_ERROR);
                    } else if ($back_account != $shop_name) {
                        throw new ValidationException($file . ' 第' . ($key+1) . '行，银行账号不是: ' . $shop_name);
                    }
                    $bank_flow_data = [
                        'type' => $type,//1收入，2支出
                        'bank_id' => $bank_account_info->bank_id,//银行id
                        'bank_name' => 'Flash Pay',//银行名字
                        'bank_account_id' => $bank_account_info->id,//银行账号id
                        'bank_account' => $back_account,//银行账号
                        'date' => substr($datetime, 0, 10),//交易日期（年月日）
                        'time' => substr($datetime, 11, 19),//交易时间（时分秒）
                        'ticket_no' => '',//支票号
                        'get_amount' => ($type == BankFlowEnums::BANK_FLOW_TYPE_GET_INCOME) ? $amount : 0.00,//收款金额【若pay流水类型=1】存储，否则0
                        'pay_amount' => ($type == BankFlowEnums::BANK_FLOW_TYPE_PAY_OUTLAY) ? $amount : 0.00,//付款金额【若pay流水类型=2】存储，否则0
                        'real_amount' => ($type == BankFlowEnums::BANK_FLOW_TYPE_PAY_OUTLAY) ? '-' . $amount : $amount,//若pay流水类型=1存收款金额，否则存 -付款金额
                        'bank_left_amount' => $bank_left_amount,//银行账号余额 = pay账户余额
                        'currency' => $currency_enums[$currency],//币种
                        'trade_desc' => $trade_no,//描述
                        'bank_flow_expense_id' => ($type == BankFlowEnums::BANK_FLOW_TYPE_GET_INCOME) ? 0 : $bank_flow_expense_id,//银行流水费用类型id（费用表支出-OA）
                        'edit_status' => BankFlowEnums::BANK_FLOW_UNCONFIRMED_STATUS,//编辑状态，1待编辑
                        'confirm_status' => BankFlowEnums::BANK_FLOW_UNCONFIRMED_STATUS,//确认状态，1待确认
                        'created_staff_id' => StaffInfoEnums::SUPER_ADMIN_STAFF_ID,
                        'created_staff_name' => StaffInfoEnums::SUPER_ADMIN_STAFF_NAME,
                        'updated_staff_id' => StaffInfoEnums::SUPER_ADMIN_STAFF_ID,
                        'updated_staff_name' => StaffInfoEnums::SUPER_ADMIN_STAFF_NAME,
                        'created_at' => $now,
                        'updated_at' => $now
                    ];
                    //插入银行流水
                    $bank_flow = new BankFlowModel();
                    $bool = $bank_flow->i_create($bank_flow_data);
                    if ($bool === false) {
                        throw new BusinessException('online pay into bank_flow error : ' . json_encode($bank_flow_data, JSON_UNESCAPED_UNICODE) . ';可能的原因是：' . get_data_object_error_msg($bank_flow), ErrCode::$BUSINESS_ERROR);
                    }

                    //付款类型的流水号，需要根据pay的交易号去payment表查询oa的单据号
                    if ($type == BankFlowEnums::BANK_FLOW_TYPE_PAY_OUTLAY && !empty($trade_no)) {
                        $payment = Payment::findFirst([
                            'columns' => 'oa_type,no,amount_total_actually',
                            'conditions' => 'out_trade_no = :out_trade_no:',
                            'bind' => ['out_trade_no' => $trade_no]
                        ]);
                        if (empty($payment)) {
                            //18891需求，非oa的业务单据支付流水，费用类型要为0
                            $bank_flow->bank_flow_expense_id = 0;
                            $bank_flow->updated_at = $now;
                            $bool = $bank_flow->save();
                            if ($bool === false) {
                                throw new BusinessException('online pay update bank_flow error : ' . json_encode($bank_flow->toArray(), JSON_UNESCAPED_UNICODE) . ';可能的原因是：' . get_data_object_error_msg($bank_flow), ErrCode::$BUSINESS_ERROR);
                            }
                            //非oa的业务单据找不到支付单据时付款的单据无需关联系统单号直接跳过
                            continue;
                        }

                        $bank_flow_oa_data = [
                            'bank_flow_id' => $bank_flow->id,//银行流水id
                            'oa_type' => $payment->oa_type,//关联模块oa_type
                            'no' => $payment->no,//关联单号
                            'diff_amount' => bcsub($bank_flow->pay_amount, $payment->amount_total_actually, 2),//这笔流水的付款金额（pay_amount）-OA的实付金额总计（payment表中amount_total_actually），保留2位小数
                            'diff_info' => '',//差异说明
                            'is_cancel' => 0,//是否撤销，0不是，1是
                            'is_sure' => 0,//可抵扣确认，0不是，1是
                            'created_id' => $bank_flow->created_staff_id,//添加人
                            'updated_id' => $bank_flow->created_staff_id,//更新人
                            'bank_account_id' => $bank_flow->bank_account_id,//银行流水交易账号id
                            'date' => $bank_flow->date,//银行流水交易日期
                            'created_at' => $bank_flow->created_at,
                            'updated_at' => $bank_flow->created_at
                        ];

                        //获取支付模块关联的模块的业务单据信息
                        $model = BankFlowPayFlowService::getInstance()->getModelByTypeId($payment->oa_type);
                        $model = $model->getModelByNo($payment->no);
                        if (empty($model)) {
                            //根据单据号未找到业务模块单据，无需处理，直接跳过
                            continue;
                        }
                        $format_data = $model->getFormatData();
                        $bank_flow_oa_data['oa_value'] = $format_data['oa_value'];//关联模块主键id
                        $bank_flow_oa_data['amount'] = $format_data['amount'];//金额
                        $bank_flow_oa_data['currency'] = $format_data['currency'];//币种
                        $bank_flow_oa_data['real_amount'] = GlobalEnums::getAmountByDefaultRate($format_data['amount'], $format_data['currency'], $bank_flow->currency);

                        $bank_flow_oa = new BankFlowOaModel();
                        $bool = $bank_flow_oa->i_create($bank_flow_oa_data);
                        if ($bool === false) {
                            throw new BusinessException('online pay bank_flow_oa error : ' . json_encode($bank_flow_oa_data, JSON_UNESCAPED_UNICODE) . ';可能的原因是：' . get_data_object_error_msg($bank_flow_oa), ErrCode::$BUSINESS_ERROR);
                        }

                        //关联单据成功，需要把银行流水的一些状态做变更操作
                        $bank_flow->edit_status = BankFlowEnums::BANK_FLOW_CONFIRMED_STATUS;//编辑状态，2已编辑
                        $bank_flow->confirm_status = BankFlowEnums::BANK_FLOW_CONFIRMED_STATUS;//确认状态，2已确认
                        $bank_flow->updated_staff_id = StaffInfoEnums::SUPER_ADMIN_STAFF_ID;//更新人工号
                        $bank_flow->updated_staff_name = StaffInfoEnums::SUPER_ADMIN_STAFF_NAME;//更新人姓名
                        $bank_flow->updated_at = $now;
                        $bool = $bank_flow->save();
                        if ($bool === false) {
                            throw new BusinessException('online pay update bank_flow error : ' . json_encode($bank_flow->toArray(), JSON_UNESCAPED_UNICODE) . ';可能的原因是：' . get_data_object_error_msg($bank_flow), ErrCode::$BUSINESS_ERROR);
                        }
                    }
                }

                //按照最后一笔流水更新银行账号余额
                $last_data = end($excel_data);
                $bank_account_info->left_amount = $last_data[6];//剩余金额（最后一笔流水的账户余额）
                $bank_account_info->left_amount_date = substr(trim($last_data[3]), 0, 10);//账号余额更新日期(最后一笔流水的交易日期)
                $bank_account_info->updated_id = StaffInfoEnums::SUPER_ADMIN_STAFF_ID;//更新人工号
                $bank_account_info->updated_at = $now;
                $bool = $bank_account_info->save();
                if ($bool === false) {
                    throw new BusinessException('online pay 更新银行账户余额 error : ' . json_encode($bank_account_info->toArray(), JSON_UNESCAPED_UNICODE) . ';可能的原因是：' . get_data_object_error_msg($bank_account_info), ErrCode::$BUSINESS_ERROR);
                }

                $log .= $file . ' 数据处理完毕' . PHP_EOL;

                //操作完要删掉该文件
                unlink($local_dir . $file);
                $log .= $file . ' 已从临时目录删除' . PHP_EOL;
            }

            $db->commit();
            $log .= '写入银行流水操作处理完毕' . PHP_EOL;
        } catch (ValidationException $e) {
            $message = $e->getMessage();
            $is_exception = true;
        } catch (BusinessException $e) {
            $message = $e->getMessage();
            $is_exception = true;
        } catch (Exception $e) {
            $message = $e->getMessage();
            $is_exception = true;
        }

        if ($is_exception) {
            $db->rollback();
            $log .= $message . PHP_EOL;
            $log .= $link_file . ' 文件内容有异常未处理；所有操作事务已回滚' . PHP_EOL;
            if (!empty($link_file)) {
                //操作完要删掉该文件
                unlink($local_dir . $link_file);
                $log .= $link_file . ' 已从临时目录删除' . PHP_EOL;
            }
            $this->logger->warning('银行流水模块-导入FlashPay在线支付银行流水-任务: ' . $log);
        } else {
            $this->logger->info('银行流水模块-导入FlashPay在线支付银行流水-任务: ' . $log);
        }
    }
}