<?php

use App\Library\ApiClient;
use App\Library\OssHelper;

class ArchiveTask extends BaseTask
{
    /**
     * php app/cli archive import cache/contract_archive
     * 目录以项目根目录为基准，如 OA/cache/contract_archive,传入cache/contract_archive
     * @param $params
     */
    public function importAction($params)
    {
        set_time_limit(0);
        $dir = $params[0] ?? '';
        $from_dir = BASE_PATH . '/' . $dir;
        if(!is_dir($from_dir)){
            exit('the directory is not exist!');
        }
        $insert_data = [];
        echo 'start' . PHP_EOL;
        $from_files = scandir($from_dir);
        if (!empty($from_files)) {
            foreach ($from_files as $file) {
                if($file == '.' || $file == '..' || $file == '.DS_Store'){
                    continue;
                }
                if (is_dir($from_dir . '/' . $file)) {
                    exit($file . '非文件' . PHP_EOL);
                }
                if (pathinfo($file, PATHINFO_EXTENSION) != 'pdf') {
                    exit($file . '文件格式错误' . PHP_EOL);
                }
                $file_name = pathinfo($file, PATHINFO_FILENAME);
                $file_arr = explode('-', $file_name);
                if (count($file_arr) != 4) {
                    exit($file . '文件名称有误' . PHP_EOL);
                }
                [$type, $cno, $cname, $approval_at] = $file_arr;
                if (empty($type)) {
                    exit($file . '文件名称第一部分有误' . PHP_EOL);
                }
                $type = $this->getType($type);
                $cno = trim($cno);
                if (strpos($cno, 'FEX') === false || strlen($cno) != 11) {
                    exit($file . '文件名称第二部分有误' . PHP_EOL);
                }
                $cname = trim($cname);
                if (empty($cname)) {
                    exit($file . '文件名称第三部分有误' . PHP_EOL);
                }
                $approval_at = trim($approval_at);
                if (empty(strtotime($approval_at))) {
                    exit($file . '文件名称第四部分有误' . PHP_EOL);
                }
                $contract_file = $this->upload_oss($file, $from_dir . '/' . $file);
                $temp = [
                    'cno' => $cno,
                    'cname' => $cname,
                    'template_id' => $type,
                    'approved_at' => $approval_at,
                    'status' => 2,
                    'is_master' => 1,
                    'filing_at' => date('Y-m-d H:i:s'),
                    'contract_file' => $contract_file,
                ];
                $insert_data[] = $temp;
            }
            $this->insert_db($insert_data);
        }
        echo 'end' . PHP_EOL;
        exit();
    }

    /**
     * @param $type
     * @return int
     */
    private function getType($type)
    {
        $template = 0;
        $type = trim($type);
        $type = explode(' ', $type)[0] ?? '';
        switch ($type) {
            case 'Material':
                $template = 11;
                break;
            case 'Equipment':
                $template = 12;
                break;
            case 'Decoration':
                $template = 13;
                break;
            case 'Training':
                $template = 14;
                break;
            case 'Other':
                $template = 15;
                break;
            default:
                break;
        }
        return $template;
    }

    /**
     * @param $file
     * @param $upload_file
     * @return string
     * @throws \App\Library\Exception\BusinessException
     */
    private function upload_oss($file, $upload_file)
    {

        if (env('break_away_from_ms')) {
            $return['result'] = OssHelper::uploadFileHcm($file);
        } else {
            $return['result'] = OssHelper::uploadFileFle($file, 'oca');
        }

        $put_url = $return['result']['put_url'] ?? '';
        $res = $this->curlPut($put_url, $upload_file, ['Content-Type: application/pdf']);
        if ($res['code'] != 1) {
            exit($res['resp'] . PHP_EOL);
        }
        $bucket = $return['result']['bucket_name'] ?? '';
        $obj_key = $return['result']['object_key'] ?? '';
        return $file . '@' . $bucket . '@' . $obj_key;

    }

    private function curlPut($destUrl, $sourceFileDir, $headerArr = array(), $timeout = 10)
    {
        $ch = curl_init(); //初始化curl
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true); //返回字符串,而不直接输出
        curl_setopt($ch, CURLOPT_URL, $destUrl); //设置put到的url
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headerArr);
        curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); //不验证对等证书
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0); //不检查服务器SSL证书

        curl_setopt($ch, CURLOPT_PUT, true); //设置为PUT请求
        curl_setopt($ch, CURLOPT_INFILE, fopen($sourceFileDir, 'rb')); //设置资源句柄
        curl_setopt($ch, CURLOPT_INFILESIZE, filesize($sourceFileDir));

        $response = curl_exec($ch);
        $error = curl_error($ch) ?? $response;
        if ($error)
        {
            $bkArr =  array(
                'code' => 0,
                'msg' => $error,
            );
        }
        else
        {
            $bkArr =  array(
                'code' => 1,
                'msg' => 'ok',
                'resp' => $response,
            );
        }

        curl_close($ch); // 关闭 cURL 释放资源

        return $bkArr;
    }

    /**
     * @param $insert_data
     * @return bool
     */
    private function insert_db($insert_data)
    {
        if (empty($insert_data)) {
            exit('没有可入库的数据' . PHP_EOL);
        }
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $res = (new \App\Modules\Contract\Models\ContractArchive())->batch_insert($insert_data);
            if ($res === false) {
                $db->rollback();
                throw new Exception('insert failed');
            }
        } catch (Exception $e) {
            $db->rollback();
            exit($e->getMessage() . PHP_EOL);
        }
        $db->commit();
        return true;
    }
}
