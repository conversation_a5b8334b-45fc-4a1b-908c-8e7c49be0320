<?php

use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Organization\Models\HrStaffInfoModel;
use App\Modules\Organization\Models\HrStaffInfoPositionModel;


/**
 * Author: Bruce
 * Date  : 2021-12-23 15:45
 * Description:
 */



class ExtinguisherPermissionTask extends BaseTask
{

    /**
     * 批量开通oa权限
     * @params $params 命令行参数
     * PS:
     * 1. 第一个参数: all: 全量员工; incr: 增量员工(默认增量: hr_staff_info 表 24 小时内添加的)
     * 2. 第二个参数: 权限配置key; (setting_env, 默认:batch_extinguisher_permission_ids)
     *
     * @param array $params
     */
    public function add_extinguisher_staff_permissionAction($params = [])
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log = '任务名称: ' . $process_name . ' ------ 系统所在国家: ' . get_country_code() . ' ----- ' . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        try {

            // 取数条件初值
            $get_staff_type = isset($params[0]) && $params[0] == 'all' ? 'all' : 'incr';
            $permission_code = isset($params[1]) ? (string)$params[1] : 'batch_extinguisher_permission_ids';
            $log .= "传入参数: get_staff_type = $get_staff_type, permission_code = $permission_code" . PHP_EOL;
            $this->check_process($process_name);

            // 取权限配置
            $permission_config      = EnvModel::getEnvByCode($permission_code);
            $log .= "待配置的权限: $permission_config" . PHP_EOL;

            $permission_config      = json_decode($permission_config, true);

            $roleIds = $permission_config['role_ids'];
            $permission_config = $permission_config['permission'];

            // 未配置权限
            if (empty($permission_config)) {
                throw new ValidationException("setting_env[{$permission_code}] 未配置 或 配置内容为空", ErrCode::$VALIDATE_ERROR);
            }
            $page = 0;
            $page_size = 1000;
            $batch_count = 0;

            $staff_info_data = $this->getStaffInfoResult($page, $page_size, $get_staff_type, $roleIds, $permission_config);
            $log .= PHP_EOL . "处理批次[batch=$batch_count]: start = $page, per_length = $page_size, 本批次待处理员工数[From HR]: " . count($staff_info_data) . PHP_EOL;


            // 对员工分批次处理权限
            while (!empty($staff_info_data)) {
                $staff_info_ids = array_keys($staff_info_data);
                $staff_permission_data = \App\Modules\User\Models\StaffPermissionModel::find([
                    "conditions" => 'staff_id in ({ids:array})',
                    "bind"       => ['ids' => $staff_info_ids]
                ])->toArray();
                $permission_staff_ids  = array_column($staff_permission_data, 'staff_id');
                $permission_ids        = array_column($staff_permission_data, 'permission_ids', 'staff_id');

                $log .= '已有OA账号的员工数[In OA]: ' . count($staff_permission_data) . PHP_EOL;

                // 计算 hr工号 与 OA已分配权限工号的交集
                $intersect = array_intersect($staff_info_ids, $permission_staff_ids);

                // 计算 hr 新增的工号(取差集)
                $add_staff_permission = array_diff($staff_info_ids, $intersect);

                $last_updated_at = date("Y-m-d H:i:s");

                $addList = [];

                $log .= PHP_EOL . '需在OA新增的员工数[Insert OA]: ' . count($add_staff_permission) . PHP_EOL;

                if (!empty($add_staff_permission)) {
                    // 给 hr 新员工 分配预设置的权限
                    foreach ($add_staff_permission as $k => $v) {
                        $addList[] = [
                            'staff_id'        => $v,
                            'permission_ids'  => implode(',', $staff_info_data[$v]),
                            'is_granted'      => 1,
                            'last_updated_at' => $last_updated_at
                        ];
                    }
                }

                // *** 本批次员工权限处理开始*** //
                $db_oa  = $this->getDI()->get('db_oa');
                $db_oa->begin();
                try {
                    if (!empty($addList)) {
                        $res = '';
                        $res = (new \App\Modules\User\Models\StaffPermissionModel())->batch_insert($addList);

                        if ($res === false) {
                            throw new Exception('本批次批量新增失败: 员工共 ' . count($addList) . '个, 明细: ' . json_encode($addList, JSON_UNESCAPED_UNICODE));
                        }

                        $log .= '本批次批量新增成功 '.count($addList) . ' 个' . PHP_EOL;
                    }

                    $log .= PHP_EOL . '需在OA更新的员工数[Update OA]: ' . count($intersect) . PHP_EOL;

                    // 需要更新的人
                    if (!empty($intersect)) {
                        foreach ($intersect as $k => $v) {
                            // 员工在OA已有的权限
                            $permission_id_arr = explode(',', $permission_ids[$v]);

                            // 已有权限 与 待分配权限全集
                            $permission_id_arrs = array_unique(array_merge($permission_id_arr, $staff_info_data[$v]));

                            // 员工本次待追加的权限
                            $permission_diff_ids = array_diff($permission_id_arrs, $permission_id_arr);
                            if (!$permission_diff_ids) {
                                $log .= '权限无新增, 不需更新, 跳过: staff_id = ' . $v . PHP_EOL;
                                continue;
                            }

                            // 待追加权限
                            $permission_v_ids  = '';
                            $permission_v_ids  = implode(",", $permission_id_arrs);

                            $sql               = "update staff_permission set `permission_ids`='{$permission_v_ids}',`last_updated_at`='{$last_updated_at}'  where staff_id= {$v}";
                            $flag              = $db_oa->execute($sql);
                            if (empty($flag)) {
                                throw new Exception("权限更新失败: staff_id = $v, permission = $permission_v_ids");
                            }

                            $log .= "权限更新成功: staff_id = $v, permission = $permission_v_ids" . PHP_EOL;
                        }
                    }

                    $db_oa->commit();

                }  catch (\Exception $e) {
                    $db_oa->rollback();

                    $log .= "本批次权限新增/更新异常[已跳过该批次]: " . $e->getMessage() . PHP_EOL;
                }
                // *** 本批次员工权限处理结束*** //

                sleep(5);

                // 取下一批次工号
                $page++;
                $batch_count++;
                $staff_info_data = $this->getStaffInfoResult($page, $page_size, $get_staff_type, $roleIds, $permission_config);
                $log .= PHP_EOL . "处理批次[batch=$batch_count]: start = $page, per_length = $page_size, 本批次待处理员工数[From HR]: " . count($staff_info_data) . PHP_EOL;

            }

        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;
        } catch (\Exception $e) {
            $log .= $e->getMessage() . PHP_EOL;
        }

        $log .= "结束时间: " . date('Y-m-d H:i:s') . PHP_EOL;
        $this->getDI()->get('logger')->info("add-extinguisher-staff-permission " . $log);
        exit($log);
    }

    /**
     * 分批查询，并按工号维度，处理一个员工多个角色所拥有的权限，进行合并去重。
     * @param $page
     * @param $page_size
     * @param $get_staff_type
     * @param $roleIds
     * @param $permission_config
     * @return array
     */
    public function getStaffInfoResult($page, $page_size, $get_staff_type, $roleIds, $permission_config)
    {
        $builder = $this->getStaffInfoQuery($get_staff_type, $roleIds);
        $builder->limit($page_size, $page_size * $page);
        //order by 执行计划：Using where; Using temporary; Using filesort
        //根据数据量调整。
        $builder->orderBy('hsi.id DESC');
        $staff_info_data =  $builder->getQuery()->execute()->toArray();


        $staff_info_permission = [];
        foreach ($staff_info_data as $oneKey => $oneStaff) {
            $positionCategory = explode(',', $oneStaff['position_category']);
            foreach ($positionCategory as $onePosition){
                if(isset($staff_info_permission[$oneStaff['staff_info_id']])) {
                    $staff_info_permission[$oneStaff['staff_info_id']] = array_values(array_unique(array_merge($staff_info_permission[$oneStaff['staff_info_id']], $permission_config['role_id_' . $onePosition])));
                } else {
                    $staff_info_permission[$oneStaff['staff_info_id']] = $permission_config['role_id_' . $onePosition];
                }
            }
        }
        return $staff_info_permission;
    }

    /**
     * 查询员工角色信息
     * 员工状态：在职 + 有编制 + 非待离职 + 非子账号
     * @param $get_staff_type
     * @param $roleIds
     * @return mixed
     */
    public function getStaffInfoQuery($get_staff_type, $roleIds)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('hsi.staff_info_id, GROUP_CONCAT(hsip.position_category) position_category');
        $builder->from(['hsi' => HrStaffInfoModel::class]);
        $builder->join(HrStaffInfoPositionModel::class, 'hsi.staff_info_id = hsip.staff_info_id', 'hsip');
        $builder->where('hsi.state = 1 AND hsi.formal = 1 AND hsi.wait_leave_state = 0 AND hsi.is_sub_staff = 0');
        $builder->inWhere('hsip.position_category', $roleIds);
        if($get_staff_type == 'incr'){
            $builder->andWhere('hsi.created_at >= :created_at:', ['created_at' => date('Y-m-d H:i:s', strtotime('-1 day'))]);
        }
        $builder->groupBy("hsip.staff_info_id");

        return $builder;
    }
}