<?php


use App\Library\Enums\GlobalEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Library\Exception\BusinessException;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\oa\MiniHrStaffInfoModel;
use App\Models\oa\AutoIncrementIdRecordModel;
use App\Models\backyard\HrOperateLogsModel;
use App\Repository\backyard\StaffResignRepository;

class SyncDataTask extends BaseTask
{
    /**
     * 同步by hr_staff_info 表到 oa mini_hr_staff_info
     * 只同步部分字段 非子账号 is_sub_staff =0，正式员工 staff_type =0，
     * 定频同步增量数据
     *
     * @param $params $params[0] 为1  初始化数据全量同步
     *
     * php app/cli.php sync_data sync_hr_staff_data
     */
    public function sync_hr_staff_dataAction($params)
    {
        // 全量同步
        $process_name = str_replace('Action', '', __FUNCTION__);
        $this->check_process($process_name);
        $is_full_data = $params[0] ?? 0;
        echo 'begin: ' . date('Y-m-d H:i:s');
        echo PHP_EOL;
        echo '系统分配给当前脚本的内存量：' . memory_usage() . PHP_EOL;

        $db_by_obj = $this->db_backyard;

        try {
            //全量数据同步
            if ($is_full_data == 1) {
                $i     = 0;
                $total = 0;
                $sql   = "select id,staff_info_id,`name`,name_en,nick_name,sex,job_title,sys_store_id,sys_department_id,
                        formal,node_department_id,state,wait_leave_state,leave_date,leave_source,hire_type,contract_company_id
                        from hr_staff_info  where is_sub_staff = 0 and staff_type = 0   limit {$i} ,2000";

                $data            = $db_by_obj->query($sql);
                $staff_info_data = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);

                while (!empty($staff_info_data)) {
                    $mini_model      = new MiniHrStaffInfoModel();
                    //取所有离职来源为by申请离职的员工离职申请信息
                    $resign_data = $this->getResignData($staff_info_data);
                    //计算最后工作日
                    foreach ($staff_info_data as &$one_staff) {
                        //计算最后工作日
                        $last_work_date = null;
                        if (($one_staff['state'] == StaffInfoEnums::STAFF_STATE_IN && $one_staff['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES)
                            || $one_staff['state'] == StaffInfoEnums::STAFF_STATE_LEAVE) {
                            if (isset($resign_data[$one_staff['staff_info_id']])) {
                                $last_work_date = $resign_data[$one_staff['staff_info_id']];
                            } elseif (!empty($one_staff['leave_date'])) {
                                $last_work_date = date('Y-m-d', strtotime($one_staff['leave_date'] . ' -1 days '));
                            }
                        }
                        $one_staff['last_work_date'] = $last_work_date;
                    }

                    if ($mini_model->batch_insert($staff_info_data) === false) {
                        $this->logger->warning('mini_hr_staff_info 入库失败, data = ' . json_encode($staff_info_data, JSON_UNESCAPED_UNICODE));
                    }

                    echo '入库处理完成' . count($staff_info_data) . '条数' . PHP_EOL;

                    $i += 2000;

                    $total += count($staff_info_data);

                    $sql             = "select id,staff_info_id,`name`,name_en,nick_name,sex,job_title,sys_store_id,sys_department_id,formal,node_department_id,state,wait_leave_state,leave_date,leave_source,hire_type  from hr_staff_info  where is_sub_staff = 0 and staff_type = 0   limit {$i} ,2000";
                    $data            = $db_by_obj->query($sql);
                    $staff_info_data = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
                }

                echo 'hr_staff_info 全量数据迁移成功入库 mini_hr_staff_info' . $total . ' 条' . PHP_EOL;

            } elseif ($is_full_data == 2) {
                //全量更新mini表数据

                $i = 0;
                $total = 0;
                $sql = "select id,staff_info_id,leave_source,last_work_date,contract_company_id  from mini_hr_staff_info order by id asc limit {$i} ,2000";
                $staff_info_data = $this->db_oa->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
                while (!empty($staff_info_data)) {
                    $curr_batch_staff_count = count($staff_info_data);
                    echo '本批次-处理开始: 预处理员工 ' . $curr_batch_staff_count . ' 条数, 目前内存占用:' . memory_usage() . PHP_EOL;
                    if ($curr_batch_staff_count <= 0) {
                        break;
                    }

                    //查询by数据
                    $staff_ids = array_column($staff_info_data, 'staff_info_id');
                    $staff_ids = implode(',', $staff_ids);
                    $sql = "select id,staff_info_id,state,wait_leave_state,leave_date,leave_source,hire_type,contract_company_id  from hr_staff_info  where staff_info_id in ({$staff_ids}) limit {$i} ,2000";
                    $by_staff_info_data = $db_by_obj->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
                    unset($staff_ids);
                    //取所有离职来源为by申请离职的员工离职申请信息
                    $resign_data = $this->getResignData($by_staff_info_data);
                    //计算最后工作日
                    foreach ($by_staff_info_data as &$one_staff) {
                        //计算最后工作日
                        $last_work_date = null;
                        if (($one_staff['state'] == StaffInfoEnums::STAFF_STATE_IN && $one_staff['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES)
                            || $one_staff['state'] == StaffInfoEnums::STAFF_STATE_LEAVE) {
                            if (isset($resign_data[$one_staff['staff_info_id']])) {
                                $last_work_date = $resign_data[$one_staff['staff_info_id']];
                            } elseif (!empty($one_staff['leave_date'])) {
                                $last_work_date = date('Y-m-d', strtotime($one_staff['leave_date'] . ' -1 days '));
                            }
                        }
                        $one_staff['last_work_date'] = $last_work_date;
                    }
                    unset($resign_data);

                    $by_staff_info_data = array_column($by_staff_info_data, null, 'staff_info_id');
                    //执行更新
                    $continue_staff_id = [];
                    foreach ($staff_info_data as $item) {
                        if (!isset($by_staff_info_data[$item['staff_info_id']])) {
                            $continue_staff_id[] = $item['staff_info_id'];
                            continue;
                        }
                        $update_data = [
                            'leave_source' => $by_staff_info_data[$item['staff_info_id']]['leave_source'],
                            'last_work_date' => $by_staff_info_data[$item['staff_info_id']]['last_work_date'],
                            'hire_type' => $by_staff_info_data[$item['staff_info_id']]['hire_type'],
                            'contract_company_id' => $by_staff_info_data[$item['staff_info_id']]['contract_company_id'],
                        ];
                        $update_flag_bool = $this->db_oa->updateAsDict(
                            'mini_hr_staff_info',
                            $update_data,
                            ['conditions' => "id = {$item['id']}"]
                        );
                        if (!$update_flag_bool) {
                            $this->logger->warning('mini_hr_staff_info 更新失败, id:' . $item['id'] . '; 修改数据:' . json_encode($update_data, JSON_UNESCAPED_UNICODE));
                        }
                    }
                    $this->logger->info('全量更新跳过的员工id=' . json_encode($continue_staff_id, JSON_UNESCAPED_UNICODE));
                    echo '更新处理完成' . $curr_batch_staff_count . '条数, 目前内存占用:' . memory_usage() . PHP_EOL;
                    unset($continue_staff_id);

                    $i += 2000;
                    $total += $curr_batch_staff_count;
                    unset($staff_info_data);
                    unset($by_staff_info_data);
                    $sql = "select id,staff_info_id,leave_source,last_work_date from mini_hr_staff_info order by id asc limit {$i} ,2000";
                    $staff_info_data = $this->db_oa->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);


                    echo '本批次-处理完成: ' . $curr_batch_staff_count . '条数, 目前内存占用:' . memory_usage() . PHP_EOL . PHP_EOL;
                }
                echo 'hr_staff_info 全量数据更新成功 mini_hr_staff_info' . $total . ' 条' . PHP_EOL;
            } else {
                //增量数据

                //新增部分数据
                $last_staff_info_id = MiniHrStaffInfoModel::findFirst([
                    'columns' => 'id',
                    'order'   => 'id desc'
                ]);

                $z = 0;

                $total = 0;
                $sql   = "select id,staff_info_id,`name`,name_en,nick_name,sex,job_title,sys_store_id,sys_department_id,formal,node_department_id,state,wait_leave_state,leave_date,leave_source,hire_type,contract_company_id  
                            from hr_staff_info  where is_sub_staff = 0 and staff_type = 0 and id > {$last_staff_info_id->id}   limit {$z} ,2000";

                $data            = $db_by_obj->query($sql);
                $staff_info_data = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);

                if (empty($staff_info_data)) {
                    echo '没有新增员工需要处理' . PHP_EOL;
                }

                while (!empty($staff_info_data)) {
                    $mini_model      = new MiniHrStaffInfoModel();
                    //取所有离职来源为by申请离职的员工离职申请信息
                    $resign_data = $this->getResignData($staff_info_data);
                    //计算最后工作日
                    foreach ($staff_info_data as &$one_staff) {
                        //计算最后工作日
                        $last_work_date = null;
                        if (($one_staff['state'] == StaffInfoEnums::STAFF_STATE_IN && $one_staff['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES)
                            || $one_staff['state'] == StaffInfoEnums::STAFF_STATE_LEAVE) {
                            if (isset($resign_data[$one_staff['staff_info_id']])) {
                                $last_work_date = $resign_data[$one_staff['staff_info_id']];
                            } elseif (!empty($one_staff['leave_date'])) {
                                $last_work_date = date('Y-m-d', strtotime($one_staff['leave_date'] . ' -1 days '));
                            }
                        }
                        $one_staff['last_work_date'] = $last_work_date;
                    }
                    if ($mini_model->batch_insert($staff_info_data) === false) {
                        $this->logger->warning('mini_hr_staff_info 入库失败, data = ' . json_encode($staff_info_data, JSON_UNESCAPED_UNICODE));
                    }

                    echo '新增员工工号' . implode(',', array_column($staff_info_data, 'staff_info_id')) . PHP_EOL;
                    echo '入库处理完成' . count($staff_info_data) . '条数' . PHP_EOL;

                    $z += 2000;

                    $total += count($staff_info_data);

                    $sql             = "select id,staff_info_id,`name`,name_en,nick_name,sex,job_title,sys_store_id,sys_department_id,formal,node_department_id,state,wait_leave_state,leave_date,leave_source,hire_type,contract_company_id  from hr_staff_info  where is_sub_staff = 0 and staff_type = 0  and id >{$last_staff_info_id->id}   limit {$z} ,2000";
                    $data            = $db_by_obj->query($sql);
                    $staff_info_data = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
                }

                echo 'hr_staff_info 增量数据迁移成功入库 mini_hr_staff_info ' . $total . ' 条' . PHP_EOL;
                echo '开始处理员工变更的数据' . PHP_EOL;

                //更新部分数据
                //查询上次操作的最大id
                $auto_model = AutoIncrementIdRecordModel::findFirst([
                    'conditions' => 'type =:type:',
                    'bind'       => ['type' => AutoIncrementIdRecordModel::OPERATE_TYPE_1]
                ]);

                $last_staff_operate_id = $auto_model->last_id;
                $update_staff_info     = HrOperateLogsModel::find([
                    'columns'    => 'id,staff_info_id',
                    'conditions' => "id >:id: and type = 'staff'",
                    'bind'       => ['id' => $last_staff_operate_id]

                ])->toArray();

                if ($update_staff_info) {
                    $last_operate_id = HrOperateLogsModel::findFirst([
                        'columns' => 'id',
                        'order'   => 'id desc'

                    ]);

                    $update_staff_info = HrStaffInfoModel::find([
                        'columns'    => 'id,staff_info_id,name,name_en,nick_name,sex,job_title,sys_store_id,sys_department_id,formal,node_department_id,state,wait_leave_state,leave_date,leave_source,hire_type,contract_company_id',
                        'conditions' => 'staff_info_id in ({staff_info_id:array})',
                        'bind'       => [
                            'staff_info_id' => array_values(array_unique(array_column($update_staff_info, 'staff_info_id')))
                        ]

                    ])->toArray();
                    //取所有离职来源为by申请离职的员工离职申请信息
                    $resign_data = $this->getResignData($update_staff_info);
                    foreach ($update_staff_info as $item) {
                        $model = MiniHrStaffInfoModel::findFirst([
                            'conditions' => 'id =:id:',
                            'bind'       => [
                                'id' => $item['id']
                            ]
                        ]);
                        if (empty($model)) {
                            continue;
                        }
                        //计算最后工作日
                        $last_work_date = null;
                        if (($item['state'] == StaffInfoEnums::STAFF_STATE_IN && $item['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES)
                            || $item['state'] == StaffInfoEnums::STAFF_STATE_LEAVE) {
                            if (isset($resign_data[$item['staff_info_id']])) {
                                $last_work_date = $resign_data[$item['staff_info_id']];
                            } elseif (!empty($item['leave_date'])) {
                                $last_work_date = date('Y-m-d', strtotime($item['leave_date'] . ' -1 days '));
                            }
                        }
                        $update_data = [
                            'name'               => $item['name'],
                            'name_en'            => $item['name_en'],
                            'nick_name'          => $item['nick_name'],
                            'sex'                => $item['sex'],
                            'job_title'          => $item['job_title'],
                            'sys_store_id'       => $item['sys_store_id'],
                            'sys_department_id'  => $item['sys_department_id'],
                            'formal'             => $item['formal'],
                            'node_department_id' => $item['node_department_id'],
                            'state'              => $item['state'],
                            'wait_leave_state'   => $item['wait_leave_state'],
                            'leave_date'         => $item['leave_date'],
                            'leave_source'       => $item['leave_source'],
                            'last_work_date'     => $last_work_date,
                            'hire_type' => $item['hire_type'],
                            'contract_company_id' => $item['contract_company_id'],
                        ];
                        $update_flag_bool = $model->update($update_data);
                        if ($update_flag_bool == false) {
                            $this->logger->warning('mini_hr_staff_info 更新失败' . $item['staff_info_id'] . get_data_object_error_msg($model));
                        }
                    }

                    $auto_model->last_id    = $last_operate_id->id;
                    $auto_model->updated_at = date('Y-m-d H:i:s');
                    $auto_model->save();
                    echo '变更员工工号' . implode(',', array_values(array_unique(array_column($update_staff_info, 'staff_info_id')))) . PHP_EOL;
                    echo 'update mini_hr_staff_info 成功 ' . count($update_staff_info) . ' 条' . PHP_EOL;

                } else {
                    echo '没有员工变更数据需要同步' . PHP_EOL;
                }
            }
        } catch (Exception $e) {
            $this->logger->warning('sync_mini_staff_info_task_exception:' . $e->getMessage());
        }

        echo '当前脚本已使用的内存量：' . memory_usage() . PHP_EOL;
        echo 'end: ' . date('Y-m-d H:i:s') . PHP_EOL;
        exit();
    }

    /**
     * 获取离职申请数据
     * @param $staff_data
     * @return array|mixed
     */
    public function getResignData($staff_data)
    {
        //取所有离职来源为by申请离职的员工离职申请信息
        $resign_staff_ids = [];
        foreach ($staff_data as $temp_v) {
            if ($temp_v['leave_source'] == StaffInfoEnums::LEAVE_SOURCE_BY_APPLY
                &&
                (
                    ($temp_v['state'] == StaffInfoEnums::STAFF_STATE_IN && $temp_v['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES)
                    || $temp_v['state'] == StaffInfoEnums::STAFF_STATE_LEAVE
                )
            ) {
                $resign_staff_ids[] = $temp_v['staff_info_id'];
            }
        }
        $resign_data = [];
        if (!empty($resign_staff_ids)) {
            $resign_data = StaffResignRepository::getInstance()->getDataByStaffIds($resign_staff_ids);
            $resign_data = array_column($resign_data, 'last_work_date', 'submitter_id');
        }
        return $resign_data;
    }
}
