<?php

use App\Modules\Common\Models\EnvModel;


class PurchaseAcceptanceTask extends BaseTask
{


    /**
     * 每天早上发送前一天审批通过的采购数据到采购组邮箱
     * @date 2022/7/24
     */
    public function send_audit_mailAction()
    {
        $last_date  = date('Y-m-d', strtotime('-1 day'));
        $start_date = $last_date . ' 00:00:00';
        $end_date   = $last_date . ' 23:59:59';
        $db_obj     = $this->db_oa;
        try {
            //查询前一天审批通过的数据
            $i   = 0;
            $sql = "select a.no from purchase_acceptance as a left join purchase_acceptance_product as b on a.id = b.pa_id where a.approve_at>'{$start_date}' and a.approve_at<'{$end_date}'  and  a.status = 3 and b.check_result=2";

            $data       = $db_obj->query($sql);
            $acceptance = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);


            if (empty($acceptance)) {
                echo "采购验收单待处理数据为空 start_date:" . $start_date . ' end_date:' . $end_date . ' ,执行时间:' . date('Y-m-d H:i:s') . PHP_EOL;
                $this->logger->info('purchase_acceptance_send_mail 数据为空, start_date:' . $start_date . ' end_date:' . $end_date . ' ,执行时间:' . date('Y-m-d H:i:s'));
                exit;
            }
            //获取订单号
            $pa_nos = array_column($acceptance, 'no');
            $emails = EnvModel::getEnvByCode('purchase_acceptance_email');
            if (empty($emails)) {
                echo "采购组邮箱为空 start_date:" . $start_date . ' end_date:' . $end_date . ' ,执行时间:' . date('Y-m-d H:i:s') . PHP_EOL;
                $this->logger->info('purchase_acceptance_send_mail 采购组邮箱为空, start_date:' . $start_date . ' end_date:' . $end_date . ' ,执行时间:' . date('Y-m-d H:i:s'));
                exit;
            }
            $emails = explode(',', $emails);
            $title  = 'Reminder of Acceptance Note';
            $html   = "Dear all,<br/>";
            $html   .= "<p>Please kindly notice that Acceptance Note Numbers below contain those which did not pass acceptance</p>";
            foreach ($pa_nos as $no) {
                $html .= "<p>{$no}</p>";
            }
            $send_res = $this->mailer->sendAsync($emails, $title, $html);
            if ($send_res) {
                echo "采购验收单邮件发送成功,收件邮箱:" . implode(',',
                        $emails) . ", start_date:" . $start_date . ' end_date:' . $end_date . ' ,执行时间:' . date('Y-m-d H:i:s') . PHP_EOL;
                $this->logger->info('purchase_acceptance_send_mail 发送成功,收件邮箱' . implode(',',
                        $emails) . ", start_date:" . $start_date . ' end_date:' . $end_date . ' ,执行时间:' . date('Y-m-d H:i:s'));
            } else {
                echo "采购验收邮件发送失败,收件邮箱:" . implode(',',
                        $emails) . ", start_date:" . $start_date . ' end_date:' . $end_date . ' ,执行时间:' . date('Y-m-d H:i:s') . PHP_EOL;
                $this->logger->warning("purchase_acceptance_send_mail 发送失败,收件邮箱:" . implode(',',
                        $emails) . ", start_date:" . $start_date . ' end_date:' . $end_date . ' ,执行时间:' . date('Y-m-d H:i:s'));
            }


            echo 'end: ' . date('Y-m-d H:i:s');
        } catch (Exception $e) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('purchase_acceptance_task_exception:' . $e->getMessage());
            var_dump($e->getMessage());
            return false;
        }
    }
}