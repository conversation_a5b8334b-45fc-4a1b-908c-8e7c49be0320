<?php


use App\Library\Enums\GlobalEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Library\Exception\BusinessException;
use App\Modules\Setting\Services\FinancialService;


class FinancialApprovalTask extends BaseTask
{
    /**
     * @description:执行子部门更新操作 或者 管辖范围清除操作
     * @param null
     * @return
     */
    public function consumption_redis_listAction()
    {
        echo 'begin' . date('Y-m-d H:i:s') . PHP_EOL;
        try {
            $start_time = time();
            $redis = $this->getDI()->get("redis");
            while (true) {
                try {
                    $redis_data = $redis->rpop(FinancialService::$redis_list_name);
                    if (!empty($redis_data)) {
                        $this->logger->info('consumption_redis_list_send  ' . $redis_data);
                        $redis_data = json_decode($redis_data, true);
                        print_r($redis_data);
                        FinancialService::getInstance()->consumptionRedisList($redis_data);
                    } else {
                        $end_time = time();
                        if ($end_time - $start_time > 7200) {
                            exit('时间超过2小时重启脚本consumption_redis_list' . date('Y-m-d H:i:s'));
                        }
                        sleep(10);
                    }

                } catch (\Exception $e) {
                    $this->logger->warning('consumptionRedisListAction end ' . $e->getMessage() . $e->getTraceAsString());
                    break;
                }
            }

        } catch (Exception $e) {
            $this->logger->warning('consumptionRedisListAction end ' . $e->getMessage() . $e->getTraceAsString());

        }

        echo 'end' . date('Y-m-d H:i:s') . PHP_EOL;
        exit();

    }

}
