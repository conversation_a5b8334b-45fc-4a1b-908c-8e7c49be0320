<?php


use App\Modules\Share\Models\ShareCenterFile;
use App\Modules\Share\Models\ShareCenterPermission;
use App\Modules\Share\Services\AddService;

class ShareCenterTask extends BaseTask
{
    /**
     * Notes: 信息共享中心文件权限处理，14900 需求临时数据处理脚本
     */
    public function temp_share_center_permissionAction($params)
    {
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $addService = AddService::getInstance();
            if (isset($params[0])) {
                $files = ShareCenterFile::find([
                    'conditions' => 'id = :id: AND is_delete = 0',
                    'bind'       => [
                        'id' => $params[0],
                    ],
                ]);
            } else {
                $files = ShareCenterFile::find([
                    'conditions' => 'is_delete = 0',
                ]);
            }

            foreach ($files as $file) {
                /**
                 * @var $file ShareCenterFile
                 */
                if ($file->view_type == -1) {
                    // 全部都有权限，不用处理
                    continue;
                }
                // 目前只有按照部门限制的
                $permissions       = ShareCenterPermission::find([
                    'conditions' => 'file_id = :file_id:',
                    'bind'       => [
                        'file_id' => $file->id,
                    ],
                ]);
                $existsPermissionT = [];
                $allPermissions    = [];
                foreach ($permissions as $permission) {
                    /**
                     * @var $permission ShareCenterPermission
                     */
                    $existsPermissionT[$addService->getExistsPermissionsKey($file->id,
                        $permission->permission_value)] = $permission;
                    if ($permission->is_delete == 1) {
                        continue;
                    }
                    $temp                   = [];
                    $temp['id']             = $permission->permission_value;
                    $temp['is_include_sub'] = $permission->is_include_sub;
                    $allPermissions[]       = $temp;
                }

                $addService->savePermissionValue($allPermissions, $file->id, $existsPermissionT);
                echo "file_Id:{$file->id} 处理成功 \n";
            }
            $db->commit();
        } catch (\Exception $e) {
            $db->rollback();
            echo 'error:'.$e->getMessage().PHP_EOL;
        }
        echo "脚本执行完毕\n";
    }
}