<?php

use App\Library\ApiClient;
use App\Library\Validation\Validation;
use App\Models\backyard\HrStaffInfoModel;
use App\Modules\Common\Services\CommonService;
use App\Modules\CrmQuotation\Models\CrmQuotationApplyModel;
use app\modules\Organization\models\SysDepartmentFleModel;
use App\Modules\User\Services\UserService;
use App\Repository\HrStaffRepository;
use App\Modules\Common\Services\SmsService;
use App\Modules\Setting\Services\CommonDataPermissionService;

/**
 *TestTask.php
 * Description:
 * Created by: Lqz.
 */
class TestTask extends BaseTask
{

    public function testAction()
    {
        $data = \App\Models\fle\SysStoreModel::findFirst(
            [
                'columns'    => 'id,province_code,city_code,district_code,name,detail_address',
                'conditions' => 'id = :id:',
                'bind'       => ['id' => 'TH01010298'],
            ]
        )->toArray();

        $scm = new \App\Modules\Material\Services\ScmService();
        $re  = $scm->autoChangeRealStoreInfo('TH01010298', $data);
        var_dump($re);
    }

    // 任务输出测试
    public function outputAction()
    {
        exit(date('Y-m-d H:i:s') . ' success ' . PHP_EOL);
    }

    public function mailAction()
    {
        try {
            $count  = 2;
            $emails = [
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
            ];

            for ($i = 1; $i <= $count; $i++) {
                $index   = $i % 2;
                $content = 'ธนาคารกรุงเทพ' . $i;
                $s       = $this->mailer->sendAsync($emails[$index], 'Test' . $i, '<h1>' . $content . '</h1>');
                var_dump('send res - ' . $s);
                sleep(1);
            }
        } catch (\Exception $e) {
            var_dump('sendEmailToAuditors 发送失败！原因可能是：' . $e->getMessage());
        }
    }

    public function cls3Action()
    {
        $nowTime = strtotime('2021-05-10 21:30:40');
        var_dump(gmdate('Y-m-d H:i:s', $nowTime));
        $nowTimeTH  = date('Y-m-d 00:00:00', $nowTime + get_sys_time_offset() * 3600);
        $valid_time = '2021-05-08 00:00:00';
        $validTime  = $nowTimeTH > date('Y-m-d 00:00:00', strtotime($valid_time)) ? $nowTimeTH : date('Y-m-d 00:00:00',
            strtotime($valid_time));
        var_dump(date('Y-m-d 00:00:00', strtotime($valid_time)));
        var_dump($nowTimeTH, $validTime);
    }

    public function cls2Action()
    {
        $item = CrmQuotationApplyModel::findFirst([
            'conditions' => ' id = :id: ',
            'bind'       => ['id' => 342],
            'for_update' => true,
        ]);

        $nowTime   = time();
        $nowTimeTH = gmdate('Y-m-d 00:00:00', $nowTime + get_sys_time_offset() * 3600);
        $validTime = $nowTimeTH > gmdate('Y-m-d 00:00:00',
            strtotime($item->valid_time)) ? $nowTimeTH : gmdate('Y-m-d 00:00:00', strtotime($item->valid_time));
        if ($item->discount_valid_type == 2) {
            // 加天数
            $nowTimeTH = $nowTimeTH > $validTime ? $nowTimeTH : $validTime;
            $endTimeTH = gmdate('Y-m-d 00:00:00', strtotime($nowTimeTH) + 86400 * $item->valid_days);
        } else {
            // 固定时间
            $endTimeTH = $item->invalid_time ? gmdate('Y-m-d 00:00:00', strtotime($item->invalid_time)) : $nowTimeTH;
        }

        var_dump($item->valid_time);
        var_dump($nowTimeTH, $endTimeTH);
        die;
        $publicParam = [
            "related_id"             => (int)$item->id,
            "client_id"              => $item->customer_id,
            "customer_type_category" => (int)$item->customer_type_category,
            "customer_name"          => $item->customer_name,
            "customer_mobile"        => $item->customer_mobile,
            "price_type"             => (int)$item->price_type,
            "disc_start_date"        => strtotime($nowTimeTH),
            "disc_end_date"          => strtotime($endTimeTH),
            "valid_dates"            => (int)$item->valid_days,
            "channel"                => (int)3,
            "staff_info_id"          => (int)$item->create_id,
        ];

        $params['discount_apply_list'] = [];
        if ($item->valid_promise_num) { //折扣期内承诺单量
            $params['discount_apply_list'][] = array_merge($publicParam, [
                "valid_date"          => 999 * 30,
                "disc_end_date"       => strtotime(gmdate('Y-m-d 00:00:00', strtotime($nowTimeTH) + 86400 * 999 * 30)),
                "price_rule_category" => 6,
                "current_disc"        => 0,
                "request_disc"        => (float)$item->valid_promise_num,
                "current_disc_str"    => (string)0,
                "request_disc_str"    => (string)$item->valid_promise_num,
            ]);
        }
        if ($item->credit_period) { //信用期限
            $params['discount_apply_list'][] = array_merge($publicParam, [
                "price_rule_category" => 5,
                "current_disc"        => 0,
                "request_disc"        => (float)$item->credit_period,
                "current_disc_str"    => (string)0,
                "request_disc_str"    => (string)$item->credit_period,
            ]);
        }
        if (bccomp($item->cod_fee_rate, 0.00, 2)) { // cod 手续费率
            $params['discount_apply_list'][] = array_merge($publicParam, [
                "price_rule_category" => 4,
                "current_disc"        => 0,
                "request_disc"        => (float)$item->cod_fee_rate,
                "current_disc_str"    => (string)0,
                "request_disc_str"    => (string)$item->cod_fee_rate,
            ]);
        }
        if (bccomp($item->return_discount_rate, 0.00, 2)) { //退件折扣率,
            $params['discount_apply_list'][] = array_merge($publicParam, [
                "price_rule_category" => 3,
                "current_disc"        => 0,
                "request_disc"        => (int)$item->return_discount_rate,
                "current_disc_str"    => (string)0,
                "request_disc_str"    => (string)(int)$item->return_discount_rate,
            ]);
        }
        if (bccomp($item->lowest_discount_rate, 0.00, 2)) { //运费折扣率
            $params['discount_apply_list'][] = array_merge($publicParam, [
                "price_rule_category" => 1,
                "current_disc"        => 0,
                "request_disc"        => (int)$item->lowest_discount_rate,
                "current_disc_str"    => (string)0,
                "request_disc_str"    => (string)(int)$item->lowest_discount_rate,
            ]);
        }

        if ($item->calculation_method) { // 计费方式 按体积重
            $params['discount_apply_list'][] = array_merge($publicParam, [
                "price_rule_category" => 7, //计费方式 7
                "current_disc"        => 0,
                "request_disc"        => (int)$item->calculation_method,
                "current_disc_str"    => (string)0,
                "request_disc_str"    => (string)(int)$item->calculation_method,
            ]);
        }

        // 偏远地区是否免运费
        if ($item->faraway_fee) {
            $params['discount_apply_list'][] = array_merge($publicParam, [
                "price_rule_category" => 10,
                "current_disc"        => 0,
                "request_disc"        => (int)$item->faraway_fee ? 50 : 0,
                "current_disc_str"    => (string)0,
                "request_disc_str"    => (string)$item->faraway_fee ? 50 : 0,
            ]);
        }
        var_dump($item->valid_days);
        var_dump($params);
        var_dump($nowTimeTH, $endTimeTH);
        die;
    }

    public function clsAction()
    {
        $data = <<<EOF
{
    "code": 1,
    "message": "success",
    "data": [
        {
            "id": "55",
            "label": "Asset Resource Management",
            "ancestry": "0",
            "children": [
                {
                    "id": "75",
                    "ancestry": "55",
                    "label": "Asset Data",
                    "type": "3",
                    "children": [
                        {
                            "id": "138",
                            "ancestry": "75",
                            "label": "Data Support",
                            "type": "3"
                        },
                        {
                            "id": "139",
                            "ancestry": "75",
                            "label": "System Support",
                            "type": "3"
                        }
                    ]
                },
                {
                    "id": "77",
                    "ancestry": "55",
                    "label": "Asset Operations",
                    "type": "3",
                    "children": [
                        {
                            "id": "137",
                            "ancestry": "77",
                            "label": "Asset Approval",
                            "type": "3"
                        },
                        {
                            "id": "140",
                            "ancestry": "77",
                            "label": "Asset Check",
                            "type": "3"
                        }
                    ]
                },
                {
                    "id": "76",
                    "ancestry": "55",
                    "label": "Asset Support",
                    "type": "3",
                    "children": [
                        {
                            "id": "142",
                            "ancestry": "76",
                            "label": "Asset Recycle",
                            "type": "3"
                        },
                        {
                            "id": "141",
                            "ancestry": "76",
                            "label": "Asset Repair",
                            "type": "3"
                        },
                        {
                            "id": "143",
                            "ancestry": "76",
                            "label": "Asset Uniform&Insurance",
                            "type": "3"
                        }
                    ]
                }
            ]
        },
        {
            "id": "72",
            "label": "Capital Market",
            "ancestry": "0",
            "children": []
        },
        {
            "id": "150",
            "ancestry": "50001",
            "label": "F - Commerce Department",
            "type": "2",
            "children": [
                {
                    "id": "110",
                    "ancestry": "150",
                    "label": "E - Commerce Marketing",
                    "type": "2"
                },
                {
                    "id": "109",
                    "ancestry": "150",
                    "label": "E - Commerce Operaions",
                    "type": "2"
                }
            ]
        },
        {
            "id": "17",
            "label": "Finance and Accounting",
            "ancestry": "0",
            "children": [
                {
                    "id": "171",
                    "ancestry": "17",
                    "label": "Accounting",
                    "type": "3",
                    "children": [
                        {
                            "id": "79",
                            "ancestry": "171",
                            "label": "Account Payable",
                            "type": "3"
                        },
                        {
                            "id": "80",
                            "ancestry": "171",
                            "label": "Account Receivable",
                            "type": "3"
                        }
                    ]
                },
                {
                    "id": "170",
                    "ancestry": "17",
                    "label": "Finance Management",
                    "type": "3"
                },
                {
                    "id": "81",
                    "ancestry": "17",
                    "label": "Settlement",
                    "type": "3"
                },
                {
                    "id": "78",
                    "ancestry": "17",
                    "label": "Tax Management",
                    "type": "3"
                }
            ]
        },
        {
            "id": "3",
            "ancestry": "1",
            "label": "Customer Service",
            "type": "2",
            "children": [
                {
                    "id": "175",
                    "ancestry": "3",
                    "label": "Claim",
                    "type": "2"
                },
                {
                    "id": "89",
                    "ancestry": "3",
                    "label": "Customer Service Online",
                    "type": "2"
                },
                {
                    "id": "177",
                    "ancestry": "3",
                    "label": "Customer Service Quality",
                    "type": "2"
                },
                {
                    "id": "88",
                    "ancestry": "3",
                    "label": "Customer Service Representative",
                    "type": "2"
                },
                {
                    "id": "176",
                    "ancestry": "3",
                    "label": "Customer Service Training",
                    "type": "2"
                }
            ]
        },
        {
            "id": "93",
            "ancestry": "1",
            "label": "EHS",
            "type": "2",
            "children": [
                {
                    "id": "162",
                    "ancestry": "93",
                    "label": "EHS Audit",
                    "type": "2"
                },
                {
                    "id": "161",
                    "ancestry": "93",
                    "label": "EHS Management",
                    "type": "2"
                },
                {
                    "id": "160",
                    "ancestry": "93",
                    "label": "EHS Operations",
                    "type": "2"
                },
                {
                    "id": "159",
                    "ancestry": "93",
                    "label": "EHS Training",
                    "type": "2"
                }
            ]
        },
        {
            "id": "20",
            "ancestry": "1",
            "label": "Express Thai Product",
            "type": "2"
        },
        {
            "id": "65",
            "ancestry": "1",
            "label": "Flash Freight Hub",
            "type": "2"
        },
        {
            "id": "18",
            "ancestry": "1",
            "label": "Flash Home",
            "type": "2",
            "children": [
                {
                    "id": "86",
                    "ancestry": "18",
                    "label": "Flash Home Customer Service",
                    "type": "2"
                },
                {
                    "id": "84",
                    "ancestry": "18",
                    "label": "Flash Home Operations",
                    "type": "2"
                },
                {
                    "id": "87",
                    "ancestry": "18",
                    "label": "Flash Home Settlement",
                    "type": "2"
                },
                {
                    "id": "83",
                    "ancestry": "18",
                    "label": "Flash Home Support",
                    "type": "2"
                }
            ]
        },
        {
            "id": "184",
            "ancestry": "1",
            "label": "GM's Office - Flash Express Thailand",
            "type": "2"
        },
        {
            "id": "25",
            "ancestry": "1",
            "label": "Hub Management",
            "type": "2",
            "children": [
                {
                    "id": "57",
                    "ancestry": "25",
                    "label": "Hub Central Area",
                    "type": "2"
                },
                {
                    "id": "107",
                    "ancestry": "25",
                    "label": "Hub Headquarter",
                    "type": "2",
                    "children": [
                        {
                            "id": "68",
                            "ancestry": "107",
                            "label": "Hub Admin",
                            "type": "2"
                        },
                        {
                            "id": "70",
                            "ancestry": "107",
                            "label": "Hub Industrial Engineering",
                            "type": "2"
                        },
                        {
                            "id": "67",
                            "ancestry": "107",
                            "label": "Hub Planning",
                            "type": "2"
                        },
                        {
                            "id": "66",
                            "ancestry": "107",
                            "label": "Hub Quality Control",
                            "type": "2"
                        },
                        {
                            "id": "69",
                            "ancestry": "107",
                            "label": "Hub Standardization",
                            "type": "2"
                        },
                        {
                            "id": "71",
                            "ancestry": "107",
                            "label": "Hub Training",
                            "type": "2"
                        }
                    ]
                },
                {
                    "id": "106",
                    "ancestry": "25",
                    "label": "Hub North Area",
                    "type": "2"
                },
                {
                    "id": "58",
                    "ancestry": "25",
                    "label": "Hub Northeast Area",
                    "type": "2"
                },
                {
                    "id": "59",
                    "ancestry": "25",
                    "label": "Hub South Area",
                    "type": "2"
                },
                {
                    "id": "95",
                    "ancestry": "25",
                    "label": "KAP Area",
                    "type": "2"
                }
            ]
        },
        {
            "id": "41",
            "ancestry": "1",
            "label": "Marketing",
            "type": "2"
        },
        {
            "id": "4",
            "ancestry": "1",
            "label": "Network Management",
            "type": "2",
            "children": [
                {
                    "id": "34",
                    "ancestry": "4",
                    "label": "Network Bulky",
                    "type": "2"
                },
                {
                    "id": "32",
                    "ancestry": "4",
                    "label": "Network Operations",
                    "type": "2"
                },
                {
                    "id": "30",
                    "ancestry": "4",
                    "label": "Network Planning",
                    "type": "2"
                },
                {
                    "id": "33",
                    "ancestry": "4",
                    "label": "Network Support",
                    "type": "2"
                },
                {
                    "id": "31",
                    "ancestry": "4",
                    "label": "Network Training",
                    "type": "2"
                }
            ]
        },
        {
            "id": "22",
            "ancestry": "1",
            "label": "Project Management",
            "type": "2",
            "children": [
                {
                    "id": "37",
                    "ancestry": "22",
                    "label": "Cross-Border Business Management Center",
                    "type": "2"
                },
                {
                    "id": "36",
                    "ancestry": "22",
                    "label": "Project Management Center",
                    "type": "2"
                },
                {
                    "id": "35",
                    "ancestry": "22",
                    "label": "Project Support Center",
                    "type": "2"
                }
            ]
        },
        {
            "id": "12",
            "ancestry": "1",
            "label": "QAQC",
            "type": "2"
        },
        {
            "id": "40",
            "ancestry": "1",
            "label": "Sales",
            "type": "2"
        },
        {
            "id": "13",
            "ancestry": "1",
            "label": "Shop Management",
            "type": "2",
            "children": [
                {
                    "id": "51",
                    "ancestry": "13",
                    "label": "Shop Operations",
                    "type": "2"
                },
                {
                    "id": "104",
                    "ancestry": "13",
                    "label": "Shop Planning",
                    "type": "2"
                },
                {
                    "id": "105",
                    "ancestry": "13",
                    "label": "Shop Support",
                    "type": "2"
                }
            ]
        },
        {
            "id": "26",
            "ancestry": "1",
            "label": "Transportation",
            "type": "2",
            "children": [
                {
                    "id": "62",
                    "ancestry": "26",
                    "label": "Central Control Dispatching",
                    "type": "2"
                },
                {
                    "id": "64",
                    "ancestry": "26",
                    "label": "Fleet Management",
                    "type": "2"
                },
                {
                    "id": "97",
                    "ancestry": "26",
                    "label": "Logistics",
                    "type": "2"
                },
                {
                    "id": "60",
                    "ancestry": "26",
                    "label": "Transportation Planning",
                    "type": "2"
                },
                {
                    "id": "61",
                    "ancestry": "26",
                    "label": "Transportation Procurement",
                    "type": "2"
                },
                {
                    "id": "94",
                    "ancestry": "26",
                    "label": "Transportation QC",
                    "type": "2"
                },
                {
                    "id": "63",
                    "ancestry": "26",
                    "label": "Transportation Settlement",
                    "type": "2"
                }
            ]
        },
        {
            "id": "182",
            "ancestry": "80001",
            "label": "Express Philippines Product",
            "type": "2"
        },
        {
            "id": "124",
            "ancestry": "80001",
            "label": "Flash Home Philippines",
            "type": "2"
        },
        {
            "id": "129",
            "ancestry": "80001",
            "label": "Flash Philippines Customer Service",
            "type": "2"
        },
        {
            "id": "181",
            "ancestry": "80001",
            "label": "Flash Philippines Finance and Accounting",
            "type": "2"
        },
        {
            "id": "130",
            "ancestry": "80001",
            "label": "Flash Philippines QAQC",
            "type": "2"
        },
        {
            "id": "122",
            "ancestry": "80001",
            "label": "Flash Philippines Sales",
            "type": "2"
        },
        {
            "id": "127",
            "ancestry": "80001",
            "label": "Flash Philippines Transportation",
            "type": "2"
        },
        {
            "id": "121",
            "ancestry": "80001",
            "label": "GM Office",
            "type": "2"
        },
        {
            "id": "179",
            "ancestry": "80001",
            "label": "HR",
            "type": "2",
            "children": [
                {
                    "id": "180",
                    "ancestry": "179",
                    "label": "Admin",
                    "type": "2"
                }
            ]
        },
        {
            "id": "126",
            "ancestry": "80001",
            "label": "Hub",
            "type": "2"
        },
        {
            "id": "128",
            "ancestry": "80001",
            "label": "Marketing and PR",
            "type": "2"
        },
        {
            "id": "125",
            "ancestry": "80001",
            "label": "Network",
            "type": "2"
        },
        {
            "id": "131",
            "ancestry": "80001",
            "label": "Safety and Security",
            "type": "2"
        },
        {
            "id": "123",
            "ancestry": "80001",
            "label": "Shop",
            "type": "2"
        },
        {
            "id": "15",
            "ancestry": "20001",
            "label": "Fulfillment Department",
            "type": "2",
            "children": [
                {
                    "id": "144",
                    "ancestry": "15",
                    "label": "Fulfillment Finance and Accounting",
                    "type": "2"
                },
                {
                    "id": "145",
                    "ancestry": "15",
                    "label": "Fulfillment HR",
                    "type": "2"
                },
                {
                    "id": "20003",
                    "ancestry": "15",
                    "label": "Fulfillment Project",
                    "type": "2",
                    "children": [
                        {
                            "id": "146",
                            "ancestry": "20003",
                            "label": "Fulfillment Customer Service",
                            "type": "2"
                        },
                        {
                            "id": "178",
                            "ancestry": "20003",
                            "label": "Fulfillment Project Management",
                            "type": "2"
                        },
                        {
                            "id": "147",
                            "ancestry": "20003",
                            "label": "Fulfillment Sales",
                            "type": "2"
                        }
                    ]
                },
                {
                    "id": "172",
                    "ancestry": "15",
                    "label": "Fulfillment Thailand Product",
                    "type": "2"
                },
                {
                    "id": "20004",
                    "ancestry": "15",
                    "label": "International Department",
                    "type": "2"
                },
                {
                    "id": "20002",
                    "ancestry": "15",
                    "label": "Warehouse Operations",
                    "type": "2",
                    "children": [
                        {
                            "id": "148",
                            "ancestry": "20002",
                            "label": "Fulfillment Transportation",
                            "type": "2"
                        },
                        {
                            "id": "149",
                            "ancestry": "20002",
                            "label": "Security and QAQC",
                            "type": "2"
                        }
                    ]
                }
            ]
        },
        {
            "id": "40002",
            "ancestry": "40001",
            "label": "Flash Laos Department",
            "type": "2",
            "children": [
                {
                    "id": "167",
                    "ancestry": "40002",
                    "label": "Flash Laos Admin",
                    "type": "2"
                },
                {
                    "id": "166",
                    "ancestry": "40002",
                    "label": "Flash Laos Finance",
                    "type": "2"
                },
                {
                    "id": "165",
                    "ancestry": "40002",
                    "label": "Flash Laos HR",
                    "type": "2"
                },
                {
                    "id": "164",
                    "ancestry": "40002",
                    "label": "Flash Laos Operations",
                    "type": "2"
                },
                {
                    "id": "163",
                    "ancestry": "40002",
                    "label": "Flash Laos Product",
                    "type": "2"
                }
            ]
        },
        {
            "id": "28",
            "ancestry": "10001",
            "label": "Logistic",
            "type": "2"
        },
        {
            "id": "16",
            "ancestry": "30001",
            "label": "Flash Money Department",
            "type": "2",
            "children": [
                {
                    "id": "117",
                    "ancestry": "16",
                    "label": "Business Development",
                    "type": "2"
                },
                {
                    "id": "119",
                    "ancestry": "16",
                    "label": "Call Center",
                    "type": "2"
                },
                {
                    "id": "120",
                    "ancestry": "16",
                    "label": "Money Thai Product",
                    "type": "2"
                },
                {
                    "id": "118",
                    "ancestry": "16",
                    "label": "Operations",
                    "type": "2"
                }
            ]
        },
        {
            "id": "115",
            "ancestry": "60001",
            "label": "Flash Pay Department",
            "type": "2",
            "children": [
                {
                    "id": "134",
                    "ancestry": "115",
                    "label": "Flash Pay Call Center",
                    "type": "2"
                },
                {
                    "id": "132",
                    "ancestry": "115",
                    "label": "Pay Thai Product",
                    "type": "2"
                },
                {
                    "id": "133",
                    "ancestry": "115",
                    "label": "Payment",
                    "type": "2",
                    "children": [
                        {
                            "id": "135",
                            "ancestry": "133",
                            "label": "Payment Operations",
                            "type": "2"
                        },
                        {
                            "id": "136",
                            "ancestry": "133",
                            "label": "Payment Settlement",
                            "type": "2"
                        }
                    ]
                }
            ]
        },
        {
            "id": "38",
            "label": "Group CEO Office",
            "ancestry": "0",
            "children": [
                {
                    "id": "73",
                    "ancestry": "38",
                    "label": "Corporate Development",
                    "type": "3"
                },
                {
                    "id": "156",
                    "ancestry": "38",
                    "label": "Corporate Support",
                    "type": "3"
                },
                {
                    "id": "157",
                    "ancestry": "38",
                    "label": "International Coordinator",
                    "type": "3"
                },
                {
                    "id": "158",
                    "ancestry": "38",
                    "label": "Relationship",
                    "type": "3"
                },
                {
                    "id": "10",
                    "ancestry": "38",
                    "label": "Special Project",
                    "type": "3"
                }
            ]
        },
        {
            "id": "49",
            "label": "HR Business Partner",
            "ancestry": "0",
            "children": [
                {
                    "id": "168",
                    "ancestry": "49",
                    "label": "Business Partner",
                    "type": "3"
                },
                {
                    "id": "169",
                    "ancestry": "49",
                    "label": "Employee Relations",
                    "type": "3"
                }
            ]
        },
        {
            "id": "7",
            "label": "HR Functions",
            "ancestry": "0",
            "children": [
                {
                    "id": "48",
                    "ancestry": "7",
                    "label": "HR Development",
                    "type": "3"
                },
                {
                    "id": "47",
                    "ancestry": "7",
                    "label": "HR Management",
                    "type": "3",
                    "children": [
                        {
                            "id": "111",
                            "ancestry": "47",
                            "label": "Admin Head Office",
                            "type": "3"
                        }
                    ]
                },
                {
                    "id": "90",
                    "ancestry": "7",
                    "label": "HR Organization Development and Strategy",
                    "type": "3"
                },
                {
                    "id": "50",
                    "ancestry": "7",
                    "label": "Talent Acquisition",
                    "type": "3"
                }
            ]
        },
        {
            "id": "183",
            "label": "HR Payroll",
            "ancestry": "0",
            "children": []
        },
        {
            "id": "21",
            "label": "IT",
            "ancestry": "0",
            "children": [
                {
                    "id": "45",
                    "ancestry": "21",
                    "label": "IT Security",
                    "type": "3"
                },
                {
                    "id": "44",
                    "ancestry": "21",
                    "label": "IT Support",
                    "type": "3"
                },
                {
                    "id": "46",
                    "ancestry": "21",
                    "label": "IT System and Maintenance",
                    "type": "3"
                }
            ]
        },
        {
            "id": "54",
            "label": "Legal and Compliance",
            "ancestry": "0",
            "children": [
                {
                    "id": "27",
                    "ancestry": "54",
                    "label": "Business Compliance",
                    "type": "3"
                },
                {
                    "id": "174",
                    "ancestry": "54",
                    "label": "Finance Compliance",
                    "type": "3"
                },
                {
                    "id": "173",
                    "ancestry": "54",
                    "label": "Lawyer",
                    "type": "3"
                },
                {
                    "id": "91",
                    "ancestry": "54",
                    "label": "Legal",
                    "type": "3"
                }
            ]
        },
        {
            "id": "8",
            "label": "Procurement",
            "ancestry": "0",
            "children": []
        },
        {
            "id": "19",
            "label": "Public Relations",
            "ancestry": "0",
            "children": []
        },
        {
            "id": "112",
            "label": "Research and Development",
            "ancestry": "0",
            "children": []
        }
    ]
}
EOF;

        $data = json_decode($data, true)['data'];

        foreach ($data as $datum) {
            if ($this->isHasNodeDepartment([$datum], 106)) {
                $data = [$datum];
                break;
            }
        }

        var_dump($data);
    }

    private function isHasNodeDepartment($departments, $departmentId)
    {
        foreach ($departments as $department) {
            if ($department['id'] == $departmentId) {
                return true;
            } elseif (isset($department['children']) && $department['children']) {
                $result = $this->isHasNodeDepartment($department['children'], $departmentId);
                if ($result) {
                    return true;
                }
            }
        }
        return false;
    }

    //清除redis 缓存
    public function dealCacheAction($argv)
    {
        $redis = $this->getDI()->get("redis");
        $key   = $argv[0] ?? 'scm_cargo_owner_key_1';
        $bool  = $redis->del($key);
        var_dump($bool);
        exit();
    }


    /**
     * 处理数据左侧菜单到导出xls使用
     * @Date: 2022-03-23 14:47
     * @return:
     **@author: peak pan
     */

    public function exceltoolAction($param)
    {
        $page      = $param[0];
        $page_size = $param[1];
        try {
            echo date('Ymd H:i:s') . " 处理数据左侧菜单到导出xls使用 脚本开始执行 入参" . $page . '/' . $page_size . PHP_EOL;
            $user_xls = new UserService();
            if (empty($page)) {
                $page = 1;
            }
            if (empty($page_size)) {
                $page_size = 10000;
            }
            $tool_ = $user_xls->download($page, $page_size);
            echo date('Ymd H:i:s') . " 处理数据左侧菜单到导出xls使用 执行完毕" . PHP_EOL . json_encode($tool_);
            $this->logger->info(json_encode($tool_));
        } catch (\Exception $e) {
            $this->logger->info(json_encode($e->getMessage()));
            die($e->getMessage());
        }
    }

    /**
     * 测试用例: 获取通用数据权限
     *
     * php app/cli.php test get_common_data_permission
     */
    public function get_common_data_permissionAction()
    {
        // 登录用户信息
        $user = [
            'id'                 => '19515',
            'node_department_id' => '110',
            'job_title_id'       => '50',
        ];

        // 业务侧查询构造器
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['contract' => \App\Modules\Contract\Models\Contract::class]);

        // 业务模块标识
        $module_key = 'sys_module_other_contract';

        // 业务表参数
        $table_params = [
            'table_alias_name'                => 'contract',
            'create_id_field'                 => 'create_id',
            'create_node_department_id_filed' => 'create_department_id',
        ];

        $builder = CommonDataPermissionService::getInstance()->getCommonDataPermission($user, $builder, $module_key,
            $table_params);

        $items = $builder->getQuery()->execute()->toArray();

        var_dump($items);
    }

    /**
     * 站内信发送测试
     * php app/cli.php test send_msg
     */
    public function send_msgAction()
    {
        $by_msg_param = [
            'staff_users'     => [['id' => 19515]],
            'message_title'   => 'การแจ้งเตือนการดูความคิดเห็น 评论查看提醒',
            'message_content' => '<div>10000Admin，您好，您于2020-01-01提交的支付管理No0009393930新增一条评论：<br />
评论人：100000-Admin-J<br />
评论内容为test！<br />
<b>请您及时登录OA进行查看，谢谢！</b></div>',
            'category'        => 0,
        ];

        $bi_rpc = (new ApiClient('hcm_rpc', '', 'add_kit_message'));
        $bi_rpc->setParams([$by_msg_param]);
        $res = $bi_rpc->execute();
        var_dump($res);
        die;
    }

    /**
     * 获取图片base64编码
     *
     * php app/cli.php test get_img_base64
     */
    public function get_img_base64Action()
    {
        $ttt = get_curr_micro_time();

        $img_url = 'https://fle-training-asset-internal.oss-ap-southeast-1.aliyuncs.com/workOrder/oca-1722946568-36ebbc94572b45fab8e84cc49fcf1e9b.jpg';

//        $img_url = './wby_sign.jpg';

//        var_dump(file_exists($img_url));die;

        $base64 = file_get_contents($img_url);
        echo get_exec_time($ttt), PHP_EOL;

        $img_info          = getimagesize($img_url);
        $img_base64_prefix = "data:{$img_info['mime']};base64,";
        $img_content       = $img_base64_prefix . $base64;
        $img_type          = explode('/', $img_info['mime'])[1];

        echo PHP_EOL, $img_url, PHP_EOL;
        var_dump($img_info);
        echo PHP_EOL;
        echo 'mime: data:', $img_base64_prefix, PHP_EOL;
//        echo $img_content;
        echo PHP_EOL;

        echo get_exec_time($ttt);
        echo PHP_EOL;
//        echo file_put_contents("./t.txt", $img_content);
    }

    public function ascAction()
    {

    }

    /**
     * 给指定手机号 发送 指定内容
     *
     * php app/cli.php test send_sms
     *
     */
    public function send_smsAction()
    {
        $log = '任务名称: send_push' . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        // 获取手机号 和 对应的短信内容
        $sms_content = file_get_contents(BASE_PATH . '/public/template/sms_content.txt');
        $sms_content = explode("\n", $sms_content);
        if (empty($sms_content)) {
            exit('配置内容为空');
        }

        $log .= '待发人数: ' . count($sms_content) . PHP_EOL;

        $src = 'oa_zb_flashpay_online_sms_send';

        $success_count = 0;
        $error_item    = [];
        $error_count   = 0;
        foreach ($sms_content as $content) {
            $item = explode('	', $content);

            $params = [
                'mobile' => trim($item[0]),
                'msg'    => trim($item[1]),
                'nation' => 'TH',
            ];

            var_dump($params);

            if (SmsService::getInstance()->send($params, $src)) {
                $success_count++;
            } else {
                $error_item[] = $params;
                $error_count++;
            }
        }

        $log .= '成功数: ' . $success_count . PHP_EOL;
        $log .= '失败数: ' . $error_count . PHP_EOL;
        $log .= '失败详情请见: ' . $src . PHP_EOL;
        $this->logger->info([$src => $error_item]);

        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $this->logger->info($log);
        exit($log);
    }

    /**
     * 测试各环境pdf服务效率
     *
     * php app/cli.php test pdf_api_speed
     * @param $params
     */
    public function pdf_api_speedAction($params)
    {
        echo '当前时间: ' . get_datetime_with_milliseconds() . PHP_EOL;

        $pdf_total   = $params[0] ?? 1;
        $pdf_content = file_get_contents(BASE_PATH . '/public/template/test_pdf_content.txt');
        if (empty($pdf_content)) {
            exit('pdf 内容为空' . PHP_EOL);
        }

        echo "本次待生成pdf文件数量: {$pdf_total}" . PHP_EOL;
        $pdf_params = json_decode(base64_decode($pdf_content), true);

        $s1              = get_curr_micro_time();
        $total_exec_time = 0;
        for ($i = 1; $i <= $pdf_total; $i++) {
            $s2            = get_curr_micro_time();
            $api_path      = '/api/pdf/v2/createPdfByJvppeteer';
            $response_data = CommonService::getInstance()->newPostRequest(env('pdf_rpc_endpoint') . $api_path,
                json_encode($pdf_params, JSON_UNESCAPED_UNICODE));

            $per_exec_time = get_exec_time($s2);

            preg_match('/\d{1,}/', $per_exec_time, $match_res);
            $total_exec_time += $match_res[0] ?? 0;
            echo "第 $i 次 执行时长: " . $per_exec_time, '; 文件地址=' . ($response_data['data']['object_url'] ?? 'null') . PHP_EOL;
        }

        echo '总执行时长: ' . get_exec_time($s1) . PHP_EOL;
        echo '平均执行时长: ' . (round($total_exec_time / $pdf_total, 3)) . ' ms' . PHP_EOL;
        echo '结束时间: ' . get_datetime_with_milliseconds() . PHP_EOL;
        exit();
    }

    /**
     * php app/cli.php test send_push
     * @param array $params
     */
    public function send_pushAction($params = [])
    {
        //        // push
        $push_data = [
            'staff_info_id'    => $params[0] ?? '19515',                    // 员工工号
            'message_title'    => '报销单提交失败提醒-th 报销单提交失败提醒-en',              // 标题
            'message_content'  => '',                                       // 内容
            'message_scheme'   => 'flashbackyard://fe/html?url=https%3A%2F%2Fdev-01-th-by-v3-ui.fex.pub%2Freimbursement-apply-detail%3Forder_no%3DBX202504220003%26src%3Dmsg',
            'message_priority' => 1,// push优先级: 0-普通; 1-优先
            'src'              => 'backyard',
            'silence'          => 0 // 0-普通推送; 1-静默推送
        ];
        $ret       = new ApiClient('bi_svc', '', 'push_to_staff');
        $ret->setParams([$push_data]);
        $res = $ret->execute();
        var_dump($push_data, $res);
        exit();
    }

}
