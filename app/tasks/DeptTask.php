<?php

use App\Modules\Organization\Services\DeptPcCodeService;
use App\Modules\Organization\Services\DepartmentService;
use App\Modules\Organization\Services\HrJobTitleService;
use App\Library\Enums\StaffInfoEnums;
use App\Models\backyard\HrStaffInfoModel;
use App\Modules\User\Models\StaffPermissionModel;
use App\Modules\User\Models\PermissionModel;
use App\Modules\Organization\Services\BaseService;
use App\Repository\HrStaffRepository;
use App\Library\RoleEnums;

class DeptTask extends BaseTask
{
    /*
     * 每周一发送上一周的变更数据
     */
    public function sendDeptOperateLogAction()
    {
        try {
            echo 'begin' . PHP_EOL;
            $current_date        = date('Y-m-d');
            $where['start_time'] = date('Y-m-d 00:00:00', strtotime("$current_date - 7 days"));  //上周开始日期
            $where['end_time']   = date('Y-m-d 23:59:59', strtotime("$current_date - 1 days"));  //上周结束日期
            echo '时间范围：' . $where['start_time'] . '-' . $where['end_time'] . PHP_EOL;

            $dept_pc_code_service = new DeptPcCodeService();
            $dept_pc_code_service::setLanguage('zh-CN');
            $list = $dept_pc_code_service->getOperateLogList($where);

            $subject    = 'OA Organization Change Reminder';
            $table_list = '';
            foreach ($list as $value) {
                $table_list .= "<tr>";
                $table_list .= "<td style='padding: 10px;'>{$value['created_at']}</td>";    //时间
                $table_list .= "<td style='padding: 10px;'>{$value['operate']}</td>";       //操作人
                $table_list .= "<td style='padding: 10px;'>{$value['dept_name']}</td>";     //操作人
                $table_list .= "<td style='padding: 10px;'>{$value['operate_type']}</td>";  //操作类型
                $table_list .= "<td style='padding: 10px;'>{$value['change_content']}</td>";//操作内容
                $table_list .= "<td style='padding: 10px;'>{$value['before']}</td>";        //Before
                $table_list .= "<td style='padding: 10px;'>{$value['after']}</td>";         //After
                $table_list .= "</tr>";
            }

            $content = "
            <p><strong>Dear all,</strong></p>
            <p>Please kindly note that below items were changed in OA system. </p>
            <table border='1' cellspacing='0' cellpadding='0'>
                <th>时间</th>
                <th>操作人</th>
                <th>部门</th>
                <th>操作类型</th>
                <th>操作内容</th>
                <th>Before</th>
                <th>After</th>
                {$table_list}
            </table>
            <p>Thanks!</p><p>OA system reminder</p>
            ";

            $emails = \App\Modules\Common\Models\EnvModel::getEnvByCode("send_dept_operate_log_email");
            if (empty($emails)) {
                $this->logger->warning("purchase order send email null==" . json_encode($emails,
                        JSON_UNESCAPED_UNICODE));
            }
            $emailArr = explode(",", $emails);
            $flag     = $this->mailer->sendAsync($emailArr, $subject, $content);
            if ($flag) {
                $this->logger->info('sendDeptOperateLog 发送成功');
            } else {
                $this->logger->warning("sendDeptOperateLog 发送失败");
            }
            echo '结果：' . $flag . PHP_EOL;
            echo 'end' . PHP_EOL;
        } catch (\Exception $e) {
            $this->logger->error('发送组织架构数据变更记录失败：' . $e->getMessage());
        }
    }

    /**
     * 临时脚本 导出有组织架构权限工号
     * @return void
     */
    public function export_staff_permission_by_organizationAction()
    {
        echo 'begin' . PHP_EOL;
        $list = StaffPermissionModel::find([
            "conditions" => "find_in_set('140', permission_ids)",
        ])->toArray();

        $staff_info_ids = array_column($list, 'staff_id');

        $staff_info_list = HrStaffInfoModel::find([
            "conditions" => "staff_info_id in ({staff_info_id:array})",
            "bind"       => [
                "staff_info_id" => $staff_info_ids,
            ],
            "columns"    => "staff_info_id,name,state,formal,sys_store_id,job_title,node_department_id",
        ])->toArray();

        $department_ids  = array_column($staff_info_list, 'node_department_id');
        $job_title_ids   = array_column($staff_info_list, 'job_title');
        $department_list = (new DepartmentService())->getDepartmentInfoByIds($department_ids);
        $department_list = array_column($department_list, 'name', 'id');
        $job_title_list  = (new HrJobTitleService())->getJobInfoByIds($job_title_ids);
        $job_title_list  = array_column($job_title_list, 'job_name', 'id');
        $staff_info_list = array_column($staff_info_list, null, 'staff_info_id');

        // 59  部门架构
        // 311 职级体系
        $organization_permission            = [140];
        $organization_department_permission = [59, 60, 61, 62, 63, 141, 142, 201, 202, 365, 665];
        $organization_job_permission        = [331, 338, 339, 340, 341, 380];
        $organization_job_permission_338    = [342, 343, 344, 345, 346, 347, 348, 720];
        $organization_job_permission_339    = [349, 350, 351, 352, 353, 354, 689];
        $organization_job_permission_340    = [355, 356, 357, 358, 359, 690];
        $organization_job_permission_341    = [360, 361, 362, 363, 364, 691];
        $organization_job_permission_380    = [381];

        $organization_all_ids = array_merge(
            $organization_permission,
            $organization_department_permission,
            $organization_job_permission,
            $organization_job_permission_338,
            $organization_job_permission_339,
            $organization_job_permission_340,
            $organization_job_permission_341,
            $organization_job_permission_380
        );

        $organization_permission_list = PermissionModel::find([
            "conditions" => "id in ({id:array})",
            "bind"       => [
                "id" => $organization_all_ids,
            ],
        ])->toArray();
        $organization_permission_list = array_column($organization_permission_list, null, 'id');
        $excel_result                 = [];
        foreach ($list as $key => $value) {
            $staff_info      = $staff_info_list[$value['staff_id']] ?? [];
            $staff_name      = '';
            $state_text      = '';
            $department_name = '';
            $job_title_name  = '';
            if (!empty($staff_info)) {
                $staff_name = $staff_info['name'];
                switch ($staff_info['state']) {
                    case StaffInfoEnums::STAFF_STATE_IN:
                        $state_text = '在职';
                        break;
                    case StaffInfoEnums::STAFF_STATE_LEAVE:
                        $state_text = '离职';
                        break;
                    case StaffInfoEnums::STAFF_STATE_STOP:
                        $state_text = '停职';
                        break;
                }
                $department_name = $department_list[$staff_info['node_department_id']];
                $job_title_name  = $job_title_list[$staff_info['job_title']];
            }

            $staff_permission_arr = explode(',', $value['permission_ids']);
            foreach ($staff_permission_arr as $p_v) {
                if (in_array($p_v, $organization_all_ids)) {
                    $organization_permission_name = '[' . $p_v . ']' . $organization_permission_list[$p_v]['name'];
                    $ancestry_id                  = $organization_permission_list[$p_v]['ancestry'];
                    $ancestry_name                = !empty($ancestry_id) ? '[' . $ancestry_id . ']' . $organization_permission_list[$ancestry_id]['name'] . '-' : '';
                    $organization_permission_name = $ancestry_name . $organization_permission_name;
                    $excel_result[]               = [
                        $organization_permission_name,
                        $value['staff_id'],
                        $staff_name,
                        $state_text,
                        $department_name,
                        $job_title_name,
                    ];
                }
            }
        }
        $upload_key = get_country_code() . '-staff_organization_permission' . time();
        //导出xlsx
        $excel_header = ['菜单功能', '工号', '姓名', '在职状态', '部门', '职位',];

        $path = BaseService::excelToFile($excel_header, $excel_result, $upload_key . '.xlsx');
        $url  = $path['object_url'] ?? '生成失败';
        echo $url . PHP_EOL;
        echo 'end' . PHP_EOL;
    }

    //临时脚本 给有组织架构权限的工号刷上新增菜单
    public function import_staff_permission_by_organizationAction()
    {
        echo 'begin' . PHP_EOL;
        $list = StaffPermissionModel::find([
            "conditions" => "find_in_set('140', permission_ids)",
        ]);

        $hrbp_list = (new HrStaffRepository())->getStaffListByRoleId(RoleEnums::ROLE_HRBP);//hrbp 68
        $hrpb_staff_info_ids = array_column($hrbp_list, 'staff_info_id');

        foreach ($list as $key => $staff_permission) {
            echo '工号：' . $staff_permission->staff_id . PHP_EOL;

            if(empty($staff_permission->permission_ids)) {
                echo $staff_permission->staff_id . '工号没有配置任何权限' . PHP_EOL;
                continue;
            }

            $before_permission_ids_str = $staff_permission->permission_ids;

            $before_permission_ids = $permission_ids = explode(',', $staff_permission->permission_ids);
            //历史数据中，已配置【批量创建部门】功能的工号，批量勾选中 批量新建 批量新建部门
            if(in_array(665, $before_permission_ids)) {
                $permission_ids = array_merge($permission_ids, [665,1049,1038,59,140]);
            }

            if(in_array(61, $before_permission_ids)) {
                if(in_array($staff_permission->staff_id, $hrpb_staff_info_ids)) {
                    //hrbp 如果角色含HRBP[id=68]，则批量勾选中 单个新建 新建部门
                    $permission_ids = array_merge($permission_ids, [61,1048,1038,59,140]);
                } else {
                    //如果不含HRBP[id=68]，则批量勾选中 单个新建 新建Clevel 新建BU 新建部门
                    $permission_ids = array_merge($permission_ids, [61,1050,1051,1048,1038,59,140]);
                }
            }

            //1. 历史数据中，已配置【更新部门】功能的工号，批量勾选中
            //  1. 设置Clevel
            //    1. 设置组织信息-Clevel
            //  2. 设置BU
            //    1. 设置组织信息-BU
            //  3. 编辑部门
            //    1. 编辑组织信息-部门
            if(in_array(62, $before_permission_ids)) {
                $permission_ids = array_merge($permission_ids, [62,1044,1054,1043,1052,1042,1039,59,140]);
            }

            //2. 历史数据中，已配置【部门SAP信息编辑】功能的工号，批量勾选中
            //  1. 设置Clevel
            //    1. 设置SAP信息-Clevel
            //  2. 设置BU
            //    1. 设置SAP信息-BU
            //  3. 编辑部门
            //    1. 编辑SAP信息-部门
            if(in_array(365, $before_permission_ids)) {
                $permission_ids = array_merge($permission_ids, [379,1044,1055,1043,1053,1042,1039,59,140]);
            }

            //1. 历史数据中，已配置【部门删除】功能的工号，批量勾选中
            //  1. 删除组织
            //    1. 删除Clevel
            //    2. 删除BU
            //    3. 删除部门
            if(in_array(63, $before_permission_ids)) {
                $permission_ids = array_merge($permission_ids, [63,1045,1046,1040,59,140]);
            }

            //1. 历史数据中，已配置【部门导出】或【导出所有部门】功能的工号，批量勾选中
            //  1. 导出组织
            //    1. 导出网点
            //    2. 导出部门
            if(in_array(201, $before_permission_ids) || in_array(141, $before_permission_ids)) {
                $permission_ids = array_merge($permission_ids, [201,1047,1041,59,140]);
            }

            $permission_ids = array_unique($permission_ids);

            $staff_permission->permission_ids  = implode(',', $permission_ids);
            $staff_permission->last_updated_at = date('Y-m-d H:i:s');

            $after_permission_ids_str = $staff_permission->permission_ids;

            $r = $staff_permission->save();
            echo '-----增加菜单;结果：' . $r . PHP_EOL;
            echo '-----增加菜单；原菜单ID：' . $before_permission_ids_str . PHP_EOL;
            echo '-----增加菜单；增加后菜单ID：' . $after_permission_ids_str . PHP_EOL;
        }

        echo 'end' . PHP_EOL;
    }

    //临时脚本 HRBP角色仅名单中的工号权限需要保留，其他员工的权限需要取消 ，未来手动配置
    public function import_hrbp_permission_by_organizationAction()
    {
        echo 'begin' . PHP_EOL;
        $hrbp_list = (new HrStaffRepository())->getStaffListByRoleId(RoleEnums::ROLE_HRBP);//hrbp 68

        $organization_permission            = [140];
        $organization_department_permission = [59,60,142,202,1038,1048,61,1050,1051,1049,665,1039,1042,1052,1053,1043,1054,1055,1044,62,379,1040,63,1045,1046,1041,201,1047];
        $organization_job_permission        = [331,338,342,343,344,345,346,347,348,720,339,349,350,351,352,353,354,689,340,355,356,357,358,359,690,341,360,361,362,363,364,691,380,381];
        $organization_all_ids = array_merge($organization_permission, $organization_department_permission, $organization_job_permission);

        $save_staff_info_ids = [58256,56007,23799,39462,604538,36742,28595,21314,624433,637194,636324,20252,28228,604607,76611,641910,119025,77049,78729,83036,118699];

        foreach ($hrbp_list as $key => $value) {
            echo '工号：' . $value['staff_info_id'] . PHP_EOL;
            $staff_permission = StaffPermissionModel::findFirst([
                "conditions" => "staff_id = :staff_info_id:",
                "bind"       => [
                    'staff_info_id' => $value['staff_info_id'],
                ],
            ]);
            //HRBP角色仅名单中的工号权限需要保留，其他员工的权限需要取消 ，未来手动配置
            if (!empty($staff_permission)) {
                if (!in_array($value['staff_info_id'], $save_staff_info_ids)) {
                    if (empty($staff_permission->permission_ids)) {
                        echo $value['staff_info_id'] . '没有配置任何菜单权限' . PHP_EOL;
                        continue;
                    }

                    $before_permission_ids_str = $staff_permission->permission_ids;
                    $permission_ids            = explode(',', $staff_permission->permission_ids);

                    $permission_ids = array_unique(array_diff($permission_ids, $organization_all_ids));

                    $staff_permission->permission_ids = implode(',', $permission_ids);

                    $staff_permission->last_updated_at = date('Y-m-d H:i:s');

                    $after_permission_ids_str = $staff_permission->permission_ids;

                    $r = $staff_permission->save();
                    echo '-----删除菜单;结果：' . $r;
                    echo '-----删除菜单；原菜单ID：' . $before_permission_ids_str . PHP_EOL;
                    echo '-----删除菜单；删除后菜单ID：' . $after_permission_ids_str . PHP_EOL;
                }
            } else {
                echo $value['staff_info_id'] . '--未配置权限' . PHP_EOL;
            }
        }
        echo 'end' . PHP_EOL;
    }

    /**
     * 临时脚本
     * 「OA-组织架构-职位体系-职位管理」中新建职位权限的工号：自动赋予「OA-组织架构-职位体系-职位列表」列表查看、新建职位、导出Excel权限
     * 「OA-组织架构-职位体系-职位管理」中修改职位名称权限的工号：自动赋予「OA-组织架构-职位体系-职位列表」列表查看、修改职位名称、导出Excel权限
     * @return void
     */
    public function updateJobTitlePermissionAction()
    {
        echo 'begin' . PHP_EOL;
        //343 - 新建职位 - 60人
        $list = StaffPermissionModel::find([
            "conditions" => "find_in_set('343', permission_ids) OR find_in_set('344', permission_ids)",
        ])->toArray();


        $db = $this->getDI()->get('db_oa');

        foreach ($list as $key => $value) {
            $permission_ids = explode(',', $value['permission_ids']);
            $permission_ids[] = 1283;
            $permission_ids[] = 1284;
            $permission_ids[] = 1285;
            $permission_ids = array_values(array_unique($permission_ids));
            $permission_ids = implode(',', $permission_ids);

            $db->updateAsDict(
                'staff_permission',
                [
                    'permission_ids' => $permission_ids,
                ],
                ['conditions' => 'id=?', 'bind' => $value['id']]
            );

            $this->logger->info('update_permission '.json_encode($value));
        }

        echo 'end' . PHP_EOL;
    }
}