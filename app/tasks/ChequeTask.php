<?php


use App\Modules\Cheque\Services\ChequeService;

class ChequeTask extends BaseTask
{


    /**
     * 支票台账与支付模块数据同步
     * @Date: 1/12/23 6:04 PM
     *  php  cli.php  Cheque batch_account_to_cheque_account
     * @author: peak pan
     **/

    public function batch_account_to_cheque_accountAction()
    {
        try {
            echo date('Ymd H:i:s') . ' 脚本开始执行' . PHP_EOL;
            $count_cheque = ChequeService::getInstance()->batchAccountToChequeAccountBusiness();
            echo date('Ymd H:i:s') . ' 脚本执行结束，执行数据' . $count_cheque . '条' . PHP_EOL;
        } catch (Exception $e) {
            $this->logger->warning('email error==' . $e->getMessage());
        }
    }

}
