<?php
/**
 * Class EmailTask
 * 邮件相关处理
 */

use App\Library\Validation\ValidationException;
use App\Library\ErrCode;

class EmailTask extends BaseTask
{

    /**
     * 定时定量消费待发邮件
     *
     * php app/cli.php email main
     */
    public function mainAction()
    {
        // 为适应 xxl-job k8s 分布式部署, 邮件队列消费的频率改为如下:
        // 1. 每分钟执行一次, 每次处理 30 条; 每天可消费: 30 * 1440 = 43200
        // 2. 以泰国生产为例, 当前每天的待审批的邮件数约为 700 左右, + 其他邮件通知场景, 1 中的消费量应该可以满足(及时性 可根据实际情况再做调整)
        $length = 30;

        $log = PHP_EOL . "Mail consumption queue: total loop $length" . PHP_EOL;
        $log .= 'Start Time: ' . date('Y-m-d H:i:s') . PHP_EOL;

        while ($length > 0) {
            $log .= PHP_EOL . "Curr loop: $length" . PHP_EOL;

            try {
                $task = $this->mailer->receiveAsync();

                if (!$task) {
                    $log .= 'Curr mail: [' . date('Y-m-d H:i:s') . '] null' . PHP_EOL;
                    break;
                }

                extract($task);

                $log .= 'Curr mail: [' . date('Y-m-d H:i:s') . '] (' . json_encode($emails,
                        JSON_UNESCAPED_UNICODE) . ", {$subject})" . PHP_EOL;

                $send_res = $this->mailer->send($emails, $subject, $content, $attachments, $cc);
                if ($send_res) {
                    $log .= 'Send success [' . date('Y-m-d H:i:s') . ']' . PHP_EOL;
                } else {
                    $log .= 'Send fail [' . date('Y-m-d H:i:s') . ']' . PHP_EOL;
                }
            } catch (Throwable $e) {
                $log .= 'Send abnormal [' . date('Y-m-d H:i:s') . ']'
                    . $e->getMessage()
                    . $e->getFile()
                    . $e->getLine()
                    . PHP_EOL;
            }

            $length--;
        }

        $log .= PHP_EOL . 'End Time: ' . date('Y-m-d H:i:s') . PHP_EOL;

        $this->logger->info("Email Consumption Queue Result: " . $log);
        exit($log);
    }

    /**
     * 查看邮件队列
     * @param array $params
     */
    public function countAction(array $params)
    {
        $log = PHP_EOL . '待发送邮件个数: ' . $this->mailer->getQueueLen() . PHP_EOL;
        echo $log;

        if (isset($params[0]) && $params[0] == 'll') {
            $log .= '邮件列表: ' . PHP_EOL;
            echo $log;

            $list = $this->mailer->getQueueItem();
            var_dump($list);
        }
        exit();
    }

    /**
     * 清空邮件队列
     * @param array $params
     */
    public function clearAction(array $params)
    {
        if (!isset($params[0]) || $params[0] != 'clr') {
            exit(PHP_EOL . 'Error Access' . PHP_EOL);
        }

        $log = PHP_EOL;
        if ($this->mailer->clearQueue()) {
            $log .= 'clear success';
        } else {
            $log .= 'clear fail';
        }

        $log .= PHP_EOL;

        exit($log);
    }

    /**
     * 添加测试邮件
     * @param array $params
     * php app/cli.php email test_send
     */
    public function test_sendAction(array $params)
    {
        try {
            if (!isset($params[0]) || $params[0] != 't') {
                exit(PHP_EOL . 'Error Access' . PHP_EOL);
            }

            $emails = [
                $params[1] ?? '<EMAIL>',
            ];

            $subject = 'Test Email Send';
//            $content = 'Email Send Test Content';

            $content = <<<EOF
            <br />
            <span style="margin-left: 20px"></span>%staff_info_id%%staff_info_name%，您好，您于%apply_date%提交的%module_name%%oa_biz_no%新增一条评论：<br />
            <span style="margin-left: 20px"></span>评论人：%comment_staff_info%<br />
            <span style="margin-left: 20px"></span>评论内容为%comment_content%！<br />
            <span style="margin-left: 20px"></span><b>请您及时登录OA或者点击<a href="%oa_sys_url%" target="_blank">%oa_sys_url%</a>进行查看，谢谢！</b><br />
EOF;

            $search  = [
                '%staff_info_id%',
                '%staff_info_name%',
                '%apply_date%',
                '%module_name%',
                '%oa_biz_no%',
                '%comment_staff_info%',
                '%comment_content%',
                '%oa_sys_url%',
            ];
            $replace = [
                '10000',
                'Admin',
                '2023-01-01',
                '支付管理',
                'ZF102020293',
                'Admin-Bike-J',
                '评论内容',
                'https://baidu.com',
            ];
            $content = str_replace($search, $replace, $content);


            $log = ["emails" => $emails, "subject" => $subject, "content" => $content];

            var_dump('测试参数: ', $log);

            $send_res = $this->mailer->send($emails, $subject, $content, '', '');

            var_dump('发送结果: ', $send_res);
        } catch (Exception $e) {
            $log = 'Send abnormal [' . date('Y-m-d H:i:s') . ']'
                . $e->getMessage()
                . $e->getFile()
                . $e->getLine()
                . PHP_EOL;

            var_dump($log);
        }
        exit();
    }

    /**
     * 添加异步邮件队列 - 测试
     *
     * @param array $params [0] 接收测试邮件的地址
     *
     * php app/cli.php email async_add_test <EMAIL>
     */
    public function async_add_testAction(array $params)
    {
        $log = '异步邮件测试-队列添加邮件' . PHP_EOL;

        $emails = [$params[0] ?? '<EMAIL>'];

        $title    = 'Test Email Async Send - ' . get_country_code();
        $content  = 'Test Email Async Send: ' . date('Y-m-d H:i:s');
        $send_res = $this->mailer->sendAsync($emails, $title, $content);

        $log .= "收件人: {$emails[0]}; 主题: $title" . PHP_EOL;
        if ($send_res) {
            $log .= '添加成功' . PHP_EOL;
        } else {
            $log .= '添加失败' . PHP_EOL;
        }

        $log .= '当前时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        exit($log);
    }

    /**
     * 实时发送邮件, 作为守护进程, job后台配置
     *
     * @param array $params 第一个参数为每分钟限制发送的数量, 默认 30条, 建议不要超出 60条
     * mail_queue_asynchronous_consumption
     *
     * php app/cli.php email async_send
     */
    public function async_sendAction($params = [])
    {
        $logger_type = 'info';

        try {
            // 待发邮件队列消费
            $task_sleep_count    = 0;                         // sleep 计数
            $per_sleep_seconds   = 60;                        // 每次 sleep 秒数
            $restart_sleep_count = 100;                       // sleep 累计超过该阈值, 则强制进程退出

            // 初始化发送参数
            $per_minute_send_limit_total = $params[0] ?? 30;  // 每分钟发送条数, 默认30
            $start_minute                = date('i', time()); // 每轮发送所在的分
            $per_minute_send_count       = 0;                 // 每分钟发送计数

            while (true) {
                // 如果同一分钟内, 发送条数超出指定条数, 则 sleep, sleep 时长为该分钟内剩余秒数
                if ($start_minute == date('i') && $per_minute_send_count >= $per_minute_send_limit_total) {
                    // 该分钟内剩余秒数
                    $remaining_seconds = $this->calFrequentlySendSleepSeconds();
                    $tmp_log           = "第 $start_minute 分钟内发送次数超过 $per_minute_send_limit_total 次, 将 sleep $remaining_seconds 秒";
                    $this->logger->info($tmp_log);
                    echo $tmp_log;
                    sleep($remaining_seconds);

                    // 重置 发送参数
                    $start_minute          = date('i', time());
                    $per_minute_send_count = 0;
                }

                $log = '当前时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

                // 获取待发邮件
                $task_data = $this->mailer->receiveAsync();
                if (empty($task_data)) {
                    $log .= '无待发邮件' . PHP_EOL;

                    $task_sleep_count++;
                    if ($task_sleep_count > $restart_sleep_count) {
                        $sleep_minutes = ($task_sleep_count * $per_sleep_seconds) / 60;
                        throw new ValidationException($log . "任务提醒: 任务已累计 sleep $task_sleep_count 次, 累计 sleep $sleep_minutes 分钟, 将退出重启",
                            ErrCode::$VALIDATE_ERROR);
                    }

                    $this->logger->info($log);
                    echo $log;
                    sleep($per_sleep_seconds);
                } else {
                    $log .= '有待发邮件: ' . json_encode($task_data, JSON_UNESCAPED_UNICODE) . PHP_EOL;

                    if (empty($task_data['emails']) && empty($task_data['cc'])) {
                        $log .= '收件人 和 抄送人均为空, 不予发送: ' . date('Y-m-d H:i:s') . PHP_EOL;
                    } else {
                        // 发送邮件
                        $send_res = $this->mailer->send($task_data['emails'], $task_data['subject'],
                            $task_data['content'], $task_data['attachments'], $task_data['cc']);
                        if ($send_res) {
                            $log .= '发送成功: ' . date('Y-m-d H:i:s') . PHP_EOL;
                        } else {
                            $log .= '发送失败: ' . date('Y-m-d H:i:s') . PHP_EOL;
                        }
                    }

                    // 发送计数
                    $per_minute_send_count++;

                    $this->logger->info($log);
                    echo $log;
                }
            }
        } catch (ValidationException $e) {
            $log = $e->getMessage() . PHP_EOL;
        } catch (Exception $e) {
            $error_log = '任务异常: ' . date('Y-m-d H:i:s') . ' ' . $e->getMessage() . $e->getFile() . $e->getLine() . PHP_EOL;

            // 针对 454 too frequently sending 发送异常的情况优化: 重新入队
            if (stripos($error_log, '454') && stripos($error_log, 'too frequently sending') && !empty($task_data)) {
                // 异常类型: 454 超出指定时间段的发送频率限制
                $this->mailer->sendAsync($task_data['emails'], $task_data['subject'], $task_data['content'],
                    $task_data['attachments'], $task_data['cc']);
                $remaining_seconds = $this->calFrequentlySendSleepSeconds();
                $error_log         .= "已重新入队, 待 $remaining_seconds 秒后, 重试发送" . PHP_EOL;
                sleep($remaining_seconds);
            } elseif (stripos($error_log, '559') || stripos($error_log, '554')) {
                // 异常类型: 559 邮件地址无效; 554 无效地址;
                // $logger_type = 'notice';

            } else {
                $logger_type = 'warning';
            }

            $log = isset($log) ? $log . $error_log : $error_log;
        }

        $this->logger->$logger_type($log);
        exit($log);
    }

    /**
     * 计算频繁发送异常时, 需sleep的秒数
     * 获取一分钟内剩余秒数
     */
    private function calFrequentlySendSleepSeconds()
    {
        // 该分钟内剩余秒数
        $remaining_seconds = 60 - date('s');
        return $remaining_seconds > 0 ? $remaining_seconds + 3 : 10;
    }

}
