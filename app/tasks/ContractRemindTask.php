<?php


use App\Library\Enums;
use App\Library\Enums\ContractEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Models\backyard\SysStoreModel;
use App\Models\oa\ContractStoreRentingRemindModel;
use App\Modules\Contract\Models\ContractStoreRentingModel;
use App\Modules\Payment\Models\PaymentStoreRenting;
use App\Modules\Payment\Models\PaymentStoreRentingDetail;
use App\Repository\oa\ContractStoreRentingNotRemindRepository;
use App\Repository\oa\ContractStoreRentingRemindRepository;
use App\Repository\StoreRepository;

class ContractRemindTask extends BaseTask
{
    /**
     * 生成网点合同签署提醒-待处理任务
     * php cli.php contract_remind create_contract_remind
     */
    public function create_contract_remindAction()
    {
        //上次进程是否结束
        $process_name = str_replace('Action', '', __FUNCTION__);
        $this->check_process($process_name);
        try {
            //查询网点(by库)
            $category = array_merge(GlobalEnums::STORE_CATEGORY_NETWORK, GlobalEnums::STORE_CATEGORY_NETWORK_BULKY);
            $store_data = SysStoreModel::find([
                'columns' => 'id, name, created_at',
                'conditions' => 'category in ({category:array}) and state = :state:',
                'bind' => [
                    'category' => $category,
                    'state' => 1
                ]
            ])->toArray();
            //查询不提醒网点id
            $not_remind_store_data = ContractStoreRentingNotRemindRepository::getInstance()->getData();
            //取id
            $not_remind_store_ids = array_column($not_remind_store_data, 'store_id');
            //判断网点创建时间是否大于7日
            foreach ($store_data as $k => $store) {
                $date_diff = strtotime(date('Y-m-d')) - strtotime(date('Y-m-d', strtotime($store['created_at'])));
                if ($date_diff < 7 * 24 * 60 * 60) {
                    unset($store_data[$k]);
                    continue;
                }
                //新增判断是否不提醒
                if (in_array($store['id'], $not_remind_store_ids)) {
                    unset($store_data[$k]);
                    continue;
                }
            }
            if (empty($store_data)) {
                echo '没有需要提醒的的网点数据1' . PHP_EOL;
                exit();
            }
            //查询这批网点待处理任务
            $store_data_kv = array_column($store_data, null, 'id');
            $store_ids = array_column($store_data, 'id');
            $store_ids = array_values($store_ids);
            $have_task_store = ContractStoreRentingRemindModel::find([
                'columns' => 'store_id',
                'conditions' => 'store_id in ({store_ids:array}) and task_status = :task_status:',
                'bind' => [
                    'store_ids' => $store_ids,
                    'task_status' => ContractEnums::TASK_STATUS_TODO
                ]
            ])->toArray();
            $have_task_store_ids = array_column($have_task_store, 'store_id');
            //找没有任务的网点
            $not_have_task_store_ids = array_diff($store_ids, $have_task_store_ids);
            $not_have_task_store_ids = array_values($not_have_task_store_ids);
            if (empty($not_have_task_store_ids)) {
                echo '没有需要提醒的的网点数据2' . PHP_EOL;
                exit();
            }
            //记录需要提醒的数据
            $need_remind_data = [];
            //下边只处理没有待处理任务的网点
            $now_date = date('Y-m-d');
            $now_date_time = date('Y-m-d H:i:s');
            //查询有效合同(主合同,待审核和审批通过 在有效期内) 构建为store_id为key的二维数组
            $valid_contract = ContractStoreRentingModel::find([
                'columns' => 'contract_id,store_id,contract_begin,contract_end,state,contract_status',
                'conditions' => 'store_id in ({store_ids:array}) and contract_begin <= :now_date: and contract_end >= :now_date:
                 and is_main = :is_main: and contract_status in ({contract_status:array})',
                'bind' => [
                    'store_ids' => $not_have_task_store_ids,
                    'now_date' => $now_date,
                    'is_main' => Enums::CONTRACT_IS_MASTER_YES,
                    'contract_status' => [Enums::CONTRACT_STATUS_PENDING, Enums::CONTRACT_STATUS_APPROVAL]
                ]
            ])->toArray();
            $valid_contract_data = [];
            foreach ($valid_contract as $valid_contract_v) {
                $valid_contract_data[$valid_contract_v['store_id']][] = $valid_contract_v;
            }
            //查询所有有效意向书 构建为store_id为key的二维数组
            $valid_loi = ContractStoreRentingModel::find([
                'columns' => 'contract_id,store_id,contract_begin,contract_end,state,contract_status',
                'conditions' => 'store_id in ({store_ids:array}) and contract_end >= :now_date:
                 and is_main = :is_main: and contract_status in ({contract_status:array})',
                'bind' => [
                    'store_ids' => $not_have_task_store_ids,
                    'now_date' => $now_date,
                    'is_main' => Enums::CONTRACT_IS_LOI_YES,
                    'contract_status' => [Enums::CONTRACT_STATUS_PENDING, Enums::CONTRACT_STATUS_APPROVAL]
                ]
            ])->toArray();

            //查询所有有效 主合同 构建为store_id为key的二维数组
            $valid_master = ContractStoreRentingModel::find([
                'columns' => 'contract_id,store_id,contract_begin,contract_end,state,contract_status',
                'conditions' => 'store_id in ({store_ids:array}) and contract_end >= :now_date:
                 and is_main = :is_main: and contract_status in ({contract_status:array})',
                'bind' => [
                    'store_ids' => $not_have_task_store_ids,
                    'now_date' => $now_date,
                    'is_main' => Enums::CONTRACT_IS_MASTER_YES,
                    'contract_status' => [Enums::CONTRACT_STATUS_PENDING, Enums::CONTRACT_STATUS_APPROVAL]
                ]
            ])->toArray();
            $valid_loi_data = [];
            foreach ($valid_loi as $valid_loi_v) {
                $valid_loi_data[$valid_loi_v['store_id']][] = $valid_loi_v;
            }

            $valid_master_data = [];
            foreach ($valid_master as $valid_master_v) {
                $valid_master_data[$valid_master_v['store_id']][] = $valid_master_v;
            }

            //有效合同不为空
            //符合条件的意向书
            $eligible_loi = [];
            $eligible_master = [];

            if (!empty($valid_contract_data)) {
                foreach ($not_have_task_store_ids as $v) {
                    //存在有效合同(在生效期内)
                    if (isset($valid_contract_data[$v])) {
                        foreach ($valid_contract_data[$v] as $master_contract_datum) {
                            //存在有效合同,合同有效期<90天
                            $date_diff = strtotime($master_contract_datum['contract_end']) - strtotime($now_date);
                            if ($date_diff < 90 * 24 * 60 * 60) {
                                $loi_valid_begin = date('Y-m-d', strtotime('-60 day', strtotime($master_contract_datum['contract_end'])));
                                $loi_valid_end = date('Y-m-d', strtotime('+60 day', strtotime($master_contract_datum['contract_end'])));
                                //存在有效合同,合同有效期<90天, 意向书结束时间在主合同结束时间+-60天范围内
                                if (isset($valid_loi_data[$v])) {
                                    foreach ($valid_loi_data[$v] as $loi_datum) {
                                        if (strtotime($loi_datum['contract_begin']) >= strtotime($loi_valid_begin)
                                            && strtotime($loi_datum['contract_begin']) <= strtotime($loi_valid_end)) {
                                            $eligible_loi[$v][] = $loi_datum;
                                        }
                                    }
                                }

                                //存在有效合同,合同有效期<90天, 意向书结束时间在主合同结束时间+-60天范围内
                                if (isset($valid_master_data[$v])) {
                                    foreach ($valid_master_data[$v] as $master_datum) {
                                        if ($master_contract_datum['contract_id'] == $master_datum['contract_id']) {
                                            continue;
                                        }
                                        if (strtotime($master_datum['contract_begin']) >= strtotime($loi_valid_begin)
                                            && strtotime($master_datum['contract_begin']) <= strtotime($loi_valid_end)) {
                                            $eligible_master[$v][] = $master_datum;
                                        }
                                    }
                                }
                                //合同有效期<90天,但主合同不符合条件的,且主合同不符合条件,需要[合同到期提醒]
                                if (!key_exists($v, $eligible_master) && !key_exists($v, $eligible_loi)) {
                                    $need_remind_data[$v] = [
                                        'store_id' => $v,
                                        'store_name' => $store_data_kv[$v]['name'],
                                        'store_created_at' => $store_data_kv[$v]['created_at'],
                                        'contract_begin' => $master_contract_datum['contract_begin'],//生成任务时判断的合同的开始时间
                                        'contract_end' => $master_contract_datum['contract_end'],//生成任务时判断的合同的结束时间
                                        'task_type' => ContractEnums::TASK_TYPE_CONTRACT_EXPIRE,
                                        'task_status' => ContractEnums::TASK_STATUS_TODO,
                                        'created_at' => $now_date_time,
                                        'updated_at' => $now_date_time,
                                    ];
                                }
                            }
                        }
                    } else {
                        //不存在合同的网点id
                        $not_have_contract_store_ids[] = $v;
                    }
                }

            } else {
                //不存在合同的网点id
                $not_have_contract_store_ids = $not_have_task_store_ids;
            }

            //不存在有效合同的网点处理
            if (!empty($not_have_contract_store_ids)) {
                foreach ($not_have_contract_store_ids as $v) {
                    //不存在有效合同,存在有效的意向书
                    if (isset($valid_loi_data[$v])) {
                        foreach ($valid_loi_data[$v] as $loi_datum) {
                            if ($loi_datum['contract_begin'] <= $now_date && $loi_datum['contract_end'] >= $now_date) {
                                $eligible_loi[$v][] = $loi_datum;
                            }
                        }
                    }
                    //不存在有效合同,也不存在有效意向书,需要[提醒意向书任务]
                    if (!key_exists($v, $eligible_loi)) {
                        $need_remind_data[$v] = [
                            'store_id' => $v,
                            'store_name' => $store_data_kv[$v]['name'],
                            'store_created_at' => $store_data_kv[$v]['created_at'],
                            'contract_begin' => null,
                            'contract_end' => null,
                            'task_type' => ContractEnums::TASK_TYPE_LOI_SIGN,
                            'task_status' => ContractEnums::TASK_STATUS_TODO,
                            'created_at' => $now_date_time,
                            'updated_at' => $now_date_time,
                        ];
                    }
                }
            }
            //拥有符合条件的意向书的网点=>意向书数据
            if (!empty($eligible_loi)) {
                $passed_loi_data = [];
                $passed_loi_no = [];
                foreach ($eligible_loi as $k_of_store_id => $v_of_loi_data) {
                    foreach ($v_of_loi_data as $loi_info) {
                        if ($loi_info['contract_status'] == Enums::CONTRACT_STATUS_APPROVAL) {
                            //已同意的意向书合同编号
                            $passed_loi_data[$k_of_store_id][] = $loi_info;
                            $passed_loi_no[] = $loi_info['contract_id'];
                        }
                    }
                }
                //查询所有意向书关联的定金付款
                if (!empty($passed_loi_data)) {
                    $passed_loi_no = array_values(array_unique($passed_loi_no));
                    $builder = $this->modelsManager->createBuilder();
                    $builder->columns('pd.contract_no,p.pay_status');
                    $builder->from(['pd' => PaymentStoreRentingDetail::class]);
                    $builder->leftjoin(PaymentStoreRenting::class, 'pd.store_renting_id=p.id', 'p');
                    $builder->inWhere('pd.contract_no', $passed_loi_no);
                    $builder->inWhere('p.approval_status', [Enums::PAYMENT_APPLY_STATUS_PENDING, Enums::PAYMENT_APPLY_STATUS_APPROVAL]);
                    $builder->andWhere('pd.cost_type = :cost_type:', ['cost_type' => ContractEnums::COST_TYPE_CONTRACT_DEPOSIT]);
                    $loi_deposit = $builder->getQuery()->execute()->toArray();
                    $loi_deposit_kv = [];
                    if (!empty($loi_deposit)) {
                        foreach ($loi_deposit as $v) {
                            $loi_deposit_kv[$v['contract_no']][] = $v;
                        }
                    }
                    foreach ($passed_loi_data as $k_for_store_id => $v_of_loi_data) {
                        //是否拥有定金支付单
                        $is_have_pay = false;
                        foreach ($v_of_loi_data as $loi_info) {
                            if (isset($loi_deposit_kv[$loi_info['contract_id']])) {
                                $is_have_pay = true;
                                //存在有效意向书,意向书定金已支付-签署合同提醒
                                foreach ($loi_deposit_kv[$loi_info['contract_id']] as $vof_loi_deposit) {
                                    if ($vof_loi_deposit['pay_status'] == Enums::PAYMENT_PAY_STATUS_PAY) {
                                        $need_remind_data[$k_for_store_id] = [
                                            'store_id' => $k_for_store_id,
                                            'store_name' => $store_data_kv[$k_for_store_id]['name'],
                                            'store_created_at' => $store_data_kv[$k_for_store_id]['created_at'],
                                            'contract_begin' => null,
                                            'contract_end' => null,
                                            'task_type' => ContractEnums::TASK_TYPE_CONTRACT_SIGN,
                                            'task_status' => ContractEnums::TASK_STATUS_TODO,
                                            'created_at' => $now_date_time,
                                            'updated_at' => $now_date_time,
                                        ];
                                        break;
                                    }
                                }
                            }
                        }
                        //存在有效意向书,但没有支付定金-支付定金提醒
                        if ($is_have_pay == false) {
                            $need_remind_data[$k_for_store_id] = [
                                'store_id' => $k_for_store_id,
                                'store_name' => $store_data_kv[$k_for_store_id]['name'],
                                'store_created_at' => $store_data_kv[$k_for_store_id]['created_at'],
                                'contract_begin' => null,
                                'contract_end' => null,
                                'task_type' => ContractEnums::TASK_TYPE_DEPOSIT_PAY,
                                'task_status' => ContractEnums::TASK_STATUS_TODO,
                                'created_at' => $now_date_time,
                                'updated_at' => $now_date_time,
                            ];
                        }
                    }
                }
            }
            //提醒数据入库
            if (empty($need_remind_data)) {
                echo '没有需要提醒的的网点数据3' . PHP_EOL;
                exit();
            }
            $model = new ContractStoreRentingRemindModel();
            $bool = $model->batch_insert($need_remind_data);
            if ($bool === false) {
                throw new Exception('网点合同签署提醒生成失败' . ' 可能的原因是: ' . get_data_object_error_msg($model) . '; 数据是: ' . json_encode($need_remind_data, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }
        } catch (Exception $e) {
            $this->logger->error('网点合同签署提醒生成失败: message=' . $e->getMessage() . '; trace=' . $e->getTraceAsString());
            echo 'error:' . $e->getMessage() . PHP_EOL;
            exit();
        }
        $this->logger->info('网点合同签署提醒生成成功: need_remind_data=' . json_encode($need_remind_data ?? [], JSON_UNESCAPED_UNICODE));
        $result = '执行结束,共生成' . count($need_remind_data) . '条';
        exit($result);
    }

    /**
     * 结束网点合同签署提醒-删除的网点待处理改成已处理
     * php cli.php contract_remind over_contract_remind
     */
    public function over_contract_remindAction()
    {
        //上次进程是否结束
        $process_name = str_replace('Action', '', __FUNCTION__);
        $this->check_process($process_name);
        try {
            //需要结束的任务
            $need_finished_data = $success_data = $error_data = [];
            //查询待处理的所有网点提醒 ph目前200+条数据
            $remind_data = ContractStoreRentingRemindRepository::getInstance()->getPendingData();
            $remind_data_arr = $remind_data->toArray();
            if (!empty($remind_data_arr)) {
                //取所有网点id
                $store_ids = array_column($remind_data_arr, 'store_id');
                //查所有网点详情
                $sys_store_data = (new StoreRepository())->getStoreListByIds($store_ids);
                $sys_store_ids = array_unique(array_column($sys_store_data, 'id'));
                $now_date = date('Y-m-d H:i:s');

                foreach ($remind_data as $item) {
                    //没在里边,说明被删除了
                    if (!in_array($item->store_id, $sys_store_ids)) {
                        $item->task_status = ContractEnums::TASK_STATUS_DONE;
                        $item->updated_at = $now_date;
                        $item->finished_at = $now_date;
                        $item_arr = $item->toArray();
                        //保存数据,记录日志
                        $need_finished_data[] = $item_arr;
                        if ($item->save() == false) {
                            $error_data[] = $item_arr;
                            $this->logger->warning('结束网点合同签署提醒-失败' . ' 可能的原因是: ' . get_data_object_error_msg($item) . '; 数据是: ' . json_encode($item->toArray(), JSON_UNESCAPED_UNICODE));
                        } else {
                            $success_data[] = $item_arr;
                        }

                    }
                }
            }
        } catch (Exception $e) {
            $this->logger->error('结束网点合同签署提醒-失败: message=' . $e->getMessage() . '; trace=' . $e->getTraceAsString());
            echo 'error:' . $e->getMessage() . PHP_EOL;
            exit();
        }
        $log = '结束网点合同签署提醒-成功: need_remind_data=' . json_encode($need_finished_data, JSON_UNESCAPED_UNICODE) . '; success_data=' . json_encode($success_data, JSON_UNESCAPED_UNICODE) . '; error_data=' . json_encode($error_data, JSON_UNESCAPED_UNICODE);
        $result = '执行结束,共处理' . count($need_finished_data) . '条, 成功:' . count($success_data) . '条, 失败:' . count($error_data) . '条';
        if (empty($error_data)) {
            $this->logger->info($log);
        } else {
            $this->logger->warning($log);
        }
        exit($result);
    }
}
