<?php

use App\Library\Enums;
use App\Library\Enums\VendorEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Modules\User\Models\AttachModel;
use App\Modules\Vendor\Models\SupplierOwnershipModel;
use App\Modules\Vendor\Models\Vendor;
use App\Modules\Vendor\Models\VendorApplicationModuleRelModel;
use App\Modules\Vendor\Models\VendorHistoryApprovalVersionModel;
use App\Modules\Vendor\Services\BaseService;
use App\Modules\Vendor\Services\SupplierCodeService;
use App\Modules\Organization\Models\SysDepartmentModel;
use App\Modules\Purchase\Services\OrderService;
use App\Models\oa\VendorGradeLogModel;


class VendorTask extends BaseTask
{
    const GROUP_CODE_1 = 4010;
    const GROUP_CODE_2 = 4020;
    const TH_OWNER = 2;
    const PH_OWNER = 5;
    const MY_OWNER = 6;
    const MY_TAX_RATE_CODE = 1;
    const TH_TAX_RATE_CODE = 53;
    const SUPPLIER_CATE = 101;


    // 供应商二期数据初始化: 供应商二期上线后，仅执行一次
    // 1.历史供应商数据与应用模块关系初始化
    // 2.历史供应商数据归档到审核通过版本记录表
    public function initAction()
    {
        $base_service = new BaseService();

        // 1.找出现有的供应商
        $all_vendor = Vendor::find()->toArray();

        $log = PHP_EOL . '共 ' . count($all_vendor) . '个供应商' . PHP_EOL;

        $rel_count       = 0;
        $rel_err_sql     = '';
        $version_count   = 0;
        $version_err_sql = '';

        $attach_count   = 0;
        $attach_err_sql = '';


        foreach ($all_vendor as $vendor) {
            // 2.创建供应商与应用模块（采购）的关系
            $_rel = [
                'vendor_id'             => $vendor['vendor_id'],
                'application_module_id' => 1,
            ];

            if ((new VendorApplicationModuleRelModel())->i_create($_rel) === false) {
                $rel_err_sql .= $vendor['vendor_id'] . PHP_EOL;
            } else {
                $rel_count++;
            }

            // 3.供应商附件迁往附件表
            if (!empty($vendor['attachment'])) {
                $attachment_arr = $base_service->getRemotePath(explode(',', $vendor['attachment']));

                foreach ($attachment_arr as $k => $file) {
                    $attachment_arr[$k]['oss_bucket_type'] = Enums::OSS_BUCKET_TYPE_VENDOR_BACK;
                    $attachment_arr[$k]['oss_bucket_key']  = $vendor['id'];
                }

                $attach      = new AttachModel();
                $attach_bool = $attach->batchInsert($attachment_arr);
                if ($attach_bool === false) {
                    $attach_err_sql .= $vendor['vendor_id'] . PHP_EOL;
                } else {
                    $attach_count++;
                }
            }

            // 4.供应商数据归档到历史通过版本表
            $vendor['src_id'] = $vendor['id'];
            unset($vendor['id']);
            $vendor['archive_time']           = date('Y-m-d H:i:s');
            $vendor['archiver_id']            = 10000;
            $vendor['application_module_ids'] = 1;

            if ((new VendorHistoryApprovalVersionModel())->i_create($vendor) === false) {
                $version_err_sql .= $vendor['vendor_id'] . PHP_EOL;
            } else {
                $version_count++;
            }
        }

        $log .= '关系成功创建 ' . $rel_count . ' 条' . PHP_EOL;
        $log .= '关系创建失败的供应商: ' . $rel_err_sql . PHP_EOL . PHP_EOL;

        $log .= '附件成功迁移 ' . $attach_count . ' 条' . PHP_EOL;
        $log .= '附件迁移失败的供应商: ' . $attach_err_sql . PHP_EOL . PHP_EOL;

        $log .= '创建版本 ' . $version_count . ' 条' . PHP_EOL;
        $log .= '版本创建失败的供应商: ' . $version_err_sql . PHP_EOL;
        exit($log);
    }

    public function createSapAction()
    {
        $this->checkLock(__METHOD__);
        echo 'begin: ' . date('Y-m-d H:i:s');
        echo PHP_EOL;

        try {
            $cost_company_id = OrderService::getInstance()->getEnvByCode('vendor_sap_company_ids');
            if (empty($cost_company_id)) {
                throw new ValidationException('vendor_sap_company_ids 未配置', ErrCode::$VALIDATE_ERROR);
            }

            $sap_company_ids = SysDepartmentModel::find(
                [
                    'conditions' => 'id in ({ids:array})',
                    'bind'       => ['ids' => explode(',', $cost_company_id)],
                ]
            )->toArray();
            $sap_company_ids = array_column($sap_company_ids, 'sap_company_id') ?? [];
            //iso编码
            $ownership_item = SupplierOwnershipModel::find([
                'condition' => 'is_del = 0',
                'columns'   => ['id', 'iso_code'],
            ])->toArray();
            $ownership_item = array_column($ownership_item, 'iso_code', 'id');
            $db_obj         = $this->db_oa;

            $send_data = [];
            //发送邮件 目前只有泰国
            $email = \App\Modules\Common\Models\EnvModel::getEnvByCode('purchase_email');
            $email = explode(',', $email);

            $i   = 0;
            $x   = 0;
            $sql = "select id,vendor_id,vendor_name,vendor_short_name,ownership,sap_supplier_no,is_send_sap from vendor  where is_send_sap =1  limit {$i} ,100";

            $data          = $db_obj->query($sql);
            $deal_sap_data = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            $x             = 0;
            while (!empty($deal_sap_data)) {
                foreach ($deal_sap_data as $key => $value) {
                    $request_data = [];
                    $return_data  = [];

                    $vendor = Vendor::findFirst([
                        'id = :id:',
                        'bind' => ['id' => $value['id']],
                    ]);

                    if (empty($vendor)) {
                        continue;
                    }
                    $supplier_code = $value['sap_supplier_no'];
                    if (empty($value['sap_supplier_no'])) {//为空创建
                        $supplier_num  = SupplierCodeService::getInstance()->createSupplierNumber(1, 1);
                        $supplier_code = SupplierCodeService::getInstance()->createSupplierCode($supplier_num[0]);
                    }

                    $request_data = [
                        'vendor_id'         => $value['vendor_id'],
                        'cost_company'      => $sap_company_ids[0] ?? 'FEX01',
                        'sap_supplier_no'   => trim($supplier_code),
                        'vendor_name'       => $value['vendor_name'],
                        'vendor_short_name' => $value['vendor_short_name'],
                        'ownership'         => $value['ownership'],
                        'ownership_code'    => $ownership_item[$value['ownership']],
                        'group_code'        => '',
                    ];
                    $z            = 0;
                    echo 'vendor id:' . $value['id'];
                    echo PHP_EOL;
                    while (empty($return_data) && $z < 1) {
                        $z++;
                        $return_data = $this->sendSap(get_country_code(), $request_data);
                        if (isset($return_data['UUID']) && !empty(['UUID'])) {
                            $x++;
                            $vendor->i_update([
                                'is_send_sap'     => 2,
                                'sap_supplier_no' => trim($supplier_code),
                                'updated_at'      => date('Y-m-d H:i:s'),
                            ]);
                            echo 'success vendor id:' . $value['id'];
                        } else {
                            $vendor->i_update(['is_send_sap' => 3, 'updated_at' => date('Y-m-d H:i:s')]);
                        }
                        sleep(2);
                    }

                    echo PHP_EOL;
                }


                sleep(1);
                $i   += 100;
                $sql = "select id,vendor_id,vendor_name,ownership,sap_supplier_no,is_send_sap from vendor  where is_send_sap =1 limit {$i} ,100";

                $data          = $db_obj->query($sql);
                $deal_sap_data = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            }

            echo 'vendor 成功:' . $x . '条数' . PHP_EOL;
        } catch (ValidationException $e) {
            $this->logger->notice('vendor_sap_task_validation:' . $e->getMessage());
            echo $e->getMessage() . PHP_EOL;
        } catch (Exception $e) {
            $this->logger->warning('vendor_sap_task_exception:' . $e->getMessage());
            echo $e->getMessage() . PHP_EOL;
        }

        echo 'end: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $this->clearLock(__METHOD__);
        exit();
    }

    public function sendSap($country_code, $request_data)
    {
        $return_data = [];
        switch ($country_code) {
            case 'TH':
                $group_code = self::GROUP_CODE_2;
                if ($request_data['ownership'] == self::TH_OWNER) {
                    $group_code = self::GROUP_CODE_1;
                }
                $request_data['group_code']    = $group_code;
                $request_data['tax_rate_code'] = self::TH_TAX_RATE_CODE;
                $request_data['supplier_cate'] = '';

                $return_data = \App\Modules\Vendor\Services\SapsService::getInstance()->createdSAP($request_data);
                break;
            case 'PH':
                $group_code = self::GROUP_CODE_2;
                if ($request_data['ownership'] == self::PH_OWNER) {
                    $group_code = self::GROUP_CODE_1;
                }
                $request_data['group_code'] = $group_code;
                $return_data                = \App\Modules\Vendor\Services\SapsService::getInstance()->createdSAPPh($request_data);

                break;
            case 'MY':
                $group_code = self::GROUP_CODE_2;
                if ($request_data['ownership'] == self::MY_OWNER) {
                    $group_code = self::GROUP_CODE_1;
                }
                $request_data['tax_rate_code'] = self::MY_TAX_RATE_CODE;

                $request_data['group_code']    = $group_code;
                $request_data['supplier_cate'] = self::SUPPLIER_CATE;
                $return_data                   = \App\Modules\Vendor\Services\SapsService::getInstance()->createdSAP($request_data);

                break;
            default;
        }

        return $return_data;
    }

    /**
     * 历史数据迁移
     * 主表部分字段迁移到详情表
     * */

    public function deal_vendor_dataAction()
    {
        $db_obj            = $this->db_oa;
        $insert_bank_data  = [];
        $insert_third_data = [];
        try {
            $i = 0;

            $sql = "select id,vendor_id,`contact`,`contact_phone`, `contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`payment_method`, `third_payer`,`third_payee_account_code`,`third_payee_account_name`,`swift_code` from vendor limit {$i} ,500";

            $data          = $db_obj->query($sql);
            $deal_sap_data = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            $x             = 0;

            while (!empty($deal_sap_data)) {
                foreach ($deal_sap_data as $key => $value) {
                    $x++;
                    switch ($value['payment_method']) {
                        case 1:
                            $insert_bank_data[] = [
                                'main_id'           => $value['id'],
                                'contact'           => $value['contact'],
                                'contact_phone'     => $value['contact_phone'],
                                'contact_email'     => $value['contact_email'],
                                'bank_no'           => $value['bank_no'],
                                'bank_account_name' => $value['bank_account_name'],
                                'payment_method'    => $value['payment_method'],
                                'bank_pay_name'     => $value['bank_code'],
                                'swift_code'        => $value['swift_code'],
                                'created_at'        => date('Y-m-d H:i:s'),
                                'updated_at'        => date('Y-m-d H:i:s'),
                            ];
                            break;
                        case 2:
                            $insert_third_data[] = [
                                'main_id'           => $value['id'],
                                'contact'           => $value['contact'],
                                'contact_phone'     => $value['contact_phone'],
                                'contact_email'     => $value['contact_email'],
                                'bank_no'           => $value['third_payee_account_code'],
                                'bank_account_name' => $value['third_payee_account_name'],
                                'payment_method'    => $value['payment_method'],
                                'bank_pay_name'     => $value['third_payer'],
                                'swift_code'        => $value['swift_code'],
                                'created_at'        => date('Y-m-d H:i:s'),
                                'updated_at'        => date('Y-m-d H:i:s'),
                            ];
                            break;
                        case 3:
                            $insert_bank_data[]  = [
                                'main_id'           => $value['id'],
                                'contact'           => $value['contact'],
                                'contact_phone'     => $value['contact_phone'],
                                'contact_email'     => $value['contact_email'],
                                'bank_no'           => $value['bank_no'],
                                'bank_account_name' => $value['bank_account_name'],
                                'payment_method'    => 1,
                                'bank_pay_name'     => $value['bank_code'],
                                'swift_code'        => $value['swift_code'],
                                'created_at'        => date('Y-m-d H:i:s'),
                                'updated_at'        => date('Y-m-d H:i:s'),
                            ];
                            $insert_third_data[] = [
                                'main_id'           => $value['id'],
                                'contact'           => $value['contact'],
                                'contact_phone'     => $value['contact_phone'],
                                'contact_email'     => $value['contact_email'],
                                'bank_no'           => $value['third_payee_account_code'],
                                'bank_account_name' => $value['third_payee_account_name'],
                                'payment_method'    => 2,
                                'bank_pay_name'     => $value['third_payer'],
                                'swift_code'        => $value['swift_code'],
                                'created_at'        => date('Y-m-d H:i:s'),
                                'updated_at'        => date('Y-m-d H:i:s'),
                            ];
                            break;
                        default;
                    }
                }


                sleep(1);
                $i   += 500;
                $sql = "select id,vendor_id,`contact`,`contact_phone`, `contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`payment_method`, `third_payer`,`third_payee_account_code`,`third_payee_account_name`,`swift_code` from vendor limit {$i} ,500";


                $data          = $db_obj->query($sql);
                $deal_sap_data = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            }

            $insert_data = [];
            if (!empty($insert_bank_data)) {
                $insert_data = array_merge($insert_bank_data, $insert_third_data);

                $obj  = new \App\Modules\Vendor\Models\VendorPaymentDetailModel();
                $bool = $obj->batch_insert($insert_data);
                if ($bool) {
                    echo '总条数' . $x . '条';
                }
            }


            echo 'end: ' . date('Y-m-d H:i:s');
        } catch (Exception $e) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('vendor_sap_task_exception:' . $e->getMessage());
            var_dump($e->getMessage());
            return false;
        }
    }


    /**
     * 处理暂停状态结束的供应商
     * 恢复正常状态
     * */
    public function recover_normal_vendorAction()
    {
        echo 'begin:' . date('Y-m-d H:i:s') . PHP_EOL;

        $db_obj = $this->db_oa;
        $db_obj->begin();

        try {
            $data       = [];
            $vendor_ids = [];

            $res = Vendor::find([
                'conditions' => 'suspend_end_time <= :suspend_end_time: and grade_status = :grade_status:',
                'bind'       => [
                    'suspend_end_time' => date('Y-m-d H:i:s'),
                    'grade_status'     => VendorEnums::VENDOR_GRADE_STATUS_SUSPEND,
                ],
                'columns'    => ['vendor_id'],
            ])->toArray();

            if (empty($res)) {
                throw new ValidationException('没有数据需要处理', ErrCode::$VALIDATE_ERROR);
            }

            $change_description = 'Suspended over, automatically return to normal status';

            foreach ($res as $item) {
                $vendor_ids[] = $item['vendor_id'];
                $data[]       = [
                    'vendor_id'  => $item['vendor_id'],
                    'type'       => VendorEnums::VENDOR_GRADE_TYPE_2,
                    'content'    => json_encode([
                        'change_before'      => VendorEnums::VENDOR_GRADE_STATUS_SUSPEND,
                        'change_after'       => VendorEnums::VENDOR_GRADE_STATUS_NORMAL,
                        'change_description' => $change_description,

                    ], JSON_UNESCAPED_UNICODE),
                    'create_id'  => 10000,
                    'created_at' => date('Y-m-d H:i:s'),
                ];
            }
            print_r($vendor_ids);
            $vendor_ids = "'" . implode("','", $vendor_ids) . "'";

            $update_bool = $db_obj->updateAsDict(
                (new Vendor())->getSource(),
                [
                    'grade_status'       => VendorEnums::VENDOR_GRADE_STATUS_NORMAL,
                    'change_description' => $change_description,
                    'grade_change_at'    => date('Y-m-d H:i:s'),

                ],
                ['conditions' => "vendor_id in ($vendor_ids)"]
            );
            if ($update_bool === false) {
                throw new BusinessException('vendor 更新失败');
            }

            $vendor_grade_obj = new VendorGradeLogModel();

            $insert_bool = $vendor_grade_obj->batch_insert($data);

            if ($insert_bool === false) {
                throw new BusinessException('vendor_grade_log 插入失败');
            }

            $db_obj->commit();
        } catch (ValidationException $e) {
            $db_obj->rollback();
            $this->logger->info('vendor_recover_normal_task_validation:' . $e->getMessage());
            echo $e->getMessage() . PHP_EOL;
        } catch (BusinessException $e) {
            $db_obj->rollback();
            $this->logger->notice('vendor_recover_normal_task_exception:' . $e->getMessage());
            echo $e->getMessage() . PHP_EOL;
        } catch (Exception $e) {
            $db_obj->rollback();
            $this->logger->warning('vendor_recover_normal_task_exception:' . $e->getMessage());
            echo $e->getMessage() . PHP_EOL;
        }

        echo 'end: ' . date('Y-m-d H:i:s') . PHP_EOL;
        exit();
    }

    /**
     * 处理含采购
     * 供应商引入理由给默认值
     * */

    public function deal_vendor_reasonAction()
    {
        echo 'begin:' . date('Y-m-d H:i:s') . PHP_EOL;
        $db_obj = $this->db_oa;
        $db_obj->begin();
        try {
            $builder = $this->modelsManager->createBuilder();
            $builder->columns(['main.vendor_id']);
            $builder->from(['main' => Vendor::class]);
            $builder->leftjoin(VendorApplicationModuleRelModel::class, 'amr.vendor_id = main.vendor_id ', 'amr');
            $builder->andWhere('amr.application_module_id = :application_module_id:',
                ['application_module_id' => VendorEnums::VENDOR_PURCHASE]);
            $data  = $builder->getQuery()->execute()->toArray();
            $count = 0;
            if (!empty($data)) {
                $vendor_ids = array_column($data, 'vendor_id');
                $count      = count($vendor_ids);

                $vendor_ids = "'" . implode("','", $vendor_ids) . "'";

                $reason      = '历史供应商Historical Supplier';
                $update_bool = $db_obj->updateAsDict(
                    (new Vendor())->getSource(),
                    [
                        'is_purchase_reason' => $reason,

                    ],
                    ['conditions' => "vendor_id in ($vendor_ids)"]
                );
                if ($update_bool === false) {
                    throw new BusinessException('vendor is_purchase_reason 更新失败');
                }
            }
            echo '执行成功条数：' . $count . PHP_EOL;

            $db_obj->commit();
        } catch (BusinessException $e) {
            $db_obj->rollback();
            $this->logger->warning('deal_vendor_reason_task_exception:' . $e->getMessage());
        } catch (Exception $e) {
            $this->logger->warning('deal_vendor_reason_task_exception:' . $e->getMessage());
            var_dump($e->getMessage());
        }
        echo 'end:' . date('Y-m-d H:i:s') . PHP_EOL;

        exit();
    }

}