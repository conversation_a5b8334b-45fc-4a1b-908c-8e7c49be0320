<?php

use App\Library\Enums\DownloadCenterEnum;
use App\Modules\Common\Models\ExcelTaskModel;

class ExcelTask extends BaseTask
{
    /**
     * 人才盘点下载任务
     */
    public function talent_reviewAction() {
        echo 'begin' . PHP_EOL;
        $list = ExcelTaskModel::find([
            'conditions' => '  type in ({type:array}) and is_deleted = 0  and status = 0 ',
            'bind' => [
                'type' => [
                    DownloadCenterEnum::TYPE_TALENT_REVIEW_STAFF_SCOPE_1,
                    DownloadCenterEnum::TYPE_TALENT_REVIEW_STAFF_SCOPE_2,
                    DownloadCenterEnum::TYPE_TALENT_REVIEW_STAFF_SCOPE_3
                ]
            ],
            'limit' => 1,
            'order' => 'id asc',
        ])->toArray();

        if (!empty($list)) {
            foreach ($list as $key => $value) {
                try {
                    $value['action_name'] = trim($value['action_name']);
                    $str = PHP_BINDIR . "/php {$value['executable_path']} {$value['action_name']} {$value['args_json']} {$value['id']}";
                    echo '下载任务:' . $str . PHP_EOL;
                    $this->logger->info('excel_task:'. $str);
                    exec($str);
                } catch (Exception $e) {
                    echo 'error:' . $e->getMessage() . PHP_EOL;
                    $this->logger->error('excel_task_error'. '--code:'.$e->getCode(). '--message:'.$e->getMessage(). '--trace:'.$e->getTraceAsString());
                }
            }
        } else {
            echo '没有下载任务' . PHP_EOL;
        }
        echo 'end' . PHP_EOL;
    }
}