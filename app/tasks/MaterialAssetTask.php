<?php

use App\Library\Enums\GlobalEnums;
use App\Library\Enums\ImportCenterEnums;
use App\Models\oa\ImportTaskModel;
use App\Modules\Material\Models\MaterialAssetsModel;
use App\Library\Enums\MaterialClassifyEnums;
use App\Library\Enums\MaterialEnums;
use App\Library\Enums;
use App\Modules\Material\Models\MaterialAssetTransferBatchModel;
use App\Modules\Material\Models\MaterialUpdateLogModel;
use App\Modules\Payment\Services\StoreRentingAddService;
use App\Modules\Purchase\Services\BaseService as PurchaseService;
use App\Modules\User\Services\StaffService;
use App\Modules\Material\Models\MaterialAssetUpdateLogModel;
use App\Modules\Material\Models\MaterialAssetTransferLogModel;
use App\Modules\Material\Models\MaterialAssetOutStorageModel;
use App\Library\Enums\MaterialAssetOutStorageEnums;
use App\Modules\Material\Services\ScmService;
use App\Modules\User\Services\UserService;
use App\Modules\Material\Services\AssetAccountService;
use App\Library\BaseController;
use App\Library\ErrCode;
use App\Library\OssHelper;
use App\Library\Enums\StaffInfoEnums;
use App\Models\oa\MaterialAssetScmCallbackLogModel;
use App\Modules\Common\Services\EnumsService;
use App\Repository\DepartmentRepository;
use App\Models\oa\MaterialAssetReturnStorageModel;
use App\Library\Validation\ValidationException;
use App\Models\backyard\HrStaffInfoModel;

/**
 * 资产台账相关任务
 * Class MaterialAssetTask
 */
class MaterialAssetTask extends BaseTask
{
    /**
     * 同步资产台账使用人信息
     */
    public function sync_staffAction()
    {
        $log = '';
        try {
            echo date('Ymd H:i:s')." 脚本开始执行".PHP_EOL;
            //第一步获取满足条件的资产台账总记录数
            $count = $this->getSyncStaffAssetCount();
            if ($count > 0) {
                //查询coo/ceo下的公司 查询逻辑和资产台账编辑时保持一致
                //AssetAccountService::getInstance()->getCompanyByDepartment();
                $company_list = (new PurchaseService())->getCooCostCompany();
                $cost_company_kv = array_column($company_list, 'cost_company_name', 'cost_company_id');
                //实例化,预备查询公司信息
                $department_repository = new DepartmentRepository();
                $company_data_pool = [];
                //分页查询处理
                $page_size = 2000;
                $page = ceil($count / $page_size);
                for ($i = 1; $i <= $page; $i++) {
                    $list = $this->getSyncStaffAssetList($page_size, $i);
                    if (empty($list)) {
                        break;
                    }
                    //员工信息组
                    $staff_info_arr = [];
                    //获取满足条件的员工工号
                    $staff_id_arr = array_values(array_unique(array_column($list,"staff_id")));
                    $staff_list = StaffService::getInstance()->searchStaff(['staff_id'=>$staff_id_arr, 'limit'=>count($staff_id_arr)]);
                    if (!empty($staff_list['data'])) {
                        $staff_info_arr = array_column($staff_list['data'], null, 'staff_id');
                    }
                    //查询到了员工信息
                    if (!empty($staff_info_arr)) {
                        //使用用的修改数据
                        $transfer_arr = $update_log_arr = $change_asset_arr = [];
                        //非使用中的修改数据
                        $not_using_change_asset_arr = [];
                        //使用中台账修改数量
                        $change_count = 0;
                        //非使用中台账修改数量
                        $not_using_change_count = 0;
                        //成本中心组
                        $pc_code_arr = [];

                        //分成 使用中数据和非使用中的数据 改的逻辑不同
                        $list_using = [];
                        $list_not_using = [];
                        foreach ($list as $value) {
                            if (in_array($value['status'], [MaterialEnums::ASSET_STATUS_USING, MaterialEnums::ASSET_STATUS_UNUSED, MaterialEnums::ASSET_STATUS_REPAIRED, MaterialEnums::ASSET_STATUS_REPAIRING, MaterialEnums::ASSET_STATUS_TO_BE_SCRAPPED, MaterialEnums::ASSET_STATUS_LOST])) {
                                //使用中、闲置、已报修、维修中、待报废、已丢失
                                $list_using[] = $value;
                            } elseif ($value['status'] != MaterialEnums::ASSET_STATUS_DRAFT) {
                                //调拨中、已处置、已报废、出库中、出库拒绝、退回中、入库中
                                $list_not_using[] = $value;
                            }
                        }
                        //获取 员工协议公司信息
                        $staffData = [];
                        if (!empty($list_using)) {
                            $fromStaff = array_column($list_using, 'staff_id');
                            $toStaff   = array_column($staff_info_arr, 'staff_id');
                            $allStaff  = array_values(array_unique(array_merge($fromStaff, $toStaff)));
                            //查询员工协议公司
                            $staffData = HrStaffInfoModel::find([
                                'columns'    => 'staff_info_id, contract_company_id',
                                'conditions' => 'staff_info_id in ({ids:array})',
                                'bind'       => ['ids' => $allStaff],
                            ])->toArray();
                            $staffData = empty($staffData) ? [] : array_column($staffData, 'contract_company_id', 'staff_info_id');
                        }
                        //获取公司名称
                        $companyList = EnumsService::getInstance()->getPayrollCompanyInfo();

                        //使用中台账处理
                        foreach ($list_using as $item) {
                            if (!isset($staff_info_arr[$item['staff_id']])) {
                                //未找到员工工号意味着没查询到满足条件的，不做处理
                                continue;
                            }
                            $staff_info = $staff_info_arr[$item['staff_id']];
                            //需要修改费用公司的条件
                            $update_company_condition = $item['company_id'] != $staff_info['company_id'] && key_exists($staff_info['company_id'], $cost_company_kv);
                            //查询$item['company_id']的公司名称赋值
                            if (!isset($company_data_pool[$item['company_id']])) {
                                $company_info = $department_repository->getDepartmentDetail($item['company_id']);
                                $company_data_pool[$item['company_id']] = $company_info['name'] ?? '';
                            }
                            //不需要修改费用公司,但公司名称需要刷新
                            $need_update_company_name = !$update_company_condition && $company_data_pool[$item['company_id']] != $item['company_name'];
                            //满足下列任何一个条件,则更新
                            if ($item['staff_name'] != $staff_info['staff_name'] || $item['state'] != $staff_info['state'] || $item['wait_leave_state'] != $staff_info['wait_leave_state'] || $item['leave_date'] != $staff_info['leave_date']
                                || $item['node_department_id'] != $staff_info['node_department_id'] || $item['node_department_name'] != $staff_info['node_department_name']
                                || $item['sys_store_id'] != $staff_info['sys_store_id'] || $item['store_name'] != $staff_info['store_name']
                                || $item['job_id'] != $staff_info['job_id'] || $item['job_name'] != $staff_info['job_name'] || $update_company_condition || $need_update_company_name) {
                                //员工基础信息发生了变更
                                $change_asset_arr[$item['staff_id']][] = $item['id'];
                                $change_count ++;
                                //如果公司不需要变更,需判断
                                //1. 员工最新公司id == 台账公司id, 使用员工最新公司名称更新台账
                                //2. 员工最新公司id != 台账公司id, 使用台账公司id查询公司名称更新台账
                                if (!$update_company_condition) {
                                    $staff_info_arr[$item['staff_id']]['company_id'] = $item['company_id'];
                                    $staff_info_arr[$item['staff_id']]['company_name'] = $company_data_pool[$item['company_id']] ?? '';
                                    $staff_info['company_id'] = $staff_info_arr[$item['staff_id']]['company_id'];
                                    $staff_info['company_name'] = $staff_info_arr[$item['staff_id']]['company_name'];
                                }
                            }
                            $logData = [];
                            //员工更换了所属网点非总部 -记录操作日志
                            if ($item['sys_store_id'] != $staff_info['sys_store_id'] && $staff_info['sys_store_id'] != Enums::HEAD_OFFICE_STORE_FLAG) {
                                $logData[] = [
                                    'before' => $item['sys_store_id'],
                                    'after' => $staff_info['sys_store_id'],
                                    'field_name' => 'sys_store_id'
                                ];
                            }
                            if ($item['node_department_id'] != $staff_info['node_department_id']) {
                                //员工更换了部门-记录操作日志
                                $logData[] = [
                                    'before' => $item['node_department_id'],
                                    'after' => $staff_info['node_department_id'],
                                    'field_name' => 'node_department_id'
                                ];
                            }
                            if ($logData) {
                                //涉及到网点或者部门变更，需要查询新的pc_code
                                if ($staff_info['sys_store_id'] == Enums::HEAD_OFFICE_STORE_FLAG) {
                                    //总部
                                    $pc_id = $staff_info['node_department_id'];
                                    $type = 1;
                                } else {
                                    //网点
                                    $pc_id = $staff_info['sys_store_id'];
                                    $type = 2;
                                }
                                //不存在则查找
                                if (!isset($pc_code_arr[$pc_id])) {
                                    $pc_code_data = StoreRentingAddService::getInstance()->getPcCode($pc_id, $type);
                                    $pc_code = !empty($pc_code_data['data']) ? $pc_code_data['data']['pc_code'] : '';
                                    $pc_code_arr[$pc_id] = $pc_code;
                                    $staff_info_arr[$item['staff_id']]['pc_code'] = $staff_info['pc_code'] = $pc_code;
                                } else {
                                    $staff_info_arr[$item['staff_id']]['pc_code'] = $staff_info['pc_code'] = $pc_code_arr[$pc_id];
                                }
                                if ($item['pc_code'] != $staff_info['pc_code']) {
                                    //记录成本中心
                                    $logData[] = [
                                        'before' => $item['pc_code'],
                                        'after' => $staff_info['pc_code'],
                                        'field_name' => 'pc_code'
                                    ];
                                }
                                [$transfer, $update_log] = $this->saveTransfer($item, $staff_info, $logData);
                                //新增协议签署公司字段
                                $transfer['from_contract_company_id']   = $staffData[$transfer['from_staff_id']] ?? 0;
                                $transfer['from_contract_company_name'] = $companyList[$staffData[$transfer['from_staff_id']] ?? 0] ?? '';
                                $transfer['to_contract_company_id']     = $staffData[$transfer['to_staff_id']] ?? 0;
                                $transfer['to_contract_company_name']   = $companyList[$staffData[$transfer['to_staff_id']] ?? 0] ?? '';
                                $transfer_arr[] = $transfer;
                                $update_log_arr[] = $update_log;
                            }
                        }

                        //非使用中的修改
                        foreach ($list_not_using as $not_using_item) {
                            if (!isset($staff_info_arr[$not_using_item['staff_id']])) {
                                //未找到员工工号意味着没查询到满足条件的，不做处理
                                continue;
                            }
                            //如果公司名称变了, 就刷新公司名称
                            //查询$item['company_id']的公司名称赋值
                            if (!isset($company_data_pool[$not_using_item['company_id']])) {
                                $company_info = $department_repository->getDepartmentDetail($not_using_item['company_id']);
                                $company_data_pool[$not_using_item['company_id']] = $company_info['name'] ?? '';
                            }
                            //员工名称不同直接改成最新的
                            $one_staff_info = $staff_info_arr[$not_using_item['staff_id']];
                            if ($not_using_item['staff_name'] != $one_staff_info['staff_name'] || $not_using_item['company_name'] != $company_data_pool[$not_using_item['company_id']] || $not_using_item['job_id'] != $one_staff_info['job_id'] || $not_using_item['job_name'] != $one_staff_info['job_name']) {
                                $not_using_change_asset_arr[$not_using_item['staff_id']]['asset_id'][] = $not_using_item['id'];
                                $not_using_change_asset_arr[$not_using_item['staff_id']]['staff_name'] = $one_staff_info['staff_name'];
                                $not_using_change_asset_arr[$not_using_item['staff_id']]['job_id'] = $one_staff_info['job_id'];
                                $not_using_change_asset_arr[$not_using_item['staff_id']]['job_name'] = $one_staff_info['job_name'];
                                $not_using_change_asset_arr[$not_using_item['staff_id']]['company_name'] = $company_data_pool[$not_using_item['company_id']];
                                $not_using_change_count++;
                            }
                        }

                        $this->saveData($staff_info_arr, $change_asset_arr, $transfer_arr, $update_log_arr, $not_using_change_asset_arr);
                        echo '第' . $i . '页查询出来使用中的台账' . count($list_using) . '条,需要处理' . $change_count . '条; 查询出来非使用中的台账' . count($list_not_using) . '条,需要处理' . $not_using_change_count . '条，数据全已处理完毕' . PHP_EOL;
                        $log .= '第' . $i . '页查询出来使用中的台账' . count($list_using) . '条,需要处理' . $change_count . '条; 查询出来非使用中的台账' . count($list_not_using) . '条,需要处理' . $not_using_change_count . '条，数据全已处理完毕;';
                    } else {
                        echo '第'.$i.'页未查到满足条件的员工信息组'.PHP_EOL;
                        $log.='第'.$i.'页未查到满足条件的员工信息组;';
                    }
                }
            } else {
                $log = date('Ymd H:i:s').' 暂无资产台账需要同步使用人信息';
                echo $log.PHP_EOL;
            }
            $this->logger->info($log);
        } catch (\Exception $e) {
            $log = date('Ymd H:i:s') . ' 同步资产台账使用人信息失败[异常]' . $e->getMessage() . PHP_EOL;
            echo $log;
            $this->logger->warning($log);
        }
        echo date('Ymd H:i:s'). " 脚本执行结束". PHP_EOL;
    }

    /**
     * 获取查询条件
     * @param object $builder 查询器
     * @return mixed
     */
    private function getCondition($builder)
    {
        $builder->where('is_deleted = :is_deleted:', ['is_deleted' => MaterialClassifyEnums::IS_DELETED_NO]);
        $builder->andWhere('staff_id > :staff_id:', ['staff_id'=>0]);
        $builder->andWhere('status in ({status:array})', ['status' => [MaterialEnums::ASSET_STATUS_USING, MaterialEnums::ASSET_STATUS_UNUSED,
            MaterialEnums::ASSET_STATUS_ALLOT, MaterialEnums::ASSET_STATUS_REPAIRED, MaterialEnums::ASSET_STATUS_REPAIRING, MaterialEnums::ASSET_STATUS_TO_BE_SCRAPPED,
            MaterialEnums::ASSET_STATUS_LOST, MaterialEnums::ASSET_STATUS_OUT_STORAGE, MaterialEnums::ASSET_STATUS_OUT_STORAGE_REJECT]]);
        return $builder;
    }

    /**
     * 获取满足条件的资产台账总记录数
     * @return int
     */
    private function getSyncStaffAssetCount()
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(id) as total');
        $builder->from(['main' => MaterialAssetsModel::class]);
        $builder = $this->getCondition($builder);
        $data = $builder->getQuery()->getSingleResult();
        return intval($data->total);
    }

    /**
     * 获取满足条件的资产台账记录
     * @param integer $page_size 每页条数
     * @param integer $page 当前页码
     * @return mixed
     */
    public function getSyncStaffAssetList($page_size, $page)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['main' => MaterialAssetsModel::class]);
        $builder->columns('id, bar_code,asset_code, staff_id, staff_name, state, wait_leave_state,leave_date,node_department_id,node_department_name,sys_store_id,store_name,job_id,job_name,pc_code,use_land,company_id,company_name,status');
        $builder = $this->getCondition($builder);
        $offset = $page_size * ($page - 1);
        $builder->limit($page_size, $offset);
        $builder->orderBy('id desc');
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 组装更新的员工信息
     * @param array $staff_info 员工信息
     * @return array
     */
    public function saveStaffInfo($staff_info)
    {
        $update_staff_info = [
            'staff_name' => $staff_info['staff_name'],
            'state' => $staff_info['state'],
            'wait_leave_state' => $staff_info['wait_leave_state'],
            'leave_date' => $staff_info['leave_date'],
            'node_department_id' => $staff_info['node_department_id'],
            'node_department_name' => $staff_info['node_department_name'],
            'sys_store_id' => $staff_info['sys_store_id'],
            'store_name' => $staff_info['store_name'],
            'job_id' => $staff_info['job_id'] ?? 0,
            'job_name' => $staff_info['job_name'] ?? '',
            'company_id' => $staff_info['company_id'],
            'company_name' => $staff_info['company_name'],
        ];
        //V19094,员工使用网点为总部，无需变更台账上使用网点、使用地址信息
        if ($staff_info['sys_store_id'] == Enums::HEAD_OFFICE_STORE_FLAG) {
            unset($update_staff_info['sys_store_id'], $update_staff_info['store_name']);
        }
        if (isset($staff_info['pc_code'])) {
            $update_staff_info['pc_code'] = $staff_info['pc_code'];
        }
        return $update_staff_info;
    }

    /**
     * 组装转移记录以及操作记录
     * @param array $asset 原资产信息
     * @param array $staff_info 新使用人信息
     * @param array $logData 操作日志
     * @return array
     */
    public function saveTransfer($asset, $staff_info, $logData)
    {
        $now_time = date('Y-m-d H:i:s');
        //存在变更，记录转移信息
        $transfer = [
            'asset_id' => $asset['id'],
            'barcode' => $asset['bar_code'],
            'asset_code' => $asset['asset_code'],
            'from_staff_id' => $asset['staff_id'],
            'from_staff_name' => $asset['staff_name'],
            'from_node_department_id' => $asset['node_department_id'],
            'from_node_department_name' => $asset['node_department_name'],
            'from_sys_store_id' => $asset['sys_store_id'],
            'from_store_name' => $asset['store_name'],
            'from_pc_code' => $asset['pc_code'],
            'from_use_land' => $asset['use_land'],
            'from_company_id' => $asset['company_id'],
            'from_company_name' => $asset['company_name'],
            'to_staff_id' => $staff_info['staff_id'],
            'to_staff_name' => $staff_info['staff_name'],
            'to_node_department_id' => $staff_info['node_department_id'],
            'to_node_department_name' => $staff_info['node_department_name'],
            'to_sys_store_id' => $staff_info['sys_store_id'],
            'to_store_name' => $staff_info['store_name'],
            'to_pc_code' => $staff_info['pc_code'],
            'to_use_land' => '',
            'to_company_id' => $staff_info['company_id'],
            'to_company_name' => $staff_info['company_name'],
            'status' => MaterialEnums::TRANSFER_LOG_STATUS_RECEIVED,
            'transfer_type' => MaterialEnums::TRANSFER_TYPE_USER_SYNC,
            'operator_id' => $staff_info['staff_id'],
            'transfer_at' => $now_time,
            'finished_at' => $now_time,
            'updated_at' => $now_time,
        ];
        //v19094需求，网点变化了但是是总部，to的网点信息、使用地信息仍为from网点信息、使用地信息
        if ($asset['sys_store_id'] != $staff_info['sys_store_id'] && $staff_info['sys_store_id'] == Enums::HEAD_OFFICE_STORE_FLAG) {
            $transfer['to_sys_store_id'] = $transfer['from_sys_store_id'];
            $transfer['to_store_name'] = $transfer['from_store_name'];
            $transfer['to_use_land'] = $transfer['from_use_land'];
        }
        $update_log = [
            'asset_code' => $asset['asset_code'],
            'staff_id' => StaffInfoEnums::SUPER_ADMIN_STAFF_ID,
            'staff_name' => StaffInfoEnums::SUPER_ADMIN_STAFF_NAME,
            'content' => json_encode($logData, JSON_UNESCAPED_UNICODE),
            'type' => MaterialEnums::OPERATE_TYPE_BATCH_TRANSFER,
            'created_at' => $now_time,
            'updated_at' => $now_time,
        ];
        return [$transfer, $update_log];
    }

    /**
     * 数据库-落库操作-保存数据
     * @param array $staff_info_arr 涉及到的员工信息组
     * @param array $change_asset_arr 需要改变资产使用人信息组
     * @param array $transfer 需要记录的转移记录
     * @param array $update_log 需要记录的操作记录
     * @param array $not_using_change_asset_arr 非使用中资产的使用人信息组
     * @throws Exception
     */
    public function saveData($staff_info_arr, $change_asset_arr, $transfer, $update_log, $not_using_change_asset_arr)
    {
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            //开始操作需要变更员工信息的资产台账数据
            if (!empty($change_asset_arr)) {
                foreach ($change_asset_arr as $staff_id=>$value) {
                    $asset_ids = implode(",",$value);
                    $db->updateAsDict(
                        (new MaterialAssetsModel())->getSource(),
                        $this->saveStaffInfo($staff_info_arr[$staff_id]),
                        [
                            'conditions' => " id IN ($asset_ids)",
                        ]
                    );
                }
            }
            if (!empty($not_using_change_asset_arr)) {
                foreach ($not_using_change_asset_arr as $staff_id => $value) {
                    $asset_ids = implode(',', $value['asset_id']);
                    $db->updateAsDict(
                        (new MaterialAssetsModel())->getSource(),
                        [
                            'staff_name' => $value['staff_name'],
                            'company_name' => $value['company_name'],
                            'job_id' => $value['job_id'],
                            'job_name' => $value['job_name'],
                        ],
                        [
                            'conditions' => " id IN ($asset_ids) ",
                        ]
                    );
                }
            }
            //涉及到部门或者网点变更的要记录转移记录
            if (!empty($transfer)) {
                $now_time = date('Y-m-d H:i:s');
                $transfer_batch = new MaterialAssetTransferBatchModel();
                $bool = $transfer_batch->i_create([
                    'staff_id' => 10000,
                    'staff_name' => 'SuperAdmin',
                    'type' => MaterialEnums::TRANSFER_TYPE_BATCH,
                    'status' =>MaterialEnums::TRANSFER_BATCH_STATUS_RECEIVED,
                    'mark' => '系统脚本针对使用中资产在使用人部门或者网点发生变化做转移操作',
                    'created_at' => $now_time,
                    'updated_at' => $now_time
                ]);
                if ($bool) {
                    foreach ($transfer as &$item) {
                        $item['batch_id'] = $transfer_batch->id;
                        $item['created_at'] = $now_time;
                        $item['updated_at'] = $now_time;
                    }
                    $transfer_log = new MaterialAssetTransferLogModel();
                    $transfer_log->batch_insert($transfer);
                }
            }
            //涉及到部门或者网点变更的要记录操作记录
            if (!empty($update_log)) {
                //记录操作日志
                $log = new MaterialAssetUpdateLogModel();
                $log->batch_insert($update_log);
            }
            $db->commit();
        } catch (\Exception $e) {
            $db->rollback();
            throw $e;
        }
    }

    /**
     * 调取SCM端审核接口，审核资产领用出库单，并更新出库单状态
     * @return bool
     */
    public function sync_scm_audit_asset_out_storageAction() {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $this->check_process($process_name);
        echo 'start: ' . date('Y-m-d H:i:s').PHP_EOL;
        try {
            $i = 1;
            $start_time =date('Y-m-d H:i:s',time()-3600);
            $end_time = date('Y-m-d H:i:s');
            $deal_audit_data =  $this->getAssetOutStorageList($i, $start_time, $end_time);
            while (!empty($deal_audit_data)) {
                echo '第'.$i.'页处理开始' . PHP_EOL;
                $scm = new ScmService();
                foreach ($deal_audit_data as $key => $value) {
                    $asset_out_storage = MaterialAssetOutStorageModel::findFirst([
                        'id = :id:  and is_deleted=:is_deleted:',
                        'bind' => ['id' => $value['id'], 'is_deleted'=>MaterialClassifyEnums::IS_DELETED_NO]
                    ]);
                    if(empty($asset_out_storage)){
                        continue;
                    }
                    $return_data = false;
                    //防止scm那边请求超时
                    $z = 0;
                    while (empty($return_data) && $z < 2) {
                        $z++;
                        $return_data = $scm->auditOutBound($asset_out_storage->mach_code, $asset_out_storage->scm_no);
                        if ($return_data) {
                            $asset_out_storage->status = $return_data;
                            $asset_out_storage->updated_at = date('Y-m-d H:i:s');
                            $asset_out_storage->save();
                            echo 'material_asset_out_storage success id:'. $value['id'].PHP_EOL;
                        }
                        sleep(1);
                    }
                }
                echo '第'.$i.'页处理结束' . PHP_EOL;
                sleep(1);
                $i += 1;
                $deal_audit_data = $this->getAssetOutStorageList($i, $start_time, $end_time);
            }
            if (empty($deal_audit_data)) {
                echo "数据均已处理完毕".PHP_EOL;
            }
            echo 'end: ' . date('Y-m-d H:i:s').PHP_EOL;
        } catch (Exception $e) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('material-asset-out-storage-scm-audit-task-exception: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取需要审核的出库单列表
     * @param int $page_num 当前页码
     * @param string $start_time 开始时间
     * @param string $end_time 结束时间
     * @return mixed
     */
    private function getAssetOutStorageList($page_num, $start_time, $end_time)
    {
        $page_size = 100;
        return MaterialAssetOutStorageModel::find([
            'columns' => 'id, scm_no, status, mach_code',
            'conditions' => 'status = :status: and is_deleted = :is_deleted: and created_at >= :start_time: and created_at <= :end_time:',
            'bind' => ['status'=> MaterialAssetOutStorageEnums::STATUS_WAIT_APPROVE, 'is_deleted' => MaterialClassifyEnums::IS_DELETED_NO, 'start_time' => $start_time, 'end_time' => $end_time],
            'limit' => $page_size,
            'offset' => $page_size * ($page_num - 1),
        ])->toArray();
    }



    /**
     * 资产台账-批量导入-批量报废
     * @Date: 11/18/22 10:33 AM
     * php cli.php material_asset batch_scrap_update
     **/
    public function batch_scrap_updateAction()
    {
        $this->checkLock(__METHOD__);
        $log = '';
        try {
            // 查询当前是否存在待处理的任务  查询最近一次待下载任务
            $scrap_data = ImportTaskModel::findFirst([
                'conditions' => 'type = :type: and status = :status: and is_deleted = :is_deleted:',
                'bind' => [
                    'type' => ImportCenterEnums::TYPE_ASSET_ACCOUNT_SCRAP_UPDATE,
                    'status' => ImportCenterEnums::STATUS_WAITING,
                    'is_deleted' => ImportCenterEnums::NOT_DELETED
                ],
                'order' => 'id asc',
            ]);
            if (!$scrap_data) {
                throw new ValidationException('无待处理的任务', ErrCode::$VALIDATE_ERROR);
            }

            // 存在下载任务,开始执行
            $scrap_data->status = ImportCenterEnums::STATUS_DOING;
            if ($scrap_data->save() === false) {
                throw new Exception('更新为「处理中」失败, 原因可能是=' . get_data_object_error_msg($scrap_data), ErrCode::$SYSTEM_ERROR);
            }

            $tmp_dir = sys_get_temp_dir() . '/';
            $scrap_batch_service = AssetAccountService::getInstance();
            $scrap_batch_service::setLanguage($scrap_data->language ?? 'en');
            $oss_info = $scrap_batch_service->getOssInfoByUrl($scrap_data->file_url);
            $file_name = $oss_info['file_name'];
            ob_start();
            readfile($scrap_data->file_url);
            $file_data = ob_get_contents();
            ob_end_clean();
            file_put_contents($tmp_dir . $file_name, $file_data);
            // 2. 读取文件
            $config = ['path' => $tmp_dir];
            $excel = new \Vtiful\Kernel\Excel($config);

            // 读取上传文件数据
            $excel_data = $excel->openFile($file_name)
                ->openSheet()
                ->setType([
                    0 => \Vtiful\Kernel\Excel::TYPE_STRING,
                    1 => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP,
                    2 => \Vtiful\Kernel\Excel::TYPE_STRING
                ])
                ->getSheetData();

            // 读取每行单元格数据时，可指定每个单元格数据类型进行读取
            if (empty($excel_data)) {
                // 正常情况下进入任务是会校验, 所以这种空文件基本不会出现, 如果出现记录异常
                throw new Exception('file is empty');
            }
            // 3. 组合和controllers一样的user信息
            $user = new UserService();
            $user_obj = $user->getUserById($scrap_data->staff_info_id);
            $base_controller = new BaseController();
            $user_info = $base_controller->format_user($user_obj);
            // 提取Excel Header
            $excel_header_column_one = array_shift($excel_data);
            $excel_header_column_two = array_shift($excel_data);
            $res = $scrap_batch_service->batchUpdateEdit($excel_data, $user_info, true);
            // 4. 生成结果文件
            if ($res['code'] == ErrCode::$SUCCESS) {
                // 处理成功, 生成上传结果文件
                $upload_result_data = $res['data']['excel_data'];
                array_unshift($upload_result_data, $excel_header_column_two);
                $excel_extra_config = [
                    'file_name' => date('YmdHis') . '_' . $user_info['id'] . '_import_scrap_update_result.xlsx',
                    'end_column_char' => 'U',
                    'column_width' => 15,
                ];
                $path = self::customizeExcelToFile($excel_header_column_one, $upload_result_data, $excel_extra_config);
                $oss_result = OssHelper::uploadFile($path);
            }

            // 再查一次数据,长时间处理, 上边的数据库连接会自动关闭
            $after_scrap_update_data = ImportTaskModel::findFirst($scrap_data->id);
            if (!$after_scrap_update_data || $after_scrap_update_data->status != ImportCenterEnums::STATUS_DOING) {
                throw new Exception('result save error, not found task');
            }

            $after_scrap_update_data->success_num = $res['data']['success_num'];
            $after_scrap_update_data->error_num = $res['data']['failed_sum'];
            $after_scrap_update_data->result_file_url = $oss_result['object_url'] ?? '';
            $after_scrap_update_data->status = ImportCenterEnums::STATUS_DONE;
            $after_scrap_update_data->finished_at = date('Y-m-d H:i:s');
            if ($after_scrap_update_data->save() === false) {
                throw new Exception('result save error, message='.get_data_object_error_msg($after_scrap_update_data));
            }
            $log .= 'batch scrap update success' . PHP_EOL;
            $this->logger->info('batch_scrap_update_success: result=' . json_encode($res ?? []));
        } catch (ValidationException $e) {
            echo '任务结果: ' . $e->getMessage() . PHP_EOL;
        } catch (Exception $e) {
            $this->logger->error('batch_scrap_update: message=' . $e->getMessage() . '; trace=' . $e->getTraceAsString());

            // 导入结果置为失败
            $after_scrap_update_data = ImportTaskModel::findFirst($scrap_data->id);
            if (!$after_scrap_update_data || $after_scrap_update_data->status != ImportCenterEnums::STATUS_DOING) {
                echo 'error: result failed save error, not found task' . PHP_EOL;
                $this->clearLock(__METHOD__);
                exit();
            }
            $after_scrap_update_data->status = ImportCenterEnums::STATUS_FAILED;
            $after_scrap_update_data->finished_at = date('Y-m-d H:i:s');
            if ($after_scrap_update_data->save() === false) {
                $this->logger->error('batch_scrap_update_error: ' . get_data_object_error_msg($after_scrap_update_data));
            }

            $log .= 'error:' . $e->getMessage() . PHP_EOL;
        }

        $this->clearLock(__METHOD__);
        exit($log);
    }


    /**
    * 资产台账-批量导入-导入修改财务
    * @Date: 11/18/22 10:33 AM
    * php cli.php material_asset batch_finance_update
    **/
    public function batch_finance_updateAction()
    {
        $this->checkLock(__METHOD__);
        $log = '';
        // 不能开启事务, 时间长数据库连接自动关闭, 事务提交会报错(MySQL server has gone away )
        try {
            $scrap_data = ImportTaskModel::findFirst([
                'conditions' => 'type = :type: and status = :status: and is_deleted = :is_deleted:',
                'bind' => [
                    'type' => ImportCenterEnums::TYPE_ASSET_ACCOUNT_FINANCE_UPDATE,
                    'status' => ImportCenterEnums::STATUS_WAITING,
                    'is_deleted' => ImportCenterEnums::NOT_DELETED
                ],
                'order' => 'id asc',
            ]);
            if (!$scrap_data) {
                throw new ValidationException('没有等待导入的任务', ErrCode::$VALIDATE_ERROR);
            }

            // 存在下载任务,开始执行
            $scrap_data->status = ImportCenterEnums::STATUS_DOING;
            if ($scrap_data->save() === false) {
                throw new Exception('下载任务启动异常: 更新为"处理中"失败 原因是: ' . get_data_object_error_msg($scrap_data), ErrCode::$SYSTEM_ERROR);
            }

            $tmp_dir = sys_get_temp_dir() . '/';
            $scrap_batch_service = AssetAccountService::getInstance();
            $scrap_batch_service::setLanguage($scrap_data->language ?? 'en');
            $oss_info = $scrap_batch_service->getOssInfoByUrl($scrap_data->file_url);
            $file_name = $oss_info['file_name'];
            ob_start();
            readfile($scrap_data->file_url);
            $file_data = ob_get_contents();
            ob_end_clean();
            file_put_contents($tmp_dir . $file_name, $file_data);
            // 2. 读取文件
            $config = ['path' => $tmp_dir];
            $excel = new \Vtiful\Kernel\Excel($config);

            // 读取上传文件数据
            $excel_data = $excel->openFile($file_name)
                ->openSheet()
                ->setType([
                    0 => \Vtiful\Kernel\Excel::TYPE_STRING
                ])
                ->getSheetData();

            // 读取每行单元格数据时，可指定每个单元格数据类型进行读取
            if (empty($excel_data)) {
                // 正常情况下进入任务是会校验, 所以这种空文件基本不会出现, 如果出现记录异常
                throw new Exception('file is empty');
            }
            $user = new UserService();
            $user_obj = $user->getUserById($scrap_data->staff_info_id);
            $base_controller = new BaseController();
            $user_info = $base_controller->format_user($user_obj);

            // 提取Excel Header
            $excel_header_column_one = array_shift($excel_data);
            $excel_header_column_two = array_shift($excel_data);

            $res = $scrap_batch_service->batchUpdateEdit($excel_data, $user_info, false);
            //生成结果文件
            if ($res['code'] == ErrCode::$SUCCESS) {
                // 处理成功, 生成上传结果文件
                $upload_result_data = $res['data']['excel_data'];
                array_unshift($upload_result_data, $excel_header_column_two);
                $excel_extra_config = [
                    'file_name' => date('YmdHis') . '_' . $user_info['id'] . '_import_finance_update_result.xlsx',
                    'end_column_char' => 'U',
                    'column_width' => 15,
                ];
                $path = self::customizeExcelToFile($excel_header_column_one, $upload_result_data, $excel_extra_config);
                $oss_result = OssHelper::uploadFile($path);
            }

            // 再查一次数据,长时间处理, 上边的数据库连接会自动关闭
            $after_scrap_update_data = ImportTaskModel::findFirst($scrap_data->id);
            if (!$after_scrap_update_data || $after_scrap_update_data->status != ImportCenterEnums::STATUS_DOING) {
                throw new Exception('result save error, not found task');
            }

            $after_scrap_update_data->success_num = $res['data']['success_num'];
            $after_scrap_update_data->error_num = $res['data']['failed_sum'];
            $after_scrap_update_data->result_file_url = $oss_result['object_url'] ?? '';
            $after_scrap_update_data->status = ImportCenterEnums::STATUS_DONE;
            $after_scrap_update_data->finished_at = date('Y-m-d H:i:s');
            if ($after_scrap_update_data->save() === false) {
                throw new Exception('result save error, message='.get_data_object_error_msg($after_scrap_update_data));
            }
            $log .= 'batch finance update success' . PHP_EOL;
            $this->logger->info('batch_finance_update_success: result=' . json_encode($res ?? []));
        } catch (ValidationException $e) {
            $log .= '任务结果: ' . $e->getMessage() . PHP_EOL;
        } catch (Exception $e) {
            $this->logger->error('batch_finance_update: message=' . $e->getMessage() . '; trace=' . $e->getTraceAsString());

            // 导入结果置为失败
            $after_scrap_update_data = ImportTaskModel::findFirst($scrap_data->id);
            if (!$after_scrap_update_data || $after_scrap_update_data->status != ImportCenterEnums::STATUS_DOING) {
                echo 'error: result failed save error, not found task' . PHP_EOL;
                $this->clearLock(__METHOD__);
                exit();
            }
            $after_scrap_update_data->status = ImportCenterEnums::STATUS_FAILED;
            $after_scrap_update_data->finished_at = date('Y-m-d H:i:s');
            if ($after_scrap_update_data->save() === false) {
                $this->logger->error('batch_finance_update_error: ' . get_data_object_error_msg($after_scrap_update_data));
            }

            $log .= 'error:' . $e->getMessage() . PHP_EOL;
        }
        $this->clearLock(__METHOD__);
        exit($log);
    }


    /**
     * scm修改sn失败发送邮件给资产部
     * 每天发送前一天的数据
     * */

    public function send_asset_emailAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $this->check_process($process_name);
        echo 'begin: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $start_time = date('Y-m-d 00:00:00', strtotime('-1 day'));
        $end_time   = date('Y-m-d 00:00:00');

        try {
            //设置语种
            $country_code = get_country_code();
            self::setLanguage($country_code);
            $data = MaterialAssetScmCallbackLogModel::find([
                'conditions' => 'created_at >=:start_time: and created_at <:end_time:',
                'bind'       => ['start_time' => $start_time, 'end_time' => $end_time]
            ])->toArray();

            $title = '{' . $country_code . '}SCM修改SN同步到OA失败数据 the OA  error data of SCM update SN';
            if ($data) {
                $email = EnumsService::getInstance()->getSettingEnvValueIds('asset_department_email');
                $html  = $this->format_html($data);
                $bool  = $this->mailer->sendAsync($email, $title, $html);
                $log   = '数据：' . json_encode($data);
                if ($bool) {
                    $log .= '发送成功';
                    echo $log;

                } else {
                    $log .= '发送失败';
                    echo $log;
                }

            }


        } catch (Exception $e) {
            $this->logger->error('send_asset_email_task_exception:' . $e->getMessage());
        }
        if (!empty($log)) {
            $this->logger->info($log);
        }
        echo 'end: ' . date('Y-m-d H:i:s') . PHP_EOL;

        exit();

    }

    //整理 sap 订单提醒邮件内容
    public function format_html($data)
    {
        $country_code = get_country_code();
        $date         = date('Y-m-d', strtotime('-1 day'));
        $html         = "<p>你好，附件是{{$country_code}}（{$date}）   SCM修改SN，回传OA的错误数据。详细数据请看附件</p>";
        $html         .= "<p>Hello, the attachment is {{$country_code}}（{$date}）SCM revises the SN and returns wrong data of OA. Please see the attachment for detailed data.</p></br></br>";
        if ($country_code == GlobalEnums::TH_COUNTRY_CODE) {
            $html .= "<p>สวัสดีค่ะ ในไฟล์แนบคือ {{$country_code}}（{$date}） เมื่อแก้รหัสSNในระบบSCM ระบบส่งข้อมูลที่ไม่ถูกต้องกลับมาที่ระบบOA รายละเอียดเพิ่มเติมสามารถดูได้ในไฟล์แนบ</p></br></br>";
        }

        $html .= "</table>";

        $html .= AssetAccountService::getInstance()->exportFailSn($data);
        return $html;

    }


    /**
     * 处理批量作废日志转移
     * @param int $param 一次处理的条数
     * @Date: 11/11/23 4:51 PM
     *  php app/cli.php material_asset cancel_log_transfer eg 参数1 一次处理的条数
     **/
    public function cancel_log_transferAction($param)
    {
        $count = $param[0] ?? 0;
        $this->logTransfer('cancel', $count);

    }

    /**
     * 处理批量修改财务日志转移
     * @param int $param 一次处理的条数
     *  php app/cli.php material_asset finance_log_transfer  eg 参数1 一次处理的条数
     **/
    public function finance_log_transferAction($param)
    {
        $count = $param[0] ?? 0;
        $this->logTransfer('finance', $count);
    }


    /**
     * 处理批量作废日志转移
     * @Date: 11/11/23 4:51 PM
     * @param string $log_type 日志分类 cancel ，finance
     * @param int $count 本次执行处理条数
     **/
    public function logTransfer(string $log_type, $count)
    {
        $str          = '1970-01-01';
        $page_size    = 1000;//分批处理 一次1000
        $max_count    = 50000;//默认最大处理条数
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log          = '任务名称: ' . $process_name . PHP_EOL;
        $log          .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log          .= '初始内存: ' . memory_usage() . PHP_EOL;
        $is_exception = false;

        if ($count > 0) {
            $all_total = $count > $max_count ? $max_count : $count;
        } else {
            // 获取数据总量
            $all_total = $this->materialUpdateLogCount($log_type);
        }

        $total_page_num = ceil($all_total / $page_size);
        $log            .= '预计总量: ' . $all_total . PHP_EOL;
        $log            .= '预计批次: ' . $total_page_num . PHP_EOL;

        //分批取数
        $page_num = 1;
        for ($page_num; $page_num <= $total_page_num; $page_num++) {
            $log  .= '----- 取数批次: ' . $page_num . '-----' . PHP_EOL;
            $list = $this->getList($log_type, $page_size);
            if (empty($list)) {
                $log  .= '----- 批次无数据-----' . PHP_EOL;
                break;
            }
            //合并数据
            $id_arr = $add_data = [];
            $ids    = '';
            foreach ($list as $item) {
                $item['type'] = $log_type == 'cancel' ? MaterialEnums::OPERATE_TYPE_BATCH_CANCEL : MaterialEnums::OPERATE_TYPE_IMPORT_FINANCE;
                if (strpos($item['content'], $str)) {
                    $item['content'] = str_replace($str, $item['scrap_date'], $item['content']);
                }
                $id_arr[] = $item['id'];
                unset($item['scrap_date']);
                unset($item['id']);
                $item['updated_at'] = '2023-11-10 23:59:59';
                $add_data[]         = $item;
            }
            $db = $this->getDI()->get('db_oa');
            $db->begin();
            try {
                if (!empty($add_data)) {
                    $material_asset_update_log_model = new MaterialAssetUpdateLogModel();
                    $batch_insert_task_result        = $material_asset_update_log_model->batch_insert($add_data);
                    if ($batch_insert_task_result === false) {
                        throw new Exception('批量更新失败');
                    }
                    $ids        = implode(',', $id_arr);
                    $update_log = $db->updateAsDict(
                        (new MaterialUpdateLogModel)->getSource(),
                        [
                            'is_deleted' => GlobalEnums::IS_DELETED
                        ],
                        ['conditions' => " id IN ($ids)",]
                    );
                    if (!$update_log) {
                        throw new Exception('批量更新失败');
                    }
                }
                $db->commit();
            } catch (Exception $e) {
                $db->rollback();
                $log          .= $e->getMessage() . PHP_EOL;
                $is_exception = true;
            }
            $log .= '本批执行数量是: ' . count($id_arr) . '条,数据id是:' . $ids . PHP_EOL;
            unset($add_data);
            unset($list);
            $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        }


        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        if ($is_exception) {
            $this->logger->error('处理批量作废日志转移: ' . $log);
        } else {
            $this->logger->info('处理批量作废日志转移 成功: ' . $log);
        }

        exit($log);
    }

    /**
     * 查询标准型号数据操作记录符合条件的条数
     * @param string $log_type
     **/
    public function materialUpdateLogCount($log_type)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(main.id) as total');
        $builder->from(['main' => MaterialUpdateLogModel::class]);
        $builder->leftjoin(MaterialAssetsModel::class, 'main.operate_id = ma.id', 'ma');
        if ($log_type == 'cancel') {
            $builder->andWhere('main.content like :content:', ['content' => '%scrap_date%']);
        } else {
            $builder->andWhere('main.content like :purchase_price: or main.content like :net_value: or main.content like :pc_code: or main.content like :company_name: or main.content like :currency:',
                [
                    'purchase_price' => '%purchase_price%',
                    'net_value'      => '%net_value%',
                    'pc_code'        => '%pc_code%',
                    'company_name'   => '%company_name%',
                    'currency'       => '%currency%',
                ]);
        }
        $builder->andWhere('main.type = :type: and ma.id > :id: and main.is_deleted = :is_deleted:', ['type' => 2, 'id' => 0, 'is_deleted' => GlobalEnums::IS_NO_DELETED]);
        return intval($builder->getQuery()->getSingleResult()->total);
    }

    /**
     * 标准型号数据操作记录符合条件的数据
     * @param string $log_type
     * @param int $page_size
     **/
    public function getList(string $log_type, $page_size)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('main.id, ma.asset_code, main.staff_id,main.staff_name,main.content,main.created_at,main.is_deleted,main.created_at,main.updated_at,ma.scrap_date');
        $builder->from(['main' => MaterialUpdateLogModel::class]);
        $builder->leftjoin(MaterialAssetsModel::class, 'main.operate_id = ma.id', 'ma');
        if ($log_type == 'cancel') {
            $builder->andWhere('main.content like :content:', ['content' => '%scrap_date%']);

        } else {
            $builder->andWhere('main.content like :purchase_price: or main.content like :net_value: or main.content like :pc_code: or main.content like :company_name: or main.content like :currency:',
                [
                    'purchase_price' => '%purchase_price%',
                    'net_value'      => '%net_value%',
                    'pc_code'        => '%pc_code%',
                    'company_name'   => '%company_name%',
                    'currency'       => '%currency%',
                ]);
        }
        $builder->andWhere('main.type = :type: and main.is_deleted = :is_deleted: and ma.id > :id:', ['type' => 2, 'is_deleted' => GlobalEnums::IS_NO_DELETED, 'id' => 0]);
        $builder->limit($page_size);
        $data = $builder->getQuery()->execute()->toArray();

        return $data;
    }

    /**
     * 批量导出台账协议
     * @param $params
     */
    public function batch_export_protocolAction($params)
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $this->check_process($process_name);
        $log = 'begin: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $pdf_list = [];
        try {
            $type = $params[0] ?? MaterialEnums::TRANSFER_EXPORT_PDF_TYPE_APPLY;
            $lang = $params[1] ?? 'zh-CN';
            self::setLanguage($lang);
                $staff_ids = EnumsService::getInstance()->getSettingEnvValueIds('material_asset_batch_export_protocol_staff_ids');
            if ($staff_ids) {
                foreach ($staff_ids as $staff_id) {
                    $res = AssetAccountService::getInstance()->exportProtocol([
                        'type' => $type,
                        'staff_id' => trim($staff_id)
                    ]);
                    $pdf_list[] = [
                        trim($staff_id),
                        $res['code'] == ErrCode::$SUCCESS ? $res['data'] : $res['message']
                    ];
                }
                $export_data = AssetAccountService::getInstance()->exportExcel([
                    '员工工号',
                    'pdf地址'
                ], $pdf_list);
                $log .= 'batch_export_protocol ' . ($export_data['data'] ?? '') . PHP_EOL;
                $log .= '全部处理完毕' . PHP_EOL;
            } else {
                $log .= '未设置导出人工号，无需处理' . PHP_EOL;
            }
        } catch (Exception $e) {
            $this->logger->error('batch_export_protocol_task_exception:' . $e->getMessage());
        }
        $log .= 'end: ' . date('Y-m-d H:i:s') . PHP_EOL;
        exit($log);
    }

    /**
     * SCM退库单审核脚本 每30分钟执行一次
     * php app/cli.php material_asset sync_scm_audit_asset_return_in_storage
     */
    public function sync_scm_audit_asset_return_in_storageAction()
    {
        $this->checkLock(__METHOD__, 1800);
        $exception = false;
        $log = 'start: ' . date('Y-m-d H:i:s') . PHP_EOL;
        try {
            $i = 1;
            $start_time = date('Y-m-d H:i:s', time() - 3600);
            $end_time = date('Y-m-d H:i:s');
            $deal_audit_data =  $this->getAssetReturnStorageList($i, $start_time, $end_time);
            while (!empty($deal_audit_data)) {
                $log .= '第' . $i . '页处理开始' . PHP_EOL;
                $scm = new ScmService();
                foreach ($deal_audit_data as $key => $value) {
                    $return_storage = MaterialAssetReturnStorageModel::findFirst([
                        'id = :id:  and is_deleted = :is_deleted:',
                        'bind' => ['id' => $value['id'], 'is_deleted'=> GlobalEnums::IS_NO_DELETED]
                    ]);
                    if(empty($return_storage)) {
                        continue;
                    }
                    $return_data = false;
                    //防止scm那边请求超时
                    $z = 0;
                    while (empty($return_data) && $z < 2) {
                        $z++;
                        $return_data = $scm->auditInBound($return_storage->mach_code, ['orderSn' => $return_storage->scm_no]);
                        if ($return_data) {
                            $return_storage->status = MaterialEnums::RETURN_STORAGE_STATUS_AUDIT;
                            $return_storage->updated_at = date('Y-m-d H:i:s');
                            $return_storage->save();
                            $log .=  'material_asset_return_storage success scm_no: ' . $value['scm_no'] . PHP_EOL;
                        }
                        sleep(1);
                    }
                }
                $log .= '第' . $i . '页处理结束' . PHP_EOL;
                sleep(1);
                $i += 1;
                $deal_audit_data = $this->getAssetReturnStorageList($i, $start_time, $end_time);
            }
            if (empty($deal_audit_data)) {
                $log .= '数据均已处理完毕' . PHP_EOL;
            }
            $log .= 'end: ' . date('Y-m-d H:i:s') . PHP_EOL;
        } catch (Exception $e) {
            $exception = true;
            $log .= 'material-asset-return-in-storage-scm-audit-task-exception:' . $e->getMessage() . $e->getTraceAsString();
        }
        if ($exception) {
            $this->logger->warning($log);
        } else {
            $this->logger->info($log);
        }
        $this->clearLock(__METHOD__);
        exit($log);
    }

    /**
     * 获取需要审核的入库单列表
     * @param int $page_num 当前页码
     * @param string $start_time 开始时间
     * @param string $end_time 结束时间
     * @return mixed
     */
    private function getAssetReturnStorageList($page_num, $start_time, $end_time)
    {
        $page_size = 100;
        return MaterialAssetReturnStorageModel::find([
            'columns' => 'id, scm_no, status, mach_code',
            'conditions' => 'status = :status: and is_deleted = :is_deleted: and created_at >= :start_time: and created_at <= :end_time:',
            'bind' => ['status'=> MaterialEnums::RETURN_STORAGE_STATUS_WAIT, 'is_deleted' => GlobalEnums::IS_NO_DELETED, 'start_time' => $start_time, 'end_time' => $end_time],
            'limit' => $page_size,
            'offset' => $page_size * ($page_num - 1),
        ])->toArray();
    }

}
