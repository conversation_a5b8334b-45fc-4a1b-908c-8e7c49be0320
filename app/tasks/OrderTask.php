<?php

use App\Modules\Purchase\Services\OrderService;
use App\Modules\Common\Services\EnumsService;
use App\Library\Validation\ValidationException;
use App\Library\ErrCode;


class OrderTask extends BaseTask{

    /**
     * 给指定邮箱地址发送采购订单的全量导出数据
     */
    public function send_emailAction()
    {
        $log = __FUNCTION__ . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        $logger_type = 'info';
        try {
            //中文
            OrderService::setLanguage('zh-CN');
            $item = OrderService::getInstance()->export([], [], OrderService::LIST_TYPE_DATA);
            if (empty($item) || $item['code'] == 0) {
                throw new ValidationException('get export file error:' . json_encode($item, JSON_UNESCAPED_UNICODE), ErrCode::$VALIDATE_ERROR);
            }

            $email_arr = EnumsService::getInstance()->getSettingEnvValueIds('purchase_order_email');
            $log .= 'email address list:' . json_encode($email_arr, JSON_UNESCAPED_UNICODE) . PHP_EOL;
            if (empty($email_arr)) {
                throw new ValidationException('email address null', ErrCode::$VALIDATE_ERROR);
            }

            $date = date('Y-m-d', strtotime('-1 days'));
            $title = $date . ' 采购订单详情 (' . get_country_code() . ' - OA)';
            $html = <<<EOF
    <p>hi all,</p>
    <p style="margin-left: 16px">附件为截止到{$date} 24:00采购订单详情，请<a href="{$item['data']}">点击下载</a>。</p>
EOF;

            $flag = $this->mailer->sendAsync($email_arr, $title, $html);
            if ($flag) {
                $log .= 'purchase order send email success' . PHP_EOL;
            } else {
                throw new Exception('purchase order send email error', ErrCode::$VALIDATE_ERROR);
            }
        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;
        } catch (Exception $e) {
            $logger_type = 'error';
            $log .= $e->getMessage() . PHP_EOL;
        }

        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $this->logger->$logger_type($log);
        exit($log);
    }

    public function flush_approve_timeAction(){
        $list = \App\Modules\Purchase\Models\PurchaseOrder::find(
            [
                'conditions'=>' status = '.\App\Library\Enums::CONTRACT_STATUS_APPROVAL
            ]
        );

        foreach ($list as $item){
            $request = \App\Modules\Workflow\Models\WorkflowRequestModel::findFirst(
                [
                    'conditions'=>'biz_type='.\App\Library\Enums::WF_PURCHASE_ORDER.' and biz_value='.$item->id
                ]
            );

            if(!empty($request)){
                $item->approve_at = $request->approved_at;
                $item->save();
            }
        }
    }

}