<?php

use App\Library\Enums\ImportCenterEnums;
use App\Library\Enums\MaterialClassifyEnums;
use App\Library\Enums\MaterialWmsEnums;
use App\Models\oa\ImportTaskModel;
use App\Models\oa\MaterialWmsApplyModel;
use App\Models\oa\MaterialWmsApplyProductModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Material\Models\MaterialSauModel;
use App\Models\oa\MaterialSauStorageModel;
use App\Models\oa\MaterialWmsOutStorageModel;
use App\Models\oa\MaterialWmsOutStorageProductModel;
use App\Models\oa\MaterialWmsOutStorageBoxModel;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums;
use App\Library\Enums\MaterialSettingEnums;
use App\Modules\Material\Services\MaterialSettingService;
use App\Modules\Payment\Services\StoreRentingAddService;
use App\Modules\User\Services\StaffService;
use App\Modules\Material\Services\ScmService;
use App\Library\ErrCode;
use App\Library\BaseService;
use App\Util\RedisKey;
use App\Models\backyard\SysStoreModel;
use App\Modules\Shop\Models\HeadquartersAddressModel;
use App\Modules\Material\Models\MaterialCategoryModel;
use App\Modules\Material\Models\MaterialFinanceCategoryModel;
use App\Modules\Material\Services\WmsOutStorageService;
use App\Modules\Material\Services\WmsApplyService;
use App\Modules\Material\Services\WarehouseDivisionRuleService;
use App\Modules\Material\Services\AssetOutStorageService;
use App\Library\Validation\ValidationException;
use App\Library\Exception\BusinessException;
use App\Library\OssHelper;

/**
 * 耗材相关任务
 * Class MaterialWmsTask
 */
class MaterialWmsTask extends BaseTask
{

    /**
     * 自动拆单
     * php cli.php  material_wms auto_split_wms_out_storage
     */
    public function auto_split_wms_out_storageAction()
    {
        $this->checkLock(__METHOD__);

        echo date('Ymd H:i:s') . " 自动拆单脚本开始执行" . PHP_EOL;

        //设置语言
        self::setLanguage('zh-CN');

        try {
            $count = $this->getWmsApplyCount();
            if ($count > 0) {
                $apply_arr_obj = $this->getWmsApplyList();

                $apply_arr = $apply_arr_obj->toArray();
                if (empty($apply_arr)) {
                    throw new ValidationException(date('Ymd H:i:s') . " 无拆单数据", ErrCode::$VALIDATE_ERROR);
                }
                $product_list = $this->getWmsApplyProductList(array_values(array_column($apply_arr, 'id')));
                if (empty($product_list)) {
                    throw new ValidationException(date('Ymd H:i:s') . " 拆单详情数据为空", ErrCode::$VALIDATE_ERROR);
                }

                $staff_ids  = array_values(array_unique(array_column($apply_arr, 'staff_id')));
                $staff_info = StaffService::getInstance()->searchStaff([
                    'staff_id' => $staff_ids,
                    'limit'    => count($staff_ids)
                ]);
                if (!empty($staff_info['data'])) {
                    $staff_info = array_column($staff_info['data'], null, 'staff_id');
                }
                //查询出库单必填成本中心配置
                $setting_pccode_required = EnumsService::getInstance()->getSettingEnvValueIds('material_asset_pccode_required');

                //17404查询分仓规则开关
                $material_store_storage_open_status = WarehouseDivisionRuleService::getInstance()->getStoreStorageOpenStatus();

                foreach ($apply_arr_obj as $apply_obj) {
                    $log = date('Y-m-d H:i:s') . " ";

                    $db = $this->getDI()->get('db_oa');
                    $db->begin();
                    try {
                        $apply               = $apply_obj->toArray();
                        $apply_product_list = $product_list[$apply['id']] ?? [];
                        if (empty($apply_product_list)) {
                            $db->rollback();
                            $this->logger->info('auto_split_wms_out_storage （没有找到拆单数据查找记录):' . json_encode($product_list, JSON_UNESCAPED_UNICODE) . '当前订单数据为' . json_encode($product_list, JSON_UNESCAPED_UNICODE));
                            continue;
                        }
                        $data = [
                            'apply'      => $apply,
                            'staff_info' => $staff_info,
                            'status'     => MaterialWmsEnums::STATUS_DRAFT
                        ];
                        if ($material_store_storage_open_status == MaterialSettingEnums::STORE_STORAGE_OPEN) {
                            //17404，开启了分仓规则，则按照分仓拆单逻辑走
                            $this->warehouseDivisionSplitOrder($data, $apply_product_list, $setting_pccode_required, $log);
                        } else {
                            $update_to_scm_arr   = array_unique(array_column($apply_product_list, 'update_to_scm'));
                            $log                 .= '申请单号' . $apply['apply_no'] . '(' . $apply['id'] . ')数据,自动拆单，包含不同步scm数据' . json_encode($apply_product_list, JSON_UNESCAPED_UNICODE) . ';' . PHP_EOL;
                            $judge_available = $this->getJudgeAvailableInventory($apply_product_list);
                            if ($judge_available) {
                                $log .= '申请单号' . $apply['apply_no'] . '(' . $apply['id'] . ')数据,自动拆单，包含申请数量大于可用库存数量的数据是:' . json_encode($apply_product_list, JSON_UNESCAPED_UNICODE) . PHP_EOL;
                            }
                            if (in_array(MaterialClassifyEnums::MATERIAL_CATEGORY_OFF, $update_to_scm_arr) || $judge_available) {
                                //不拆单的数据 生成出库单
                                $this->addOutStorage($this->draftBarcodeArr($apply_product_list), $data, MaterialClassifyEnums::MATERIAL_CATEGORY_OFF, $setting_pccode_required);

                                $log .= $add_out_storage_title = '申请单号' . $apply['apply_no'] . '(' . $apply['id'] . ')数据,自动拆单，不拆单添加成功' . PHP_EOL;
                                echo $add_out_storage_title;
                            } else {
                                $barcode_arr         = array_values(array_unique(array_column($apply_product_list, 'barcode')));
                                $storage_arr = $this->getMaterialSauStorageList($barcode_arr);
                                //开始拆单 公司货主仓库唯一 拆一单
                                $storage_arr = $this->getMachByStockArr($storage_arr, $apply_product_list, $apply['company_id']);
                                $log         .= $split = '申请单号' . $apply['apply_no'] . '(' . $apply['id'] . ')数据,自动拆单，被拆为' . count($storage_arr) . '单，拆单数据为:' . json_encode($apply, JSON_UNESCAPED_UNICODE) . ';' . PHP_EOL;
                                echo $split;

                                if (!empty($storage_arr)) {
                                    foreach ($storage_arr as $split_data) {
                                        $this->addOutStorage($split_data, $data, MaterialClassifyEnums::MATERIAL_CATEGORY_NO, $setting_pccode_required);
                                        $log .= $add_out_storage_title = '申请单号' . $apply['apply_no'] . '(' . $apply['id'] . ')数据,自动拆单，拆单添加成功' . PHP_EOL;
                                        echo $add_out_storage_title;
                                    }
                                }
                            }
                        }
                        $apply_obj->is_split   = MaterialWmsEnums::IS_SPLIT_CORRECT;
                        $apply_obj->updated_at = date('Y-m-d H:i:s', time());
                        $bool                  = $apply_obj->save();
                        if ($bool === false) {
                            throw new Exception('修改领用单失败, message=' . get_data_object_error_msg($apply_obj) . 'data:' . json_encode($apply_obj->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$MATERIAL_WMS_OUT_STORAGE_ADD_ERROR);
                            echo date('Ymd H:i:s') . " 修改领用单失败" . PHP_EOL;
                        }
                        $db->commit();
                        $this->logger->info($log);
                    } catch (\Exception $e) {
                        $db->rollback();
                        $this->logger->warning('auto_split_wms_out_storage (自动拆单添加失败):' . '失败原因:' . $e->getMessage() . ' code:' . $e->getCode() .'log :'. $log);
                    }
                }

            } else {
                echo date('Ymd H:i:s') . " 暂无拆单数据" . PHP_EOL;
            }

        } catch (ValidationException $e) {
            $this->logger->info('auto_split_wms_out_storage' . $e->getMessage());
            echo $e->getMessage() . PHP_EOL;
        } catch (Exception $e) {
            $this->logger->warning('auto_split_wms_out_storage' . $e->getMessage());
            echo $e->getMessage() . PHP_EOL;
        }

        echo date('Ymd H:i:s') . '自动拆单脚本执行结束' . PHP_EOL;
        $this->clearLock(__METHOD__);
        exit();
    }

    /**
     * 获取申请单满足条件的数量
     * @param array $product 分组数据
     * @param array $data 数据集合
     * @param int $update_to_scm
     * @param array $setting_pccode_required 成本中心是否必填配置
     * @return bool
     * @throws ValidationException
     * @throws BusinessException
     */
    private function addOutStorage(array $product, array $data ,int $update_to_scm = MaterialClassifyEnums::MATERIAL_CATEGORY_OFF, array $setting_pccode_required = [])
    {
        $this->logger->info('拆单的数据 生成出库单数据记录, 领用单数据: ' . json_encode($product, JSON_UNESCAPED_UNICODE) . '传入的数据: ' . json_encode($data, JSON_UNESCAPED_UNICODE) . ' 是否更新scm: ' . $update_to_scm);
        $processor_id   = '10000';
        $processor_name = 'SuperAdmin';
        $is_submit_scm = true;
        $all_last_time_num = array_sum(array_column($product['product_arr'],'last_time_num'));
        if (empty($all_last_time_num)) {
            //如果全部是0 不出库
            return true;
        }
        $apply          = $data['apply'];
        $status         = $data['status'];
        //获取收货人的公司和部门
        $consignee_info = StaffService::getInstance()->searchStaff(['staff_id' => $apply['consignee_id'], 'limit' => 1]);
        $consignee_data = $consignee_info['data'][0] ?? [];
        $type           = $apply['headquarters'] == Enums::HEAD_OFFICE_STORE_FLAG ? 1 : 2;
        $pc_id          = $apply['headquarters'] == Enums::HEAD_OFFICE_STORE_FLAG ? $consignee_data['node_department_id'] : $apply['use_land_id'];
        $pc_code        = StoreRentingAddService::getInstance()->getPcCode($pc_id, $type);

        $wms_no = BaseService::genSerialNo(MaterialWmsEnums::MATERIAL_WMS_OUT_STORAGE_NO_PREFIX, RedisKey::MATERIAL_WMS_OUT_STORAGE_COUNTER, 5);
        $wms_out_storage = MaterialWmsOutStorageModel::findFirst([
            'conditions' => 'wms_no = :wms_no: and is_deleted = :is_deleted:',
            'bind'       => ['wms_no' => $wms_no, 'is_deleted' => GlobalEnums::IS_NO_DELETED]
        ]);
        if (!empty($wms_out_storage)) {
            throw new Exception('该耗材领用出库单号已存在', ErrCode::$VALIDATE_ERROR);
        }
        $address_info = $this->getAddressInfo($apply);
        if (empty($address_info['province_code']) || empty($address_info['city_code']) || empty($address_info['district_code']) || empty($address_info['postal_code']) || empty($address_info['address'])) {
            //如果省市区地址邮编为空 回导致无法添加出库 所以变草稿
            $is_submit_scm = false;
        }
        $out_storage_model = new MaterialWmsOutStorageModel();
        $out_storage_data  = [
            'wms_no'               => $wms_no,
            'apply_id'             => $apply['id'],
            'apply_no'             => $apply['apply_no'],
            'scm_no'               => '',
            'staff_id'             => $apply['staff_id'],
            'staff_name'           => $apply['staff_name'],
            'company_id'           => $apply['company_id'],
            'company_name'         => $apply['company_name'],
            'node_department_id'   => $apply['node_department_id'],
            'node_department_name' => $apply['node_department_name'],
            'sys_store_id'         => $apply['sys_store_id'],
            'store_name'           => $apply['store_name'],
            'use_staff_id'         => $apply['consignee_id'],
            'use_staff_name'       => $apply['consignee_name'],
            'use_company_id'       => $consignee_data['company_id'] ?? 0,
            'use_company_name'     => $consignee_data['company_name'] ?? '',
            'use_department_id'    => $consignee_data['node_department_id'] ?? 0,
            'use_department_name'  => $consignee_data['node_department_name'] ?? '',
            'use_store_id'         => $apply['use_land_id'],
            'use_store_name'       => $apply['use_land_name'],
            'job_id'               => !empty($data['staff_info'][$apply['staff_id']]) ? $data['staff_info'][$apply['staff_id']]['job_id'] : 0,
            'job_name'             => !empty($data['staff_info'][$apply['staff_id']]) ? $data['staff_info'][$apply['staff_id']]['job_name'] : '',
            'pc_code'              => $pc_code['data']['pc_code'] ?? '',//成本中心
            'receive_store_id'     => $apply['use_land_id'],
            'receive_name'         => $apply['use_land_name'],
            'province_code'        => $address_info['province_code'] ?? '',
            'city_code'            => $address_info['city_code'] ?? '',
            'district_code'        => $address_info['district_code'] ?? '',
            'postal_code'          => $address_info['postal_code'] ?? '',
            'address'              => $address_info['address'] ?? '',
            'processor_id'         => $processor_id,
            'processor_name'       => $processor_name,
            'apply_date'           => $apply['created_at'],
            'remark'               => $apply['reason'] ?? '',
            'status'               => $data['status'],
            'update_to_scm'        => $update_to_scm,
            'is_submit_scm'        => MaterialWmsEnums::IS_SUBMIT_SCM_DENY,
            'mach_code'            => $product['mach_code'] ?? '',
            'mach_name'            => $product['mach_name'] ?? '',
            'stock_id'             => $product['stock_id'] ?? '',
            'stock_name'           => $product['stock_name'] ?? '',
            'delivery_way'         => $apply['delivery_way'],
            'is_deleted'           => GlobalEnums::IS_NO_DELETED,
            'split_id'             => 0,
            'split_type'           => MaterialWmsEnums::MATERIAL_WMS_SPLIT_TYPE_AUTOMATIC,
            'is_new'               => 1,//V22106 新数据
            'created_at'           => date('Y-m-d H:i:s')
        ];
        $out_storage       = $out_storage_model->i_create($out_storage_data);
        if ($out_storage === false) {
            throw new Exception('result add material_wms_out_storage error, message=' . get_data_object_error_msg($out_storage_model) . 'data:' . json_encode($out_storage_data, JSON_UNESCAPED_UNICODE)  , ErrCode::$MATERIAL_WMS_OUT_STORAGE_ADD_ERROR);
        }
        $product_data = [];
        $product_arr    = $product['product_arr'];
        if (!empty($product_arr)) {
            foreach ($product_arr as $product_item) {
                $product_data[] = [
                    'wms_id'                => $out_storage_model->id,
                    'wms_no'                => $out_storage_model->wms_no,
                    'barcode'               => $product_item['barcode'],
                    'name_zh'               => $product_item['name_zh'],
                    'name_en'               => $product_item['name_en'],
                    'name_local'            => $product_item['name_local'],
                    'unit_zh'               => $product_item['unit_zh'],
                    'unit_en'               => $product_item['unit_en'],
                    'model'                 => $product_item['model'],
                    'this_time_num'         => $product_item['last_time_num'],
                    'real_out_num'          => 0,
                    'apply_product_id'      => $product_item['id'],
                    'apply_num'             => $product_item['this_time_num'] ?? 0,
                    'use_val'               => $product_item['use_val'],
                    'category_id'           => !empty($product_item['category_id']) ? $product_item['category_id'] :0,
                    'category_name'         => $product_item['category_name'] ?? '',
                    'category_code'         => $product_item['category_code'] ?? '',
                    'finance_category_id'   => !empty($product_item['finance_category_id']) ? $product_item['finance_category_id'] : 0,
                    'finance_category_name' => $product_item['finance_category_name'] ?? '',
                    'finance_category_code' => $product_item['finance_category_code'] ?? '',
                    'available_inventory'   => 0,
                    'is_deleted'            => GlobalEnums::IS_NO_DELETED,
                    'created_at'            => date('Y-m-d H:i:s', time())
                ];
            }
        }
        if (!empty($product_data)) {
            $wms_out_storage_product = new MaterialWmsOutStorageProductModel();
            if ($wms_out_storage_product->batch_insert($product_data) === false) {
                throw new Exception('result add material_wms_out_storage_product error , message=' . get_data_object_error_msg($wms_out_storage_product) . ' data:' . json_encode($product_data, JSON_UNESCAPED_UNICODE), ErrCode::$MATERIAL_WMS_OUT_STORAGE_ADD_ERROR);
            }
        }

        //总部需要根据页面设置的[部门是否必填成本中心]判断成本中心是否为空, 如果命中条件就不能同步scm
        $continue_pc_code_is_empty = false;
        if ($apply['headquarters'] == Enums::HEAD_OFFICE_STORE_FLAG && in_array($consignee_data['node_department_id'], $setting_pccode_required) && empty($out_storage_data['pc_code'])) {
            $continue_pc_code_is_empty = true;
        }
        //总开关成本中心不可为空
        $setting_pc_code = MaterialSettingService::getPcCodeToScmRequired();
        if ($apply['headquarters'] == Enums::HEAD_OFFICE_STORE_FLAG && $setting_pc_code && empty($out_storage_data['pc_code'])) {
            $continue_pc_code_is_empty = true;
        }

        if (!empty($product['mach_code']) && !empty($product['stock_id']) && !$continue_pc_code_is_empty) {

            //查询库存 如果库存为0 拆单为草稿
            $update_to_scm_barcode = array_values(array_column($product_arr, 'barcode'));
            $scm                   = new ScmService();
            $scm_result            = $scm->goodsStock($product['mach_code'], $product['stock_id'], implode(',', $update_to_scm_barcode), '');
            $scm_result_data       = $scm_result['data'];
            $has_stock_barcode     = $err_barcode = [];
            if (!empty($scm_result_data)) {
                $has_stock_barcode = !empty($scm_result_data['stock']) ? $scm_result_data['stock'] : [];
                $err_barcode       = !empty($scm_result_data['errBarcode']) ? $scm_result_data['errBarcode'] : [];
            }
            $this->logger->info('拆单的数据 查询拆单数据的库存详情(stock):  ' . json_encode($has_stock_barcode, JSON_UNESCAPED_UNICODE) . '未查询到的(errBarcode): ' . json_encode($err_barcode, JSON_UNESCAPED_UNICODE) . ' 传入scm数据 : ' . json_encode($product, JSON_UNESCAPED_UNICODE));
            $warehouse = false;
            //查询库存 如果有不存在 或者库存为0 的
            if (empty($err_barcode) && !empty($has_stock_barcode) && !in_array(0, array_column($has_stock_barcode, 'availableInventory'))) {
                $warehouse = true;
            }

            if ($update_to_scm == MaterialClassifyEnums::MATERIAL_CATEGORY_NO && $status == MaterialWmsEnums::STATUS_DRAFT && $is_submit_scm && $warehouse) {
                //同步scm出库
                $out_storage_data['staff_id'] = $apply['consignee_id'];
                $out_storage_data['staff_name'] = $apply['consignee_name'];
                $scm                    = new ScmService();

                $sys_store_id =  $apply['headquarters'] == Enums::HEAD_OFFICE_STORE_FLAG ? Enums::HEAD_OFFICE_STORE_FLAG :$apply['use_land_id'];
                $apply_data             = array_merge(
                    $out_storage_data,
                    ['no' => $out_storage_data['wms_no']],
                    ['mark' => $out_storage_data['remark']],
                    ['bizExt' => (string)MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_WMS],
                    ['deliveryWay' => ScmService::$delivery_way_list[$apply['delivery_way']]],
                    ['headquarters' => $apply['headquarters']],
                    ['use_land_id' => $apply['use_land_id']],
                    ['sys_store_id' => $sys_store_id ]
                );
                $apply_data['products'] = $product_data;
                $scm_no = '';
                try {
                    $scm_no = $scm->warehouseAdd($product['mach_code'], $product['stock_id'], $apply_data);
                } catch (\Exception $e) {
                    $this->logger->warning('auto_split_wms_out_storage (自动撤单 失败变更为草稿):' . '仓库-货主:' . $product['mach_code'] . '-' . $product['stock_id'] . '入参:' . json_encode($apply_data, JSON_UNESCAPED_UNICODE) . ' 失败原因:' . $e->getMessage() . ' code:' . $e->getCode());
                }
                if (!empty($scm_no)) {
                    $out_storage_model->scm_no = $scm_no;
                    $out_storage_model->status = MaterialWmsEnums::STATUS_WAIT_APPROVE;
                }
                $out_storage_model->is_submit_scm = MaterialWmsEnums::IS_SUBMIT_SCM_CORRECT;
                $out_storage_model->updated_at    = date('Y-m-d H:i:s');
                if ($out_storage_model->save() === false) {
                    throw new Exception('result save material_wms_out_storage error, message=' . get_data_object_error_msg($out_storage_model) . json_encode($out_storage_model->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$MATERIAL_WMS_OUT_STORAGE_SAVE_ERROR);
                }
            }
            sleep(1);
        }
        $old_main_model          = ['wms_no' => $wms_no, 'status' => ''];
        $before_data             = $out_storage_model->toArray();
        $before_data['products'] = $product_data;
        $user                    = ['id' => $processor_id, 'name' => $processor_name];
        //添加日志
        WmsOutStorageService::getInstance()->addMaterialWmsUpdateLog(MaterialWmsEnums::MATERIAL_WMS_UPDATE_LOG_TYPE_ADD, $old_main_model, $before_data, $user);
    }

    /**
     * 获取省市区地址邮编
     * @param array $apply 条件数据
     * @return array
     */
    private function getAddressInfo(array $apply)
    {
        $address_info = SysStoreModel::findFirst([
            'columns'    => 'province_code, city_code, district_code, postal_code, detail_address as address',
            'conditions' => 'id = :id:',
            'bind'       => ['id' => $apply['use_land_id']]
        ]);
        $address      = [];
        if (!empty($address_info)) {
            $address = $address_info->toArray();
        } else {
            $address_info = HeadquartersAddressModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $apply['use_land_id']]
            ]);
            if (!empty($address_info)) {
                $address = $address_info->toArray();
            }
        }
        return $address;
    }

    /**
     * 获取申请单满足条件的数量
     * @return int
     */
    private function getWmsApplyCount()
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(id) as total');
        $builder->from(['main' => MaterialWmsApplyModel::class]);
        $builder->where('is_deleted = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);
        $builder->andWhere('is_split = :is_split:', ['is_split' => MaterialWmsEnums::IS_SPLIT_DENY]);
        $builder->andWhere('status = :status:', ['status' => Enums::WF_STATE_APPROVED]);
        return intval($builder->getQuery()->getSingleResult()->total);
    }


    /**
     * 获取申请单满足条件的数据
     * @return mixed
     */
    private function getWmsApplyList()
    {
        return MaterialWmsApplyModel::find(
                [
                    'conditions' => 'status = :status: and is_deleted = :is_deleted: and is_split = :is_split: ',
                    'bind'       => ['status' => Enums::WF_STATE_APPROVED, 'is_deleted' => GlobalEnums::IS_NO_DELETED, 'is_split' => MaterialWmsEnums::IS_SPLIT_DENY]
                ]
            );
    }

    /**
     * 获取申请单满足条件的数据
     * @param array $ids 详情id集合
     * @return mixed
     */
    private function getWmsApplyProductList(array $ids)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            'mwap.id',
            'mwap.apply_id',
            'mwap.barcode',
            'mwap.name_zh',
            'mwap.name_en',
            'mwap.name_local',
            'mwap.unit_zh',
            'mwap.unit_en',
            'mwap.use_val',
            'mwap.model',
            'mwap.last_time_num',
            'mwap.this_time_num',
            'mwap.available_inventory',
            'mwap.is_deleted',
            'mwap.created_at',
            'mwap.updated_at',
            'ms.category_id',
            'ms.finance_category_id',
            'mc.name as category_name',
            'mc.code as category_code',
            'mfc.name as finance_category_name',
            'mfc.code as finance_category_code',
            'ms.update_to_scm',
            'ms.name_zh',
            'ms.name_local',
            'ms.model',
            'ms.unit_zh',
            'ms.unit_en',
            'ms.remark',
            'ms.category_type',
            'ms.purchase_type',
            'ms.brand',
            'ms.price',
            'ms.currency',
            'ms.enable_sn',
            'ms.status',
        ]);
        $builder->from(['mwap' => MaterialWmsApplyProductModel::class]);
        $builder->leftjoin(MaterialSauModel::class, 'ms.barcode = mwap.barcode', 'ms');
        $builder->leftjoin(MaterialCategoryModel::class, 'mc.id = ms.category_id', 'mc');
        $builder->leftjoin(MaterialFinanceCategoryModel::class, 'mfc.id = ms.finance_category_id', 'mfc');
        $builder->where('mwap.apply_id in ({apply_id:array}) and mwap.last_time_num > 0 and mwap.is_deleted = :is_deleted: and ms.is_deleted = :is_deleted:', ['apply_id' => $ids, 'is_deleted' => GlobalEnums::IS_NO_DELETED]);
        $product_arr = $builder->getQuery()->execute()->toArray();
        $product     = [];
        if (!empty($product_arr)) {
            foreach ($product_arr as $product_item) {
                $product[$product_item['apply_id']][] = $product_item;
            }
        }
        return $product;
    }

    /**
     * 获取barcode标准型号仓储信息
     * @param array $barcode barcode
     * @return mixed
     */
    private function getMaterialSauStorageList($barcode)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            'mss.id', 'mss.sau_id', 'mss.barcode', 'mss.company_id', 'mss.company_name', 'mss.mach_code', 'mss.mach_name', 'mss.stock_id', 'mss.stock_name', 'mss.is_default'
        ]);
        $builder->from(['mss' => MaterialSauStorageModel::class]);
        $builder->where('mss.is_deleted = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);
        $builder->inWhere('mss.barcode', $barcode);
        $builder->orderBy('mss.is_default desc');
        return $builder->getQuery()->execute()->toArray();
    }


    /**
     * 获取barcode标准型号仓储信息
     * @return mixed
     */
    private function getMaterialSauList($barcode)
    {
        $material_sau_arr = [];
        if(!empty($barcode)){
            $material_sau_arr =  MaterialSauModel::find(
                [
                    'conditions' => 'barcode in ({barcode:array}) and is_deleted = :is_deleted:',
                    'bind'       => ['barcode' => $barcode, 'is_deleted' => GlobalEnums::IS_NO_DELETED]
                ]
            )->toArray();
        }
        return $material_sau_arr;
    }

    /**
     * 申请的数量有没有大于可用库存的
     * @return mixed
     */
    private function getJudgeAvailableInventory($apply_arr)
    {
        $split = false;
        foreach ($apply_arr as $value) {
            if ($value['last_time_num'] > $value['available_inventory']) {
                $split = true;
                break;
            }
        }
        return $split;
    }


    /**
     * 查询拆单符合条件的货主和仓库
     * @param array $storage 按照拆单数据的barcode查询sau的全部数据（material_sau_storage）表
     * @param array $product_arr 需要拆单的数据
     * @param int $company_id 公司
     * @return mixed
     */
    private function getMachByStockArr($storage, $product_arr, $company_id)
    {
        if (!empty($storage) && !empty($product_arr)) {
            $storage_item = [];
            $mach_data    = $default_company = $barcode_arr = [];
            $product_arr =  array_column($product_arr,null, 'barcode');
            foreach ($storage as $item) {
                    if ($item['company_id'] == $company_id && !in_array($item['barcode'], $barcode_arr)) {
                        //barcode、货主、仓库、公司 组合唯一key
                        $title_key                  = $company_id . '_' . $item['mach_code'] . '_' . $item['stock_id'];
                        $storage_item[$title_key][] = $item;
                        $storage_item[$title_key]['product_arr'][] = $product_arr[$item['barcode']];
                        $barcode_arr[]              = $item['barcode'];
                    } else {
                        //如果公司相同 但是默认值不同  取第一个默认值 组合一起
                        if (in_array($item['company_id'], $default_company)) {
                            $item['is_default'] = 1;
                        }
                        //barcode、公司 和默认组合唯一key 目前不存在默认的情况
                        $title_key_default = $item['company_id'] . '_' . $item['is_default'];
                        if (!empty($item['is_default']) && !in_array($item['barcode'], $barcode_arr)) {
                            $default_company[]                  = $item['company_id'];
                            $storage_item[$title_key_default][] = $item;
                            $storage_item[$title_key_default]['product_arr'][] = $product_arr[$item['barcode']];
                            $barcode_arr[]                      = $item['barcode'];
                        } else if (!in_array($item['barcode'], $barcode_arr)) {
                            //没得默认值
                            $title_key_null_default                  = $item['company_id'] . '_' . $item['mach_code'] . '_' . $item['stock_id'];
                            $storage_item[$title_key_null_default][] = $item;
                            $storage_item[$title_key_null_default]['product_arr'][] = $product_arr[$item['barcode']];
                            $barcode_arr[]              = $item['barcode'];
                        }
                    }
            }
            //相同的一组key 得到一个分组
            foreach ($storage_item as $item) {
                    $rs = [];
                    $rs['mach_code']             = $item[0]['mach_code'];
                    $rs['mach_name']             = $item[0]['mach_name'];
                    $rs['stock_id']              = $item[0]['stock_id'];
                    $rs['stock_name']            = $item[0]['stock_name'];
                    $rs['product_arr']           = $item['product_arr'];
                    $mach_data[]                 = $rs;
            }
            //提取不存在storage表的数据(提交的数据 一种是 storage表存在 ，一种是不存在的)
            $diff_barcode_arr = array_diff(array_unique(array_column($product_arr, 'barcode')), array_unique(array_column($storage, 'barcode')));

            if (!empty($diff_barcode_arr)) {
                foreach ($diff_barcode_arr as $diff_barcode){
                    $data[] = $product_arr[$diff_barcode];
                }
                $rs_diff[]          = $this->draftBarcodeArr($data);
                $mach_data          = array_merge($mach_data, $rs_diff);
            }
            $this->logger->info('查询拆单符合条件的货主和仓库记录，拆单数据' . json_encode($mach_data, JSON_UNESCAPED_UNICODE) . '按照拆单数据的barcode查询sau的全部数据: ' . json_encode($storage, JSON_UNESCAPED_UNICODE) . ' 需要拆单的数据 : ' . json_encode($product_arr, JSON_UNESCAPED_UNICODE) . ' 公司：' . $company_id );
        } else {
            //如果没有配置任何公司和货主数据 兜底做草稿
            $mach_data[] = $this->draftBarcodeArr($product_arr);
        }
        return $mach_data;
    }

    /**
     * 分仓规则拆单逻辑
     * @param array $data['apply'=>每笔申请单信息组,'staff_info'=>'所有申请单申请人信息组','status'=>'草稿']
     * @param array $product_list 申请单-产品明细
     * @param array $setting_pccode_required 成本中心是否必填配置
     * @param string $log 日志
     * @throws BusinessException
     * @throws ValidationException
     */
    private function warehouseDivisionSplitOrder($data, $product_list, $setting_pccode_required, &$log)
    {
        //判断申请单上的use_land_id（使用网点）、公司、片区（总部不找）、大区（总部不找），均没设置分仓规则，直接生成草稿出库单
        $apply_info = $data['apply'];
        $add_out_storage_title = '申请单号' . $apply_info['apply_no'] . '(' . $apply_info['id'] . ')数据';
        $level = MaterialWmsEnums::MATERIAL_STORE_STORAGE_RULE_LEVEL_ALL;//全部级别
        if ($apply_info['delivery_way'] == MaterialClassifyEnums::DELIVERY_WAY_SELF) {
            $level = MaterialWmsEnums::MATERIAL_STORE_STORAGE_RULE_LEVEL_ONE;//一级
        }
        $store_storage_rules_list = WarehouseDivisionRuleService::getInstance()->getStoreStorageRulesList(['company_id' => $apply_info['company_id'], 'use_land_id' => $apply_info['use_land_id']], $level);
        if (empty($store_storage_rules_list)) {
            //不拆单的数据，直接生成草稿出库单
            $this->addOutStorage($this->draftBarcodeArr($product_list), $data, MaterialClassifyEnums::MATERIAL_CATEGORY_OFF, $setting_pccode_required);
            $add_out_storage_title .= ' ;分仓开关开启，但三种类型的分仓均未设置分仓规则，直接生成草稿出库单' . PHP_EOL;
        } else {
            //草稿出库单产品明细清单组
            $draft_product_list = [];
            //待审核的出库单
            $wait_audit_product_list = [];
            //过滤到一定条件后剩下的barcode组
            $last_product_barcode = [];
            //将产品明细按照barcode为key设置产品明细组
            $product_arr =  array_column($product_list,null, 'barcode');

            //有禁用、非耗材、不更新至SCM的放到草稿出库单产品明细组里
            foreach ($product_list as $barcode_item) {
                $barcode = $barcode_item['barcode'];
                if ($barcode_item['status'] == MaterialClassifyEnums::MATERIAL_PROHIBITED_USE || $barcode_item['category_type'] != MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_WMS || $barcode_item['update_to_scm'] == MaterialClassifyEnums::MATERIAL_CATEGORY_OFF) {
                    $draft_product_list[] = $product_arr[$barcode];
                } else {
                    $last_product_barcode[$barcode] = $barcode_item;
                }
            }

            //剩下的barcode组 && 循环出库优先级的，找到复合出库条件的barcode出库单
            if (!empty($last_product_barcode)) {
                //循环遍历分仓规则优先级
                foreach ($store_storage_rules_list as $rule) {
                    //无剩余的barcode组，则循环终止
                    if (empty($last_product_barcode)) {
                        break;
                    }
                    //按照货主、仓库查询剩下barcode的库存
                    $update_to_scm_barcode = array_keys($last_product_barcode);
                    $barcode_available_inventory = AssetOutStorageService::getInstance()->handleUpdateToScmBarcode($update_to_scm_barcode, $rule['mach_code'], $rule['stock_id']);
                    foreach ($last_product_barcode as $barcode => $barcode_item) {
                        if (!is_numeric($barcode_available_inventory[$barcode])) {
                            //库存非数字，包含仓库中未查找到或者其他原因，则直接放入草稿出库单产品明细清单组且改barcode需从剩下的barcode组中移除
                            $draft_product_list[] = $product_arr[$barcode];
                            unset($last_product_barcode[$barcode]);
                        } else if ($product_arr[$barcode]['last_time_num'] <= $barcode_available_inventory[$barcode]) {
                            //库存是数字，且申请数量 <= 库存数量，则需要出待审核出库单【按照货主+仓库分组】且改barcode需从剩下的barcode组中移除
                            $wait_audit_product_info = $product_arr[$barcode];

                            $mach_code_stock_key = $rule['mach_code'] . '_' . $rule['stock_id'];
                            //相同货主仓库下，货主仓库信息只需赋予一份
                            if (!isset($wait_audit_product_list[$mach_code_stock_key])) {
                                $wait_audit_product_list[$mach_code_stock_key] = [
                                    'mach_code' => $rule['mach_code'],
                                    'mach_name' => $rule['mach_name'],
                                    'stock_id' => $rule['stock_id'],
                                    'stock_name' => $rule['stock_name']
                                ];
                            }
                            //出库产品明细累计即可
                            $wait_audit_product_list[$mach_code_stock_key]['product_arr'][] = $wait_audit_product_info;
                            //从剩下的barcode组中移除
                            unset($last_product_barcode[$barcode]);
                        } else {
                            //若是其他情况（库存为0或者申请数量>库存数量）等的barcode，则继续去下一个优先级中找寻
                        }
                    }
                }
            }

            //若最终优先级循环完了，剩余的barcode组里依然有剩余，则放到草稿出库单明细组里
            if (!empty($last_product_barcode)) {
                foreach ($last_product_barcode as $barcode => $barcode_item) {
                    $draft_product_list[] = $product_arr[$barcode];
                }
            }
            //若有草稿出库单产品明细清单组，则需要生产草稿出库单
            if (!empty($draft_product_list)) {
                $this->addOutStorage($this->draftBarcodeArr($draft_product_list), $data, MaterialClassifyEnums::MATERIAL_CATEGORY_OFF, $setting_pccode_required);
                $add_out_storage_title .= ' ;分仓开关开启，也设置分仓规则，但存在库存不足或非耗材或禁用或不更新至SCM等情况的barcode数据;生成草稿出库单成功'. PHP_EOL;
            }

            //若有待审核的出库单，则需要生产待审核出库单
            if (!empty($wait_audit_product_list)) {
                foreach ($wait_audit_product_list as $mach_code_stock_key => $item) {
                    $this->addOutStorage($item, $data, MaterialClassifyEnums::MATERIAL_CATEGORY_NO, $setting_pccode_required);
                    $add_out_storage_title .= ' ;分仓开关开启，也设置分仓规则，按照【 ' . $mach_code_stock_key . ' 】，分组拆单成功' . PHP_EOL;
                }
            }
        }
        $log .= $add_out_storage_title . PHP_EOL;
        echo $add_out_storage_title;
    }
    /**
     * 草稿数据
     * @param array $diff_barcode_arr 在barcode找不到的数据做草稿处理
     * @return array
     **/
    private function draftBarcodeArr($diff_barcode_arr)
    {
        $data['mach_code']             = '';
        $data['mach_name']             = '';
        $data['stock_id']              = '';
        $data['stock_name']            = '';
        $data['product_arr']            = $diff_barcode_arr;
        return $data;
    }


    /**
     * 补偿脚本 主要修复调取SCM添加出库的时候没响应的单子
     * php cli.php  material_wms compensate_wms_out_storage
     */
    public function compensate_wms_out_storageAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $this->check_process($process_name);
        $log = '';
        try {
            echo date('Ymd H:i:s') . " 补偿机制脚本开始执行" . PHP_EOL;

            $out_storage_arr = MaterialWmsOutStorageModel::find([
                'conditions' => "scm_no = ''  and  status = :status: and is_submit_scm = :is_submit_scm: and update_to_scm = :update_to_scm:",
                'bind'       => [
                    'status'        => MaterialWmsEnums::STATUS_DRAFT,
                    'is_submit_scm' => MaterialWmsEnums::IS_SUBMIT_SCM_CORRECT,
                    'update_to_scm' => MaterialClassifyEnums::MATERIAL_CATEGORY_NO],
            ]);
            if (!empty($out_storage_arr->toArray())) {
                foreach ($out_storage_arr as $out_storage_item) {

                    $log .= date('Ymd H:i:s') . " 执行数据 耗材出库单号为" . $out_storage_item->wms_no . 'mach_code为 :' . $out_storage_item->mach_code . ' 开始查询scm' . PHP_EOL;
                    $scm = new ScmService();

                    $scm_no['outSn'] = '';
                    try {
                        $scm_no = $scm->outboundOrderStatus($out_storage_item->mach_code, $out_storage_item->scm_no);
                    } catch (\Exception $e) {
                        //补偿机制查询 也存在失败情况  不能无限补偿
                        $this->logger->warning(' 补偿机制失败,失败原因:' . $e->getMessage() . '记录数据为:' . $out_storage_item->mach_code . '/' . $out_storage_item->wms_no);
                    }
                    $log                      .= date('Ymd H:i:s') . " 执行数据 耗材出库单号为" . $out_storage_item->wms_no . 'mach_code为 :' . $out_storage_item->mach_code . ' 执行结果为' . json_encode($scm_no, JSON_UNESCAPED_UNICODE) . PHP_EOL;
                    $out_storage_item->scm_no = $scm_no['outSn'];
                    if (!empty($scm_no['outSn'])) {
                        $out_storage_item->status        = MaterialWmsEnums::STATUS_WAIT_APPROVE;
                    } else {
                        $out_storage_item->is_submit_scm = MaterialWmsEnums::IS_SUBMIT_SCM_DENY;
                    }
                    $out_storage_item->updated_at    = date('Y-m-d H:i:s', time());
                    $bool = $out_storage_item->save();
                    if ($bool === false) {
                        throw new Exception('补偿机制脚本修复数据 耗材出库单号为' . get_data_object_error_msg($out_storage_item) . json_encode($out_storage_item->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$MATERIAL_WMS_OUT_STORAGE_SAVE_ERROR);
                    }
                    sleep(1);
                }
            } else {
                echo date('Ymd H:i:s') . " 补偿机制查询暂时没有需要执行的数据" . PHP_EOL;
            }
        } catch (\Exception $e) {
            $log  = date('Ymd H:i:s') . ' 补偿机制失败' . $e->getMessage() . ' code:' . $e->getCode() . PHP_EOL;
            echo $log;
            $this->logger->warning($log);
        }
        echo date('Ymd H:i:s') . '补偿机制脚本执行结束' . PHP_EOL;
    }


    /**
     *  调取SCM端审核接口，审核耗材出库单，并更新出库单状态
     *  php cli.php  material_wms sync_scm_audit_wms_out_storage
     * @return bool
     */
    public function sync_scm_audit_wms_out_storageAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $this->check_process($process_name);
        echo '审核耗材出库单 开始执行: ' . date('Y-m-d H:i:s') . PHP_EOL;
        try {
            $i               = 1;
            $start_time      = date('Y-m-d H:i:s', time() - 86400 * 30);
            $end_time        = date('Y-m-d H:i:s');
            $deal_audit_data = $this->getWmsOutStorageList($i, $start_time, $end_time);
            if (empty($deal_audit_data)) {
                echo '审核耗材出库单 暂无需要审核的数据' . PHP_EOL;
                exit;
            }
            while (!empty($deal_audit_data)) {
                echo '第' . $i . '页处理开始' . PHP_EOL;
                $scm = new ScmService();
                foreach ($deal_audit_data as $key => $value) {
                    $wms_out_storage = MaterialWmsOutStorageModel::findFirst([
                        'id = :id:  and is_deleted = :is_deleted:',
                        'bind' => ['id' => $value['id'], 'is_deleted' => GlobalEnums::IS_NO_DELETED]
                    ]);
                    if (empty($wms_out_storage)) {
                        continue;
                    }
                    $return_data = false;
                    $z           = 0;
                    while (empty($return_data) && $z < 2) {
                        $z++;
                        $return_data = $scm->auditOutBound($wms_out_storage->mach_code, $wms_out_storage->scm_no);
                        if ($return_data) {
                            $wms_out_storage->status     = $return_data;
                            $wms_out_storage->updated_at = date('Y-m-d H:i:s');
                            $bool = $wms_out_storage->save();
                            if ($bool === false) {
                                throw new Exception('result save material_wms_out_storage error, message=' . get_data_object_error_msg($wms_out_storage) . json_encode($wms_out_storage->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$MATERIAL_WMS_OUT_STORAGE_SAVE_ERROR);
                            }
                            echo 'material_wms_out_storage success id:' . $value['id'] . PHP_EOL;
                        }
                        sleep(2);
                    }
                }
                echo '第' . $i . '页处理结束' . PHP_EOL;
                sleep(1);
                $i               += 1;
                $deal_audit_data = $this->getWmsOutStorageList($i, $start_time, $end_time);
            }
            if (empty($deal_audit_data)) {
                echo "数据均已处理完毕" . PHP_EOL;
            }
            echo 'end: ' . date('Y-m-d H:i:s') . PHP_EOL;
        } catch (Exception $e) {
            $this->logger->warning('material_wms_out_storage_scm_audit: ' . $e->getMessage() . ' code: ' .  $e->getCode());
            return false;
        }
    }

    /**
     * 获取需要审核的出库单列表
     * @param int $page_num 当前页码
     * @param string $start_time 开始时间
     * @param string $end_time 结束时间
     * @return mixed
     */
    private function getWmsOutStorageList($page_num, $start_time, $end_time)
    {
        $page_size = 100;
        return MaterialWmsOutStorageModel::find([
            'columns'    => 'id, scm_no, status, mach_code',
            'conditions' => "status = :status: and  scm_no != '' and is_deleted = :is_deleted: and created_at >= :start_time: and created_at <= :end_time:",
            'bind'       => ['status' => MaterialWmsEnums::STATUS_WAIT_APPROVE, 'is_deleted' => GlobalEnums::IS_NO_DELETED, 'start_time' => $start_time, 'end_time' => $end_time],
            'limit'      => $page_size,
            'offset'     => $page_size * ($page_num - 1),
        ])->toArray();
    }

    /**
     * 批量审核
     * php cli.php  material_wms batch_audit
     */
    public function batch_auditAction()
    {
        $this->checkLock(__METHOD__);

        $process_name = str_replace('Action', '', __FUNCTION__);
        $log          = '任务名称: ' . $process_name . PHP_EOL;
        $log          .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        try {
            // 查询当前是否存在待处理的任务
            // 查询最近一次待下载任务
            $audit_data = ImportTaskModel::findFirst([
                'conditions' => 'type = :type: and status = :status: and is_deleted = :is_deleted:',
                'bind'       => [
                    'type'       => ImportCenterEnums::TYPE_MATERIAL_WMS_AUDIT_UPDATE,
                    'status'     => ImportCenterEnums::STATUS_WAITING,
                    'is_deleted' => ImportCenterEnums::NOT_DELETED
                ],
                'order'      => 'id asc',
            ]);
            if (empty($audit_data)) {
                throw new ValidationException('没有等待处理的任务', ErrCode::$VALIDATE_ERROR);
            }

            // 存在下载任务,开始执行
            // 1. 先改为处理中
            $audit_data->status = ImportCenterEnums::STATUS_DOING;
            if ($audit_data->save() === false) {
                $log .= '下载任务启动异常: 更新为 处理中 失败' . PHP_EOL;
                throw new Exception('Download task start exception: update to processing failure');
            }
            // 1. 下载文件
            $tmp_dir           = sys_get_temp_dir() . '/';
            $wms_apply_service = WmsApplyService::getInstance();
            $wms_apply_service::setLanguage($audit_data->language ?? 'en');
            $oss_info  = get_oss_info_by_url($audit_data->file_url);
            $file_name = $oss_info['file_name'];
            ob_start();
            readfile($audit_data->file_url);
            $file_data = ob_get_contents();
            ob_end_clean();
            $tmp_path = $tmp_dir . $file_name;
            file_put_contents($tmp_path, $file_data);
            // 2. 读取文件
            $config = ['path' => $tmp_dir];
            $excel  = new \Vtiful\Kernel\Excel($config);

            // 读取上传文件数据
            $excel_data = $excel->openFile($file_name)
                ->openSheet()
                ->setType([
                    0  => \Vtiful\Kernel\Excel::TYPE_STRING,//耗材申请单
                    1  => \Vtiful\Kernel\Excel::TYPE_STRING,//barcode
                    14 => \Vtiful\Kernel\Excel::TYPE_STRING,//审批结果
                ])
                ->getSheetData();
            unlink($tmp_path);
            if (empty($excel_data)) {
                throw new Exception('file is empty');
            }
            $user_id                 = $audit_data->staff_info_id;
            $excel_header_column_one = array_shift($excel_data);
            $update_result_column    = 17;//备注写入列
            $res                     = $wms_apply_service->batchAuditEdit($excel_data, $user_id, $update_result_column);
            $this->logger->info('耗材批量审核,校验之后的数据' . json_encode($res, JSON_UNESCAPED_UNICODE));

            // 4. 生成结果文件
            if ($res['code'] == ErrCode::$SUCCESS) {
                // 处理成功, 生成上传结果文件
                $upload_result_data = $res['data']['excel_data'];
                $excel_extra_config = [
                    'file_name'       => date('YmdHis') . '_' . $user_id . '_batch_audit_import_result.xlsx',
                    'end_column_char' => 'U',
                    'column_width'    => 15,
                ];
                $path               = self::customizeExcelToFile($excel_header_column_one, $upload_result_data, $excel_extra_config);
                $oss_result         = OssHelper::uploadFile($path);
            }
            // 5. 写入结果
            // 再查一次数据,长时间处理, 上边的数据库连接会自动关闭,保存报错
            $after_audit_data = ImportTaskModel::findFirst($audit_data->id);
            if (!$after_audit_data || $after_audit_data->status != ImportCenterEnums::STATUS_DOING) {
                throw new Exception('result save error, not found task');
            }
            $after_audit_data->success_num     = $res['data']['success_num'];
            $after_audit_data->error_num       = $res['data']['failed_sum'];
            $after_audit_data->result_file_url = $oss_result['object_url'] ?? '';
            $after_audit_data->status          = ImportCenterEnums::STATUS_DONE;
            $after_audit_data->finished_at     = date('Y-m-d H:i:s');
            if ($after_audit_data->save() === false) {
                // 正常情况下进入任务是会校验, 所以这种空文件基本不会出现, 如果出现记录异常
                throw new Exception('result save error, message=' . get_data_object_error_msg($after_audit_data));
            }
        } catch (ValidationException $e) {
            $this->logger->info('batch_audit_update_error: validation=' . $e->getMessage() . '; trace=' . $e->getTraceAsString());
            $log .= $e->getMessage() . PHP_EOL;

        } catch (Exception $e) {
            $log .= $e->getMessage() . PHP_EOL;

            $this->logger->error('batch_audit_update_error: message=' . $e->getMessage() . '; trace=' . $e->getTraceAsString());
            // 导入结果置为失败
            $after_audit_data              = ImportTaskModel::findFirst($audit_data->id);
            $after_audit_data->status      = ImportCenterEnums::STATUS_FAILED;
            $after_audit_data->finished_at = date('Y-m-d H:i:s');
            if ($after_audit_data->save() === false) {
                $this->logger->error('batch_audit_update_set_failed_error: ' . get_data_object_error_msg($after_audit_data));
            }
        }

        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $this->logger->info($log);
        $this->clearLock(__METHOD__);
        exit($log);
    }

    /**
     *  一次性脚本，把出库单状态为作废和已出库的出库单可用库存修改为最新
     *  php cli.php  material_wms update_wms_out_storage_available_inventory
     * @return bool
     */
    public function update_wms_out_storage_available_inventoryAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $this->check_process($process_name);
        try {
            $out_storage_data = $this->getWmsOutStorageStatusData();
            if (empty($out_storage_data)) {
                echo '出库单状态为作废和已出库的出库单数据为空' . PHP_EOL;
                exit;
            }
            $data = array_chunk($out_storage_data, 20);
            //分组处理
            $scm = new ScmService();
            foreach ($data as $item) {
                $available_inventory = [];
                //对数据按照货主分组
                $mach_code_arr = $this->machCodeByStockGroup($item);
                foreach ($mach_code_arr as $mach_code) {
                    $to_scm_barcode  = implode(',', array_values(array_unique(array_column($mach_code, 'barcode'))));
                    $scm_result      = $scm->goodsStock($mach_code[0]['mach_code'], $mach_code[0]['stock_id'], $to_scm_barcode, '');
                    $scm_result_data = $scm_result['data'];
                    $this->logger->info('货主/仓库为 ' . $mach_code[0]['mach_code'] . '/' . $mach_code[0]['stock_id'] . '查询可用库存为 : ' . json_encode($scm_result_data['stock'], JSON_UNESCAPED_UNICODE));
                    if (!empty($scm_result_data) && !empty($scm_result_data['stock'])) {
                        foreach ($scm_result_data['stock'] as $key => $inventory) {
                            $available_inventory[$key . '_' . $mach_code[0]['stock_id']] = $inventory['availableInventory'];
                        }
                        sleep(1);
                    }
                }
                $wms_out_storage_product_ids = array_values(array_column($item, 'id'));
                $out_storage_product_arr     = MaterialWmsOutStorageProductModel::find([
                    'conditions' => 'id in ({id:array})',
                    'bind'       => [
                        'id' => $wms_out_storage_product_ids
                    ]
                ]);
                $product_arr                 = $out_storage_product_arr->toArray();
                if (!empty($product_arr)) {
                    //查询barcode对应的仓库id 处理相同的barcode  在不同的仓库下的库存
                    $out_storage_arr = MaterialWmsOutStorageModel::find([
                        'conditions' => 'id in ({id:array})',
                        'bind'       => [
                            'id' => array_values(array_unique(array_column($product_arr, 'wms_id')))
                        ]
                    ])->toArray();
                    $stock_by_id_arr = array_column($out_storage_arr, 'stock_id', 'id');
                    foreach ($out_storage_product_arr as $out_product_item) {
                        $barcode_id                            = $out_product_item->barcode . '_' . $stock_by_id_arr[$out_product_item->wms_id];
                        $out_product_item->available_inventory = $available_inventory[$barcode_id] ?? 0;
                        $out_product_item->updated_at          = date('Y-m-d H:i:s', time());
                        $bool                                  = $out_product_item->save();
                        if ($bool === false) {
                            throw new Exception('修改出库单状态为作废和已出库的出库单可用库存修改为最新 失败' . get_data_object_error_msg($out_product_item) . json_encode($out_storage_product_arr->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$MATERIAL_WMS_OUT_STORAGE_SAVE_ERROR);
                        }
                        $this->logger->info('修改数据成功 id为 : ' . $out_product_item->id . ' 修改可用库存为  ' . $out_product_item->available_inventory);
                    }
                }
            }
            echo 'end: ' . date('Y-m-d H:i:s') . PHP_EOL;
        } catch (Exception $e) {
            $this->logger->warning('update_wms_out_storage_available_inventory : ' . $e->getMessage() . ' code: ' . $e->getCode());
            exit('修改出库单状态为作废和已出库的出库单可用库存修改为最新失败');
        }
    }

    /**
     * 获取出库单状态为作废和已出库数据
     * @return array
     */
    private function getWmsOutStorageStatusData()
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            'mwosp.id', 'mwosp.barcode', 'mwosp.barcode', 'mwos.mach_code', 'mwos.stock_id'
        ]);
        $builder->from(['mwosp' => MaterialWmsOutStorageProductModel::class]);
        $builder->leftjoin(MaterialWmsOutStorageModel::class, 'mwos.id = mwosp.wms_id', 'mwos');
        $builder->where('mwos.mach_code != "" and  mwos.stock_id != ""  and  mwosp.available_inventory = 0');
        $builder->inWhere('mwos.status', [MaterialWmsEnums::STATUS_OUT, MaterialWmsEnums::STATUS_CANCEL]);
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 按照仓库和货主给数据分组，方便批量查询可用库存数据
     * @param array $data 处理的数据
     * @return array
     */
    private function machCodeByStockGroup(array $data)
    {
        $result = $res = [];
        foreach ($data as $key => $value) {
            $result[$value['mach_code'] . '_' . $value['stock_id']][] = $value;
        }
        foreach ($result as $key => $value) {
            array_push($res, $value);
        }
        return $res;
    }

    /**
     * 变更耗材出库单-箱单号、运单号-妥投状态、妥投日期
     * php app/cli.php material_wms wms_out_storage_box
     */
    public function wms_out_storage_boxAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log          = '任务名称: ' . $process_name . PHP_EOL;
        $log          .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log          .= '初始内存: ' . memory_usage() . PHP_EOL . PHP_EOL;
        $is_exception = false;
        try {
            BaseTask::setLanguage('zh');
            //获取所有状态为未妥投的运单号
            $list = MaterialWmsOutStorageBoxModel::find([
                'conditions' => 'status = :status:',
                'bind'       => ['status' => MaterialWmsEnums::MATERIAL_WMS_BOX_STATUS_NOT_DELIVERED],
            ])->toArray();
            if ($list) {
                $log       .= '预计总量: ' . count($list) . PHP_EOL;
                $page_size  = 100;
                $log       .= '每' . $page_size . '个为一组' . PHP_EOL;
                $child_list = array_chunk($list, $page_size);
                $log       .= '共计' . count($child_list) . '组' . PHP_EOL;
                foreach ($child_list as $i => $child) {
                    $log      .= '----- 第【 ' . (++ $i) . ' 】 组数据为: ' . json_encode($child, JSON_UNESCAPED_UNICODE) . '-----' . PHP_EOL;

                    //调取快递接口中获取运单号妥投日期
                    $pno_list = array_column($child, 'express_sn');
                    $data     = WmsOutStorageService::getInstance()->getExpressDate($pno_list);

                    if (empty($data)) {
                        $log .= '未查询到运单号妥投日期信息' . PHP_EOL;
                        continue;
                    }

                    $has_finished_data = array_column($data, 'finishedDate', 'pno');
                    foreach ($child as $item) {
                        //若运单号无妥投日期跳过
                        if (empty($has_finished_data[$item['express_sn']])) {
                            $log .= $item['express_sn'] . '无妥投日期，不处理跳过' . PHP_EOL;
                            continue;
                        }

                        $box_model = MaterialWmsOutStorageBoxModel::findFirst([
                            'conditions' => 'id = :id: and status = :status:',
                            'bind'       => ['id' => $item['id'], 'status' => MaterialWmsEnums::MATERIAL_WMS_BOX_STATUS_NOT_DELIVERED,],
                        ]);

                        //非未妥投单据跳过不处理
                        if (empty($box_model)) {
                            $log .= $item['express_sn'] . '非未妥投，不处理跳过' . PHP_EOL;
                            continue;
                        }
                        $box_update_data = [
                            'status'        => MaterialWmsEnums::MATERIAL_WMS_BOX_STATUS_DELIVERED,
                            'delivery_date' => $has_finished_data[$item['express_sn']],
                            'updated_at'    => date('Y-m-d H:i:s'),
                        ];
                        $bool = $box_model->i_update($box_update_data);
                        $log .= $item['express_sn'] . '变更妥投状态、妥投日期：' . ($bool ? '成功' : ('失败，数据是：' . json_encode($box_update_data, JSON_UNESCAPED_UNICODE) . '；可能的原因是：' . get_data_object_error_msg($box_model))) . PHP_EOL;
                    }
                }
            } else {
                $log .= '暂无数据需要处理' . PHP_EOL;
            }
        } catch (\Exception $e) {
            $log          .= $e->getMessage() . PHP_EOL;
            $is_exception = true;
        }

        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        if ($is_exception) {
            $this->logger->error('变更耗材出库单-箱单号、运单号-妥投状态、妥投日期任务: ' . $log);
        } else {
            $this->logger->info($log);
        }
        echo $log;
    }
}
