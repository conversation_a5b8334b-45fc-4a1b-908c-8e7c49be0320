<?php
/**
 * Created by PhpStorm.
 * User: z<PERSON><PERSON>
 * Date: 2020/10/26
 * Time: 11:49
 */


use AliyunMNS\Client;
use AliyunMNS\Exception\MessageNotExistException;
use AliyunMNS\Exception\MnsException;
use App\Modules\Common\Models\EnvModel;


abstract class MnsBaseTask extends \BaseTask
{
    protected $accessId;
    protected $accessKey;
    protected $endPoint;
    protected $queueName;
    protected $logger;
    protected $begintime;

    //子类去实现,这个接口,是否需要退出,默认运行2小时退出
    protected function beforeGetMsg()
    {
        $curtime = time();
        $chatime = $curtime - $this->begintime;
        if ($chatime > 7200) {
            $this->logger->info("force exit for running timeout");
            return true;
        }
        return false;
    }

    //子类去实现,这个接口,处理消息主逻辑 true表示成功,可以删除消息了
    abstract protected function processOneMsg($msgBody);


    protected function init()
    {
        //系统公共的必须的
        $accessId        = EnvModel::getEnvByCode('mns_access_id');
        $accessKey       = EnvModel::getEnvByCode('mns_access_key');
        $endPoint        = EnvModel::getEnvByCode('mns_end_point');
        $this->accessId  = $accessId;
        $this->accessKey = $accessKey;
        $this->endPoint  = $endPoint;
        $this->client    = new Client($this->endPoint, $this->accessId, $this->accessKey);
        $this->begintime = time();
        $this->initSignalHandler();

    }


    public function mainAction()
    {
        $this->init();
        if (empty($this->queueName)) {
            throw new Exception('queueName undefind');
            return;
        }
        if (empty($this->accessId) || empty($this->accessKey) || empty($this->endPoint)) {
            throw new Exception("Must Provide AccessId/AccessKey/EndPoint to Run the task");
            return;
        }

        $client = new Client($this->endPoint, $this->accessId, $this->accessKey);


        $queue = $client->getQueueRef($this->queueName, false);

        while (1) {
            $needExit = $this->beforeGetMsg();
            if ($needExit) {
                break;
            }
            pcntl_signal_dispatch();

            $receiptHandle = null;
            try {
                // when receiving messages, it's always a good practice to set the waitSeconds to be 30.
                // it means to send one http-long-polling request which lasts 30 seconds at most.
                $res           = $queue->receiveMessage(30);
                $receiptHandle = $res->getReceiptHandle();
            } catch (MessageNotExistException $e) {
                $this->logger->info("MessageNotExist");
                sleep(2);
                continue;
            } catch (MnsException $e) {
                $this->logger->error("process ReceiveMessage  has MnsException: " . $e->getTraceAsString() . $e->getCode() . $e->getMessage());
                sleep(5);
                continue;
            }

            if ($res->isSucceed()) {
                $msgBody    = $res->getMessageBody();
                $message_id = $res->getMessageId();
                $processSuc = $this->processOneMsg($msgBody);
                if ($processSuc) {
                    // 4. delete message
                    try {
                        $queue->deleteMessage($receiptHandle);
                        $this->logger->info("DeleteMessage Succeed!" . 'msg_id:' . $message_id . ',msg_content:' . base64_decode($msgBody));
                    } catch (MnsException $e) {
                        $this->logger->error("DeleteMessage Fail: " . 'msg_id:' . $message_id . ',msg_content:' . base64_decode($msgBody) . 'error_info:' . $e);
                        sleep(5);
                        continue;
                    }
                }
            } else {
                $this->logger->error("isSucceed  is fail");
                sleep(5);
                continue;
            }
        }
    }

    protected function initSignalHandler()
    {
        // 注册
        pcntl_signal(SIGHUP, [$this, 'signalHandler']);
        pcntl_signal(SIGINT, [$this, 'signalHandler']);
        pcntl_signal(SIGTERM, [$this, 'signalHandler']);
    }

    /**
     * @param $signo
     */
    private function signalHandler($signo)
    {
        switch ($signo) {
            case SIGHUP:
            case SIGINT:
            case SIGTERM:
                exit('退出');
                break;
            default:
                // 处理所有其他信号
                break;
        }
    }

}