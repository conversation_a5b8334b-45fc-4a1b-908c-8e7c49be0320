<?php
/**
 * Author: Bruce
 * Date  : 2021-11-23 10:46
 * Description:
 */

use App\Modules\Extinguisher\Models\ExtinguisherBaseModel;
use App\Modules\Extinguisher\Models\ExtinguisherStaffRelationModel;

use App\Modules\Extinguisher\Models\ExtinguisherTaskModel;
use App\Modules\Extinguisher\Models\ExtinguisherTaskStaffRelationModel;
use App\Modules\Extinguisher\Models\HrStaffInfoModel;
use App\Modules\Extinguisher\Services\PushService;
use App\Models\backyard\SysStoreModel;
use App\Models\backyard\HrStaffItemsModel;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\ExtinguisherEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\Enums;
use App\Modules\Extinguisher\Models\HrStaffInfoPositionModel;

class ExtinguisherTaskCreateTask extends BaseTask
{
    // 日志输出
    protected static $output_log = PHP_EOL;

    public static $department_arr = [4, 13, 34];
    public static $position_arr = [2,18];
    public static $roleId = 30;

    public function store_extinguisher_taskAction()
    {
        $logger = $this->getDI()->get('logger');

        echo date('Ymd H:i:s')." 脚本开始执行".PHP_EOL;
        $logger->info(date('Ymd H:i:s')." 脚本开始执行");

        $store_arr = $this->getExtinguisherInfo();
        echo date('Ymd H:i:s')." 脚本创建任务完成".PHP_EOL;
        $logger->info(date('Ymd H:i:s')." 脚本创建任务完成");

        $logger->info(date('Ymd H:i:s')." 脚本开始发送消息");
        echo date('Ymd H:i:s')." 脚本开始发送消息".PHP_EOL;

        $logger->info(date('Ymd H:i:s') . ' 共有 ' . count($store_arr) . ' 个网点');
        echo date('Ymd H:i:s') . ' 共有 ' . count($store_arr) . ' 个网点' . PHP_EOL;

        if(!$store_arr){
            $logger->info(date('Ymd H:i:s')." 脚本停止发送消息：没有网点信息");
            exit(date('Ymd H:i:s')." 脚本停止发送消息：没有网点信息".PHP_EOL);
        }

        $push_staff_ids = $this->getStaffIdsByDepartmentRoleStore(self::$department_arr, self::$position_arr, $store_arr);

        $logger->info(date('Ymd H:i:s') . ' 共有 ' . count($push_staff_ids) . ' 个员工');
        echo date('Ymd H:i:s') . ' 共有 ' . count($push_staff_ids) . ' 个员工' . PHP_EOL;

        $this->sendMsg($push_staff_ids);
        $logger->info(date('Ymd H:i:s')." 脚本发送消息完成");
        exit(date('Ymd H:i:s')." 脚本发送消息完成".PHP_EOL);
    }

    public function getExtinguisherInfo()
    {
        $db = $this->getDI()->get('db_backyard');
        $logger = $this->getDI()->get('logger');
        $add_hour = $this->config->application->add_hour;
        $check_month = gmdate('Y-m', time() + $add_hour  * 3600 );
        try {
            $db->begin();

            $count = $this->getExtinguisherBaseCount();
            if($count == 0) {
                $logger->info(date('Ymd H:i:s')." 脚本结束执行，暂无灭火器信息");
                echo date('Ymd H:i:s')." 脚本结束执行，暂无灭火器信息".PHP_EOL;
                return false;
            }
            //查询当月是否已经创建任务，创建过的不再创建
            $issetTaskInfo = $this->getExtinguisherTaskInfoByMonth($check_month);
            if($issetTaskInfo) {
                $issetTaskInfoExtinguisherId = array_column($issetTaskInfo, 'id','extinguisher_info_id');
            }

            $storeIdAll  = [];
            $page = 1;
            $page_size = 1000;
            $countPage = ceil( $count / $page_size);
            while($page <= $countPage) {
                //获取灭火器信息
                $extinguisherInfoAll = $this->getExtinguisherBaseAllInfo($page, $page_size);
                $taskInfoAll = [];
                foreach ($extinguisherInfoAll as $oneExtinguisher){
                    if(!isset($issetTaskInfoExtinguisherId[$oneExtinguisher['id']])) {//当月不存在，该灭火器任务，则新增。
                        $extinguisherBaseJson['asset_code'] = $oneExtinguisher['asset_code'];
                        $extinguisherBaseJson['longitude'] = $oneExtinguisher['longitude'];
                        $extinguisherBaseJson['latitude'] = $oneExtinguisher['latitude'];
                        $extinguisherBaseJson['coordinate'] = $oneExtinguisher['coordinate'];
                        $extinguisherBaseJson['weight'] = $oneExtinguisher['weight'];
                        $extinguisherBaseJson['photo_url'] = $oneExtinguisher['photo_url'];
                        $extinguisherBaseJson['operator_id'] = $oneExtinguisher['operator_id'];
                        $extinguisherBaseJson['deleted'] = $oneExtinguisher['deleted'];
                        $taskInfo['extinguisher_base_json']= json_encode($extinguisherBaseJson);

                        $taskInfo['extinguisher_info_id'] = $oneExtinguisher['id'];
                        $taskInfo['store_id']             = $oneExtinguisher['store_id'];
                        $taskInfo['extinguisher_code']    = $oneExtinguisher['extinguisher_code'];
                        $taskInfo['type']                 = $oneExtinguisher['type'];
                        $taskInfo['check_month'] = $check_month;
                        $taskInfoAll[] = $taskInfo;
                    }
                }
                //插入任务信息
                if($taskInfoAll) {
                    $this->batch_insert_execute('extinguisher_task', $taskInfoAll);
                }
                //取出新建任务的灭火器信息id
                $extinguisherInfoAllId = array_column($taskInfoAll, 'extinguisher_info_id');
                //获取灭火器与管理员关联关系
                $staffInfo = $this->getExtinguisherStaffRelation($extinguisherInfoAllId);
                $newStaffInfo = [];
                foreach ($staffInfo as $staffKey => $oneStaff)
                {
                    $newStaffInfo[$oneStaff['extinguisher_info_id']][] = $oneStaff['staff_id'];
                }

                //获取新创建的任务信息：任务id
                $taskInfoList = $this->getExtinguisherTaskInfo($extinguisherInfoAllId, $check_month);
                if(!$taskInfoList) {
                    echo date('Ymd H:i:s')." 脚本结束执行，未获取到灭火器任务信息：id" . PHP_EOL;
                    $logger->info(date('Ymd H:i:s')." 脚本结束执行，未获取到灭火器任务信息：id");
                    return false;
                }
                //取出新建任务的id 与灭火器id
                $taskInfoToId = array_column($taskInfoList, 'id', 'extinguisher_info_id');
                //构造staff_id 与 extinguisher_task_id；进行归档
                $allTaskStaffInfo = [];
                foreach ($newStaffInfo as $oneKey => $oneItem) {
                    foreach ($oneItem as $item) {
                        if(isset($taskInfoToId[$oneKey])) {
                            $taskStaffInfo['extinguisher_task_id'] = $taskInfoToId[$oneKey];
                            $taskStaffInfo['staff_id'] = $item;
                            $allTaskStaffInfo[] = $taskStaffInfo;
                        }
                    }
                }
                if($allTaskStaffInfo) {
                    $this->batch_insert_execute('extinguisher_task_staff_relation', $allTaskStaffInfo);
                }
                $storeIdAll[] = array_unique(array_column($taskInfoAll, 'store_id'));
                $page++;
            }
            $db->commit();
            $uniStoreId = [];
            foreach ($storeIdAll as $oneBatch) {
                foreach ($oneBatch as $oneStore){
                    $uniStoreId[] = $oneStore;
                }
            }
            return array_values(array_unique($uniStoreId));
        } catch (\Exception $e) {
            $db->rollback();
            $_err_msg = '创建灭火器任务异常 - ' . $e->getMessage();
            $this->logger->error($_err_msg);
            self::$output_log .= $_err_msg . PHP_EOL;
            return false;
        }
    }

    /**
     * 获取灭火器信息
     * @param $page
     * @param $page_size
     * @return mixed
     */
    public function getExtinguisherBaseAllInfo($page, $page_size)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('*');
        $builder->from(ExtinguisherBaseModel::class);
        $builder->where('deleted = 0');
        $builder->limit($page_size, $page_size * ($page - 1));
        $builder->orderBy('id');
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 获取灭火器任务信息
     * @param $extinguisherIds
     * @param $checkMonth
     * @return mixed
     */
    public function getExtinguisherTaskInfo($extinguisherIds, $checkMonth)
    {
        if (empty($extinguisherIds)) {
            return [];
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->columns('id, extinguisher_info_id');
        $builder->from(ExtinguisherTaskModel::class);
        $builder->where("check_month = :check_month:", ['check_month' => $checkMonth]);
        $builder->andWhere("deleted = 0 AND extinguisher_info_id IN ({extinguisher_ids:array})", ['extinguisher_ids' => $extinguisherIds]);
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 获取当月灭火器任务信息
     * @param $checkMonth
     * @return mixed
     */
    public function getExtinguisherTaskInfoByMonth($checkMonth)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('id, extinguisher_info_id');
        $builder->from(ExtinguisherTaskModel::class);
        $builder->where("check_month = :check_month:", ['check_month' => $checkMonth]);
        $builder->andWhere("deleted = 0");
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 获取有效灭火器信息数量
     * @return int
     */
    public function getExtinguisherBaseCount()
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('*');
        $builder->from(ExtinguisherBaseModel::class);
        $builder->where('deleted = 0');
        return $builder->getQuery()->execute()->count();
    }

    /**
     * 查询灭火器管理人员与灭火器关联信息
     * @param $extinguisherIds
     * @return mixed
     */
    public function getExtinguisherStaffRelation($extinguisherIds)
    {
        if (empty($extinguisherIds)) {
            return [];
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->columns('extinguisher_info_id, staff_id');
        $builder->from(ExtinguisherStaffRelationModel::class);
        $builder->where("deleted = 0 AND extinguisher_info_id IN ({extinguisher_ids:array})", ['extinguisher_ids' => $extinguisherIds]);
        return $builder->getQuery()->execute()->toArray();
    }
    /**
     * @param $table
     * @param array $data
     * @return bool
     */
    public function batch_insert_execute($table, array $data){
        if (empty($data)) {
            return false;
        }
        $keys = array_keys(reset($data));
        $keys = array_map(function ($key) {
            return "`{$key}`";
        }, $keys);
        $keys = implode(',', $keys);
        $sql = "INSERT INTO " . $table . " ({$keys}) VALUES ";
        foreach ($data as $v) {
            $v = array_map(function ($value) {
                if ($value === null) {
                    return 'NULL';
                } else {
                    $value = addslashes($value); //处理特殊符号，如单引号
                    return "'{$value}'";
                }
            }, $v);
            $values = implode(',', array_values($v));
            $sql .= " ({$values}), ";
        }
        $sql = rtrim(trim($sql), ',');
        //DI中注册的数据库服务名称为"db"
        $result = $this->getDI()->get('db_backyard')->execute($sql);
        if (!$result) {
            return false;
        }
        return $result;
    }

    /**
     * 调用push接口发送站内信和消息
     * @param $staff_arr
     */
    function sendMsg($staff_arr)
    {
        try {
            if (!empty($staff_arr)) {
                $staff_ids = array_chunk($staff_arr, 1000);
                foreach ($staff_ids as $one_staff) {
                    PushService::getInstance()->sendMessage($one_staff);  //站内信
                    PushService::getInstance()->sendPush($one_staff);  //消息
                }
            }
        } catch (\Exception $e) {
            $this->logger->error('push_extinguisher_msg-failed:' . $e->getMessage());
        }
    }


    /**
     * 未检查灭火器PUSH提醒
     * @param int $post_staff_arr 发送人信息
     * @param array $staff_info 发送人下属信息
     **/
    function notCheckedSendMsg($post_staff_arr, $staff_info)
    {
        try {
            if (!empty($post_staff_arr)) {
                $title       = 'extinguisher_not_checked_send_title';
                $content     = 'extinguisher_not_checked_send_content';
                $content_msg = 'extinguisher_not_checked_send_content_msg';
                $data        = [
                    'staff_info' => $staff_info['staff_id'] . '+' . $staff_info['name'],
                    'store_name' => $staff_info['store_id'] = Enums::HEAD_OFFICE_STORE_FLAG ? Enums::PAYMENT_HEADER_STORE_NAME : $staff_info['store_name'],
                    'end_date'   => date('Y-m-07', time()),
                ];
                PushService::getInstance()->sendMessage([$post_staff_arr], $title, $content_msg, $data);  //站内信
                PushService::getInstance()->sendPush([$post_staff_arr], $title, $content, $data);  //消息
            }
        } catch (\Exception $e) {
            $this->logger->error('push_not_checked_send_msg failed:' . $e->getMessage());
        }
    }

    /**
     * 未检查灭火器PUSH提醒 灭火器管理员 的直线上级提醒
     * php cli.php  extinguisher_task_create extinguisher_not_checked_msg
     **/
    public function extinguisher_not_checked_msgAction()
    {
        try {
            $log = date('Ymd H:i:s') . '脚本开始执行' . PHP_EOL;
            //查询未检查灭火器数据 管理员id集合
            $staff_id_arr = $this->getExtinguisherTask();
            $log          .= date('Ymd H:i:s') . '查询未检查灭火器数据为:' . count($staff_id_arr) . '条' . PHP_EOL;
            if (!empty($staff_id_arr)) {
                //查询他们的上级
                $manger_ids = $this->getMangerIds($staff_id_arr);
                $log        .= date('Ymd H:i:s') . '查询符合的直线上级:' . count($manger_ids) . '条 数据为：' . json_encode($manger_ids, JSON_UNESCAPED_UNICODE) . PHP_EOL;
                if (!empty($manger_ids)) {
                    foreach ($staff_id_arr as $staff) {
                        if ($manger_ids[$staff['staff_id']]) {
                            $log .= date('Ymd H:i:s') . ' 开始准备发送push: 上级id是' . $manger_ids[$staff['staff_id']]['value'] . '条 数据为：' . json_encode($staff, JSON_UNESCAPED_UNICODE) . PHP_EOL;
                            $this->notCheckedSendMsg($manger_ids[$staff['staff_id']]['value'], $staff);
                        }
                    }
                }
            } else {
                $log .= date('Ymd H:i:s') . '暂无数据：' . PHP_EOL;
            }
            $this->logger->info($log);
        } catch (\Exception $e) {
            $this->logger->error('extinguisher_not_checked_msg failed:' . $e->getMessage());
        }
    }


    /**
     * 未检查灭火器 数据
     * @return array
     **/
    public function getExtinguisherTask()
    {
        $add_hour   = $this->config->application->add_hour;
        $begin_time = date('Y-m-01 00:00:00');//当前时区的年月日
        $moon_start = date('Y-m-d H:i:s', strtotime($begin_time) - $add_hour * 3600);
        $end_time   = date('Y-m-03 10:00:00');//当前时区的年月日
        $moon_end   = date('Y-m-d H:i:s', strtotime($end_time) - $add_hour * 3600);
        echo '查询起始时间为:' . $moon_start . ' 结束时间为:' . $moon_end . PHP_EOL;
        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            'staff.staff_id',
            'hsi.name',
            'et.store_id',
            'ss.name as store_name'
        ]);
        $builder->from(['staff' => ExtinguisherTaskStaffRelationModel::class]);
        $builder->leftjoin(ExtinguisherTaskModel::class, 'staff.extinguisher_task_id = et.id', 'et');
        $builder->leftjoin(HrStaffInfoModel::class, 'hsi.staff_info_id = staff.staff_id', 'hsi');
        $builder->leftjoin(SysStoreModel::class, 'ss.id = et.store_id', 'ss');
        $builder->Where('et.deleted = :deleted:', ['deleted' => GlobalEnums::IS_NO_DELETED]);
        $builder->andWhere('et.status = :status:', ['status' => ExtinguisherEnums::EXTINGUISHER_CHECKOUT_STATUS_NOT]);
        $builder->betweenWhere('et.created_at', $moon_start, $moon_end);
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 用户的直线上级 且在职的数据
     * @param array $staff_info_id 需要查询直线上级用户id
     * @return array
     **/
    public function getMangerIds(array $staff_info_id)
    {
        $ids       = array_values(array_unique(array_column($staff_info_id, 'staff_id')));
        $staff_arr = [];
        if (!empty($ids)) {
            $builder = $this->modelsManager->createBuilder();
            $builder->columns([
                'hi.value,hi.staff_info_id'
            ]);
            $builder->from(['hi' => HrStaffItemsModel::class]);
            $builder->leftjoin(HrStaffInfoModel::class, 'his.staff_info_id = hi.value', 'his');
            $builder->Where('his.state = :state: and wait_leave_state = :wait_leave_state: and  hi.item = :item:', ['state' => StaffInfoEnums::STAFF_STATE_IN, 'wait_leave_state' => StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_NO, 'item' => 'MANGER']);
            $builder->inWhere('hi.staff_info_id', $ids);
            $staff_items_arr = $builder->getQuery()->execute()->toArray();
            $staff_arr       = array_column($staff_items_arr, null, 'staff_info_id');
        }
        return $staff_arr;
    }

    /**
     * 获取发送消息员工id
     * @param array $department_arr
     * @param $role_arr
     * @param $store_arr
     * @return array
     */
    function getStaffIdsByDepartmentRoleStore(array $department_arr, $role_arr, $store_arr)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('hsi.staff_info_id');
        $builder->from(['hsi' => HrStaffInfoModel::class]);
        $builder->leftJoin(hrStaffInfoPositionModel::class, 'hsi.staff_info_id = hsip.staff_info_id', 'hsip');
        $builder->where(
            'hsi.state = :state: AND hsi.formal = :formal: AND hsi.is_sub_staff = :is_sub_staff:',
            [
                'state'        => StaffInfoEnums::STAFF_STATE_IN,
                'formal'       => StaffInfoEnums::FORMAL_IN,
                'is_sub_staff' => StaffInfoEnums::IS_SUB_STAFF_NO
            ]
        );
        $builder->inWhere('hsi.sys_store_id', $store_arr);
        $builder->inWhere('hsip.position_category', $role_arr);
        $builder->inWhere('hsi.sys_department_id', $department_arr);
        $staff_list = $builder->getQuery()->execute()->toArray();
        return array_unique(array_column($staff_list, 'staff_info_id'));


    }
}
