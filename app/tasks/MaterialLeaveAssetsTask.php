<?php

use App\Library\BaseService;
use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\MaterialEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\ErrCode;
use App\Models\backyard\HrJobTitleModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\SysDepartmentModel;
use App\Models\backyard\SysManagePieceModel;
use App\Models\backyard\SysStoreModel;
use App\Models\oa\MaterialLeaveAssetsDetailModel;
use App\Models\oa\MaterialLeaveAssetsModel;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Material\Services\LeaveAssetsExcelService;
use app\modules\Rpc\callback\LeaveAsset;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Material\Services\LeaveAssetsService;
use App\Modules\Material\Models\MaterialAssetsModel;
use App\Modules\Material\Models\MaterialAssetTransferLogModel;

/**
 * 离职资产相关任务
 * Class MaterialLeaveAssetsTask
 */
class MaterialLeaveAssetsTask extends BaseTask
{
    public $staff_leave_excel_max = 50000;
    public $auto_process_once_max = 500;//自动处理资产每批次数

    /**
     * 资产部定邮, 每天早上发送前一天离职,停职,全量待离职员工资产 到资产部(前一天10点-今天9:59:59, 上午10点后跑)
     * php app/cli.php material_leave_assets send_mail_leave_staff
     * @param date $params_date 指定日期 Y-m-d
     */
    public function send_mail_leave_staffAction($params_date)
    {
        try {
            $custom_date = $params_date[0] ?? '';
            if (empty($custom_date)) {
                //默认查询前一天10点到今天9:59:59
                $last_date = date('Y-m-d', strtotime('-1 day'));
                $start_date = $last_date . ' 10:00:00';
                $end_date = date('Y-m-d 09:59:59');
            } else {
                //指定开始时间,就查询指定时间的前一天到指定日期的10:00:00
                $last_date = date('Y-m-d', strtotime('-1 day', strtotime($custom_date)));
                $start_date = $last_date . ' 10:00:00';
                $end_day = date('Y-m-d', strtotime('+1 day', strtotime($last_date)));
                $end_date = $end_day . ' 09:59:59';
            }
            $all_staff = $this->getLeaveStaff($start_date, $end_date);
            //查询资产数据
            //查询资产数据
            //起到去重的作用
            $all_staff = array_column($all_staff, null, 'staff_info_id');
            $all_staff_ids = array_values(array_keys($all_staff));
            if (empty($all_staff)) {
                echo '待离职,停职,离职员工为空 start_date:' . $start_date . ' end_date:' . $end_date . ' ,执行时间:' . date('Y-m-d H:i:s') . PHP_EOL;
                $this->logger->info('send_mail_leave_staff 待离职,停职,离职员工为空, start_date:' . $start_date . ' end_date:' . $end_date . ' ,执行时间:' . date('Y-m-d H:i:s'));
                exit;
            }
            //分批取数据
            $count = $this->getLeaveAssetsCount($all_staff_ids);
            if ($count > $this->staff_leave_excel_max) {
                echo '待离职,停职,离职员工 离职资产超过' . $this->staff_leave_excel_max . '条, start_date:' . $start_date . ' end_date:' . $end_date . ' ,执行时间:' . date('Y-m-d H:i:s') . PHP_EOL;
                $this->logger->warning('send_mail_leave_staff 待离职,停职,离职员工 离职资产超过' . $this->staff_leave_excel_max . '条, start_date:' . $start_date . ' end_date:' . $end_date . ' ,执行时间:' . date('Y-m-d H:i:s'));
                exit;
            }
            $page_size = 2000;
            $step = ceil($count / $page_size);
            $all_asset_data = [];
            for ($i = 1; $i <= $step; $i++) {
                $page_num = $i;
                $assets_data = $this->getLeaveAssets($page_size, $page_num, $all_staff_ids, $count);
                $all_asset_data = array_merge($all_asset_data, $assets_data);
            }
            $leave_asset_data_kv = [];
            foreach ($all_asset_data as $asset_info) {
                $leave_asset_data_kv[$asset_info['staff_info_id']][] = $asset_info;
            }
            //取中英翻译
            $zh_translation = BaseService::getTranslation('zh');
            $en_translation = BaseService::getTranslation('en');
            //生成最终数据
            $country_code = get_country_code();
            $excel_data = [];
            foreach ($all_staff as $staff_info) {
                $staff_leave_asset_data = $leave_asset_data_kv[$staff_info['staff_info_id']] ?? [];
                //在职状态处理
                $staff_state = ($staff_info['state'] == StaffInfoEnums::STAFF_STATE_IN && $staff_info['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES)
                    ? StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE
                    : $staff_info['state'];
                $staff_state = StaffInfoEnums::$staff_state[$staff_state] ?? '';
                //离职来源翻译key处理
                if ($country_code == GlobalEnums::LA_COUNTRY_CODE && $staff_info['leave_source'] == StaffInfoEnums::LEAVE_SOURCE_NO_ATTENDANCE) {
                    $leave_translate_key = StaffInfoEnums::TRANSLATE_LEAVE_SOURCE_NO_ATTENDANCE_LA;
                } else {
                    $leave_translate_key = StaffInfoEnums::$leave_source[$staff_info['leave_source']] ?? '';
                }
                $tmp_data = [];
                $tmp_data['staff_info_id'] = $staff_info['staff_info_id'];
                $tmp_data['staff_name'] = $staff_info['name'];
                $tmp_data['job_name'] = $staff_info['job_name'];
                $tmp_data['store_name'] = $staff_info['store_name'];
                $tmp_data['department_name'] = $staff_info['department_name'];
                $tmp_data['piece_name'] = $staff_info['piece_name'];
                $tmp_data['region_name'] = $staff_info['region_name'];
                $tmp_data['staff_state'] = $zh_translation->_($staff_state) . ' ' . $en_translation->_($staff_state);
                $tmp_data['leave_source'] = $zh_translation->_($leave_translate_key) . ' ' . $en_translation->_($leave_translate_key);
                $tmp_data['leave_date'] = $staff_info['leave_date'] ?? '';
                if (!empty($staff_leave_asset_data)) {
                    //有资产的情况
                    foreach ($staff_leave_asset_data as $leave_asset_info) {
                        $tmp_data['own_asset'] = 'Y';
                        $tmp_data['asset_code'] = $leave_asset_info['asset_code'];
                        $tmp_data['sn_code'] = $leave_asset_info['sn_code'];
                        $tmp_data['asset_name'] = $leave_asset_info['asset_name_zh'] . ' ' . $leave_asset_info['asset_name_en'];
                        $tmp_data['purchase_price'] = $leave_asset_info['purchase_price'];
                        $tmp_data['net_value'] = $leave_asset_info['net_value'];
                        $excel_data[] = array_values($tmp_data);
                    }
                } else {
                    //无资产的情况
                    $tmp_data['own_asset'] = 'N';
                    $tmp_data['asset_code'] = '';
                    $tmp_data['sn_code'] = '';
                    $tmp_data['asset_name'] = '';
                    $tmp_data['purchase_price'] = '';
                    $tmp_data['net_value'] = '';
                    $excel_data[] = array_values($tmp_data);
                }
            }
            //导出
            $header = [
                $zh_translation->_('material_leave_asset_email.staff_id'),//工号
                $zh_translation->_('material_leave_asset_email.staff_name'),//姓名
                $zh_translation->_('material_leave_asset_email.job_name'),//职位
                $zh_translation->_('material_leave_asset_email.sys_store_name'),//所属网点
                $zh_translation->_('material_leave_asset_email.node_department_name'),//所属部门
                $zh_translation->_('material_leave_asset_email.piece_name'),//所属片区
                $zh_translation->_('material_leave_asset_email.region_name'),//所属大区
                $zh_translation->_('material_leave_asset_email.staff_state'),//在职状态
                $zh_translation->_('material_leave_asset_email.leave_source'),//离职来源
                $zh_translation->_('material_leave_asset_email.leave_date'),//离职日期
                $zh_translation->_('material_leave_asset_email.own_asset'),//名下是否有资产
                $zh_translation->_('material_leave_asset_email.asset_code'),//资产编码
                $zh_translation->_('material_leave_asset_email.sn_code'),//sn码
                $zh_translation->_('material_leave_asset_email.asset_name'),//资产名称
                $zh_translation->_('material_leave_asset_email.purchase_price'),//采购价
                $zh_translation->_('material_leave_asset_email.net_value'),//净值
            ];
            $header_two = [
                $en_translation->_('material_leave_asset_email.staff_id'),//工号
                $en_translation->_('material_leave_asset_email.staff_name'),//姓名
                $en_translation->_('material_leave_asset_email.job_name'),//职位
                $en_translation->_('material_leave_asset_email.sys_store_name'),//所属网点
                $en_translation->_('material_leave_asset_email.node_department_name'),//所属部门
                $en_translation->_('material_leave_asset_email.piece_name'),//所属片区
                $en_translation->_('material_leave_asset_email.region_name'),//所属大区
                $en_translation->_('material_leave_asset_email.staff_state'),//在职状态
                $en_translation->_('material_leave_asset_email.leave_source'),//离职来源
                $en_translation->_('material_leave_asset_email.leave_date'),//离职日期
                $en_translation->_('material_leave_asset_email.own_asset'),//名下是否有资产
                $en_translation->_('material_leave_asset_email.asset_code'),//资产编码
                $en_translation->_('material_leave_asset_email.sn_code'),//sn码
                $en_translation->_('material_leave_asset_email.asset_name'),//资产名称
                $en_translation->_('material_leave_asset_email.purchase_price'),//采购价
                $en_translation->_('material_leave_asset_email.net_value'),//净值
            ];
            //把第二行表头压进数据数组
            array_unshift($excel_data, $header_two);
            $file_name = 'staffLeaveAssets' . date('Ymd') . '.xlsx';
            $export_excel = LeaveAssetsExcelService::getInstance()->exportExcel($header, $excel_data, $file_name);
            if (!$export_excel || !isset($export_excel['data'])) {
                echo '离职资产定邮生成附件失败 start_date:' . $start_date . ' end_date:' . $end_date . ' ,执行时间:' . date('Y-m-d H:i:s') . PHP_EOL;
                $this->logger->warning('send_mail_leave_staff 离职资产定邮生成附件失败 start_date:' . $start_date . ' end_date:' . $end_date . ' ,执行时间:' . date('Y-m-d H:i:s'));
                exit;
            }
            $str_emails = EnvModel::getEnvByCode('leave_asset_asset_department_email');
            if (empty($str_emails)) {
                echo '资产部邮箱为空 start_date:' . $start_date . ' end_date:' . $end_date . ' ,执行时间:' . date('Y-m-d H:i:s') . PHP_EOL;
                $this->logger->info('send_mail_leave_staff 资产部邮箱为空, start_date:' . $start_date . ' end_date:' . $end_date . ' ,执行时间:' . date('Y-m-d H:i:s'));
                exit;
            }
            $emails = explode(',', $str_emails);
            $last_ymd = date('Ymd', strtotime($last_date));
            $title = $country_code . $last_ymd . ' 新增停职/待离职/离职员工资产数据（Add asset data of pending employees）';
            $html_zh = '各位好:<br/>';
            $html_zh .= '<p>附件"' . $export_excel['data'] . '"为' . $start_date . ' 至 ' . $end_date . '新增的离职员工资产数据，请进行下载，请密切关注，及时去OA-物料/资产管理-离职资产处理进行查看和处理。</p>';
            $html_en = 'Dear all:<br/>';
            $html_en .= '<p>The attachment "' . $export_excel['data'] . '" is the newly added asset data of the demission employee from ' . $start_date . ' to ' . $end_date . '.Please download url and pay close attention to it and go to OA- Material/Asset Management - demission Asset Processing to check and deal with it in time.</p>';
            $html = $html_zh . '<br/>' . $html_en;
            $send_result = $this->mailer->sendAsync($emails, $title, $html);

            if ($send_result) {
                echo '离职资产定邮发送成功,收件邮箱:' . $str_emails . ', start_date:' . $start_date . ' end_date:' . $end_date . ' ,执行时间:' . date('Y-m-d H:i:s') . PHP_EOL;
                $this->logger->info('send_mail_leave_staff 离职资产定邮发送成功,收件邮箱' . $str_emails . ', start_date:' . $start_date . ' end_date:' . $end_date . ' ,执行时间:' . date('Y-m-d H:i:s'));
            } else {
                echo '离职资产定邮发送失败,收件邮箱:' . $str_emails . ', start_date:' . $start_date . ' end_date:' . $end_date . ' ,执行时间:' . date('Y-m-d H:i:s') . PHP_EOL;
                $this->logger->warning('send_mail_leave_staff 离职资产定邮发送失败,收件邮箱:' . $str_emails . ', start_date:' . $start_date . ' end_date:' . $end_date . ' ,执行时间:' . date('Y-m-d H:i:s'));
            }
        } catch(\Exception $e) {
            echo '离职资产定邮发送异常: code=' . $e->getCode() . ' message=' . $e->getMessage() . ' , 执行时间:' . date('Y-m-d H:i:s') . PHP_EOL;
            $this->logger->error('离职资产定邮发送异常: code=' . $e->getCode() . ' message=' . $e->getMessage() . ' trace=' . $e->getTraceAsString() . ' , 执行时间:' . date('Y-m-d H:i:s'));
            exit();
        }
    }

    /**
     * 查询所有待离职 和 指定时间的离职/停职员工
     * @param $start_date
     * @param $end_date
     * @return array
     */
    public function getLeaveStaff($start_date, $end_date)
    {
        $staff_columns = 'staff_info_id, name, job_title, sys_store_id, node_department_id, state, wait_leave_state, leave_source, leave_date';
        //查询全部待离职,
        $wait_leave_staff = HrStaffInfoModel::find([
            'columns' => $staff_columns,
            'conditions' => 'state = :state: and wait_leave_state = :wait_leave_state:',
            'bind' => [
                'state' => StaffInfoEnums::STAFF_STATE_IN,
                'wait_leave_state' => StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES,
            ]
        ])->toArray();
        //查询前一天离职员工
        $leave_staff = HrStaffInfoModel::find([
            'columns' => $staff_columns,
            'conditions' => 'state = :state: and leave_date >= :leave_date_start: and leave_date <= :leave_date_end:',
            'bind' => [
                'state' => StaffInfoEnums::STAFF_STATE_LEAVE,
                'leave_date_start' => $start_date,
                'leave_date_end' => $end_date,
            ]
        ])->toArray();
        //查询前一天停职员工
        $stop_staff = HrStaffInfoModel::find([
            'columns' => $staff_columns,
            'conditions' => 'state = :state: and stop_duties_date >= :stop_duties_date_start: and stop_duties_date <= :stop_duties_date_end:',
            'bind' => [
                'state' => StaffInfoEnums::STAFF_STATE_STOP,
                'stop_duties_date_start' => $start_date,
                'stop_duties_date_end' => $end_date,
            ]
        ])->toArray();
        $all_staff = array_merge($wait_leave_staff, $leave_staff, $stop_staff);
        if (!empty($all_staff)) {
            //查员工附属信息
            $store_ids = array_values(array_unique(array_column($all_staff, 'sys_store_id')));
            $job_titles = array_values(array_unique(array_column($all_staff, 'job_title')));
            $node_department_ids = array_values(array_unique(array_column($all_staff, 'node_department_id')));
            $store_kv = [];
            $job_kv = [];
            $department_kv = [];
            if (!empty($store_ids)) {
                $store_data = $this->getStoreData($store_ids);
                $store_kv = array_column($store_data, null, 'id');
            }
            if (!empty($job_titles)) {
                $job_data = HrJobTitleModel::find([
                    'columns' => 'id, job_name',
                    'conditions' => 'id in ({ids:array})',
                    'bind' => [
                        'ids' => $job_titles,
                    ],
                ])->toArray();
                $job_kv = array_column($job_data, 'job_name', 'id');
            }
            if (!empty($node_department_ids)) {
                $department_data = SysDepartmentModel::find([
                    'columns' => 'id, name',
                    'conditions' => 'id in ({ids:array})',
                    'bind' => [
                        'ids' => $node_department_ids,
                    ],
                ])->toArray();
                $department_kv = array_column($department_data, 'name', 'id');
            }
            foreach ($all_staff as &$staff) {
                $staff['store_name'] = $store_kv[$staff['sys_store_id']]['name'] ?? '';
                $staff['piece_name'] = $store_kv[$staff['sys_store_id']]['piece_name'] ?? '';
                $staff['region_name'] = $store_kv[$staff['sys_store_id']]['region_name'] ?? '';
                $staff['job_name'] = $job_kv[$staff['job_title']] ?? '';
                $staff['department_name'] = $department_kv[$staff['node_department_id']] ?? '';
            }
        }
        return $all_staff;
    }

    /**
     * 获取员工的离职资产总数量
     * @param $staff_ids
     * @return int
     */
    public function getLeaveAssetsCount($staff_ids)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['main' => MaterialLeaveAssetsModel::class]);
        $builder->leftJoin(MaterialLeaveAssetsDetailModel::class, 'main.id = detail.leave_assets_id', 'detail');
        $builder->inWhere('main.staff_info_id', $staff_ids);
        $builder->andWhere('detail.is_deleted = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);
        $builder->columns('count(detail.id) as count');
        return (int)$builder->getQuery()->getSingleResult()->count;
    }

    /**
     * 获取员工的离职资产数据
     * @param $page_size
     * @param $page_num
     * @param $staff_ids
     * @param $all_count
     * @return array
     */
    public function getLeaveAssets($page_size, $page_num, $staff_ids, $all_count)
    {
        $offset = $page_size * ($page_num - 1);
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['main' => MaterialLeaveAssetsModel::class]);
        $builder->columns('main.staff_info_id, detail.asset_code, detail.sn_code, detail.asset_name_zh, detail.asset_name_en, detail.purchase_price, detail.net_value');
        $builder->leftJoin(MaterialLeaveAssetsDetailModel::class, 'main.id = detail.leave_assets_id', 'detail');
        $builder->inWhere('main.staff_info_id', $staff_ids);
        $builder->andWhere('main.is_valid = :is_valid:', ['is_valid' => MaterialEnums::LEAVE_ASSET_IS_VALID_YES]);
        $builder->andWhere('detail.is_deleted = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);
        $items = [];
        if ($all_count > 0) {
            $builder->limit($page_size, $offset);
            $builder->orderby('detail.id desc');
            $items = $builder->getQuery()->execute()->toArray();
        }
        return $items;
    }

    /**
     * 获取网点,大区,片区
     * @param $store_ids
     * @return array
     */
    public function getStoreData($store_ids)
    {
        //处理总部
        $is_have_head_office = array_search(Enums::HEAD_OFFICE_STORE_FLAG, $store_ids);
        if ($is_have_head_office !== false) {
            unset($store_ids[$is_have_head_office]);
            $store_ids = array_values($store_ids);
        }
        $store_data = [];
        if (!empty($store_ids)) {
            $store_data = SysStoreModel::find([
                'columns' => 'id, name, manage_piece, manage_region',
                'conditions' => 'id in ({ids:array})',
                'bind' => [
                    'ids' => $store_ids,
                ],
            ])->toArray();
            $manage_piece_ids = array_values(array_unique(array_column($store_data, 'manage_piece')));
            $manage_region_ids = array_values(array_unique(array_column($store_data, 'manage_region')));
            $piece_data_kv = [];
            if (!empty($manage_piece_ids)) {
                $piece_data = SysManagePieceModel::find([
                    'columns' => 'id, name',
                    'conditions' => 'id in ({ids:array})',
                    'bind' => [
                        'ids' => $manage_piece_ids,
                    ],
                ])->toArray();
                $piece_data_kv = array_column($piece_data, 'name', 'id');
            }
            $region_data_kv = [];
            if (!empty($manage_region_ids)) {
                $piece_data = SysManagePieceModel::find([
                    'columns' => 'id, name',
                    'conditions' => 'id in ({ids:array})',
                    'bind' => [
                        'ids' => $manage_region_ids,
                    ],
                ])->toArray();
                $region_data_kv = array_column($piece_data, 'name', 'id');
            }
            foreach ($store_data as &$store) {
                $store['piece_name'] = $piece_data_kv[$store['manage_piece']] ?? '';
                $store['region_name'] = $region_data_kv[$store['manage_region']] ?? '';
            }
        }
        //如果有总部, 把总部手动添加进去
        if ($is_have_head_office !== false) {
            $store_data[] = [
                'id' => Enums::HEAD_OFFICE_STORE_FLAG,
                'name' => Enums::PAYMENT_HEADER_STORE_NAME,
                'manage_piece' => 0,
                'manage_region' => 0,
                'piece_name' => '',
                'region_name' => '',
            ];
        }
        return $store_data;
    }

    /**
     * 补录离职资产
     * @param array $params  参数
     * 任务 php app/cli.php material_leave_assets retry_create_leave_assets
     * 参数 传引号包裹的json  '{"staff_info_id":"25568","source":"-1","operation_staff_id":"10000","staff_state":"2","leave_date":"2023-05-31","last_work_date":"2023-05-31","staff_resign_id":"0","work_handover":"0"}'
     */
    public function retry_create_leave_assetsAction($params)
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log = '任务名称: ' . $process_name . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s')  . PHP_EOL;

        try {
            if (empty($params[0])) {
                $log .= '参数无效' . $params[0];
                exit($log);
            }
            $params = $params[0];
            $params_arr = json_decode($params, true);
            $leave_asset_call_back = new LeaveAsset();
            $return = $leave_asset_call_back->createLeaveAsset(['locale' => 'en'], $params_arr);
            // 3.更新任务
            if (!empty($return['code']) && $return['code'] == ErrCode::$SUCCESS) {
                $log .= '重新生成离职资产成功, 参数:' . $params . '结果:' . json_encode($return, JSON_UNESCAPED_UNICODE) . PHP_EOL;
            } else {
                // 记录错误日志
                $log .= '重新生成离职资产失败, 参数:' . $params . '结果:' . json_encode($return, JSON_UNESCAPED_UNICODE) . PHP_EOL;
                $this->logger->warning("补录离职资产失败 " . $log);
            }

        }catch (Exception $e) {
            $log .= $e->getMessage() . PHP_EOL;
            $log .= '重新生成离职资产失败, 参数' . $params . '结果:' . json_encode($return ?? [], JSON_UNESCAPED_UNICODE) . PHP_EOL;
            $this->logger->error("补录离职资产失败 " . $log);
        }
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $this->logger->info("补录离职资产结果 " . $log);
        exit($log);
    }

    /**
     * 修复离职资产总扣费金额错误
     * @param array $params 参数
     * 任务 php app/cli.php material_leave_assets update_leave_assets_all_deduct_amount
     * 参数 最大id
     */
    public function update_leave_assets_all_deduct_amountAction($params)
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log = '任务名称: ' . $process_name . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        try {
            $last_id = 0;
            if (!empty($params[0])) {
                if (!is_numeric($params[0])) {
                    exit('修复离职资产总扣费金额-参数错误' . $params[0]);
                }
                $last_id = $params[0];
            }

            $db = $this->getDI()->get('db_oa');
            $success = $error = [];


            do {
                $builder = $this->modelsManager->createBuilder();
                $builder->from(['main' => MaterialLeaveAssetsModel::class]);
                $builder->columns('main.id, main.staff_info_id, main.all_deduct_amount, sum(detail.deduct_amount) as detail_deduct_amount');
                $builder->leftjoin(MaterialLeaveAssetsDetailModel::class, 'detail.leave_assets_id = main.id and detail.is_deleted = 0', 'detail');
                $builder->andWhere('main.id > :last_id:', ['last_id' => $last_id]);
                $builder->andWhere('main.is_valid = :is_valid:', ['is_valid' => MaterialEnums::LEAVE_ASSET_IS_VALID_YES]);
                $builder->groupBy('main.id');
                $builder->orderBy('main.id asc');
                $builder->limit(1000);
                $items = $builder->getQuery()->execute()->toArray();
                foreach ($items as $v) {
                    $current_detail_deduct_amount = !empty($v['detail_deduct_amount']) ? $v['detail_deduct_amount'] : '0.00';
                    if (bccomp($v['all_deduct_amount'], $current_detail_deduct_amount, 2) !== 0) {
                        $result = $db->updateAsDict('material_leave_assets', ['all_deduct_amount' => $current_detail_deduct_amount], 'id = ' . $v['id']);
                        if ($result) {
                            $success[] = [
                                'before' => $v,
                                'after_amount' => $current_detail_deduct_amount
                            ];
                        } else {
                            $error[] = [
                                'before' => $v,
                                'after_amount' => $current_detail_deduct_amount
                            ];
                        }
                    }
                    $last_id = $v['id'];
                }
            } while (!empty($items));

            $this->logger->info('update_leave_assets_all_deduct_amount : 资产总扣费金额修复成功, data=' . json_encode($success, JSON_UNESCAPED_UNICODE));
            if (!empty($error)) {
                $this->logger->warning('update_leave_assets_all_deduct_amount error : 资产总扣费金额修复失败, data=' . json_encode($error, JSON_UNESCAPED_UNICODE));
            }
            $log .= '执行完成, 共处理' . count($success) . '条';
        } catch (Exception $e) {
            $log .= $e->getMessage() . PHP_EOL;
            $log .= '资产总扣费金额修复失败, 参数' . $params[0] . PHP_EOL;
            $this->logger->error("资产总扣费金额修复失败 " . $log);
        }
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        exit($log);
    }

    /**
     * 员工离职名下没有资产，资产处理状态自动改成已处理
     * php app/cli.php material_leave_assets auto_process
     */
    public function auto_processAction()
    {
        $this->checkLock(__METHOD__);
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log = '任务名称: ' . $process_name . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s')  . PHP_EOL;
        $is_exception = false;

        try {
            $company_ids = EnumsService::getInstance()->getSettingEnvValueIds('material_leave_asset_auto_process_company_ids');
            if ($company_ids) {
                $asset_list = MaterialLeaveAssetsModel::find([
                    'conditions' => 'is_valid = :is_valid: and asset_department_status in ({status:array}) and company_id in ({company_id:array})',
                    'bind' => ['is_valid' => MaterialEnums::LEAVE_ASSET_IS_VALID_YES, 'status' => [MaterialEnums::ASSET_DEPARTMENT_STATUS_TODO, MaterialEnums::ASSET_DEPARTMENT_STATUS_DOING] , 'company_id' => $company_ids]
                ])->toArray();
                if ($asset_list) {
                    $count = count($asset_list);
                    $log .= '本次获取到需要处理的离职员工数据总数为：' . $count . PHP_EOL;
                    if ($count <= $this->auto_process_once_max) {
                        $res = LeaveAssetsService::getInstance()->autoProcess($asset_list);
                        $log .= '不符合条件的员工离职资产组：【 ' . implode(PHP_EOL, $res['un_process_ids']) . ' 】' . PHP_EOL;
                        $log .= '处理成功的员工离职资产组：【 ' . implode(PHP_EOL, $res['process_success_ids']) . ' 】' . PHP_EOL;
                        $log .= '处理失败的员工离职资产组：【 ' . implode(PHP_EOL, $res['process_fail_ids']) . ' 】' . PHP_EOL;
                    } else {
                        $log .= '分批次处理' . PHP_EOL;
                        $i = 0;
                        do {
                            $this_process_data = array_slice($asset_list, $i, $this->auto_process_once_max);
                            $i += $this->auto_process_once_max;
                            if (!empty($this_process_data)) {
                                $res = LeaveAssetsService::getInstance()->autoProcess($this_process_data);
                                $log .= '第' . $i . '批次处理情况如下：' . PHP_EOL;
                                $log .= '不符合条件的员工离职资产组：【 ' . implode(PHP_EOL, $res['un_process_ids']) . ' 】' . PHP_EOL;
                                $log .= '处理成功的员工离职资产组：【 ' . implode(PHP_EOL, $res['process_success_ids']) . ' 】' . PHP_EOL;
                                $log .= '处理失败的员工离职资产组：【 ' . implode(PHP_EOL, $res['process_fail_ids']) . ' 】' . PHP_EOL;
                            }
                        } while (!empty($this_process_data));
                    }
                    $log .= '全部处理完毕' . PHP_EOL;
                } else {
                    $log .= '暂未查询到需要处理的离职资产' . PHP_EOL;
                }
            } else {
                $log .= '开启名下0离职资产的员工自动处理的BU未配置，无需处理' . PHP_EOL;
            }
        } catch (\Exception $e) {
            $log .= $e->getMessage() . PHP_EOL;
            $is_exception = true;
        }
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        if ($is_exception) {
            $this->logger->error('员工离职名下没有资产，资产处理状态自动改成已处理: ' . $log);
        } else {
            $this->logger->info($log);
        }
        $this->clearLock(__METHOD__);
        exit($log);
    }

    /**
     * 物料资产管理-离职资产处理-维护名下资产是否为0字段标识位
     * 资产台账里，使用人=该员工，资产数量-0 &资产转移表里，接收人=该员工，接收状态为=待接收，记录=0 & 离职资产里，资产数量=0
     * 三种情况都是0 才能标为1是，否则是2否
     * php app/cli.php material_leave_assets is_have_own_asset
     * @param $params
     */
    public function is_have_own_assetAction($params)
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log = '任务名称: ' . $process_name . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s')  . PHP_EOL;
        $log .= '初始内存: ' . memory_usage() . PHP_EOL;
        $is_exception = false;
        $month = $params[0] ?? '';
        $is_all = false;
        try {
            //第一步 首次上线需跑全量 、后期跑需要指定某个月份之前的范围内的数据
            if ($month) {
                $start = date('Y-m-d 00:00:00', strtotime('-' . $month . ' month'));
                $conditions = 'is_valid = :is_valid: and created_at between :start: and :end:';
                $bind = ['is_valid' => MaterialEnums::LEAVE_ASSET_IS_VALID_YES, 'start' => $start, 'end' => date('Y-m-d H:i:s')];
            } else {
                //不指定月份默认跑全部 - 仅上线的时候跑不指定
                $conditions = 'is_valid = :is_valid: and all_asset_number = :all_asset_number:';
                $bind = ['is_valid' => MaterialEnums::LEAVE_ASSET_IS_VALID_YES, 'all_asset_number' => 0];
                $is_all = true;
            }
            $leave_asset_list = MaterialLeaveAssetsModel::find(['columns' => 'staff_info_id, all_asset_number', 'conditions' => $conditions, 'bind' => $bind])->toArray();
            if ($leave_asset_list) {
                //默认获取范围内的所有工号
                $all_leave_staff_ids = $leave_staff_ids = array_column($leave_asset_list, 'staff_info_id');
                //若非全部初始化，需要找到资产数量=0的离职员工工号组
                if (!$is_all) {
                    $leave_staff_ids = [];
                    foreach ($leave_asset_list as $item) {
                        if ($item['all_asset_number']) {
                            continue;
                        }
                        $leave_staff_ids[] = $item['staff_info_id'];
                    }
                }
                $leave_staff_count = count($leave_staff_ids);
                //找到符合三种条件均为0的工号组
                $transfer_staff_ids = [];
                //第二步 离职资产里，资产数量=0 存在
                if ($leave_staff_count) {
                    $log .= '离职资产里资产数量=0的工号：共计' . $leave_staff_count . '个' . '，分别为【' . json_encode($leave_staff_ids, JSON_UNESCAPED_UNICODE) . '】' . PHP_EOL;
                    $page_size = 2000;
                    //第三步 资产台账表名下无资产的工号组
                    $material_assets_staff_ids = [];
                    $page = ceil($leave_staff_count / $page_size);
                    $material_asset_status = EnumsService::getInstance()->getSettingEnvValueIds('material_inventory_assets_status');
                    for ($i = 1; $i <= $page; $i++) {
                        $slice_leave_staff_ids = array_slice($leave_staff_ids, ($i - 1) * $page_size, $page_size);
                        $asset_list = MaterialAssetsModel::find([
                            'columns' => 'staff_id',
                            'conditions' => 'staff_id in ({staff_ids:array}) and is_deleted = :is_deleted: and status in ({status:array})',
                            'bind' => ['staff_ids' => $slice_leave_staff_ids, 'is_deleted' => GlobalEnums::IS_NO_DELETED, 'status' => $material_asset_status]
                        ])->toArray();
                        $material_assets_staff_ids = array_merge($material_assets_staff_ids, array_diff($slice_leave_staff_ids, array_unique(array_column($asset_list, 'staff_id'))));
                    }
                    //第四步 资产转移表里接收人=过滤后的账号组，接收状态为=待接收记录=0
                    $material_asset_staff_count = count($material_assets_staff_ids);
                    if ($material_asset_staff_count) {
                        $log .= '离职资产里资产数量=0的工号组 & 资产台账里名下资产数量=0的工号：共计' . $material_asset_staff_count . '个' . '，分别为【' . json_encode($material_assets_staff_ids, JSON_UNESCAPED_UNICODE) . '】' . PHP_EOL;
                        $page = ceil($material_asset_staff_count / $page_size);
                        for ($i = 1; $i <= $page; $i++) {
                            $slice_asset_staff_ids = array_slice($material_assets_staff_ids, ($i - 1) * $page_size, $page_size);
                            $transfer_list = MaterialAssetTransferLogModel::find([
                                'columns' => 'to_staff_id',
                                'conditions' => 'to_staff_id in ({to_staff_ids:array}) and is_deleted = :is_deleted: and status = :status:',
                                'bind' => ['to_staff_ids' => $slice_asset_staff_ids, 'is_deleted' => GlobalEnums::IS_NO_DELETED, 'status' => MaterialEnums::TRANSFER_LOG_STATUS_UNRECEIVED]
                            ])->toArray();
                            $transfer_staff_ids = array_merge($transfer_staff_ids, array_diff($slice_asset_staff_ids, array_unique(array_column($transfer_list, 'to_staff_id'))));
                        }

                        //第四步 找到符合三种条件均为0的工号进行 维护离职资产表 名下资产是否为0字段 标识位 为1是
                        if ($transfer_staff_ids) {
                            $log .= '离职资产里资产数量=0 & 资产台账里名下资产数量=0 & 转移记录里数量=0的工号：共计' . count($transfer_staff_ids) . '个' . '，分别为【' . json_encode($transfer_staff_ids, JSON_UNESCAPED_UNICODE) . '】' . PHP_EOL;
                            $transfer_staff_ids_str = implode(',', $transfer_staff_ids);
                            $bool = $this->getDI()->get('db_oa')->updateAsDict(
                                (new MaterialLeaveAssetsModel)->getSource(),
                                ['is_have_own_asset' => MaterialEnums::ALL_ASSET_NUMBER_IS_ZERO_YES],
                                ['conditions' => "staff_info_id in ({$transfer_staff_ids_str}) and all_asset_number = 0 and is_valid = " . MaterialEnums::LEAVE_ASSET_IS_VALID_YES]
                            );
                            $log .= '变更符合条件的离职员工名下资产是否为0字段标识位为是 ' . ($bool ? '成功' : '失败请等下次脚本') . PHP_EOL;
                        } else {
                            $log .= '离职资产里资产数量=0 & 资产台账里名下资产数量=0 & 转移记录里数量=0的工号 均不为0无需处理' . PHP_EOL;
                        }
                    }
                }

                //第五步，找到符合三种条件均为0的工号 剩下为未符合但被标记为是的需要还原
                $reset_staff_ids = array_diff($all_leave_staff_ids, $transfer_staff_ids);
                if ($reset_staff_ids) {
                    $log .= '找到符合三种条件均为0的工号 剩下为未符合但被标记为是的需要还原工号：共计' . count($reset_staff_ids) . '个' . '，分别为【' . json_encode($reset_staff_ids, JSON_UNESCAPED_UNICODE) . '】' . PHP_EOL;
                    $reset_staff_ids_str = implode(',', $reset_staff_ids);
                    $bool = $this->getDI()->get('db_oa')->updateAsDict(
                        (new MaterialLeaveAssetsModel)->getSource(),
                        ['is_have_own_asset' => 0],
                        ['conditions' => "staff_info_id in ({$reset_staff_ids_str}) and is_valid = " . MaterialEnums::LEAVE_ASSET_IS_VALID_YES]
                    );
                    $log .= '还原之前被标记为名下无资产的离职人员现在有资产需要还原 ' . ($bool ? '成功' : '失败请等下次脚本') . PHP_EOL;
                }
            }
        } catch (\Exception $e) {
            $log .= $e->getMessage() . PHP_EOL;
            $is_exception = true;
        }
        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log = '物料资产管理-离职资产处理-维护名下资产是否为0字段标识位 ' . PHP_EOL . $log ;
        if ($is_exception) {
            $this->logger->error($log);
        } else {
            $this->logger->info($log);
        }

        exit($log);
    }
}
