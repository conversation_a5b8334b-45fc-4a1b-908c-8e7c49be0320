<?php

use App\Library\BaseService;
use App\Library\Enums\GlobalEnums;
use App\Library\Validation\ValidationException;
use App\Models\backyard\HrJobDepartmentRelationChangeModel;
use App\Models\backyard\HrJobTitleModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\SettingEnvModel;
use App\Models\backyard\SysDepartmentModel;
use App\Models\fle\StaffAccountModel;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Hc\Services\BudgetService;
use App\Modules\Hc\Services\HcShareBudgetService;
use App\Modules\Organization\Models\HrJobDepartmentRelationModel;
use App\Modules\Organization\Services\JobService;
use App\Modules\User\Models\StaffInfoModel;
use App\Repository\DepartmentRepository;

class JobTask extends BaseTask
{

    /**
     * 同步 bi库下历史 部门职位职级 数据到新表中
     */
    public function syncLevelDataAction(){

        (new HrJobDepartmentRelationModel())->doJobLevelRelation();

    }

    /**
     * 发送邮件
     * @param $args
     * @return void
     * @throws ValidationException
     * php app/cli.php job send_mail
     */
    public function send_mailAction($args = [])
    {
        if (empty($args[0])) {
            $date = date('Y-m-d 14:00:00', strtotime('-1 day'));
        } else {
            $date = $args[0];
        }
        // 获取变更数据
        $data = HrJobDepartmentRelationChangeModel::find([
            'conditions' => 'change_time > :start_time:',
            'bind'       => [
                'start_time' => $date,
            ],
            'columns'    => 'department_id,job_id',
            'group'      => 'job_id,department_id',
        ])->toArray();
        if (empty($data)) {
            echo "无数据";
            exit('无数据');
        }

        $emailData = [];
        $shareService = HcShareBudgetService::getInstance()->init();

        foreach ($data as $item) {
            $onJobStaffCount = $hcSurplusCount = $hcPendingEntryCount = $hcBudgetAdoptCount = $transferEntry = 0;

            $changeInfo = HrJobDepartmentRelationChangeModel::find([
                'conditions' => 'change_time > :start_time: and department_id = :department_id: and job_id = :job_id:',
                'bind' => [
                    'start_time'    => $date,
                    'department_id' => $item['department_id'],
                    'job_id'        => $item['job_id'],
                ],
                'columns' => 'hc_origin_budget,hc_budget,operator_id,change_time',
                'order' => 'change_time desc'
            ])->toArray();

            //获取预算变更数据
            $budgetChangeInfo = JobService::getInstance()->calcChangeData($changeInfo);
            if ($budgetChangeInfo['origin_budget'] == $budgetChangeInfo['current_budget']) {
                continue;
            }

            if ($shareService->isShare($item['department_id'], $item['job_id'])) { //是共享
                $shareGroup    = $shareService->getShareGroup($item['department_id'], $item['job_id']);
                foreach ($shareGroup as $shareDepartmentId => $shareJobTitle_ids) {
                    [$tmpOnJobStaffCount, $tmpHcSurplusCount, $tmpHcPendingEntryCount, $tmpHcBudgetAdoptCount, $tmpTransferEntry]
                        = BudgetService::getInstance()->getLeftHcReuqestCnt($shareDepartmentId, $shareJobTitle_ids);
                    $onJobStaffCount += $tmpOnJobStaffCount;
                    $hcSurplusCount += $tmpHcSurplusCount;
                    $hcPendingEntryCount += $tmpHcPendingEntryCount;
                    $hcBudgetAdoptCount += $tmpHcBudgetAdoptCount;
                    $transferEntry  += $tmpTransferEntry;
                }
            } else { //非共享的
                [$onJobStaffCount, $hcSurplusCount, $hcPendingEntryCount, $hcBudgetAdoptCount, $transferEntry]
                    = BudgetService::getInstance()->getLeftHcReuqestCnt($item['department_id'], [$item['job_id']]);
            }

            echo sprintf("department_id : %d,job_id: %d,onJob:%d,Surplus:%d,PendingEntry:%d,BudgetAdopt%d,JobTransferEntry:%d", $item['department_id'], $item['job_id'],$onJobStaffCount,
                $hcSurplusCount, $hcPendingEntryCount, $hcBudgetAdoptCount, $transferEntry), PHP_EOL;
            $this->logger->info(sprintf("department_id : %d,job_id: %d,onJob:%d,Surplus:%d,PendingEntry:%d,BudgetAdopt:%d,JobTransferEntry:%d", $item['department_id'], $item['job_id'],$onJobStaffCount,
                $hcSurplusCount, $hcPendingEntryCount, $hcBudgetAdoptCount, $transferEntry));
            if ($hcSurplusCount > 0 || $hcPendingEntryCount > 0 || $hcBudgetAdoptCount > 0 || $transferEntry > 0) {
                continue;
            }

            if ($budgetChangeInfo['current_budget'] >= $onJobStaffCount) {
                continue;
            }

            $emailData[] = [
                'origin'        => $budgetChangeInfo['origin_budget'] ?? 0,
                'current'       => $budgetChangeInfo['current_budget'] ?? 0,
                'department_id' => $item['department_id'],
                'job_id'        => $item['job_id'],
                'operator_id'   => $budgetChangeInfo['operator_id'] ?? 0,
            ];
        }
        if (empty($emailData)) {
            $message = sprintf('[send_mailAction][%s] 无数据，不发送hc 邮件提醒', $date);
            $this->logger->info($message);
            echo $message, PHP_EOL;
            exit();
        }

        //部门
        $departmentIds = array_column($emailData, 'department_id');
        $departmentModel = new SysDepartmentModel();
        $departmentInfo = $departmentModel->getDataByIds($departmentIds, 'id, name');
        $departmentInfo = array_column($departmentInfo, 'name', 'id');

        //职位
        $jobTitleIds = array_column($emailData, 'job_id');
        $jobModel = new HrJobTitleModel();
        $jobInfo = $jobModel->getDataByIds($jobTitleIds, 'id, job_name');
        $jobInfo = array_column($jobInfo, 'job_name', 'id');

        //工号
        $staffIds = array_column($emailData, 'operator_id');
        $hrStaffInfoModel = new HrStaffInfoModel();
        $staffInfo = $hrStaffInfoModel->getDataByIds($staffIds, 'staff_info_id, name', 'staff_info_id');
        $staffInfo = array_column($staffInfo, 'name', 'staff_info_id');

        //发送邮件并记录日志

        //HC超预算提醒
        $tmp = BaseService::getTranslation('en');
        $title = $tmp->_('hc_email.title');

        //以下部门和职位的HC预算小于在职人数，请关注。
        $body = $tmp->_('hc_email.main_text') . '</br></br><tr>
            <td>' . $tmp->_('export_budget_key_department_name') . '</td>
            <td>' . $tmp->_('material_asset.job_name') . '</td>
            <td>' . $tmp->_('hc_email.hc_num_origin') . '</td>
            <td>' . $tmp->_('hc_email.modify_hc') . '</td>
            <td>' . $tmp->_('material_asset_transfer.operator_name') . '</td>
        </tr>';

        foreach ($emailData as $item) {
            $body .= sprintf("<tr>
                <td>%s</td>
                <td>%s</td>
                <td>%d</td>
                <td>%d</td>
                <td>%s</td>
            </tr>", $departmentInfo[$item['department_id']] ?? '',
                $jobInfo[$item['job_id']] ?? '',
                $item['origin'],
                $item['current'],
                sprintf('%s(%s)', $staffInfo[$item['operator_id']] ?? '', $item['operator_id'])
            );
        }
        $content = sprintf("<table border='1'>%s</table>", $body);

        //获取收件人
        $emailsConfigs = (new SettingEnvModel())->listByCode(['hc_budget_change_emails', 'hc_budget_change_cc_emails']);
        $emailsConfigs = array_column($emailsConfigs, 'set_val', 'code');
        $emails = array_values(array_unique(array_filter(explode(',', $emailsConfigs['hc_budget_change_emails']))));
        $ccEmails = array_values(array_unique(array_filter(explode(',', $emailsConfigs['hc_budget_change_cc_emails']))));

        // 如果收件人邮箱为空，则不发
        if (empty($emails)) {
            throw new ValidationException("no emails addressee, stop send");
        }

        $log = ["emails" => $emails, "title" => $title, "html" => $content, "ccEmails" => $ccEmails];
        if ($this->mailer->sendAsync($emails, $title, $content, [], $ccEmails)) {
            $this->logger->info('send success'. json_encode($log, JSON_UNESCAPED_UNICODE));
            echo date('Ymd H:i:s') . " 邮件发送成功:" . json_encode($log, JSON_UNESCAPED_UNICODE) . PHP_EOL;
            sleep(2);
        } else {
            echo date('Ymd H:i:s') . " 邮件发送失败:" . json_encode($log, JSON_UNESCAPED_UNICODE) . PHP_EOL;
            $this->logger->warning('send fail');
        }
    }

    /**
     *
     */
    public function send_mail_to_managerAction($args = [])
    {
        if (empty($args[0])) {
            $date = date('Y-m-d 14:00:00', strtotime('-1 day'));
        } else {
            $date = $args[0];
        }
        // 获取变更数据
        $data = HrJobDepartmentRelationChangeModel::find([
            'conditions' => 'change_time > :start_time:',
            'bind'       => [
                'start_time' => $date,
            ],
            'columns'    => 'department_id,job_id',
            'group'      => 'job_id,department_id',
        ])->toArray();
        if (empty($data)) {
            echo "无数据";
            exit('无数据');
        }

        $emailData      = [];
        $departmentList = [];
        $jobTitleList   = [];
        $operatorList   = [];
        $staffList      = [];
        $departmentRepo = new DepartmentRepository();

        foreach ($data as $item) {

            $changeInfo = HrJobDepartmentRelationChangeModel::find([
                'conditions' => 'change_time > :start_time: and department_id = :department_id: and job_id = :job_id:',
                'bind' => [
                    'start_time'    => $date,
                    'department_id' => $item['department_id'],
                    'job_id'        => $item['job_id'],
                ],
                'columns' => 'hc_origin_budget,hc_budget,operator_id,change_time',
                'order' => 'change_time desc'
            ])->toArray();

            //获取预算变更数据
            $budgetChangeInfo = JobService::getInstance()->calcChangeData($changeInfo);
            if ($budgetChangeInfo['origin_budget'] == $budgetChangeInfo['current_budget']) {
                continue;
            }

            //归属
            $departmentInfo = $departmentRepo->getDepartmentDetail($item['department_id']);
            if (empty($departmentInfo)) {
                echo "部门数据异常", PHP_EOL;
                continue;
            }
            $firstDepartmentInfo = $departmentRepo->getFirstLevelDepartmentByAncestryV3($departmentInfo['ancestry_v3']);
            if (empty($firstDepartmentInfo)) {
                echo "部门数据异常", PHP_EOL;
                continue;
            }
            $staffInfoIds = [$firstDepartmentInfo['manager_id'], $firstDepartmentInfo['assistant_id']];
            $staffInfoIds = array_values(array_unique(array_filter($staffInfoIds)));
            if (empty($staffInfoIds)) {
                echo "没有人", PHP_EOL;
                continue;
            }
            foreach ($staffInfoIds as $staffInfoId) {
                $emailData[$staffInfoId][] = [
                    'origin'        => $budgetChangeInfo['origin_budget'] ?? 0,
                    'current'       => $budgetChangeInfo['current_budget'] ?? 0,
                    'department_id' => $item['department_id'],
                    'job_id'        => $item['job_id'],
                    'operator_id'   => $budgetChangeInfo['operator_id'] ?? 0,
                ];
            }
            $departmentList[] = $item['department_id'];
            $jobTitleList[] = $item['job_id'];
            $operatorList[] = $budgetChangeInfo['operator_id'];
            $staffList = array_merge($staffList, $staffInfoIds);
        }

        if (empty($emailData)) {
            $message = sprintf('[send_mail_to_managerAction][%s] 无数据，不发送hc 邮件提醒', $date);
            $this->logger->info($message);
            echo $message, PHP_EOL;
            exit();
        }

        //部门
        $departmentModel = new SysDepartmentModel();
        $departmentInfo = $departmentModel->getDataByIds($departmentList, 'id, name');
        $departmentInfo = array_column($departmentInfo, 'name', 'id');

        //职位
        $jobModel = new HrJobTitleModel();
        $jobInfo = $jobModel->getDataByIds($jobTitleList, 'id, job_name');
        $jobInfo = array_column($jobInfo, 'job_name', 'id');

        //工号
        $hrStaffInfoModel = new HrStaffInfoModel();
        $staffInfo = $hrStaffInfoModel->getDataByIds($operatorList, 'staff_info_id, name', 'staff_info_id');
        $staffInfo = array_column($staffInfo, 'name', 'staff_info_id');

        //发送邮件并记录日志
        //HC超预算提醒
        $test_flag = in_array(env('runtime'), []);

        $staffList = array_values(array_unique($staffList));
        foreach ($staffList as $staffInfoId) {
            $items = $emailData[$staffInfoId];
            if (!isset($items) || empty($items)) {
                continue;
            }
            $lang = StaffAccountModel::getAcceptLanguage($staffInfoId);
            $tmp = BaseService::getTranslation($lang);
            if ($test_flag) {
                $title = $tmp->_('hc_email.budget_change_title') . sprintf(" %s", $staffInfoId);
            } else {
                $title = $tmp->_('hc_email.budget_change_title');
            }

            //以下部门和职位的HC预算小于在职人数，请关注。
            $body = $tmp->_('hc_email.budget_change_main_text') . '</br></br><tr>
                <td>' . $tmp->_('export_budget_key_department_name') . '</td>
                <td>' . $tmp->_('material_asset.job_name') . '</td>
                <td>' . $tmp->_('hc_email.hc_num_origin') . '</td>
                <td>' . $tmp->_('hc_email.modify_hc') . '</td>
                <td>' . $tmp->_('material_asset_transfer.operator_name') . '</td>
            </tr>';
            foreach ($items as $item) {
                $body .= sprintf("<tr>
                        <td>%s</td>
                        <td>%s</td>
                        <td>%d</td>
                        <td>%d</td>
                        <td>%s</td>
                        </tr>", $departmentInfo[$item['department_id']] ?? '',
                    $jobInfo[$item['job_id']] ?? '',
                    $item['origin'],
                    $item['current'],
                    sprintf('%s(%s)', $staffInfo[$item['operator_id']] ?? '', $item['operator_id'])
                );
            }
            $content = sprintf("<table border='1'>%s</table>", $body);

            //获取收件人
            // 开发/测试/tra环境收件人取指定配置的
            // 只在测试环境，给测试人员发邮件。
            if ($test_flag) {
                $email_addressee_val = EnvModel::getEnvByCode(GlobalEnums::EMAIL_NOTICE_BUDGET_CHANGE);
                $emails              = !empty($email_addressee_val) ? explode(',', $email_addressee_val) : [];
            } else {
                // 生产取工号对应的
                $emails = (new StaffInfoModel())->getEmails([$staffInfoId]);
            }

            // 如果收件人邮箱为空，则不发
            if (empty($emails)) {
                continue;
                //throw new ValidationException("no emails addressee, stop send");
            }

            $log = ["emails" => $emails, "title" => $title, "html" => $content];
            if ($this->mailer->sendAsync($emails, $title, $content)) {
                $this->logger->info('send success'. json_encode($log, JSON_UNESCAPED_UNICODE));
                echo date('Ymd H:i:s') . " 邮件发送成功:" . json_encode($log, JSON_UNESCAPED_UNICODE) . PHP_EOL;
                sleep(2);
            } else {
                echo date('Ymd H:i:s') . " 邮件发送失败:" . json_encode($log, JSON_UNESCAPED_UNICODE) . PHP_EOL;
                $this->logger->warning('send fail');
            }
        }
    }
}