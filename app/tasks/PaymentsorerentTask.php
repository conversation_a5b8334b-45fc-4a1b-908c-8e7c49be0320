<?php
/**
 * Created by PhpStorm.
 * Date: 2022/5/16
 * Time: 12:02
 */

use App\Modules\Common\Services\EnumsService;
use App\Modules\Payment\Services\SapsService;
use App\Modules\Purchase\Services\OrderService;
use App\Modules\Organization\Models\SysDepartmentModel;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Training\Services\TaskService;
use App\Library\Enums\GlobalEnums;
use App\Modules\Payment\Models\PaymentStoreRentingDetail;
use App\Modules\OrdinaryPayment\Services\SapService as oSapService;

class PaymentsorerentTask extends BaseTask
{

    /**
     * 租房付款发送sap
     * */
    public function pay_store_sapAction($params)
    {
        echo 'begin: ' . date('Y-m-d H:i:s') . PHP_EOL;

        $process_name = str_replace('Action', '', __FUNCTION__);
        $this->check_process($process_name);

        $real_pay_at = '2022-05-01 00:00:00';
        $days        = $params[0] ?? 60;
        $real_pay_at = date('Y-m-d H:i:s', time() - 3600 * 24 * $days);
        if ($real_pay_at < '2022-06-01 00:00:00') {
              $real_pay_at = '2022-06-01 00:00:00';
        }

        $cost_company_id = EnumsService::getInstance()->getSettingEnvValueIds('purchase_sap_company_ids');
        $sap_company_ids = SysDepartmentModel::find(
            [
                'conditions' => 'id in ({ids:array})',
                'bind'       => ['ids' => $cost_company_id]
            ]
        )->toArray();
        $cost_company_ids = implode(',', $cost_company_id);

        $sap_company_ids = array_column($sap_company_ids, 'sap_company_id', 'id') ?? [];

        $cost_type_data = EnvModel::getEnvByCode('payment_store_sap_cost_type');
        $cost_type_data = json_decode($cost_type_data, true);

        $cost_type_id   = array_column($cost_type_data, 'id');
        $cost_type_id   = implode(',', $cost_type_id);
        $cost_type_data = array_column($cost_type_data, 'code', 'id');


        $db_obj = $this->db_oa;
        $t      = TaskService::getTranslation(get_country_code());

        try {

            $send_data = array();
            //发送邮件
            $email = \App\Modules\Common\Models\EnvModel::getEnvByCode('payment_store_sap_email');
            $email = explode(',', $email);

            $i   = 0;
            $x   = 0;
            $sql = "select a.id,a.cost_company_id,a.apply_no,a.currency,a.created_at,a.create_date,a.approved_at,b.id as d_id,b.store_id,b.sap_supplier_no,b.amount,b.vat_amount,b.cost_type,b.cost_center_code,b.vat_rate,b.wht_tax_rate,b.transaction_date,b.index_no,b.cost_start_date,b.created_at   from payment_store_renting as a  left join payment_store_renting_detail as b on a.id=b.store_renting_id where b.transaction_date>='{$real_pay_at}' and a.approval_status =3 and a.cost_company_id in ({$cost_company_ids}) and b.cost_type in ({$cost_type_id})  and sync_sap in (0,3) limit {$i} ,100";

            $data          = $db_obj->query($sql);
            $deal_sap_data = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            $x             = 0;
            while (!empty($deal_sap_data)) {
                foreach ($deal_sap_data as $key => $value) {

                    $request_data = [];
                    $return_data  = [];

                    $reserve_found = PaymentStoreRentingDetail::findFirst([
                        'id = :id:',
                        'bind' => ['id' => $value['d_id']]
                    ]);

                    if (empty($reserve_found)) {
                        continue;
                    }
                    if(ltrim(date('m',strtotime($value['created_at'])),0)==ltrim(date('m',strtotime($value['cost_start_date'])),0)){
                        continue;
                    }


                    $request_data = [
                        'cost_company'     => $sap_company_ids[$value['cost_company_id']] ?? 'FEX01',
                        'apply_index_no'   => $value['apply_no'] . '_'.$value['index_no'],
                        'currency'         => $t->_(GlobalEnums::$currency_item[$value['currency']]),
                        'transaction_date' => $value['transaction_date'],
                        'approved_at'      => date('Y-m-d', strtotime($value['approved_at'])),
                        'create_date'      => $value['create_date'],
                        'sap_supplier_no'  => $value['sap_supplier_no'],
                        'amount'           => $value['amount'],
                        'vat_amount'       => $value['vat_amount'],
                        'account'          => $cost_type_data[$value['cost_type']],
                        'vat_rate'         => $value['vat_rate'],
                        'wht_tax_rate'     => $value['wht_tax_rate'],
                        'cost_center_code' => $value['cost_center_code'],
                        'total_amount'     => bcadd($value['amount'],$value['vat_amount'],2),
                        'vat_code'         =>oSapService::getInstance()->vat_code(0,0),
                        'wht_tax_code'     => oSapService::getInstance()->wht_code(0,$value['wht_tax_rate'])
                    ];

                    $z = 0;
                    echo 'pay_store_rent_detail id:' . $value['id'];
                    echo PHP_EOL;
                    while (empty($return_data) && $z < 1) {
                        $z++;
                        $return_data = SapsService::getInstance()->PaymentRentSap($request_data);

                        if (isset($return_data['UUID']) && !empty($return_data['UUID'])) {
                            $x++;
                            $reserve_found->i_update(['sync_sap' => 1, 'sap_uuid' => $return_data['UUID'] ?? '', 'updated_at' => date('Y-m-d H:i:s')]);
                            echo 'success pay store detail id:' . $value['id'];
                            $row['no']   = $value['apply_no'] . '_' . $value['index_no'];
                            $send_data[] = $row;
                        } else {
                            if (isset($return_data['log']['Item']) && !empty($return_data['log']['Item'])) {
                                $note = '';
                                $note = array_column($return_data['log']['Item'], 'Note');
                                $note = implode(',', $note);

                            }
                         $reserve_found->i_update(['sync_sap' => 2, 'updated_at' => date('Y-m-d H:i:s'), 'sap_note' => $note??'']);

                        }
                        sleep(2);
                    }

                    echo PHP_EOL;


                }

                //拼接 html  发送邮件
                if (!empty($send_data) && !empty($email)) {

                    $title = "OA SAP document creation reminder";
                    $html  = $this->format_html($send_data);

                    $this->mailer->sendAsync($email, $title, $html);
                }
                sleep(1);
                $i   += 100;
                $sql = "select a.id,a.cost_company_id,a.apply_no,a.currency,a.created_at,a.create_date,a.approved_at,b.id as d_id,b.store_id,b.sap_supplier_no,b.amount,b.vat_amount,b.cost_type,b.cost_center_code,b.vat_rate,b.wht_tax_rate,b.transaction_date,b.index_no,b.cost_start_date,b.created_at   from payment_store_renting as a  left join payment_store_renting_detail as b on a.id=b.store_renting_id where b.transaction_date>='{ $real_pay_at}' and a.approval_status =3 and a.cost_company_id in ({$cost_company_ids}) and b.cost_type in ({$cost_type_id})  and sync_sap in (0,3) limit {$i} ,100";


                $data          = $db_obj->query($sql);
                $deal_sap_data = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            }

            echo 'pay_store_renting成功:' . $x . '条数' . PHP_EOL;

        } catch (Exception $e) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('payment_store_sap_task_exception:' . $e->getMessage());
            echo $e->getMessage() . PHP_EOL;
        }

        echo 'end: ' . date('Y-m-d H:i:s');
        exit();
    }

    //整理 sap 订单提醒邮件内容
    public function format_html($data)
    {
        $html = "<p>Dear all,<?p></br></br>";
        $html .= "<p>Below data are posted in SAP, please have a check! Thanks!</p></br></br>";
        $html .= "<p>以下数据已过账到SAP，请检查！谢谢</p>";

        $html .= "<table border='1'><tr><td>OA number</td></tr>";
        foreach ($data as $da) {
            $html .= "<tr><td>{$da['no']}</td></tr>";
        }

        $html .= "</table>";
        return $html;

    }


}