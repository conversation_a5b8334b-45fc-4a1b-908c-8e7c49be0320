<?php
/**
 * 转岗模块权限初始化
 *
 * 需求文档：17689【TH|BY&OA|转岗】泰国转岗功能线上化
 * https://flashexpress.feishu.cn/docx/GIvGdnBVGoMwYhxsnRMcWTbsnyc
 *
 * 权限规则：
 * https://flashexpress.feishu.cn/sheets/BYMCs7s1yhUH3ftJ7hncswMinLg
 */

use app\library\Enums\JobTransferEnums;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\SettingEnvModel;
use App\Models\backyard\SysManagePieceModel;
use App\Models\backyard\SysManageRegionModel;
use App\Modules\Transfer\Models\StaffInfoModel;
use App\Modules\Transfer\Models\StaffInfoPositionModel;
use App\Modules\Transfer\Models\StaffJobTransferPermissionLogModel;
use App\Modules\Transfer\Models\SysDepartmentModel;
use App\Modules\User\Models\StaffPermissionModel;
use App\Modules\User\Models\StoreModel;

class JobTransferPermissionV2Task extends BaseTask
{
    // 新员工清单: 最新提取的待分配权限的工号清单
    protected static $_new_staff_item = [];

    // 旧员工清单: 上次已分配权限的工号清单
    protected static $_old_staff_item = [];

    // 系统员工当前权限
    protected static $_sys_staff_permission = [];

    // 权限有调整(增/删)的员工清单 10000 => [1,2,3,4]
    protected static $_waiting_save_staff_permission = [];

    // 上次转岗分配权限的日志
    protected static $_log_last_staff_permission = [];

    // 待计入权限分配日志的数据
    protected static $_log_curr_staff_permission = [];

    // 员工转岗权限是否清空标识
    protected static $_staff_sys_permission_clear_job_transfer_permission = [];

    // 脚本开始运行时间
    protected static $script_start_at = 0;

    // 脚本运行时长
    protected static $script_exec_at = 0;

    // 脚本使用内存
    protected static $script_used_mem = 0;

    // 日志输出
    protected static $output_log = PHP_EOL;


    /**
     * 权限初始化
     * php app/cli.php job_transfer_permission_v2 init
     */
    public function initAction()
    {
        $this->calScriptExecTime('start');

        try {
            // 获取最新应分配权限的组织架构/角色对应的工号清单
            $this->getOrganizationAndRolesStaffIds();

            // 获取系统员工权限清单
            $this->getSysStaffPermission();

            // 获取上次员工分配权限的日志
            $this->getLastStaffPermissionLog();

            // 权限剔除
            // 获取上次已分配权限的组织架构/角色对应的工号及权限清单
            $this->delPermission();

            // 权限添加
            if ($this->addPermission()) {
                $_info_log = '员工转岗权限初始化完毕: 成功';
                $this->logger->info($_info_log);
                self::$output_log .= $_info_log.PHP_EOL;
            } else {
                throw new \Exception('员工转岗权限初始化完毕: 失败');
            }
        } catch (\Exception $e) {
            $_err_msg = '员工转岗权限初始化异常 - '.$e->getMessage();
            $this->logger->error($_err_msg);
            self::$output_log .= $_err_msg.PHP_EOL;
        }

        $this->calScriptExecTime('end');
        $this->calScriptUsedMem();

        self::$output_log .= '脚本执行时长: '.self::$script_exec_at.' ms'.PHP_EOL;
        self::$output_log .= '脚本内存消耗: '.self::$script_used_mem.' MB'.PHP_EOL;

        echo self::$output_log;
    }

    /**
     * 权限添加
     * task: 每天执行一次
     */
    protected function addPermission()
    {
        // 遍历待分配权限主体类型: 组织架构/角色
        foreach (self::$_new_staff_item as $main_type => $group) {
            // 组织架构/角色对应的权限配置
            $permission_setting = [];
            switch ($main_type) {
                case JobTransferEnums::PERMISSION_ASSIGN_ORGANIZATION_TYPE:
                    $permission_setting = JobTransferEnums::$organization_permission_map;
                    break;
                case JobTransferEnums::PERMISSION_ASSIGN_ROLES_TYPE:
                    $permission_setting = JobTransferEnums::$roles_permission_map;
                    break;
            }

            if (empty($permission_setting)) {
                continue;
            }

            // 遍历主体列表: 组织架构/角色列表
            foreach ($group as $group_key => $staff_item) {
                // 查找不同组织架构/角色配置的菜单权限
                $group_permission_item = $permission_setting[$group_key];

                // 遍历主体所属的员工列表: 工号清单
                foreach ($staff_item as $staff_id) {
                    // 员工权限日志存储key
                    $_log_staff_permission_flag = $staff_id.'-'.$main_type.'-'.$group_key;

                    // 初始化员工权限分配日志
                    if (!isset(self::$_log_curr_staff_permission[$_log_staff_permission_flag])) {
                        self::$_log_curr_staff_permission[$_log_staff_permission_flag] = $group_permission_item;
                    }

                    // 每次分配转岗权限前, 先将该员工系统权限中已分配的转岗权限清除, 然后重新分配
                    if (!isset(self::$_staff_sys_permission_clear_job_transfer_permission[$staff_id])) {
                        $staff_sys_permission = self::$_sys_staff_permission[$staff_id] ?? '';
                        if (!empty($staff_sys_permission)) {
                            $staff_sys_permission                   = explode(',', trim($staff_sys_permission));
                            self::$_sys_staff_permission[$staff_id] = array_diff($staff_sys_permission,
                                JobTransferEnums::$job_transfer_all_permission);
                        }

                        self::$_staff_sys_permission_clear_job_transfer_permission[$staff_id] = 1;
                    }

                    // 初始化员工待更新的权限
                    if (!isset(self::$_waiting_save_staff_permission[$staff_id])) {
                        self::$_waiting_save_staff_permission[$staff_id] = isset(self::$_sys_staff_permission[$staff_id]) && is_array(self::$_sys_staff_permission[$staff_id]) ? self::$_sys_staff_permission[$staff_id] : [];
                    }

                    // 以下 [1]/[2] 可解决某个组织架构/角色对应权限变更的情况
                    // [1] 去除员工昨日已分配的权限
                    $staff_last_assign_permission = self::$_log_last_staff_permission[$_log_staff_permission_flag] ?? '';
                    if (!empty($staff_last_assign_permission)) {
                        $staff_last_assign_permission                    = explode(',', $staff_last_assign_permission);
                        self::$_waiting_save_staff_permission[$staff_id] = array_diff(self::$_waiting_save_staff_permission[$staff_id],
                            $staff_last_assign_permission);
                    }

                    // [2] 添加员工今日待分配的权限 (提取员工待分配的转岗权限 与 已员工已获得的权限差集: 差集 则为需追加的权限)
                    $need_append_permission = array_diff($group_permission_item,
                        self::$_waiting_save_staff_permission[$staff_id]);

                    // 员工权限追加
                    if (!empty($need_append_permission)) {
                        self::$_waiting_save_staff_permission[$staff_id] = array_merge(self::$_waiting_save_staff_permission[$staff_id],
                            $need_append_permission);
                    }
                }
            }
        }

        // 增加权限完毕: 权限入库 并 记日志
        return $this->saveStaffPermission();
    }

    /**
     * 记录权限分配日志
     * 添加: 批量处理
     */
    protected function saveLog()
    {
        try {
            $log_data = [];

            $exec_date = gmdate_customize_by_timestamp(time(), 'Y-m-d');
            foreach (self::$_log_curr_staff_permission as $key => $value) {
                $log_data[] = [
                    'exec_date'               => $exec_date,
                    'staff_permission_flag'   => $key,
                    'staff_permission_detail' => implode(',', $value),
                    'created_at'              => gmdate_customize_by_timestamp(time(), 'Y-m-d H:i:s'),
                ];
            }

            if ((new StaffJobTransferPermissionLogModel())->batch_insert($log_data)) {
                $_info_msg = '员工权限日志保存成功, 共 '.count(self::$_log_curr_staff_permission).' 条';
                $this->logger->info($_info_msg);
                self::$output_log .= $_info_msg.PHP_EOL;

                return true;
            } else {
                $_info_msg = '员工权限日志保存失败, 共 '.count(self::$_log_curr_staff_permission).' 条';
                $this->logger->info($_info_msg);
                self::$output_log .= $_info_msg.PHP_EOL;

                return false;
            }
        } catch (\Exception $e) {
            $_err_msg = '员工权限日志保存异常 - '.$e->getMessage();
            $this->logger->error($_err_msg);
            self::$output_log .= $_err_msg.PHP_EOL;

            return false;
        }
    }

    /**
     * 权限保存
     * 更新: 逐条处理
     * 添加: 批量处理
     */
    protected function saveStaffPermission()
    {
        $oa_db = $this->getDI()->get('db_oa');

        try {
            $oa_db->begin();

            // 需新增的员工权限
            $add_staff_permission_data = [];

            foreach (self::$_waiting_save_staff_permission as $staff_id => $permission_item) {
                $permission_item = array_unique(array_filter($permission_item));

                // 更新
                if (isset(self::$_sys_staff_permission[$staff_id])) {
                    $update_sql   = "
                        -- 更新员工权限 - 转岗审批
                        UPDATE
                            staff_permission
                        SET
                            permission_ids = ?, last_updated_at = ?
                        WHERE
                            staff_id = ?        
                    ";
                    $update_param = [
                        implode(',', $permission_item),
                        gmdate_customize_by_timestamp(time(), 'Y-m-d H:i:s'),
                        $staff_id,
                    ];

                    if (!$oa_db->execute($update_sql, $update_param)) {
                        throw new \Exception("员工权限日志更新失败: sql = $update_sql, param = ".json_encode($update_param,
                                JSON_UNESCAPED_UNICODE));
                    }
                } else {
                    // 添加
                    $add_staff_permission_data[] = [
                        'staff_id'        => $staff_id,
                        'permission_ids'  => implode(',', $permission_item),
                        'is_granted'      => 1,
                        'last_updated_at' => gmdate_customize_by_timestamp(time(), 'Y-m-d H:i:s'),
                    ];
                }
            }

            // 权限创建
            $staff_permission_add_res = true;
            if (!empty($add_staff_permission_data)) {
                $staff_permission_add_res = (new StaffPermissionModel())->batch_insert($add_staff_permission_data);
            }

            if ($staff_permission_add_res) {
                $_info_msg = '员工权限保存成功, 共处理 '.count(self::$_waiting_save_staff_permission).' 个员工, 其中新增 '.count($add_staff_permission_data).' 个员工';
                $this->logger->info($_info_msg);
                self::$output_log .= $_info_msg.PHP_EOL;
            } else {
                throw new \Exception("员工权限添加失败");
            }

            // 日志保存
            if (!$this->saveLog()) {
                throw new \Exception("员工权限日志保存失败");
            }

            $oa_db->commit();

            return true;
        } catch (\Exception $e) {
            $oa_db->rollback();

            $_err_msg = '员工权限任务异常 - '.$e->getMessage();
            $this->logger->error($_err_msg);
            self::$output_log .= $_err_msg.PHP_EOL;

            return false;
        }
    }


    /**
     * 权限删除
     * task: 每天执行一次
     */
    protected function delPermission()
    {
        // 需删除权限的员工: 日志使用
        $need_del_staff_permission_item = [];

        // 上次分配权限的日志
        foreach (self::$_log_last_staff_permission as $flag => $log_permission) {
            $staff_permission_attr = explode('-', $flag);

            // 找当前应分配权限的主体对应的员工列表
            if (!isset(self::$_new_staff_item[$staff_permission_attr[1]][$staff_permission_attr[2]])) {
                if (!isset(self::$_staff_sys_permission_clear_job_transfer_permission[$staff_permission_attr[0]])) {
                    $staff_sys_permission = self::$_sys_staff_permission[$staff_permission_attr[0]] ?? '';
                    if (!empty($staff_sys_permission)) {
                        $staff_sys_permission                                   = explode(',', trim($staff_sys_permission));
                        self::$_sys_staff_permission[$staff_permission_attr[0]] = array_diff($staff_sys_permission,
                            JobTransferEnums::$job_transfer_all_permission);
                    }

                    self::$_staff_sys_permission_clear_job_transfer_permission[$staff_permission_attr[0]] = 1;
                }
                continue;
            }
            $group_permission_curr_staff_item = self::$_new_staff_item[$staff_permission_attr[1]][$staff_permission_attr[2]];

            // 上次获的该权限的员工 已不在 该权限组 中, 删除该员工上次所分配的该权限组的权限
            if (!in_array($staff_permission_attr[0], $group_permission_curr_staff_item)) {
                // 若该员工已不是应有权限的人, 则清空该员工的所有转岗权限, 若该员工在其他组织/角色中存在，则会在权限添加环节 把相应权限加回来 [addPermission]
                if (!isset(self::$_staff_sys_permission_clear_job_transfer_permission[$staff_permission_attr[0]])) {
                    $staff_sys_permission = self::$_sys_staff_permission[$staff_permission_attr[0]] ?? '';
                    if (!empty($staff_sys_permission)) {
                        $staff_sys_permission                                   = explode(',', trim($staff_sys_permission));
                        self::$_sys_staff_permission[$staff_permission_attr[0]] = array_diff($staff_sys_permission,
                            JobTransferEnums::$job_transfer_all_permission);
                    }

                    self::$_staff_sys_permission_clear_job_transfer_permission[$staff_permission_attr[0]] = 1;
                }

                // 初始化员工待更新的权限
                if (!isset(self::$_waiting_save_staff_permission[$staff_permission_attr[0]])) {
                    self::$_waiting_save_staff_permission[$staff_permission_attr[0]] = is_array(self::$_sys_staff_permission[$staff_permission_attr[0]]) ? self::$_sys_staff_permission[$staff_permission_attr[0]] : [];
                }

                $need_del_staff_permission_item[$flag] = $log_permission;
            }
        }

        $this->logger->info('需删除权限的员工: 共 '.count($need_del_staff_permission_item).' 个, 删除明细 - '.json_encode($need_del_staff_permission_item,
                JSON_UNESCAPED_UNICODE));

        return true;
    }

    /**
     * oa.staff_job_transfer_permission_log
     * 获取上次分配权限的日志
     */
    protected function getLastStaffPermissionLog()
    {
        try {
            // 获取上次分配权限日志的日期
            $last_log_date = StaffJobTransferPermissionLogModel::findFirst([
                'columns' => ['exec_date'],
                'order'   => 'exec_date DESC',
            ]);

            $exec_date = '';
            if (!empty($last_log_date)) {
                $exec_date = $last_log_date->exec_date;
            }

            $log                              = $this->getLog($exec_date);
            $log                              = $log ? array_column($log, 'staff_permission_detail',
                'staff_permission_flag') : [];
            self::$_log_last_staff_permission = $log;

            $_info_msg = "员工权限分配日志提取成功[date = $exec_date], 共 ".count($log).' 条';
            $this->logger->info($_info_msg);
            self::$output_log .= $_info_msg.PHP_EOL;

            return $log;
        } catch (\Exception $e) {
            $_err_msg = "员工权限分配日志提取异常[date = $exec_date] - ".$e->getMessage();
            $this->logger->error($_err_msg);
            self::$output_log .= $_err_msg.PHP_EOL;

            return [];
        }
    }

    /**
     * 获取指定日期的权限分配日志
     * @param string $exec_date
     * @return mixed
     */
    protected function getLog(string $exec_date)
    {
        if (empty($exec_date)) {
            return [];
        }

        return StaffJobTransferPermissionLogModel::find([
                'conditions' => 'exec_date = :exec_date:',
                'bind'       => ['exec_date' => $exec_date],
                'columns'    => ['staff_permission_flag', 'staff_permission_detail'],
            ]
        )->toArray();
    }

    /**
     * 获取系统员工现有权限
     */
    protected function getSysStaffPermission()
    {
        try {
            $item                        = StaffPermissionModel::find([
                'columns' => [
                    'staff_id',
                    'permission_ids',
                ],
            ])->toArray();
            $item                        = array_column($item, 'permission_ids', 'staff_id');
            self::$_sys_staff_permission = $item;

            $_info_msg = '系统员工权限提取完毕: 共 ' . count($item) . ' 个';
            $this->logger->info($_info_msg);
            self::$output_log .= $_info_msg . PHP_EOL;

            return $item;
        } catch (\Exception $e) {
            $_err_msg = '系统员工权限提取异常 - '.$e->getMessage();
            $this->logger->error($_err_msg);
            self::$output_log .= $_err_msg.PHP_EOL;

            return [];
        }
    }

    /**
     * 获取组织架构 和 角色关联的工号
     */
    protected function getOrganizationAndRolesStaffIds()
    {
        try {
            $this->getStoreManagerIds();
            $this->getPieceAreaManagerIds();
            $this->getRegionManagerIds();
            $this->getDepartmentManagerIds();
            $this->getRolesStaffIds();
            $this->getStaffManager();
            $this->getSpecJobTitleStaffIds();

            $this->logger->info('各组织架构和角色对应的工号提取完毕 - '.json_encode(self::$_new_staff_item,
                    JSON_UNESCAPED_UNICODE));
            self::$output_log .= '各组织架构和角色对应的工号提取完毕'.PHP_EOL;

            return self::$_new_staff_item;
        } catch (\Exception $e) {
            $_err_log = '各组织架构和角色对应的工号提取异常 - '.$e->getMessage();
            $this->logger->error($_err_log);
            self::$output_log .= $_err_log.PHP_EOL;

            return [];
        }
    }

    /**
     * fle.sys_store[4529]
     * 获取网点负责人工号清单
     */
    protected function getStoreManagerIds()
    {
        try {
            //排掉加盟商网点
            $manager_ids = StoreModel::find([
                'conditions' => 'state = :state: and category != 6',
                'bind' => ['state' => 1],
                'columns' => 'manager_id',
                'group' => 'manager_id'
            ])->toArray();

            $manager_ids = array_values(array_filter(array_column($manager_ids, 'manager_id')));
            self::$_new_staff_item[JobTransferEnums::PERMISSION_ASSIGN_ORGANIZATION_TYPE][JobTransferEnums::ORGANIZATION_STORE_MANAGER] = $manager_ids;

            $_info_log = '网点负责人提取完毕: 共 ' . count($manager_ids) . ' 个';
            $this->logger->info($_info_log);
            self::$output_log .= $_info_log . PHP_EOL;

            return $manager_ids;
        } catch (\Exception $e) {
            $_err_log = '网点负责人提取异常 - ' . $e->getMessage();
            $this->logger->error($_err_log);
            self::$output_log .= $_err_log . PHP_EOL;

            return [];
        }
    }


    /**
     * fle.sys_manage_piece[50]
     * 获取片区负责人工号清单
     */
    protected function getPieceAreaManagerIds()
    {
        try {
            $manager_ids = SysManagePieceModel::find([
                'conditions' => 'deleted = :deleted:',
                'bind' => ['deleted' => 0],
                'columns' => 'manager_id',
            ])->toArray();

            $manager_ids = array_values(array_unique(array_filter(array_column($manager_ids, 'manager_id'))));
            self::$_new_staff_item[JobTransferEnums::PERMISSION_ASSIGN_ORGANIZATION_TYPE][JobTransferEnums::ORGANIZATION_PIECE_AREA_MANAGER] = $manager_ids;

            $_info_log = '片区负责人提取完毕: 共 ' . count($manager_ids) . ' 个';
            $this->logger->info($_info_log);
            self::$output_log .= $_info_log . PHP_EOL;

            return $manager_ids;
        } catch (\Exception $e) {
            $_err_log = '片区负责人提取异常 - ' . $e->getMessage();
            $this->logger->error($_err_log);
            self::$output_log .= $_err_log . PHP_EOL;

            return [];
        }
    }

    /**
     * fle.sys_manage_region[27]
     * 获取大区负责人工号清单
     */
    protected function getRegionManagerIds()
    {
        try {
            $manager_ids = SysManageRegionModel::find([
                'conditions' => 'deleted = :deleted:',
                'bind' => ['deleted' => 0],
                'columns' => 'manager_id',
            ])->toArray();

            $manager_ids = array_values(array_unique(array_filter(array_column($manager_ids, 'manager_id'))));
            self::$_new_staff_item[JobTransferEnums::PERMISSION_ASSIGN_ORGANIZATION_TYPE][JobTransferEnums::ORGANIZATION_REGION_MANAGER] = $manager_ids;

            $_info_log = '大区负责人提取完毕: 共 ' . count($manager_ids) . ' 个';
            $this->logger->info($_info_log);
            self::$output_log .= $_info_log . PHP_EOL;

            return $manager_ids;
        } catch (\Exception $e) {
            $_err_log = '大区负责人提取异常 - ' . $e->getMessage();
            $this->logger->error($_err_log);
            self::$output_log .= $_err_log . PHP_EOL;

            return [];
        }
    }

    /**
     * fle.sys_department[112]
     * 获取部门负责人工号清单
     */
    protected function getDepartmentManagerIds()
    {
        try {
            $departmentInfo = SysDepartmentModel::find([
                'conditions' => 'deleted = :deleted:',
                'bind'       => ['deleted' => 0],
                'columns'    => 'manager_id,assistant_id',
            ])->toArray();

            $manager_ids = array_column($departmentInfo, 'manager_id');
            $assistant_ids = array_column($departmentInfo, 'assistant_id');
            $total_manager_ids = array_values(array_unique(array_filter(array_merge($manager_ids, $assistant_ids))));

            self::$_new_staff_item[JobTransferEnums::PERMISSION_ASSIGN_ORGANIZATION_TYPE][JobTransferEnums::ORGANIZATION_DEPARTMENT_MANAGER] = $total_manager_ids;

            $_info_log = '部门负责人、助理提取完毕: 共 '.count($total_manager_ids).' 个';
            $this->logger->info($_info_log);
            self::$output_log .= $_info_log.PHP_EOL;

            return $total_manager_ids;
        } catch (\Exception $e) {
            $_err_log = '部门负责人、助理提取异常 - '.$e->getMessage();
            $this->logger->error($_err_log);
            self::$output_log .= $_err_log.PHP_EOL;

            return [];
        }
    }

    /**
     * fle.staff_info_position[545]
     * fle.staff_info[277]
     * 获取角色对应的工号清单
     * 'HRBP','HR Management','Payroll','HRIS管理员','HR Specialist','系统管理员','超级管理员'
     */
    protected function getRolesStaffIds()
    {
        try {
            // 角色对应的工号
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['position' => StaffInfoPositionModel::class]);
            $builder->leftjoin(StaffInfoModel::class, 'position.staff_info_id = staff.id', 'staff');
            $builder->inWhere('position.position_category', JobTransferEnums::$roles_ids);
            $builder->andWhere('staff.state = :state:', ['state' => 1]);
            $builder->andWhere('staff.formal = :formal:', ['formal' => 1]);
            $builder->andWhere('staff.virtual_number_enabled = :virtual_number_enabled:',
                ['virtual_number_enabled' => 0]);
            $builder->columns([
                'position.staff_info_id',
                'position.position_category',
            ]);
            $staff_list = $builder->getQuery()->execute()->toArray();

            $roles_staff_ids = [];
            foreach ($staff_list as $staff) {
                $roles_staff_ids[$staff['position_category']][] = $staff['staff_info_id'];
            }

            self::$_new_staff_item[JobTransferEnums::PERMISSION_ASSIGN_ROLES_TYPE] = $roles_staff_ids;

            foreach ($roles_staff_ids as $role_id => $staffs) {
                $_info_log = "角色 [role_id = $role_id] 员工提取完毕: 共 ".count($staffs).' 个';
                $this->logger->info($_info_log);
                self::$output_log .= $_info_log.PHP_EOL;
            }

            return $roles_staff_ids;
        } catch (\Exception $e) {
            $role_ids = implode(',', JobTransferEnums::$roles_ids);

            $_err_log = "角色 [role_ids = $role_ids] 对应员工提取异常 - ".$e->getMessage();
            $this->logger->error($_err_log);
            self::$output_log .= $_err_log.PHP_EOL;

            return [];
        }
    }

    protected function getStaffManager()
    {
        try {
            // 角色对应的工号
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['hsi' => HrStaffInfoModel::class]);
            $builder->andWhere('hsi.state = :state:', ['state' => HrStaffInfoModel::STATE_ON_JOB]);
            $builder->andWhere('hsi.formal = :formal:', ['formal' => 1]);
            $builder->andWhere('is_sub_staff = :is_sub_staff:', ['is_sub_staff' => 0]);
            $builder->columns([
                'hsi.manger',
            ]);
            $staff_list = $builder->getQuery()->execute()->toArray();

            $manager_staff_ids = array_column($staff_list, 'manger');

            self::$_new_staff_item[JobTransferEnums::PERMISSION_ASSIGN_ORGANIZATION_TYPE][JobTransferEnums::ORGANIZATION_STAFF_MANAGER] = $manager_staff_ids;

            $_info_log = '上级提取完毕: 共 '.count($manager_staff_ids).' 个';
            $this->logger->info($_info_log);
            self::$output_log .= $_info_log.PHP_EOL;

            return $manager_staff_ids;
        } catch (\Exception $e) {

            $_err_log = '上级提取异常 - '.$e->getMessage();
            $this->logger->error($_err_log);
            self::$output_log .= $_err_log.PHP_EOL;

            return [];
        }
    }

    /**
     * 计算脚本时间
     * @param string $type
     * @return mixed
     */
    protected function calScriptExecTime(string $type = 'start')
    {
        if ($type == 'start') {
            self::$script_start_at = microtime(true);
        } else {
            self::$script_exec_at = intval((microtime(true) - self::$script_start_at) * 1000);
        }
    }

    /**
     * 脚本占用内存
     */
    protected function calScriptUsedMem()
    {
        self::$script_used_mem = round(memory_get_peak_usage() / 1024 / 1024, 2);// . 'MB';
    }

    /**
     * 获取指定职位对应的工号清单
     * by setting_env (job_transfer_position_permission)
     */
    private function getSpecJobTitleStaffIds()
    {
        try {
            //获取配置
            $jobTitleIds = (new SettingEnvModel())->getSetVal('job_transfer_position_permission', ',');

            //获取职位对应的工号
            if (empty($jobTitleIds)) {
                $manager_staff_ids = [];
            } else {
                $builder = $this->modelsManager->createBuilder();
                $builder->from(['hsi' => HrStaffInfoModel::class]);
                $builder->andWhere('hsi.state = :state:', ['state' => HrStaffInfoModel::STATE_ON_JOB]);
                $builder->andWhere('hsi.formal = :formal:', ['formal' => HrStaffInfoModel::FORMAL_NORMAL]);
                $builder->andWhere('is_sub_staff = :is_sub_staff:', ['is_sub_staff' => HrStaffInfoModel::IS_NOT_SUB_STAFF]);
                $builder->inWhere('hsi.job_title', $jobTitleIds);
                $builder->columns([
                    'hsi.staff_info_id',
                ]);
                $staff_list = $builder->getQuery()->execute()->toArray();
                $manager_staff_ids = array_column($staff_list, 'staff_info_id');
            }
            self::$_new_staff_item[JobTransferEnums::PERMISSION_ASSIGN_ORGANIZATION_TYPE][JobTransferEnums::ORGANIZATION_SPEC_JOB_TITLE] = $manager_staff_ids;

            $_info_log = '指定职位提取完毕: 共 '.count($manager_staff_ids).' 个';
            $this->logger->info($_info_log);
            self::$output_log .= $_info_log.PHP_EOL;

            return $manager_staff_ids;
        } catch (\Exception $e) {

            $_err_log = '上级提取异常 - '.$e->getMessage();
            $this->logger->error($_err_log);
            self::$output_log .= $_err_log.PHP_EOL;

            return [];
        }
    }
}
