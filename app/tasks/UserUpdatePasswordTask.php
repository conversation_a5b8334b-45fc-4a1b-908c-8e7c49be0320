<?php

use App\Library\ErrCode;
use App\Modules\User\Services\UserService;
use App\Library\RocketMQ;

/**
 * Class UserUpdatePasswordTask
 * php app/cli.php user_update_password main
 */
class UserUpdatePasswordTask extends RocketMqBaseTask
{
    protected $consumeOnceLimit = 10;

    public function initialize()
    {
        $this->tq = 'user-update-password';
        parent::initialize();
    }

    /**
     * 解密消息体
     * @param string $msgBody
     * @return false|array
     */
    protected function getMessageData(string $msgBody)
    {
        $data = json_decode($msgBody, true);
        if (!isset($data['jsonCondition'])) {
            return false;
        }
        if ($data['handleType'] != RocketMQ::TAG_STAFF_UPDATE_PASSWORD) {
            return false;
        }

        unset($msgBody);
        return json_decode($data['jsonCondition'], true);
    }

    /**
     * mainAction
     * @param $msgBody
     * @return bool
     */
    protected function processOneMsg($msgBody)
    {
        $this->logger->info('processOneMsg ' . base64_decode($msgBody));
        $msg_data = $this->getMessageData($msgBody);
        if (empty($msg_data)) {
            return false;
        }

        // OA 用户token各端缓存标识
        $cache_platform_item = [
            UserService::USER_LOGIN_PLATFORM_PC,
            UserService::USER_LOGIN_PLATFORM_MOBILE,
        ];

        $user_service = new UserService();
        $log          = "用户密码变更: {$msg_data['staff_id']} - ";
        foreach ($cache_platform_item as $platform) {
            $log .= $platform;

            $clear_res = $user_service->clearUserToken($platform, $msg_data['staff_id']);
            if ($clear_res['code'] == ErrCode::$SUCCESS) {
                $log .= ' token缓存清理成功; ';
            } else {
                $log .= ' token缓存清理失败; ';
            }
        }

        echo $log;
        $this->logger->info($log);
        return true;
    }

}