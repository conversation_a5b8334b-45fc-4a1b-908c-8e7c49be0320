<?php

use App\Modules\Training\Services\TaskService;

class TrainingTask extends BaseTask
{


    /**
     * 全量同步 到 培训人员池中
     *
     * @param $params
     *
     */
    public function sync_all_staffsAction($params)
    {
        try {

            $staffService = new \App\Modules\Training\Services\StaffService();
            $staffService->syncAllSTaffs();


        } catch (\Exception $exception) {

            $this->logger->error('sync_all_staffs ' .
                ' code ' . $exception->getCode() .
                ' file ' . $exception->getFile() .
                ' line ' . $exception->getLine() .
                ' message ' . $exception->getMessage() .
                ' trace ' . $exception->getTraceAsString()
            );

        }
    }

    /**
     * 增量同步 培训池 当天入职的人
     *
     * @param $params
     *
     */
    public function sync_current_day_staffsAction($params)
    {

        try {
            date_default_timezone_set("Asia/Bangkok");//设置时区为泰国时间
            $staffService = new \App\Modules\Training\Services\StaffService();
            $staffService->addWheres = [
                ['hsi.hire_date = :hire_date: ', ['hire_date' => date('Y-m-d')]]
            ];
            $staffService->syncAllSTaffs();


        } catch (\Exception $exception) {

            $this->logger->error('sync_current_staffs ' .
                ' code ' . $exception->getCode() .
                ' file ' . $exception->getFile() .
                ' line ' . $exception->getLine() .
                ' message ' . $exception->getMessage() .
                ' trace ' . $exception->getTraceAsString()
            );

        }
    }


    /**
     *
     * 每分钟跑一次
     *
     * 审核任务 过了结束时间将任务置为待审核
     *
     * @param $params
     */
    public function review_taskAction($params)
    {
        try {
            date_default_timezone_set("Asia/Bangkok");//设置时区为泰国时间

            $trainingTasks = \App\Modules\Training\Models\TrainingTask::find([
                'conditions' => ' end_time < :end_time: and status = :status:',
                'bind' => [
                    'end_time' => date('Y-m-d H:i:s'),
                    'status' => TaskService::TASK_STATUS_1,
                ]
            ]);

            foreach ($trainingTasks as $trainingTask) {
                $this->getDI()->get('logger')->info('review_task_ ' . $trainingTask->task_no);
                echo 'review_task_ ' . $trainingTask->task_no . "\r\n";
                $trainingTask->status = TaskService::TASK_STATUS_2;
                $trainingTask->save();
            }

        } catch (\Exception $exception) {
            $this->logger->error('review_task_action ' .
                ' code ' . $exception->getCode() .
                ' file ' . $exception->getFile() .
                ' line ' . $exception->getLine() .
                ' message ' . $exception->getMessage() .
                ' trace ' . $exception->getTraceAsString()
            );
        }
    }

    /**
     *
     * 每小时跑一次
     *
     * 超时修改默认消息
     *
     * @param $params
     */
    public function default_msg_ruleAction()
    {
        $log = '';
        $log .= '开始时间: '.date('Y-m-d H:i:s').PHP_EOL;
        try {
            $start_date = gmdate('Y-m-d H:i:s', strtotime('-2 days') + get_sys_time_offset() * 3600);
            $end_date   = gmdate('Y-m-d H:i:s', strtotime('+1 days') + get_sys_time_offset() * 3600);

            $data = (new TaskService())->defaultMsgRule($start_date, $end_date);
            $num  = $data ? count($data) : 0;
            $log  .= "共执行成功:$num".PHP_EOL;
        } catch (\Exception $exception) {
            $log .= '异常: '.$exception->getMessage().PHP_EOL;

            $this->logger->error('review_task_action '.
                ' code '.$exception->getCode().
                ' file '.$exception->getFile().
                ' line '.$exception->getLine().
                ' message '.$exception->getMessage().
                ' trace '.$exception->getTraceAsString()
            );
        }
        $log .= '结束时间: '.date('Y-m-d H:i:s').PHP_EOL;

        exit($log);
    }

    public function overdue_trainingAction(){

        try {
            date_default_timezone_set("Asia/Bangkok");//设置时区为泰国时间
            $service = new TaskService();
            $data = $service->overtime();
        } catch (\Exception $exception) {
            $this->logger->error('overdue_training_action ' .
                                 ' code ' . $exception->getCode() .
                                 ' file ' . $exception->getFile() .
                                 ' line ' . $exception->getLine() .
                                 ' message ' . $exception->getMessage() .
                                 ' trace ' . $exception->getTraceAsString()
            );
        }
    }

    /**
     *
     * 导入培训数据
     *
     * @param $params
     *
     */
    public function import_peopleAction($params)
    {

        try {

            error_reporting(E_ALL);
            date_default_timezone_set("Asia/Bangkok");//设置时区为泰国时间
            if (isset($params) && $params[0]) {
                if (is_file($params[0]) && is_readable($params[0])) {
                    $excelPath = $params[0];


                    $excel = new \Vtiful\Kernel\Excel(['path' => BASE_PATH]);

                    $sheet = $excel->openFile($excelPath)->openSheet();
                    $taskService = new TaskService();
                    while ($data = $sheet->nextRow([
                        \Vtiful\Kernel\Excel::TYPE_STRING,
                        \Vtiful\Kernel\Excel::TYPE_STRING,
                        \Vtiful\Kernel\Excel::TYPE_STRING,
                        \Vtiful\Kernel\Excel::TYPE_INT,
                        \Vtiful\Kernel\Excel::TYPE_STRING,
                        \Vtiful\Kernel\Excel::TYPE_STRING,
                        \Vtiful\Kernel\Excel::TYPE_STRING,
                        \Vtiful\Kernel\Excel::TYPE_STRING,
                        \Vtiful\Kernel\Excel::TYPE_STRING,
                        \Vtiful\Kernel\Excel::TYPE_STRING,
                        \Vtiful\Kernel\Excel::TYPE_STRING,
                        \Vtiful\Kernel\Excel::TYPE_STRING,
                        \Vtiful\Kernel\Excel::TYPE_STRING,
                        \Vtiful\Kernel\Excel::TYPE_STRING,
                    ])) {
//                        $data = '["Area 6","","员工入职安全培训证书",23421,"นาย อนุชา ชุมสิน","2020-6-9","2020-6-9","EHS","นายวันเฉลิม พรมสุวรรณ","","已完成","","","",""]';
//                        $data = json_decode($data, true);

                        $staffId = (int) $data[3];
                        if ($staffId) {
                            echo json_encode($data, JSON_UNESCAPED_UNICODE) . "\r\n";
                            $taskService->importTask($data);
                        }

                    }

                }

            }

        } catch (\Exception $exception) {

            var_dump(
                ' code ' . $exception->getCode() .
                ' file ' . $exception->getFile() .
                ' line ' . $exception->getLine() .
                ' message ' . $exception->getMessage() .
                ' trace ' . $exception->getTraceAsString()

            );
            $this->logger->error('overdue_training_action ' .
                ' code ' . $exception->getCode() .
                ' file ' . $exception->getFile() .
                ' line ' . $exception->getLine() .
                ' message ' . $exception->getMessage() .
                ' trace ' . $exception->getTraceAsString()
            );
        }

    }



}
