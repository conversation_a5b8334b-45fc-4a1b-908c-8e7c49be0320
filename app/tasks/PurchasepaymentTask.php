<?php

use App\Library\Enums;
use App\Models\backyard\HrStaffInfoModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Purchase\Services\PaymentService;
use App\Modules\Purchase\Models\PurchasePayment;
use App\Modules\Purchase\Models\PurchaseOrder;
use App\Modules\Purchase\Models\PurchaseOrderProduct;
use App\Models\backyard\SysDepartmentModel;
use App\Models\oa\VendorModel;
use App\Models\oa\LedgerAccountModel;
use App\Modules\Purchase\Models\PurchaseApply;
use App\Library\Validation\ValidationException;
use App\Library\ErrCode;

/**
 * Created by PhpStorm.
 * Date: 2022/4/18
 * Time: 11:12
 */
class PurchasepaymentTask extends BaseTask
{
    /**
     * 处理采购付款单发票税额总计历史数据
     * */
    public function dealTicketAction(){

        $db_obj = $this->db_oa;
        $i=0;
        $count = 0;
        $sql = "select DISTINCT(a.id) , sum(b.ticket_tax) as ticket_tax from  purchase_payment as a left join purchase_payment_receipt as b on a.id = b.ppid where a.`created_at` >'2022-01-01 00:00:00' and b.pop_id>0  group by a.id limit {$i} ,2000";
        $data          = $db_obj->query($sql);
        $deal_data = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        $total =count($deal_data);

        while (!empty($deal_data)){
            foreach ($deal_data as $value){

                $payment = PurchasePayment::getFirst([
                    'conditions' => 'id = ?0',
                    'bind' => [$value['id']]
                ]);

                if(empty($payment)){
                    continue;
                }
                $payment->ticket_amount_tax = $value['ticket_tax'] ;
                $bool= false;
               $bool= $payment->save();
             if($bool){
                 $count++;
             }


            }
            $i+=2000;
            $sql = "select DISTINCT(a.id) , sum(b.ticket_tax) as ticket_tax from  purchase_payment as a left join purchase_payment_receipt as b on a.id = b.ppid where a.`created_at` >'2022-01-01 00:00:00' and b.pop_id>0  group by a.id limit {$i} ,100";

            $data          = $db_obj->query($sql);
            $deal_data  = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        }


        echo '总数：'.$total.'条'.'成功：'.$count.'条';




    }

    /**
     * 采购付款申请单传输至SAP
     * @param $params
     */
    public function sync_sapAction($params)
    {
        $this->checkLock(__METHOD__);

        $is_exception = false;
        $log = 'begin: ' . date('Y-m-d H:i:s') . PHP_EOL;
        try {
            $country_code = get_country_code();
            //捞数起始日期
            $apply_date = ($country_code == Enums\GlobalEnums::MY_COUNTRY_CODE) ? '2023-08-23': '2023-07-31';
            //设置语种
            self::setLanguage($country_code);

            //计算本月日期1-指定日期 只执行上个月 11-31号只执行本月数据
            $start_date = date('Y-m-01');//1号
            $end_date = date("Y-m-{$this->getSapTaskSendDate()}");//指定日期
            $today = $params[0] ? $params : date('Y-m-d');
            if ($today >= $start_date && $today < $end_date) {
                //若今天在1-10之间，则需要获取上个月(上月1号含～本月1号不含)的数据同步至SAP
                $approve_at_start = date('Y-m-01 00:00:00', strtotime(date('Y-m-01') . ' -1 month'));
                $approve_at_end = date('Y-m-01 00:00:00');//本月1号不含
            } else {
                //11号之后的日期，需要获取本月（本月1号含～下月1号不含）的数据同步至SAP
                $approve_at_start = date('Y-m-01 00:00:00');//本月1号含
                $approve_at_end = date('Y-m-01 00:00:00', strtotime(date('Y-m-01') . ' +1 month'));//下月1号不含
            }

            //获取设置的费用所属公司
            $cost_company_id = (new EnumsService())->getSettingEnvValueIds('purchase_sap_company_ids');
            $sap_company_ids= SysDepartmentModel::find([
                'conditions' => 'id in ({ids:array})',
                'bind'       => ['ids' => $cost_company_id]
            ])->toArray();
            $sap_company_ids = $sap_company_ids ? array_column($sap_company_ids, 'sap_company_id', 'id') : [];

            //采购类型
            $purchase_type =  (new EnumsService())->getSettingEnvValueIds('purchase_sap_purchase_type');

            //税务代码配置
            $purchase_sap_vat = (new EnumsService())->getSettingEnvValueMap('purchase_sap_vat');
            //代扣代缴税代码
            $wht_ratio = $this->getWhtRatio()[$country_code];
            //默认币种
            $default_currency = (new EnumsService())->getSysCurrencyInfo();
            $default_currency_code = $default_currency['code'] ?? 0;
            $page = 1;
            $total = 0;
            $sap_count = 0;
            $fail_sap_count = 0;
            $deal_sap_data = $this->getSapList($page, $apply_date, $approve_at_start, $approve_at_end, $cost_company_id, $purchase_type);

            while (!empty($deal_sap_data)) {
                $total += count($deal_sap_data);
                $log .= '第' . $page . '页处理开始' . PHP_EOL;
                //获取供应商的sap编码
                $vendor_data = [];
                $vendor_ids = array_values(array_unique(array_filter(array_column($deal_sap_data, 'vendor_id'))));
                if (!empty($vendor_ids)) {
                    $vendor_list = VendorModel::find([
                        'columns' => 'vendor_id, sap_supplier_no',
                        'conditions' => 'vendor_id in ({vendor_ids:array})',
                        'bind'       => ['vendor_ids' => $vendor_ids]
                    ])->toArray();
                    $vendor_data = $vendor_list ? array_column($vendor_list, 'sap_supplier_no', 'vendor_id') : [];
                }

                //开始处理查询到的需同步至SAP的采购付款申请单
                foreach ($deal_sap_data as $item) {
                    //查询单笔采购付款申请单信息
                   $purchase_payment_info = PurchasePayment::findFirst([
                        'conditions' => 'id = :id:',
                        'bind' => ['id' => $item['id']]
                    ]);
                    if (empty($purchase_payment_info)) {
                        continue;
                    }

                    //查询单笔采购付款申请单下的发票数据
                    $purchase_payment_receipt = $purchase_payment_info->getReceipts()->toArray();
                    $purchase_payment_receipt_data = [];
                    //获取关联的采购订单行的sap行号
                    //非服务类才有关联采购订单信息
                    $pop_ids = array_values(array_unique(array_filter(array_column($purchase_payment_receipt, 'pop_id'))));
                    $purchase_order_product = PurchaseOrderProduct::find([
                        'columns' => 'id, wrs_code, sap_item_id, price, no_tax_price, no_tax_num',
                        'conditions' => 'id in ({ids:array})',
                        'bind'       => ['ids' => $pop_ids]
                    ])->toArray();
                    $purchase_order_product = $purchase_order_product ? array_column($purchase_order_product, null, 'id') : [];
                    //获取关联的核算科目账号
                    $ledger_account = [];
                    if ($item['purchase_type'] == Enums\PurchaseEnums::PURCHASE_TYPE_SERVICE) {
                        //服务类，才获取关联的核算科目的账户号
                        $ledger_account_ids = array_values(array_unique(array_filter(array_column($purchase_payment_receipt, 'ledger_account_id'))));
                        if (!empty($ledger_account_ids)) {
                            $ledger_account_list = LedgerAccountModel::find([
                                'columns' => 'id, account',
                                'conditions' => 'id in ({ids:array})',
                                'bind'       => ['ids' => $ledger_account_ids]
                            ])->toArray();
                            $ledger_account = $ledger_account_list ? array_column($ledger_account_list, 'account', 'id') : [];
                        }
                    }

                    foreach ($purchase_payment_receipt as $receipt) {
                        $wht_ratio_val = ($country_code == Enums\GlobalEnums::TH_COUNTRY_CODE) ? ($wht_ratio[$receipt['wht_type']][$receipt['wht_ratio']] ?? '') : ($wht_ratio[$receipt['wht_ratio']] ?? '');
                        $purchase_payment_receipt_data[] = [
                            'total' => ($receipt['pay_method'] == Enums\PurchaseEnums::PAYMENT_PAY_METHOD_NUMBER) ? $receipt['pay_num'] : round(($receipt['percent'] * $receipt['total']) / 100, 2 ),//数量 = 付款形式=按收货数量，则传输付款数量；付款形式=按百分比，则根据发票行的付款比例乘以订单数量，四舍五入保留2位小数
                            'not_tax_price' => $purchase_order_product[$receipt['pop_id']]['no_tax_price'] ? bcdiv($purchase_order_product[$receipt['pop_id']]['no_tax_price'], 1000, 2) : bcdiv($purchase_order_product[$receipt['pop_id']]['price'] ?? 0, 1000000, 2),//不含税单价[取发票行所关联的采购订单行上的不含税单价（SAP）。如果不含税单价（SAP）为0或者为空，传输采购订单行上的不含税单价
                            'no_tax_num' => $purchase_order_product[$receipt['pop_id']]['no_tax_num'] ?? 1,//不含税单价单位数量[取发票行所关联的采购订单行上的不含税单价单位数量（SAP）。如果不含税单价单位数量（SAP）为0或者为空，传输1]
                            'product_option_code' => $receipt['product_option_code'],//产品编号
                            'product_desc' => $receipt['product_desc'] ? str_replace('&', ' ', mb_substr($receipt['product_desc'], 0, 40)) : '' ,//产品描述
                            'wrs_code' => $purchase_order_product[$receipt['pop_id']]['wrs_code'] ?? '',//财务分类编码
                            'vat7_rate' => (in_array($country_code, [Enums\GlobalEnums::TH_COUNTRY_CODE, Enums\GlobalEnums::PH_COUNTRY_CODE]) && $item['currency'] != $default_currency_code) ? '200' : ($purchase_sap_vat[$receipt['vat7_rate']] ?? ''),//税务代码
                            'list_agency_id' => ($country_code == Enums\GlobalEnums::PH_COUNTRY_CODE && !empty($wht_ratio_val)) ? 'http://**********-one-off.sap.com/YCB5ESA8Y_' : '',//自定义代扣代缴税代码命名空间
                            'wht_ratio' => $wht_ratio_val,//代扣代缴税代码
                            'sap_item_id' => $purchase_order_product[$receipt['pop_id']]['sap_item_id'] ?? 0,//采购订单行号
                            'ledger_account' => $ledger_account[$receipt['ledger_account_id']] ?? '',//成本分摊-总账科目
                            'cost_center_name' => $receipt['cost_center_name'],//成本分摊-成本中心
                        ];
                    }

                    //开始同步SAP
                    $request_data = [
                        'ppno' => $item['ppno'],//申请单编号
                        'apply_date' => $item['apply_date'],//申请日期
                        'approve_at' => date('Y-m-d', strtotime($item['approve_at'])),//过账日期
                        'currency_text' => static::$t->_(Enums\GlobalEnums::$currency_item[$item['currency']]),//币种
                        'receipt_amount' => bcdiv($item['receipt_amount'], 1000, 2),// 发票金额总计
                        'ticket_amount_tax' => bcdiv($item['ticket_amount_tax'], 1000, 2),// 发票税额总计
                        'sap_company_id' => $sap_company_ids[$item['cost_company_id']] ?? '',//SAP维护的公司ID
                        'sap_supplier_no' => $vendor_data[$item['vendor_id']] ?? '',//SAP供应商号码
                        'pono' => $item['pono'],//采购订单号
                        'purchase_type' => $item['purchase_type'],
                        'sap_order_no' => $item['sap_order_no'] ?? '',//采购订单上的sap订单号
                        'product_type_code' => ($country_code == Enums\GlobalEnums::TH_COUNTRY_CODE || (in_array($country_code, [Enums\GlobalEnums::MY_COUNTRY_CODE, Enums\GlobalEnums::PH_COUNTRY_CODE]) && in_array($item['purchase_type'], [Enums\PurchaseEnums::PURCHASE_TYPE_STOCK, Enums\PurchaseEnums::PURCHASE_TYPE_COST]))) ? 1 : 2,//泰国：固定传1马来和菲律宾： 采购付款单所关联的采购订单的采购类型是库存类，或者辅助类的，传输1；采购付款单所关联的采购订单的的采购类型为服务类，则传输2。
                        'purchase_payment_receipt' => $purchase_payment_receipt_data
                    ];

                    $return_data  = [];
                    $z = 0;
                    while (empty($return_data) && $z < 1) {
                        $z++;
                        $return_data = PaymentService::getInstance()->createSapOrder($request_data);
                        if (isset($return_data['business_transaction_id']) && !empty($return_data['business_transaction_id'])) {
                            $sap_count++;
                            $purchase_payment_info->i_update(['sap_btd_id' => $return_data['business_transaction_id'], 'updated_at' => date('Y-m-d H:i:s')]);
                            $log .= 'success purchase_payment id:' . $item['id'] . ' ppno：' . $item['ppno'] . PHP_EOL;
                        } else {
                            $fail_sap_count++;
                            $log .= 'fail purchase_payment id:' . $item['id'] . ' ppno：' . $item['ppno'] . PHP_EOL;
                        }

                        sleep(5);
                    }
                }

                $log .= '第' . $page . '页处理结束' . PHP_EOL;

                sleep(1);
                $page += 1;
                $deal_sap_data = $this->getSapList($page, $apply_date, $approve_at_start, $approve_at_end, $cost_company_id, $purchase_type);
            }
            $log .= 'purchase_payment 总数:' . $total . '条数' . PHP_EOL;
            $log .= 'purchase_payment 成功:' . $sap_count . '条数' . PHP_EOL;
            $log .= 'purchase_payment 失败:' . $fail_sap_count . '条数' . PHP_EOL;
        } catch (Exception $e) {
            $is_exception = true;
            $log .= 'purchase_payment_sync_sap-exception: ' . $e->getMessage();
        }
        if ($is_exception) {
            $this->logger->warning($log);
        } else {
            $this->logger->info($log);
        }

        $log .= 'end: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $this->clearLock(__METHOD__);
        exit($log);
    }

    /**
     * 返回可同步至SAP的采购付款申请单列表
     * @param int $page_num 当天第几页
     * @param string $apply_date 申请日期
     * @param string $approve_at_start 审批通过日期-起始
     * @param string $approve_at_end 审批通过日期-截止
     * @param array $cost_company_id 费用所属公司ID组
     * @param array $purchase_type 采购类型组
     * @return mixed
     */
    private function getSapList($page_num, $apply_date, $approve_at_start, $approve_at_end, $cost_company_id, $purchase_type)
    {
        $page_size = 100;
        $offset = $page_size * ($page_num - 1);
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('pp.id, pp.ppno, pp.apply_date, pp.approve_at, pp.receipt_amount, pp.ticket_amount_tax, pp.currency, pp.vendor_id, pp.cost_company_id, po.pono, po.purchase_type, po.sap_order_no');
        $builder->from(['pp' => PurchasePayment::class]);
        $builder->leftJoin(PurchaseOrder::class, 'po.id = pp.po_id', 'po');
        $builder->where('pp.is_link_pa = :is_link_pa: and pp.status = :status: and pp.method = :method: and pp.apply_date > :apply_date:', ['is_link_pa' => Enums\PurchaseEnums::IS_LINK_PO_NO, 'status' => Enums::WF_STATE_APPROVED, 'method' => Enums\PurchaseEnums::PAYMENT_PAY_METHOD_1, 'apply_date' => $apply_date]);
        $builder->andWhere('pp.sap_btd_id = "" OR pp.sap_btd_id IS NULL');
        $builder->inWhere('pp.cost_company_id', $cost_company_id);
        $builder->andWhere('pp.approve_at >= :approve_at_start: and pp.approve_at < :approve_at_end:', ['approve_at_start' => $approve_at_start, 'approve_at_end' => $approve_at_end]);
        $builder->inWhere('po.purchase_type', $purchase_type);
        $builder->limit($page_size, $offset);
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * SAP代扣代缴税代码码与OA wht_ratio映射关系
     * @return array
     */
    private function getWhtRatio()
    {
        return [
            Enums\GlobalEnums::TH_COUNTRY_CODE => [
                1 => [//wht_type = pnd3
                    '15' => '004',
                    '10' => '003',
                    '3' => '023',
                    '1' => '024'
                ],
                2 => [//wht_type = pnd53
                    '5' => '068',
                    '3' => '064',
                    '2' => '071',
                    '1.5' => '063',
                    '1' => '082'
                ]
            ],
            Enums\GlobalEnums::PH_COUNTRY_CODE => [
                '2' => '6100',
                '1' => '6200',
                '5' => '6300',
                '25' => '6400',
                '10' => '6500',
                '15' => '6600',
                '12' => '6700'
            ],
            Enums\GlobalEnums::MY_COUNTRY_CODE => [
            ]
        ];
    }

    /**
     * 同步PAR 单币种与PUR/PO单币种一致，但汇率不一致的情况：将PUR/PO汇率 固化到 PAR单汇率
     *
     * 说明: 关联 的 PUR / PO 单汇率为0时, 该PAR 汇率无需更新
     *
     * php app/cli.php purchasepayment handle_par_exchange_rate
     */
    public function handle_par_exchange_rateAction()
    {
        $log = '任务名称: ' . __FUNCTION__ . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $logger_type = 'info';

        try {
            /******* 一、更新关联了PUR的PAR单据 **********/
            // 1. 查找PAR与PUR币种一致 但 汇率不一致的 PAR单据
            // 如果PUR的汇率为0, 则PAR汇率无需更新
            $columns = [
                'par.id AS par_id',
                'par.currency AS par_currency',
                'par.exchange_rate AS par_exchange_rate',
                'pur.id AS pur_id',
                'pur.currency AS pur_currency',
                'pur.exchange_rate AS pur_exchange_rate',
            ];
            $builder = $this->modelsManager->createBuilder();
            $builder->columns($columns);
            $builder->from(['par' => PurchasePayment::class]);
            $builder->leftjoin(PurchaseApply::class, 'pur.id = par.pa_id', 'pur');
            $builder->where('par.is_link_pa = 1 AND par.currency = pur.currency AND par.exchange_rate != pur.exchange_rate');
            $builder->andwhere('pur.exchange_rate > 0');
            $par_list_linked_pur = $builder->getQuery()->execute()->toArray();

            $log .= PHP_EOL . '共有 ' . count($par_list_linked_pur) . ' 条par单据的汇率需更新[linked pur]' . PHP_EOL;

            if (!empty($par_list_linked_pur)) {
                $log .= '待处理的par单据[linked pur]: ' . json_encode($par_list_linked_pur, JSON_UNESCAPED_UNICODE) . PHP_EOL;

                // par_id => pur_exchange_rate
                $par_list_linked_pur = array_column($par_list_linked_pur, 'pur_exchange_rate', 'par_id');

                // 2. 更新PAR单据
                $par_models_linked_pur = PurchasePayment::find([
                    'conditions' => 'id IN ({ids:array})',
                    'bind' => ['ids' => array_keys($par_list_linked_pur)]
                ]);

                $par_null_count = 0;
                $par_null_ids = '';
                $par_save_fail_count = 0;
                $par_save_success_count = 0;
                $par_save_fail_log = '';
                foreach ($par_models_linked_pur as $par) {
                    $new_par_exchange_rate = $par_list_linked_pur[$par->id] ?? null;
                    if (is_null($new_par_exchange_rate)) {
                        $par_null_count++;
                        $par_null_ids .= $par->id . '; ';
                        continue;
                    }

                    $save_data = [
                        'exchange_rate' => $new_par_exchange_rate,
                    ];
                    if ($par->i_update($save_data) === false) {
                        $par_save_fail_count++;
                        $par_save_fail_log .= "par: {$par->id} -> {$new_par_exchange_rate} 更新失败, 原因可能是: " . get_data_object_error_msg($par) . PHP_EOL;
                        continue;
                    }

                    $par_save_success_count++;
                }

                $log .= '未找到对应pur的par: ' . $par_null_count . ' 条; par_id: ' . $par_null_ids . PHP_EOL;
                $log .= '更新成功: ' . $par_save_success_count . ' 条;' . PHP_EOL;
                $log .= '更新失败: ' . $par_save_fail_count . ' 条;' . PHP_EOL;
                $log .= '失败日志: ' . $par_save_fail_log . PHP_EOL;
            }

            /********* 二、*********/
            // 1. 查找PAR与PO币种一致 但 汇率不一致的 PAR单据
            // 如果PO的汇率为0, 则PAR汇率无需更新
            $columns = [
                'par.id AS par_id',
                'par.currency AS par_currency',
                'par.exchange_rate AS par_exchange_rate',
                'po.id AS po_id',
                'po.currency AS po_currency',
                'po.exchange_rate AS po_exchange_rate',
            ];
            $builder = $this->modelsManager->createBuilder();
            $builder->columns($columns);
            $builder->from(['par' => PurchasePayment::class]);
            $builder->leftjoin(PurchaseOrder::class, 'par.po_id = po.id', 'po');
            $builder->where('po.currency = par.currency AND po.exchange_rate != par.exchange_rate');
            $builder->andwhere('par.is_link_pa = 0 AND po.exchange_rate > 0');
            $par_list_linked_po = $builder->getQuery()->execute()->toArray();

            $log .= '共有 ' . count($par_list_linked_po) . ' 条par单据的汇率需更新[linked po]' . PHP_EOL;

            if (empty($par_list_linked_po)) {
                throw new ValidationException('无符合条件的par, par无需更新', ErrCode::$VALIDATE_ERROR);
            }

            $log .= '待处理的par单据[linked po]: ' . json_encode($par_list_linked_po, JSON_UNESCAPED_UNICODE) . PHP_EOL;
            
            // par_id => po_exchange_rate
            $par_list_linked_po = array_column($par_list_linked_po, 'po_exchange_rate', 'par_id');

            // 2. 更新PO单据
            $par_models_linked_po = PurchasePayment::find([
                'conditions' => 'id IN ({ids:array})',
                'bind' => ['ids' => array_keys($par_list_linked_po)]
            ]);

            $par_null_count = 0;
            $par_null_ids = '';
            $par_save_fail_count = 0;
            $par_save_success_count = 0;
            $par_save_fail_log = '';
            foreach ($par_models_linked_po as $par) {
                $new_par_exchange_rate = $par_list_linked_po[$par->id] ?? null;
                if (is_null($new_par_exchange_rate)) {
                    $par_null_count++;
                    $par_null_ids .= $par->id . '; ';
                    continue;
                }

                $save_data = [
                    'exchange_rate' => $new_par_exchange_rate,
                ];
                if ($par->i_update($save_data) === false) {
                    $par_save_fail_count++;
                    $par_save_fail_log .= "par: {$par->id} -> {$new_par_exchange_rate} 更新失败, 原因可能是: " . get_data_object_error_msg($par) . PHP_EOL;
                    continue;
                }

                $par_save_success_count++;
            }

            $log .= '未找到对应po的par: ' . $par_null_count . ' 条; par_id: ' . $par_null_ids . PHP_EOL;
            $log .= '更新成功: ' . $par_save_success_count . ' 条;' . PHP_EOL;
            $log .= '更新失败: ' . $par_save_fail_count . ' 条;' . PHP_EOL;
            $log .= '失败日志: ' . $par_save_fail_log . PHP_EOL;

        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;

        }  catch (Exception $e) {
            $logger_type = 'error';
            $log .= $e->getMessage() . PHP_EOL;
        }

        $this->logger->$logger_type($log);
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        exit($log);
    }

    /**
     * 发送预计清关日期提醒邮件
     *
     * php app/cli.php purchasepayment sendEmailToNoticeClearance
     * @return void
     */
    public function sendEmailToNoticeClearanceAction()
    {
        $logMsg = '任务名称: ' . __FUNCTION__ . PHP_EOL;
        $logMsg .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $logger_type = 'info';

        try {
            // 1. 获取配置信息
            $excludedPurchaseTypes = EnumsService::getInstance()->getSettingEnvValueIds('not_display_clearance_order_type');
            $notificationEmails = EnumsService::getInstance()->getSettingEnvValueIds('clearance_notice_email');
            
            // 检查通知邮箱配置
            if (empty($notificationEmails)) {
                $this->logger->warning("未配置通知邮箱，任务终止");
                return;
            }

            // 2. 获取归属地不等于马来西亚的供应商
            $vendorIds = $this->getNonMalaysianVendors();
            if (empty($vendorIds)) {
                $this->logger->info('无归属地不等于马来西亚的供应商');
                return;
            }

            // 3. 获取需要提醒的采购付款单
            $paymentList = $this->getPaymentsNeedingReminder($vendorIds, $excludedPurchaseTypes);
            if (empty($paymentList)) {
                $this->logger->info('无需要提醒的采购付款单数据');
                return;
            }

            // 4. 获取申请人邮箱信息
            $creatorIds = array_column($paymentList, 'create_id');
            $staffEmails = $this->getStaffEmails($creatorIds);
            
            // 5. 按申请人分组采购单
            $groupedPayments = $this->groupPaymentsByCreator($paymentList);
            
            // 6. 发送邮件
            $this->sendNotificationEmails($notificationEmails, $paymentList);
            $this->sendCreatorEmails($groupedPayments, $staffEmails);
            
            $logMsg .= '处理完成，共发送提醒 ' . count($paymentList) . ' 条';
            $this->logger->$logger_type($logMsg);
            
        } catch (Exception $e) {
            $logger_type = 'error';
            $logMsg .= '异常: ' . $e->getMessage() . PHP_EOL;
            $this->logger->$logger_type($logMsg);
        }
    }
    
    /**
     * 获取归属地不等于马来西亚的供应商ID列表
     * @return array
     */
    private function getNonMalaysianVendors()
    {
        $vendorList = VendorModel::find([
            'conditions' => 'ownership != :ownership:',
            'bind' => [
                'ownership' => VendorTask::MY_OWNER,
            ],
            'columns' => 'vendor_id',
        ])->toArray();
        
        return array_column($vendorList, 'vendor_id');
    }
    
    /**
     * 获取需要提醒的采购付款单
     * @param array $vendorIds 供应商ID列表
     * @param array $excludedPurchaseTypes 排除的采购类型
     * @return array
     */
    private function getPaymentsNeedingReminder($vendorIds, $excludedPurchaseTypes)
    {
        $columns = [
            'par.id',
            'par.ppno',
            'par.create_id'
        ];
        
        $builder = $this->modelsManager->createBuilder();
        $builder->columns($columns);
        $builder->from(['par' => PurchasePayment::class]);
        $builder->leftjoin(PurchaseOrder::class, 'puro.id = par.po_id', 'puro');
        $builder->where('par.status = :status:' , ['status' => Enums::PURCHASE_APPLY_STATUS_APPROVAL]);
        $builder->inWhere('par.pay_status', [Enums::PAYMENT_PAY_STATUS_PENDING, Enums::PAYMENT_PAY_STATUS_PAY]);
        
        if (!empty($excludedPurchaseTypes)) {
            $builder->notInWhere('puro.purchase_type', $excludedPurchaseTypes);
        }
        
        $builder->andWhere('par.method = :method:', ['method' => Enums\PurchaseEnums::PAYMENT_PAY_METHOD_2]);
        $builder->andWhere('par.is_clearance = :is_clearance:', ['is_clearance' => Enums\OrdinaryPaymentEnums::QUERY_CLEARANCE_QUEST_YES]);
        $builder->inWhere('par.vendor_id', $vendorIds);
        $builder->andWhere('par.expect_clearance_date is not null and par.actual_clearance_date is null');
        $builder->andWhere('par.expect_clearance_date < CURRENT_DATE()');
        
        return $builder->getQuery()->execute()->toArray();
    }
    
    /**
     * 获取员工邮箱信息
     * @param array $staffIds 员工ID列表
     * @return array
     */
    private function getStaffEmails($staffIds)
    {
        if (empty($staffIds)) {
            return [];
        }
        
        $staffInfo = HrStaffInfoModel::find([
            'conditions' => 'staff_info_id in({staff_info_ids:array})',
            'bind' => [
                'staff_info_ids' => $staffIds
            ],
            'columns' => 'staff_info_id,email',
        ])->toArray();
        
        return array_column($staffInfo, 'email', 'staff_info_id');
    }
    
    /**
     * 按申请人分组采购单
     * @param array $paymentList 采购付款单列表
     * @return array
     */
    private function groupPaymentsByCreator($paymentList)
    {
        $groupedPayments = [];
        foreach ($paymentList as $payment) {
            $createId = $payment['create_id'];
            if (!isset($groupedPayments[$createId])) {
                $groupedPayments[$createId] = [];
            }
            $groupedPayments[$createId][] = $payment['ppno'];
        }
        
        return $groupedPayments;
    }
    
    /**
     * 发送通知邮件给配置的邮箱
     * @param array $emails 邮箱列表
     * @param array $paymentList 采购付款单列表
     */
    private function sendNotificationEmails($emails, $paymentList)
    {
        $title = 'Reminder for filling in customs clearance date on PAR 采购付款申请单清关日期填写提醒';
        $emailTemplate = '您好，有以下采购付款单，已到了预计清关日期，请及时去系统"OA-采购管理-采购申请-采购付款申请单-补充附件"，补充清关日期。单号：%s</br> Hello, the following purchase payment orders have reached the estimated customs clearance date. Please go to the system "OA-Purchase Management-Purchase Application-Purchase Payment Application-Supplementary Attachments" in time to add the customs clearance date. Order number: %s';
        
        $paymentNumbers = array_column($paymentList, 'ppno');
        $paymentNumbersStr = join(',', $paymentNumbers);
        $emailContent = sprintf($emailTemplate, $paymentNumbersStr, $paymentNumbersStr);
        
        $logData = ["emails" => $emails, "title" => $title, "html" => $emailContent, "ccEmails" => []];
        
        if ($this->mailer->sendAsync($emails, $title, $emailContent, [], [])) {
            $this->logger->info('通知邮件发送成功: ' . json_encode($logData, JSON_UNESCAPED_UNICODE));
            echo date('Ymd H:i:s') . " 通知邮件发送成功:" . json_encode($logData, JSON_UNESCAPED_UNICODE) . PHP_EOL;
        } else {
            $this->logger->warning('通知邮件发送失败: ' . json_encode($logData, JSON_UNESCAPED_UNICODE));
            echo date('Ymd H:i:s') . " 通知邮件发送失败:" . json_encode($logData, JSON_UNESCAPED_UNICODE) . PHP_EOL;
        }
    }
    
    /**
     * 发送邮件给各个申请人
     * @param array $groupedPayments 按申请人分组的采购单
     * @param array $staffEmails 员工邮箱信息
     */
    private function sendCreatorEmails($groupedPayments, $staffEmails)
    {
        $title = 'Reminder for filling in customs clearance date on PAR 采购付款申请单清关日期填写提醒';
        $emailTemplate = '您好，有以下采购付款单，已到了预计清关日期，请及时去系统"OA-采购管理-采购申请-采购付款申请单-补充附件"，补充清关日期。单号：%s</br> Hello, the following purchase payment orders have reached the estimated customs clearance date. Please go to the system "OA-Purchase Management-Purchase Application-Purchase Payment Application-Supplementary Attachments" in time to add the customs clearance date. Order number: %s';
        
        foreach ($groupedPayments as $creatorId => $paymentNumbers) {
            $staffEmail = $staffEmails[$creatorId] ?? '';
            if (empty($staffEmail)) {
                $this->logger->notice(sprintf('申请人ID %d 邮箱为空，发送清关提醒失败 %s', $creatorId, json_encode($paymentNumbers)));
                continue;
            }
            
            $paymentNumbersStr = join(',', $paymentNumbers);
            $emailContent = sprintf($emailTemplate, $paymentNumbersStr, $paymentNumbersStr);
            
            $logData = ["email" => $staffEmail, "title" => $title, "html" => $emailContent];
            
            if ($this->mailer->sendAsync([$staffEmail], $title, $emailContent, [], [])) {
                $this->logger->info('申请人邮件发送成功: ' . json_encode($logData, JSON_UNESCAPED_UNICODE));
                echo date('Ymd H:i:s') . " 申请人邮件发送成功:" . json_encode($logData, JSON_UNESCAPED_UNICODE) . PHP_EOL;
                sleep(1); // 减少发送间隔，提高效率
            } else {
                $this->logger->warning('申请人邮件发送失败: ' . json_encode($logData, JSON_UNESCAPED_UNICODE));
                echo date('Ymd H:i:s') . " 申请人邮件发送失败:" . json_encode($logData, JSON_UNESCAPED_UNICODE) . PHP_EOL;
            }
        }
    }
}
