<?php

use App\Library\Enums\BankFlowEnums;
use App\Library\Enums\DownloadCenterEnum;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Services\DownloadCenterService;
use App\Modules\Pay\Models\Payment;
use App\Modules\Pay\Services\PayService;
use App\Modules\Wages\Services\AddService;
use App\Modules\Wages\Services\ListService;
use App\Modules\Wages\Services\WagesFlowService;
use App\Library\Enums;
use App\Library\Enums\WagesEnums;
use App\Modules\Wages\Models\WagesModel;
use App\Models\oa\PaymentPushRecordModel;


class WagesTask extends BaseTask
{
    /**
     * 众包/快递个人代理数据-创建审批流
     * php app/cli.php  wages wages_create_request
     */
    public function wages_create_requestAction()
    {
        $this->checkLock(__METHOD__);

        $process_name = str_replace('Action', '', __FUNCTION__);
        $log          = '任务名称: ' . $process_name . PHP_EOL;
        $log          .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log          .= '初始内存: ' . memory_usage() . PHP_EOL;
        $is_exception = true;
        try {
            $task_model = (new AddService())->getAddFlowTask();
            if (!empty($task_model->toArray())) {
                $service            = new WagesFlowService();
                $status =  Enums::WF_STATE_PENDING;
                foreach ($task_model as $index => $wages) {
                    // 每个单据的处理日志
                    $_log = '众包/快递个人代理数据处理-待处理单号 wages.no: ' . $wages->no . PHP_EOL;

                    $wages->execute_status = WagesEnums::WAGES_EXECUTE_STATUS_ING;
                    if ($wages->save() === false) {
                        $_log .= '众包/快递个人代理数据处理状态修改失败[待处理->处理中]' . PHP_EOL;
                        $this->logger->notice($_log);
                        continue;
                    }

                    $db = $this->getDI()->get('db_oa');
                    $db->begin();
                    try {
                        $_log          .= '任务ID: ' . $wages->id . PHP_EOL;
                        $execute_status = WagesEnums::WAGES_EXECUTE_STATUS_SUCCESS;
                        $user           = ['id' => $wages->apply_id, 'name' => $wages->apply_name];
                        $request_id     = $service->createRequest($wages->id, $user);

                        $_log          .= '审批工作流ID为: ' . $request_id . PHP_EOL;
                        if (!$request_id) {
                            $_log .= '众包/快递个人代理数据-创建审批流任务失败' . PHP_EOL;
                            throw new Exception($_log, ErrCode::$BUSINESS_ERROR);
                        }

                        // 审核通过 增加到支付模块
                        PayService::getInstance()->saveOne($wages);
                        $db->commit();

                        $status = Enums::WF_STATE_APPROVED;

                    } catch (Exception $e) {
                        $db->rollback();
                        $execute_status = WagesEnums::WAGES_EXECUTE_STATUS_FAIL;

                        $_log          .= '众包数据-审批流或支付模块数据写入异常, 原因可能是: ' . $e->getMessage() . PHP_EOL;
                        $this->logger->warning($_log);
                    }

                    $save_res = [
                        'execute_status'     => $execute_status, // 任务状态
                        'status'             => $status // 单据审批状态
                    ];

                    $wages_success_task = $db->updateAsDict(
                        (new WagesModel())->getSource(), $save_res, ['conditions' => "id = {$wages->id}"]
                    );
                    if (!$wages_success_task) {
                        $_log .= '众包/快递个人代理数据状态修改失败: ' . json_encode($save_res, JSON_UNESCAPED_UNICODE) . PHP_EOL;
                        $this->logger->warning($_log);
                    } else {
                        $_log .= '众包/快递个人代理数据状态修改成功: ' . json_encode($save_res, JSON_UNESCAPED_UNICODE) . PHP_EOL;
                       $this->logger->info($_log);
                    }

                    // 打印每个单据的处理结果
                    echo $_log;

                    // 将每个单据日志写入总日志中
                    $log .= $_log;

                    // 每批次累了就歇会
                    if ($index % 1000 == 0) {
                        sleep(5);
                    }
                }
            } else {
                $log .= '没有待处理的数据' . PHP_EOL;
            }
        } catch (Exception $e) {
            $is_exception = false;
            $log          .= $e->getMessage() . PHP_EOL;
        }
        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        if ($is_exception) {
            $this->logger->info('众包/快递个人代理脚本执行数据' . $log);
        } else {
            $this->logger->error('众包/快递个人代理脚本执行异常' . $log);
        }

        $this->clearLock(__METHOD__);
        exit($log);
    }



    /**
     * 众包/个人代理数据-flash_pay 支付状态推送到众包
     * php app/cli.php  wages wages_flash_pay_to_request
     */
    public function wages_flash_pay_to_requestAction()
    {
        $this->checkLock(__METHOD__);

        $process_name = str_replace('Action', '', __FUNCTION__);
        $log          = '任务名称: ' . $process_name . PHP_EOL;
        $log          .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log          .= '初始内存: ' . memory_usage() . PHP_EOL;
        try {
            $payment_push_record = PaymentPushRecordModel::find(
                [
                    'conditions' => 'status = :status:',
                    'bind'       => [
                        'status'         => Enums::PAYMENT_PUSH_RECORD_STATUS_PENDING
                    ]
                ]
            );
            $is_exception = true;
            if (!empty($payment_push_record->toArray())) {

                $res   = [];

                $wages_add_service = new AddService();
                foreach ($payment_push_record as $record) {
                    $record->status = WagesEnums::WAGES_EXECUTE_STATUS_ING;
                    if ($record->save() === false) {
                        $this->logger->error('众包/个人代理数据-flash_pay 支付状态推送到众包/个人代理-修改状态失败: ' . $log);
                        $log .= '众包/个人代理数据-flash_pay 支付状态推送到众包/个人代理-修改状态失败:' . $record->no . PHP_EOL;
                    }

                    $wages = $this->getWagesObj($record);
                    $item  = $this->getPaymentObj($record);
                    if (!empty($wages) && !empty($item)) {
                        if ($wages->apply_type == WagesEnums::APPLY_TYPE_CROWD_SOURCING) {
                            //众包
                            $res = $wages_add_service->postCrowdSourcingPayStatus($wages, $item);
                        } else if ($wages->apply_type == WagesEnums::APPLY_TYPE_PERSONAL_AGENT) {
                            //个人代理
                            $res = $wages_add_service->postBIPayStatus($wages, $item);
                        }
                    }
                    $record->status      = !empty($res) && $res['code'] == ErrCode::$SUCCESS ? WagesEnums::WAGES_EXECUTE_STATUS_SUCCESS : WagesEnums::WAGES_EXECUTE_STATUS_FAIL;
                    $record->push_result = json_encode($res, JSON_UNESCAPED_UNICODE);
                    $record->push_at     = date('Y-m-d H:i:s', time());
                    if ($record->save() === false) {
                        $this->logger->error('众包/个人代理数据-flash_pay 支付状态推送到众包-修改状态失败: ' . $log);
                        $log .= '众包/个人代理数据-flash_pay 支付状态推送到众包-修改状态失败:' . $record->no . PHP_EOL;
                    }
                    $log .= '支付状态推送到众包/个人代理 (' . $record->no . ')' . PHP_EOL;
                }
            } else {
                $log .= '没有待处理的数据' . PHP_EOL;
            }

        } catch (Exception $e) {
            $is_exception = false;
            $log          .= $e->getMessage() . PHP_EOL;
        }
        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        if ($is_exception) {
            $this->logger->info('支付状态推送到众包/个人代理脚本执行数据' . $log);
        } else {
            $this->logger->error('支付状态推送到众包/个人代理脚本执行异常' . $log);
        }

        $this->clearLock(__METHOD__);
        exit($log);
    }


    /**
    * 获取薪酬扣款-众包数据对象
    * @param object $params
    * @return object

    **/
    public function getWagesObj($params)
    {
        return  WagesModel::findFirst(
            [
                'conditions' => 'no = :no:',
                'bind'       => [
                    'no'         => $params->no,

                ]
            ]
        );
    }


    /**
     * 获取薪酬扣款-众包数据对象
     * @param object $params
     * @return object

     **/
    public function getPaymentObj($params)
    {
        return    Payment::findFirst(
            [
                'conditions' => 'no = :no:',
                'bind'       => [
                    'no'         => $params->no
                ]
            ]
        );
    }

    /**
     * OA-薪酬扣款审批-数据查询-导出excel
     * 每5分钟执行一次
     */
    public function dataExportAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log          = '任务名称: ' . $process_name . PHP_EOL;
        $log          .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log          .= '初始内存: ' . memory_usage() . PHP_EOL;
        $is_exception = false;

        try {
            // 1.提取待处理的下载任务(最早入队的)
            $task_model = DownloadCenterService::getInstance()->getFirstPendingTask(DownloadCenterEnum::WAGES_PAYMENT_APPROVAL_EXPORT);
            if (empty($task_model)) {
                throw new ValidationException('没有待下载的任务', ErrCode::$VALIDATE_ERROR);
            }

            // 2.1解析参数
            $params             = !empty($task_model->args_json) ? json_decode(base64_decode($task_model->args_json,
                true), true) : [];
            $params['language'] = $params['language'] ?? DownloadCenterEnum::DETAUL_LANGUAGE;

            $log .= '任务ID: ' . $task_model->id . PHP_EOL;
            $log .= '任务参数: ' . json_encode($params, JSON_UNESCAPED_UNICODE) . PHP_EOL;


            // 2.2设置系统语言
            ListService::setLanguage($params['language']);
            $service = new ListService();
            // 2.3获取数据
            $excel_data = $service->handleExportData($params,$task_model->staff_info_id);
            // 2.4获取Excel表头
            $excel_header = $service->getExportExcelHeaderFields();
            // 2.5生成Excel
            $excel_result = $service->exportExcel($excel_header, $excel_data,
                $task_model->file_name);
            $log          .= 'Excel结果: ' . json_encode($excel_result, JSON_UNESCAPED_UNICODE) . PHP_EOL;
            unset($excel_data);

            // 3.更新任务
            if (isset($excel_result['code']) && $excel_result['code'] == ErrCode::$SUCCESS && !empty($excel_result['data'])) {
                $save_model_result = DownloadCenterService::getInstance()->saveTask($task_model,
                    DownloadCenterEnum::TASK_STATUS_SUCCESS, $excel_result['data']);
                if ($save_model_result === false) {
                    $log .= '任务状态: 更新失败, 原因可能是: ' . get_data_object_error_msg($task_model) . PHP_EOL;
                } else {
                    $log .= '任务状态: 更新成功' . PHP_EOL;
                }
            } else {
                // 记录错误日志
                $log .= '任务状态: 未更新, 生成Excel失败, 待下次重新执行' . PHP_EOL;
            }
        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;
        } catch (Exception $e) {
            $log          .= $e->getMessage() . PHP_EOL;
            $is_exception = true;
        }
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        if ($is_exception) {
            $this->logger->error('薪酬扣款审批-数据查询-导出任务: ' . $log);
        } else {
            $this->logger->info('薪酬扣款审批-数据查询-导出任务: ' . $log);
        }

        echo $log;
    }








}
