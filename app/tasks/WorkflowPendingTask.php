<?php
/**
 * 审批流待办统计任务处理
 */

use App\Library\ApiClient;
use App\Library\Enums\StaffInfoEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\RedisClient;
use App\Library\Validation\ValidationException;
use App\Library\Exception\BusinessException;
use App\Library\Enums\WorkflowPendingEnums;
use App\Models\oa\SettingEnvModel;
use App\Models\oa\WorkflowRequestModel;
use App\Models\oa\WorkflowRequestNodeAt;
use App\Models\oa\WorkflowRequestNodeAuditorModel;
use App\Modules\Common\Services\WorkflowPendingService;
use App\Modules\Common\Services\SmsService;
use App\Modules\Common\Services\EnumsService;
use App\Models\oa\SmsPendingDataModel;
use App\Modules\Transfer\Models\AuditApprovalModel;
use App\Modules\Workflow\Services\WorkflowEventService;
use App\Repository\HrStaffRepository;
use App\Repository\DepartmentRepository;
use App\Library\Enums;
use App\Util\RedisKey;

use const App\Repository\DepartmentRepository;

class WorkflowPendingTask extends BaseTask
{
    // dev/test/tra 环境发信记录
    static protected $tmp_send_records = [];

    // 验收工号 和 手机号 和 手机号所属国家
    protected $test_staff_id     = '';
    protected $test_staff_mobile = '';
    protected $test_staff_nation = '';

    // 手机号黑名单
    protected $mobile_blacklist = [];

    // 不发信黑名单(工号)
    protected $staff_blacklist = [];

    // 非白名单发送时段
    protected $non_whitelist_sending_periods = [];

    // 手机号黑名单配置key
    protected $mobile_blacklist_setting_key = 'workflow_pending_sms_blacklist';

    // 工号黑名单配置key
    protected $staff_blacklist_setting_key = 'approval_pending_sms_staff_blacklist';

    // 非白名单待办人的发信时段配置key
    protected $non_whitelist_sending_period_setting_key = 'approval_pending_sms_sending_period';

    // 当前发信时段(小时)
    protected $current_time_hour;

    // 待办数据发信任务日志
    protected $pending_send_sms_task_log;

    // 待办数据push队列
    const PENDING_DATA_QUEUE_NAME = 'workflow_pending_sms_push';

    /**
     * 白名单用户 ID
     * @var array
     */
    public  $whitelistStaffIds  = [];
    /**
     * 短信发送统一入口
     *
     * @param $params
     *
     * 说明：支持验收环节指定工号 和 指定手机号, 如果没有指定, 则仍按预配置的手机号动态分配工号进行
     *
     * php app/cli.php workflow_pending send_sms 10000 15100010002 CN
     *
     */
    public function send_smsAction($params)
    {
        $this->checkLock(__METHOD__);

        $sys_country_code                = get_country_code();
        $this->pending_send_sms_task_log = PHP_EOL . "发送待办短信Task[$sys_country_code]" . PHP_EOL;
        $this->pending_send_sms_task_log .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        // 默认日志级别
        $log_level = 'info';
        try {
            // 测试用
            $this->test_staff_id     = isset($params[0]) && !empty($params[0]) ? $params[0] : '';
            $this->test_staff_mobile = isset($params[1]) && !empty($params[1]) ? $params[1] : '';
            $this->test_staff_nation = isset($params[2]) && !empty($params[2]) ? strtoupper($params[2]) : '';

            // 当前时段设置(单位: 小时)
            $this->current_time_hour = date('G');

            // 手机号黑名单, 在黑名单中的, 无需发送短信
            $this->mobile_blacklist = EnumsService::getInstance()->getSettingEnvValueIds($this->mobile_blacklist_setting_key);

            // 工号黑名单, 工号在黑名单中的, 无需发信
            $this->staff_blacklist = EnumsService::getInstance()->getSettingEnvValueIds($this->staff_blacklist_setting_key);

            // 非白名单待办人需发短信的时段, 当前时段在配置时段的, 需发送短信; 否则, 不发
            $this->non_whitelist_sending_periods = EnumsService::getInstance()->getSettingEnvValueIds($this->non_whitelist_sending_period_setting_key);

            // 待办数据短信发送
            $this->sendPendingSms($sys_country_code);
        } catch (ValidationException $e) {
            $this->pending_send_sms_task_log .= $e->getMessage() . PHP_EOL;
        } catch (BusinessException $e) {
            $log_level                       = 'warning';
            $this->pending_send_sms_task_log .= $e->getMessage() . PHP_EOL;
        } catch (\Exception $e) {
            $log_level                       = 'error';
            $this->pending_send_sms_task_log .= $e->getMessage() . PHP_EOL;
        }

        $this->pending_send_sms_task_log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        $this->clearLock(__METHOD__);

        $this->logger->$log_level($this->pending_send_sms_task_log);
        exit($this->pending_send_sms_task_log);
    }


    /**
     * 初始化审批待办数据
     *
     * @param $sys_country_code
     * @return array
     * @throws BusinessException
     * @throws ValidationException
     */
    protected function initPendingData($sys_country_code)
    {
        $this->pending_send_sms_task_log .= PHP_EOL . "(一) 审批人待办数据初始化[$sys_country_code]" . PHP_EOL;
        $this->pending_send_sms_task_log .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        // 可正常发短信的工号
        $normal_staff_ids = [];

        // 1 获取OA系统审批待办
        // 1.1 取系统所在国家的待办
        if ($sys_country_code == GlobalEnums::TH_COUNTRY_CODE) {
            // 泰国: 所有待办(白名单 + 非白名单)
            $whitelist_strategy = WorkflowPendingEnums::GET_PENDING_DATA_STRATEGY_ALL;
        } else {
            // 其他国家: 取非白名单待办
            $whitelist_strategy = WorkflowPendingEnums::GET_PENDING_DATA_STRATEGY_FILTER_WHITELIST;
        }
        $result                          = WorkflowPendingService::getInstance()->getWorkflowPendingData(WorkflowPendingEnums::PENDING_DATA_TIMEOUT_MIN,
            $whitelist_strategy);
        $this->pending_send_sms_task_log .= "本次待办数据[$sys_country_code]共 " . count($result['data']) . ' 条' . PHP_EOL;

        // 存储对应国家的数据
        $all_data = [
            $sys_country_code => $result['data'],
        ];

        // 1.2 泰国: 需汇总其他国家白名单待办
        if ($sys_country_code == GlobalEnums::TH_COUNTRY_CODE) {
            $other_country_api_list = WorkflowPendingEnums::$need_collect_whitelist_pending_data_api_list[get_runtime_env()] ?? [];

            // 汇总合其他国家数据到泰国
            $header = [
                'content-type: application/json',
                'pending-auth-code: ' . WorkflowPendingEnums::HTTP_GET_DATA_AUTH_CODE,
            ];

            foreach ($other_country_api_list as $country_code => $country_api) {
                $this->pending_send_sms_task_log .= PHP_EOL . "取数国家[$country_code], api request: $country_api" . PHP_EOL;
                $curl_result                     = curl_request($country_api, null, 'get', $header);
                $this->pending_send_sms_task_log .= "取数国家[$country_code], api response: {$curl_result}" . PHP_EOL;

                // 返回的json数据解码
                $api_result       = json_decode($curl_result, true);
                $tmp_country_data = $api_result['data'] ?? [];

                $this->pending_send_sms_task_log .= "取数国家[$country_code], 待办数据共 " . count($tmp_country_data) . ' 条' . PHP_EOL;

                // 取数结果
                $all_data[$country_code] = $tmp_country_data;
            }

            $this->pending_send_sms_task_log .= PHP_EOL;
        }

        // 1.3 处理OA待办数据结构
        $pending_data_list = [];
        foreach ($all_data as $data_country_code => $data_item) {
            // 待办数据所属国家: 若未配置, 则该国的待办数据不发
            $pending_country_code = WorkflowPendingEnums::$sys_country_code_sms_country_map[$data_country_code] ?? '';
            if (empty($pending_country_code)) {
                continue;
            }

            // 遍历各国待办数据
            foreach ($data_item as $item) {
                // 该员工待办是否已初始化
                $staff_pending_data = $pending_data_list[$item['staff_id']] ?? [];
                if (!empty($staff_pending_data)) {
                    // 已初始化, 拼接待办数据即可
                    $staff_pending_data['pending_data'] .= ' ' . $pending_country_code . ':' . $item['count'];
                } else {
                    // 待办内容初始化: (示例 TH:10)
                    $staff_pending_data = [
                        'staff_id'        => $item['staff_id'],
                        'pending_data'    => $pending_country_code . ':' . $item['count'],
                        'by_pending_data' => '',
                    ];
                }

                $pending_data_list[$item['staff_id']] = $staff_pending_data;
            }
        }
        unset($all_data);
        $this->pending_send_sms_task_log .= 'OA待办共 ' . count($pending_data_list) . ' 条' . PHP_EOL;

        // 2 获取BY系统审批待办
        $by_pending_data_list            = $this->getByPendingData();
        $this->pending_send_sms_task_log .= 'BY待办共 ' . count($by_pending_data_list) . ' 条' . PHP_EOL;
        foreach ($by_pending_data_list as $staff_data) {
            $exist_staff_pending_data = $pending_data_list[$staff_data['staff_info_id']] ?? [];
            // 该员工有 OA 待办, 直接补充 BY 待办数即可
            if (!empty($exist_staff_pending_data)) {
                $pending_data_list[$staff_data['staff_info_id']]['by_pending_data'] = $staff_data['msg'];
            } else {
                // 该员工无 OA 待办, 初始化数据结构
                $pending_data_list[$staff_data['staff_info_id']] = [
                    'staff_id'        => $staff_data['staff_info_id'],
                    'pending_data'    => '',
                    'by_pending_data' => $staff_data['msg'],
                ];
            }
        }
        unset($by_pending_data_list);
        if (empty($pending_data_list)) {
            throw new ValidationException('OA+BY待办数据为空, 无需继续处理', ErrCode::$VALIDATE_ERROR);
        }

        // 3 获取待办人基础信息(HRIS员工信息 和 白名单清单)
        // 3.1 获取待办人的HRIS个人信息: 手机号/工作所在国家/在职状态
        $hris_staff_list = $this->getStaffBaseInfo(array_keys($pending_data_list));

        // 3.2 获取白名单人员
        $whitelist_staff_list = WorkflowPendingService::getInstance()->getWhitelist();
        $whitelist_staff_list = array_column($whitelist_staff_list, null, 'staff_id');

        // 3.3 当前时段是否跳过发送(针对非白名单: 若上一时段成功发送过, 则当前时段不再发送)
        $previous_hour_sms_send_records = $this->getSmsSendrRecordsByIgnoreHour();

        // 3.4 当前时段是否取消发送(针对非白名单)
        $is_cancel_send_by_current_hour = $this->isCancelSendByCurrentHour();

        // 3.5 配置的非白名单发信时段
        $non_whitelist_sending_periods = implode(',', $this->non_whitelist_sending_periods);

        // 4 构造待发短信数据 并 固化
        foreach ($pending_data_list as $key => $staff_data) {
            // 初始化固化结构
            $tmp_val = [
                'staff_id'        => $staff_data['staff_id'],
                'staff_name'      => '',
                'mobile'          => '',
                'country_id'      => 0,
                'sms_nation'      => '',
                'pending_data'    => $staff_data['pending_data'],
                'by_pending_data' => $staff_data['by_pending_data'],
                'send_status'     => WorkflowPendingEnums::SMS_SEND_STATUS_WAITING,
                'create_time'     => date('Y-m-d H:i:s'),
                'create_date'     => date('Y-m-d'),
                'remark'          => '',
                'from_sys'        => $sys_country_code,
            ];

            // 是否在HRIS系统中
            $staff_base_info = $hris_staff_list[$staff_data['staff_id']] ?? [];
            if (!empty($staff_base_info)) {
                $tmp_val['staff_name'] = $staff_base_info['name'];
                $tmp_val['mobile']     = trim($staff_base_info['mobile']);
                $tmp_val['country_id'] = !empty($staff_base_info['working_country_id']) ? $staff_base_info['working_country_id'] : 0;
                $tmp_val['sms_nation'] = WorkflowPendingEnums::$country_id_and_sms_nation_map[(int)$tmp_val['country_id']] ?? '';

                // 是否在白名单
                $staff_whitelist_info = $whitelist_staff_list[$staff_data['staff_id']] ?? [];

                // 白名单
                if (!empty($staff_whitelist_info)) {
                    $tmp_val['mobile']     = trim($staff_whitelist_info['mobile']);
                    $tmp_val['country_id'] = $staff_whitelist_info['country_id'];
                    $tmp_val['sms_nation'] = $staff_whitelist_info['sms_nation'];
                } else {
                    // 非白名单独有的验证
                    // 工作所在国家与系统所在国家不一致: 不发送
                    if ($tmp_val['sms_nation'] != $sys_country_code) {
                        $tmp_val['send_status'] = WorkflowPendingEnums::SMS_SEND_STATUS_IGNORE;
                        $tmp_val['remark']      .= '工作所在国家与系统国家不一致;';
                    }
                }
                //邮件需求 白名单发送时间规则与非白名单一致

                // 上一时段, 给该用户成功发送过短信, 则当前时段不再发送
                $previous_hour_sms_send_time = $previous_hour_sms_send_records[$staff_data['staff_id']] ?? '';
                if (!empty($previous_hour_sms_send_time)) {
                    $tmp_val['send_status'] = WorkflowPendingEnums::SMS_SEND_STATUS_IGNORE;
                    $tmp_val['remark']      .= "上一时段[$previous_hour_sms_send_time]已发送;";
                }

                // 指定设置时段取消发送
                if ($is_cancel_send_by_current_hour) {
                    $tmp_val['send_status'] = WorkflowPendingEnums::SMS_SEND_STATUS_IGNORE;
                    $tmp_val['remark']      .= '当前时段在[取消发送时段];';
                }

                // 是否在配置的发信时段, 发信时段为空, 则不发
                if (!in_array($this->current_time_hour, $this->non_whitelist_sending_periods)) {
                    $tmp_val['send_status'] = WorkflowPendingEnums::SMS_SEND_STATUS_IGNORE;
                    $tmp_val['remark']      .= "当前时段不在非白名单配置的发信时段[{$non_whitelist_sending_periods}];";
                }

                // 白名单 和 非白名单的 公共验证
                // 验证手机号
                if (empty($tmp_val['mobile'])) {
                    $tmp_val['send_status'] = WorkflowPendingEnums::SMS_SEND_STATUS_IGNORE;
                    $tmp_val['remark']      .= '手机号为空;';
                } else {
                    // 在手机号黑名单
                    if (in_array($tmp_val['mobile'], $this->mobile_blacklist, true)) {
                        $tmp_val['send_status'] = WorkflowPendingEnums::SMS_SEND_STATUS_IGNORE;
                        $tmp_val['remark']      .= '手机号在黑名单;';
                    }

                    // 手机号不符规则(手机号为同一字符构成)
                    $first_char = mb_substr($tmp_val['mobile'], 0, 1);
                    if (mb_substr_count($tmp_val['mobile'], $first_char) == mb_strlen($tmp_val['mobile'])) {
                        $tmp_val['send_status'] = WorkflowPendingEnums::SMS_SEND_STATUS_IGNORE;
                        $tmp_val['remark']      .= '手机号不符规则;';
                    }
                }

                // 待办人在工号黑名单, 不发
                if (in_array($tmp_val['staff_id'], $this->staff_blacklist)) {
                    $tmp_val['send_status'] = WorkflowPendingEnums::SMS_SEND_STATUS_IGNORE;
                    $tmp_val['remark']      .= '员工在不发短信黑名单;';
                }

                // 在职状态: 离职/停职不发送
                if (in_array($staff_base_info['state'],
                    [StaffInfoEnums::STAFF_STATE_LEAVE, StaffInfoEnums::STAFF_STATE_STOP])) {
                    $tmp_val['send_status'] = WorkflowPendingEnums::SMS_SEND_STATUS_IGNORE;
                    $tmp_val['remark']      .= '离职或停职状态;';
                }

                // OA 或 BY 待办内容均为空, 无需发
                if (empty($tmp_val['pending_data']) && empty($tmp_val['by_pending_data'])) {
                    $tmp_val['send_status'] = WorkflowPendingEnums::SMS_SEND_STATUS_IGNORE;
                    $tmp_val['remark']      .= 'OA和BY待办内容均为空;';
                }
            } else {
                $tmp_val['send_status'] = WorkflowPendingEnums::SMS_SEND_STATUS_IGNORE;
                $tmp_val['remark']      .= 'HRIS员工信息不存在;';
            }

            // 收集符合发送条件的工号列表
            if ($tmp_val['send_status'] == WorkflowPendingEnums::SMS_SEND_STATUS_WAITING) {
                $normal_staff_ids[] = $tmp_val['staff_id'];

                // 入push队列
                $pending_push_data = [
                    'staff_id'        => $tmp_val['staff_id'],
                    'oa_pending_data' => $tmp_val['pending_data'],
                    'by_pending_data' => $tmp_val['by_pending_data'],
                ];
                $this->pendingDataAddQueue($pending_push_data);
            }

            $pending_data_list[$key] = $tmp_val;
        }

        $this->pending_send_sms_task_log .= '本次待办[汇总后]共 ' . count($pending_data_list) . ' 条' . PHP_EOL;
        $this->pending_send_sms_task_log .= '本次符合发送短信条件的[汇总后]共 ' . count($normal_staff_ids) . ' 条' . PHP_EOL;

        $pending_data_list      = array_values($pending_data_list);
        $sms_pending_data_model = new SmsPendingDataModel();
        if ($sms_pending_data_model->batch_insert($pending_data_list) === false) {
            throw new BusinessException('待办数据固化失败, 原因可能是=' . get_data_object_error_msg($sms_pending_data_model) . '数据=' . json_encode($pending_data_list,
                    JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
        }

        $this->pending_send_sms_task_log .= '固化成功' . PHP_EOL;
        $this->pending_send_sms_task_log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        return $normal_staff_ids;
    }

    /**
     * 当前时段是否取消发送
     *
     * 当前应用对象: 非白名单待办人
     *
     * @return bool
     */
    private function isCancelSendByCurrentHour()
    {
        $cancel_send_hour_item = EnumsService::getInstance()->getSettingEnvValueIds('audit_pending_sms_cancel_send_hour_setting');
        return in_array($this->current_time_hour, $cancel_send_hour_item);
    }

    /**
     * 获取指定时段的上一小时段内待办数据的成功发送记录
     *
     * @return array
     */
    private function getSmsSendrRecordsByIgnoreHour()
    {
        $sms_send_records = [];

        $ignore_hour_item = EnumsService::getInstance()->getSettingEnvValueIds('audit_pending_sms_ignore_hour_setting');
        if (!in_array($this->current_time_hour, $ignore_hour_item)) {
            return $sms_send_records;
        }

        // 上一时段开始时间/结束时间
        $previous_hour_start_time = date('Y-m-d H:00:00', strtotime('-1 hour'));
        $previous_hour_end_time   = date('Y-m-d H:00:00');

        $sms_send_records = SmsPendingDataModel::find([
            'conditions' => 'send_time >= :start_time: AND send_time < :end_time: AND send_status = :send_status:',
            'bind'       => [
                'start_time'  => $previous_hour_start_time,
                'end_time'    => $previous_hour_end_time,
                'send_status' => WorkflowPendingEnums::SMS_SEND_STATUS_SUCCESS,
            ],
            'columns'    => ['staff_id', 'send_time'],
        ])->toArray();
        return array_column($sms_send_records, 'send_time', 'staff_id');
    }

    /**
     * 获取 系统所在国家BY的审批待办数据
     *
     * @return array
     * @throws BusinessException
     */
    private function getByPendingData()
    {
        $api_client_instance = new ApiClient('by', '', 'gatherApprovalSmsToBeSent');
        $api_client_instance->setParams([[]]);
        $result = $api_client_instance->execute();
        $this->logger->info('BY审批待办数据: ' . json_encode($result, JSON_UNESCAPED_UNICODE));

        if (empty($result['result']) || $result['result']['code'] != ErrCode::$SUCCESS) {
            throw new BusinessException('BY审批待办数据获取失败, msg=' . $result['result']['msg'], ErrCode::$BUSINESS_ERROR);
        }

        return $result['result']['data'] ?? [];
    }

    /**
     * 发送短信-审批待办数据
     * 说明: 适用所有国家
     *
     * @param $sys_country_code
     * @throws BusinessException
     * @throws ValidationException
     */
    protected function sendPendingSms($sys_country_code)
    {
        // 1. 初始化审批待办数据
        $normal_staff_ids = $this->initPendingData($sys_country_code);

        // 2. 提取待发数据 并 发送
        $this->getPendingDataAndSend($normal_staff_ids);
    }

    /**
     * 获取指定工号的手机号/工作所在国家
     *
     * @param array $staff_ids
     * @return array
     */
    private function getStaffBaseInfo(array $staff_ids)
    {
        if (empty($staff_ids)) {
            return [];
        }

        // 获取员工基本信息
        $hr_staff_repository = new HrStaffRepository();
        $staff_list          = $hr_staff_repository->getStaffListByStaffIds($staff_ids);

        // 获取员工工作所在国家
        $staff_working_country_list = $hr_staff_repository->getStaffItems($staff_ids,
            [StaffInfoEnums::HR_STAFF_ITEMS_WORKING_COUNTRY]);
        foreach ($staff_working_country_list as $item) {
            $staff_base_info = $staff_list[$item['staff_info_id']] ?? [];
            if (!empty($staff_base_info)) {
                $staff_list[$item['staff_info_id']]['working_country_id'] = $item['value'];
            }
        }

        return $staff_list;
    }

    /**
     * 提取指定员工待发送的待办数据 并 发送
     *
     * @param array $normal_staff_ids 待发短信的员工列表, 防止多发, 目前根据指定工号 和 待发状态来提取待发短信的数据
     * @return mixed
     * @throws ValidationException
     */
    private function getPendingDataAndSend(array $normal_staff_ids = [])
    {
        // 1. 指定工号为空, 不进行处理
        if (empty($normal_staff_ids)) {
            throw new ValidationException('无符合条件的待发员工数据', ErrCode::$VALIDATE_ERROR);
        }

        $this->pending_send_sms_task_log .= PHP_EOL . '(二) 短信发送' . PHP_EOL;

        // 2. 发送短信: 仅发本批次审批人待发送的: 若后续数据量增加, 则调整取数和发送方案
        $sms_data_item = SmsPendingDataModel::find([
            'conditions' => 'staff_id IN ({staff_ids:array}) AND send_status = :send_status:',
            'bind'       => [
                'staff_ids'   => $normal_staff_ids,
                'send_status' => WorkflowPendingEnums::SMS_SEND_STATUS_WAITING,
            ],
        ]);

        if (empty($sms_data_item)) {
            throw new ValidationException('无符合条件的待发短信数据', ErrCode::$VALIDATE_ERROR);
        }

        $this->pending_send_sms_task_log .= '本次待发短信共 ' . count($sms_data_item->toArray()) . ' 条' . PHP_EOL;

        // 3. 发送基本统计
        $send_success_count   = 0;
        $send_fail_count      = 0;
        $save_fail_count      = 0;
        $send_fail_detail_log = '发送失败清单: ' . PHP_EOL;
        $save_fail_detail_log = '发送状态更新失败清单: ' . PHP_EOL;
        $this->whitelistStaffIds = [];
        if (get_country_code() == 'PH') {
            $this->whitelistStaffIds = array_column(WorkflowPendingService::getInstance()->getWhitelist(),'staff_id');
        }
        foreach ($sms_data_item as $sms_item) {
            // 发送短信: 测试/tra环境 给指定的测试手机号发短信
            $sms_item->send_time = date('Y-m-d H:i:s');
            $sms_item->send_date = date('Y-m-d');
            $send_res            = $this->sendSms($sms_item->toArray());
            if (isset($send_res['code']) && $send_res['code'] == ErrCode::$SUCCESS) {
                // 发送成功
                $sms_item->send_status = WorkflowPendingEnums::SMS_SEND_STATUS_SUCCESS;
                $send_success_count++;
            } else {
                // 发送失败
                $sms_item->send_status = WorkflowPendingEnums::SMS_SEND_STATUS_FAIL;
                $send_fail_count++;
                $send_fail_detail_log .= "data_id: {$sms_item->id}; staff_id: {$sms_item->staff_id}; ";
            }

            $sms_item->remark .= $send_res['msg'] ?? '';

            if ($sms_item->save() === false) {
                $save_fail_detail_log .= "data_id: {$sms_item->id}; staff_id: {$sms_item->staff_id};";
                $save_fail_count++;
            }
        }

        $this->pending_send_sms_task_log .= "发送结果: 成功 $send_success_count, 失败 $send_fail_count" . PHP_EOL;
        $this->pending_send_sms_task_log .= "更新结果: 失败 $save_fail_count" . PHP_EOL;

        $this->pending_send_sms_task_log .= $send_fail_detail_log . PHP_EOL;
        $this->pending_send_sms_task_log .= $save_fail_detail_log . PHP_EOL;
    }

    /**
     * 短信发送
     *
     * @param array $sms_data
     * @param string $src
     * @return mixed
     */
    private function sendSms(array $sms_data = [], string $src = 'oa_and_by_audit_pending_task')
    {
        // 外部测试参数应用
        if (!empty($this->test_staff_nation)) {
            $sms_data['sms_nation'] = $this->test_staff_nation;
        }

        // 短信api参数
        $sms_api_param = [
            'mobile' => $sms_data['mobile'],
            'nation' => $sms_data['sms_nation'],
        ];

        // 发送时间格式化
        $send_time = str_replace('-', '.', substr($sms_data['send_time'], 0, 16));

        // 中国区短信 阿里云通道
        if ($sms_data['sms_nation'] == WorkflowPendingEnums::ALI_SMS_CN_CHANNEL_CODE) {
            // 短信模板变量替换
            // OA 和 BY 均有待办
            if (!empty($sms_data['pending_data']) && !empty($sms_data['by_pending_data'])) {
                $template_code  = WorkflowPendingEnums::BOTH_ALI_SMS_TMP_CN_CODE;
                $content_values = [
                    'by_pending_data' => $sms_data['by_pending_data'],
                    'oa_pending_data' => $sms_data['pending_data'],
                    'send_datetime'   => $send_time,
                ];
            } else {
                if (!empty($sms_data['pending_data'])) {
                    // 仅 OA 有待办
                    $template_code  = WorkflowPendingEnums::OA_ALI_SMS_TMP_CN_CODE;
                    $content_values = [
                        'pending_data'  => $sms_data['pending_data'],
                        'send_datetime' => $send_time,
                    ];
                } else {
                    // 仅 BY 有待办
                    $template_code  = WorkflowPendingEnums::BY_ALI_SMS_TMP_CN_CODE;
                    $content_values = [
                        'pending_data'  => $sms_data['by_pending_data'],
                        'send_datetime' => $send_time,
                    ];
                }
            }

            $sms_api_param['msg']              = json_encode($content_values);
            $sms_api_param['template']         = $template_code;
            $sms_api_param['service_provider'] = WorkflowPendingEnums::ALI_SMS_CN_SERVICE_PROVIDER;
            $sms_api_param['sendname']         = WorkflowPendingEnums::ALI_SMS_CN_SEND_NAME;
        } else {
            // 其他短信通道
            // OA 和 BY 均有待办
            if (!empty($sms_data['pending_data']) && !empty($sms_data['by_pending_data'])) {
                $template_content = WorkflowPendingEnums::BOTH_PENDING_SMS_CONTENT_TMP;
                $content_keys     = [
                    '${by_pending_data}',
                    '${oa_pending_data}',
                    '${send_datetime}',
                ];

                $content_values = [
                    $sms_data['by_pending_data'],
                    $sms_data['pending_data'],
                    '[' . $send_time . ']',
                ];
            } else {
                if (!empty($sms_data['pending_data'])) {
                    // 仅 OA 有待办
                    $template_content = WorkflowPendingEnums::OA_PENDING_SMS_CONTENT_TMP;
                    $content_keys     = [
                        '${pending_data}',
                        '${send_datetime}',
                    ];

                    $content_values = [
                        $sms_data['pending_data'],
                        '[' . $send_time . ']',
                    ];
                } else {
                    // 仅 BY 有待办
                    $template_content = WorkflowPendingEnums::BY_PENDING_SMS_CONTENT_TMP;
                    $content_keys     = [
                        '${pending_data}',
                        '${send_datetime}',
                    ];

                    $content_values = [
                        $sms_data['by_pending_data'],
                        '[' . $send_time . ']',
                    ];
                }
            }

            $sms_api_param['msg'] = str_replace($content_keys, $content_values, $template_content);
        }
        //符合条件 viber 发送
        if (get_country_code() == 'PH'
            && $sms_data['sms_nation'] != WorkflowPendingEnums::ALI_SMS_CN_CHANNEL_CODE
            && !in_array($sms_data['staff_id'], $this->whitelistStaffIds)
        ) {
            $sms_api_param['type']             = 0;
            $sms_api_param['service_provider'] = 9;// 服务商 Viber 固定值
            $sms_api_param['nation']           = 'PH';
        }
        // 如果是测试/tra环境，则仅给指定国家的指定手机号发一次即可，其他的默认为发送成功
        if (in_array(get_runtime_env(), ['dev', 'training'])) {
            $sms_api_param['staff_id'] = $sms_data['staff_id'];

            return $this->sendTest($sms_api_param, $src);
        }

        $return = ['code' => ErrCode::$DEFAULT, 'msg' => ''];
        if (SmsService::getInstance()->send($sms_api_param, $src)) {
            $return['code'] = ErrCode::$SUCCESS;
        }

        return $return;
    }

    /**
     * 测试/tra 环境发信策略
     * 说明: 上述环境, 同一个国家发送给指定手机号 且 仅一次, 同一个国家的其他信息均返回成功;
     * 若没有找到指定国家的指定手机号配置，也按成功处理;
     * 上述非真实发送的, 短信备注注明类似 test auto success
     *
     * @param array $sms_api_param
     * @param string $src
     *
     * @return mixed
     */
    protected function sendTest(array $sms_api_param, string $src)
    {
        $return = [
            'code' => ErrCode::$SUCCESS,
            'msg'  => '(test auto success)',
        ];

        // 该国是否已发送
        if (isset(self::$tmp_send_records[$sms_api_param['nation']]) && self::$tmp_send_records[$sms_api_param['nation']]) {
            $return['msg'] = '(test auto success - the nation already send success)';
            return $return;
        }

        // 给指定工号发
        if (!empty($this->test_staff_id)) {
            if ($this->test_staff_id == $sms_api_param['staff_id']) {
                $get_setting_test_mobile = !empty($this->test_staff_mobile) ? $this->test_staff_mobile : $sms_api_param['mobile'];
            } else {
                $return['msg'] = '(test auto ignore - the customize staff mobile nation)';
                return $return;
            }
        } else {
            // 获取该国预配置的测试号码
            $get_setting_test_mobile = WorkflowPendingEnums::$receive_sms_test_mobiles[$sms_api_param['nation']];
        }

        if (empty($get_setting_test_mobile)) {
            $return['msg'] = '(test auto success - the nation test mobile not setting)';
            return $return;
        }

        unset($sms_api_param['staff_id']);

        // 手机号替换为测试手机号并发送
        $sms_api_param['mobile'] = $get_setting_test_mobile;
        if (SmsService::getInstance()->send($sms_api_param, $src)) {
            self::$tmp_send_records[$sms_api_param['nation']] = true;

            $return['msg'] = "(send success:$get_setting_test_mobile)";
        } else {
            $return['code'] = ErrCode::$DEFAULT;
            $return['msg']  = "(send fail:$get_setting_test_mobile)";
        }

        return $return;
    }

    /**
     * 短信测试
     *
     * php app/cli.php workflow_pending test_send t CN
     *
     * @param array $params
     */
    public function test_sendAction($params = [])
    {
        if (!isset($params[0]) && $params[0] != 't') {
            exit('^_^');
        }

        $src = 'oa_pending_task_test';

        $mobile = $params[2] ?? '18301195455';

        // 中国区短信 阿里云通道
        if (isset($params[1]) && strtoupper($params[1]) == WorkflowPendingEnums::ALI_SMS_CN_CHANNEL_CODE) {
            $content_values = [
                'pending_data'  => 'TH:3 PH:2 MY:1 [test]',
//                'oa_pending_data' => 'TH:3 PH:5 [test]',
                'send_datetime' => date('Y.m.d H:i'),
            ];

            $params = [
                'mobile'           => $mobile,
                'msg'              => json_encode($content_values),
                'nation'           => WorkflowPendingEnums::ALI_SMS_CN_CHANNEL_CODE,
                'service_provider' => WorkflowPendingEnums::ALI_SMS_CN_SERVICE_PROVIDER,
                // 必填, 短信服务商: 3 - 阿里云短信服务
                'template'         => 'SMS_472100068',
                //WorkflowPendingEnums::BOTH_ALI_SMS_TMP_CN_CODE,// 阿里云短信模板id
                'sendname'         => 'flash_cn',
                //WorkflowPendingEnums::ALI_SMS_CN_SEND_NAME,// 阿里云服务商时, 需填写
            ];
        } else {
            $content_keys = [
                '${pending_data}',
                '${send_datetime}',
            ];

            $content_values = [
                'TH:3 PH:2 MY:1 [test]',
                '[' . date('Y.m.d H:i') . ']',
            ];

            $params = [
                'mobile' => $mobile,
                'msg'    => str_replace($content_keys, $content_values,
                    WorkflowPendingEnums::OA_PENDING_SMS_CONTENT_TMP),
                'nation' => isset($params[1]) ? strtoupper($params[1]) : 'TH',
            ];
        }

        $log = '短信测试任务' . PHP_EOL;
        $log .= '短信参数: ' . json_encode($params, JSON_UNESCAPED_UNICODE) . PHP_EOL;

        $res = SmsService::getInstance()->send($params, $src);

        $log .= '发送结果: ' . $res ? '成功' : '失败' . PHP_EOL . PHP_EOL;
        exit($log);
    }

    /**
     * 查找 审批人 和 申请人 均离职的 且 待审批的单据
     *
     * php app/cli.php workflow_pending get_holding_orders
     */
    public function get_holding_ordersAction()
    {
        try {
            // 查找当前待审批状态的单据
            $pending_list = WorkflowRequestModel::find([
                'conditions' => 'state = :state: AND is_abandon = :is_abandon:',
                'bind'       => [
                    'state'      => Enums::WF_STATE_PENDING,
                    'is_abandon' => GlobalEnums::WORKFLOW_ABANDON_STATE_NO,
                ],
                'columns'    => [
                    'id',
                    'name',
                    'current_node_auditor_id',
                    'create_staff_id',
                    'created_at',
                    'updated_at',
                    'biz_type',
                ],
            ])->toArray();

            $create_staff_id          = array_column($pending_list, 'create_staff_id');
            $current_node_auditor_ids = implode(',', array_column($pending_list, 'current_node_auditor_id'));
            $current_node_auditor_ids = array_values(array_filter(array_unique(array_merge($create_staff_id,
                explode(',', $current_node_auditor_ids)))));

            // 获取相关人的在职状态
            $staff_list = (new HrStaffRepository())->getStaffListByStaffIds($current_node_auditor_ids);

            // 筛选申请人 / 审批人 均离职的单据
            $holding_list = [];
            foreach ($pending_list as $item) {
                $staff_ids   = explode(',', $item['current_node_auditor_id']);
                $staff_ids[] = $item['create_staff_id'];
                $staff_ids   = array_filter(array_unique($staff_ids));

                // 判断审批人和申请人 是否 都离职
                $is_have_on_job = false;
                foreach ($staff_ids as $staff_id) {
                    $staff_state = $staff_list[$staff_id]['state'] ?? null;
                    if (in_array($staff_state, [StaffInfoEnums::STAFF_STATE_IN, StaffInfoEnums::STAFF_STATE_STOP])) {
                        $is_have_on_job = true;
                        break;
                    }
                }

                if ($is_have_on_job == false) {
                    $holding_list[] = [
                        $item['id'],
                        preg_replace('/[^0-9a-zA-Z]/', '', $item['name']),
                        $item['current_node_auditor_id'],
                        $item['create_staff_id'],
                        $item['created_at'],
                        $item['updated_at'],
                        $item['biz_type'],
                    ];
                }
            }

            // 表头
            $header = [
                'ID',
                '单号',
                '审批人',
                '申请人',
                '申请时间',
                '流转到该节点时间',
                '业务类型枚举',
            ];

            $file_name = 'holding_workflow_orders_' . date('YmdHis') . '.xlsx';
            $res       = WorkflowPendingService::getInstance()->exportExcel($header, $holding_list, $file_name);

            var_dump($res);
        } catch (Exception $e) {
            echo $e->getMessage();
        }

        exit;
    }

    /**
     * 替换被夯住的审批流的审批人
     *
     * php app/cli.php workflow_pending replace_auditors
     */
    public function replace_auditorsAction()
    {
        $log = '';
        $log .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        try {
            // 根据配置获取需替换审批人的审批流[一组单据 => 新的相同审批人]
            $pending_workflow_auditors_config = EnumsService::getInstance()->getSettingEnvValueMap('replace_pending_workflow_auditors_config');

            // 1. 待替换的配置[审批中的单据 -> 新审批人]
            $this->logger->info('replace_auditors_config=' . json_encode($pending_workflow_auditors_config,
                    JSON_UNESCAPED_UNICODE));
            if (empty($pending_workflow_auditors_config)) {
                throw new ValidationException('配置为空, 请检查', ErrCode::$VALIDATE_ERROR);
            }

            // 2. 查找单据的审批流
            foreach ($pending_workflow_auditors_config as $config_index => $config_item) {
                $new_auditor   = $config_item['new_auditor'];
                $order_no_list = trim_array(array_unique(array_filter(explode(',', $config_item['order_no_list']))));
                if (empty($new_auditor) || empty($order_no_list)) {
                    $log .= '当前配置项的单据号 或 新审批人为空, 配置项索引: ' . $config_index . PHP_EOL;
                    continue;
                }

                // 查找单据对应的待审批流
                foreach ($order_no_list as $order_index => $order_no) {
                    $db = $this->getDI()->get('db_oa');
                    $db->begin();

                    $request_model = WorkflowRequestModel::findFirst([
                        'conditions' => 'name = :name: AND state = :state: AND is_abandon = :is_abandon:',
                        'bind'       => [
                            'name'       => $order_no,
                            'state'      => Enums::WF_STATE_PENDING,
                            'is_abandon' => GlobalEnums::WORKFLOW_ABANDON_STATE_NO,
                        ],
                    ]);
                    if (empty($request_model)) {
                        $log .= '单据不存在审批中的审批流: ' . $order_no . PHP_EOL;
                        continue;
                    }

                    $handled_orders = "待处理的单据-{$order_index}: {$order_no}, request_id={$request_model->id}, biz_type={$request_model->biz_type}, 节点ID={$request_model->current_flow_node_id}, 原审批人={$request_model->current_node_auditor_id}, 提交时间={$request_model->created_at}, 上次处理时间={$request_model->updated_at}, 新审批人={$new_auditor}";

                    $auditors = array_values(array_unique(array_filter(explode(',',
                        $request_model->current_node_auditor_id))));
                    // 审批流关联的节点与审批人关系
                    // 查找当前审批流审批人待审批的关系数据
                    if (!empty($auditors)) {
                        $node_auditor_models = WorkflowRequestNodeAuditorModel::find([
                            'conditions' => 'request_id = :request_id: AND flow_node_id = :flow_node_id: AND audit_status = :audit_status: AND auditor_id IN ({auditors:array})',
                            'bind'       => [
                                'request_id'   => $request_model->id,
                                'flow_node_id' => $request_model->current_flow_node_id,
                                'audit_status' => Enums::WF_STATE_PENDING,
                                'auditors'     => $auditors,
                            ],
                        ]);

                        if (!empty($node_auditor_models)) {
                            // 将待审批节点的原审批人更新为新审批人
                            foreach ($node_auditor_models as $node_auditor) {
                                $node_auditor_update = [
                                    'auditor_id' => $new_auditor,
                                ];
                                $handled_orders      .= "; 节点关系表ID={$node_auditor->id}, 原审批人={$node_auditor->auditor_id}, 新审批人={$new_auditor}";
                                if ($node_auditor->i_update($node_auditor_update) === false) {
                                    $handled_orders .= ', 节点关系表更新失败';
                                } else {
                                    $handled_orders .= ', 节点关系表更新成功';
                                }
                            }
                        } else {
                            $handled_orders .= '; 节点关系表无数据';
                        }
                    }

                    // 更新审批流主表的审批人为新审批人
                    $viewer_ids     = array_unique(array_filter(array_merge(explode(',', $request_model->viewer_ids),
                        [$new_auditor])));
                    $request_update = [
                        'current_node_auditor_id' => $new_auditor,
                        'viewer_ids'              => implode(',', $viewer_ids),
                    ];
                    if ($request_model->i_update($request_update) === false) {
                        $handled_orders .= '; 审批流主表更新失败';
                        $db->rollback();
                    } else {
                        $handled_orders .= '; 审批流主表更新成功';
                        $db->commit();
                    }

                    // 3. 输出处理结果
                    $handled_orders .= PHP_EOL;
                    echo $handled_orders;
                }
            }
        } catch (ValidationException $e) {
            $log .= '校验异常: ' . $e->getMessage() . PHP_EOL;
        } catch (Exception $e) {
            $log .= '处理异常: ' . $e->getMessage() . PHP_EOL;
        }

        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        exit($log);
    }

    /**
     * OA待办人缓存预热
     *
     * php app/cli.php workflow_pending pending_perhot
     */
    public function pending_perhotAction()
    {
        $log         = '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $logger_type = 'info';

        try {
            // 获取OA业务审批待办人(不含支付管理模块的待审批)
            $oa_request_list = WorkflowRequestModel::find([
                'columns'    => ['current_node_auditor_id', 'id'],
                'conditions' => 'biz_type NOT IN ({biz_types:array}) AND state = :state: AND is_abandon = :is_abandon:',
                'bind'       => [
                    'biz_types'  => WorkflowEventService::$not_biz_type,
                    'state'      => Enums::WF_STATE_PENDING,
                    'is_abandon' => GlobalEnums::WORKFLOW_ABANDON_STATE_NO,
                ],
            ])->toArray();
            $oa_auditor_ids  = array_unique(array_filter(explode(',', implode(',', array_column($oa_request_list, 'current_node_auditor_id')))));

            $log .= 'OA待审批单据: ' . count($oa_request_list) . ' 个' . PHP_EOL;
            $log .= 'OA待审批人: ' . count($oa_auditor_ids) . ' 个' . PHP_EOL;

            // 获取OA征询待回复人(不含支付管理模块的待回复)
            $oa_reply_ids = [];
            if (!empty($oa_request_list)) {
                $oa_request_ids = array_column($oa_request_list, 'id');
                unset($oa_request_list);

                $oa_reply_ids = WorkflowRequestNodeAt::find([
                    'conditions' => 'request_id IN ({request_ids:array}) AND is_reply = :is_reply:',
                    'bind'       => [
                        'request_ids' => $oa_request_ids,
                        'is_reply'    => GlobalEnums::CONSULTED_REPLY_STATE_PENDING,
                    ],
                    'columns'    => ['staff_id'],
                ])->toArray();
                $oa_reply_ids = array_unique(array_column($oa_reply_ids, 'staff_id'));
                $log          .= 'OA待回复人: ' . count($oa_reply_ids) . ' 个' . PHP_EOL;
            }

            // 获取OA中用了BY审批的待办人
            $builder = $this->modelsManager->createBuilder();
            $builder->columns('approval_id');
            $builder->from(AuditApprovalModel::class);
            $builder->inWhere('biz_type', WorkflowPendingEnums::$by_pending_audit_type_for_oa_reddot);
            $builder->andWhere('state = :state:', ['state' => Enums::WF_STATE_PENDING]);
            $builder->andWhere('deleted = :deleted:', ['deleted' => GlobalEnums::IS_NO_DELETED]);
            $by_auditor_ids = $builder->getQuery()->execute()->toArray();
            $by_auditor_ids = array_unique(array_column($by_auditor_ids, 'approval_id'));

            $by_pending_audit_type_for_oa_reddot = implode(',', WorkflowPendingEnums::$by_pending_audit_type_for_oa_reddot);
            $log .= 'BY待审批人: ' . count($by_auditor_ids) . " 个[biz_type={$by_pending_audit_type_for_oa_reddot}]" . PHP_EOL;

            // 获取学习计划审批人
            $school_admin_ids = SettingEnvModel::find([
                'conditions' => 'code IN ({codes:array})',
                'bind'       => ['codes' => ['school_data_supper_admin_ids', 'school_data_admin_ids']],
                'columns'    => ['val'],
            ])->toArray();
            $school_admin_ids = array_unique(array_filter(explode(',', implode(',', array_column($school_admin_ids, 'val')))));
            $log              .= 'School管理员: ' . count($school_admin_ids) . ' 个' . PHP_EOL;

            // 所有待办人
            $all_pending_ids = array_unique(trim_array(array_merge($oa_auditor_ids, $oa_reply_ids, $by_auditor_ids, $school_admin_ids)));
            $log             .= '本次待初始化: ' . count($all_pending_ids) . ' 个 OA待办人' . PHP_EOL;
            $log             .= '清单如下: ' . implode(',', $all_pending_ids) . PHP_EOL;

            // 移除原有的待办人
            $prehot_cache_key = RedisKey::OA_REDDOT_PENDING_PREHOT_CACHE_KEY;
            RedisClient::getInstance()->getClient()->delete($prehot_cache_key);

            // 批量添加新的待办人
            if (!empty($all_pending_ids)) {
                $sadd_count = RedisClient::getInstance()->getClient()->sadd($prehot_cache_key, ...$all_pending_ids);
                if (!$sadd_count) {
                    throw new Exception('初始化失败', ErrCode::$SYSTEM_ERROR);
                } else {
                    $log .= "初始化成功[{$sadd_count}]" . PHP_EOL;
                }
            }
        } catch (Exception $e) {
            $log         .= '处理异常: ' . $e->getMessage() . PHP_EOL;
            $logger_type = 'notice';
        }

        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $this->logger->$logger_type($log);
        exit($log);
    }

    /**
     * OA红点高优工号动态维护(静态高优工号的补充)
     *
     * php app/cli.php workflow_pending get_redhot_staffs
     */
    public function get_redhot_staffsAction()
    {
        $log         = '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $logger_type = 'info';

        try {
            // 获取在职/待离职状态的一级部门负责人
            $dept_manager_ids = (new DepartmentRepository())->getDepartmentManagerIds(1);

            $log .= '共 ' . count($dept_manager_ids) . ' 个 1 级部门负责人' . PHP_EOL;

            // 提取在职/待离职的负责人
            $dept_manager_ids = (new HrStaffRepository())->getOnJobStaffIds($dept_manager_ids);
            $log              .= '其中 ' . count($dept_manager_ids) . ' 个 在职/待离职' . PHP_EOL;

            $oa_redhot_dynamic_high_priority_staffs = implode(',', $dept_manager_ids);
            $log                                    .= '清单如下: ' . $oa_redhot_dynamic_high_priority_staffs . PHP_EOL;

            // 更新setting_env
            $env_code      = 'oa_redhot_dynamic_high_priority_staffs';
            $setting_model = SettingEnvModel::findFirst([
                'conditions' => 'code = :code:',
                'bind'       => ['code' => $env_code],
            ]);
            if (empty($setting_model)) {
                $setting_model             = new SettingEnvModel();
                $setting_model->code       = $env_code;
                $setting_model->is_edit    = Enums\SettingEnums::SETTING_ENUMS_IS_EDIT;
                $setting_model->content    = 'BY实时获取OA红点的高优用户配置, 多个工号用英文逗号间隔, 脚本定时动态获取, 请勿人工修改';
                $setting_model->created_at = date('Y-m-d H:i:s');
            }

            $setting_model->val            = $oa_redhot_dynamic_high_priority_staffs;
            $setting_model->updated_at     = date('Y-m-d H:i:s');
            $setting_model->last_update_id = 10000;
            if ($setting_model->save() === false) {
                throw new Exception('setting_env save error, 原因可能是=' . get_data_object_error_msg($setting_model),
                    '; data=' . json_encode($setting_model->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$SYSTEM_ERROR);
            }

            $log .= 'setting_env save success' . PHP_EOL;

            // 更新缓存
            // 删除旧数据
            $cache_key = RedisKey::OA_REDDOT_DYNAMIC_HIGH_PRIORITY_STAFF_CACHE_KEY;
            RedisClient::getInstance()->getClient()->delete($cache_key);

            // 写入新数据
            if (!RedisClient::getInstance()->getClient()->sadd($cache_key, ...$dept_manager_ids)) {
                throw new Exception('cache sadd error', ErrCode::$SYSTEM_ERROR);
            } else {
                $log .= 'cache sadd success' . PHP_EOL;
            }
        } catch (Exception $e) {
            $log         .= '脚本异常: ' . $e->getMessage() . PHP_EOL;
            $logger_type = 'notice';
        }

        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $this->logger->$logger_type($log);
        exit($log);
    }

    /**
     * 需要待办短信的待办人数据入队，发送push
     * [
     *    staff_id => xxx,
     *    pending_data => yyy (oa + by)
     * ]
     *
     *
     * @param array $pending_data
     * @return bool
     */
    protected function pendingDataAddQueue(array $pending_data = [])
    {
        if (empty($pending_data)) {
            return true;
        }

        $add_res = $this->redis->lpush(self::PENDING_DATA_QUEUE_NAME, json_encode($pending_data));
        if ($add_res) {
            $logger_type                      = 'info';
            $pending_data['add_queue_result'] = 'success';
        } else {
            $logger_type                      = 'notice';
            $pending_data['add_queue_result'] = 'fail';
        }

        $this->logger->$logger_type(['workflow_pending_data_add_queue_data' => $pending_data]);
        return $add_res;
    }

    /**
     * 待办短信数据, 发送push
     *
     * php app/cli.php workflow_pending send_push
     */
    public function send_pushAction()
    {
        $log = '任务名称: send_push' . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        $logger_type = 'info';

        try {
            // 系统所在国家
            $current_country = get_country_code();

            // 当前环境
            $runtime_env = get_runtime_env();

            // 短信内容中的国家简称与系统国家码映射
            $sms_country_sys_country_code_map = array_flip(WorkflowPendingEnums::$sys_country_code_sms_country_map);

            // push api 加签 key
            $api_private_key = md5(WorkflowPendingEnums::WORKFLOW_PENDING_PUSH_API_PRIVATE_KEY);

            // push参数
            static::setLanguage('zh-CN');
            $push_title                  = static::$t->_('workflow_pending_silence_push_title');
            $push_text                   = static::$t->_('workflow_pending_silence_push_text');
            $current_country_push_scheme = 'flashbackyard://fe/html?url=' . urlencode(env('by_url_prefix_new') . '/oa-push-transfer');

            while (true) {
                $pending_data = $this->redis->rpop(self::PENDING_DATA_QUEUE_NAME);
                if (!empty($pending_data)) {
                    $pending_data = json_decode($pending_data, true);
                    $this->logger->info(['workflow_pending_data_waiting_push_data' => $pending_data]);

                    preg_match_all('/([a-zA-Z]{2,3}):/', $pending_data['oa_pending_data'], $oa_pending_country);
                    preg_match_all('/([a-zA-Z]{2,3}):/', $pending_data['by_pending_data'], $by_pending_country);

                    $pending_country_list = array_unique(array_merge($oa_pending_country[1], $by_pending_country[1]));
                    $this->logger->info(['pending_country_list' => $pending_country_list]);

                    // 匹配待办数据所属国家, 给待办人的待办数据所属国家发送push
                    $push_log = "待办人[{$pending_data['staff_id']}]: ";
                    foreach ($pending_country_list as $country) {
                        $pending_data_country = $sms_country_sys_country_code_map[$country];
                        $push_log             .= "待push国家[{$pending_data_country}]";

                        $push_params = [
                            'staff_info_id'    => $pending_data['staff_id'],
                            'message_title'    => $push_title,
                            'message_content'  => $push_text,
                            'message_priority' => 1,// push优先级: 0-普通; 1-优先
                            'src'              => 'backyard',
                            'silence'          => 1 // 0-普通推送; 1-静默推送
                        ];

                        if ($pending_data_country == $current_country) {
                            // 当前系统所属国 rpc
                            $push_params['message_scheme'] = $current_country_push_scheme;

                            $push_log .= ', push参数=' . json_encode($push_params, JSON_UNESCAPED_UNICODE);

                            $ret = new ApiClient('bi_svc', '', 'push_to_staff');
                            $ret->setParams([$push_params]);
                            $res         = $ret->execute();
                            $bi_return   = $res && isset($res['result']) && $res['result'] == true;
                            $push_result = $bi_return ? '成功' : '失败';
                        } else {
                            // 其他国家 http
                            $push_api = WorkflowPendingEnums::$workflow_pending_push_api_list[$runtime_env][$pending_data_country] ?? '';

                            $post_params['push_data'] = base64_encode(serialize($push_params));
                            $post_params['timestamp'] = time();
                            $post_params['source']    = $current_country;
                            $post_params['nonce']     = get_generate_random_string();
                            $post_params              = build_params_sign($post_params, $api_private_key);

                            $push_log .= ', push参数=' . json_encode($post_params, JSON_UNESCAPED_UNICODE);

                            $header = [
                                'content-type: application/json',
                                'pending-auth-code: ' . $post_params['sign'],
                            ];

                            $curl_result = curl_request($push_api, json_encode($post_params), 'post', $header);
                            $api_result  = json_decode($curl_result, true);
                            $push_result = isset($api_result['code']) && $api_result['code'] == ErrCode::$SUCCESS ? '成功' : '失败';
                        }

                        $push_log .= ", push结果: {$push_result}; ";
                    }

                    $this->logger->info($push_log);
                    echo $push_log . PHP_EOL;
                } else {
                    throw new ValidationException("无待push数据, [" . date('Y-m-d h:i:s') . ']', ErrCode::$VALIDATE_ERROR);
                }
            }
        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;
            sleep(5);
        } catch (Exception $e) {
            $logger_type = 'notice';
            $log         .= $e->getMessage() . PHP_EOL;
            sleep(10);
        }

        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $this->logger->$logger_type($log);
        exit($log);
    }
}
