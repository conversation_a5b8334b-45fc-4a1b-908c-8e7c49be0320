<?php

use App\Modules\Material\Models\MaterialAttachmentModel;
use App\Modules\Material\Models\MaterialSyncScmLogModel;
use App\Modules\Material\Models\MaterialUpdateLogModel;
use App\Modules\Material\Services\StandardService;
use App\Modules\Purchase\Models\ScmCargoOwnerModel;
use App\Modules\Material\Models\MaterialSauModel;
use App\Modules\Material\Models\MaterialSauSkuModel;
use App\Modules\Purchase\Models\PurchaseOrderProduct;
use App\Modules\Purchase\Models\PurchaseOrder;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Material\Services\ScmService;
use App\Library\Enums\MaterialClassifyEnums;
use App\Modules\Material\Services\SapsService;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums;

/**
 * 物料数据同步到SCM脚本集合
 * Class MaterialSauTask
 */
class MaterialSauTask extends BaseTask
{
    /**
     * 同步标准型号信息到SCM
     */
    public function sync_scmAction()
    {
        $log = '';
        try {
            echo date('Ymd H:i:s')." 脚本开始执行".PHP_EOL;
            //第一步找到所有需要同步到SCM的标准型号总记录数,失败的只重试3次
            $where = 'source_type = :source_type: and status != :status: and is_deleted = :is_deleted: and fail_num < :fail_num:';
            $bind = [
                'source_type' => MaterialClassifyEnums::TO_SCM_SOURCE_TYPE_SAU,
                'status' => MaterialClassifyEnums::TO_SCM_SYNC_STATUS_SUCCESS,
                'is_deleted' => MaterialClassifyEnums::IS_DELETED_NO,
                'fail_num' => 3,
            ];
            $count = MaterialSyncScmLogModel::count([
                'columns' => 'id',
                'conditions' => $where,
                'bind' => $bind
            ]);
            if ($count > 0) {
                //第二步找到所有需要同步到SCM的标准型号数据列表
                $list_arr = MaterialSyncScmLogModel::find([
                    'columns' => 'id,sync_id,scm_cargo_id,sync_type,fail_num',
                    'conditions' => $where,
                    'bind' => $bind
                ])->toArray();
                if ($list_arr) {
                    //第三步获取需要同步的scm货主列表
                    $scm_cargo_ids = array_values(array_unique(array_column($list_arr, "scm_cargo_id")));
                    $scm_list = ScmCargoOwnerModel::find([
                        'columns' => 'id,mach_code',
                        'conditions' => 'id in ({ids:array}) and type = :type:',
                        'bind' => ['ids'=>$scm_cargo_ids, 'type' => ScmService::TYPE_SYNC_SCM]
                    ])->toArray();
                    if ($scm_list) {
                        $scm_list = array_column($scm_list, null, "id");
                        //第四步获取需要同步的标准型号列表
                        $sau_ids =array_values(array_unique(array_column($list_arr, "sync_id")));
                        $builder = $this->modelsManager->createBuilder();
                        $builder->columns('sau.id,sau.barcode,sau.finance_category_id,sau.name_zh,sau.name_en,sau.name_local,sau.model,sau.enable_sn,sau.unit_en,sau.remark,sau.price,sau.status,sau.enable_asset_code, sku.length,sku.length_unit, sku.width, sku.width_unit, sku.height,sku.height_unit,sku.weight,sku.weight_unit,sku.small_bag_unit_en,sku.small_bag_val,sku.big_bag_unit_en,sku.big_bag_val');
                        $builder->from(['sau' => MaterialSauModel::class]);
                        $builder->leftjoin(MaterialSauSkuModel::class, 'sku.sau_id = sau.id','sku');
                        $builder->where('sau.id in ({ids:array}) and sau.is_deleted=:is_deleted: and sku.is_deleted=:is_deleted:', ['ids'=>$sau_ids, 'is_deleted' => MaterialClassifyEnums::IS_DELETED_NO]);
                        $sau_list = $builder->getQuery()->execute()->toArray();
                        if ($sau_list) {
                            $sau_list = array_column($sau_list, null, "id");
                            //开始同步标准型号到SCM
                            ScmService::setLanguage("zh-CN");
                            $scm = new ScmService();
                            foreach ($list_arr as $item) {
                                $mach_code = $scm_list[$item['scm_cargo_id']]['mach_code'];
                                $sau_info = isset($sau_list[$item['sync_id']]) ? $sau_list[$item['sync_id']] :[];
                                if (!$sau_info) continue;
                                $log = "标准型号ID[".$item['sync_id']."]，";
                                switch ($item['sync_type']) {
                                    case MaterialClassifyEnums::TO_SCM_SYNC_TYPE_ADD:
                                        //添加同步至SCM
                                        [$result, $msg] = $scm->syncAddGoods($mach_code, $item, $sau_info);
                                        $msg = $result?'[成功]':'[失败：'.$msg.']';
                                        $log.="添加同步至SCM结果：".$msg.PHP_EOL;
                                        break;
                                    case MaterialClassifyEnums::TO_SCM_SYNC_TYPE_UPDATE:
                                        //更新同步至SCM
                                        [$result, $msg] = $scm->syncEditGoods($mach_code, $item, $sau_info);
                                        $msg = $result?'[成功]':'[失败：'.$msg.']';
                                        $log.="更新同步至SCM结果：".$msg.PHP_EOL;
                                        break;
                                    case MaterialClassifyEnums::TO_SCM_SYNC_TYPE_STATUS:
                                        //启用禁用状态同步至SCM
                                        [$result, $msg] = $scm->syncGoodsChangeStatus($mach_code, $item, $sau_info);
                                        $msg = $result?'[成功]':'[失败：'.$msg.']';
                                        $log.="启用禁用状态同步至SCM结果：".$msg.PHP_EOL;
                                        break;
                                    default:
                                        break;
                                }
                                echo $log.PHP_EOL;
                            }
                        }
                    }
                }
            } else {
                $log = date('Ymd H:i:s').' 标准型号信息均已同步至SCM';
                echo $log.PHP_EOL;
            }
            $this->logger->info($log);
        } catch (\Exception $e) {
            $log = date('Ymd H:i:s') . ' 同步标准型号信息到SCM失败[异常]' . $e->getMessage() . PHP_EOL;
            echo $log;
            $this->logger->warning($log);
        }
        echo date('Ymd H:i:s'). " 脚本执行结束". PHP_EOL;
    }

    /**
     * barcode 图片附件初始化
     * @param array $param 参数组
     * @return bool
     */
    public function imgAction($param)
    {
        try{
            $url = isset($param[0]) ? $param[0] : ''; //url地址
            if(empty($url))
                die('need url');
            $path = sys_get_temp_dir().'/';
            $excel_file = 'barcode.xlsx';
            file_put_contents($path.$excel_file,file_get_contents($url));
            $config = ['path' =>$path];
            $excel = new \Vtiful\Kernel\Excel($config);
            $excel_data = $excel->openFile($excel_file)
                ->openSheet()
                ->getSheetData();
            if(empty($excel_data)) {
                return false;
            }
            $excel_header_column = array_shift($excel_data);
            $is_error = false;
            $attachment = [];
            $data = '';
            foreach ($excel_data as $key => &$item) {
                $http_header = @get_headers($item[1], true);
                $http_response_header = $http_header[0] ?? 'header null';
                if (!stripos($http_response_header, '200')) {
                    $is_error = true;
                    $item[3] = '失败，图片不存在，需要手动上传';
                    continue;
                }
                $result = \App\Library\OssHelper::uploadFile($item[1]);
                $attachment[] = [
                    'oss_bucket_type' => 1,
                    'sub_type' => 0,
                    'oss_bucket_key' => $item[0],
                    'bucket_name' => $result['bucket_name'],
                    'object_key' => $result['object_key'],
                    'file_name' => $result['file_name'],
                    'deleted' => 0,
                    'created_at' => date('Y-m-d H:i:s')
                ];
            }
            if ($is_error) {
                $new_excel_data = [];
                foreach ($excel_data as $item) {
                    $new_excel_data[] = array_values($item);
                }
                $result = StandardService::getInstance()->exportExcel($excel_header_column, $new_excel_data, "批量上传图片failed-" . date("Ymd"));
                $data = $result['data'];
            }
            if ($attachment) {
                $attachment_obj = new MaterialAttachmentModel();
                $attachment_obj->batch_insert($attachment);
                $data = $data ? $data : '成功导入:['.count($attachment).']';
            }
            $this->logger->info('MaterialSauTask-img-[ '.$data.' ]');
            echo $data.PHP_EOL;
            unlink($path.$excel_file);
        } catch (\Exception $e){
            die($e->getMessage());
        }
    }

    /**
     * 同步标准型号信息到SAP
     */
    public function sync_sapAction()
    {
        $this->checkLock(__METHOD__);
        $log = date('Ymd H:i:s').' 脚本开始执行'.PHP_EOL;
        try {
            //运营地点
            $place = EnvModel::getEnvByCode('purchase_order_place');
            $site_info = [];
            if (!empty($place)) {
                $site_info = array_column(json_decode($place, true), 'place_name', 'id');
            }
            //币种
            GlobalEnums::init();
            //币种文本信息
            $currency_text = GlobalEnums::$sys_default_currency_symbol;
            $i = 1;
            $deal_sap_data =  $this->getSauToSapList($i);
            while (!empty($deal_sap_data)) {
                $log .= '第'.$i.'页处理开始' . PHP_EOL;
                foreach ($deal_sap_data as $key => $value) {
                    $material_sau = MaterialSauModel::findFirst([
                        'conditions' => 'id = :id: and is_send_sap = :is_send_sap: and is_deleted = :is_deleted:',
                        'bind' => ['id' => $value['id'], 'is_send_sap' => MaterialClassifyEnums::IS_SEND_SAP_WAIT, 'is_deleted' => MaterialClassifyEnums::IS_DELETED_NO],
                    ]);
                    if(empty($material_sau)) {
                        continue;
                    }
                    //用于记录操作日志
                    $log_model = clone $material_sau;
                    //sau修改数据组
                    $update_data = [];
                    $material_sau_data = $material_sau->toArray();
                    $finance_category_code = StandardService::getInstance()->getFinanceSecondCategoryCode($material_sau_data['update_to_sap'], $material_sau_data['finance_category_id']);
                    if (!empty($finance_category_code) && in_array($finance_category_code, MaterialClassifyEnums::$send_to_sap_finance_category_code)) {
                        //只有是传输至SAP=是 && 二级财务分类code在G010,G020,G030这三个码里才可以传输
                        $material_sau_data['finance_category_code'] = $finance_category_code;
                        //营业地点
                        $material_sau_data['site_id'] = !empty($site_info) && isset($site_info[1]) ? $site_info[1] : '';
                        //sap公司
                        $material_sau_data['sap_company_id'] = SapsService::getInstance()->getSapCompanyId();
                        $return_data = false;
                        //防止sap那边请求超时
                        $z = 0;
                        while (empty($return_data) && $z < 2) {
                            $z++;
                            //先查询该标准型号在SAP是否存在
                            $sap_material_sau_data = SapsService::getInstance()->checkIsInSap($value['barcode']);
                            if (empty($sap_material_sau_data)) {
                                //主数据不存在，则新增
                                $action_code = MaterialClassifyEnums::SAP_ACTION_CODE_CREATE;
                                //主数据要新增，那么评估也是要新增
                                $valuation_action_code = MaterialClassifyEnums::SAP_ACTION_CODE_CREATE;
                            } else {
                                //主数据存在，则更新
                                $action_code = MaterialClassifyEnums::SAP_ACTION_CODE_UPDATE;
                                //主数据存在，检查下是否返回了评估信息
                                if (isset($sap_material_sau_data['Valuation'])) {
                                    //存在，则更新
                                    $valuation_action_code = MaterialClassifyEnums::SAP_ACTION_CODE_UPDATE;
                                } else {
                                    //不存在，则新增
                                    $valuation_action_code = MaterialClassifyEnums::SAP_ACTION_CODE_CREATE;
                                }
                            }
                            $return_data = SapsService::getInstance()->sendToSap($action_code, $material_sau_data);
                             if (isset($return_data['UUID']) && !empty($return_data['UUID'])) {
                                 //发送标准型号到SAP主数据接口成功，需要调取评估接口
                                 $material_sau_data['currency_text'] = $currency_text;
                                 $return_valuation_data = SapsService::getInstance()->sendToSapValuation($valuation_action_code, $material_sau_data, $return_data['UUID']);
                                 if (isset($return_valuation_data['MaterialInternalID']) && !empty($return_valuation_data['MaterialInternalID']) && $material_sau_data['barcode'] == $return_valuation_data['MaterialInternalID']) {
                                     //评估接口传输成功了
                                     $update_data['is_send_sap'] = MaterialClassifyEnums::IS_SEND_SAP_SUCCESS;//发送至SAP结果：2成功
                                 } else {
                                     //评估接口传输失败了
                                     $update_data['is_send_sap'] = MaterialClassifyEnums::IS_SEND_SAP_FAIL;//发送至SAP结果：3失败
                                 }
                             } else {
                                 //用于防止sap重复请求接口会返回return_data为空数组；不需要再次请求sap了
                                 $return_data = true;
                                 //发送标准型号到SAP主数据接口失败
                                 $this->logger->info('标准型号：' . $material_sau->barcode . '发送标准型号到SAP主数据接口传输失败;等待下次脚本执行接着传输');
                                 $log .= '标准型号：' . $material_sau->barcode . '发送标准型号到SAP主数据接口传输失败' . PHP_EOL;
                                 $update_data['is_send_sap'] = MaterialClassifyEnums::IS_SEND_SAP_FAIL;//发送至SAP结果：3失败
                             }
                            sleep(2);
                        }
                    } else {
                        //不满足传输条件，该条数据此次任务无需同步
                        $update_data['is_send_sap'] = StandardService::DEFAULT_PARAMETER_ZERO;//发送至SAP结果：0默认值-不需要传输
                    }
                    if (!empty($update_data)) {
                        //存在要更新的数据
                        $update_data['updated_at'] = date('Y-m-d H:i:s');//更新时间
                        $bool = $material_sau->i_update($update_data);
                        if ($bool === true) {
                            //记录操作日志
                            $update_log_model = new MaterialUpdateLogModel();
                            $update_log_model->dealEditField(MaterialClassifyEnums::OPERATE_TYPE_UPDATE, $log_model, $update_data, ['id' => 10000, 'name' => 'SuperAdmin', 'nick_name' => '']);
                            $log .= '标准型号：' . $material_sau->barcode . '处理完毕' . PHP_EOL;
                        }
                    }
                }
                $log .= '第'.$i.'页处理结束' . PHP_EOL;
                sleep(1);
                $i += 1;
                $deal_sap_data = $this->getSauToSapList($i);
            }
            if (empty($deal_sap_data)) {
                $log .= '数据均已处理完毕' . PHP_EOL;
            }
        } catch (Exception $e) {
            $this->logger->warning('material_sau_sync_sap_task_exception:' . $e->getMessage());
            $log .= date('Ymd H:i:s') . ' 同步标准型号信息到SAP失败[异常]' . $e->getMessage() . PHP_EOL;
        }
        $log .= date('Ymd H:i:s') . ' 脚本执行结束' . PHP_EOL;
        $this->clearLock(__METHOD__);
        exit($log);
    }

    /**
     * 获取需要审核的出库单列表
     * @param int $page_num 当前页码
     * @return mixed
     */
    private function getSauToSapList($page_num)
    {
        $page_size = 100;
        //待发送 && 启用 && 正常的barcode数据
        return MaterialSauModel::find([
            'conditions' => 'is_send_sap = :is_send_sap: and status = :status: and is_deleted = :is_deleted:',
            'bind' => ['is_send_sap' => MaterialClassifyEnums::IS_SEND_SAP_WAIT, 'status' => MaterialClassifyEnums::MATERIAL_START_USING, 'is_deleted' => MaterialClassifyEnums::IS_DELETED_NO],
            'limit' => $page_size,
            'offset' => $page_size * ($page_num - 1),
        ])->toArray();
    }
    
    /**
     * 同步参考单价
     * 每个月1号 06:00:10 跑脚本
     */
    public function sync_priceAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $this->check_process($process_name);
        echo date('Ymd H:i:s').' 脚本开始执行'.PHP_EOL;
        $log = 'sync_price ';
        try {
            $i = 1;
            //申请日期在3个月内的
            $apply_date_start = date('Y-m-01', strtotime('-3 Months'));//申请日期-起始日期(前3个月的1号)
            $apply_date_end = date('Y-m-t', strtotime('-1 Months'));//申请日期-结束日期(上个月的月末)

            //浮动比例
            $floating_ratio = EnvModel::getEnvByCode('material_sau_floating_ratio');
            //按照除以100的数值储存，比如10%,就填写0.1;比如200%,就填写2，如果为空，默认为1。如果为0 ，就乘以0
            $floating_ratio = ($floating_ratio === '') ? 1 : $floating_ratio;

            //开始同步sau参考单价
            $deal_sau_data =  $this->getSauPriceList($i, $apply_date_start, $apply_date_end);
            while (!empty($deal_sau_data)) {
                echo '第' . $i . '页处理开始' . PHP_EOL;
                $sau_ids = array_values(array_column($deal_sau_data, 'id'));
                $deal_sau_list = array_column($deal_sau_data, null, 'id');
                $sau_list = MaterialSauModel::find([
                    'conditions' => 'id in ({ids:array})',
                    'bind' => ['ids' => $sau_ids]
                ]);
                if (!empty($sau_list->toArray())) {
                    foreach ($sau_list as $sau) {
                        $deal_sau_info = $deal_sau_list[$sau->id];
                        if (is_numeric($deal_sau_info['total']) && $deal_sau_info['total'] != 0) {
                            //若数量不为0才可做除法
                            $price = round(bcmul(bcdiv($deal_sau_info['price'], $deal_sau_info['total'], 8), $floating_ratio, 8), 6);
                        } else {
                            $price = 0;
                        }
                        $log .= 'barcode：' . $sau->barcode . '; price由：' . $sau->price . ' 变更为：' . $price . '; 更新结果为：';
                        $sau->price = $price;
                        $sau->updated_at = date('Y-m-d H:i:s');
                        $result = $sau->save();
                        $log .= ($result === false) ? '失败' : '成功';
                        $log .= PHP_EOL . '   ';
                    }
                }
                echo '第' . $i . '页处理结束' . PHP_EOL;
                sleep(1);
                $i += 1;
                $deal_sau_data = $this->getSauPriceList($i, $apply_date_start, $apply_date_end);
            }
            if (empty($deal_sau_data)) {
                echo '数据均已处理完毕' . PHP_EOL;
            }
            if ($log) {
                $this->logger->info($log);
            }
        } catch (Exception $e) {
            $this->logger->warning('material_sau_sync_price_task_exception:' . $e->getMessage());
            echo date('Ymd H:i:s') . ' 同步参考单价失败[异常]' . $e->getMessage() . PHP_EOL;
        }
        echo date('Ymd H:i:s') . ' 脚本执行结束' . PHP_EOL;
    }

    /**
     * 获取需要审核的出库单列表
     * @param int $page_num 当前页码
     * @param string $apply_date_start 申请日期-起始日期
     * @param string $apply_date_end 申请日期-结束日期
     * @return mixed
     */
    private function getSauPriceList($page_num, $apply_date_start, $apply_date_end)
    {
        $page_size = 100;
        $offset = $page_size * ($page_num - 1);
        //material_sau表未删除、启用的barcode && purchae_order表审批状态=待审批、已通过 && apply_date申请日期在3个月内的 && purchase_order_product表，product_option_code产品标号 = barcode
        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            's.id',
            'SUM(pop.price*po.exchange_rate * pop.total/1000000) as price',
            'SUM(pop.total) as total'
        ]);
        $builder->from(['s' => MaterialSauModel::class]);
        $builder->leftjoin(PurchaseOrderProduct::class, 'pop.product_option_code = s.barcode', 'pop');
        $builder->leftjoin(PurchaseOrder::class, 'po.id = pop.poid', 'po');
        $builder->where('s.is_deleted = :is_deleted: AND s.status = :status:', ['is_deleted' => GlobalEnums::IS_NO_DELETED, 'status' => MaterialClassifyEnums::MATERIAL_START_USING]);
        $builder->inWhere('po.status', [Enums::WF_STATE_PENDING, Enums::WF_STATE_APPROVED]);
        $builder->betweenWhere('po.apply_date', $apply_date_start, $apply_date_end);
        $builder->limit($page_size, $offset);
        $builder->groupBy('s.id');
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 发送参考单价是0的清单到集团资产&采购邮箱
     * 每个月1号 北京时间10:00:10
     */
    public function send_price_emailAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $this->check_process($process_name);
        echo date('Ymd H:i:s').' 脚本开始执行'.PHP_EOL;
        try {
            $sau = MaterialSauModel::find([
                'columns' => 'barcode, name_zh, name_en, price',
                'conditions' => 'is_deleted = :is_deleted: and price = :price: and status = :status:',
                'bind' => ['is_deleted' => GlobalEnums::IS_NO_DELETED, 'price' => 0, 'status' => MaterialClassifyEnums::MATERIAL_START_USING]
            ])->toArray();
            if (!empty($sau)) {
                //邮箱
                $emails = EnvModel::getEnvByCode('material_sau_price_email');
                if (empty($emails)) {
                    echo '集团资产&采购邮箱为空' . PHP_EOL;
                    $this->logger->info('material_sau_send_price_email 集团资产&采购邮箱为空');
                    exit();
                }
                $file_path = $this->exportPrice($sau);
                $email_arr = explode(',', $emails);
                $country = get_country_code();
                $title = 'barcode 价格为0（purchase price = 0）-' . $country;
                $html = '<p> 您好，附件是barcode价格为0的列表，请查看并及时去OA系统进行维护。请点击下述链接进行下载：<br/><a href="' . $file_path . '">barcode单价为0（barcode price=0）</a></p>';
                $html .= '<p> hi, attached is the list of barcode price is 0, please check and go to OA system for maintenance in time.Please click the following link to download:</br><a href="' . $file_path . '">barcode单价为0（barcode price=0）</a></p>';
                $send_result = $this->mailer->sendAsync($email_arr, $title, $html);
                if ($send_result) {
                    echo '发送参考单价是0的清单到集团资产&采购邮箱发送成功,收件邮箱:' . $emails. PHP_EOL;
                } else {
                    echo '发送参考单价是0的清单到集团资产&采购邮箱发送失败,收件邮箱:' . $emails . PHP_EOL;
                    $this->logger->warning('material_sau_send_price_email_task 邮件发送失败，收件邮箱:' . $emails);
                }
            } else {
                echo '暂无数据需要发送邮件至集团资产&采购' . PHP_EOL;
            }
        } catch (Exception $e) {
            $this->logger->warning('material_sau_send_price_email_task_exception:' . $e->getMessage());
            echo date('Ymd H:i:s') . ' 发送参考单价是0的清单到集团资产&采购邮箱[异常]' . $e->getMessage() . PHP_EOL;
        }
        echo date('Ymd H:i:s') . ' 脚本执行结束' . PHP_EOL;
    }

    /**
     * 将price=0的标准型号写入excel，作为邮件附件发送
     * @param array $sau 标准型号
     * @return mixed|string 文件地址
     */
    private function exportPrice($sau)
    {
        $header = [
            'barcode',
            '中文名称',
            '英文名称',
            '参考单价'
        ];
        $excel_data[] = [
            'barcode',
            'CN-Name',
            'EN-Name',
            'price'
        ];
        foreach ($sau as $item) {
            $excel_data[] = [
                $item['barcode'],
                $item['name_zh'],
                $item['name_en'],
                $item['price']
            ];
        }
        $result = StandardService::getInstance()->exportExcel($header, $excel_data, 'barcode单价为0（barcode price=0）');
        return $result['data'] ?? '';
    }
}