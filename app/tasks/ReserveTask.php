<?php

use App\Library\Enums;
use App\Library\ErrCode;
use App\Library\OssHelper;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\ReserveFundReturnEnums;
use App\Modules\Common\Models\EnvModel;
use App\Modules\ReserveFund\Services\ApplyService;
use App\Modules\ReserveFund\Services\PayFlowService;
use App\Modules\Training\Services\TaskService;
use App\Modules\ReserveFund\Services\SapsService;
use App\Modules\Organization\Models\SysDepartmentModel;
use App\Modules\ReserveFund\Models\ReserveFundApply;
use App\Modules\Common\Services\EnumsService;
use App\Models\backyard\HrStaffItemsModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Library\Validation\ValidationException;
use App\Modules\OrdinaryPayment\Services\SapService as oSapService;
use App\Library\Validation\Validation;
use App\Modules\User\Services\UserService;
use App\Library\BaseController;
use App\Modules\ReserveFund\Models\ReserveFundReturn;
use App\Models\oa\ReserveFundApplyReturnRelModel;

class ReserveTask extends BaseTask{
    public function flush_reserve_role_authAction(){
        // 权限暂时不加
        if (0) {
            $sql = "select staff_info_id from `hr_staff_info` where `staff_info_id` in (select distinct `staff_info_id` from `hr_staff_info_position` where `position_category` in (4,18)) and formal=1 and state=1 and wait_leave_state=0;";
            $details = $this->db_rbackyard->fetchAll($sql,\Phalcon\Db::FETCH_ASSOC);
            $ids = array_filter(array_column($details,"staff_info_id"));
            // 给备用金申请和归还添加权限
            $successNum = $failedNum = 0;
            $totalNum = !empty($ids) ? count($ids) : 0;
            foreach ($ids as $id) {
                // 不存在的员工
                $sql = "select * from staff_permission where staff_id='{$id}'";
                $item = $this->db_oa->fetchOne($sql,\Phalcon\Db::FETCH_ASSOC);
                if (empty($item)) {
                    $sql = "insert into staff_permission(staff_id,permission_ids,is_granted) values({$id},'293,294,300,301,302,303,298,315,316,317,318',1);";
                } else {
                    $permissionIds = $item['permission_ids'].',293,294,300,301,302,303,298,315,316,317,318';
                    $permissionIdStr = implode(',',array_unique(array_filter(explode(',',$permissionIds))));
                    $sql = "update staff_permission set permission_ids='{$permissionIdStr}' where staff_id='{$id}'";
                }

                $flag = $this->db_oa->execute($sql);
                if (empty($flag)) {
                    $failedNum++;
                    $this->logger->warning("备用金申请和归还添加权限更新失败: staff_info_id => $id");
                } else {
                    $successNum++;
                }
            }
            echo "备用金申请和归还添加权限,共计：$totalNum, 成功：$successNum, 失败：$failedNum \n";
        }

        // 给每一个员工添加权限
        $sql = "select * from staff_permission;";
        $details = $this->db_oa->fetchAll($sql,\Phalcon\Db::FETCH_ASSOC);
        $idsArr = array_column($details,null,"id");
        $successNum = $failedNum = 0;
        $totalNum = !empty($idsArr) ? count($idsArr) : 0;
        // 给备用金审核和回复添加权限
        foreach ($idsArr as $id => $idItem) {
            $permissionIds = $idItem['permission_ids'].',293,295,304,305,297,306,307,308,309,310,311';
            $permissionIdStr = implode(',',array_unique(array_filter(explode(',',$permissionIds))));
            $sql = "update staff_permission set permission_ids='{$permissionIdStr}' where id={$id}";

            $flag = $this->db_oa->execute($sql);
            if (empty($flag)) {
                $failedNum++;
                $this->logger->warning("备用金审核和回复添加权限更新失败: id => $id");
            } else {
                $successNum++;
            }
        }

        echo "备用金申请和归还添加权限,共计：$totalNum, 成功：$successNum, 失败：$failedNum \n";
    }

    // 备用金申请编号占用
    public function flush_reserve_fund_rfanoAction($params){
        set_time_limit(0);
        $total = $params[0] ?? 1;
        $now_date = date("Ymd");

        for ($i = 0; $i < $total; $i++) {
            echo 'WDBYJ' . \App\Modules\ReserveFund\Services\ApplyService::getNo($now_date) . "\n";
        }

        // 最后的最后，输出文件
        echo "生成申请单编号完成，共 {$total} 条\n";
    }

    /**
     * 备用金申请发送sap
     * */
    public function reserve_apply_sapAction()
    {
        $this->checkLock(__METHOD__, 10800);
        echo 'begin: ' . date('Y-m-d H:i:s') . PHP_EOL;

        try {
            //计算本月日期1-指定日期 只执行上个月 指定日期-31号只执行本月数据
            $start_date     = date('Y-m-01 00:00:00');
            $end_date       = date("Y-m-{$this->getSapTaskSendDate()} 00:00:00");
            $is_little_date = true;

            if (date('Y-m-d H:i:s') >= $start_date && date('Y-m-d H:i:s') < $end_date) {
                $end_time   = $start_date;
                $start_time = date('Y-m-01 00:00:00', strtotime(date('Y-m-01') . ' -1 month'));
            }

            $last_day   = oSapService::getLastDay(ltrim(date('m')), ltrim(date('Y')));
            $end_date_2 = date('Y-m-d H:i:s', (strtotime($start_date) + 3600 * 24 * $last_day));
            if (date('Y-m-d H:i:s') >= $end_date && date('Y-m-d H:i:s') < $end_date_2) {
                $start_time     = $start_date;
                $end_time       = date('Y-m-d H:i:s');
                $is_little_date = false;
            }

            echo 'is_little_date: ' . ($is_little_date ? 'Yes' : 'No') . PHP_EOL;
            echo "real_pay_at 时间范围: start_time-{$start_time},  end_time-{$end_time}" . PHP_EOL;

            $ledger_account       = EnvModel::getEnvByCode('reserve_sap_ledger_account');
            $ledger_account       = json_decode($ledger_account,true);
            $cost_company_id = EnumsService::getInstance()->getSettingEnvValueIds('purchase_sap_company_ids');
            if (empty($cost_company_id)) {
                throw new ValidationException('purchase_sap_company_ids 未配置', ErrCode::$VALIDATE_ERROR);
            }

            $sap_company_ids = SysDepartmentModel::find([
                'conditions' => 'id in ({ids:array})',
                'bind'       => ['ids' => $cost_company_id],
            ])->toArray();
            $cost_company_ids = implode(',', $cost_company_id);

            echo 'purchase_sap_company_ids: ' . $cost_company_ids . PHP_EOL;

            $sap_company_ids = array_column($sap_company_ids, 'sap_company_id', 'id') ?? [];

            $db_obj         = $this->db_oa;
            $t                    = TaskService::getTranslation(get_country_code());

            $send_data = array();
            //发送邮件
            $email = \App\Modules\Common\Models\EnvModel::getEnvByCode('reserve_apply_email');
            $email = explode(',', $email);

            $i   = 0;
            $x   = 0;
            $sql = "select id,create_store_id,rfano,currency,amount,pc_code,real_pay_at,apply_date,sync_sap,voucher_description,create_store_name,create_store_id,create_company_id from reserve_fund_apply where real_pay_at>='{$start_time}' and real_pay_at<'{$end_time}' and status = 3 and pay_status= 2 and create_company_id in ({$cost_company_ids})  and sync_sap in (0,3) limit {$i} ,100";

            $data          = $db_obj->query($sql);
            $deal_sap_data = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            $x             = 0;
            while (!empty($deal_sap_data)) {
                foreach ($deal_sap_data as $key => $value) {
                    echo "当前待处理单号: {$value['rfano']}" . PHP_EOL;
                    echo 'reserve apply id:' . $value['id'] . PHP_EOL;

                    echo "实际支付时间: {$value['real_pay_at']}" . PHP_EOL;

                    $request_data = [];
                    $return_data  = [];

                    // 1-指定日期不是上个月数据跳过  指定日期-31不是本月数据跳过
                    if ($is_little_date) {
                        if (ltrim(date('m', strtotime($value['real_pay_at']))) == ltrim(date('m'))) {
                            echo '1-10, 非上月, 跳过' . PHP_EOL;
                            continue;
                        }
                    } else {
                        if (ltrim(date('m', strtotime($value['real_pay_at']))) != ltrim(date('m'))) {
                            echo '11-31, 非本月, 跳过' . PHP_EOL;
                            continue;
                        }
                    }

                    $reserve_found = ReserveFundApply::findFirst([
                        'id = :id:',
                        'bind' => ['id' => $value['id']]
                    ]);

                    if (empty($reserve_found)) {
                        echo '单据不存在, 跳过' . PHP_EOL;
                        continue;
                    }

                    $request_data = [
                        'supplier'     => 'S00215',
                        'company'      => $sap_company_ids[$value['create_company_id']] ?? 'FEX01',
                        'rfano'        => $value['rfano'],
                        'credit'       => 4,
                        'description'  => $value['voucher_description'],
                        'currency'     => $t->_(GlobalEnums::$currency_item[$value['currency']]),
                        'real_pay_at'  => date('Y-m-d',strtotime($value['real_pay_at'])),
                        'apply_at'     => $value['apply_date'],
                        'pc_code'      => $value['pc_code'],
                        'amount'       => bcdiv($value['amount'], 1000, 2),
                        'account_code' =>  $ledger_account['ledger_account_id']??'********',

                    ];
                    if (get_country_code() == GlobalEnums::MY_COUNTRY_CODE) {
                        $request_data['description'] = (strlen($value['create_store_name']) > 40) ? $value['create_store_id'] : $value['create_store_name'];
                    }

                    $z            = 0;
                    while (empty($return_data) && $z < 1) {
                        $z++;
                        $return_data =SapsService::getInstance()->ReserveApplySap($request_data);
                        if (isset($return_data['SAP_UUID']) && !empty($return_data['SAP_UUID'])) {
                            echo '抛数成功, SAP_UUID=' . $return_data['SAP_UUID'] . PHP_EOL;

                            $x++;
                            $update_res = $reserve_found->i_update(['sync_sap' => 1, 'sap_uuid' => $return_data['SAP_UUID'] ?? '', 'updated_at' => date('Y-m-d H:i:s')]);

                            $row['no'] = $value['rfano'];
                            $send_data[] = $row;
                        } else {
                            $note = '';
                            if (isset($return_data['log']['Item']) && !empty($return_data['log']['Item'])) {
                                $note = array_column($return_data['log']['Item'], 'Note');
                                $note = implode(',', $note);
                            }

                            echo '抛数失败, sap_note=' . $note . PHP_EOL;

                            $update_res = $reserve_found->i_update(['sync_sap' => 2, 'updated_at' => date('Y-m-d H:i:s'),'sap_note'=>$note]);
                        }

                        echo '抛数结果DB状态更新: ' . ($update_res === false ? '失败' : '成功') . PHP_EOL;

                        sleep(2);
                    }

                    echo PHP_EOL;
                }

                //拼接 html  发送邮件
                if(!empty($send_data) && !empty($email)){
                    $title = "OA SAP document creation reminder";
                    $html = $this->format_html($send_data);

                    $this->mailer->sendAsync($email, $title, $html);
                }
                sleep(1);
                $i   += 100;
                $sql = "select id,create_store_id,rfano,currency,amount,pc_code,real_pay_at,apply_date,sync_sap,voucher_description,create_store_name,create_store_id,create_company_id from reserve_fund_apply where  real_pay_at>='{$start_time}' and real_pay_at<'{$end_time}' and status = 3 and pay_status= 2 and create_company_id in ({$cost_company_ids})  and sync_sap in (0,3) limit {$i} ,100";


                $data          = $db_obj->query($sql);
                $deal_sap_data = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            }

            echo 'reserve_found_apply 成功:' . $x . '条数' . PHP_EOL;

        } catch (ValidationException $e) {
            $this->logger->notice('reserve_apply_sap_task_validation:' . $e->getMessage());
            echo $e->getMessage() . PHP_EOL;
        } catch (Exception $e) {
            $this->logger->warning('reserve_apply_sap_task_exception:' . $e->getMessage());
            echo $e->getMessage() . PHP_EOL;
        }

        echo 'end: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $this->clearLock(__METHOD__);
        exit();
    }

    //整理 sap 订单提醒邮件内容
    public function format_html($data){
        $html  = "<p>Dear all,<?p></br></br>";
        $html .= "<p>Below data are posted in SAP, please have a check! Thanks!</p></br></br>";
        $html .= "<p>以下数据已过账到SAP，请检查！谢谢</p>";

        $html .= "<table border='1'><tr><td>OA number</td></tr>";
        foreach ($data as $da){
            $html .= "<tr><td>{$da['no']}</td></tr>";
        }

        $html .= "</table>";
        return $html;

    }

    /**
     * 发送上个月未归还和已归还的备用金申请单提醒邮件
     */
    public function send_reserve_fund_last_monthAction(){
        // 上个月第一天
        $last_month_first = date('Y-m-d 00:00:00', strtotime(date('Y-m-01') . ' -1 month')); // 计算出本月第一天再减一个月
        //　上个月最后一天:
        $last_month_last = date('Y-m-d 23:59:59', strtotime(date('Y-m-01') . ' -1 day')); // 计算出本月第一天再减一天

        try {
            // 取当前国家的快递公司
            $sys_company_ids = EnumsService::getInstance()->getSysDepartmentCompanyIds();
            //Flash Express(泰国)\Flash Express_PH(PH系统）\Flash Malaysia Express(MY系统）/ Flash Laos（LA系统）
            $express_company_id = $sys_company_ids['FlashExpress'] ?? 'noset';

            $obj = ApplyService::getInstance();
            $obj->setLanguage('en');
            // 统计所有历史未归还的备用金数据
            $noback_data = $obj->exportList([
                'create_company_id' => $express_company_id,
                'return_status' => ReserveFundReturnEnums::BACK_STATUS_NOT
            ]);
            // 统计上个月1日~上月最后一天内，已归还的备用金数据
            $last_month_data = $obj->exportList([
                'create_company_id' => $express_company_id,
                'return_status' => ReserveFundReturnEnums::BACK_STATUS_BACK,
                'return_date_start' => $last_month_first,
                'return_date_end' => $last_month_last
            ]);
            $total_data = array_merge($noback_data,$last_month_data);
            if (empty($total_data)) {
                $this->myLogger('未归还备用金数据以及上个月已归还为空');
                exit;
            }

            // 生成excel借款数据表格
            $res = $obj->returnExport($total_data);
            if (empty($res['data'])) {
                $this->myLogger('导出备用金Excel数据失败');
                exit;
            }

            //取配置中的发送人
            $emails = EnvModel::getEnvByCode('loan_data_send_email','');
            $emails = array_values(array_filter(explode(',',$emails)));
            if (empty($emails)) {
                $this->myLogger('未配置邮箱或者配置的邮箱地址为空');
                exit;
            }
            //发送邮件并记录日志
            $title = 'Petty Cash Data（网点备用金数据）';
            $content = <<<EOF
            Hi All，<br><br>
                Please check the attachment regarding Branch Petty Cash data in OA system.<br/>
                They are outstanding or returned applications in last month.<br/>
                Thanks!<br/>
                请检查附件网点备用金数据。该部分数据是系统上个月的未归还清单以及在上月归还的数据。谢谢！<br/>
EOF;
            if ($this->mailer->send($emails, $title, $content,[$res['data']])) {
                $this->myLogger('send email:'.implode($emails).' success');
            } else {
                $this->myLogger('send email:'.implode($emails).' fail');
            }

        }catch (Exception $e){
            $this->myLogger('email error=='.$e->getMessage(),1);
        }
    }

    /**
     * 统计所有历史未归还的备用金数据并发送给申请人，上级和财务组
     */
    public function send_reserve_fund_to_applyAction()
    {

        try {
            // 取当前国家的快递公司
            $sys_company_ids    = EnumsService::getInstance()->getSysDepartmentCompanyIds();
            $express_company_id = $sys_company_ids['FlashExpress'] ?? 'noset';

            $obj = ApplyService::getInstance();
            $obj->setLanguage('en');
            // 统计所有历史未归还的备用金数据

            $no_back_data = ReserveFundApply::find([
                'conditions' => 'return_status = :return_status:',
                'bind'       => ['return_status' => ReserveFundReturnEnums::BACK_STATUS_NOT],
                'columns'    => ['create_id', 'create_company_id']
            ])->toArray();

            // 备用金申请人id
            $create_ids = array_filter(array_column($no_back_data, 'create_id', 'id'));
            // 申请人所属公司
            $create_companies = array_column($no_back_data, 'create_company_id', 'id');
            if (empty($create_ids)) {
                $this->myLogger('收件人为空');
                exit;
            }

            //备用金申请人直接上级id
            $builder = $this->modelsManager->createBuilder();
            $builder->columns(['main.value,main.staff_info_id']);
            $builder->from(['main' => HrStaffItemsModel::class]);
            $builder->leftJoin(HrStaffInfoModel::class, 'main.staff_info_id=info.staff_info_id', 'info');
            $builder->andWhere('main.item = :item:', [
                'item' => 'MANGER'
            ]);
            $builder->inWhere('main.staff_info_id', array_values($create_ids));
            $director_ids       = $builder->getQuery()->execute()->toArray();
            $director_staff_ids = !empty($director_ids) ? array_column($director_ids, 'value', 'staff_info_id') : [];
            $director_ids       = !empty($director_ids) ? array_unique(array_filter(array_column($director_ids, 'value'))) : [];

            $tmp_director_ids = [];
            if (!empty($director_ids)) {
                foreach ($director_ids as $id) {
                    $tmp_director_ids = array_filter(array_merge($tmp_director_ids, explode(',', $id)));
                }
            }
            $tmp_director_ids = array_merge($tmp_director_ids, $create_ids);
            $all_emails       = HrStaffInfoModel::find([
                'conditions' => 'staff_info_id in({ids:array}) and state = 1',
                'bind'       => [
                    'ids' => $tmp_director_ids
                ]
            ])->toArray();
            $all_emails       = array_filter(array_column($all_emails, 'email', 'staff_info_id'));

            // 银行详情
            $bank_info = EnvModel::getEnvByCode('email_content_bank_info', '');

            // 邮箱详情
            $email_info = EnvModel::getEnvByCode('email_content_send_email', '');

            // 当地财务组邮箱
            $fmails = EnvModel::getEnvByCode('loan_data_send_email', '');
            $fmails = array_values(array_filter(explode(',', $fmails)));
            //发送邮件并记录日志
            $title        = 'Reminder:Outstanding Petty Cash';
            $unique_email = [];
            foreach ($create_ids as $id => $employ_id) {
                $employ_email = $all_emails[$employ_id] ?? '';
                if (empty($employ_email) || in_array($employ_email, $unique_email)) {
                    continue;
                }
                $unique_email[]  = $employ_email;
                $bank_email_info = '';
                // 是否是当前国家的快递公司
                if (isset($create_companies[$id]) &&
                    $express_company_id == $create_companies[$id]) {
                    $bank_email_info = <<<EOF
【below  the bank information. <br>
        {$bank_info}
        Regards,<br>
        {$email_info}】<br/>
EOF;
                }

                $content        = <<<EOF
    Dear {$employ_id},<br><br>
        This is a reminder that you have petty cash fund under your account in OA system.<br><br>
        If you have already liquidated, please forward proof of payment to the email below and return petty cash in OA system in time. If not, kindly settle promptly. <br><br>
        Once you shift to another Hub/Network, please return petty cash to our company bank account first and reapply petty cash.<br><br>
        {$bank_email_info}
EOF;
                $director_email = (isset($director_staff_ids[$employ_id]) && isset($all_emails[$director_staff_ids[$employ_id]]) &&
                    !empty($all_emails[$director_staff_ids[$employ_id]])) ? [$all_emails[$director_staff_ids[$employ_id]]] : [];
                // 包含当地财务
                $director_email = array_merge($director_email, $fmails);
                try {
                    if ($this->mailer->sendAsync($employ_email, $title, $content, [], $director_email)) {
                        $this->myLogger('邮箱地址：' . $employ_email . ' send success');
                    } else {
                        $this->myLogger('邮箱地址：' . $employ_email . ' send fail');
                    }
                } catch (Exception $e) {
                    $this->myLogger('email error==' . $e->getMessage());
                }
            }
        } catch (Exception $e) {
            $this->myLogger('email error==' . $e->getMessage(), 1);
        }
    }

    public function myLogger($str,$flag=0){
        echo "reserveTask==".$str.PHP_EOL;
        if(empty($flag)){
            $this->logger->info($str);
        }else{
            $this->logger->warning($str);
        }
    }

    /**
     * 开通备用机申请和归还权限
     * @params $params 命令行参数
     * PS:
     * 1. 第一个参数: all: 全量员工; incr: 增量员工(默认增量: hr_staff_info 表 24 小时内添加的)
     * 2. 第二个参数: 权限配置key; (reserve_fund_manager_auth_ids)
     **/
    public function add_permission_to_managerAction($params = [])
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log = '任务名称: ' . $process_name . ' ------ 系统所在国家: ' . get_country_code() . ' ----- ' . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        try {
            // 取数条件初值
            $get_staff_type = isset($params[0]) && $params[0] == 'all' ? 'all' : 'incr';
            $permission_code = isset($params[1]) ? (string)$params[1] : 'reserve_fund_manager_auth_ids';

            $log .= "传入参数: get_staff_type = $get_staff_type, permission_code = $permission_code" . PHP_EOL;

            $this->check_process($process_name);

            // 取权限配置
            $permission_config      = EnvModel::getEnvByCode($permission_code);
            $log .= "待配置的权限: $permission_config" . PHP_EOL;

            $permission_config      = json_decode($permission_config, true);
            // 未配置权限
            if (empty($permission_config)) {
                throw new ValidationException("setting_env[{$permission_code}] 未配置 或 配置内容为空", ErrCode::$VALIDATE_ERROR);
            }

            $permission_config_data = [];
            foreach ($permission_config as $k1 => $v1) {
                $permission_config_data = array_merge($v1, $permission_config_data);
            }
            foreach ($permission_config_data as $key1=> $value1){
                $permission_config_data[$key1] = (string)$value1;
            }

            // 配置权限id
            $permission_config_ids = implode(',', $permission_config_data);
            // 添加权限的职位id
            $job_title_ids      = EnvModel::getEnvByCode('reserve_fund_auth_job_title_id');
            $job_title_ids      = explode(',',$job_title_ids);
            // 在职 + 有编制 + 非代理制 + 非子账号
            $get_hr_staff_sql = "state = :state: AND formal = :formal: AND wait_leave_state = :wait_leave_state: AND is_sub_staff = :is_sub_staff: AND job_title in({job_title:array})";
            $get_hr_staff_bind = [
                'state' => 1,
                'formal' => 1,
                'wait_leave_state' => 0,
                'is_sub_staff' => 0,
                'job_title' => $job_title_ids
            ];

            if ($get_staff_type == 'incr') {
                $get_hr_staff_sql .= " AND created_at >= :created_at:";
                $get_hr_staff_bind['created_at'] = date('Y-m-d H:i:s', strtotime('-1 day'));
            }

            $i   = 0;
            $batch_count = 0;
            $per_length = 1000;
            $staff_info_data = HrStaffInfoModel::find([
                'conditions' => $get_hr_staff_sql,
                'bind' => $get_hr_staff_bind,
                'offset' => $i,
                'limit' => $per_length,
                'order' => 'id DESC',
                'columns' => ['staff_info_id']
            ])->toArray();

            $log .= PHP_EOL . "处理批次[batch=$batch_count]: start = $i, per_length = $per_length, 本批次待处理员工数[From HR]: " . count($staff_info_data) . PHP_EOL;

            // 对员工分批次处理权限
            while (!empty($staff_info_data)) {
                $staff_info_ids        = array_column($staff_info_data, 'staff_info_id');

                $staff_permission_data = \App\Modules\User\Models\StaffPermissionModel::find([
                    "conditions" => 'staff_id in ({ids:array})',
                    "bind"       => ['ids' => $staff_info_ids]
                ])->toArray();
                $permission_staff_ids  = array_column($staff_permission_data, 'staff_id');
                $permission_ids        = array_column($staff_permission_data, 'permission_ids', 'staff_id');

                $log .= '已有OA账号的员工数[In OA]: ' . count($staff_permission_data) . PHP_EOL;

                // 计算 hr工号 与 OA已分配权限工号的交集
                $intersect = array_intersect($staff_info_ids, $permission_staff_ids);

                // 计算 hr 新增的工号(取差集)
                $add_staff_permission = array_diff($staff_info_ids, $intersect);

                $last_updated_at = date("Y-m-d H:i:s");

                $addList = [];

                $log .= PHP_EOL . '需在OA新增的员工数[Insert OA]: ' . count($add_staff_permission) . PHP_EOL;

                if (!empty($add_staff_permission)) {
                    // 给 hr 新员工 分配预设置的权限
                    foreach ($add_staff_permission as $k => $v) {
                        $addList[] = [
                            'staff_id'        => $v,
                            'permission_ids'  => $permission_config_ids,
                            'is_granted'      => 1,
                            'last_updated_at' => $last_updated_at
                        ];
                    }
                }

                // *** 本批次员工权限处理开始*** //
                $db_oa  = $this->getDI()->get('db_oa');
                $db_oa->begin();
                try {
                    if (!empty($addList)) {
                        $res = '';
                        $res = (new \App\Modules\User\Models\StaffPermissionModel())->batch_insert($addList);

                        if ($res === false) {
                            throw new Exception('本批次批量新增失败: 员工共 ' . count($addList) . '个, 明细: ' . json_encode($addList, JSON_UNESCAPED_UNICODE));
                        }

                        $log .= '本批次批量新增成功 '.count($addList) . ' 个' . PHP_EOL;
                    }

                    $log .= PHP_EOL . '需在OA更新的员工数[Update OA]: ' . count($intersect) . PHP_EOL;

                    // 需要更新的人
                    if (!empty($intersect)) {
                        foreach ($intersect as $k => $v) {
                            // 员工在OA已有的权限
                            $permission_id_arr = explode(',', $permission_ids[$v]);

                            // 已有权限 与 待分配权限全集
                            $permission_id_arrs = array_unique(array_merge($permission_id_arr, $permission_config_data));

                            // 员工本次待追加的权限
                            $permission_diff_ids = array_diff($permission_id_arrs, $permission_id_arr);
                            if (!$permission_diff_ids) {
                                $log .= '权限无新增, 不需更新, 跳过: staff_id = ' . $v . PHP_EOL;
                                continue;
                            }

                            // 待追加权限
                            $permission_v_ids  = implode(",", $permission_id_arrs);

                            $sql               = "update staff_permission set `permission_ids`='{$permission_v_ids}',`last_updated_at`='{$last_updated_at}'  where staff_id= {$v}";
                            $flag              = $db_oa->execute($sql);
                            if (empty($flag)) {
                                throw new Exception("权限更新失败: staff_id = $v, permission = $permission_v_ids");
                            }

                            $log .= "权限更新成功: staff_id = $v, permission = $permission_v_ids" . PHP_EOL;
                        }
                    }

                    $db_oa->commit();

                }  catch (\Exception $e) {
                    $db_oa->rollback();

                    $log .= "本批次权限新增/更新异常[已跳过该批次]: " . $e->getMessage() . PHP_EOL;
                }
                // *** 本批次员工权限处理结束*** //

                sleep(5);

                // 取下一批次工号
                $i += $per_length;
                $batch_count++;
                $staff_info_data = HrStaffInfoModel::find([
                    'conditions' => $get_hr_staff_sql,
                    'bind' => $get_hr_staff_bind,
                    'offset' => $i,
                    'limit' => $per_length,
                    'order' => 'id DESC',
                    'columns' => ['staff_info_id']
                ])->toArray();

                $log .= PHP_EOL . "处理批次[batch=$batch_count]: start = $i, per_length = $per_length, 本批次待处理员工数[From HR]: " . count($staff_info_data) . PHP_EOL;
            }

        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;
        } catch (\Exception $e) {
            $log .= $e->getMessage() . PHP_EOL;
        }

        $log .= "结束时间: " . date('Y-m-d H:i:s') . PHP_EOL;
        $this->getDI()->get('logger')->info("add-staff-permission " . $log);
        exit($log);
    }

    /**
     * 备用金归还审批工具
     * 说明: 用于处理待审批人离职的单据
     *
     * 参数示例:
     * 参数加密前:
     * $request_data = [
     *     'auditor_id' => 10000, // 必填: 审批人
     *     'biz_id' => 9, // 必填: 业务表id
     *     'audit_action' => 1, // 必填: 审批行为 1=通过; 2=驳回
     *     'audit_remark' => '', // 选填: 审批备注
     *     'bank_flow_date' => null // 选填: 审批通过时, 是否修改银行流水日期, 格式示例: 2023-01-01
     * ]
     * 参数加密规则: base64_encode(serialize($request_data))
     *
     * php app/cli.php reserve return_audit YTo1OntzOjEwOiJhdWRpdG9yX2lkIjtpOjE5NTE1O3M6NjoiYml6X2lkIjtpOjY3MDtzOjEyOiJhdWRpdF9hY3Rpb24iO2k6MTtzOjEyOiJhdWRpdF9yZW1hcmsiO3M6MDoiIjtzOjE0OiJiYW5rX2Zsb3dfZGF0ZSI7Tjt9
     *
     * @param array $args $args[0] 提取上述参数
     *
     *
     */
    public function return_auditAction(array $args)
    {
        $process_name = str_replace('Action', '_task', __FUNCTION__);
        $log = '任务名称: ' . $process_name . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log .= '初始内存: ' . memory_usage() . PHP_EOL;

        $is_exception = false;

        try {
            // 请求参数
            $request_params = unserialize(base64_decode($args[0]));
            $log .= '任务参数: ' . json_encode($request_params, JSON_UNESCAPED_UNICODE) . PHP_EOL;

            // 校验规则
            $params_rules = [
                'auditor_id' => 'Required|IntGe:1',
                'biz_id' => 'Required|IntGe:1',
                'audit_action' => 'Required|IntIn:1,2',
                'audit_remark' => 'IfIntEq:audit_action,2|Required|StrLenGeLe:1,1000',
            ];

            if (!empty($request_params['bank_flow_date'])) {
                $params_rules['bank_flow_date'] = 'Required|Date|DateTo:' . date('Y-m-d');
            }

            Validation::validate($request_params, $params_rules);

            // 审批人信息
            $user_object = (new UserService())->getUserById($request_params['auditor_id']);
            if (empty($user_object)) {
                throw new ValidationException("HRIS系统中不存在该审批人[{$request_params['auditor_id']}]", ErrCode::$VALIDATE_ERROR);
            }

            $user_info = (new BaseController())->format_user($user_object);

            // 审批人可修改的表单项
            $audit_update = [];
            if ($request_params['audit_action'] == 1 && !empty($request_params['bank_flow_date'])) {
                $audit_update = [
                    'bank_flow_date' => $request_params['bank_flow_date']
                ];
            }

            $service = new PayFlowService(Enums::WF_RESERVE_FUND_RETURN, $audit_update);
            if ($request_params['audit_action'] == 1) {
                //通过
                $res = $service->approve($request_params['biz_id'], $request_params['audit_remark'], $user_info);
            } else {
                $res = $service->reject($request_params['biz_id'], $request_params['audit_remark'], $user_info);
            }

            $log .= '审批结果: ' . json_encode($res, JSON_UNESCAPED_UNICODE) . PHP_EOL;

        } catch (ValidationException $e) {
            $log .= '验证异常: ' . $e->getMessage() . PHP_EOL;

        } catch (Exception $e) {
            $log .= '系统异常: ' . $e->getMessage() . PHP_EOL;
            $is_exception = true;
        }

        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        if ($is_exception) {
            $this->logger->error($log);
        } else {
            $this->logger->info($log);
        }

        exit($log);
    }


    /**
     * 备用金归还发送sap
     * @return bool
     */
    public function reserve_reback_sapAction()
    {
        $this->checkLock(__METHOD__);
        $log = 'begin: ' . date('Y-m-d H:i:s') . PHP_EOL;
        //purchase_sap_company_ids 只有配置了这个才会启动脚本 目前开通sap 国家 泰国菲律宾马来
        $cost_company_id = (new EnumsService())->getSettingEnvValueIds('purchase_sap_company_ids');
        //获取配置的公司对应的sap公司信息
        $sap_company_ids  = SysDepartmentModel::find(['conditions' => 'id in ({ids:array})', 'bind' => ['ids' => $cost_company_id]])->toArray();
        $sap_company_ids  = array_column($sap_company_ids, 'sap_company_id', 'id') ?? [];
        $default_currency = (new EnumsService())->getSysCurrencyInfo();
        $country_code = get_country_code();//国家码
        $t = TaskService::getTranslation($country_code);

        //计算本月日期1-指定日期 只执行上个月 指定日期-31号只执行本月数据
        $start_date     = date('Y-m-01 00:00:00');
        $end_date       = date("Y-m-{$this->getSapTaskSendDate()} 00:00:00");
        $is_little_date = true;

        if (date('Y-m-d H:i:s') >= $start_date && date('Y-m-d H:i:s') < $end_date) {
            $end_time   = $start_date;
            $start_time = date('Y-m-01', strtotime(date('Y-m-01') . ' -1 month'));

        }

        $last_day   = oSapService::getInstance()->getLastDay(ltrim(date('m')), ltrim(date('Y')));
        $end_date_2 = date('Y-m-d H:i:s', (strtotime($start_date) + 3600 * 24 * $last_day));
        if (date('Y-m-d H:i:s') >= $end_date && date('Y-m-d H:i:s') < $end_date_2) {
            $start_time     = $start_date;
            $end_time       = date('Y-m-d', strtotime('+1 day'));
            $is_little_date = false;
        }

        try {
            $i = 0;
            $x = 0;
            $deal_sap_data = $this->getReserveRebackSapList($default_currency['code'], $cost_company_id, $i, $start_time, $end_time);
            while (!empty($deal_sap_data)) {
                foreach ($deal_sap_data as $key => $value) {
                    $return_data  = [];
                    // 1-指定日期不是上个月数据跳过  指定日期-31不是本月数据跳过
                    if ($is_little_date) {
                        if (ltrim(date('m', strtotime($value['approve_at']))) == ltrim(date('m'))) {
                            continue;
                        }
                    } else {
                        if (ltrim(date('m', strtotime($value['approve_at']))) != ltrim(date('m'))) {
                            continue;
                        }
                    }

                    //获取备用金归还单信息
                    $item = ReserveFundReturn::findFirst([
                        'conditions' => 'id = :id:',
                        'bind' => ['id' => $value['return_id']]
                    ]);
                    if (empty($item)) {
                        continue;
                    }

                    $request_data = [
                        'currency'              => $t->_(GlobalEnums::$currency_item[$value['currency']]),//货币
                        'cost_company_id'       => $sap_company_ids[$value['create_company_id']] ?? 'FEX01',//公司
                        'rfano'                 => $value['rfano'],//外部参考-OA的备用金申请单号
                        'create_id'             => $value['create_id'],//单据描述-员工工号
                        'create_date'           => $value['apply_date'],//申请日期
                        'back_amount'           => bcdiv($value['amount'], 1000, 2),//归还金额
                        'back_transaction_date' => $value['bank_flow_date'],//归还单银行流水日期
                        'back_no'               => $value['rrno']
                    ];
                    $z = 0;
                    $log .= 'start handle reserve_return id:' . $value['return_id'] . PHP_EOL;
                    while (empty($return_data) && $z < 1) {
                        $z++;
                        $return_data = SapsService::getInstance()->reserveBackSap($request_data, $country_code);
                        if (isset($return_data['SAP_UUID']) && !empty($return_data['SAP_UUID'])) {
                            $item->i_update(['back_sync_sap' => 1, 'back_sap_uuid' => $return_data['SAP_UUID'] ?? '', 'updated_at' => date('Y-m-d H:i:s')]);
                            $x++;
                            $log .= 'handle success reserve_return id:' . $value['return_id'] . PHP_EOL;
                        } else {
                            $note = '';
                            if (isset($return_data['log']['Item']) && !empty($return_data['log']['Item'])) {
                                $note = array_column($return_data['log']['Item'], 'Note');
                                $note = implode(',', $note);
                            }
                            $item->i_update(['back_sync_sap' => 2, 'updated_at' => date('Y-m-d H:i:s'), 'back_sap_note' => $note]);
                            $log .= 'handle fail reserve_return id:' . $value['return_id'] . PHP_EOL;
                        }
                        sleep(2);
                    }
                    $log .= PHP_EOL;
                }

                sleep(1);
                $i   += 100;
                $deal_sap_data = $this->getReserveRebackSapList($default_currency['code'], $cost_company_id, $i, $start_time, $end_time);
            }

            $log .= 'sap reserve_return 成功:' . $x . '条数' . PHP_EOL;
            $log .= 'end: ' . date('Y-m-d H:i:s') . PHP_EOL;
            $this->logger->info('reserve_back_sap_task:' . $log);
        } catch (Exception $e) {
            $this->logger->warning('reserve_back_sap_task_exception:' . $e->getMessage());
        }
        $this->clearLock(__METHOD__);
        exit($log);
    }

    /**
     * 获取备用金归还同步sap的数据
     * @param string $currency 币种
     * @param array $cost_company_ids 所属公司ID组
     * @param integer $offset 分页偏移量
     * @param string $start_time 起始时间
     * @param string $end_time 截止时间
     * @return mixed
     */
    private function getReserveRebackSapList($currency, $cost_company_ids, $offset, $start_time, $end_time)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('apply.create_id, apply.currency, apply.rfano, apply.create_company_id, main.id as return_id, main.rrno, main.apply_date, main.bank_flow_date, main.amount, main.approve_at');
        $builder->from(['main' => ReserveFundReturn::class]);
        $builder->leftjoin(ReserveFundApplyReturnRelModel::class, 'rel.return_id = main.id', 'rel');
        $builder->leftjoin(ReserveFundApply::class, 'rel.apply_id = apply.id', 'apply');
        $builder->where('rel.is_deleted = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);
        //归还单状态为“已归还”且币种=当前国家的默认币种
        $builder->andWhere('apply.return_status = :return_status: and apply.currency = :currency:', ['return_status' => ReserveFundReturnEnums::BACK_STATUS_BACK, 'currency' => $currency]);
        //费用所属公司=快递公司（即费用公司在系统配置里“传输sap的公司”）
        $builder->inWhere('apply.create_company_id', $cost_company_ids);
        //备用金归还申请单归还审批通过日期=当月
        $builder->andWhere('main.status = :status: and main.type = :type: and main.approve_at >= :date_start: and main.approve_at < :date_end:', ['status' => Enums::WF_STATE_APPROVED, 'type' => ReserveFundReturnEnums::RETURN_TYPE, 'date_start' => $start_time, 'date_end' => $end_time]);
        //未同步、同步重试
        $builder->inWhere('main.back_sync_sap', [0,3]);
        $builder->limit(100, $offset);
        return $builder->getQuery()->execute()->toArray();
    }
}
