<?php

use App\Modules\Purchase\Models\PurchaseOrder;


class FileTask extends BaseTask
{
    public function catAction(){
        $list = PurchaseOrder::find();
        $list = $list->toArray();
        foreach ($list as $k=>$v){
            if(empty($v['attachment'])){
                continue;
            }

            $temp = explode(",",$v['attachment']);
            foreach ($temp as $kk=>$vv){
                $t = explode('@',$vv);
                if(count($t)!=3){
                    echo "order id==".$v['id']."=第".$kk."个===值=".$vv.PHP_EOL;
                    continue;
                }
            }
        }


        $list = \App\Modules\Purchase\Models\PurchasePayment::find();
        $list = $list->toArray();
        foreach ($list as $k=>$v){
            if(empty($v['attachment'])){
                continue;
            }
            $temp = explode(",",$v['attachment']);
            foreach ($temp as $kk=>$vv){
                $t = explode('@',$vv);
                if(count($t)!=3){
                    echo "payment id==".$v['id']."=第".$kk."个===值=".$vv.PHP_EOL;
                    continue;
                }
            }
        }

        $list = \App\Modules\Contract\Models\Contract::find();
        $list = $list->toArray();

        foreach ($list as $k=>$v){
            if(empty($v['attachment']) && empty($v['contract_file'])){
                continue;
            }


            if(!empty($v['attachment'])){
                $temp = explode(",",$v['attachment']);
                foreach ($temp as $kk=>$vv){
                    $t = explode('@',$vv);
                    if(count($t)!=3){
                        echo "contract attachment id==".$v['id']."=第=".$kk."个===值=".$vv." attachment continue".PHP_EOL;
                        continue;
                    }
                }
            }

            if(!empty($v['contract_file'])){
                $temp = explode(",",$v['contract_file']);
                foreach ($temp as $kk=>$vv){
                    $t = explode('@',$vv);
                    if(count($t)!=3){
                        echo "contract file id==".$v['id']."=第".$kk."个===值=".$vv." file continue".PHP_EOL;
                    }
                }
            }
        }


        $list = \App\Modules\Contract\Models\ContractArchive::find();
        $list = $list->toArray();

        foreach ($list as $k=>$v){
            if(empty($v['contract_file'])){
                continue;
            }
            if(!empty($v['contract_file'])){
                $temp = explode(",",$v['contract_file']);
                foreach ($temp as $kk=>$vv){
                    $t = explode('@',$vv);
                    if(count($t)!=3){
                        echo "contract archive id==".$v['id']."=第".$kk."===值=".$vv." file continue".PHP_EOL;
                        continue;
                    }
                }
            }
        }
    }



    public function flush_orderAction(){
        $list = PurchaseOrder::find();
        $list = $list->toArray();

        $attachments = [];

        foreach ($list as $k=>$v){
            if(empty($v['attachment'])){
                continue;
            }

            $temp = explode(",",$v['attachment']);
            foreach ($temp as $kk=>$vv){
                $t = explode('@',$vv);
                if(count($t)!=3){
                    echo "order id==".$v['id']."=$kk==".$vv." continue".PHP_EOL;
                    continue;
                }

                $t_data = [];
                $t_data['oss_bucket_type'] = \App\Library\Enums::OSS_BUCKET_TYPE_PURCHASE_ORDER;
                $t_data['oss_bucket_key'] = $v['id'];
                $t_data['bucket_name'] = $t[0];
                $t_data['object_key'] = $t[2];
                $t_data['file_name'] = $t[1];
                $attachments[] = $t_data;
            }
        }

        $attach = new \App\Modules\User\Models\AttachModel();
        $flag = $attach->batch_insert($attachments);
        if($flag===false){
            $message = $attach->getMessages();
            echo "刷数据失败==".explode(",",$message)."\n";
        }else{
            echo "成功===".count($attachments);
        }
    }

    public function flush_paymentAction(){
        $list = \App\Modules\Purchase\Models\PurchasePayment::find();
        $list = $list->toArray();

        $attachments = [];

        foreach ($list as $k=>$v){
            if(empty($v['attachment'])){
                continue;
            }

            $temp = explode(",",$v['attachment']);
            foreach ($temp as $kk=>$vv){
                $t = explode('@',$vv);
                if(count($t)!=3){
                    echo "payment id==".$v['id']."=$kk==".$vv." continue".PHP_EOL;
                    continue;
                }

                $t_data = [];
                $t_data['oss_bucket_type'] = \App\Library\Enums::OSS_BUCKET_TYPE_PURCHASE_PAYMENT;
                $t_data['oss_bucket_key'] = $v['id'];
                $t_data['bucket_name'] = $t[0];
                $t_data['object_key'] = $t[2];
                $t_data['file_name'] = $t[1];
                $attachments[] = $t_data;
            }
        }

        $attach = new \App\Modules\User\Models\AttachModel();
        $flag = $attach->batch_insert($attachments);
        if($flag===false){
            $message = $attach->getMessages();
            echo "刷数据失败==".explode(",",$message)."\n";
        }else{
            echo "成功===".count($attachments);
        }
    }

    public function flush_contractAction(){
        $list = \App\Modules\Contract\Models\Contract::find();
        $list = $list->toArray();

        $attachments = [];

        foreach ($list as $k=>$v){
            if(empty($v['attachment']) && empty($v['contract_file'])){
                continue;
            }


            if(!empty($v['attachment'])){
                $temp = explode(",",$v['attachment']);
                foreach ($temp as $kk=>$vv){
                    $t = explode('@',$vv);
                    if(count($t)!=3){
                        echo "contract id==".$v['id']."=$kk==".$vv." attachment continue".PHP_EOL;
                        continue;
                    }

                    $t_data = [];
                    $t_data['oss_bucket_type'] = \App\Library\Enums::OSS_BUCKET_TYPE_CONTRACT_ATTACHMENT;
                    $t_data['oss_bucket_key'] = $v['id'];
                    $t_data['bucket_name'] = $t[1];
                    $t_data['object_key'] = $t[2];
                    $t_data['file_name'] = $t[0];
                    $attachments[] = $t_data;
                }
            }

            if(!empty($v['contract_file'])){
                $temp = explode(",",$v['contract_file']);
                foreach ($temp as $kk=>$vv){
                    $t = explode('@',$vv);
                    if(count($t)!=3){
                        echo "id==".$v['id']."=$kk==".$vv." file continue".PHP_EOL;
                        continue;
                    }

                    $t_data = [];
                    $t_data['oss_bucket_type'] = \App\Library\Enums::OSS_BUCKET_TYPE_CONTRACT_FILE;
                    $t_data['oss_bucket_key'] = $v['id'];
                    $t_data['bucket_name'] = $t[1];
                    $t_data['object_key'] = $t[2];
                    $t_data['file_name'] = $t[0];
                    $attachments[] = $t_data;
                }
            }
        }

        $attach = new \App\Modules\User\Models\AttachModel();
        $flag = $attach->batch_insert($attachments);
        if($flag===false){
            $message = $attach->getMessages();
            echo "刷数据失败==".explode(",",$message)."\n";
        }else{
            echo "成功===".count($attachments);
        }
    }


    public function flush_contract_archiveAction(){
        $list = \App\Modules\Contract\Models\ContractArchive::find();
        $list = $list->toArray();

        $attachments = [];

        foreach ($list as $k=>$v){
            if(empty($v['contract_file'])){
                continue;
            }

            if(!empty($v['contract_file'])){
                $temp = explode(",",$v['contract_file']);
                foreach ($temp as $kk=>$vv){
                    $t = explode('@',$vv);
                    if(count($t)!=3){
                        echo "contract_archive id==".$v['id']."=$kk==".$vv." file continue".PHP_EOL;
                        continue;
                    }

                    $t_data = [];
                    $t_data['oss_bucket_type'] = \App\Library\Enums::OSS_BUCKET_TYPE_ARCHIVE_CONTRACT_FILE;
                    $t_data['oss_bucket_key'] = $v['id'];
                    $t_data['bucket_name'] = $t[1];
                    $t_data['object_key'] = $t[2];
                    $t_data['file_name'] = $t[0];
                    $attachments[] = $t_data;
                }
            }
        }

        $attach = new \App\Modules\User\Models\AttachModel();
        $flag = $attach->batch_insert($attachments);
        if($flag===false){
            $message = $attach->getMessages();
            echo "刷数据失败==".explode(",",$message)."\n";
        }else{
            echo "成功===".count($attachments);
        }
    }




}