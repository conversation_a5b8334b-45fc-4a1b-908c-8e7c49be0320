<?php
use App\Library\Enums;
use App\Library\Enums\LoanEnums;
use App\Modules\Loan\Models\Loan;
use App\Modules\Reimbursement\Models\Reimbursement;
use App\Modules\Reimbursement\Models\ReimbursementRelLoan;

class PaymentEventTask extends BaseTask
{
    /**
     * 银行流水关联报销单-借款归还状态处理
     * @param $params
     * @date 2022/9/7
     */
    public function after_payment_reimbursementAction($params)
    {
        // 指定时间
        if (isset($params[0]) && isset($params[1])) {
            $start_date = $params[0];
            $end_date = $params[1];
        } else {
            // 取前1天数据
            $start_date = date('Y-m-d H:i:s', time() - 86400);
            $end_date = date('Y-m-d H:i:s', time());
        }
        //查询当天所有银行流水处理的支付
        $data = Reimbursement::find([
            'conditions' => 'status=:status: and pay_status = :pay_status: and pay_from = :pay_from: and pay_operate_date>=:start_date: and pay_operate_date<=:end_date:',
            'bind' => [
                'status' => Enums::CONTRACT_STATUS_APPROVAL,
                'pay_status' => Enums::LOAN_PAY_STATUS_PAY,
                'pay_from' => 2,
                'start_date' => $start_date,
                'end_date' => $end_date
            ]
        ])->toArray();
        if (empty($data)) {
            echo '借款单处理完毕,共0条' . PHP_EOL;
            return;
        }
        //1.查所有关联的借款
        $re_ids = array_column($data, 'id');
        $rel_loan_data = ReimbursementRelLoan::find([
            'conditions' => 're_id in ({re_ids:array}) and is_deleted = :is_deleted:',
            'bind' => ['re_ids' => $re_ids, 'is_deleted' => Enums\GlobalEnums::IS_NO_DELETED]
        ])->toArray();
        if (empty($rel_loan_data)) {
            echo '借款单处理完毕,共0条' . PHP_EOL;
            return;
        }
        $loan_ids = array_column($rel_loan_data, 'loan_id');
        $loan_data = Loan::find([
            'conditions' => 'id in ({loan_ids:array})',
            'bind' => ['loan_ids' => $loan_ids]
        ])->toArray();
        //2.查借款关联的报销单有没有未支付的
        $rel_re_data = ReimbursementRelLoan::find([
            'conditions' => 'loan_id in ({loan_ids:array}) and is_deleted = :is_deleted:',
            'bind' => ['loan_ids' => $loan_ids, 'is_deleted' => Enums\GlobalEnums::IS_NO_DELETED]
        ])->toArray();
        //构造 array[借款id]=>[报销id,报销id]
        $loan_re_kv = [];
        foreach ($rel_re_data as $k => $v) {
            $loan_re_kv[$v['loan_id']][] = $v['re_id'];
        }
        //$all_re_ids = array_column($rel_re_data,'re_id');
        //$diff_re_ids = array_diff($re_ids,$all_re_ids);
        ////构造 array[借款id] => 是否存在未支付的报销单
        //$is_exist_kv = [];
        //if (!empty($diff_re_ids)){
        //    $diff_data = Reimbursement::find([
        //        'conditions' => 'id in ({ids:array}) and status in({status:array}) and pay_status = :pay_status:',
        //        'bind' => ['ids' => array_values($diff_re_ids),'status' => [1,3],'pay_status'=>1]
        //    ])->toArray();
        //    foreach ($loan_re_kv as $loan_kv_k=>$loan_kv_v){
        //        foreach ($diff_data as $re_k=>$re_v){
        //            if (in_array($re_v['id'],$loan_kv_v)){
        //                $is_exist_kv[$loan_kv_k] = 1;
        //                break;
        //            }
        //        }
        //    }
        //}
        //3.循环处理每个借款的状态
        $db = $this->getDI()->get('db_oa');
        $loan_model = new Loan();
        $result = [
            'all' => count($loan_data),
            'success' => 0,
            'error' => 0,
        ];
        foreach ($loan_data as $key => $value) {
            if (!isset($loan_re_kv[$value['id']])) {
                continue;
            }
            //单个借款单被报销抵扣的总金额
            $total = Reimbursement::findFirst([
                'conditions' => 'id in({ids:array}) and status=3 and pay_status=2',
                'columns' => 'sum(loan_amount) total_loan_amount',
                'bind' => ['ids' => array_values($loan_re_kv[$value['id']])]
            ]);
            $re_amount = (!empty($total) ? $total->total_loan_amount : 0);
            if (empty($value['re_amount'])) {
                $value['re_amount'] = $value['amount'];   //兼容老数据  原报销金额 是在支付时更新到 loan 表  现在是在提交时更新
            }
            //由于现在又改回了支付时变更借款的借款单状态，所以脚本逻辑也要调整
            $paid_return_amount = $re_amount + $value['back_amount'];
            $loan_status = $value['loan_status'];
            if ($paid_return_amount == 0) {
                //未开始归还：已归还金额=0
                $loan_status = LoanEnums::LOAN_STATUS_NOT_START_RETURN;
            } elseif ($paid_return_amount < $value['amount']) {
                //部分归还：已归还金额<借款金额
                $loan_status = LoanEnums::LOAN_STATUS_PARTIAL_RETURN;
            } else if ($paid_return_amount == $value['amount']) {
                //已还清：已归还总金额=借款金额
                $loan_status = LoanEnums::LOAN_STATUS_PAID_OFF;
            } else if ($paid_return_amount > $value['amount']) {
                //超额归还：已归还总金额＞借款金额
                $loan_status = LoanEnums::LOAN_STATUS_OVER_RETURN;
            }
            if ((string)$loan_status != $value['loan_status']) {
                $success = $db->updateAsDict(
                    $loan_model->getSource(),
                    ['loan_status' => $loan_status],
                    ['conditions' => 'id=?', 'bind' => $value['id']]
                );
                if ($success) {
                    $result['success'] += 1;
                } else {
                    $result['error'] += 1;
                }
            }
        }
        $this->logger->info('借款单处理完毕,共' . $result['all'] . '条,成功' . $result['success'] . '条,失败' . $result['error'] . '条; 时间范围: 开始时间=' . $start_date . ',结束时间=' . $end_date);
        echo '借款单处理完毕,共' . $result['all'] . '条,成功' . $result['success'] . '条,失败' . $result['error'] . '条' . PHP_EOL;
    }
}