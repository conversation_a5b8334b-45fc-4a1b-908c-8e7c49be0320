<?php
use App\Modules\Kpi\Services\ActivityService;
use App\Models\backyard\KpiActivityStaffModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\KpiStaffChangeLeaderLogModel;
use App\Library\Enums\StaffInfoEnums;
use App\Library\Enums\KpiActivityStaffEnums;

/**
 * KPI目标管理相关任务
 * Class KpiTask
 */
class KpiTask extends BaseTask
{
    /**
     * 在oa做过目标制定人变更操作的目标制定人需要监听在职状态
     * 回退到直线上级并记录变更记录
     */
    public function change_leaderAction()
    {
        // 同期运行的脚本进程只能有一个
        $process_name = str_replace('Action', '', __FUNCTION__);
        $this->check_process($process_name);
        try {
            echo date('Ymd H:i:s') . " 变更的目标制定人若离职做相应处理脚本开始执行".PHP_EOL;
            //第一步找到所有进行中的KPI活动
            $ing_activity_list = ActivityService::getInstance()->getIngActivityList();
            if (!empty($ing_activity_list)) {
                //有进行中的KPI活动
                $activity_ids = array_column($ing_activity_list, "id");
                echo date('Ymd H:i:s') . ' 进行中的活动id组为[ ' . implode(",", $activity_ids) . ' ]'. PHP_EOL;

                //查找变更的目标制定人中离职的目标制定人所关联的员工列表
                $i = 1;
                $deal_data = $this->getStateStopList($activity_ids, $i);
                while (!empty($deal_data)) {
                    echo date('Ymd H:i:s') . ' 第'.$i.'批处理开始' . PHP_EOL;

                    //第二步获取到变更的指定人已离职的员工找寻他们的直线上级
                    $staff_ids = array_values(array_unique(array_column($deal_data, 'staff_id')));
                    $staff_list = HrStaffInfoModel::find([
                        'columns' => 'staff_info_id, manger',
                       'conditions' => 'staff_info_id in ({staff_ids:array})',
                       'bind' => ['staff_ids' => $staff_ids],
                    ])->toArray();

                    //第三步开始获取满足条件的员工所属的直线上级信息列表
                    $manger_ids = array_values(array_unique(array_filter(array_column($staff_list, 'manger'))));
                    $manger_list = HrStaffInfoModel::find([
                        'columns' => 'staff_info_id, name',
                        'conditions' => 'staff_info_id in ({manger_ids:array})',
                        'bind' => ['manger_ids' => $manger_ids],
                    ])->toArray();

                    //直线上级信息组
                    $manger_list = array_column($manger_list, null, 'staff_info_id');
                    //满足条件的员工信息组
                    $staff_list = array_column($staff_list, null, 'staff_info_id');

                    //第四步组装变更记录组
                    $change_log_arr = [];
                    foreach ($deal_data as $item) {
                        $one_manger_id = !empty($staff_list[$item['staff_id']]) ? $staff_list[$item['staff_id']]['manger'] : 0;
                        $change_log_arr[] = [
                            'activity_id' => $item['activity_id'],
                            'staff_id' => $item['staff_id'],
                            'from_leader_id' => $item['leader_id'],
                            'from_leader_name' => $item['leader_name'],
                            'to_leader_id' => !empty($manger_list[$one_manger_id]) ? $manger_list[$one_manger_id]['staff_info_id'] : 0,
                            'to_leader_name' => !empty($manger_list[$one_manger_id]) ? $manger_list[$one_manger_id]['name'] : '',
                            'create_id' => '10000',
                            'create_name' => 'SuperAdmin',
                            'created_at' => date('Y-m-d H:i:s'),
                            'updated_at' => date('Y-m-d H:i:s')
                        ];
                    }

                    //第五步操作数据库
                    $this->changeLeaderDb($deal_data, $change_log_arr, $i);
                    echo date('Ymd H:i:s') . ' 第'.$i.'批处理结束' . PHP_EOL;

                    sleep(5);

                    // 取下一批数据
                    $i += 1;
                    $deal_data = $this->getStateStopList($activity_ids);
                }
                if (empty($deal_data)) {
                    echo '数据均已处理完毕' . PHP_EOL;
                }
            } else {
                //没有进行中的KPI活动
                echo date('Ymd H:i:s') . ' 暂无进行中的KPI活动'. PHP_EOL;
            }
        } catch (\Exception $e) {
            $this->logger->warning('change_leader-变更的目标制定人若离职做相应处理脚本异常' . $e->getMessage());
        }
        echo date('Ymd H:i:s') . " 变更的目标制定人若离职做相应处理脚本结束执行". PHP_EOL;
    }

    /**
     * 变更目标制定人脚本-获取离职的员工与目标制定人关系列表
     * @param array $activity_ids 活动ID组
     * @return mixed
     */
    private function getStateStopList(array $activity_ids)
    {
        $page_size = 2000;
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['kas' => KpiActivityStaffModel::class]);
        $builder->leftjoin(HrStaffInfoModel::class, 'staff.staff_info_id = kas.leader_id', 'staff');
        $builder->columns(['kas.id', 'kas.activity_id', 'kas.staff_id', 'kas.leader_id', 'kas.leader_name']);
        $builder->where('kas.leader_id != :leader_id:', ['leader_id' => 0]);
        $builder->andWhere('staff.state = :state:', ['state' => StaffInfoEnums::STAFF_STATE_LEAVE]);
        $builder->inWhere('kas.activity_id', $activity_ids);
        $builder->limit($page_size);
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 变更目标制定人脚本-操作数据库
     * @param array $deal_data 需要处理的参数组
     * @param array $change_log_arr 变更记录
     * @param int $i 批次
     * @throws Exception
     */
    private function changeLeaderDb($deal_data, $change_log_arr, $i)
    {
        $db = $this->getDI()->get('db_backyard');
        try {
            $db->begin();

            //KPI目标活动参与员工表id组
            $deal_activity_staff_ids_str = implode(',', array_column($deal_data, 'id'));

            //回退到直线上级
            $bool = $db->updateAsDict(
                (new KpiActivityStaffModel())->getSource(),
                ['leader_id' => 0, 'leader_name' => '', 'is_change_leader' => KpiActivityStaffEnums::IS_CHANGE_LEADER_YES, 'updated_at' => date('Y-m-d H:i:s')],
                ['conditions' => "id IN ($deal_activity_staff_ids_str)"]
            );
            if ($bool === false) {
                throw new Exception('第'. $i .'批，涉及到KPI目标活动参与员工表id组：['. $deal_activity_staff_ids_str .'];回退到直线上级失败' . ';可能存在的原因是：' . get_data_object_error_msg($db));
            }

            //批量插入变更记录
            $change_log = new KpiStaffChangeLeaderLogModel();
            $bool = $change_log->batch_insert($change_log_arr, 'db_backyard');
            if ($bool === false) {
                throw new Exception('第'. $i .'批，涉及到KPI目标活动参与员工表id组['. $deal_activity_staff_ids_str .'], 插入变更记录组['. json_encode($change_log_arr, JSON_UNESCAPED_UNICODE) .'];保存变更记录失败' . ';可能存在的原因是：' . get_data_object_error_msg($change_log));
            }
            echo date('Ymd H:i:s') . ' 第'. $i .'批，涉及到KPI目标活动参与员工表id组['. $deal_activity_staff_ids_str .'], 插入变更记录组['. json_encode($change_log_arr, JSON_UNESCAPED_UNICODE) .']' . PHP_EOL;
            $db->commit();
        } catch (Exception $e) {
            $db->rollback();
            $this->logger->info($e->getMessage());
        }
    }
}