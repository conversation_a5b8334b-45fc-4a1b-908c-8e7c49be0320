<?php

/**
 * Created by PhpStorm.
 * Date: 2025/02/11
 * Time:
 */

use App\Library\Enums;
use App\Library\Enums\ChequeEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\PayEnums;
use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Library\Exception\BusinessException;
use App\Models\oa\ChequeAccountModel;
use App\Models\oa\LoanModel;
use App\Models\oa\LoanReturnModel;
use App\Models\oa\OrdinaryPaymentExtendModel;
use App\Models\oa\OrdinaryPaymentModel;
use App\Models\oa\PaymentCheckModel;
use App\Models\oa\PaymentModel;
use App\Models\oa\PaymentStoreRentingModel;
use App\Models\oa\PurchasePaymentModel;
use App\Models\oa\ReimbursementModel;
use App\Models\oa\SmsPendingDataModel;
use App\Models\oa\StaffManageListRelateModel;
use App\Models\oa\WorkflowRequestNodePushMsg;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Payment\Models\PaymentStoreRentingDetail;
use App\Modules\Payment\Models\PaymentStoreRentingPay;
use App\Modules\Purchase\Models\PurchaseOrder;
use App\Modules\Purchase\Models\PurchaseOrderProduct;
use App\Modules\ReserveFund\Models\ReserveFundApply;
use App\Modules\SapManage\Services\BaseService as SapBaseService;

class ToolTask extends BaseTask
{
    // 飞书: OA业务监测提醒群消息key
    const FEISHU_OA_BIZ_WARNING_GROUP_MSG_KEY = '0a1b4a48-a527-48f9-b12a-665b1489a25a';

    // 付款模块标识
    const SYS_REIMBURSEMENT_MODULE = 'BX';
    const SYS_LOAN_MODULE = 'JK';
    const SYS_LOAN_BACK_MODULE = 'JKGH';
    const SYS_PURCHASE_PAYMENT_MODULE = 'CGFK';
    const SYS_PAYMENT_STORE_RENTING_MODULE = 'FK';
    const SYS_ORDINARY_PAYMENT_MODULE = 'PTFK';
    const SYS_VENDOR_MODULE = 'GYS';
    const SYS_RESERVE_FUND_APPLY_MODULE = 'WDBYJ';


    // 工具集
    const FEISHU_OA_BIZ_WARNING_SCENE_PAYSTATUS_ROLLBACK = '单据支付状态回滚[已支付->待支付]';
    const FEISHU_OA_BIZ_WARNING_SCENE_SAP_STATUS_AMEND = '单据SAP抛数状态修正[失败->成功]';
    const FEISHU_OA_BIZ_WARNING_SCENE_OPEN_PO_IS_CAN_UPDATE = '设置采购订单状态[is_can_update: 0->1]';
    const FEISHU_OA_BIZ_WARNING_SCENE_CLOSE_PO_IS_CAN_UPDATE = '设置采购订单状态[is_can_update: 1->0]';

    /**
     * 回滚付款模块付款单据的支付状态（仅支持 已支付 -> 待支付）
     *
     * php app/cli.php tool rollback_paystatus
     *
     */
    public function rollback_paystatusAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log          = '任务名称: ' . $process_name . PHP_EOL;
        $log          .= '开始时间: ' . get_datetime_with_milliseconds() . PHP_EOL;

        $logger_type = 'info';

        $exec_result = '失败';

        $country_code = get_country_code();

        $msg_content_tpl = '<at user_id="all">所有人</at>' . PHP_EOL;
        $msg_content_tpl .= '场景: ' . self::FEISHU_OA_BIZ_WARNING_SCENE_PAYSTATUS_ROLLBACK . PHP_EOL;
        $msg_content_tpl .= '国家: ' . $country_code . PHP_EOL;
        $msg_content_tpl .= '环境: ' . RUNTIME . PHP_EOL;
        $msg_content_tpl .= '单据编号: order_no' . PHP_EOL;
        $msg_content_tpl .= '执行结果: exec_result' . PHP_EOL;
        $msg_content_tpl .= '执行日志: exec_log' . PHP_EOL;

        $replace_search_key = ['order_no', 'exec_result', 'exec_log'];

        $db = $this->getDI()->get('db_oa');
        $db->begin();

        try {
            $support_module = [
                self::SYS_REIMBURSEMENT_MODULE,
                self::SYS_PURCHASE_PAYMENT_MODULE,
                self::SYS_ORDINARY_PAYMENT_MODULE,
                self::SYS_PAYMENT_STORE_RENTING_MODULE,

                // 暂不开放
                // self::SYS_LOAN_MODULE,
            ];

            // 获取待回滚单据的配置
            $order_data = EnumsService::getInstance()->getSettingEnvValueMap('sys_tool_rollback_payment_order_paystatus');

            $log .= '配置内容: 模块-' . ($order_data['biz_module'] ?? '') . '; 单据号-' . ($order_data['order_no'] ?? '') . PHP_EOL;
            if (empty($order_data['biz_module']) || empty($order_data['order_no'])) {
                throw new ValidationException('sys_tool_rollback_payment_order_paystatus 配置有误, 请检查',
                    ErrCode::$VALIDATE_ERROR);
            }

            $order_data['biz_module'] = strtoupper($order_data['biz_module']);
            if (!in_array($order_data['biz_module'], $support_module)) {
                throw new ValidationException("{$order_data['biz_module']} 该模块单据状态回滚, 暂不支持, 请产研确认",
                    ErrCode::$VALIDATE_ERROR);
            }

            // 获取业务侧单据
            $pay_info_model = null;
            switch ($order_data['biz_module']) {
                case self::SYS_REIMBURSEMENT_MODULE:
                    $order_model = ReimbursementModel::findFirst([
                        'conditions' => 'no = :order_no:',
                        'bind'       => ['order_no' => $order_data['order_no']],
                        'for_update' => true,
                    ]);

                    break;
                case self::SYS_LOAN_MODULE:
                    $order_model = LoanModel::findFirst([
                        'conditions' => 'lno = :order_no:',
                        'bind'       => ['order_no' => $order_data['order_no']],
                        'for_update' => true,
                    ]);

                    // 有支付信息副表 loan_pay 已付或未付 插入数据


                    break;
                case self::SYS_PURCHASE_PAYMENT_MODULE:
                    $order_model = PurchasePaymentModel::findFirst([
                        'conditions' => 'ppno = :order_no:',
                        'bind'       => ['order_no' => $order_data['order_no']],
                        'for_update' => true,
                    ]);

                    break;
                case self::SYS_PAYMENT_STORE_RENTING_MODULE:
                    $order_model = PaymentStoreRentingModel::findFirst([
                        'conditions' => 'apply_no = :order_no:',
                        'bind'       => ['order_no' => $order_data['order_no']],
                        'for_update' => true,
                    ]);

                    // 有支付信息副表 payment_store_renting_pay 已付 或 未付 插入数据
                    // 是否需要管理行的支付状态 payment_store_renting_detail . pay_status
                    // 来自马来支付模块的付款操作 或 自身模块的支付操作 或 ph支付模块的非支票的支付, 同时会把 维护明细行的支付状态 = 单据的支付状态

                    // 回滚流程
                    // 1. 业务侧: 明细行的支付状态 与 单据状态 保持一致，已支付 -> 待支付；支付信息表删除
                    // 2. 若对接了支付模块，则
                    // 支付模块: 支付单据由已支付 回滚为 银行支付中；若支付方式是支票支付 且 国家为ph，则需要将关联的支票，将其在支票台账中的释放状态 由 已释放 -> 待释放，清空释放时间

                    // 付款明细表
                    $order_detail_models = PaymentStoreRentingDetail::find([
                        'conditions' => 'store_renting_id = :main_id:',
                        'bind'       => ['main_id' => $order_model->id ?? 0],
                        'for_update' => true,
                    ]);
                    $order_detail_array  = $order_detail_models->toArray();
                    if (empty($order_detail_array)) {
                        throw new ValidationException('关联的明细表[payment_store_renting_detail]数据不存在, 请核实',
                            ErrCode::$VALIDATE_ERROR);
                    }

                    $detail_log = [];
                    foreach ($order_detail_models as $detail_model) {
                        $detail_update_data = [
                            'pay_status' => Enums::PAYMENT_PAY_STATUS_PENDING,
                        ];

                        $detail_log[] = "{$detail_model->id} 修改前支付状态-{$detail_model->pay_status}, 修改后支付状态-{$detail_update_data['pay_status']}";
                        if ($detail_model->i_update($detail_update_data) === false) {
                            throw new BusinessException('业务侧明细表[payment_store_renting_detail]支付状态重置失败, 原因: ' . get_data_object_error_msg($detail_model),
                                ErrCode::$BUSINESS_ERROR);
                        }
                    }

                    $log .= '业务模块的明细表变动: ' . implode('; ', $detail_log) . PHP_EOL;

                    // 支付信息关联表
                    $pay_info_model = PaymentStoreRentingPay::findFirst([
                        'conditions' => 'store_renting_id = :main_id:',
                        'bind'       => ['main_id' => $order_model->id ?? 0],
                        'for_update' => true,
                    ]);

                    if (empty($pay_info_model)) {
                        throw new ValidationException('关联的支付信息表[payment_store_renting_pay]数据不存在, 请核实',
                            ErrCode::$VALIDATE_ERROR);
                    }

                    $log .= "业务模块的支付信息表: 修改前支付状态-{$pay_info_model->is_pay}" . PHP_EOL;
                    if ($pay_info_model->is_pay != Enums::PAYMENT_PAY_STATUS_PAY) {
                        throw new ValidationException('关联的支付信息表[payment_store_renting_pay]支付状态 不是 已支付, 请核实',
                            ErrCode::$VALIDATE_ERROR);
                    }

                    if ($pay_info_model->delete() === false) {
                        throw new BusinessException('关联的支付信息表[payment_store_renting_pay] 删除失败, 原因可能是 ' . get_data_object_error_msg($pay_info_model),
                            ErrCode::$BUSINESS_ERROR);
                    }

                    $log .= '业务模块的支付信息表: 删除成功 [新的支付操作, 会重新写入该表], 备份数据=' . json_encode($pay_info_model->toArray(),
                            JSON_UNESCAPED_UNICODE) . '' . PHP_EOL;

                    break;
                case self::SYS_ORDINARY_PAYMENT_MODULE:
                    $order_model = OrdinaryPaymentModel::findFirst([
                        'conditions' => 'apply_no = :order_no:',
                        'bind'       => ['order_no' => $order_data['order_no']],
                        'for_update' => true,
                    ]);

                    // 有支付信息副表 ordinary_payment_extend 无数据 插入 / 有数据更新, 仅回滚支付状态
                    $pay_info_model = OrdinaryPaymentExtendModel::findFirst([
                        'conditions' => 'ordinary_payment_id = :main_id:',
                        'bind'       => ['main_id' => $order_model->id ?? 0],
                        'for_update' => true,
                    ]);

                    if (empty($pay_info_model)) {
                        throw new ValidationException('关联的支付信息表[ordinary_payment_extend]数据不存在, 请核实',
                            ErrCode::$VALIDATE_ERROR);
                    }

                    $log .= "业务模块的支付信息表: 修改前支付状态-{$pay_info_model->is_pay}" . PHP_EOL;

                    if ($pay_info_model->is_pay != Enums::PAYMENT_PAY_STATUS_PAY) {
                        throw new ValidationException('关联的支付信息表[ordinary_payment_extend]支付状态 不是 已支付, 请核实',
                            ErrCode::$VALIDATE_ERROR);
                    }

                    // 更新业务侧付款表支付状态
                    $pay_info_update_data = [
                        'is_pay' => 0,
                    ];
                    if ($pay_info_model->i_update($pay_info_update_data) === false) {
                        throw new BusinessException('业务侧支付信息表支付状态重置失败, 原因: ' . get_data_object_error_msg($pay_info_model),
                            ErrCode::$BUSINESS_ERROR);
                    }

                    $log .= "业务模块的支付信息表: 修改后支付状态-{$pay_info_model->is_pay}, [默认值, 新的支付操作, 会更新该字段]" . PHP_EOL;

                    break;
            }

            if (empty($order_model)) {
                throw new ValidationException('业务表不存在该单据, 请核实', ErrCode::$VALIDATE_ERROR);
            }

            $log .= "业务模块: 修改前支付状态-{$order_model->pay_status}; 是否进入支付模块-{$order_model->is_pay_module}" . PHP_EOL;
            if ($order_model->pay_status != Enums::PAYMENT_PAY_STATUS_PAY) {
                throw new ValidationException('业务表支付状态 不是已支付, 请核实', ErrCode::$VALIDATE_ERROR);
            }

            // 是否对接了支付模块
            if ($order_model->is_pay_module) {
                $payment_model = PaymentModel::findFirst([
                    'conditions' => 'no = :order_no:',
                    'bind'       => ['order_no' => $order_data['order_no']],
                    'for_update' => true,
                ]);

                if (empty($payment_model)) {
                    throw new ValidationException('支付模块表不存在该单据, 请核实', ErrCode::$VALIDATE_ERROR);
                }

                $log .= "支付模块: 支付方式-{$payment_model->pay_method}, 修改前支付状态-{$payment_model->pay_status}" . PHP_EOL;

                if ($payment_model->pay_status != PayEnums::PAYMENT_MODULE_PAY_STATUS_PAY) {
                    throw new ValidationException('支付模块表支付状态 不是已支付, 请核实', ErrCode::$VALIDATE_ERROR);
                }

                // 更新支付模块侧支付状态
                $payment_update_data = [
                    'pay_status' => PayEnums::PAYMENT_MODULE_PAY_STATUS_BANKING,
                ];

                if ($payment_model->i_update($payment_update_data) === false) {
                    throw new BusinessException('支付模块表支付状态更新失败, 原因: ' . get_data_object_error_msg($payment_model),
                        ErrCode::$BUSINESS_ERROR);
                }

                $log .= "支付模块: 修改后支付状态-{$payment_model->pay_status}" . PHP_EOL;

                // 若是租房付款模块 且 支付方式是支票支付 且 国家为ph, 则需同步回滚支票 承兑日期和承兑状态
                if ($order_data['biz_module'] == self::SYS_PAYMENT_STORE_RENTING_MODULE && $country_code == GlobalEnums::PH_COUNTRY_CODE && $payment_model->pay_method == GlobalEnums::PAYMENT_METHOD_CHECK) {
                    // 获取待回滚的支票
                    $payment_check_item = PaymentCheckModel::find([
                        'conditions' => 'payment_id = :payment_id:',
                        'bind'       => ['payment_id' => $payment_model->id],
                        'columns'    => ['ticket_no'],
                    ])->toArray();
                    $payment_check_item = array_values(array_unique(array_column($payment_check_item, 'ticket_no')));

                    $log .= '支付模块: 待回滚的支票号-' . implode(',', $payment_check_item) . PHP_EOL;
                    if (empty($payment_check_item)) {
                        throw new ValidationException('支付模块支付单据与支票关系数据为空, 请核实', ErrCode::$VALIDATE_ERROR);
                    }

                    // 回滚支票
                    $cheque_account_models = ChequeAccountModel::find([
                        'conditions' => 'cheque_code IN ({cheque_code:array})',
                        'bind'       => ['cheque_code' => $payment_check_item],
                        'for_update' => true,
                    ]);
                    if (empty($cheque_account_models->toArray())) {
                        throw new ValidationException('支票台账数据为空, 请核实', ErrCode::$VALIDATE_ERROR);
                    }

                    // 支票台账中的释放状态 由 已释放 -> 待释放，清空释放时间
                    $cheque_account_log = [];
                    foreach ($cheque_account_models as $cheque_model) {
                        $cheque_update = [
                            'release_status' => ChequeEnums::CHEQUE_ACCOUNT_RELEASE_PENDING,
                            'release_at'     => null,
                        ];

                        $cheque_account_log[] = "{$cheque_model->cheque_code}, release_status修改前-{$cheque_model->release_status}, 修改后-{$cheque_update['release_status']}; release_at修改前-{$cheque_model->release_at}, 修改后-null";
                        if ($cheque_model->i_update($cheque_update) === false) {
                            throw new BusinessException("支票台账释放失败, 请核实[{$cheque_model->cheque_code}]",
                                ErrCode::$BUSINESS_ERROR);
                        }
                    }

                    $log .= '支票台账表变动: ' . implode(',', $cheque_account_log) . PHP_EOL;
                }
            }

            // 更新业务侧支付状态
            // 更新支付模块侧支付状态
            $order_update_data = [
                'pay_status' => Enums::PAYMENT_PAY_STATUS_PENDING,
            ];

            if ($order_model->i_update($order_update_data) === false) {
                throw new BusinessException('业务表支付状态更新失败, 原因: ' . get_data_object_error_msg($order_model),
                    ErrCode::$BUSINESS_ERROR);
            }

            $log .= "业务模块: 修改后支付状态-{$order_model->pay_status}" . PHP_EOL;
            $log .= '回滚成功' . PHP_EOL;

            $db->commit();

            $order_no    = $order_data['order_no'];
            $exec_result = '成功';
        } catch (ValidationException $e) {
            $db->rollback();

            $log .= $e->getMessage() . PHP_EOL;
        } catch (BusinessException $e) {
            $db->rollback();

            $log         .= $e->getMessage() . PHP_EOL;
            $logger_type = 'notice';
        } catch (Exception $e) {
            $db->rollback();

            $log         .= $e->getMessage() . PHP_EOL;
            $logger_type = 'error';
        }

        $log .= '结束时间: ' . get_datetime_with_milliseconds() . PHP_EOL;

        $replace_values = [
            'order_no'    => $order_no ?? '',
            'exec_result' => $exec_result,
            'exec_log'    => str_replace(PHP_EOL, '; ', $log),
        ];
        $msg_content    = str_replace($replace_search_key, $replace_values, $msg_content_tpl);

        if (get_runtime_env() == 'pro') {
            send_feishu_text_msg($msg_content, self::FEISHU_OA_BIZ_WARNING_GROUP_MSG_KEY);
        }

        $this->logger->$logger_type($log);
        exit($log);
    }

    /**
     * 修正单据sap抛数状态（仅支持 失败 -> 成功）
     *
     * php app/cli.php tool amend_sapstatus
     *
     */
    public function amend_sapstatusAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log          = '任务名称: ' . $process_name . PHP_EOL;
        $log          .= '开始时间: ' . get_datetime_with_milliseconds() . PHP_EOL;

        $logger_type = 'info';

        $msg_content_tpl = '<at user_id="all">所有人</at>' . PHP_EOL;
        $msg_content_tpl .= '场景: ' . self::FEISHU_OA_BIZ_WARNING_SCENE_SAP_STATUS_AMEND . PHP_EOL;
        $msg_content_tpl .= '国家: ' . get_country_code() . PHP_EOL;
        $msg_content_tpl .= '环境: ' . RUNTIME . PHP_EOL;
        $msg_content_tpl .= '本批单据: all_order_no' . PHP_EOL;
        $msg_content_tpl .= '处理成功: success_order_total' . PHP_EOL;
        $msg_content_tpl .= '处理失败: error_order_no' . PHP_EOL;
        $msg_content_tpl .= '处理时间: ' . get_datetime_with_milliseconds() . PHP_EOL;
        $msg_content_tpl .= '执行说明: 上述单号修正前, 须保证已跟SAP侧确认' . PHP_EOL;

        $replace_search_key = ['all_order_no', 'success_order_total', 'error_order_no'];

        $all_order_no        = '';
        $success_order_total = 0;
        $error_order_no      = '';

        try {
            $support_module = [
                self::SYS_REIMBURSEMENT_MODULE,
                self::SYS_ORDINARY_PAYMENT_MODULE,
                self::SYS_LOAN_MODULE,
                self::SYS_LOAN_BACK_MODULE,
                self::SYS_RESERVE_FUND_APPLY_MODULE,
            ];

            // 获取待调整的单据配置
            $order_data = EnumsService::getInstance()->getSettingEnvValueMap('sys_tool_amend_order_sapstatus');

            $order_no_list = explode(',', $order_data['order_no']);
            $order_no_list = array_map(function ($v) {
                return trim($v);
            }, $order_no_list);

            $order_no_list = array_values(array_unique(array_filter($order_no_list)));
            $all_order_no  = implode(',', $order_no_list);

            $log .= '配置内容: 模块-' . ($order_data['biz_module'] ?? '') . '; 单据号-' . $all_order_no . PHP_EOL;
            if (empty($order_data['biz_module']) || empty($order_no_list)) {
                throw new ValidationException('sys_tool_amend_order_sapstatus 配置有误, 请检查', ErrCode::$VALIDATE_ERROR);
            }

            $order_data['biz_module'] = strtoupper($order_data['biz_module']);
            if (!in_array($order_data['biz_module'], $support_module)) {
                throw new ValidationException("{$order_data['biz_module']} 该模块处理, 暂不支持, 请产研确认",
                    ErrCode::$VALIDATE_ERROR);
            }

            // 获取业务侧单据
            $apply_no_field_name   = 'no';
            $sync_sap_status_field = 'sync_sap';
            switch ($order_data['biz_module']) {
                case self::SYS_REIMBURSEMENT_MODULE:
                    $order_models = ReimbursementModel::find([
                        'conditions' => 'no IN ({order_no:array})',
                        'bind'       => ['order_no' => $order_no_list],
                    ]);

                    break;
                case self::SYS_ORDINARY_PAYMENT_MODULE:
                    $order_models = OrdinaryPaymentModel::find([
                        'conditions' => 'apply_no IN ({order_no:array})',
                        'bind'       => ['order_no' => $order_no_list],
                    ]);

                    $apply_no_field_name = 'apply_no';
                    break;

                case self::SYS_RESERVE_FUND_APPLY_MODULE:
                    $order_models = ReserveFundApply::find([
                        'conditions' => 'rfano IN ({order_no:array})',
                        'bind'       => ['order_no' => $order_no_list],
                    ]);

                    $apply_no_field_name = 'rfano';

                    break;
                case self::SYS_LOAN_MODULE:
                    $order_models = LoanModel::find([
                        'conditions' => 'lno IN ({order_no:array})',
                        'bind'       => ['order_no' => $order_no_list],
                    ]);

                    $apply_no_field_name = 'lno';
                    break;

                case self::SYS_LOAN_BACK_MODULE:
                    $order_models = LoanReturnModel::find([
                        'conditions' => 'back_no IN ({order_no:array})',
                        'bind'       => ['order_no' => $order_no_list],
                    ]);

                    $apply_no_field_name   = 'back_no';
                    $sync_sap_status_field = 'back_sync_sap';
                    break;
            }

            if (empty($order_models) || empty($order_models->toArray())) {
                throw new ValidationException('业务表不存在上述单据, 请核实', ErrCode::$VALIDATE_ERROR);
            }

            $success_order_no_item = [];
            $error_order_no_item   = [];
            foreach ($order_models as $model) {
                if ($model->$sync_sap_status_field != SapBaseService::SYNC_SAP_FAIL) {
                    $error_order_no_item[] = $model->$apply_no_field_name . "[修改前的sap状态-{$model->sync_sap}, 非失败]";
                    continue;
                }

                $update_data = [
                    $sync_sap_status_field => SapBaseService::SYNC_SAP_SUCCESS,
                ];

                if ($model->i_update($update_data) === false) {
                    $error_order_no_item[] = $model->$apply_no_field_name . '[更新失败]';
                    continue;
                }

                $success_order_no_item[] = $model->$apply_no_field_name;
            }

            $success_order_total = count($success_order_no_item);

            $error_order_no = implode(',', $error_order_no_item);

            $log .= '处理成功的: ' . (implode(',', $success_order_no_item)) . PHP_EOL;
            $log .= '处理失败的: ' . $error_order_no . PHP_EOL;
        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;
        } catch (BusinessException $e) {
            $log         .= $e->getMessage() . PHP_EOL;
            $logger_type = 'notice';
        } catch (Exception $e) {
            $log         .= $e->getMessage() . PHP_EOL;
            $logger_type = 'error';
        }

        $log .= '结束时间: ' . get_datetime_with_milliseconds() . PHP_EOL;

        $all_order_total   = count($order_no_list ?? []);
        $error_order_total = $all_order_total - $success_order_total;
        $replace_values    = [
            'all_order_no'        => "(共{$all_order_total}笔) " . $all_order_no,
            'success_order_total' => $success_order_total . ' 笔',
            'error_order_no'      => "{$error_order_total} 笔 " . $error_order_no,
        ];
        $msg_content       = str_replace($replace_search_key, $replace_values, $msg_content_tpl);

        if (get_runtime_env() == 'pro') {
            send_feishu_text_msg($msg_content, self::FEISHU_OA_BIZ_WARNING_GROUP_MSG_KEY);
        }

        $this->logger->$logger_type($log);
        exit($log);
    }

    /**
     * PO单可修改数量状态维护[0->1; 1->0]
     *
     * php app/cli.php tool set_po_is_can_update
     *
     */
    public function set_po_is_can_updateAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log          = '任务名称: ' . $process_name . PHP_EOL;
        $log          .= '开始时间: ' . get_datetime_with_milliseconds() . PHP_EOL;

        $logger_type = 'info';

        $msg_content_tpl = '<at user_id="all">所有人</at>' . PHP_EOL;
        $msg_content_tpl .= '场景: scence_title' . PHP_EOL;
        $msg_content_tpl .= '国家: ' . get_country_code() . PHP_EOL;
        $msg_content_tpl .= '环境: ' . RUNTIME . PHP_EOL;
        $msg_content_tpl .= '处理单据: order_detail' . PHP_EOL;
        $msg_content_tpl .= '处理结果: handle_result' . PHP_EOL;
        $msg_content_tpl .= '处理时间: ' . get_datetime_with_milliseconds() . PHP_EOL;
        $msg_content_tpl .= '执行说明: 单据数量待产品调整完毕后，务必还原状态[is_can_update]' . PHP_EOL;
        $msg_content_tpl .= '执行日志: exec_log' . PHP_EOL;

        $replace_search_key = ['scence_title', 'order_detail', 'handle_result', 'exec_log'];

        // 模板默认值
        $scence_title  = '';
        $order_detail  = '';
        $handle_result = '失败';

        $db = $this->getDI()->get('db_oa');
        $db->begin();

        try {
            // 获取待调整的单据配置
            $config_info = EnumsService::getInstance()->getSettingEnvValueMap('sys_tool_set_purchase_ordre_is_can_update');
            $log         .= '配置内容: ' . json_encode($config_info, JSON_UNESCAPED_UNICODE) . PHP_EOL;

            $config_info = trim_array($config_info);
            if (empty($config_info['order_no']) || empty($config_info['product_ids']) || !in_array($config_info['is_can_update'],
                    [0, 1]) || !in_array($config_info['update_obj'], ['order', 'product'])) {
                throw new ValidationException('sys_tool_set_purchase_ordre_is_can_update 配置有误, 请检查',
                    ErrCode::$VALIDATE_ERROR);
            }

            $scence_title = $config_info['is_can_update'] == 1 ? self::FEISHU_OA_BIZ_WARNING_SCENE_OPEN_PO_IS_CAN_UPDATE : self::FEISHU_OA_BIZ_WARNING_SCENE_CLOSE_PO_IS_CAN_UPDATE;

            $order_detail .= $config_info['order_no'] . "[{$config_info['product_ids']}]";

            // 获取单据主数据
            $main_model = PurchaseOrder::findFirst([
                'conditions' => 'pono = :order_no:',
                'bind'       => ['order_no' => $config_info['order_no']],
            ]);
            if (empty($main_model)) {
                throw new ValidationException('PO单为空, 请检查配置', ErrCode::$VALIDATE_ERROR);
            }
            $log .= "{$config_info['order_no']}, 主单据 is_can_update={$main_model->is_can_update}[修改前]" . ($config_info['update_obj'] == 'order' ? ', 主单据状态需更新' : ', 主单据状态无需更新') . PHP_EOL;

            // 获取行
            $conditions = ['poid = :poid:'];
            $bind       = ['poid' => $main_model->id];
            if ($config_info['product_ids'] != '*') {
                $product_ids  = explode(',', $config_info['product_ids']);
                $product_ids  = array_map(function ($v) {
                    return trim($v);
                }, $product_ids);
                $product_ids  = array_values(array_unique(array_filter($product_ids)));
                $conditions[] = 'id IN ({ids:array})';
                $bind['ids']  = $product_ids;
            }

            $conditions     = implode(' AND ', $conditions);
            $product_models = PurchaseOrderProduct::find([
                'conditions' => $conditions,
                'bind'       => $bind,
            ]);
            if (empty($product_models->toArray())) {
                throw new ValidationException('PO单明细行为空, 请检查配置', ErrCode::$VALIDATE_ERROR);
            }

            // 判断是否需更新主单据
            if ($config_info['update_obj'] == 'order') {
                $log .= "主单据 is_can_update={$config_info['is_can_update']}[修改后]" . PHP_EOL;

                $main_update = [
                    'is_can_update' => $config_info['is_can_update'],
                ];
                if ($main_model->i_update($main_update) === false) {
                    throw new BusinessException('PO单主数据状态更新失败, 原因可能是 ' . get_data_object_error_msg($main_model),
                        ErrCode::$BUSINESS_ERROR);
                }
            }

            // 更新明细行
            foreach ($product_models as $product_model) {
                $log .= "明细行-{$product_model->id} is_can_update={$product_model->is_can_update}[修改前], is_can_update={$config_info['is_can_update']}[修改后]" . PHP_EOL;

                $product_update = [
                    'is_can_update' => $config_info['is_can_update'],
                ];
                if ($product_model->i_update($product_update) === false) {
                    throw new BusinessException('PO单明细行状态更新失败, 原因可能是 ' . get_data_object_error_msg($product_model),
                        ErrCode::$BUSINESS_ERROR);
                }
            }

            $db->commit();

            $handle_result = '成功';
        } catch (ValidationException $e) {
            $db->rollback();
            $log .= $e->getMessage() . PHP_EOL;
        } catch (BusinessException $e) {
            $db->rollback();
            $log         .= $e->getMessage() . PHP_EOL;
            $logger_type = 'notice';
        } catch (Exception $e) {
            $db->rollback();
            $log         .= $e->getMessage() . PHP_EOL;
            $logger_type = 'error';
        }

        $log .= '处理结果: ' . $handle_result . PHP_EOL;
        $log .= '结束时间: ' . get_datetime_with_milliseconds() . PHP_EOL;

        $replace_values = [
            'scence_title'  => $scence_title,
            'order_detail'  => $order_detail,
            'handle_result' => $handle_result,
            'exec_log'      => str_replace(PHP_EOL, '; ', $log),
        ];
        $msg_content    = str_replace($replace_search_key, $replace_values, $msg_content_tpl);
        if (get_runtime_env() == 'pro') {
            send_feishu_text_msg($msg_content, self::FEISHU_OA_BIZ_WARNING_GROUP_MSG_KEY);
        }

        $this->logger->$logger_type($log);
        exit($log);
    }

    /**
     * 拉取配置
     * @param $args
     * @return void
     * @throws Exception
     */
    public function pull_nacosAction($args = [])
    {
        $countryCode = isset($args[0]) && !empty($args[0])
            ? strtolower(substr(trim($args[0]), 0, 2))
            : 'th';

        if (!in_array($countryCode, ['th', 'ph', 'my', 'la', 'vn', 'id'])) {
            throw new Exception('invalid country code');
        }

        $baseUrl = 'https://dev-nacos.fex.pub/nacos/v1/cs/configs';
        $queryParams = [
            'dataId' => sprintf("data-flashhr-%s-oa-api-01", $countryCode),
            'group' => 'group-flashhr-dev',
            'tenant' => '8e3641ab-4d05-4ee4-8e8b-b1dba8e2cea8',
        ];
        $url = $baseUrl . '?' . http_build_query($queryParams);

        $config = file_get_contents($url);
        if (empty($config)) {
            throw new Exception("Config is empty. URL: $url");
        }

        $envPath = BASE_PATH . DIRECTORY_SEPARATOR . '.env';
        if (!file_exists($envPath)) {
            throw new Exception('.env file not found in ' . __DIR__);
        }
        if (!is_writable(__DIR__)) {
            throw new Exception('Directory is not writable: ' . __DIR__);
        }
        file_put_contents($envPath, $config);
    }

    /**
     * 定期清理待办短信日志的无效数据
     * 待办短信日志表, 保留近90天的
     *
     * php app/cli.php tool clean_sms_send_log
     *
     */
    public function clean_sms_send_logAction($params)
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log          = '任务名称: ' . $process_name . PHP_EOL;
        $log          .= '开始时间: ' . get_datetime_with_milliseconds() . PHP_EOL;

        $logger_type = 'info';

        try {
            $retention_days = $params[0] ?? 90;
            if (!is_numeric($retention_days)) {
                throw new ValidationException("保留天数参数有误, 请核查[{$retention_days}]", ErrCode::$VALIDATE_ERROR);
            }

            $create_time = date('Y-m-d 00:00:00', strtotime("-{$retention_days} days"));

            $log .= "待清理 {$create_time} 之前的无效历史日志数据[保留天数-{$retention_days}]" . PHP_EOL;

            // 获取保留日期前的数据
            $total_count = SmsPendingDataModel::count([
                'conditions' => 'create_time < :create_time:',
                'bind'       => ['create_time' => $create_time],
            ]);

            $log .= "共有 {$total_count} 条" . PHP_EOL;
            if (!$total_count) {
                throw new ValidationException('无待清理数据', ErrCode::$VALIDATE_ERROR);
            }

            // 删除
            $sql     = 'DELETE FROM `sms_pending_data` WHERE `create_time` < :create_time';
            $del_res = $this->getDI()->get('db_oa')->execute($sql, ['create_time' => $create_time]);
            if (!$del_res) {
                throw new BusinessException('清理失败', ErrCode::$BUSINESS_ERROR);
            }

            $log .= '清理成功' . PHP_EOL;
        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;
        } catch (BusinessException $e) {
            $log         .= $e->getMessage() . PHP_EOL;
            $logger_type = 'notice';
        } catch (Exception $e) {
            $log         .= $e->getMessage() . PHP_EOL;
            $logger_type = 'error';
        }

        $log .= '结束时间: ' . get_datetime_with_milliseconds() . PHP_EOL;
        $this->logger->$logger_type($log);
        exit($log);
    }

    /**
     * 定期清理待办push日志的无效数据
     * 待办push日志表, 保留近30天的
     *
     * php app/cli.php tool clean_push_msg_log
     *
     */
    public function clean_push_msg_logAction($params)
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log          = '任务名称: ' . $process_name . PHP_EOL;
        $log          .= '开始时间: ' . get_datetime_with_milliseconds() . PHP_EOL;

        $logger_type = 'info';

        try {
            $retention_days = $params[0] ?? 30;
            if (!is_numeric($retention_days)) {
                throw new ValidationException("保留天数参数有误, 请核查[{$retention_days}]", ErrCode::$VALIDATE_ERROR);
            }

            $create_time = date('Y-m-d 00:00:00', strtotime("-{$retention_days} days"));

            $log .= "待清理 {$create_time} 之前的无效历史日志数据[保留天数-{$retention_days}]" . PHP_EOL;

            // 获取保留日期前的数据
            $total_count = WorkflowRequestNodePushMsg::count([
                'conditions' => 'created_at < :create_time:',
                'bind'       => ['create_time' => $create_time],
            ]);

            $log .= "共有 {$total_count} 条" . PHP_EOL;
            if (!$total_count) {
                throw new ValidationException('无待清理数据', ErrCode::$VALIDATE_ERROR);
            }

            // 删除
            $sql     = 'DELETE FROM `workflow_request_node_push_msg` WHERE `created_at` < :create_time';
            $del_res = $this->getDI()->get('db_oa')->execute($sql, ['create_time' => $create_time]);
            if (!$del_res) {
                throw new BusinessException('清理失败', ErrCode::$BUSINESS_ERROR);
            }

            $log .= '清理成功' . PHP_EOL;
        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;
        } catch (BusinessException $e) {
            $log         .= $e->getMessage() . PHP_EOL;
            $logger_type = 'notice';
        } catch (Exception $e) {
            $log         .= $e->getMessage() . PHP_EOL;
            $logger_type = 'error';
        }

        $log .= '结束时间: ' . get_datetime_with_milliseconds() . PHP_EOL;
        $this->logger->$logger_type($log);
        exit($log);
    }

    /**
     * 定期清理审批组软删态的无效关系数据
     * 保留近3天的
     *
     * php app/cli.php tool clean_wf_list_deleted_relate
     *
     */
    public function clean_wf_list_deleted_relateAction($params)
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log          = '任务名称: ' . $process_name . PHP_EOL;
        $log          .= '开始时间: ' . get_datetime_with_milliseconds() . PHP_EOL;

        $logger_type = 'info';

        try {
            $retention_days = $params[0] ?? 3;
            if (!is_numeric($retention_days)) {
                throw new ValidationException("保留天数参数有误, 请核查[{$retention_days}]", ErrCode::$VALIDATE_ERROR);
            }

            $create_time = date('Y-m-d 00:00:00', strtotime("-{$retention_days} days"));

            $log .= "待清理 {$create_time} 之前的无效历史日志数据[保留天数-{$retention_days}]" . PHP_EOL;

            // 获取保留日期前的数据
            $total_count = StaffManageListRelateModel::count([
                'conditions' => 'created_at < :create_time: AND is_deleted = :is_deleted:',
                'bind'       => ['create_time' => $create_time, 'is_deleted' => GlobalEnums::IS_DELETED],
            ]);

            $log .= "共有 {$total_count} 条" . PHP_EOL;
            if (!$total_count) {
                throw new ValidationException('无待清理数据', ErrCode::$VALIDATE_ERROR);
            }

            // 删除
            $sql     = 'DELETE FROM `workflow_staff_manage_list_relate` WHERE `created_at` < :create_time AND `is_deleted` = :is_deleted';
            $del_res = $this->getDI()->get('db_oa')->execute($sql, ['create_time' => $create_time, 'is_deleted' => GlobalEnums::IS_DELETED]);
            if (!$del_res) {
                throw new BusinessException('清理失败', ErrCode::$BUSINESS_ERROR);
            }

            $log .= '清理成功' . PHP_EOL;
        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;
        } catch (BusinessException $e) {
            $log         .= $e->getMessage() . PHP_EOL;
            $logger_type = 'notice';
        } catch (Exception $e) {
            $log         .= $e->getMessage() . PHP_EOL;
            $logger_type = 'error';
        }

        $log .= '结束时间: ' . get_datetime_with_milliseconds() . PHP_EOL;
        $this->logger->$logger_type($log);
        exit($log);
    }

}
