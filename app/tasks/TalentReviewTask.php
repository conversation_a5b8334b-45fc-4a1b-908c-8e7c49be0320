<?php

use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\HrStaffInfoPositionModel;
use App\Modules\Organization\Services\DepartmentService;
use App\Modules\TalentReview\Services\PermissionsService as TalentReviewPermissionsService;
use App\Modules\User\Models\StaffPermissionModel;
use App\Repository\DepartmentRepository;
use App\Modules\TalentReview\Services\BaseService as TalentReviewBaseService;
use App\Modules\Common\Models\ExcelTaskModel;
use App\Modules\TalentReview\Services\StaffService as TalentReviewStaffService;

class TalentReviewTask extends BaseTask
{
    //生成员工下载Excel
    public function export_staffAction($params) {
        echo 'begin'  . PHP_EOL;
        try {
            if(empty($params[0]) || empty($params[1])) {
                echo '参数不能为空' . PHP_EOL;
            }
            $args_json = $params[0];
            $excel_task_id = $params[1];
            $excel_detail = ExcelTaskModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => [
                    'id' => $excel_task_id
                ],
            ]);

            if(!empty($excel_detail)) {
                $args_params = json_decode(base64_decode($args_json, true), true);
                $args_params['excel_task_id'] = $excel_task_id;
                $result = (new TalentReviewStaffService())->exportStaffExcel($args_params, $excel_task_id, $excel_detail->file_name);
                echo '结果：' . $result . PHP_EOL;
            } else {
                echo '未找到下载任务,参数:' . $excel_task_id . PHP_EOL;
            }
        } catch (Exception $e) {
            $this->logger->error('error:' . $e->getMessage());
            echo 'error:' . $e->getMessage() . PHP_EOL;
        }
        echo 'end'  . PHP_EOL;
    }


    //删除部门负责人配置的所有菜单
    public function del_department_manager_permissionAction() {
        echo "begin:" . date('Y-m-d H:i:s') . PHP_EOL;
        //$talent_review_menu = [755,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786];
        //$talent_review_permission_ids = \App\Modules\TalentReview\Services\BaseService::$talent_review_dept_permission_ids;

        $del_ids = [
            TalentReviewBaseService::$talent_review_department_staff_scope_1,
            TalentReviewBaseService::$talent_review_team_analysis_staff_scope_1,
            TalentReviewBaseService::$talent_review_report_map_staff_scope_1,
        ];
        $del_ids = array_merge(
            $del_ids,
            TalentReviewBaseService::$talent_review_department_staff_scope_1_action,
            TalentReviewBaseService::$talent_review_team_analysis_staff_scope_1_action,
            TalentReviewBaseService::$talent_review_report_map_staff_scope_1_action
        );

        $department_list = (new DepartmentRepository())->getDepartmentList();
        $staff_info_ids = array_unique(array_column($department_list, 'manager_id'));

        foreach ($staff_info_ids as $manager_id) {
            if(!empty($manager_id)) {
                $staff_permission = StaffPermissionModel::findFirst([
                    "conditions" =>"staff_id = :staff_info_id:",
                    "bind"=>[
                        'staff_info_id' => $manager_id,
                    ]
                ]);
                echo '-----负责人工号：' . $manager_id . PHP_EOL;
                if(!empty($staff_permission)) {
                    $before_permission_ids_str = $staff_permission->permission_ids;
                    $permission_ids = explode(',', $staff_permission->permission_ids);

                    //判断是否有 我的管辖和公司全员 如果有的话 不删除上级菜单
                    if(!in_array(TalentReviewBaseService::$talent_review_department_staff_scope_2, $permission_ids)
                        && !in_array(TalentReviewBaseService::$talent_review_department_staff_scope_3, $permission_ids)) {
                        $del_ids[] = TalentReviewBaseService::$talent_review_department_staff;
                    }

                    if(!in_array(TalentReviewBaseService::$talent_review_team_analysis_staff_scope_2, $permission_ids)
                        && !in_array(TalentReviewBaseService::$talent_review_team_analysis_staff_scope_3, $permission_ids)) {
                        $del_ids[] = TalentReviewBaseService::$talent_review_team_analysis;
                    }

                    if(!in_array(TalentReviewBaseService::$talent_review_report_map_staff_scope_3, $permission_ids)) {
                        $del_ids[] = TalentReviewBaseService::$talent_review_report_map;
                    }

                    if(in_array(TalentReviewBaseService::$talent_review_department_staff, $del_ids)
                        && in_array(TalentReviewBaseService::$talent_review_team_analysis, $del_ids)
                        && in_array(TalentReviewBaseService::$talent_review_report_map, $del_ids)) {
                        $del_ids[] = TalentReviewBaseService::$talent_review_permission_id;
                    }

                    $permission_ids = array_unique(array_diff($permission_ids, $del_ids));
                    $staff_permission->permission_ids = implode(',', $permission_ids);
                    $staff_permission->last_updated_at = date('Y-m-d H:i:s');

                    $after_permission_ids_str = $staff_permission->permission_ids;

                    $r = $staff_permission->save();
                    echo '-----删除菜单;结果：' . $r . PHP_EOL;
                    echo '-----删除菜单；原菜单ID：' . $before_permission_ids_str . PHP_EOL;
                    echo '-----删除菜单；增加后菜单ID：' . $after_permission_ids_str . PHP_EOL;
                }
            }
        }
        echo "end:" . date('Y-m-d H:i:s') . PHP_EOL;
    }

    //给部门负责人设置人才盘点菜单权限
    public function add_department_manager_permissionAction() {
        $add_ids[] = TalentReviewBaseService::$talent_review_permission_id;
        $add_ids[] = TalentReviewBaseService::$talent_review_department_staff;
        $add_ids[] = TalentReviewBaseService::$talent_review_department_staff_scope_1;
        $add_ids[] = TalentReviewBaseService::$talent_review_team_analysis;
        $add_ids[] = TalentReviewBaseService::$talent_review_team_analysis_staff_scope_1;
        $add_ids[] = TalentReviewBaseService::$talent_review_report_map;
        $add_ids[] = TalentReviewBaseService::$talent_review_report_map_staff_scope_1;

        $add_ids = array_merge(
            $add_ids,
            TalentReviewBaseService::$talent_review_department_staff_scope_1_action,
            TalentReviewBaseService::$talent_review_team_analysis_staff_scope_1_action,
            TalentReviewBaseService::$talent_review_report_map_staff_scope_1_action
        );

        $department_list = (new DepartmentRepository())->getDepartmentList();
        echo "begin:" . date('Y-m-d H:i:s') . PHP_EOL;

        $staff_info_ids = array_unique(array_column($department_list, 'manager_id'));
        foreach ($staff_info_ids as $manager_id) {
            echo '负责人工号：' . $manager_id . PHP_EOL;
            if(!empty($manager_id)) {
                $staff_permission = StaffPermissionModel::findFirst([
                    "conditions" =>"staff_id = :staff_info_id:",
                    "bind"=>[
                        'staff_info_id' => $manager_id,
                    ]
                ]);

                if(!empty($staff_permission)) {
                    $before_permission_ids_str = $staff_permission->permission_ids;
                    $permission_ids = explode(',', $staff_permission->permission_ids);
                    $permission_ids = array_unique(array_merge($permission_ids, $add_ids));
                    $staff_permission->permission_ids = implode(',', $permission_ids);
                    $staff_permission->last_updated_at = date('Y-m-d H:i:s');

                    $after_permission_ids_str = $staff_permission->permission_ids;

                    $r = $staff_permission->save();
                    echo '-----增加菜单;结果：' . $r;
                    echo '-----增加菜单；原菜单ID：' . $before_permission_ids_str . PHP_EOL;
                    echo '-----增加菜单；增加后菜单ID：' . $after_permission_ids_str . PHP_EOL;
                } else {
                    $staff_permission = new StaffPermissionModel();
                    $staff_permission->staff_id = $manager_id;
                    $staff_permission->permission_ids = implode(',', $add_ids);
                    $staff_permission->is_granted = 1;
                    $staff_permission->last_updated_at = date('Y-m-d H:i:s');
                    $r = $staff_permission->save();
                    echo '-----创建菜单;结果：' . $r . PHP_EOL;
                }
            }
        }
        echo "end:" . date('Y-m-d H:i:s') . PHP_EOL;
    }

    //删除hrbp 人才盘点菜单
    public function del_hrbp_permissionAction() {
        echo "begin:" . date('Y-m-d H:i:s') . PHP_EOL;
        $list = $this->getHrbpStaffList();

        $del_ids[] = TalentReviewBaseService::$talent_review_department_staff_scope_2;
        $del_ids[] = TalentReviewBaseService::$talent_review_team_analysis_staff_scope_2;
        $del_ids = array_merge(
            $del_ids,
            TalentReviewBaseService::$talent_review_department_staff_scope_2_action,
            TalentReviewBaseService::$talent_review_team_analysis_staff_scope_2_action
        );

        foreach ($list as $key => $value) {
            echo '工号：' . $value['staff_info_id'] . PHP_EOL;
            $staff_permission = StaffPermissionModel::findFirst([
                "conditions" =>"staff_id = :staff_info_id:",
                "bind"=>[
                    'staff_info_id' => $value['staff_info_id'],
                ]
            ]);

            if(!empty($staff_permission)) {
                $before_permission_ids_str = $staff_permission->permission_ids;
                $permission_ids = explode(',', $staff_permission->permission_ids);

                if(!in_array(TalentReviewBaseService::$talent_review_department_staff_scope_1, $permission_ids)
                    && !in_array(TalentReviewBaseService::$talent_review_department_staff_scope_3, $permission_ids)) {
                    $del_ids[] = TalentReviewBaseService::$talent_review_department_staff;
                }

                if(!in_array(TalentReviewBaseService::$talent_review_team_analysis_staff_scope_1, $permission_ids)
                    && !in_array(TalentReviewBaseService::$talent_review_team_analysis_staff_scope_3, $permission_ids)) {
                    $del_ids[] = TalentReviewBaseService::$talent_review_team_analysis;
                }

                if(!in_array(TalentReviewBaseService::$talent_review_report_map_staff_scope_1, $permission_ids) &&
                    !in_array(TalentReviewBaseService::$talent_review_report_map_staff_scope_3, $permission_ids)) {
                    $del_ids[] = TalentReviewBaseService::$talent_review_report_map;
                }

                if(in_array(TalentReviewBaseService::$talent_review_department_staff, $del_ids)
                    && in_array(TalentReviewBaseService::$talent_review_team_analysis, $del_ids)
                    && in_array(TalentReviewBaseService::$talent_review_report_map, $del_ids)) {
                    $del_ids[] = TalentReviewBaseService::$talent_review_permission_id;
                }

                $permission_ids = array_unique(array_diff($permission_ids, $del_ids));
                $staff_permission->permission_ids = implode(',', $permission_ids);

                $staff_permission->last_updated_at = date('Y-m-d H:i:s');

                $after_permission_ids_str = $staff_permission->permission_ids;

                $r = $staff_permission->save();
                echo '-----删除菜单;结果：' . $r;
                echo '-----删除菜单；原菜单ID：' . $before_permission_ids_str . PHP_EOL;
                echo '-----删除菜单；增加后菜单ID：' . $after_permission_ids_str . PHP_EOL;
            }
        }

        echo "end:" . date('Y-m-d H:i:s') . PHP_EOL;
    }

    //给hrbp工号 增加管辖权限 talent_review add_hrbp_permission
    public function add_hrbp_permissionAction() {
        echo "begin:" . date('Y-m-d H:i:s') . PHP_EOL;
        $list = $this->getHrbpStaffList();
//        echo "需要设置人才盘点菜单HRBP共计：" . count($list);

//        $add_ids[] = TalentReviewBaseService::$talent_review_permission_id;
//        $add_ids[] = TalentReviewBaseService::$talent_review_department_staff;
//        $add_ids[] = TalentReviewBaseService::$talent_review_department_staff_scope_2;
//        $add_ids[] = TalentReviewBaseService::$talent_review_team_analysis;
//        $add_ids[] = TalentReviewBaseService::$talent_review_team_analysis_staff_scope_2;
//
//        $add_ids = array_merge(
//            $add_ids,
//            TalentReviewBaseService::$talent_review_department_staff_scope_2_action,
//            TalentReviewBaseService::$talent_review_team_analysis_staff_scope_2_action
//        );

        echo "需要设置组织架构菜单HRBP共计：" . count($list);
        $add_ids[] = DepartmentService::$organization_menu_id; //组织架构
        $add_ids[] = DepartmentService::$organization_department_menu_id; //部门架构
        $add_ids[] = DepartmentService::$organization_job_manage_menu_id; //职位体系
        $add_ids[] = DepartmentService::$organization_job_manage_job_menu_id;//职位管理
        $add_ids[] = DepartmentService::$organization_job_manage_job_log_menu_id; //变更记录

        $add_ids = array_merge(
            $add_ids,
            DepartmentService::$organization_department_menu_action_id,
            DepartmentService::$organization_job_manage_job_menu_action_id,
            DepartmentService::$organization_job_manage_job_log_action_id
        );

        foreach ($list as $key => $value) {
            echo '工号：' . $value['staff_info_id'] . PHP_EOL;
            $staff_permission = StaffPermissionModel::findFirst([
                "conditions" =>"staff_id = :staff_info_id:",
                "bind"=>[
                    'staff_info_id' => $value['staff_info_id'],
                ]
            ]);

            if(!empty($staff_permission)) {
                $before_permission_ids_str = $staff_permission->permission_ids;
                $permission_ids = explode(',', $staff_permission->permission_ids);
                $permission_ids = array_unique(array_merge($permission_ids, $add_ids));

                $staff_permission->permission_ids = implode(',', $permission_ids);
                $staff_permission->last_updated_at = date('Y-m-d H:i:s');

                $after_permission_ids_str = $staff_permission->permission_ids;

                $r = $staff_permission->save();
                echo '-----增加菜单;结果：' . $r;
                echo '-----增加菜单；原菜单ID：' . $before_permission_ids_str . PHP_EOL;
                echo '-----增加菜单；增加后菜单ID：' . $after_permission_ids_str . PHP_EOL;
            } else {
                $staff_permission = new StaffPermissionModel();
                $staff_permission->staff_id = $value['staff_info_id'];
                $staff_permission->permission_ids = implode(',', $add_ids);
                $staff_permission->is_granted = 1;
                $staff_permission->last_updated_at = date('Y-m-d H:i:s');
                $r = $staff_permission->save();
                echo '-----创建菜单;结果：' . $r . PHP_EOL;
            }
        }
        echo "end:" . date('Y-m-d H:i:s') . PHP_EOL;
    }

    public function getHrbpStaffList() {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            'hr_staff.staff_info_id',
            'hr_staff.name',
            'hr_staff.job_title',
            'hr_staff.sys_store_id',
            'hr_staff.sys_department_id',
            'hr_staff.node_department_id',
            'hr_staff.state'
        ]);
        $builder->from(['hr_staff' => HrStaffInfoModel::class]);
        $builder->leftJoin(HrStaffInfoPositionModel::class, 'hr_staff.staff_info_id = p.staff_info_id', 'p');
        $builder->where('hr_staff.is_sub_staff = 0');
        $builder->inWhere('hr_staff.state', [1,3]);
        $builder->inWhere('hr_staff.formal', [1, 4]);//员工属性1正式 4实习生
        $builder->andWhere('p.position_category = 68');
        $list = $builder->getQuery()->execute()->toArray();

        return $list;
    }

    //给hrbp工号 增加管辖权限 talent_review fix_hrbp_permission
    public function fix_hrbp_permissionAction()
    {
        $list = $this->getHrbpStaffList();
        foreach ($list as $key => $value) {
            (new TalentReviewPermissionsService())->addHrbpPermission($value['staff_info_id']);
        }
    }

}