<?php

use App\Library\Enums\StaffInfoEnums;
use App\Library\Enums;
use App\Library\ErrCode;
use App\Library\OssHelper;
use App\Library\Validation\ValidationException;
use App\Models\backyard\HrJobTitleModel;
use App\Modules\AdministrationTicket\Services\TicketListService;
use App\Modules\User\Models\RolePermissionModel;
use App\Modules\User\Models\StaffPermissionModel;
use App\Modules\User\Services\UserService;
use App\Modules\User\Services\PermissionService;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Organization\Models\HrStaffInfoModel;
use App\Models\backyard\SysDepartmentModel;
use App\Library\Enums\GlobalEnums;
use App\Repository\BySettingEnvRepository;
use App\Repository\HrStaffRepository;
use App\Modules\User\Models\PermissionModel;

class PermissionTask extends BaseTask{

    private $_incr_staff_id_code = 'oa_permission_incr_staff_id'; //记录增量员工当前任务执行的员工表最大自增ID code

    public function outAction($params){



        $list = $list_keys = (new PermissionService())->getAllPermissions()->toArray();
        $permissions = list_to_tree($list, 'id', 'ancestry', 'children');
        $list_keys = array_column($list_keys,null,'id');
        $_data = [];
        foreach ($permissions as $k => $v) {
            $tmp = [];
            $tmp['id'] = $v['id'];
            $tmp['name'] = $list_keys[$v['id']]['name'];
            $_data[$v['id']] = $tmp;
            if($v['children']){
                foreach ($v['children'] as $k1 => $v1) {
                    $tmp = [];
                    $tmp['id'] = $v1['id'];
                    $tmp['name'] = $list_keys[$v['id']]['name'] .'--' .$list_keys[$v1['id']]['name'];
                    $_data[$v1['id']] = $tmp;
                    if($v1['children']){
                        foreach ($v1['children'] as $k2 => $v2) {
                            $tmp = [];
                            $tmp['id'] = $v2['id'];
                            $tmp['name'] = $list_keys[$v['id']]['name'] .'--'.$list_keys[$v1['id']]['name'].'--'.$list_keys[$v2['id']]['name'];
                            $_data[$v2['id']] = $tmp;
                            if($v2['children']) {
                                foreach ($v2['children'] as $k3 => $v3) {
                                    $tmp = [];
                                    $tmp['id'] = $v3['id'];
                                    $tmp['name'] = $list_keys[$v['id']]['name'] .'--'.$list_keys[$v1['id']]['name'].'--'.$list_keys[$v2['id']]['name'].'--'.$list_keys[$v3['id']]['name'];
                                    $_data[$v3['id']] = $tmp;
                                }
                            }
                        }
                    }
                }
            }



        }
        if (!empty($params[0])) {
            $condition = "staff_id = " . $params[0];
        } else {
            $condition = "staff_id > 0";
        }

        $_data = array_column($_data,'name','id');
        //[2]oa系统中的权限表中涉及的全部用户
        $oaStaffList = \App\Modules\User\Models\StaffPermissionModel::find([
            'columns'    => 'staff_id',
            'conditions' => $condition,
        ])->toArray();
        $_tmp_data = [];

        $job_title = \App\Modules\User\Models\StaffInfoJobTitleModel::find()->toArray();
        $job_title = array_column($job_title,'name','id');
        $staff_list = \App\Modules\User\Models\StaffInfoModel::find()->toArray();
        $staff_list = array_column($staff_list,null,'id');

        $DepartmentList = \App\Modules\User\Models\DepartmentModel::find()->toArray();
        $DepartmentList = array_column($DepartmentList,null,'id');

        foreach ($oaStaffList as $k => $v) {
            $tmp_data = (new UserService())->getUserPermissions($v['staff_id'])->toArray();
            foreach ($tmp_data as $k1 => $v1) {
                if($_data[$v1['id']]){
                    $permission =  explode('--',$_data[$v1['id']]);
                    $tmp['name'] = $_data[$v1['id']];
                    $tmp['id'] = $v1['id'];
                    $tmp['staff_id'] = $v['staff_id'];
                    $tmp['job_title_id'] = $staff_list[$v['staff_id']]['job_title'] ?? '';
                    $tmp['job_title'] = $job_title[$staff_list[$v['staff_id']]['job_title']] ?? '';
                    $_tmp_data[] = [
                        $permission[0] ?? '',
                        $permission[1] ?? '',
                        $permission[2] ?? '',
                        $permission[3] ?? '',
                        $v['staff_id'],
                        $staff_list[$v['staff_id']]['name'] ?? '',
                        $job_title[$staff_list[$v['staff_id']]['job_title']] ?? '',
                        $DepartmentList[$staff_list[$v['staff_id']]['department_id']]['name'] ?? '',
                    ];
                }
            }
        }

        $head = [
            '模块名称',
            '菜单名称',
            'tab页面',
            '按钮名称',
            '工号',
            '员工姓名',
            '职位',
            '部门',
        ];
        $fileName = 'a.xlsx';
        $config = [
            'path' => BASE_PATH . '/public/',
        ];
        echo 'start'.date('H:i:s').PHP_EOL;
        $excel = new \Vtiful\Kernel\Excel($config);
        // 此处会自动创建一个工作表
        $fileObject = $excel->fileName($fileName);
        $fileObject->header($head)->data($_tmp_data);
        echo 'end'.date('H:i:s').PHP_EOL;
        // 最后的最后，输出文件
        $filePath = $fileObject->output();
        $file_arr = OssHelper::uploadFile($filePath);
        print_r($file_arr);

    }

    /**
     * 刷权限专用
     * php app/cli permission addids ./1.txt ,2,3,4
     * @param $param
     */
    public function addidsAction($param)
    {
        $fh = fopen($param[0], "r");
        if (!$fh || empty($param[1]) || count(explode(',',$param[1])) == 0) {
            exit("error open file or permission empty");
        }
        $permission_ids = $param[1];
        $data = [];
        //文件处理
        while (!feof($fh)) {
            $line = trim(fgets($fh, 9999));
            if (empty($line)) {
                continue;
            }
            $data[] = $line;
        }
        fclose($fh);

        //添加权限
        foreach ($data as $k => $v) {
            $model = StaffPermissionModel::findFirst([
                'staff_id = :id: and permission_ids like :permission: ',
                'bind' => ['id' => $v,'permission'=> '%'.$permission_ids.'%'],
            ]);
            //全匹配到就跳过不用重复添加
            if($model){
                echo sprintf(" line %s => %s %s %s ",$k,count($data),$v,' there are already ') .PHP_EOL ;
                continue;
            }
            $model = StaffPermissionModel::findFirst("staff_id = $v");
            if($model){
                $_tmp = $model->permission_ids.$permission_ids;
                $model->permission_ids = $_tmp;
                $model->last_updated_at = date('Y-m-d H:i:s');
                $model->update();
                echo sprintf("update line  %s => %s %s %s ",$k,count($data),$v,$permission_ids) .PHP_EOL ;
            }else{
                $model = new StaffPermissionModel();
                $model->staff_id = $v;
                $model->permission_ids = $permission_ids;
                $model->is_granted = 1;
                $model->last_updated_at = date('Y-m-d H:i:s');
                $model->save();
                echo sprintf(" save line  %s => %s %s %s ",$k,count($data),$v,$permission_ids) .PHP_EOL ;
            }
            unset($model);
        }
        echo 'end';
    }

    /**
     * 批量开通oa权限
     * @param $params
     * PS:
     * 1. 第一个参数: all: 全量员工; incr: 增量员工(默认增量: hr_staff_info 表 24 小时内添加的)
     * 2. 第二个参数: 权限配置key; (setting_env, 默认:oa_batch_permission_ids)
     * 3. 第三个参数: 权限id; 如果有该权限id，才更新
     *
     * php app/cli.php permission add_staff_permission all
     **/
    public function add_staff_permissionAction($params = [])
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log = '任务名称: ' . $process_name . ' ------ 系统所在国家: ' . get_country_code() . ' ----- ' . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        try {

            // 取数条件初值
            $get_staff_type = isset($params[0]) && $params[0] == 'all' ? 'all' : 'incr';
            $permission_code = isset($params[1]) ? (string)$params[1] : 'oa_batch_permission_ids';
            $permission_condition_key = isset($params[2]) ? (string)$params[2] : '';

            $log .= "传入参数: get_staff_type = $get_staff_type, permission_code = $permission_code" . PHP_EOL;

            $this->check_process($process_name);

            // 取权限配置
            $permission_config      = EnvModel::getEnvByCode($permission_code);
            $log .= "待配置的权限: $permission_config" . PHP_EOL;

            $permission_config      = json_decode($permission_config, true);

            // 未配置权限
            if (empty($permission_config)) {
                throw new ValidationException("setting_env[{$permission_code}] 未配置 或 配置内容为空", ErrCode::$VALIDATE_ERROR);
            }
            $permission_condition_id = 0;
            if (!empty($permission_condition_key)) {
                $keyObj = \App\Modules\User\Models\PermissionModel::findFirst([
                    'key = :key: ',
                    'bind' => ['key' => $permission_condition_key],
                ]);
                if (!isset($keyObj->id)) {
                    throw new ValidationException("未获取到条件内的key值", ErrCode::$VALIDATE_ERROR);
                }
                $permission_condition_id = $keyObj->id;
            }

            $permission_config_data = [];
            foreach ($permission_config as $k1 => $v1) {
                $permission_config_data = array_merge($v1, $permission_config_data);
            }
            foreach ($permission_config_data as $key1=> $value1){
                $permission_config_data[$key1] = (string)$value1;
            }

            // 配置权限id
            $permission_config_ids = implode(',', $permission_config_data);

            // 取人
            // 在职 + 有编制 + 非代理制 + 非子账号
            $get_hr_staff_sql = "state = :state: AND formal = :formal: AND wait_leave_state = :wait_leave_state: AND is_sub_staff = :is_sub_staff:";
            $get_hr_staff_bind = [
                'state' => 1,
                'formal' => 1,
                'wait_leave_state' => 0,
                'is_sub_staff' => 0,
            ];

            if ($get_staff_type == 'incr') {
                //获取当前记录自增权限用户表最大自增ID配置项
                $env_incr_staff_code = EnvModel::findFirst([
                    'conditions' => 'code=:code:',
                    'bind' => ['code' => $this->_incr_staff_id_code],
                ]);
                if (!isset($env_incr_staff_code->val) || !(int)$env_incr_staff_code->val) {
                    throw new ValidationException("setting_env[{$this->_incr_staff_id_code}] 未配置 或 配置内容为空", ErrCode::$VALIDATE_ERROR);
                }
                $get_hr_staff_sql .= " AND id > :max_id:";
                $get_hr_staff_bind['max_id'] = $env_incr_staff_code->val;
            }

            $i   = 0;
            $batch_count = 0;
            $per_length = 1000;
            $staff_info_data = HrStaffInfoModel::find([
                'conditions' => $get_hr_staff_sql,
                'bind' => $get_hr_staff_bind,
                'offset' => $i,
                'limit' => $per_length,
                'order' => 'id DESC',
                'columns' => ['id','staff_info_id'],
            ])->toArray();

            $log .= PHP_EOL . "处理批次[batch=$batch_count]: start = $i, per_length = $per_length, 本批次待处理员工数[From HR]: " . count($staff_info_data) . PHP_EOL;
            //最大自增ID
            $max_staff_id = $staff_info_data ? $staff_info_data[0]['id'] : 0;
            // 对员工分批次处理权限
            while (!empty($staff_info_data)) {

                $staff_permission_data = [];
                $staff_info_ids        = '';
                $staff_info_ids        = array_column($staff_info_data, 'staff_info_id');

                $staff_permission_data = \App\Modules\User\Models\StaffPermissionModel::find([
                    "conditions" => 'staff_id in ({ids:array})',
                    "bind"       => ['ids' => $staff_info_ids],
                ])->toArray();
                $permission_staff_ids  = array_column($staff_permission_data, 'staff_id');
                $permission_ids        = array_column($staff_permission_data, 'permission_ids', 'staff_id');

                $log .= '已有OA账号的员工数[In OA]: ' . count($staff_permission_data) . PHP_EOL;

                // 计算 hr工号 与 OA已分配权限工号的交集
                $intersect = array_intersect($staff_info_ids, $permission_staff_ids);

                // 计算 hr 新增的工号(取差集)
                $add_staff_permission = array_diff($staff_info_ids, $intersect);

                $last_updated_at = date("Y-m-d H:i:s");

                $addList = [];

                $log .= PHP_EOL . '需在OA新增的员工数[Insert OA]: ' . count($add_staff_permission) . PHP_EOL;

                if (!empty($add_staff_permission) && empty($permission_condition_key)) {
                    // 给 hr 新员工 分配预设置的权限
                    foreach ($add_staff_permission as $k => $v) {
                        $addList[] = [
                            'staff_id'        => $v,
                            'permission_ids'  => $permission_config_ids,
                            'is_granted'      => 1,
                            'last_updated_at' => $last_updated_at,
                        ];
                    }
                }

                // *** 本批次员工权限处理开始*** //
                $db_oa  = $this->getDI()->get('db_oa');
                $db_oa->begin();
                try {
                    if (!empty($addList)) {
                        $res = '';
                        $res = (new \App\Modules\User\Models\StaffPermissionModel())->batch_insert($addList);

                        if ($res === false) {
                            throw new Exception('本批次批量新增失败: 员工共 ' . count($addList) . '个, 明细: ' . json_encode($addList, JSON_UNESCAPED_UNICODE));
                        }

                        $log .= '本批次批量新增成功 '.count($addList) . ' 个' . PHP_EOL;
                    }

                    $log .= PHP_EOL . '需在OA更新的员工数[Update OA]: ' . count($intersect) . PHP_EOL;

                    // 需要更新的人
                    if (!empty($intersect)) {
                        foreach ($intersect as $k => $v) {
                            // 员工在OA已有的权限
                            $permission_id_arr = explode(',', $permission_ids[$v]);

                            //如果有权限判断条件,只又在该条件下才更新
                            if (!empty($permission_condition_key) && !in_array($permission_condition_id,$permission_id_arr)) {
                                continue;
                            }

                            // 已有权限 与 待分配权限全集
                            $permission_id_arrs = array_unique(array_merge($permission_id_arr, $permission_config_data));

                            // 员工本次待追加的权限
                            $permission_diff_ids = array_diff($permission_id_arrs, $permission_id_arr);
                            if (!$permission_diff_ids) {
                                $log .= '权限无新增, 不需更新, 跳过: staff_id = ' . $v . PHP_EOL;
                                continue;
                            }

                            // 待追加权限
                            $permission_v_ids  = '';
                            $permission_v_ids  = implode(",", $permission_id_arrs);

                            $sql               = "update staff_permission set `permission_ids`='{$permission_v_ids}',`last_updated_at`='{$last_updated_at}'  where staff_id= {$v}";
                            $flag              = $db_oa->execute($sql);
                            if (empty($flag)) {
                                throw new Exception("权限更新失败: staff_id = $v, permission = $permission_v_ids");
                            }

                            $log .= "权限更新成功: staff_id = $v, permission = $permission_v_ids" . PHP_EOL;
                        }
                    }

                    $db_oa->commit();

                }  catch (\Exception $e) {
                    $db_oa->rollback();

                    $log .= "本批次权限新增/更新异常[已跳过该批次]: " . $e->getMessage() . PHP_EOL;
                }
                // *** 本批次员工权限处理结束*** //

                sleep(5);

                // 取下一批次工号
                $i += $per_length;
                $batch_count++;
                $staff_info_data = HrStaffInfoModel::find([
                    'conditions' => $get_hr_staff_sql,
                    'bind' => $get_hr_staff_bind,
                    'offset' => $i,
                    'limit' => $per_length,
                    'order' => 'id DESC',
                    'columns' => ['id','staff_info_id'],
                ])->toArray();

                $log .= PHP_EOL . "处理批次[batch=$batch_count]: start = $i, per_length = $per_length, 本批次待处理员工数[From HR]: " . count($staff_info_data) . PHP_EOL;
            }

        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;
        } catch (\Exception $e) {
            $log .= $e->getMessage() . PHP_EOL;
        }
        //存在部分批次执行失败的场景，通过日志记录再处理，不影响此次最大ID值更新
        if ($get_staff_type == 'incr' && $env_incr_staff_code && $max_staff_id) {
            //回更ENV配置表
            $env_incr_staff_code->val = $max_staff_id;
            $env_incr_staff_code->updated_at = date('Y-m-d H:i:s');
            $env_incr_staff_code->save();
            $log .= PHP_EOL . "修改ENV配置表【{$this->_incr_staff_id_code}】:【{$max_staff_id}】" . PHP_EOL;
        }
        $log .= "结束时间: " . date('Y-m-d H:i:s') . PHP_EOL;
        $this->logger->info("add-staff-permission " . $log);
        exit($log);
    }

    /**
     * 打印OA权限菜单树
     */
    public function showAction()
    {
        $list = $list_keys = (new PermissionService())->getAllPermissions()->toArray();
        $permissions = list_to_tree($list, 'id', 'ancestry', 'children');
        $list_keys = array_column($list_keys,null,'id');
        $_data = [];
        $menu_tree = PHP_EOL . 'OA权限菜单结构' . PHP_EOL;
        foreach ($permissions as $k => $v) {
            $tmp = [];
            $tmp['id'] = $v['id'];
            $tmp['name'] = $list_keys[$v['id']]['name'];
            $_data[$v['id']] = $tmp;
            $menu_tree .= PHP_EOL . $v['id'] . ' ----------------------------- ' . $v['name'] . ' [level = 0]' . PHP_EOL;
            if($v['children']){
                foreach ($v['children'] as $k1 => $v1) {
                    $tmp = [];
                    $tmp['id'] = $v1['id'];
                    $tmp['name'] = $list_keys[$v['id']]['name'] .'--' .$list_keys[$v1['id']]['name'];
                    $_data[$v1['id']] = $tmp;

                    $menu_tree .= "\t" . $v1['id'] . ' ------------------- ' . $v1['name'] . " [level = 1, parent_id = {$v['id']}]" . PHP_EOL;

                    if($v1['children']){
                        foreach ($v1['children'] as $k2 => $v2) {
                            $tmp = [];
                            $tmp['id'] = $v2['id'];
                            $tmp['name'] = $list_keys[$v['id']]['name'] .'--'.$list_keys[$v1['id']]['name'].'--'.$list_keys[$v2['id']]['name'];
                            $_data[$v2['id']] = $tmp;

                            $menu_tree .= "\t\t" . $v2['id'] . ' ----------- ' . $v2['name'] . " [level = 2, parent_id = {$v1['id']}]" . PHP_EOL;

                            if($v2['children']) {
                                foreach ($v2['children'] as $k3 => $v3) {
                                    $tmp = [];
                                    $tmp['id'] = $v3['id'];
                                    $tmp['name'] = $list_keys[$v['id']]['name'] .'--'.$list_keys[$v1['id']]['name'].'--'.$list_keys[$v2['id']]['name'].'--'.$list_keys[$v3['id']]['name'];
                                    $_data[$v3['id']] = $tmp;

                                    $menu_tree .= "\t\t\t" . $v3['id'] . ' --- ' . $v3['name'] . " [level = 3, parent_id = {$v2['id']}]" . PHP_EOL;
                                }
                            }
                        }
                    }
                }
            }
        }

        echo $menu_tree;
    }


	/**
	 * @description:  根据 hc 预算汇总菜单权限 的人 批量补充 功能权限
	 *
	 * @param null
	 *
	 * @return     :
	 * <AUTHOR> L.J
	 * @time       : 2021/12/3 14:33
	 */

	public function hc_permission_brush_dataAction(){
		set_time_limit(60*10);//10 分钟
		ini_set('memory_limit', '1024M');
		try {
			$permission_log_id = '206'; //用户菜单权限的人
			//现在需要添加的功能 id
			$permission_log_add_id = ['206', '525', '526', '527', '560'];
			//查询需要更新的人
			$builder    = $this->modelsManager->createBuilder();
			$column_str = 'w.*';
			$builder->columns($column_str);
			$builder->from(['w' => StaffPermissionModel::class]);
			$sql = "FIND_IN_SET(:permission_log_id:,w.permission_ids) ";
			$builder->andWhere($sql, ['permission_log_id' => $permission_log_id]);
			$items = $builder->getQuery()->execute()->toArray();
			if (!empty($items)) {
				foreach ($items as $k => $v) {
					//查询以前的权限
					$permission = [];
					$permission = explode(',', $v['permission_ids']);
					$permission = array_unique(array_merge($permission, $permission_log_add_id));//把本次需要添加的权限 添加进去 然后去重
					$model      = StaffPermissionModel::findFirst(["conditions" => "id = :id:", "bind" => ["id" => $v['id']]]);
					if ($model) {
						$log_permission         = $model->permission_ids;
						$model->permission_ids  = implode(',', $permission);
						$model->last_updated_at = date('Y-m-d H:i:s');
						$model->update();
						echo '修改权限id ==> ' . $v['id'] . ' 工号 ==> ' . $v['staff_id'] . '  源权限=>' . $log_permission . ' 修改为==>' . $model->permission_ids . PHP_EOL;
					}
				}
			}
			//查询需要更新的角色  角色是需要删除 206 和需要添加的功能权限
//			$builder    = $this->modelsManager->createBuilder();
//			$column_str = 'w.*';
//			$builder->columns($column_str);
//			$builder->from(['w' => RolePermissionModel::class]);
//			$sql = "FIND_IN_SET(:permission_log_id:,w.permission_ids)";
//			$builder->andWhere($sql, ['permission_log_id' => $permission_log_id]);
//			$Role_items = $builder->getQuery()->execute()->toArray();
//			if (!empty($Role_items)) {
//				foreach ($Role_items as $key => $val) {
//					$permission = [];
//					$permission = explode(',', $val['permission_ids']);
//					//取差
//					$save_permission = array_diff($permission, $permission_log_add_id);
//					$model           = RolePermissionModel::findFirst(["conditions" => "id = :id:", "bind" => ["id" => $val['id']]]);
//					if ($model) {
//						$log_permission         = $model->permission_ids;
//						$model->permission_ids  = implode(',', $save_permission);
//						$model->last_updated_at = date('Y-m-d H:i:s');
//						$model->update();
//						echo '修改权限id ==> ' . $val['id'] . ' 角色 id ==> ' . $val['role_id'] . '  源权限=>' . $log_permission . ' 修改为==>' . $model->permission_ids . PHP_EOL;
//					}
//
//				}
//			} else {
//				echo '没有待解决的角色' . PHP_EOL;
//			}
		} catch (\Exception $e) {
			echo $e->getMessage() . PHP_EOL;
		}

		echo '解决完了';
	}


    /**
     * 根据部门等级自己指定的左侧权限
     * php cli.php permission add_level_permission
     */
    public function add_level_permissionAction()
    {
        $process_name    = str_replace('Action', '', __FUNCTION__);
        $log             = '任务名称: ' . $process_name . ' ------ 系统所在国家: ' . get_country_code() . ' ----- ' . PHP_EOL;
        $log             .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $last_updated_at = date('Y-m-d H:i:s', time());
        $is_granted      = 1;
        $logger_type     = 'info';

        $permission_authority = json_decode(EnvModel::getEnvByCode('appoint_department_level_add_permission_authority'), true) ?? [];//根据部门等级自己指定的左侧权限

        if (empty($permission_authority) || empty($permission_authority['department_level']) || empty($permission_authority['permission_authority'])) {
            throw new ValidationException('根据部门等级自己指定的左侧权限数据不能为空', ErrCode::$VALIDATE_ERROR);
        }
        $level = $permission_authority['department_level'];
        $permission_arr = $permission_authority['permission_authority'];
        try {
            if (empty($permission_arr) || !is_array($permission_arr)) {
                throw new ValidationException('权限数据不能为空 且必须是数组', ErrCode::$VALIDATE_ERROR);
            }
            $department_arr = SysDepartmentModel::find([
                'conditions' => "level = :level: and deleted = :deleted: and manager_id != '' ",
                'bind'       => [
                    'level' => $level,
                    'deleted' =>GlobalEnums::IS_NO_DELETED,
                ],
                'columns'    => 'manager_id',
            ])->toArray();
            if (empty($department_arr)) {
                throw new ValidationException('部门暂无数据, 请忽略', ErrCode::$VALIDATE_ERROR);
            }

            $staff_data = HrStaffInfoModel::find(
                ['conditions' => 'staff_info_id in ({ids:array}) and state = :state: and wait_leave_state = :wait_leave_state:',
                 'bind'       => [
                     'ids'              => array_values(array_unique(array_filter(array_column($department_arr, 'manager_id')))),
                     'state'            => StaffInfoEnums::STAFF_STATE_IN,
                     'wait_leave_state' => StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_NO,
                 ],
                 'columns'    => ['staff_info_id'],
                ])->toArray();
            if (empty($staff_data)) {
                throw new ValidationException('部门负责人为空, 请忽略', ErrCode::$VALIDATE_ERROR);
            }

            $staff_id_all         = array_values(array_unique(array_column($staff_data, 'staff_info_id')));
            $staff_permission_arr = StaffPermissionModel::find(
                ['conditions' => 'staff_id in ({staff_id:array})',
                 'bind'       => [
                     'staff_id' => $staff_id_all,
                 ],
                 'columns'    => ['id', 'staff_id', 'permission_ids'],
                ])->toArray();
            $staff_permission_ids = array_column($staff_permission_arr, 'staff_id');
            $diff_staff_ids       = array_diff($staff_id_all, $staff_permission_ids);//差集新增
            $recorded_staff_ids   = array_intersect($staff_permission_ids, $staff_id_all);//交集修改

            if (!empty($diff_staff_ids)) {
                $add_permission = [];
                foreach ($diff_staff_ids as $staff_id) {
                    $add_permission[] = [
                        'staff_id'        => $staff_id,
                        'permission_ids'  => implode(',', $permission_arr),
                        'is_granted'      => $is_granted,
                        'last_updated_at' => $last_updated_at,
                    ];
                }
                $res = (new StaffPermissionModel())->batch_insert($add_permission);
                if ($res === false) {
                    throw new Exception('本批次批量新增失败: 员工共 ' . count($add_permission) . '个, 明细: ' . json_encode($add_permission, JSON_UNESCAPED_UNICODE));
                }
                $log .= '本批次批量新增成功 ' . count($add_permission) . ' 个' . PHP_EOL;
            }

            // 需要更新的人
            if (!empty($recorded_staff_ids)) {
                $staff_permission_arr = array_column($staff_permission_arr, null, 'staff_id');
                $db_oa            = $this->getDI()->get('db_oa');
                foreach ($recorded_staff_ids as $staff_id) {
                    // 已有权限 与 待分配权限全集
                    $old_permission_ids = explode(',', $staff_permission_arr[$staff_id]['permission_ids']);
                    $permission_id_arr  = array_unique(array_merge($old_permission_ids, $permission_arr));
                    // 员工本次待追加的权限
                    $permission_diff_ids = array_diff($permission_id_arr, $old_permission_ids);
                    if (!$permission_diff_ids) {
                        $log .= '权限无新增, 不需更新, 跳过: staff_id = ' . $staff_id . PHP_EOL;
                        continue;
                    }
                    $permission_v_ids = implode(',', $permission_id_arr);
                    $sql              = "update staff_permission set `permission_ids`='{$permission_v_ids}',`last_updated_at`='{$last_updated_at}'  where staff_id= {$staff_id}";
                    $bool             = $db_oa->execute($sql);
                    if (empty($bool)) {
                        throw new Exception("权限更新失败: staff_id = $staff_id, permission = $permission_v_ids");
                    }
                    $log .= "权限更新成功: staff_id = $staff_id, permission = $permission_v_ids" . PHP_EOL;
                }
            }
        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;
        } catch (\Exception $e) {
            $log         .= "本批次权限新增/更新异常[已跳过该批次]: " . $e->getMessage() . PHP_EOL;
            $logger_type = 'error';
        }
        $this->logger->$logger_type('level_add_permission' . $log);
        exit($log);
    }


    /**
     * 已有预算查询菜单权限的工号，开通“查询”和“导出”的权限 一次性脚本
     * php cli.php permission save_budget_search_permission
     */
    public function save_budget_search_permissionAction()
    {
        $process_name     = str_replace('Action', '', __FUNCTION__);
        $log              = '任务名称: ' . $process_name . '-- 系统所在国家: ' . get_country_code() . ' --- ' . PHP_EOL;
        $log              .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $last_updated_at  = date('Y-m-d H:i:s', time());
        $logger_type      = 'info';
        $budget_search_id = '291';
        $permission_arr   = [1076, 1077, 1078, 1079, 1082, 1083, 216, 291];
        try {
            //注意此处为291 like查询数据库最大是1097
            $staff_permission = StaffPermissionModel::find(
                ['conditions' => 'FIND_IN_SET (:permission_ids:, permission_ids)',
                 'bind'       => [
                     'permission_ids' => $budget_search_id,
                 ],
                ]);
            $staff_permission_arr = $staff_permission->toArray();
            if (!empty($staff_permission_arr)) {
                foreach ($staff_permission as $staff) {
                    $permission_intersect_ids = array_intersect($permission_arr, explode(',', $staff->permission_ids));
                    $permission_diff_ids      = array_diff($permission_arr, $permission_intersect_ids);
                    if (empty($permission_diff_ids)) {
                        $log .= '权限无新增, 不需更新, 跳过: id = ' . $staff->id . ' staff_id = ' . $staff->staff_id . PHP_EOL;
                        continue;
                    }
                    $permission_v_ids       = implode(',', array_unique(array_merge(explode(',', $staff->permission_ids), $permission_arr)));
                    $staff->permission_ids  = $permission_v_ids;
                    $staff->last_updated_at = $last_updated_at;
                    $bool                   = $staff->save();
                    if ($bool === false) {
                        throw new Exception('权限更新失败: id = ' . $staff->id . ', permission = ' . $permission_v_ids);
                    }
                    $log .= '权限更新成功: id =  ' . $staff->id . ',permission = ' . $permission_v_ids . PHP_EOL;
                }
            }
        } catch (Exception $e) {
            $log         .= '本批次权限更新异常: ' . $e->getMessage() . PHP_EOL;
            $logger_type = 'error';
        }
        $this->logger->$logger_type('level_add_permission' . $log);
        exit($log);
    }


    /**
     * 耗材给总部所有人默认开放申请权限 非总部根据职位开放
     * php cli.php permission add_wms_permission
     */
    public function add_wms_permissionAction()
    {
        $process_name    = str_replace('Action', '', __FUNCTION__);
        $log             = '任务名称: ' . $process_name . ' -- 系统所在国家: ' . get_country_code() . ' -- ' . PHP_EOL;
        $log             .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $last_updated_at = date('Y-m-d H:i:s', time());
        $is_granted      = 1;
        $logger_type     = 'info';
        try {
            $job_ids = json_decode(EnvModel::getEnvByCode('material_wms_open_rule'), true) ?? [];//获取职位
            if (empty($job_ids) || empty($job_ids['job_ids'])) {
                throw new ValidationException('职位不能为空', ErrCode::$VALIDATE_ERROR);
            }
            $permission_arr = json_decode(EnvModel::getEnvByCode('material_wms_permission_ids'), true) ?? [];//获取权限配置id

            if (empty($permission_arr) || empty($permission_arr['permission_ids'])) {
                throw new ValidationException('权限数据不能为空', ErrCode::$VALIDATE_ERROR);
            }

            $staff_data = HrStaffInfoModel::find(
                ['conditions' => '(job_title in ({job_title:array}) or sys_store_id = :sys_store_id: ) and state = :state: and wait_leave_state = :wait_leave_state: and formal = :formal:',
                 'bind'       => [
                     'job_title'        => array_values(array_unique(array_filter(explode(',', $job_ids['job_ids'])))),
                     'sys_store_id'     => Enums::HEAD_OFFICE_STORE_FLAG,
                     'state'            => StaffInfoEnums::STAFF_STATE_IN,
                     'wait_leave_state' => StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_NO,
                     'formal'           => StaffInfoEnums::FORMAL_IN,
                 ],
                 'columns'    => ['staff_info_id', 'sys_store_id', 'job_title'],
                ])->toArray();


            if (empty($staff_data)) {
                throw new ValidationException('对应的职位和总部的人员为空, 请忽略', ErrCode::$VALIDATE_ERROR);
            }
            $staff_id_all         = array_values(array_unique(array_column($staff_data, 'staff_info_id')));
            $staff_permission_arr = StaffPermissionModel::find(
                ['conditions' => 'staff_id in ({staff_id:array})',
                 'bind'       => [
                     'staff_id' => $staff_id_all,
                 ],
                 'columns'    => ['id', 'staff_id', 'permission_ids'],
                ])->toArray();
            $staff_permission_ids = array_column($staff_permission_arr, 'staff_id');
            $diff_staff_ids       = array_diff($staff_id_all, $staff_permission_ids);//差集新增
            $recorded_staff_ids   = array_intersect($staff_permission_ids, $staff_id_all);//交集修改

            if (!empty($diff_staff_ids)) {
                $add_permission = [];
                foreach ($diff_staff_ids as $staff_id) {
                    $add_permission[] = [
                        'staff_id'        => $staff_id,
                        'permission_ids'  => $permission_arr['permission_ids'],
                        'is_granted'      => $is_granted,
                        'last_updated_at' => $last_updated_at,
                    ];
                }

                $res = (new StaffPermissionModel())->batch_insert($add_permission);
                if ($res === false) {
                    throw new Exception('本批次批量新增失败: 员工共 ' . count($add_permission) . '个, 明细: ' . json_encode($add_permission, JSON_UNESCAPED_UNICODE));
                }
                $log .= '本批次批量新增成功 ' . count($add_permission) . ' 个' . PHP_EOL;
            }

            // 需要更新的人
            if (!empty($recorded_staff_ids)) {
                $staff_permission_arr = array_column($staff_permission_arr, null, 'staff_id');
                $db_oa                = $this->getDI()->get('db_oa');
                foreach ($recorded_staff_ids as $staff_id) {
                    // 已有权限 与 待分配权限全集
                    $old_permission_ids = explode(',', $staff_permission_arr[$staff_id]['permission_ids']);
                    $permission_ids     = explode(',', $permission_arr['permission_ids']);
                    $permission_id_arr  = array_unique(array_merge($old_permission_ids, $permission_ids));
                    // 员工本次待追加的权限
                    $permission_diff_ids = array_diff($permission_id_arr, $old_permission_ids);
                    if (!$permission_diff_ids) {
                        $log .= '权限无新增, 不需更新, 跳过: staff_id = ' . $staff_id . PHP_EOL;
                        continue;
                    }
                    //现有的权限+新增的权限
                    $permission_v_ids = implode(',', $permission_id_arr);
                    $sql              = "update staff_permission set `permission_ids`='{$permission_v_ids}', `last_updated_at`='{$last_updated_at}'  where staff_id= {$staff_id}";
                    $bool             = $db_oa->execute($sql);
                    if ($bool === false) {
                        throw new Exception("权限更新失败: staff_id = $staff_id, permission = $permission_v_ids");
                    }
                    $log .= "权限更新成功: staff_id = $staff_id, permission = $permission_v_ids" . PHP_EOL;
                }
            }
        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;
        } catch (\Exception $e) {
            $log         .= '本批次权限新增/更新异常: ' . $e->getMessage() . PHP_EOL;
            $logger_type = 'error';
        }
        $this->logger->$logger_type('add_wms_permission' . $log);
        exit($log);
    }

    /**
     * PH国家
     * 需求文档：https://flashexpress.feishu.cn/docx/JyeydhmWMoZQOlxV1Yjc6rfAnBd
     * 每日为配置的工号、职位ID、所属部门id增加行政工单的-列表、搜索/查看/导出/处理工单的菜单权限（没有关闭工单）
     * 运行命令：php app/cli.php permission add_xz_order_permission
     * 运行周期：每次凌晨五分运行一次
     *
     * @return void
     * @throws Exception
     */
    public function add_xz_order_permissionAction()
    {
        echo 'start'.PHP_EOL;
        $this->logger->info('脚本自动处理工单权限 start');
        TicketListService::getInstance()->autoEditPermission();
        echo 'end';
    }

    /**
     * 导出有OA权限的员工[不等于离职的]
     *
     * php app/cli.php permission export_staff
     */
    public function export_staffAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log = '任务名称: ' . $process_name . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log .= '当前内存: ' . memory_usage() . PHP_EOL;

        try {
            // 获取有OA权限的员工
            $staff_list = StaffPermissionModel::find([
                'columns' => ['staff_id'],
            ])->toArray();

            $log .= '共 ' . count($staff_list) . ' 名员工有权限' . PHP_EOL;
            $log .= '当前内存: ' . memory_usage() . PHP_EOL;

            // 设置系统语言
            static::setLanguage('zh-CN');

            // 过滤掉离职的员工
            $staff_list = array_column($staff_list, 'staff_id');
            $staff_list_chunk = array_chunk($staff_list, 3000);
            unset($staff_list);

            // 职位
            $job_title_list = HrJobTitleModel::find([
                'columns' => ['id', 'job_name'],
            ])->toArray();
            $job_title_list = array_column($job_title_list, 'job_name', 'id');

            $leave_staff = '';
            $all_staff_list = [];
            foreach ($staff_list_chunk  as $chunk) {
                // 获取员工信息
                $trunk_staff_list = (new HrStaffRepository())->getStaffListByStaffIds(array_values(array_filter($chunk)));
                foreach ($chunk as $staff_id) {
                    $staff_info = $trunk_staff_list[$staff_id] ?? [];
                    if (isset($staff_info['state']) && $staff_info['state'] == StaffInfoEnums::STAFF_STATE_LEAVE) {
                        $leave_staff .= $staff_id . ', ';
                        continue;
                    }

                    // 未离职的员工
                    if (empty($staff_info)) {
                        $all_staff_list[] = [
                            $staff_id,
                            '',
                            '',
                            '',
                        ];
                    } else {
                        if ($staff_info['state'] == StaffInfoEnums::STAFF_STATE_IN && $staff_info['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES ) {
                            $staff_info['state'] = StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE;
                        }

                        $all_staff_list[] = [
                            $staff_id,
                            $staff_info['name'],
                            $job_title_list[$staff_info['job_title']] ?? '',
                            static::$t->_(StaffInfoEnums::$staff_state[$staff_info['state']]),
                        ];
                    }
                }
            }
            unset($staff_list_chunk);

            // 组装员工列表
            $excel_header = [
                '工号',
                '姓名',
                '职位',
                '在职状态',
            ];
            $file_name = '有OA权限的员工清单-不含已离职- ' . date('Ymd') . '.xlsx';
            $file_info = (new PermissionService())->exportExcel($excel_header, $all_staff_list, $file_name);
            $log .= '导出结果: '  . $file_info['data'] . PHP_EOL;
            $log .= '已离职的员工: ' . $leave_staff . PHP_EOL;
            $log .= '当前内存: ' . memory_usage() . PHP_EOL;

        } catch (Exception $e) {
            $log .= '脚本异常: ' . $e->getMessage() . PHP_EOL;
        }

        $log .= "结束时间: " . date('Y-m-d H:i:s') . PHP_EOL;
        $this->logger->info('export_permission_staff ' . $log);
        exit($log);
    }


    public function brush_salary_wagesAction()
    {
      
        try {
            $permission_log_id_salary = '182'; //薪酬扣款审批
            $permission_log_id_wage   = '249'; //薪资发放审批
            //现在删除的功能 id
            $permission_log_del_id = [
                182,
                193,
                194,
                195,
                196,
                249,
                250,
                251,
                252,
                253,
            ];

            $out_staff_id = [
                20254,
                23116,
                675806,
                666024,
                668900,
                673964,
                642210,
                678243,
                17348,
                688372,
                614145,];
            if (get_runtime_env() == 'dev') {
                $out_staff_id[] = 19515;
                $out_staff_id[] = 22568;
            }
            //查询需要更新的人
            $builder = $this->modelsManager->createBuilder();
            $column_str = 'w.*';
            $builder->columns($column_str);
            $builder->from(['w' => StaffPermissionModel::class]);
            $sql = "FIND_IN_SET(:permission_log_id_salary:,w.permission_ids) or FIND_IN_SET(:permission_log_id_wage:,w.permission_ids) ";
            $builder->andWhere($sql, [
                'permission_log_id_salary' => $permission_log_id_salary,
                'permission_log_id_wage'   => $permission_log_id_wage,
            ]);
            $builder->andWhere('w.staff_id not in ({staff_id:array})',['staff_id'=>$out_staff_id]);
            $items = $builder->getQuery()->execute()->toArray();
            if (!empty($items)) {
                foreach ($items as $k => $v) {
                    //查询以前的权限
                    $permission = explode(',', $v['permission_ids']);
                    $permission = array_diff($permission, $permission_log_del_id);//把本次需要的权限 剔除
                    $model      = StaffPermissionModel::findFirst([
                        "conditions" => "id = :id:",
                        "bind"       => ["id" => $v['id']],
                    ]);
                    if ($model) {
                        $model->permission_ids = implode(',', $permission);
                        $model->update();
                        $this->logger->info('修改权限id ==> ' . $v['id'] . ' 工号 ==> ' . $v['staff_id'] . '  原权限=>' . $v['permission_ids'] . ' 修改为==>' . $model->permission_ids);
                    }
                }
            }
        } catch (\Exception $e) {
            echo $e->getMessage() . PHP_EOL;
        }

        echo '解决完了';
    }

    //批量给指定职位 开通权限 每天凌晨1点跑一次
    public function approvalOvertimeAction(){
        //批量审批加班的权限id
        $permissionIds = [1310,1311,1312,1313,1314];
        $setting = EnvModel::getEnvByCode('Batch_OT_Positionids');
        $this->logger->info('Batch_OT_Positionids ' . $setting);
        if(empty($setting)){
            return ;
        }
        $setting = explode(',', $setting);

        //对应职位的人 在职 非子账号
        $staffData = HrStaffInfoModel::find([
            'columns' => 'staff_info_id',
            'conditions' => 'state = 1 and is_sub_staff = 0 and job_title in ({ids:array})',
            'bind' => ['ids' => $setting],
        ])->toArray();

        if(empty($staffData)){
            $this->logger->info('staff data empty');
            return ;
        }

        $staffIds = array_column($staffData,'staff_info_id');
        $exist = StaffPermissionModel::find([
            'conditions' => 'staff_id in ({staffs:array})',
            'bind' => ['staffs' => $staffIds],
        ])->toArray();
        $model = new StaffPermissionModel();
        $exist = array_column($exist,'permission_ids', 'staff_id');

        //全是新增 一般不可能出现
        foreach ($staffIds as $staffId){
            if(!isset($exist[$staffId])){
                $row['staff_id'] = $staffId;
                $row['permission_ids'] = implode(',', $permissionIds);
                $row['is_granted'] = 1;
                $clone = clone $model;
                $clone->create($row);
                continue;
            }
            $staffPermission = explode(',', $exist[$staffId]);
            foreach ($permissionIds as $pid){
                if(in_array($pid, $staffPermission)){
                    continue;
                }
                //更新权限
                $staffPermission[] = $pid;
            }
            //排除空
            $staffPermission = array_diff($staffPermission,['']);
            $staffPermission = array_values(array_unique($staffPermission));
            $info = StaffPermissionModel::findFirst("staff_id = {$staffId}");
            $info->update(['permission_ids' => implode(',',$staffPermission)]);

        }
        echo '跑完了';
        return ;
    }

    /**
     * 指定职位 提供供应商+备用金权限
     * @param $params
     * @return void
     */
    public function supplierAndPettyCashAction($params)
    {
        $staffNos = [];
        if (!empty($params[0])) {
            $staffNos = explode(',', $params[0]);
        }
        //供应商管理4-供应商信息382--新建供应商,查询,编辑,查看
        $supplierPermissionIds = [4, 382, 10, 17, 18, 19];
        //网点备用金293 - 备用金申请294--(300,301,302,303,739)  备用金归还298 --(315,316,317,318)
        $pettyCashPermissionIds = [293, 294, 298, 300, 301, 302, 303, 739, 315, 316, 317, 318];

        $conditions = ['formal = 1 AND is_sub_staff = 0 AND state = 1'];
        $bind       = [];
        // 按职位查询
        $setting = EnvModel::getEnvByCode('supplier_petty_cash_job_title_ids');
        if (empty($setting)) {
            return;
        }
        $jobIds         = explode(',', $setting);
        $conditions[]   = 'job_title IN ({jobIds:array})';
        $bind['jobIds'] = $jobIds;
        // 按工号查询
        if (!empty($staffNos)) {
            $staffNoArray = array_filter($staffNos);
            if (!empty($staffNoArray)) {
                $conditions[]     = 'staff_info_id IN ({staffNos:array})';
                $bind['staffNos'] = $staffNoArray;
            }
        }

        $page     = 1;
        $pageSize = 100;
        while (true) {
            // 分页查询符合条件的员工
            $staffs = \App\Models\backyard\HrStaffInfoModel::find([
                'columns' => 'staff_info_id',
                'conditions' => implode(' AND ', $conditions),
                'bind'       => $bind,
                'limit'      => $pageSize,
                'offset'     => ($page - 1) * $pageSize,
            ]);

            if ($staffs->count() == 0) {
                break;
            }

            foreach ($staffs as $staff) {
                // 查询员工现有权限
                $staffPermission = StaffPermissionModel::findFirst([
                    'conditions' => 'staff_id = :staff_id:',
                    'bind'       => ['staff_id' => $staff->staff_info_id],
                ]);

                $permissionIds = [];
                if ($staffPermission) {
                    // 已有权限记录，获取现有权限
                    $permissionIds = array_filter(explode(',', $staffPermission->permission_ids));
                } else {
                    // 无权限记录，创建新记录
                    $staffPermission             = new StaffPermissionModel();
                    $staffPermission->staff_id   = $staff->staff_info_id;
                    $staffPermission->is_granted = 1;
                }

                // 合并现有权限和新权限
                $permissionIds = array_merge($permissionIds, $supplierPermissionIds, $pettyCashPermissionIds);
                $permissionIds = array_unique(array_filter($permissionIds));

                // 更新权限
                $staffPermission->permission_ids  = implode(',', $permissionIds);
                $staffPermission->last_updated_at = date('Y-m-d H:i:s');
                $staffPermission->save();
            }

            $page++;
        }

        echo "权限分配完成\n";
    }

    /**
     * 导出-采购相关权限
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function purchase_selectAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log = '任务名称: ' . $process_name . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $permissionIds = [107,108,109,463,668,804];

        try {
            // 获取有OA权限的员工
            $permission_staff_list = StaffPermissionModel::find([
                'conditions' => 'permission_ids like :permission_ids:',
                'bind' => ['permission_ids' => "%,73,%"],
                'columns' => ['staff_id', 'permission_ids'],
            ])->toArray();

            $log .= '共 ' . count($permission_staff_list) . ' 名员工有权限' . PHP_EOL;
            $log .= '当前内存: ' . memory_usage() . PHP_EOL;

            // 设置系统语言
            static::setLanguage('zh-CN');

            // 过滤掉离职的员工
            $staff_list_to_id = array_column($permission_staff_list, 'permission_ids','staff_id');
            $staff_ids = array_column($permission_staff_list, 'staff_id');

            $permissionInfo = PermissionModel::find([
                'conditions' => 'id in ({ids:array})',
                'bind' => ['ids' => $permissionIds],
                'columns' => ['id', 'name'],
            ])->toArray();
            $permissionInfoToId = array_column($permissionInfo, 'name', 'id');

            // 职位
            $job_title_list = HrJobTitleModel::find([
                'columns' => ['id', 'job_name'],
            ])->toArray();
            $job_title_list = array_column($job_title_list, 'job_name', 'id');

            $department_arr = SysDepartmentModel::find([
                'columns'    => 'id,name',
            ])->toArray();

            $department_arr_to_id = array_column($department_arr, 'name', 'id');

            $all_staff_list = [];

            // 获取员工信息
            $staff_list = (new HrStaffRepository())->getStaffListByStaffIds(array_values(array_filter($staff_ids)));
            foreach ($staff_ids as $staffId) {

                if(!isset($staff_list_to_id[$staffId])) {
                    continue;
                }
                $permission = explode(',', $staff_list_to_id[$staffId]);

                $staff_permission = array_values(array_unique(array_intersect($permission, $permissionIds)));
                if(empty($staff_permission)) {
                    continue;
                }

                if(!isset($staff_list[$staffId])) {
                    continue;
                }
                $staff_info = $staff_list[$staffId] ?? [];
                if ($staff_info['state'] != StaffInfoEnums::STAFF_STATE_IN) {
                    continue;
                }

                foreach ($staff_permission as $onePermission) {
                    if(!isset($permissionInfoToId[$onePermission])) {
                        continue;
                    }
                    $all_staff_list[] = [
                        $staffId,
                        $staff_info['name'],
                        $job_title_list[$staff_info['job_title']] ?? '',
                        $department_arr_to_id[$staff_info['node_department_id']] ?? '',
                        $department_arr_to_id[$staff_info['sys_department_id']] ?? '',
                        $permissionInfoToId[$onePermission],

                    ];
                }
            }

            // 组装员工列表
            $excel_header = [
                '工号',
                '姓名',
                '职位',
                '所属部门',
                '一级部门',
                '菜单',
            ];
            $file_name = get_country_code() .'-采购相关数据查询权限- ' . date('Ymd') . '.xlsx';
            $file_info = (new PermissionService())->exportExcel($excel_header, $all_staff_list, $file_name);
            $log .= '导出结果: '  . $file_info['data'] . PHP_EOL;
            $log .= '当前内存: ' . memory_usage() . PHP_EOL;

        } catch (Exception $e) {
            $log .= '脚本异常: ' . $e->getMessage() . PHP_EOL;
        }

        $log .= "结束时间: " . date('Y-m-d H:i:s') . PHP_EOL;
        $this->logger->info('export_permission_purchase_staff ' . $log);
        exit($log);
    }
}
