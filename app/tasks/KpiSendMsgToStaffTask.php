<?php

use App\Modules\Kpi\Services\ActivityService;
use App\Modules\Kpi\Services\ActivityStaffService;
use App\Models\backyard\KpiActivityStaffModel;
use App\Modules\Kpi\Services\PushService;
use App\Library\Enums\KpiActivityStaffEnums;

/**
 * PPM/PBP提醒员工确认KPI【定时提醒频次：每天, 提醒时间：根据活动配置中的时间进行提醒，运行中周期，每间隔一个小时一次】
 * Class KpiSendMsgToStaffTask
 */
class KpiSendMsgToStaffTask extends BaseTask
{
    /**
     * 根据活动配置的自动提醒时间进行提醒。
     * 寻找当前阶段为员工确认，阶段为待确认的所有员工数据
     */
    public function mainAction()
    {
        try {
            echo date('Ymd H:i:s')." 脚本开始执行".PHP_EOL;
            //第一步找到所有进行中的KPI活动
            $ing_activity_list = ActivityService::getInstance()->getIngActivityList();
            if (empty($ing_activity_list) || !is_array($ing_activity_list)) {
                //没有进行中的KPI活动
                $log = date('Ymd H:i:s').' 暂无进行中的KPI活动'.PHP_EOL;
                echo $log;
                $this->logger->info($log);
                exit("Empty data - Script run completed");
            }

            $cur_hour = date('H');
            $send_notice_list = [];
            // 在所有进行中的活动中，找到自动提醒开关是打开的，且符合当前运行时间的数据
            foreach ($ing_activity_list AS $key  => $value) {
                $json_array = json_decode($value['config'], true);
                if (json_last_error() != JSON_ERROR_NONE) {
                    // json 解析失败
                    continue;
                }
                $staff_auto_remind_array = $json_array['auto_remind']['staff'];
                // 开启了自动提醒，且当前时间正好是提醒的时间 才发送push，否则直接跳过
                if ($staff_auto_remind_array['on-off'] == 1 && $staff_auto_remind_array['time'] == $cur_hour) {
                    $send_notice_list[] = $value;
                }
            }

            // 所有进行的活动中，如果都没有开启自动提醒，或者开启了自动提醒且未到发送周期时，则直接退出脚本
            if (empty($send_notice_list)) {
                $log = date('Ymd H:i:s').' 未找到需要发送的数据信息'.PHP_EOL;
                echo $log;
                $this->logger->info($log);
                exit("No information found - Script run completed");
            }

            //有进行中的KPI活动
            $activity_ids      = array_column($send_notice_list, "id");
            $activity_info_arr = array_column($send_notice_list, null, "id");
            //第二步找到所有当前阶段为员工确认，阶段为待确认的所有员工数据
            $count = $this->getStaffListCount($activity_ids);
            $log   = date('Ymd H:i:s')." PPM/PBP提醒员工确认KPI定时发送消息涉及员工总数为:".$count."个".PHP_EOL;
            $this->logger->info($log);
            echo $log;
            if (0 == $count) {
                //没有进行中的KPI活动
                exit("Confirmation completed - Script run completed");
            }

            //第三步分页查询所有当前阶段为员工确认，阶段为待确认的所有员工数据
            $page_size = 2000;
            $page      = ceil($count / $page_size);
            for ($i = 1; $i <= $page; $i++) {
                $staff_list = $this->getStaffList($activity_ids, $i, $page_size);
                if (empty($staff_list) || !is_array($staff_list)) {
                    // 如果娶不到数据了,没有必要再往下翻页继续获取了，直接退出即可
                    break;
                }
                //给员工发送消息
                $operate_count = $this->batchSendMsgToStaff($staff_list, $activity_info_arr);
                $log           = date('Ymd H:i:s').' PPM/PBP提醒员工确认KPI定时发送消息任务，第'.$i.'页, 每页操作'.$page_size.'条: 成功[共计发送消息条数为:'.$operate_count.'条]'.PHP_EOL;
                echo $log;
                $this->logger->info($log);
            }
        } catch (\Exception $e) {
            $log = date('Ymd H:i:s').'  PPM/PBP提醒员工确认KPI定时发送消息任务: 失败[异常]'.$e->getMessage().PHP_EOL;
            echo $log;
            $this->logger->warning($log);
        }
        echo date('Ymd H:i:s')." 脚本执行结束".PHP_EOL;
    }

    /**
     * 查询当前阶段处于员工确认-待确认员工总数
     * @param array $activity_ids 活动ID组
     * @return int
     */
    private function getStaffListCount(array $activity_ids): int
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns(['count(distinct main.staff_id,main.activity_id) as total']);
        $builder->from(['main' => KpiActivityStaffModel::class]);
        $builder    = $this->getCondition($builder, ['activity_ids' => $activity_ids]);
        $total_info = $builder->getQuery()->getSingleResult();
        return intval($total_info->total);
    }

    /**
     * 组装查询条件
     * @param object $builder 查询器
     * @param array $condition ['activity_ids'=>'进行中的活动ID组']
     * @return mixed
     */
    private function getCondition(object $builder, array $condition)
    {
        $builder->andWhere('main.activity_id in ({activity_ids:array})',
            ['activity_ids' => $condition['activity_ids']]);
        $builder->andWhere('main.stage = :stage:', ['stage' => KpiActivityStaffEnums::STAGE_STAFF]);
        return $builder;
    }

    /**
     * 分页获取当前阶段处于员工确认-待确认员工列表
     * @param array $activity_ids 活动ID组
     * @param int $page 当前页
     * @param int $page_size 每页条数
     * @return mixed
     */
    private function getStaffList(array $activity_ids, int $page, int $page_size)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns(['main.staff_id', 'main.activity_id']);
        $builder->from(['main' => KpiActivityStaffModel::class]);
        $builder = $this->getCondition($builder, ['activity_ids' => $activity_ids]);
        $builder->limit($page_size, $page_size * ($page - 1));
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 给员工发送消息
     * @param array $staff_list 员工列表
     * @param array $activity_info_arr 活动列表
     * @return int
     * @throws Exception
     */
    private function batchSendMsgToStaff(array $staff_list, array $activity_info_arr): int
    {
        $push_service = new PushService();
        $staff_users  = []; //站内信用户组
        foreach ($staff_list as $staff) {
            $push_activity_info = $activity_info_arr[$staff['activity_id']];
            $staff_users[$staff['activity_id']]['ids'][]         = ['id' => $staff['staff_id']];
            $staff_users[$staff['activity_id']]['activity_info'] = $activity_info_arr[$staff['activity_id']];
            $log = "正在给活动ID为：[".$push_activity_info['id']."],员工ID为：[".$staff['staff_id']."]发送push".PHP_EOL;
            $this->logger->info($log);
            echo $log;
            $message_scheme = ActivityStaffService::getInstance()->getStaffMessageScheme($staff['activity_id']);//消息跳转地址
            //发送push
            $push_service->sendPush(
                $staff['staff_id'],
                $push_activity_info,
                KpiActivityStaffEnums::REMIND_STAFF_TITLE,
                KpiActivityStaffEnums::REMIND_STAFF_CONTENT,
                $message_scheme
            );
        }

        $send_count = 0;
        //定制模版内容
        foreach ($staff_users as $key => $users) {
            $log = "正在给活动ID为：[".$key."],批量发送提醒员工确认消息的员工组为：[".implode(",",
                    array_column($users['ids'], "id"))."]发送消息".PHP_EOL;
            $this->logger->info($log);
            echo $log;
            $activity_info = $users['activity_info'];
            $msg_info      = ['years' => $activity_info['years'], 'period' => $activity_info['period']];
            $title         = $push_service->getFormatMsg(KpiActivityStaffEnums::REMIND_STAFF_TITLE, $msg_info);
            //$msg_content = $push_service->getFormatMsg(KpiActivityStaffEnums::REMIND_STAFF_CONTENT, $msg_info, '<br>');
            //按照活动类别批量发送站内信
            $push_service->sendMessage($users['ids'], $title, $key,
                KpiActivityStaffEnums::MESSAGE_CATEGORY_KPI_STAFF_CONFIRM);
            $send_count += count($users['ids']);
        }
        return $send_count;
    }
}