<?php

/**
 * Created by PhpStorm.
 * Date: 2021/11/2
 * Time: 11:03
 */

use App\Library\Validation\ValidationException;
use App\Modules\Common\Services\SmsService;
use App\Modules\Common\Services\ShortUrlService;
use App\Modules\WithholdingTax\Models\WithholdingTax;
use App\Modules\WithholdingTax\Models\WithholdingTaxDetail;
use App\Modules\Common\Services\AmountCapitalizeService;
use App\Modules\WithholdingTax\Services\WithholdingTaxService;
use App\Modules\WithholdingTax\Models\WithholdingTaxTask as WithholdingTaxTaskModel;

class WithholdingtaxTask extends BaseTask
{

    public function deal_withholding_taskAction()
    {
        $this->checkLock(__METHOD__);

        echo 'begin:' . date('Y-m-d H:i:s') . PHP_EOL;
        try {
            //查询
            $task_id = WithholdingTaxTaskModel::findfirst([
                'conditions' => 'status =:status:',
                'bind'       => ['status' => 0],
                'columns'    => 'id',
            ]);

            if (empty($task_id)) {
                throw new ValidationException('无要处理的task');
            }

            //查询withholding_task
            $res = WithholdingTax::find([
                'conditions' => 'task_id = :id: and is_send = 0',
                'bind'       => ['id' => $task_id->id],

            ])->toArray();
            if (!empty($res)) {
                echo 'data total count:' . count($res) . PHP_EOL;
                $i = 0;
                foreach ($res as $key => $value) {
                    $data       = [];
                    $short_ur   = '';
                    $pdf_url    = '';
                    $detail_res = [];
                    $bool       = false;

                    //查询子表
                    $detail       = WithholdingTaxDetail::find([
                        'conditions' => 'main_no = :no:',
                        'bind'       => ['no' => $value['no']],
                    ])->toArray();
                    $pay_time_str = '';
                    foreach ($detail as $k1 => $v2) {
                        if (1 == $v2['wt_type']) {
                            $detail_res[0] = $v2;
                        }

                        if (2 == $v2['wt_type']) {
                            $detail_res[1] = $v2;
                        }
                        $pay_time_str .= ' ' . $v2['pay_time'];
                    }
                    $pay_time_str  = trim($pay_time_str);
                    $value['ho']   = ($value['ho'] == 0) ? 'สำนักงานใหญ่' : '';
                    $value['wt_1'] = ($value['wt'] == 1) ? 'X' : '';
                    $value['wt_2'] = ($value['wt'] == 2) ? 'X' : '';
                    $value['wt_3'] = ($value['wt'] == 3) ? 'X' : '';
                    $value['wt_4'] = ($value['wt'] == 4) ? 'X' : '';

                    $value['pnd_type_1'] = ($value['pnd_type'] == 1) ? 'X' : '';
                    $value['pnd_type_2'] = ($value['pnd_type'] == 2) ? 'X' : '';
                    $value['pnd_type_3'] = ($value['pnd_type'] == 3) ? 'X' : '';
                    $value['pnd_type_4'] = ($value['pnd_type'] == 4) ? 'X' : '';
                    $value['pnd_type_5'] = ($value['pnd_type'] == 5) ? 'X' : '';
                    $value['pnd_type_6'] = ($value['pnd_type'] == 6) ? 'X' : '';
                    $value['pnd_type_7'] = ($value['pnd_type'] == 7) ? 'X' : '';

                    $value['total_amount']    = bcdiv(bcadd(bcmul($detail_res[0]['amount'] ?? 0, 100) ?? 0,
                        bcmul($detail_res[1]['amount'] ?? 0, 100) ?? 0), 100, 2);
                    $value['total_wt_amount'] = bcdiv(bcadd(bcmul($detail_res[0]['wt_amount'] ?? 0, 100) ?? 0,
                        bcmul($detail_res[1]['wt_amount'] ?? 0, 100) ?? 0), 100, 2);
                    //手工填写字段
                    $value['wt_amount_txt'] = $value['capital_letters'];

                    $value['total_amount']    = number_format($value['total_amount'], 2, '.', ',');
                    $value['total_wt_amount'] = number_format($value['total_wt_amount'], 2, '.', ',');


                    if (isset($detail_res[0])) {
                        $detail_res[0]['amount']    = number_format($detail_res[0]['amount'], 2, '.', ',');
                        $detail_res[0]['wt_amount'] = number_format($detail_res[0]['wt_amount'], 2, '.', ',');
                    }

                    if (isset($detail_res[1])) {
                        $detail_res[1]['amount']    = number_format($detail_res[1]['amount'], 2, '.', ',');
                        $detail_res[1]['wt_amount'] = number_format($detail_res[1]['wt_amount'], 2, '.', ',');
                    }

                    $value['detail'] = $detail_res;

                    //生产pdf
                    $pdf_url   = WithholdingTaxService::getInstance()->createPdf($value);
                    $short_url = '';
                    if (!empty($pdf_url)) {
                        $short_url = ShortUrlService::getInstance()->getShortUrl($pdf_url);
                    }

                    //发送短信
                    $msg = WithholdingTaxService::MSG_CONTENT . $short_url;
                    if (!empty($short_url) && !empty($value['mobile'])) {
                        $bool = SmsService::getInstance()->send(['msg' => $msg, 'mobile' => $value['mobile']],
                            'oa_withholding_tax_notice');
                    }

                    // email
                    if (!empty($value['email'])) {
                        //单号 客户名称 付款日期
                        $email_data = $value['no'] . '  ' . $pay_time_str;
                        $email      = explode(',', $value['email']);
                        $title      = 'Flash Express ใบหัก ณ ที่จ่าย ' . $email_data . '  ' . $value['supplier_name'];
                        //邮件最大限制120个字符,超长课程出现乱码
                        $title = mb_substr($title, 0, 121, 'UTF-8');
                        foreach ($email as $k3 => $v3) {
                            $v3 = trim($v3);
                            if (empty($v3) || !filter_var($v3, FILTER_VALIDATE_EMAIL)) {
                                echo '邮箱地址: ' . $v3 . ' 异常, 邮件未发' . PHP_EOL;
                                continue;
                            }

                            $this->mailer->sendAsync($v3, $title,
                                $this->format_html($short_url, $value['supplier_name'], $email_data));
                        }
                    }

                    //更新
                    $withholding_tax = WithholdingTax::findFirst([
                        'conditions' => 'id = :id:',
                        'bind'       => ['id' => $value['id']],
                    ]);


                    if ($bool) {
                        $i++;

                        $withholding_tax->is_send   = 1;
                        $withholding_tax->short_url = $short_url ?? '';
                        $withholding_tax->pdf_url   = $pdf_url;

                        $withholding_tax->send_time = date('Y-m-d H:i:s');

                        $withholding_tax->save();
                    }
                }
                $task_model = WithholdingTaxTaskModel::findFirst(
                    [
                        'conditions' => 'id = :id:',
                        'bind'       => ['id' => $task_id->id],
                    ]
                );

                $task_model->status     = 1;
                $task_model->updated_at = date('Y-m-d H:i:s');
                $task_model->save();

                echo '处理成功' . $i . '条数';
            } else {
                $task_id->status = 1;
                $task_id->save();
            }
        } catch (ValidationException $e) {
            $this->logger->info('withholding-tax-send-validation: ' . $e->getMessage());
            echo $e->getMessage() . PHP_EOL;
        } catch (Exception $e) {
            $this->logger->warning('withholding-tax-send-exception: ' . $e->getMessage());
            echo $e->getMessage() . PHP_EOL;
        }

        $this->clearLock(__METHOD__);
        echo 'end :' . date('Y-m-d H:i:s') . PHP_EOL;
        exit();
    }


    protected function format_html($url, $customer_name, $email_data)
    {
        $html = "<p>Dear " . $customer_name . "</p>";
        $html .= "<p>Flash Express นำส่ง ใบหัก ณ ที่จ่าย ของคุณสามารถดาวน์โหลดที่ Flash Express นำส่ง ใบหัก ณ ที่จ่ายเลขที่ " . $email_data . "</p>";
        $html .= "<p>ดาวน์โหลด" . $url . "</p>";
        return $html;
    }


}

