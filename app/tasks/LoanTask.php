<?php

use App\Library\Validation\ValidationException;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Loan\Models\Loan;
use App\Modules\User\Models\HrStaffItemsModel;
use App\Modules\User\Models\StaffExpenseModel;
use App\Modules\User\Models\StaffExpenseLogModel;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Organization\Models\HrStaffInfoModel;
use App\Modules\Loan\Services\LoanExportService;
use App\Library\Enums\StaffInfoEnums;
use App\Modules\Training\Services\TaskService;
use App\Library\Enums\GlobalEnums;
use App\Modules\Loan\Services\SapService;
use App\Modules\OrdinaryPayment\Services\SapService as oSapService;
use App\Modules\Organization\Models\SysDepartmentModel;
use App\Library\Enums;
use App\Library\Enums\LoanEnums;
use App\Models\oa\LoanReturnModel;
use App\Library\BaseService;
use App\Modules\Workflow\Models\WorkflowRequestModel;
use App\Modules\Workflow\Models\WorkflowRequestNodeAuditorModel;
use App\Repository\HrStaffRepository;
use App\Library\ApiClient;
use App\Library\ErrCode;
use App\Modules\ReserveFund\Models\ReserveFundApply;

class LoanTask extends BaseTask
{

    /**
     *每天7点执行，统计预计还款时间是昨天的借款金额
     *
     */
    public function add_staff_expenseAction(){
        $today = date("Y-m-d");     //小于


        $this->myLogger("loan-add_staff_expense-$today-start:");


        $yesterday = date("Y-m-d",strtotime("-1 days"));//大于等于


        $db =$this->getDI()->get("db_oa");

        $sql = "SELECT
    id,create_id,amount
FROM
	loan 
WHERE
	pay_status = 2 
	AND back_at >= :start
	AND back_at < :end
UNION 
SELECT 
id,create_id,amount 
FROM 
	loan 
WHERE 
	pay_status = 2 
	AND updated_at >= :start
	AND updated_at < :end
	AND back_at < updated_at";

        $loans = $db->fetchAll($sql,\Phalcon\Db::FETCH_ASSOC,["start"=>$yesterday,"end"=>$today]);
        if(empty($loans)){
            $this->myLogger( "loan-add_staff_expense-end-$today:no data on >=".$yesterday." and <".$today);
            return;
        }

        $staffAmount = [];

        $nowtime = date("Y-m-d H:i:s");
        try {
            $db->begin();
            foreach ($loans as $loan){
                $log = [];
                $log['src_id'] = $loan['id'];
                $log['staff_info_id'] = $loan['create_id'];
                $log['type'] = 1;
                $log['amount'] = $loan['amount'];
                $log['created_at'] = $nowtime;
                $log['created_date'] = $yesterday;

                $log_item = StaffExpenseLogModel::findFirst([
                    'conditions'=>"staff_info_id=:id: and type=1 and src_id=:src_id:",
                    'bind'=>['id'=>$log['staff_info_id'],"src_id"=>$log['src_id']]
                ]);

                //这个人这个借款id统计过，不能重复统计
                if(!empty($log_item)){
                    $this->myLogger( "借款统计=".$log['staff_info_id']."在".$yesterday."统计过，跳过");
                    continue;
                }

                if(empty($staffAmount[$log['staff_info_id']])){
                    $staffAmount[$log['staff_info_id']] = 0;
                }
                $staffAmount[$log['staff_info_id']] = bcadd($staffAmount[$log['staff_info_id']],$log['amount']);

                (new \App\Modules\User\Models\StaffExpenseLogModel())->save($log);
            }


            foreach ($staffAmount as $staff_info_id=>$amount){
                $item = StaffExpenseModel::findFirst([
                    'conditions'=>"staff_info_id=:id:",
                    'bind'=>["id"=>$staff_info_id]
                ]);

                if(empty($item)){
                    $data = [];
                    $data['staff_info_id'] = $staff_info_id;
                    $data['loan_amount'] = $amount;
                    $data['re_amount'] = 0;
                    $data['created_at'] = $nowtime;
                    $data['updated_at'] = $nowtime;

                    (new StaffExpenseModel())->save($data);
                }else{
                    $item->loan_amount = bcadd($item->loan_amount,$amount);
                    $item->save();
                }
            }

            $db->commit();
        }catch (Exception $e){
            $this->myLogger("借款统计=在".$yesterday."执行失败==".$e->getMessage(),1);
            $db->rollback();
        }

        $this->myLogger( "loan-add_staff_expense-end-$today: success==>=".$yesterday." and <".$today);
    }

    public function myLogger($str,$flag=0){
        echo "loanTask==".$str.PHP_EOL;
        if(empty($flag)){
            $this->logger->info($str);
        }else{
            $this->logger->warning($str);
        }
    }


    /**
     * 16871需求改功能废弃
     * 给借款快到还款时间--发送邮件提醒
     */

    public function emailAction(){
        $ten_days = date("Y-m-d",strtotime("-10 days"));
        $five_days = date("Y-m-d",strtotime("-5 days"));
        $now = date("Y-m-d");


        try {
            $loans = Loan::find(
                [
                    'conditions' => 'pay_status = :pay_status: and back_status = :back_status: and back_at in ({date:array})',
                    'bind' => [
                        'pay_status' => \App\Library\Enums::LOAN_PAY_STATUS_PAY,
                        'back_status' => \App\Library\Enums::LOAN_BACK_STATUS_NOT,
                        'date' => [$ten_days, $five_days, $now]
                    ]
                ]
            )->toArray();

            if (empty($loans)) {
                $this->myLogger('email==not found loans');
                exit;
            }


            $th = \App\Library\BaseService::getTranslation('th');
            $en = \App\Library\BaseService::getTranslation('en');
            $cn = \App\Library\BaseService::getTranslation('zh-CN');

            $staff_ids = array_column($loans,"create_id");

            $staffIdToEmail = (new \App\Modules\User\Models\StaffInfoModel())->getUserIdToEmail($staff_ids);

            foreach ($loans as $item) {
                $email = $staffIdToEmail[$item['create_id']] ?? '';

                if(env('runtime') == 'dev'){
                    $email = ['<EMAIL>','<EMAIL>'];
                }
                if(empty($email)){
                    $this->myLogger('email==staff_id=='.$item['create_id']."===not found email");
                }

                $this->myLogger("Email Info: staff_id = {$item['create_id']}, email = {$email}");

                $titleArr = [];
                $titleArr['no'] = $item['lno'];
                if ($item['back_at'] == $now) {
                    //$title = '您的借款-' . $item['lno'] . '在今天到期';
                    $cn_title = $cn->_("loan_back_title_1",$titleArr);
                    $th_title = $th->_("loan_back_title_1",$titleArr);
                    $en_title = $en->_("loan_back_title_1",$titleArr);
                } else {
                    if($item['back_at'] == $ten_days){
                        $titleArr['day'] = 10;
                    }else{
                        $titleArr['day'] = 5;
                    }
                    $cn_title = $cn->_("loan_back_title_0",$titleArr);
                    $th_title = $th->_("loan_back_title_0",$titleArr);
                    $en_title = $en->_("loan_back_title_0",$titleArr);
                }

                $title = $cn_title.'|'.$th_title."|".$en_title;


                $content = <<<EOF
    hi,<br>
        {$cn_title}，{$cn->_("loan_back_where")}<br/>
        {$th_title},{$th->_("loan_back_where")}<br/>
        {$en_title},{$en->_("loan_back_where")}<br/>
EOF;


                if ($this->mailer->send($email, $title, $content)) {
                    $this->myLogger('send success');
                } else {
                    $this->myLogger('send fail');
                }
            }
        }catch (Exception $e){
            $this->myLogger('email error=='.$e->getMessage(),1);
        }
    }

    /**
     * 16871需求改功能废弃
     * 每月整理未归还借款单提醒邮件--发送邮件提醒
     */
    public function send_loan_data_per_monthAction(){
        // 上个月第一天
        $last_month_first = date('Y-m-d 00:00:00', strtotime(date('Y-m-01') . ' -1 month')); // 计算出本月第一天再减一个月
        //　上个月最后一天:
        $last_month_last = date('Y-m-d 23:59:59', strtotime(date('Y-m-01') . ' -1 day')); // 计算出本月第一天再减一天

        try {
            //取所有申请状态=已通过、支付状态=已支付、归还状态=未归还借款
            $noback_data = Loan::find(
                [
                    'conditions' => 'status = :status: and pay_status = :pay_status: and back_status = :back_status:',
                    'bind' => [
                        'status' => \App\Library\Enums::ORDINARY_PAYMENT_APPROVAL_STATUS_PASSED,
                        'pay_status' => \App\Library\Enums::LOAN_PAY_STATUS_PAY,
                        'back_status' => \App\Library\Enums::LOAN_BACK_STATUS_NOT
                    ]
                ]
            )->toArray();

            //取上月已归还借款数据
            $last_month_data = Loan::find(
                [
                    'conditions' => 'status = :status: and pay_status = :pay_status: and back_status = :back_status: 
                    and (back_approved_at between :start: and :end:)',
                    'bind' => [
                        'status' => \App\Library\Enums::ORDINARY_PAYMENT_APPROVAL_STATUS_PASSED,
                        'pay_status' => \App\Library\Enums::LOAN_PAY_STATUS_PAY,
                        'back_status' => \App\Library\Enums::LOAN_BACK_STATUS_BACK,
                        'start' => $last_month_first,
                        'end' => $last_month_last
                    ]
                ]
            )->toArray();
            $total_loan_data = array_merge($noback_data,$last_month_data);
            if (empty($total_loan_data)) {
                $this->myLogger('上个月未归还借款数据为空');
                exit;
            }

            // 生成excel借款数据表格
            $filename = 'load_export_'.date('Y-m-d');
            $obj = LoanExportService::getInstance();
            $obj->setLanguage('en');
            $result = $obj->exportLoanData($filename,$total_loan_data);
            if (!$result) {
                $this->myLogger('导入借款数据生成Excel失败');
                exit;
            }

            //取配置中的发送人
            $emails = EnvModel::getEnvByCode('loan_data_send_email','');
            if (empty($emails)) {
                $this->myLogger('未配置邮箱或者配置的邮箱地址为空');
                exit;
            }
            $emails = array_values(array_filter(explode(',',$emails)));
            //发送邮件并记录日志
            $title = 'Loan Data（借款数据）';
            $content = <<<EOF
    Hi All，<br><br>
        Please check the attachment as the loan data in OA system.<br/>
        They are outstanding or returned applications in this month.<br/>
        Thanks!<br/>
        请检查附件借款数据。该部分数据是系统的未归还清单以及在本月归还的数据。谢谢！<br/>
EOF;
            if ($this->mailer->send($emails, $title, $content,[$result['data']])) {
                $this->myLogger('send success');
            } else {
                $this->myLogger('send fail');
            }

        }catch (Exception $e){
            $this->myLogger('email error=='.$e->getMessage(),1);
        }
    }

    /**
     * 16871需求改功能废弃
     * 每天发送申请状态=已通过、支付状态=已支付、归还状态=未归还 且 离应归还日期超过7日的借款申请
     */
    public function send_loan_data_per_dayAction(){
        //　7天前:
        $last_seven_last = date('Y-m-d', strtotime(date('Y-m-d') . ' -7 day')); // 计算出本月第一天再减一天

        try {
            //取所有申请状态=已通过、支付状态=已支付、归还状态=未归还借款
            $noback_data = Loan::find(
                [
                    'conditions' => 'status = :status: and pay_status = :pay_status: and back_status = :back_status: and back_at < :back_at:',
                    'bind' => [
                        'status' => \App\Library\Enums::ORDINARY_PAYMENT_APPROVAL_STATUS_PASSED,
                        'pay_status' => \App\Library\Enums::LOAN_PAY_STATUS_PAY,
                        'back_status' => \App\Library\Enums::LOAN_BACK_STATUS_NOT,
                        'back_at' => $last_seven_last
                    ]
                ]
            )->toArray();
            if (empty($noback_data)) {
                $this->myLogger('离应归还日期超过7日未归还借款数据为空');
                exit;
            }

            // 生成excel借款数据表格
            $filename = 'load_export_'.date('Y-m-d');
            $obj = LoanExportService::getInstance();
            $obj->setLanguage('en');
            $result = $obj->exportLoanData($filename,$noback_data);
            if (!$result) {
                $this->myLogger('导入借款数据生成Excel失败');
                exit;
            }

            //取配置中的发送人
            $emails = EnvModel::getEnvByCode('loan_data_send_email','');
            if (empty($emails)) {
                $this->myLogger('未配置邮箱或者配置的邮箱地址为空');
                exit;
            }
            $emails = array_values(array_filter(explode(',',$emails)));
            //发送邮件并记录日志
            $title = 'Loan Return Data（未借款归还数据）';
            $content = <<<EOF
    Hi All，<br><br>
        Please check the attachment as the loan data in OA system. <br>
        They are loan applications  more than 7 days from the due date of return.<br>
        Thanks!<br>
        请查收附件借款数据，该数据是离应归还日期已超过7日的借款申请。谢谢！<br>
EOF;
            if ($this->mailer->send($emails, $title, $content,[$result['data']])) {
                $this->myLogger('send success');
            } else {
                $this->myLogger('send fail');
            }

        }catch (Exception $e){
            $this->myLogger('email error=='.$e->getMessage(),1);
        }
    }

    /**
     * 16871需求改功能废弃
     * 申请人未归还邮箱提醒
     */
    public function send_loan_data_to_applyAction(){
        // 当天时间前7天
        $before_seven_day = date('Y-m-d', strtotime(date('Y-m-d') . ' -7 day')); // 当天前七天

        try {
            //取所有申请状态=已通过、支付状态=已支付、归还状态=未归还借款
            $noback_data = Loan::find(
                [
                    'conditions' => 'status = :status: and pay_status = :pay_status: and back_status = :back_status: and back_at < :back_at:',
                    'bind' => [
                        'status' => \App\Library\Enums::ORDINARY_PAYMENT_APPROVAL_STATUS_PASSED,
                        'pay_status' => \App\Library\Enums::LOAN_PAY_STATUS_PAY,
                        'back_status' => \App\Library\Enums::LOAN_BACK_STATUS_NOT,
                        'back_at' => $before_seven_day
                    ]
                ]
            )->toArray();
            if (empty($noback_data)) {
                $this->myLogger('离应归还日期超过7天的未归还借款数据为空');
                exit;
            }

            // 生成excel借款数据表格
            $filename = 'load_export_'.date('Y-m-d');
            $obj = LoanExportService::getInstance();
            $obj->setLanguage('en');
            $result = $obj->exportLoanData($filename,$noback_data);
            if (!$result) {
                $this->myLogger('导入借款数据生成Excel失败');
                exit;
            }

            //借款申请人
            $create_ids = array_filter(array_column($noback_data,'create_id','id'));
            $create_emails = array_filter(array_column($noback_data,'create_email','id'));
            $create_companies = array_column($noback_data,'create_company_id','id');
            if (empty($create_ids) || empty($create_emails)) {
                $this->myLogger('收件人为空');
                exit;
            }

            //借款申请人直接上级id
            $builder = $this->modelsManager->createBuilder();
            $builder->columns(['main.value,main.staff_info_id']);
            $builder->from(['main' => HrStaffItemsModel::class]);
            $builder->leftJoin(HrStaffInfoModel::class, 'main.staff_info_id=info.staff_info_id', 'info');
            $builder->andWhere('main.item = :item:',[
                'item' => 'MANGER'
            ]);
            $builder->inWhere('main.staff_info_id',array_values($create_ids));
            $director_ids = $builder->getQuery()->execute()->toArray();
            $director_staff_ids = !empty($director_ids) ? array_column($director_ids,'value','staff_info_id') : [];
            $director_ids = !empty($director_ids) ? array_unique(array_filter(array_column($director_ids,'value'))) : [];

            $director_emails = [];
            if (!empty($director_ids)) {
                $tmp_director_ids = [];
                foreach ($director_ids as $id) {
                    $tmp_director_ids = array_filter(array_merge($tmp_director_ids,explode(',',$id)));
                }
                $director_emails = HrStaffInfoModel::find([
                    'conditions' => 'staff_info_id in({ids:array}) and state = :state:',
                    'bind' => [
                        'ids' => $tmp_director_ids,
                        'state' => StaffInfoEnums::STAFF_STATE_IN
                    ]
                ])->toArray();
                $director_emails = array_filter(array_column($director_emails,'email','staff_info_id'));
            }

            // 银行详情
            $bank_info = EnvModel::getEnvByCode('email_content_bank_info','');

            // 邮箱详情
            $email_info = EnvModel::getEnvByCode('email_content_send_email','');

            //发送邮件并记录日志
            $title = 'Reminder:Outstanding Loan  แจ้งเตือน：ชำระคืนเงินกู้';
            $unique_email = [];
            foreach ($create_ids as $id => $employ_id) {
                if (!isset($create_emails[$id]) || empty($create_emails[$id]) || in_array($create_emails[$id],$unique_email)) {
                    continue;
                }
                $unique_email[] = $create_emails[$id];
                $bank_email_info = '';
                // 是否是当前国家的快递公司
                $sys_company_ids = EnumsService::getInstance()->getSysDepartmentCompanyIds(true);
                $express_company_id = $sys_company_ids['FlashExpress'] ?? '';
                if (isset($create_companies[$id]) &&
                    $express_company_id == $create_companies[$id]) {
                    $bank_email_info = <<<EOF
【below  the bank information.  ข้อมูลบัญชีธนาคารด้านล่าง：<br>
        {$bank_info}
        Regards,<br>
        {$email_info}】<br/>
EOF;
                }

                $content = <<<EOF
    Dear {$employ_id},<br><br>
        This is a reminder that your loan reference# is exceeded the 7 days permitted loan cycle.<br><br>
        The company has the right to directly deduct on your salaries if you are unable to liquidate/return your loan within 3 days. <br><br>
        If you have already liquidate/return, please forward proof of payment to finance mailbox. If not, kindly settle promptly.<br><br>
        คุณมีรายการกู้เงินค้างชำระเกินกำหนด 7 วัน กรุณาดำเนินการชำระโดยด่วน<br><br>
        ทั้งนี้หากภายใน 3 วัน ยังไม่ดำเนินการชำระ บริษัทมีอำนาจในการหักยอดค้างชำระจากเงินเดือน<br><br>
        หากทำการชำระแล้ว รบกวนแนบหลักฐานการชำระเงินและส่งอีเมลมายังฝ่ายการเงิน ขอบคุณสำหรับความร่วมมือ<br><br>
        {$bank_email_info}
EOF;
                $director_email = (isset($director_staff_ids[$employ_id]) && isset($director_emails[$director_staff_ids[$employ_id]]) &&
                !empty($director_emails[$director_staff_ids[$employ_id]])) ? [$director_emails[$director_staff_ids[$employ_id]]] : [];
                try {
                    if ($this->mailer->sendAsync($create_emails[$id], $title, $content,[],$director_email)) {
                        $this->myLogger('邮箱地址：'.$create_emails[$id].' send success');
                    } else {
                        $this->myLogger('邮箱地址：'.$create_emails[$id].' send fail');
                    }
                } catch (Exception $e){
                    $this->myLogger('email error=='.$e->getMessage());
                }
            }
        }catch (Exception $e){
            $this->myLogger('email error=='.$e->getMessage(),1);
        }
    }


    /**
     * 借款申请发送sap
     * */
    public function send_order_sapAction()
    {
        $this->checkLock(__METHOD__, 10800);
        echo 'begin: ' . date('Y-m-d H:i:s') . PHP_EOL;

        try {
            //purchase_sap_company_ids 只有配置了这个才会启动脚本 目前开通sap 国家 泰国菲律宾马来
            $cost_company_id = EnumsService::getInstance()->getSettingEnvValueIds('purchase_sap_company_ids');
            if (empty($cost_company_id)) {
                throw new ValidationException('purchase_sap_company_ids 未配置', ErrCode::$VALIDATE_ERROR);
            }

            $sap_company_ids = SysDepartmentModel::find(
                [
                    'conditions' => 'id in ({ids:array})',
                    'bind'       => ['ids' => $cost_company_id]
                ]
            )->toArray();
            $cost_company_ids = implode(',', $cost_company_id);

            echo 'purchase_sap_company_ids: ' . $cost_company_ids . PHP_EOL;

            $sap_company_ids = array_column($sap_company_ids, 'sap_company_id', 'id') ?? [];

            $default_currency = EnumsService::getInstance()->getSysCurrencyInfo();
            $t                = TaskService::getTranslation(get_country_code());

            $start_time = date('Y-m-01');
            //计算本月日期1-指定日期 只执行上个月 11-31号只执行本月数据
            $start_date     = date('Y-m-01 00:00:00');
            $end_date       = date("Y-m-{$this->getSapTaskSendDate()} 00:00:00");
            $is_little_date = true;

            if (date('Y-m-d H:i:s') >= $start_date && date('Y-m-d H:i:s') < $end_date) {
                $end_time   = $start_date;
                $start_time = date('Y-m-01', strtotime(date('Y-m-01') . ' -1 month'));
            }

            $last_day   = oSapService::getInstance()->getLastDay(ltrim(date('m')), ltrim(date('Y')));
            $end_date_2 = date('Y-m-d H:i:s', (strtotime($start_date) + 3600 * 24 * $last_day));
            if (date('Y-m-d H:i:s') >= $end_date && date('Y-m-d H:i:s') < $end_date_2) {
                $start_time     = $start_time;
                $end_time       = date('Y-m-d', strtotime('+1 day'));
                $is_little_date = false;
            }


            echo 'is_little_date: ' . ($is_little_date ? 'Yes' : 'No') . PHP_EOL;
            echo "loan_pay.pay_date 时间范围: start_time-{$start_time},  end_time-{$end_time}" . PHP_EOL;

            $db_obj     = $this->db_oa;

            $send_data = array();
            //发送邮件 目前只有泰国
            $email = EnvModel::getEnvByCode('purchase_email');
            $email = explode(',', $email);

            $i = 0;

            $sql = "select a.id,a.lno,a.create_id,a.create_date,a.cost_center_name,a.amount,a.currency,a.create_company_id,b.pay_transaction_date,b.pay_date from loan as a left join loan_pay as b on a.id = b.loan_id where  a.currency = '{$default_currency['code']}'  and a.create_company_id  in ({$cost_company_ids}) and a.pay_status= 2 and b.pay_date>= '{$start_time}' and b.pay_date< '{$end_time}' and a.sync_sap in (0,3)  limit {$i} ,100";

            $data          = $db_obj->query($sql);
            $deal_sap_data = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            $x             = 0;
            while (!empty($deal_sap_data)) {
                foreach ($deal_sap_data as $key => $value) {
                    echo "当前待处理单号: {$value['lno']}" . PHP_EOL;
                    echo 'loan id:' . $value['id'] . PHP_EOL;

                    echo "实际支付时间: {$value['pay_date']}" . PHP_EOL;

                    $request_data = [];
                    $return_data  = [];

                    // 1-指定日期不是上个月数据跳过  11-31不是本月数据跳过
                    if ($is_little_date) {
                        if (ltrim(date('m', strtotime($value['pay_date']))) == ltrim(date('m'))) {
                            echo '1-10, 非上月, 跳过' . PHP_EOL;
                            continue;
                        }
                    } else {
                        if (ltrim(date('m', strtotime($value['pay_date']))) != ltrim(date('m'))) {
                            echo '11-31, 非本月, 跳过' . PHP_EOL;
                            continue;
                        }
                    }

                    $item         = Loan::getFirst([
                        'id = :id:',
                        'bind' => ['id' => $value['id']]
                    ]);

                    if (empty($item)) {
                        echo '单据不存在, 跳过' . PHP_EOL;
                        continue;
                    }

                    $request_data = [
                        'currency'             => $t->_(GlobalEnums::$currency_item[$value['currency']]),
                        'cost_company_id'      => $sap_company_ids[$value['create_company_id']] ?? 'FEX01',
                        'lno'                  => $value['lno'],
                        'create_id'            => $value['create_id'],
                        'create_date'          => $value['create_date'],
                        'cost_center_name'     => $value['cost_center_name'],
                        'amount'               => bcdiv($value['amount'], 1000, 2),
                        'pay_transaction_date' => empty($value['pay_transaction_date']) ? $value['pay_date'] : $value['pay_transaction_date'],
                    ];

                    $z            = 0;
                    while (empty($return_data) && $z < 1) {
                        $z++;
                        $return_data = SapService::getInstance()->loanSap($request_data);
                        if (isset($return_data['SAP_UUID']) && !empty($return_data['SAP_UUID'])) {
                            echo '抛数成功, SAP_UUID=' . $return_data['SAP_UUID'] . PHP_EOL;

                            $update_res = $item->i_update(['sync_sap' => 1, 'sap_uuid' => $return_data['SAP_UUID'] ?? '', 'updated_at' => date('Y-m-d H:i:s')]);
                            $x++;
                        } else {
                            $note = '';
                            if (isset($return_data['log']['Item']) && !empty($return_data['log']['Item'])) {
                                $note = array_column($return_data['log']['Item'], 'Note');
                                $note = implode(',', $note);

                            }

                            echo '抛数失败, sap_note=' . $note . PHP_EOL;

                            $update_res = $item->i_update(['sync_sap' => 2, 'updated_at' => date('Y-m-d H:i:s'), 'sap_note' => $note]);
                        }

                        echo '抛数结果DB状态更新: ' . ($update_res === false ? '失败' : '成功') . PHP_EOL;

                        sleep(2);
                    }

                    echo PHP_EOL;
                }

                sleep(1);
                $i   += 100;
                $sql = "select a.id,a.lno,a.create_id,a.create_date,a.cost_center_name,a.amount,a.currency,a.create_company_id,b.pay_transaction_date,b.pay_date from loan as a left join loan_pay as b on a.id = b.loan_id where  a.currency = '{$default_currency['code']}'  and a.create_company_id  in ({$cost_company_ids}) and a.pay_status= 2 and  b.pay_date>='{$start_date}' and a.sync_sap in (0,3)  limit {$i} ,100";

                $data          = $db_obj->query($sql);
                $deal_sap_data = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            }

            echo 'loan 成功:' . $x . '条数' . PHP_EOL;

        } catch (ValidationException $e) {
            $this->logger->notice('loan_sap_task_validation:' . $e->getMessage());
            echo $e->getMessage() . PHP_EOL;
        } catch (Exception $e) {
            $this->logger->warning('loan_sap_task_exception:' . $e->getMessage());
            echo $e->getMessage() . PHP_EOL;
        }

        echo 'end: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $this->clearLock(__METHOD__);
        exit();
    }

    /**
     * 借款归还发送sap
     * @return bool
     */
    public function loan_reback_sapAction()
    {
        $this->checkLock(__METHOD__, 7200);
        echo 'begin: ' . date('Y-m-d H:i:s') . PHP_EOL;

        try {
            //purchase_sap_company_ids 只有配置了这个才会启动脚本 目前开通sap 国家 泰国菲律宾马来
            $cost_company_id = EnumsService::getInstance()->getSettingEnvValueIds('purchase_sap_company_ids');
            if (empty($cost_company_id)) {
                throw new ValidationException('purchase_sap_company_ids 未配置', ErrCode::$VALIDATE_ERROR);
            }

            echo 'purchase_sap_company_ids: ' . (implode(',', $cost_company_id)) . PHP_EOL;

            //获取配置的公司对应的sap公司信息
            $sap_company_ids  = SysDepartmentModel::find([
                'conditions' => 'id in ({ids:array})',
                'bind'       => ['ids' => $cost_company_id]
            ])->toArray();
            $sap_company_ids  = array_column($sap_company_ids, 'sap_company_id', 'id') ?? [];
            $default_currency = EnumsService::getInstance()->getSysCurrencyInfo();

            $country_code = get_country_code();//国家码
            $t = TaskService::getTranslation($country_code);

            //计算本月日期1-指定日期 只执行上个月 11-31号只执行本月数据
            $start_date     = date('Y-m-01 00:00:00');
            $end_date       = date("Y-m-{$this->getSapTaskSendDate()} 00:00:00");
            $is_little_date = true;

            if (date('Y-m-d H:i:s') >= $start_date && date('Y-m-d H:i:s') < $end_date) {
                $end_time   = $start_date;
                $start_time = date('Y-m-01', strtotime(date('Y-m-01') . ' -1 month'));
            }

            $last_day   = oSapService::getInstance()->getLastDay(ltrim(date('m')), ltrim(date('Y')));
            $end_date_2 = date('Y-m-d H:i:s', (strtotime($start_date) + 3600 * 24 * $last_day));
            if (date('Y-m-d H:i:s') >= $end_date && date('Y-m-d H:i:s') < $end_date_2) {
                $start_time     = $start_date;
                $end_time       = date('Y-m-d', strtotime('+1 day'));
                $is_little_date = false;
            }

            echo 'is_little_date: ' . ($is_little_date ? 'Yes' : 'No') . PHP_EOL;
            echo "loan_return.back_date 时间范围: start_time-{$start_time},  end_time-{$end_time}" . PHP_EOL;

            $i = 0;
            $x = 0;
            $deal_sap_data = $this->getLoanRebackSapList($country_code, $default_currency['code'], $cost_company_id, $i, $start_time, $end_time);
            while (!empty($deal_sap_data)) {
                foreach ($deal_sap_data as $key => $value) {
                    echo "当前待处理单号: {$value['back_no']}, 关联借款单号: {$value['lno']}" . PHP_EOL;
                    echo 'start handle loan_return id: ' . $value['return_id'] . PHP_EOL;

                    echo "归还时间: {$value['back_date']}" . PHP_EOL;
                    echo "归还审核通过时间: {$value['back_approved_at']}" . PHP_EOL;

                    $return_data  = [];
                    // 1-指定日期不是上个月数据跳过  11-31不是本月数据跳过
                    if ($is_little_date) {
                        if (ltrim(date('m', strtotime($value['back_date']))) == ltrim(date('m')) && (in_array($country_code, [GlobalEnums::MY_COUNTRY_CODE, GlobalEnums::TH_COUNTRY_CODE]) && $value['is_new'] == Enums\LoanEnums::IS_NEW_YES || $country_code == GlobalEnums::PH_COUNTRY_CODE && $value['is_new'] == Enums\LoanEnums::IS_NEW_NO)) {
                            //泰国、马来多笔归还、菲律宾单笔归还按照银行流水日期过滤
                            continue;
                        } else if (ltrim(date('m', strtotime($value['back_approved_at']))) == ltrim(date('m')) && $country_code == GlobalEnums::PH_COUNTRY_CODE && $value['is_new'] == Enums\LoanEnums::IS_NEW_YES) {
                            //菲律宾多笔归还按照审批通过日期
                            continue;
                        }
                    } else {
                        if (ltrim(date('m', strtotime($value['back_date']))) != ltrim(date('m')) && (in_array($country_code, [GlobalEnums::MY_COUNTRY_CODE, GlobalEnums::TH_COUNTRY_CODE]) && $value['is_new'] == Enums\LoanEnums::IS_NEW_YES || $country_code == GlobalEnums::PH_COUNTRY_CODE && $value['is_new'] == Enums\LoanEnums::IS_NEW_NO)) {
                            //泰国、马来多笔归还、菲律宾单笔归还按照银行流水日期过滤
                            continue;
                        } else if (ltrim(date('m', strtotime($value['back_approved_at']))) != ltrim(date('m')) && $country_code == GlobalEnums::PH_COUNTRY_CODE && $value['is_new'] == Enums\LoanEnums::IS_NEW_YES) {
                            //菲律宾多笔归还按照审批通过日期
                            continue;
                        }
                    }

                    //获取归还单信息
                    $item = LoanReturnModel::findFirst([
                        'conditions' => 'id = :id:',
                        'bind' => ['id' => $value['return_id']]
                    ]);
                    if (empty($item)) {
                        echo '单据不存在, 跳过' . PHP_EOL;
                        continue;
                    }

                    $request_data = [
                        'currency'              => $t->_(GlobalEnums::$currency_item[$value['currency']]),//货币
                        'cost_company_id'       => $sap_company_ids[$value['create_company_id']] ?? 'FEX01',//公司
                        'lno'                   => $value['lno'],//外部参考-借款申请单号
                        'create_id'             => $value['create_id'],//单据描述-员工工号
                        'create_date'           => (GlobalEnums::PH_COUNTRY_CODE == $country_code && $value['is_new'] == Enums\LoanEnums::IS_NEW_YES) ? $value['back_date'] : $value['create_date'],//BusinessTransactionDocumentDate菲律宾多笔归还传递归还单上的back_date银行流水日期其他传递借款单create_date申请日期
                        'back_amount'           => bcdiv($value['back_actual_amount'], 1000, 2),// 1. TransactionCurrencyNetAmount字段传递归还单中实际归还金额back_actual_amount字段
                        'back_transaction_date' => (GlobalEnums::PH_COUNTRY_CODE == $country_code && $value['is_new'] == Enums\LoanEnums::IS_NEW_YES) ? substr($value['back_approved_at'], 0 ,10) : (empty($value['back_transaction_date']) ? $value['back_date'] : $value['back_transaction_date']),//PostingDate菲律宾多笔归还传递归还单back_approved_at审批通过日期其他传递归还单银行流水日期
                        'back_no'               => $value['back_no'],//归还单编号
                    ];

                    $z = 0;
                    while (empty($return_data) && $z < 1) {
                        $z++;
                        $return_data = SapService::getInstance()->loanBackSap($request_data, $country_code);
                        if (isset($return_data['SAP_UUID']) && !empty($return_data['SAP_UUID'])) {
                            echo '抛数成功, SAP_UUID=' . $return_data['SAP_UUID'] . PHP_EOL;

                            $update_res = $item->i_update(['back_sync_sap' => 1, 'back_sap_uuid' => $return_data['SAP_UUID'] ?? '', 'updated_at' => date('Y-m-d H:i:s')]);
                            $x++;
                        } else {
                            $note = '';
                            if (isset($return_data['log']['Item']) && !empty($return_data['log']['Item'])) {
                                $note = array_column($return_data['log']['Item'], 'Note');
                                $note = implode(',', $note);
                            }

                            echo '抛数失败, sap_note=' . $note . PHP_EOL;

                            $update_res = $item->i_update(['back_sync_sap' => 2, 'updated_at' => date('Y-m-d H:i:s'), 'back_sap_note' => $note]);
                        }

                        echo '抛数结果DB状态更新: ' . ($update_res === false ? '失败' : '成功') . PHP_EOL;

                        sleep(2);
                    }

                    echo PHP_EOL;
                }

                sleep(1);
                $i   += 100;
                $deal_sap_data = $this->getLoanRebackSapList($country_code, $default_currency['code'], $cost_company_id, $i, $start_time, $end_time);
            }

            echo 'sap loan return 成功:' . $x . '条数' . PHP_EOL;
        } catch (ValidationException $e) {
            $this->logger->notice('loan_back_sap_task_exception:' . $e->getMessage());
            echo $e->getMessage() . PHP_EOL;
        } catch (Exception $e) {
            $this->logger->warning('loan_back_sap_task_exception:' . $e->getMessage());
            echo $e->getMessage() . PHP_EOL;
        }

        echo 'end: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $this->clearLock(__METHOD__);
        exit();
    }

    /**
     * 获取借款归还同步sap的数据
     * @param string $country_code 国家码
     * @param string $currency 币种
     * @param array $cost_company_ids 所属公司ID组
     * @param integer $offset 分页偏移量
     * @param string $start_time 起始时间
     * @param string $end_time 截止时间
     * @return mixed
     */
    private function getLoanRebackSapList($country_code, $currency,$cost_company_ids, $offset, $start_time, $end_time)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('main.id,lno,create_id,create_date,currency,create_company_id,return.id as return_id,return.back_no,return.back_actual_amount,return.back_date,return.back_transaction_date,return.is_new,return.back_approved_at');
        $builder->from(['main' => Loan::class]);
        $builder->leftjoin(LoanReturnModel::class, 'return.loan_id = main.id', 'return');
        $builder->where('currency = :currency: and main.pay_status = :pay_status: and return.back_status = :back_status: and return.back_audit_status = :back_audit_status:', ['currency' => $currency, 'pay_status' => Enums::LOAN_PAY_STATUS_PAY, 'back_status' => Enums\LoanEnums::LOAN_BACK_STATUS_BACK, 'back_audit_status' => Enums::WF_STATE_APPROVED]);
        $builder->inWhere('main.create_company_id', $cost_company_ids);
        $builder->inWhere('return.back_sync_sap', [0,3]);
        $builder->limit(100, $offset);
        if (in_array($country_code, [GlobalEnums::TH_COUNTRY_CODE, GlobalEnums::MY_COUNTRY_CODE])) {
            //is_new=1；归还单归还时银行流水日期back_date=当月的
            $builder->andWhere('return.is_new = :is_new: and return.back_date >= :date_start: and return.back_date < :date_end:', ['is_new' =>Enums\LoanEnums::IS_NEW_YES, 'date_start' => $start_time, 'date_end' => $end_time]);
        } else if ($country_code == GlobalEnums::PH_COUNTRY_CODE) {
            //菲律宾获取数据范围分两种；1. is_new=0；历史单据按照银行流水日期back_date=当月的;2. is_new=1；新单据按照back_approved_at归还审批通过日期=当月
            $builder->andWhere('(return.is_new = :is_new: and return.back_date >= :date_start: and return.back_date < :date_end:) OR (return.is_new = :is_new_val: and return.back_approved_at >= :date_start: and return.back_approved_at < :date_end:)',
                ['is_new' =>Enums\LoanEnums::IS_NEW_NO, 'date_start' => $start_time, 'date_end' => $end_time, 'is_new_val' => Enums\LoanEnums::IS_NEW_YES]);
            $builder->andWhere('return.back_date is not null and return.back_type = :back_type:', ['back_type' => Enums\LoanEnums::LOAN_BACK_TYPE_TRANSFER]);
        }
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 历史归还数据写入借款单归还单记录表
     * 一次性脚本
     */
    public function loan_history_reback_saveAction()
    {
        echo date('Ymd H:i:s').' 脚本开始执行'.PHP_EOL;

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            //借款单审批状态=通过&&借款单支付状态=已支付&&is_new=0
            $deal_loan_return = Loan::find([
                'conditions' => 'status = :status: and pay_status = :pay_status: and is_new = :is_new:',
                'bind' => [
                    'status' => Enums::WF_STATE_APPROVED,
                    'pay_status' => Enums::LOAN_PAY_STATUS_PAY,
                    'is_new' => Enums\LoanEnums::IS_NEW_NO
                ],
            ]);
            $deal_loan_return_list = $deal_loan_return->toArray();
            echo '共查询到:'.count($deal_loan_return_list).'条借款单需要处理'.PHP_EOL;
            if (!empty($deal_loan_return_list)) {
                $i = 0;
                $now = date('Y-m-d H:i:s');
                foreach ($deal_loan_return as $loan) {
                    $this->logger->info('借款单【'.$loan->id.'】before数据：'.json_encode($loan->toArray()));
                    echo '借款单id【'.$loan->id.'】开始处理'.PHP_EOL;
                    //借款单中原始借款金额
                    $loan_back_amount = $loan->back_amount;
                    //归还中 && 归还金额>0,现在借款单中的back_amount表示的是实际归还金额总计，所以在途的归还金额需要从借款单back_amount中还原;
                    if ($loan->back_status == Enums\LoanEnums::LOAN_BACK_STATUS_ING && $loan->back_amount > 0) {
                        $loan->back_amount = 0;
                    }
                   //已归还金额 = 报销抵扣（re_amount）+ 归还金额（back_amount）
                    $paid_return_amount = $loan->re_amount + $loan->back_amount;
                    if ($paid_return_amount == 0) {
                       //未开始归还：已归还金额=0
                        $loan->loan_status = Enums\LoanEnums::LOAN_STATUS_NOT_START_RETURN;
                    } elseif ($paid_return_amount < $loan->amount) {
                        //部分归还：已归还金额<借款金额
                        $loan->loan_status = Enums\LoanEnums::LOAN_STATUS_PARTIAL_RETURN;
                    } else if ($paid_return_amount == $loan->amount) {
                        //已还清：已归还总金额=借款金额
                        $loan->loan_status = Enums\LoanEnums::LOAN_STATUS_PAID_OFF;
                    } else if ($paid_return_amount > $loan->amount) {
                        //超额归还：已归还总金额＞借款金额
                        $loan->loan_status = Enums\LoanEnums::LOAN_STATUS_OVER_RETURN;
                    }
                    $loan->updated_at = $now;
                    $result = $loan->save();
                    //借款单保存成功,组装归还单数据
                    if ($result === true) {
                        $this->logger->info('借款单【'.$loan->id.'】更新后after数据：'.json_encode($loan->toArray()));
                        if (($loan_back_amount == 0 && $loan->back_audit_status == Enums::WF_STATE_REJECTED && $loan->back_status == Enums\LoanEnums::LOAN_BACK_STATUS_NOT) || (in_array($loan->back_status, [Enums\LoanEnums::LOAN_BACK_STATUS_ING, Enums\LoanEnums::LOAN_BACK_STATUS_BACK]) && $loan_back_amount > 0)) {
                            //1.归还驳回 && 归还状态未归还 && 归还金额是0（历史驳回会从借款单表中置为0），则需要记录转账归还记录
                            //2.归还中、已归还确实有归还金额的才需要将归还写入归还明细
                            $loan_return_arr = [
                                'back_no' => BaseService::genSerialNo(Enums\LoanEnums::LOAN_RETURN_BACK_APPLY_NO_PREFIX, 'loan_return_back_apply_counter', 5),//归还单申请编号
                                'back_apply_date' => date('Y-m-d'),//归还申请日期
                                'loan_id' => $loan->id,//借款单id
                                'back_amount' => $loan_back_amount,//归还金额
                                'back_actual_amount' => ($loan->back_audit_status == Enums::WF_STATE_APPROVED) ? $loan->back_amount : 0,//实际归还金额
                                'back_status' => $loan->back_status,//还款状态
                                'back_mark' => $loan->back_mark,//备注
                                'back_audit_status' => $loan->back_audit_status,//归还审核状态
                                'back_approved_at' => $loan->back_approved_at,//归还通过时间
                                'back_rejected_at' => $loan->back_rejected_at,//归还拒绝时间
                                'back_refuse_reason' => $loan->back_refuse_reason ?? '',//归还拒绝原因
                                'back_cancel_reason' => $loan->back_cancel_reason ?? '',//归还撤销原因
                                'back_date' => $loan->back_date,//归还时银行流水日期
                                'back_sync_sap' => $loan->back_sync_sap,//同步状态
                                'back_sap_note' => $loan->back_sap_note,//归还sap
                                'back_transaction_date' => $loan->back_transaction_date,//借款归还过账日期
                                'back_type' => Enums\LoanEnums::LOAN_BACK_TYPE_TRANSFER,//归还方式：1转账归还
                                'back_progress' => ($loan->back_audit_status == Enums::WF_STATE_PENDING) ? Enums\LoanEnums::LOAN_RETURN_BACK_PROGRESS_ING : Enums\LoanEnums::LOAN_RETURN_BACK_PROGRESS_DONE,//处理进度
                                'is_new' => Enums\LoanEnums::IS_NEW_NO,
                                'created_at' => $now,
                                'updated_at' => $now
                            ];
                            $loan_return_model = new LoanReturnModel();
                            $return_result = $loan_return_model->save($loan_return_arr);
                            if ($return_result === true) {
                                //对应的转账归还记录保存成功，则需要获取该借款单之前对应的归还审批流
                                $workflow_request = WorkflowRequestModel::findFirst([
                                    'conditions' => 'biz_type = :type: and biz_value= :biz_value: and is_abandon = :is_abandon:',
                                    'bind' => ['type'=> Enums::WF_LOAN_BACK_TYPE , 'biz_value' => $loan->id, 'is_abandon' => Enums\GlobalEnums::WORKFLOW_ABANDON_STATE_NO],
                                    'order'=>'id desc'
                                ]);
                                if (!empty($workflow_request)) {
                                    $this->logger->info('借款单id【'.$loan->id.'】关联的workflow_request_id：【'.$workflow_request->id.'】before数据：'.json_encode($workflow_request->toArray()));
                                    //需要将关联的借款单的归还审批绑定到归还单的身上
                                    $workflow_request->biz_value = $loan_return_model->id;
                                    $workflow_result = $workflow_request->save();
                                    if ($workflow_result === true) {
                                        $this->logger->info('借款单id【'.$loan->id.'】关联的workflow_request_id：【'.$workflow_request->id.'】更新后after数据：'.json_encode($workflow_request->toArray()));
                                        $workflow_node_auditor = WorkflowRequestNodeAuditorModel::find([
                                            'columns' => 'id',
                                            'conditions' => 'biz_type = :type: and biz_value= :biz_value: and request_id = :request_id:',
                                            'bind' => ['type'=> Enums::WF_LOAN_BACK_TYPE , 'biz_value' => $loan->id, 'request_id' => $workflow_request->id],
                                        ])->toArray();
                                        if (!empty($workflow_node_auditor)) {
                                            $this->logger->info('借款单id【'.$loan->id.'】关联的workflow_request_id：【'.$workflow_request->id.'】表关联的workflow_node_auditor表before数据：'.json_encode($workflow_node_auditor));
                                            $auditor_result = $db->updateAsDict(
                                                (new WorkflowRequestNodeAuditorModel)->getSource(),
                                                [
                                                    'biz_value' => $loan_return_model->id
                                                ],
                                                ['conditions' => 'id in (' . implode(',', array_column($workflow_node_auditor, 'id')) . ')']
                                            );
                                            if ($auditor_result === true) {
                                                $this->logger->info('借款单id【'.$loan->id.'】关联的workflow_request_id：【'.$workflow_request->id.'】表关联的workflow_node_auditor表更新后after数据：'.json_encode(['biz_value' => $loan_return_model->id, 'workflow_node_auditorids'=>implode(',', array_column($workflow_node_auditor, 'id'))]));
                                            } else {
                                                echo '借款单id【'.$loan->id.'】workflow_request_node_auditor更新biz_value 从借款单id到归还单id失败'.PHP_EOL;
                                            }
                                        }
                                    } else {
                                        echo '借款单id【'.$loan->id.'】workflow_request更新biz_value 从借款单id到归还单id失败'.PHP_EOL;
                                    }
                                }
                            } else {
                                echo '借款单id【'.$loan->id.'】loan_return创建归还单失败'.PHP_EOL;
                            }
                        }
                        echo '借款单id【'.$loan->id.'】处理完毕'.PHP_EOL;
                        $i ++;
                    } else {
                        echo '借款单id【'.$loan->id.'】处理失败'.PHP_EOL;
                    }
                }
                $db->commit();
                echo '共处理：'.$i.'条借款单'.PHP_EOL;
                echo '历史归还数据写入借款单归还单记录表全部处理完毕'.PHP_EOL;
            } else {
                echo '暂无数据需要处理'.PHP_EOL;
                $this->logger->info('暂无数据需要处理');
            }
        } catch (Exception $e) {
            $db->rollback();
            $this->logger->warning('loan_history_reback_save_task_exception:' . $e->getMessage());
            echo date('Ymd H:i:s') . ' 历史归还数据写入借款单归还单记录表失败[异常],操作已回滚' . $e->getMessage() . PHP_EOL;
        }
        echo date('Ymd H:i:s'). " 脚本执行结束". PHP_EOL;
    }

    /**
     * 16871给借款申请人发送归还提醒
     * 每周一到周五，系统本地时间 9：00
     */
    public function send_reback_msg_to_applyAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $this->check_process($process_name);
        $is_exception = false;
        $log = 'begin: ' . date('Y-m-d H:i:s') . PHP_EOL;
        try {
            //距离应归还日期10日，5日
            $distance_back_at_ten_date = date('Y-m-d', strtotime('+10 days'));
            $distance_back_at_five_date = date('Y-m-d', strtotime('+5 days'));
            //超过应归还日期  3日，5日
            $pass_back_at_three_date = date('Y-m-d', strtotime(' -3 days'));
            $pass_back_at_five_date = date('Y-m-d', strtotime(' -5 days'));
            $log .= '距离应还日期10日：' . $distance_back_at_ten_date . PHP_EOL;
            $log .= '距离应还日期5日：' . $distance_back_at_five_date . PHP_EOL;
            $log .= '超过应还日期3日：' . $pass_back_at_three_date . PHP_EOL;
            $log .= '超过应还日期5日：' . $pass_back_at_five_date . PHP_EOL;
            $today = date('Y-m-d');
            //获取满足归还日期条件的借款列表
            $loan_list = Loan::find([
                'columns' => 'id, lno, create_id, create_name, back_at',
                'conditions' => 'status = :status: and pay_status = :pay_status: and loan_status in ({loan_status:array}) and back_at in ({back_at:array})',
                'bind' => [
                    'status' => Enums::WF_STATE_APPROVED,
                    'pay_status' => Enums::PAYMENT_PAY_STATUS_PAY,
                    'loan_status' => [LoanEnums::LOAN_STATUS_NOT_START_RETURN, LoanEnums::LOAN_STATUS_PARTIAL_RETURN],
                    'back_at' => [$distance_back_at_ten_date, $distance_back_at_five_date, $today, $pass_back_at_three_date, $pass_back_at_five_date]
                ],
            ])->toArray();

            $final_list = [];
            if (!empty($loan_list)) {
                $loan_ids = array_column($loan_list, 'id');
                //获取借款审批中的转账归还借款信息
                $loan_return_list = LoanReturnModel::find([
                    'columns' => 'loan_id',
                    'conditions' => 'loan_id in ({loan_ids:array}) and back_type = :back_type: and back_audit_status = :back_audit_status:',
                    'bind' => ['loan_ids' => $loan_ids, 'back_type' => LoanEnums::LOAN_BACK_TYPE_TRANSFER, 'back_audit_status' => Enums::WF_STATE_PENDING]
                ])->toArray();
                $pending_return_loan_ids = array_column($loan_return_list, 'loan_id');
                //获取借款申请人信息
                $staff_ids = array_values(array_unique(array_column($loan_list, 'create_id')));
                $staff_list = (new HrStaffRepository())->getStaffListByStaffIds($staff_ids);
                foreach ($loan_list as $item) {
                    //借款款关联的转账归还单审批状态=审批中的不发
                    if (in_array($item['id'], $pending_return_loan_ids)) {
                        continue;
                    }
                    $one_staff_info = $staff_list[$item['create_id']] ?? [];
                    //不存在的员工或者离职、停职员工不发
                    if (empty($one_staff_info) || in_array($one_staff_info['state'], [StaffInfoEnums::STAFF_STATE_LEAVE, StaffInfoEnums::STAFF_STATE_STOP])){
                        continue;
                    }
                    $final_list[$item['create_id']]['create_name'] = $item['create_name'];
                    $final_list[$item['create_id']]['content'] .= 'Loan number' . $item['lno'] . '  Appointed return date ' . $item['back_at'] . '；<br/>';
                }
            }

            //发送站内信
            if (!empty($final_list)) {
                $bi_rpc = (new ApiClient('hcm_rpc', '', 'add_kit_message'));
                foreach ($final_list as $staff_id => $item) {
                    $bi_rpc->setParams([[
                        'staff_users' => [['id' => $staff_id]],
                        'message_title' => 'Loan Due Reminder',
                        'category' => LoanEnums::MESSAGE_CATEGORY_LOAN_RETURN_NOTICE,
                        'message_content' => 'Dear  ' . $item['create_name'] . ',<br/> The loan you applied for<br/>' . $item['content'] . 'Please log into the OA system for restitution/settlement operation on time.<br/>If you do not return the loan within 3 days of the appointed return date, the company has the right to deduct the loan from your salary.<br/>If there is an error in the status of the loan order, please contact the finance department.<br/>If you have already applied for a return, please ignore this message.'
                    ]]);
                    $res = $bi_rpc->execute();
                    $send_ret = false;
                    if (isset($res['result']['code']) && ($res['result']['code'] == ErrCode::$SUCCESS)) {
                        $send_ret = true;
                    }
                    $log .= '员工：' . $staff_id . ';发送借款归还提醒：' . ($send_ret ? '成功' : '失败') . PHP_EOL;
                }
            } else {
                $log .= '暂无员工需要提醒还款' . PHP_EOL;
            }
            $log .= 'end: ' . date('Y-m-d H:i:s');
        } catch (Exception $e) {
            $is_exception = true;
            $log .= 'send_return_msg_to_apply-exception: ' . $e->getMessage();
        }
        if ($is_exception) {
            $this->logger->warning($log);
        } else {
            $this->logger->info($log);
        }
        exit($log);
    }

    //借款 和备用金 合并 发送邮件 + 附件
    public function loanFundMailAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log = '任务名称: ' . $process_name . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s')  . PHP_EOL;
        $log .= '初始内存: ' . memory_usage() . PHP_EOL;
        $is_exception = false;

        //借款 待审批 或者 审批通过 待支付 不超过20 条
        $loanData = Loan::find([
            'columns'    => 'lno as no,created_at,status,pay_status,create_id,create_name',
            'conditions' => 'status = 1 or (status = 3 and pay_status = 1)',
        ])->toArray();

        //备用金 待审批 或者 审批通过 待支付 不超过20条
        $fundData  = ReserveFundApply::find([
            'columns'    => 'rfano as no, created_at, status, pay_status, create_id, create_name',
            'conditions' => 'status = 1 or (status = 3 and pay_status = 1)',
        ])->toArray();
        $loanStaff = empty($loanData) ? [] : array_column($loanData, 'create_id');
        $fundStaff = empty($fundData) ? [] : array_column($fundData, 'create_id');

        //在职状态
        $staffIds = array_merge($loanStaff, $fundStaff);
        $staffIds = array_values(array_unique($staffIds));

        if (empty($staffIds)) {
            echo '没有员工数据';
            return;
        }

        $staffData = HrStaffInfoModel::find([
            'columns'    => 'staff_info_id, state, wait_leave_state',
            'conditions' => 'staff_info_id in ({staffIds:array}) and (state != 1 or (state = 1 and wait_leave_state = 1))',
            'bind'       => ['staffIds' => $staffIds],
        ])->toArray();

        if (empty($staffData)) {
            echo '员工状态正常';
            return;
        }
        $staffData = array_column($staffData, null, 'staff_info_id');
        $data      = array_merge($loanData, $fundData);
        $stateList = StaffInfoEnums::$staff_state;
        $excelData = [];
        $otherLang = in_array(get_country_code(), ['TH', 'LA']) ? 'th' : 'en';
        $other     = \App\Library\BaseService::getTranslation($otherLang);
        $cn        = \App\Library\BaseService::getTranslation('zh-CN');

        foreach ($data as &$da) {
            $staffId = $da['create_id'];
            if (empty($staffData[$staffId])) {
                continue;
            }
            $da['created_at'] = show_time_zone($da['created_at']);
            //单据状态
            if (!empty($da['status'])) {
                $da['status'] = $other->_(Enums::$loan_status[$da['status']]) . ' ' . $cn->_(Enums::$loan_status[$da['status']]);
            }
            //支付状态
            if (!empty($da['pay_status'])) {
                $da['pay_status'] = $other->_(Enums::$loan_pay_status[$da['pay_status']]) . ' ' . $cn->_(Enums::$loan_pay_status[$da['pay_status']]);
            }
            //在职状态
            $state = $staffData[$staffId]['state'];
            if ($state == StaffInfoEnums::STAFF_STATE_IN && $staffData[$staffId]['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES) {
                $state = StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE;
            }
            $da['state_text'] = $other->_($stateList[$state]) . ' ' . $cn->_($stateList[$state]);
            $excelData[]      = array_values($da);
        }

        // 获取表头
        $header = [
            $other->_('csr_field_flow_no') . ' ' . $cn->_('csr_field_flow_no'),// 单号
            $other->_('budget_adjust_apply_date') . ' ' . $cn->_('budget_adjust_apply_date'),//申请时间
            $other->_('material_asset_apply.status') . ' ' . $cn->_('material_asset_apply.status'),//单据状态
            $other->_('cheque_pay_status') . ' ' . $cn->_('cheque_pay_status'),//支付状态
            $other->_('cheque_apply_id') . ' ' . $cn->_('cheque_apply_id'),//申请人工号
            $other->_('cheque_apply_name') . ' ' . $cn->_('cheque_apply_name'),//申请人姓名
            $other->_('working_state') . ' ' . $cn->_('working_state'),//在职状态
        ];

        $file_name = date('Y-m-d') . "_To be resigned|resigned_apply loan|Reserve Fund.xlsx";
        $result    = LoanExportService::getInstance()->exportExcel($header, $excelData, $file_name);
        $fileUrl   = $result['code'] == ErrCode::$SUCCESS ? $result['data'] : '';
        if (empty($fileUrl)) {
            $this->logger->error("loanFundMailAction 生成 excel 失败 ");
            return false;
        }

        $mails = EnumsService::getInstance()->getSettingEnvValueIds('notice_mail');
        //发送邮件 取配置 notice_mail
        $title    = $other->_('loan_fund_mail_title') . ' ' . $cn->_('loan_fund_mail_title');
        $content  = '<p>' . $other->_('loan_fund_mail_content', ['file_url' => $fileUrl, 'file_name' => $file_name]) . '</p>';
        $content  .= '<p>' . $cn->_('loan_fund_mail_content', ['file_url' => $fileUrl, 'file_name' => $file_name]) . '</p>';
        $content  .= $fileUrl;
        $send_res = $this->mailer->sendAsync($mails, $title, $content);

        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $this->logger->info("loanFundMailAction 发送结果 " . json_encode($send_res, JSON_UNESCAPED_UNICODE));
        exit($log);
    }

}