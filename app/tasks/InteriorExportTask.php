<?php
/**
 * 员工商城相关模块的异步下载任务
 */

use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Library\Enums\DownloadCenterEnum;
use App\Modules\Common\Services\DownloadCenterService;
use App\Modules\Shop\Services\GoodsSizeService;


class InteriorExportTask extends BaseTask
{


    /**
     * 员工商城-员工工服尺码列表异步下载
     * php cli.php  interior_export interior_goods_size
     */
    public function interior_goods_sizeAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log          = '任务名称: ' . $process_name . PHP_EOL;
        $log          .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log          .= '初始内存: ' . memory_usage() . PHP_EOL;
        $is_exception = false;

        try {
            // 1.提取待处理的下载任务(最早入队的)
            $task_model = DownloadCenterService::getInstance()->getFirstPendingTask(DownloadCenterEnum::INTERIOR_STAFF_ENTRY_SIZE);
            if (empty($task_model)) {
                throw new ValidationException('没有待下载的任务', ErrCode::$VALIDATE_ERROR);
            }

            // 2.1解析参数
            $params             = !empty($task_model->args_json) ? json_decode(base64_decode($task_model->args_json, true), true) : [];
            $params['language'] = $params['language'] ?? DownloadCenterEnum::DETAUL_LANGUAGE;
            $user               = ['id' => $params['user_id']];

            $log .= '任务ID: ' . $task_model->id . PHP_EOL;
            $log .= '任务参数: ' . json_encode($params, JSON_UNESCAPED_UNICODE) . PHP_EOL;

            // 2.2获取数据总量
            $all_total      = GoodsSizeService::getInstance()->getListCount($params, $params['language'], $user);
            $total_page_num = ceil($all_total / DownloadCenterEnum::DEFAUL_PER_PAGE_SIZE);
            $log            .= '预计总量: ' . $all_total . PHP_EOL;
            $log            .= '预计批次: ' . $total_page_num . PHP_EOL;

            // 2.3设置系统语言
            GoodsSizeService::setLanguage($params['language']);

            // 2.4分批取数
            $excel_data = [];
            $page_num   = DownloadCenterEnum::INIT_PAGE_NUM;
            for ($page_num; $page_num <= $total_page_num; $page_num++) {
                $log .= '----- 取数批次: ' . $page_num . '-----' . PHP_EOL;

                $params['pageNum']  = $page_num;
                $params['pageSize'] = DownloadCenterEnum::DEFAUL_PER_PAGE_SIZE;
                $list               = GoodsSizeService::getInstance()->getExportList($params['language'], $params, $all_total, $user);
                // 2.5合并数据
                foreach ($list['data']['items'] as $item) {
                    $excel_data[] = array_values($item);
                }

                $log .= '本批数量: ' . count($list['data']['items']) . PHP_EOL;
                unset($list);

                $log .= '当前内存: ' . memory_usage() . PHP_EOL;
            }

            $log .= '实际总量: ' . count($excel_data) . PHP_EOL;
            $log .= '当前内存: ' . memory_usage() . PHP_EOL;

            // 2.6获取Excel表头
            $excel_header = GoodsSizeService::getInstance()->getBatchSizeHeader();

            // 2.7生成Excel
            $excel_result = GoodsSizeService::getInstance()->exportExcel($excel_header, $excel_data, $task_model->file_name);
            $log          .= 'Excel结果: ' . json_encode($excel_result, JSON_UNESCAPED_UNICODE) . PHP_EOL;
            unset($excel_data);

            // 3.更新任务
            if (isset($excel_result['code']) && $excel_result['code'] == ErrCode::$SUCCESS && !empty($excel_result['data'])) {
                $save_model_result = DownloadCenterService::getInstance()->saveTask($task_model, DownloadCenterEnum::TASK_STATUS_SUCCESS, $excel_result['data']);
                if ($save_model_result === false) {
                    $log .= '任务状态: 更新失败, 原因可能是: ' . get_data_object_error_msg($task_model) . PHP_EOL;

                } else {
                    $log .= '任务状态: 更新成功' . PHP_EOL;
                }

            } else {
                // 记录错误日志
                $log .= '任务状态: 未更新, 生成Excel失败, 待下次重新执行' . PHP_EOL;
            }

        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;

        } catch (Exception $e) {
            $log          .= $e->getMessage() . PHP_EOL;
            $is_exception = true;
        }

        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        if ($is_exception) {
            $this->logger->error('员工商城-员工工服尺码列表-导出任务: ' . $log);
        } else {
            $this->logger->info('员工商城-员工工服尺码列表-导出任务: ' . $log);
        }

        exit($log);
    }


}
