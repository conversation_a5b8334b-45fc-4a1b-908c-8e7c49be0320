<?php
use App\Modules\Material\Models\MaterialInventoryCheckModel;
use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\InventoryCheckEnums;
use App\Modules\Material\Services\InventoryCheckService;

/**
 * 盘点单时间结束，盘点状态更改为已完成脚本
 * Class InventoryCheckDoneTask
 */
class InventoryCheckDoneTask extends BaseTask
{
    /**
     * 更改盘点单状态为已完成脚本
     */
    public function mainAction()
    {
        $log = '';
        try {
            $log .= date('Ymd H:i:s') . ' 脚本开始执行' . PHP_EOL;
            //先找到总数，然后分页获变更状态
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['main' => MaterialInventoryCheckModel::class]);
            $builder->andWhere('main.is_deleted = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);
            $builder->inWhere('main.status', [InventoryCheckEnums::INVENTORY_CHECK_STATUS_NOT_STARTED, InventoryCheckEnums::INVENTORY_CHECK_STATUS_ING]);
            $builder->inWhere('main.is_send_task', [InventoryCheckEnums::TASK_WAIT, InventoryCheckEnums::TASK_DONE]);
            $builder->andWhere('main.end_at <= :check_end:', ['check_end' => date('Y-m-d H:i:s')]);
            $list_obj  = $builder->getQuery()->execute();
            $list_data = $list_obj->toArray();
            if (!empty($list_data)) {
                foreach ($list_obj as $inventory) {
                    try {
                        InventoryCheckService::getInstance()->updateStaffTask($inventory);
                        $log .= date('Ymd H:i:s') . ' 盘点单ID：【' . $inventory->id . '】标记为已完成成功' . PHP_EOL;
                    } catch (Exception $e) {
                        $log .= date('Ymd H:i:s') . ' 盘点单ID：【' . $inventory->id . '】标记为已完成失败;原因是：' . $e->getMessage() . PHP_EOL;
                    }
                }
            } else {
                $log .= '暂无需要盘点单数据需要处理' . PHP_EOL;
            }
        } catch (Exception $e) {
            $log .= date('Ymd H:i:s') . ' 修改盘点单状态为已完成: 失败[异常]' . $e->getMessage() . PHP_EOL;
            $this->logger->warning($log);
        }
        $log .= date('Ymd H:i:s'). ' 脚本执行结束'. PHP_EOL;
        exit($log);
    }
}