<?php

use App\Library\BaseService;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\SysDepartmentModel;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Material\Models\MaterialAssetsModel;
use App\Library\Enums\MaterialEnums;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Material\Models\MaterialAssetTransferBatchModel;
use App\Modules\Material\Services\AssetTransferExportService;
use App\Modules\Material\Models\MaterialAssetTransferLogModel;
use App\Modules\Material\Services\AssetTransferListService;
use App\Modules\Material\Services\AssetTransferMessageService;
use App\Modules\Material\Services\AssetTransferService;
use App\Repository\HrStaffRepository;

/**
 * 资产转移相关任务
 * Class MaterialTransferTask
 */
class MaterialTransferTask extends BaseTask
{
    public static $auto_reception_once_max = 500;

    /**
     * 每天早上发送前一天拒绝接收的资产到资产部
     * @date 2022/6/24
     */
    public function send_mail_rejectAction()
    {
        $last_date = date('Y-m-d', strtotime('-1 day'));
        $start_date = $last_date . ' 00:00:00';
        $end_date = $last_date . ' 23:59:59';
        //查询前一天拒绝数据
        $builder = $this->modelsManager->createBuilder();
        $columns = 'log.transfer_type, log.operator_id, log.from_staff_id, log.from_node_department_name, log.from_store_name, log.status,
        asset.bar_code, asset.asset_code, asset.old_asset_code, asset.name_zh, asset.sn_code, log.transfer_remark, log.transfer_at,
        log.to_staff_id, log.to_node_department_name, log.to_store_name, log.remark, log.finished_at';
        $builder->columns($columns);
        $builder->from(['log' => MaterialAssetTransferLogModel::class]);
        $builder->leftjoin(MaterialAssetsModel::class, 'log.asset_id = asset.id', 'asset');
        $builder->andWhere('log.finished_at >= :finished_at_start:', ['finished_at_start' => $start_date]);
        $builder->andWhere('log.finished_at <= :finished_at_end:', ['finished_at_end' => $end_date]);
        $builder->andWhere('log.status = :status:', ['status' => MaterialEnums::TRANSFER_LOG_STATUS_REJECTED]);
        $builder->andWhere('log.is_deleted = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);
        $reject_data = $builder->getQuery()->execute()->toArray();
        if (empty($reject_data)) {
            echo '资产转移拒绝数据为空 start_date:' . $start_date . ' end_date:' . $end_date . ' ,执行时间:' . date('Y-m-d H:i:s') . PHP_EOL;
            $this->logger->info('material_transfer_send_mail 数据为空, start_date:' . $start_date . ' end_date:' . $end_date . ' ,执行时间:' . date('Y-m-d H:i:s'));
            exit;
        }
        //绑定to_staff_name,from_staff_name,operator_name,operator_department
        $reject_data = AssetTransferListService::getInstance()->setStaffName($reject_data);
        //导出
        $obj = AssetTransferExportService::getInstance();
        $obj->setLanguage('en');
        $export_excel = $obj->exportRejectData('refuse-asset-list', $reject_data);
        if (!$export_excel || !isset($export_excel['data'])) {
            echo '资产转移拒绝数据导出excel失败 start_date:' . $start_date . ' end_date:' . $end_date . ' ,执行时间:' . date('Y-m-d H:i:s') . PHP_EOL;
            $this->logger->warning('material_transfer_send_mail 资产转移拒绝数据导出excel失败 start_date:' . $start_date . ' end_date:' . $end_date . ' ,执行时间:' . date('Y-m-d H:i:s'));
            exit;
        }
        $emails = EnvModel::getEnvByCode('asset_department_email');
        if (empty($emails)) {
            echo '资产部邮箱为空 start_date:' . $start_date . ' end_date:' . $end_date . ' ,执行时间:' . date('Y-m-d H:i:s') . PHP_EOL;
            $this->logger->info('material_transfer_send_mail 资产部邮箱为空, start_date:' . $start_date . ' end_date:' . $end_date . ' ,执行时间:' . date('Y-m-d H:i:s'));
            exit;
        }
        $emails = explode(',', $emails);
        $country = get_country_code();
        $title = $country . date('Ymd', strtotime($last_date)) . ' 拒收资产明细（refuse-asset-list）';
        $html = 'Hi,<br/>';
        $html .= '<p>文件是《' . $country . ' ' . date('Ymd', strtotime($last_date)) . ' 拒收资产明细（refuse-asset-list）》，请查阅。谢谢</p>';
        $send_result = $this->mailer->sendAsync($emails, $title, $html, [$export_excel['data']]);
        if ($send_result) {
            echo '资产转移拒绝邮件发送成功,收件邮箱:' . implode(',', $emails) . ', start_date:' . $start_date . ' end_date:' . $end_date . ' ,执行时间:' . date('Y-m-d H:i:s') . PHP_EOL;
            $this->logger->info('material_transfer_send_mail 资产转移拒绝邮件发送成功,收件邮箱' . implode(',', $emails) . ', start_date:' . $start_date . ' end_date:' . $end_date . ' ,执行时间:' . date('Y-m-d H:i:s'));
        } else {
            echo '资产转移拒绝邮件发送失败,收件邮箱:' . implode(',', $emails) . ', start_date:' . $start_date . ' end_date:' . $end_date . ' ,执行时间:' . date('Y-m-d H:i:s') . PHP_EOL;
            $this->logger->warning('material_transfer_send_mail 资产转移拒绝邮件发送失败,收件邮箱:' . implode(',', $emails) . ', start_date:' . $start_date . ' end_date:' . $end_date . ' ,执行时间:' . date('Y-m-d H:i:s'));
        }

    }

    /**
     * 自动接收资产
     * @throws \App\Library\Exception\BusinessException
     * @date 2022/11/12
     */
    public function auto_receptionAction()
    {
        try {
            //获取3天前,4天前,8天前的时间
            $three_day = date('Y-m-d', strtotime('-3 day', time()));
            $four_day = date('Y-m-d', strtotime('-4 day', time()));
            $eight_day = date('Y-m-d', strtotime('-8 day', time()));
            //取资产部转移, 出库转移-不通过SCM的数据 3天前的
            $transfer_data = MaterialAssetTransferLogModel::find([
                'columns' => 'id, to_staff_id, asset_id',
                'conditions' => 'transfer_at <= :three_date: AND transfer_type in ({transfer_type:array}) AND status = :status: AND is_deleted = :is_deleted: and aor_scm_no = :aor_scm_no_empty:',
                'bind' => [
                    'three_date' => $three_day . ' 23:59:59',
                    'transfer_type' => [MaterialEnums::TRANSFER_TYPE_ASSET_DEPARTMENT, MaterialEnums::TRANSFER_TYPE_OUT_STORAGE],
                    'status' => MaterialEnums::TRANSFER_LOG_STATUS_UNRECEIVED,
                    'is_deleted' => GlobalEnums::IS_NO_DELETED,
                    'aor_scm_no_empty' => ''
                ],
            ])->toArray();
            $this->logger->info('自动接收资产 3天前数据=:' . json_encode($transfer_data));
            //取出库转移-通过SCM出库的数据 泰国/马来/老挝/越南/印尼 4天前的 菲律宾8天前的
            $scm_transfer_data = [];
            $country = get_country_code();
            if (in_array($country, [GlobalEnums::TH_COUNTRY_CODE, GlobalEnums::MY_COUNTRY_CODE, GlobalEnums::LA_COUNTRY_CODE, GlobalEnums::VN_COUNTRY_CODE, GlobalEnums::ID_COUNTRY_CODE])) {
                $scm_transfer_data = MaterialAssetTransferLogModel::find([
                    'columns' => 'id, to_staff_id, asset_id',
                    'conditions' => 'transfer_at <= :four_date: AND transfer_type = :transfer_type: AND status = :status: AND is_deleted = :is_deleted: and aor_scm_no != :aor_scm_no_empty:',
                    'bind' => [
                        'four_date' => $four_day . ' 23:59:59',
                        'transfer_type' => MaterialEnums::TRANSFER_TYPE_OUT_STORAGE,
                        'status' => MaterialEnums::TRANSFER_LOG_STATUS_UNRECEIVED,
                        'is_deleted' => GlobalEnums::IS_NO_DELETED,
                        'aor_scm_no_empty' => '',
                    ],
                ])->toArray();
            } elseif ($country == GlobalEnums::PH_COUNTRY_CODE) {
                $scm_transfer_data = MaterialAssetTransferLogModel::find([
                    'columns' => 'id, to_staff_id, asset_id',
                    'conditions' => 'transfer_at <= :eight_date: AND transfer_type = :transfer_type: AND status = :status: AND is_deleted = :is_deleted: and aor_scm_no != :aor_scm_no_empty:',
                    'bind' => [
                        'eight_date' => $eight_day . ' 23:59:59',
                        'transfer_type' => MaterialEnums::TRANSFER_TYPE_OUT_STORAGE,
                        'status' => MaterialEnums::TRANSFER_LOG_STATUS_UNRECEIVED,
                        'is_deleted' => GlobalEnums::IS_NO_DELETED,
                        'aor_scm_no_empty' => '',
                    ],
                ])->toArray();
            }
            $this->logger->info('自动接收资产 4天前数据=:' . json_encode($scm_transfer_data));
            //下边有调用service,初始化语言
            BaseService::setLanguage('en');
            //自动接收
            $all_transfer_data = array_merge($transfer_data, $scm_transfer_data);
            if (empty($all_transfer_data)) {
                echo '自动接收资产失败-资产转移待接收数据为空 three_day:' . $three_day . ' four_day:' . $four_day . ' eight_day:' . $eight_day . ' ,执行时间:' . date('Y-m-d H:i:s') . PHP_EOL;
                $this->logger->info('自动接收资产失败-资产转移待接收数据为空 three_day:' . $three_day . ' four_day:' . $four_day . ' eight_day:' . $eight_day . ' ,执行时间:' . date('Y-m-d H:i:s'));
                exit;
            }
            if (count($all_transfer_data) <= self::$auto_reception_once_max) {
                $reception_result = $this->autoReception($all_transfer_data);
                if (!isset($reception_result) || $reception_result['code'] != ErrCode::$SUCCESS) {
                    echo '自动接收资产失败, message=' . $reception_result['message'];
                    $this->logger->warning('自动接收资产失败-接收错误 待接收数据:' . json_encode($all_transfer_data, JSON_UNESCAPED_UNICODE) . ' 返回结果:' . json_encode($reception_result, JSON_UNESCAPED_UNICODE) . '执行时间:' . date('Y-m-d H:i:s'));
                    exit;
                }
            } else {
                $i = 0;
                do {
                    $this_transfer_data = array_slice($all_transfer_data, $i, self::$auto_reception_once_max);
                    $i += self::$auto_reception_once_max;
                    if (!empty($this_transfer_data)) {
                        $reception_result = $this->autoReception($this_transfer_data);
                        if (!isset($reception_result) || $reception_result['code'] != ErrCode::$SUCCESS) {
                            echo '自动接收资产失败, message=' . $reception_result['message'];
                            $this->logger->warning('自动接收资产失败-接收错误 待接收数据:' . json_encode($all_transfer_data, JSON_UNESCAPED_UNICODE) . ' 返回结果:' . json_encode($reception_result, JSON_UNESCAPED_UNICODE) . '执行时间:' . date('Y-m-d H:i:s'));
                            exit;
                        }
                    }
                } while (!empty($this_transfer_data));
            }
            echo '自动接收资产成功: 共处理' . count($all_transfer_data) . '条 ,执行时间:' . date('Y-m-d H:i:s') . PHP_EOL;
            $this->logger->info('自动接收资产成功, 接收数据' . json_encode($all_transfer_data, JSON_UNESCAPED_UNICODE) . ' , 执行时间:' . date('Y-m-d H:i:s'));

            exit();
        } catch (\Exception $e) {
            echo '自动接收资产异常: code=' . $e->getCode() . ' message=' . $e->getMessage() . ' , 执行时间:' . date('Y-m-d H:i:s') . PHP_EOL;
            $this->logger->error('自动接收资产异常: code=' . $e->getCode() . ' message=' . $e->getMessage() . ' trace=' . $e->getTraceAsString() . ' , 执行时间:' . date('Y-m-d H:i:s'));
            exit();
        }
    }

    /**
     * 批量接收资产
     * @param $transfer_data
     * @return array
     * @date 2022/11/28
     */
    public function autoReception($transfer_data)
    {
        $transfer_ids = array_column($transfer_data, 'id');
        //开始自动接收
        $result = AssetTransferService::getInstance()->autoReception(['transfer_ids' => $transfer_ids]);
        $this->logger->info('自动接收资产autoReception返回 ' . json_encode($result, JSON_UNESCAPED_UNICODE) . ' , 执行时间:' . date('Y-m-d H:i:s'));
        if (isset($result['code']) && $result['code'] == ErrCode::$SUCCESS && !empty($result['data']['success'])) {
            $success_asset_ids = array_column($result['data']['success'], 'asset_id');
            //发送push和站内信
            $staff_assets = $staff_ids = [];
            foreach ($transfer_data as $one_transfer) {
                //只有成功的才发
                if (in_array($one_transfer['asset_id'], $success_asset_ids)) {
                    $staff_assets[$one_transfer['to_staff_id']][] = $one_transfer['asset_id'];
                    $staff_ids[$one_transfer['to_staff_id']] = $one_transfer['to_staff_id'];
                }
            }
            if ($staff_assets) {
                AssetTransferMessageService::getInstance()->sendAutoReceptionMessage($staff_assets);
            }
            if ($staff_ids) {
                AssetTransferMessageService::getInstance()->sendPush($staff_ids, AssetTransferMessageService::$push_type_receive);
            }
        }
        return $result;
    }


    /**
     * 处理历史数据
     * transfer_at is null判定为历史数据,需要修复
     * @date 2023/3/1
     */
    public function update_history_dataAction()
    {
        //查总数
        $builder = $this->modelsManager->createBuilder();
        $builder->from(MaterialAssetTransferLogModel::class);
        $builder->columns('count(id) as count');
        $builder->andWhere('is_deleted = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);
        $builder->andWhere('transfer_at is null');
        $count = (int)$builder->getQuery()->getSingleResult()->count;
        $step = 200;
        $model = new MaterialAssetTransferLogModel();

        echo '处理历史数据开始: 共有' . $count . '条 ,执行时间:' . date('Y-m-d H:i:s') . PHP_EOL;
        $this->logger->info('处理历史数据开始: 共有' . $count . '条 ,执行时间:' . date('Y-m-d H:i:s'));
        $for_number = ceil($count / $step);
        for ($i = 0; $i < $for_number; $i++) {
            //批量查询历史数据,一次处理500条
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['log' => MaterialAssetTransferLogModel::class]);
            $builder->leftjoin(MaterialAssetTransferBatchModel::class, 'batch.id = log.batch_id', 'batch');
            $builder->columns('log.id,log.from_staff_id,log.to_staff_id,log.created_at,log.finished_at,
            log.operator_id,log.transfer_remark,log.transfer_operator_id,log.transfer_at,
            batch.staff_id,batch.staff_name,batch.mark');
            $builder->andWhere('log.is_deleted = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);
            $builder->andWhere('log.transfer_at is null');
            $builder->orderby('log.id ASC');
            $builder->limit($step);
            $items = $builder->getQuery()->execute()->toArray();
            //获取用户id
            $from_user_ids = array_column($items, 'from_staff_id');
            $to_user_ids = array_column($items, 'to_staff_id');
            $all_user_ids = array_merge($from_user_ids, $to_user_ids);
            $all_user_ids = array_values(array_unique($all_user_ids));
            if (!empty($all_user_ids)) {
                //查询用户
                $staff_data = HrStaffInfoModel::find([
                    'conditions' => 'staff_info_id in ({staff_info_id:array}) ',
                    'bind' => [
                        'staff_info_id' => $all_user_ids
                    ]
                ])->toArray();
                //用户id为key的数据
                $staff_data_kv = array_column($staff_data, null, 'staff_info_id');
                //获取用户部门
                $department_ids = array_column($staff_data, 'node_department_id');
                $department_ids = array_values(array_unique($department_ids));
                //查询部门公司
                $department_data = SysDepartmentModel::find([
                    'conditions' => ' id in ({ids:array}) ',
                    'bind' => [
                        'ids' => $department_ids
                    ]
                ])->toArray();
                //部门公司关系
                $department_company = array_column($department_data, null, 'id');
            }
            foreach ($items as $key => $value) {
                $update_data = [];
                //转出人信息
                $from_staff_info = $staff_data_kv[$value['from_staff_id']] ?? [];
                if (!empty($from_staff_info)) {
                    $update_data['from_staff_name'] = $from_staff_info['name'] ?? '';
                    $update_data['from_company_id'] = $department_company[$from_staff_info['node_department_id']]['company_id'] ?? 0;
                    $update_data['from_company_name'] = $department_company[$from_staff_info['node_department_id']]['company_name'] ?? '';
                }

                //接收人信息
                $to_staff_info = $staff_data_kv[$value['to_staff_id']] ?? [];
                if (!empty($to_staff_info)) {
                    $update_data['to_staff_name'] = $to_staff_info['name'] ?? '';
                    $update_data['to_company_id'] = $department_company[$to_staff_info['node_department_id']]['company_id'] ?? 0;
                    $update_data['to_company_name'] = $department_company[$to_staff_info['node_department_id']]['company_name'] ?? '';
                }
                //其他默认值
                if (empty($value['finished_at'])) {
                    $update_data['finished_at'] = $value['created_at'];
                }
                if (empty($value['operator_id'])) {
                    $update_data['operator_id'] = $value['to_staff_id'];
                }
                if (empty($value['transfer_remark'])) {
                    $update_data['transfer_remark'] = $value['mark'];
                }
                if (empty($value['transfer_operator_id'])) {
                    $update_data['transfer_operator_id'] = $value['staff_id'];
                }
                if (empty($value['transfer_operator_name'])) {
                    $update_data['transfer_operator_name'] = $value['staff_name'];
                }
                if (empty($value['transfer_at'])) {
                    $update_data['transfer_at'] = !empty($value['created_at']) ? $value['created_at'] : '';
                }
                if (empty($value['auto_recipient'])) {
                    $update_data['auto_recipient'] = MaterialEnums::MATERIAL_TRANSFER_AUTO_RECIPIENT_YES;
                }

                $one_log = $model::findFirst($value['id']);
                if ($one_log->i_update($update_data) === false) {
                    $this->logger->warning('更新失败 : updateData=' . json_encode($update_data, JSON_UNESCAPED_UNICODE) . ';  可能的原因是=' . get_data_object_error_msg($one_log));
                    echo '处理历史数据失败,第' . $i . '页 ,执行时间:' . date('Y-m-d H:i:s') . ';  可能的原因是=' . get_data_object_error_msg($one_log) . PHP_EOL;
                    exit();
                }
                unset($one_log);
            }
            echo '处理历史数据 当前处理第' . $i . '页 ,执行时间:' . date('Y-m-d H:i:s') . PHP_EOL;
            $this->logger->info('处理历史数据 当前处理第' . $i . '页 ,执行时间:' . date('Y-m-d H:i:s'));

            //处理一批休息1s
            sleep(1);
        }
        echo '处理历史数据完毕 ,执行时间:' . date('Y-m-d H:i:s') . PHP_EOL;
        $this->logger->info('处理历史数据完毕, 共处理' . $count . '条 , 执行时间:' . date('Y-m-d H:i:s'));

    }

    /**
     * 一次性脚本 修复资产台账staff_name=staff_id
     * 任务 php app/cli.php material_transfer update_material_asset_staff_name
     * 20230707目前数据量, TH-1500 MY-702 PH-361 LA-75 ID-0 VN-0
     */
    public function update_material_asset_staff_nameAction()
    {
        //查台账数据
        $material_assets = MaterialAssetsModel::find([
            'conditions' => 'staff_id = staff_name AND staff_id != 0 AND is_deleted = :is_deleted:',
            'bind' => [
                'is_deleted' => GlobalEnums::IS_NO_DELETED,
            ],
        ]);
        //分批取员工数据
        $material_assets_array = $material_assets->toArray();
        $staff_id_arr = array_column($material_assets_array, 'staff_id');
        $staff_id_arr = array_values(array_unique($staff_id_arr));

        $staff_id_arr_all = array_chunk($staff_id_arr, 500);
        $hr_staff_repository = new HrStaffRepository();
        $staff_list = [];
        foreach ($staff_id_arr_all as $staff_id_chunk) {
            $tmp_staff_list = $hr_staff_repository->getStaffListByStaffIds($staff_id_chunk);
            $staff_list = array_merge($staff_list, $tmp_staff_list);
        }
        $staff_list = array_column($staff_list, null, 'staff_info_id');
        //全部log
        $log = [];
        //错误log
        $error_log = [];
        //本次查到的总数量
        $all_num = count($material_assets_array);
        //需处理总量, 成功总量, 失败总量
        $all_update_num = $success_num = $error_num = 0;
        //循环处理台账
        foreach ($material_assets as $one_asset) {
            $tmp_log = [];
            if (isset($staff_list[$one_asset->staff_id]) && $one_asset->staff_name != $staff_list[$one_asset->staff_id]['name']) {
                $tmp_log = [
                    'asset_id' => $one_asset->id,
                    'asset_code' => $one_asset->asset_code,
                    'before_staff_name' => $one_asset->staff_name,
                ];
                $one_asset->staff_name = $staff_list[$one_asset->staff_id]['name'];
                $tmp_log['after_staff_name'] = $one_asset->staff_name;
                if ($one_asset->save()) {
                    //成功
                    $success_num += 1;
                    $tmp_log['result'] = 'success';
                } else {
                    //失败
                    $error_num += 1;
                    $tmp_log['result'] = 'error';
                    $error_log[] = $tmp_log;
                }
                $all_update_num += 1;
                $log[] = $tmp_log;
            }
        }
        //分批记录结果
        $log_chunk = array_chunk($log, 100);
        foreach ($log_chunk as $one_log_chunk) {
            $this->logger->info('本次处理结果data=' . json_encode($one_log_chunk, JSON_UNESCAPED_UNICODE));
        }
        if (!empty($error_log)) {
            $error_log_chunk = array_chunk($error_log, 100);
            foreach ($error_log_chunk as $one_error_log_chunk) {
                $this->logger->warning('处理结果异常data=' . json_encode($one_error_log_chunk, JSON_UNESCAPED_UNICODE));
            }
        }
        $result = '共查到数据:' . $all_num . '条; 需处理:' . $all_update_num . '条; 成功处理:' . $success_num . '条; 失败' . $error_num . '条  ,执行时间:' . date('Y-m-d H:i:s');
        $this->logger->info($result);
        exit($result);
    }

    /**
     * 一次性脚本 修复资产转移记录 to_staff_id=to_staff_name
     * 任务 php app/cli.php material_transfer update_material_asset_transfer_staff_name
     * 20230707目前数据量, TH-11795 MY-1542 PH-2857 LA-154 ID-0 VN-0
     */
    public function update_material_asset_transfer_staff_nameAction()
    {
        //查总数
        $staff_condition = 'to_staff_id > 0 and to_staff_id = to_staff_name';
        $builder = $this->modelsManager->createBuilder();
        $builder->from(MaterialAssetTransferLogModel::class);
        $builder->columns('count(id) as count');
        $builder->andWhere('is_deleted = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);
        $builder->andWhere($staff_condition);
        $count = (int)$builder->getQuery()->getSingleResult()->count;
        $step = 500;
        $hr_staff_repository = new HrStaffRepository();

        echo '处理资产转移数据开始: 共有' . $count . '条 ,执行时间:' . date('Y-m-d H:i:s') . PHP_EOL;
        $this->logger->info('处理历史数据开始: 共有' . $count . '条 ,执行时间:' . date('Y-m-d H:i:s'));
        $for_number = ceil($count / $step);
        for ($i = 0; $i < $for_number; $i++) {
            //批量查询历史数据,一次处理500条
            $builder = $this->modelsManager->createBuilder();
            $builder->from(MaterialAssetTransferLogModel::class);
            $builder->andWhere('is_deleted = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);
            $builder->andWhere($staff_condition);
            $builder->orderby('id ASC');
            //修改完下次条件就不命中已修改的了, 所以limit不用加第二个参数, 如果本页失败会停止脚本
            $builder->limit($step);
            $items_obj = $builder->getQuery()->execute();
            //获取用户id
            $to_user_ids = array_column($items_obj->toArray(), 'to_staff_id');
            $all_user_ids = array_values(array_unique($to_user_ids));
            if (empty($all_user_ids)) {
                continue;
            }
            //查询用户
            $staff_data_kv = $hr_staff_repository->getStaffListByStaffIds($all_user_ids);

            foreach ($items_obj as $key => $value) {
                //接收人信息
                $to_staff_info = $staff_data_kv[$value->to_staff_id] ?? [];
                if (!empty($to_staff_info) && $value->to_staff_name != $to_staff_info['name']) {
                    $value->to_staff_name = $to_staff_info['name'] ?? '';
                }
                if ($value->save() === false) {
                    $this->logger->warning('处理资产转移数据: 更新失败 : updateData=' . json_encode($value->toArray(), JSON_UNESCAPED_UNICODE) . ';  可能的原因是=' . get_data_object_error_msg($value));
                    echo '处理资产转移数据: 更新失败, 第' . $i . '页 ,执行时间:' . date('Y-m-d H:i:s') . ';  可能的原因是=' . get_data_object_error_msg($value) . PHP_EOL;
                    exit();
                }
            }
            echo '处理资产转移数据 当前处理第' . $i . '页 ,执行时间:' . date('Y-m-d H:i:s') . PHP_EOL;
            $this->logger->info('处理资产转移数据开始 当前处理第' . $i . '页 ,执行时间:' . date('Y-m-d H:i:s'));

            //处理一批休息1s
            sleep(3);
        }
        echo '处理资产转移数据结束 共处理' . $count . '条 ,执行时间:' . date('Y-m-d H:i:s') . PHP_EOL;
        $this->logger->info('处理资产转移数据结束, 共处理' . $count . '条 , 执行时间:' . date('Y-m-d H:i:s'));

    }

    /**
     * 每天当地时间6点,捞取转移记录里，接收状态为“待接收”，创建时间距离跑脚本时间超过 "资产转移间隔时间" ,则给待接收人发送Push和by消息
     * php app/cli.php material_transfer asset_pending_receipt_reminder
     */
    public function asset_pending_receipt_reminderAction()
    {
        $this->checkLock(__METHOD__);
        $log = '任务名称: ' . __FUNCTION__ . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log .= '初始内存: ' . memory_usage() . PHP_EOL;
        $material_asset_transfer_interval_days = EnumsService::getInstance()->getSettingEnvValue('material_asset_transfer_interval_days', 0);
        if ($material_asset_transfer_interval_days) {
            $end_created_at = date('Y-m-d H:i:s', strtotime('-' . $material_asset_transfer_interval_days . 'days'));
            $i = 1;
            $deal_audit_data =  $this->getReminderStaffList($i, $end_created_at);
            while (!empty($deal_audit_data)) {
                $log .= '第' . $i . '页处理开始' . PHP_EOL;
                $staff_ids = array_column($deal_audit_data, 'to_staff_id');
                $message_info = ['message_content' => '', 'category' => MaterialEnums::MATERIAL_TRANSFER_MESSAGE_CATEGORY_PENDING_RECEIPT_REMINDER];
                foreach ($deal_audit_data as $key => $value) {
                    $message_info['staff_users'][] = ['id' => $value['to_staff_id']];
                }
                AssetTransferMessageService::getInstance()->sendPendingReceiptReminder($message_info);
                //推送PUSH - by
                AssetTransferMessageService::getInstance()->sendPush($staff_ids, AssetTransferMessageService::$push_type_pending_receipt_reminder, 'flashbackyard://fe/tab?index=message');
                //推送PUSH - kit
                AssetTransferMessageService::getInstance()->sendPush($staff_ids, AssetTransferMessageService::$push_type_pending_receipt_reminder, 'flashbackyard://fe/tab?index=message', 'kit');
                $log .= '第' . $i . '页处理工号组：' . json_encode($staff_ids, JSON_UNESCAPED_UNICODE) . PHP_EOL;
                $log .= '第' . $i . '页处理结束' . PHP_EOL;
                sleep(1);
                $i += 1;
                $deal_audit_data =  $this->getReminderStaffList($i, $end_created_at);
            }
        } else {
            $log .= '资产转移间隔时间未配置无需提醒' . PHP_EOL;
        }
        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $this->logger->info($log);
        $this->clearLock(__METHOD__);
        exit($log);
    }

    /**
     * 获取需要审核的出库单列表
     * @param int $page_num 当前页码
     * @param string $end_time 结束时间
     * @return mixed
     */
    private function getReminderStaffList($page_num, $end_time)
    {
        $page_size = 100;
        return MaterialAssetTransferLogModel::find([
            'columns' => 'DISTINCT to_staff_id',
            'conditions' => 'to_staff_id > :to_staff_id: and status = :status: and is_deleted = :is_deleted: and created_at <= :created_at:',
            'bind' => ['to_staff_id' => 0, 'status' => MaterialEnums::TRANSFER_LOG_STATUS_UNRECEIVED, 'is_deleted' => GlobalEnums::IS_NO_DELETED, 'created_at' => $end_time],
            'limit' => $page_size,
            'offset' => $page_size * ($page_num - 1),
        ])->toArray();
    }

}