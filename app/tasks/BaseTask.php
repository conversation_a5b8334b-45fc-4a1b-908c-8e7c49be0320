<?php

use App\Library\Response;
use Phalcon\CLI\Task;
use App\Library\Logger;
use App\Library\BaseService;
use Phalcon\Http\ResponseInterface;
use App\Modules\Common\Services\EnumsService;

/**
 * Class BaseTask
 * @property Logger $logger
 * @package App\Library
 */
class BaseTask extends Task
{
    protected static $t;
    protected static $language;
	public function initialize()
	{
		//设置零时区
        date_default_timezone_set(env('default_timezone','Asia/Bangkok'));
        ini_set('memory_limit', -1);
    }

    /**
     * 设置语种
     * @param string $locale 语种
     */
    public static function setLanguage($locale)
    {
        $locale = $locale == 'zh-CN' ? $locale : strtolower($locale);
        static::$language = $locale;
        BaseService::setLanguage($locale);
        static::$t = BaseService::getTranslation($locale);
    }

    /**
     * 返回请求json串
     *
     * @param $code
     * @param $message
     * @param $data
     * @param int $statusCode
     * @return \Phalcon\Http\Response|ResponseInterface
     */
    protected function returnJson($code, $message, $data=null,$statusCode=Response::OK)
    {
        $result = [
            'code' => $code,
            'message' => $message,
            'data'=> $data,
        ];
        $this->response->setStatusCode($statusCode);
        $this->response->setJsonContent($result);

        return $this->response;
    }

    /**
     * check进程数
     *
     * @param $process_name
     */
    function check_process($process_name)
    {
        $res = array();
        exec("ps -ef | grep " . $process_name . "|grep -v grep | wc -l", $res);
        echo "进程数" . (trim($res[0])) . PHP_EOL;
        $process_num = trim($res[0]);
        if (($process_num) > 2) {
            exit;
        }
    }

    /**
     * 生成自定义的Excel文件
     * @param array $head Excel 列名
     * @param array $data Excel 业务数据
     * @param array $extra_data 额外数据,
     * 示例:
     * file_desc: Excel 首行说明文案内容
     * end_column_char: 最后列字母
     * column_width: 列宽
     * header_column_font_color: 表头字体颜色
     * header_columns: 表头包含列
     * header_column_row: 表头所在行
     *
     * @return string
     */
    protected function customizeExcelToFile(array $head, array $data = [], array $extra_data = [])
    {
        $config = [
            'path' => sys_get_temp_dir() . '/'
        ];
        $excel = new \Vtiful\Kernel\Excel($config);

        // Excel；临时文件名
        $fileName = $extra_data['file_name'] ?? time() . '_excel.xlsx';

        // 此处会自动创建一个工作表
        $fileObject = $excel->fileName($fileName);
        $fileHandle = $fileObject->getHandle();

        // 设置Excel首行文件说明文案
        $end_column_char = $extra_data['end_column_char'] ?? 'Z';
        if (!empty($extra_data['file_desc'])) {
            $format    = new \Vtiful\Kernel\Format($fileHandle);
            $wrapStyle = $format->wrap()->align(\Vtiful\Kernel\Format::FORMAT_ALIGN_VERTICAL_CENTER)->toResource();
            $column_high = $extra_data['column_high'] ?? 200;
            $fileObject->mergeCells("A1:{$end_column_char}1", $extra_data['file_desc'])->setRow("A1:{$end_column_char}1", $column_high, $wrapStyle);
        }

        // 设置Excel第二行格式加文案[分组]
        if (!empty($extra_data['second_content'])) {
            $format = new \Vtiful\Kernel\Format($fileHandle);
            $wrapStyle = $format->wrap()->align(\Vtiful\Kernel\Format::FORMAT_ALIGN_VERTICAL_CENTER)->toResource();
            foreach ($extra_data['second_content'] as $item) {
                $fileObject->mergeCells("{$item['start_column_char']}2:{$item['end_column_char']}2", $item['file_desc'])->setRow("{$item['start_column_char']}2:{$item['end_column_char']}2", 20, $wrapStyle);
            }
        }

        // 设置列宽
        if (!empty($extra_data['column_width'])) {
            $fileObject->header($head)->data($data)->setColumn("A:{$end_column_char}", $extra_data['column_width']);
        }

        // 设置表头字体颜色
        if (!empty($extra_data['header_column_font_color'])) {
            $format    = new \Vtiful\Kernel\Format($fileHandle);
            $colorStyle = $format->fontColor($extra_data['header_column_font_color'])->toResource();
            foreach ($extra_data['header_columns'] as $column) {
                $fileObject->setRow("{$column}{$extra_data['header_column_row']}", 15, $colorStyle);
            }
        }

        // 最后的最后，输出文件
        $filePath = $fileObject->output();

        //判断文件是否生成成功了
        if (!is_file($filePath)) {
            return '';
        }

        return $filePath;
    }

    //验证任务是否执行中
    protected function checkLock($taskName, $ex = 3600)
    {
        $key = 'taskLock:' . $taskName;

        if (in_array(get_runtime_env(), ['dev', 'training'])) {
            $key = get_country_code() . ':' . $key;
        }

        //任务锁
        $redis = $this->getDI()->get("redis");
        $rs    = $redis->set($key, 1, ['nx', 'ex' => $ex]);//锁1小时
        if (!$rs) {
            $message = $taskName . ' 任务已在执行中, 不可并行处理, 请知悉, time: ' . get_datetime_with_milliseconds();
            $this->logger->notice(['task_process_key' => $key, 'message' => $message]);
            exit($message);
        } else {
            $this->logger->info(['task_process_key' => $key, 'message' => '进程加锁成功', 'time' => get_datetime_with_milliseconds()]);
        }
    }

    //释放锁
    protected function clearLock($taskName)
    {
        $key = 'taskLock:' . $taskName;

        if (in_array(get_runtime_env(), ['dev', 'training'])) {
            $key = get_country_code() . ':' . $key;
        }

        $clear_res = $this->getDI()->get("redis")->del($key);//释放锁
        if (!$clear_res) {
            $this->logger->notice(['task_process_key' => $key, 'message' => '进程锁释放异常, 请排查', 'time' => get_datetime_with_milliseconds()]);
        }
        return $clear_res;
    }

    /**
     * SAP推送日期配置
     * @return mixed
     */
    protected function getSapTaskSendDate()
    {
        return EnumsService::getInstance()->getSettingEnvValue('sap_task_send_date');
    }
}




