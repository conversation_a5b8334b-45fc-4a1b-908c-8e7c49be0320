<?php


use App\Library\Enums\ShopEnums;
use App\Models\oa\SettingEnvModel;
use App\Models\backyard\InteriorOrdersModel;

/**
 * 员工商城 订单处理
 * Class InventoryOrdersTask
 */
class InventoryOrdersTask extends BaseTask
{

    /**
     *  员工商城收款核对提醒 发送邮件  早上9点开始到18点结束每2小时执行一次定时任务
     *  当前订单中所有支付方式为线下支付、订单状态为待发货、支付凭证审核状态为待核对的数据
     *
     *  php cli.php  inventory_orders send_email_interior_orders_audit
     */
    public function send_email_interior_orders_auditAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $this->check_process($process_name);
        echo date('Ymd H:i:s') . ' 脚本开始执行' . PHP_EOL;
        try {
            //邮箱
            $emails = SettingEnvModel::getValByCode('interior_orders_payment_voucher_audit_email');
            if (empty($emails)) {
                echo '支付凭证审核邮件配置为空' . PHP_EOL;
                $this->logger->info('interior_orders_payment_voucher_audit_email');
                exit();
            }
            $interior_orders = InteriorOrdersModel::find([
                'columns'    => ['id'],
                'conditions' => 'pay_method = :pay_method: and order_status = :order_status: and payment_voucher_audit_status = :payment_voucher_audit_status:',
                'bind'       => [
                    'pay_method'                   => ShopEnums::PAY_METHOD_OFFLINE_PAY,
                    'order_status'                 => ShopEnums::ORDER_STATUS_WARTING_SEND_CODE,
                    'payment_voucher_audit_status' => ShopEnums::PAYMENT_VOUCHER_AUDIT_STATUS_WAIT,
                ],
            ])->toArray();
            if (!empty($interior_orders)) {
                $email_arr   = explode(',', $emails);
                $title       = '员工商城收款核对提醒 Employee Mall payment verification reminder';
                $html        = '<p>员工商城有待核对的订单，请登录OA系统，进入“员工商城>收款核对”进行处理！</p>';
                $html        .= '<p>There are orders to be verified in the Employee Mall, Please log in to the OA system and enter "Employee Mall > Payment Verification" for processing!</p>';
                $send_result = $this->mailer->sendAsync($email_arr, $title, $html);
                if ($send_result) {
                    echo '发送收款核对邮件提醒,收件邮箱:' . $emails . PHP_EOL;
                } else {
                    echo '发送收款核对邮件提醒发送失败,收件邮箱:' . $emails . PHP_EOL;
                    $this->logger->warning('发送收款核对邮件提醒发送失败 收件邮箱:' . $emails);
                }
            } else {
                echo '暂无数据需要发送邮件' . PHP_EOL;
            }
        } catch (Exception $e) {
            $this->logger->warning('发送收款核对邮件提醒:' . $e->getMessage());
            echo date('Ymd H:i:s') . ' 发送收款核对邮件提醒[异常]' . $e->getMessage() . PHP_EOL;
        }
        echo date('Ymd H:i:s') . ' 脚本执行结束' . PHP_EOL;
    }





}
