<?php

use App\Library\Enums\GlobalEnums;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Organization\Models\SysDepartmentModel;
use App\Modules\Purchase\Models\PurchaseOrderProduct;
use App\Modules\Purchase\Models\PurchasePayment;
use App\Modules\Purchase\Models\PurchaseStorageProduct;
use App\Modules\Purchase\Services\OrderService;
use App\Modules\Purchase\Models\PurchaseOrder;
use App\Modules\Training\Services\TaskService;
use App\Modules\Purchase\Models\PurchaseStorage;
use App\Modules\Purchase\Services\SapsService;
use App\Modules\Purchase\Models\PurchaseSapTask;
use App\Modules\Purchase\Services\ScmService;
use App\Modules\Reimbursement\Services\SapService as reSapService;
use App\Modules\Purchase\Services\RemindService;
use App\Modules\Purchase\Models\PurchaseApply;
use App\Library\Validation\ValidationException;
use App\Library\ErrCode;
use App\Repository\HrStaffRepository;

use App\Library\Enums;

class PurchaseorderTask extends BaseTask
{

    public function syncSapAction($params)
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $this->check_process($process_name);
        echo 'begin: ' . date('Y-m-d H:i:s');
        echo PHP_EOL;
        $is_specify_date = $params[0]??2;
        $specify_date= $params[1]??'2021-11-27';

        echo '指定日期：';echo ($is_specify_date==1)?'是:'.$specify_date:'否';echo PHP_EOL;
        $purchase_order_place = array_column(OrderService::getInstance()->getEnvByCode('purchase_order_place'), 'place_name', 'id');
        $cost_company_id = (new EnumsService())->getSettingEnvValueIds('purchase_sap_company_ids');
        $t                    = TaskService::getTranslation(strtolower(env('country_code', 'th')));
        $sap_company_ids= SysDepartmentModel::find(
            [
                'conditions' => 'id in ({ids:array})',
                'bind'       => ['ids' => $cost_company_id]
            ]
        )->toArray();
        $cost_company_ids = implode(',', $cost_company_id);

        $sap_company_ids = array_column($sap_company_ids,'sap_company_id','id')??[];
        $db_obj = $this->db_oa;
        try {
            $apply_date = $params[2] ?? '2021-05-18';
            $helper_apply_date = $params[2] ?? '2022-11-10';//辅助类申请起始日期

            switch (get_country_code()) {
                case 'TH':
                    $apply_date = $params[2] ?? '2021-01-18';
                    break;
                case 'PH':
                    $apply_date = $params[2] ?? '2021-05-18';
                    break;
                case 'MY':
                    $apply_date = $params[2] ?? '2022-03-01';
                    break;
                default;
            }
            $send_data = array();
            //发送邮件 目前只有泰国
            $email = \App\Modules\Common\Models\EnvModel::getEnvByCode('purchase_email');
            $email = explode(',',$email);

            $i   = 0;
            $sql  = 'select id,pono,sap_supplier_no,cost_company,currency,loan_time_id,purchase_type,apply_date from purchase_order';
            $sql .= ' WHERE CASE purchase_type WHEN ' . Enums\PurchaseEnums::PURCHASE_TYPE_STOCK . ' THEN apply_date > "' . $apply_date .'" ELSE apply_date > "' . $helper_apply_date . '" END';
            $sql .= ' AND status = '.Enums::PAYMENT_APPLY_STATUS_APPROVAL;
            $sql .= ' AND purchase_type in ( ' . Enums\PurchaseEnums::PURCHASE_TYPE_STOCK . ',' . Enums\PurchaseEnums::PURCHASE_TYPE_COST . ' )';
            $sql .= ' AND cost_company in ( ' . $cost_company_ids . ' )';
            $sql .= ' AND sap_order_no is null';
            $sql .= ' LIMIT ' . $i . ', 100';
            $data          = $db_obj->query($sql);
            $deal_sap_data = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            $x = 0;
            while (!empty($deal_sap_data)) {
                foreach ($deal_sap_data as $key => $value) {
                    $request_data = [];
                    $return_data  = [];
                    //特定日期发送
                    if ($is_specify_date == 1 && $value['apply_date'] != $specify_date) {
                        continue;
                    }

                    $apply        = PurchaseOrder::findFirst([
                        'id = :id:',
                        'bind' => ['id' => $value['id']]
                    ]);

                    if(empty($apply)){
                        continue;
                    }

                    $products_obj = $apply->getProducts();
                    $products     = $products_obj->toArray();

                    foreach ($products as $k => $item) {
                        $products[$k]['delivery_place_txt'] = $purchase_order_place[$item['delivery_place']] ?? '';
                        $products[$k]['vat_code']           = $this->vat_code($value['currency'], $item['vat7_rate']);
                        $products[$k]['no_tax_price']       = bcdiv($item['no_tax_price'], 1000, 2);
                        $products[$k]['sap_item_id'] = 10 * ($k + 1);
                    }
                    $request_data = [
                        'currency'        => $t->_(GlobalEnums::$currency_item[$value['currency']]),
                        'cost_company'    => $sap_company_ids[$value['cost_company']] ?? 'FEX01',
                        'sap_supplier_no' => trim($value['sap_supplier_no']),
                        'loan_time_id'    => $value['loan_time_id'],
                        'pono'            => $value['pono'],
                        'purchase_type'   => $value['purchase_type'],
                        'product_v1'      => $products
                    ];
                    $z            = 0;
                    echo 'purchase_order id:'. $value['id'];
                    echo PHP_EOL;

                    while (empty($return_data) && $z < 1) {
                        $z++;
                        //先查询是否在sap 创建  是 更细数据   否 创建

                        $update_data = SapsService::getInstance()->getSapNo($value['pono']);

                        if ($update_data['code'] == 1) {
                            $apply->i_update(['sap_order_no' => $update_data['sap_order_no'], 'updated_at' => date('Y-m-d H:i:s')]);
                            echo 'success update  purchase_order id:' . $value['id'];

                        } else {
                            $return_data = OrderService::getInstance()->createSapOrder($request_data);
                            if (isset($return_data['business_transaction_id']) && !empty($return_data['business_transaction_id'])) {
                                $x++;
                                $apply->i_update(['sap_order_no' => $return_data['business_transaction_id'], 'updated_at' => date('Y-m-d H:i:s')]);

                                // 17353需求，需要记录sap行号
                                $product_info_arr = array_column($products, 'sap_item_id', 'id');
                                foreach ($products_obj as $product) {
                                    $product->sap_item_id = $product_info_arr[$product->id] ?? 0;
                                    $product->save();
                                }
                                echo 'success purchase_order id:' . $value['id'];
                            }
                        }


                        sleep(5);
                    }

                    echo PHP_EOL;

                    //新增需求 同步 sap 后 发邮件 https://l8bx01gcjr.feishu.cn/docs/doccn1FyP5dLv9lAtrEYMKXrpcb#
                    if(!empty($return_data['business_transaction_id']) && !empty($email)){
                        $row['pono'] = $value['pono'];
                        $row['sap_order_no'] = $return_data['business_transaction_id'];
                        $send_data[] = $row;
                    }

                }

                //拼接 html  发送邮件
                if(!empty($send_data)){
                    $title = "SAP PO creation reminder";
                    $html = $this->format_html($send_data);
                    $this->mailer->sendAsync($email, $title, $html);
                }

                sleep(1);
                $i   += 100;
                $sql  = 'SELECT id,pono,sap_supplier_no,cost_company,currency,loan_time_id,purchase_type,apply_date FROM purchase_order';
                $sql .= ' WHERE CASE purchase_type WHEN ' . Enums\PurchaseEnums::PURCHASE_TYPE_STOCK . ' THEN apply_date > "' . $apply_date .'" ELSE apply_date > "' . $helper_apply_date . '" END';
                $sql .= ' AND status = '.Enums::PAYMENT_APPLY_STATUS_APPROVAL;
                $sql .= ' AND purchase_type in ( ' . Enums\PurchaseEnums::PURCHASE_TYPE_STOCK . ',' . Enums\PurchaseEnums::PURCHASE_TYPE_COST . ' )';
                $sql .= ' AND cost_company in ( ' . $cost_company_ids . ' )';
                $sql .= ' AND sap_order_no is null';
                $sql .= ' LIMIT ' . $i . ', 100';
                $data          = $db_obj->query($sql);
                $deal_sap_data = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            }

            echo 'purchase_order 成功:' . $x.'条数'.PHP_EOL;

            echo 'end: ' . date('Y-m-d H:i:s');

        } catch (Exception $e) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('purchase_sap_task_exception:' . $e->getMessage());
            var_dump($e->getMessage());
            return false;
        }


    }

    /**
     * 将历史已同步至SAP的PO单行上补录SAP行编号
     * @return bool
     */
    public function sync_history_sap_item_idAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $this->check_process($process_name);
        echo 'begin: ' . date('Y-m-d H:i:s');
        echo PHP_EOL;
        try {
            $i = 1;
            $deal_sap_list =  $this->getHistorySapItemIdSapList($i);
            $deal_sap_data = $deal_sap_list->toArray();
            while (!empty($deal_sap_data)) {
                echo '第' . $i . '页处理开始' . PHP_EOL;
                foreach ($deal_sap_list as $order) {
                    echo '订单：' . $order->pono . ' 订单ID：' . $order->id . ' 采购类型：' . $order->purchase_type . ' 开始处理' . PHP_EOL;
                    $products = $order->getProducts();
                    //只有行号都是0的才需要修复
                    foreach ($products as $key => $product) {
                        // 库存类 = 10 * 行号；辅助类 = 行号
                        $product->sap_item_id = ($order->purchase_type == Enums\PurchaseEnums::PURCHASE_TYPE_STOCK) ? 10 * ($key + 1) : ($key + 1);
                        $product->save();
                        echo '产品id：' . $product->id . ' 行号更新为：' . $product->sap_item_id . '完毕' . PHP_EOL;
                    }

                    echo '订单：' . $order->pono . '处理完毕' . PHP_EOL . PHP_EOL;
                }

                echo '第' . $i . '页处理结束' . PHP_EOL . PHP_EOL;
                sleep(1);
                $i += 1;
                $deal_sap_list = $this->getHistorySapItemIdSapList($i);
                $deal_sap_data = $deal_sap_list->toArray();
            }
            echo 'end: ' . date('Y-m-d H:i:s');
        } catch (Exception $e) {
            $this->logger->warning('purchase_sap_history_item_id_task_exception:' . $e->getMessage());
        }
    }

    /**
     * 同步传输至SAP的库存类、辅助类PO单据的行号
     * @param int $page_num 当前页码
     * @return mixed
     */
    private function getHistorySapItemIdSapList($page_num)
    {
        $page_size = 100;
        //传输至SAP的库存类、辅助类PO单据
        return PurchaseOrder::find([
            'conditions' => 'sap_order_no is not null and purchase_type in ({purchase_type:array})',
            'bind' => ['purchase_type' => [Enums\PurchaseEnums::PURCHASE_TYPE_STOCK, Enums\PurchaseEnums::PURCHASE_TYPE_COST]],
            'limit' => $page_size,
            'offset' => $page_size * ($page_num - 1),
        ]);
    }

    /**
     * 泰国 vat code
     * */
    public function vat_th_code($currency, $vat_rate)
    {
        $vat_code = '';
        if (0 == $vat_rate) {
            $vat_code = '011';
        }
        switch ($currency) {
            case 1:
                if ($vat_rate == 7) {
                    $vat_code = 'Z20';
                }
                break;
            default:
                if ($vat_rate == 7) {
                    $vat_code = '201';
                }
        }

        return $vat_code;
    }
    /**
     * 审核入库单
     * */
    public function auditScmAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $this->check_process($process_name);
        echo 'start: ' . date('Y-m-d H:i:s') . PHP_EOL;

        $db_obj = $this->db_oa;
        try {
            $i          = 0;
            $start_time = date('Y-m-d H:i:s', time() - 3600);
            $end_time   = date('Y-m-d H:i:s');
            $sql        = "SELECT `id`,`scm_no`,`status`,`mach_code` FROM `purchase_storage` WHERE  `update_at`>'{$start_time}' AND `update_at`<'{$end_time }' AND `status` = 1 limit {$i} ,100";

            $data            = $db_obj->query($sql);
            $deal_audit_data = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            while (!empty($deal_audit_data)) {
                foreach ($deal_audit_data as $key => $value) {
                    $return_data      = [];
                    $purchase_storage = PurchaseStorage::findFirst([
                        'id = :id:',
                        'bind' => ['id' => $value['id']],
                    ]);
                    if (empty($purchase_storage)) {
                        continue;
                    }

                    $z = 0;
                    while (empty($return_data) && $z < 2) {
                        $z++;
                        $return_data = (new ScmService($value['mach_code']))->audits(['orderSn' => $purchase_storage->scm_no]);

                        if (isset($return_data['code']) && 1 == $return_data['code']) {
                            $purchase_storage->i_update(['status' => 2, 'update_at' => date('Y-m-d H:i:s')]);
                            echo 'purchase_storage success id:' . $value['id'];
                            echo PHP_EOL;
                        }

                        sleep(1);
                    }
                }


                sleep(1);
                $i               += 100;
                $sql             = "SELECT `id`,`scm_no`,`status`,`mach_code` FROM `purchase_storage` WHERE  `update_at`>'{$start_time}' AND `update_at`<'{$end_time }' AND `status` = 1 limit {$i} ,100";
                $data            = $db_obj->query($sql);
                $deal_audit_data = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            }
            echo 'end: ' . date('Y-m-d H:i:s');
        } catch (Exception $e) {
            $this->logger->warning('purchase-scm-audit-exception: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 同步sap任务号
     *
     * */
    public function updateSapTaskAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $this->check_process($process_name);
        echo 'start: ' . date('Y-m-d H:i:s') . PHP_EOL;
        exit;//暂停

        $db_obj = $this->db_oa;
        try {

            $i   = 0;


            $start_time =date('Y-m-d H:i:s',time()-3600*24*17);
            $end_time = date('Y-m-d H:i:s');
            $sql = "SELECT `id`,`pono`,`sap_order_no` FROM `purchase_order` WHERE  `updated_at`>'{$start_time}' AND `updated_at`<'{$end_time }' AND `is_sap_task` = 0 AND  `sap_order_no` is not null limit {$i} ,100";

            $data            = $db_obj->query($sql);
            $deal_sap_task = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            while (!empty($deal_sap_task)) {
                foreach ($deal_sap_task as $key => $value) {


                    $return_data      = [];
                    $purchase_order = PurchaseOrder::findFirst([
                        'id = :id:',
                        'bind' => ['id' => $value['id']]
                    ]);
                    if (empty($purchase_order)) {
                        continue;
                    }

                    $z = 0;
                    while (empty($return_data) && $z < 2) {
                        echo $value['sap_order_no'].PHP_EOL;
                        $z++;
                        $return_data = SapsService::getInstance()->updateSapTask(['sap_order_no' => $value['sap_order_no']]);
                        if (!empty($return_data)) {
                            $purchase_order_model = new PurchaseSapTask();
                            $purchase_order_model->i_create([
                                'sap_order_no' => $value['sap_order_no'],
                                'return_data'  => json_encode($return_data),
                                'create_at'    => date('Y-m-d H:i:s'),
                                'update_at'    => date('Y-m-d H:i:s')
                            ]);

                            $purchase_order->i_update(['is_sap_task' => 1,'updated_at'    => date('Y-m-d H:i:s')]);

                            echo 'storage_order_id :' .$value['id'];
                            echo PHP_EOL;

                        }

                        sleep(1);
                    }


                }

                sleep(1);
                $i               += 100;
                $sql = "SELECT `id`,`pono`,`sap_order_no` FROM `purchase_order` WHERE  `updated_at`>'{$start_time}' AND `updated_at`<'{$end_time }' AND `is_sap_task` = 0 limit {$i} ,100";
                $data            = $db_obj->query($sql);
                $deal_sap_task = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            }
            echo 'end: ' . date('Y-m-d H:i:s');

            exit();
        } catch (Exception $e) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('purchase-sap-task-num-exception: ' . $e->getMessage());
            var_dump($e->getMessage());
            return false;
        }
    }


    public function confirmSapTaskInAction($params)
    {
        $this->checkLock(__METHOD__);

        echo 'start: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $days = $params[0]??60;

        $db_obj = $this->db_oa;
        try {
            $i          = 0;

            $start_time = date('Y-m-d H:i:s', time() - 3600*24*$days);

            $end_time   = date('Y-m-d H:i:s');

            $sql = "SELECT `id`,`sap_order_no`,`update_at`,`scm_no` FROM `purchase_storage` WHERE  `update_at`>'{$start_time}' AND `update_at`<'{$end_time}' AND `status`= 3 AND `purchase_type`=18 AND `is_sync_sap` = 1  limit {$i} ,100";

            $data              = $db_obj->query($sql);
            $deal_storage_data = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            /**
             * 12614【ALL-OA】资产标准型号&采购入库&资产台账-P1
             * 原来是根据product_option_code从purchase_product_list表中找base_uom字段作为sap单位传输
             * 本期需求修改为从入库通知单产品清单上unit作为sap单位传输
             * 需求修改为获取新表 barcode 动态单位
             */

            $purchase_unit_products  = \App\Modules\Material\Models\MaterialSauModel::find([
                'columns'    => 'barcode,sap_unit',
                'conditions' => 'is_deleted=:is_deleted:',
                'bind'       => ['is_deleted' => 0]
            ])->toArray();
           $purchase_unit_products = array_column($purchase_unit_products,'sap_unit','barcode');

            while (!empty($deal_storage_data)) {
                foreach ($deal_storage_data as $key => $value) {

                    $return_data   = [];
                    $post_data     = [];

                    $sap_task_data  = [];

                    $purchase_storage = PurchaseStorage::findFirst([
                        'id = :id:',
                        'bind' => ['id' => $value['id']]
                    ]);

                    //product 数据
                    $purchase_storage_products = PurchaseStorageProduct::find([
                        'columns'    => 'product_option_code,real_quantity_received,unit, item_id',
                        'conditions' => 'psnoid =:id:',
                        'bind'       => ['id' => $value['id']]
                    ])->toArray();

                    //sap 任务号获取
                    $sap_task_data = SapsService::getInstance()->updateSapTask(['scm_no' => $value['scm_no']]);
                    $purchase_order_model = new PurchaseSapTask();
                    $purchase_order_model->i_create([
                        'sap_order_no' => $value['sap_order_no'],
                        'psno'         => $purchase_storage->psno,
                        'return_data'  => json_encode($sap_task_data ),
                        'create_at'    => date('Y-m-d H:i:s'),
                        'update_at'    => date('Y-m-d H:i:s')
                    ]);

                    usleep(100000);//防止sap 阻塞

                    if (empty($purchase_storage) || empty($sap_task_data) || empty($purchase_storage_products)) {
                        echo 'continue id:' . $value['id'] . PHP_EOL;
                        continue;
                    }

                    //V21690 按照barcode+sap行号对应任务号 回传入库单实际收货数量
                    $barcode_item_purchase_storage_products = [];
                    foreach ($purchase_storage_products as $item) {
                        $barcode_item_purchase_storage_products[$item['product_option_code'] . '_' . $item['item_id']] = $item;
                    }

                    $MaterialOutputs = [];
                    $MaterialOutput  = $sap_task_data['SiteLogisticsTaskReferencedObject']['SiteLogisticsLotOperationActivity']['MaterialOutput'];

                    if (!isset($MaterialOutput[0])) {
                        $MaterialOutput['num']  = $barcode_item_purchase_storage_products[$MaterialOutput['ProductID'] . '_' . $MaterialOutput['EX_POItemNumber']]['real_quantity_received'] ?? 0;
                        $MaterialOutput['unit'] = $purchase_unit_products[$MaterialOutput['ProductID']] ?? 'EA';

                        $MaterialOutputs[]     = $MaterialOutput;
                    } else {
                        foreach ($MaterialOutput as $k => $v) {
                            $v['num']  = $barcode_item_purchase_storage_products[$v['ProductID'] . '_' . $v['EX_POItemNumber']]['real_quantity_received'] ?? 0;
                            $v['unit'] = $purchase_unit_products[$v['ProductID']] ?? 'EA';
                            $MaterialOutputs[] = $v;
                        }
                    }

                    $post_data = [
                        'SiteLogisticsTaskID'                   => $sap_task_data['SiteLogisticsTaskID'] ?? '',
                        'SiteLogisticTaskUUID'                  => $sap_task_data['SiteLogisticsTaskUUID'] ?? '',
                        'date'                                  => date('Y-m-d',strtotime($value['update_at'])),
                        'ReferencedObjectUUID'                  => $sap_task_data['SiteLogisticsTaskReferencedObject']['ReferencedObjectUUID'] ?? '',
                        'SiteLogisticsLotOperationActivityUUID' => $sap_task_data['SiteLogisticsTaskReferencedObject']['SiteLogisticsLotOperationActivity']['SiteLogisticsLotOperationActivityUUID'] ?? '',
                        'MaterialOutput'                        => $MaterialOutputs
                    ];


                    $z = 0;
                    while (empty($return_data) && $z < 2) {
                        $z++;
                        $return_data = SapsService::getInstance()->confirmSapTaskIn($post_data);

                        if (isset($return_data['SiteLogisticsTaskLog']['SiteLogisticsTaskSeverityCode']) && 'S' == $return_data['SiteLogisticsTaskLog']['SiteLogisticsTaskSeverityCode']) {
                            $purchase_storage->i_update(['status'=>4, 'update_at' => date('Y-m-d H:i:s')]);
                            echo 'purchase_storage success id:' . $value['id'];
                            echo PHP_EOL;
                        }

                        sleep(1);
                    }
                }

                sleep(1);
                $i                 += 100;
                $sql               = "SELECT `id`,`sap_order_no`,`update_at`,`scm_no` FROM `purchase_storage` WHERE  `update_at`>'{$start_time}' AND `update_at`<'{$end_time}' AND `status`= 3 AND `purchase_type`=18 AND `is_sync_sap` = 1   limit {$i} ,100";
                $data              = $db_obj->query($sql);
                $deal_storage_data = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            }

        } catch (ValidationException $e) {
            $this->logger->info('confirmSapTaskIn-task-validation: ' . $e->getMessage());
            echo $e->getMessage() . PHP_EOL;
        } catch (Exception $e) {
            $this->logger->warning('confirmSapTaskIn-task-exception: ' . $e->getMessage());
            echo $e->getMessage() . PHP_EOL;
        }

        echo 'end: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $this->clearLock(__METHOD__);
        exit();
    }


    //整理 sap 订单提醒邮件内容
    public function format_html($data){
        $html = "<p>今日系统新增SAP采购订单如下，请知悉！</p>";
        $html .= "<p>The SAP PO numbers are listed as follow, please have a check! Thanks!</p></br></br>";

        $html .= "<table border='1'><tr><td>SAP PO number</td><td>OA PO number</td></tr>";
        foreach ($data as $da){
            $html .= "<tr><td>{$da['sap_order_no']}</td><td>{$da['pono']}</td></tr>";
        }

        $html .= "</table>";
        return $html;

    }

    /**
     * 菲律宾 vat code
     * */
    public function vat_ph_code($vat_rate)
    {
        switch ($vat_rate) {
            case 0:
                $vat_code = '012';
                break;
            case 12:
                $vat_code = '010';
                break;
            default:
                $vat_code = '';
        }

        return $vat_code;
    }


    /**
     * 马来 vat code
     * */
    public function vat_my_code($vat_rate)
    {
        switch ($vat_rate) {
            case 0:
                $vat_code = '630';
                break;
            case 5:
                $vat_code = '611';
                break;
            case 6:
                $vat_code = '810';
                break;
            case 10:
                $vat_code = '610';
                break;
            default:
                $vat_code = '';
        }

        return $vat_code;
    }

    public function vat_code($currency,$vat_rate){
        $country_code = get_country_code();
        switch ($country_code){
            case 'TH':
                $vat_code = $this->vat_th_code($currency,$vat_rate);
                break;
            case 'PH':
                $vat_code = $this->vat_ph_code($vat_rate);
                break;
            case 'MY':
                $vat_code= reSapService::getInstance()->vat_code_my($vat_rate);
                break;
            default:
            $vat_code = '';

        }
        return $vat_code;

    }

    /**
     * 入库单同步给sap
     * */
    public function sendStorageSapAction()
    {
        $this->checkLock(__METHOD__);

        echo 'start: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $days = $params[0]??60;

        $db_obj = $this->db_oa;
        try {
            $i          = 0;

            $start_time = (date('Y-m-d H:i:s', time() - 3600*24*$days)>'2022-01-01 00:00:00')?date('Y-m-d H:i:s', time() - 3600*24*$days):'2022-01-01 00:00:00';


            $end_time   = date('Y-m-d H:i:s');

            $purchase_order_place = array_column(OrderService::getInstance()->getEnvByCode('purchase_order_place'), 'place_name', 'id');

            $cost_company_id = EnumsService::getInstance()->getSettingEnvValueIds('purchase_sap_company_ids');
            if (empty($cost_company_id)) {
                throw new ValidationException('purchase_sap_company_ids 配置为空, 请确认', ErrCode::$VALIDATE_ERROR);
            }

            $sap_company_ids= SysDepartmentModel::find(
                [
                    'conditions' => 'id in ({ids:array})',
                    'bind'       => ['ids' => $cost_company_id]
                ]
            )->toArray();
            $sap_company_ids = array_column($sap_company_ids,'sap_company_id','id')??[];

            $sql = "SELECT `id`,`po_id`,`scm_no`,`sap_order_no`,`update_at` FROM `purchase_storage` WHERE  `update_at`>'{$start_time}' AND `update_at`<'{$end_time }' AND `status`= 3 AND `purchase_type`=18 AND `is_sync_sap` = 0  limit {$i} ,100";

            $data              = $db_obj->query($sql);
            $deal_storage_data = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);

            while (!empty($deal_storage_data)) {
                foreach ($deal_storage_data as $key => $value) {

                    $return_data   = [];
                    $post_data     = [];

                    $sap_task_data  = [];


                    $po_order = PurchaseOrder::findFirst([
                        'conditions' => 'id = :id:',
                        'bind'       => ['id' => $value['po_id']]
                    ]);


                    $purchase_storage_products = PurchaseStorageProduct::find([
                        'columns'    => 'product_option_code,real_quantity_received,this_time_num,item_id',
                        'conditions' => 'psnoid =:id:',
                        'bind'       => ['id' =>  $value['id']]
                    ])->toArray();

                    $po_products = PurchaseOrderProduct::getFirst(
                        [
                            'conditions' => 'poid = ?0',
                            'columns'    => 'delivery_place',
                            'bind'       => [$value['po_id']]
                        ]
                    );

                    if(empty($po_products)){
                        continue;
                    }
                    $po_products     = $po_products->toArray();

                    $delivery_place   = (isset($po_products['delivery_place'])) ? $purchase_order_place[$po_products['delivery_place']] : '';

                    $storage_sap_data = [
                        'sap_supplier_no' => $po_order->sap_supplier_no,
                        'delivery_place'  => $delivery_place,
                        'sap_order_no'    => $value['sap_order_no'],
                        'scm_no'          => $value['scm_no'],
                        'cost_company'    => $sap_company_ids[$po_order->cost_company]??'FEX01',
                        'item'            => $purchase_storage_products
                    ];
                    $is_success_sap   = 0;
                    $sap_res          = SapsService::getInstance()->storageToSap($storage_sap_data);
                    if (isset($sap_res['UUID']) && $sap_res['UUID']) {
                        $purchase_storage = PurchaseStorage::findFirst([
                            'id = :id:',
                            'bind' => ['id' => $value['id']]
                        ]);

                        $purchase_storage->i_update(['is_sync_sap'=>1, 'update_at' => date('Y-m-d H:i:s')]);
                        echo 'purchase_storage send sap success id:' . $value['id'];
                        echo PHP_EOL;
                    }
                }

                sleep(1);
                $i                 += 100;
                $sql               = "SELECT `id`,`po_id`,`scm_no`,`sap_order_no`,`update_at` FROM `purchase_storage` WHERE  `update_at`>'{$start_time}' AND `update_at`<'{$end_time }' AND `status`= 3 AND `purchase_type`=18 AND `is_sync_sap` = 0  limit {$i} ,100";
                $data              = $db_obj->query($sql);
                $deal_storage_data = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            }

        } catch (ValidationException $e) {
            $this->logger->info('send_storage_sap-task-validation: ' . $e->getMessage());
            echo $e->getMessage() . PHP_EOL;
        } catch (Exception $e) {
            $this->logger->warning('send_storage_sap-task-exception: ' . $e->getMessage());
            echo $e->getMessage() . PHP_EOL;
        }

        echo 'end: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $this->clearLock(__METHOD__);
        exit();
    }

    public function dealCargoInfoAction($params)
    {

        $purchase_payment = PurchasePayment::find([
            'columns' => 'id,inbound_no',
        ]);
        if (empty($purchase_payment)) {
            return [];
        }

        $purchase_payment = $purchase_payment->toArray();

        $insert_data = [];
        foreach ($purchase_payment as $key => $value) {

            if (!empty($value['inbound_no'])) {

                foreach (explode(',', $value['inbound_no']) as $k1 => $v1) {
                    $tmp = [];

                    $tmp = [
                        'inbound_no' => $v1,
                        'payment_id' => $value['id'],
                        'mach_code'  => $params[0],
                        'mach_name'  => $params[1],
                        'created_at' => date('Y-m-d H:i:s'),
                    ];

                    $insert_data[] = $tmp;

                }
            }

        }
        $purchase_payment_cargo = new \App\Modules\Purchase\Models\PurchasePaymentCargoModel();

        $bool = $purchase_payment_cargo->batchInsert($insert_data);
        if ($bool == false) {
            echo '处理失败' . PHP_EOL;
        }
        echo '处理成功' . PHP_EOL;

        exit();

    }

    /**
     * 采购经理/集团采购总监[给审批人发送待审批采购订单短信提醒]
     * 采购审批KPI：当天下午15点之前到达的审批，当天晚上24点之前审核完成，15点之后的审批，隔天上午12点之前审批完成
     *
     * 每周一到周五当地时间晚上18:00/上午11:00
     * @param $params
     */
    public function remind_purchaseAction($params)
    {
        $this->checkLock(__METHOD__);
        $is_exception = false;
        $log = 'purchase_order remind_purchase ' . PHP_EOL;
        $log .= date('Ymd H:i:s') . ' 脚本开始执行' . PHP_EOL;
        try {
            $hours = $params[0] ? $params[0] : date('H');
            if ($hours == 18) {
                $updated_at_start = date('Y-m-d 00:00:00');
                $updated_at_end = date('Y-m-d 15:00:00');
                $msg_content_key = 'purchase_pending_remind_night';
            } else if ($hours == 11) {
                $updated_at_start = date('Y-m-d 15:00:00', strtotime('-1 days'));
                $updated_at_end = date('Y-m-d 00:00:00');
                $msg_content_key = 'purchase_pending_remind_am';
            } else {
                $log .= 'remind_purchase: 只能设置为每周一到周五当地时间晚上晚上18:00/上午11:00跑'. PHP_EOL;
                $log .= date('Y-m-d H:i:s'). ' 脚本执行结束'. PHP_EOL;
                $this->clearLock(__METHOD__);
                exit($log);
            }
            $country_code = get_country_code();

            //采购经理【未发起过征询】
            $current_node_auditor_id = RemindService::getInstance()->getRemindPurchaseData(Enums::WF_PURCHASE_ORDER, Enums::WF_NODE_TAG_PM, $updated_at_start, $updated_at_end);
            if (empty($current_node_auditor_id)) {
                //采购经理【发起过征询】
                $current_node_auditor_id = RemindService::getInstance()->getRemindPurchaseFyrData(Enums::WF_PURCHASE_ORDER, Enums::WF_NODE_TAG_PM, $updated_at_start, $updated_at_end);
            }
            if (!empty($current_node_auditor_id)) {
                //存在满足条件的采购经理
                $log .= RemindService::getInstance()->sendRemindPendingMsg($current_node_auditor_id, RemindService::SEND_MSG_TYPE_PENDING, ['biz_type' => Enums::WF_PURCHASE_ORDER, 'remind_time' => $msg_content_key, 'country_code' => $country_code]);
            } else {
                $log .= '暂无待审批数据，无需给采购经理发送采购申请单待审批短信提醒' . PHP_EOL;
            }

            //集团采购总监【未发起过征询】
            $current_node_auditor_id = RemindService::getInstance()->getRemindPurchaseData(Enums::WF_PURCHASE_ORDER, Enums::WF_NODE_TAG_GROUP_PURCHASE_DIRECTOR, $updated_at_start, $updated_at_end);
            if (empty($current_node_auditor_id)) {
                //集团采购总监【发起过征询】
                $current_node_auditor_id = RemindService::getInstance()->getRemindPurchaseFyrData(Enums::WF_PURCHASE_ORDER, Enums::WF_NODE_TAG_GROUP_PURCHASE_DIRECTOR, $updated_at_start, $updated_at_end);
            }
            if (!empty($current_node_auditor_id)) {
                //存在满足条件的集团采购总监
                $log .= RemindService::getInstance()->sendRemindPendingMsg($current_node_auditor_id, RemindService::SEND_MSG_TYPE_PENDING, ['biz_type' => Enums::WF_PURCHASE_ORDER, 'remind_time' => $msg_content_key, 'country_code' => $country_code]);
            } else {
                $log .= '暂无待审批数据，无需给集团采购总监发送采购订单待审批短信提醒' . PHP_EOL;
            }
        } catch (Exception $e) {
            $is_exception = true;
            $log .= date('Ymd H:i:s') . ' 给采购经理/采购总监发送采购订单待审批短信提醒失败[异常]' . $e->getMessage() . PHP_EOL;
        }
        $log .= date('Ymd H:i:s') . ' 脚本执行结束' . PHP_EOL;

        if ($is_exception) {
            $this->logger->warning('purchase_order_remind_purchase_task_exception:' . $log);
        } else {
            $this->logger->info($log);
        }

        $this->clearLock(__METHOD__);
        exit($log);
    }

    /** 
     * 
     * 针对财务：PO从AP（TH）节点到财务总监（local）子节点会签的时长
     * 当地国家时间（9:00-21:00）每个整点统计
     */
    public function remind_financeAction()
    {
        $this->checkLock(__METHOD__);
        $is_exception = false;
        $log = 'purchase_order remind_finance ' . PHP_EOL;
        $log .= date('Ymd H:i:s') . ' 脚本开始执行' . PHP_EOL;
        try {
           $is_holiday = (new HrStaffRepository())->checkIsHoliday(date('Y-m-d'));
           if ($is_holiday === false) {
               $current_node_auditor_id = RemindService::getInstance()->getRemainFinancePurchaseData();
               if (!empty($current_node_auditor_id)) {
                   $log .= RemindService::getInstance()->sendRemindPendingMsg($current_node_auditor_id, RemindService::SEND_MSG_TYPE_FINANCE_PENDING, ['country_code' => get_country_code()]);
               } else {
                   $log .= '暂无23H～24H审批数据，无需给财务节点审批人发送采购申请单待审批短信提醒' . PHP_EOL;
               }
           } else {
               $log .= '公休日无需发送短信提醒' . PHP_EOL;
           }
        } catch (Exception $e) {
            $is_exception = true;
            $log .= date('Ymd H:i:s') . ' 给财务节点审批人发短信提醒失败[异常]' . $e->getMessage() . PHP_EOL;
        }
        $log .= date('Ymd H:i:s') . ' 脚本执行结束' . PHP_EOL;

        if ($is_exception) {
            $this->logger->warning('purchase_order_remind_finance_task_exception:' . $log);
        } else {
            $this->logger->info($log);
        }
        $this->clearLock(__METHOD__);
        exit($log);
    }

    /**
     * 每周一上午当地时间10点给财务部领导发短信，发邮件
     * 财务总监节点的审批通过：AP（TH）子节点会签的审批时效+财务经理会签的审批时效+财务总监会签的审批时效>24H，这种数据需要发。
     */
    public function remind_finance_departmentAction()
    {
        $this->checkLock(__METHOD__);
        $is_exception = false;
        $log = 'purchase_order remind_finance_department ' . PHP_EOL;
        $log .= date('Ymd H:i:s') . ' 脚本开始执行' . PHP_EOL;
        try {
            //获取财务部提醒人员名单
            $staff_ids = EnumsService::getInstance()->getSettingEnvValue('purchase_remind_finance_department_staff_ids');
            if (!empty($staff_ids)) {
                //获取需要提醒采购订单列表
                $purchase_order_list = RemindService::getInstance()->getFinancePurchaseLeaderList();
                $purchase_list = $purchase_order_list['purchase_list'];
                if (!empty($purchase_list)) {
                    //模板替换内容信息组
                    $content_params = ['country_code' => get_country_code(), 'apply_num' => $purchase_order_list['apply_num']];

                    //发送短信
                    $log .= RemindService::getInstance()->sendRemindPendingMsg($staff_ids, RemindService::SEND_MSG_TYPE_FINANCE_DEPARTMENT, $content_params);

                    //发送邮件
                    $log .= RemindService::getInstance()->sendEmailToLeader(explode(',', $staff_ids), $purchase_list, RemindService::SEND_MSG_TYPE_FINANCE_DEPARTMENT, $content_params);

                } else {
                    $log .= '无需给财务部领导发短信/邮件提醒' . PHP_EOL;
                }
            } else {
                $log .= '未配置提醒的财务部领导工号' . PHP_EOL;
            }
        } catch (Exception $e) {
            $is_exception = true;
            $log .= date('Ymd H:i:s') . ' 给财务部领导发短信/邮件提醒失败[异常]' . $e->getMessage() . PHP_EOL;
        }
        $log .= date('Ymd H:i:s') . ' 脚本执行结束' . PHP_EOL;

        if ($is_exception) {
            $this->logger->warning('purchase_order_remind_finance_department_task_exception:' . $log);
        } else {
            $this->logger->info($log);
        }
        $this->clearLock(__METHOD__);
        exit($log);
    }

    /**
     * 同步PO单币种与PUR单币种一致，但汇率不一致的情况：将PUR汇率 固化到 PO单汇率
     *
     * 说明: 关联 的 PUR 单汇率为0时, 该PO 汇率无需更新
     *
     * php app/cli.php purchaseorder handle_po_exchange_rate
     */
    public function handle_po_exchange_rateAction()
    {
        $log = '任务名称: ' . __FUNCTION__ . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $logger_type = 'info';

        try {
            // 1. 查找PO与PUR币种一致 但 汇率不一致的 PO单据
            // 如果PUR的汇率为0, 则PO汇率无需更新
            $columns = [
                'po.id AS po_id',
                'po.currency AS po_currency',
                'po.exchange_rate AS po_exchange_rate',
                'pur.id AS pur_id',
                'pur.currency AS pur_currency',
                'pur.exchange_rate AS pur_exchange_rate',
            ];
            $builder = $this->modelsManager->createBuilder();
            $builder->columns($columns);
            $builder->from(['po' => PurchaseOrder::class]);
            $builder->leftjoin(PurchaseApply::class, 'pur.id = po.pa_id', 'pur');
            $builder->where('po.currency = pur.currency AND po.exchange_rate != pur.exchange_rate');
            $builder->andwhere('pur.exchange_rate > 0');
            $po_list = $builder->getQuery()->execute()->toArray();

            $log .= '共有 ' . count($po_list) . ' 条po单据的汇率需更新' . PHP_EOL;

            if (empty($po_list)) {
                throw new ValidationException('无符合条件的po, po无需更新', ErrCode::$VALIDATE_ERROR);
            }

            $log .= '待处理的po单据: ' . json_encode($po_list, JSON_UNESCAPED_UNICODE) . PHP_EOL;

            // po_id => pur_exchange_rate
            $po_list = array_column($po_list, 'pur_exchange_rate', 'po_id');

            // 2. 更新PO单据
            $po_models = PurchaseOrder::find([
                'conditions' => 'id IN ({ids:array})',
                'bind' => ['ids' => array_keys($po_list)]
            ]);

            $po_null_count = 0;
            $po_null_ids = '';
            $po_save_fail_count = 0;
            $po_save_success_count = 0;
            $po_save_fail_log = '';
            foreach ($po_models as $po) {
                $new_po_exchange_rate = $po_list[$po->id] ?? null;
                if (is_null($new_po_exchange_rate)) {
                    $po_null_count++;
                    $po_null_ids .= $po->id . '; ';
                    continue;
                }

                $save_data = [
                    'exchange_rate' => $new_po_exchange_rate,
                ];
                if ($po->i_update($save_data) === false) {
                    $po_save_fail_count++;
                    $po_save_fail_log .= "po: {$po->id} -> {$new_po_exchange_rate} 更新失败, 原因可能是: " . get_data_object_error_msg($po) . PHP_EOL;
                    continue;
                }

                $po_save_success_count++;
            }

            $log .= '未找到对应pur的po: ' . $po_null_count . ' 条; po_id: ' . $po_null_ids . PHP_EOL;
            $log .= '更新成功: ' . $po_save_success_count . ' 条;' . PHP_EOL;
            $log .= '更新失败: ' . $po_save_fail_count . ' 条;' . PHP_EOL;
            $log .= '失败日志: ' . $po_save_fail_log . PHP_EOL;

        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;

        } catch (Exception $e) {
            $logger_type = 'error';
            $log .= $e->getMessage() . PHP_EOL;
        }

        $this->logger->$logger_type($log);
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        exit($log);
    }

}
