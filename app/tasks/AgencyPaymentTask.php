<?php
use App\Models\oa\AgencyPaymentModel;
use App\Models\oa\AgencyPaymentDetailModel;
use App\Library\Enums\AgencyPaymentEnums;
use App\Modules\AgencyPayment\Services\AgencyPaymentService;
use App\Library\Enums;
use App\Library\Enums\PayEnums;
use App\Library\Enums\GlobalEnums;
use App\Modules\AgencyPayment\Services\AgencyPaymentPayService;
use App\Library\ErrCode;
use App\Modules\Common\Services\EnumsService;
use App\Modules\OrdinaryPayment\Services\SapService;
use App\Repository\DepartmentRepository;

/**
 * 代理支付-任务
 * Class AgencyPaymentTask
 */
class AgencyPaymentTask extends BaseTask {
    /**
     * 代理支付-系统暂存数据清除
     * 每天定时脚本，将-1的单据创建时间超过5天的数据进行删除
     * 每1小时执行一次
     * @param array $params 参数组
     */
    public function staging_payment_delAction($params)
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log = '任务名称: ' . $process_name . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s')  . PHP_EOL;
        $log .= '初始内存: ' . memory_usage() . PHP_EOL . PHP_EOL;
        $is_exception = false;
        try {
            $days = $params[0] ?? 5;
            $created_at = date('Y-m-d H:i:s', strtotime('-' . $days . 'days'));
            $agency_payment_list = AgencyPaymentModel::find([
                'conditions' => 'status = :status: and created_at <= :created_at:',
                'bind' => ['status' => AgencyPaymentEnums::AGENCY_PAYMENT_STATUS_STAGING, 'created_at' => $created_at]
            ])->toArray();
            if ($agency_payment_list) {
                foreach ($agency_payment_list as $agency_payment_info) {
                    try {
                        AgencyPaymentService::getInstance()->stagingPaymentDel($agency_payment_info['id']);
                        $log .= $agency_payment_info['batch_no'] . '暂存数据删除成功'. PHP_EOL;
                    } catch (\Exception $e) {
                        $log .= $agency_payment_info['batch_no'] . '暂存数据删除失败，原因是：' . $e->getMessage(). PHP_EOL;
                    }
                }
            } else {
                $log .= '暂无暂存数据需要处理'. PHP_EOL;
            }
        } catch (\Exception $e) {
            $log .= $e->getMessage() . PHP_EOL;
            $is_exception = true;
        }

        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        if ($is_exception) {
            $this->logger->error('代理支付-系统暂存数据清除任务: ' . $log);
        } else {
            $this->logger->info($log);
        }
        exit($log);
    }

    /**
     * 推送代理支付单据到支付模块
     * 每5分钟执行一次
     */
    public function push_pay_moduleAction()
    {
        $this->checkLock(__METHOD__);
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log = '任务名称: ' . $process_name . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s')  . PHP_EOL;
        $log .= '初始内存: ' . memory_usage() . PHP_EOL . PHP_EOL;
        $is_exception = false;
        try {
            //查询审核通过 && 进入了支付模块 && 推送支付模块未推送 && 待支付
            $agency_payment_obj = AgencyPaymentModel::find([
                'conditions' => 'status = :status: and is_pay_module = :is_pay_module: and is_push_pay_module = :is_push_pay_module: and pay_status = :pay_status:',
                'bind' => [
                    'status' => Enums::WF_STATE_APPROVED,
                    'is_pay_module' => PayEnums::BIZ_DATA_IS_PAY_MODULE_YES,
                    'is_push_pay_module' => AgencyPaymentEnums::AGENCY_PAYMENT_IS_PUSH_PAY_MODULE_DEFAULT,
                    'pay_status' => AgencyPaymentEnums::AGENCY_PAYMENT_PAY_STATUS_PENDING,
                ]
            ]);
            $agency_payment_list = $agency_payment_obj->toArray();
            if ($agency_payment_list) {
                self::setLanguage(get_country_code());
                foreach ($agency_payment_obj as $agency_payment_info) {
                    //推送单据到支付模块
                    $res = AgencyPaymentPayService::getInstance()->pushPayModule($agency_payment_info);
                    $log .= $agency_payment_info->batch_no . '推送' . ($res['code'] == ErrCode::$SUCCESS ? '成功' : ('失败 ' . $res['message'])) . PHP_EOL;
                }
            } else {
                $log .= '暂无需推送到支付模块的代理支付单据数据，无需处理'. PHP_EOL;
            }
        } catch (\Exception $e) {
            $log .= $e->getMessage() . PHP_EOL;
            $is_exception = true;
        }

        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        if ($is_exception) {
            $this->logger->error('推送代理支付单据到支付模块任务: ' . $log);
        } else {
            $this->logger->info($log);
        }
        $this->clearLock(__METHOD__);
        exit($log);
    }

    /**
     * 变更流进支付模块、未流进支付模块 && 来自银行流水的的代理支付批次支付状态
     * 每5分钟执行一次
     */
    public function update_agency_paymentAction()
    {
        $this->checkLock(__METHOD__);
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log = '任务名称: ' . $process_name . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s')  . PHP_EOL;
        $log .= '初始内存: ' . memory_usage() . PHP_EOL . PHP_EOL;
        $is_exception = false;
        try {
            //查询审核通过 && 待支付、支付中
            $agency_payment_list = AgencyPaymentModel::find([
                'conditions' => 'status = :status: and pay_status in({pay_status:array})',
                'bind' => [
                    'status' => Enums::WF_STATE_APPROVED,
                    'pay_status' => [AgencyPaymentEnums::AGENCY_PAYMENT_PAY_STATUS_PENDING, AgencyPaymentEnums::AGENCY_PAYMENT_PAY_STATUS_ING],
                ]
            ])->toArray();
            if ($agency_payment_list) {
                self::setLanguage('zh-CN');
                foreach ($agency_payment_list as $agency_payment_info) {
                    //流进支付模块 && 未推送的不可更新
                    if ($agency_payment_info['is_pay_module'] == PayEnums::BIZ_DATA_IS_PAY_MODULE_YES && $agency_payment_info['is_push_pay_module'] == AgencyPaymentEnums::AGENCY_PAYMENT_IS_PUSH_PAY_MODULE_DEFAULT) {
                        continue;
                    }
                    //未流进支付模块 && 非来自银行流水的，不可更新
                    if ($agency_payment_info['is_pay_module'] == PayEnums::BIZ_DATA_IS_PAY_MODULE_NO) {
                        $count = AgencyPaymentDetailModel::count([
                            'conditions' => 'agency_payment_id = :agency_payment_id: and pay_from = :pay_from: and pay_status = :pay_status:',
                            'bind' => [
                                'agency_payment_id' => $agency_payment_info['id'],
                                'pay_from' => PayEnums::IS_FROM_PAY,
                                'pay_status' => AgencyPaymentEnums::AGENCY_PAYMENT_PAY_STATUS_PAY
                            ]
                        ]);
                        if (!$count) {
                            continue;
                        }
                    }

                    //更新支付状态
                    $res = AgencyPaymentPayService::getInstance()->updatePayInfo($agency_payment_info['id']);
                    $log .= $agency_payment_info['batch_no'] . '更新支付信息' . ($res['code'] == ErrCode::$SUCCESS ? '成功' : ('失败原因： ' . $res['message'])) . PHP_EOL;
                }
            } else {
                $log .= '暂无数据需要处理'. PHP_EOL;
            }
        } catch (\Exception $e) {
            $log .= $e->getMessage() . PHP_EOL;
            $is_exception = true;
        }

        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        if ($is_exception) {
            $this->logger->error('变更流进支付模块的代理支付批次支付状态任务: ' . $log);
        } else {
            $this->logger->info($log);
        }
        $this->clearLock(__METHOD__);
        exit($log);
    }

    /**
     * 代理支付模块传输sap应付发票
     * @param array $params 参数组
     */
    public function send_to_sapAction($params)
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log = '任务名称: ' . $process_name . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s')  . PHP_EOL;
        $log .= '初始内存: ' . memory_usage() . PHP_EOL . PHP_EOL;
        $is_exception = false;

        //计算本月日期1-指定日期 只执行上个月 指定日期-31号只执行本月数据
        $start_date = date('Y-m-01 00:00:00');
        $end_date = date("Y-m-{$this->getSapTaskSendDate()} 00:00:00");
        $is_little_date = true;

        if (date('Y-m-d H:i:s') >= $start_date && date('Y-m-d H:i:s') < $end_date) {
            $end_time = $start_date;
            $start_time = date('Y-m-01', strtotime(date('Y-m-01') . ' -1 month'));

        }

        $last_day = SapService::getInstance()->getLastDay(ltrim(date('m')), ltrim(date('Y')));
        $end_date_2 = date('Y-m-d H:i:s', (strtotime($start_date) + 3600 * 24 * $last_day));
        if (date('Y-m-d H:i:s') >= $end_date && date('Y-m-d H:i:s') < $end_date_2) {
            $start_time = $start_date;
            $end_time = date('Y-m-d', strtotime('+1 day'));
            $is_little_date = false;
        }

        try {
            $online_time = $params[0] ?? '';
            if (empty($online_time)) {
                throw new Exception('未指定上线日期');
            }

            self::setLanguage('en');

            //代理支付-同步sap的各项配置（供应商编码、科目编码、成本中心、税务代码、代扣代缴代码）
            $agency_payment_sap = EnumsService::getInstance()->getSettingEnvValueMap('agency_payment_sap');

            //获取费用类型枚举
            $cost_type_enums = AgencyPaymentService::getCostTypeEnums();

            $i = 0;
            $condition = ['approved_at_start' => date('Y-m-d 00:00:00', strtotime($start_time)), 'approved_at_end' => date('Y-m-d 00:00:00', strtotime($end_time)), 'online_time' => $online_time . ' 00:00:00'];
            $deal_sap_data = $this->getAgencySapList($i, $condition);
            while (!empty($deal_sap_data)) {
                //部门列表用于获取sap公司id
                $department_list = (new DepartmentRepository())->getDepartmentByIds(array_column($deal_sap_data, 'cost_company_id'), 2);

                foreach ($deal_sap_data as $key => $value) {
                    $return_data  = [];
                    // 1-指定日期不是上个月数据跳过  指定日期-31不是本月数据跳过
                    if ($is_little_date) {
                        if (ltrim(date('m', strtotime($value['approved_at']))) == ltrim(date('m'))) {
                            continue;
                        }
                    } else {
                        if (ltrim(date('m', strtotime($value['approved_at']))) != ltrim(date('m'))) {
                            continue;
                        }
                    }

                    $agency_payment_info = AgencyPaymentModel::findFirst(['conditions' => 'id = :id:', 'bind' => ['id' => $value['id']]]);
                    if (empty($agency_payment_info)) {
                        continue;
                    }

                    $log .= 'agency_payment id:' . $agency_payment_info->id . '; 批次号： ' . $agency_payment_info->batch_no . '，处理开始' . PHP_EOL;

                    //sap应付发票 0未同步1已同步2同步失败3同步重试
                    if (in_array($agency_payment_info->sync_sap, [0, 3])) {
                        $cost_type_text = self::$t->_($cost_type_enums[$agency_payment_info->cost_type] ?? '');//费用类型
                        $request_data = [
                            'batch_no' => $agency_payment_info->batch_no,
                            'apply_date' => $agency_payment_info->apply_date,
                            'approved_at' => date('Y-m-d', strtotime($agency_payment_info->approved_at)),
                            'payable_amount' => $agency_payment_info->payable_amount,//应付金额
                            'currency' => self::$t->_(GlobalEnums::$currency_item[$agency_payment_info->currency]),
                            'amount_total_vat' => $agency_payment_info->amount_total_vat,//VAT总金额
                            'ticket_desc' => $agency_payment_info->batch_no . ','. $cost_type_text,//发票描述
                            'cost_company_id' => isset($department_list[$agency_payment_info->cost_company_id]) ? $department_list[$agency_payment_info->cost_company_id]['sap_company_id'] : 'FEX01',
                            'sap_supplier_no' => isset($agency_payment_sap[$agency_payment_info->cost_type]) ? ($agency_payment_sap[$agency_payment_info->cost_type]['vendor'] ?? '') : '',
                            'items' => [
                                'voucher_description' => mb_substr($cost_type_text . ',' . $agency_payment_info->mark, 0, 40),//传费用类型的英文名称+备注，用英文逗号隔开
                                'amount_total_no_tax' => $agency_payment_info->amount_total_no_tax,
                                'ledger_account_name' => isset($agency_payment_sap[$agency_payment_info->cost_type]) ? ($agency_payment_sap[$agency_payment_info->cost_type]['ledger_account'] ?? '') : '',
                                'cost_center_name' => isset($agency_payment_sap[$agency_payment_info->cost_type]) ? ($agency_payment_sap[$agency_payment_info->cost_type]['cost_center'] ?? '') : '',
                                'tax_code' => isset($agency_payment_sap[$agency_payment_info->cost_type]) ? ($agency_payment_sap[$agency_payment_info->cost_type]['tax_code'] ?? '') : '',
                                'wht_tax_code' => isset($agency_payment_sap[$agency_payment_info->cost_type]) ? ($agency_payment_sap[$agency_payment_info->cost_type]['wht_tax_code'] ?? '') : '',
                                'finance_category_code' => isset($agency_payment_sap[$agency_payment_info->cost_type]) ? ($agency_payment_sap[$agency_payment_info->cost_type]['finance_category'] ?? '') : '',
                            ]
                        ];

                        $z = 0;
                        while (empty($return_data) && $z < 1) {
                            $z++;
                            $return_data = SapService::getInstance()->agency_payment_payable_invoices_to_sap($request_data);
                            if (isset($return_data['UUID']) && !empty($return_data['UUID'])) {
                                $agency_payment_info->i_update(['sync_sap' => 1, 'sap_uuid' => $return_data['UUID'] ?? '', 'sap_note' => '', 'updated_at' => date('Y-m-d H:i:s')]);
                                $log .= 'agency_payment id:' . $agency_payment_info->id . '; 批次号： ' . $agency_payment_info->batch_no . '，应付发票传输成功' . PHP_EOL;
                            } else {
                                $note = '';
                                if (isset($return_data['log']['Item']) && !empty($return_data['log']['Item'])) {
                                    $note = array_column($return_data['log']['Item'], 'Note');
                                    $note = implode(',', $note);
                                }
                                $agency_payment_info->i_update(['sync_sap' => 2, 'updated_at' => date('Y-m-d H:i:s'), 'sap_note' => $note]);
                                $log .= 'agency_payment id:' . $agency_payment_info->id . '; 批次号： ' . $agency_payment_info->batch_no . '，应付发票传输失败'  . PHP_EOL;
                            }
                            sleep(2);
                        }
                    } else {
                        $log .= 'agency_payment id:' . $agency_payment_info->id . '; 批次号： ' . $agency_payment_info->batch_no . '，应付发票已传输'  . PHP_EOL;
                    }

                    //sap总账凭证 存在未传输到总账的明细行，需要传输至sap总账凭证
                    if ($agency_payment_info->sap_uuid) {
                        $log .= 'agency_payment id:' . $agency_payment_info->id . '; 批次号： ' . $agency_payment_info->batch_no . '，传输总账凭证开始'  . PHP_EOL;
                        $agency_payment_detail_count = $this->getAgencyDetailSapCount($agency_payment_info->id);
                        if ($agency_payment_detail_count) {
                            //存在未传输到总账的明细行，需要传输至sap总账凭证
                            $this->agency_detail_to_sap($agency_payment_info, $agency_payment_sap, $cost_type_enums, $department_list, $log);
                        } else {
                            $log .= 'agency_payment id:' . $agency_payment_info->id . '; 批次号： ' . $agency_payment_info->batch_no . '，总账凭证已传输完毕'  . PHP_EOL;
                        }

                        $log .= 'agency_payment id:' . $agency_payment_info->id . '; 批次号： ' . $agency_payment_info->batch_no . '，传输总账凭证结束'  . PHP_EOL;
                    }

                    $log .= 'agency_payment id:' . $agency_payment_info->id . '; 批次号： ' . $agency_payment_info->batch_no . '，处理结束' . PHP_EOL . PHP_EOL;
                }

                sleep(1);
                $i += 100;
                $deal_sap_data = $this->getAgencySapList($i, $condition);
            }
        } catch (Exception $e) {
            $is_exception = true;
            $log .=  '脚本执行有异常' . $e->getMessage() . $e->getTraceAsString() . PHP_EOL;
        }
        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        if ($is_exception) {
            $this->logger->warning('agency_payment_payable_invoices_sap-exception: ' . $log);
        } else {
            $this->logger->info('agency_payment_payable_invoices_sap : ' . $log);
        }
        exit($log);
    }


    /**
     * 获取代理支付传输sap的头信息
     * @param integer $offset 分页偏移量
     * @param array $condition 参数组
     * @return mixed
     */
    private function getAgencySapList($offset, $condition)
    {
        //代理支付同步sap的公司ID和费用类型
        $agency_payment_sap_company_cost_type = EnumsService::getInstance()->getSettingEnvValueMap('agency_payment_sap_company_cost_type');
        //未设置
        if (empty($agency_payment_sap_company_cost_type)) {
            return [];
        }
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('id, approved_at, cost_company_id, sync_sap, sap_uuid');
        $builder->from(AgencyPaymentModel::class);

        //代理支付同步sap的公司ID和费用类型 同个公司的费用类型是或 不同公司之间是或
        foreach ($agency_payment_sap_company_cost_type as $cost_company_id => $cost_types) {
            $builder->orWhere("cost_company_id = :cost_company_id_{$cost_company_id}: and cost_type in({cost_type_{$cost_company_id}:array})", ['cost_company_id_' . $cost_company_id => $cost_company_id, 'cost_type_' . $cost_company_id => explode(',', $cost_types)]);
        }

        //非未支付的
        $builder->andWhere('status = :status: and pay_status != :pay_status:', ['status' => Enums::WF_STATE_APPROVED, 'pay_status' => AgencyPaymentEnums::AGENCY_PAYMENT_PAY_STATUS_NOTPAY]);

        //上线日期
        if (!empty($condition['online_time'])) {
            $builder->andWhere('approved_at > :online_time:', ['online_time' => $condition['online_time']]);
        }
        //审批日期开始日期
        if (!empty($condition['approved_at_start'])) {
            $builder->andWhere('approved_at >= :approved_at_start:', ['approved_at_start' => $condition['approved_at_start']]);
        }
        //审批日期结束日期
        if (!empty($condition['approved_at_end'])) {
            $builder->andWhere('approved_at < :approved_at_end:', ['approved_at_end' => $condition['approved_at_end']]);
        }
        $builder->limit(100, $offset);
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 获取某批次下未传输到总账的明细行总数
     * @param integer $agency_payment_id 代理支付批次id
     * @return int
     */
    private function getAgencyDetailSapCount($agency_payment_id)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(id) as total');
        $builder->from(AgencyPaymentDetailModel::class);
        $builder->where('agency_payment_id = :agency_payment_id: and accounting_entry_id = :accounting_entry_id:', ['agency_payment_id' => $agency_payment_id, 'accounting_entry_id' => '']);
        return (int) $builder->getQuery()->getSingleResult()->total;
    }

    /**
     * 获取某批次下未传输到总账的明细行
     * @param integer $agency_payment_id 代理支付批次id
     * @return mixed
     */
    private function getAgencyDetailSapList($agency_payment_id)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('*');
        $builder->from(AgencyPaymentDetailModel::class);
        $builder->where('agency_payment_id = :agency_payment_id: and accounting_entry_id = :accounting_entry_id:', ['agency_payment_id' => $agency_payment_id, 'accounting_entry_id' => '']);
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 传输sap总账凭证
     * @param object $agency_payment_info 代理支付批次对象
     * @param array $agency_payment_sap 代理支付-同步sap的各项配置（供应商编码、科目编码、成本中心、税务代码、代扣代缴代码）
     * @param array $cost_type_enums 费用类型枚举
     * @param array $department_list 部门列表
     * @param string $log 日志
     */
    private function agency_detail_to_sap($agency_payment_info, $agency_payment_sap, $cost_type_enums, $department_list, &$log)
    {
        $page_size = 500;
        $ledger_account_name = isset($agency_payment_sap[$agency_payment_info->cost_type]) ? ($agency_payment_sap[$agency_payment_info->cost_type]['ledger_account'] ?? '') : '';
        $all_detail_list =  $this->getAgencyDetailSapList($agency_payment_info->id, $page_size);

        //分批次处理数据
        $detail_list_chunk = array_chunk($all_detail_list, $page_size);
        foreach ($detail_list_chunk as $batch_num => $deal_detail_sap_data) {
            $return_data = $borrower = [];
            $borrower_amount_total_actually_total = 0;
            foreach ($deal_detail_sap_data as $detail) {
                $borrower[] = [
                    'no' => $detail['no'],
                    'ledger_account_name' => $ledger_account_name,//总账科目
                    'amount_total_actually' => $detail['amount_total_actually'],//实付金额
                    'cost_center_code' => $detail['cost_center_code'],//成本中心
                ];
                $borrower_amount_total_actually_total = bcadd($borrower_amount_total_actually_total, $detail['amount_total_actually'], 2);
            }
            $request_data = [
                'batch_no' => $agency_payment_info->batch_no,
                'approved_at' => date('Y-m-d', strtotime($agency_payment_info->approved_at)),
                'currency' => self::$t->_(GlobalEnums::$currency_item[$agency_payment_info->currency]),
                'cost_company_id' => isset($department_list[$agency_payment_info->cost_company_id]) ? $department_list[$agency_payment_info->cost_company_id]['sap_company_id'] : 'FEX01',
                'cost_type_text' => self::$t->_($cost_type_enums[$agency_payment_info->cost_type] ?? ''),//费用类型
                'borrower' => $borrower,//借方
                'lender' => [//贷方
                    'ledger_account_name' => $ledger_account_name,//总账科目
                    'amount_total_actually' => $borrower_amount_total_actually_total,//借方实付金额之和
                    'cost_center_name' => isset($agency_payment_sap[$agency_payment_info->cost_type]) ? ($agency_payment_sap[$agency_payment_info->cost_type]['cost_center'] ?? '') : '',//配置里的sap成本中心
                ]
            ];
            $detail_z = 0;
            while (empty($return_data) && $detail_z < 1) {
                $detail_z++;
                $return_data = SapService::getInstance()->agency_payment_general_ledger_voucher_to_sap($request_data);
                if (isset($return_data['ID']) && !empty($return_data['ID'])) {
                    $detail_ids = implode(',', array_column($deal_detail_sap_data, 'id'));
                    $db = $this->getDI()->get('db_oa');
                    $db->updateAsDict(
                        (new AgencyPaymentDetailModel())->getSource(),
                        [
                            'accounting_entry_id' => $return_data['ID'],
                            'updated_at' => date('Y-m-d H:i:s')
                        ],
                        ['conditions' => "id IN ($detail_ids)"]
                    );
                    $log .= 'agency_payment id:' . $agency_payment_info->id . '; 批次号： ' . $agency_payment_info->batch_no . '，传输总账第' . ($batch_num + 1) . '批，凭证成功' . PHP_EOL;
                } else {
                    $log .= 'agency_payment id:' . $agency_payment_info->id . '; 批次号： ' . $agency_payment_info->batch_no . '，传输总账第' . ($batch_num + 1) . '批，凭证失败' . PHP_EOL;
                }
                sleep(2);
            }
        }
    }
}
