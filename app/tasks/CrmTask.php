<?php


use App\Library\ApiClient;
use App\Library\Enums;
use App\Modules\CrmQuotation\Models\CrmQuotationApplyModel;
use App\Modules\CrmQuotation\Services\Coupon as CrmCouponServices;

class CrmTask extends BaseTask
{
    /**
     * 重新执行报价单同步coupon
     * @param $params 1. 传字符串'code' 这种情况按照代码中写好的单号去重试, 2. 传1个或多个(最多10个)单号 eg: R202209292015899 R202210015991085
     * @date 2022/10/20
     */
    public function retry_sync_couponAction($params)
    {
        if (!isset($params[0]) || empty($params[0])) {
            exit('参数错误！' . PHP_EOL);
        }
        //需要重新同步的报价单号
        if ($params[0] == 'code') {
            $quoted_price_list_sn = ['R202209292015899', 'R202210015991085', 'R202210049381718', 'R202210045106572', 'R202210043283691',
                'R202210043973638', 'R202210041097077', 'R202210187239306', 'R202210183410926'];
        } else {
            $quoted_price_list_sn = [];
            for ($i = 0; $i < 10; $i++) {
                if (isset($params[$i]) && !empty($params[$i])) {
                    $quoted_price_list_sn[] = $params[$i];
                }
            }
            $quoted_price_list_sn = array_unique($quoted_price_list_sn);
        }
        //循环处理
        foreach ($quoted_price_list_sn as $one_sn) {
            //查报价单信息
            $item = CrmQuotationApplyModel::findFirst([
                'conditions' => ' quoted_price_list_sn = :quoted_price_list_sn: ',
                'bind' => ['quoted_price_list_sn' => $one_sn],
                'for_update' => true
            ]);
            if (empty($item)) {
                exit('不存在此申请单！quoted_price_list_sn=' . $one_sn . PHP_EOL);
            }
            if ($item->state != Enums::CONTRACT_STATUS_APPROVAL) {
                exit('报价单状态未审批通过！quoted_price_list_sn=' . $one_sn . PHP_EOL);
            }
            //跟Coupon同步
            $coupons = (new CrmCouponServices())->getNormal($one_sn);
            $paramCoupon = [];
            foreach ($coupons as $k => $coupon) {
                $paramCoupon[$k]['coupon_validity_time'] = $coupon['coupon_expire'];
                $paramCoupon[$k]['coupon_category'] = $coupon['coupon_type'];
                $paramCoupon[$k]['code_number'] = $coupon['coupon_num'];
            }
            $is_success = 0;
            if (!empty($paramCoupon)) {
                $syncParams = [
                    'customer_id' => $item->customer_id,
                    'customer_type' => $item->customer_type_category,
                    'apply_key' => $item->crmno,
                    'coupon_apply' => $paramCoupon
                ];

                $rpc = new ApiClient('coupon_rpc', '', 'backyardApplyCoupon', 'th');
                $rpc->setParams([$syncParams]);
                $data = $rpc->execute();
                if (isset($data['result'])) {
                    $is_success = 1;
                    $this->logger->info('retry_coupon_rpc_request :' . json_encode($syncParams) . ":retry_coupon_rpc_response:" . json_encode($data));
                } else {
                    $is_success = 2;
                    $this->logger->error('retry_coupon_rpc_request :' . json_encode($syncParams) . ":retry_coupon_rpc_response:" . json_encode($data));
                }
            }
            if ($is_success == 1) {
                echo '重新同步成功！quoted_price_list_sn=' . $one_sn . PHP_EOL;
                $this->logger->info('重新同步成功！quoted_price_list_sn=' . $one_sn . '; coupon返回 :' . json_encode($data ?? [], JSON_UNESCAPED_UNICODE));
            } elseif ($is_success == 2) {
                echo '重新同步失败！quoted_price_list_sn=' . $one_sn . PHP_EOL;
                $this->logger->info('重新同步失败！quoted_price_list_sn=' . $one_sn . '; coupon返回 :' . json_encode($data ?? [], JSON_UNESCAPED_UNICODE));
            } else {
                echo '重新同步失败！quoted_price_list_sn=' . $one_sn . PHP_EOL;
                $this->logger->info('重新同步失败,没有请求coupon！quoted_price_list_sn=' . $one_sn . '; paramCoupon :' . json_encode($paramCoupon, JSON_UNESCAPED_UNICODE));
            }


        }
        exit('执行完毕！' . PHP_EOL);
    }

}
