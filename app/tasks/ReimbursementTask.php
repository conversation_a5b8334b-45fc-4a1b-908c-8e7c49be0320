<?php

use App\Library\ApiClient;
use App\Library\Enums;
use App\Library\Enums\ReimbursementEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\OssHelper;
use App\Library\RedisClient;
use App\Library\Validation\ValidationException;
use App\Models\oa\ReimbursementDetailSupportRelModel;
use App\Models\oa\ReimbursementDetailTravelRoommateRelModel;
use App\Models\oa\ReimbursementDraftModel;
use App\Models\oa\ReimbursementMsgLogModel;
use App\Modules\BankFlow\Models\BankFlowOaModel;
use App\Modules\Budget\Services\LedgerAccountService;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Loan\Models\Loan;
use App\Modules\Reimbursement\Models\Detail as ReimbursementDetailModel;
use App\Modules\Reimbursement\Models\Reimbursement;
use App\Modules\Reimbursement\Models\ReimbursementRelLoan;
use App\Modules\Reimbursement\Services\ConfigService as ReimbursementConfigService;
use App\Modules\Reimbursement\Services\SapService;
use App\Modules\Reimbursement\Services\BaseService;
use App\Modules\Reimbursement\Services\ListService;
use App\Modules\Reimbursement\Services\AddService;
use App\Modules\OrdinaryPayment\Services\OrdinaryPaymentListService;
use App\Modules\Training\Services\TaskService;
use App\Library\Enums\GlobalEnums;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Organization\Models\SysDepartmentModel;
use App\Modules\Purchase\Services\PaymentService;
use App\Modules\SapManage\Services\ReimbursementSapService;
use App\Repository\HrStaffRepository;
use App\Util\RedisKey;


class ReimbursementTask extends BaseTask
{
    public function flush_rateAction()
    {
        //获得
        $sql     = "select * from reimbursement_detail where re_id in (select id from reimbursement where status=1)";
        $details = $this->db_oa->fetchAll($sql, \Phalcon\Db::FETCH_ASSOC);

        $ids = array_filter(array_column($details, "id"));

        foreach ($details as $detail) {
            //不含税金额
            if (empty($detail['tax_not'])) {
                //echo "id==".$detail['id']."==not_tax===".$detail['tax_not'].PHP_EOL;
                continue;
            }

            //保留3位小数，四舍五入，保留两位小数，乘以1000，是整数
            $rate = bcmul(round(bcdiv($detail['tax'], $detail['tax_not'], 3), 2), 1000);

            //如果两个数相同，也跳过
            if ($rate == $detail['rate']) {
                //echo "id==".$detail['id']."==rate===".$detail['rate']."=====cal_rate==".$rate.PHP_EOL;
                continue;
            }

            $sql = "update reimbursement_detail set rate=" . $rate . " where id =" . $detail['id'];
            $this->db_oa->query($sql);
            echo "id==" . $detail['id'] . "=re_id==" . $detail['re_id'] . "=rate=" . $detail['rate'] . "=====cal_rate==" . $rate . PHP_EOL;
        }
    }

    public function test_groupAction()
    {
        $item = (new \App\Modules\Workflow\Services\WorkflowServiceV2());
        var_dump($item->getGroupManagerByDepartmentId(20));
    }

    public function clear_dirty_reserveAction()
    {
        //获得
        $sql     = "select rfr.id,rfr.rfano,rfr.rei_detail_id from reimbursement r left join reserve_fund_reimburse rfr on rfr.rei_id=r.id where r.status in(2,4) or r.pay_status=3";
        $details = $this->db_oa->fetchAll($sql, \Phalcon\Db::FETCH_ASSOC);

        foreach ($details as $detail) {
            if (!empty($detail['rfano']) || !empty($detail['rei_detail_id'])) {
                $reservModel = \App\Modules\ReserveFund\Models\ReserveFundReimburse::findFirst(
                    [
                        'id = :id:',
                        'bind' => ['id' => $detail['id']],
                    ]
                );
                isset($reservModel->id) ? $reservModel->delete($reservModel->id) : '';
            }
        }

        echo '任务执行成功.';
    }

    /**
     * 报销单据推送sap
     *
     * php app/cli.php reimbursement reimbursement_to_sap
     *
     * @param $params
     * @return void
     */
    public function reimbursement_to_sapAction($params)
    {
        $this->checkLock(__METHOD__, 10800);

        $logger_type = 'info';

        $log = '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        $is_specify_date = $params[0] ?? 2;
        $specify_date    = $params[1] ?? '2021-11-18';
        $days            = $params[2] ?? 17;

        $is_specify_date_flag = ($is_specify_date == 1) ? '是 ' . $specify_date : '否';
        $log                  .= '指定日期: ' . $is_specify_date_flag . PHP_EOL;

        try {
            $start_time = date('Y-m-d H:i:s', time() - 3600 * 24 * $days);
            $end_time   = date('Y-m-d H:i:s');

            $apply_ph_date = $params[3] ?? '2021-11-18 23:59:59';
            if ($start_time < '2022-03-01 00:00:00') {
                $start_time = '2022-03-01 00:00:00';
            }
            //计算本月日期1-设定日期 只执行上个月 11-31号只执行本月数据
            $start_date     = date('Y-m-01 00:00:00');
            $end_date       = date("Y-m-{$this->getSapTaskSendDate()} 00:00:00");
            $is_little_date = true;

            if (date('Y-m-d H:i:s') >= $start_date && date('Y-m-d H:i:s') < $end_date) {
                $end_time   = $start_date;
                $start_time = date('Y-m-d H:i:s', strtotime($end_time) - 3600 * 24 * $days);
            }

            $last_day   = OrdinaryPaymentListService::getLastDay(ltrim(date('m')), ltrim(date('Y')));
            $end_date_2 = date('Y-m-d H:i:s', (strtotime($start_date) + 3600 * 24 * $last_day));
            if (date('Y-m-d H:i:s') >= $end_date && date('Y-m-d H:i:s') < $end_date_2) {
                $start_time     = $start_date;
                $end_time       = date('Y-m-d H:i:s');
                $is_little_date = false;
            }

            $t           = TaskService::getTranslation(strtolower(env('country_code', 'th')));
            $ledger_res  = LedgerAccountService::getInstance()->getList();
            $ledger_info = '';
            if ($ledger_res['code'] == ErrCode::$SUCCESS) {
                $ledger_info = array_column($ledger_res['data'], 'account', 'id');
            }

            $log .= 'is_little_date = ' . ($is_little_date ? 'true' : 'false') . PHP_EOL;

            $send_data = [];

            //发送邮件 目前只有泰国
            $email = EnvModel::getEnvByCode('reimbursement_email');
            $email = explode(',', $email);
            //purchase_sap_company_ids 只有配置了这个才会启动脚本 目前开通sap 国家 泰国菲律宾马来

            $cost_company_id = EnvModel::getEnvByCode('purchase_sap_company_ids');
            if (empty($cost_company_id)) {
                throw new ValidationException('purchase_sap_company_ids 配置为空, 请检查', ErrCode::$VALIDATE_ERROR);
            }

            $sap_company_ids = SysDepartmentModel::find(
                [
                    'conditions' => 'id in ({ids:array})',
                    'bind'       => ['ids' => explode(',', $cost_company_id)],
                ]
            )->toArray();
            $sap_company_ids = array_column($sap_company_ids, 'sap_company_id', 'id') ?? [];

            //获取默认币种
            $default_currency = EnumsService::getInstance()->getSysCurrencyInfo();

            $log .= "取数范围: 审批日期 {$start_time} ~ {$end_time}, 费用公司 {$cost_company_id}, 币种 {$default_currency['code']}, 创建时间 {$apply_ph_date}" . PHP_EOL;

            $i            = 0;
            $country_code = get_country_code();

            $db_obj = $this->db_oa;

            $sql = "select `id`,`apply_id`,`cost_company_id`,`no`,`apply_date`,`extra_message`,`voucher_abstract`,`currency`,`amount`,`real_amount`,`loan_amount`,`cost_center_code`,`approved_at`,`pay_at` from reimbursement  where  `created_at`>'{$apply_ph_date}' and `approved_at`>'{$start_time}' and `approved_at` < '{$end_time}' and  `status`= 3   and  `currency` = '{$default_currency['code']}'  and cost_company_id in ({$cost_company_id}) and sync_sap in (0,3) limit {$i} ,100";

            $data                    = $db_obj->query($sql);
            $deal_reimbursement_data = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);

            $batch_num = 0;
            while (!empty($deal_reimbursement_data)) {
                $batch_num++;
                $log .= "待处理批次[{$batch_num}] " . count($deal_reimbursement_data) . ' 条' . PHP_EOL;

                $curr_batch_log = "当前处理批次[{$batch_num}]: " . PHP_EOL;
                foreach ($deal_reimbursement_data as $key => $value) {
                    $curr_batch_log .= "待处理单据: {$value['no']}, id={$value['id']}, 审批时间-{$value['approved_at']}";

                    // 1-10号不是上个月数据跳过  11-31不是本月数据跳过
                    if ($is_little_date) {
                        if (ltrim(date('m', strtotime($value['approved_at']))) == ltrim(date('m'))) {
                            $curr_batch_log .= ', m = m, 跳过' . PHP_EOL;
                            continue;
                        }
                    } else {
                        if (ltrim(date('m', strtotime($value['approved_at']))) != ltrim(date('m'))) {
                            $curr_batch_log .= ', m != m, 跳过' . PHP_EOL;
                            continue;
                        }
                    }

                    //特定日期发送
                    if ($is_specify_date == 1 && $value['apply_date'] != $specify_date) {
                        $curr_batch_log .= ", 申请日期 {$value['apply_date']}, 非指定日期 {$specify_date}, 跳过" . PHP_EOL;
                        continue;
                    }

                    $item = Reimbursement::getFirst([
                        'id = :id:',
                        'bind' => ['id' => $value['id']],
                    ]);

                    if (empty($item)) {
                        $curr_batch_log .= ', 未找到报销主数据, 跳过' . PHP_EOL;
                        continue;
                    }

                    if ($item->sync_sap == ReimbursementSapService::SYNC_SAP_SUCCESS) {
                        $curr_batch_log .= ', 已被成功同步过, 跳过' . PHP_EOL;
                        continue;
                    }

                    $details = $item->getDetails()->toArray();
                    if (empty($details)) {
                        $curr_batch_log .= ', 未找到报销明细数据, 跳过' . PHP_EOL;
                        continue;
                    }

                    // 查询是否有冲减借款
                    $lno = '';
                    if ($value['loan_amount'] != 0) {
                        $rel_loan = ReimbursementRelLoan::getFirst([
                            'conditions' => 're_id = :re_id: and is_deleted =:is_deleted:',
                            'columns'    => 'loan_id',
                            'bind'       => ['re_id' => $value['id'], 'is_deleted' => GlobalEnums::IS_NO_DELETED],
                        ]);
                        if (!empty($rel_loan)) {
                            $loan_data = Loan::getFirst([
                                'conditions' => 'id = :id:',
                                'columns'    => 'lno',
                                'bind'       => ['id' => $rel_loan->loan_id],
                            ]);
                            $lno       = $loan_data->lno;
                        }
                    }

                    //马来借款需要支付后发送
                    $pay_at = '';

                    // v21198 MY关联了借款单的过账日期 调整为 报销单的审批通过日期
//                    if ($country_code == GlobalEnums::MY_COUNTRY_CODE && !empty($lno) && empty($value['pay_at'])) {
//                        continue;
//                    }
//                    if ($country_code == GlobalEnums::MY_COUNTRY_CODE && !empty($lno) && !empty($value['pay_at'])) {
//                        $pay_at = $value['pay_at'];
//                    }

                    $request_data = [
                        'currency'         => $t->_(GlobalEnums::$currency_item[$value['currency']]),
                        'cost_company_id'  => $sap_company_ids[$value['cost_company_id']] ?? 'FEX01',
                        'order_no'         => $value['no'],
                        'posting_date'     => empty($pay_at) ? date('Y-m-d', strtotime($value['approved_at'])) : date('Y-m-d', strtotime($pay_at)),
                        'apply_date'       => $value['apply_date'],
                        'note'             => str_replace('&', '', $value['voucher_abstract']),
                        'extra_message'    => $value['extra_message'],
                        'apply_id'         => $value['apply_id'],
                        'amount'           => bcdiv($value['amount'], 1000, 2),
                        'loan_amount'      => bcdiv($value['loan_amount'], 1000, 2),
                        'real_amount'      => bcdiv($value['real_amount'], 1000, 2),
                        'lno'              => $lno,
                        'cost_center_code' => $value['cost_center_code'],
                        'notes'            => trim(substr($value['voucher_abstract'], 0, 40)),
                        'items'            => $details,
                    ];

                    $return_data = SapService::getInstance()->ReimbursementToSap($request_data, $ledger_info);
                    if (isset($return_data['UUID']) && !empty($return_data['UUID'])) {
                        $curr_batch_log .= ', 抛数成功';

                        $update_data = [
                            'sync_sap'   => ReimbursementSapService::SYNC_SAP_SUCCESS,
                            'sap_uuid'   => $return_data['UUID'] ?? '',
                            'updated_at' => date('Y-m-d H:i:s'),
                        ];
                        if ($item->i_update($update_data) === false) {
                            $curr_batch_log .= ', 状态更新异常' . PHP_EOL;
                        } else {
                            $curr_batch_log .= ', 状态更新成功' . PHP_EOL;
                        }
                    } else {
                        $curr_batch_log .= ', 抛数失败';

                        $note = '';
                        if (isset($return_data['log']['Item']) && !empty($return_data['log']['Item'])) {
                            if (count($return_data['log']['Item']) == count($return_data['log']['Item'], 1)) {//判断xml解析出来是否为一维数组
                                $note = $return_data['log']['Item']['Note'] ?? '';
                            } else {
                                $note = array_column($return_data['log']['Item'], 'Note');
                                $note = implode(',', $note);
                            }
                        }

                        $update_data = [
                            'sync_sap'   => ReimbursementSapService::SYNC_SAP_FAIL,
                            'updated_at' => date('Y-m-d H:i:s'),
                            'sap_note'   => $note,
                        ];
                        if ($item->i_update($update_data) === false) {
                            $curr_batch_log .= ', 状态更新异常' . PHP_EOL;
                        } else {
                            $curr_batch_log .= ', 状态更新成功' . PHP_EOL;
                        }
                    }

                    sleep(1);

                    // 同步 sap 后 发邮件
                    if (isset($return_data['UUID']) && !empty($return_data['UUID'])) {
                        $row['no']   = $value['no'];
                        $send_data[] = $row;
                    }
                }

                // 每批次记录日志
                $this->logger->info($curr_batch_log);
                $log .= $curr_batch_log;

                //拼接 html  发送邮件
                if (!empty($send_data) && !empty($email)) {
                    $title = "OA SAP document creation reminder";
                    $html  = $this->format_html($send_data);
                    $this->mailer->sendAsync($email, $title, $html);
                }

                sleep(1);

                // 下一批次
                $i   += 100;
                $sql = "select `id`,`apply_id`,`cost_company_id`,`no`,`apply_date`,`extra_message`,`voucher_abstract`,`currency`,`amount`,`real_amount`,`loan_amount`,`cost_center_code`,`approved_at`,`pay_at` from reimbursement  where  `created_at`>'{$apply_ph_date}' and `approved_at`>'{$start_time}' and `approved_at` < '{$end_time}' and  `status`= 3 and  `currency` = '{$default_currency['code']}'  and cost_company_id in ({$cost_company_id}) and sync_sap in (0,3)  limit {$i} ,100";

                $data                    = $db_obj->query($sql);
                $deal_reimbursement_data = $data->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            }
        } catch (ValidationException $e) {
            $logger_type = 'notice';
            $log         .= $e->getMessage() . PHP_EOL;
        } catch (Exception $e) {
            $logger_type = 'error';
            $log         .= $e->getMessage() . PHP_EOL;
        }

        $this->clearLock(__METHOD__);
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $this->logger->$logger_type($log);
        exit($log);
    }


    //整理 sap 订单提醒邮件内容
    public function format_html($data)
    {
        $html = "<p>Dear all,<?p></br></br>";
        $html .= "<p>Below data are posted in SAP, please have a check! Thanks!</p></br></br>";
        $html .= "<p>以下数据已过账到SAP，请检查！谢谢</p>";

        $html .= "<table border='1'><tr><td>OA number</td></tr>";
        foreach ($data as $da) {
            $html .= "<tr><td>{$da['no']}</td></tr>";
        }

        $html .= "</table>";
        return $html;
    }

    /**
     * 每周一北京时间早上10点,给财务发邮件,统计过去7天上传过附件的单号
     */
    public function send_attachment_no_per_weekAction()
    {
        // 上周第一天
        $last_week_first = date('Y-m-d 00:00:00', strtotime(date('Y-m-d') . ' -7 day'));
        //　上周最后一天:
        $last_week_last = date('Y-m-d 23:59:59', strtotime(date('Y-m-d') . ' -1 day'));

        try {
            // 取报销待审核/已通过，待支付或者已支付的单号下的补充附件
            $reimbursement_list         = ListService::getInstance()->getAttachmentNoList([
                'start' => $last_week_first,
                'end'   => $last_week_last,
            ]);
            $reimbursement_list_content = !empty($reimbursement_list) ? implode('<br>', $reimbursement_list) : '';

            // 取普通付款待审核/已通过数据
            $ordinary_list = OrdinaryPaymentListService::getInstance()->getAttachmentNoList([
                'start' => $last_week_first,
                'end'   => $last_week_last,
            ]);

            //付款申请单待审核/已通过，待支付或者已支付的单号下的补充附件
            $payment_list = PaymentService::getInstance()->getAttachNoList([
                'start' => $last_week_first,
                'end'   => $last_week_last,
            ]);

            $payment_list_content = !empty($payment_list) ? implode('<br>', $payment_list) : '';

            if (empty($reimbursement_list) && empty($ordinary_list) && empty($payment_list)) {
                $_log = '过去7天上传过附件的单号数据为空';
                $this->myLogger($_log);
                exit($_log);
            }

            $ordinary_list_content = !empty($ordinary_list) ? implode('<br>', $ordinary_list) : '';

            //取配置中的发送人
            $emails = EnvModel::getEnvByCode('loan_data_send_email', '');
            $emails = array_values(array_filter(explode(',', $emails)));
            if (empty($emails)) {
                $_log = '未配置邮箱或者配置的邮箱地址为空';
                $this->myLogger($_log);
                exit($_log);
            }

            //发送邮件并记录日志
            $title   = 'OA系统单号已补充附件 The attachment has been added to the OA system number';
            $content = <<<EOF
    hi，<br>
        过去一周，以下OA系统单号已补充附件，请知悉。<br>
        Please note that the following OA system order numbers have been added to the attachment in the past week.<br>
        {$reimbursement_list_content}<br>
        {$ordinary_list_content}<br>
        {$payment_list_content}<br>

EOF;
            if ($this->mailer->send($emails, $title, $content)) {
                $this->myLogger('send success');
            } else {
                $this->myLogger('send fail');
            }
        } catch (Exception $e) {
            $this->myLogger('email error==' . $e->getMessage(), 1);
        }
    }

    /**
     * 每天北京时间早上10点,给财务发邮件,统计超过支付日期7天，未上传过附件的单号
     */
    public function send_attachment_no_per_dayAction()
    {
        // 上周第一天
        $pay_at = date('Y-m-d H:m:s', strtotime(date('Y-m-d H:i:s') . ' -7 day'));

        try {
            // 取报销待审核/已通过，待支付或者已支付的单号下的补充附件
            $reimbursement_list = ListService::getInstance()->getSupplementAttachments(['pay_at' => $pay_at]);

            // 取普通付款待审核/已通过数据
            $ordinary_list = OrdinaryPaymentListService::getInstance()->getSupplementAttachments([
                'pay_at' => $pay_at,
            ]);

            $payment_list = PaymentService::getInstance()->getSupplementAttachments([
                'pay_at' => $pay_at,
            ]);

            if (empty($reimbursement_list['no_list']) && empty($ordinary_list['no_list']) && empty($payment_list['no_list'])) {
                $_log = '支付7天后补充附件未上传数据为空';
                $this->myLogger($_log);
                exit($_log);
            }
            // 合并两者的结果集
            $total_list = $reimbursement_list['no_list'];
            foreach ($ordinary_list['email_list'] as $k => $iem) {
                $reimbursement_list['email_list'][$k] = $iem;
            }

            foreach ($payment_list['email_list'] as $k => $iem_2) {
                $reimbursement_list['email_list'][$k] = $iem_2;
            }

            foreach ($ordinary_list['no_list'] as $k => $oitem) {
                $total_list[$k] = isset($total_list[$k]) ? array_merge($total_list[$k], $oitem) : $oitem;
            }

            foreach ($payment_list['no_list'] as $k => $pay_item) {
                $total_list[$k] = isset($total_list[$k]) ? array_merge($total_list[$k], $pay_item) : $pay_item;
            }

            //取申请人邮箱作为发送人
            if (empty($reimbursement_list['email_list'])) {
                $_log = '发送人邮箱地址为空';
                $this->myLogger($_log);
                exit($_log);
            }

            //发送邮件并记录日志
            $title    = '请及时在OA系统补充发票Please supplement invoices in OA system in time';
            $page_url = EnvModel::getEnvByCode('oa_dashboard_page_url');
            foreach ($total_list as $k => $noitem) {
                if (!isset($reimbursement_list['email_list'][$k]) || empty($noitem)) {
                    continue;
                }

                $list_content = implode('<br>', $noitem);
                $content      = <<<EOF
    hi，<br>
        {$list_content}<br>
        需要补充发票，请及时在“OA-采购申请/报销申请/普通付款申请”补充发票 {$page_url}<br>
        requires additional invoices, please supplement invoices in "OA-Procurement Application/Reimbursement Application/Ordinary Application-Supplemental Attachments" in a timely manner {$page_url}<br>
EOF;

                try {
                    if ($this->mailer->sendAsync($reimbursement_list['email_list'][$k], $title, $content)) {
                        $this->myLogger('邮箱地址：' . $reimbursement_list['email_list'][$k] . ' send success');
                    } else {
                        $this->myLogger('邮箱地址：' . $reimbursement_list['email_list'][$k] . ' send fail');
                    }
                } catch (Exception $e) {
                    $this->myLogger('email error==' . $e->getMessage());
                }
            }
        } catch (Exception $e) {
            $this->myLogger('email error==' . $e->getMessage(), 1);
        }
    }

    /**
     * 每月1号早上9:00（北京时间）发送各个国家系统的全量数据到财务分析组
     */
    public function send_attachment_to_financialAction()
    {
        echo 'Start ' . date('Y-m-d H:i:s') . PHP_EOL;

        try {
            ListService::setLanguage('zh-CN');

            //取配置中的发送人
            $emails = EnumsService::getInstance()->getSettingEnvValueIds('reimbursement_financial_email');
            if (empty($emails)) {
                $this->myLogger('未配置邮箱或者配置的邮箱地址为空');
                exit('未配置邮箱或者配置的邮箱地址为空');
            }

            // 导出符合如下条件的数据
            $condition = [
                'pageNum'    => 1,     // 初始第一页
                'pageSize'   => 10000, // 每次1w条
                'status'     => [Enums::CONTRACT_STATUS_PENDING, Enums::CONTRACT_STATUS_APPROVAL],
                'pay_status' => [Enums::LOAN_PAY_STATUS_PENDING, Enums::LOAN_PAY_STATUS_PAY],
                'created_at' => '2021-01-01 00:00:00' // 产品只需要2021-01-01开始的数据
            ];

            $res = ListService::getInstance()->export($condition, [], true);
            if (isset($res['code']) && $res['code'] == ErrCode::$SUCCESS && !empty($res['data'])) {
                $attachment = $res['data'];
                $this->myLogger('取数成功: Excel File = ' . $attachment);
            } else {
                $this->myLogger('取数 或 生成Excel 失败，请检查', 1);
                exit('取数 或 生成Excel 失败，请检查');
            }

            //发送邮件并记录日志
            $title        = '报销数据';
            $country_name = ListService::getInstance()->getCountryCnName();
            $content      = 'Dear all，<br>
            请通过此链接下载报销相关数据: 网页下载地址，' . $attachment;

            // 分多次发送邮件
            foreach ($emails as $email) {
                if ($this->mailer->sendAsync($email, $title . '-' . $country_name, $content)) {
                    $this->myLogger('send success');
                    echo $email . ' send success' . $title . PHP_EOL;
                } else {
                    $this->myLogger('send fail');
                    echo $email . ' send fail' . $title . PHP_EOL;
                }
            }
        } catch (Exception $e) {
            $this->myLogger('email error==' . $e->getMessage(), 1);
        }

        exit('End ' . date('Y-m-d H:i:s'));
    }

    /**
     * 日志打印接口
     */
    public function myLogger($str, $flag = 0)
    {
        echo "loanTask==" . $str . PHP_EOL;
        if (empty($flag)) {
            $this->logger->info($str);
        } else {
            $this->logger->warning($str);
        }
    }

    /**
     * 定期清理草稿数据
     *
     * php app/cli.php reimbursement clear_draft
     *
     */
    public function clear_draftAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log          = '任务名称: ' . $process_name . PHP_EOL;
        $log          .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        $logger_type = 'info';
        try {
            // 删除30天前变更的草稿的(不含草稿创建当日 和 脚本执行当日)
            $end_time = date('Y-m-d 00:00:00', strtotime('-31 day'));
            $models   = ReimbursementDraftModel::find([
                'conditions' => "updated_at < :updated_at:",
                'bind'       => ['updated_at' => $end_time],
            ]);

            $list = $models->toArray();
            $log  .= "待清理草稿的截止时间: {$end_time}" . PHP_EOL;
            $log  .= '待清理草稿数量: ' . count($list) . PHP_EOL;

            if (empty($list)) {
                throw new ValidationException('无待清理的草稿数据, 请忽略', ErrCode::$VALIDATE_ERROR);
            }

            // 待清理的草稿数据写入文件日志
            foreach ($list as $draft_data) {
                $draft_data['content'] = json_decode($draft_data['content'], true);
                $this->logger->info(['reimbursement_clear_draft_backup' => $draft_data]);
            }

            // 批删除
            if ($models->delete() === false) {
                throw new BusinessException('清理失败, 请确认', ErrCode::$BUSINESS_ERROR);
            }

            $log .= "清理成功" . PHP_EOL;
        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;
        } catch (BusinessException $e) {
            $log         .= $e->getMessage() . PHP_EOL;
            $logger_type = 'notice';
        } catch (Exception $e) {
            $log         .= $e->getMessage() . PHP_EOL;
            $logger_type = 'error';
        }

        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        $this->logger->$logger_type('报销草稿数据定期清理: ' . $log);
        exit($log);
    }

    /**
     * 拉取前一天的汇率: 一天一次
     *
     * php app/cli.php reimbursement sync_exchange_rate
     * @param $params
     */
    public function sync_exchange_rateAction($params)
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log          = '任务名称: ' . $process_name . PHP_EOL;
        $log          .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        $logger_type = 'info';
        try {
            $period_date = $params[0] ?? date('Y-m-d', strtotime('-1 day'));
            $log         .= "待获取汇率日期 {$period_date}" . PHP_EOL;

            static::setLanguage('zh-CN');

            $retry_count = 0;
            do {
                $res = ReimbursementConfigService::getInstance()->updateSysExchangeRateList($period_date);
                if ($res['code'] == ErrCode::$SUCCESS) {
                    $log .= "同步结果: {$res['message']}" . PHP_EOL;
                    break;
                } else {
                    $log .= "retry_count = {$retry_count}, update_res_msg = " . $res['message'] . PHP_EOL;
                }

                $retry_count++;
            } while ($res['is_retry'] && $retry_count < 3);

            if ($retry_count >= 3 && $res['code'] != ErrCode::$SUCCESS) {
                throw new BusinessException("重试{$retry_count}次均失败, 请同步产品同学核查", ErrCode::$BUSINESS_ERROR);
            }
        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;
        } catch (BusinessException $e) {
            $log         .= $e->getMessage() . PHP_EOL;
            $logger_type = 'notice';
        } catch (Exception $e) {
            $log         .= $e->getMessage() . PHP_EOL;
            $logger_type = 'error';
        }

        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        $this->logger->$logger_type('调用BOT接口获取汇率: ' . $log);
        echo $log;
    }

    /**
     * 初始化汇率: 某段日期范围
     *
     * php app/cli.php reimbursement init_exchange_rate 2025-03-01 2025-03-10
     * @param $params
     */
    public function init_exchange_rateAction($params)
    {
        echo '当前时间 ' . get_datetime_with_milliseconds() . PHP_EOL;
        $start_date = $params[0];
        $end_date   = $params[1];
        if (!preg_match('/\d{4}-\d{2}-\d{2}/', $start_date) || !preg_match('/\d{4}-\d{2}-\d{2}/', $end_date)) {
            exit('开始日期或结束日期格式错误[示例: YYYY-MM-DD]' . PHP_EOL);
        }

        if ($end_date < $start_date) {
            exit("结束日期[$end_date]不可早于开始日期[$start_date]" . PHP_EOL);
        }

        $current_date = $start_date;
        while ($current_date <= $end_date) {
            echo '待初始化日期 ' . $current_date . PHP_EOL;
            $this->sync_exchange_rateAction([$current_date]);
            $current_date = date('Y-m-d', strtotime('+1 days', strtotime($current_date)));
            echo $current_date . '处理完毕' . PHP_EOL . PHP_EOL;
        }

        echo '结束时间 ' . get_datetime_with_milliseconds() . PHP_EOL;
        exit('初始化完毕' . PHP_EOL);
    }

    /**
     *
     * 迁移明细行支援单号存储结构(一次性)
     *
     * php app/cli.php reimbursement convert_detail_support_rel
     *
     * TRUNCATE reimbursement_detail_support_rel;
     *
     * @param $params
     */
    public function convert_detail_support_relAction($params)
    {
        echo '开始时间: ' . get_datetime_with_milliseconds() . PHP_EOL;
        echo '当前内存: ' . memory_usage() . PHP_EOL;

        $chunk_length = $params[0] ?? 10000;
        echo '每批次数据量: ' . $chunk_length . PHP_EOL;

        // 取出有支援单号的明细行
        $detail_list = ReimbursementDetailModel::find([
            'conditions' => 'support_serial_no <> :support_serial_no_null:',
            'bind'       => ['support_serial_no_null' => ''],
            'columns'    => ['id', 're_id', 'product_id', 'support_serial_no'],
        ])->toArray();

        echo '共有 ' . count($detail_list) . ' 行待处理' . PHP_EOL;
        if (empty($detail_list)) {
            exit('无待处理的支援行' . PHP_EOL);
        }

        echo '当前内存: ' . memory_usage() . PHP_EOL;

        // 构造新结构
        // 分批处理, 没批1w条
        $detail_chunk = array_chunk($detail_list, $chunk_length);
        foreach ($detail_chunk as $chunk_index => $detail_chunk_list) {
            echo PHP_EOL . '第 ' . $chunk_index . ' 批, 需处理 ' . count($detail_chunk_list) . ' 条' . PHP_EOL;
            $rel_data = [];
            foreach ($detail_chunk_list as $detail) {
                $rel_data[] = [
                    're_id'             => $detail['re_id'],
                    'detail_id'         => $detail['id'],
                    'product_id'        => $detail['product_id'],
                    'support_serial_no' => trim($detail['support_serial_no']),
                ];
            }

            $rel_model = new ReimbursementDetailSupportRelModel();
            if (!empty($detail_chunk_list) && $rel_model->batch_insert($rel_data) === false) {
                echo '批量写入关系表-失败, 终止处理 [需清空关系表数据, 重新执行脚本], 原因可能是 ' . $rel_model->getErrorMessagesString() . PHP_EOL;
                exit();
            }

            echo '批量写入关系表-成功' . PHP_EOL;
            echo '当前内存: ' . memory_usage() . PHP_EOL;
        }

        echo '结束时间: ' . get_datetime_with_milliseconds() . PHP_EOL;
        echo '当前内存: ' . memory_usage() . PHP_EOL;
        exit();
    }

    /**
     * 处理共同住宿人待确认数据
     * 当地时间每天9点
     *
     * php app/cli.php reimbursement handle_roommate_pending
     *
     */
    public function handle_roommate_pendingAction()
    {
        $log = '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        $logger_type = 'info';
        try {
            $list = Reimbursement::find([
                'conditions' => 'status = :status:',
                'bind'       => ['status' => ReimbursementEnums::STATUS_WAITING_CONFIRMED],
                'columns'    => ['id', 'no'],
            ])->toArray();

            $log .= '待共同住宿人确认的报销单数量: ' . count($list) . PHP_EOL;
            if (empty($list)) {
                throw new ValidationException('无待共同住宿人确认的报销单', ErrCode::$VALIDATE_ERROR);
            }

            // 共同住宿超时天数配置
            $valid_days = EnumsService::getInstance()->getSettingEnvValue('reimbursement_roommate_valid_days');
            if (!is_numeric($valid_days)) {
                throw new BusinessException('共同住宿人确认时效天数配置有误, 请联系产品[reimbursement_roommate_valid_days]', ErrCode::$VALIDATE_ERROR);
            }

            $th_lang = BaseService::getTranslation('th');
            $en_lang = BaseService::getTranslation('en');
            $invalid_reason = $en_lang->_('reimbursement_save_error_039') . $th_lang->_('reimbursement_save_error_039');

            $current_date = new DateTime();

            $hr_staff_repository = new HrStaffRepository();
            $base_service        = new BaseService();

            $completed_confirmed_count = 0;
            $need_again_confirm_count  = 0;
            $invalid_order_count       = 0;
            foreach ($list as $value) {
                $logger_level = 'info';
                $_log         = '当前单号 ' . $value['no'] . PHP_EOL;

                $db = $this->getDI()->get('db_oa');
                $db->begin();
                try {
                    // 单据维度, 加锁
                    $order_model = Reimbursement::findFirst([
                        'conditions' => 'id = :id:',
                        'bind'       => ['id' => $value['id']],
                        'for_update' => true,
                    ]);

                    if ($order_model->status != ReimbursementEnums::STATUS_WAITING_CONFIRMED) {
                        throw new ValidationException("非待共同住宿人确认状态, 跳过 [status={$order_model->status}]", ErrCode::$VALIDATE_ERROR);
                    }

                    // 获取共同住宿人确认信息
                    $roommate_rel_models = ReimbursementDetailTravelRoommateRelModel::find([
                        'conditions' => 're_id = :re_id:',
                        'bind'       => ['re_id' => $order_model->id],
                        'for_update' => true,
                    ]);

                    // 获取共同住宿人在职状态
                    $apply_staff_ids = array_column($roommate_rel_models->toArray(), 'apply_staff_id');
                    $apply_staff_ids = $hr_staff_repository->getStaffListByStaffIds($apply_staff_ids);
                    $apply_staff_ids = array_column($apply_staff_ids, 'state', 'staff_info_id');

                    $confirmed_count        = 0;
                    $is_invalid_order       = false;
                    $remind_apply_staff_ids = [];
                    $invalid_type           = '';
                    foreach ($roommate_rel_models as $rel_model) {
                        $_log .= "当前共同住宿出差单信息: ID-{$rel_model->id}; 申请人-{$rel_model->apply_staff_id}; 出差单号-{$rel_model->serial_no}; 确认状态-{$rel_model->confirm_status}" . PHP_EOL;

                        // 已确认的 计数
                        if (in_array($rel_model->confirm_status, [
                            ReimbursementEnums::TRAVEL_ROOMMATE_CONFIRM_STATUS_2,
                            ReimbursementEnums::TRAVEL_ROOMMATE_CONFIRM_STATUS_3,
                        ])) {
                            $confirmed_count++;
                            continue;
                        }

                        // 单据作废
                        if (in_array($rel_model->confirm_status, [
                            ReimbursementEnums::TRAVEL_ROOMMATE_CONFIRM_STATUS_4,
                            ReimbursementEnums::TRAVEL_ROOMMATE_CONFIRM_STATUS_5,
                        ])) {
                            if ($rel_model->confirm_status == ReimbursementEnums::TRAVEL_ROOMMATE_CONFIRM_STATUS_4) {
                                $invalid_type = ReimbursementEnums::INVALID_TYPE_2;
                            } else {
                                $invalid_type = ReimbursementEnums::INVALID_TYPE_4;
                            }

                            $is_invalid_order = true;
                            continue;
                        }

                        // 待确认的
                        $apply_staff_state = $apply_staff_ids[$rel_model->apply_staff_id] ?? '';

                        // 自动离职确认
                        if ($apply_staff_state == StaffInfoEnums::STAFF_STATE_LEAVE) {
                            $update_rel_data = [
                                'confirm_status' => ReimbursementEnums::TRAVEL_ROOMMATE_CONFIRM_STATUS_3,
                                'confirm_at'     => date('Y-m-d H:i:s'),
                            ];

                            if ($rel_model->i_update($update_rel_data) === false) {
                                throw new Exception('离职自动确认状态更新失败,' . $rel_model->getErrorMessagesString(), ErrCode::$SYSTEM_ERROR);
                            }

                            $confirmed_count++;
                            continue;
                        }

                        // 未离职, 判断确认是否超时
                        $created_date = new DateTime($order_model->apply_date);
                        $interval     = $created_date->diff($current_date);
                        if ($interval->days > $valid_days) {
                            $update_rel_data = [
                                'confirm_status' => ReimbursementEnums::TRAVEL_ROOMMATE_CONFIRM_STATUS_5,
                                'confirm_at'     => date('Y-m-d H:i:s'),
                            ];
                            if ($rel_model->i_update($update_rel_data) === false) {
                                throw new Exception('超时自动拒绝状态更新失败,' . $rel_model->getErrorMessagesString(), ErrCode::$SYSTEM_ERROR);
                            }

                            $invalid_type     = ReimbursementEnums::INVALID_TYPE_4;
                            $is_invalid_order = true;
                        } else {
                            $remind_apply_staff_ids[] = $rel_model->apply_staff_id;
                        }
                    }

                    // 作废单据
                    if ($is_invalid_order) {
                        $_log .= ', 已作废' . PHP_EOL;

                        $update_order_data = [
                            'status'     => ReimbursementEnums::STATUS_DISCARDED,
                            'pay_status' => ReimbursementEnums::PAY_STATUS_UN_PAY,
                            'updated_at' => date('Y-m-d H:i:s'),
                        ];
                        if ($order_model->i_update($update_order_data) === false) {
                            throw new Exception('单据作废状态更新失败,' . $order_model->getErrorMessagesString(), ErrCode::$SYSTEM_ERROR);
                        }

                        if ($invalid_type == ReimbursementEnums::INVALID_TYPE_2) {
                            $invalid_reason = $en_lang->_('reimbursement_save_error_035') . $th_lang->_('reimbursement_save_error_035');
                        }

                        $invalid_params = [
                            'operate_channel' => ReimbursementEnums::OPERATE_CHANNEL_OA,
                            'operate_type'    => $invalid_type,
                            'invalid_reason'  => $invalid_reason,
                        ];
                        $invalid_user   = [
                            'id' => 10000,
                        ];
                        AddService::getInstance()->createInvalidRecord($order_model, $invalid_params, $invalid_user);

                        $invalid_order_count++;
                    } elseif ($confirmed_count == count($roommate_rel_models)) {
                        $_log .= ', 已全部确认, 将发送签字消息提醒' . PHP_EOL;

                        // 全部确认->待签字
                        $update_order_data = [
                            'status'     => ReimbursementEnums::STATUS_WAITING_SIGNED,
                            'updated_at' => date('Y-m-d H:i:s'),
                        ];

                        if ($order_model->i_update($update_order_data) === false) {
                            throw new Exception('单据待签字状态更新失败,' . $order_model->getErrorMessagesString(), ErrCode::$SYSTEM_ERROR);
                        }

                        // 给申请人发消息
                        $send_res = $base_service->sendMsgNotice(ReimbursementEnums::MSG_SCENCE_WAITING_SIGNED, [$order_model->apply_id],
                            $order_model);
                        if ($send_res) {
                            $_log .= '待签字消息发送成功' . PHP_EOL;
                        } else {
                            throw new BusinessException('待签字消息发送失败', ErrCode::$BUSINESS_ERROR);
                        }

                        $completed_confirmed_count++;
                    }

                    // 共同住宿人再次确认提醒
                    if (!empty($remind_apply_staff_ids)) {
                        $remind_apply_staff_ids = array_unique($remind_apply_staff_ids);
                        $send_res               = $base_service->sendMsgNotice(ReimbursementEnums::MSG_SCENCE_WAITING_CONFIRMED,
                            $remind_apply_staff_ids, $order_model);
                        if ($send_res) {
                            $_log .= '再次确认消息发送成功' . PHP_EOL;
                        } else {
                            throw new BusinessException('再次确认消息发送失败', ErrCode::$BUSINESS_ERROR);
                        }

                        $need_again_confirm_count++;
                    }

                    $db->commit();
                } catch (ValidationException $e) {
                    $db->rollback();
                    $_log .= $e->getMessage();
                } catch (Exception $e) {
                    $db->rollback();
                    $_log         .= $e->getMessage();
                    $logger_level = 'notice';
                }

                $this->logger->$logger_level($_log);
                echo $_log . PHP_EOL;
            }
        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;
        } catch (BusinessException $e) {
            $log         .= $e->getMessage() . PHP_EOL;
            $logger_type = 'notice';
        } catch (Exception $e) {
            $log         .= $e->getMessage() . PHP_EOL;
            $logger_type = 'error';
        }

        $log .= '已完成确认的数量: ' . ($completed_confirmed_count ?? 'null') . PHP_EOL;
        $log .= '需再次确认的数量: ' . ($need_again_confirm_count ?? 'null') . PHP_EOL;
        $log .= '已作废的数量: ' . ($invalid_order_count ?? 'null') . PHP_EOL;
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        $this->logger->$logger_type('处理共同住宿人待确认数据: ' . $log);
        exit($log);
    }

    /**
     * 报销申请待办人缓存预热初始化(BY端 员工获取 报销申请红点数所需)
     *
     * php app/cli.php reimbursement apply_pending_perhot
     */
    public function apply_pending_perhotAction()
    {
        $log         = '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $logger_type = 'info';

        try {
            // 获取报销申请的待办人
            $apply_pending_list = Reimbursement::find([
                'conditions' => 'status IN ({status_item:array})',
                'bind'       => [
                    'status_item' => [
                        ReimbursementEnums::STATUS_WAITING_SIGNED,
                        ReimbursementEnums::STATUS_WAITING_SUBMITTED,
                    ],
                ],
                'columns'    => ['created_id', 'apply_id'],
            ])->toArray();
            $created_ids        = array_column($apply_pending_list, 'created_id');
            $apply_ids          = array_column($apply_pending_list, 'apply_id');

            // 待确认的共同住宿人
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['main' => Reimbursement::class]);
            $builder->leftjoin(ReimbursementDetailTravelRoommateRelModel::class, 'main.id = rel.re_id', 'rel');
            $builder->where('rel.confirm_status = :confirm_status:', ['confirm_status' => ReimbursementEnums::TRAVEL_ROOMMATE_CONFIRM_STATUS_1,]);
            $builder->andWhere('main.status = :status:', ['status' => ReimbursementEnums::STATUS_WAITING_CONFIRMED]);
            $waiting_confirmed_ids = $builder->columns('rel.apply_staff_id')->getQuery()->execute()->toArray();
            $waiting_confirmed_ids = array_column($waiting_confirmed_ids, 'apply_staff_id');

            $all_pending_ids = array_unique(array_merge($created_ids, $apply_ids, $waiting_confirmed_ids));

            $log .= 'OA-报销申请待办人(待提交/待签字/待确认): ' . count($all_pending_ids) . ' 个' . PHP_EOL;
            $log .= '清单如下: ' . implode(',', $all_pending_ids) . PHP_EOL;

            // 移除原有的待办人
            $prehot_cache_key = RedisKey::OA_REIMBURSEMENT_APPLY_PREHOT_CACHE_KEY;
            RedisClient::getInstance()->getClient()->delete($prehot_cache_key);

            // 批量添加新待办人
            if (!empty($all_pending_ids)) {
                $sadd_count = RedisClient::getInstance()->getClient()->sadd($prehot_cache_key, ...$all_pending_ids);
                if (!$sadd_count) {
                    throw new Exception('初始化失败', ErrCode::$SYSTEM_ERROR);
                } else {
                    $log .= "初始化成功[{$sadd_count}]" . PHP_EOL;
                }
            }

        } catch (Exception $e) {
            $log         .= '处理异常: ' . $e->getMessage() . PHP_EOL;
            $logger_type = 'notice';
        }

        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $this->logger->$logger_type($log);
        exit($log);
    }

    /**
     * 处理申请人待签字数据
     * 当地时间每天10点
     *
     * php app/cli.php reimbursement handle_sign_pending
     *
     */
    public function handle_sign_pendingAction()
    {
        $log = '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        $logger_type = 'info';
        try {
            $list = Reimbursement::find([
                'conditions' => 'status = :status:',
                'bind'       => ['status' => ReimbursementEnums::STATUS_WAITING_SIGNED],
                'columns'    => ['id', 'no'],
            ])->toArray();

            $log .= '待签字的报销单数量: ' . count($list) . PHP_EOL;
            if (empty($list)) {
                throw new ValidationException('无待签字的报销单', ErrCode::$VALIDATE_ERROR);
            }

            // 共同住宿超时天数配置
            $valid_days = EnumsService::getInstance()->getSettingEnvValue('reimbursement_apply_sign_valid_days');
            if (!is_numeric($valid_days)) {
                throw new BusinessException('申请人签字时效天数配置有误, 请联系产品[reimbursement_apply_sign_valid_days]', ErrCode::$VALIDATE_ERROR);
            }

            $current_date = new DateTime();
            $base_service = new BaseService();

            $th_lang        = $base_service::getTranslation('th');
            $en_lang        = $base_service::getTranslation('en');
            $invalid_reason = $en_lang->_('reimbursement_save_error_025') . $th_lang->_('reimbursement_save_error_025');

            $need_again_remind_count = 0;
            $invalid_order_count     = 0;
            foreach ($list as $value) {
                $logger_level = 'info';
                $_log         = '当前单号 ' . $value['no'] . PHP_EOL;

                $db = $this->getDI()->get('db_oa');
                $db->begin();
                try {
                    // 单据维度, 加锁
                    $order_model = Reimbursement::findFirst([
                        'conditions' => 'id = :id:',
                        'bind'       => ['id' => $value['id']],
                        'for_update' => true,
                    ]);

                    if ($order_model->status != ReimbursementEnums::STATUS_WAITING_SIGNED) {
                        throw new ValidationException("非待签字状态, 跳过 [status={$order_model->status}]", ErrCode::$VALIDATE_ERROR);
                    }

                    // 判断是否超时
                    $created_date = new DateTime($order_model->apply_date);
                    $interval     = $created_date->diff($current_date);
                    if ($interval->days > $valid_days) {
                        $_log              .= "已超过签字时效 {$interval->days} 天";
                        $update_order_data = [
                            'status'     => ReimbursementEnums::STATUS_DISCARDED,
                            'pay_status' => ReimbursementEnums::PAY_STATUS_UN_PAY,
                            'updated_at' => date('Y-m-d H:i:s'),
                        ];
                        if ($order_model->i_update($update_order_data) === false) {
                            throw new Exception('单据作废状态更新失败,' . $order_model->getErrorMessagesString(), ErrCode::$SYSTEM_ERROR);
                        }

                        $invalid_params = [
                            'operate_channel' => ReimbursementEnums::OPERATE_CHANNEL_OA,
                            'operate_type'    => ReimbursementEnums::INVALID_TYPE_5,
                            'invalid_reason'  => $invalid_reason,
                        ];
                        $invalid_user   = [
                            'id' => 10000,
                        ];
                        AddService::getInstance()->createInvalidRecord($order_model, $invalid_params, $invalid_user);

                        $invalid_order_count++;
                    } else {
                        $_log .= "未超过签字时效, 再次发送签字消息提醒";

                        // 给申请人发消息
                        $send_res = $base_service->sendMsgNotice(ReimbursementEnums::MSG_SCENCE_WAITING_SIGNED, [$order_model->apply_id],
                            $order_model);
                        if (!$send_res) {
                            throw new BusinessException('待签字消息发送失败', ErrCode::$BUSINESS_ERROR);
                        }

                        $need_again_remind_count++;

                        $_log .= ' ,发送成功';
                    }

                    $db->commit();
                } catch (ValidationException $e) {
                    $db->rollback();
                    $_log .= $e->getMessage();
                } catch (Exception $e) {
                    $db->rollback();
                    $_log         .= $e->getMessage();
                    $logger_level = 'notice';
                }

                $this->logger->$logger_level($_log);
                echo $_log . PHP_EOL;
            }
        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;
        } catch (BusinessException $e) {
            $log         .= $e->getMessage() . PHP_EOL;
            $logger_type = 'notice';
        } catch (Exception $e) {
            $log         .= $e->getMessage() . PHP_EOL;
            $logger_type = 'error';
        }

        $log .= '再次提醒的数量: ' . ($need_again_confirm_count ?? 'null') . PHP_EOL;
        $log .= '已作废的数量: ' . ($invalid_order_count ?? 'null') . PHP_EOL;
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        $this->logger->$logger_type('处理申请人待签字数据: ' . $log);
        exit($log);
    }

    /**
     * 报销消息发送
     *
     * php app/cli.php reimbursement send_msg
     */
    public function send_msgAction()
    {
        $log         = '当前时间: ' . get_datetime_with_milliseconds() . PHP_EOL;
        $logger_type = 'info';

        try {
            $base_service = new BaseService();
            $queue_name   = $base_service->getRedisMsgQueueName();
            while (true) {
                $_log = '';

                // 读取redis队列消息
                $msg_data = $base_service->getRedisQueueData($queue_name);
                if (empty($msg_data)) {
                    throw new ValidationException("{$queue_name} 无待发消息", ErrCode::$VALIDATE_ERROR);
                }

                $msg_id     = $msg_data['msg_id'] ?? '';
                $created_at = $msg_data['created_at'] ?? '';
                $msg_params = $msg_data['msg_params'] ?? [];

                $_log .= "msg_id={$msg_id}, created_at={$created_at}";

                if (empty($msg_id) || empty($created_at) || empty($msg_params)) {
                    throw new BusinessException("{$queue_name} 取出的数据不合法, {$_log}, 请确认", ErrCode::$VALIDATE_ERROR);
                }

                $msg_log_model = ReimbursementMsgLogModel::findFirst([
                    'conditions' => 'msg_id = :msg_id:',
                    'bind'       => ['msg_id' => $msg_id],
                ]);
                if (empty($msg_log_model)) {
                    $time_interval = time() - strtotime($created_at);
                    if ($time_interval > 180) {
                        $minute_interval = floor($time_interval / 60);
                        $_log            .= ", 表中未查到, 且 该消息创建已超过 {$minute_interval} 分钟, 归为无效数据, 不再重新入队发送";
                    } else {
                        $base_service->setRedisQueueData($queue_name, $msg_data);
                        $_log .= ', 表中未查到, 可能数据落库有延迟, 已重新入队, 待稍后发送';
                        sleep(2);
                    }

                    echo $_log . PHP_EOL;
                    $this->logger->info($_log);
                    continue;
                }

                if ($msg_log_model->send_status == ReimbursementEnums::MSG_SEND_SUCCESS) {
                    $_log .= ', 该消息已发送成功, 跳过';
                    echo $_log . PHP_EOL;
                    $this->logger->info($_log);
                    continue;
                }

                $push_params = $msg_params['push_params'];
                $kit_params  = $msg_params['kit_params'];

                // push
                $push_data = [
                    'staff_info_id'    => $push_params['staff_info_id'],        // 员工工号
                    'message_title'    => $push_params['message_title'],        // 标题
                    'message_content'  => $push_params['message_content'] ?? '',// 内容
                    'message_scheme'   => $push_params['message_scheme'],       // 地址
                    'message_priority' => 1,                                    // push优先级: 0-普通; 1-优先
                    'src'              => 'backyard',
                    'silence'          => 0 // 0-普通推送; 1-静默推送
                ];
                $ret       = new ApiClient('bi_svc', '', 'push_to_staff');
                $ret->setParams([$push_data]);
                $res = $ret->execute();
                if (isset($res['result']) && $res['result'] == true) {
                    $_log .= ', push发送成功';
                } else {
                    $_log .= ", push发送失败";
                }

                // 站内信
                $client = new ApiClient('hcm_rpc', '', 'add_kit_message');
                $client->setParams([$kit_params]);
                $send_res = $client->execute();
                if (isset($send_res['result']['code']) && $send_res['result']['code'] == ErrCode::$SUCCESS) {
                    $log_update['send_status'] = ReimbursementEnums::MSG_SEND_SUCCESS;
                    $_log                      .= ', 站内信发送成功';
                } else {
                    $log_update['send_status'] = ReimbursementEnums::MSG_SEND_FAIL;
                    $_log                      .= ', 站内信发送失败';
                }

                $log_update['send_at'] = date('Y-m-d H:i:s');
                if ($msg_log_model->i_update($log_update) === false) {
                    $_log .= ', 日志表更新失败';
                } else {
                    $_log .= ', 日志表更新成功';
                }

                echo $_log . PHP_EOL;
                $this->logger->info($_log);
            }
        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;
            sleep(3);
        } catch (BusinessException $e) {
            $logger_type = 'notice';
            $log         .= $e->getMessage() . PHP_EOL;
            sleep(5);
        } catch (Exception $e) {
            $logger_type = 'warning';
            $log         .= $e->getMessage() . PHP_EOL;
            sleep(10);
        }

        $log .= '结束时间: ' . get_datetime_with_milliseconds() . PHP_EOL;
        $this->logger->$logger_type('报销-消息-发送: ' . $log);
        exit($log);
    }

    /**
     * 消息补发
     *
     * php app/cli.php reimbursement manual_send_msg
     */
    public function manual_send_msgAction(array $params = [])
    {
        $msg_id = $params[0] ?? '';

        $log         = '当前时间: ' . get_datetime_with_milliseconds() . PHP_EOL;
        $log         .= 'msg_id: ' . $msg_id . PHP_EOL;
        $logger_type = 'info';

        try {
            if (empty($msg_id)) {
                throw new ValidationException('msg_id为空, 请确认', ErrCode::$VALIDATE_ERROR);
            }

            $msg_log_model = ReimbursementMsgLogModel::findFirst([
                'conditions' => 'msg_id = :msg_id:',
                'bind'       => ['msg_id' => $msg_id],
            ]);
            if (empty($msg_log_model)) {
                throw new ValidationException('表中不存在待补发的消息, 请确认msg_id', ErrCode::$VALIDATE_ERROR);
            }

            $msg_params = json_decode($msg_log_model->msg_params, true);

            // PUSH
            $push_params = $msg_params['push_params'];
            $kit_params  = $msg_params['kit_params'];

            // push
            $push_data = [
                'staff_info_id'    => $push_params['staff_info_id'],        // 员工工号
                'message_title'    => $push_params['message_title'],        // 标题
                'message_content'  => $push_params['message_content'] ?? '',// 内容
                'message_scheme'   => $push_params['message_scheme'],       // 地址
                'message_priority' => 1,                                    // push优先级: 0-普通; 1-优先
                'src'              => 'backyard',
                'silence'          => 0 // 0-普通推送; 1-静默推送
            ];
            $ret       = new ApiClient('bi_svc', '', 'push_to_staff');
            $ret->setParams([$push_data]);
            $res = $ret->execute();
            if (isset($res['result']) && $res['result'] == true) {
                $log .= 'push发送成功' . PHP_EOL;
            } else {
                $log .= 'push发送失败' . PHP_EOL;
            }

            // 站内信
            $client = new ApiClient('hcm_rpc', '', 'add_kit_message');
            $client->setParams([$kit_params]);
            $send_res = $client->execute();
            if (isset($send_res['result']['code']) && $send_res['result']['code'] == ErrCode::$SUCCESS) {
                $log_update['send_status'] = ReimbursementEnums::MSG_SEND_SUCCESS;
                $log                       .= '站内信发送成功' . PHP_EOL;
            } else {
                $log_update['send_status'] = ReimbursementEnums::MSG_SEND_FAIL;
                $log                       .= '站内信发送失败' . PHP_EOL;
            }

            $log_update['send_at'] = date('Y-m-d H:i:s');
            if ($msg_log_model->i_update($log_update) === false) {
                $log .= '日志表更新失败' . PHP_EOL;
            } else {
                $log .= '日志表更新成功' . PHP_EOL;
            }

        } catch (ValidationException $e) {
            $logger_type = 'notice';
            $log         .= $e->getMessage() . PHP_EOL;
        } catch (Exception $e) {
            $logger_type = 'warning';
            $log         .= $e->getMessage() . PHP_EOL;
        }

        $log .= '结束时间: ' . get_datetime_with_milliseconds() . PHP_EOL;
        $this->logger->$logger_type('报销-消息-补发: ' . $log);
        exit($log);
    }


}
