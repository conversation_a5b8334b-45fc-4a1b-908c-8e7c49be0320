<?php

use App\Library\Enums;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\SysDepartmentModel;
use App\Models\oa\WorkflowRequestModel;
use App\Modules\Deposit\Models\DepositModel;
use App\Modules\Deposit\Models\DepositReturnModel;
use App\Modules\Deposit\Services\DepositService;
use App\Modules\Common\Models\EnvModel;
use App\Library\Enums\DepositEnums;
use App\Modules\OrdinaryPayment\Models\OrdinaryPayment;
use App\Modules\OrdinaryPayment\Models\OrdinaryPaymentDetail;
use App\Modules\Payment\Models\PaymentStoreRenting;
use App\Modules\Payment\Models\PaymentStoreRentingDetail;
use App\Modules\Purchase\Models\PurchasePayment;
use App\Modules\Purchase\Models\PurchasePaymentReceipt;
use App\Modules\Reimbursement\Models\Detail;
use App\Modules\Reimbursement\Models\Reimbursement;
use App\Modules\Reimbursement\Services\ListService;
use App\Library\ErrCode;
use App\Library\Enums\GlobalEnums;
use App\Modules\User\Services\UserService;
use App\Modules\Workflow\Models\WorkflowAuditLogModel;

class DepositTask extends BaseTask
{
    /**
     * 每月第一天北京时间5：30点  部门负责人
     * @Date: 8/22/22 5:39 PM
     *  php  cli.php  deposit send_month_department
     **@author: peak pan
     */
    public function send_month_departmentAction()
    {
        ListService::setLanguage('en');
        // 各环境各国家dashboard页地址
        $url = EnvModel::getEnvByCode(GlobalEnums::OA_DASHBOARD_PAGE_URL_CODE);
        $url = !empty($url) ? $url : GlobalEnums::OA_DASHBOARD_PAGE_URL_DEFAULT;
        try {
            echo date('Ymd H:i:s') . " 脚本开始执行" . PHP_EOL;

            //查询报销部门负责人
            $reimbursement = DepositService::getInstance()->getReimbursementToEmailData();
            echo date('Ymd H:i:s') . " 查询报销部门负责人 查询的数据" . json_encode($reimbursement) . PHP_EOL;

            //查询付款部门负责人
            $ordinary_payment = DepositService::getInstance()->getOrdinaryPaymentToEmailData();
            echo date('Ymd H:i:s') . " 查询付款部门负责人 查询的数据" . json_encode($ordinary_payment) . PHP_EOL;

            //查询采购部门负责人
            $purchase_payment = DepositService::getInstance()->getPurchasePaymentToEmailData();
            echo date('Ymd H:i:s') . " 查询采购部门负责人 查询的数据" . json_encode($purchase_payment) . PHP_EOL;

            //查询租房部门负责人
            $payment_store_renting = DepositService::getInstance()->getPaymentStoreRentingToEmailData();
            echo date('Ymd H:i:s') . " 查询租房部门负责人 查询的数据" . json_encode($payment_store_renting) . PHP_EOL;


            $email_arr = array_merge($reimbursement ?? [], $ordinary_payment ?? [], $purchase_payment ?? [],
                $payment_store_renting ?? []);

            if (empty($email_arr)) {
                echo date('Ymd H:i:s') . " 无数据" . json_encode($payment_store_renting) . PHP_EOL;
            }

            $rs_email_data = [];
            foreach ($email_arr as $data) {
                $rs_email_data[$data['department']][] = array_unique($data['apply_no']);
            }
            $department    = array_filter(array_unique(array_column($email_arr, 'department')));
            $email_arr_dep = array_column($email_arr, 'email', 'department');

            $rs = [];
            foreach ($department as $item) {
                $rty['department'] = $item;
                $rty['email']      = $email_arr_dep[$item];
                $rty['apply_no']   = array_reduce($rs_email_data[$item], 'array_merge', []);
                $rs[]              = $rty;
            }

            echo date('Ymd H:i:s') . " 合并过滤之后的数据" . json_encode($rs) . PHP_EOL;

            if (empty($rs)) {
                echo date('Ymd H:i:s') . " 邮件地址为空 " . json_encode($rs) . PHP_EOL;
                exit;
            }

            foreach ($rs as $senddata) {
                $params['apply_no']      = $senddata['apply_no'];
                $params['export_type']   = DepositEnums::EXPORT_TYPE_ZERO;
                $params['type']          = DepositEnums::DEPOSIT_IS_DELETED_YES;
                $params['return_status'] = DepositEnums::DEPOSIT_RETURN_STATUS_NOT;

                $result = DepositService::getInstance()->download($params, ['id' => 0],
                    DepositEnums::DEPOSIT_TASK_DOWNLOAD_LIMIT);

                if (isset($result['code']) && $result['code'] == ErrCode::$SUCCESS && !empty($result['data'])) {
                    $attachment = $result['data'];
                    $this->logger->info('取数成功: Excel File = ' . $attachment);
                } else {
                    $this->logger->warning('取数 或 生成Excel 失败，请检查');
                    exit();
                }

                //发送邮件并记录日志
                $title   = '未归还押金数据 Reminder of Outstanding Deposit';
                $content = '
        Hi,<br><br>
         附件是当前您负责的押金清单，请查收。可以在“OA-押金管理-我的押金”查看更多详细信息，谢谢！【<a href="' . $url . '" target="_blank">"' . $url . '" </a>】<br/>
        Attachment is the list of outstanding deposit items, please have a check! You can check in "OA-Deposit Management- My Deposit" to get further information! Thanks!【<a href="' . $url . '" target="_blank">"' . $url . '" </a>】<br/>
        附件链接Attachment Link:' . $attachment;
                if ($this->mailer->sendAsync($senddata['email'], $title, $content)) {
                    $this->logger->info('send success');
                    echo date('Ymd H:i:s') . " 邮件发送成功:" . $senddata['email'] . PHP_EOL;
                    sleep(2);
                } else {
                    echo date('Ymd H:i:s') . " 邮件发送失败:" . $senddata['email'] . PHP_EOL;
                    $this->logger->warning('send fail');
                }
                $title = $content = $senddata['email'] = '';
            }
        } catch (Exception $e) {
            $this->logger->warning('email error==' . $e->getMessage());
        }
    }


    /**
     * 如果合同押金状态=已过期，且当前日期大于等于合同结束日+7天、或终止日期+7天后，或作废日期+7天后，且未归还的押金清单
     * @Date: 8/22/22 8:58 PM
     * @return:
     *  php  cli.php  deposit send_day_department_not_deposit
     **@author: peak pan
     */
    public function send_day_department_not_depositAction()
    {
        ListService::setLanguage('en');
        $url    = EnvModel::getEnvByCode(GlobalEnums::OA_DASHBOARD_PAGE_URL_CODE);
        $url    = !empty($url) ? $url : GlobalEnums::OA_DASHBOARD_PAGE_URL_DEFAULT;
        $status = 2;
        try {
            echo date('Ymd H:i:s') . " 脚本开始执行" . PHP_EOL;

            $payment_store_renting = DepositService::getInstance()->getPaymentStoreRentingToEmailData($status);

            echo date('Ymd H:i:s') . " 查询租房部门负责人 查询的数据" . json_encode($payment_store_renting) . PHP_EOL;

            $rs_email_data = [];
            foreach ($payment_store_renting as $data) {
                $rs_email_data[$data['department']][] = $data['apply_no'];
            }

            $department = array_filter(array_unique(array_column($payment_store_renting, 'department')));

            $payment_store_dep = array_column($payment_store_renting, 'email', 'department');

            $rs = [];
            foreach ($department as $item) {
                $rty['department'] = $item;
                $rty['email']      = $payment_store_dep[$item];
                $rty['detail_id']  = $rs_email_data[$item];
                $rs[]              = $rty;
            }

            foreach ($rs as $senddata) {
                $params['detail_id']     = $senddata['detail_id'];
                $params['export_type']   = DepositEnums::EXPORT_TYPE_ZERO;
                $params['type']          = DepositEnums::DEPOSIT_PAYMENT_STORE_RENTING;
                $params['return_status'] = DepositEnums::DEPOSIT_RETURN_STATUS_NOT;

                $result = DepositService::getInstance()->download($params, ['id' => 0],
                    DepositEnums::DEPOSIT_TASK_DOWNLOAD_LIMIT);

                if (isset($result['code']) && $result['code'] == ErrCode::$SUCCESS && !empty($result['data'])) {
                    $attachment = $result['data'];
                    echo date('Ymd H:i:s') . " 取数成功: Excel File" . json_encode($attachment) . PHP_EOL;
                    $this->logger->info('取数成功: Excel File = ' . $attachment);
                } else {
                    echo date('Ymd H:i:s') . " 取数失败:原因" . json_encode($result['message']) . PHP_EOL;
                    $this->logger->warning('取数 或 生成Excel 失败，请检查');
                    exit();
                }

                //发送邮件并记录日志
                $title   = '合同已过期 押金未归还数据';
                $content = '
        Hi,<br><br>
          附件是合同已过期，但是押金还未归还的数据清单，请查收。可以在“OA-押金管理-我的押金”查看更多详细信息，谢谢！【<a href="' . $url . '" target="_blank">"' . $url . '" </a>】<br/>
        Attachment is the list of expired contract information which is not returned in time. You can check in "OA-Deposit Management- My Deposit" to get further information! Thanks!【<a href="' . $url . '" target="_blank">"' . $url . '" </a>】<br/>
        附件链接Attachment Link:' . $attachment;

                if ($this->mailer->sendAsync($senddata['email'], $title, $content)) {
                    echo date('Ymd H:i:s') . " 邮件发送成功:" . $senddata['email'] . PHP_EOL;
                    sleep(2);
                    $this->logger->info('send success');
                } else {
                    echo date('Ymd H:i:s') . " 邮件发送失败:" . $senddata['email'] . PHP_EOL;
                    $this->logger->warning('send fail');
                }

                $content = $senddata['email'] = '';
            }
        } catch (Exception $e) {
            $this->logger->warning('email error==' . $e->getMessage());
        }
    }

    /**
     * 每月第一天北京时间6点；内容：所有未归还的押金清单,提供excel导出清单
     * @Date: 8/22/22 8:58 PM
     * @return:
     *  php  cli.php  deposit send_month_one_day_finance_deposit
     **@author: peak pan
     */
    public function send_month_one_day_finance_depositAction()
    {
        ListService::setLanguage('en');

        $url        = EnvModel::getEnvByCode(GlobalEnums::OA_DASHBOARD_PAGE_URL_CODE);
        $url        = !empty($url) ? $url : GlobalEnums::OA_DASHBOARD_PAGE_URL_DEFAULT;
        $from_email = EnvModel::getEnvByCode('loan_data_send_email');

        try {
            echo date('Ymd H:i:s') . " 脚本开始执行" . PHP_EOL;

            $emails = explode(',', $from_email);

            echo date('Ymd H:i:s') . " 接收人邮箱" . json_encode($emails) . PHP_EOL;

            if (empty($emails)) {
                echo date('Ymd H:i:s') . " 邮件地址为空 " . json_encode($emails) . PHP_EOL;
                exit;
            }

            $params['export_type']   = DepositEnums::EXPORT_TYPE_ZERO;
            $params['type']          = DepositEnums::DEPOSIT_IS_DELETED_YES;
            $params['return_status'] = DepositEnums::DEPOSIT_RETURN_STATUS_NOT;
            $result                  = DepositService::getInstance()->download($params, ['id' => 0],
                DepositEnums::DEPOSIT_TASK_DOWNLOAD_LIMIT);

            if (isset($result['code']) && $result['code'] == ErrCode::$SUCCESS && !empty($result['data'])) {
                $attachment = $result['data'];
                echo date('Ymd H:i:s') . " 取数成功: Excel File" . json_encode($attachment) . PHP_EOL;
                $this->logger->info('取数成功: Excel File = ' . $attachment);
            } else {
                echo date('Ymd H:i:s') . " 取数失败:原因" . json_encode($result['message']) . PHP_EOL;
                $this->logger->warning('取数 或 生成Excel 失败，请检查');
                exit();
            }

            //发送邮件并记录日志
            $title   = '未归还押金数据';
            $content = '
        Hi,<br><br>
           附件是所有未归还的押金，请查收。可以在“OA-押金管理-数据查询”查看更多详细信息，谢谢！【<a href="' . $url . '" target="_blank">"' . $url . '" </a>】<br/>
        Attachement is the list of outstanding deposit items, please have a check! You can check in "OA-Deposit Management- Data Query" to get further information! Thanks!【<a href="' . $url . '" target="_blank">"' . $url . '" </a>】<br/>
        附件链接Attachment Link:' . $attachment;

            foreach ($emails as $email) {
                if ($this->mailer->sendAsync($email, $title, $content)) {
                    echo date('Ymd H:i:s') . " 邮件发送成功:" . $email . PHP_EOL;
                    $this->logger->info('send success');
                } else {
                    $this->logger->warning('send fail');
                }
            }
        } catch (Exception $e) {
            $this->logger->warning('email error==' . $e->getMessage());
        }
    }

    /**
     * 同步押金负责人的所属部门(一次性)
     *
     * php app/cli.php deposit sync_node_dept_info
     *
     */
    public function sync_node_dept_infoAction()
    {
        $log = '任务名称: ' . __FUNCTION__ . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log .= '初始内存: ' . memory_usage() . PHP_EOL;

        // 获取押金负责人部门为空的押金
        $models = DepositModel::find([
            'conditions' => 'apply_node_department_id = 0',
        ]);

        $models_count = count($models);
        $log          .= '待初始化押金条数: ' . $models_count . PHP_EOL;
        if (!$models_count) {
            exit($log);
        }

        // 获取押金负责人
        $apply_ids = array_values(array_unique(array_column($models->toArray(), 'apply_id')));
        $log       .= '押金负责人共: ' . count($apply_ids) . PHP_EOL;

        // 获取押金负责人所属部门ID和名称
        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            'staff.staff_info_id',
            'staff.node_department_id',
            'dept.name AS node_department_name',
        ]);
        $builder->from(['staff' => HrStaffInfoModel::class]);
        $builder->leftJoin(SysDepartmentModel::class, 'staff.node_department_id = dept.id', 'dept');
        $builder->inWhere('staff.staff_info_id', $apply_ids);
        $apply_ids = $builder->getQuery()->execute()->toArray();
        $apply_ids = array_column($apply_ids, null, 'staff_info_id');
        $log       .= '共找到员工数: ' . count($apply_ids) . PHP_EOL;

        // 逐个更新押金的负责部门
        $null_apply_log     = [];
        $save_error_log     = [];
        $save_success_count = 0;
        foreach ($models as $model) {
            $apply_info = $apply_ids[$model->apply_id] ?? [];
            if (empty($apply_info)) {
                $null_apply_log[] = $model->business_no . '-' . $model->detail_id . '-' . $model->apply_id;
                continue;
            }

            $update_info = [
                'apply_node_department_id'   => $apply_info['node_department_id'] ?? 0,
                'apply_node_department_name' => $apply_info['node_department_name'] ?? '',
            ];
            if ($model->i_update($update_info) === false) {
                $save_error_log[] = $model->business_no . '-' . $model->detail_id . '-' . $model->apply_id;
                continue;
            }

            $save_success_count++;
        }

        $log .= '更新成功数: ' . $save_success_count . PHP_EOL;
        $log .= '更新失败的: ' . implode(PHP_EOL, $save_error_log) . PHP_EOL;
        $log .= '未更新的(负责人信息不存在): ' . implode(PHP_EOL, $null_apply_log) . PHP_EOL;

        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        exit($log);
    }

    /**
     * 同步押金表单号为空的数据(一次性)
     *
     * php app/cli.php deposit sync_no
     *
     */
    public function sync_noAction()
    {
        $log = '任务名称: ' . __FUNCTION__ . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log .= '初始内存: ' . memory_usage() . PHP_EOL;

        // 获取单号为空的押金
        $models = DepositModel::find([
            'conditions' => "business_no = ''",
        ]);

        $models_count = count($models);
        $log          .= '单号为空的押金条数: ' . $models_count . PHP_EOL;
        if (!$models_count) {
            exit($log);
        }

        // 逐个更新押金的单号
        $null_log           = [];
        $save_error_log     = [];
        $save_success_count = 0;
        foreach ($models as $model) {
            $business_no = '';

            // 获取押金所属模块的单号
            switch ($model->deposit_type) {
                case DepositEnums::DEPOSIT_REIMBURSEMENT:
                    $builder = $this->modelsManager->createBuilder();
                    $builder->columns([
                        'main.no AS business_no',
                    ]);
                    $builder->from(['detail' => Detail::class]);
                    $builder->leftJoin(Reimbursement::class, 'detail.re_id = main.id', 'main');
                    $builder->where('detail.id = :detail_id:', ['detail_id' => $model->detail_id]);
                    $business_no = $builder->getQuery()->getSingleResult()['business_no'] ?? '';
                    break;

                case DepositEnums::DEPOSIT_ORDINARY_PAYMENT:
                    $builder = $this->modelsManager->createBuilder();
                    $builder->columns([
                        'main.apply_no AS business_no',
                    ]);
                    $builder->from(['detail' => OrdinaryPaymentDetail::class]);
                    $builder->leftJoin(OrdinaryPayment::class, 'detail.ordinary_payment_id = main.id', 'main');
                    $builder->where('detail.id = :detail_id:', ['detail_id' => $model->detail_id]);
                    $business_no = $builder->getQuery()->getSingleResult()['business_no'] ?? '';
                    break;

                case DepositEnums::DEPOSIT_PURCHASE_PAYMENT:
                    $builder = $this->modelsManager->createBuilder();
                    $builder->columns([
                        'main.ppno AS business_no',
                    ]);
                    $builder->from(['detail' => PurchasePaymentReceipt::class]);
                    $builder->leftJoin(PurchasePayment::class, 'detail.ppid = main.id', 'main');
                    $builder->where('detail.id = :detail_id:', ['detail_id' => $model->detail_id]);
                    $business_no = $builder->getQuery()->getSingleResult()['business_no'] ?? '';
                    break;

                case DepositEnums::DEPOSIT_PAYMENT_STORE_RENTING:
                    $builder = $this->modelsManager->createBuilder();
                    $builder->columns([
                        'main.apply_no AS business_no',
                    ]);
                    $builder->from(['detail' => PaymentStoreRentingDetail::class]);
                    $builder->leftJoin(PaymentStoreRenting::class, 'detail.store_renting_id = main.id', 'main');
                    $builder->where('detail.id = :detail_id:', ['detail_id' => $model->detail_id]);
                    $business_no = $builder->getQuery()->getSingleResult()['business_no'] ?? '';
                    break;
            }

            if (empty($business_no)) {
                $null_log[] = $model->deposit_type . '-' . $model->detail_id . '-' . $model->apply_id;
                continue;
            }

            $update_info = [
                'business_no' => $business_no,
            ];
            if ($model->i_update($update_info) === false) {
                $save_error_log[] = $model->deposit_type . '-' . $model->detail_id . '-' . $model->apply_id;
                continue;
            }

            $save_success_count++;
        }

        $log .= '更新成功数: ' . $save_success_count . PHP_EOL;
        $log .= '更新失败数: ' . implode(PHP_EOL, $save_error_log) . PHP_EOL;
        $log .= '单号未找的的数: ' . implode(PHP_EOL, $null_log) . PHP_EOL;

        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        exit($log);
    }

    /**
     * 同步押金归还单的申请人工号(一次性)
     *
     * php app/cli.php deposit sync_return_created_id
     */
    public function sync_return_created_idAction()
    {
        $log = '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log .= '初始内存: ' . memory_usage() . PHP_EOL;

        try {
            // 获取归还申请人是空值的单据
            $null_return_models = DepositReturnModel::find(['conditions' => 'create_id = 0']);
            $null_return_count  = count($null_return_models);
            $log                .= '归还单申请人是空值的单据数量: ' . $null_return_count . PHP_EOL;
            if (empty($null_return_count)) {
                throw new ValidationException('无待处理的归还单', ErrCode::$VALIDATE_ERROR);
            }

            $deposit_ids  = array_column($null_return_models->toArray(), 'deposit_id');
            $deposit_list = DepositModel::find([
                'conditions' => 'id IN ({ids:array})',
                'bind'       => ['ids' => $deposit_ids],
                'columns'    => ['id', 'apply_id'],
            ])->toArray();
            $deposit_list = array_column($deposit_list, 'apply_id', 'id');

            $success_count = 0;
            foreach ($null_return_models as $return_model) {
                $db = $this->getDI()->get('db_oa');
                $db->begin();
                try {
                    $_log = "当前处理归还单号: id-{$return_model->id}, return_code-" . $return_model->return_code . PHP_EOL;
                    $_log .= "原创建人工号 create_id = {$return_model->create_id}" . PHP_EOL;

                    $deposit_apply_id = $deposit_list[$return_model->deposit_id] ?? null;

                    $_log .= "新创建人工号 create_id = {$deposit_apply_id}" . PHP_EOL;
                    if (empty($deposit_apply_id)) {
                        $_log .= '关联的押金申请人为空, 跳过';
                        echo $_log . PHP_EOL;
                        $this->logger->info(['null_return_handle_log' => $_log]);
                        continue;
                    }

                    // 同步审批流
                    $wf_request_models = WorkflowRequestModel::find([
                        'conditions' => 'biz_type = :biz_type: AND biz_value = :biz_value:',
                        'bind'       => [
                            'biz_type'  => Enums::DEPOSIT_RETURN_BIZ_TYPE,
                            'biz_value' => $return_model->id,
                        ],
                    ]);

                    $_log .= '关联审批流数量 ' . count($wf_request_models) . PHP_EOL;
                    foreach ($wf_request_models as $wf_index => $wf_model) {
                        $_log .= "待处理的审批流 {$wf_index}, id={$wf_model->id}, 原申请人={$wf_model->create_staff_id}, 原viewer_ids={$wf_model->viewer_ids}" . PHP_EOL;

                        // 审批流申请人不为空, 无需处理
                        if (!empty($wf_model->create_staff_id)) {
                            continue;
                        }

                        $viewer_ids = $deposit_apply_id . ',' . $wf_model->viewer_ids;
                        $viewer_ids = implode(',', array_filter(array_unique(explode(',', $viewer_ids))));

                        $wf_update = [
                            'create_staff_id' => $deposit_apply_id,
                            'viewer_ids'      => $viewer_ids,
                            'updated_at'      => $wf_model->updated_at,
                        ];

                        $_log .= "同步后申请人={$deposit_apply_id}, viewer_ids={$viewer_ids}" . PHP_EOL;

                        if ($wf_model->i_update($wf_update) === false) {
                            throw new BusinessException('审批流表更新失败', ErrCode::$BUSINESS_ERROR);
                        }

                        $_log .= '审批流表更新成功' . PHP_EOL;

                        // 同步审批流日志申请人
                        $wf_audit_log_model = WorkflowAuditLogModel::findFirst([
                            'conditions' => 'request_id = :request_id: AND audit_action = :audit_action:',
                            'bind'       => [
                                'request_id'   => $wf_model->id,
                                'audit_action' => Enums::WF_ACTION_APPLY,
                            ],
                        ]);

                        $_log .= "待处理的审批流日志 id={$wf_audit_log_model->id}, 原申请人={$wf_audit_log_model->staff_id}" . PHP_EOL;

                        $apply_info = UserService::getInstance()->getLoginUser($deposit_apply_id);
                        if (!empty($wf_audit_log_model) && empty($wf_audit_log_model->staff_id) && !empty($apply_info)) {
                            $_log .= "同步后申请人={$deposit_apply_id}" . PHP_EOL;

                            $audit_log_update = [
                                'staff_id'         => $deposit_apply_id,
                                'staff_name'       => get_name_and_nick_name($apply_info['name'], $apply_info['nick_name']),
                                'staff_department' => $apply_info['department'] ?? '',
                                'staff_job_title'  => $apply_info['job_title'] ?? '',
                                'audit_at'         => $wf_audit_log_model->audit_at,
                            ];
                            if ($wf_audit_log_model->update($audit_log_update) === false) {
                                throw new BusinessException('审批流日志表更新失败', ErrCode::$BUSINESS_ERROR);
                            }

                            $_log .= '审批流日志表更新成功' . PHP_EOL;
                        }
                    }

                    // 同步归还表申请人
                    $return_update = [
                        'create_id'  => $deposit_apply_id,
                        'updated_at' => $return_model->updated_at,
                    ];
                    if ($return_model->i_update($return_update) === false) {
                        throw new BusinessException('归还表更新失败', ErrCode::$BUSINESS_ERROR);
                    }

                    $_log .= '归还表更新成功' . PHP_EOL;

                    $success_count++;

                    echo $_log . PHP_EOL;
                    $this->logger->info(['null_return_handle_log' => $_log]);

                    $db->commit();
                } catch (Exception $e) {
                    $db->rollback();
                    $_log .= '处理异常, 已回滚, 异常原因: ' . $e->getMessage();
                    echo $_log . PHP_EOL;
                    $this->logger->info(['null_return_handle_log' => $_log]);
                }
            }

            $log .= "成功处理: {$success_count}" . PHP_EOL;
        } catch (ValidationException $e) {
            $log .= $e->getMessage();
        }

        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        exit($log);
    }

}
