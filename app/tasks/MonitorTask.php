<?php

/**
 * Created by PhpStorm.
 * Date: 2022/11/7
 * Time: 14:54
 */

use App\Library\Enums;
use App\Library\Enums\AgencyPaymentEnums;
use App\Library\Enums\DownloadCenterEnum;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\MaterialEnums;
use App\Library\Enums\OAWorkflowEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\AccessDataEnums;
use App\Library\Enums\WorkflowPendingEnums;
use App\Library\Enums\BankFlowEnums;
use App\Library\Enums\ImportCenterEnums;
use App\Library\ErrCode;
use App\Library\OssHelper;
use App\Library\Validation\ValidationException;
use App\Library\Exception\BusinessException;
use App\Models\oa\MaterialLeaveAssetsDetailModel;
use App\Models\oa\MaterialLeaveAssetsModel;
use App\Models\oa\WorkflowRequestModel;
use App\Modules\Common\Models\ExcelTaskModel;
use App\Modules\AccessData\Models\AccessDataStaffInfoModel;
use App\Modules\AccessData\Models\AccessDataDepartmentModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\oa\WorkflowModel;
use App\Models\oa\WorkflowNodeModel;
use App\Models\oa\WorkflowSubNodeModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Common\Services\ShortUrlService;
use App\Modules\Reimbursement\Models\RequestSapLog;
use App\Modules\Setting\Services\DataPermissionModuleConfigService;
use App\Modules\Transfer\Models\AuditApprovalModel;
use App\Modules\User\Services\UserService;
use App\Repository\HrStaffRepository;
use App\Modules\BankFlow\Models\BankAccountModel;
use App\Modules\BankFlow\Models\BankFlowModel;
use App\Models\oa\ImportTaskModel;
use App\Models\oa\AgencyPaymentDetailModel;

class MonitorTask extends BaseTask
{
    // 飞书: 产研群机器人消息key
    const FEISHU_PM_ARD_GROUP_MSG_KEY = '561afecd-49ef-4534-a66a-336175988d51';

    // 飞书: OA业务监测提醒群消息key
    const FEISHU_OA_BIZ_WARNING_GROUP_MSG_KEY = '0a1b4a48-a527-48f9-b12a-665b1489a25a';

    // OA业务监测提醒场景一: OA审批流异常提醒(审批人离职/不存在)
    const FEISHU_OA_BIZ_WARNING_SCENE_WORKFLOW_ABNORMAL = 'OA审批流异常提醒';

    const FEISHU_OA_BIZ_WARNING_SCENE_RED_DOT_ABNORMAL = 'OA菜单红点异常提醒';

    const FEISHU_OA_BIZ_WARNING_SCENE_SAP_REQUEST_ABNORMAL = 'SAP抛数异常';

    const FEISHU_OA_BIZ_WARNING_SCENE_FLASH_PAY_BANK_FLOW_ABNORMAL = 'OA在线支付昨日银行付款流水费用类型为空异常提醒';

    const FEISHU_OA_BIZ_WARNING_SCENE_AGENCY_PAYMENT_DETAIL_ABNORMAL = 'OA代理支付外部系统单号在待支付、已支付状态下一个单号存在多条异常提醒';


    /**
     * excel_task监控
     * @param $argv
     * */
    public function excel_task_monitorAction($argv)
    {
        //查询需要报警的task

        echo 'begin' . PHP_EOL;
        $minutes    = $argv[0] ?? 30;
        $start_date = date('Y-m-d H:i:s', strtotime("-1 week"));

        $end_date = date('Y-m-d H:i:s', strtotime("-" . $minutes . "minutes"));
        $list     = ExcelTaskModel::find([
            'conditions' => 'created_at < :end_date: and created_at > :start_date: and status = :status: and is_deleted = :is_deleted:',
            'bind'       => [
                'start_date' => $start_date,
                'end_date'   => $end_date,
                'status'     => DownloadCenterEnum::TASK_STATUS_PENDING,
                'is_deleted' => GlobalEnums::IS_NO_DELETED,
            ],

        ])->toArray();
        if (empty($list)) {
            exit('没有需要预警的task');
        }
        $notice_content['action'] = 'excel_task';
        foreach ($list as $k => $v) {
            $notice_content['task_list'] [] =
                [
                    'staff_info_id' => $v['staff_info_id'],
                    'action_name'   => $v['action_name'],
                    'type'          => $v['type'],
                ];
        }
        $notice_content['reason'] = '超' . $minutes . '分钟未完成任务';
        $notice_content           = json_encode($notice_content, JSON_UNESCAPED_UNICODE);
        echo $notice_content . PHP_EOL;
        $this->logger->warning($notice_content);
        echo 'end' . PHP_EOL;
        exit;
    }

    /**
     * 取数工单系统: 相关部门负责人在职状态监控
     *
     * 说明: 部门负责人非在职时, 需给取数工单系统中的数据部门同事发送相关的邮件提醒, 以便其及时在OA系统取数工单系统中的权限设置模块重新设置部门负责人,
     * 避免出现审批流流转/审批异常的现象
     *
     * php app/cli.php monitor access_data_department_manager_state
     */
    public function access_data_department_manager_stateAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log          = '任务名称: ' . $process_name . PHP_EOL;
        $log          .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        try {
            // 1. 获取取数工单系统各部门负责人
            $builder = $this->modelsManager->createBuilder();
            $builder->columns([
                'staff.staff_id',
                'staff.staff_name',
                'dept.name AS department_name',
            ]);
            $builder->from(['staff' => AccessDataStaffInfoModel::class]);
            $builder->leftjoin(AccessDataDepartmentModel::class, 'staff.department_id = dept.id', 'dept');
            $builder->andWhere('staff.duty_flag = :duty_flag:', ['duty_flag' => AccessDataEnums::STAFF_DUTY_MANAGER]);
            $builder->andWhere('staff.is_del = :staff_is_del:', ['staff_is_del' => GlobalEnums::IS_NO_DELETED]);
            $builder->andWhere('dept.is_del = :dept_is_del:', ['dept_is_del' => GlobalEnums::IS_NO_DELETED]);
            $all_department_managers = $builder->getQuery()->execute()->toArray();
            if (empty($all_department_managers)) {
                throw new BusinessException('取数工单系统的所有部门负责人为空, 请和数据团队确认', ErrCode::$BUSINESS_ERROR);
            }

            $all_department_manager_ids = array_column($all_department_managers, null, 'staff_id');

            // 2. 查看上述部门负责人的在职状态
            $staff_list = HrStaffInfoModel::find([
                'conditions' => 'staff_info_id IN ({staff_info_ids:array})',
                'bind'       => ['staff_info_ids' => array_keys($all_department_manager_ids)],
                'columns'    => ['staff_info_id', 'state'],
            ])->toArray();
            if (empty($staff_list)) {
                throw new BusinessException('取数工单系统的部门负责人在HRIS为空, 请和数据团队确认', ErrCode::$BUSINESS_ERROR);
            }
            $staff_list = array_column($staff_list, 'state', 'staff_info_id');

            // 异常的部门负责人信息
            $abnormal_department_managers = '';
            foreach ($all_department_manager_ids as $manager_info) {
                $staff_info_state = $staff_list[$manager_info['staff_id']] ?? 0;
                if ($staff_info_state != StaffInfoEnums::STAFF_STATE_IN) {
                    $abnormal_department_managers .= "{$manager_info['department_name']} — {$manager_info['staff_name']} ({$manager_info['staff_id']})\n";
                }
            }
            if (empty($abnormal_department_managers)) {
                throw new ValidationException('取数工单系统的部门负责人在职状态无异常', ErrCode::$VALIDATE_ERROR);
            }

            $log .= '取数工单系统的部门负责人非在职状态的名单: ' . PHP_EOL . $abnormal_department_managers;

            // 3. 取数部门负责人不在HRIS 或 非在职的 给 数据部门同事发邮件
            $data_department_staffs = AccessDataStaffInfoModel::find([
                'conditions' => 'department_id = :department_id: AND is_del = :is_del:',
                'bind'       => [
                    'department_id' => AccessDataEnums::DATA_DEPARTMENT_ID,
                    'is_del'        => GlobalEnums::IS_NO_DELETED,
                ],
                'columns'    => ['staff_id'],
            ])->toArray();
            if (empty($data_department_staffs)) {
                throw new BusinessException('取数工单系统的数据部门员工为空, 请和数据团队确认', ErrCode::$BUSINESS_ERROR);
            }

            $data_staff_emails = HrStaffInfoModel::find([
                'conditions' => 'staff_info_id IN ({staff_info_ids:array}) AND state = :state:',
                'bind'       => [
                    'staff_info_ids' => array_column($data_department_staffs, 'staff_id'),
                    'state'          => StaffInfoEnums::STAFF_STATE_IN,
                ],
                'columns'    => ['email'],
            ])->toArray();
            if (empty($data_staff_emails)) {
                throw new BusinessException('取数工单系统的数据部门员工在HRIS为空, 请和数据团队确认', ErrCode::$BUSINESS_ERROR);
            }

            // 可用的邮箱
            $is_send_email = false;
            $title         = '【告警】 OA - 取数工单系统 - 部门负责人在职状态异常';
            $abnormal_log  = str_replace("\n", '<br>', $abnormal_department_managers);
            $content       = <<<EOF
    hi,<br><br>
    OA 取数工单系统中的部门负责人在职状态异常, 请及时在「OA - 取数工单系统 - 权限配置」中重新设置这些部门的负责人, 否则将会影响审批流的正常流转.<br><br>
    非在职状态的部门负责人清单:<br>
    $abnormal_log
    <br><br>
EOF;
            foreach ($data_staff_emails as $staff) {
                if (filter_var($staff['email'], FILTER_VALIDATE_EMAIL)) {
                    $emails[] = $staff['email'];
                    if ($this->mailer->sendAsync($staff['email'], $title, $content)) {
                        $is_send_email = true;
                    }
                }
            }

            // 可用的告警邮箱为空, 走研发的群消息告警
            if (!$is_send_email) {
                throw new BusinessException('取数工单系统的数据部门员工邮箱地址异常, 邮件发送失败, 请和数据团队核实', ErrCode::$BUSINESS_ERROR);
            }
        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;
            $this->logger->info('应用监控 - 取数工单系统 - 部门负责人在职状态: ' . $log);
        } catch (BusinessException $e) {
            $log .= $e->getMessage() . PHP_EOL;
            $this->logger->warning('应用监控 - 取数工单系统 - 部门负责人在职状态: ' . $log);
        } catch (Exception $e) {
            $log .= $e->getMessage() . PHP_EOL;
            $this->logger->error('应用监控 - 取数工单系统 - 部门负责人在职状态: ' . $log);
        }

        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        exit($log);
    }

    /**
     * OA审批流系统: 固定工号审批人离职状态监控
     *
     * 说明: 固定工号审批人离职后, 推送关联的审批流到指定的飞书通知群
     *
     * @param array $args
     *
     * php app/cli.php monitor workflow_auditors_state
     */
    public function workflow_auditors_stateAction(array $args)
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log          = '任务名称: ' . $process_name . PHP_EOL;
        $log          .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        // 默认日志类型
        $logger_type = 'info';
        try {
            // 是否发消息, 若仅需控制台输出或打印日志, 则传该参即可
            $is_send_msg = empty($args[0]);

            // 1. 获取固定工号审批流的审批人
            $builder = $this->modelsManager->createBuilder();
            $builder->columns([
                'wf.id AS flow_id',
                'wf.name AS flow_name',
                'wf.biz_type',
                'node.auditor_id AS node_auditor_id',
                'sub_node.auditor_id AS sub_node_auditor_id',
            ]);
            $builder->from(['wf' => WorkflowModel::class]);
            $builder->leftjoin(WorkflowNodeModel::class, 'wf.id = node.flow_id', 'node');
            $builder->leftjoin(WorkflowSubNodeModel::class, 'node.id = sub_node.parent_node_id', 'sub_node');
            $builder->andWhere('wf.enable_status = :enable_status:',
                ['enable_status' => OAWorkflowEnums::WORKFLOW_ENABLE_STATUS_ING]);
            $builder->andWhere('node.auditor_type = :auditor_type: OR sub_node.auditor_type = :auditor_type:',
                ['auditor_type' => OAWorkflowEnums::AUDITOR_TYPE_STAFF_INFO_ID]);
            $workflow_node_list = $builder->getQuery()->execute()->toArray();
            if (empty($workflow_node_list)) {
                throw new BusinessException('固定工号配置的审批流数据为空, 请检查数据表配置', ErrCode::$BUSINESS_ERROR);
            }

            // 2. 提取审批节点的审批人
            $all_auditors = [];
            foreach ($workflow_node_list as $key => &$node) {
                $node_auditor_id     = $node['node_auditor_id'] ? array_filter(explode(',',
                    $node['node_auditor_id'])) : [];
                $sub_node_auditor_id = $node['sub_node_auditor_id'] ? array_filter(explode(',',
                    $node['sub_node_auditor_id'])) : [];
                if (empty($node_auditor_id) && empty($sub_node_auditor_id)) {
                    unset($workflow_node_list[$key]);
                    continue;
                }

                $node['node_all_auditors'] = $_node_all_auditors = array_unique(array_merge($node_auditor_id,
                    $sub_node_auditor_id));
                $all_auditors              = array_merge($all_auditors, $_node_all_auditors);
            }

            $all_auditors = array_values(array_unique($all_auditors));
            if (empty($all_auditors)) {
                throw new BusinessException('固定工号审批节点的审批人为空, 请检查节点表配置', ErrCode::$BUSINESS_ERROR);
            }

            // 3. 提取离职状态的审批人
            $leave_staff_list = HrStaffInfoModel::find([
                'conditions' => 'staff_info_id IN ({staff_info_ids:array})',
                'bind'       => ['staff_info_ids' => $all_auditors],
                'columns'    => ['staff_info_id', 'state'],
            ])->toArray();
            if (empty($leave_staff_list)) {
                throw new ValidationException('固定工号审批人均未离职, 请忽略', ErrCode::$VALIDATE_ERROR);
            }
            $leave_staff_list = array_column($leave_staff_list, 'state', 'staff_info_id');

            // 消息模板
            $msg_content_tpl = '<at user_id="all">所有人</at>' . PHP_EOL;
            $msg_content_tpl .= '场景: ' . self::FEISHU_OA_BIZ_WARNING_SCENE_WORKFLOW_ABNORMAL . PHP_EOL;
            $msg_content_tpl .= '国家: ' . get_country_code() . PHP_EOL;
            $msg_content_tpl .= '环境: ' . RUNTIME . PHP_EOL;
            $msg_content_tpl .= '审批流名称: WORKFLOW_NAME' . PHP_EOL;
            $msg_content_tpl .= '异常工号: NODE_AUDITOR_ID' . PHP_EOL;
            $msg_content_tpl .= '其他补充: OTHER_INFO' . PHP_EOL;

            $replace_search_key = ['WORKFLOW_NAME', 'NODE_AUDITOR_ID', 'OTHER_INFO'];
            foreach ($workflow_node_list as $node) {
                foreach ($node['node_all_auditors'] as $auditor_id) {
                    // 员工状态
                    $staff_state = $leave_staff_list[$auditor_id] ?? false;

                    // 飞书指定群告警通知
                    $_auditor_id_txt = '';
                    if ($staff_state == StaffInfoEnums::STAFF_STATE_LEAVE) {
                        // 离职
                        $_auditor_id_txt = $auditor_id . '（离职）';
                    } elseif ($staff_state === false) {
                        // 工号不存在 暂停通知
//                        $_auditor_id_txt = $auditor_id . '（不存在）';
                    }

                    if (!empty($_auditor_id_txt)) {
                        $replace_values = [
                            trim($node['flow_name']),
                            $_auditor_id_txt,
                            "biz_type-{$node['biz_type']}, flow_id-{$node['flow_id']}",
                        ];
                        $msg_content    = str_replace($replace_search_key, $replace_values, $msg_content_tpl);
                        if ($is_send_msg) {
                            send_feishu_text_msg($msg_content, self::FEISHU_OA_BIZ_WARNING_GROUP_MSG_KEY);
                        }

                        // 同步记录日志
                        $log .= $msg_content . '-------------' . PHP_EOL;
                    }
                }
            }
        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;
        } catch (BusinessException $e) {
            $log         .= $e->getMessage() . PHP_EOL;
            $logger_type = 'warning';
        } catch (Exception $e) {
            $log         .= $e->getMessage() . PHP_EOL;
            $logger_type = 'error';
        }

        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $this->logger->$logger_type('应用监控-OA审批流-固定工号审批流离职状态: ' . $log);
        exit($log);
    }


    /**
     * 数据权限-预算查询异动: 监控数据权限里的员工所属部门的变化
     * php app/cli.php monitor data_permission_department_change
     */
    public function data_permission_department_changeAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log          = '任务名称: ' . $process_name . PHP_EOL;
        $log          .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        $logger_type = 'info';
        DataPermissionModuleConfigService::setLanguage('zh-CN');
        try {
            $department_change = DataPermissionModuleConfigService::getInstance()->getDepartmentChangeData();
            if (empty($department_change)) {
                throw new ValidationException('员工所属部门数据未变化,请忽略', ErrCode::$VALIDATE_ERROR);
            }
            $msg_content_tpl    = '<at user_id="all">所有人</at>' . PHP_EOL;
            $msg_content_tpl    .= '场景: 数据配置-module_type模块异动 ' . PHP_EOL;
            $msg_content_tpl    .= '国家: ' . get_country_code() . PHP_EOL;
            $msg_content_tpl    .= '环境: ' . RUNTIME . PHP_EOL;
            $msg_content_tpl    .= '异常工号: staff_info_id' . PHP_EOL;
            $msg_content_tpl    .= '原先部门: old_staff_department_name' . PHP_EOL;
            $msg_content_tpl    .= '现在部门: new_staff_department_name' . PHP_EOL;
            $replace_search_key = [
                'module_type',
                'staff_info_id',
                'old_staff_department_name',
                'new_staff_department_name',
            ];
            foreach ($department_change as $department) {
                $replace_values = [
                    $department['module_type'],
                    $department['staff_info_id'],
                    $department['staff_department_name'],
                    $department['new_staff_department_name'],
                ];
                $msg_content    = str_replace($replace_search_key, $replace_values, $msg_content_tpl);
                $send_data      = send_feishu_text_msg($msg_content, self::FEISHU_OA_BIZ_WARNING_GROUP_MSG_KEY);
                // 同步记录日志
                $log .= $msg_content . ' 发送返回的结果是: ' . $send_data . PHP_EOL;
            }
        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;
        } catch (Exception $e) {
            $log         .= $e->getMessage() . PHP_EOL;
            $logger_type = 'error';
        }
        $this->logger->$logger_type('数据权限-预算查询异动: 监控数据权限里的员工所属部门的变化: ' . $log);
        exit($log);
    }

    /**
     * 监控离职资产总扣费金额错误
     * @param array $params 参数
     * 任务 php app/cli.php monitor monitor_leave_assets_all_deduct_amount
     * 参数 最大id
     */
    public function monitor_leave_assets_all_deduct_amountAction($params)
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log          = '任务名称: ' . $process_name . PHP_EOL;
        $log          .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        try {
            $last_id = 0;
            if (!empty($params[0])) {
                if (!is_numeric($params[0])) {
                    exit('修复离职资产总扣费金额-参数错误' . $params[0]);
                }
                $last_id = $params[0];
            }

            $result = [];
            do {
                $builder = $this->modelsManager->createBuilder();
                $builder->from(['main' => MaterialLeaveAssetsModel::class]);
                $builder->columns('main.id, main.staff_info_id, main.all_deduct_amount, sum(detail.deduct_amount) as detail_deduct_amount');
                $builder->leftjoin(MaterialLeaveAssetsDetailModel::class,
                    'detail.leave_assets_id = main.id and detail.is_deleted = 0', 'detail');
                $builder->andWhere('main.id > :last_id:', ['last_id' => $last_id]);
                $builder->andWhere('main.is_valid = :is_valid:',
                    ['is_valid' => MaterialEnums::LEAVE_ASSET_IS_VALID_YES]);
                $builder->groupBy('main.id');
                $builder->orderBy('main.id asc');
                $builder->limit(1000);
                $items = $builder->getQuery()->execute()->toArray();
                foreach ($items as $v) {
                    $current_detail_deduct_amount = !empty($v['detail_deduct_amount']) ? $v['detail_deduct_amount'] : '0.00';
                    if (bccomp($v['all_deduct_amount'], $current_detail_deduct_amount, 2) !== 0) {
                        $result[] = [
                            'before'       => $v,
                            'after_amount' => $current_detail_deduct_amount,
                        ];
                    }
                    $last_id = $v['id'];
                }
                sleep(10);
            } while (!empty($items));
            if (!empty($result)) {
                $this->logger->warning('monitor_leave_assets_all_deduct_amount error : 资产总扣费金额异常,请检查, data=' . json_encode($result,
                        JSON_UNESCAPED_UNICODE));
            }
            $log .= '执行完成, 共找到异常' . count($result) . '条';
        } catch (Exception $e) {
            $log .= $e->getMessage() . PHP_EOL;
            $log .= '资产总扣费金额监控失败, 参数' . $params[0] . PHP_EOL;
            $this->logger->error("资产总扣费金额监控失败 " . $log);
        }
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        exit($log);
    }

    /**
     * OA系统待审批人红点监控
     * 说明: 对比待审批人OA菜单红点是否异常: 红点取数是否受菜单权限限制-即有待办审批通知,却无OA菜单权限, 看不到菜单红点, 影响审批效率
     *
     * php app/cli.php monitor red_dot 1 1111,2222
     *
     * @param array $args 参数1: 是否发飞书消息 1-不发, 0-发; 参数2: 自定义待办人的工号, 英文逗号间隔
     */
    public function red_dotAction(array $args = [])
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log          = '任务名称: ' . $process_name . PHP_EOL;
        $log          .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        // 默认日志类型
        $logger_type = 'info';
        try {
            // 是否发飞书消息, 默认发, 为空 也发; 不空, 不发(仅控制台输出)
            $is_send_msg = empty($args[0]);

            // 待办人取系统的 还是 自定义的
            if (empty($args[1])) {
                // 1. 获取OA审批流待审批人
                $oa_penging_auditors = WorkflowRequestModel::find([
                    'conditions' => 'state = :state: AND is_abandon = :is_abandon:',
                    'bind'       => [
                        'state'      => Enums::WF_STATE_PENDING,
                        'is_abandon' => GlobalEnums::WORKFLOW_ABANDON_STATE_NO,
                    ],
                    'columns'    => ['current_node_auditor_id'],
                ])->toArray();
                $oa_penging_auditors = implode(',', array_column($oa_penging_auditors, 'current_node_auditor_id'));
                $oa_penging_auditors = array_values(array_filter(array_unique(explode(',', $oa_penging_auditors))));
                $log                 .= 'OA审批流待办人: 共 ' . count($oa_penging_auditors) . ' 个; 具体=' . json_encode($oa_penging_auditors,
                        JSON_UNESCAPED_UNICODE) . PHP_EOL;

                // 2. 获取BY审批流中有在OA待办业务的待审批人
                $by_penging_auditors = AuditApprovalModel::find([
                    'conditions' => 'state = :state: AND deleted = :deleted: AND biz_type IN ({biz_type:array}) ',
                    'bind'       => [
                        'state'    => Enums::WF_STATE_PENDING,
                        'deleted'  => GlobalEnums::IS_NO_DELETED,
                        'biz_type' => WorkflowPendingEnums::$by_pending_audit_type_list,
                    ],
                    'columns'    => ['approval_id'],
                ])->toArray();
                $by_penging_auditors = array_values(array_unique(array_column($by_penging_auditors, 'approval_id')));
                $log                 .= 'BY审批流待办人: 共 ' . count($by_penging_auditors) . ' 个; 具体=' . json_encode($by_penging_auditors,
                        JSON_UNESCAPED_UNICODE) . PHP_EOL;

                // 所有待办人
                $all_pending_auditors = array_merge($oa_penging_auditors, $by_penging_auditors);
            } else {
                // 自定义待办人
                $all_pending_auditors = explode(',', $args[1]);
            }

            // 要确认的待办人
            $all_pending_auditors = array_values(array_filter(array_unique($all_pending_auditors)));
            $log                  .= '所有审批流待办人(OA+BY): 共 ' . count($all_pending_auditors) . ' 个; 具体=' . json_encode($all_pending_auditors,
                    JSON_UNESCAPED_UNICODE) . PHP_EOL;

            if (empty($all_pending_auditors)) {
                throw new ValidationException('无待办人, 本轮监控无需预警', ErrCode::$VALIDATE_ERROR);
            }

            // 设置系统语言
            self::setLanguage('zh-CN');

            // 确认在职情况, 离职/停职的不提醒
            $staff_list = (new HrStaffRepository())->getStaffListByStaffIds($all_pending_auditors);
            $staff_list = array_column($staff_list, 'state', 'staff_info_id');

            // 消息模板
            $msg_content_tpl    = '<at user_id="all">所有人</at>' . PHP_EOL;
            $msg_content_tpl    .= '场景: ' . self::FEISHU_OA_BIZ_WARNING_SCENE_RED_DOT_ABNORMAL . PHP_EOL;
            $msg_content_tpl    .= '国家: ' . get_country_code() . PHP_EOL;
            $msg_content_tpl    .= '环境: ' . RUNTIME . PHP_EOL;
            $msg_content_tpl    .= '员工: STAFF_ID' . PHP_EOL;
            $msg_content_tpl    .= '红点K: MENU_RED_DOT_KEY' . PHP_EOL;
            $msg_content_tpl    .= '说明: 请根据红点key确认对应的菜单 并 核实菜单权限' . PHP_EOL;
            $replace_search_key = ['STAFF_ID', 'MENU_RED_DOT_KEY'];

            // 哪些红点key不需要提醒
            $no_need_remind_red_hot_keys = EnumsService::getInstance()->getSettingEnvValueIds('monitor_no_need_remind_red_hot_keys');
            $log                         .= '无需监控的红点Key: 共 ' . count($no_need_remind_red_hot_keys) . ' 个; 具体 = ' . json_encode($no_need_remind_red_hot_keys,
                    JSON_UNESCAPED_UNICODE) . PHP_EOL;

            // 3. 获取待办人待办数
            $user_service      = new UserService();
            $abnormal_auditors = [];
            foreach ($all_pending_auditors as $index => $auditor) {
                $_start_at = get_curr_micro_time();
                // 3.1 获取每个待办人OA现行的红点取数结果(受菜单权限限制的)
                $user_info                      = ['id' => $auditor];
                $limited_menu_red_hot_item      = $user_service->getStaffWfInfo($user_info, false);
                $limited_menu_red_hot_sum_total = array_sum($limited_menu_red_hot_item);

                // 3.2 获取每个待办人OA所有菜单的红点取数结果(不受菜单权限限制的)
                $all_menu_red_hot_item      = $user_service->getStaffWfInfo($user_info, true);
                $all_menu_red_hot_sum_total = array_sum($all_menu_red_hot_item);

                // 待办人在职状态
                $auditor_state       = $staff_list[$auditor] ?? null;
                $auditor_state_label = $auditor_state ? static::$t[StaffInfoEnums::$staff_state[$auditor_state]] : '不在HR员工表';

                // 4. 对比上述两个取数结果
                // 4.1 总数是否一致
                if ($limited_menu_red_hot_sum_total == $all_menu_red_hot_sum_total) {
                    $_log = "{$auditor}-{$auditor_state_label} 红点数一致, 无需预警: 总数合计 {$limited_menu_red_hot_sum_total}, 对比花费时长: " . get_exec_time($_start_at);
                    echo $_log . PHP_EOL;
                    $this->logger->info('OA菜单红点数监控: ' . $_log);
                    continue;
                }

                // 4.2 总数不一致, 找出不一致的红点key
                $diff_red_hot_item = array_diff_assoc($all_menu_red_hot_item, $limited_menu_red_hot_item);

                // 找出具体的不一致, 前者红点0, 后者红点有数的, 认为 是菜单缺失导致, 做提醒; 前/后者 都有大于0的红点数, 认为菜单有数, 仅记日志, 不做提醒
                $_log = "{$auditor}-{$auditor_state_label} 红点数不一致, 请核实, 有差异的红点key: 具体=" . PHP_EOL;

                // 真正要提醒的红点key
                $remind_red_hot_keys = [];
                foreach ($diff_red_hot_item as $diff_k => $diff_v) {
                    $limited_menu_red_hot_count = $limited_menu_red_hot_item[$diff_k] ?? 0;
                    $all_menu_red_hot_count     = $all_menu_red_hot_item[$diff_k] ?? 0;

                    $_log .= "{$diff_k}: all_menu_red_hot_count-{$all_menu_red_hot_count}, limited_menu_red_hot_count-{$limited_menu_red_hot_count}; ";

                    // 缺菜单 且 不在 无需提醒的配置中
                    if ($all_menu_red_hot_count > 0 && $limited_menu_red_hot_count == 0 && !in_array($diff_k,
                            $no_need_remind_red_hot_keys)) {
                        $remind_red_hot_keys[] = $diff_k;
                    }
                }

                $_log .= '对比花费时长: ' . get_exec_time($_start_at);
                echo $_log . PHP_EOL;
                $this->logger->info('OA菜单红点数监控: ' . $_log);

                $_remind_log = "{$auditor}-{$auditor_state_label}: 需提醒的红点 = " . json_encode($remind_red_hot_keys,
                        JSON_UNESCAPED_UNICODE);
                echo $_remind_log . PHP_EOL;
                $this->logger->info('OA菜单红点数监控: ' . $_remind_log);

                // 没有要提醒的红点, 下一位
                if (empty($remind_red_hot_keys)) {
                    continue;
                }

                // 4.3 收集不一致的红点key
                $diff_red_hot_keys = implode(' | ', $remind_red_hot_keys);

                // 5. 发送飞书提醒(仅在职的 和 不在HR员工表的)
                if ($auditor_state == StaffInfoEnums::STAFF_STATE_IN || $auditor_state === null) {
                    // 异常的待办(在职人)
                    $abnormal_auditors[] = [
                        $auditor . "-$auditor_state_label" => $diff_red_hot_keys,
                    ];

                    $replace_values = [$auditor . " ($auditor_state_label) ", $diff_red_hot_keys];
                    $msg_content    = str_replace($replace_search_key, $replace_values, $msg_content_tpl);
                    if ($is_send_msg) {
                        send_feishu_text_msg($msg_content, self::FEISHU_OA_BIZ_WARNING_GROUP_MSG_KEY);
                    }
                }

                // 每处理10个 歇5s
                if ($index % 10 == 0) {
                    sleep(5);
                }
            }

            $log .= '异常待办人: 共 ' . count($abnormal_auditors) . ' 个; 具体 = ' . json_encode($abnormal_auditors,
                    JSON_UNESCAPED_UNICODE) . PHP_EOL;
        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;
        } catch (BusinessException $e) {
            $log         .= $e->getMessage() . PHP_EOL;
            $logger_type = 'warning';
        } catch (Exception $e) {
            $log         .= $e->getMessage() . PHP_EOL;
            $logger_type = 'error';
        }

        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $this->logger->$logger_type('应用监控-OA菜单红点数量: ' . $log);
        exit($log);
    }

    /**
     * sap 抛数异常监控 (sap无响应, uuid空)
     * php app/cli.php monitor sap_data_abnormal
     */
    public function sap_data_abnormalAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log          = '任务名称: ' . $process_name . PHP_EOL;
        $log          .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        $logger_type = 'info';
        try {
            // 获取sap抛数单据, sap无响应的日志 (昨天 和 今天的)
            $start_time = date('Y-m-d 00:00:00', strtotime('-1 day'));
            $sap_log    = RequestSapLog::find([
                'conditions' => "create_at >= :create_at: AND uuid = :uuid: AND response_data = :response_data:",
                'bind'       => [
                    'create_at'     => $start_time,
                    'uuid'          => '',
                    'response_data' => '',
                ],
                'order'      => 'id DESC',
            ])->toArray();

            $current_time = date('Y-m-d H:i:s');
            if (empty($sap_log)) {
                throw new ValidationException("SAP抛数 无异常[时间范围{$start_time} - {$current_time}], 请忽略",
                    ErrCode::$VALIDATE_ERROR);
            }

            // 再次查下是否有被重试抛数成功的, 需要过滤掉
            $sap_log_order_code_list  = array_unique(array_column($sap_log, 'order_code'));
            $retry_success_order_list = [];
            if (!empty($sap_log_order_code_list)) {
                $retry_success_order_list = RequestSapLog::find([
                    'conditions' => "order_code IN ({order_code:array}) AND uuid != :uuid: AND response_data != :response_data:",
                    'bind'       => [
                        'order_code'    => array_values($sap_log_order_code_list),
                        'uuid'          => '',
                        'response_data' => '',
                    ],
                    'columns'    => ['order_code'],
                ])->toArray();
                $retry_success_order_list = array_column($retry_success_order_list, 'order_code');
            }

            $msg_content_tpl    = '<at user_id="all">所有人</at>' . PHP_EOL;
            $msg_content_tpl    .= '场景: ' . self::FEISHU_OA_BIZ_WARNING_SCENE_SAP_REQUEST_ABNORMAL . PHP_EOL;
            $msg_content_tpl    .= '国家: ' . get_country_code() . PHP_EOL;
            $msg_content_tpl    .= '环境: ' . RUNTIME . PHP_EOL;
            $msg_content_tpl    .= '异常单号: sap_abnormal_order' . PHP_EOL;
            $msg_content_tpl    .= '日志文件: sap_log_file' . PHP_EOL;
            $msg_content_tpl    .= '取数范围: ' . $start_time . ' 至 ' . $current_time . PHP_EOL;
            $msg_content_tpl    .= '备注: 若单据侧SAP抛数状态已被人工调整，请忽略' . PHP_EOL;
            $replace_search_key = ['sap_abnormal_order', 'sap_log_file'];

            $file_data       = [];
            $order_code_list = [];
            foreach ($sap_log as $value) {
                // 重试成功, 不再提醒
                if (in_array($value['order_code'], $retry_success_order_list)) {
                    continue;
                }

                $order_code_list[] = $value['order_code'];
                $file_data[]       = [
                    'id'            => $value['id'],
                    'order_code'    => $value['order_code'],
                    'create_at'     => $value['create_at'],
                    'uuid'          => $value['uuid'],
                    'response_data' => $value['response_data'],
                    'request_data'  => $value['request_data'],
                ];
            }

            if (empty($order_code_list)) {
                throw new ValidationException("SAP抛数 无异常(被重试)[时间范围{$start_time} - {$current_time}], 请忽略",
                    ErrCode::$VALIDATE_ERROR);
            }

            $sap_abnormal_order = implode(',', $order_code_list);
            $log                .= '异常单号: ' . $sap_abnormal_order . PHP_EOL;

            // 生成日志文件
            $file_path = sys_get_temp_dir() . '/SapRequestLog_' . date('YmdHis') . '.txt';
            if (!file_put_contents($file_path, var_export($file_data, true))) {
                throw new BusinessException('SAP log 监控文件生成失败, 请检查=' . $file_path, ErrCode::$BUSINESS_ERROR);
            }

            $upload_res = OssHelper::uploadFile($file_path);
            if (empty($upload_res['object_url'])) {
                throw new BusinessException('SAP log 监控文件生成失败, 请检查', ErrCode::$BUSINESS_ERROR);
            }

            $log .= '文件地址: ' . $upload_res['object_url'] . PHP_EOL;

            // 生成短连接
            $short_url = ShortUrlService::getInstance()->getShortUrl($upload_res['object_url']);
            if (empty($short_url)) {
                throw new BusinessException('SAP log 监控文件短链生成失败, 请检查', ErrCode::$BUSINESS_ERROR);
            }

            $log .= '短链接: ' . $short_url . PHP_EOL;

            $replace_values = [
                'sap_abnormal_order' => $sap_abnormal_order,
                'sap_log_file'       => $short_url,
            ];
            $msg_content    = str_replace($replace_search_key, $replace_values, $msg_content_tpl);
            send_feishu_text_msg($msg_content, self::FEISHU_OA_BIZ_WARNING_GROUP_MSG_KEY);
        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;
        } catch (BusinessException $e) {
            $log         .= $e->getMessage() . PHP_EOL;
            $logger_type = 'notice';
        } catch (Exception $e) {
            $log         .= $e->getMessage() . PHP_EOL;
            $logger_type = 'error';
        }

        $this->logger->$logger_type('SAP数据同步-SAP异常监控: ' . $log);
        exit($log);
    }


    /**
     * OA在线支付: 预警重复支付
     *
     * 说明: 检测昨日付流水，其中费用类型为空的, 推送异常信息到指定的飞书通知群
     *
     * @param array $args
     *
     * php app/cli.php monitor flash_pay_bank_flow
     */
    public function flash_pay_bank_flowAction(array $args)
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log          = '任务名称: ' . $process_name . PHP_EOL;
        $log          .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        // 默认日志类型
        $logger_type = 'info';
        try {
            // 是否发消息, 若仅需控制台输出或打印日志, 则传该参即可
            $is_send_msg = empty($args[0]);

            // 1. 获取flash_pay相关的银行账号
            $bank_account_list = BankAccountModel::find([
                'columns'    => 'id',
                'conditions' => 'bank_id = :bank_id: and is_deleted = :is_deleted:',
                'bind'       => ['bank_id' => 14, 'is_deleted' => GlobalEnums::IS_NO_DELETED],
            ])->toArray();
            if (empty($bank_account_list)) {
                throw new ValidationException('未获取到Flash Pay相关的银行账号信息', ErrCode::$VALIDATE_ERROR);
            }

            // 2. 获取Flash Pay相关的银行账号下昨日付款流水且费用类型为空的数据
            $yesterday          = date('Y-m-d', strtotime('-1 day'));
            $repeated_pay_count = BankFlowModel::count([
                'conditions' => 'type = :type: and bank_flow_expense_id = :bank_flow_expense_id: and bank_account_id in({bank_account_ids:array}) and date = :date:',
                'bind'       => [
                    'type'                 => BankFlowEnums::BANK_FLOW_TYPE_PAY_OUTLAY,
                    'bank_flow_expense_id' => 0,
                    'bank_account_ids'     => array_column($bank_account_list, 'id'),
                    'date'                 => $yesterday,
                ],
            ]);

            // 3. 存在昨日付流水，其中费用类型为空的数据
            if ($repeated_pay_count > 0) {
                // 消息模板
                $msg_content = '<at user_id="all">所有人</at>' . PHP_EOL;
                $msg_content .= '场景: ' . self::FEISHU_OA_BIZ_WARNING_SCENE_FLASH_PAY_BANK_FLOW_ABNORMAL . PHP_EOL;
                $msg_content .= '国家: ' . get_country_code() . PHP_EOL;
                $msg_content .= '环境: ' . RUNTIME . PHP_EOL;
                $msg_content .= '取数范围: ' . $yesterday . PHP_EOL;
                if ($is_send_msg) {
                    send_feishu_text_msg($msg_content, self::FEISHU_OA_BIZ_WARNING_GROUP_MSG_KEY);
                }

                // 同步记录日志
                $log .= $msg_content . '-------------' . PHP_EOL;
            } else {
                $log .= '无异常数据产生，无需关注' . PHP_EOL;
            }
        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;
        } catch (Exception $e) {
            $log         .= $e->getMessage() . PHP_EOL;
            $logger_type = 'error';
        }

        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $this->logger->$logger_type('应用监控-OA在线支付-昨日银行付款流水费用类型为空异常提醒: ' . $log);
        exit($log);
    }

    /**
     * import_task 异步导入任务监控
     *
     * 说明: 检测异步导入任务近一周内处理中任务超过距离脚本执行时间-设定分钟数之前未执行完毕的任务需要告警
     *
     * @param array $argv 设定的分钟数（默认30分钟）
     *
     * php app/cli.php monitor import_task_monitor
     */
    public function import_task_monitorAction($argv)
    {
        //查询需要报警的task
        $log        = 'begin' . date('Y-m-d H:i:s') . PHP_EOL;
        $minutes    = $argv[0] ?? 30;
        $start_date = date('Y-m-d H:i:s', strtotime('-1 week'));
        $end_date   = date('Y-m-d H:i:s', strtotime('-' . $minutes . 'minutes'));
        $list       = ImportTaskModel::find([
            'conditions' => 'created_at < :end_date: and created_at > :start_date: and status = :status: and is_deleted = :is_deleted:',
            'bind'       => [
                'start_date' => $start_date,
                'end_date'   => $end_date,
                'status'     => ImportCenterEnums::STATUS_DOING,
                'is_deleted' => GlobalEnums::IS_NO_DELETED,
            ],
        ])->toArray();
        if (empty($list)) {
            exit('没有需要预警的导入任务' . PHP_EOL);
        }
        $notice_content['action'] = 'import_task';
        foreach ($list as $k => $v) {
            $notice_content['task_list'][] = [
                'id'            => $v['id'],
                'staff_info_id' => $v['staff_info_id'],
                'task_remark'   => $v['task_remark'],
                'type'          => $v['type'],
            ];
        }
        $notice_content['reason'] = '超' . $minutes . '分钟未完成任务';
        $notice_content           = json_encode($notice_content, JSON_UNESCAPED_UNICODE);
        $log                      .= $notice_content . PHP_EOL;
        $this->logger->warning($notice_content);
        $log .= 'end' . date('Y-m-d H:i:s') . PHP_EOL;
        exit($log);
    }

    /**
     * 代理支付: 预警重复支付
     *
     * 说明: 监控外部系统单号在待支付、已支付状态下若一个单号存在多条则告警, 推送异常信息到指定的飞书通知群
     *
     * @param array $args
     *
     * php app/cli.php monitor agency_payment_detail
     */
    public function agency_payment_detailAction(array $args)
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log          = '任务名称: ' . $process_name . PHP_EOL;
        $log          .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        // 默认日志类型
        $logger_type = 'info';
        try {
            // 是否发消息, 若仅需控制台输出或打印日志, 则传该参即可
            $is_send_msg = empty($args[0]);

            // 1. 获取代理支付明细行、待支付、 已支付每个外部单号的总数
            $start_date = date('Y-m-d', strtotime('-30 day'));
            $pay_list   = AgencyPaymentDetailModel::find([
                'columns'    => 'out_no',
                'conditions' => 'out_no != :out_no: and pay_status in ({pay_status:array}) and created_at >= :created_at:',
                'bind'       => [
                    'out_no'     => '',
                    'pay_status' => [
                        AgencyPaymentEnums::AGENCY_PAYMENT_PAY_STATUS_PENDING,
                        AgencyPaymentEnums::AGENCY_PAYMENT_PAY_STATUS_PAY,
                    ],
                    'created_at' => $start_date
                ]
            ])->toArray();

            // 2. 统计每个外部单号出现的次数
            $repeated_pay_out_no = [];
            if ($pay_list) {
                $out_no_numbers = array_count_values(array_column($pay_list, 'out_no'));
                foreach ($out_no_numbers as $out_no => $number) {
                    if ($number >= 2) {
                        $repeated_pay_out_no[] = $out_no;
                    }
                }
            }

            // 3. 存在超过2条以上的外部单好则报警
            if ($repeated_pay_out_no) {
                // 消息模板
                $msg_content = '<at user_id="all">所有人</at>' . PHP_EOL;
                $msg_content .= '场景: ' . self::FEISHU_OA_BIZ_WARNING_SCENE_AGENCY_PAYMENT_DETAIL_ABNORMAL . PHP_EOL;
                $msg_content .= '国家: ' . get_country_code() . PHP_EOL;
                $msg_content .= '环境: ' . RUNTIME . PHP_EOL;
                $msg_content .= '取数范围: 从' . $start_date  . '至今' . PHP_EOL;
                $msg_content .= '异常单号: ' . implode(',', $repeated_pay_out_no)  . PHP_EOL;
                if ($is_send_msg) {
                    send_feishu_text_msg($msg_content, self::FEISHU_OA_BIZ_WARNING_GROUP_MSG_KEY);
                }

                // 同步记录日志
                $log .= $msg_content . '-------------' . PHP_EOL;
            } else {
                $log .= '无异常数据产生，无需关注' . PHP_EOL;
            }
        } catch (Exception $e) {
            $log         .= $e->getMessage() . PHP_EOL;
            $logger_type = 'error';
        }

        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $this->logger->$logger_type('应用监控-代理支付-外部系统单号在待支付、已支付状态下若一个单号存在多条则告警，异常提醒: ' . $log);
        exit($log);
    }
}
