<?php
/**
 * 数据报表导出
 *
 * 1.导出指定业务模块/指定范围/指定列的数据
 * 2.发送指定邮件组
 */

use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Library\Enums\DownloadCenterEnum;
use App\Models\backyard\JobTransferModel;
use App\Library\BaseService;
use App\Models\backyard\SettingEnvModel;
use App\Modules\Common\Services\DownloadCenterService;
use App\Modules\Contract\Services\ListService as ContractListService;
use App\Modules\JobTransfer\Services\ListService;
use App\Modules\Loan\Services\ListService AS LoanListService;
use App\Modules\Purchase\Services\PaymentService;
use App\Modules\ReserveFund\Services\ApplyService AS ReserveFundApplyService;
use App\Modules\Organization\Services\JobService;
use App\Modules\Contract\Services\ContractStoreRentingService;
use App\Modules\Reimbursement\Services\ListService AS ReimbursementListService;
use App\Modules\OrdinaryPayment\Services\OrdinaryPaymentListService;
use App\Modules\User\Services\UserService;
use GuzzleHttp\Exception\GuzzleException;

class ExportTask extends BaseTask
{

    /**
     * 导出普通付款含附件
     * @param $params
     *
     * 1. 原始参数示例: 具体支持的参数, 请参考数据查询接口, 可适当扩展 并 丰富 getCondition 查询条件组合
     * $params =
     *   [
     *       'budget_ids' => [1,2],// 预算科目ID 示例 [1,2,3]
     *       'apply_start_date' => '',// 开始申请日期 示例 2022-01-01
     *       'approval_status' => '',// 审批状态 示例 1
     *       'pay_status' => '',// 支付状态 示例2
     *       'lang' => 'zh-CN', // 语言, 默认zh-CN
     *    ];
     * 2. 对原始参数进行加签处理: base64_encode(json_encode($params))
     *
     * 3. 接收参数, 对参数解签: 参数为空, 即 取所有
     * php app/cli.php export ordinary_payment eyJidWRnZXRfaWRzIjpbMSwyXSwiYXBwbHlfc3RhcnRfZGF0ZSI6IiIsImFwcHJvdmFsX3N0YXR1cyI6IiIsInBheV9zdGF0dXMiOiIiLCJsYW5nIjoiemgtQ04ifQ==
     *
     */
    public function ordinary_paymentAction($params)
    {
        try {
            $condition = !empty($params[0]) ? json_decode(base64_decode($params[0]), true) : [];
            $condition['lang'] = $condition['lang'] ?? 'zh-CN';
            $res = OrdinaryPaymentListService::getInstance()->dataExportAllByTask($condition);
        } catch (Exception $e) {
            $res = [
            'code' => 0,
            'message' => $e->getMessage(),
            'data' => [],
            ];
        }

        echo PHP_EOL;
        var_dump($condition, $res);
        echo PHP_EOL;
        exit;
    }

    /**
     * 导出采购付款的
     *
     * @param $params
     *
     * 1. 原始参数示例: 具体支持的参数, 请参考数据查询接口, 可适当扩展 并 丰富 getCondition 查询条件组合
     * $params =
     *   [
     *       'budget_ids' => [1,2],// 预算科目ID 示例 [1,2,3]
     *       'created_at_start' => '',// 开始申请日期 示例 2022-01-01
     *       'status' => '',// 审批状态 示例 1
     *       'pay_status' => '',// 支付状态 示例2
     *       'lang' => 'zh-CN', // 语言, 默认zh-CN
     *    ];
     * 2. 对原始参数进行加签处理: base64_encode(json_encode($params))
     *
     * 3. 接收参数, 对参数解签: 参数为空, 即 取所有
     * php app/cli.php export purchase_payment eyJidWRnZXRfaWRzIjpbMSwyXSwiY3JlYXRlZF9hdF9zdGFydCI6IiIsInN0YXR1cyI6IiIsInBheV9zdGF0dXMiOiIiLCJsYW5nIjoiemgtQ04ifQ==
     *
     */
    public function purchase_paymentAction($params)
    {
        try {
            $condition = !empty($params[0]) ? json_decode(base64_decode($params[0]), true) : [];
            $condition['lang'] = $condition['lang'] ?? 'zh-CN';

            // 设置系统语言
            PaymentService::setLanguage($condition['lang']);
            $res = PaymentService::getInstance()->export($condition, [], PaymentService::LIST_TYPE_DATA);

        } catch (Exception $e) {
            $res = [
                'code' => 0,
                'message' => $e->getMessage(),
                'data' => [],
            ];
        }

        echo PHP_EOL;
        var_dump($condition, $res);
        echo PHP_EOL;
        exit;
    }

    /**
     * 导出其他合同含附件及审批日志
     *
     * php app/cli.php export other_contract 999/545 21,22
     *
     * @param $params
     * @throws GuzzleException
     */
    public function other_contractAction($params)
    {
        $condition = [
            'ancestry' => $params[0] ?? '',// 从根节点开始 且 含当前节点, 示例: 999/444/210
            'template_id_item' => $params[1] ? explode(',', $params[1]) : [],
            'lang' => $params[2] ?? 'zh-CN',
        ];

        try {
            $res = ContractListService::getInstance()->getListByTask($condition);
        } catch (BusinessException $e) {
            $res = [
                'code' => 0,
                'message' => $e->getMessage(),
                'data' => [],
            ];
        }

        echo PHP_EOL;
        var_dump($condition);
        var_dump($res);
        echo PHP_EOL;
        exit;
    }

    /**
     * 导出租房合同含附件及审批日志
     * @param $params
     * php app/cli.php export rent_contract
     */
    public function rent_contractAction($params)
    {
        $condition = [
            'ancestry' => $params[0] ?? '',// 从根节点开始 且 含当前节点, 示例: 999/444/210
            'lang' => $params[1] ?? 'zh-CN',
        ];

        try {
            $res = ContractStoreRentingService::getInstance()->getAllListByTask($condition);
        } catch (Exception $e) {
            $res = [
                'code' => 0,
                'message' => $e->getMessage(),
                'data' => [],
            ];
        }

        echo PHP_EOL;
        var_dump($condition);
        var_dump($res);
        echo PHP_EOL;
        exit;
    }

    /**
     * 导出报销含附件及审批日志
     *
     * @param $params
     *
     * 1. 原始参数示例: 具体支持的参数, 请参考数据查询接口, 可适当扩展 并 丰富 getCondition 查询条件组合
     * $params =
     *   [
     *       'created_department_ids' => [1,2],// 部门ID列表 示例 [1,2,3]
     *       'budget_id' => [1,2],// 预算科目ID列表
     *       'created_at' => '',// 创建时间 2022-01-01 00:00:00
     *       'status' => '',// 审批状态 示例 1
     *       'pay_status' => '',// 支付状态 示例2
     *       'lang' => 'zh-CN', // 语言, 默认zh-CN
     *       'pageNum' => 1, // 初始页码, 默认 1
     *       'pageSize' => 10000, // 每页条数, 默认 10000
     *    ];
     * 2. 对原始参数进行加签处理: base64_encode(json_encode($params))
     *
     * 3. 接收参数, 对参数解签: 参数为空, 即 取所有
     * php app/cli.php export reimbursement eyJjcmVhdGVkX2RlcGFydG1lbnRfaWRzIjpbMSwyXSwiYnVkZ2V0X2lkIjpbMSwyXSwiY3JlYXRlZF9hdCI6IiIsInN0YXR1cyI6IiIsInBheV9zdGF0dXMiOiIiLCJsYW5nIjoiemgtQ04iLCJwYWdlTnVtIjoxLCJwYWdlU2l6ZSI6MTAwMDB9
     * @throws GuzzleException
     */
    public function reimbursementAction($params)
    {
        try {
            $condition = !empty($params[0]) ? json_decode(base64_decode($params[0]), true) : [];
            $condition['lang'] = $condition['lang'] ?? 'zh-CN';
            $condition['pageNum'] = $condition['pageNum'] ?? 1;
            $condition['pageSize'] = $condition['pageSize'] ?? 10000;

            $res = ReimbursementListService::getInstance()->exportByTask($condition, [], true);

        } catch (Exception $e) {
            $res = [
                'code' => 0,
                'message' => $e->getMessage(),
                'data' => [],
            ];
        }

        echo PHP_EOL;
        var_dump($condition, $res);
        echo PHP_EOL;
        exit;
    }

    /**
     * 借款和备用金的某时刻的快照数据导出(同功能菜单:数据查询-导出Excel)
     * 每月1号0:0:1执行 [1 0 0 1 * ?]
     * cli: php app/cli.php export loan_and_reserve_snapshot 2023-01-01 zh
     *
     * @param array $args 外部参数
     */
    public function loan_and_reserve_snapshotAction(array $args = [])
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log = '任务名称: ' . $process_name . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s')  . PHP_EOL;
        $log .= '初始内存: ' . memory_usage() . PHP_EOL;
        $is_exception = false;

        try {
            // 1. 数据截止日期 外部不传则默认为 上月最后一天的的23:59:59
            if (!empty($args[0]) && !preg_match('/^[1-9]{1}[0-9]{0,3}-[0-9]{1,2}-[0-9]{1,2}$/', $args[0])) {
                throw new ValidationException('年月日参数错误(YYYY-MM-DD) ' . $args[0], ErrCode::$VALIDATE_ERROR);
            }

            $end_time = $args[0] ? $args[0] . ' 23:59:59' : date('Y-m-d 23:59:59', strtotime('last day of -1 month'));
            $language = $args[1] ?? DownloadCenterEnum::DETAUL_LANGUAGE;

            $log .= '数据截止时间: ' . $end_time . PHP_EOL;
            $log .= '数据导出语言: ' . $language . PHP_EOL;

            // 2.获取各业务的数据->Excel
            BaseService::setLanguage($language);
            $country_code = get_country_code();
            $data_last_year_month = substr($end_time, 0, 7);

            // 2.1 备用金
            $reserve_fund_params = [
                'create_end_time' => $end_time,
                'export_file_name' => "$country_code-$data_last_year_month-Petty Cash data.xlsx",
            ];
            $log .= '任务参数(备用金): ' . json_encode($reserve_fund_params, JSON_UNESCAPED_UNICODE) . PHP_EOL;
            $reserve_fund_export_res = ReserveFundApplyService::getInstance()->export($reserve_fund_params, []);
            $log .= 'Excel结果(备用金): ' . json_encode($reserve_fund_export_res, JSON_UNESCAPED_UNICODE) . PHP_EOL;
            $log .= '当前内存: ' . memory_usage() . PHP_EOL;

            $reserve_fund_export_file_url = '';
            if ($reserve_fund_export_res['code'] == ErrCode::$SUCCESS && !empty($reserve_fund_export_res['data'])) {
                $reserve_fund_export_file_url = $reserve_fund_export_res['data'];
            }

            // 2.2 借款
            $loan_params = [
                'create_end_time' => $end_time,
                'export_file_name' => "$country_code-$data_last_year_month-loan data.xlsx",
            ];
            $log .= '任务参数(借款): ' . json_encode($loan_params, JSON_UNESCAPED_UNICODE) . PHP_EOL;
            $loan_export_res = LoanListService::getInstance()->export($loan_params, [], LoanListService::LIST_TYPE_EXPORT);
            $log .= 'Excel结果(借款): ' . json_encode($loan_export_res, JSON_UNESCAPED_UNICODE) . PHP_EOL;
            $log .= '当前内存: ' . memory_usage() . PHP_EOL;

            $loan_export_file_url = '';
            if ($loan_export_res['code'] == ErrCode::$SUCCESS && !empty($loan_export_res['data'])) {
                $loan_export_file_url = $loan_export_res['data'];
            }

            if (empty($reserve_fund_export_file_url) || empty($loan_export_file_url)) {
                throw new Exception('Excel文件生成失败', ErrCode::$SYSTEM_ERROR);
            }

            // 3. 组装邮件
            $email_subject = 'Loan AND Petty Cash of Branch ' . $data_last_year_month;
            $email_content = <<<EOF
loan Data  Please click on the link below to download<br>
$loan_export_file_url<br><br>
Petty Cash of Branch  Data Please click on the link below to download<br>
$reserve_fund_export_file_url<br><br>
EOF;


            // 4. 发送邮件
            $emails = SettingEnvModel::getValByCode('loan_and_reserve_snapshot_data_emails');
            $emails = array_values(array_unique(array_filter(explode(',', $emails))));

            $log .= '收件人: ' . json_encode($emails, JSON_UNESCAPED_UNICODE) . PHP_EOL;
            $email_send_res = $this->mailer->sendAsync($emails, $email_subject, $email_content);
            if (!$email_send_res) {
                throw new Exception('邮件入队失败', ErrCode::$SYSTEM_ERROR);
            }

            $log .= '邮件入队成功' . PHP_EOL;

        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;

        } catch (Exception $e) {
            $log .= $e->getMessage() . PHP_EOL;
            $is_exception = true;
        }

        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        if ($is_exception) {
            $this->logger->error('借款-备用金月初数据报表导出及邮件任务: ' . $log);
        } else {
            $this->logger->info('借款-备用金月初数据报表导出及邮件任务: ' . $log);
        }

        exit($log);
    }

    public function testAction()
    {
        echo "aaa";
    }

    /**
     * 导出计划HC数
     */
    public function plan_hc_numAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log = '任务名称: ' . $process_name . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s')  . PHP_EOL;
        $log .= '初始内存: ' . memory_usage() . PHP_EOL;
        $is_exception = false;

        try {
            // 1.提取待处理的下载任务(最早入队的)
            $task_model = DownloadCenterService::getInstance()->getFirstPendingTask(DownloadCenterEnum::ORGANIZATION_JOB_DEPARTMENT_INFO);
            if (empty($task_model)) {
                throw new ValidationException('没有待下载的任务', ErrCode::$VALIDATE_ERROR);
            }

            // 2.1解析参数
            $params = !empty($task_model->args_json) ? json_decode(base64_decode($task_model->args_json, true), true) : [];
            $params['language'] = $params['language'] ?? DownloadCenterEnum::DETAUL_LANGUAGE;
            $params['staff_info_id'] = $task_model->staff_info_id ?? 0;

            $log .= '任务ID: ' . $task_model->id . PHP_EOL;
            $log .= '任务参数: ' . json_encode($params, JSON_UNESCAPED_UNICODE) . PHP_EOL;

            // 2.2设置系统语言
            JobService::setLanguage($params['language']);

            // 2.3获取数据总量
            $all_total = JobService::getInstance()->getDataExportTotal($params);

            $total_page_num = ceil($all_total / DownloadCenterEnum::DEFAUL_PER_PAGE_SIZE);
            $log .= '预计总量: ' . $all_total . PHP_EOL;
            $log .= '预计批次: ' . $total_page_num . PHP_EOL;

            // 2.4分批取数
            $excel_data = [];
            $page_num = DownloadCenterEnum::INIT_PAGE_NUM;
            for ($page_num; $page_num <= $total_page_num; $page_num++) {
                $log .= '----- 取数批次: ' . $page_num . '-----' . PHP_EOL;

                $params['pageNum'] = $page_num;
                $params['pageSize'] = DownloadCenterEnum::DEFAUL_PER_PAGE_SIZE;

                $list = JobService::getInstance()->getExportData($params);

                $log .= '本批数量: ' . count($list) . PHP_EOL;

                // 2.5合并数据
                $excel_data = array_merge($excel_data, $list);

                unset($list);

                $log .= '当前内存: ' . memory_usage() . PHP_EOL;
            }

            $log .= '实际总量: ' . count($excel_data) . PHP_EOL;
            $log .= '当前内存: ' . memory_usage() . PHP_EOL;

            // 2.6获取Excel表头
            $excel_header = JobService::getInstance()->getExportExcelHeaderFields();

            // 2.7生成Excel
            $excel_result = JobService::getInstance()->exportExcel($excel_header, $excel_data, $task_model->file_name);
            $log .= 'Excel结果: ' . json_encode($excel_result, JSON_UNESCAPED_UNICODE) . PHP_EOL;
            unset($excel_data);

            // 3.更新任务
            if (isset($excel_result['code']) && $excel_result['code'] == ErrCode::$SUCCESS && !empty($excel_result['data'])) {
                $save_model_result = DownloadCenterService::getInstance()->saveTask($task_model, DownloadCenterEnum::TASK_STATUS_SUCCESS, $excel_result['data']);
                if ($save_model_result === false) {
                    $log .= '任务状态: 更新失败, 原因可能是: ' . get_data_object_error_msg($task_model) . PHP_EOL;

                } else {
                    $log .= '任务状态: 更新成功' . PHP_EOL;
                }

            } else {
                // 记录错误日志
                $log .= '任务状态: 未更新, 生成Excel失败, 待下次重新执行' . PHP_EOL;
            }

        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;

        } catch (Exception $e) {
            $log .= $e->getMessage() . PHP_EOL;
            $is_exception = true;
        }

        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        if ($is_exception) {
            $this->logger->error('导出计划HC数任务: ' . $log);
        } else {
            $this->logger->info('导出计划HC数任务: ' . $log);
        }

        exit($log);
    }

    /**
     * 导出转岗列表
     * php app/cli.php export job_transfer_list
     */
    public function job_transfer_listAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log = '任务名称: ' . $process_name . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s')  . PHP_EOL;
        $log .= '初始内存: ' . memory_usage() . PHP_EOL;
        $is_exception = false;

        try {
            // 1.提取待处理的下载任务(最早入队的)
            $task_model = DownloadCenterService::getInstance()->getFirstPendingTask(DownloadCenterEnum::JOB_TRANSFER_DATA_EXPORT);
            if (empty($task_model)) {
                throw new ValidationException('没有待下载的任务', ErrCode::$VALIDATE_ERROR);
            }

            // 2.1解析参数
            $params                  = !empty($task_model->args_json) ? json_decode(base64_decode($task_model->args_json,
                true), true) : [];
            $params['language']      = $params['language'] ?? DownloadCenterEnum::DETAUL_LANGUAGE;
            $params['staff_info_id'] = $task_model->staff_info_id ?? 0;
            $params['file_name']     = $task_model->file_name;

            $log .= '任务ID: ' . $task_model->id . PHP_EOL;
            $log .= '任务参数: ' . json_encode($params, JSON_UNESCAPED_UNICODE) . PHP_EOL;

            // 2.2设置系统语言
            JobService::setLanguage($params['language']);

            // 2.3获取数据总量
            [$excel_result, $log, $total] = $this->generateJobTransferXlsx($params, $log);

            // 3.更新任务
            if (isset($excel_result['code']) && $excel_result['code'] == ErrCode::$SUCCESS && !empty($excel_result['data'])) {
                $save_model_result = DownloadCenterService::getInstance()->saveTask($task_model, DownloadCenterEnum::TASK_STATUS_SUCCESS, $excel_result['data']);
                if ($save_model_result === false) {
                    $log .= '任务状态: 更新失败, 原因可能是: ' . get_data_object_error_msg($task_model) . PHP_EOL;

                } else {
                    $log .= '任务状态: 更新成功' . PHP_EOL;
                }

            } else {
                // 记录错误日志
                $log .= '任务状态: 未更新, 生成Excel失败, 待下次重新执行' . PHP_EOL;
            }

        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;

        } catch (Exception $e) {
            $log .= $e->getMessage() . $e->getTraceAsString() . PHP_EOL;
            $is_exception = true;
        }

        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        if ($is_exception) {
            $this->logger->error('导出转岗列表: ' . $log);
        } else {
            $this->logger->info('导出转岗列表: ' . $log);
        }

        exit($log);
    }

    /**
     * 导出转岗payroll数据
     * @throws BusinessException
     * @throws GuzzleException
     */
    public function job_transfer_payroll_listAction()
    {
        $task_model = DownloadCenterService::getInstance()->getFirstPendingTask(DownloadCenterEnum::JOB_TRANSFER_DATA_FOR_PAYROLL_EXPORT);
        if (empty($task_model)) {
            return;
        }
        $params             = empty($task_model->args_json) ? [] : json_decode(base64_decode($task_model->args_json,
            true), true);
        $params['language'] = $params['language'] ?? DownloadCenterEnum::DETAUL_LANGUAGE;
        JobService::setLanguage($params['language']);
        $params['hire_type_not_agent'] = true;

        $list         = ListService::getInstance()->getTransferredJobDataList($params);
        $header       = ListService::getInstance()->getExportPayrollList();
        $excel_result = ListService::getInstance()->exportExcel($header, $list, $task_model->file_name);
        unset($list);
        if (isset($excel_result['code']) && $excel_result['code'] == ErrCode::$SUCCESS && !empty($excel_result['data'])) {
            DownloadCenterService::getInstance()->saveTask($task_model,
                DownloadCenterEnum::TASK_STATUS_SUCCESS, $excel_result['data']);
        }
        echo json_encode([$params, $excel_result]);
        $this->logger->info([$params, $excel_result]);
    }

    /**
     * 发送邮件
     * @return void
     * @throws GuzzleException
     * php app/cli.php export send_hire_type_change_email
     */
    public function send_hire_type_change_emailAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log = '任务名称: ' . $process_name . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s')  . PHP_EOL;
        $log .= '初始内存: ' . memory_usage() . PHP_EOL;
        $is_exception = false;

        try {
            $today                         = date('Y-m-d');
            $params['language']            = DownloadCenterEnum::DETAUL_LANGUAGE;
            $params['staff_info_id']       = null;
            $params['file_name']           = sprintf('Abnormal transfer employees_%s.xlsx', date('Ymd'));
            $params['all_permission']      = true;
            $params['is_hire_type_change'] = true;
            $params['actual_date_start']   = $today;
            $params['actual_date_end']     = $today;
            $params['state']               = [JobTransferModel::JOB_TRANSFER_STATE_COMPLETED];

            $log .= '任务参数: ' . json_encode($params, JSON_UNESCAPED_UNICODE) . PHP_EOL;

            // 2.2设置系统语言
            JobService::setLanguage($params['language']);

            // 2.3获取数据总量
            [$excel_result, $log, $total] = $this->generateJobTransferXlsx($params, $log);

            // 3.更新任务
            if (isset($excel_result['code']) && $excel_result['code'] == ErrCode::$SUCCESS && $total) {
                $title = 'Abnormal transfer employee contract processing reminder';
                $html = "Dear all,<br/>";
                $html .= sprintf('<p>Some employees who have transferred from permanent employee to other employment types need special contract handling. Click <a href="%s" target="_blank"> Excel </a> to download for details.</p>', $excel_result['data']);
                $html .= "<br/>";
                $html .= "<p>Thank you!</p>";

                $emails = (new SettingEnvModel())->getValByCode('abnormal_transfer_send_email');
                if (empty($emails)) {
                    $this->logger->warning("purchase order send email null==" . json_encode($emails,
                            JSON_UNESCAPED_UNICODE));
                    exit;
                }
                $emailArr = explode(",", $emails);
                $flag     = $this->mailer->sendAsync($emailArr, $title, $html);
                if ($flag) {
                    $this->logger->info('sendDeptOperateLog 发送成功');
                } else {
                    $this->logger->warning("sendDeptOperateLog 发送失败");
                }
                echo '结果：' . $flag . PHP_EOL;
                echo 'end' . PHP_EOL;
            } else {
                // 记录错误日志
                $log .= '数据为空，不生成excel' . PHP_EOL;
            }

        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;

        } catch (Exception $e) {
            $log .= $e->getMessage() . $e->getTraceAsString() . PHP_EOL;
            $is_exception = true;
        }

        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        if ($is_exception) {
            $this->logger->error('导出转岗列表: ' . $log);
        } else {
            $this->logger->info('导出转岗列表: ' . $log);
        }

        exit($log);
    }

    /**
     * @param $params
     * @param $log
     * @return array
     * @throws BusinessException
     * @throws GuzzleException
     */
    private function generateJobTransferXlsx($params, $log)
    {
        $all_total = ListService::getInstance()->getDataExportTotal($params, $params['staff_info_id']);
//        if ($all_total == 0) {
//            return [['code' => ErrCode::$SUCCESS, 'message' => 'success', 'data' => []], $log];
//        }

        $total_page_num = ceil($all_total / DownloadCenterEnum::DEFAUL_PER_PAGE_SIZE);
        $log .= '预计总量: ' . $all_total . PHP_EOL;
        $log .= '预计批次: ' . $total_page_num . PHP_EOL;

        // 2.4分批取数
        $excel_data = [];

        //获取管辖权限
        for ($page_num = DownloadCenterEnum::INIT_PAGE_NUM; $page_num <= $total_page_num; $page_num++) {
            $log .= '----- 取数批次: ' . $page_num . '-----' . PHP_EOL;

            $params['page_num']  = $page_num;
            $params['page_size'] = DownloadCenterEnum::DEFAUL_PER_PAGE_SIZE;

            $list = ListService::getInstance()->getExportData($params, $params['staff_info_id']);

            $log .= '本批数量: ' . count($list) . PHP_EOL;

            // 2.5合并数据
            $excel_data = array_merge($excel_data, $list);

            unset($list);

            $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        }

        $log .= '实际总量: ' . count($excel_data) . PHP_EOL;
        $log .= '当前内存: ' . memory_usage() . PHP_EOL;

        // 2.6获取Excel表头
        $excel_header = ListService::getInstance()->getExportExcelHeaderFields();

        // 2.7生成Excel
        $excel_result = ListService::getInstance()->exportExcel($excel_header, $excel_data, $params['file_name']);
        $log .= 'job_transfer_list Excel结果: ' . json_encode($excel_result, JSON_UNESCAPED_UNICODE) . PHP_EOL;

        return [$excel_result, $log, $all_total];
    }

    /**
     * 临时导出: 关联了报销 和 发起归还借款单的数据(全量-菲律宾)
     *
     * 每天一次, 直到需求正式上线
     *
     * php app/cli.php export tmp_loan_rel_data
     *
     * @param $params
     */
    public function tmp_loan_rel_dataAction($params)
    {
        $log = '借款单相关明细数据报表(全量)' . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        try {
            // 设置导出的语言
            $language = $params[0] ?? 'en';
            BaseService::setLanguage($language);

            // 1. 关联了报销的借款单
            $rel_reimbursement_excel = LoanListService::getInstance()->exportAllLoanDataByRelReimbursement();
            $log .= '冲抵报销单Excel生成结果: ' . json_encode($rel_reimbursement_excel, JSON_UNESCAPED_UNICODE) . PHP_EOL;

            // 2. 发起了归还的借款单
            $rel_return_excel = LoanListService::getInstance()->exportLoanDataByRelReturn();
            $log .= '发起归还单Excel生成结果: ' . json_encode($rel_return_excel, JSON_UNESCAPED_UNICODE) . PHP_EOL;


            // 3. 消息通知模板
            $msg_content_tpl = '<at user_id="<EMAIL>">程雪敬</at>' . PHP_EOL;
            $msg_content_tpl .= '场景: 借款单相关明细数据报表(全量)'. PHP_EOL;
            $msg_content_tpl .= '国家: ' . get_country_code() . PHP_EOL;
            $msg_content_tpl .= '环境: ' . RUNTIME . PHP_EOL;
            $msg_content_tpl .= '系统时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
            $msg_content_tpl .= '借款单冲抵报销明细: ' . $rel_reimbursement_excel['data'] . PHP_EOL;
            $msg_content_tpl .= '借款单归还明细: ' . $rel_return_excel['data'] . PHP_EOL;
            $res = send_feishu_text_msg($msg_content_tpl, MonitorTask::FEISHU_PM_ARD_GROUP_MSG_KEY);

            $log .= '飞书消息发送结果: ' . json_encode($res, JSON_UNESCAPED_UNICODE) . PHP_EOL;

        } catch (Exception $e) {
            $log .= '报表生成异常, ' . $e->getMessage();
            $this->logger->error($log);
        }

        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        exit($log);
    }

    /**
     * 其他合同数据查询-全量数据导出(临时)
     *
     * 参数1: 导出用户工号
     * 参数2: 导出参数, 同页面请求参数(对json base64_encode)
     * 参数3: 导出语言
     * php app/cli.php export other_contract_data 19515 eyJjbm8iOiIiLCJjbmFtZSI6IiIsInRlbXBsYXRlX2lkIjoiIiwiZmlsaW5nX2F0X3N0YXJ0IjoiIiwiZmlsaW5nX2F0X2VuZCI6IiIsImNyZWF0ZWRfYXRfc3RhcnQiOiIiLCJjcmVhdGVkX2F0X2VuZCI6IiIsImFtb3VudF90eXBlIjoiIiwiYXBwcm92ZWRfYXRfc3RhcnQiOiIiLCJhcHByb3ZlZF9hdF9lbmQiOiIiLCJlZmZlY3RpdmVfZGF0ZV9zdGFydCI6IiIsImVmZmVjdGl2ZV9kYXRlX2VuZCI6IiIsImV4cGlyeV9kYXRlX3N0YXJ0IjoiIiwiZXhwaXJ5X2RhdGVfZW5kIjoiIiwiY3JlYXRlX25hbWUiOiIiLCJzdGF0dXMiOiIiLCJmbGFnIjpudWxsLCJpc19yZXBseSI6bnVsbCwicXVvdGF0aW9uX25vIjoiIiwiaXNfZ3JvdXBfY29udHJhY3QiOiIiLCJjcmVhdGVfZGVwYXJ0bWVudF9pZCI6IiIsImN1c3RvbWVyX2NvbXBhbnlfbmFtZSI6IiIsImNvbXBhbnlfY29kZSI6IiIsInNpZ25fdHlwZSI6IiIsInBhZ2VTaXplIjoxMDAsInBhZ2VOdW0iOjF9 zh-CN
     *
     * base64_encode({"cno":"","cname":"","template_id":"","filing_at_start":"","filing_at_end":"","created_at_start":"","created_at_end":"","amount_type":"","approved_at_start":"","approved_at_end":"","effective_date_start":"","effective_date_end":"","expiry_date_start":"","expiry_date_end":"","create_name":"","status":"","flag":null,"is_reply":null,"quotation_no":"","is_group_contract":"","create_department_id":"","customer_company_name":"","company_code":"","sign_type":"","pageSize":100,"pageNum":1})
     *
     * @param array $args
     */
    public function other_contract_dataAction($args = [])
    {
        $log = '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        try {
            $staff_id = $args[0] ?? '';
            $params = !empty($args[1]) ? json_decode(base64_decode($args[1]), true): [];
            $log .= '操作人: ' . $staff_id . PHP_EOL;
            if (empty($staff_id)) {
                throw new Exception('操作人参数为空', ErrCode::$SYSTEM_ERROR);
            }

            if (empty($params)) {
                throw new Exception('请求参数为空', ErrCode::$SYSTEM_ERROR);
            }

            // 操作人信息
            $user_info = UserService::getInstance()->getLoginUser($staff_id);
            if (empty($user_info)) {
                throw new Exception('操作人信息不存在', ErrCode::$SYSTEM_ERROR);
            }

            static::setLanguage($args[2] ?? 'zh-CN');

            $res = ContractListService::getInstance()->getDownloadList($params, $user_info);
            $file_url = $res['data'] ?? '';
            $log .= '导出文件: ' . $file_url . PHP_EOL;
        } catch (Exception $e) {
            $log .= $e->getMessage();
        }

        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        exit($log);
    }


}
