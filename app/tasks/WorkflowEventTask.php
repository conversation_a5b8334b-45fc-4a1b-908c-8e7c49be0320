<?php
use App\Models\oa\WorkflowRequestNodePushMsg;
use App\Modules\Common\Services\StaffLangService;
use App\Library\Enums;
use App\Modules\Workflow\Services\WorkflowEventService;
use App\Library\ApiClient;

/**
 * 审批流待审核、审核通过、审核驳回、撤回、被征询、已回复
 */
class WorkflowEventTask extends RocketMqBaseTask
{
    public $consumeOnceLimit = 12;

    public function initialize()
    {
        $this->tq = 'message_push';
        parent::initialize();
    }

    /**
     * 发送push
     * @param string $msgBody 消息体
     * @return bool
     */
    protected function processOneMsg($msgBody)
    {
        try {
            $this->logger->info('WorkflowEventTask processOneMsg msgBody : ' . base64_decode($msgBody));
            $msg_data = $this->getMessageData($msgBody);
            $this->logger->info('WorkflowEventTask processOneMsg msg_data : ' . json_encode($msg_data, JSON_UNESCAPED_UNICODE));
            if (!empty($msg_data)) {
                $push_info = WorkflowRequestNodePushMsg::findFirst([
                    'conditions' => 'request_id = :request_id: and auditor_id = :auditor_id: and audit_action = :audit_action: and status = :status:',
                    'bind' => [
                        'request_id' => $msg_data['data']['request_id'],
                        'auditor_id' => $msg_data['data']['auditor_id'],
                        'audit_action' => $msg_data['data']['audit_action'],
                        'status' => 0
                    ]
                ]);
                if (!empty($push_info)) {
                    $push_info->status = 1;//已推送
                    $push_info->updated_at = date('Y-m-d H:i:s');
                    if ($push_info->save()) {
                        //存在待推送的数据，获取审批人的语种
                        self::setLanguage($this->getStaffLatestMobileLang($msg_data['data']['auditor_id']));

                        //开始push
                        $push_content = $this->getPushContent($push_info->audit_action);

                        // 待审批/待回复 的 消息是普通推送(+1); 同意/驳回/撤销/已回复 的 消息是静默推送(-1)
                        // 1-待审批, 2-驳回, 3-通过, 4-撤回, 5-发起征询, 6-回复征询
                        $silence = in_array($push_info->audit_action, [1, 5]) ? 0 : 1;
                        $data = [
                            'staff_info_id' => $push_info->auditor_id,  //员工工号
                            'message_title' => static::$t->_($push_content['title']),  //标题
                            'message_content' => static::$t->_($push_content['message'], ['no' => $push_info->no]),//内容
                            'message_scheme' => 'flashbackyard://fe/html?url=' . urlencode(env('by_url_prefix_new') . '/oa-push-transfer'), //地址
                            'message_priority' => 1,// push优先级: 0-普通; 1-优先
                            'src' => 'backyard',
                            'silence' => $silence // 0-普通推送; 1-静默推送
                        ];
                        $ret = new ApiClient('bi_svc', '', 'push_to_staff');
                        $ret->setParams([$data]);
                        $res = $ret->execute();
                        $bi_return = $res && isset($res['result']) && $res['result'] == true;
                        $res_msg = $bi_return ? '成功' : '失败';
                        $res_log = '给平台为[backyard]员工工号[' . $push_info->auditor_id . ']推送审批/征询/回复消息，发送时间[' . date('Y-m-d H:i:s') . ';send_to_user=' . json_encode($data, JSON_UNESCAPED_UNICODE) . '，发送结果[' . $res_msg . ']';
                        $this->logger->info($res_log);
                    } else {
                        $this->logger->warning('WorkflowEventTask processOneMsg change status error');
                    }
                } else {
                    $this->logger->info('WorkflowEventTask processOneMsg workflow_request_node_push_msg 未查到对应的msg数据');
                }
            }

        } catch (\Exception $e) {
            $this->logger->warning('WorkflowEventTask processOneMsg error : ' . $e->getMessage());
        }
        return true;
    }

    /**
     * push的标题和正文均走翻译系统，根据当前用户上次登录BY的最新语言获取对应的语言，发送push，系统中 =中，系统英=英，系统泰=泰，非中英泰获取英文
     * @param int $staff_id 员工工号
     * @return mixed|string
     */
    private function getStaffLatestMobileLang($staff_id)
    {
        $staff_language = StaffLangService::getInstance()->getLatestMobileLang($staff_id, 'en');
        //若不是中英泰,就用英
        $staff_language = strtolower($staff_language);
        if ($staff_language == 'zh-cn') {
            $staff_language = 'zh';
        }
        if (!in_array($staff_language, ['zh', 'th', 'en'])) {
            $staff_language = 'en';
        }
        return $staff_language;
    }

    /**
     * 获取每种状态的翻译key
     * @param int $audit_action 当前状态，1待审批，2驳回 ，3通过，4撤回，5发起征询，6回复征询
     * @return array
     */
    private function getPushContent($audit_action)
    {
        $push_content = [
            Enums::WF_STATE_PENDING => [
                'title' => 'oa_pending_push_title',
                'message' => 'oa_pending_push_message'
            ],
            Enums::WF_STATE_REJECTED => [
                'title' => 'oa_rejected_push_title',
                'message' => 'oa_rejected_push_message'
            ],
            Enums::WF_STATE_APPROVED => [
                'title' => 'oa_approved_push_title',
                'message' => 'oa_approved_push_message'
            ],
            Enums::WF_STATE_CANCEL => [
                'title' => 'oa_cancel_push_title',
                'message' => 'oa_cancel_push_message'
            ],
            WorkflowEventService::AUDIT_ACTION_ASK => [
                'title' => 'oa_fyr_ask_push_title',
                'message' => 'oa_fyr_ask_push_message'
            ],
            WorkflowEventService::AUDIT_ACTION_REPLY => [
                'title' => 'oa_fyr_reply_push_title',
                'message' => 'oa_fyr_reply_push_message'
            ]
        ];

        return $push_content[$audit_action];
    }
}
