<?php
use App\Library\Validation\ValidationException;
use App\Library\ErrCode;
use App\Library\Enums\WarehouseEnums;
use App\Models\oa\WarehouseThreadVerifyRecordModel;
use App\Repository\HrStaffRepository;
use App\Modules\Common\Services\EnumsService;

class WarehouseTask extends BaseTask
{
    /**
     * 增加仓库线索编号ENV配置，可配置线索编号和更新后的验证人，通过脚本方式执行更新验仓人
     *
     * php app/cli.php warehouse replace_thread_verify_person_liable
     */
    public function replace_thread_verify_person_liableAction()
    {
        $log = '';
        $log .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        try {
            // 根据配置获取需替换验仓库人的线索编号[一组单据 => 新的相同验仓人]
            $warehouse_replace_thread_verify_person_liable = EnumsService::getInstance()->getSettingEnvValueMap('warehouse_replace_thread_verify_person_liable');

            // 1. 待替换的配置[线索编号 -> 新验仓人]
            $this->logger->info('replace_auditors_config=' . json_encode($warehouse_replace_thread_verify_person_liable, JSON_UNESCAPED_UNICODE));
            if (empty($warehouse_replace_thread_verify_person_liable)) {
                throw new ValidationException('配置为空, 请检查', ErrCode::$VALIDATE_ERROR);
            }

            // 2. 替换线索下的验证记录验仓人信息
            $hr_staff_repository = new HrStaffRepository();
            $now = date('Y-m-d H:i:s');
            foreach ($warehouse_replace_thread_verify_person_liable as $config_index => $config_item) {
                $new_auditor   = $config_item['new_auditor'];
                $thread_no_list= array_unique(array_filter(explode(',', $config_item['thread_no_list'])));
                if (empty($new_auditor) || empty($thread_no_list)) {
                    $log .= '当前配置项的线索编号 或 新验仓人为空, 配置项索引: ' . $config_index . PHP_EOL;
                    continue;
                }

                // 查找线索下的验证记录
                foreach ($thread_no_list as $index => $thread_no) {
                    $thread_verify_info = WarehouseThreadVerifyRecordModel::findFirst([
                        'conditions' => 'thread_no = :thread_no: and status = :status:',
                        'bind'       => ['thread_no' => $thread_no, 'status' => WarehouseEnums::THREAD_VERIFY_STATUS_VERIFY],
                    ]);
                    if (empty($thread_verify_info)) {
                        $log .= '未找到线索-待验证信息: ' . $thread_no . PHP_EOL;
                        continue;
                    }

                    $detail_log = "待处理的线索-待验证-{$index}: {$thread_no}, verify_id={$thread_verify_info->id}, 原验仓人={$thread_verify_info->person_liable_id}, 新验仓人={$new_auditor}";

                    $staff_info = $hr_staff_repository->getStaffById($new_auditor);
                    if (empty($staff_info)) {
                        $log .= '未找到新验仓人信息: ' . $new_auditor . PHP_EOL;
                        continue;
                    }
                    $thread_verify_info->person_liable_id   = $new_auditor;
                    $thread_verify_info->person_liable_name = $staff_info['name'];
                    $thread_verify_info->updated_at         = $now;
                    $bool = $thread_verify_info->save();
                    $detail_log .= '处理' . ($bool ? '成功' : '失败') . PHP_EOL;
                    echo $detail_log;
                }
            }
        } catch (ValidationException $e) {
            $log .= '校验异常: ' . $e->getMessage() . PHP_EOL;
        } catch (Exception $e) {
            $log .= '处理异常: ' . $e->getMessage() . PHP_EOL;
        }

        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        exit($log);
    }
}
