<?php

use App\Library\Enums\SettingEnums;
use App\Library\RedisClient;
use App\Util\RedisKey;
use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Library\Exception\BusinessException;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Setting\Services\SystemService;

class TranslationTask extends BaseTask
{
    public function mainAction($params)
    {
        try{
            echo 'start '.date('Y-m-d H:i:s',time()).PHP_EOL;
            $phpPath = APP_PATH.'/language';
            if (!file_exists($phpPath)){
                mkdir($phpPath, 0777, true);
            }
            $langs = ['en','th','zh-CN'];
            foreach ($langs as $lang) {
                $i18n = 'https://ard-static.flashexpress.com';
                $url = sprintf($i18n."/oa-api/lang/%s.json",substr($lang,0,2));
                $json_data = file_get_contents($url);
                $langData = json_decode($json_data, true);
                if (is_array($langData) ) {
                    $phpContent = '<?php return ' . var_export($langData, true) . ';';
                    file_put_contents("{$phpPath}/{$lang}.php", $phpContent);
                }
            }
            echo 'done  '.date('Y-m-d H:i:s',time()).PHP_EOL;
        }catch (\Exception $e){
            $this->logger->error($e->getMessage());
        }
    }

    public function genAction($params)
    {
        $this->logger->info('start at ' . time());
        $ver = $params[0] ?? '';
        $ver = empty($ver) ? '': '/' .$ver;
        $phpPath = APP_PATH.'/language' . $ver;
        $langs = ['zh-CN','en','th'];
        if (!file_exists($phpPath)){
            mkdir($phpPath, 0777, true);
        }
        foreach ($langs as $lang){
            $langData = $this->getTranslationFrom($lang);
            $data = [];
            foreach ($langData as $v){
                $data[$v['t_key']] = $v['t_value'];
            }
            $phpContent = '<?php return ' . var_export($data,true) . ';';
            file_put_contents("{$phpPath}/{$lang}.php", $phpContent);
        }
        $this->logger->info('done at ' . time());
    }

    /**
     * @param $lang
     * @return mixed
     */
    protected function getTranslationFrom($lang)
    {
        $db = $this->getDI()->get('db_oa');

        $sql = "select `t_key`,`t_value` from translations where lang='$lang'";

        return $db->fetchAll($sql);
    }

    /**
     * 监听翻译是否需拉取
     * php app/cli.php translation pull_monitor
     *
     * @param array $args
     */
    public function pull_monitorAction(array $args = [])
    {
        echo '拉取翻译: ' . date('Y-m-d H:i:s') . PHP_EOL;
        try {
            // 执行策略: 默认永久执行(外部参数为 tmp, 则执行一次)
            $exec_strategy = isset($args[0]) && $args[0] == 'tmp' ? false : true;

            // 每 3 分钟重启一次
            $init_time = time();
            $per_run_second = 180;
            do {
                $runned_second = time() - $init_time;
                if ($runned_second > $per_run_second) {
                    throw new ValidationException("进程已持续运行 {$runned_second} s, 将退出重启", ErrCode::$VALIDATE_ERROR);
                }

                $pull_res = $this->pullLangMain();
                echo $pull_res['logger_content'] . PHP_EOL;

                if ($exec_strategy) {
                    sleep($pull_res['logger_type'] == 'info' ? 60 : $per_run_second);
                }
                
            } while ($exec_strategy);

        } catch (ValidationException $e) {
            $log = '进程维护: ' . $e->getMessage() . PHP_EOL;
            $this->logger->info($log);
            echo $log;
        } catch (Exception $e) {
            $error_log = '系统异常: ' . $e->getMessage() . PHP_EOL;
            $this->logger->error($error_log);
            echo $error_log;
        }

        exit('进程退出: ' . date('Y-m-d H:i:s') . PHP_EOL);
    }

    /**
     * 监听翻译是否需拉取
     *
     * @return string[] $log
     */
    public function pullLangMain()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log = '任务名称: ' . $process_name . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s')  . PHP_EOL;

        // 默认日志类型
        $logger_type = 'info';
        try {
            // 1. 获取当前的语言同步版本
            $redis = RedisClient::getInstance()->getClient();
            $center_latest_version_key = RedisKey::SYS_SETTING_SYNC_LANGUAGE_CENTER_VERSION;
            $center_latest_version = (int) $redis->get($center_latest_version_key);

            // 2. 获取上次的语言同步版本
            $center_last_version_key = RedisKey::SYS_SETTING_SYNC_LANGUAGE_LAST_CENTER_VERSION;
            $center_last_version = (int) $redis->get($center_last_version_key);

            if ($center_last_version >= $center_latest_version) {
                throw new ValidationException("翻译版本未更新, 无需拉取[last->latest: $center_last_version -> $center_latest_version]", ErrCode::$VALIDATE_ERROR);
            }

            // 3. 拉取job主机翻译 和 api主机翻译
            // 3.1 job主机翻译拉取
            SystemService::getInstance()->pullCurrentHostLanguage('oa-job');
            $log .= 'oa-job主机拉取成功: ' . gethostname() . PHP_EOL;

            // 3.2 api 相关主机翻译拉取 [获取主机配置数(约为实际主机数的两倍, 防止遍历各主机时有遗漏)]
            $all_host_count = EnumsService::getInstance()->getSettingEnvValue('all_oa_api_host_total', 0);

            $oa_api = $this->config->application->baseUri . '/common/healthy/pullLang';
            $oa_api_parse = parse_url($oa_api);
            $oa_host = $oa_api_parse['host'];
            $header = [
                "GET $oa_api HTTP/1.1",
                "Host: $oa_host",
                'Content-Type: application/json; charset=UTF-8',
                'Accept: */*',
                'User-Agent: Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 5.1; SV1)',
                'HTTP-PULL-LANG-AUTH-CODE: ' . SettingEnums::HTTP_PULL_LANGUAGE_AUTH_CODE
            ];

            // 3.3 遍历各oa-api主机
            $log .= 'oa-api主机拉取明细: ' . PHP_EOL;

            $exec_res = true;
            for ($i = 1; $i <= $all_host_count; $i++) {
                $oa_api_url = $oa_api . '?r=' . rand(100, 999) . '_' . date('H:i:s');
                $log .= "遍历批次 $i, url: $oa_api_url; ";

                // 3.4 请求oa-api拉取翻译接口
                $curl_res = curl_request($oa_api_url, null, 'get', $header, true, 20);
                if ($curl_res === false) {
                    $log .= '结果: curl 请求失败' . PHP_EOL;
                    $exec_res = false;
                    continue;
                }

                $curl_res = json_decode($curl_res, true);
                if (isset($curl_res['code']) && $curl_res['code'] == ErrCode::$SUCCESS) {
                    $log .= "结果: 拉取成功, {$curl_res['message']}" . PHP_EOL;
                } else {
                    $exec_res = false;
                    $log .= "结果: 拉取失败, {$curl_res['message']}" . PHP_EOL;
                }

                sleep(2);
            }

            // 3.5 全部拉取成功, 修改上次执行版本 为 当前最新版本
            if ($exec_res) {
                $redis->setex($center_last_version_key, SettingEnums::I18N_TRANSLATION_CATCH_PERIOD, $center_latest_version);
                $log .= "同步结果: 成功 latest_version-{$center_latest_version}" . PHP_EOL;
            }

        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;

        } catch (BusinessException $e) {
            $log .= $e->getMessage() . PHP_EOL;
            $logger_type = 'warning';

        } catch (Exception $e) {
            $log .= $e->getMessage() . PHP_EOL;
            $logger_type = 'error';
        }

        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $this->logger->$logger_type('OA翻译拉取:' . $log);

        return [
            'logger_type' => $logger_type,
            'logger_content' => $log
        ];
    }
}