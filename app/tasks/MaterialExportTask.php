<?php
/**
 * 资产耗材相关模块的异步下载任务
 */

use App\Library\Enums\MaterialEnums;
use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Library\Enums\DownloadCenterEnum;
use App\Modules\Common\Services\DownloadCenterService;
use App\Modules\Material\Services\AssetApplyService;
use App\Modules\Material\Services\LeaveAssetsService;
use App\Modules\Material\Services\WmsPlanService;
use App\Modules\Material\Services\AssetAccountService;
use App\Modules\Material\Services\AssetOutStorageService;
use App\Modules\Material\Services\WmsApplyService;
use App\Modules\Material\Services\WmsOutStorageService;
use App\Library\Enums\MaterialWmsEnums;
use App\Modules\Material\Services\InventoryCheckService;
use App\Library\Enums\MaterialAssetApplyEnums;
use App\Modules\Material\Services\AssetReturnService;
use App\Modules\Material\Services\LeaveAssetsExcelService;
use App\Modules\User\Services\UserService;
use App\Modules\Material\Services\WmsAllotService;
use App\Modules\Material\Services\WmsStockService;
use App\Library\BaseController;

class MaterialExportTask extends BaseTask
{
    /**
     * 库存盘点 - 盘点单名称 - 导出盘点报表
     */
    public function wms_inventory_reportAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log = '任务名称: ' . $process_name . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s')  . PHP_EOL;
        $log .= '初始内存: ' . memory_usage() . PHP_EOL;
        $is_exception = false;

        try {
            // 1.提取待处理的下载任务(最早入队的)
            $task_model = DownloadCenterService::getInstance()->getFirstPendingTask(DownloadCenterEnum::MATERIAL_WMS_PLAN_INFO_INVENTORY_REPORT);
            if (empty($task_model)) {
                throw new ValidationException('没有待下载的任务', ErrCode::$VALIDATE_ERROR);
            }

            // 2.1解析参数
            $params = !empty($task_model->args_json) ? json_decode(base64_decode($task_model->args_json, true), true) : [];
            $params['language'] = $params['language'] ?? DownloadCenterEnum::DETAUL_LANGUAGE;

            $log .= '任务ID: ' . $task_model->id . PHP_EOL;
            $log .= '任务参数: ' . json_encode($params, JSON_UNESCAPED_UNICODE) . PHP_EOL;

            // 2.2取数并生成Excel
            $export_result = WmsPlanService::getInstance()->getPlanInfoExport($params, $task_model->file_name);
            $log .= '取数结果: ' . json_encode($export_result, JSON_UNESCAPED_UNICODE) . PHP_EOL;

            // 3.更新任务
            if (!empty($export_result['file_url'])) {
                $save_model_result = DownloadCenterService::getInstance()->saveTask($task_model, DownloadCenterEnum::TASK_STATUS_SUCCESS, $export_result['file_url']);
                if ($save_model_result === false) {
                    $log .= '任务状态更新失败, 原因可能是: ' . get_data_object_error_msg($task_model) . PHP_EOL;

                } else {
                    $log .= '任务状态更新成功' . PHP_EOL;
                }

            } else {
                // 记录错误日志
                $log .= '任务状态: 未更新, 生成Excel失败, 待下次重新执行' . PHP_EOL;
            }

        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;

        } catch (Exception $e) {
            $log .= $e->getMessage() . PHP_EOL;
            $is_exception = true;
        }

        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        if ($is_exception) {
            $this->logger->error('库存盘点-盘点单名称-导出盘点报表任务: ' . $log);
        } else {
            $this->logger->info('库存盘点-盘点单名称-导出盘点报表任务: ' . $log);
        }

        exit($log);
    }

    /**
     * 资产台账 - 导出台账
     */
    public function asset_accountAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log = '任务名称: ' . $process_name . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s')  . PHP_EOL;
        $log .= '初始内存: ' . memory_usage() . PHP_EOL;
        $is_exception = false;

        try {
            // 1.提取待处理的下载任务(最早入队的)
            $task_model = DownloadCenterService::getInstance()->getFirstPendingTask(DownloadCenterEnum::MATERIAL_ASSET_ACCOUNT);
            if (empty($task_model)) {
                throw new ValidationException('没有待下载的任务', ErrCode::$VALIDATE_ERROR);
            }

            // 2.1解析参数
            $params = !empty($task_model->args_json) ? json_decode(base64_decode($task_model->args_json, true), true) : [];
            $params['language'] = $params['language'] ?? DownloadCenterEnum::DETAUL_LANGUAGE;
            //组合和controllers一样的user信息
            $user = (new UserService())->getUserById($task_model->staff_info_id);
            $user_info = (new BaseController())->format_user($user);

            $log .= '任务ID: ' . $task_model->id . PHP_EOL;
            $log .= '任务参数: ' . json_encode($params, JSON_UNESCAPED_UNICODE) . PHP_EOL;

            // 2.2设置系统语言
            AssetAccountService::setLanguage($params['language']);

            // 2.4获取数据总量
            $all_total = AssetAccountService::getInstance()->getListCount($user_info, $params, true);
            $total_page_num = ceil($all_total / DownloadCenterEnum::DEFAUL_PER_PAGE_SIZE);
            $log .= '预计总量: ' . $all_total . PHP_EOL;
            $log .= '预计批次: ' . $total_page_num . PHP_EOL;

            // 2.4分批取数
            $excel_data = [];
            $page_num = DownloadCenterEnum::INIT_PAGE_NUM;
            for ($page_num; $page_num <= $total_page_num; $page_num++) {
                $log .= '----- 取数批次: ' . $page_num . '-----' . PHP_EOL;

                $params['pageNum'] = $page_num;
                $params['pageSize'] = DownloadCenterEnum::DEFAUL_PER_PAGE_SIZE;
                $list = AssetAccountService::getInstance()->getList($user_info, $params, true, $all_total);

                // 2.5合并数据
                foreach ($list['data']['items'] as $item) {
                    $excel_data[] = array_values($item);
                }

                $log .= '本批数量: ' . count($list['data']['items']) . PHP_EOL;

                unset($list);

                $log .= '当前内存: ' . memory_usage() . PHP_EOL;
            }

            $log .= '实际总量: ' . count($excel_data) . PHP_EOL;
            $log .= '当前内存: ' . memory_usage() . PHP_EOL;

            // 2.6获取Excel表头
            $excel_header = AssetAccountService::getInstance()->getExportExcelHeaderFields();

            // 2.7生成Excel
            $excel_result = AssetAccountService::getInstance()->exportExcel($excel_header, $excel_data, $task_model->file_name);
            $log .= 'Excel结果: ' . json_encode($excel_result, JSON_UNESCAPED_UNICODE) . PHP_EOL;
            unset($excel_data);

            // 3.更新任务
            if (isset($excel_result['code']) && $excel_result['code'] == ErrCode::$SUCCESS && !empty($excel_result['data'])) {
                $save_model_result = DownloadCenterService::getInstance()->saveTask($task_model, DownloadCenterEnum::TASK_STATUS_SUCCESS, $excel_result['data']);
                if ($save_model_result === false) {
                    $log .= '任务状态: 更新失败, 原因可能是: ' . get_data_object_error_msg($task_model) . PHP_EOL;

                } else {
                    $log .= '任务状态: 更新成功' . PHP_EOL;
                }

            } else {
                // 记录错误日志
                $log .= '任务状态: 未更新, 生成Excel失败, 待下次重新执行' . PHP_EOL;
            }

        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;

        } catch (Exception $e) {
            $log .= $e->getMessage() . PHP_EOL;
            $is_exception = true;
        }

        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        if ($is_exception) {
            $this->logger->error('资产台账-导出台账任务: ' . $log);
        } else {
            $this->logger->info('资产台账-导出台账任务: ' . $log);
        }

        exit($log);
    }

    /**
     * 资产领用申请 - 导出
     */
    public function asset_apply_auditAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log          = '任务名称: ' . $process_name . PHP_EOL;
        $log          .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log          .= '初始内存: ' . memory_usage() . PHP_EOL;
        $logger_type     = 'info';
        try {
            //提取待处理的下载任务
            $task_model = DownloadCenterService::getInstance()->getFirstPendingTask(DownloadCenterEnum::MATERIAL_ASSET_APPLY_AUDIT);
            if (empty($task_model)) {
                throw new ValidationException('没有待下载的任务', ErrCode::$VALIDATE_ERROR);
            }
            //第一步先获取导出人名下待审核的申请总记录数
            $params = !empty($task_model->args_json) ? json_decode(base64_decode($task_model->args_json, true), true) : [];
            $params['language'] = $params['language'] ?? DownloadCenterEnum::DETAUL_LANGUAGE;
            $params['user_id']  = $task_model->staff_info_id;
            $log .= '任务ID: ' . $task_model->id . PHP_EOL;
            $log .= '任务参数: ' . json_encode($params, JSON_UNESCAPED_UNICODE) . PHP_EOL;
            $all_total = AssetApplyService::getInstance()->getAuditListCount($params);
            $total_count = !empty($all_total['total_count']) ? $all_total['total_count'] : 0;
            $total_page_num = ceil($total_count / DownloadCenterEnum::DEFAUL_PER_PAGE_SIZE);
            $log .= '预计总量: ' . $total_count . PHP_EOL;
            $log .= '预计批次: ' . $total_page_num . PHP_EOL;
            //第二步获取导出人名下待审核的申请明细
            self::setLanguage($params['language']);
            $params['workflow_no']  = $all_total['serial_no'];
            $excel_data = [];
            $page_num = DownloadCenterEnum::INIT_PAGE_NUM;
            for ($page_num; $page_num <= $total_page_num; $page_num++) {
                $log                .= '----- 取数批次: ' . $page_num . '-----' . PHP_EOL;
                $params['pageNum']  = $page_num;
                $params['pageSize'] = DownloadCenterEnum::DEFAUL_PER_PAGE_SIZE;
                $list = AssetApplyService::getInstance()->getExportList($params, MaterialAssetApplyEnums::LIST_TYPE_APPLY_AUDIT, $total_count);
                //合并数据
                foreach ($list['data']['items'] as $item) {
                    $excel_data[] = array_values($item);
                }
                $log .= '本批数量: ' . count($list['data']['items']) . PHP_EOL;
                unset($list);
                $log .= '当前内存: ' . memory_usage() . PHP_EOL;
            }
            $log .= '实际总量: ' . count($excel_data) . PHP_EOL;
            $log .= '当前内存: ' . memory_usage() . PHP_EOL;
            $excel_header = AssetApplyService::getInstance()->getAuditExportHeader();
            $excel_result = AssetApplyService::getInstance()->exportExcel($excel_header, $excel_data, $task_model->file_name);
            $log .= 'Excel结果: ' . json_encode($excel_result, JSON_UNESCAPED_UNICODE) . PHP_EOL;
            unset($excel_data);
            //更新任务
            if (isset($excel_result['code']) && $excel_result['code'] == ErrCode::$SUCCESS && !empty($excel_result['data'])) {
                $save_model_result = DownloadCenterService::getInstance()->saveTask($task_model, DownloadCenterEnum::TASK_STATUS_SUCCESS, $excel_result['data']);
                if ($save_model_result === false) {
                    $log .= '任务状态: 更新失败, 原因可能是: ' . get_data_object_error_msg($task_model) . PHP_EOL;

                } else {
                    $log .= '任务状态: 更新成功' . PHP_EOL;
                }

            } else {
                $log .= '任务状态: 未更新, 生成Excel失败, 待下次重新执行' . PHP_EOL;
            }

        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;

        } catch (Exception $e) {
            $log .= $e->getMessage() . PHP_EOL;
            $logger_type = 'error';
        }

        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        $this->logger->$logger_type('资产-资产领用审批-待处理-导出 : ' . $log);
        exit($log);
    }

    /**
     * 资产领用出库 - 导出
     */
    public function asset_out_storageAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log = '任务名称: ' . $process_name . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s')  . PHP_EOL;
        $log .= '初始内存: ' . memory_usage() . PHP_EOL;
        $is_exception = false;

        try {
            // 1.提取待处理的下载任务(最早入队的)
            $task_model = DownloadCenterService::getInstance()->getFirstPendingTask(DownloadCenterEnum::MATERIAL_ASSET_OUT_STORAGE);
            if (empty($task_model)) {
                throw new ValidationException('没有待下载的任务', ErrCode::$VALIDATE_ERROR);
            }

            // 2.1解析参数
            $params = !empty($task_model->args_json) ? json_decode(base64_decode($task_model->args_json, true), true) : [];
            $params['language'] = $params['language'] ?? DownloadCenterEnum::DETAUL_LANGUAGE;
            //组合和controllers一样的user信息
            $user = (new UserService())->getUserById($task_model->staff_info_id);
            $user_info = (new BaseController())->format_user($user);
            $log .= '任务ID: ' . $task_model->id . PHP_EOL;
            $log .= '任务参数: ' . json_encode($params, JSON_UNESCAPED_UNICODE) . PHP_EOL;

            // 2.2设置系统语言
            AssetOutStorageService::setLanguage($params['language']);

            // 2.3获取数据总量
            $all_total = AssetOutStorageService::getInstance()->getExportListCount($user_info, $params);
            $total_page_num = ceil($all_total / DownloadCenterEnum::DEFAUL_PER_PAGE_SIZE);
            $log .= '预计总量: ' . $all_total . PHP_EOL;
            $log .= '预计批次: ' . $total_page_num . PHP_EOL;

            // 2.4分批取数
            $excel_data = [];
            $page_num = DownloadCenterEnum::INIT_PAGE_NUM;
            for ($page_num; $page_num <= $total_page_num; $page_num++) {
                $log .= '----- 取数批次: ' . $page_num . '-----' . PHP_EOL;

                $params['pageNum'] = $page_num;
                $params['pageSize'] = DownloadCenterEnum::DEFAUL_PER_PAGE_SIZE;
                $list = AssetOutStorageService::getInstance()->getList($user_info, $params, true, $all_total);

                // 2.5合并数据
                foreach ($list['data']['items'] as $item) {
                    $excel_data[] = array_values($item);
                }

                $log .= '本批数量: ' . count($list['data']['items']) . PHP_EOL;

                unset($list);

                $log .= '当前内存: ' . memory_usage() . PHP_EOL;
            }

            $log .= '实际总量: ' . count($excel_data) . PHP_EOL;
            $log .= '当前内存: ' . memory_usage() . PHP_EOL;

            // 2.6获取Excel表头
            $excel_header = AssetOutStorageService::getInstance()->getExportExcelHeaderFields();

            // 2.7生成Excel
            $excel_result = AssetOutStorageService::getInstance()->exportExcel($excel_header, $excel_data, $task_model->file_name);
            $log .= 'Excel结果: ' . json_encode($excel_result, JSON_UNESCAPED_UNICODE) . PHP_EOL;
            unset($excel_data);

            // 3.更新任务
            if (isset($excel_result['code']) && $excel_result['code'] == ErrCode::$SUCCESS && !empty($excel_result['data'])) {
                $save_model_result = DownloadCenterService::getInstance()->saveTask($task_model, DownloadCenterEnum::TASK_STATUS_SUCCESS, $excel_result['data']);
                if ($save_model_result === false) {
                    $log .= '任务状态: 更新失败, 原因可能是: ' . get_data_object_error_msg($task_model) . PHP_EOL;

                } else {
                    $log .= '任务状态: 更新成功' . PHP_EOL;
                }

            } else {
                // 记录错误日志
                $log .= '任务状态: 未更新, 生成Excel失败, 待下次重新执行' . PHP_EOL;
            }

        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;

        } catch (Exception $e) {
            $log .= $e->getMessage() . PHP_EOL;
            $is_exception = true;
        }

        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        if ($is_exception) {
            $this->logger->error('资产领用出库-导出任务: ' . $log);
        } else {
            $this->logger->info('资产领用出库-导出任务: ' . $log);
        }

        exit($log);
    }

    /**
     * 资产盘点 - 盘点报表 - 导出
     */
    public function inventory_asset_detailAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log = '任务名称: ' . $process_name . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s')  . PHP_EOL;
        $log .= '初始内存: ' . memory_usage() . PHP_EOL;
        $is_exception = false;

        try {
            // 1.提取待处理的下载任务(最早入队的)
            $task_model = DownloadCenterService::getInstance()->getFirstPendingTask(DownloadCenterEnum::MATERIAL_INVENTORY_ASSET_EXPORT);
            if (empty($task_model)) {
                throw new ValidationException('没有待下载的任务', ErrCode::$VALIDATE_ERROR);
            }

            // 2.1解析参数
            $params = !empty($task_model->args_json) ? json_decode(base64_decode($task_model->args_json, true), true) : [];
            $params['language'] = $params['language'] ?? DownloadCenterEnum::DETAUL_LANGUAGE;

            $log .= '任务ID: ' . $task_model->id . PHP_EOL;
            $log .= '任务参数: ' . json_encode($params, JSON_UNESCAPED_UNICODE) . PHP_EOL;

            // 2.2获取数据总量
            $all_total = InventoryCheckService::getInstance()->getInventoryTaskAssetExportTotal($params);
            $total_page_num = ceil($all_total / DownloadCenterEnum::DEFAUL_PER_PAGE_SIZE);
            $log .= '预计总量: ' . $all_total . PHP_EOL;
            $log .= '预计批次: ' . $total_page_num . PHP_EOL;

            // 2.3设置系统语言
            InventoryCheckService::setLanguage($params['language']);

            // 2.4分批取数
            $excel_data = [];
            $page_num = DownloadCenterEnum::INIT_PAGE_NUM;
            for ($page_num; $page_num <= $total_page_num; $page_num++) {
                $log .= '----- 取数批次: ' . $page_num . '-----' . PHP_EOL;

                $params['pageNum'] = $page_num;
                $params['pageSize'] = DownloadCenterEnum::DEFAUL_PER_PAGE_SIZE;
                $list = InventoryCheckService::getInstance()->getInventoryTaskAssetList($params, true);

                // 2.5合并数据
                $items = $list['data']['items'];
                foreach ($items as $item) {
                    $excel_data[] = array_values($item);
                }

                $log .= '本批数量: ' . count($items) . PHP_EOL;
                
                unset($items);
                unset($list);

                $log .= '当前内存: ' . memory_usage() . PHP_EOL;
            }

            $log .= '实际总量: ' . count($excel_data) . PHP_EOL;
            $log .= '当前内存: ' . memory_usage() . PHP_EOL;

            // 2.6获取Excel表头
            $excel_header = InventoryCheckService::getInstance()->getExportExcelHeaderFields();

            // 2.7生成Excel
            $excel_result = InventoryCheckService::getInstance()->exportExcel($excel_header, $excel_data, $task_model->file_name);
            $log .= 'Excel结果: ' . json_encode($excel_result, JSON_UNESCAPED_UNICODE) . PHP_EOL;
            unset($excel_data);

            // 3.更新任务
            if (isset($excel_result['code']) && $excel_result['code'] == ErrCode::$SUCCESS && !empty($excel_result['data'])) {
                $save_model_result = DownloadCenterService::getInstance()->saveTask($task_model, DownloadCenterEnum::TASK_STATUS_SUCCESS, $excel_result['data']);
                if ($save_model_result === false) {
                    $log .= '任务状态: 更新失败, 原因可能是: ' . get_data_object_error_msg($task_model) . PHP_EOL;

                } else {
                    $log .= '任务状态: 更新成功' . PHP_EOL;
                }

            } else {
                // 记录错误日志
                $log .= '任务状态: 未更新, 生成Excel失败, 待下次重新执行' . PHP_EOL;
            }

        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;

        } catch (Exception $e) {
            $log .= $e->getMessage() . PHP_EOL;
            $is_exception = true;
        }

        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        if ($is_exception) {
            $this->logger->error('资产盘点 - 盘点报表 - 导出任务: ' . $log);
        } else {
            $this->logger->info('资产盘点 - 盘点报表 - 导出任务: ' . $log);
        }

        exit($log);
    }

    /**
     * 耗材 - 耗材领用出库-导出
     */
    public function wms_out_storageAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log = '任务名称: ' . $process_name . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s')  . PHP_EOL;
        $log .= '初始内存: ' . memory_usage() . PHP_EOL;
        $is_exception = false;
        try {
            $task_model = DownloadCenterService::getInstance()->getFirstPendingTask(DownloadCenterEnum::MATERIAL_WMS_OUT_STORAGE);
            if (empty($task_model)) {
                throw new ValidationException('没有待下载的任务', ErrCode::$VALIDATE_ERROR);
            }
            $params = !empty($task_model->args_json) ? json_decode(base64_decode($task_model->args_json, true), true) : [];
            $params['language'] = $params['language'] ?? DownloadCenterEnum::DETAUL_LANGUAGE;

            $log .= '任务ID: ' . $task_model->id . PHP_EOL;
            $log .= '任务参数: ' . json_encode($params, JSON_UNESCAPED_UNICODE) . PHP_EOL;
            $all_total = WmsOutStorageService::getInstance()->getExportListCount($params['language'], $params);
            $total_page_num = ceil($all_total / DownloadCenterEnum::DEFAUL_PER_PAGE_SIZE);
            $log .= '预计总量: ' . $all_total . PHP_EOL;
            $log .= '预计批次: ' . $total_page_num . PHP_EOL;
            WmsOutStorageService::setLanguage($params['language']);
            $excel_data = [];
            $page_num = DownloadCenterEnum::INIT_PAGE_NUM;
            for ($page_num; $page_num <= $total_page_num; $page_num++) {
                $log .= '----- 取数批次: ' . $page_num . '-----' . PHP_EOL;

                $params['pageNum'] = $page_num;
                $params['pageSize'] = DownloadCenterEnum::DEFAUL_PER_PAGE_SIZE;
                $list = WmsOutStorageService::getInstance()->getExportList($params['language'], $params, $all_total);
                foreach ($list['data']['items'] as $item) {
                    $excel_data[] = array_values($item);
                }
                $log .= '本批数量: ' . count($list['data']['items']) . PHP_EOL;

                unset($list);

                $log .= '当前内存: ' . memory_usage() . PHP_EOL;
            }
            $log .= '实际总量: ' . count($excel_data) . PHP_EOL;
            $log .= '当前内存: ' . memory_usage() . PHP_EOL;
            $excel_header = WmsOutStorageService::getInstance()->getExportHeader();
            $excel_result = AssetOutStorageService::getInstance()->exportExcel($excel_header, $excel_data, $task_model->file_name);

            $log .= 'Excel结果: ' . json_encode($excel_result, JSON_UNESCAPED_UNICODE) . PHP_EOL;
            unset($excel_data);

            //更新任务
            if (isset($excel_result['code']) && $excel_result['code'] == ErrCode::$SUCCESS && !empty($excel_result['data'])) {
                $save_model_result = DownloadCenterService::getInstance()->saveTask($task_model, DownloadCenterEnum::TASK_STATUS_SUCCESS, $excel_result['data']);
                if ($save_model_result === false) {
                    $log .= '任务状态: 更新失败, 原因可能是: ' . get_data_object_error_msg($task_model) . PHP_EOL;
                } else {
                    $log .= '任务状态: 更新成功' . PHP_EOL;
                }
            } else {
                //记录错误日志
                $log .= '任务状态: 未更新, 生成Excel失败, 待下次重新执行' . PHP_EOL;
            }

        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;

        } catch (Exception $e) {
            $log .= $e->getMessage() . PHP_EOL;
            $is_exception = true;
        }

        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        if ($is_exception) {
            $this->logger->error('耗材领用出库-导出 失败: ' . $log);
        } else {
            $this->logger->info('耗材领用出库-导出 成功: ' . $log);
        }
        exit($log);
    }


    /**
     * 耗材管理-数据查询-耗材申请-导出
     * php cli.php  material_export wms_apply_data
     */
    public function wms_apply_dataAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log          = '任务名称: ' . $process_name . PHP_EOL;
        $log          .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log          .= '初始内存: ' . memory_usage() . PHP_EOL;
        $logger_type     = 'info';
        try {
            $task_model = DownloadCenterService::getInstance()->getFirstPendingTask(DownloadCenterEnum::MATERIAL_WMS_APPLY_DATA);
            if (empty($task_model)) {
                throw new ValidationException('没有待下载的任务', ErrCode::$VALIDATE_ERROR);
            }
            $params             = !empty($task_model->args_json) ? json_decode(base64_decode($task_model->args_json, true), true) : [];
            $params['language'] = $params['language'] ?? DownloadCenterEnum::DETAUL_LANGUAGE;

            $log .= '任务ID: ' . $task_model->id . PHP_EOL;
            $log .= '任务参数: ' . json_encode($params, JSON_UNESCAPED_UNICODE) . PHP_EOL;

            //获取数据总量
            $all_total      = WmsApplyService::getInstance()->getListCount($params, MaterialWmsEnums::WMS_LIST_TYPE_APPLY_DATA, [], true);
            $total_page_num = ceil($all_total / DownloadCenterEnum::DEFAUL_PER_PAGE_SIZE);
            $log            .= '预计总量: ' . $all_total . PHP_EOL;
            $log            .= '预计批次: ' . $total_page_num . PHP_EOL;
            WmsApplyService::setLanguage($params['language']);
            $excel_data = [];
            $page_num   = DownloadCenterEnum::INIT_PAGE_NUM;
            for ($page_num; $page_num <= $total_page_num; $page_num++) {
                $log                .= '----- 取数批次: ' . $page_num . '-----' . PHP_EOL;
                $params['pageNum']  = $page_num;
                $params['pageSize'] = DownloadCenterEnum::DEFAUL_PER_PAGE_SIZE;
                $list               = WmsApplyService::getInstance()->getExportList($params, MaterialWmsEnums::WMS_LIST_TYPE_APPLY_DATA, $all_total);
                // 2.5合并数据
                foreach ($list['data']['items'] as $item) {
                    $excel_data[] = array_values($item);
                }
                $log .= '本批数量: ' . count($list['data']['items']) . PHP_EOL;
                unset($list);
                $log .= '当前内存: ' . memory_usage() . PHP_EOL;
            }
            $log .= '实际总量: ' . count($excel_data) . PHP_EOL;
            $log .= '当前内存: ' . memory_usage() . PHP_EOL;

            $excel_header = WmsApplyService::getInstance()->getAuditExportHeader();
            $excel_result = WmsApplyService::getInstance()->exportExcel($excel_header, $excel_data, $task_model->file_name);
            $log          .= 'Excel结果: ' . json_encode($excel_result, JSON_UNESCAPED_UNICODE) . PHP_EOL;
            unset($excel_data);

            //更新任务
            if (isset($excel_result['code']) && $excel_result['code'] == ErrCode::$SUCCESS && !empty($excel_result['data'])) {
                $save_model_result = DownloadCenterService::getInstance()->saveTask($task_model, DownloadCenterEnum::TASK_STATUS_SUCCESS, $excel_result['data']);
                if ($save_model_result === false) {
                    $log .= '任务状态: 更新失败, 原因可能是: ' . get_data_object_error_msg($task_model) . PHP_EOL;

                } else {
                    $log .= '任务状态: 更新成功' . PHP_EOL;
                }

            } else {
                $log .= '任务状态: 未更新, 生成Excel失败, 待下次重新执行' . PHP_EOL;
            }

        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;

        } catch (Exception $e) {
            $log          .= $e->getMessage() . PHP_EOL;
            $logger_type     = 'error';
        }

        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        $this->logger->$logger_type('耗材管理-数据查询-耗材申请-导出: ' . $log);
        exit($log);
    }


    /**
     * 耗材-耗材审批-待处理-导出
     * php cli.php  material_export wms_apply
     */
    public function wms_applyAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log          = '任务名称: ' . $process_name . PHP_EOL;
        $log          .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log          .= '初始内存: ' . memory_usage() . PHP_EOL;
        $logger_type     = 'info';
        try {
            //提取待处理的下载任务
            $task_model = DownloadCenterService::getInstance()->getFirstPendingTask(DownloadCenterEnum::MATERIAL_WMS_APPLY);
            if (empty($task_model)) {
                throw new ValidationException('没有待下载的任务', ErrCode::$VALIDATE_ERROR);
            }
            $params             = !empty($task_model->args_json) ? json_decode(base64_decode($task_model->args_json, true), true) : [];
            $params['language'] = $params['language'] ?? DownloadCenterEnum::DETAUL_LANGUAGE;
            $params['user_id']  = $task_model->staff_info_id;
            $log .= '任务ID: ' . $task_model->id . PHP_EOL;
            $log .= '任务参数: ' . json_encode($params, JSON_UNESCAPED_UNICODE) . PHP_EOL;
            $all_total      = WmsApplyService::getInstance()->getAuditListCount($params);
            $total_count = !empty($all_total['total_count']) ? $all_total['total_count'] : 0;
            $params['workflow_no']  = $all_total['serial_no'];
            $total_page_num = ceil($total_count / DownloadCenterEnum::DEFAUL_PER_PAGE_SIZE);
            $log            .= '预计总量: ' . $total_count . PHP_EOL;
            $log            .= '预计批次: ' . $total_page_num . PHP_EOL;
            WmsApplyService::setLanguage($params['language']);
            $excel_data = [];
            $page_num   = DownloadCenterEnum::INIT_PAGE_NUM;
            for ($page_num; $page_num <= $total_page_num; $page_num++) {
                $log                .= '----- 取数批次: ' . $page_num . '-----' . PHP_EOL;
                $params['pageNum']  = $page_num;
                $params['pageSize'] = DownloadCenterEnum::DEFAUL_PER_PAGE_SIZE;
                $list               = WmsApplyService::getInstance()->getExportList($params, MaterialWmsEnums::WMS_LIST_TYPE_APPLY_AUDIT, $total_count);
                //合并数据
                foreach ($list['data']['items'] as $item) {
                    $excel_data[] = array_values($item);
                }
                $log .= '本批数量: ' . count($list['data']['items']) . PHP_EOL;
                unset($list);
                $log .= '当前内存: ' . memory_usage() . PHP_EOL;
            }
            $log .= '实际总量: ' . count($excel_data) . PHP_EOL;
            $log .= '当前内存: ' . memory_usage() . PHP_EOL;
            $excel_header = WmsApplyService::getInstance()->getExportHeader();
            $excel_result = WmsApplyService::getInstance()->exportExcel($excel_header, $excel_data, $task_model->file_name);
            $log          .= 'Excel结果: ' . json_encode($excel_result, JSON_UNESCAPED_UNICODE) . PHP_EOL;
            unset($excel_data);
            //更新任务
            if (isset($excel_result['code']) && $excel_result['code'] == ErrCode::$SUCCESS && !empty($excel_result['data'])) {
                $save_model_result = DownloadCenterService::getInstance()->saveTask($task_model, DownloadCenterEnum::TASK_STATUS_SUCCESS, $excel_result['data']);
                if ($save_model_result === false) {
                    $log .= '任务状态: 更新失败, 原因可能是: ' . get_data_object_error_msg($task_model) . PHP_EOL;

                } else {
                    $log .= '任务状态: 更新成功' . PHP_EOL;
                }

            } else {
                $log .= '任务状态: 未更新, 生成Excel失败, 待下次重新执行' . PHP_EOL;
            }

        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;

        } catch (Exception $e) {
            $log          .= $e->getMessage() . PHP_EOL;
            $logger_type     = 'error';
        }

        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        $this->logger->$logger_type('耗材-耗材审批-待处理-导出 : ' . $log);
        exit($log);
    }

    /**
     * 物料资产管理-离职资产处理-导出
     * php app/cli.php  material_export leave_asset_export
     */
    public function leave_asset_exportAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log = '任务名称: ' . $process_name . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s')  . PHP_EOL;
        $log .= '初始内存: ' . memory_usage() . PHP_EOL;
        $is_exception = false;

        try {
            // 1.提取待处理的下载任务(最早入队的)
            $task = DownloadCenterService::getInstance()->getMorePendingTask([DownloadCenterEnum::MATERIAL_LEAVE_ASSETS_EXPORT, DownloadCenterEnum::MATERIAL_LEAVE_ASSETS_DETAIL_EXPORT, DownloadCenterEnum::MATERIAL_LEAVE_ASSETS_TRANSFER_EXPORT]);
            if (empty($task->toArray())) {
                throw new ValidationException('没有待下载的任务', ErrCode::$VALIDATE_ERROR);
            }
            foreach ($task as $task_model) {
                // 2.1解析参数
                $params = !empty($task_model->args_json) ? json_decode(base64_decode($task_model->args_json, true), true) : [];
                $params['language'] = $params['language'] ?? DownloadCenterEnum::DETAUL_LANGUAGE;

                $log .= '任务ID: ' . $task_model->id . PHP_EOL;
                $log .= '任务参数: ' . json_encode($params, JSON_UNESCAPED_UNICODE) . PHP_EOL;

                // 2.2设置系统语言
                LeaveAssetsExcelService::setLanguage($params['language']);

                // 操作者信息: 对接通用数据权限
                $user_info = (new UserService())->getLoginUser($task_model->staff_info_id);

                // 2.3获取数据总量
                $all_total = LeaveAssetsExcelService::getInstance()->getExcelCount($params, $user_info);
                $total_page_num = ceil($all_total / DownloadCenterEnum::DEFAUL_PER_PAGE_SIZE);
                $log .= '预计总量: ' . $all_total . PHP_EOL;
                $log .= '预计批次: ' . $total_page_num . PHP_EOL;

                // 2.4分批取数
                $excel_data = [];
                $page_num = DownloadCenterEnum::INIT_PAGE_NUM;
                for ($page_num; $page_num <= $total_page_num; $page_num++) {
                    $log .= '----- 取数批次: ' . $page_num . '-----' . PHP_EOL;
                    $params['pageNum'] = $page_num;
                    $params['pageSize'] = DownloadCenterEnum::DEFAUL_PER_PAGE_SIZE;
                    $rows = [];
                    if ($params['export_type'] == MaterialEnums::LEAVE_ASSETS_EXPORT_TYPE_COUNT) {
                        $list = LeaveAssetsService::getInstance()->getList($params, $params['language'], $user_info, true, $all_total);
                        $rows = $list['data']['items'] ?? [];
                    } elseif ($params['export_type'] == MaterialEnums::LEAVE_ASSETS_EXPORT_TYPE_DETAIL) {
                        //导出明细表
                        $rows = LeaveAssetsExcelService::getInstance()->getExportList($params, $params['language'], $user_info, $all_total);
                    } elseif ($params['export_type'] == MaterialEnums::LEAVE_ASSETS_EXPORT_TYPE_TRANSFER) {
                        //导出明细表(在途资产)
                        $rows = LeaveAssetsExcelService::getInstance()->getExportTransferList($params,  $params['language'],$user_info, $all_total);
                    }
                    $log .= '本批数量: ' . count($rows) . PHP_EOL;

                    // 2.5合并数据
                    $excel_data = array_merge($excel_data, $rows);

                    unset($list);

                    $log .= '当前内存: ' . memory_usage() . PHP_EOL;
                }

                $log .= '实际总量: ' . count($excel_data) . PHP_EOL;
                $log .= '当前内存: ' . memory_usage() . PHP_EOL;

                // 2.6获取Excel表头
                $header = LeaveAssetsExcelService::getInstance()->getExcelHeader($params['export_type']);

                // 2.7生成Excel
                $excel_result = LeaveAssetsExcelService::getInstance()->exportExcel($header, $excel_data, $task_model->file_name);
                $log .= 'Excel结果: ' . json_encode($excel_result, JSON_UNESCAPED_UNICODE) . PHP_EOL;
                unset($excel_data);

                // 3.更新任务
                if (isset($excel_result['code']) && $excel_result['code'] == ErrCode::$SUCCESS && !empty($excel_result['data'])) {
                    $save_model_result = DownloadCenterService::getInstance()->saveTask($task_model, DownloadCenterEnum::TASK_STATUS_SUCCESS, $excel_result['data']);
                    if ($save_model_result === false) {
                        $log .= '任务状态: 更新失败, 原因可能是: ' . get_data_object_error_msg($task_model) . PHP_EOL;

                    } else {
                        $log .= '任务状态: 更新成功' . PHP_EOL;
                    }

                } else {
                    // 记录错误日志
                    $log .= '任务状态: 未更新, 生成Excel失败, 待下次重新执行' . PHP_EOL;
                }
            }
        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;

        } catch (Exception $e) {
            $log .= $e->getMessage() . PHP_EOL;
            $is_exception = true;
        }

        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        if ($is_exception) {
            $this->logger->error('物料资产管理-离职资产处理-导出任务: ' . $log);
        } else {
            $this->logger->info('物料资产管理-离职资产处理-导出任务: ' . $log);
        }

        exit($log);
    }

    /**
     * 资产退回处理 - 待处理、已转交、已处理 - 导出
     * php app/cli.php material_export return_asset_detail
     */
    public function return_asset_detailAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log = '任务名称: ' . $process_name . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s')  . PHP_EOL;
        $log .= '初始内存: ' . memory_usage() . PHP_EOL;
        $is_exception = false;

        try {
            // 1.提取待处理的下载任务(最早入队的)
            $task = DownloadCenterService::getInstance()->getMorePendingTask([
                DownloadCenterEnum::MATERIAL_ASSET_RETURN_WAIT_EXPORT,
                DownloadCenterEnum::MATERIAL_ASSET_RETURN_TRANSFER_EXPORT,
                DownloadCenterEnum::MATERIAL_ASSET_RETURN_DONE_EXPORT
            ]);
            if (empty($task->toArray())) {
                throw new ValidationException('没有待下载的任务', ErrCode::$VALIDATE_ERROR);
            }
            foreach ($task as $task_model) {
                // 2.1解析参数
                $params = !empty($task_model->args_json) ? json_decode(base64_decode($task_model->args_json, true), true) : [];
                $params['language'] = $params['language'] ?? DownloadCenterEnum::DETAUL_LANGUAGE;

                $log .= '任务ID: ' . $task_model->id . PHP_EOL;
                $log .= '任务参数: ' . json_encode($params, JSON_UNESCAPED_UNICODE) . PHP_EOL;

                // 2.2设置系统语言
                AssetReturnService::setLanguage($params['language']);

                // 操作者信息: 对接通用数据权限
                $user_info = (new UserService())->getLoginUser($task_model->staff_info_id);

                // 2.3获取数据总量
                $params['flag'] = AssetReturnService::$list_type[$task_model->type];
                $all_total = AssetReturnService::getInstance()->getListCount($params, $user_info);
                $total_page_num = ceil($all_total / DownloadCenterEnum::DEFAUL_PER_PAGE_SIZE);
                $log .= '预计总量: ' . $all_total . PHP_EOL;
                $log .= '预计批次: ' . $total_page_num . PHP_EOL;

                // 2.4分批取数
                $excel_data = [];
                $page_num = DownloadCenterEnum::INIT_PAGE_NUM;
                for ($page_num; $page_num <= $total_page_num; $page_num++) {
                    $log .= '----- 取数批次: ' . $page_num . '-----' . PHP_EOL;

                    $params['pageNum'] = $page_num;
                    $params['pageSize'] = DownloadCenterEnum::DEFAUL_PER_PAGE_SIZE;
                    $list = AssetReturnService::getInstance()->getReturnList($params, $user_info, true);
                    $log .= '本批数量: ' . count($list) . PHP_EOL;

                    // 2.5合并数据
                    $excel_data = array_merge($excel_data, $list);

                    unset($list);

                    $log .= '当前内存: ' . memory_usage() . PHP_EOL;
                }

                $log .= '实际总量: ' . count($excel_data) . PHP_EOL;
                $log .= '当前内存: ' . memory_usage() . PHP_EOL;

                // 2.6获取Excel表头
                $excel_title = AssetReturnService::getInstance()->getExportExcelHeaderFields($params['flag']);

                // 2.7生成Excel
                $excel_result = AssetReturnService::getInstance()->exportExcel($excel_title, $excel_data, $task_model->file_name);
                $log .= 'Excel结果: ' . json_encode($excel_result, JSON_UNESCAPED_UNICODE) . PHP_EOL;
                unset($excel_data);

                // 3.更新任务
                if (isset($excel_result['code']) && $excel_result['code'] == ErrCode::$SUCCESS && !empty($excel_result['data'])) {
                    $save_model_result = DownloadCenterService::getInstance()->saveTask($task_model, DownloadCenterEnum::TASK_STATUS_SUCCESS, $excel_result['data']);
                    if ($save_model_result === false) {
                        $log .= '任务状态: 更新失败, 原因可能是: ' . get_data_object_error_msg($task_model) . PHP_EOL;

                    } else {
                        $log .= '任务状态: 更新成功' . PHP_EOL;
                    }

                } else {
                    // 记录错误日志
                    $log .= '任务状态: 未更新, 生成Excel失败, 待下次重新执行' . PHP_EOL;
                }
            }

        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;

        } catch (Exception $e) {
            $log .= $e->getMessage() . PHP_EOL;
            $is_exception = true;
        }

        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        if ($is_exception) {
            $this->logger->error('资产退回处理 - 待处理、已转交、已处理 - 导出任务: ' . $log);
        } else {
            $this->logger->info('资产退回处理 - 待处理、已转交、已处理 - 导出任务: ' . $log);
        }

        exit($log);
    }

    /**
     * 资产退回处理 - 退回入库 - 导出
     * php app/cli.php material_export return_asset_storage_detail
     */
    public function return_asset_storage_detailAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log = '任务名称: ' . $process_name . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s')  . PHP_EOL;
        $log .= '初始内存: ' . memory_usage() . PHP_EOL;
        $is_exception = false;

        try {
            // 1.提取待处理的下载任务(最早入队的)
            $task_model = DownloadCenterService::getInstance()->getFirstPendingTask(DownloadCenterEnum::MATERIAL_ASSET_RETURN_STORAGE_EXPORT);
            if (empty($task_model)) {
                throw new ValidationException('没有待下载的任务', ErrCode::$VALIDATE_ERROR);
            }

            // 2.1解析参数
            $params = !empty($task_model->args_json) ? json_decode(base64_decode($task_model->args_json, true), true) : [];
            $params['language'] = $params['language'] ?? DownloadCenterEnum::DETAUL_LANGUAGE;

            $log .= '任务ID: ' . $task_model->id . PHP_EOL;
            $log .= '任务参数: ' . json_encode($params, JSON_UNESCAPED_UNICODE) . PHP_EOL;

            // 2.2设置系统语言
            AssetReturnService::setLanguage($params['language']);

            // 2.3获取数据总量
            $all_total = AssetReturnService::getInstance()->getStorageListCount($params, true);
            $total_page_num = ceil($all_total / DownloadCenterEnum::DEFAUL_PER_PAGE_SIZE);
            $log .= '预计总量: ' . $all_total . PHP_EOL;
            $log .= '预计批次: ' . $total_page_num . PHP_EOL;

            // 2.4分批取数
            $excel_data = [];
            $page_num = DownloadCenterEnum::INIT_PAGE_NUM;
            for ($page_num; $page_num <= $total_page_num; $page_num++) {
                $log .= '----- 取数批次: ' . $page_num . '-----' . PHP_EOL;

                $params['pageNum'] = $page_num;
                $params['pageSize'] = DownloadCenterEnum::DEFAUL_PER_PAGE_SIZE;
                $list = AssetReturnService::getInstance()->getStorageDetailList($params, true);
                $log .= '本批数量: ' . count($list) . PHP_EOL;

                // 2.5合并数据
                $excel_data = array_merge($excel_data, $list);

                unset($list);

                $log .= '当前内存: ' . memory_usage() . PHP_EOL;
            }

            $log .= '实际总量: ' . count($excel_data) . PHP_EOL;
            $log .= '当前内存: ' . memory_usage() . PHP_EOL;

            // 2.6获取Excel表头
            $excel_title = AssetReturnService::getInstance()->getExportExcelHeaderFields(AssetReturnService::LIST_TYPE_STORAGE);

            // 2.7生成Excel
            $excel_result = AssetReturnService::getInstance()->exportExcel($excel_title, $excel_data, $task_model->file_name);
            $log .= 'Excel结果: ' . json_encode($excel_result, JSON_UNESCAPED_UNICODE) . PHP_EOL;
            unset($excel_data);

            // 3.更新任务
            if (isset($excel_result['code']) && $excel_result['code'] == ErrCode::$SUCCESS && !empty($excel_result['data'])) {
                $save_model_result = DownloadCenterService::getInstance()->saveTask($task_model, DownloadCenterEnum::TASK_STATUS_SUCCESS, $excel_result['data']);
                if ($save_model_result === false) {
                    $log .= '任务状态: 更新失败, 原因可能是: ' . get_data_object_error_msg($task_model) . PHP_EOL;

                } else {
                    $log .= '任务状态: 更新成功' . PHP_EOL;
                }
            } else {
                // 记录错误日志
                $log .= '任务状态: 未更新, 生成Excel失败, 待下次重新执行' . PHP_EOL;
            }

        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;

        } catch (Exception $e) {
            $log .= $e->getMessage() . PHP_EOL;
            $is_exception = true;
        }

        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        if ($is_exception) {
            $this->logger->error('资产退回处理 - 退回入库 - 导出任务: ' . $log);
        } else {
            $this->logger->info('资产退回处理 - 退回入库 - 导出任务: ' . $log);
        }

        exit($log);
    }

    /**
     * 网点耗材调拨导出
     * php app/cli.php material_export wms_package_allot
     */
    public function wms_package_allotAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log = '任务名称: ' . $process_name . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s')  . PHP_EOL;
        $log .= '初始内存: ' . memory_usage() . PHP_EOL;
        $is_exception = false;

        try {
            // 1.提取待处理的下载任务(最早入队的)
            $task_model = DownloadCenterService::getInstance()->getFirstPendingTask(DownloadCenterEnum::MATERIAL_WMS_PACKAGE_ALLOT);
            if (empty($task_model)) {
                throw new ValidationException('没有待下载的任务', ErrCode::$VALIDATE_ERROR);
            }

            // 2.1解析参数
            $params = !empty($task_model->args_json) ? json_decode(base64_decode($task_model->args_json, true), true) : [];
            $params['language'] = $params['language'] ?? DownloadCenterEnum::DETAUL_LANGUAGE;
            //组合和controllers一样的user信息
            $user = (new UserService())->getUserById($task_model->staff_info_id);
            $user_info = (new BaseController())->format_user($user);

            $log .= '任务ID: ' . $task_model->id . PHP_EOL;
            $log .= '任务参数: ' . json_encode($params, JSON_UNESCAPED_UNICODE) . PHP_EOL;

            // 2.2设置系统语言
            WmsAllotService::setLanguage($params['language']);

            // 2.3获取数据总量
            $all_total = WmsAllotService::getInstance()->getDataExportTotal($params, $user_info);
            $total_page_num = ceil($all_total / DownloadCenterEnum::DEFAUL_PER_PAGE_SIZE);
            $log .= '预计总量: ' . $all_total . PHP_EOL;
            $log .= '预计批次: ' . $total_page_num . PHP_EOL;

            // 2.4分批取数
            $excel_data = [];
            $page_num = DownloadCenterEnum::INIT_PAGE_NUM;
            for ($page_num; $page_num <= $total_page_num; $page_num++) {
                $log .= '----- 取数批次: ' . $page_num . '-----' . PHP_EOL;

                $params['pageNum'] = $page_num;
                $params['pageSize'] = DownloadCenterEnum::DEFAUL_PER_PAGE_SIZE;
                $list = WmsAllotService::getInstance()->getExportData($params, $user_info);
                $log .= '本批数量: ' . count($list) . PHP_EOL;

                // 2.5合并数据
                $excel_data = array_merge($excel_data, $list);

                unset($list);

                $log .= '当前内存: ' . memory_usage() . PHP_EOL;
            }

            $log .= '实际总量: ' . count($excel_data) . PHP_EOL;
            $log .= '当前内存: ' . memory_usage() . PHP_EOL;

            // 2.6获取Excel表头
            $excel_title = WmsAllotService::getInstance()->getExportExcelHeaderFields();

            // 2.7生成Excel
            $excel_result = WmsAllotService::getInstance()->exportExcel($excel_title, $excel_data, $task_model->file_name);
            $log .= 'Excel结果: ' . json_encode($excel_result, JSON_UNESCAPED_UNICODE) . PHP_EOL;
            unset($excel_data);

            // 3.更新任务
            if (isset($excel_result['code']) && $excel_result['code'] == ErrCode::$SUCCESS && !empty($excel_result['data'])) {
                $save_model_result = DownloadCenterService::getInstance()->saveTask($task_model, DownloadCenterEnum::TASK_STATUS_SUCCESS, $excel_result['data']);
                if ($save_model_result === false) {
                    $log .= '任务状态: 更新失败, 原因可能是: ' . get_data_object_error_msg($task_model) . PHP_EOL;

                } else {
                    $log .= '任务状态: 更新成功' . PHP_EOL;
                }
            } else {
                // 记录错误日志
                $log .= '任务状态: 未更新, 生成Excel失败, 待下次重新执行' . PHP_EOL;
            }

        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;

        } catch (Exception $e) {
            $log .= $e->getMessage() . PHP_EOL;
            $is_exception = true;
        }

        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        if ($is_exception) {
            $this->logger->error('网点耗材调拨导出任务: ' . $log);
        } else {
            $this->logger->info('网点耗材调拨导出任务: ' . $log);
        }

        exit($log);
    }

    /**
     * 网点耗材进销存导出
     * php app/cli.php material_export wms_package_stock
     */
    public function wms_package_stockAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log = '任务名称: ' . $process_name . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s')  . PHP_EOL;
        $log .= '初始内存: ' . memory_usage() . PHP_EOL;
        $is_exception = false;

        try {
            // 1.提取待处理的下载任务(最早入队的)
            $task_model = DownloadCenterService::getInstance()->getFirstPendingTask(DownloadCenterEnum::MATERIAL_WMS_PACKAGE_STOCK);
            if (empty($task_model)) {
                throw new ValidationException('没有待下载的任务', ErrCode::$VALIDATE_ERROR);
            }

            // 2.1解析参数
            $params = !empty($task_model->args_json) ? json_decode(base64_decode($task_model->args_json, true), true) : [];
            $params['language'] = $params['language'] ?? DownloadCenterEnum::DETAUL_LANGUAGE;
            //组合和controllers一样的user信息
            $user = (new UserService())->getUserById($task_model->staff_info_id);
            $user_info = (new BaseController())->format_user($user);

            $log .= '任务ID: ' . $task_model->id . PHP_EOL;
            $log .= '任务参数: ' . json_encode($params, JSON_UNESCAPED_UNICODE) . PHP_EOL;

            // 2.2设置系统语言
            WmsStockService::setLanguage($params['language']);

            // 2.3获取数据总量
            $all_total = WmsStockService::getInstance()->getDataTotal($params, $user_info);
            $total_page_num = ceil($all_total / DownloadCenterEnum::DEFAUL_PER_PAGE_SIZE);
            $log .= '预计总量: ' . $all_total . PHP_EOL;
            $log .= '预计批次: ' . $total_page_num . PHP_EOL;

            // 2.4分批取数
            $excel_data = [];
            $page_num = DownloadCenterEnum::INIT_PAGE_NUM;
            for ($page_num; $page_num <= $total_page_num; $page_num++) {
                $log .= '----- 取数批次: ' . $page_num . '-----' . PHP_EOL;

                $params['pageNum'] = $page_num;
                $params['pageSize'] = DownloadCenterEnum::DEFAUL_PER_PAGE_SIZE;
                $list = WmsStockService::getInstance()->getDataList($params, $user_info, true);
                $log .= '本批数量: ' . count($list) . PHP_EOL;

                // 2.5合并数据
                $excel_data = array_merge($excel_data, $list);

                unset($list);

                $log .= '当前内存: ' . memory_usage() . PHP_EOL;
            }

            $log .= '实际总量: ' . count($excel_data) . PHP_EOL;
            $log .= '当前内存: ' . memory_usage() . PHP_EOL;

            // 2.6获取Excel表头
            $excel_title = WmsStockService::getInstance()->getExportExcelHeaderFields();

            // 2.7生成Excel
            $excel_result = WmsStockService::getInstance()->exportExcel($excel_title, $excel_data, $task_model->file_name);
            $log .= 'Excel结果: ' . json_encode($excel_result, JSON_UNESCAPED_UNICODE) . PHP_EOL;
            unset($excel_data);

            // 3.更新任务
            if (isset($excel_result['code']) && $excel_result['code'] == ErrCode::$SUCCESS && !empty($excel_result['data'])) {
                $save_model_result = DownloadCenterService::getInstance()->saveTask($task_model, DownloadCenterEnum::TASK_STATUS_SUCCESS, $excel_result['data']);
                if ($save_model_result === false) {
                    $log .= '任务状态: 更新失败, 原因可能是: ' . get_data_object_error_msg($task_model) . PHP_EOL;

                } else {
                    $log .= '任务状态: 更新成功' . PHP_EOL;
                }
            } else {
                // 记录错误日志
                $log .= '任务状态: 未更新, 生成Excel失败, 待下次重新执行' . PHP_EOL;
            }

        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;

        } catch (Exception $e) {
            $log .= $e->getMessage() . PHP_EOL;
            $is_exception = true;
        }

        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        if ($is_exception) {
            $this->logger->error('网点耗材进销存导出任务: ' . $log);
        } else {
            $this->logger->info('网点耗材进销存导出任务: ' . $log);
        }

        exit($log);
    }
}
