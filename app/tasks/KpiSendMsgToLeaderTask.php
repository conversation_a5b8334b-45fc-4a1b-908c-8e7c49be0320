<?php

use App\Library\Enums\KpiActivityStaffEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\KpiActivityStaffModel;
use App\Models\backyard\KpiLeaderMsgSendLogModel;
use App\Modules\Kpi\Services\ActivityService;
use App\Modules\Kpi\Services\PushService;
use App\Modules\Kpi\Services\StaffService;

/**
 * PPM/PBP提醒上级填写KPI【定时提醒频次：每天, 提醒时间：根据活动配置中的时间进行提醒，运行周期: 每间隔一个小时执行一次】
 * Class KpiSendMsgToLeaderTask
 */
class KpiSendMsgToLeaderTask extends BaseTask
{
    public function mainAction()
    {
        try {
            echo date('Ymd H:i:s')." 脚本开始执行".PHP_EOL;
            //第一步找到所有进行中的KPI活动
            $ing_activity_list = ActivityService::getInstance()->getIngActivityList();
            if (empty($ing_activity_list) || !is_array($ing_activity_list)) {
                //没有进行中的KPI活动
                $log = date('Ymd H:i:s').' 暂无进行中的KPI活动'.PHP_EOL;
                $this->logger->info($log);
                echo $log;
                exit("No activities in progress - Script run completed");
            }

            // 在所有进行中的活动中，找到自动提醒开关是打开的，且符合当前运行时间的数据
            $cur_hour         = date('H');
            $send_notice_list = [];
            foreach ($ing_activity_list as $key => $value) {
                $json_array = json_decode($value['config'], true);
                if (json_last_error() != JSON_ERROR_NONE) {
                    // json 解析失败
                    continue;
                }
                $leader_auto_remind_config = $json_array['auto_remind']['leader'];   // 自动提醒
                if ($leader_auto_remind_config['on-off'] == 1 && $leader_auto_remind_config['time'] == $cur_hour) {
                    $send_notice_list[] = $value;
                }
            }

            // 所有进行的活动中，如果都没有开启自动提醒，或者开启了自动提醒且未到发送周期时，则直接退出脚本
            if (empty($send_notice_list)) {
                $log = date('Ymd H:i:s').' 未找到需要发送的数据信息'.PHP_EOL;
                echo $log;
                $this->logger->info($log);
                exit("No information found - Script run completed");
            }

            //有进行中的KPI活动
            $activity_ids      = array_column($send_notice_list, "id");
            $activity_info_arr = array_column($send_notice_list, null, "id");
            //第二步找到所有处于上级填写阶段的leader总数
            $count = $this->getStaffListCount($activity_ids);
            $log   = date('Ymd H:i:s')." PPM/PBP提醒上级填写KPI定时发送消息涉及直线上级总数为:".$count."个".PHP_EOL;
            $this->logger->info($log);
            echo $log;
            if (0 == $count) {
                //未找到 待制定计划的数据
                exit("Superior confirmation completed - Script run completed");
            }

            //第三步分页查询找到所有处于上级填写阶段的leader数据
            $page_size = 2000;
            $page      = ceil($count / $page_size);
            for ($i = 1; $i <= $page; $i++) {
                $leader_list = $this->getStaffList($activity_ids, $i, $page_size);
                if (empty($leader_list)) {
                    // 如果有下一页没有数据了，则直接退出当前循环
                    break;
                }

                // todo 去重处理 同一个活动下，不同的员工直线上级和在oa变更过的上级相同时 上述sql会返回两条数据，这里需要去重
                $unique_leader_list = $this->arrayUnion($leader_list);
                $staff_ids = array_values(array_unique(array_column($leader_list,'staff_id')));

                //给上级发送消息
                $operate_count = $this->batchSendMsgToLeader($unique_leader_list, $activity_info_arr, $staff_ids);
                $log           = date('Ymd H:i:s').' PPM/PBP提醒上级填写KPI定时发送消息任务，第'.$i.'页, 每页操作'.$page_size.'条: 成功[共计发送消息条数为:'.$operate_count.'条]'.PHP_EOL;
                $this->logger->info($log);
                echo $log;
            }
        } catch (\Exception $e) {
            $log = date('Ymd H:i:s').'  PPM/PBP提醒上级填写KPI定时发送消息任务: 失败[异常]'.$e->getMessage().PHP_EOL;
            echo $log;
            $this->logger->warning($log);
        }
        echo date('Ymd H:i:s')." 脚本执行结束".PHP_EOL;
    }

    /**
     * 同一个活动下 相同的leader_id去重处理
     * @param array $leader_list
     * @return array
     */
    public function arrayUnion(array $leader_list): array
    {
        $temArray = [];
        foreach ($leader_list AS $key => $value) {
            if (empty($value['leader_id'])) {
                unset($leader_list[$key]);
            }
            $idx = $value['activity_id'].'_'.$value['leader_id'];
            if (isset($temArray[$idx]) && !empty($temArray[$idx])) {
                unset($leader_list[$key]);
            }
            $temArray[$idx] = $value;
        }
        return array_values($leader_list);
    }

    /**
     * 查询当前阶段处于上级填写的上级信息
     * @param array $activity_ids 活动ID组
     * @return int
     */
    private function getStaffListCount(array $activity_ids): int
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns(['COUNT(distinct kas.leader_id, kas.activity_id) as total']);
        $builder->from(['kas' => KpiActivityStaffModel::class]);
        $builder->leftJoin(HrStaffInfoModel::class, "hsi.staff_info_id = kas.staff_id", 'hsi');
        $builder->leftjoin(HrStaffInfoModel::class, 'manger.staff_info_id = hsi.manger', 'manger');

        $builder    = $this->getCondition($builder, ['activity_ids' => $activity_ids]);
        $total_info = $builder->getQuery()->getSingleResult();
        return intval($total_info->total);
    }

    /**
     * 组装查询条件
     * @param object $builder 查询器
     * @param array $condition ['activity_ids'=>'进行中的活动ID组']
     * @return object
     */
    private function getCondition(object $builder, array $condition): object
    {
        $builder->andWhere('hsi.formal IN ({formal:array}) AND hsi.is_sub_staff = :is_sub_staff: AND hsi.state = :state:',
            [
                'formal'       => [StaffInfoEnums::FORMAL_IN, StaffInfoEnums::FORMAL_TRAINEE],
                'is_sub_staff' => StaffInfoEnums::IS_SUB_STAFF_NO,
                'state'        => StaffInfoEnums::STAFF_STATE_IN,
            ]);
        $builder->andWhere('kas.activity_id IN ({activity_ids:array})',
            ['activity_ids' => $condition['activity_ids']]);
        $builder->andWhere('kas.stage = :stage:', [
            'stage' => KpiActivityStaffEnums::STAGE_LEADER,
        ]);
        return $builder;
    }

    /**
     * 分页获取当前阶段处于上级填写的上级信息
     * @param array $activity_ids 活动ID组
     * @param int $page 当前页
     * @param int $page_size 每页条数
     * @return array
     */
    private function getStaffList(array $activity_ids, int $page, int $page_size): array
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('kas.id,
            kas.activity_id,  
            kas.staff_id,  
            IF(kas.leader_id = 0,manger.staff_info_id,kas.leader_id ) AS leader_id,
            IF(kas.leader_id = 0,manger.name,kas.leader_name) AS leader_name'
        );
        $builder->from(['kas' => KpiActivityStaffModel::class]);
        $builder->leftJoin(HrStaffInfoModel::class, "hsi.staff_info_id = kas.staff_id", 'hsi');
        $builder->leftjoin(HrStaffInfoModel::class, 'manger.staff_info_id = hsi.manger', 'manger');
        $builder = $this->getCondition($builder, ['activity_ids' => $activity_ids]);
        $builder->limit($page_size, $page_size * ($page - 1));
        // todo 去掉 groupby 是因为 当kas表的leader为0 时 无法实现分组，后续程序去重
        //$builder->groupBy('kas.leader_id, kas.activity_id');
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 给上级发送消息
     * @param array $leader_list 上级列表
     * @param array $activity_info_arr 活动列表
     * @return int
     * @throws Exception
     */
    private function batchSendMsgToLeader(array $leader_list, array $activity_info_arr, array $staff_ids): int
    {
        $push_service = new PushService();
        $staff_users  = [];                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         //站内信用户组
        //$log_data                   = [];                                                   //日志记录数据
        $message_scheme             = StaffService::getInstance()->getLeaderMessageScheme();                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        //leader的push消息跳转地址

        //以活动维度，获取leader首次消息记录的时间
        $activity_ids         = array_values(array_unique(array_column($leader_list, 'activity_id')));
        $leader_ids           = array_values(array_unique(array_column($leader_list, 'leader_id')));
        $leader_send_log_list = $this->getKpiLeaderMsgSendLogByLeaderIds($activity_ids, $leader_ids, $staff_ids);

        foreach ($leader_list as $key => $leader) {
            $first_send_leader_log_date = ''; //date('Y-m-d');
            $cut_off_time = '';
            //获取活动信息
            $push_activity_info        = $activity_info_arr[$leader['activity_id']];
            $activity_configs          = json_decode($push_activity_info['config'], true);
            $leader_time_node_config   = $activity_configs['time_node']['leader'];     // 限制打卡

            //组装发送站内信的数据
            $staff_users[$leader['activity_id']]['ids'][]         = ['id' => $leader['leader_id']];
            $staff_users[$leader['activity_id']]['activity_info'][$key] = $push_activity_info;
            $staff_users[$leader['activity_id']]['activity_info'][$key]['leader_id'][] = ['id' => $leader['leader_id']];

            $title_key   = KpiActivityStaffEnums::STAFF_KPI_REMIND_LEADER_TITLE;
            $content_key = KpiActivityStaffEnums::STAFF_KPI_REMIND_LEADER_CONTENT;
            if ($leader_time_node_config['on-off'] == 1) {
                // 开启限制打卡文案
                $content_key = KpiActivityStaffEnums::STAFF_KPI_REMIND_LEADER_CONTENT_1;
            }

            $leader_send_log = $leader_send_log_list[$leader['activity_id']][$leader['leader_id']] ?? [];
            if (!empty($leader_send_log)) {
                $first_send_leader_log_date = date("Y-m-d", strtotime($leader_send_log['created_at']));
                // 计算最后限制打卡的时间
                $cut_off_time = date("Y-m-d", strtotime("+".(int)$leader_time_node_config['day']." days",
                    strtotime($first_send_leader_log_date)));
            }

            if (empty($first_send_leader_log_date)) {
                $content_key = KpiActivityStaffEnums::STAFF_KPI_REMIND_LEADER_CONTENT_2;
            }

            $staff_users[$leader['activity_id']]['activity_info'][$key]['cut_off_time'] = $cut_off_time;
            $staff_users[$leader['activity_id']]['activity_info'][$key]['title_key']    = $title_key;
            $staff_users[$leader['activity_id']]['activity_info'][$key]['content_key']  = $content_key;
            $log                                                                  = "正在给活动ID为：[".$push_activity_info['id']."],直线上级ID为：[".$leader['leader_id']."]发送push".PHP_EOL;
            $this->logger->info($log);
            echo $log;
            //发送push
            $push_service->sendPush(
                $leader['leader_id'],
                $push_activity_info,
                $title_key,
                $content_key,
                $message_scheme,
                $cut_off_time
            );
        }

        $send_count = 0;
        //定制模版内容
        foreach ($staff_users as $key => $users) {
            $log = "正在给活动ID为：[".$key."],批量发送消息，提醒上级指定kpi，直线上级ID组为：[".implode(",",
                    array_column($users['ids'], "id"))."]".PHP_EOL;
            $this->logger->info($log);
            echo $log;
            //$activity_info = $users['activity_info'];
            foreach ($users['activity_info'] AS $activity_info) {
                $msg_info      = [
                    'years'  => $activity_info['years'],
                    'period' => $activity_info['period'],
                    'date'   => $activity_info['cut_off_time'],
                ];
                $title         = $push_service->getFormatMsg($activity_info['title_key'], $msg_info);
                $content       = $push_service->getFormatMsg($activity_info['content_key'], $msg_info, '<br>');
                $content       = KpiActivityStaffEnums::MSG_STYLE_BEGIN.$content.KpiActivityStaffEnums::MSG_STYLE_END;
                //按照活动类别批量发送站内信
                $push_service->sendMessage($activity_info['leader_id'], $title, $content,
                    KpiActivityStaffEnums::MESSAGE_CATEGORY_KPI_LEADER);
            }
            $send_count += count($users['ids']);
        }
        return $send_count;
    }

    /**
     * @param array $activity_ids
     * @param array $leader_ids
     * @return array
     */
    public function getKpiLeaderMsgSendLogByLeaderIds(array $activity_ids, array $leader_ids, array $staff_ids = []): array
    {
        $list = [];
        if (empty($activity_ids) || empty($leader_ids)) {
            return $list;
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->columns(
            'klm.id, klm.activity_id, klm.staff_id, klm.leader_id, klm.leader_name, klm.created_at'
        );
        $builder->from(['klm' => KpiLeaderMsgSendLogModel::class]);
        $builder->andWhere('klm.activity_id IN ({activity_ids:array})', ['activity_ids' => $activity_ids]);
        $builder->andWhere('klm.leader_id IN ({leader_ids:array})', ['leader_ids' => $leader_ids]);

        if (!empty($staff_ids)) {
            $builder->andWhere('klm.staff_id IN ({staff_ids:array})', ['staff_ids' => $staff_ids]);
        }

        $builder->orderBy('klm.created_at ASC');
        $data = $builder->getQuery()->execute()->toArray();
        if (empty($data) || !is_array($data)) {
            return $list;
        }

        foreach ($data as $key => $val) {
            // sql 中的group by 可以实现去重 ，要实现去重后剩余的数据为创建时间最小的数据，需要临时表或者子查询，这里还是利用程序处理吧
            // sql 语句是按照时间降序，这里这里只是收入第一条数据 也就是created_at 时间最小的数据
            if (isset($list[$val['activity_id']][$val['leader_id']]) && !empty($list[$val['activity_id']][$val['leader_id']])) {
               continue;
            }
            $list[$val['activity_id']][$val['leader_id']] = $val;
        }
        return $list;
    }
}