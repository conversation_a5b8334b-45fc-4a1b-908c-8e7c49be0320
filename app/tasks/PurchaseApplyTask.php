<?php

use App\Modules\Common\Models\EnvModel;
use App\Modules\Purchase\Models\PurchaseApply;
use App\Modules\Purchase\Models\PurchaseApplyProduct;
use App\Modules\Purchase\Models\PurchaseOrder;
use App\Modules\Purchase\Models\PurchaseOrderProduct;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Library\Exception\BusinessException;
use App\Modules\Purchase\Services\PayFlowService;
use App\Library\Enums;
use App\Library\Enums\PurchaseEnums;
use App\Modules\Purchase\Services\RemindService;
use App\Modules\Common\Services\EnumsService;

class PurchaseApplyTask extends BaseTask{

    public function flushAction(){

        $list = \App\Modules\Purchase\Models\PurchaseApply::find();

        foreach ($list as $item){
            $products = $item->getProducts();

            $amount = 0;
            foreach ($products as $product){
                $amount = bcadd($amount,$product->all_total);
            }

            if($amount!=$item->amount){
                $item->amount = $amount;
                $item->save();

                echo $item->id." 修复".PHP_EOL;
            }
        }
    }

    public function freeAction($params)
    {
        $purchase_apply_id = $params[0] ?? '';
        $exec_auth = $params[1] ?? '';
        if ($exec_auth != 'fe_tmp') {
            exit(PHP_EOL.'^_^'.PHP_EOL.PHP_EOL);
        }

        $res = \App\Modules\Purchase\Services\ApplyService::getInstance()->freedBudget($purchase_apply_id, []);

        echo '执行结果: ', PHP_EOL;
        var_dump($res);
    }

    /**
     * php app/cli Purchase_apply cancel PUR202103170106
     * @param $params
     */
    public function cancelAction($params)
    {
        $service = (new PayFlowService(Enums::WF_PURCHASE_APPLY));
        $apply = PurchaseApply::findFirst(
            [
                'pano = ?0',
                'bind' => [$params[0]]
            ]
        );
        if(empty($apply)){
            var_dump($params[0]." empty");
        }
        var_dump(
            $service->cancel($apply->id, '', ['id' => $apply->create_id, 'name' => $apply->create_name, 'nick_name' => ''])
        );

    }

    /**
     * php app/cli Purchase_apply sync_vat_rate
     * @param $params
     */
    public function sync_vat_whtAction()
    {
        $list = \App\Modules\Purchase\Models\PurchaseApplyProduct::find();
        foreach ($list as $item){
            if(!empty($item->total_price)){
                $item->vat7_rate = round($item->vat7/$item->total_price,2)*100;
            }else {
                $item->vat7_rate = 0;
            }
            $item->save();
        }
        echo "采购申请单vat7_rate字段数据处理完成".PHP_EOL;

        $list = \App\Modules\Purchase\Models\PurchaseOrderProduct::find();
        foreach ($list as $item){
            if(!empty($item->total_price)){
                $item->vat7_rate = round($item->vat7/$item->total_price,2)*100;
            }else {
                $item->vat7_rate = 0;
            }
            $item->save();
        }
        echo "采购订单vat7_rate字段数据处理完成".PHP_EOL;

        $list = \App\Modules\Purchase\Models\PurchasePaymentReceipt::find();
        foreach ($list as $item){
            if(!empty($item->total_price)){
                $item->vat7_rate = round($item->tax_ratio/$item->total_price,2)*100;
            }else {
                $item->vat7_rate = 0;
            }
            $item->save();
        }
        echo "采购付款申请订单vat7_rate字段数据处理完成".PHP_EOL;
    }

    /**
     * 每天早上发送前一天审批通过的采购申请数据到采购组邮箱
     * @date 2022/6/24
     */
    public function send_mail_audit_passAction(){
        $last_date = date('Y-m-d',strtotime('-1 day'));
        $start_date = $last_date.' 00:00:00';
        $end_date = $last_date.' 23:59:59';
        //查询前一天审批通过的数据
        $apply = PurchaseApply::find(
            [
                'status = :status: and approve_at>=:start_date: and approve_at<=:end_date:',
                'bind' => ['status'=>3,'start_date'=>$start_date,'end_date'=>$end_date]
            ]
        )->toArray();
        if (empty($apply)){
            echo "采购申请单待处理数据为空 start_date:".$start_date.' end_date:'.$end_date.' ,执行时间:'.date('Y-m-d H:i:s').PHP_EOL;
            $this->logger->info('purchase_apply_send_mail 数据为空, start_date:'.$start_date.' end_date:'.$end_date.' ,执行时间:'.date('Y-m-d H:i:s'));
            exit;
        }
        //获取订单号
        $pa_nos = array_column($apply,'pano');
        $emails = EnvModel::getEnvByCode('procurement_email');
        if (empty($emails)){
            echo "采购组邮箱为空 start_date:".$start_date.' end_date:'.$end_date.' ,执行时间:'.date('Y-m-d H:i:s').PHP_EOL;
            $this->logger->info('purchase_apply_send_mail 采购组邮箱为空, start_date:'.$start_date.' end_date:'.$end_date.' ,执行时间:'.date('Y-m-d H:i:s'));
            exit;
        }
        $emails = explode(',',$emails);
        $title = '审批已通过的采购申请单 Approved Purchase Requisition';
        $html = "Hi,<br/>";
        $html .= "<p>以下采购申请单已审批通过，请及时跟进，谢谢！</p>";
        $html .= "<p>Below Purchase Requisition were approved in system, please have a check!</p></br></br>";
        foreach ($pa_nos as $no){
            $html .= "<p>{$no}</p>";
        }
        $send_res = $this->mailer->sendAsync($emails, $title, $html);
        if($send_res) {
            echo "采购申请单邮件发送成功,收件邮箱:".implode(',',$emails).", start_date:".$start_date.' end_date:'.$end_date.' ,执行时间:'.date('Y-m-d H:i:s').PHP_EOL;
            $this->logger->info('purchase_apply_send_mail 发送成功,收件邮箱'.implode(',',$emails).", start_date:".$start_date.' end_date:'.$end_date.' ,执行时间:'.date('Y-m-d H:i:s'));
        } else {
            echo "采购申请单邮件发送失败,收件邮箱:".implode(',',$emails).", start_date:".$start_date.' end_date:'.$end_date.' ,执行时间:'.date('Y-m-d H:i:s').PHP_EOL;
            $this->logger->warning("purchase_apply_send_mail 发送失败,收件邮箱:".implode(',',$emails).", start_date:".$start_date.' end_date:'.$end_date.' ,执行时间:'.date('Y-m-d H:i:s'));
        }

    }

    /**
     * 处理采购订单历史数据的 order_use_total 字段值
     * 并 同步 维护 采购订单主表的is_close 和 execute_status 字段状态
     *
     * console command: php app/cli.php purchase_apply cal_order_use_total
     *
     * @document=https://flashexpress.feishu.cn/docx/VvIxdYDNwoKV9Lxp6ekcSRnpnjg
     * @param array $args 外部参数
     */
    public function cal_order_use_totalAction($args = [])
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log = '任务名称: ' . $process_name . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s')  . PHP_EOL;

        // 默认日志类型
        $logger_type = 'info';

        // 收集SQL处理失败的日志
        $fail_log = '';

        try {
            // 获取采购申请单历史数据的截止日期
            $apply_end_date = $args[0] ?? '';
            Validation::validate(['apply_end_date' => $apply_end_date], ['apply_end_date' => 'Required|Date|>>>:申请截止日期错误']);

            $log .= '截止日期: ' . $apply_end_date . PHP_EOL;

            // 1. 取截止日期内 且 审批通过的 采购申请单
            $apply_models = PurchaseApply::find([
                'conditions' => 'apply_date <= :apply_date: AND status = :status:',
                'bind' => ['apply_date' => $apply_end_date, 'status' => Enums::WF_STATE_APPROVED]
            ]);

            if (empty($apply_models->toArray())) {
                throw new ValidationException('采购申请单数据为空', ErrCode::$VALIDATE_ERROR);
            }

            $log .= '待处理申请单数量: ' . count($apply_models) . PHP_EOL;
            foreach ($apply_models as $index => $apply) {
                // 2.  采购申请单明细
                $apply_products = $apply->getProducts();
                if (empty($apply_products->toArray())) {
                    $log .= $apply->pano . ' 申请单明细数据为空, 跳过' . PHP_EOL;
                    continue;
                }

                // 3. 计算明细的 order_use_total
                // 取 审批状态是 待审核 / 已通过 purchase_order 的 purchase_order_product . total字段 之和
                $builder = $this->modelsManager->createBuilder();
                $builder->from(['main' => PurchaseOrder::class]);
                $builder->leftjoin(PurchaseOrderProduct::class, 'main.id = order_product.poid', 'order_product');
                $builder->where('main.pa_id = :pa_id:', ['pa_id' => $apply->id]);
                $builder->inWhere('main.status', [Enums::WF_STATE_PENDING, Enums::WF_STATE_APPROVED]);
                $builder->columns([
                    'order_product.apply_product_id',
                    'SUM(order_product.total) AS order_product_total'
                ]);
                $builder->groupBy('order_product.apply_product_id');
                $link_order_prudocts = $builder->getQuery()->execute()->toArray();

                // 申请单所有行的数量合计
                $apply_all_total_sum = 0;

                // 申请单所有行的订单使用数量合计
                $apply_all_order_use_total_sum = 0;

                // 申请行有关联订单行, 进行计算并更新
                if (!empty($link_order_prudocts)) {
                    $link_order_prudocts = array_column($link_order_prudocts, 'order_product_total', 'apply_product_id');

                    // 4. 更新明细的 order_use_total
                    foreach ($apply_products as $product) {
                        $apply_all_total_sum += $product->total;

                        // 申请行 有 关联的 订单行, 再更新
                        if (isset($link_order_prudocts[$product->id])) {
                            $product->order_use_total = $link_order_prudocts[$product->id];
                            if ($product->save() === false) {
                                $fail_log .= "申请单行save失败: UPDATE purchase_apply_product SET order_use_total={$product->order_use_total} WHERE id={$product->id}; 原因可能是: " . get_data_object_error_msg($product) . PHP_EOL;
                            }

                            $apply_all_order_use_total_sum += $product->order_use_total;
                        }
                    }
                }

                // 5. 维护采购申请单的 is_close / execute_status 字段
                if ($apply_all_order_use_total_sum == 0) {
                    // 如果没有被订单使用过,考虑到历史数据未来要程序维护, 所以execute_status更新一下
                    // is_close从0改成2未关闭, 0是历史数据本次修复历史数据这里也得更新一下
                    $apply->execute_status = PurchaseEnums::APPLY_EXECUTE_STATUS_NO;
                    if ($apply->is_close == PurchaseEnums::IS_CLOSE_HISTORY) {
                        $apply->is_close = PurchaseEnums::IS_CLOSE_NO;
                    }

                } else if ($apply_all_order_use_total_sum >= $apply_all_total_sum) {
                    // 如果完全生成po, 关闭状态改为已关闭
                    $apply->execute_status = PurchaseEnums::APPLY_EXECUTE_STATUS_DONE;
                    $apply->is_close = PurchaseEnums::IS_CLOSE_YES;

                } else {
                    // po使用数量大于0且小于总量 : 部分生成po
                    $apply->execute_status = PurchaseEnums::APPLY_EXECUTE_STATUS_PARTLY;

                    // 正常情况无需修该is_close假如人工设置为已关闭,这里判断部分生成po改成未关闭就错了
                    // 但本次属于历史数据修复都应该从0改成正确的值
                    if ($apply->is_close == PurchaseEnums::IS_CLOSE_HISTORY) {
                        $apply->is_close = PurchaseEnums::IS_CLOSE_NO;
                    }
                }

                if ($apply->save() === false) {
                    $fail_log .= "申请单save失败: UPDATE purchase_apply SET is_close={$apply->is_close}, execute_status={$apply->execute_status} WHERE id={$apply->id}; 原因可能是: " . get_data_object_error_msg($apply) . PHP_EOL;
                }

                // 处理1000个申请单, 歇10秒
                if ($index % 1000 == 0) {
                    sleep(10);
                }
            }

            $log .= '处理完毕' . PHP_EOL;

        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;

        } catch (BusinessException $e) {
            $log .= $e->getMessage() . PHP_EOL;
            $logger_type = 'warning';

        } catch (Exception $e) {
            $log .= $e->getMessage() . PHP_EOL;
            $logger_type = 'error';
        }

        if (!empty($fail_log)) {
            $this->logger->error('SQL执行异常的日志-cal_order_use_total_error_log' . PHP_EOL . $fail_log);
        }

        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $this->logger->$logger_type('数据维护-采购申请单-order_use_total: ' . $log);
        exit($log);
    }

    /**
     * 采购经理/集团采购总监[给审批人发送待审批采购申请单短信提醒]
     * 采购审批KPI：当天下午15点之前到达的审批，当天晚上24点之前审核完成，15点之后的审批，隔天上午12点之前审批完成
     *
     * 每周一到周五当地时间晚上18:00/上午11:00
     * @param $params
     */
    public function remind_purchaseAction($params)
    {
        $this->checkLock(__METHOD__);
        $is_exception = false;
        $log = 'purchase_apply remind_purchase ' . PHP_EOL;
        $log .= date('Ymd H:i:s') . ' 脚本开始执行' . PHP_EOL;
        try {
            $hours = $params[0] ? $params[0] : date('H');
            if ($hours == 18) {
                $updated_at_start = date('Y-m-d 00:00:00');
                $updated_at_end = date('Y-m-d 15:00:00');
                $msg_content_key = 'purchase_pending_remind_night';
            } else if ($hours == 11) {
                $updated_at_start = date('Y-m-d 15:00:00', strtotime('-1 days'));
                $updated_at_end = date('Y-m-d 00:00:00');
                $msg_content_key = 'purchase_pending_remind_am';
            } else {
                $log .= 'remind_purchase: 只能设置为每周一到周五当地时间晚上晚上18:00/上午11:00跑'. PHP_EOL;
                $log .= date('Y-m-d H:i:s'). ' 脚本执行结束'. PHP_EOL;
                $this->clearLock(__METHOD__);
                exit($log);
            }
            $country_code = get_country_code();

            //采购经理【未发起过征询】
            $current_node_auditor_id = RemindService::getInstance()->getRemindPurchaseData(Enums::WF_PURCHASE_APPLY, Enums::WF_NODE_TAG_PM, $updated_at_start, $updated_at_end);
            if (empty($current_node_auditor_id)) {
                //采购经理【发起过征询】
                $current_node_auditor_id = RemindService::getInstance()->getRemindPurchaseFyrData(Enums::WF_PURCHASE_APPLY, Enums::WF_NODE_TAG_PM, $updated_at_start, $updated_at_end);
            }
            if (!empty($current_node_auditor_id)) {
                //存在满足条件的采购经理
                $log .= RemindService::getInstance()->sendRemindPendingMsg($current_node_auditor_id, RemindService::SEND_MSG_TYPE_PENDING, ['biz_type' => Enums::WF_PURCHASE_APPLY, 'remind_time' => $msg_content_key, 'country_code' => $country_code]);
            } else {
                $log .= '暂无待审批数据，无需给采购经理发送采购申请单待审批短信提醒' . PHP_EOL;
            }

            //集团采购总监【未发起过征询】
            $current_node_auditor_id = RemindService::getInstance()->getRemindPurchaseData(Enums::WF_PURCHASE_APPLY, Enums::WF_NODE_TAG_GROUP_PURCHASE_DIRECTOR, $updated_at_start, $updated_at_end);
            if (empty($current_node_auditor_id)) {
                //集团采购总监【发起过征询】
                $current_node_auditor_id = RemindService::getInstance()->getRemindPurchaseFyrData(Enums::WF_PURCHASE_APPLY, Enums::WF_NODE_TAG_GROUP_PURCHASE_DIRECTOR, $updated_at_start, $updated_at_end);
            }
            if (!empty($current_node_auditor_id)) {
                //存在满足条件的集团采购总监
                $log .= RemindService::getInstance()->sendRemindPendingMsg($current_node_auditor_id, RemindService::SEND_MSG_TYPE_PENDING, ['biz_type' => Enums::WF_PURCHASE_APPLY, 'remind_time' => $msg_content_key, 'country_code' => $country_code]);
            } else {
                $log .= '暂无待审批数据，无需给集团采购总监发送采购申请单待审批短信提醒' . PHP_EOL;
            }
        } catch (Exception $e) {
            $is_exception = true;
            $log .= date('Ymd H:i:s') . ' 给采购经理/采购总监发送采购申请单待审批短信提醒失败[异常]' . $e->getMessage() . PHP_EOL;
        }
        $log .= date('Ymd H:i:s') . ' 脚本执行结束' . PHP_EOL;

        if ($is_exception) {
            $this->logger->warning('purchase_apply_remind_purchase_task_exception:' . $log);
        } else {
            $this->logger->info($log);
        }
        $this->clearLock(__METHOD__);
        exit($log);
    }

    /**
     * 资产经理[给审批人发送待审批采购申请单短信提醒]
     * 资产审批KPI：当天下午18点之前到达的审批，当天晚上24点之前审核完成，18点之后的审批，隔天上午12点之前审批完成
     *
     * 每周一到周五当地时间晚上19:00/上午11:00
     * @param $params
     */
    public function remind_asset_mangerAction($params)
    {
        $this->checkLock(__METHOD__);

        $is_exception = false;
        $log = 'purchase_apply remind_asset_manger ' . PHP_EOL;
        $log .= date('Ymd H:i:s') . ' 脚本开始执行' . PHP_EOL;
        try {
            $hours = $params[0] ? $params[0] : date('H');
            if ($hours == 19) {
                $updated_at_start = date('Y-m-d 00:00:00');
                $updated_at_end = date('Y-m-d 18:00:00');
                $msg_content_key = 'purchase_pending_remind_night';
            } else if ($hours == 11) {
                $updated_at_start = date('Y-m-d 18:00:00', strtotime('-1 days'));
                $updated_at_end = date('Y-m-d 00:00:00');
                $msg_content_key = 'purchase_pending_remind_am';
            } else {
                $log .= 'remind_purchase: 只能设置为每周一到周五当地时间晚上19:00/上午11:00跑'. PHP_EOL;
                $log .= date('Y-m-d H:i:s'). ' 脚本执行结束'. PHP_EOL;
                $this->clearLock(__METHOD__);
                exit($log);
            }
            //资产经理【未发起过征询】
            $current_node_auditor_id = RemindService::getInstance()->getRemindPurchaseData(Enums::WF_PURCHASE_APPLY, Enums::WF_NODE_TAG_ASSET_MANAGER, $updated_at_start, $updated_at_end);

            if (empty($current_node_auditor_id)) {
                //资产经理【发起过征询】
                $current_node_auditor_id = RemindService::getInstance()->getRemindPurchaseFyrData(Enums::WF_PURCHASE_APPLY, Enums::WF_NODE_TAG_ASSET_MANAGER, $updated_at_start, $updated_at_end);
            }
            
            if (!empty($current_node_auditor_id)) {
                //存在满足条件的资产经理
                $log .= RemindService::getInstance()->sendRemindPendingMsg($current_node_auditor_id, RemindService::SEND_MSG_TYPE_PENDING, ['biz_type' => Enums::WF_PURCHASE_APPLY, 'remind_time' => $msg_content_key, 'country_code' => get_country_code()]);
            } else {
                $log .= '暂无待审批数据，无需给资产经理发送采购申请单待审批短信提醒' . PHP_EOL;
            }
        } catch (Exception $e) {
            $is_exception = true;
            $log .= date('Ymd H:i:s') . ' 给资产经理发送采购申请单待审批短信提醒失败[异常]' . $e->getMessage() . PHP_EOL;
        }
        $log .= date('Ymd H:i:s') . ' 脚本执行结束' . PHP_EOL;

        if ($is_exception) {
            $this->logger->warning('purchase_apply_remind_asset_manger_task_exception:' . $log);
        } else {
            $this->logger->info($log);
        }
        $this->clearLock(__METHOD__);
        exit($log);
    }

    /**
     * 每周一上午当地时间10点给采购部领导发短信，发邮件
     * 获取以下PUR/PO读取采购经理/集团采购总监的上周一到周日的审批时效
     */
    public function remind_purchase_departmentAction()
    {
        $this->checkLock(__METHOD__);
        $is_exception = false;
        $log = 'purchase_apply remind_purchase_department ' . PHP_EOL;
        $log .= date('Ymd H:i:s') . ' 脚本开始执行' . PHP_EOL;
        try {
            //获取采购部提醒人员名单
            $staff_ids = EnumsService::getInstance()->getSettingEnvValue('purchase_remind_purchase_department_staff_ids');
            if (!empty($staff_ids)) {
                //获取需要提醒采购申请单列表
                $purchase_apply_list = RemindService::getInstance()->getRemindPurchaseList([Enums::WF_PURCHASE_APPLY], [Enums::WF_NODE_TAG_PM, Enums::WF_NODE_TAG_GROUP_PURCHASE_DIRECTOR], RemindService::SEND_MSG_TYPE_PURCHASE_DEPARTMENT);

                //获取需要提醒采购订单列表
                $purchase_order_list = RemindService::getInstance()->getRemindPurchaseList([Enums::WF_PURCHASE_ORDER], [Enums::WF_NODE_TAG_PM, Enums::WF_NODE_TAG_GROUP_PURCHASE_DIRECTOR], RemindService::SEND_MSG_TYPE_PURCHASE_DEPARTMENT);

                $purchase_list = array_merge($purchase_apply_list['purchase_list'], $purchase_order_list['purchase_list']);
                if (!empty($purchase_list)) {
                    //模板替换内容信息组
                    $content_params = ['country_code' => get_country_code(), 'apply_num' => $purchase_apply_list['apply_num'], 'order_num' => $purchase_order_list['apply_num']];

                    //发送短信
                    $log .= RemindService::getInstance()->sendRemindPendingMsg($staff_ids, RemindService::SEND_MSG_TYPE_PURCHASE_DEPARTMENT, $content_params);

                    //发送邮件
                    $log .= RemindService::getInstance()->sendEmailToLeader(explode(',', $staff_ids), $purchase_list, RemindService::SEND_MSG_TYPE_PURCHASE_DEPARTMENT, $content_params);

                } else {
                    $log .= '无需给采购部领导发短信/邮件提醒' . PHP_EOL;
                }
            } else {
                $log .= '未配置提醒的采购部领导工号' . PHP_EOL;
            }
        } catch (Exception $e) {
            $is_exception = true;
            $log .= date('Ymd H:i:s') . ' 给采购部领导发短信/邮件提醒失败[异常]' . $e->getMessage() . PHP_EOL;
        }
        $log .= date('Ymd H:i:s') . ' 脚本执行结束' . PHP_EOL;

        if ($is_exception) {
            $this->logger->warning('purchase_apply_remind_purchase_department_task_exception:' . $log);
        } else {
            $this->logger->info($log);
        }
        $this->clearLock(__METHOD__);
        exit($log);
    }

    /**
     * 每周一上午当地时间10点给资产部领导发短信，发邮件
     * 获取以下PUR读取资产经理的上周一到周日的审批时效
     */
    public function remind_asset_departmentAction()
    {
        $this->checkLock(__METHOD__);
        $is_exception = false;
        $log = 'purchase_apply remind_asset_department ' . PHP_EOL;
        $log .= date('Ymd H:i:s') . ' 脚本开始执行' . PHP_EOL;
        try {
            //获取资产部提醒人员名单
            $staff_ids = EnumsService::getInstance()->getSettingEnvValue('purchase_remind_asset_department_staff_ids');
            if (!empty($staff_ids)) {
                //获取需要提醒采购订单列表
                $purchase_apply_list = RemindService::getInstance()->getRemindPurchaseList([Enums::WF_PURCHASE_APPLY], [Enums::WF_NODE_TAG_ASSET_MANAGER], RemindService::SEND_MSG_TYPE_ASSET_DEPARTMENT);
                $purchase_list = $purchase_apply_list['purchase_list'];
                if (!empty($purchase_list)) {
                    //模板替换内容信息组
                    $content_params = ['country_code' => get_country_code(), 'apply_num' => $purchase_apply_list['apply_num']];

                    //发送短信
                    $log .= RemindService::getInstance()->sendRemindPendingMsg($staff_ids, RemindService::SEND_MSG_TYPE_ASSET_DEPARTMENT, $content_params);

                    //发送邮件
                    $log .= RemindService::getInstance()->sendEmailToLeader(explode(',', $staff_ids), $purchase_list, RemindService::SEND_MSG_TYPE_ASSET_DEPARTMENT, $content_params);
                } else {
                    $log .= '无需给资产部领导发短信/邮件提醒' . PHP_EOL;
                }
            } else {
                $log .= '未配置提醒的资产部领导工号' . PHP_EOL;
            }
        } catch (Exception $e) {
            $is_exception = true;
            $log .= date('Ymd H:i:s') . ' 给资产部领导发短信/邮件提醒失败[异常]' . $e->getMessage() . PHP_EOL;
        }
        $log .= date('Ymd H:i:s') . ' 脚本执行结束' . PHP_EOL;

        if ($is_exception) {
            $this->logger->warning('purchase_apply_remind_asset_department_task_exception:' . $log);
        } else {
            $this->logger->info($log);
        }
        $this->clearLock(__METHOD__);
        exit($log);
    }
}