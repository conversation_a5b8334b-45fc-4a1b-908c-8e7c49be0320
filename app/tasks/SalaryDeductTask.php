<?php

use App\Library\Enums\SalaryDeductEnums;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Loan\Models\Loan;
use App\Modules\Loan\Models\LoanPay;
use App\Models\oa\LoanReturnModel;
use App\Modules\Reimbursement\Models\Reimbursement;
use App\Modules\Reimbursement\Models\ReimbursementRelLoan;
use App\Modules\ReserveFund\Models\ReserveFundApply;
use App\Models\oa\ReserveFundApplyReturnRelModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Modules\ReserveFund\Models\ReserveFundReturn;
use App\Models\oa\SalaryDeductBatchModel;
use  App\Models\oa\SalaryDeductTaskModel;
use App\Models\oa\SalaryDeductBatchUserModel;
use App\Models\oa\SalaryDeductBatchUserOrderModel;
use App\Models\oa\ImportTaskModel;
use App\Library\BaseService;
use App\Util\RedisKey;
use App\Modules\ReserveFund\Services\BaseService as ReserveFundBaseService;
use App\Modules\SalaryDeduct\Services\DeductService;
use App\Library\Enums;
use App\Library\Exception\BusinessException;
use App\Library\ErrCode;
use App\Library\OssHelper;

/**
 * 薪资抵扣
 * Class SalaryDeductTask
 */
class SalaryDeductTask extends BaseTask
{
    /**
     * 薪资抵扣数据源生成（目前是借款、备用金）
     * 脚本执行时间和频率：每月固定生成两次。（当月6日2点、当月21日2点）
     */
    public function mainAction()
    {
        $now = date('Y-m-d H:i:s');
        echo $now.' 脚本开始执行'.PHP_EOL;
        $db = $this->getDI()->get('db_oa');
        try {
            //查询是否有待处理的task
            $current_date = date('Y-m-d H:i:s');
            if ($current_date >= date('Y-m-06 02:00:00') && $current_date <= date('Y-m-09 23:00:00')) {
                $type = SalaryDeductEnums::SALARY_DEDUCT_TASK_TYPE_1;
            }

            if ($current_date >= date('Y-m-21 02:00:00') && $current_date <= date('Y-m-24 23:00:00')) {
                $type = SalaryDeductEnums::SALARY_DEDUCT_TASK_TYPE_2;
            }
            if (empty($type)) {
                echo date('Y-m-d H:i:s'). ' 脚本执行结束'. PHP_EOL;
                exit();
            }

            $task_detail = SalaryDeductTaskModel::findFirst([
                'conditions' => 'month = :month: and type = :type: and status = :status:',
                'bind'       => ['month' => date('Y-m'), 'type' => $type, 'status' => SalaryDeductEnums::SALARY_DEDUCT_TASK_STATUS_2],
            ]);

            if (empty($task_detail)) {
                echo '没有待处理task' . PHP_EOL;
                echo date('Y-m-d H:i:s') . ' 脚本执行结束' . PHP_EOL;
                exit();
            }





            //应扣日期[6日跑就是当月-10号；21日跑就是当月-25号]
            $today = date('d');
            $deduct_date = in_array($today, [6, 7, 8, 9]) ? date('Y-m-10') : (in_array($today, [21, 22, 23, 24]) ? date('Y-m-25') : '');
            if (empty($deduct_date)) {
                //应扣日期不对，说明脚本运行配置日期不对，不符合规则（6日、21日跑）
                $this->logger->warning('salary_deduct_task_time_set_error: 只能设置为每月6日、21日凌晨2点跑');
                echo 'salary_deduct_task_time_set_error: 只能设置为每月6日、21日凌晨2点跑'. PHP_EOL;
                echo date('Y-m-d H:i:s'). ' 脚本执行结束'. PHP_EOL;
                exit();
            }
            //筛选条件起始日期
            $start_time = '2023-03-06 00:00:00';
            //筛选条件截止日期
            if ($type == SalaryDeductEnums::SALARY_DEDUCT_TASK_TYPE_1) {
                $end_time = date('Y-m-10 00:00:00');
            } else {
                $end_time = date('Y-m-25 00:00:00');
            }
            //数据生成日期
            $created_at = $now;

            //当前币种信息
            $default_currency = (new EnumsService())->getSysCurrencyInfo();
            $currency = $default_currency['code'];

            //第一步先查询借款应扣明细
            $user_loan_order = $this->findLoan($start_time, $end_time, $deduct_date, $currency, $created_at);

            //第二步查询备用金应扣明细
            [$user_reserve_fund_order, $user_reserve_fund_return] = $this->findReserveFund($start_time, $end_time, $deduct_date, $currency, $created_at);

            //找到应扣明细，则开始生成薪资抵扣数据
            if (!empty($user_loan_order) || !empty($user_reserve_fund_order)) {
                $db->begin();

                //第三步创建批次任务
                $deduct_date_no = date('Ymd', strtotime($deduct_date));
                $no = Enums\SalaryDeductEnums::SALARY_DEDUCT_PREFIX . $deduct_date_no;
                $salary_deduct_batch = new SalaryDeductBatchModel();
                $batch_data = [
                    'no' => $no,//批次编号
                    'deduct_date' => $deduct_date,//应扣日期
                    'status' => Enums\SalaryDeductEnums::SALARY_DEDUCT_BATCH_STATUS_UN_UPLOAD,//未上传
                    'actual_deduct_at' => NULL,//扣款反馈时间
                    'created_at' => $created_at,
                    'updated_at' => $created_at
                ];
                $bool = $salary_deduct_batch->i_create($batch_data);
                if ($bool === false) {
                    $db->rollback();
                    throw new BusinessException('薪资抵扣总数据-生成【批次任务】失败=' . json_encode($batch_data, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($salary_deduct_batch), ErrCode::$BUSINESS_ERROR);
                }
                //批次id
                $batch_id = $salary_deduct_batch->id;
                echo date('Y-m-d H:i:s') . '  批次任务已生成'.PHP_EOL;

                //第四步组装薪资抵扣总数据-批次员工明细组并入库
                $salary_deduct_batch_user_data = [];
                $user_order = array_merge($user_loan_order, $user_reserve_fund_order);
                foreach ($user_order as $order) {
                    if (isset($salary_deduct_batch_user_data[$order['staff_id']])) {
                        //只要有一条是全额抵扣，就是全额
                        $user_deduct_type = $salary_deduct_batch_user_data[$order['staff_id']]['deduct_type'];
                        $salary_deduct_batch_user_data[$order['staff_id']]['deduct_type'] = (($user_deduct_type == Enums\SalaryDeductEnums::SALARY_DEDUCT_TYPE_LIMIT && $order['deduct_type'] == Enums\SalaryDeductEnums::SALARY_DEDUCT_TYPE_ALL) || $user_deduct_type == Enums\SalaryDeductEnums::SALARY_DEDUCT_TYPE_ALL) ? Enums\SalaryDeductEnums::SALARY_DEDUCT_TYPE_ALL : Enums\SalaryDeductEnums::SALARY_DEDUCT_TYPE_LIMIT;
                        $salary_deduct_batch_user_data[$order['staff_id']]['deduct_amount'] = bcadd($salary_deduct_batch_user_data[$order['staff_id']]['deduct_amount'], $order['deduct_amount']);//累计应扣金额
                    } else {
                        $salary_deduct_batch_user_data[$order['staff_id']] = [
                            'batch_id' => $batch_id,//批次ID
                            'serial_number' => $no . $order['staff_id'],//UID编号
                            'staff_id' => $order['staff_id'],//员工工号
                            'staff_name' => $order['staff_name'],//员工姓名
                            'state' => $order['state'],//员工状态
                            'wait_leave_state' => $order['wait_leave_state'],//待离职状态0非待离职1待离职
                            'deduct_amount' => $order['deduct_amount'],//应扣金额
                            'actual_deduct_amount' => 0,//实扣金额
                            'company_name' => '',//扣款所属公司
                            'deduct_type' => $order['deduct_type'],//抵扣类型
                            'status' => Enums\SalaryDeductEnums::SALARY_DEDUCT_USER_STATUS_UN_START,//扣款状态
                            'actual_deduct_at' => NULL,//实扣数据生成时间
                            'created_at' => $created_at,
                            'updated_at' => $created_at,
                        ];
                    }
                }

                $final_salary_deduct_batch_user_data = [];
                $final_user_ids = [];
                foreach ($salary_deduct_batch_user_data  as $staff_id => $user_data) {
                    //若员工名下的应扣明细金额是0，则没必要去生成薪资抵扣总数据
                    if ($user_data['deduct_amount'] == 0) {
                        continue;
                    }
                    $final_user_ids[] = $staff_id;
                    $final_salary_deduct_batch_user_data[] = $user_data;
                }
                if (empty($final_salary_deduct_batch_user_data)) {
                    //不存在要生产的薪资抵扣总数据,则事务回滚
                    $db->rollback();
                    echo date('Y-m-d H:i:s') . ' 批次任务-暂无员工明细需要生成' . PHP_EOL;
                    echo date('Y-m-d H:i:s') . ' 本期无需生成薪资抵扣数据-批次任务已删除' . PHP_EOL;
                    echo date('Y-m-d H:i:s'). ' 脚本执行结束'. PHP_EOL;
                    exit();
                }
                //存在要生产的薪资抵扣总数据
                $salary_deduct_batch_user = new SalaryDeductBatchUserModel();
                $bool = $salary_deduct_batch_user->batch_insert($final_salary_deduct_batch_user_data);
                if ($bool === false) {
                    $db->rollback();
                    throw new BusinessException('薪资抵扣总数据-生成【批次员工明细】失败=' . json_encode($salary_deduct_batch_user_data, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($salary_deduct_batch_user), ErrCode::$BUSINESS_ERROR);
                }
                echo date('Y-m-d H:i:s') . '  批次任务-员工明细已生成'.PHP_EOL;

                //第五步存在借款的生成借款应扣明细并与批次绑定关系
                foreach ($user_loan_order as $key => &$loan) {
                    //若明细用户id不在最终需要生成薪资抵扣总数据的用户组里，也不需要生成借款明细
                    if (!in_array($loan['staff_id'], $final_user_ids)) {
                        unset($user_loan_order[$key]);
                        continue;
                    }
                    $loan['batch_id'] = $batch_id;
                }
                if (!empty($user_loan_order)) {
                    $salary_deduct_batch_user_order = new SalaryDeductBatchUserOrderModel();
                    $bool = $salary_deduct_batch_user_order->batch_insert($user_loan_order);
                    if ($bool === false) {
                        $db->rollback();
                        throw new BusinessException('薪资抵扣总数据-批次-生成【借款应扣明细】失败=' . json_encode($user_loan_order, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($salary_deduct_batch_user_order), ErrCode::$BUSINESS_ERROR);
                    }
                    echo date('Y-m-d H:i:s') . '  批次任务-借款应扣明细已生成'.PHP_EOL;
                }

                //第六步存在备用金的生成备用金应扣明细并与批次绑定关系
                foreach ($user_reserve_fund_order as $key => &$fund) {
                    //若明细用户id不在最终需要生成薪资抵扣总数据的用户组里，也不需要生成备用金明细
                    if (!in_array($fund['staff_id'], $final_user_ids)) {
                        unset($user_reserve_fund_order[$key]);
                        continue;
                    }
                    $fund['batch_id'] = $batch_id;
                }
                if (!empty($user_reserve_fund_order)) {
                    $salary_deduct_batch_user_order = new SalaryDeductBatchUserOrderModel();
                    $bool = $salary_deduct_batch_user_order->batch_insert($user_reserve_fund_order);
                    if ($bool === false) {
                        $db->rollback();
                        throw new BusinessException('薪资抵扣总数据-批次-生成【备用金应扣明细】失败=' . json_encode($user_reserve_fund_order, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($salary_deduct_batch_user_order), ErrCode::$BUSINESS_ERROR);
                    }
                    echo date('Y-m-d H:i:s') . '  批次任务-备用金应扣明细已生成'.PHP_EOL;

                    //第七步存在备用金归还单明细
                    if (!empty($user_reserve_fund_return)) {
                        //更新备用金申请单-归还状态-归还中
                        $reserve_apply_ids = array_unique(array_column($user_reserve_fund_order, 'order_id'));
                        $bool = $db->updateAsDict(
                            (new ReserveFundApply())->getSource(),
                            [
                                'return_status' => Enums\ReserveFundReturnEnums::BACK_STATUS_ING,
                                'updated_at' => $created_at
                            ],
                            ['conditions' => 'id in (' . implode(',', $reserve_apply_ids) .')']
                        );
                        if ($bool === false) {
                            $db->rollback();
                            throw new BusinessException('薪资抵扣总数据-批次-更新【备用金申请单归还状态】失败=' . json_encode(['status' => Enums\ReserveFundReturnEnums::BACK_STATUS_ING, 'ids' => $reserve_apply_ids], JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($db), ErrCode::$BUSINESS_ERROR);
                        }

                        //添加备用金申请单-备用金归还单-关系 && 备用金归还单信息
                        foreach ($user_reserve_fund_return as $return) {
                            $apply_id = $return['apply_id'];
                            //没有生成备用金应扣明细的也无需生成备用金归还单信息
                            if (!in_array($apply_id, $reserve_apply_ids)) {
                                continue;
                            }
                            unset($return['apply_id']);
                            //先插入归还明细，后绑定关系
                            $reserve_fund_return = new ReserveFundReturn();
                            $bool = $reserve_fund_return->i_create($return);
                            if ($bool === false) {
                                $db->rollback();
                                throw new BusinessException('薪资抵扣总数据-批次-生成【备用金归还明细】失败=' . json_encode($return, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($reserve_fund_return), ErrCode::$BUSINESS_ERROR);
                            }

                            //备用金申请单与归还单关联关系组
                            $rel_data = [
                                'apply_id' => $apply_id,
                                'return_id' => $reserve_fund_return->id,
                                'is_deleted' => Enums\GlobalEnums::IS_NO_DELETED,
                                'created_at' => $created_at
                            ];
                            $reserve_fund_apply_return_rel = new ReserveFundApplyReturnRelModel();
                            $bool = $reserve_fund_apply_return_rel->i_create($rel_data);
                            if ($bool === false) {
                                $db->rollback();
                                throw new BusinessException('薪资抵扣总数据-批次-生成【备用金申请单与归还单关联关系】失败=' . json_encode($rel_data, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($reserve_fund_apply_return_rel), ErrCode::$BUSINESS_ERROR);
                            }
                        }
                        echo date('Y-m-d H:i:s') . '  批次任务-备用金应扣明细-备用金申请单与归还单关联已生成'.PHP_EOL;
                    }
                }

                $task_detail->status = SalaryDeductEnums::SALARY_DEDUCT_TASK_STATUS_3;
                if ($task_detail->save() == false) {
                    $db->rollback();
                    throw new BusinessException('薪资抵扣总数据-批次-生成 task 状态修改失败=' . json_encode($rel_data, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($task_detail), ErrCode::$BUSINESS_ERROR);
                }

                $db->commit();
            }
        } catch (Exception $e) {
            $this->logger->warning('salary_deduct_task_exception:' . $e->getMessage());
            echo date('Y-m-d H:i:s') . ' 薪资抵扣数据源生成（目前是借款、备用金）[异常],操作已回滚' . $e->getMessage() . PHP_EOL;
        }
        echo date('Y-m-d H:i:s'). ' 脚本执行结束'. PHP_EOL;
        exit();
    }

    /**
     * 获取未归还借款列表【借款应扣明细】
     * @param string $start_time 起始时间
     * @param string $end_time  截止时间
     * @param string $deduct_date 应扣日期
     * @param integer $currency 币种
     * @param string $created_at 数据生成日期
     * @return array
     */
    private function findLoan($start_time, $end_time, $deduct_date, $currency, $created_at)
    {
        //最终符合条件的未归还借款单
        $final_loan_apply_list = [];
        //应扣明细单据
        $user_order = [];

        //若是每年的12月21日，截止日期为当年12月1日
        $today = date('m-d');
        if (in_array($today, ['12-22', '12-22', '12-23', '12-24'])) {
            $end_time = date('Y-12-01 00:00:00');
            $loan_days = 0;//不限制借款时长
            $deduct_type = Enums\SalaryDeductEnums::SALARY_DEDUCT_TYPE_ALL;//全额
        } else {
            //非每年12月21日，则需要取借款时长超过30天的数据 = 当前时间 - 付款日期(pay_date)；17275需求由30天调整为29天
            $loan_days = 29;
            $deduct_type = Enums\SalaryDeductEnums::SALARY_DEDUCT_TYPE_LIMIT;//限额
        }

        //找到时间范围内，审批通过、已支付、部分归还或未开始归还或超额归还的借款单
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['l' => Loan::class]);
        $builder->leftJoin(LoanPay::class, 'p.loan_id = l.id', 'p');
        $builder->columns('l.id, l.lno, l.create_id, l.create_name, l.create_node_department_id, l.create_node_department_name, l.create_date, l.amount, l.re_amount, l.back_amount, p.pay_date, (TO_DAYS(NOW()) - TO_DAYS(p.pay_date)) as loan_days');
        $builder->where('l.currency = :currency: and l.status = :status: and l.pay_status = :pay_status:', ['currency' => $currency, 'status' => Enums::WF_STATE_APPROVED, 'pay_status' => Enums::PAYMENT_PAY_STATUS_PAY]);
        $builder->inWhere('l.loan_status', [Enums\LoanEnums::LOAN_STATUS_NOT_START_RETURN, Enums\LoanEnums::LOAN_STATUS_PARTIAL_RETURN, Enums\LoanEnums::LOAN_STATUS_OVER_RETURN]);
        $builder->andWhere('p.pay_date >= :start_time: and p.pay_date < :end_time:', ['start_time' => $start_time, 'end_time' => $end_time]);
        if ($loan_days) {
            //需要限制借款时长范围
            $builder->andWhere('(TO_DAYS(NOW()) - TO_DAYS(p.pay_date)) > :loan_days:', ['loan_days' => $loan_days]);
        }
        $loan_apply_list = $builder->getQuery()->execute()->toArray();
        if (!empty($loan_apply_list)) {
            $staff_ids = array_values(array_unique(array_filter(array_column($loan_apply_list, 'create_id'))));
            $staff_list = HrStaffInfoModel::find([
                'conditions' => 'staff_info_id in ({staff_ids:array}) and state = :state:',
                'bind' => ['staff_ids' => $staff_ids, 'state' => Enums\StaffInfoEnums::STAFF_STATE_IN],
                'columns' => 'staff_info_id, state, wait_leave_state'
            ])->toArray();
            if (!empty($staff_list)) {
                $in_staff_ids = array_column($staff_list, 'staff_info_id');
                $in_staff_list = array_column($staff_list, null, 'staff_info_id');
                foreach ($loan_apply_list as $apply) {
                    if (!in_array($apply['create_id'], $in_staff_ids)) {
                        //非在职、待离职的过滤，跳过
                        continue;
                    }
                    $apply['state'] = $in_staff_list[$apply['create_id']]['state'];
                    $apply['wait_leave_state'] = $in_staff_list[$apply['create_id']]['wait_leave_state'];
                    $final_loan_apply_list[] = $apply;
                }
            }
        }

        //查找到满足条件的借款申请单
        if (!empty($final_loan_apply_list)) {
            //抵扣批次号
            $deduct_date_no = date('Ymd', strtotime($deduct_date));
            $serial_no = Enums\SalaryDeductEnums::SALARY_DEDUCT_USER_ORDER_LOAN_PREFIX . $deduct_date_no;

            //借款申请单ID组
            $loan_ids = array_column($final_loan_apply_list, 'id');

            //获取每笔借款下转账归还处理中申请归还金额
            $loan_return_amount = $this->getLoanReturnAmount($loan_ids);


            foreach ($final_loan_apply_list as $apply) {
                //应扣金额=借款单总金额-报销抵扣小计-归还金额小记-转账归还处理中申请归还金额+审批状态为审批中的报销金额。
                $back_amount = bcsub(bcsub(bcsub($apply['amount'], $apply['re_amount']), $apply['back_amount']), $loan_return_amount[$apply['id']] ?? 0);
                //借款应扣金额是0则不生成应扣明细
                if ($back_amount == 0) {
                    continue;
                }

                //借款应扣明细
                $user_order[] = [
                    'serial_no' => $serial_no,//抵扣批次号
                    'serial_number' => BaseService::genSerialNo(Enums\SalaryDeductEnums::SALARY_DEDUCT_USER_ORDER_LOAN_PREFIX, RedisKey::SALARY_DEDUCT_LOAN_ORDER_LOCK, 5, $deduct_date_no),//抵扣批次明细编号
                    'order_id' => $apply['id'],//借款/备用金申请表-ID
                    'order_no' => $apply['lno'],//借款/备用金申请表-单号
                    'staff_id' => $apply['create_id'],//申请人-员工工号
                    'staff_name' => $apply['create_name'],//申请人-员工姓名
                    'state' => $apply['state'],//员工状态
                    'wait_leave_state' => $apply['wait_leave_state'],//待离职状态0非待离职1待离职
                    'node_department_id' => $apply['create_node_department_id'],//申请人-部门ID
                    'node_department_name' => $apply['create_node_department_name'],//申请人-部门名字
                    'apply_date' => $apply['create_date'],//申请日期
                    'amount' => $apply['amount'],//申请金额
                    'bank_pay_date' => $apply['pay_date'],//借款单银行流水日期
                    'loan_days' => $apply['loan_days'],//借款单借款时长
                    'deduct_date' => $deduct_date,//应扣日期
                    'deduct_amount' => $back_amount,//应扣金额
                    'actual_deduct_amount' => 0,//实扣金额
                    'company_name' => '',//扣款所属公司
                    'deduct_type' => ($deduct_type == Enums\SalaryDeductEnums::SALARY_DEDUCT_TYPE_LIMIT && $apply['loan_days'] > 30 && $apply['loan_days'] < 60) ? Enums\SalaryDeductEnums::SALARY_DEDUCT_TYPE_LIMIT : Enums\SalaryDeductEnums::SALARY_DEDUCT_TYPE_ALL,//抵扣类型:每年12月21日或借款时长>=60天都是：2全额;否则都是：1限额
                    'source_type' => Enums\SalaryDeductEnums::SALARY_DEDUCT_USER_ORDER_SOURCE_TYPE_LOAN,//单据类型:借款
                    'status' => Enums\SalaryDeductEnums::SALARY_DEDUCT_USER_ORDER_STATUS_UN_CREATE,//实扣状态:未生成
                    'actual_deduct_at' => NULL,//实扣数据生成时间
                    'created_at' => $created_at,//应扣数据生成时间
                    'updated_at' => $created_at
                ];
            }
        }
        return $user_order;
    }

    /**
     * 获取每笔借款下转账归还处理中申请归还金额
     * @param array $loan_ids 借款单id组
     * @return array
     */
    private function getLoanReturnAmount($loan_ids)
    {
        $loan_return_list = LoanReturnModel::find([
            'conditions' => 'loan_id in ({loan_ids:array}) and back_status = :back_status: and back_type = :back_type:',
            'bind' => ['loan_ids' => $loan_ids, 'back_status' => Enums\LoanEnums::LOAN_BACK_STATUS_ING, 'back_type' => Enums\LoanEnums::LOAN_BACK_TYPE_TRANSFER],
            'columns' => 'loan_id, SUM(back_amount) as back_amount',
            'group' => 'loan_id'
        ])->toArray();
        return array_column($loan_return_list, 'back_amount', 'loan_id');
    }

    /**
     * 获取每笔借款下审批状态为审批中的报销金额
     * @param array $loan_ids 借款单id组
     * @param integer $currency 币种
     * @return array
     */
    private function getLoanReimbursementAmount($loan_ids, $currency)
    {
        //查询员工报销冲减总金额(已支付)
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['l' => ReimbursementRelLoan::class]);
        $builder->leftjoin(Loan::class, 'lo.id = l.loan_id', 'lo');
        $builder->leftjoin(Reimbursement::class, 'r.id = l.re_id', 'r');
        $builder->columns('lo.id, SUM(if(r.currency=' . $currency . ', r.loan_amount, r.loan_amount*r.exchange_rate)) as loan_amount');
        $builder->where("lo.id in ({loan_ids:array}) and r.loan_amount != :loan_amount: and l.is_deleted = :is_deleted:", ['loan_ids' => $loan_ids, 'loan_amount' => 0, 'is_deleted' => Enums\GlobalEnums::IS_NO_DELETED]);
        $builder->andWhere('r.status = :status_approval_pending:',['status_approval_pending' => Enums::WF_STATE_PENDING]);
        $builder->groupBy('lo.id');
        $loan_reimbursement_list = $builder->getQuery()->execute()->toArray();
        return array_column($loan_reimbursement_list, 'loan_amount' , 'id');
    }

    /**
     * 获取未归还备用金列表【备用金应扣明细、备用金薪资抵扣归还明细】
     * @param string $start_time 起始时间
     * @param string $end_time  截止时间
     * @param string $deduct_date 应扣日期
     * @param integer $currency 币种
     * @param string $created_at 数据生成日期
     * @return array
     */
    private function findReserveFund($start_time, $end_time, $deduct_date, $currency, $created_at)
    {
        //最终符合条件的未归还备用金
        $final_fund_apply_list = [];
        //应扣明细单据
        $user_order = [];
        //备用金归还明细
        $fund_return = [];

        //找到时间范围内，审批通过、已支付 未归还的备用金申请单 以及 每笔备用金申请单下的已归还总金额（备用金归还单状态=审批同意 && 归还类型是归还、薪资抵扣）
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['a' => ReserveFundApply::class]);
        $builder->leftJoin(ReserveFundApplyReturnRelModel::class, 'rl.apply_id = a.id and rl.is_deleted = ' . Enums\GlobalEnums::IS_NO_DELETED, 'rl');
        $builder->leftJoin(ReserveFundReturn::class, 'r.id = rl.return_id and r.status = ' . Enums::WF_STATE_APPROVED . ' and r.type in( ' . Enums\ReserveFundReturnEnums::RETURN_TYPE . ',' . Enums\ReserveFundReturnEnums::RETURN_SALARY_DEDUCT_TYPE . ') AND rl.is_deleted = ' . Enums\GlobalEnums::IS_NO_DELETED, 'r');
        $builder->columns('a.id, a.rfano, a.create_id, a.create_name, a.create_department_id, a.create_department_name, a.apply_date, a.create_company_id, a.create_company_name, a.amount, a.create_store_id, a.create_store_name, a.currency, SUM(r.amount) as back_amount');
        $builder->where('a.currency = :currency: and a.return_status = :return_status: and a.status = :status: and a.pay_status = :pay_status: and a.apply_date >= :start_time: and a.apply_date < :end_time:', ['currency' => $currency, 'return_status' =>Enums\ReserveFundReturnEnums::BACK_STATUS_NOT, 'status' => Enums::WF_STATE_APPROVED, 'pay_status' => Enums::PAYMENT_PAY_STATUS_PAY, 'start_time' => $start_time, 'end_time' => $end_time]);
        $builder->groupBy('a.id');//按照申请单纬度汇总每笔申请单的已归还金额
        $fund_apply_list = $builder->getQuery()->execute()->toArray();
        if (!empty($fund_apply_list)) {
            $staff_ids = array_values(array_unique(array_filter(array_column($fund_apply_list, 'create_id'))));
            if (!empty($staff_ids)) {
                //只找到备用金申请人的目前在职状态=在职的数据
                $staff_list = HrStaffInfoModel::find([
                    'conditions' => 'staff_info_id in ({staff_ids:array}) and state = :state: and wait_leave_state = :wait_leave_state:',
                    'bind' => ['staff_ids' => $staff_ids, 'state' => Enums\StaffInfoEnums::STAFF_STATE_IN, 'wait_leave_state' => Enums\StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_NO],
                    'columns' => 'staff_info_id, sys_store_id, state, wait_leave_state'
                ])->toArray();
                if (!empty($staff_list)) {
                    $in_staff_ids = array_column($staff_list, 'staff_info_id');
                    $in_staff_list = array_column($staff_list, null, 'staff_info_id');
                    foreach ($fund_apply_list as $apply) {
                        if (!in_array($apply['create_id'], $in_staff_ids)) {
                            //非在职的过滤，跳过
                            continue;
                        }
                        //在职过员工所属网点 = 申请单上的[申请人所属网点（create_store_id）过滤掉，跳过
                        if ($in_staff_list[$apply['create_id']]['sys_store_id'] == $apply['create_store_id']) {
                            continue;
                        }
                        //只要在职的 && 员工当前所属网点 != 某员工该备用金申请单上的[申请人所属网点（create_store_id）
                        $final_fund_apply_list[] = $apply;
                    }
                }
            }
        }
        //组装备用金应扣明细表、备用金薪资抵扣归还单数据，状态待审核（不用走审批流）
        if ($final_fund_apply_list) {
            //获取各网点维度申请备用金金额
            $fund_create_store_ids = array_values(array_unique(array_column($final_fund_apply_list, 'create_store_id')));
            $store_create_total_amount = $this->getPaidTotalMoney($fund_create_store_ids);

            //抵扣批次号
            $deduct_date_no = date('Ymd', strtotime($deduct_date));
            $serial_no = Enums\SalaryDeductEnums::SALARY_DEDUCT_USER_ORDER_RESERVE_PREFIX . $deduct_date_no;
            foreach ($final_fund_apply_list as $apply) {
                //本条备用金申请金额-本条备用金已归还金额总额（备用金归还单状态=审批同意 && 归还类型是归还、薪资抵扣）
                $back_amount = bcsub($apply['amount'], $apply['back_amount']);

                //备用金应扣明细
                $user_order[] = [
                    'serial_no' => $serial_no,//抵扣批次号
                    'serial_number' => BaseService::genSerialNo(Enums\SalaryDeductEnums::SALARY_DEDUCT_USER_ORDER_RESERVE_PREFIX, RedisKey::SALARY_DEDUCT_RESERVE_FUND_ORDER_LOCK, 5, $deduct_date_no),//抵扣批次明细编号
                    'order_id' => $apply['id'],//借款/备用金申请表-ID
                    'order_no' => $apply['rfano'],//借款/备用金申请表-单号
                    'staff_id' => $apply['create_id'],//申请人-员工工号
                    'staff_name' => $apply['create_name'],//申请人-员工姓名
                    'state' => Enums\StaffInfoEnums::STAFF_STATE_IN,//备用金只要在职
                    'wait_leave_state' => Enums\StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_NO,//备用金只要在职
                    'node_department_id' => $apply['create_department_id'],//申请人-部门ID
                    'node_department_name' => $apply['create_department_name'],//申请人-部门名称
                    'apply_date' => $apply['apply_date'],//申请日期
                    'amount' => $apply['amount'],//申请金额
                    'bank_pay_date' => NULL,
                    'loan_days' => 0,
                    'deduct_date' => $deduct_date,//应扣日期
                    'deduct_amount' => $back_amount,//应扣金额
                    'actual_deduct_amount' => 0,//实扣金额
                    'company_name' => '',//扣款所属公司
                    'deduct_type' => Enums\SalaryDeductEnums::SALARY_DEDUCT_TYPE_ALL,//抵扣类型:全是全额
                    'source_type' => Enums\SalaryDeductEnums::SALARY_DEDUCT_USER_ORDER_SOURCE_TYPE_RESERVE_FUND,//单据类型:备用金
                    'status' => Enums\SalaryDeductEnums::SALARY_DEDUCT_USER_ORDER_STATUS_UN_CREATE,//实扣状态:未生成
                    'actual_deduct_at' => NULL,//实扣数据生成时间
                    'created_at' => $created_at,//应扣数据生成时间
                    'updated_at' => $created_at
                ];

                //备用金归还明细
                $fund_return[] = [
                    'apply_id' => $apply['id'],//备用金申请单id
                    'rrno' => 'WDBGH' . ReserveFundBaseService::getNo(date('Ymd')),//归还编号
                    'apply_date' => date('Y-m-d'),//申请日期
                    'create_id' => $apply['create_id'],//原持有人工号
                    'create_name' => $apply['create_name'],//原持有人姓名
                    'create_department_id' => $apply['create_department_id'],//原持有人-部门ID
                    'create_department_name' => $apply['create_department_name'],//原持有人-部门名字
                    'create_company_id' => $apply['create_company_id'],//原持有人-公司ID
                    'create_company_name' => $apply['create_company_name'],//原持有人-公司名字
                    'create_store_id' => $apply['create_store_id'],//原持有人-所属网点ID
                    'create_store_name' => $apply['create_store_name'],//原持有人-网点名称
                    'create_total_amount' => $store_create_total_amount[$apply['create_store_id']]['sumatory'] ?? 0,//原持有人-备用金总金额
                    'currency' => $apply['currency'],//'币种
                    'return_reason' => 'Initiate payroll offsetting for Return of the reserve',//归还原因
                    'amount' => $back_amount,//申请归还金额总计
                    'status' => Enums::WF_STATE_PENDING,//待审核
                    'created_at' => $created_at,
                    'updated_at' => $created_at,
                    'type' => Enums\ReserveFundReturnEnums::RETURN_SALARY_DEDUCT_TYPE,//3薪资抵扣
                    'bank_flow_date' => $deduct_date//银行流水日期
                ];
            }
        }
        return [$user_order, $fund_return];
    }

    /**
     * 获取各网点维度申请备用金金额
     * @param array $create_store_ids 网点组
     * @return array
     */
    private function getPaidTotalMoney($create_store_ids)
    {
        // 网点维度申请备用金金额 由于之前需求漏洞只统计了已通过已支付数据，这期增加待审核、已通过待支付+已支付逻辑
        $total_sum = ReserveFundApply::sum([
            'conditions' => 'create_store_id in ({create_store_ids:array}) and status in ({status:array}) and pay_status in ({pay_status:array})',
            'bind' => [
                'create_store_ids' => $create_store_ids,
                'status' => [Enums::WF_STATE_PENDING, Enums::WF_STATE_APPROVED],
                'pay_status' => [Enums::PAYMENT_PAY_STATUS_PENDING, Enums::PAYMENT_PAY_STATUS_PAY]
            ],
            'column' => 'amount',
            'group' => 'create_store_id'
        ])->toArray();
        $total_sum = array_column($total_sum, null, 'create_store_id');
        // 网点维度归还备用金状态为待审核/已通过
        $total_sum_return = ReserveFundReturn::sum([
            'conditions' => 'create_store_id in ({create_store_ids:array}) and status in ({status:array}) and type in ({type:array})',
            'bind' => [
                'create_store_ids' => $create_store_ids,
                'status' => [Enums::WF_STATE_PENDING, Enums::WF_STATE_APPROVED],
                'type' => [
                    Enums\ReserveFundReturnEnums::RETURN_TYPE,
                    Enums\ReserveFundReturnEnums::RETURN_SALARY_DEDUCT_TYPE
                ]
            ],
            'column' => 'amount',
            'group' => 'create_store_id'
        ])->toArray();
        $total_sum_return = array_column($total_sum_return, null, 'create_store_id');

        foreach ($total_sum as $store_id => &$total) {
            $total['sumatory'] = bcsub($total['sumatory'], $total_sum_return[$store_id]['sumatory'] ?? 0);
        }
        return $total_sum;
    }

    /**
     * 历史有已归还/坏账的备用金申请单据与归还单绑定关系，反写到备用金归还表关系表中
     * 一次性脚本
     */
    public function history_reserve_fundAction()
    {
        echo date('Ymd H:i:s').' 脚本开始执行'.PHP_EOL;
        try {
            //查询出来return_id> 0 or return_bad_id > 0 && 审批通过 && 已支付 的 备用金申请单列表
            $deal_apply_list = ReserveFundApply::find([
                'conditions' => 'status = :status: and pay_status = :pay_status: and (return_id > :return_id: or return_bad_id > :return_id:)',
                'bind' => ['status' => Enums::WF_STATE_APPROVED, 'pay_status' => Enums::LOAN_PAY_STATUS_PAY, 'return_id' => 0],
                'columns' => ['id', 'return_id', 'return_bad_id']
            ])->toArray();
            echo '共查询到:'.count($deal_apply_list).'条备用金申请单需要处理'.PHP_EOL;
            if (!empty($deal_apply_list)) {
                $now = date('Y-m-d H:i:s');
                $apply_return_rel = [];
                foreach ($deal_apply_list as $apply) {
                    if ($apply['return_id'] > 0) {
                        $apply_return_rel[] = [
                            'apply_id' => $apply['id'],
                            'return_id' => $apply['return_id'],
                            'created_at' => $now
                        ];
                    }
                    if ($apply['return_bad_id'] > 0) {
                        $apply_return_rel[] = [
                            'apply_id' => $apply['id'],
                            'return_id' => $apply['return_bad_id'],
                            'created_at' => $now
                        ];
                    }
                }
                if (!empty($apply_return_rel)) {
                    (new ReserveFundApplyReturnRelModel())->batch_insert($apply_return_rel);
                }
                echo '历史有已归还/坏账的备用金申请单据与归还单绑定关系，反写到备用金归还表关系表全部处理完毕'.PHP_EOL;
            } else {
                echo '暂无数据需要处理'.PHP_EOL;
                $this->logger->info('暂无数据需要处理');
            }
        } catch (Exception $e) {
            $this->logger->warning('history_reserve_fund_save_task_exception:' . $e->getMessage());
            echo date('Ymd H:i:s') . ' 历史有已归还/坏账的备用金申请单据与归还单绑定关系，反写到备用金归还表关系表失败[异常]' . $e->getMessage() . PHP_EOL;
        }
        echo date('Ymd H:i:s'). " 脚本执行结束". PHP_EOL;
    }

    /**
     * 下载中心-上传实扣金额-异步任务-处理
     */
    public function deductAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $this->check_process($process_name);
        //查询当前是否存在待处理的任务
        $salary_deduct_data = ImportTaskModel::findFirst([
            'conditions' => 'type = :type: and status = :status: and is_deleted = :is_deleted:',
            'bind' => [
                'type' => Enums\ImportCenterEnums::TYPE_SALARY_DEDUCT,
                'status' => Enums\ImportCenterEnums::STATUS_WAITING,
                'is_deleted' => Enums\GlobalEnums::IS_NO_DELETED
            ],
            'order' => 'id asc',
        ]);
        if (empty($salary_deduct_data)) {
            echo '没有等待导入的任务:' . PHP_EOL;
            exit();
        }
        // 存在下载任务,开始执行
        // 1. 先改为处理中
        $update_at = date('Y-m-d H:i:s');
        $salary_deduct_data->status = Enums\ImportCenterEnums::STATUS_DOING;
        $salary_deduct_data->updated_at = $update_at;
        $bool = $salary_deduct_data->save();
        if ($bool === false) {
            echo '下载任务启动异常: 更新为"处理中"失败' . PHP_EOL;
            exit();
        }
        // 不能开启事务, 时间长数据库连接自动关闭, 事务提交会报错(MySQL server has gone away )
        try {
            // 1. 下载文件
            $excel_data = get_oss_file_excel_data($salary_deduct_data->file_url, [\Vtiful\Kernel\Excel::TYPE_STRING]);
            // 读取每行单元格数据时，可指定每个单元格数据类型进行读取
            if (empty($excel_data)) {
                // 正常情况下进入任务是会校验, 所以这种空文件基本不会出现, 如果出现记录异常
                throw new Exception('file is empty');
            }

            // 3. 批量导入
            $salary_deduct_service = DeductService::getInstance();
            $salary_deduct_service::setLanguage($salary_deduct_data->language ?? 'en');
            $res = $salary_deduct_service->handleDeductTask($excel_data);
            // 4. 生成结果文件
            if ($res['code'] == ErrCode::$SUCCESS) {
                // 处理成功, 生成上传结果文件
                $upload_result_data = $res['data']['excel_data'];
                $excel_extra_config = [
                    'file_name' => date('YmdHis') . '_' . $salary_deduct_data->staff_info_id . '_import_result.xlsx',
                    'end_column_char' => 'I',
                    'column_width' => 15,
                ];
                $header = array_shift($upload_result_data);
                $path = self::customizeExcelToFile($header, $upload_result_data, $excel_extra_config);
                $oss_result = OssHelper::uploadFile($path);
            } else {
                throw new Exception($res['message'], $res['code']);
            }

            // 5. 写入结果
            // 再查一次数据,长时间处理, 上边的数据库连接会自动关闭,保存报错
            $after_salary_deduct_data = ImportTaskModel::findFirst($salary_deduct_data->id);
            if (empty($after_salary_deduct_data) || $after_salary_deduct_data->status != Enums\ImportCenterEnums::STATUS_DOING) {
                throw new Exception('error: result failed save error, not found task');
            }
            $after_salary_deduct_data->success_num = $res['data']['success_num'];
            $after_salary_deduct_data->error_num = $res['data']['failed_sum'];
            $after_salary_deduct_data->result_file_url = $oss_result['object_url'] ?? '';
            $after_salary_deduct_data->status = Enums\ImportCenterEnums::STATUS_DONE;
            $after_salary_deduct_data->finished_at = $update_at;
            $after_salary_deduct_data->updated_at = $update_at;
            $bool = $after_salary_deduct_data->save();
            if ($bool === false) {
                // 正常情况下进入任务是会校验, 所以这种空文件基本不会出现, 如果出现记录异常
                throw new Exception('salary_deduct_task save result error, message=' . get_data_object_error_msg($after_salary_deduct_data));
            }
        } catch (Exception $e) {
            $this->logger->error('salary_deduct_error: message=' . $e->getMessage() . '; trace=' . $e->getTraceAsString());

            // 导入结果置为失败
            $after_salary_deduct_data = ImportTaskModel::findFirst($salary_deduct_data->id);
            if (empty($after_salary_deduct_data) || $after_salary_deduct_data->status != Enums\ImportCenterEnums::STATUS_DOING) {
                echo 'error: result failed save error, not found task' . PHP_EOL;
                exit();
            }
            $after_salary_deduct_data->status = Enums\ImportCenterEnums::STATUS_FAILED;
            $after_salary_deduct_data->finished_at = $update_at;
            $after_salary_deduct_data->updated_at = $update_at;
            $bool = $after_salary_deduct_data->save();
            if ($bool === false) {
                $this->logger->error('salary_deduct_task save result error, message=' . get_data_object_error_msg($after_salary_deduct_data));
            }

            echo 'error:' . $e->getMessage() . PHP_EOL;exit();
        }
        $this->logger->info('salary_deduct_task success: result=' . json_encode($res ?? []));
        echo 'salary deduct task success' . PHP_EOL;
    }
}