<?php


use App\Library\Enums\BankFlowEnums;
use App\Library\Enums\DownloadCenterEnum;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\ImportCenterEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Models\oa\ImportTaskModel;
use App\Modules\Common\Services\DownloadCenterService;
use App\Modules\SimManage\Services\CompanyMobileService;
use App\Modules\SimManage\Services\FeedbackService;
use App\Modules\SimManage\Services\SimCardService;
use GuzzleHttp\Exception\GuzzleException;


class SimManageTask extends BaseTask
{
    
    /**
     * 手机号码批量新增
     * @return void
     * @throws BusinessException
     * @throws GuzzleException
     * @throws ValidationException
     */
    public function companyMobileAddAction()
    {
        // 查询最近一次待下载任务
        $importCenterModel = $this->getImportModel(ImportCenterEnums::TYPE_SIM_COMPANY_MOBILE_ADD);
        if (empty($importCenterModel)) {
            return;
        }
        $this->checkLock(__METHOD__);
        // 1. 下载文件
        CompanyMobileService::setLanguage($importCenterModel->language ?? 'en');
        $service = CompanyMobileService::getInstance();
        $user    = json_decode(base64_decode($importCenterModel->args_json), true);
        try {
            $result = $service->batchAdd($importCenterModel->file_url, $user);
            $this->updateImportModel($importCenterModel, $result);
        } catch (ValidationException|BusinessException|GuzzleException|Exception $e) {
            throw $e;
        }
        $this->clearLock(__METHOD__);
    }

    /**
     * 手机号码批量编辑
     * @return void
     * @throws BusinessException
     * @throws GuzzleException
     * @throws ValidationException
     */
    public function companyMobileEditAction()
    {
        // 查询最近一次待下载任务
        $importCenterModel = $this->getImportModel(ImportCenterEnums::TYPE_SIM_COMPANY_MOBILE_EDIT);
        if (empty($importCenterModel)) {
            return;
        }
        $this->checkLock(__METHOD__);
        // 1. 下载文件
        CompanyMobileService::setLanguage($importCenterModel->language ?? 'en');
        $service = CompanyMobileService::getInstance();
        $user    = json_decode(base64_decode($importCenterModel->args_json), true);
        try {
            $result = $service->batchEdit($importCenterModel->file_url, $user);
            $this->updateImportModel($importCenterModel, $result);
        } catch (ValidationException|BusinessException|GuzzleException|Exception $e) {
            throw $e;
        }
        $this->clearLock(__METHOD__);
    }


    /**
     * 手机号码批量新增
     * @return void
     * @throws BusinessException
     * @throws GuzzleException
     * @throws ValidationException
     */
    public function simCardAddAction()
    {
        // 查询最近一次待下载任务
        $importCenterModel = $this->getImportModel(ImportCenterEnums::TYPE_SIM_CARD_ADD);
        if (empty($importCenterModel)) {
            echo '没有等待导入的任务:' . PHP_EOL;
            return;
        }

        $this->checkLock(__METHOD__);

        // 1. 下载文件
        SimCardService::setLanguage($importCenterModel->language ?? 'en');
        $service = SimCardService::getInstance();
        $user    = json_decode(base64_decode($importCenterModel->args_json), true);
        try {
            $result = $service->batchAdd($importCenterModel->file_url, $user);
            $this->updateImportModel($importCenterModel, $result);
        } catch (ValidationException|BusinessException|GuzzleException|Exception $e) {
            throw $e;
        }
        $this->clearLock(__METHOD__);
    }

    /**
     * 手机号码批量编辑
     * @return void
     * @throws BusinessException
     * @throws GuzzleException
     * @throws ValidationException
     */
    public function simCardEditAction()
    {
        // 查询最近一次待下载任务
        $importCenterModel = $this->getImportModel(ImportCenterEnums::TYPE_SIM_CARD_EDIT);
        if (empty($importCenterModel)) {
            echo '没有等待导入的任务:' . PHP_EOL;
            return;
        }

        $this->checkLock(__METHOD__);

        // 1. 下载文件
        SimCardService::setLanguage($importCenterModel->language ?? 'en');
        $service = SimCardService::getInstance();
        $user    = json_decode(base64_decode($importCenterModel->args_json), true);
        try {
            $result = $service->batchEdit($importCenterModel->file_url, $user);
            $this->updateImportModel($importCenterModel, $result);
        } catch (ValidationException|BusinessException|GuzzleException|Exception $e) {
            throw $e;
        }
        $this->clearLock(__METHOD__);
    }

    /**
     * 更新数据信息
     * @param ImportTaskModel $importCenterModel
     * @param $result
     * @return void
     */
    private function updateImportModel(ImportTaskModel $importCenterModel, $result)
    {
        $importCenterModel->success_num     = $result['success_num'];
        $importCenterModel->error_num       = $result['fail_num'];
        $importCenterModel->result_file_url = $result['result_url'] ?? '';
        $importCenterModel->status          = ImportCenterEnums::STATUS_DONE;
        $importCenterModel->finished_at     = date('Y-m-d H:i:s');
        $importCenterModel->save();
    }

    /**
     * 待处理数据
     * @param $type
     * @return false
     */
    private function getImportModel($type)
    {
        $importCenterModel = ImportTaskModel::findFirst([
            'conditions' => 'type = :type: and status = :status: and is_deleted = :is_deleted:',
            'bind'       => [
                'type'       => $type,
                'status'     => ImportCenterEnums::STATUS_WAITING,
                'is_deleted' => ImportCenterEnums::NOT_DELETED,
            ],
            'order'      => 'id asc',
        ]);
        if (empty($importCenterModel)) {
            return false;
        }
        return $importCenterModel;
    }


    /**
     * OA-SIM卡管理-公司号码-导出excel
     * 每5分钟执行一次
     */
    public function companyMobileExportAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log          = '任务名称: ' . $process_name . PHP_EOL;
        $log          .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log          .= '初始内存: ' . memory_usage() . PHP_EOL;
        $is_exception = false;

        try {
            // 1.提取待处理的下载任务(最早入队的)
            $task_model = DownloadCenterService::getInstance()->getFirstPendingTask(DownloadCenterEnum::COMPANY_MOBILE_EXPORT);
            if (empty($task_model)) {
                throw new ValidationException('没有待下载的任务', ErrCode::$VALIDATE_ERROR);
            }

            // 2.1解析参数
            $params             = !empty($task_model->args_json) ? json_decode(base64_decode($task_model->args_json,
                true), true) : [];
            $params['language'] = $params['language'] ?? DownloadCenterEnum::DETAUL_LANGUAGE;

            $log .= '任务ID: ' . $task_model->id . PHP_EOL;
            $log .= '任务参数: ' . json_encode($params, JSON_UNESCAPED_UNICODE) . PHP_EOL;

            // 2.2设置系统语言
            CompanyMobileService::setLanguage($params['language']);
            // 2.3获取数据
            $file_url = CompanyMobileService::getInstance()->downloadExcelData($params);

            // 3.更新任务
            if (!empty($file_url)) {
                $save_model_result = DownloadCenterService::getInstance()->saveTask($task_model,
                    DownloadCenterEnum::TASK_STATUS_SUCCESS, $file_url);
                if ($save_model_result === false) {
                    $log .= '任务状态: 更新失败, 原因可能是: ' . get_data_object_error_msg($task_model) . PHP_EOL;
                } else {
                    $log .= '任务状态: 更新成功' . PHP_EOL;
                }
            } else {
                // 记录错误日志
                $log .= '任务状态: 未更新, 生成Excel失败, 待下次重新执行' . PHP_EOL;
            }
        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;
        } catch (Exception $e) {
            $log          .= $e->getMessage() . PHP_EOL;
            $is_exception = true;
        }
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        if ($is_exception) {
            $this->logger->error('SIM卡管理-公司号码-导出任务: ' . $log);
        } else {
            $this->logger->info('SIM卡管理-公司号码-导出任务: ' . $log);
        }

        echo $log;
    }

    /**
     * OA-SIM卡管理-SIM卡池-导出excel
     * 每5分钟执行一次
     */
    public function simCardExportAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log          = '任务名称: ' . $process_name . PHP_EOL;
        $log          .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log          .= '初始内存: ' . memory_usage() . PHP_EOL;
        $is_exception = false;

        try {
            // 1.提取待处理的下载任务(最早入队的)
            $task_model = DownloadCenterService::getInstance()->getFirstPendingTask(DownloadCenterEnum::COMPANY_MOBILE_SIM_EXPORT);
            if (empty($task_model)) {
                throw new ValidationException('没有待下载的任务', ErrCode::$VALIDATE_ERROR);
            }

            // 2.1解析参数
            $params             = !empty($task_model->args_json) ? json_decode(base64_decode($task_model->args_json,
                true), true) : [];
            $params['language'] = $params['language'] ?? DownloadCenterEnum::DETAUL_LANGUAGE;

            $log .= '任务ID: ' . $task_model->id . PHP_EOL;
            $log .= '任务参数: ' . json_encode($params, JSON_UNESCAPED_UNICODE) . PHP_EOL;

            // 2.2设置系统语言
            SimCardService::setLanguage($params['language']);
            // 2.3获取数据
            $file_url = SimCardService::getInstance()->downloadExcelData($params);
            // 3.更新任务
            if (!empty($file_url)) {
                $save_model_result = DownloadCenterService::getInstance()->saveTask($task_model,
                    DownloadCenterEnum::TASK_STATUS_SUCCESS, $file_url);
                if ($save_model_result === false) {
                    $log .= '任务状态: 更新失败, 原因可能是: ' . get_data_object_error_msg($task_model) . PHP_EOL;
                } else {
                    $log .= '任务状态: 更新成功' . PHP_EOL;
                }
            } else {
                // 记录错误日志
                $log .= '任务状态: 未更新, 生成Excel失败, 待下次重新执行' . PHP_EOL;
            }
        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;
        } catch (Exception $e) {
            $log          .= $e->getMessage() . PHP_EOL;
            $is_exception = true;
        }
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        if ($is_exception) {
            $this->logger->error('SIM卡管理-SIM卡池-导出任务: ' . $log);
        } else {
            $this->logger->info('SIM卡管理-SIM卡池-导出任务: ' . $log);
        }

        echo $log;
    }

    /**
     * 手机号码批量编辑
     * @return void
     * @throws BusinessException
     * @throws GuzzleException
     * @throws ValidationException
     */
    public function feedbackNumberEditAction()
    {
        // 查询最近一次待下载任务
        $importCenterModel = $this->getImportModel(ImportCenterEnums::TYPE_SIM_COMPANY_MOBILE_FEEDBACK_EDIT);
        if (empty($importCenterModel)) {
            return;
        }
        get_runtime() != 'dev' && $this->checkLock(__METHOD__);
        // 1. 下载文件
        FeedbackService::setLanguage($importCenterModel->language ?? 'en');
        $service = FeedbackService::getInstance();
        $user    = json_decode(base64_decode($importCenterModel->args_json), true);
        try {
            $result = $service->batchEdit($importCenterModel->file_url, $user);
            $this->updateImportModel($importCenterModel, $result);
        } catch (ValidationException|BusinessException|GuzzleException|Exception $e) {
            throw $e;
        }
        $this->clearLock(__METHOD__);
    }


    /**
     * OA-SIM卡管理-异常反馈号码-导出excel
     * 每5分钟执行一次
     */
    public function feedbackNumberExportAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log          = '任务名称: ' . $process_name . PHP_EOL;
        $log          .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log          .= '初始内存: ' . memory_usage() . PHP_EOL;
        $is_exception = false;

        try {
            // 1.提取待处理的下载任务(最早入队的)
            $task_model = DownloadCenterService::getInstance()->getFirstPendingTask(DownloadCenterEnum::COMPANY_MOBILE_FEEDBACK_EXPORT);
            if (empty($task_model)) {
                throw new ValidationException('没有待下载的任务', ErrCode::$VALIDATE_ERROR);
            }

            // 2.1解析参数
            $params             = !empty($task_model->args_json) ? json_decode(base64_decode($task_model->args_json,
                true), true) : [];
            $params['language'] = $params['language'] ?? DownloadCenterEnum::DETAUL_LANGUAGE;

            $log .= '任务ID: ' . $task_model->id . PHP_EOL;
            $log .= '任务参数: ' . json_encode($params, JSON_UNESCAPED_UNICODE) . PHP_EOL;

            // 2.2设置系统语言
            FeedbackService::setLanguage($params['language']);
            // 2.3获取数据
            $file_url = FeedbackService::getInstance()->downloadExcelData($params);
            // 3.更新任务
            if (!empty($file_url)) {
                $save_model_result = DownloadCenterService::getInstance()->saveTask($task_model,
                    DownloadCenterEnum::TASK_STATUS_SUCCESS, $file_url);
                if ($save_model_result === false) {
                    $log .= '任务状态: 更新失败, 原因可能是: ' . get_data_object_error_msg($task_model) . PHP_EOL;
                } else {
                    $log .= '任务状态: 更新成功' . PHP_EOL;
                }
            } else {
                // 记录错误日志
                $log .= '任务状态: 未更新, 生成Excel失败, 待下次重新执行' . PHP_EOL;
            }
        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;
        } catch (Exception $e) {
            $log          .= $e->getMessage() . PHP_EOL;
            $is_exception = true;
        }
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        if ($is_exception) {
            $this->logger->error('SIM卡管理-SIM卡池-导出任务: ' . $log);
        } else {
            $this->logger->info('SIM卡管理-SIM卡池-导出任务: ' . $log);
        }

        echo $log;
    }


}
