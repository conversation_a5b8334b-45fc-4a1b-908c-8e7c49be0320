# FlashPay支付状态更新服务使用说明

## 概述

`FlashPayStatusUpdateService` 是一个通用的FlashPay支付状态更新服务类，旨在统一处理SFTP和API两种方式的支付状态更新逻辑，提高代码复用性和维护性。

## 主要特性

- **统一接口**: 提供统一的支付状态更新接口，支持SFTP和API两种方式
- **参数验证**: 完善的参数验证机制，确保数据完整性
- **异常处理**: 完善的异常处理和错误日志记录
- **单例模式**: 采用单例模式，确保服务实例的唯一性
- **可扩展性**: 易于扩展支持更多支付方式

## 类结构

```php
namespace App\Modules\Pay\Services;

class FlashPayStatusUpdateService extends BaseService
{
    // 单例实例
    private static $instance;
    
    // 主要方法
    public static function getInstance()
    public function updatePaymentStatus($params)
    
    // 私有方法
    private function updateSftpPaymentStatus($params, $logger)
    private function updateApiPaymentStatus($params, $logger)
    private function validateParams($params)
    // ... 其他辅助方法
}
```

## 使用方法

### 1. SFTP方式支付状态更新

```php
use App\Modules\Pay\Services\FlashPayStatusUpdateService;

// SFTP方式更新支付状态
$result = FlashPayStatusUpdateService::getInstance()->updatePaymentStatus([
    'payment' => $payment,                    // Payment模型实例
    'transaction_status' => 'success',       // 交易状态: success/failed
    'transaction_batch_no' => 'BATCH_001',   // 交易批次号
    'transaction_order_no' => 'ORDER_001',   // 交易订单号
    'transaction_fail_reasons' => '',        // 失败原因（失败时提供）
    'payment_method' => 'sftp',              // 支付方式: sftp
    'logger' => $this->logger                // 日志对象
]);

if ($result['success']) {
    echo "SFTP支付状态更新成功";
} else {
    echo "更新失败: " . $result['error'];
}
```

### 2. API方式支付状态更新

```php
use App\Modules\Pay\Services\FlashPayStatusUpdateService;

// API方式更新支付状态
$result = FlashPayStatusUpdateService::getInstance()->updatePaymentStatus([
    'payment' => $payment,                    // Payment模型实例
    'transaction_status' => 3,               // 交易状态码
    'payment_data' => $paymentData,          // FlashPay返回的支付数据
    'payment_result' => $paymentResult,      // FlashPay返回的完整结果
    'pay_config' => $payConfig,              // 支付配置信息
    'payment_method' => 'api',               // 支付方式: api
    'logger' => $this->logger                // 日志对象
]);

if ($result['success']) {
    echo "API支付状态更新成功";
} else {
    echo "更新失败: " . $result['error'];
}
```

## 参数说明

### 通用参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| payment | Payment | 是 | 支付记录模型实例 |
| transaction_status | string/int | 是 | 交易状态 |
| payment_method | string | 否 | 支付方式，默认'api' |
| logger | object | 否 | 日志对象，默认使用服务内置logger |

### SFTP方式专用参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| transaction_batch_no | string | 是 | 交易批次号 |
| transaction_order_no | string | 是 | 交易订单号 |
| transaction_fail_reasons | string | 否 | 失败原因 |

### API方式专用参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| payment_data | array | 是 | FlashPay返回的支付数据 |
| payment_result | array | 否 | FlashPay返回的完整结果 |
| pay_config | array | 否 | 支付配置信息 |

## 交易状态说明

### SFTP方式状态

- `success`: 支付成功
- `failed`: 支付失败

### API方式状态

- `0`: 交易待支付
- `2`: 交易处理中
- `3`: 交易成功
- `4`: 交易失败
- `5`: 交易关闭

## 返回值格式

```php
[
    'success' => true,    // 布尔值，表示操作是否成功
    'error' => ''         // 字符串，错误信息（成功时为空）
]
```

## 异常处理

服务会自动处理以下异常情况：

1. **参数验证异常**: 缺少必要参数或参数格式错误
2. **数据库操作异常**: 支付记录保存失败
3. **业务逻辑异常**: 未知交易状态等
4. **系统异常**: 其他系统级异常

所有异常都会被捕获并记录到日志中，同时返回错误信息。

## 最佳实践

1. **事务管理**: 在调用此服务前，建议开启数据库事务
2. **日志记录**: 传入合适的日志对象，便于问题排查
3. **错误处理**: 检查返回结果，妥善处理失败情况
4. **参数验证**: 在调用前进行基本的参数检查

## 示例：在现有服务中使用

### FlashPaySftpService中的使用

```php
// 原来的代码
private function updatePaymentStatus($payment, $transactionStatus, $transactionBatchNo, $transactionOrderNo, $transactionFailReasons)
{
    // 大量重复的状态更新逻辑...
}

// 优化后的代码
private function updatePaymentStatus($payment, $transactionStatus, $transactionBatchNo, $transactionOrderNo, $transactionFailReasons)
{
    return FlashPayStatusUpdateService::getInstance()->updatePaymentStatus([
        'payment' => $payment,
        'transaction_status' => $transactionStatus,
        'transaction_batch_no' => $transactionBatchNo,
        'transaction_order_no' => $transactionOrderNo,
        'transaction_fail_reasons' => $transactionFailReasons,
        'payment_method' => 'sftp',
        'logger' => $this->logger
    ]);
}
```

### FlashPayService中的使用

```php
// 原来的代码
public function syncFlashPayTradeStatus($payment, $pay_config)
{
    // 大量重复的状态更新逻辑...
}

// 优化后的代码
public function syncFlashPayTradeStatus($payment, $pay_config)
{
    // ... 前置逻辑 ...
    
    $updateResult = FlashPayStatusUpdateService::getInstance()->updatePaymentStatus([
        'payment' => $payment,
        'transaction_status' => $payment_data['tradeStatus'],
        'payment_data' => $payment_data,
        'payment_result' => $payment_result,
        'pay_config' => $pay_config,
        'payment_method' => 'api',
        'logger' => $this->logger
    ]);
    
    if (!$updateResult['success']) {
        throw new Exception($updateResult['error']);
    }
}
```

## 注意事项

1. 此服务是单例模式，请使用 `getInstance()` 方法获取实例
2. 不同支付方式需要提供不同的参数，请参考参数说明
3. 服务会自动处理业务模块状态更新和飞书预警
4. 建议在使用前进行充分的单元测试

## 扩展说明

如需支持新的支付方式，可以：

1. 在 `updatePaymentStatus` 方法中添加新的分支
2. 实现对应的私有更新方法
3. 在 `validateParams` 方法中添加相应的参数验证
4. 更新文档和测试用例
